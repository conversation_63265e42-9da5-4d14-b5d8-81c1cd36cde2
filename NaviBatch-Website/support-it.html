<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supporto - NaviBatch</title>
    <meta name="description" content="Supporto NaviBatch - Ottieni aiuto con pianificazione percorsi, navigazione e ottimizzazione consegne.">
    <link rel="stylesheet" href="css/combined.min.css">
    <style>
        body {
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .support-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 120px 20px 60px;
            color: #1e293b;
        }

        .support-header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .support-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 163, 255, 0.05) 0%, rgba(96, 165, 250, 0.05) 100%);
            z-index: 1;
        }

        .support-header > * {
            position: relative;
            z-index: 2;
        }

        .support-header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00A3FF, #60A5FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        .support-header p {
            color: #64748b;
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .support-section {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            padding: 40px;
            border-radius: 16px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .support-section:hover {
            box-shadow: 0 8px 25px rgba(0, 163, 255, 0.1);
            border-color: #00A3FF;
        }

        .support-section h2 {
            color: #00A3FF;
            margin-bottom: 25px;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .support-section h3 {
            color: #1e40af;
            margin-top: 30px;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .support-section p {
            margin-bottom: 20px;
            color: #334155;
            font-size: 1.05rem;
            line-height: 1.7;
        }

        .support-section ul, .support-section ol {
            margin-bottom: 25px;
            padding-left: 25px;
        }

        .support-section li {
            margin-bottom: 12px;
            color: #334155;
            font-size: 1.05rem;
            line-height: 1.6;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .contact-method {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            padding: 30px;
            border-radius: 16px;
            border: 2px solid #e2e8f0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-method::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .contact-method:hover::before {
            left: 100%;
        }

        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 163, 255, 0.2);
            border-color: #00A3FF;
        }

        .contact-method i {
            font-size: 2rem;
            color: #00A3FF;
            margin-bottom: 20px;
            display: block;
        }

        .contact-method h3 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .contact-method p {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .contact-method a {
            color: #00A3FF;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 10px 20px;
            border: 2px solid #00A3FF;
            border-radius: 8px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .contact-method a:hover {
            background: #00A3FF;
            color: white;
            transform: scale(1.05);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            margin-bottom: 30px;
            color: #00A3FF;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 10px 20px;
            border-radius: 10px;
            background: rgba(0, 163, 255, 0.1);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #00A3FF;
            color: white;
            transform: translateX(-5px);
        }

        .language-switcher {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
        }

        .language-switcher a {
            color: #00A3FF;
            text-decoration: none;
            margin: 5px 10px;
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .language-switcher a:hover {
            background: rgba(0, 163, 255, 0.1);
            transform: translateY(-2px);
        }

        .language-switcher .current {
            color: #1e293b;
            font-weight: 700;
            background: #00A3FF;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px 10px;
            display: inline-block;
        }

        .faq-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 25px 0;
            transition: all 0.3s ease;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-item:hover {
            background: rgba(0, 163, 255, 0.02);
            border-radius: 10px;
            padding: 25px 20px;
            margin: 0 -20px;
        }

        .faq-question {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 1.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faq-question::before {
            content: '❓';
            font-size: 1.1rem;
        }

        .faq-answer {
            color: #64748b;
            line-height: 1.7;
            font-size: 1.05rem;
            padding-left: 30px;
        }

        @media (max-width: 768px) {
            .support-container {
                padding: 100px 15px 40px;
            }

            .support-header h1 {
                font-size: 2.2rem;
            }

            .contact-methods {
                grid-template-columns: 1fr;
            }

            .language-switcher a, .language-switcher .current {
                margin: 3px 5px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="support-container">
        <a href="#" class="back-link" onclick="window.location.href = window.location.origin + '/'; return false;">← Torna a NaviBatch</a>

        <div class="language-switcher">
            <a href="/support.html">English</a>
            <a href="/support-zh.html">中文</a>
            <a href="/support-ja.html">日本語</a>
            <a href="/support-fr.html">Français</a>
            <a href="/support-de.html">Deutsch</a>
            <a href="/support-es.html">Español</a>
            <span class="current">Italiano</span>
            <a href="/support-ar.html">العربية</a>
            <a href="/support-ko.html">한국어</a>
            <a href="/support-pt.html">Português</a>
            <a href="/support-ru.html">Русский</a>
            <a href="/support-nl.html">Nederlands</a>
        </div>

        <div class="support-header">
            <h1>Supporto NaviBatch</h1>
            <p>Ottieni aiuto con pianificazione percorsi, navigazione e ottimizzazione consegne</p>
        </div>

        <div class="support-section">
            <h2>📞 Contatta il Supporto</h2>
            <p>Il nostro team di supporto è qui per aiutarti con qualsiasi domanda o problema che potresti avere con NaviBatch.</p>

            <div class="contact-methods">
                <div class="contact-method">
                    <i class="fas fa-envelope"></i>
                    <h3>Supporto Generale</h3>
                    <p>Per domande generali e richieste</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-bug"></i>
                    <h3>Problemi Tecnici</h3>
                    <p>Segnala bug e problemi tecnici</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-lightbulb"></i>
                    <h3>Richieste di Funzionalità</h3>
                    <p>Suggerisci nuove funzionalità e miglioramenti</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="support-section">
            <h2>❓ Domande Frequenti</h2>

            <div class="faq-item">
                <div class="faq-question">Come posso ottimizzare i miei percorsi di consegna?</div>
                <div class="faq-answer">Aggiungi semplicemente i tuoi indirizzi di consegna a NaviBatch, e il nostro algoritmo intelligente calcolerà automaticamente il percorso più efficiente per farti risparmiare tempo e carburante.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Posso importare indirizzi da un file?</div>
                <div class="faq-answer">Sì! NaviBatch supporta l'importazione di indirizzi da file CSV, fogli di calcolo Excel e file di testo. Puoi anche incollare più indirizzi contemporaneamente.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Come funziona la funzione di navigazione batch?</div>
                <div class="faq-answer">La nostra tecnologia di navigazione batch in attesa di brevetto ti consente di navigare verso più indirizzi con un solo clic, passando automaticamente tra le destinazioni mentre completi ogni consegna.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">I miei dati sono sicuri?</div>
                <div class="faq-answer">Assolutamente. Utilizziamo crittografia standard del settore e misure di sicurezza per proteggere i tuoi dati. Le tue informazioni sui percorsi e le consegne sono archiviate in modo sicuro e non vengono mai condivise con terze parti.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Posso usare NaviBatch offline?</div>
                <div class="faq-answer">NaviBatch richiede una connessione internet per l'ottimizzazione dei percorsi e la navigazione in tempo reale. Tuttavia, una volta ottimizzato il tuo percorso, puoi utilizzare Apple Maps per la navigazione offline.</div>
            </div>
        </div>

        <div class="support-section">
            <h2>🚀 Iniziare</h2>
            <h3>Guida Rapida</h3>
            <ol>
                <li><strong>Scarica</strong> NaviBatch dall'App Store</li>
                <li><strong>Aggiungi Indirizzi</strong> digitando, incollando o importando da file</li>
                <li><strong>Ottimizza Percorso</strong> con il nostro algoritmo intelligente</li>
                <li><strong>Inizia Navigazione</strong> con navigazione batch a un clic</li>
                <li><strong>Traccia Progresso</strong> e cattura foto di prova di consegna</li>
            </ol>

            <h3>Funzionalità Pro</h3>
            <ul>
                <li>Indirizzi illimitati (versione gratuita limitata a 20)</li>
                <li>Navigazione batch a un clic (brevetto in attesa)</li>
                <li>Ricerca avanzata pacchi</li>
                <li>Supporto clienti prioritario</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>📱 Requisiti di Sistema</h2>
            <ul>
                <li>iOS 16.0 o successivo</li>
                <li>iPhone (ottimizzato per consegne professionali)</li>
                <li>Connessione internet per ottimizzazione percorsi</li>
                <li>Permesso servizi di localizzazione per navigazione accurata</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>🔗 Link Utili</h2>
            <ul>
                <li><a href="/privacy-it.html">Informativa sulla Privacy</a></li>
                <li><a href="/terms.html">Termini di Servizio</a></li>
                <li><a href="https://apps.apple.com/app/id6746371287">Scarica dall'App Store</a></li>
                <li><a href="/">Homepage NaviBatch</a></li>
            </ul>
        </div>
    </div>
</body>
</html>