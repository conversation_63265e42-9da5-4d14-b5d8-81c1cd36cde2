<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدعم - NaviBatch</title>
    <meta name="description" content="دعم NaviBatch - احصل على المساعدة في تخطيط المسارات والملاحة وتحسين التسليم.">
    <link rel="stylesheet" href="css/combined.min.css">
    <style>
        body {
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .support-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 120px 20px 60px;
            color: #1e293b;
        }

        .support-header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .support-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 163, 255, 0.05) 0%, rgba(96, 165, 250, 0.05) 100%);
            z-index: 1;
        }

        .support-header > * {
            position: relative;
            z-index: 2;
        }

        .support-header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00A3FF, #60A5FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        .support-header p {
            color: #64748b;
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .support-section {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            padding: 40px;
            border-radius: 16px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .support-section:hover {
            box-shadow: 0 8px 25px rgba(0, 163, 255, 0.1);
            border-color: #00A3FF;
        }

        .support-section h2 {
            color: #00A3FF;
            margin-bottom: 25px;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .support-section h3 {
            color: #1e40af;
            margin-top: 30px;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .support-section p {
            margin-bottom: 20px;
            color: #334155;
            font-size: 1.05rem;
            line-height: 1.7;
        }

        .support-section ul, .support-section ol {
            margin-bottom: 25px;
            padding-left: 25px;
        }

        .support-section li {
            margin-bottom: 12px;
            color: #334155;
            font-size: 1.05rem;
            line-height: 1.6;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .contact-method {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            padding: 30px;
            border-radius: 16px;
            border: 2px solid #e2e8f0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-method::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .contact-method:hover::before {
            left: 100%;
        }

        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 163, 255, 0.2);
            border-color: #00A3FF;
        }

        .contact-method i {
            font-size: 2rem;
            color: #00A3FF;
            margin-bottom: 20px;
            display: block;
        }

        .contact-method h3 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .contact-method p {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .contact-method a {
            color: #00A3FF;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 10px 20px;
            border: 2px solid #00A3FF;
            border-radius: 8px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .contact-method a:hover {
            background: #00A3FF;
            color: white;
            transform: scale(1.05);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            margin-bottom: 30px;
            color: #00A3FF;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 10px 20px;
            border-radius: 10px;
            background: rgba(0, 163, 255, 0.1);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #00A3FF;
            color: white;
            transform: translateX(-5px);
        }

        .language-switcher {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
        }

        .language-switcher a {
            color: #00A3FF;
            text-decoration: none;
            margin: 5px 10px;
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .language-switcher a:hover {
            background: rgba(0, 163, 255, 0.1);
            transform: translateY(-2px);
        }

        .language-switcher .current {
            color: #1e293b;
            font-weight: 700;
            background: #00A3FF;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px 10px;
            display: inline-block;
        }

        .faq-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 25px 0;
            transition: all 0.3s ease;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-item:hover {
            background: rgba(0, 163, 255, 0.02);
            border-radius: 10px;
            padding: 25px 20px;
            margin: 0 -20px;
        }

        .faq-question {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 1.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faq-question::before {
            content: '❓';
            font-size: 1.1rem;
        }

        .faq-answer {
            color: #64748b;
            line-height: 1.7;
            font-size: 1.05rem;
            padding-left: 30px;
        }

        @media (max-width: 768px) {
            .support-container {
                padding: 100px 15px 40px;
            }

            .support-header h1 {
                font-size: 2.2rem;
            }

            .contact-methods {
                grid-template-columns: 1fr;
            }

            .language-switcher a, .language-switcher .current {
                margin: 3px 5px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="support-container">
        <a href="#" class="back-link" onclick="window.location.href = window.location.origin + '/'; return false;">← العودة إلى NaviBatch</a>

        <div class="language-switcher">
            <a href="/support.html">English</a>
            <a href="/support-zh.html">中文</a>
            <a href="/support-ja.html">日本語</a>
            <a href="/support-fr.html">Français</a>
            <a href="/support-de.html">Deutsch</a>
            <a href="/support-es.html">Español</a>
            <a href="/support-it.html">Italiano</a>
            <span class="current">العربية</span>
            <a href="/support-ko.html">한국어</a>
            <a href="/support-pt.html">Português</a>
            <a href="/support-ru.html">Русский</a>
            <a href="/support-nl.html">Nederlands</a>
        </div>

        <div class="support-header">
            <h1>دعم NaviBatch</h1>
            <p>احصل على المساعدة في تخطيط المسارات والملاحة وتحسين التسليم</p>
        </div>

        <div class="support-section">
            <h2>📞 اتصل بالدعم</h2>
            <p>فريق الدعم لدينا هنا لمساعدتك في أي أسئلة أو مشاكل قد تواجهها مع NaviBatch.</p>

            <div class="contact-methods">
                <div class="contact-method">
                    <i class="fas fa-envelope"></i>
                    <h3>الدعم العام</h3>
                    <p>للأسئلة العامة والاستفسارات</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-bug"></i>
                    <h3>المشاكل التقنية</h3>
                    <p>الإبلاغ عن الأخطاء والمشاكل التقنية</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-lightbulb"></i>
                    <h3>طلبات الميزات</h3>
                    <p>اقتراح ميزات جديدة وتحسينات</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="support-section">
            <h2>❓ الأسئلة الشائعة</h2>

            <div class="faq-item">
                <div class="faq-question">كيف يمكنني تحسين مسارات التسليم الخاصة بي؟</div>
                <div class="faq-answer">ببساطة أضف عناوين التسليم الخاصة بك إلى NaviBatch، وسيقوم خوارزميتنا الذكية بحساب المسار الأكثر كفاءة تلقائياً لتوفير الوقت والوقود.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">هل يمكنني استيراد العناوين من ملف؟</div>
                <div class="faq-answer">نعم! يدعم NaviBatch استيراد العناوين من ملفات CSV وجداول بيانات Excel والملفات النصية. يمكنك أيضاً لصق عدة عناوين في مرة واحدة.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">كيف تعمل ميزة الملاحة المجمعة؟</div>
                <div class="faq-answer">تقنية الملاحة المجمعة المعلقة براءة الاختراع تسمح لك بالتنقل إلى عدة عناوين بنقرة واحدة فقط، مع التبديل التلقائي بين الوجهات أثناء إكمال كل تسليم.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">هل بياناتي آمنة؟</div>
                <div class="faq-answer">بالتأكيد. نحن نستخدم التشفير المعياري في الصناعة وتدابير الأمان لحماية بياناتك. معلومات المسارات والتسليم الخاصة بك مخزنة بأمان ولا تُشارك أبداً مع أطراف ثالثة.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">هل يمكنني استخدام NaviBatch بدون اتصال بالإنترنت؟</div>
                <div class="faq-answer">يتطلب NaviBatch اتصالاً بالإنترنت لتحسين المسارات والملاحة في الوقت الفعلي. ومع ذلك، بمجرد تحسين مسارك، يمكنك استخدام خرائط Apple للملاحة بدون اتصال.</div>
            </div>
        </div>

        <div class="support-section">
            <h2>🚀 البدء</h2>
            <h3>دليل البدء السريع</h3>
            <ol>
                <li><strong>تحميل</strong> NaviBatch من متجر التطبيقات</li>
                <li><strong>إضافة العناوين</strong> بالكتابة أو اللصق أو الاستيراد من الملفات</li>
                <li><strong>تحسين المسار</strong> باستخدام خوارزميتنا الذكية</li>
                <li><strong>بدء الملاحة</strong> مع الملاحة المجمعة بنقرة واحدة</li>
                <li><strong>تتبع التقدم</strong> والتقاط صور إثبات التسليم</li>
            </ol>

            <h3>ميزات برو</h3>
            <ul>
                <li>عناوين غير محدودة (النسخة المجانية محدودة بـ 20)</li>
                <li>الملاحة المجمعة بنقرة واحدة (براءة اختراع معلقة)</li>
                <li>باحث الطرود المتقدم</li>
                <li>دعم العملاء ذو الأولوية</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>📱 متطلبات النظام</h2>
            <ul>
                <li>iOS 16.0 أو أحدث</li>
                <li>iPhone (محسن للتسليم المهني)</li>
                <li>اتصال بالإنترنت لتحسين المسارات</li>
                <li>إذن خدمات الموقع للملاحة الدقيقة</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>🔗 روابط مفيدة</h2>
            <ul>
                <li><a href="/privacy-ar.html">سياسة الخصوصية</a></li>
                <li><a href="/terms.html">شروط الخدمة</a></li>
                <li><a href="https://apps.apple.com/app/id6746371287">تحميل من متجر التطبيقات</a></li>
                <li><a href="/">الصفحة الرئيسية NaviBatch</a></li>
            </ul>
        </div>
    </div>
</body>
</html>