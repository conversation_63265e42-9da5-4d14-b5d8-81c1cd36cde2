window.addEventListener('error',function(e){console.warn('Global error caught:',e.error)});window.addEventListener('unhandledrejection',function(e){console.warn('Unhandled promise rejection:',e.reason)});window.addEventListener('load',function(){setTimeout(function(){document.body.classList.add('loaded')},200);if('caches'in window){caches.keys().then(function(cacheNames){return Promise.all(cacheNames.map(function(cacheName){if(cacheName.startsWith('navibatch-v1.1.1')||cacheName.startsWith('navibatch-v1.1.0')){console.log('Deleting old cache:',cacheName);return caches.delete(cacheName)}}))})}if('serviceWorker'in navigator&&location.protocol==='https:'){navigator.serviceWorker.register('/sw.js').then(function(registration){console.log('Service Worker registered successfully')}).catch(function(error){console.warn('Service Worker registration failed:',error)})}});document.addEventListener('DOMContentLoaded',function(){const urlParams=new URLSearchParams(window.location.search);if(urlParams.get('success')==='1'){alert('Your message has been sent successfully! We will respond as soon as possible.');window.history.replaceState({},document.title,window.location.pathname)}setupImageUpload();const inquiryTypeSelect=document.getElementById('inquiry-type');if(inquiryTypeSelect){inquiryTypeSelect.addEventListener('change',toggleImageUpload)}setupLogoClickHandler();initMobileMenu();initScrollEffects();initBackToTop()});function setupLogoClickHandler(){const logoLink=document.querySelector('.logo a');if(logoLink){logoLink.addEventListener('click',function(e){const isHomePage=window.location.pathname==='/'||window.location.pathname==='/index.html'||window.location.pathname==='';if(isHomePage){e.preventDefault();window.scrollTo({top:0,behavior:'smooth'})}})}}function initMobileMenu(){const mobileMenuToggle=document.querySelector('.mobile-menu-toggle');let mobileMenu=document.querySelector('.mobile-menu');const body=document.body;if(mobileMenuToggle&&!mobileMenu&&window.innerWidth<=992){if(document.querySelector('.mobile-menu')){return}const newMobileMenu=document.createElement('div');newMobileMenu.className='mobile-menu';const closeButton=document.createElement('button');closeButton.className='mobile-menu-close';closeButton.innerHTML='<i class="fas fa-times"></i>';const nav=document.createElement('nav');nav.className='mobile-menu-nav';const mainNav=document.querySelector('.main-nav');if(mainNav){const navItems=mainNav.cloneNode(true);navItems.className='mobile-menu-nav';nav.appendChild(navItems)}const ctaButtons=document.querySelector('.cta-buttons');if(ctaButtons){const ctaClone=ctaButtons.cloneNode(true);ctaClone.className='mobile-menu-cta';nav.appendChild(ctaClone)}newMobileMenu.appendChild(closeButton);newMobileMenu.appendChild(nav);document.body.insertBefore(newMobileMenu,document.body.firstChild);mobileMenu=newMobileMenu;mobileMenuToggle.addEventListener('click',function(){mobileMenu.classList.add('active');body.style.overflow='hidden'});closeButton.addEventListener('click',function(){mobileMenu.classList.remove('active');body.style.overflow=''});mobileMenu.addEventListener('click',function(e){if(e.target.tagName==='A'){mobileMenu.classList.remove('active');body.style.overflow=''}})}}function initScrollEffects(){const scrollElements=document.querySelectorAll('.feature-card, .section-header, .case-study-card');const elementInView=(el,scrollOffset=100)=>{const elementTop=el.getBoundingClientRect().top;return elementTop<=(window.innerHeight||document.documentElement.clientHeight)-scrollOffset};const displayScrollElement=(element)=>{element.classList.add('scrolled')};const hideScrollElement=(element)=>{element.classList.remove('scrolled')};const handleScrollAnimation=()=>{scrollElements.forEach((el)=>{if(elementInView(el,100)){displayScrollElement(el)}else{hideScrollElement(el)}})};window.addEventListener('scroll',()=>{handleScrollAnimation()});handleScrollAnimation()}function initBackToTop(){const backToTopBtn=document.getElementById('back-to-top-btn');if(backToTopBtn){window.addEventListener('scroll',()=>{if(window.pageYOffset>300){backToTopBtn.classList.add('visible')}else{backToTopBtn.classList.remove('visible')}});backToTopBtn.addEventListener('click',(e)=>{e.preventDefault();window.scrollTo({top:0,behavior:'smooth'})})}}document.querySelectorAll('a[href^="#"]').forEach(anchor=>{anchor.addEventListener('click',function(e){if(this.hasAttribute('data-case-study')){return}e.preventDefault();const targetId=this.getAttribute('href');if(targetId==='#')return;const targetElement=document.querySelector(targetId);if(targetElement){const activeMobileMenu=document.querySelector('.mobile-menu.active');if(activeMobileMenu){activeMobileMenu.classList.remove('active');document.body.style.overflow=''}window.scrollTo({top:targetElement.offsetTop-80,behavior:'smooth'})}})});let uploadedImages=[];function setupImageUpload(){const imageUpload=document.getElementById('image-upload');const imagePreview=document.getElementById('image-preview');if(imageUpload){imageUpload.addEventListener('change',handleImageUpload)}}function handleImageUpload(event){const files=Array.from(event.target.files);const maxFiles=3;const maxSize=10*1024*1024;if(uploadedImages.length+files.length>maxFiles){alert(`You can only upload up to ${maxFiles} images.`);return}files.forEach(file=>{if(file.size>maxSize){alert(`File ${file.name} is too large. Maximum size is 10MB.`);return}if(!file.type.startsWith('image/')){alert(`File ${file.name} is not an image.`);return}const reader=new FileReader();reader.onload=function(e){const imageData={file:file,name:file.name,size:file.size,dataUrl:e.target.result};uploadedImages.push(imageData);displayImagePreview(imageData)};reader.readAsDataURL(file)})}function displayImagePreview(imageData){const imagePreview=document.getElementById('image-preview');const previewItem=document.createElement('div');previewItem.className='image-preview-item';previewItem.innerHTML=`<img src="${imageData.dataUrl}" alt="${imageData.name}"><div class="image-info"><span class="image-name">${imageData.name}</span><span class="image-size">${formatFileSize(imageData.size)}</span></div><button type="button" class="remove-image" onclick="removeImage('${imageData.name}')">×</button>`;imagePreview.appendChild(previewItem)}function removeImage(imageName){uploadedImages=uploadedImages.filter(img=>img.name!==imageName);const imagePreview=document.getElementById('image-preview');const previewItems=imagePreview.querySelectorAll('.image-preview-item');previewItems.forEach(item=>{const nameSpan=item.querySelector('.image-name');if(nameSpan&&nameSpan.textContent===imageName){item.remove()}})}function clearUploadedImages(){uploadedImages=[];const imagePreview=document.getElementById('image-preview');if(imagePreview){imagePreview.innerHTML=''}}function formatFileSize(bytes){if(bytes===0)return'0 Bytes';const k=1024;const sizes=['Bytes','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+' '+sizes[i]}function toggleImageUpload(){const inquiryType=document.getElementById('inquiry-type').value;const imageUploadGroup=document.getElementById('image-upload-group');if(inquiryType==='technical'){imageUploadGroup.style.display='block'}else{imageUploadGroup.style.display='none';clearUploadedImages()}}
