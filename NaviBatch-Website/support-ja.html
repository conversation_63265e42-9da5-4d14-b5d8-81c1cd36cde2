<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>サポート - NaviBatch</title>
    <meta name="description" content="NaviBatch サポート - ルート計画、ナビゲーション、配送最適化に関するヘルプを取得します。">
    <link rel="stylesheet" href="css/combined.min.css">
    <style>
        body {
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .support-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 120px 20px 60px;
            color: #1e293b;
            line-height: 1.6;
        }

        .support-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .support-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #00A3FF, #60A5FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .support-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .support-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #00A3FF;
        }

        .support-section h2 {
            color: #00A3FF;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .support-section h3 {
            color: #1e40af;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .support-section p {
            margin-bottom: 15px;
            color: #334155;
            font-size: 1rem;
            line-height: 1.7;
        }

        .support-section ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .support-section li {
            margin-bottom: 10px;
            color: #334155;
            font-size: 1rem;
            line-height: 1.6;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .contact-method {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }

        .contact-method i {
            font-size: 2rem;
            color: #00A3FF;
            margin-bottom: 15px;
        }

        .contact-method h3 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .contact-method p {
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .contact-method a {
            color: #00A3FF;
            text-decoration: none;
            font-weight: 500;
        }

        .contact-method a:hover {
            text-decoration: underline;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #00A3FF;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .language-switcher {
            text-align: center;
            margin-bottom: 20px;
        }

        .language-switcher a {
            color: #00A3FF;
            text-decoration: none;
            margin: 0 10px;
            font-weight: 500;
        }

        .language-switcher a:hover {
            text-decoration: underline;
        }

        .language-switcher .current {
            color: #1e293b;
            font-weight: 600;
        }

        .faq-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 0;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-question {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .faq-answer {
            color: #64748b;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="support-container">
        <a href="#" class="back-link" onclick="window.location.href = window.location.origin + '/'; return false;">← NaviBatch に戻る</a>

        <div class="language-switcher">
            <a href="/support.html">English</a>
            <a href="/support-zh.html">中文</a>
            <span class="current">日本語</span>
            <a href="/support-fr.html">Français</a>
            <a href="/support-de.html">Deutsch</a>
            <a href="/support-es.html">Español</a>
            <a href="/support-it.html">Italiano</a>
            <a href="/support-ar.html">العربية</a>
            <a href="/support-ko.html">한국어</a>
            <a href="/support-pt.html">Português</a>
            <a href="/support-ru.html">Русский</a>
            <a href="/support-nl.html">Nederlands</a>
        </div>

        <div class="support-header">
            <h1>NaviBatch サポート</h1>
            <p>ルート計画、ナビゲーション、配送最適化に関するヘルプを取得します</p>
        </div>

        <div class="support-section">
            <h2>📞 サポートに連絡</h2>
            <p>私たちのサポートチームは、NaviBatch に関するご質問や問題についてお手伝いいたします。</p>
            
            <div class="contact-methods">
                <div class="contact-method">
                    <i class="fas fa-envelope"></i>
                    <h3>一般サポート</h3>
                    <p>一般的な質問とお問い合わせ</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-bug"></i>
                    <h3>技術的な問題</h3>
                    <p>バグや技術的な問題の報告</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-lightbulb"></i>
                    <h3>機能リクエスト</h3>
                    <p>新機能や改善の提案</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="support-section">
            <h2>❓ よくある質問</h2>
            
            <div class="faq-item">
                <div class="faq-question">配送ルートを最適化するにはどうすればよいですか？</div>
                <div class="faq-answer">配送先住所を NaviBatch に追加するだけで、インテリジェントアルゴリズムが自動的に最も効率的なルートを計算し、時間と燃料を節約します。</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">ファイルから住所をインポートできますか？</div>
                <div class="faq-answer">はい！NaviBatch は CSV ファイル、Excel スプレッドシート、テキストファイルからの住所インポートをサポートしています。複数の住所を一度に貼り付けることもできます。</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">バッチナビゲーション機能はどのように動作しますか？</div>
                <div class="faq-answer">特許出願中のバッチナビゲーション技術により、ワンクリックで複数の住所にナビゲートでき、各配送を完了すると自動的に次の目的地に切り替わります。</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">私のデータは安全ですか？</div>
                <div class="faq-answer">絶対に安全です。業界標準の暗号化とセキュリティ対策を使用してデータを保護しています。ルートと配送情報は安全に保存され、第三者と共有されることはありません。</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">NaviBatch はオフラインで使用できますか？</div>
                <div class="faq-answer">NaviBatch はルート最適化とリアルタイムナビゲーションにインターネット接続が必要です。ただし、ルートが最適化されると、Apple マップを使用してオフラインナビゲーションを行うことができます。</div>
            </div>
        </div>

        <div class="support-section">
            <h2>🚀 はじめに</h2>
            <h3>クイックスタートガイド</h3>
            <ol>
                <li><strong>ダウンロード</strong> App Store から NaviBatch をダウンロード</li>
                <li><strong>住所を追加</strong> 入力、貼り付け、またはファイルからのインポートで住所を追加</li>
                <li><strong>ルートを最適化</strong> インテリジェントアルゴリズムでルートを最適化</li>
                <li><strong>ナビゲーション開始</strong> ワンクリックバッチナビゲーション</li>
                <li><strong>進捗を追跡</strong> 配送証明写真を撮影</li>
            </ol>
            
            <h3>プロ機能</h3>
            <ul>
                <li>無制限の住所（無料版は20個まで）</li>
                <li>ワンクリックバッチナビゲーション（特許出願中）</li>
                <li>高度なパッケージファインダー</li>
                <li>優先カスタマーサポート</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>📱 システム要件</h2>
            <ul>
                <li>iOS 16.0 以降</li>
                <li>iPhone（プロフェッショナル配送用に最適化）</li>
                <li>ルート最適化にはインターネット接続が必要</li>
                <li>正確なナビゲーションには位置情報サービスの許可が必要</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>🔗 便利なリンク</h2>
            <ul>
                <li><a href="/privacy-ja.html">プライバシーポリシー</a></li>
                <li><a href="/terms.html">利用規約</a></li>
                <li><a href="https://apps.apple.com/app/id6746371287">App Store からダウンロード</a></li>
                <li><a href="/">NaviBatch ホームページ</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
