<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suporte - NaviBatch</title>
    <meta name="description" content="Suporte NaviBatch - Obtenha ajuda com planejamento de rotas, navegação e otimização de entregas.">
    <link rel="stylesheet" href="css/combined.min.css">
    <style>
        body {
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .support-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 120px 20px 60px;
            color: #1e293b;
        }

        .support-header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .support-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 163, 255, 0.05) 0%, rgba(96, 165, 250, 0.05) 100%);
            z-index: 1;
        }

        .support-header > * {
            position: relative;
            z-index: 2;
        }

        .support-header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00A3FF, #60A5FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        .support-header p {
            color: #64748b;
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .support-section {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            padding: 40px;
            border-radius: 16px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .support-section:hover {
            box-shadow: 0 8px 25px rgba(0, 163, 255, 0.1);
            border-color: #00A3FF;
        }

        .support-section h2 {
            color: #00A3FF;
            margin-bottom: 25px;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .support-section h3 {
            color: #1e40af;
            margin-top: 30px;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .support-section p {
            margin-bottom: 20px;
            color: #334155;
            font-size: 1.05rem;
            line-height: 1.7;
        }

        .support-section ul, .support-section ol {
            margin-bottom: 25px;
            padding-left: 25px;
        }

        .support-section li {
            margin-bottom: 12px;
            color: #334155;
            font-size: 1.05rem;
            line-height: 1.6;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .contact-method {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            padding: 30px;
            border-radius: 16px;
            border: 2px solid #e2e8f0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-method::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 163, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .contact-method:hover::before {
            left: 100%;
        }

        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 163, 255, 0.2);
            border-color: #00A3FF;
        }

        .contact-method i {
            font-size: 2rem;
            color: #00A3FF;
            margin-bottom: 20px;
            display: block;
        }

        .contact-method h3 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .contact-method p {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .contact-method a {
            color: #00A3FF;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 10px 20px;
            border: 2px solid #00A3FF;
            border-radius: 8px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .contact-method a:hover {
            background: #00A3FF;
            color: white;
            transform: scale(1.05);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            margin-bottom: 30px;
            color: #00A3FF;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 10px 20px;
            border-radius: 10px;
            background: rgba(0, 163, 255, 0.1);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #00A3FF;
            color: white;
            transform: translateX(-5px);
        }

        .language-switcher {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
        }

        .language-switcher a {
            color: #00A3FF;
            text-decoration: none;
            margin: 5px 10px;
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .language-switcher a:hover {
            background: rgba(0, 163, 255, 0.1);
            transform: translateY(-2px);
        }

        .language-switcher .current {
            color: #1e293b;
            font-weight: 700;
            background: #00A3FF;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px 10px;
            display: inline-block;
        }

        .faq-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 25px 0;
            transition: all 0.3s ease;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-item:hover {
            background: rgba(0, 163, 255, 0.02);
            border-radius: 10px;
            padding: 25px 20px;
            margin: 0 -20px;
        }

        .faq-question {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 1.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faq-question::before {
            content: '❓';
            font-size: 1.1rem;
        }

        .faq-answer {
            color: #64748b;
            line-height: 1.7;
            font-size: 1.05rem;
            padding-left: 30px;
        }

        @media (max-width: 768px) {
            .support-container {
                padding: 100px 15px 40px;
            }

            .support-header h1 {
                font-size: 2.2rem;
            }

            .contact-methods {
                grid-template-columns: 1fr;
            }

            .language-switcher a, .language-switcher .current {
                margin: 3px 5px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="support-container">
        <a href="#" class="back-link" onclick="window.location.href = window.location.origin + '/'; return false;">← Voltar ao NaviBatch</a>

        <div class="language-switcher">
            <a href="/support.html">English</a>
            <a href="/support-zh.html">中文</a>
            <a href="/support-ja.html">日本語</a>
            <a href="/support-fr.html">Français</a>
            <a href="/support-de.html">Deutsch</a>
            <a href="/support-es.html">Español</a>
            <a href="/support-it.html">Italiano</a>
            <a href="/support-ar.html">العربية</a>
            <a href="/support-ko.html">한국어</a>
            <span class="current">Português</span>
            <a href="/support-ru.html">Русский</a>
            <a href="/support-nl.html">Nederlands</a>
        </div>

        <div class="support-header">
            <h1>Suporte NaviBatch</h1>
            <p>Obtenha ajuda com planejamento de rotas, navegação e otimização de entregas</p>
        </div>

        <div class="support-section">
            <h2>📞 Contatar Suporte</h2>
            <p>Nossa equipe de suporte está aqui para ajudá-lo com qualquer dúvida ou problema que você possa ter com o NaviBatch.</p>

            <div class="contact-methods">
                <div class="contact-method">
                    <i class="fas fa-envelope"></i>
                    <h3>Suporte Geral</h3>
                    <p>Para perguntas gerais e consultas</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-bug"></i>
                    <h3>Problemas Técnicos</h3>
                    <p>Relatar bugs e problemas técnicos</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-lightbulb"></i>
                    <h3>Solicitações de Recursos</h3>
                    <p>Sugerir novos recursos e melhorias</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="support-section">
            <h2>❓ Perguntas Frequentes</h2>

            <div class="faq-item">
                <div class="faq-question">Como otimizo minhas rotas de entrega?</div>
                <div class="faq-answer">Simplesmente adicione seus endereços de entrega ao NaviBatch, e nosso algoritmo inteligente calculará automaticamente a rota mais eficiente para economizar tempo e combustível.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Posso importar endereços de um arquivo?</div>
                <div class="faq-answer">Sim! O NaviBatch suporta importação de endereços de arquivos CSV, planilhas Excel e arquivos de texto. Você também pode colar múltiplos endereços de uma vez.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Como funciona o recurso de navegação em lote?</div>
                <div class="faq-answer">Nossa tecnologia de navegação em lote com patente pendente permite navegar para múltiplos endereços com apenas um clique, alternando automaticamente entre destinos conforme você completa cada entrega.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Meus dados estão seguros?</div>
                <div class="faq-answer">Absolutamente. Usamos criptografia padrão da indústria e medidas de segurança para proteger seus dados. Suas informações de rotas e entregas são armazenadas com segurança e nunca compartilhadas com terceiros.</div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">Posso usar o NaviBatch offline?</div>
                <div class="faq-answer">O NaviBatch requer uma conexão com a internet para otimização de rotas e navegação em tempo real. No entanto, uma vez que sua rota esteja otimizada, você pode usar o Apple Maps para navegação offline.</div>
            </div>
        </div>

        <div class="support-section">
            <h2>🚀 Começar</h2>
            <h3>Guia de Início Rápido</h3>
            <ol>
                <li><strong>Baixar</strong> NaviBatch da App Store</li>
                <li><strong>Adicionar Endereços</strong> digitando, colando ou importando de arquivos</li>
                <li><strong>Otimizar Rota</strong> com nosso algoritmo inteligente</li>
                <li><strong>Iniciar Navegação</strong> com navegação em lote de um clique</li>
                <li><strong>Acompanhar Progresso</strong> e capturar fotos de prova de entrega</li>
            </ol>

            <h3>Recursos Pro</h3>
            <ul>
                <li>Endereços ilimitados (versão gratuita limitada a 20)</li>
                <li>Navegação em lote de um clique (patente pendente)</li>
                <li>Localizador avançado de pacotes</li>
                <li>Suporte ao cliente prioritário</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>📱 Requisitos do Sistema</h2>
            <ul>
                <li>iOS 16.0 ou posterior</li>
                <li>iPhone (otimizado para entrega profissional)</li>
                <li>Conexão com internet para otimização de rotas</li>
                <li>Permissão de serviços de localização para navegação precisa</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>🔗 Links Úteis</h2>
            <ul>
                <li><a href="/privacy-pt.html">Política de Privacidade</a></li>
                <li><a href="/terms.html">Termos de Serviço</a></li>
                <li><a href="https://apps.apple.com/app/id6746371287">Baixar da App Store</a></li>
                <li><a href="/">Página Inicial NaviBatch</a></li>
            </ul>
        </div>
    </div>
</body>
</html>