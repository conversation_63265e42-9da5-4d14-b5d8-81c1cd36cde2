# 根页面特殊处理 - 地理位置检测需要立即更新
/
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  Content-Type: text/html; charset=utf-8

# 主页面缓存控制 - 禁用缓存确保地理位置功能及时更新
/*.html
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  Content-Type: text/html; charset=utf-8

# HTML文件缓存控制 - 支持页面需要及时更新
/support*.html
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  Content-Type: text/html; charset=utf-8

# 为CSS文件设置正确的MIME类型和缓存
/css/*
  Content-Type: text/css
  Cache-Control: public, max-age=31536000, immutable

# 为JavaScript文件设置正确的MIME类型和缓存
/js/*
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

# 为图片设置正确的缓存控制
/images/*
  Cache-Control: public, max-age=31536000, immutable

# 优化图片缓存
/images/optimized/*
  Cache-Control: public, max-age=31536000, immutable

# WebP和AVIF图片特殊缓存
*.webp
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/webp

*.avif
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/avif

# SVG图片缓存
*.svg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/svg+xml
