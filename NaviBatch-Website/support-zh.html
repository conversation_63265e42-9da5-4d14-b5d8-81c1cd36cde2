<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支持 - NaviBatch</title>
    <meta name="description" content="NaviBatch 支持 - 获取路线规划、导航和配送优化方面的帮助。">
    <link rel="stylesheet" href="css/combined.min.css">
    <style>
        body {
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .support-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 120px 20px 60px;
            color: #1e293b;
            line-height: 1.6;
        }

        .support-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .support-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #00A3FF, #60A5FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .support-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .support-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #00A3FF;
        }

        .support-section h2 {
            color: #00A3FF;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .support-section h3 {
            color: #1e40af;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .support-section p {
            margin-bottom: 15px;
            color: #334155;
            font-size: 1rem;
            line-height: 1.7;
        }

        .support-section ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .support-section li {
            margin-bottom: 10px;
            color: #334155;
            font-size: 1rem;
            line-height: 1.6;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .contact-method {
            background: white;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            text-align: left;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .contact-method:hover {
            box-shadow: 0 4px 12px rgba(0, 163, 255, 0.15);
            border-color: #00A3FF;
        }

        .contact-method i {
            font-size: 1.5rem;
            color: #00A3FF;
            margin-bottom: 15px;
            display: block;
        }

        .contact-method h3 {
            color: #1e293b;
            margin-bottom: 8px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .contact-method p {
            color: #64748b;
            font-size: 0.95rem;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .contact-method a {
            color: #00A3FF;
            text-decoration: none;
            font-weight: 500;
            font-size: 1rem;
        }

        .contact-method a:hover {
            text-decoration: underline;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #00A3FF;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .language-switcher {
            text-align: center;
            margin-bottom: 20px;
        }

        .language-switcher a {
            color: #00A3FF;
            text-decoration: none;
            margin: 0 10px;
            font-weight: 500;
        }

        .language-switcher a:hover {
            text-decoration: underline;
        }

        .language-switcher .current {
            color: #1e293b;
            font-weight: 600;
        }

        .faq-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 0;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-question {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .faq-answer {
            color: #64748b;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="support-container">
        <a href="#" class="back-link" onclick="window.location.href = window.location.origin + '/'; return false;">← 返回 NaviBatch</a>

        <div class="language-switcher">
            <a href="/support.html">English</a>
            <span class="current">中文</span>
            <a href="/support-ja.html">日本語</a>
            <a href="/support-fr.html">Français</a>
            <a href="/support-de.html">Deutsch</a>
            <a href="/support-es.html">Español</a>
            <a href="/support-it.html">Italiano</a>
            <a href="/support-ar.html">العربية</a>
            <a href="/support-ko.html">한국어</a>
            <a href="/support-pt.html">Português</a>
            <a href="/support-ru.html">Русский</a>
            <a href="/support-nl.html">Nederlands</a>
        </div>

        <div class="support-header">
            <h1>NaviBatch 支持</h1>
            <p>获取路线规划、导航和配送优化方面的帮助</p>
        </div>

        <div class="support-section">
            <h2>📞 联系支持</h2>
            <p>我们的支持团队随时为您解答关于 NaviBatch 的任何问题。</p>

            <div class="contact-methods">
                <div class="contact-method">
                    <i class="fas fa-envelope"></i>
                    <h3>一般支持</h3>
                    <p>一般问题和咨询</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-bug"></i>
                    <h3>技术问题</h3>
                    <p>报告错误和技术问题</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-method">
                    <i class="fas fa-lightbulb"></i>
                    <h3>功能建议</h3>
                    <p>建议新功能和改进</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
            </div>
        </div>

        <div class="support-section">
            <h2>❓ 常见问题</h2>

            <div class="faq-item">
                <div class="faq-question">如何优化我的配送路线？</div>
                <div class="faq-answer">只需将您的配送地址添加到 NaviBatch，我们的智能算法将自动计算最高效的路线，为您节省时间和燃料。</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">我可以从文件导入地址吗？</div>
                <div class="faq-answer">可以！NaviBatch 支持从 CSV 文件、Excel 电子表格和文本文件导入地址。您也可以一次性粘贴多个地址。</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">批量导航功能是如何工作的？</div>
                <div class="faq-answer">我们的专利申请中的批量导航技术允许您只需一次点击就能导航到多个地址，在您完成每次配送时自动切换到下一个目的地。</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">我的数据安全吗？</div>
                <div class="faq-answer">绝对安全。我们使用行业标准的加密和安全措施来保护您的数据。您的路线和配送信息都安全存储，绝不与第三方共享。</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">NaviBatch 可以离线使用吗？</div>
                <div class="faq-answer">NaviBatch 需要互联网连接来进行路线优化和实时导航。但是，一旦您的路线优化完成，您可以使用苹果地图进行离线导航。</div>
            </div>
        </div>

        <div class="support-section">
            <h2>🚀 快速入门</h2>
            <h3>快速入门指南</h3>
            <ol>
                <li><strong>下载</strong> 从 App Store 下载 NaviBatch</li>
                <li><strong>添加地址</strong> 通过输入、粘贴或导入文件添加地址</li>
                <li><strong>优化路线</strong> 使用我们的智能算法优化路线</li>
                <li><strong>开始导航</strong> 一键批量导航</li>
                <li><strong>跟踪进度</strong> 拍摄配送证明照片</li>
            </ol>

            <h3>专业版功能</h3>
            <ul>
                <li>无限地址（免费版限制 20 个）</li>
                <li>一键批量导航（专利申请中）</li>
                <li>高级包裹查找器</li>
                <li>优先客户支持</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>📱 系统要求</h2>
            <ul>
                <li>iOS 16.0 或更高版本</li>
                <li>iPhone（专为专业配送优化）</li>
                <li>路线优化需要互联网连接</li>
                <li>准确导航需要位置服务权限</li>
            </ul>
        </div>

        <div class="support-section">
            <h2>🔗 有用链接</h2>
            <ul>
                <li><a href="/privacy-zh.html">隐私政策</a></li>
                <li><a href="/terms.html">服务条款</a></li>
                <li><a href="https://apps.apple.com/app/id6746371287">从 App Store 下载</a></li>
                <li><a href="/">NaviBatch 主页</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
