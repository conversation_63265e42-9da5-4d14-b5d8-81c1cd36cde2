<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STOP Wasting Time! 14 Stops in 1 Click - NaviBatch</title>
    <meta name="description" content="Delivery drivers: Stop adding stops one by one! NaviBatch pushes 14 stops to Apple Maps instantly. 60x faster. Free trial.">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/favicon-32x32.png">

    <!-- Critical CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            overflow-x: hidden;
        }

        /* Attention-Grabbing Hero */
        .hero {
            min-height: 100vh;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFD23F 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="%23000" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="%23000" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.1; }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 900px;
            padding: 0 20px;
        }

        .shock-badge {
            background: #FF0000;
            color: #fff;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 900;
            font-size: 14px;
            letter-spacing: 1px;
            margin-bottom: 20px;
            display: inline-block;
            animation: shake 2s infinite;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        .hero h1 {
            font-size: 4.5rem;
            font-weight: 900;
            line-height: 0.9;
            margin-bottom: 30px;
            color: #000;
            text-shadow: 2px 2px 0px #fff;
            animation: slideInUp 1s ease-out;
        }

        .hero h1 .highlight {
            color: #FF0000;
            text-shadow: 2px 2px 0px #fff;
        }

        .pain-point {
            font-size: 1.8rem;
            font-weight: 700;
            color: #000;
            margin-bottom: 20px;
            animation: slideInUp 1s ease-out 0.2s both;
        }

        .solution {
            font-size: 2.2rem;
            font-weight: 900;
            color: #000;
            margin-bottom: 40px;
            animation: slideInUp 1s ease-out 0.4s both;
        }

        .cta-button {
            background: #FF0000;
            color: #fff;
            padding: 25px 50px;
            font-size: 1.5rem;
            font-weight: 900;
            border: none;
            border-radius: 15px;
            text-decoration: none;
            display: inline-block;
            margin: 20px 10px;
            box-shadow: 0 10px 30px rgba(255, 0, 0, 0.4);
            transition: all 0.3s ease;
            animation: slideInUp 1s ease-out 0.6s both;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(255, 0, 0, 0.6);
        }

        .urgency {
            background: rgba(0, 0, 0, 0.8);
            color: #FFD23F;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: 700;
            margin-top: 30px;
            animation: slideInUp 1s ease-out 0.8s both;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Mobile */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .pain-point {
                font-size: 1.3rem;
            }

            .solution {
                font-size: 1.6rem;
            }

            .cta-button {
                padding: 20px 30px;
                font-size: 1.2rem;
                width: 90%;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Shock Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="shock-badge">
                🚨 DELIVERY DRIVERS: STOP WASTING TIME!
            </div>

            <h1>
                Adding Stops <span class="highlight">One by One</span><br>
                is <span class="highlight">KILLING</span> Your Productivity!
            </h1>

            <p class="pain-point">
                While you waste 60+ seconds manually adding each stop...
            </p>

            <p class="solution">
                NaviBatch does ALL 14 stops in 1 SECOND! 💥
            </p>

            <a href="https://apps.apple.com/app/id6746371287" class="cta-button" target="_blank">
                📱 GET IT FREE NOW - 14 DAY TRIAL
            </a>

            <div class="urgency">
                ⏰ Join 50,000+ drivers saving 2+ hours daily
            </div>
        </div>
    </section>

    <!-- Shocking Comparison -->
    <section style="background: #000; padding: 60px 0; text-align: center;">
        <div style="max-width: 1000px; margin: 0 auto; padding: 0 20px;">
            <h2 style="font-size: 3rem; color: #FF0000; margin-bottom: 40px; font-weight: 900;">
                STOP THE MADNESS! 🤯
            </h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 50px;">
                <!-- Old Way -->
                <div style="background: linear-gradient(135deg, #8B0000, #FF0000); padding: 30px; border-radius: 15px; border: 3px solid #FF0000;">
                    <h3 style="color: #fff; font-size: 1.8rem; margin-bottom: 20px;">😡 OLD PAINFUL WAY</h3>
                    <div style="background: rgba(0,0,0,0.5); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <p style="color: #fff; font-size: 1.1rem; line-height: 1.6;">
                            ❌ Open Apple Maps<br>
                            ❌ Add first address<br>
                            ❌ Tap "Add Stop"<br>
                            ❌ Repeat 13 MORE times<br>
                            ❌ Manually reorder stops<br>
                        </p>
                    </div>
                    <div style="background: #FF0000; color: #fff; padding: 15px; border-radius: 25px; font-weight: 900; font-size: 1.2rem;">
                        ⏱️ 60+ SECONDS WASTED!
                    </div>
                </div>

                <!-- New Way -->
                <div style="background: linear-gradient(135deg, #006400, #32CD32); padding: 30px; border-radius: 15px; border: 3px solid #32CD32; position: relative;">
                    <div style="position: absolute; top: -15px; right: -15px; background: #FFD700; color: #000; padding: 10px 20px; border-radius: 25px; font-weight: 900; font-size: 0.9rem;">
                        WORLD'S ONLY!
                    </div>
                    <h3 style="color: #fff; font-size: 1.8rem; margin-bottom: 20px;">🚀 NAVIBATCH WAY</h3>
                    <div style="background: rgba(0,0,0,0.5); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <p style="color: #fff; font-size: 1.1rem; line-height: 1.6;">
                            ✅ Open NaviBatch<br>
                            ✅ Import all 14 addresses<br>
                            ✅ Tap "Push to Apple Maps"<br>
                            ✅ <strong>DONE!</strong> All stops appear!<br>
                            <br>
                        </p>
                    </div>
                    <div style="background: #32CD32; color: #000; padding: 15px; border-radius: 25px; font-weight: 900; font-size: 1.2rem;">
                        ⚡ 1 SECOND ONLY!
                    </div>
                </div>
            </div>

            <div style="background: #FFD700; color: #000; padding: 30px; border-radius: 15px; margin-bottom: 40px;">
                <h3 style="font-size: 2.5rem; margin-bottom: 15px; font-weight: 900;">60x FASTER! 🏃‍♂️💨</h3>
                <p style="font-size: 1.3rem; font-weight: 700;">That's 2+ HOURS saved EVERY DAY!</p>
            </div>

            <a href="https://apps.apple.com/app/id6746371287" style="background: #FF0000; color: #fff; padding: 25px 50px; font-size: 1.5rem; font-weight: 900; border: none; border-radius: 15px; text-decoration: none; display: inline-block; box-shadow: 0 10px 30px rgba(255, 0, 0, 0.4); transition: all 0.3s ease;" target="_blank">
                🔥 DOWNLOAD FREE NOW - SAVE YOUR SANITY!
            </a>
        </div>
    </section>

    <!-- Social Proof -->
    <section style="background: linear-gradient(135deg, #1a1a1a, #333); padding: 60px 0; text-align: center;">
        <div style="max-width: 1000px; margin: 0 auto; padding: 0 20px;">
            <h2 style="font-size: 2.5rem; color: #FFD700; margin-bottom: 40px; font-weight: 900;">
                50,000+ DRIVERS CAN'T BE WRONG! 🏆
            </h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 40px;">
                <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; border-left: 5px solid #32CD32;">
                    <p style="font-size: 1.2rem; color: #fff; margin-bottom: 15px; font-style: italic;">
                        "This app LITERALLY changed my life! I save 2 HOURS every day!"
                    </p>
                    <strong style="color: #FFD700;">- Mike, UberEats Driver</strong>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; border-left: 5px solid #32CD32;">
                    <p style="font-size: 1.2rem; color: #fff; margin-bottom: 15px; font-style: italic;">
                        "FINALLY! An app that gets it. One-click = GENIUS!"
                    </p>
                    <strong style="color: #FFD700;">- Sarah, Amazon Flex</strong>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; border-left: 5px solid #32CD32;">
                    <p style="font-size: 1.2rem; color: #fff; margin-bottom: 15px; font-style: italic;">
                        "Works with ALL my delivery apps. GAME CHANGER!"
                    </p>
                    <strong style="color: #FFD700;">- Carlos, Multi-Platform</strong>
                </div>
            </div>

            <div style="background: #FFD700; color: #000; padding: 20px; border-radius: 15px; margin-bottom: 40px;">
                <div style="font-size: 2rem; margin-bottom: 10px;">⭐⭐⭐⭐⭐</div>
                <div style="font-size: 1.5rem; font-weight: 900;">4.8/5 on App Store</div>
                <div style="font-size: 1.1rem;">(2,847 reviews)</div>
            </div>

            <a href="https://apps.apple.com/app/id6746371287" style="background: #32CD32; color: #000; padding: 25px 50px; font-size: 1.5rem; font-weight: 900; border: none; border-radius: 15px; text-decoration: none; display: inline-block; box-shadow: 0 10px 30px rgba(50, 205, 50, 0.4); transition: all 0.3s ease;" target="_blank">
                💚 JOIN THE REVOLUTION - FREE TRIAL!
            </a>
        </div>
    </section>

    <!-- Final Urgent CTA -->
    <section style="background: linear-gradient(135deg, #FF0000, #8B0000); padding: 80px 0; text-align: center; color: #fff;">
        <div style="max-width: 800px; margin: 0 auto; padding: 0 20px;">
            <h2 style="font-size: 3.5rem; margin-bottom: 20px; font-weight: 900; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                DON'T WASTE ANOTHER SECOND! ⏰
            </h2>

            <p style="font-size: 1.5rem; margin-bottom: 30px; font-weight: 700;">
                While you're reading this, other drivers are already saving HOURS with NaviBatch!
            </p>

            <div style="background: rgba(255,255,255,0.2); padding: 30px; border-radius: 15px; margin-bottom: 40px; backdrop-filter: blur(10px);">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                    <div>✅ <strong>14-Day FREE Trial</strong></div>
                    <div>✅ <strong>Cancel Anytime</strong></div>
                    <div>✅ <strong>No Credit Card</strong></div>
                    <div>✅ <strong>Works with Apple Maps</strong></div>
                </div>
                <div style="font-size: 1.2rem; font-weight: 900; color: #FFD700;">
                    💰 MONEY-BACK GUARANTEE!
                </div>
            </div>

            <a href="https://apps.apple.com/app/id6746371287" style="background: #FFD700; color: #000; padding: 30px 60px; font-size: 1.8rem; font-weight: 900; border: none; border-radius: 15px; text-decoration: none; display: inline-block; box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4); transition: all 0.3s ease; animation: pulse 2s infinite;" target="_blank">
                🚀 DOWNLOAD FREE NOW - SAVE YOUR TIME!
            </a>

            <p style="margin-top: 30px; font-size: 1.1rem; opacity: 0.9;">
                ⚡ <strong>INSTANT DOWNLOAD</strong> - Start saving time in 30 seconds!
            </p>
        </div>
    </section>

    <!-- Simple Footer -->
    <footer style="background: #000; padding: 30px 0; text-align: center; color: #666;">
        <div style="max-width: 800px; margin: 0 auto; padding: 0 20px;">
            <p style="margin-bottom: 15px;">
                <strong style="color: #FFD700;">NaviBatch</strong> - World's Only 14-Stop One-Click Navigation
            </p>
            <p style="font-size: 0.9rem;">
                <a href="/privacy.html" style="color: #666; text-decoration: none; margin: 0 15px;">Privacy</a>
                <a href="/terms.html" style="color: #666; text-decoration: none; margin: 0 15px;">Terms</a>
                <a href="mailto:<EMAIL>" style="color: #666; text-decoration: none; margin: 0 15px;">Support</a>
            </p>
            <p style="margin-top: 15px; font-size: 0.8rem;">
                &copy; 2024 NaviBatch. All rights reserved.
            </p>
        </div>
    </footer>

    <!-- Simple Analytics -->
    <script>
        // Track all download button clicks
        document.querySelectorAll('a[href*="apps.apple.com"]').forEach(button => {
            button.addEventListener('click', function() {
                console.log('Download button clicked from landing page');
                // Add your analytics tracking here
            });
        });

        // Add pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
