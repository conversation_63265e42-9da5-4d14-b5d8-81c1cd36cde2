#loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  background-color: #0F172A;
  display: flex;
  justify-content: center;
  align-items: center;
}

#loader {
  width: 60px;
  height: 60px;
  border: 5px solid transparent;
  border-top-color: #00A3FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

#loader:before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border: 5px solid transparent;
  border-top-color: #0077B6;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loaded #loader-wrapper {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-out;
}
