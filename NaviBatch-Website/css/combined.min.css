/* 合并的CSS文件 - 优化版本 */

/* 全局样式 */
:root {
    --primary-color: #00A3FF;
    --secondary-color: #0081CC;
    --accent-color: #FFD600;
    --dark-bg: #0F172A;
    --darker-bg: #080F20;
    --light-text: #F8FAFC;
    --gray-text: #94A3B8;
    --border-color: #1E293B;
    --card-bg: #1E293B;
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --error-color: #EF4444;
    --radius: 8px;
    --large-radius: 12px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s ease;
}

/* 基础样式 */
a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
}

img {
    max-width: 100%;
    height: auto;
    content-visibility: auto;
    contain: layout paint size;
}

ul {
    list-style: none;
}

/* 头部样式 */
header {
    background-color: var(--darker-bg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 0;
    z-index: 100;
    padding: 1rem 0;
    will-change: transform;
    backface-visibility: hidden;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    flex: 0 0 160px;
}

.logo img {
    height: 40px;
}

.main-nav {
    display: flex;
    gap: 2rem;
}

.main-nav a {
    color: var(--light-text);
    font-weight: 500;
    position: relative;
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.main-nav a:hover {
    color: var(--primary-color);
}

.main-nav a:hover::after {
    width: 100%;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
}

/* 移动端菜单样式 */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--darker-bg);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-menu-nav {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.mobile-menu-nav a {
    color: var(--light-text);
    font-size: 1.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.mobile-menu-nav a:hover {
    color: var(--primary-color);
}

.mobile-menu-cta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.mobile-menu-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 2rem;
    cursor: pointer;
    padding: 0.5rem;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.6rem 1.2rem;
    border-radius: var(--radius);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--darker-bg);
    box-shadow: 0 4px 8px rgba(0, 163, 255, 0.3);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    -webkit-text-fill-color: var(--darker-bg) !important;
    color: var(--darker-bg) !important;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 163, 255, 0.4);
    -webkit-text-fill-color: var(--light-text) !important;
    color: var(--light-text) !important;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: rgba(0, 163, 255, 0.1);
    transform: translateY(-2px);
}

.btn-outline {
    background-color: transparent;
    color: white;
    border: 1px solid white;
}

.btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-text);
}

.btn-large {
    padding: 0.8rem 1.8rem;
    font-size: 1.1rem;
}

/* 英雄区域 */
.hero {
    background-color: var(--darker-bg);
    background-image: linear-gradient(135deg, var(--darker-bg), var(--dark-bg));
    color: var(--light-text);
    padding: 5rem 0;
    overflow-x: hidden;
    position: relative;
    will-change: transform;
    contain: layout paint;
}

.hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px),
               linear-gradient(0deg, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.1;
    z-index: 1;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
}

.hero h1 {
    font-weight: 700;
    line-height: 1.2;
    text-rendering: optimizeSpeed;
}

.hero h1.main-title {
    font-size: 3.2rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.hero h1.sub-title {
    font-size: 3.2rem;
    margin-bottom: 1.5rem;
}

.hero h1 .highlight {
    background: linear-gradient(90deg, #5eead4, #a3e635);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: var(--gray-text);
}

.hero-cta {
    display: flex;
    gap: 1rem;
}

/* 手机模型 */
.phone-mockup {
    position: relative;
    width: 280px;
    height: auto;
    aspect-ratio: 0.485;
    margin: 0 auto;
    background: #111827;
    border-radius: 38px;
    padding: 8px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    transform: none;
    overflow: hidden;
    z-index: 3;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 580px;
}

.phone-screen {
    width: 100% !important;
    height: 100% !important;
    background-color: #0F172A;
    border-radius: 30px;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    object-fit: cover !important;
    object-position: top center !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 浮动卡片 */
.floating-card {
    position: absolute;
    padding: 10px 16px;
    background-color: #e0f7ff !important;
    color: #0066cc !important;
    border-radius: 50px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
    font-weight: 600;
    font-size: 0.85rem;
    z-index: 100 !important;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    opacity: 1 !important;
    background: #e0f7ff !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.card-1 {
    top: 25%;
    left: 10px;
    animation: float 3s ease-in-out infinite;
    background-color: white !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25) !important;
}

.card-2 {
    bottom: 25%;
    right: 10px;
    animation: float 3s ease-in-out 0.5s infinite;
    background-color: white !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25) !important;
}

.floating-card i {
    color: #3b82f6;
    font-size: 1rem;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

/* 解决方案部分 */
.solution-section {
    padding: 5rem 0;
}

.bg-light {
    background-color: var(--darker-bg);
}

.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.section-header h2 {
    font-size: 2.4rem;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: var(--light-text);
}

.section-header p {
    font-size: 1.2rem;
    color: var(--gray-text);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
    border-color: var(--primary-color);
}

.feature-card:first-child {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--card-bg), rgba(0, 163, 255, 0.05));
}

.feature-card:first-child::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.feature-card:first-child .feature-icon {
    background-color: rgba(0, 163, 255, 0.15);
    border: 2px solid rgba(0, 163, 255, 0.3);
}

/* 为其他卡片添加微妙的视觉差异 */
.feature-card:nth-child(2) {
    border-color: rgba(0, 163, 255, 0.3);
}

.feature-card:nth-child(3) {
    border-color: rgba(0, 163, 255, 0.2);
}

.feature-card:nth-child(4) {
    border-color: rgba(0, 163, 255, 0.25);
}

.feature-card:nth-child(2):hover,
.feature-card:nth-child(3):hover,
.feature-card:nth-child(4):hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--card-bg), rgba(0, 163, 255, 0.03));
}

.feature-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(0, 163, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    transition: var(--transition);
    border: 1px solid rgba(0, 163, 255, 0.2);
}

.feature-icon i {
    font-size: 28px;
    color: var(--primary-color);
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    background-color: rgba(0, 163, 255, 0.15);
    border-color: rgba(0, 163, 255, 0.4);
}

.feature-card:hover .feature-icon i {
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 163, 255, 0.3);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
    font-weight: 600;
    line-height: 1.3;
}

.feature-card p {
    color: var(--gray-text);
    line-height: 1.6;
    font-size: 1rem;
}

.feature-card:first-child h3 {
    color: var(--primary-color);
    font-weight: 700;
}

.cta-center {
    text-align: center;
    margin: 3rem auto 2rem;
    padding: 1rem 0;
}

.cta-center .btn {
    display: inline-block;
    margin: 0 auto;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 163, 255, 0.3);
}

.cta-center .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 163, 255, 0.4);
}

/* 步骤部分样式 */
.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.step-card {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    position: relative;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
    border-color: var(--primary-color);
}

.step-number {
    position: absolute;
    top: -20px;
    left: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--darker-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.step-card h3 {
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-size: 1.4rem;
    color: var(--light-text);
}

.step-card p {
    margin-bottom: 1.5rem;
    color: var(--gray-text);
}

.step-icon-container {
    margin-top: 1.5rem;
    padding: 2rem;
    background-color: rgba(0, 163, 255, 0.1);
    border-radius: var(--radius);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid rgba(0, 163, 255, 0.2);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.step-card:hover .step-icon-container {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    background-color: rgba(0, 163, 255, 0.15);
}

.step-icon {
    font-size: 3.5rem;
    color: var(--primary-color);
}

/* 案例研究样式 */
.case-studies {
    padding: 5rem 0;
    background-color: var(--darker-bg);
}

.case-studies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.case-study-card {
    background-color: var(--card-bg);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.case-study-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
    border-color: var(--primary-color);
}

.case-study-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 0 0 0 60px;
    opacity: 0.1;
}

.case-study-content {
    position: relative;
    z-index: 2;
}

.case-study-content h3 {
    color: var(--light-text);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    line-height: 1.4;
}

.read-more {
    color: var(--primary-color);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary-color);
    border-radius: var(--radius);
    transition: var(--transition);
}

.read-more:hover {
    background-color: var(--primary-color);
    color: white;
}

.read-more::after {
    content: '→';
    transition: var(--transition);
}

.read-more:hover::after {
    transform: translateX(3px);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--large-radius);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--border-color);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--large-radius) var(--large-radius) 0 0;
}

.modal-header h2 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-body {
    padding: 2rem;
    color: var(--light-text);
    line-height: 1.6;
}

.case-study-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(0, 163, 255, 0.05);
    border-radius: var(--radius);
    border: 1px solid rgba(0, 163, 255, 0.1);
}

.case-study-stat {
    text-align: center;
}

.case-study-stat .number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    display: block;
}

.case-study-stat .label {
    font-size: 0.9rem;
    color: var(--gray-text);
}

.case-study-section {
    margin-bottom: 2rem;
}

.case-study-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.case-study-section p {
    margin-bottom: 1rem;
    color: var(--gray-text);
}

.case-study-quote {
    background: rgba(0, 163, 255, 0.1);
    border-left: 4px solid var(--primary-color);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 var(--radius) var(--radius) 0;
    font-style: italic;
}

.case-study-quote .quote-text {
    font-size: 1.1rem;
    color: var(--light-text);
    margin-bottom: 1rem;
}

.case-study-quote .quote-author {
    color: var(--gray-text);
    font-size: 0.9rem;
}

.case-study-results {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.result-item {
    background: var(--darker-bg);
    padding: 1.5rem;
    border-radius: var(--radius);
    text-align: center;
    border: 1px solid var(--border-color);
}

.result-item .result-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--success-color);
    display: block;
}

.result-item .result-label {
    color: var(--gray-text);
    font-size: 0.9rem;
}

/* 价格部分 */
.pricing-section {
    padding: 5rem 0;
    background-color: var(--dark-bg);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background-color: var(--card-bg);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
    border-color: var(--primary-color);
}

.pricing-card.popular {
    border-color: var(--primary-color);
    transform: scale(1.05);
    z-index: 2;
}

.popular-tag {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary-color);
    color: var(--darker-bg);
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.3rem 1rem;
    border-bottom-left-radius: var(--radius);
}

.pricing-header {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 0.5rem;
}

.price span {
    font-size: 1rem;
    font-weight: 400;
    color: var(--gray-text);
}

.pricing-header p {
    color: var(--gray-text);
}

.free-trial {
    margin-top: 0.75rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(90deg, rgba(0, 163, 255, 0.15), rgba(16, 185, 129, 0.15));
    border-radius: 50px;
    font-weight: 600;
    color: #10b981;
    display: inline-block;
    animation: pulse 2s infinite;
    border: 1px dashed rgba(16, 185, 129, 0.5);
}

.free-trial i {
    margin-right: 0.4rem;
    color: #FFD600;
}

.pricing-features {
    padding: 2rem;
    flex-grow: 1;
}

.pricing-features li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-text);
}

.pricing-features i.fa-check {
    color: var(--success-color);
}

.pricing-features i.fa-times {
    color: var(--error-color);
}

.pricing-features strong {
    color: var(--accent-color);
}

.feature-badge {
    display: inline-block;
    background: linear-gradient(90deg, #FF5722, #FFC107);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
    animation: pulse 1.5s infinite;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pricing-card .btn {
    margin: 0 2rem 2rem;
}

.highlight-feature {
    color: var(--accent-color) !important;
}

.speed-badge {
    display: inline-block;
    background: linear-gradient(90deg, #00A3FF, #10b981);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 0.5rem;
    animation: pulse 2s infinite;
}

/* 对比部分样式 */
.bg-dark {
    background-color: #080F20;
    position: relative;
    overflow: hidden;
}

.bg-dark::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px),
                linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
    pointer-events: none;
}

.accent-text {
    color: #FFD600;
    position: relative;
    z-index: 1;
}

.accent-text::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #FFD600, transparent);
}

.comparison-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin: 3rem 0;
    position: relative;
}

.comparison-card {
    background-color: rgba(30, 41, 59, 0.8);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.comparison-card:hover {
    transform: translateY(-5px);
}

.comparison-card.traditional {
    border-left: 4px solid #ef4444;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(30, 41, 59, 0.7));
}

.comparison-card.navibatch {
    border-left: 4px solid #00A3FF;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(15, 23, 42, 0.7));
}

.comparison-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comparison-header i {
    font-size: 1.8rem;
    margin-right: 1rem;
}

.comparison-card.traditional .comparison-header i {
    color: #ef4444;
}

.comparison-card.navibatch .comparison-header i {
    color: #00A3FF;
}

.comparison-header h3 {
    font-size: 1.8rem;
    margin: 0;
}

.comparison-list {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.comparison-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.comparison-list li:hover {
    background-color: rgba(0, 0, 0, 0.3);
    transform: translateX(5px);
}

.comparison-card.traditional .comparison-list li i {
    color: #ef4444;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.comparison-card.navibatch .comparison-list li i {
    color: #00A3FF;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.comparison-time {
    padding: 1rem;
    text-align: center;
    font-size: 1.2rem;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.25);
}

.comparison-time.highlight {
    background: linear-gradient(90deg, rgba(0, 163, 255, 0.3), rgba(16, 185, 129, 0.3));
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 163, 255, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 163, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 163, 255, 0); }
}

.vs-badge {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #ef4444, #00A3FF);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.2rem;
    z-index: 10;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* 早期采用者样式 */
.social-proof.be-first {
    position: relative;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 49, 83, 0.9), rgba(27, 20, 100, 0.9));
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 163, 255, 0.3);
    overflow: hidden;
    margin: 2rem auto;
    max-width: 500px;
}

.social-proof.be-first::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #ffffff 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.1;
    pointer-events: none;
}

.social-proof-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.early-adopter {
    text-align: center;
    position: relative;
    z-index: 1;
}

.early-adopter-badge {
    display: inline-block;
    background: linear-gradient(90deg, #FFD600, #FF9500);
    color: #080F20;
    padding: 0.4rem 1rem;
    border-radius: 50px;
    font-weight: bold;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    box-shadow: 0 3px 10px rgba(255, 214, 0, 0.3);
}

.early-adopter-badge i {
    margin-right: 0.3rem;
    animation: spin 4s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.early-adopter h3 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: white;
}

.early-adopter p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.empty-stars {
    margin-top: 1rem;
}

.empty-stars i {
    color: rgba(255, 255, 255, 0.3);
    font-size: 1.5rem;
    margin: 0 0.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.empty-stars i:hover {
    color: #FFD600;
    transform: scale(1.2);
}





/* 竞品对比样式 */
.competitor-section {
    background: linear-gradient(135deg, #0b0f19, #1a2438);
    padding: 4rem 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.competitor-section::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px),
               linear-gradient(0deg, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
    pointer-events: none;
}

.competitor-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.competitor-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #0088ff, #5acafb);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    display: inline-block;
}

.competitor-header p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    color: rgba(255, 255, 255, 0.8);
}

.competitor-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.competitor-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.competitor-card.navibatch {
    background: rgba(0, 136, 255, 0.15);
    border: 1px solid rgba(0, 136, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 136, 255, 0.2);
    transform: scale(1.05);
    position: relative;
    z-index: 2;
}

.competitor-card.navibatch::before {
    content: "Recommended";
    position: absolute;
    top: -12px;
    right: 20px;
    background: linear-gradient(90deg, #0088ff, #5acafb);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.8rem;
    box-shadow: 0 5px 15px rgba(0, 136, 255, 0.3);
}

.competitor-logo {
    height: 60px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.competitor-name {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.competitor-name.navibatch {
    color: #0088ff;
}

.competitor-price {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.feature-list li {
    margin-bottom: 0.8rem;
    padding-left: 1.8rem;
    position: relative;
    color: rgba(255, 255, 255, 0.8);
}

.feature-list li i {
    position: absolute;
    left: 0;
    top: 3px;
}

.feature-list li i.fa-check {
    color: #0ed36a;
}

.feature-list li i.fa-times {
    color: #ff4545;
}

.feature-list li i.fa-star {
    color: #ffc107;
}

.feature-list li.highlight {
    color: white;
    font-weight: 600;
}

.feature-list.navibatch li {
    color: rgba(255, 255, 255, 0.9);
}

.exclusive-badge {
    display: inline-block;
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border-radius: 20px;
    padding: 2px 8px;
    font-size: 0.7rem;
    margin-left: 8px;
    font-weight: bold;
    vertical-align: middle;
}

.winner-badge {
    display: inline-block;
    margin-top: 1rem;
    background: rgba(14, 211, 106, 0.2);
    color: #0ed36a;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: bold;
    text-align: center;
}

.competitor-table {
    margin-top: 4rem;
    overflow-x: auto;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.comparison-table th {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.2);
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table td:first-child {
    text-align: left;
    font-weight: 500;
    color: white;
}

.comparison-table td.best {
    position: relative;
}

.comparison-table td.best::after {
    content: "Best";
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 0.65rem;
    background: #0088ff;
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: bold;
}

.cta-row {
    margin-top: 3rem;
    text-align: center;
}

.cta-row .btn-primary {
    font-size: 1.1rem;
    padding: 0.8rem 2rem;
    background: linear-gradient(90deg, #0088ff, #5acafb);
    border: none;
    border-radius: 50px;
    color: white;
    font-weight: 700;
    box-shadow: 0 10px 20px rgba(0, 136, 255, 0.3);
    transition: all 0.3s ease;
}

.cta-row .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 136, 255, 0.4);
}

/* 扎心的真相样式 */
.shocking-truth {
    margin: 3rem auto 4rem;
    max-width: 800px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(24, 29, 49, 0.6));
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    overflow: hidden;
    color: white;
    text-align: center;
}

.shocking-truth::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #ffffff 1px, transparent 1px);
    background-size: 10px 10px;
    opacity: 0.05;
    pointer-events: none;
}

.shocking-truth h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #ff3a5e;
    text-shadow: 0 2px 10px rgba(255, 58, 94, 0.3);
}

.shocking-truth p {
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
    color: rgba(255, 255, 255, 0.9);
}

.shocking-truth p strong {
    font-weight: 700;
    color: white;
    position: relative;
    display: inline-block;
}

.shocking-truth p:first-of-type strong {
    color: #ff3a5e;
}

.shocking-truth p:last-of-type strong {
    color: #00e676;
}

.shocking-badge {
    display: inline-block;
    margin-top: 1.5rem;
    background: linear-gradient(90deg, #00e676, #00c853);
    color: #000;
    font-weight: 700;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(0, 230, 118, 0.4);
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(0, 230, 118, 0.4); }
    70% { box-shadow: 0 0 0 12px rgba(0, 230, 118, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 230, 118, 0); }
}

/* 进度条对比样式 */
.progress-comparison {
    margin: 3rem 0;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.progress-group {
    margin-bottom: 2rem;
}

.progress-group:last-child {
    margin-bottom: 0;
}

.progress-label {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--light-text);
}

.progress-container {
    height: 30px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-bar {
    height: 100%;
    border-radius: 15px;
    transition: width 1.5s ease-in-out;
}

.progress-bar.traditional-bar {
    background: linear-gradient(90deg, #ef4444, #ef4444aa);
    animation: pulse-error 2s infinite;
}

.progress-bar.navibatch-bar {
    background: linear-gradient(90deg, #00A3FF, #10b981);
    animation: extend 1.5s ease-out forwards, pulse-success 2s infinite 1.5s;
}

@keyframes extend {
    0% { width: 0; }
    100% { width: 1.67%; }
}

@keyframes pulse-success {
    0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
    100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

@keyframes pulse-error {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

.progress-time {
    text-align: right;
    font-size: 0.9rem;
    color: var(--gray-text);
}

.progress-group.navibatch .progress-time {
    color: #10b981;
    font-weight: bold;
}

/* 节省时间标记 */
.time-saved-badge {
    background: linear-gradient(90deg, #00A3FF, #10b981);
    padding: 1rem 2rem;
    border-radius: 50px;
    color: white;
    font-weight: bold;
    text-align: center;
    margin: 2rem auto 0;
    max-width: max-content;
    box-shadow: 0 5px 15px rgba(0, 163, 255, 0.3);
    animation: float 3s ease-in-out infinite;
}

.time-saved-badge i {
    margin-right: 0.5rem;
}

/* 配送证明样式 */

.photo-benefits {
    background-color: var(--card-bg);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    margin-top: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.photo-benefits h3 {
    margin-bottom: 1.5rem;
    color: var(--light-text);
}

.benefits-list {
    list-style: none;
    padding: 0;
}

.benefits-list li {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.benefits-list li i {
    color: var(--success-color);
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.user-testimonial {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius);
    border-left: 4px solid var(--accent-color);
}

.user-testimonial blockquote {
    font-style: italic;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.testimonial-author {
    color: var(--gray-text);
    font-size: 0.9rem;
}

/* 下载部分样式 */
.download-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--darker-bg), var(--dark-bg));
    text-align: center;
}

.ios-exclusive-badge {
    display: inline-block;
    background: linear-gradient(90deg, #007AFF, #5AC8FA);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    margin-bottom: 1rem;
}

.ios-advantages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.ios-advantage-item {
    text-align: center;
    padding: 1.5rem;
}

.ios-advantage-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.ios-advantage-item h3 {
    margin-bottom: 0.5rem;
    color: var(--light-text);
}

.ios-advantage-item p {
    color: var(--gray-text);
}

.ios-download-container {
    margin-top: 2rem;
}

.ios-app-store-button {
    display: inline-block;
    transition: transform 0.3s ease;
}

.ios-app-store-button:hover {
    transform: scale(1.05);
}

/* 联系我们样式 */
.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: white;
    font-size: 1.2rem;
}

.contact-details h3 {
    margin-bottom: 0.5rem;
    color: var(--light-text);
}

.contact-details p {
    color: var(--gray-text);
}

.contact-form {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--light-text);
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    background-color: var(--darker-bg);
    color: var(--light-text);
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 163, 255, 0.2);
}

/* 图片上传样式 */
.image-upload-container {
    position: relative;
}

.image-upload-container input[type="file"] {
    display: none;
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    background-color: var(--darker-bg);
    margin-bottom: 1rem;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(0, 163, 255, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(0, 163, 255, 0.1);
}

.upload-icon {
    margin-bottom: 1rem;
}

.upload-icon i {
    font-size: 3rem;
    color: var(--gray-text);
}

.upload-text p {
    margin: 0.5rem 0;
    color: var(--light-text);
}

.upload-hint {
    font-size: 0.9rem;
    color: var(--gray-text) !important;
}

.upload-note {
    font-size: 0.8rem;
    color: var(--primary-color) !important;
    font-style: italic;
    margin-top: 0.5rem;
}

/* 图片预览样式 */
.image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.image-preview-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: var(--radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: var(--transition);
}

.image-remove-btn:hover {
    background-color: rgba(239, 68, 68, 1);
}

.image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 0.5rem;
    font-size: 0.7rem;
    text-align: center;
}

/* CTA部分样式 */
.cta-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    text-align: center;
    color: white;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

/* Footer样式 */
footer {
    background-color: var(--darker-bg);
    color: var(--light-text);
}

.footer-top {
    padding: 3rem 0 2rem;
    border-bottom: 1px solid var(--border-color);
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
}

.footer-brand {
    max-width: 300px;
}

.footer-logo img {
    margin-bottom: 1rem;
}

.footer-tagline {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-weight: 500;
}

.footer-description p {
    color: var(--gray-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    background-color: var(--card-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-text);
    transition: var(--transition);
}

.social-icon:hover {
    background-color: var(--primary-color);
    color: white;
}

.footer-heading {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--gray-text);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-links a i {
    font-size: 0.8rem;
}

.app-store-badge {
    margin-top: 1rem;
}

.footer-bottom {
    padding: 1.5rem 0;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.copyright {
    color: var(--gray-text);
    font-size: 0.9rem;
}

.back-to-top a {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.back-to-top a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* 特殊样式类 */
.highlight-card {
    border-left: 4px solid var(--accent-color);
    position: relative;
    overflow: hidden;
}

.highlight-card::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: var(--accent-color);
    opacity: 0.1;
    border-radius: 0 0 0 100%;
}

.blink-tag {
    display: inline-block;
    background: linear-gradient(90deg, #ef4444, #FFD600);
    color: #080F20;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
    vertical-align: middle;
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.clean-quote {
    background: rgba(0, 136, 255, 0.1);
    border-left: 4px solid #0088ff;
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
    position: relative;
    line-height: 1.6;
}

.clean-quote::before {
    display: none;
}

/* Patent Pending 徽章样式 */
.patent-badge {
    display: inline-block;
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.65rem;
    font-weight: 700;
    margin-left: 0.5rem;
    vertical-align: middle;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.5);
}

.patent-badge-small {
    display: inline-block;
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 8px;
    font-size: 0.55rem;
    font-weight: 600;
    margin-left: 0.3rem;
    vertical-align: middle;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 4px rgba(30, 64, 175, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.4);
}

/* Pioneer徽章样式 */
.pioneer-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 49, 83, 0.9), rgba(27, 20, 100, 0.9));
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(0, 163, 255, 0.4);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 2rem 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.pioneer-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.pioneer-badge:hover::before {
    left: 100%;
}

.pioneer-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(0, 163, 255, 0.6);
}

.pioneer-icon {
    background: linear-gradient(135deg, #00A3FF, #0062E6);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    box-shadow: 0 8px 20px rgba(0, 163, 255, 0.5);
    position: relative;
    z-index: 2;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.pioneer-icon i {
    color: white;
    font-size: 1.8rem;
    animation: fly 3s ease-in-out infinite;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes fly {
    0% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0); }
}

.pioneer-text h3 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: white;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.pioneer-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin: 0;
    line-height: 1.4;
    position: relative;
    z-index: 2;
}

/* Pioneer Section 整体布局 */
.pioneer-section {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(0, 163, 255, 0.2);
}

.pioneer-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
    text-align: center;
}

.pioneer-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
}

.download-cta-container {
    flex-shrink: 0;
}

.download-btn {
    background: linear-gradient(135deg, #00A3FF, #0062E6);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 163, 255, 0.3);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 163, 255, 0.4);
    background: linear-gradient(135deg, #0081CC, #004A99);
}

.download-btn i {
    font-size: 1.1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .pioneer-badge {
        flex-direction: column;
        text-align: center;
    }

    .pioneer-icon {
        margin: 0 0 1rem 0;
    }

    .pioneer-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .pioneer-header-content {
        width: 100%;
        align-items: center;
    }

    .download-cta-container {
        width: 100%;
    }

    .download-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem 1.5rem;
    }
}

/* 高亮项目 */
.highlight-item {
    background: rgba(0, 163, 255, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
    margin: 0.5rem 0;
}

/* 浮动动画 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .hero .container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .hero-content {
        text-align: center;
        margin: 0 auto;
        padding: 0 10px;
    }

    .hero h1.main-title,
    .hero h1.sub-title {
        font-size: 1.8rem;
        white-space: normal;
        width: 100%;
        overflow: visible;
        display: block;
        position: relative;
        z-index: 10;
        margin-bottom: 0.5rem;
    }

    .hero-image-container {
        margin-top: 1.5rem;
        margin-left: auto;
        margin-right: auto;
        max-width: 350px;
        display: block;
    }

    .phone-mockup {
        width: 230px;
        min-height: 470px;
        margin: 0 auto;
        transform: none !important;
    }

    .floating-card {
        padding: 8px 12px;
        font-size: 0.75rem;
        background-color: white !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        opacity: 1 !important;
    }

    .card-1 {
        top: 20%;
        left: -15px;
    }

    .card-2 {
        bottom: 20%;
        right: -15px;
    }

    .comparison-container {
        flex-direction: column;
    }

    .vs-badge {
        position: relative;
        margin: 1rem auto;
        transform: none;
        left: auto;
        top: auto;
    }

    .main-nav {
        display: none;
    }

    header .container > .cta-buttons {
        display: none;
    }

    .mobile-menu-toggle {
        display: block !important;
        z-index: 1002;
    }

    /* 移动端pricing优化 */
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-card.popular {
        transform: scale(1);
    }

    .pricing-card {
        padding: 1.5rem;
    }

    .pricing-header {
        padding: 1.5rem;
    }

    .pricing-features {
        padding: 1.5rem;
    }

    /* 移动端竞品对比优化 */
    .competitor-grid {
        grid-template-columns: 1fr;
    }

    .competitor-card.navibatch {
        transform: scale(1);
    }

    /* 移动端shocking-truth优化 */
    .shocking-truth {
        padding: 1.5rem;
        margin: 2rem auto 3rem;
    }

    .shocking-truth h3 {
        font-size: 1.5rem;
    }

    .shocking-truth p {
        font-size: 1rem;
    }

    .shocking-badge {
        font-size: 1rem;
        padding: 0.6rem 1.5rem;
    }

    /* 移动端配送证明优化 */
    .photo-benefits {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    /* 移动端联系表单优化 */
    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    /* 移动端图片上传优化 */
    .upload-area {
        padding: 1.5rem 1rem;
    }

    .upload-icon i {
        font-size: 2.5rem;
    }

    .upload-text p {
        font-size: 0.9rem;
    }

    .upload-hint {
        font-size: 0.8rem !important;
    }

    .image-preview-item {
        width: 100px;
        height: 100px;
    }

    .image-preview-container {
        gap: 0.5rem;
    }

    /* 移动端CTA优化 */
    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1rem;
    }

    /* 移动端步骤优化 */
    .steps-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .step-card {
        padding: 1.5rem;
    }

    .step-icon-container {
        padding: 1.5rem;
    }

    .step-icon {
        font-size: 2.5rem;
    }

    /* 移动端案例研究优化 */
    .case-studies-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .case-study-card {
        padding: 1.5rem;
    }

    .case-study-content h3 {
        font-size: 1.2rem;
    }

    .read-more {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    /* 移动端模态框优化 */
    .modal-content {
        width: 95%;
        margin: 10% auto;
        max-height: 85vh;
    }

    .modal-header {
        padding: 1.5rem;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .case-study-meta {
        flex-direction: column;
        gap: 1rem;
    }

    .case-study-results {
        grid-template-columns: 1fr;
    }

    /* 移动端Footer优化 */
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-brand {
        max-width: 100%;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (min-width: 768px) {
    .comparison-container {
        flex-direction: row;
        align-items: stretch;
    }

    .comparison-card {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .comparison-list {
        flex: 1;
    }

    .vs-badge {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

/* 性能优化 */
@media (max-width: 480px) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hero {
        background-attachment: scroll !important;
        transform: translateZ(0);
        will-change: auto;
    }

    .phone-mockup img {
        transform: translate3d(0,0,0);
        max-width: 100%;
        width: auto;
        height: auto;
        content-visibility: auto;
        contain: layout paint size;
    }

    /* 减少重绘和回流 */
    .floating-card {
        will-change: auto;
        transform: translateZ(0);
    }

    /* 480px以下图片上传优化 */
    .upload-area {
        padding: 1rem 0.5rem;
    }

    .upload-icon i {
        font-size: 2rem;
    }

    .upload-text p {
        font-size: 0.8rem;
        margin: 0.3rem 0;
    }

    .upload-hint {
        font-size: 0.7rem !important;
    }

    .image-preview-item {
        width: 80px;
        height: 80px;
    }

    .image-remove-btn {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }

    .image-info {
        font-size: 0.6rem;
        padding: 0.3rem;
    }
}

/* FAQ Section */
.faq-section {
    background-color: #1e293b;
    padding: 80px 0;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: rgba(30, 41, 59, 0.8);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 163, 255, 0.15);
}

.faq-item h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.faq-item p {
    color: var(--light-text);
    line-height: 1.6;
    margin: 0;
}

/* 地理位置高亮样式 */
.location-highlight {
    background: linear-gradient(135deg, #00A3FF, #FFD600);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    position: relative;
    display: inline-block;
    animation: locationPulse 3s ease-in-out infinite;
}

.location-highlight::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(135deg, #00A3FF, #FFD600);
    border-radius: 2px;
    opacity: 0.6;
}

@keyframes locationPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.9; transform: scale(1.02); }
}

/* 地理位置检测加载状态 */
.location-detecting {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.location-detected {
    opacity: 1;
    transition: opacity 0.5s ease;
}