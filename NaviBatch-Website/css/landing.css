/* Landing Page Specific Styles */

/* Problem/Solution Comparison */
.problem-solution {
    padding: 80px 0;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    max-width: 1200px;
    margin: 0 auto;
}

.problem-side, .solution-side {
    padding: 40px;
    border-radius: 20px;
    position: relative;
}

.problem-side {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
    border: 2px solid rgba(239, 68, 68, 0.2);
}

.solution-side {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
    border: 2px solid rgba(16, 185, 129, 0.2);
}

.problem-header h2, .solution-header h2 {
    font-size: 2rem;
    margin-bottom: 10px;
    text-align: center;
}

.problem-header p, .solution-header p {
    text-align: center;
    color: var(--gray-text);
    margin-bottom: 30px;
}

.problem-steps, .solution-steps {
    margin-bottom: 30px;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.03);
}

.step.magic {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
    border: 1px solid rgba(16, 185, 129, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.step-number {
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    flex-shrink: 0;
}

.step.magic .step-number {
    background: var(--success-color);
    font-size: 16px;
}

.step-content h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    color: var(--light-text);
}

.step-content p {
    margin: 0;
    color: var(--gray-text);
    font-size: 0.9rem;
}

.time-waste, .time-save {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
}

.time-badge {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.time-badge.bad {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.time-badge.good {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Demo Section */
.demo-section {
    padding: 80px 0;
    background: var(--dark-bg);
    text-align: center;
}

.demo-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--light-text);
}

.demo-content p {
    font-size: 1.2rem;
    color: var(--gray-text);
    margin-bottom: 50px;
}

.video-container {
    max-width: 800px;
    margin: 0 auto 50px;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.video-placeholder {
    background: linear-gradient(135deg, var(--darker-bg), var(--dark-bg));
    aspect-ratio: 16/9;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.video-placeholder:hover {
    transform: scale(1.02);
}

.play-button {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 163, 255, 0.3);
    transition: var(--transition);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(0, 163, 255, 0.4);
}

.video-overlay {
    position: absolute;
    bottom: 30px;
    left: 30px;
    color: white;
    text-align: left;
}

.video-overlay h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.video-overlay p {
    color: var(--gray-text);
    margin: 0;
}

.demo-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 50px;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 10px;
}

.stat-label {
    color: var(--gray-text);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

/* Benefits Section */
.benefits-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--darker-bg) 0%, var(--dark-bg) 100%);
}

.benefits-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: var(--light-text);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.benefit-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 16px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.benefit-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 20px 40px rgba(0, 163, 255, 0.1);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.benefit-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--light-text);
}

.benefit-card p {
    color: var(--gray-text);
    line-height: 1.6;
}

/* Social Proof Section */
.social-proof {
    padding: 80px 0;
    background: var(--dark-bg);
    text-align: center;
}

.social-proof h2 {
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: var(--light-text);
}

.testimonials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto 50px;
}

.testimonial {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    color: var(--light-text);
}

.testimonial-author strong {
    color: var(--primary-color);
    display: block;
    margin-bottom: 5px;
}

.testimonial-author span {
    color: var(--gray-text);
    font-size: 0.9rem;
}

.app-store-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
}

.rating-stars {
    color: var(--accent-color);
    font-size: 1.5rem;
}

.rating-text strong {
    color: var(--light-text);
    font-size: 1.2rem;
}

.rating-text span {
    color: var(--gray-text);
    margin-left: 10px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .problem-side, .solution-side {
        padding: 30px 20px;
    }

    .demo-stats {
        flex-direction: column;
        gap: 30px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .testimonials {
        grid-template-columns: 1fr;
    }

    .app-store-rating {
        flex-direction: column;
        gap: 10px;
    }
}

/* Final CTA Section */
.final-cta {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    text-align: center;
    color: white;
}

.cta-content h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: 900;
}

.cta-content > p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.cta-features .feature {
    background: rgba(255, 255, 255, 0.2);
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.cta-buttons {
    margin-bottom: 30px;
}

.btn-large {
    padding: 20px 40px;
    font-size: 1.2rem;
    border-radius: 15px;
}

.money-back {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 12px;
    max-width: 600px;
    margin: 0 auto;
    backdrop-filter: blur(10px);
}

.money-back p {
    margin: 0;
    font-size: 1.1rem;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: var(--darker-bg);
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: var(--light-text);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.faq-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.faq-item h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.faq-item p {
    color: var(--gray-text);
    line-height: 1.6;
    margin: 0;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    padding: 40px 0 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.footer-logo p {
    color: var(--gray-text);
    margin: 0;
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 30px;
}

.footer-links a {
    color: var(--gray-text);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: var(--gray-text);
    margin: 0;
    font-size: 0.9rem;
}

/* Additional Mobile Responsive */
@media (max-width: 768px) {
    .final-cta .cta-content h2 {
        font-size: 2.2rem;
    }

    .cta-features {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll animations */
.problem-side {
    animation: slideInLeft 0.8s ease-out;
}

.solution-side {
    animation: slideInRight 0.8s ease-out;
}

.benefit-card {
    animation: slideInUp 0.6s ease-out;
}

.testimonial {
    animation: slideInUp 0.6s ease-out;
}

/* Hover effects */
.btn-primary {
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Focus states for accessibility */
.btn:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.faq-item:focus-within {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-color);
}
