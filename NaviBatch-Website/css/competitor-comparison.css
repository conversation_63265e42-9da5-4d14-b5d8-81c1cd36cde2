/* 竞品对比样式 */
.competitor-section {
    background: linear-gradient(135deg, #0b0f19, #1a2438);
    padding: 4rem 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.competitor-section::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px),
               linear-gradient(0deg, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
    pointer-events: none;
}

.competitor-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.competitor-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #0088ff, #5acafb);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    display: inline-block;
}

.competitor-header p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    color: rgba(255, 255, 255, 0.8);
}

.competitor-header .accent {
    color: #0088ff;
    font-weight: 700;
}

.competitor-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.competitor-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.competitor-card.navibatch {
    background: rgba(0, 136, 255, 0.15);
    border: 1px solid rgba(0, 136, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 136, 255, 0.2);
    transform: scale(1.05);
    position: relative;
    z-index: 2;
}

.competitor-card.navibatch::before {
    content: "Recommended";
    position: absolute;
    top: -12px;
    right: 20px;
    background: linear-gradient(90deg, #0088ff, #5acafb);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.8rem;
    box-shadow: 0 5px 15px rgba(0, 136, 255, 0.3);
}

.competitor-logo {
    height: 60px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.competitor-logo img {
    max-height: 100%;
    max-width: 180px;
}

.competitor-name {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.competitor-name.navibatch {
    color: #0088ff;
}

.competitor-price {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.feature-list li {
    margin-bottom: 0.8rem;
    padding-left: 1.8rem;
    position: relative;
    color: rgba(255, 255, 255, 0.8);
}

.feature-list li i {
    position: absolute;
    left: 0;
    top: 3px;
}

.feature-list li i.fa-check {
    color: #0ed36a;
}

.feature-list li i.fa-times {
    color: #ff4545;
}

.feature-list li i.fa-star {
    color: #ffc107;
}

.feature-list li.highlight {
    color: white;
    font-weight: 600;
}

.feature-list.navibatch li {
    color: rgba(255, 255, 255, 0.9);
}

.exclusive-badge {
    display: inline-block;
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border-radius: 20px;
    padding: 2px 8px;
    font-size: 0.7rem;
    margin-left: 8px;
    font-weight: bold;
    vertical-align: middle;
}

.winner-badge {
    display: inline-block;
    margin-top: 1rem;
    background: rgba(14, 211, 106, 0.2);
    color: #0ed36a;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: bold;
    text-align: center;
}

.competitor-table {
    margin-top: 4rem;
    overflow-x: auto;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.comparison-table th {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.2);
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table td:first-child {
    text-align: left;
    font-weight: 500;
    color: white;
}

.comparison-table td.best {
    position: relative;
}

.comparison-table td.best::after {
    content: "Best";
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 0.65rem;
    background: #0088ff;
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: bold;
}

.cta-row {
    margin-top: 3rem;
    text-align: center;
}

.cta-row .btn-primary {
    font-size: 1.1rem;
    padding: 0.8rem 2rem;
    background: linear-gradient(90deg, #0088ff, #5acafb);
    border: none;
    border-radius: 50px;
    color: white;
    font-weight: 700;
    box-shadow: 0 10px 20px rgba(0, 136, 255, 0.3);
    transition: all 0.3s ease;
}

.cta-row .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 136, 255, 0.4);
}

@media (max-width: 992px) {
    .competitor-grid {
        grid-template-columns: 1fr;
    }
    
    .competitor-card.navibatch {
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .competitor-table {
        font-size: 0.9rem;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 0.8rem;
    }
}
