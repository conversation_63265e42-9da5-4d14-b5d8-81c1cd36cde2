/* <PERSON>ie同意横幅样式 */
.cookie-consent {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    background: rgba(15, 23, 42, 0.98) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    color: white !important;
    padding: 20px !important;
    z-index: 999999 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(100%) !important;
    transition: transform 0.3s ease-in-out !important;
    min-height: 80px !important;
    display: block !important;
    visibility: visible !important;
}

.cookie-consent.show {
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.cookie-consent-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.cookie-consent-text {
    flex: 1;
    min-width: 300px;
}

.cookie-consent-text h4 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #00A3FF;
}

.cookie-consent-text p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #CBD5E1;
}

.cookie-consent-text a {
    color: #00A3FF;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
}

.cookie-consent-text a:hover {
    border-bottom-color: #00A3FF;
}

.cookie-consent-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.cookie-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 100px;
}

.cookie-btn-accept {
    background: linear-gradient(135deg, #00A3FF, #0081CC);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 163, 255, 0.3);
}

.cookie-btn-accept:hover {
    background: linear-gradient(135deg, #0081CC, #006BB3);
    box-shadow: 0 4px 12px rgba(0, 163, 255, 0.4);
    transform: translateY(-1px);
}

.cookie-btn-decline {
    background: transparent;
    color: #94A3B8;
    border: 1px solid #475569;
}

.cookie-btn-decline:hover {
    background: rgba(255, 255, 255, 0.05);
    color: white;
    border-color: #64748B;
}

.cookie-btn-settings {
    background: transparent;
    color: #00A3FF;
    border: 1px solid #00A3FF;
    font-size: 0.85rem;
    padding: 8px 16px;
}

.cookie-btn-settings:hover {
    background: rgba(0, 163, 255, 0.1);
    color: #60A5FA;
}

/* Cookie设置模态框 */
.cookie-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 10001;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.cookie-settings-modal.show {
    display: flex;
}

.cookie-settings-content {
    background: #0F172A;
    border-radius: 12px;
    padding: 30px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.cookie-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cookie-settings-header h3 {
    margin: 0;
    color: white;
    font-size: 1.3rem;
}

.cookie-close {
    background: none;
    border: none;
    color: #94A3B8;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.cookie-close:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.cookie-category {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.cookie-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.cookie-category h4 {
    margin: 0;
    color: white;
    font-size: 1rem;
}

.cookie-category p {
    margin: 0;
    color: #94A3B8;
    font-size: 0.9rem;
    line-height: 1.4;
}

.cookie-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.cookie-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.cookie-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #475569;
    transition: 0.3s;
    border-radius: 24px;
}

.cookie-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

.cookie-toggle input:checked + .cookie-toggle-slider {
    background-color: #00A3FF;
}

.cookie-toggle input:checked + .cookie-toggle-slider:before {
    transform: translateX(26px);
}

.cookie-toggle.disabled .cookie-toggle-slider {
    background-color: #374151;
    cursor: not-allowed;
}

.cookie-toggle.disabled .cookie-toggle-slider:before {
    background-color: #9CA3AF;
}

.cookie-settings-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cookie-consent {
        padding: 15px;
    }

    .cookie-consent-content {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .cookie-consent-text {
        min-width: auto;
    }

    .cookie-consent-actions {
        justify-content: center;
    }

    .cookie-btn {
        flex: 1;
        min-width: auto;
    }

    .cookie-settings-content {
        padding: 20px;
        margin: 10px;
    }

    .cookie-settings-actions {
        flex-direction: column;
    }

    .cookie-settings-actions .cookie-btn {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.cookie-consent.show {
    animation: slideUp 0.3s ease-out;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    .cookie-consent,
    .cookie-toggle-slider,
    .cookie-toggle-slider:before,
    .cookie-btn {
        transition: none;
    }

    .cookie-consent.show {
        animation: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .cookie-consent {
        background: black;
        border-top: 2px solid white;
    }

    .cookie-btn-accept {
        background: #0066CC;
        border: 1px solid white;
    }

    .cookie-btn-decline {
        border: 2px solid white;
    }
}