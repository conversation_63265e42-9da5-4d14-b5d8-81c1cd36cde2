.hero-image-container{width:100%;max-width:600px;aspect-ratio:3/2;min-height:400px;position:relative;margin:0 auto;z-index:2;contain:layout size style}.phone-mockup{width:300px!important;height:620px!important;position:relative;margin:0 0 0 auto;transform:rotateY(-10deg) rotateX(5deg);will-change:transform;contain:layout style paint}.phone-screen{width:100%;height:100%;object-fit:cover;object-position:top center;display:block;background-color:#0F172A;min-width:280px;min-height:580px}@font-face{font-family:'Segoe UI';font-display:swap}.hero-stats{min-height:120px;display:flex;gap:40px;align-items:center;justify-content:center}.stat-item{width:160px;height:100px;display:flex;flex-direction:column;justify-content:center;align-items:center;will-change:transform;contain:layout style}.hero-content .badge{height:44px;min-width:200px;display:inline-flex!important;align-items:center;justify-content:center;white-space:nowrap}.hero h1,.hero-subtitle,.hero-cta,.hero-stats{will-change:transform,opacity;transform:translateZ(0);backface-visibility:hidden}@media (max-width:992px){.phone-mockup{width:250px!important;height:520px!important;margin:0 auto;transform:none}.hero-image-container{min-height:320px}.hero-stats{flex-direction:column;gap:20px;min-height:300px}.stat-item{width:200px;height:80px}}@media (max-width:768px){.phone-mockup{width:220px!important;height:460px!important}.hero-image-container{min-height:280px}.hero-stats{min-height:250px;gap:15px}.stat-item{width:180px;height:70px}}img{height:auto;max-width:100%;image-rendering:auto;content-visibility:auto;contain-intrinsic-size:300px 600px}.hero::before{animation-duration:12s;animation-timing-function:ease-in-out;will-change:opacity;transform:translateZ(0)}.container{width:90%;max-width:1200px;margin:0 auto;padding:0 15px;contain:layout style}.hero-grid{display:grid;grid-template-columns:1fr 1fr;gap:40px;align-items:center;contain:layout}.phone-screen:not(.loaded){background:linear-gradient(135deg,#0F172A 0%,#1e293b 100%);background-size:400% 400%;animation:shimmerPlaceholder 2s ease-in-out infinite}@keyframes shimmerPlaceholder{0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}.critical-above-fold{--critical-css-loaded:true}.btn,.stat-item,.phone-mockup{transition:transform .3s ease,box-shadow .3s ease}.btn:hover,.stat-item:hover{transform:translateY(-2px)}
