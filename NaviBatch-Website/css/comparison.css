/* 对比部分样式增强 */
.bg-dark {
    background-color: #080F20;
    position: relative;
    overflow: visible; /* 改为visible，防止内容被截断 */
}

.bg-dark::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px),
                linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
    pointer-events: none;
}

.accent-text {
    color: #FFD600;
    position: relative;
    z-index: 1;
}

.accent-text::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #FFD600, transparent);
}
.comparison-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin: 3rem 0;
    position: relative;
}

/* 卡片样式 */
.comparison-card {
    background-color: rgba(30, 41, 59, 0.8);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.comparison-card:hover {
    transform: translateY(-5px);
}

/* 传统方式卡片 */
.comparison-card.traditional {
    border-left: 4px solid #ef4444;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(30, 41, 59, 0.7));
}

.comparison-card.traditional::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(45deg, rgba(255,255,255,0.03), rgba(255,255,255,0.03) 10px, transparent 10px, transparent 20px);
    opacity: 0.05;
    pointer-events: none;
}

/* NaviBatch卡片 */
.comparison-card.navibatch {
    border-left: 4px solid #00A3FF;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(15, 23, 42, 0.7));
}

.comparison-card.navibatch::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(59,130,246,0.05) 1px, transparent 1px),
                linear-gradient(0deg, rgba(59,130,246,0.05) 1px, transparent 1px);
    background-size: 15px 15px;
    opacity: 0.08;
    pointer-events: none;
}

/* 卡片头部 */
.comparison-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comparison-header i {
    font-size: 1.8rem;
    margin-right: 1rem;
}

.comparison-card.traditional .comparison-header i {
    color: #ef4444;
}

.comparison-card.navibatch .comparison-header i {
    color: #00A3FF;
}

.comparison-header h3 {
    font-size: 1.8rem;
    margin: 0;
}

/* 列表样式 */
.comparison-list {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.comparison-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.comparison-list li:hover {
    background-color: rgba(0, 0, 0, 0.3);
    transform: translateX(5px);
}

.comparison-card.traditional .comparison-list li i {
    color: #ef4444;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.comparison-card.navibatch .comparison-list li i {
    color: #00A3FF;
    margin-right: 1rem;
    font-size: 1.2rem;
}

/* 时间对比 */
.comparison-time {
    padding: 1rem;
    text-align: center;
    font-size: 1.2rem;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.25);
}

.comparison-time.highlight {
    background: linear-gradient(90deg, rgba(0, 163, 255, 0.3), rgba(16, 185, 129, 0.3));
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 163, 255, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 163, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 163, 255, 0); }
}

/* 对比标记 */
.vs-badge {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #ef4444, #00A3FF);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.2rem;
    z-index: 10;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* 高亮列表项 */
.highlight-item {
    background: linear-gradient(90deg, rgba(0, 163, 255, 0.2), rgba(16, 185, 129, 0.1)) !important;
    border-left: 3px solid #00A3FF;
    padding-left: 15px !important;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 闪烁标签 */
.blink-tag {
    display: inline-block;
    background: linear-gradient(90deg, #ef4444, #FFD600);
    color: #080F20;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
    vertical-align: middle;
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 进度比较 */
.progress-comparison {
    margin-top: 3rem;
    padding: 2rem;
    background-color: rgba(30, 41, 59, 0.8);
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.progress-group {
    margin-bottom: 1.5rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.progress-container {
    height: 30px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-bar {
    height: 100%;
    border-radius: 15px;
    transition: width 1.5s ease-in-out;
}

.progress-bar.traditional-bar {
    background: linear-gradient(90deg, #ef4444, #ef4444aa);
    animation: pulse-error 2s infinite;
}

.progress-bar.navibatch-bar {
    background: linear-gradient(90deg, #00A3FF, #10b981);
    animation: extend 1.5s ease-out forwards, pulse-success 2s infinite 1.5s;
}

@keyframes extend {
    0% { width: 0; }
    100% { width: 1.67%; }
}

@keyframes pulse-success {
    0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
    100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

@keyframes pulse-error {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

.progress-time {
    text-align: right;
    font-size: 0.9rem;
    color: var(--gray-text);
}

.progress-group.navibatch .progress-time {
    color: #10b981;
    font-weight: bold;
}

/* 节省时间标记 */
.time-saved-badge {
    background: linear-gradient(90deg, #00A3FF, #10b981);
    padding: 1rem 2rem;
    border-radius: 50px;
    color: white;
    font-weight: bold;
    text-align: center;
    margin: 2rem auto 0;
    max-width: max-content;
    box-shadow: 0 5px 15px rgba(0, 163, 255, 0.3);
    animation: float 3s ease-in-out infinite;
}

.time-saved-badge i {
    margin-right: 0.5rem;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

/* 速度标记 */
.speed-badge {
    display: inline-block;
    background: linear-gradient(90deg, #00A3FF, #10b981);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 0.5rem;
    animation: pulse 2s infinite;
}

/* 适配平板电脑 */
@media (min-width: 768px) {
    .comparison-container {
        flex-direction: row;
        align-items: stretch;
    }

    .comparison-card {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .comparison-list {
        flex: 1;
    }

    .vs-badge {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

/* 适配移动设备 */
@media (max-width: 767px) {
    .vs-badge {
        position: relative;
        margin: 1rem auto;
        transform: none;
        left: auto;
        top: auto;
    }
}

/* 配送证明部分样式 */

.photo-benefits {
    background-color: var(--card-bg);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    margin-top: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.photo-benefits h3 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.benefits-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 0.75rem;
    line-height: 1.5;
}

.benefits-list i {
    color: var(--success-color);
    font-size: 1.1rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.user-testimonial {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius);
    padding: 1.5rem;
    border-left: 4px solid var(--accent-color);
}

.user-testimonial blockquote {
    margin: 0 0 1rem 0;
    font-style: italic;
    line-height: 1.6;
}

.testimonial-author {
    text-align: right;
    margin: 0;
    color: var(--gray-text);
}

/* 桌面端优化 */
@media (min-width: 1024px) {
    .photo-benefits {
        max-width: 900px;
    }
}

/* 移动端优化 */
@media (max-width: 767px) {
    .photo-benefits {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .benefits-list li {
        margin-bottom: 0.8rem;
        gap: 0.5rem;
    }

    .user-testimonial {
        padding: 1.2rem;
    }
}

/* 高亮功能 */
.highlight-feature {
    color: var(--accent-color) !important;
}

.highlight-card {
    border-left: 4px solid var(--accent-color);
    position: relative;
    overflow: hidden;
}

.highlight-card::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: var(--accent-color);
    opacity: 0.1;
    border-radius: 0 0 0 100%;
}
