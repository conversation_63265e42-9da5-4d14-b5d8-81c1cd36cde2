/* iPhone XS 和小屏幕设备专用修复 */

/* iPhone XS (375px) 及更小设备 */
@media (max-width: 375px) {
    /* 容器优化 */
    .container {
        width: 95%;
        padding: 0 10px;
    }
    
    /* 英雄区域整体调整 */
    .hero {
        padding: 40px 0 80px;
        text-align: center;
    }
    
    .hero-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    /* 标题优化 */
    .hero h1 {
        font-size: 1.8rem !important;
        line-height: 1.3;
        margin-bottom: 16px;
    }
    
    .hero-subtitle {
        font-size: 1rem !important;
        line-height: 1.5;
        margin-bottom: 24px;
        padding: 0 5px;
    }
    
    /* 徽章优化 */
    .hero-content .badge {
        font-size: 12px !important;
        padding: 8px 16px !important;
        margin-bottom: 16px !important;
        min-width: 160px;
        height: 36px;
    }
    
    /* 按钮区域优化 */
    .hero-cta {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 30px;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        max-width: 280px;
        padding: 14px 20px;
        font-size: 0.95rem;
        text-align: center;
    }
    
    /* 手机模型大幅缩小 */
    .hero-image-container {
        min-height: 200px !important;
        max-width: 200px;
        margin: 0 auto;
    }
    
    .phone-mockup {
        width: 180px !important;
        height: 360px !important;
        margin: 0 auto !important;
        transform: none !important;
    }
    
    .phone-screen {
        min-width: 160px !important;
        min-height: 340px !important;
    }
    
    /* 统计数据区域优化 */
    .hero-stats {
        flex-direction: column;
        gap: 12px;
        min-height: 200px;
        margin-top: 20px;
    }
    
    .stat-item {
        width: 140px !important;
        height: 60px !important;
        padding: 10px;
    }
    
    .stat-number {
        font-size: 1.8rem !important;
        margin-bottom: 4px;
    }
    
    .stat-label {
        font-size: 0.8rem !important;
    }
}

/* iPhone SE (320px) 及更小设备 */
@media (max-width: 320px) {
    .container {
        width: 98%;
        padding: 0 5px;
    }
    
    .hero {
        padding: 30px 0 60px;
    }
    
    .hero h1 {
        font-size: 1.6rem !important;
    }
    
    .hero-subtitle {
        font-size: 0.9rem !important;
        padding: 0 2px;
    }
    
    .hero-content .badge {
        font-size: 11px !important;
        padding: 6px 12px !important;
        min-width: 140px;
        height: 32px;
    }
    
    .btn-large {
        max-width: 260px;
        padding: 12px 16px;
        font-size: 0.9rem;
    }
    
    .phone-mockup {
        width: 160px !important;
        height: 320px !important;
    }
    
    .phone-screen {
        min-width: 140px !important;
        min-height: 300px !important;
    }
    
    .hero-image-container {
        min-height: 180px !important;
        max-width: 180px;
    }
    
    .stat-item {
        width: 120px !important;
        height: 55px !important;
    }
    
    .stat-number {
        font-size: 1.6rem !important;
    }
    
    .stat-label {
        font-size: 0.75rem !important;
    }
}

/* 通用小屏幕优化 */
@media (max-width: 480px) {
    /* 确保所有文本都能正常显示 */
    * {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    /* 防止水平滚动 */
    body {
        overflow-x: hidden;
    }
    
    /* 导航栏优化 */
    header .container {
        padding: 0 10px;
    }
    
    .logo {
        flex: 0 0 120px;
    }
    
    .logo img {
        height: 32px;
    }
    
    /* 确保按钮不会超出屏幕 */
    .cta-buttons {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .btn {
        min-width: auto;
        white-space: nowrap;
    }
    
    /* 移动端菜单按钮 */
    .mobile-menu-toggle {
        font-size: 1.2rem;
    }
}

/* 横屏模式优化 */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 20px 0 40px;
    }
    
    .hero-grid {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: center;
    }
    
    .hero h1 {
        font-size: 1.5rem !important;
        margin-bottom: 12px;
    }
    
    .hero-subtitle {
        font-size: 0.9rem !important;
        margin-bottom: 16px;
    }
    
    .hero-cta {
        flex-direction: row;
        gap: 10px;
        margin-bottom: 20px;
    }
    
    .btn-large {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
    
    .phone-mockup {
        width: 140px !important;
        height: 280px !important;
    }
    
    .hero-stats {
        flex-direction: row;
        gap: 15px;
        min-height: auto;
        justify-content: center;
    }
    
    .stat-item {
        width: 100px !important;
        height: 50px !important;
    }
    
    .stat-number {
        font-size: 1.4rem !important;
    }
}

/* 修复可能的布局偏移 */
@media (max-width: 375px) {
    .hero-image-container,
    .phone-mockup,
    .hero-stats,
    .stat-item {
        contain: layout size style;
    }
    
    /* 确保图片不会导致布局偏移 */
    .phone-screen {
        background: #0F172A;
        display: block;
    }
    
    /* 预设加载状态 */
    .phone-screen:not(.loaded) {
        background: linear-gradient(135deg, #0F172A 0%, #1e293b 100%);
    }
}

/* 触摸优化 */
@media (max-width: 480px) {
    /* 增大触摸目标 */
    .btn,
    .mobile-menu-toggle {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* 优化点击区域 */
    .stat-item {
        cursor: default;
        -webkit-tap-highlight-color: transparent;
    }
}

/* 高DPI屏幕优化 */
@media (max-width: 375px) and (-webkit-min-device-pixel-ratio: 2) {
    .hero h1,
    .hero-subtitle {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .phone-mockup {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}
