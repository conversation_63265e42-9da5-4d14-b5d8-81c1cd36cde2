import AppKit
import Foundation

class ImageCompressionTest {

    static func compressImage(imagePath: String) {
        guard let nsImage = NSImage(contentsOfFile: imagePath) else {
            print("❌ 无法加载图片: \(imagePath)")
            return
        }

        // 转换为CGImage
        guard let cgImage = nsImage.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            print("❌ 无法转换图片格式")
            return
        }
        
        let originalSize = nsImage.size
        let originalPixels = originalSize.width * originalSize.height

        print("📸 原始图片信息:")
        print("   📏 尺寸: \(Int(originalSize.width))x\(Int(originalSize.height))")
        print("   📊 像素: \(String(format: "%.1f", originalPixels/1_000_000))M像素")

        // 测试不同压缩质量
        let qualities: [CGFloat] = [0.8, 0.5, 0.2, 0.1]
        let qualityNames = ["80%", "50%", "20%", "10%"]

        for (index, quality) in qualities.enumerated() {
            // 创建NSBitmapImageRep
            let bitmapRep = NSBitmapImageRep(cgImage: cgImage)

            // 设置JPEG压缩属性
            let properties: [NSBitmapImageRep.PropertyKey: Any] = [
                .compressionFactor: quality
            ]

            guard let compressedData = bitmapRep.representation(using: .jpeg, properties: properties) else {
                print("❌ 压缩失败: \(qualityNames[index])")
                continue
            }
            
            let sizeKB = compressedData.count / 1024
            let sizeMB = Double(compressedData.count) / (1024 * 1024)
            
            // 保存压缩后的图片
            let outputPath = "/Users/<USER>/Desktop/compressed_\(qualityNames[index]).jpg"
            
            do {
                try compressedData.write(to: URL(fileURLWithPath: outputPath))
                print("✅ \(qualityNames[index])压缩完成:")
                print("   📦 文件大小: \(sizeKB)KB (\(String(format: "%.2f", sizeMB))MB)")
                print("   💾 保存路径: \(outputPath)")
            } catch {
                print("❌ 保存失败 \(qualityNames[index]): \(error)")
            }
        }
        
        // 估算原始文件大小
        let bitmapRep = NSBitmapImageRep(cgImage: cgImage)
        if let originalData = bitmapRep.representation(using: .png, properties: [:]) {
            let originalSizeKB = originalData.count / 1024
            let originalSizeMB = Double(originalData.count) / (1024 * 1024)
            print("\n📊 原始PNG估算大小: \(originalSizeKB)KB (\(String(format: "%.2f", originalSizeMB))MB)")
        }

        // 显示原始JPEG文件大小
        do {
            let originalFileData = try Data(contentsOf: URL(fileURLWithPath: imagePath))
            let originalFileKB = originalFileData.count / 1024
            let originalFileMB = Double(originalFileData.count) / (1024 * 1024)
            print("📊 原始JPEG文件大小: \(originalFileKB)KB (\(String(format: "%.2f", originalFileMB))MB)")
        } catch {
            print("❌ 无法读取原始文件大小: \(error)")
        }
    }
}

// 执行压缩测试
let imagePath = "/Users/<USER>/last_mile_task_photos/photo_2025-06-27 15.03.09.jpeg"
ImageCompressionTest.compressImage(imagePath: imagePath)
