# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## User settings
xcuserdata/

## compatibility with Xcode 8 and earlier (ignoring not required starting Xcode 9)
*.xcscmblueprint
*.xccheckout

## compatibility with Xcode 3 and earlier (ignoring not required starting Xcode 4)
build/
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3

## Obj-C/Swift specific
*.hmap

## App packaging
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace

# Swift Package Manager
.build/

# CocoaPods
Carthage/Build/

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Code Injection
iOSInjectionProject/

# macOS
.DS_Store

# Node.js (for any web tools)
node_modules/

# Python (for scripts)
__pycache__/
*.py[cod]

# Temporary files
*.tmp
*.temp
*~

# IDE files
.vscode/
.idea/

# Backup files
*.backup
*.bak

# Environment variables and sensitive data
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.json
secrets.json
*.pem
*.p12
*.mobileprovision

# Log files
*.log

# Build artifacts specific to this project
*.app
*.framework

# Localization backup files (if they contain sensitive data)
# localization_backups/
# localization_reports/

# Test scripts and temporary test files
TestScripts/
test_*.swift
