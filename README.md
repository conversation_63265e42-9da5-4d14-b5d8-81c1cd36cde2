# NaviBatch - 智能批量导航应用

NaviBatch是一个专为快递员、外卖员等配送人员设计的智能批量导航应用，支持多点路线优化、AI地址识别、实时导航等功能。

## ✨ 主要功能

- 🚚 **批量导航**: 一次性导入多个配送地址，智能规划最优路线
- 🤖 **AI识别**: 支持图片、PDF、视频中的地址自动识别
- 📱 **多平台支持**: 兼容SpeedX、Amazon Flex、GoFo等主流配送平台
- 🗺️ **实时导航**: 集成Apple Maps，提供精准导航服务
- 🌍 **多语言**: 支持25+种语言，全球化配送服务
- 🎯 **智能优化**: 基于距离、时间、交通状况的路线优化

## 🏗️ 项目结构

```
NaviBatch/
├── NaviBatch/              # 主应用代码
│   ├── Views/              # UI界面
│   ├── ViewModels/         # 业务逻辑
│   ├── Models/             # 数据模型
│   ├── Services/           # 核心服务
│   ├── Managers/           # 管理器
│   └── Utilities/          # 工具类
├── NaviBatchTests/         # 单元测试
├── NaviBatchUITests/       # UI测试
├── docs/                   # 项目文档
├── TestScripts/            # 测试脚本
└── functions/              # Firebase云函数
```

## 📚 文档

详细文档请查看 [docs/](docs/) 目录：

- [📖 文档中心](docs/README.md) - 完整文档导航
- [🔧 开发文档](docs/development/) - 技术实现细节
- [🔨 修复记录](docs/fixes/) - 问题修复历史
- [📱 发布管理](docs/releases/) - 版本发布信息
- [📢 营销推广](docs/marketing/) - 推广材料

## 🚀 快速开始

### 环境要求

- iOS 15.0+
- Xcode 14.0+
- Swift 5.7+

### 安装步骤

1. 克隆项目
```bash
git clone [repository-url]
cd NaviBatch
```

2. 安装依赖
```bash
# 如果使用CocoaPods
pod install

# 如果使用Swift Package Manager
# 在Xcode中打开项目，依赖会自动解析
```

3. 配置Firebase
```bash
# 将Firebase配置文件放置到项目中
cp GoogleService-Info.plist NaviBatch/
```

4. 运行项目
```bash
# 在Xcode中打开NaviBatch.xcworkspace
# 选择目标设备并运行
```

## 🧪 测试

```bash
# 运行所有测试
./TestScripts/run_all_tests.sh

# 运行特定测试
xcodebuild test -workspace NaviBatch.xcworkspace -scheme NaviBatch -destination 'platform=iOS Simulator,name=iPhone 14'
```

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目主页: [NaviBatch官网](https://navibatch.com)
- 问题反馈: [GitHub Issues](../../issues)
- 邮箱: <EMAIL>

---

*NaviBatch - 让配送更智能，让路线更高效*
