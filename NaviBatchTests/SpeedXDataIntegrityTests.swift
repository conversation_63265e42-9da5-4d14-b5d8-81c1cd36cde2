import XCTest
@testable import NaviBatch

class SpeedXDataIntegrityTests: XCTestCase {
    
    // MARK: - 总数验证测试
    func testTotalCountValidation() {
        // 测试总数验证逻辑
        let recognizedCount = 5
        let expectedTotal = 88
        
        let completeness = Double(recognizedCount) / Double(expectedTotal) * 100
        
        XCTAssertEqual(completeness, 5.7, accuracy: 0.1, "完整度计算应该正确")
        XCTAssertLessThan(completeness, 100, "识别数量少于预期总数")
    }
    
    // MARK: - 缺失停靠点检测测试
    func testMissingStopNumberDetection() {
        let existingStopNumbers = [16, 17, 18, 19, 20]
        let expectedTotal = 88
        
        let missingNumbers = findMissingNumbers(
            existing: existingStopNumbers, 
            expectedTotal: expectedTotal
        )
        
        // 应该检测到缺失的停靠点1-15和21-88
        let expectedMissing = Array(1...15) + Array(21...88)
        
        XCTAssertEqual(missingNumbers.count, 83, "应该检测到83个缺失的停靠点")
        XCTAssertEqual(Set(missingNumbers), Set(expectedMissing), "缺失的停靠点应该正确")
    }
    
    // MARK: - 占位符创建测试
    func testPlaceholderCreation() {
        let missingStopNumber = 3
        let placeholder = createPlaceholder(for: missingStopNumber)
        
        XCTAssertTrue(placeholder.contains("缺失地址"), "占位符应该包含缺失地址标记")
        XCTAssertTrue(placeholder.contains("THIRD_PARTY_SORT:3"), "占位符应该包含正确的停靠点号码")
        XCTAssertTrue(placeholder.contains("placeholder:true"), "占位符应该有标记")
    }
    
    // MARK: - 数据完整性验证测试
    func testDataIntegrityValidation() {
        let testData = SpeedXTestData()
        testData.userInputTotalCount = 88
        testData.recognizedAddresses = [
            ("123 Main St|THIRD_PARTY_SORT:16|TRACKING:SPXSF001", 0.8),
            ("456 Oak Ave|THIRD_PARTY_SORT:17|TRACKING:SPXSF002", 0.8),
            ("789 Pine St|THIRD_PARTY_SORT:18|TRACKING:SPXSF003", 0.8),
        ]
        
        let result = testData.performValidation()
        
        XCTAssertEqual(result.totalCount, 88, "最终结果应该有88个地址")
        XCTAssertEqual(result.validAddressCount, 3, "应该有3个有效地址")
        XCTAssertEqual(result.placeholderCount, 85, "应该有85个占位符")
    }
    
    // MARK: - 停靠点唯一性测试
    func testStopNumberUniqueness() {
        let addresses = [
            "123 Main St|THIRD_PARTY_SORT:1|TRACKING:SPXSF001",
            "456 Oak Ave|THIRD_PARTY_SORT:2|TRACKING:SPXSF002",
            "789 Pine St|THIRD_PARTY_SORT:1|TRACKING:SPXSF003", // 重复停靠点
        ]
        
        let duplicates = findDuplicateStopNumbers(addresses)
        
        XCTAssertEqual(duplicates.count, 1, "应该检测到1个重复的停靠点")
        XCTAssertEqual(duplicates.first, "1", "重复的停靠点应该是1")
    }
    
    // MARK: - 地址标准化测试
    func testAddressNormalization() {
        let address1 = "123 Main St, Daly City, CA"
        let address2 = "123 MAIN ST, DALY CITY, CA"
        let address3 = "123  Main   St,  Daly  City,  CA"
        
        let normalized1 = normalizeAddress(address1)
        let normalized2 = normalizeAddress(address2)
        let normalized3 = normalizeAddress(address3)
        
        XCTAssertEqual(normalized1, normalized2, "大小写不同的地址应该标准化为相同")
        XCTAssertEqual(normalized1, normalized3, "空格不同的地址应该标准化为相同")
    }
    
    // MARK: - 序号连续性测试
    func testSequenceContinuity() {
        let stopNumbers = [1, 2, 4, 5, 7, 8, 10]
        let gaps = findSequenceGaps(stopNumbers)
        
        let expectedGaps = [3, 6, 9]
        XCTAssertEqual(gaps, expectedGaps, "应该正确检测序号间隙")
    }
    
    // MARK: - 性能测试
    func testLargeDataSetPerformance() {
        measure {
            let largeDataSet = generateLargeTestData(count: 1000)
            let result = processLargeDataSet(largeDataSet)
            XCTAssertEqual(result.count, 1000, "应该处理所有数据")
        }
    }
    
    // MARK: - Helper Methods
    
    private func findMissingNumbers(existing: [Int], expectedTotal: Int) -> [Int] {
        var missing: [Int] = []
        for i in 1...expectedTotal {
            if !existing.contains(i) {
                missing.append(i)
            }
        }
        return missing
    }
    
    private func createPlaceholder(for stopNumber: Int) -> String {
        var placeholder = "【缺失地址 - 需要补充】"
        placeholder += "|INTERNAL_SORT:\(stopNumber)"
        placeholder += "|THIRD_PARTY_SORT:\(stopNumber)"
        placeholder += "|placeholder:true"
        return placeholder
    }
    
    private func findDuplicateStopNumbers(_ addresses: [String]) -> [String] {
        var stopNumberCounts: [String: Int] = [:]
        
        for address in addresses {
            if let range = address.range(of: "THIRD_PARTY_SORT:") {
                let afterPrefix = address[range.upperBound...]
                if let endRange = afterPrefix.range(of: "|") {
                    let stopNumber = String(afterPrefix[..<endRange.lowerBound])
                    stopNumberCounts[stopNumber, default: 0] += 1
                }
            }
        }
        
        return stopNumberCounts.filter { $0.value > 1 }.map { $0.key }
    }
    
    private func normalizeAddress(_ address: String) -> String {
        return address
            .lowercased()
            .replacingOccurrences(of: #"\s+"#, with: " ", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    private func findSequenceGaps(_ numbers: [Int]) -> [Int] {
        let sortedNumbers = numbers.sorted()
        var gaps: [Int] = []
        
        guard let first = sortedNumbers.first, let last = sortedNumbers.last else {
            return gaps
        }
        
        for i in first...last {
            if !sortedNumbers.contains(i) {
                gaps.append(i)
            }
        }
        
        return gaps
    }
    
    private func generateLargeTestData(count: Int) -> [String] {
        return (1...count).map { i in
            "Address \(i), City, State|THIRD_PARTY_SORT:\(i)|TRACKING:SPXSF\(String(format: "%06d", i))"
        }
    }
    
    private func processLargeDataSet(_ data: [String]) -> [String] {
        // 模拟数据处理
        return data.filter { !$0.isEmpty }
    }
}

// MARK: - Test Data Structures

class SpeedXTestData {
    var userInputTotalCount: Int?
    var recognizedAddresses: [(String, Double)] = []
    
    func performValidation() -> ValidationResult {
        let validCount = recognizedAddresses.count
        let totalCount = userInputTotalCount ?? validCount
        let placeholderCount = totalCount - validCount
        
        return ValidationResult(
            totalCount: totalCount,
            validAddressCount: validCount,
            placeholderCount: placeholderCount
        )
    }
}

struct ValidationResult {
    let totalCount: Int
    let validAddressCount: Int
    let placeholderCount: Int
}

// MARK: - Integration Tests

class SpeedXIntegrationTests: XCTestCase {
    
    func testCompleteWorkflow() {
        // 测试完整的工作流程
        let workflow = SpeedXWorkflow()
        
        // 1. 用户输入总数
        workflow.setTotalCount(88)
        
        // 2. AI识别部分数据
        workflow.addRecognizedAddresses([
            "123 Main St|THIRD_PARTY_SORT:16|TRACKING:SPXSF001",
            "456 Oak Ave|THIRD_PARTY_SORT:17|TRACKING:SPXSF002",
            "789 Pine St|THIRD_PARTY_SORT:18|TRACKING:SPXSF003",
        ])
        
        // 3. 执行验证和补全
        let result = workflow.executeValidation()
        
        // 4. 验证结果
        XCTAssertEqual(result.totalAddresses, 88, "应该有88个地址")
        XCTAssertEqual(result.validAddresses, 3, "应该有3个有效地址")
        XCTAssertEqual(result.placeholders, 85, "应该有85个占位符")
        XCTAssertTrue(result.isSequentiallyOrdered, "地址应该按停靠点排序")
    }
}

// MARK: - Mock Workflow

class SpeedXWorkflow {
    private var totalCount: Int?
    private var addresses: [String] = []
    
    func setTotalCount(_ count: Int) {
        totalCount = count
    }
    
    func addRecognizedAddresses(_ newAddresses: [String]) {
        addresses.append(contentsOf: newAddresses)
    }
    
    func executeValidation() -> WorkflowResult {
        let validCount = addresses.count
        let total = totalCount ?? validCount
        let placeholders = total - validCount
        
        return WorkflowResult(
            totalAddresses: total,
            validAddresses: validCount,
            placeholders: placeholders,
            isSequentiallyOrdered: true
        )
    }
}

struct WorkflowResult {
    let totalAddresses: Int
    let validAddresses: Int
    let placeholders: Int
    let isSequentiallyOrdered: Bool
}
