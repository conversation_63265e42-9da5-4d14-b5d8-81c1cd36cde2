import XCTest
@testable import NaviBatch

/// 测试地址编辑修复功能
/// 验证编辑地址时 originalAddress 字段是否正确更新
class AddressEditFixTest: XCTestCase {

    func testOriginalAddressUpdate() {
        // 创建一个测试用的 DeliveryPoint
        let originalLongAddress = "1059 Wildwood Ave 1059 Wildwood Ave, Daly City, CA, USA Inna Belyaev &"
        let newShortAddress = "1059 Wildwood Ave, Daly City, CA, 94015, United States"

        let deliveryPoint = DeliveryPoint(
            sort_number: 11,
            originalAddress: originalLongAddress,
            latitude: 37.7749,
            longitude: -122.4194
        )

        // 验证初始状态
        XCTAssertEqual(deliveryPoint.originalAddress, originalLongAddress)
        XCTAssertEqual(deliveryPoint.primaryAddress, originalLongAddress)

        // 模拟用户编辑地址
        deliveryPoint.originalAddress = newShortAddress

        // 验证更新后的状态
        XCTAssertEqual(deliveryPoint.originalAddress, newShortAddress)
        XCTAssertEqual(deliveryPoint.primaryAddress, newShortAddress)

        print("✅ 测试通过：originalAddress 字段正确更新")
        print("   原地址: \(originalLongAddress)")
        print("   新地址: \(newShortAddress)")
        print("   显示地址: \(deliveryPoint.primaryAddress)")
    }

    func testStructuredAddressUpdate() {
        // 测试结构化地址更新
        let deliveryPoint = DeliveryPoint(
            sort_number: 12,
            streetNumber: "1240",
            streetName: "S Mayfair Ave",
            city: "Daly City",
            state: "CA",
            originalAddress: "1240 S Mayfair Ave, Daly City, CA, USA Myat Noe N... C",
            latitude: 37.7749,
            longitude: -122.4194
        )

        let newAddress = "1240 S Mayfair Ave, Daly City, CA, 94015, United States"

        // 模拟地址编辑
        deliveryPoint.originalAddress = newAddress

        // 验证更新
        XCTAssertEqual(deliveryPoint.originalAddress, newAddress)
        XCTAssertEqual(deliveryPoint.primaryAddress, newAddress)

        print("✅ 测试通过：结构化地址的 originalAddress 字段正确更新")
    }
}
