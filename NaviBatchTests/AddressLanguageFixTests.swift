import XCTest
import CoreLocation
@testable import NaviBatch

/// 地址语言修复测试
/// 验证地址显示语言修复功能是否正常工作
class AddressLanguageFixTests: XCTestCase {
    
    override func setUpWithError() throws {
        // 测试前设置
    }
    
    override func tearDownWithError() throws {
        // 测试后清理
    }
    
    // MARK: - 中文字符检测测试
    
    func testChineseCharacterDetection() throws {
        // 测试中文字符检测功能
        XCTAssertTrue(AddressStandardizer.containsChineseCharacters("旧金山"))
        XCTAssertTrue(AddressStandardizer.containsChineseCharacters("2–16 Ellis Street, 旧金山, CA"))
        XCTAssertTrue(AddressStandardizer.containsChineseCharacters("墨尔本"))
        XCTAssertTrue(AddressStandardizer.containsChineseCharacters("北京市朝阳区"))
        
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("San Francisco"))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("2–16 Ellis Street, San Francisco, CA"))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("Melbourne"))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters(""))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("123"))
    }
    
    // MARK: - 地址标准化测试
    
    func testAddressStandardization() throws {
        // 测试地址标准化不会引入中文
        let englishAddress = "123 Main St, San Francisco, CA, 94102, US"
        let standardized = AddressStandardizer.standardizeAddress(englishAddress)
        
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters(standardized))
        XCTAssertTrue(standardized.contains("Street")) // 应该展开缩写
    }
    
    // MARK: - 地址翻译测试
    
    func testAddressTranslation() throws {
        // 测试中文地址翻译为英文
        let chineseAddress = "北京市朝阳区建国门外大街1号"
        let translated = AddressStandardizer.translateAddressToEnglish(chineseAddress)
        
        XCTAssertNotEqual(translated, chineseAddress)
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters(translated))
    }
    
    // MARK: - 地理编码候选地址测试
    
    func testGeocodingCandidates() throws {
        // 测试地理编码候选地址生成
        let mixedAddress = "2–16 Ellis Street, 旧金山, CA"
        let candidates = AddressStandardizer.generateGeocodingCandidates(mixedAddress)
        
        XCTAssertGreaterThan(candidates.count, 1)
        
        // 应该包含翻译后的英文版本
        let hasEnglishCandidate = candidates.contains { candidate in
            candidate.contains("San Francisco") && !AddressStandardizer.containsChineseCharacters(candidate)
        }
        XCTAssertTrue(hasEnglishCandidate, "应该包含英文翻译候选地址")
    }
    
    // MARK: - 边界情况测试
    
    func testEdgeCases() throws {
        // 测试边界情况
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters(""))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("   "))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("123"))
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters("ABC"))
        
        // 测试混合字符
        XCTAssertTrue(AddressStandardizer.containsChineseCharacters("ABC中文"))
        XCTAssertTrue(AddressStandardizer.containsChineseCharacters("123中"))
    }
    
    // MARK: - 地址格式测试
    
    func testAddressFormatConsistency() throws {
        // 测试地址格式一致性
        let addresses = [
            "2–16 Ellis Street, San Francisco, CA, 94108, US",
            "123 Main St, Melbourne, VIC, 3000, AU",
            "456 Queen Street, Toronto, ON, M5V 2A8, CA"
        ]
        
        for address in addresses {
            let standardized = AddressStandardizer.standardizeAddress(address)
            
            // 标准化后应该仍然是英文
            XCTAssertFalse(AddressStandardizer.containsChineseCharacters(standardized))
            
            // 应该包含完整的街道类型
            XCTAssertTrue(standardized.contains("Street") || standardized.contains("Avenue") || standardized.contains("Road"))
        }
    }
    
    // MARK: - 性能测试
    
    func testChineseCharacterDetectionPerformance() throws {
        // 测试中文字符检测性能
        let testStrings = [
            "San Francisco, CA",
            "旧金山，加利福尼亚",
            "2–16 Ellis Street, San Francisco, CA, 94108, US",
            "北京市朝阳区建国门外大街1号",
            "123 Main Street, Melbourne, VIC, Australia"
        ]
        
        measure {
            for _ in 0..<1000 {
                for testString in testStrings {
                    _ = AddressStandardizer.containsChineseCharacters(testString)
                }
            }
        }
    }
}

// MARK: - 模拟测试扩展

extension AddressLanguageFixTests {
    
    /// 模拟地理编码结果验证
    func testMockGeocodingResults() throws {
        // 模拟Apple Maps可能返回的不同语言结果
        let mockResults = [
            ("San Francisco", false), // 英文，应该接受
            ("旧金山", true),          // 中文，应该拒绝
            ("Melbourne", false),     // 英文，应该接受
            ("墨尔本", true),          // 中文，应该拒绝
        ]
        
        for (locality, shouldReject) in mockResults {
            let hasChineseCharacters = AddressStandardizer.containsChineseCharacters(locality)
            XCTAssertEqual(hasChineseCharacters, shouldReject, "地址 '\(locality)' 的中文检测结果不正确")
        }
    }
    
    /// 测试搜索结果过滤逻辑
    func testSearchResultFiltering() throws {
        // 模拟搜索结果
        struct MockSearchResult {
            let title: String
            let subtitle: String
        }
        
        let mockResults = [
            MockSearchResult(title: "Ellis Street", subtitle: "San Francisco, CA, US"),
            MockSearchResult(title: "Ellis Street", subtitle: "旧金山, CA, US"),
            MockSearchResult(title: "Main Street", subtitle: "Melbourne, VIC, AU"),
            MockSearchResult(title: "Main Street", subtitle: "墨尔本, VIC, AU"),
        ]
        
        // 过滤掉包含中文的结果
        let englishResults = mockResults.filter { result in
            let fullText = "\(result.title) \(result.subtitle)"
            return !AddressStandardizer.containsChineseCharacters(fullText)
        }
        
        XCTAssertEqual(englishResults.count, 2, "应该过滤掉2个包含中文的结果")
        
        // 验证剩余结果都是英文
        for result in englishResults {
            let fullText = "\(result.title) \(result.subtitle)"
            XCTAssertFalse(AddressStandardizer.containsChineseCharacters(fullText))
        }
    }
    
    /// 测试语言环境配置
    func testLanguageLocaleConfiguration() throws {
        // 测试英文语言环境配置
        let englishLocales = [
            "en_US",
            "en_GB", 
            "en_AU",
            "en"
        ]
        
        for localeIdentifier in englishLocales {
            let locale = Locale(identifier: localeIdentifier)
            XCTAssertNotNil(locale)
            XCTAssertTrue(locale.identifier.hasPrefix("en"))
        }
    }
    
    /// 测试地址翻译映射
    func testAddressTranslationMapping() throws {
        // 测试常见中文地名翻译
        let translations = [
            ("旧金山", "San Francisco"),
            ("墨尔本", "Melbourne"),
            ("纽约", "New York"),
            ("洛杉矶", "Los Angeles"),
            ("堪萨斯城", "Kansas City")  // 新增堪萨斯城测试
        ]

        for (chinese, expectedEnglish) in translations {
            let translated = AddressStandardizer.translateAddressToEnglish(chinese)
            XCTAssertTrue(translated.contains(expectedEnglish), "翻译结果应该包含 '\(expectedEnglish)'")
            XCTAssertFalse(AddressStandardizer.containsChineseCharacters(translated), "翻译结果不应该包含中文字符")
        }
    }

    /// 测试堪萨斯城中文字符检测
    func testKansasCityChineseDetection() throws {
        // 测试堪萨斯城的中文字符检测
        let chineseCityNames = ["堪萨斯城", "堪薩斯城"]  // 简体和繁体
        let englishCityName = "Kansas City"

        for chineseName in chineseCityNames {
            // 验证中文字符检测
            XCTAssertTrue(AddressStandardizer.containsChineseCharacters(chineseName), "应该检测到中文字符: \(chineseName)")
        }

        // 验证英文版本不包含中文字符
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters(englishCityName), "英文城市名不应该包含中文字符")

        // 测试完整地址格式
        let chineseAddress = "9536 Drury Ave，堪萨斯城, MO 64130，美国"
        let englishAddress = "9536 Drury Ave, Kansas City, MO 64130, United States"

        XCTAssertTrue(AddressStandardizer.containsChineseCharacters(chineseAddress), "中文地址应该被检测到")
        XCTAssertFalse(AddressStandardizer.containsChineseCharacters(englishAddress), "英文地址不应该包含中文字符")
    }
}
