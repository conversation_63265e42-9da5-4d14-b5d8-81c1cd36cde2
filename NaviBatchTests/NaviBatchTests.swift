//
//  NaviBatchTests.swift
//  NaviBatchTests
//
//  Created by <PERSON> on 19/4/2025.
//  Enhanced by Augment Agent
//

import Testing
import CoreLocation
import SwiftData
@testable import NaviBatch

/// 主测试套件 - 基础功能测试
struct NaviBatchTests {

    @Test func example() async throws {
        // 基础示例测试
        #expect(true == true)
    }

    // MARK: - 地址标准化测试

    @Test func testAddressStandardization() async throws {
        // 测试澳大利亚地址标准化
        let australianAddress = "123 Main St, Glen Waverley, VIC 3150"
        let standardized = AddressStandardizer.standardizeAddress(australianAddress)

        #expect(standardized.contains("Street"))
        #expect(standardized.contains("Victoria"))
    }

    @Test func testChineseAddressTranslation() async throws {
        // 测试中文地址翻译
        let chineseAddress = "北京市朝阳区建国门外大街1号"
        let translated = AddressStandardizer.translateAddressToEnglish(chineseAddress)

        #expect(translated != chinese<PERSON>dd<PERSON>)
        #expect(!AddressStandardizer.containsChineseCharacters(translated))
    }

    @Test func testHongKongAddressHandling() async throws {
        // 测试香港地址处理
        let hkAddress = "中環德輔道中1號"
        let translated = AddressStandardizer.translateAddressToEnglish(hkAddress)

        #expect(translated.contains("Central") || translated.contains("Des Voeux Road"))
    }

    // MARK: - 地理编码候选地址生成测试

    @Test func testGeocodingCandidateGeneration() async throws {
        // 测试地理编码候选地址生成
        let address = "404/23 Main Street, Glen Waverley, VIC 3150"
        let candidates = AddressStandardizer.generateGeocodingCandidates(address)

        #expect(candidates.count > 1)
        #expect(candidates.contains(address)) // 原始地址应该在候选列表中

        // 应该包含简化的地址（移除单元号）
        let hasSimplified = candidates.contains { $0.contains("23 Main Street") && !$0.contains("404/") }
        #expect(hasSimplified)
    }
}
