import XCTest
@testable import NaviBatch

class StopNumberPriorityTests: XCTestCase {
    
    // MARK: - 场景1：相同地址+相同停靠点（重复数据）
    func testSameAddressSameStopNumber() {
        let testData = [
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001",
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001", // 重复
        ]
        
        let result = processStopNumberConflicts(testData)
        
        XCTAssertEqual(result.count, 1, "应该只保留一个地址")
        XCTAssertTrue(result[0].contains("123 Main St"), "应该保留完整地址")
        XCTAssertTrue(result[0].contains("THIRD_PARTY_SORT:1"), "应该保留停靠点1")
    }
    
    // MARK: - 场景2：不同地址+相同停靠点（冲突）
    func testDifferentAddressSameStopNumber() {
        let testData = [
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001",
            "456 Oak Ave, San Francisco, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF002", // 冲突
        ]
        
        let result = processStopNumberConflicts(testData)
        
        XCTAssertEqual(result.count, 1, "应该只保留一个地址（停靠点唯一）")
        XCTAssertTrue(result[0].contains("123 Main St"), "应该保留第一个出现的完整地址")
    }
    
    // MARK: - 场景3：不完整地址+停靠点冲突
    func testIncompleteAddressConflict() {
        let testData = [
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001",
            "456 Oak...|THIRD_PARTY_SORT:1", // 不完整，截图不全
        ]
        
        let result = processStopNumberConflicts(testData)
        
        XCTAssertEqual(result.count, 1, "应该只保留完整地址")
        XCTAssertTrue(result[0].contains("123 Main St"), "应该保留完整地址")
        XCTAssertFalse(result.joined().contains("456 Oak"), "应该忽略不完整地址")
    }
    
    // MARK: - 场景4：相同地址+不同停靠点（合理）
    func testSameAddressDifferentStopNumber() {
        let testData = [
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001",
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:2|TRACKING:SPXSF002", // 同一收件人多个包裹
        ]
        
        let result = processStopNumberConflicts(testData)
        
        XCTAssertEqual(result.count, 2, "应该保留两个地址（不同停靠点）")
        XCTAssertTrue(result.contains { $0.contains("THIRD_PARTY_SORT:1") }, "应该保留停靠点1")
        XCTAssertTrue(result.contains { $0.contains("THIRD_PARTY_SORT:2") }, "应该保留停靠点2")
    }
    
    // MARK: - 场景5：复杂混合情况
    func testComplexMixedScenario() {
        let testData = [
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001",
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:1|TRACKING:SPXSF001", // 重复
            "123 Main St, Daly City, CA|THIRD_PARTY_SORT:2|TRACKING:SPXSF002", // 同地址不同停靠点
            "456 Oak Ave|THIRD_PARTY_SORT:1", // 不完整地址冲突
            "789 Pine St, San Francisco, CA|THIRD_PARTY_SORT:3|TRACKING:SPXSF003", // 正常
        ]
        
        let result = processStopNumberConflicts(testData)
        
        XCTAssertEqual(result.count, 3, "应该保留3个有效地址")
        
        // 验证停靠点1：应该保留完整的123 Main St
        let stopNumber1 = result.first { $0.contains("THIRD_PARTY_SORT:1") }
        XCTAssertNotNil(stopNumber1, "应该有停靠点1")
        XCTAssertTrue(stopNumber1!.contains("123 Main St"), "停靠点1应该是123 Main St")
        
        // 验证停靠点2：123 Main St的第二个包裹
        let stopNumber2 = result.first { $0.contains("THIRD_PARTY_SORT:2") }
        XCTAssertNotNil(stopNumber2, "应该有停靠点2")
        
        // 验证停靠点3：789 Pine St
        let stopNumber3 = result.first { $0.contains("THIRD_PARTY_SORT:3") }
        XCTAssertNotNil(stopNumber3, "应该有停靠点3")
    }
    
    // MARK: - 地址完整性测试
    func testAddressCompleteness() {
        // 完整地址
        let completeAddress = "123 Main St, Daly City, CA"
        let completeTracking = "SPXSF001"
        XCTAssertTrue(isAddressComplete(completeAddress, tracking: completeTracking), "完整地址应该被识别为完整")
        
        // 不完整地址（截图不全）
        let incompleteAddress = "123 Main..."
        let noTracking = ""
        XCTAssertFalse(isAddressComplete(incompleteAddress, tracking: noTracking), "不完整地址应该被识别为不完整")
        
        // 缺少城市
        let noCityAddress = "123 Main St"
        XCTAssertFalse(isAddressComplete(noCityAddress, tracking: completeTracking), "缺少城市的地址应该被识别为不完整")
    }
    
    // MARK: - 优先级选择测试
    func testBestAddressSelection() {
        let addresses = [
            (index: 0, address: "addr1", isComplete: false, confidence: 0.9),
            (index: 1, address: "addr2", isComplete: true, confidence: 0.7),
            (index: 2, address: "addr3", isComplete: true, confidence: 0.8),
        ]
        
        let best = selectBestAddress(from: addresses)
        
        // 应该选择完整性优先，然后置信度优先
        XCTAssertEqual(best.index, 2, "应该选择完整且置信度最高的地址")
        XCTAssertTrue(best.isComplete, "选择的地址应该是完整的")
        XCTAssertEqual(best.confidence, 0.8, "应该选择置信度最高的完整地址")
    }
    
    // MARK: - Helper Methods for Testing
    
    private func processStopNumberConflicts(_ addresses: [String]) -> [String] {
        // 模拟停靠点冲突解决逻辑
        var stopNumberGroups: [String: [String]] = [:]
        
        // 按停靠点分组
        for address in addresses {
            let stopNumber = extractStopNumber(from: address)
            if !stopNumber.isEmpty {
                if stopNumberGroups[stopNumber] == nil {
                    stopNumberGroups[stopNumber] = []
                }
                stopNumberGroups[stopNumber]?.append(address)
            }
        }
        
        var result: [String] = []
        
        // 处理每个停靠点的冲突
        for (stopNumber, addressGroup) in stopNumberGroups {
            if addressGroup.count > 1 {
                // 应用冲突解决策略
                let bestAddress = resolveMockConflict(addressGroup)
                result.append(bestAddress)
            } else {
                result.append(addressGroup[0])
            }
        }
        
        return result
    }
    
    private func extractStopNumber(from address: String) -> String {
        if let range = address.range(of: "THIRD_PARTY_SORT:") {
            let afterPrefix = address[range.upperBound...]
            if let endRange = afterPrefix.range(of: "|") {
                return String(afterPrefix[..<endRange.lowerBound])
            } else {
                return String(afterPrefix)
            }
        }
        return ""
    }
    
    private func resolveMockConflict(_ addresses: [String]) -> String {
        // 简化的冲突解决：选择最完整的地址
        return addresses.max { addr1, addr2 in
            let complete1 = isAddressComplete(extractAddress(from: addr1), tracking: extractTracking(from: addr1))
            let complete2 = isAddressComplete(extractAddress(from: addr2), tracking: extractTracking(from: addr2))
            
            if complete1 != complete2 {
                return !complete1 // complete2 > complete1
            }
            
            return addr1.count < addr2.count // 更长的地址可能更完整
        } ?? addresses[0]
    }
    
    private func extractAddress(from fullAddress: String) -> String {
        return fullAddress.components(separatedBy: "|")[0]
    }
    
    private func extractTracking(from fullAddress: String) -> String {
        if let range = fullAddress.range(of: "TRACKING:") {
            let afterPrefix = fullAddress[range.upperBound...]
            if let endRange = afterPrefix.range(of: "|") {
                return String(afterPrefix[..<endRange.lowerBound])
            } else {
                return String(afterPrefix)
            }
        }
        return ""
    }
    
    private func isAddressComplete(_ address: String, tracking: String) -> Bool {
        let hasStreetNumber = address.range(of: #"\d+"#, options: .regularExpression) != nil
        let hasCity = address.contains(",")
        let hasTracking = !tracking.isEmpty
        let hasTruncation = address.contains("...")
        
        return hasStreetNumber && hasCity && hasTracking && !hasTruncation && address.count > 10
    }
    
    private func selectBestAddress(
        from addresses: [(index: Int, address: String, isComplete: Bool, confidence: Double)]
    ) -> (index: Int, address: String, isComplete: Bool, confidence: Double) {
        
        return addresses.sorted { addr1, addr2 in
            if addr1.isComplete != addr2.isComplete {
                return addr1.isComplete
            }
            if abs(addr1.confidence - addr2.confidence) > 0.1 {
                return addr1.confidence > addr2.confidence
            }
            return addr1.index < addr2.index
        }[0]
    }
}
