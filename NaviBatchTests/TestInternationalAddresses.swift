import Foundation
@testable import NaviBatch

/// 快速测试国际地址验证功能
/// 在开发者工具中调用此函数来测试
class TestInternationalAddresses {

    /// 快速测试函数 - 可以在开发者工具中调用
    static func quickTest() {
        print("🌍 开始快速测试国际地址验证...")

        // 测试香港地址
        testHongKongAddresses()

        // 测试美国地址
        testUSAddresses()

        // 测试澳洲地址（确保向后兼容）
        testAustralianAddresses()

        print("✅ 快速测试完成！")
    }

    /// 测试香港地址
    private static func testHongKongAddresses() {
        print("\n🇭🇰 测试香港地址...")

        let hkAddresses = [
            "荃湾海盛路3号荃新天地2座15楼A室",
            "Central, Hong Kong",
            "荃湾大河道99号99广场28楼2801室, Hong Kong"
        ]

        for address in hkAddresses {
            let country = AddressCountryDetector.detectCountry(from: address)
            let standardized = AddressStandardizer.standardizeAddress(address)

            print("  📍 地址: \(address)")
            print("     国家: \(country?.name ?? "未识别") (\(country?.code ?? "N/A"))")
            print("     标准化: \(standardized)")
            print("     ✅ 检测正确: \(country?.code == "HK" ? "是" : "否")")
            print("")
        }
    }

    /// 测试美国地址
    private static func testUSAddresses() {
        print("\n🇺🇸 测试美国地址...")

        let usAddresses = [
            "123 Main St, New York, NY 10001",
            "456 Oak Ave, Los Angeles, CA 90210",
            "789 Broadway, New York, USA"
        ]

        for address in usAddresses {
            let country = AddressCountryDetector.detectCountry(from: address)
            let standardized = AddressStandardizer.standardizeAddress(address)

            print("  📍 地址: \(address)")
            print("     国家: \(country?.name ?? "未识别") (\(country?.code ?? "N/A"))")
            print("     标准化: \(standardized)")
            print("     ✅ 检测正确: \(country?.code == "US" ? "是" : "否")")
            print("")
        }
    }

    /// 测试澳洲地址（确保向后兼容）
    private static func testAustralianAddresses() {
        print("\n🇦🇺 测试澳洲地址（向后兼容）...")

        let ausAddresses = [
            "123 Collins St, Melbourne, VIC 3000",
            "456 George St, Sydney, NSW 2000, Australia",
            "Glen Waverley Shopping Centre"
        ]

        for address in ausAddresses {
            let country = AddressCountryDetector.detectCountry(from: address)
            let standardized = AddressStandardizer.standardizeAddress(address)

            print("  📍 地址: \(address)")
            print("     国家: \(country?.name ?? "未识别") (\(country?.code ?? "N/A"))")
            print("     标准化: \(standardized)")
            print("     ✅ 检测正确: \(country?.code == "AU" ? "是" : "否")")
            print("")
        }
    }

    /// 测试地址增强功能
    static func testAddressEnhancement() {
        print("\n🔧 测试地址增强功能...")

        // 这个函数需要访问UnifiedAddressValidationService的私有方法
        // 在实际使用中，可以通过公共API来测试
        print("地址增强功能需要通过实际的地址验证API来测试")
        print("建议在应用中使用真实地址进行测试")
    }

    /// 测试香港地址解析
    static func testHongKongAddressParsing() {
        print("\n🇭🇰 测试香港地址解析...")

        let testCases = [
            "荃湾海盛路3号荃新天地2座15楼A室",
            "荃湾大河道99号99广场28楼2801室",
            "荃湾青山公路荃湾段398号中染大厦12楼B座",
            "荃湾沙咀道68号荃湾千色汇I期商场3楼301号铺"
        ]

        for address in testCases {
            let components = AddressCountryDetector.parseHongKongAddress(address)

            print("  📍 地址: \(address)")
            print("     门牌号: '\(components.streetNumber)'")
            print("     街道名: '\(components.streetName)'")
            print("     建筑: '\(components.building)'")
            print("     楼层: '\(components.floor)'")
            print("     房间: '\(components.room)'")
            print("     ✅ 解析成功: \(!components.streetNumber.isEmpty && !components.streetName.isEmpty)")
            print("")
        }
    }
}
