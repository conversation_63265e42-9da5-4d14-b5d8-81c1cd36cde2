//
//  AppleMapsFormatTest.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//  测试Apple Maps优化格式转换
//

import XCTest
@testable import NaviBatch

class AppleMapsFormatTest: XCTestCase {
    
    var aiService: FirebaseAIService!
    
    override func setUp() {
        super.setUp()
        aiService = FirebaseAIService.shared
    }
    
    override func tearDown() {
        aiService = nil
        super.tearDown()
    }
    
    func testAppleMapsFormatConversion() {
        // 测试地址格式转换
        let testCases = [
            // 输入格式 -> 期望的Apple Maps优化格式
            (
                input: "572 Reina Del Mar Ave, Pacifica, CA, 94044, USA",
                expected: "572 Reina Del Mar Ave, Pacifica,CA"
            ),
            (
                input: "250 Campana Ave, Daly City, CA, 94015, USA",
                expected: "250 Campana Ave, Daly City,CA"
            ),
            (
                input: "175 Belhaven Ave 94015 The Mariana Arr... G house is white and gray, Daly...",
                expected: "175 Belhaven Ave, Daly City,CA"
            ),
            (
                input: "223 Belhaven Ave Casa, Daly City, CA, 94015, USA",
                expected: "223 Belhaven Ave Casa, Daly City,CA"
            ),
            (
                input: "42 Woodside Ave, Daly City, CA, 94015, USA",
                expected: "42 Woodside Ave, Daly City,CA"
            )
        ]
        
        for (index, testCase) in testCases.enumerated() {
            print("测试用例 \(index + 1):")
            print("  输入: \(testCase.input)")
            
            // 使用Apple Maps格式化器
            let formatted = AppleMapsAddressFormatter.formatForDatabaseStorage(testCase.input)
            print("  实际输出: \(formatted)")
            print("  期望输出: \(testCase.expected)")
            
            // 验证关键特征
            XCTAssertFalse(formatted.contains("USA"), "不应该包含USA")
            XCTAssertFalse(formatted.contains("94015") || formatted.contains("94044"), "不应该包含ZIP码")
            
            // 验证州简称格式（CA前无空格）
            if formatted.contains("CA") {
                XCTAssertTrue(formatted.contains(",CA"), "应该是,CA格式（无空格）")
                XCTAssertFalse(formatted.contains(", CA"), "不应该是, CA格式（有空格）")
            }
            
            print("  ✅ 格式验证通过")
            print("---")
        }
    }
    
    func testZipCodeRemoval() {
        let addressesWithZip = [
            "572 Reina Del Mar Ave, Pacifica, CA, 94044, USA",
            "250 Campana Ave, Daly City, CA, 94015, USA",
            "42 Woodside Ave, Daly City, CA, 94015, USA"
        ]
        
        for address in addressesWithZip {
            // 模拟ZIP码移除逻辑
            let withoutZip = address
                .replacingOccurrences(of: "\\s*,?\\s*\\b\\d{5}(-\\d{4})?\\b", with: "", options: .regularExpression)
                .replacingOccurrences(of: "\\s*,?\\s*USA\\s*$", with: "", options: .regularExpression)
                .replacingOccurrences(of: ",,+", with: ",", options: .regularExpression)
                .replacingOccurrences(of: ",$", with: "", options: .regularExpression)
                .trimmingCharacters(in: .whitespacesAndNewlines)
            
            print("原始: \(address)")
            print("处理后: \(withoutZip)")
            
            // 验证ZIP码已被移除
            XCTAssertFalse(withoutZip.range(of: "\\d{5}", options: .regularExpression) != nil, "ZIP码应该被移除: \(withoutZip)")
            XCTAssertFalse(withoutZip.contains("USA"), "USA应该被移除: \(withoutZip)")
        }
    }
    
    func testStateAbbreviationFormat() {
        let testAddresses = [
            "572 Reina Del Mar Ave, Pacifica, CA, 94044, USA",
            "250 Campana Ave, Daly City, CA, 94015, USA"
        ]
        
        for address in testAddresses {
            let formatted = AppleMapsAddressFormatter.formatForDatabaseStorage(address)
            
            // 验证州简称格式
            if formatted.contains("CA") {
                // 应该是 ",CA" 而不是 ", CA"
                XCTAssertTrue(formatted.contains(",CA"), "州简称前不应该有空格: \(formatted)")
                
                // 确保不是在其他位置的CA
                let components = formatted.components(separatedBy: ",")
                let lastComponent = components.last?.trimmingCharacters(in: .whitespaces)
                XCTAssertEqual(lastComponent, "CA", "最后一个组件应该是CA: \(formatted)")
            }
        }
    }
    
    func testComplexAddressHandling() {
        let complexAddress = "175 Belhaven Ave 94015 The Mariana Arr... G house is white and gray, Daly..."
        
        // 这种复杂地址需要特殊处理
        let formatted = AppleMapsAddressFormatter.formatForDatabaseStorage(complexAddress)
        
        print("复杂地址处理:")
        print("  原始: \(complexAddress)")
        print("  格式化: \(formatted)")
        
        // 基本验证
        XCTAssertTrue(formatted.contains("175 Belhaven Ave"), "应该保留街道地址")
        XCTAssertFalse(formatted.contains("94015"), "ZIP码应该被移除")
        
        // 这种情况可能需要额外的智能处理来添加城市和州信息
        // 目前先验证基本的清理功能
    }
}
