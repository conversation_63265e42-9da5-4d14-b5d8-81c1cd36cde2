//
//  DeliveryPointTests.swift
//  NaviBatchTests
//
//  Created by Augment Agent
//

import Testing
import CoreLocation
import SwiftData
@testable import NaviBatch

/// DeliveryPoint 模型测试套件
struct DeliveryPointTests {
    
    // MARK: - 初始化测试
    
    @Test func testDeliveryPointInitialization() async throws {
        // 测试基本初始化
        let point = DeliveryPoint(
            sort_number: 1,
            streetNumber: "123",
            streetName: "Main Street",
            suburb: "Glen Waverley",
            city: "Melbourne",
            state: "Victoria",
            postalCode: "3150",
            country: "Australia",
            latitude: -37.8136,
            longitude: 145.1628
        )
        
        #expect(point.sort_number == 1)
        #expect(point.streetNumber == "123")
        #expect(point.streetName == "Main Street")
        #expect(point.suburb == "Glen Waverley")
        #expect(point.latitude == -37.8136)
        #expect(point.longitude == 145.1628)
        #expect(point.deliveryStatus == .pending)
    }
    
    @Test func testDeliveryPointWithUnitNumber() async throws {
        // 测试包含单元号的地址
        let point = DeliveryPoint(
            sort_number: 2,
            streetNumber: "23",
            streetName: "Collins Street",
            suburb: "Melbourne",
            unitNumber: "404",
            latitude: -37.8136,
            longitude: 144.9631
        )
        
        #expect(point.unitNumber == "404")
        #expect(point.streetNumber == "23")
        #expect(point.streetName == "Collins Street")
    }
    
    // MARK: - 地址格式化测试
    
    @Test func testPrimaryAddressFormatting() async throws {
        // 测试主要地址格式化
        let point = DeliveryPoint(
            sort_number: 1,
            streetNumber: "123",
            streetName: "Main Street",
            suburb: "Glen Waverley",
            state: "VIC",
            postalCode: "3150",
            latitude: -37.8136,
            longitude: 145.1628
        )
        
        let primaryAddress = point.primaryAddress
        #expect(primaryAddress.contains("123"))
        #expect(primaryAddress.contains("Main Street"))
        #expect(primaryAddress.contains("Glen Waverley"))
    }
    
    @Test func testFormattedAddressWithUnit() async throws {
        // 测试包含单元号的格式化地址
        let point = DeliveryPoint(
            sort_number: 1,
            streetNumber: "23",
            streetName: "Collins Street",
            suburb: "Melbourne",
            unitNumber: "404",
            latitude: -37.8136,
            longitude: 144.9631
        )

        let primaryAddress = point.primaryAddress
        #expect(primaryAddress.contains("23"))
        #expect(primaryAddress.contains("Collins Street"))
        #expect(point.hasUnitNumber == true)
        #expect(point.unitNumber == "404")
    }
    
    // MARK: - 坐标验证测试
    
    @Test func testCoordinateValidation() async throws {
        // 测试有效坐标
        let validPoint = DeliveryPoint(
            sort_number: 1,
            latitude: -37.8136,
            longitude: 145.1628
        )

        let isValid = validPoint.validateCoordinates()
        #expect(isValid == true)
        #expect(validPoint.coordinateValidated == true)
        #expect(validPoint.isWithinValidRange == true)
    }

    @Test func testInvalidCoordinateValidation() async throws {
        // 测试无效坐标
        let invalidPoint = DeliveryPoint(
            sort_number: 1,
            latitude: 0.0,
            longitude: 0.0
        )

        let isValid = invalidPoint.validateCoordinates()
        #expect(isValid == false)
        #expect(invalidPoint.coordinateValidated == true)
        #expect(invalidPoint.isWithinValidRange == false)
    }
    
    @Test func testLocationBasedValidation() async throws {
        // 测试基于用户位置的验证
        let point = DeliveryPoint(
            sort_number: 1,
            latitude: -37.8136,
            longitude: 145.1628
        )

        // 墨尔本用户位置
        let userLocation = CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)
        point.validateCoordinatesGlobally(userLocation: userLocation)

        // 验证距离计算
        #expect(point.coordinateValidated == true)
        #expect(point.isWithinValidRange == true)
    }
    
    // MARK: - 状态管理测试
    
    @Test func testDeliveryStatusUpdate() async throws {
        // 测试配送状态更新
        let point = DeliveryPoint(
            sort_number: 1,
            latitude: -37.8136,
            longitude: 145.1628
        )

        // 初始状态应该是pending
        #expect(point.deliveryStatus == .pending)

        // 更新为completed
        point.status = DeliveryStatus.completed.rawValue
        point.statusUpdateTime = Date()
        #expect(point.deliveryStatus == .completed)

        // 更新为failed
        point.status = DeliveryStatus.failed.rawValue
        #expect(point.deliveryStatus == .failed)
    }
    
    @Test func testDeliveryTypeHandling() async throws {
        // 测试配送类型处理
        let point = DeliveryPoint(
            sort_number: 1,
            latitude: -37.8136,
            longitude: 145.1628
        )
        
        // 默认应该是delivery
        #expect(point.deliveryTypeEnum == .delivery)
        
        // 设置为pickup
        point.deliveryType = DeliveryType.pickup.rawValue
        #expect(point.deliveryTypeEnum == .pickup)
    }
    
    // MARK: - 计算属性测试
    
    @Test func testCoordinateProperty() async throws {
        // 测试坐标计算属性
        let point = DeliveryPoint(
            sort_number: 1,
            latitude: -37.8136,
            longitude: 145.1628
        )
        
        let coordinate = point.coordinate
        #expect(coordinate.latitude == -37.8136)
        #expect(coordinate.longitude == 145.1628)
    }
    
    @Test func testDeliveryStatusEnum() async throws {
        // 测试配送状态枚举
        let point = DeliveryPoint(
            sort_number: 1,
            latitude: -37.8136,
            longitude: 145.1628
        )

        // 测试默认状态
        #expect(point.deliveryStatus == .pending)

        // 测试状态更新
        point.status = DeliveryStatus.completed.rawValue
        #expect(point.deliveryStatus == .completed)

        point.status = DeliveryStatus.failed.rawValue
        #expect(point.deliveryStatus == .failed)
    }
}
