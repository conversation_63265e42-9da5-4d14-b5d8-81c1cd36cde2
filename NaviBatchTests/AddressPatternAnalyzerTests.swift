import XCTest
import Foundation
@testable import NaviBatch

/// 地址模式分析器测试
class AddressPatternAnalyzerTests: XCTestCase {

    var analyzer: AddressPatternAnalyzer!

    override func setUp() {
        super.setUp()
        analyzer = AddressPatternAnalyzer()
    }

    override func tearDown() {
        analyzer = nil
        super.tearDown()
    }

    // MARK: - 地址模式分析测试

    func testAnalyzeAddressPatterns_ConsecutiveNumbers() {
        // 测试连续门牌号模式
        let addresses = [
            "397 Imperial Way Apt 238, Daly City, CA, 94015|5|SPXSF00567511770",
            "399 Imperial Way Apt 240, Daly City, CA, 94015|7|SPXSF00567511771",
            "401 Imperial Way Apt 242, Daly City, CA, 94015|9|SPXSF00567511772"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)

        XCTAssertFalse(patterns.isEmpty, "应该检测到地址模式")

        let pattern = patterns.first!
        XCTAssertEqual(pattern.streetName, "Imperial Way")
        XCTAssertEqual(pattern.baseNumber, 397)
        XCTAssertEqual(pattern.increment, 1) // 每个停靠点递增1
        XCTAssertTrue(pattern.isOddSequence, "应该检测到奇数序列")
        XCTAssertEqual(pattern.city, "Daly City")
        XCTAssertEqual(pattern.state, "CA")
        XCTAssertEqual(pattern.zipCode, "94015")
        XCTAssertGreaterThan(pattern.confidence, 0.5, "置信度应该较高")
    }

    func testAnalyzeAddressPatterns_EvenNumbers() {
        // 测试偶数门牌号模式
        let addresses = [
            "100 Main St, San Francisco, CA, 94102|1|TRACK001",
            "102 Main St, San Francisco, CA, 94102|2|TRACK002",
            "104 Main St, San Francisco, CA, 94102|3|TRACK003"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)

        XCTAssertFalse(patterns.isEmpty, "应该检测到地址模式")

        let pattern = patterns.first!
        XCTAssertEqual(pattern.streetName, "Main St")
        XCTAssertEqual(pattern.baseNumber, 100)
        XCTAssertEqual(pattern.increment, 2) // 每个停靠点递增2
        XCTAssertFalse(pattern.isOddSequence, "应该检测到偶数序列")
    }

    func testAnalyzeAddressPatterns_WithApartments() {
        // 测试包含公寓号的地址
        let addresses = [
            "25 Hyde court #2, Daly City, CA, 94015|1|SPXSF00567514335",
            "30 Lycett Cir., Daly City, CA, 94015|2|SPXSF00550017256",
            "70 Margate st., Daly City, CA, 94015|3|SPXSF00567515071"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)

        // 这种情况下可能无法检测到明确的模式，因为街道名称不同
        // 但至少不应该崩溃
        XCTAssertNoThrow(patterns)
    }

    // MARK: - 地址预测测试

    func testPredictMissingAddresses_ConsecutivePattern() {
        // 先分析模式
        let addresses = [
            "397 Imperial Way Apt 238, Daly City, CA, 94015|5|SPXSF00567511770",
            "399 Imperial Way Apt 240, Daly City, CA, 94015|7|SPXSF00567511771"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)
        XCTAssertFalse(patterns.isEmpty, "应该检测到地址模式")

        // 预测缺失的停靠点6
        let missingStops = [6]
        let predictions = analyzer.predictMissingAddresses(missingStopNumbers: missingStops, patterns: patterns)

        XCTAssertEqual(predictions.count, 1, "应该预测出1个地址")

        let prediction = predictions.first!
        XCTAssertEqual(prediction.stopNumber, 6)
        XCTAssertTrue(prediction.fullAddress.contains("398 Imperial Way"), "应该预测出398号")
        XCTAssertTrue(prediction.fullAddress.contains("Daly City, CA, 94015"), "应该包含正确的城市和邮编")
        XCTAssertGreaterThan(prediction.confidence, 0.0, "应该有置信度")
    }

    func testPredictMissingAddresses_MultipleStops() {
        // 测试预测多个缺失停靠点
        let addresses = [
            "100 Oak St, San Jose, CA, 95110|1|TRACK001",
            "102 Oak St, San Jose, CA, 95110|2|TRACK002",
            "106 Oak St, San Jose, CA, 95110|4|TRACK004"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)
        let missingStops = [3, 5]
        let predictions = analyzer.predictMissingAddresses(missingStopNumbers: missingStops, patterns: patterns)

        XCTAssertEqual(predictions.count, 2, "应该预测出2个地址")

        // 检查预测结果按停靠点序号排序
        XCTAssertEqual(predictions[0].stopNumber, 3)
        XCTAssertEqual(predictions[1].stopNumber, 5)

        // 检查预测的门牌号
        XCTAssertTrue(predictions[0].fullAddress.contains("104 Oak St"), "停靠点3应该是104号")
        XCTAssertTrue(predictions[1].fullAddress.contains("108 Oak St"), "停靠点5应该是108号")
    }

    // MARK: - 边界情况测试

    func testAnalyzeAddressPatterns_InsufficientData() {
        // 测试数据不足的情况
        let addresses = [
            "123 Test St, Test City, CA, 12345|1|TRACK001"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)

        // 单个地址无法形成模式
        XCTAssertTrue(patterns.isEmpty, "单个地址不应该产生模式")
    }

    func testAnalyzeAddressPatterns_InvalidFormat() {
        // 测试无效格式的地址
        let addresses = [
            "Invalid Address Format",
            "Another Invalid Format"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)

        // 无效格式不应该产生模式
        XCTAssertTrue(patterns.isEmpty, "无效格式不应该产生模式")
    }

    func testPredictMissingAddresses_NoPatterns() {
        // 测试没有模式时的预测
        let patterns: [AddressPatternAnalyzer.AddressPattern] = []
        let missingStops = [1, 2, 3]

        let predictions = analyzer.predictMissingAddresses(missingStopNumbers: missingStops, patterns: patterns)

        XCTAssertTrue(predictions.isEmpty, "没有模式时不应该有预测结果")
    }

    // MARK: - 性能测试

    func testPerformanceAnalyzeAddressPatterns() {
        // 创建大量测试数据
        var addresses: [String] = []
        for i in 1...100 {
            let address = "\(100 + i * 2) Test Street, Test City, CA, 12345|\(i)|TRACK\(String(format: "%03d", i))"
            addresses.append(address)
        }

        measure {
            let patterns = analyzer.analyzeAddressPatterns(from: addresses)
            XCTAssertFalse(patterns.isEmpty, "应该检测到模式")
        }
    }

    func testPerformancePredictMissingAddresses() {
        // 先创建模式
        let addresses = [
            "100 Test St, Test City, CA, 12345|1|TRACK001",
            "102 Test St, Test City, CA, 12345|2|TRACK002",
            "104 Test St, Test City, CA, 12345|3|TRACK003"
        ]

        let patterns = analyzer.analyzeAddressPatterns(from: addresses)
        let missingStops = Array(4...50) // 大量缺失停靠点

        measure {
            let predictions = analyzer.predictMissingAddresses(missingStopNumbers: missingStops, patterns: patterns)
            XCTAssertEqual(predictions.count, missingStops.count, "应该预测出所有缺失停靠点")
        }
    }
}
