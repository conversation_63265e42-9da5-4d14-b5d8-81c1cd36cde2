import Foundation
import XCTest
@testable import NaviBatch

/// GoFo地址检测测试
/// 验证95209等美国邮编能够被正确识别
class GoFoAddressDetectionTest: XCTestCase {
    
    /// 测试GoFo地址的国家检测
    func testGoFoAddressCountryDetection() {
        let testCases = [
            // GoFo常见地址格式
            ("10624 Pleasant Valley Circ, 95209", "US", "Stockton CA地址"),
            ("10624 Pleasant Valley Circle, Stockton, CA, 95209", "US", "完整Stockton地址"),
            ("500 King Dr, Daly City, CA, 94015", "US", "Daly City地址"),
            ("1721 Marina Ct, San Mateo, CA, 94403", "US", "San Mateo地址"),
            
            // 其他美国邮编
            ("123 Main St, 90210", "US", "洛杉矶邮编"),
            ("456 Broadway, 10001", "US", "纽约邮编"),
            ("789 Oak Ave, 60601", "US", "芝加哥邮编"),
            
            // 澳洲地址（确保不被误判）
            ("123 Collins St, Melbourne, VIC 3000", "AU", "墨尔本地址"),
            ("456 George St, Sydney, NSW 2000", "AU", "悉尼地址"),
        ]
        
        print("\n🧪 开始GoFo地址国家检测测试...")
        
        var passedTests = 0
        let totalTests = testCases.count
        
        for (address, expectedCountry, description) in testCases {
            let detectedCountry = AddressCountryDetector.detectCountry(from: address)
            let detectedCode = detectedCountry?.code
            
            let passed = detectedCode == expectedCountry
            
            print("\n📍 测试: \(description)")
            print("   地址: \(address)")
            print("   期望国家: \(expectedCountry)")
            print("   检测国家: \(detectedCode ?? "无")")
            print("   结果: \(passed ? "✅ 通过" : "❌ 失败")")
            
            if passed {
                passedTests += 1
            } else {
                XCTFail("地址 '\(address)' 国家检测失败: 期望 \(expectedCountry), 实际 \(detectedCode ?? "无")")
            }
        }
        
        print("\n🎯 GoFo地址检测测试结果:")
        print("   通过: \(passedTests)/\(totalTests)")
        print("   成功率: \(Int(Double(passedTests)/Double(totalTests) * 100))%")
        
        // 确保所有测试都通过
        XCTAssertEqual(passedTests, totalTests, "部分地址国家检测失败")
    }
    
    /// 测试特定的95209邮编检测
    func testSpecific95209Detection() {
        let testAddress = "10624 Pleasant Valley Circ, 95209"
        let detectedCountry = AddressCountryDetector.detectCountry(from: testAddress)
        
        print("\n🎯 特定测试: 95209邮编检测")
        print("   地址: \(testAddress)")
        print("   检测结果: \(detectedCountry?.name ?? "未检测到") (\(detectedCountry?.code ?? "无"))")
        
        XCTAssertNotNil(detectedCountry, "应该检测到国家")
        XCTAssertEqual(detectedCountry?.code, "US", "95209应该被识别为美国邮编")
        
        if detectedCountry?.code == "US" {
            print("   ✅ 95209邮编检测成功！")
        } else {
            print("   ❌ 95209邮编检测失败！")
        }
    }
}

/// 静态测试方法，可以在其他地方调用
extension GoFoAddressDetectionTest {
    
    /// 运行GoFo地址检测测试（静态方法）
    static func runGoFoAddressTest() {
        print("🚀 开始GoFo地址检测测试")
        print(String(repeating: "=", count: 50))
        
        let testInstance = GoFoAddressDetectionTest()
        testInstance.testGoFoAddressCountryDetection()
        testInstance.testSpecific95209Detection()
        
        print("\n" + String(repeating: "=", count: 50))
        print("✅ GoFo地址检测测试完成！")
    }
}
