import Testing
import SwiftUI
import MapKit
@testable import NaviBatch

// 🎯 重叠标记点击测试
@MainActor
struct OverlapClickTests {

    // 测试相同坐标点的查找
    @Test func testFindPointsAtSameCoordinate() async throws {
        // 创建测试数据：3个相同坐标的点
        let coordinate = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
        
        let point1 = DeliveryPoint(
            sort_number: 1,
            streetName: "测试地址1",
            latitude: coordinate.latitude,
            longitude: coordinate.longitude
        )

        let point2 = DeliveryPoint(
            sort_number: 2,
            streetName: "测试地址2",
            latitude: coordinate.latitude,
            longitude: coordinate.longitude
        )

        let point3 = DeliveryPoint(
            sort_number: 3,
            streetName: "测试地址3",
            latitude: 37.7750,
            longitude: -122.4195 // 稍微不同的坐标
        )
        
        let allPoints = [point1, point2, point3]

        // 测试查找相同坐标的点
        // 注意：这里需要模拟RouteView中的findPointsAtSameCoordinate方法
        let sameCoordinatePoints = findPointsAtSameCoordinate(point1, in: allPoints)

        #expect(sameCoordinatePoints.count == 2, "应该找到2个相同坐标的点")
        #expect(sameCoordinatePoints.contains { $0.sorted_number == 1 })
        #expect(sameCoordinatePoints.contains { $0.sorted_number == 2 })
        #expect(!sameCoordinatePoints.contains { $0.sorted_number == 3 })
    }
    
    // 测试点击偏移计算
    @Test func testCalculateClickOffset() async throws {
        // 测试0度旋转
        let offset0 = calculateClickOffset(for: 0)
        #expect(offset0 == CGPoint.zero, "0度旋转应该返回零偏移")

        // 测试90度旋转
        let offset90 = calculateClickOffset(for: 90)
        #expect(abs(offset90.x - 0) < 0.01, "90度旋转X偏移应该接近0")
        #expect(abs(offset90.y - 20) < 0.01, "90度旋转Y偏移应该接近20")

        // 测试180度旋转
        let offset180 = calculateClickOffset(for: 180)
        #expect(abs(offset180.x - (-20)) < 0.01, "180度旋转X偏移应该接近-20")
        #expect(abs(offset180.y - 0) < 0.01, "180度旋转Y偏移应该接近0")
    }
    
    // 测试旋转角度分配
    @Test func testRotationAssignment() async throws {
        // 创建5个相同坐标的点
        let coordinate = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
        var points: [DeliveryPoint] = []

        for i in 1...5 {
            let point = DeliveryPoint(
                sort_number: i,
                streetName: "测试地址\(i)",
                latitude: coordinate.latitude,
                longitude: coordinate.longitude
            )
            points.append(point)
        }

        // 模拟旋转角度分配（这应该在RouteViewModel中实现）
        let rotations = assignRotationAngles(for: points)

        #expect(rotations.count == 5, "应该为5个点分配旋转角度")

        // 验证角度分配是否合理（应该均匀分布）
        let expectedAngles = [0.0, 72.0, 144.0, 216.0, 288.0] // 360/5 = 72度间隔
        for (index, rotation) in rotations.enumerated() {
            #expect(abs(rotation - expectedAngles[index]) < 1.0, "旋转角度应该均匀分布")
        }
    }
    
    // 测试点击区域不重叠
    @Test func testClickAreasDoNotOverlap() async throws {
        let rotations = [0.0, 72.0, 144.0, 216.0, 288.0]
        var clickAreas: [CGRect] = []

        for rotation in rotations {
            let offset = calculateClickOffset(for: rotation)
            let clickArea = CGRect(
                x: offset.x - 22, // 44/2 = 22
                y: offset.y - 22,
                width: 44,
                height: 44
            )
            clickAreas.append(clickArea)
        }

        // 验证任意两个点击区域都不重叠
        for i in 0..<clickAreas.count {
            for j in (i+1)..<clickAreas.count {
                let area1 = clickAreas[i]
                let area2 = clickAreas[j]
                #expect(!area1.intersects(area2), "点击区域 \(i) 和 \(j) 不应该重叠")
            }
        }
    }

    @Test("测试坐标分离算法")
    func testCoordinateSeparation() async throws {
        // 创建测试数据 - 三个相同坐标的点
        let coordinate = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)

        let point1 = createTestPoint(id: UUID(), sortedNumber: 2, coordinate: coordinate)
        let point2 = createTestPoint(id: UUID(), sortedNumber: 3, coordinate: coordinate)
        let point3 = createTestPoint(id: UUID(), sortedNumber: 4, coordinate: coordinate)

        let points = [point1, point2, point3]

        // 测试坐标分离算法
        let offsets = calculateCoordinateOffsets(for: points)

        // 验证所有点都有偏移坐标
        #expect(offsets.count == 3)
        #expect(offsets[point1.id] != nil)
        #expect(offsets[point2.id] != nil)
        #expect(offsets[point3.id] != nil)

        // 验证偏移后的坐标都不相同
        let offsetCoords = Array(offsets.values)
        let uniqueCoords = Set(offsetCoords.map { "\($0.latitude),\($0.longitude)" })
        #expect(uniqueCoords.count == 3)

        // 验证偏移距离合理（不超过100米）
        for offset in offsetCoords {
            let distance = calculateDistance(from: coordinate, to: offset)
            #expect(distance < 100) // 偏移距离应该小于100米
        }
    }

    @Test("测试两点分离距离")
    func testTwoPointSeparation() async throws {
        // 创建测试数据 - 两个相同坐标的点
        let coordinate = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)

        let point1 = createTestPoint(id: UUID(), sortedNumber: 2, coordinate: coordinate)
        let point2 = createTestPoint(id: UUID(), sortedNumber: 3, coordinate: coordinate)

        let points = [point1, point2]

        // 测试坐标分离算法
        let offsets = calculateCoordinateOffsets(for: points)

        // 验证两个点都有偏移坐标
        #expect(offsets.count == 2)
        guard let offset1 = offsets[point1.id], let offset2 = offsets[point2.id] else {
            #expect(Bool(false), "应该为两个点都生成偏移坐标")
            return
        }

        // 验证两点分离距离合理（确保点击区域不重叠）
        let separationDistance = calculateDistance(from: offset1, to: offset2)
        #expect(separationDistance > 30) // 至少30米分离，确保点击区域不重叠
        #expect(separationDistance < 80) // 不超过80米，保持合理距离

        // 验证两点在同一纬度（左右分布）
        #expect(abs(offset1.latitude - offset2.latitude) < 0.000001) // 纬度基本相同
        #expect(abs(offset1.longitude - offset2.longitude) > 0.00001) // 经度有明显差异
    }

    @Test("测试重叠点查找功能")
    func testFindPointsAtSameCoordinate() async throws {
        // 创建测试数据 - 三个点，其中两个相同坐标
        let coordinate1 = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
        let coordinate2 = CLLocationCoordinate2D(latitude: 37.7750, longitude: -122.4195) // 不同坐标

        let point1 = createTestPoint(id: UUID(), sortedNumber: 1, coordinate: coordinate1)
        let point2 = createTestPoint(id: UUID(), sortedNumber: 2, coordinate: coordinate1) // 相同坐标
        let point3 = createTestPoint(id: UUID(), sortedNumber: 3, coordinate: coordinate2) // 不同坐标

        let allPoints = [point1, point2, point3]

        // 模拟查找相同坐标的点
        func findPointsAtSameCoordinate(_ coordinate: CLLocationCoordinate2D) -> [DeliveryPoint] {
            let tolerance = 0.000001
            return allPoints.filter { point in
                abs(point.coordinate.latitude - coordinate.latitude) < tolerance &&
                abs(point.coordinate.longitude - coordinate.longitude) < tolerance
            }
        }

        // 测试查找coordinate1的重叠点
        let overlappingPoints1 = findPointsAtSameCoordinate(coordinate1)
        #expect(overlappingPoints1.count == 2) // 应该找到point1和point2
        #expect(overlappingPoints1.contains { $0.id == point1.id })
        #expect(overlappingPoints1.contains { $0.id == point2.id })
        #expect(!overlappingPoints1.contains { $0.id == point3.id })

        // 测试查找coordinate2的重叠点
        let overlappingPoints2 = findPointsAtSameCoordinate(coordinate2)
        #expect(overlappingPoints2.count == 1) // 应该只找到point3
        #expect(overlappingPoints2.contains { $0.id == point3.id })
    }
}

// MARK: - 辅助函数（模拟RouteView中的私有方法）

private func findPointsAtSameCoordinate(_ targetPoint: DeliveryPoint, in points: [DeliveryPoint]) -> [DeliveryPoint] {
    let tolerance = 0.00001
    
    return points.filter { point in
        abs(point.coordinate.latitude - targetPoint.coordinate.latitude) < tolerance &&
        abs(point.coordinate.longitude - targetPoint.coordinate.longitude) < tolerance
    }.sorted { $0.sorted_number < $1.sorted_number }
}

private func calculateClickOffset(for rotation: Double) -> CGPoint {
    if rotation == 0 {
        return CGPoint.zero
    }
    
    let offsetDistance: CGFloat = 20
    let radians = rotation * .pi / 180
    
    let offsetX = cos(radians) * offsetDistance
    let offsetY = sin(radians) * offsetDistance
    
    return CGPoint(x: offsetX, y: offsetY)
}

// 模拟RouteView中的坐标分离算法
private func calculateCoordinateOffsets(for points: [DeliveryPoint]) -> [UUID: CLLocationCoordinate2D] {
    var offsets: [UUID: CLLocationCoordinate2D] = [:]

    // 按坐标分组，找出相同坐标的点
    var coordinateGroups: [String: [DeliveryPoint]] = [:]

    for point in points {
        // 创建坐标键，精确到6位小数（约1米精度）
        let coordKey = String(format: "%.6f,%.6f", point.coordinate.latitude, point.coordinate.longitude)

        if coordinateGroups[coordKey] == nil {
            coordinateGroups[coordKey] = []
        }
        coordinateGroups[coordKey]?.append(point)
    }

    // 为每组相同坐标的点计算分离偏移
    for (_, groupPoints) in coordinateGroups {
        if groupPoints.count <= 1 {
            // 单个点不需要偏移
            continue
        }

        // 多个点需要分离
        let baseCoordinate = groupPoints[0].coordinate
        let separationDistance: Double = 0.0003 // 约33米的分离距离

        // 按sorted_number排序，确保编号小的在前面
        let sortedPoints = groupPoints.sorted { $0.sorted_number < $1.sorted_number }

        for (index, point) in sortedPoints.enumerated() {
            if sortedPoints.count == 2 {
                // 两个点：左右分布
                if index == 0 {
                    // 第一个点向左偏移
                    offsets[point.id] = CLLocationCoordinate2D(
                        latitude: baseCoordinate.latitude,
                        longitude: baseCoordinate.longitude - separationDistance
                    )
                } else {
                    // 第二个点向右偏移
                    offsets[point.id] = CLLocationCoordinate2D(
                        latitude: baseCoordinate.latitude,
                        longitude: baseCoordinate.longitude + separationDistance
                    )
                }
            } else {
                // 多个点：圆形分布
                let angle = Double(index) * (2.0 * Double.pi / Double(sortedPoints.count))
                let offsetLat = baseCoordinate.latitude + separationDistance * cos(angle)
                let offsetLng = baseCoordinate.longitude + separationDistance * sin(angle)

                offsets[point.id] = CLLocationCoordinate2D(
                    latitude: offsetLat,
                    longitude: offsetLng
                )
            }
        }
    }

    return offsets
}

// 计算两个坐标之间的距离（米）
private func calculateDistance(from coord1: CLLocationCoordinate2D, to coord2: CLLocationCoordinate2D) -> Double {
    let location1 = CLLocation(latitude: coord1.latitude, longitude: coord1.longitude)
    let location2 = CLLocation(latitude: coord2.latitude, longitude: coord2.longitude)
    return location1.distance(from: location2)
}

private func assignRotationAngles(for points: [DeliveryPoint]) -> [Double] {
    let count = points.count
    if count <= 1 {
        return [0.0]
    }
    
    let angleStep = 360.0 / Double(count)
    return (0..<count).map { Double($0) * angleStep }
}
