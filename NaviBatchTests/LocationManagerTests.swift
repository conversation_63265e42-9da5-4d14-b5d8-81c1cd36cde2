//
//  LocationManagerTests.swift
//  NaviBatchTests
//
//  Created by Augment Agent
//

import Testing
import CoreLocation
@testable import NaviBatch

/// LocationManager 测试套件
@MainActor
struct LocationManagerTests {
    
    // MARK: - 初始化测试
    
    @Test func testLocationManagerSingleton() async throws {
        // 测试单例模式
        let manager1 = LocationManager.shared
        let manager2 = LocationManager.shared
        
        #expect(manager1 === manager2, "LocationManager应该是单例")
    }
    
    @Test func testInitialState() async throws {
        // 测试初始状态
        let manager = LocationManager.shared
        
        // 验证授权状态是有效的枚举值
        let validStatuses: [CLAuthorizationStatus] = [
            .notDetermined, .denied, .authorizedWhenInUse, .authorizedAlways, .restricted
        ]
        #expect(validStatuses.contains(manager.authorizationStatus), "授权状态应该是有效的枚举值")
        
        #expect(manager.isUpdatingLocation == false, "初始不应该在更新位置")
        #expect(manager.validRange == 20_000_000, "有效范围应该是20000公里（开发模式）")
    }
    
    // MARK: - 区域管理测试
    
    @Test func testGetAvailableRegions() async throws {
        // 测试获取可用区域
        let manager = LocationManager.shared
        let regions = manager.getAvailableRegions()
        
        #expect(regions.count > 0, "应该有可用的区域")
        #expect(regions.allSatisfy { !$0.isEmpty }, "所有区域名称都不应为空")
    }
    
    @Test func testGetCurrentRegion() async throws {
        // 测试获取当前区域
        let manager = LocationManager.shared
        let currentRegion = manager.getCurrentRegion()
        
        #expect(!currentRegion.isEmpty, "当前区域不应为空")
    }
    
    @Test func testSetValidRegion() async throws {
        // 测试设置有效区域
        let manager = LocationManager.shared
        let originalRegion = manager.getCurrentRegion()
        let availableRegions = manager.getAvailableRegions()
        
        // 如果有多个可用区域，测试设置不同的区域
        if availableRegions.count > 1 {
            let newRegion = availableRegions.first { $0 != originalRegion } ?? availableRegions.first!
            
            manager.setRegion(newRegion)
            #expect(manager.getCurrentRegion() == newRegion, "应该成功设置新区域")
            
            // 恢复原始区域
            manager.setRegion(originalRegion)
            #expect(manager.getCurrentRegion() == originalRegion, "应该成功恢复原始区域")
        }
    }
    
    @Test func testSetInvalidRegion() async throws {
        // 测试设置无效区域
        let manager = LocationManager.shared
        let originalRegion = manager.getCurrentRegion()
        
        // 尝试设置无效区域
        manager.setRegion("invalid_region_that_does_not_exist")
        
        // 区域应该保持不变
        #expect(manager.getCurrentRegion() == originalRegion, "设置无效区域后，当前区域应该保持不变")
    }
    
    // MARK: - 默认位置测试
    
    @Test func testGetDefaultCoordinateForCurrentRegion() async throws {
        // 测试获取当前区域的默认坐标
        let manager = LocationManager.shared
        let defaultCoordinate = manager.getDefaultCoordinateForCurrentRegion()
        
        // 验证坐标是有效的
        let isValid = CLLocationCoordinate2DIsValid(defaultCoordinate)
        #expect(isValid == true, "默认坐标应该是有效的")
        
        // 验证坐标不是零值（除非确实是全球默认位置）
        let isNotZero = defaultCoordinate.latitude != 0 || defaultCoordinate.longitude != 0
        #expect(isNotZero == true, "默认坐标不应该都是零值")
    }
    
    @Test func testUseDefaultLocation() async throws {
        // 测试使用默认位置
        let manager = LocationManager.shared
        
        // 使用默认位置
        manager.useDefaultLocation()
        
        // 验证状态更新
        #expect(manager.userLocation != nil, "应该设置了用户位置")
        #expect(manager.lastUpdateTime != nil, "应该有最后更新时间")
        
        // 验证位置是有效的
        if let userLocation = manager.userLocation {
            let isValid = CLLocationCoordinate2DIsValid(userLocation)
            #expect(isValid == true, "用户位置应该是有效的")
        }
    }
    
    // MARK: - 位置验证测试
    
    @Test func testDistanceFromUserLocation() async throws {
        // 测试距离计算
        let manager = LocationManager.shared
        
        // 确保有用户位置
        if manager.userLocation == nil {
            manager.useDefaultLocation()
        }
        
        guard let userLocation = manager.userLocation else {
            #expect(Bool(false), "应该有用户位置")
            return
        }
        
        // 测试与自身的距离
        let distanceToSelf = manager.distanceFromUserLocation(userLocation)
        #expect(distanceToSelf == 0, "与自身的距离应该是0")
        
        // 测试与其他位置的距离
        let otherLocation = CLLocationCoordinate2D(
            latitude: userLocation.latitude + 0.01,
            longitude: userLocation.longitude + 0.01
        )
        
        let distanceToOther = manager.distanceFromUserLocation(otherLocation)
        #expect(distanceToOther != nil, "应该能计算到其他位置的距离")
        #expect(distanceToOther! > 0, "到其他位置的距离应该大于0")
    }
    
    @Test func testIsWithinValidRange() async throws {
        // 测试有效范围检查
        let manager = LocationManager.shared
        
        // 确保有用户位置
        if manager.userLocation == nil {
            manager.useDefaultLocation()
        }
        
        guard let userLocation = manager.userLocation else {
            #expect(Bool(false), "应该有用户位置")
            return
        }
        
        // 测试用户位置本身应该在有效范围内
        let isUserLocationValid = manager.isWithinValidRange(userLocation)
        #expect(isUserLocationValid == true, "用户位置本身应该在有效范围内")
        
        // 测试附近位置应该在有效范围内
        let nearbyLocation = CLLocationCoordinate2D(
            latitude: userLocation.latitude + 0.001,
            longitude: userLocation.longitude + 0.001
        )
        
        let isNearbyValid = manager.isWithinValidRange(nearbyLocation)
        #expect(isNearbyValid == true, "附近位置应该在有效范围内")
    }
    
    // MARK: - 权限处理测试
    
    @Test func testHasLocationPermission() async throws {
        // 测试位置权限检查
        let manager = LocationManager.shared
        
        let hasPermission = manager.hasLocationPermission()
        
        // 权限状态应该是布尔值
        #expect(hasPermission == true || hasPermission == false, "权限状态应该是布尔值")
        
        // 验证权限状态与授权状态一致
        let expectedPermission = manager.authorizationStatus == .authorizedWhenInUse || 
                                manager.authorizationStatus == .authorizedAlways
        #expect(hasPermission == expectedPermission, "权限状态应该与授权状态一致")
    }
    
    // MARK: - 位置更新状态测试
    
    @Test func testLocationUpdateState() async throws {
        // 测试位置更新状态
        let manager = LocationManager.shared
        
        // 停止位置更新（确保状态一致）
        manager.stopUpdatingLocation()
        #expect(manager.isUpdatingLocation == false, "停止更新后状态应该为false")
        
        // 注意：在测试环境中，我们不实际开始位置更新，因为这需要真实的位置权限
        // 只测试状态管理逻辑
    }
    
    // MARK: - 坐标验证辅助测试
    
    @Test func testCoordinateValidation() async throws {
        // 测试坐标验证功能
        let validCoordinates = [
            CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628), // Melbourne
            CLLocationCoordinate2D(latitude: 22.3193, longitude: 114.1694),  // Hong Kong
            CLLocationCoordinate2D(latitude: 0, longitude: 0)                // Valid zero coordinate
        ]
        
        for coordinate in validCoordinates {
            let isValid = CLLocationCoordinate2DIsValid(coordinate)
            #expect(isValid == true, "坐标应该是有效的: \(coordinate)")
        }
        
        // 测试无效坐标
        let invalidCoordinates = [
            CLLocationCoordinate2D(latitude: 200, longitude: 200),
            CLLocationCoordinate2D(latitude: -200, longitude: -200),
            CLLocationCoordinate2D(latitude: Double.nan, longitude: 0),
            CLLocationCoordinate2D(latitude: 0, longitude: Double.nan)
        ]
        
        for coordinate in invalidCoordinates {
            let isValid = CLLocationCoordinate2DIsValid(coordinate)
            #expect(isValid == false, "坐标应该是无效的: \(coordinate)")
        }
    }
}
