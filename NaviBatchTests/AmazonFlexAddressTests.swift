//
//  AmazonFlexAddressTests.swift
//  NaviBatchTests
//
//  Created by NaviBatch on 2024/12/19.
//

import XCTest
@testable import NaviBatch

/// 🇺🇸 Amazon Flex地址识别测试
class AmazonFlexAddressTests: XCTestCase {
    
    var hybridService: HybridAddressRecognitionService!
    
    override func setUp() {
        super.setUp()
        hybridService = HybridAddressRecognitionService()
    }
    
    override func tearDown() {
        hybridService = nil
        super.tearDown()
    }
    
    // MARK: - 测试Amazon Flex重复城市名清理
    
    func testCleanAmazonFlexDuplicateCityNames() {
        // 测试重复城市名的清理
        let testCases = [
            // 重复城市名问题
            ("SAN MATEO 1715 YORK AVE, SAN MATEO", "1715 YORK AVE, SAN MATEO"),
            ("SAN FRANCISCO 123 MAIN ST, SAN FRANCISCO", "123 MAIN ST, SAN FRANCISCO"),
            ("LOS ANGELES 456 SUNSET BLVD, LOS ANGELES", "456 SUNSET BLVD, LOS ANGELES"),
            
            // 开头重复城市名
            ("SAN MATEO SAN MATEO 1715 YORK AVE", "SAN MATEO 1715 YORK AVE"),
            ("NEW YORK NEW YORK 789 BROADWAY", "NEW YORK 789 BROADWAY"),
            
            // 正常地址（不应该被改变）
            ("24 N QUEBEC ST, SAN MATEO", "24 N QUEBEC ST, SAN MATEO"),
            ("1794 SHOREVIEW AVE, SAN MATEO", "1794 SHOREVIEW AVE, SAN MATEO"),
            
            // 多余空格和标点
            ("  SAN MATEO  1715  YORK  AVE  ,  SAN MATEO  ", "1715 YORK AVE, SAN MATEO"),
            ("SAN MATEO 1715 YORK AVE,, SAN MATEO", "1715 YORK AVE, SAN MATEO"),
        ]
        
        for (input, expected) in testCases {
            let result = hybridService.testCleanAmazonFlexAddress(input)
            XCTAssertEqual(result, expected, "清理失败: '\(input)' -> '\(result)', 期望: '\(expected)'")
        }
    }
    
    // MARK: - 测试美国地址格式识别
    
    func testUSAddressStartRecognition() {
        let validUSAddressStarts = [
            "24 N QUEBEC ST",
            "1715 YORK AVE", 
            "1794 SHOREVIEW AVE",
            "1717 NASH DR",
            "1530 MAXINE AVE",
            "123 E MAIN ST",
            "456 W BROADWAY",
            "789 S FIRST AVE",
            "101 SUNSET BLVD",
            "202 HOLLYWOOD WAY",
            "303-305 MAIN ST", // 地址范围
            "123A MAIN ST", // 带字母后缀
        ]
        
        for address in validUSAddressStarts {
            let isStart = hybridService.testIsAddressStart(address)
            XCTAssertTrue(isStart, "应该识别为地址开始: '\(address)'")
        }
    }
    
    func testUSAddressContinuation() {
        let validUSAddressContinuations = [
            "SAN MATEO",
            "SAN FRANCISCO", 
            "LOS ANGELES",
            "NEW YORK",
            "CHICAGO, IL 60601",
            "HOUSTON, TX 77001",
            "PHOENIX, AZ 85001",
            "SAN MATEO, CA 94401",
            "SEATTLE, WA 98101",
        ]
        
        for continuation in validUSAddressContinuations {
            let isContinuation = hybridService.testIsAddressContinuation(continuation)
            XCTAssertTrue(isContinuation, "应该识别为地址延续: '\(continuation)'")
        }
    }
    
    func testUSAddressLikelyRecognition() {
        let validUSAddresses = [
            "24 N QUEBEC ST",
            "1715 YORK AVE, SAN MATEO",
            "1794 SHOREVIEW AVE, SAN MATEO, CA",
            "123 MAIN ST, NEW YORK, NY 10001",
            "456 SUNSET BLVD, LOS ANGELES, CA 90210",
        ]
        
        for address in validUSAddresses {
            let isLikely = hybridService.testIsLikelyAddress(address)
            XCTAssertTrue(isLikely, "应该识别为有效地址: '\(address)'")
        }
    }
    
    // MARK: - 测试Amazon Flex OCR文本提取
    
    func testAmazonFlexOCRExtraction() {
        let amazonFlexOCRText = """
        配送行程
        列表    地图    汇总
        2  ◎ # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
        24 N QUEBEC ST
        SAN MATEO
        3  ◎ # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
        SAN MATEO 1715 YORK AVE
        SAN MATEO
        4  ◎ # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
        1794 SHOREVIEW AVE
        SAN MATEO
        5  ◎ # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
        1717 NASH DR
        SAN MATEO
        """
        
        let extractedAddresses = hybridService.testExtractAddressesFromText(amazonFlexOCRText)
        
        // 验证提取的地址数量
        XCTAssertGreaterThan(extractedAddresses.count, 0, "应该提取到地址")
        
        print("🔍 提取的地址:")
        for (index, address) in extractedAddresses.enumerated() {
            print("  \(index + 1). \(address)")
        }
        
        // 验证至少提取到了一些地址
        XCTAssertGreaterThanOrEqual(extractedAddresses.count, 2, "应该提取到至少2个地址")
        
        // 验证重复城市名被清理
        let hasCleanedDuplicate = extractedAddresses.contains { address in
            address.contains("1715 YORK AVE") && !address.contains("SAN MATEO 1715 YORK AVE")
        }
        XCTAssertTrue(hasCleanedDuplicate, "重复城市名应该被清理")
    }
    
    // MARK: - 测试美国州名和城市识别
    
    func testUSStateAndCityRecognition() {
        let usAddressPatterns = [
            "123 Main St, San Mateo, CA 94401",
            "456 Broadway, New York, NY 10001", 
            "789 Sunset Blvd, Los Angeles, CA 90210",
            "101 Michigan Ave, Chicago, IL 60601",
            "202 Congress St, Boston, MA 02101",
        ]
        
        for address in usAddressPatterns {
            let containsUSPattern = hybridService.testContainsUSAddressPattern(address)
            XCTAssertTrue(containsUSPattern, "应该识别为美国地址模式: '\(address)'")
        }
    }
    
    // MARK: - 性能测试
    
    func testAmazonFlexAddressCleaningPerformance() {
        let testAddress = "SAN MATEO 1715 YORK AVE, SAN MATEO"
        
        measure {
            for _ in 0..<1000 {
                _ = hybridService.testCleanAmazonFlexAddress(testAddress)
            }
        }
    }
}

// MARK: - 测试辅助扩展

extension HybridAddressRecognitionService {

    /// 暴露内部方法用于测试
    func testCleanAmazonFlexAddress(_ address: String) -> String {
        return self.cleanAmazonFlexAddress(address)
    }

    func testIsAddressStart(_ text: String) -> Bool {
        return self.isAddressStart(text)
    }

    func testIsAddressContinuation(_ text: String) -> Bool {
        return self.isAddressContinuation(text)
    }

    func testIsLikelyAddress(_ text: String) -> Bool {
        return self.isLikelyAddress(text)
    }

    func testExtractAddressesFromText(_ text: String) -> [String] {
        return self.extractAddressesFromText(text)
    }

    func testContainsUSAddressPattern(_ text: String) -> Bool {
        return self.containsUSAddressPattern(text)
    }
}
