import Foundation
import XCTest
@testable import NaviBatch

/// Firebase AI 地址州修复测试
/// 验证 Firebase AI 识别的缺少州信息的地址能够被正确修复
class FirebaseAIAddressStateFixTest: XCTestCase {
    
    /// 测试 Firebase AI 识别地址的州修复
    func testFirebaseAIAddressStateFix() async {
        print("\n🧪 开始 Firebase AI 地址州修复测试...")
        
        let testCases = [
            // Firebase AI 典型识别结果（缺少州信息）
            ("1220 Taylor Lane, 95603", "CA", "Taylor Lane 地址"),
            ("10624 Pleasant Valley Circ, 95209", "CA", "Pleasant Valley 地址"),
            ("500 King Dr, 94015", "CA", "King Dr 地址"),
            ("1721 Marina Ct, 94403", "CA", "Marina Ct 地址"),
            
            // 其他州的测试用例
            ("123 Main St, 90210", "CA", "洛杉矶地址"),
            ("456 Broadway, 10001", "NY", "纽约地址"),
            ("789 Oak Ave, 60601", "IL", "芝加哥地址"),
        ]
        
        var passedTests = 0
        let totalTests = testCases.count
        
        for (originalAddress, expectedState, description) in testCases {
            print("\n📍 测试: \(description)")
            print("   原始地址: \(originalAddress)")
            
            // 测试地理编码前修复
            let fixedAddress = await testAddressFixing(originalAddress)
            
            let containsState = fixedAddress.contains(expectedState)
            let passed = containsState
            
            print("   修复地址: \(fixedAddress)")
            print("   期望州: \(expectedState)")
            print("   包含州: \(containsState ? "是" : "否")")
            print("   结果: \(passed ? "✅ 通过" : "❌ 失败")")
            
            if passed {
                passedTests += 1
            } else {
                XCTFail("地址 '\(originalAddress)' 州修复失败: 期望包含 \(expectedState)")
            }
        }
        
        print("\n🎯 Firebase AI 地址州修复测试结果:")
        print("   通过: \(passedTests)/\(totalTests)")
        print("   成功率: \(Int(Double(passedTests)/Double(totalTests) * 100))%")
        
        // 确保所有测试都通过
        XCTAssertEqual(passedTests, totalTests, "部分地址州修复失败")
    }
    
    /// 测试完整的地址处理流程
    func testCompleteAddressProcessingFlow() async {
        print("\n🔄 测试完整地址处理流程...")
        
        let testAddress = "1220 Taylor Lane, 95603"
        print("📥 Firebase AI 识别地址: \(testAddress)")
        
        // 1. 测试地理编码前修复
        let fixedForGeocoding = await testAddressFixing(testAddress)
        print("🔧 地理编码前修复: \(fixedForGeocoding)")
        
        // 2. 测试存储前后处理
        let processedForStorage = await testAddressPostProcessing(testAddress)
        print("💾 存储前后处理: \(processedForStorage)")
        
        // 验证两个步骤都包含州信息
        let geocodingFixed = fixedForGeocoding.contains("CA")
        let storageFixed = processedForStorage.contains("CA")
        
        print("\n📊 处理结果:")
        print("   地理编码前修复: \(geocodingFixed ? "✅ 成功" : "❌ 失败")")
        print("   存储前后处理: \(storageFixed ? "✅ 成功" : "❌ 失败")")
        
        XCTAssertTrue(geocodingFixed, "地理编码前修复应该包含州信息")
        XCTAssertTrue(storageFixed, "存储前后处理应该包含州信息")
        
        print("✅ 完整地址处理流程测试通过！")
    }
    
    /// 测试批量地址处理
    func testBatchAddressProcessing() async {
        print("\n📦 测试批量地址处理...")
        
        let batchAddresses = [
            "1220 Taylor Lane, 95603",
            "10624 Pleasant Valley Circ, 95209",
            "500 King Dr, 94015"
        ]
        
        print("📥 批量地址数量: \(batchAddresses.count)")
        
        var allFixed = true
        
        for (index, address) in batchAddresses.enumerated() {
            let fixedAddress = await testAddressFixing(address)
            let containsCA = fixedAddress.contains("CA")
            
            print("   [\(index + 1)] \(address) -> \(fixedAddress) (\(containsCA ? "✅" : "❌"))")
            
            if !containsCA {
                allFixed = false
            }
        }
        
        print("\n📊 批量处理结果: \(allFixed ? "✅ 全部成功" : "❌ 部分失败")")
        XCTAssertTrue(allFixed, "批量地址处理应该全部成功")
    }
    
    /// 测试边界情况
    func testEdgeCases() async {
        print("\n🔍 测试边界情况...")
        
        let edgeCases = [
            // 已经包含州信息的地址（不应该重复添加）
            ("1220 Taylor Lane, CA, 95603", false, "已有州信息"),
            ("1220 Taylor Lane, Auburn, CA, 95603", false, "完整地址"),
            
            // 没有邮政编码的地址（不应该修复）
            ("1220 Taylor Lane, Auburn", false, "无邮政编码"),
            ("Main Street", false, "不完整地址"),
            
            // 非美国地址（不应该修复）
            ("123 Collins St, Melbourne, VIC 3000", false, "澳洲地址"),
        ]
        
        var passedTests = 0
        let totalTests = edgeCases.count
        
        for (address, shouldBeModified, description) in edgeCases {
            let fixedAddress = await testAddressFixing(address)
            let wasModified = fixedAddress != address
            let passed = wasModified == shouldBeModified
            
            print("   \(description): \(address)")
            print("     -> \(fixedAddress)")
            print("     期望修改: \(shouldBeModified ? "是" : "否"), 实际修改: \(wasModified ? "是" : "否")")
            print("     结果: \(passed ? "✅ 通过" : "❌ 失败")")
            
            if passed {
                passedTests += 1
            }
        }
        
        print("\n📊 边界情况测试结果: \(passedTests)/\(totalTests)")
        XCTAssertEqual(passedTests, totalTests, "边界情况测试失败")
    }
    
    // MARK: - 辅助测试方法
    
    /// 测试地址修复（模拟 DeliveryPointManager.fixAddressStateIfNeeded）
    private func testAddressFixing(_ address: String) async -> String {
        // 使用 AddressStateFixService 进行修复
        if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: address) {
            return fixedAddress
        }
        return address
    }
    
    /// 测试地址后处理（模拟 DeliveryPointManager.postProcessAddressForStorage）
    private func testAddressPostProcessing(_ address: String) async -> String {
        let manager = DeliveryPointManager.shared
        return await manager.postProcessAddressForStorage(address)
    }
}

/// 静态测试方法，可以在其他地方调用
extension FirebaseAIAddressStateFixTest {
    
    /// 运行 Firebase AI 地址州修复测试（静态方法）
    static func runFirebaseAIAddressStateFixTest() {
        print("🚀 开始 Firebase AI 地址州修复测试")
        print(String(repeating: "=", count: 60))
        
        let testInstance = FirebaseAIAddressStateFixTest()
        
        Task {
            await testInstance.testFirebaseAIAddressStateFix()
            await testInstance.testCompleteAddressProcessingFlow()
            await testInstance.testBatchAddressProcessing()
            await testInstance.testEdgeCases()
            
            print("\n" + String(repeating: "=", count: 60))
            print("✅ Firebase AI 地址州修复测试完成！")
        }
    }
}
