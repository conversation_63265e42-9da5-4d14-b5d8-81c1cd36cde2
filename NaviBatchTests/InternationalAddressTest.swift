import Foundation
@testable import NaviBatch

/// 国际地址验证测试
/// 用于测试NaviBatch的国际地址验证功能
class InternationalAddressTest {

    /// 测试地址数据
    static let testAddresses = [
        // 🇭🇰 香港地址
        TestAddress(
            address: "荃湾海盛路3号荃新天地2座15楼A室",
            expectedCountry: "HK",
            description: "香港荃湾商业地址"
        ),
        TestAddress(
            address: "荃湾大河道99号99广场28楼2801室, Hong Kong",
            expectedCountry: "HK",
            description: "香港荃湾带英文标识"
        ),
        TestAddress(
            address: "Central, Hong Kong",
            expectedCountry: "HK",
            description: "香港中环简单地址"
        ),

        // 🇺🇸 美国地址
        TestAddress(
            address: "123 Main St, New York, NY 10001",
            expectedCountry: "US",
            description: "美国纽约地址"
        ),
        TestAddress(
            address: "456 Oak Ave, Los Angeles, CA 90210",
            expectedCountry: "US",
            description: "美国洛杉矶地址"
        ),
        TestAddress(
            address: "789 Broadway, New York, USA",
            expectedCountry: "US",
            description: "美国纽约带国家标识"
        ),

        // 🇨🇦 加拿大地址
        TestAddress(
            address: "123 Maple St, Toronto, ON M5V 3A8, Canada",
            expectedCountry: "CA",
            description: "加拿大多伦多地址"
        ),
        TestAddress(
            address: "456 Pine Ave, Vancouver, BC V6B 1A1",
            expectedCountry: "CA",
            description: "加拿大温哥华地址"
        ),

        // 🇬🇧 英国地址
        TestAddress(
            address: "10 Downing St, London SW1A 2AA, UK",
            expectedCountry: "GB",
            description: "英国伦敦地址"
        ),
        TestAddress(
            address: "221B Baker St, London, England",
            expectedCountry: "GB",
            description: "英国伦敦经典地址"
        ),

        // 🇦🇺 澳大利亚地址（确保向后兼容）
        TestAddress(
            address: "123 Collins St, Melbourne, VIC 3000",
            expectedCountry: "AU",
            description: "澳大利亚墨尔本地址"
        ),
        TestAddress(
            address: "456 George St, Sydney, NSW 2000, Australia",
            expectedCountry: "AU",
            description: "澳大利亚悉尼地址"
        ),

        // 🌍 无法识别的地址（应该使用全球策略）
        TestAddress(
            address: "Some Random Street 123",
            expectedCountry: nil,
            description: "无法识别国家的地址"
        )
    ]

    /// 测试地址结构
    struct TestAddress {
        let address: String
        let expectedCountry: String?
        let description: String
    }

    /// 运行国家检测测试
    static func runCountryDetectionTest() {
        print("🌍 开始国际地址国家检测测试...")

        var passedTests = 0
        let totalTests = testAddresses.count

        for testCase in testAddresses {
            let detectedCountry = AddressCountryDetector.detectCountry(from: testCase.address)
            let detectedCode = detectedCountry?.code

            let passed = detectedCode == testCase.expectedCountry

            print("\n📍 测试: \(testCase.description)")
            print("   地址: \(testCase.address)")
            print("   期望国家: \(testCase.expectedCountry ?? "无")")
            print("   检测国家: \(detectedCode ?? "无")")
            print("   结果: \(passed ? "✅ 通过" : "❌ 失败")")

            if passed {
                passedTests += 1
            }
        }

        print("\n🎯 国家检测测试结果:")
        print("   通过: \(passedTests)/\(totalTests)")
        print("   成功率: \(Int(Double(passedTests)/Double(totalTests) * 100))%")
    }

    /// 运行地址标准化测试
    static func runAddressStandardizationTest() {
        print("\n🔧 开始地址标准化测试...")

        let standardizationTests = [
            ("123 Main St, New York", "123 Main Street, New York", "美国地址标准化"),
            ("456 Oak Ave, LA", "456 Oak Avenue, LA", "美国大道标准化"),
            ("789 Collins St, Melbourne", "789 Collins Street, Melbourne", "澳洲街道标准化"),
            ("10 Downing St, London", "10 Downing Street, London", "英国地址标准化"),
            ("荃湾海盛路3号15/F A室", "荃湾海盛路3号15楼 A室", "香港楼层标准化")
        ]

        for (original, expected, description) in standardizationTests {
            let standardized = AddressStandardizer.standardizeAddress(original)
            let passed = standardized.contains(expected.components(separatedBy: " ").first!) // 简单检查

            print("\n🔧 测试: \(description)")
            print("   原始: \(original)")
            print("   标准化: \(standardized)")
            print("   结果: \(passed ? "✅ 通过" : "❌ 失败")")
        }
    }

    /// 测试地址翻译功能
    static func runAddressTranslationTest() {
        print("\n🌐 开始地址翻译测试...")

        let translationTests = [
            ("恒来邨永宁楼", "Wing Ning Building", "香港公屋翻译"),
            ("中环金钟道", "Central Admiralty Road", "香港地区+道路翻译"),
            ("旺角弥敦道", "Mong Kok Nathan Road", "香港繁忙区域翻译"),
            ("铜锣湾时代广场", "Causeway Bay Times Plaza", "香港商业区翻译"),
            ("深水埗长沙湾道", "Sham Shui Po Cheung Sha Wan Road", "香港区域+道路翻译"),
            ("九龙塘又一村", "Kowloon Tong Yau Yat Chuen", "香港住宅区翻译"),
            ("沙田大围道", "Sha Tin Tai Wai Road", "新界地址翻译"),
            ("Wing ning house", "Wing ning house", "英文地址保持不变")
        ]

        var passedTests = 0
        let totalTests = translationTests.count

        for (original, expectedKeyword, description) in translationTests {
            let translated = AddressStandardizer.translateAddressToEnglish(original)
            let passed = translated.contains(expectedKeyword) || original == translated

            print("\n🌐 测试: \(description)")
            print("   原始: \(original)")
            print("   翻译: \(translated)")
            print("   包含关键词: \(expectedKeyword)")
            print("   结果: \(passed ? "✅ 通过" : "❌ 失败")")

            if passed {
                passedTests += 1
            }
        }

        print("\n🎯 地址翻译测试结果:")
        print("   通过: \(passedTests)/\(totalTests)")
        print("   成功率: \(Int(Double(passedTests)/Double(totalTests) * 100))%")
    }

    /// 测试Wing ning house搜索案例
    static func testWingNingHouseCase() {
        print("\n🏠 测试Wing ning house特殊案例...")

        let testCases = [
            "Wing ning house",
            "恒来邨永宁楼",
            "永宁楼",
            "恒来邨"
        ]

        for address in testCases {
            let translated = AddressStandardizer.translateAddressToEnglish(address)
            print("\n搜索: \(address)")
            print("翻译: \(translated)")
            print("是否包含中文: \(AddressStandardizer.containsChineseCharacters(address))")
        }
    }

    /// 运行完整测试套件
    static func runAllTests() {
        print("🚀 开始NaviBatch国际地址验证测试套件")
        print(String(repeating: "=", count: 50))

        runCountryDetectionTest()
        runAddressStandardizationTest()
        runAddressTranslationTest()
        testWingNingHouseCase()

        print("\n" + String(repeating: "=", count: 50))
        print("✅ 国际地址验证测试完成！")
    }
}

// 字符串重复扩展（如果MemoryProfiler中已定义则移除此扩展）
// extension String {
//     static func * (left: String, right: Int) -> String {
//         return String(repeating: left, count: right)
//     }
// }
