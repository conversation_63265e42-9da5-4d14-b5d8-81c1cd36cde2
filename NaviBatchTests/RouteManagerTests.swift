//
//  RouteManagerTests.swift
//  NaviBatchTests
//
//  Created by Augment Agent
//

import Testing
import SwiftData
import CoreLocation
@testable import NaviBatch

/// RouteManager 测试套件
@MainActor
struct RouteManagerTests {
    
    // MARK: - 初始化测试
    
    @Test func testRouteManagerInitialization() async throws {
        // 创建测试用的内存数据库
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        #expect(routeManager.modelContext === container.mainContext)
    }
    

    
    // MARK: - 配送点管理测试
    
    @Test func testCreateDeliveryPoint() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建配送点
        let testCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
        let deliveryPoint = routeManager.createDeliveryPoint(
            sort_number: 1,
            address: "123 Test Street, Glen Waverley, VIC 3150",
            coordinate: testCoordinate,
            packageCount: 2,
            notes: "Test notes"
        )
        
        #expect(deliveryPoint.sort_number == 1)
        #expect(deliveryPoint.streetName == "123 Test Street, Glen Waverley, VIC 3150")
        #expect(deliveryPoint.latitude == testCoordinate.latitude)
        #expect(deliveryPoint.longitude == testCoordinate.longitude)
        #expect(deliveryPoint.packageCount == 2)
        #expect(deliveryPoint.notes == "Test notes")
    }
    
    @Test func testUpdateDeliveryPointStatus() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建配送点
        let testCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
        let deliveryPoint = routeManager.createDeliveryPoint(
            sort_number: 1,
            address: "Test Address",
            coordinate: testCoordinate
        )
        
        // 初始状态应该是pending
        #expect(deliveryPoint.deliveryStatus == .pending)
        
        // 更新状态
        routeManager.updateDeliveryPointStatus(deliveryPoint, status: .completed)
        #expect(deliveryPoint.deliveryStatus == .completed)
        
        routeManager.updateDeliveryPointStatus(deliveryPoint, status: .failed)
        #expect(deliveryPoint.deliveryStatus == .failed)
    }
    
    // MARK: - 配送组管理测试
    
    @Test func testCreateDeliveryGroup() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建配送点
        let points = [
            routeManager.createDeliveryPoint(
                sort_number: 1,
                address: "Address 1",
                coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
            ),
            routeManager.createDeliveryPoint(
                sort_number: 2,
                address: "Address 2",
                coordinate: CLLocationCoordinate2D(latitude: -37.8200, longitude: 145.1700)
            )
        ]
        
        // 创建配送组
        let group = routeManager.createDeliveryGroup(name: "Test Group", points: points, groupNumber: 1)
        
        #expect(group.name == "Test Group")
        #expect(group.groupNumber == 1)
        #expect(group.points.count == 2)
        #expect(group.createdAt != nil)
        
        // 验证点被正确分配到组
        for point in points {
            #expect(point.isAssignedToGroup == true)
            #expect(point.assignedGroupNumber == 1)
        }
    }
    
    @Test func testGetAllDeliveryGroups() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 初始应该没有组
        let initialGroups = routeManager.getAllDeliveryGroups()
        #expect(initialGroups.isEmpty)
        
        // 创建一个组
        let points = [
            routeManager.createDeliveryPoint(
                sort_number: 1,
                address: "Test Address",
                coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
            )
        ]
        
        let group = routeManager.createDeliveryGroup(name: "Test Group", points: points, groupNumber: 1)
        
        // 现在应该有一个组
        let groups = routeManager.getAllDeliveryGroups()
        #expect(groups.count == 1)
        #expect(groups.first?.id == group.id)
    }
    
    @Test func testDeleteDeliveryGroup() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建配送组
        let points = [
            routeManager.createDeliveryPoint(
                sort_number: 1,
                address: "Test Address",
                coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
            )
        ]
        
        let group = routeManager.createDeliveryGroup(name: "Test Group", points: points, groupNumber: 1)
        
        // 验证组存在
        let groupsBefore = routeManager.getAllDeliveryGroups()
        #expect(groupsBefore.count == 1)
        
        // 删除组
        try routeManager.deleteDeliveryGroup(group)
        
        // 验证组被删除
        let groupsAfter = routeManager.getAllDeliveryGroups()
        #expect(groupsAfter.isEmpty)
        
        // 验证点的分组状态被重置
        for point in points {
            #expect(point.isAssignedToGroup == false)
            #expect(point.assignedGroupNumber == nil)
        }
    }
    
    // MARK: - 点和组的关系管理测试
    
    @Test func testAddPointToGroup() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建空组
        let group = routeManager.createDeliveryGroup(name: "Test Group", points: [], groupNumber: 1)
        
        // 创建配送点
        let point = routeManager.createDeliveryPoint(
            sort_number: 1,
            address: "Test Address",
            coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
        )
        
        // 初始状态
        #expect(group.points.isEmpty)
        #expect(point.isAssignedToGroup == false)
        
        // 添加点到组
        routeManager.addPointToGroup(point, group: group)
        
        // 验证关系建立
        #expect(group.points.count == 1)
        #expect(group.points.first?.id == point.id)
        #expect(point.isAssignedToGroup == true)
        #expect(point.assignedGroupNumber == 1)
    }
    
    @Test func testRemovePointFromGroup() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建配送点
        let point = routeManager.createDeliveryPoint(
            sort_number: 1,
            address: "Test Address",
            coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
        )
        
        // 创建包含点的组
        let group = routeManager.createDeliveryGroup(name: "Test Group", points: [point], groupNumber: 1)
        
        // 验证初始状态
        #expect(group.points.count == 1)
        #expect(point.isAssignedToGroup == true)
        
        // 从组中移除点
        routeManager.removePointFromGroup(point, group: group)
        
        // 验证点被移除
        #expect(group.points.isEmpty)
        #expect(point.isAssignedToGroup == false)
        #expect(point.assignedGroupNumber == nil)
    }
    
    // MARK: - 距离计算测试
    
    @Test func testCalculateRegion() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 创建多个配送点
        let points = [
            routeManager.createDeliveryPoint(
                sort_number: 1,
                address: "Point 1",
                coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)
            ),
            routeManager.createDeliveryPoint(
                sort_number: 2,
                address: "Point 2",
                coordinate: CLLocationCoordinate2D(latitude: -37.8200, longitude: 145.1700)
            )
        ]
        
        // 计算区域
        let region = routeManager.calculateRegion(for: points)
        
        #expect(region != nil, "应该能够计算出区域")
        
        if let region = region {
            // 验证区域包含所有点
            #expect(region.center.latitude != 0)
            #expect(region.center.longitude != 0)
            #expect(region.span.latitudeDelta > 0)
            #expect(region.span.longitudeDelta > 0)
        }
    }
    
    @Test func testCalculateRegionWithEmptyPoints() async throws {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
        let container = try ModelContainer(for: schema, configurations: [config])
        
        let routeManager = RouteManager(modelContext: container.mainContext)
        
        // 空点数组应该返回nil
        let region = routeManager.calculateRegion(for: [])
        #expect(region == nil, "空点数组应该返回nil")
    }
}
