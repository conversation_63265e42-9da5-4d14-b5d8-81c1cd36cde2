import Foundation

/// 测试地址门牌号提取功能的改进
class AddressNumberExtractionTest {
    
    func testComplexStreetNumberExtraction() {
        print("🧪 开始测试复合门牌号提取功能...")
        
        // 测试用例：复合门牌号格式
        let testCases = [
            ("23/567 Botanic Drive, Glen Waverley VIC 3150", "567"),
            ("23/560-567 Botanic Drive, Glen Waverley VIC 3150", "560"),
            ("M104/25 Collins Street, Melbourne VIC 3000", "25"),
            ("Unit 5/123 Smith Road, Clayton VIC 3168", "123"),
            ("Apt 2/456 High Street, Glen Waverley VIC 3150", "456"),
            ("567 Botanic Drive, Glen Waverley VIC 3150", "567"), // 简单格式
            ("123A Collins Street, Melbourne VIC 3000", "123A"), // 带字母后缀
        ]
        
        for (address, expectedNumber) in testCases {
            let extractedNumber = extractMainStreetNumberFromTest(address)
            print("📍 地址: \(address)")
            print("   期望门牌号: \(expectedNumber)")
            print("   提取门牌号: \(extractedNumber ?? "nil")")
            print("   结果: \(extractedNumber == expectedNumber ? "✅ 通过" : "❌ 失败")")
            print("")
            
            // 记录测试结果，不中断执行
            if extractedNumber != expectedNumber {
                print("   ⚠️ 测试失败：期望 \(expectedNumber)，实际 \(extractedNumber ?? "nil")")
            }
        }
        
        print("🎯 复合门牌号提取测试完成")
    }
    
    /// 模拟 UniversalAddressProcessor 中的提取逻辑
    private func extractMainStreetNumberFromTest(_ address: String) -> String? {
        // 首先尝试提取复合门牌号的主要部分
        if let mainNumber = extractMainStreetNumber(from: address) {
            return mainNumber
        }
        
        // 如果没有复合格式，提取第一个包含数字的组件
        let components = address.components(separatedBy: " ")
        for component in components {
            if component.rangeOfCharacter(from: .decimalDigits) != nil {
                return component.trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        return nil
    }
    
    /// 从地址中提取复合门牌号的主要部分
    private func extractMainStreetNumber(from address: String) -> String? {
        // 查找包含斜杠的门牌号模式，支持字母前缀（如 M104/25）
        let pattern = "\\b[A-Za-z]*\\d+/([\\d-]+)\\b"
        
        guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
            return nil
        }
        
        let range = NSRange(address.startIndex..<address.endIndex, in: address)
        
        if let match = regex.firstMatch(in: address, options: [], range: range),
           let mainNumberRange = Range(match.range(at: 1), in: address) {
            let mainNumber = String(address[mainNumberRange])
            
            // 如果是范围格式（如 560-567），返回第一个数字
            if mainNumber.contains("-") {
                let parts = mainNumber.components(separatedBy: "-")
                if let firstPart = parts.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
                    return firstPart
                }
            }
            
            return mainNumber
        }
        
        return nil
    }
    
    func testAddressValidationImprovement() {
        print("🧪 开始测试地址验证改进...")

        // 模拟您提到的问题案例
        let problematicCases = [
            ("23/567 Botanic Drive, Glen Waverley VIC 3150", "567"),
            ("23/560-567 Main Street, Clayton VIC 3168", "560"),
            ("Unit 12/345 High Street, Glen Waverley VIC 3150", "345"),
        ]

        for (address, expectedMainNumber) in problematicCases {
            let extractedNumber = extractMainStreetNumberFromTest(address)

            print("📍 问题案例: \(address)")
            print("   应该提取: \(expectedMainNumber) (主要门牌号)")
            print("   实际提取: \(extractedNumber ?? "nil")")

            if extractedNumber == expectedMainNumber {
                print("   ✅ 改进成功：现在能正确提取主要门牌号")
            } else {
                print("   ❌ 仍有问题：未能正确提取主要门牌号")
            }
            print("")
        }

        print("🎯 地址验证改进测试完成")
    }

    func testSimplifiedHouseNumberGeneration() {
        print("🧪 开始测试简化门牌号生成...")

        // 测试带字母后缀的门牌号简化
        let letterSuffixCases = [
            ("145c Lilian Street, Glen Waverley VIC 3150", "145 Lilian Street, Glen Waverley VIC 3150"),
            ("23A Collins Street, Melbourne VIC 3000", "23 Collins Street, Melbourne VIC 3000"),
            ("567B High Street, Glen Waverley VIC 3150", "567 High Street, Glen Waverley VIC 3150"),
        ]

        for (original, expectedSimplified) in letterSuffixCases {
            let simplified = generateSimplifiedHouseNumberCandidatesTest(original)

            print("📍 原始地址: \(original)")
            print("   期望简化: \(expectedSimplified)")
            print("   生成候选: \(simplified)")

            if simplified.contains(expectedSimplified) {
                print("   ✅ 简化成功：生成了正确的候选地址")
            } else {
                print("   ❌ 简化失败：未生成期望的候选地址")
            }
            print("")
        }

        print("🎯 简化门牌号生成测试完成")
    }

    /// 模拟简化门牌号候选生成逻辑
    private func generateSimplifiedHouseNumberCandidatesTest(_ address: String) -> [String] {
        var candidates: [String] = []

        // 检测带字母后缀的门牌号（如 145c, 23A）
        let houseNumberPattern = "\\b(\\d+)[a-zA-Z]\\b"

        guard let regex = try? NSRegularExpression(pattern: houseNumberPattern, options: []) else {
            return candidates
        }

        let range = NSRange(address.startIndex..<address.endIndex, in: address)
        let matches = regex.matches(in: address, options: [], range: range)

        for match in matches {
            if let numberRange = Range(match.range(at: 1), in: address) {
                let numberOnly = String(address[numberRange])

                // 替换原地址中的门牌号
                if let fullMatchRange = Range(match.range, in: address) {
                    let originalHouseNumber = String(address[fullMatchRange])
                    let simplifiedAddress = address.replacingOccurrences(of: originalHouseNumber, with: numberOnly)

                    if simplifiedAddress != address {
                        candidates.append(simplifiedAddress)
                        print("🏠 生成简化门牌号候选: \(originalHouseNumber) -> \(numberOnly)")
                    }
                }
            }
        }

        return candidates
    }

    func testMissingHouseNumberDetection() {
        print("🧪 开始测试门牌号缺失检测...")

        // 模拟原始地址有门牌号但返回结果没有门牌号的情况
        let testCases = [
            ("145c Lilian Street, Glen Waverley VIC 3150", true),  // 有门牌号
            ("23A Collins Street, Melbourne VIC 3000", true),     // 有门牌号
            ("Lilian Street, Glen Waverley VIC 3150", false),     // 没有门牌号
            ("Collins Street, Melbourne VIC 3000", false),        // 没有门牌号
        ]

        for (address, shouldHaveHouseNumber) in testCases {
            let hasHouseNumber = hasHouseNumberInAddress(address)

            print("📍 地址: \(address)")
            print("   期望有门牌号: \(shouldHaveHouseNumber)")
            print("   检测到门牌号: \(hasHouseNumber)")

            if hasHouseNumber == shouldHaveHouseNumber {
                print("   ✅ 检测正确")
            } else {
                print("   ❌ 检测错误")
            }
            print("")
        }

        print("🎯 门牌号缺失检测测试完成")
    }

    /// 检测地址中是否包含门牌号（排除邮政编码）
    private func hasHouseNumberInAddress(_ address: String) -> Bool {
        // 使用更智能的门牌号检测逻辑
        return extractHouseNumberFromAddress(address) != nil
    }

    /// 从地址中提取门牌号（排除邮政编码）
    private func extractHouseNumberFromAddress(_ address: String) -> String? {
        // 先移除常见的邮政编码模式
        let addressWithoutPostcode = removePostcodeFromAddress(address)

        // 检测门牌号模式：地址开头的数字或数字+字母组合
        let houseNumberPattern = "^\\s*(\\d+[a-zA-Z]?)\\s+"

        guard let regex = try? NSRegularExpression(pattern: houseNumberPattern, options: []) else {
            return nil
        }

        let range = NSRange(addressWithoutPostcode.startIndex..<addressWithoutPostcode.endIndex, in: addressWithoutPostcode)

        if let match = regex.firstMatch(in: addressWithoutPostcode, options: [], range: range),
           let houseNumberRange = Range(match.range(at: 1), in: addressWithoutPostcode) {
            return String(addressWithoutPostcode[houseNumberRange])
        }

        // 检测复合门牌号模式（如 23/567）
        let compoundPattern = "^\\s*\\S+/([\\d-]+[a-zA-Z]?)\\s+"

        guard let compoundRegex = try? NSRegularExpression(pattern: compoundPattern, options: []) else {
            return nil
        }

        if let match = compoundRegex.firstMatch(in: addressWithoutPostcode, options: [], range: range),
           let mainNumberRange = Range(match.range(at: 1), in: addressWithoutPostcode) {
            return String(addressWithoutPostcode[mainNumberRange])
        }

        return nil
    }

    /// 移除地址中的邮政编码
    private func removePostcodeFromAddress(_ address: String) -> String {
        // 移除常见的邮政编码模式（如 VIC 3150, NSW 2000）
        let postcodePatterns = [
            "\\s+[A-Z]{2,3}\\s+\\d{4}\\s*$",  // VIC 3150, NSW 2000
            "\\s+\\d{4}\\s*$",                // 3150, 2000
            "\\s+\\d{5}\\s*$"                 // 美国邮编 12345
        ]

        var cleanAddress = address

        for pattern in postcodePatterns {
            cleanAddress = cleanAddress.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )
        }

        return cleanAddress.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// 运行测试的便利函数
func runAddressExtractionTests() {
    let test = AddressNumberExtractionTest()
    test.testComplexStreetNumberExtraction()
    test.testAddressValidationImprovement()
    test.testSimplifiedHouseNumberGeneration()
    test.testMissingHouseNumberDetection()
}

// 直接运行测试
//runAddressExtractionTests()
