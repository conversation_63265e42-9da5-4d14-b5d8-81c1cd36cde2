import XCTest
@testable import NaviBatch

/// 测试Apple Maps API速率限制功能
class RateLimitTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // 重置速率限制器状态
        Task {
            await GlobalGeocodingRateLimiter.shared.resetStats()
        }
    }
    
    /// 测试速率限制器基本功能
    func testRateLimiterBasicFunctionality() async {
        let rateLimiter = GlobalGeocodingRateLimiter.shared
        
        // 初始状态应该允许请求
        let canMakeInitialRequest = await rateLimiter.canMakeRequest()
        XCTAssertTrue(canMakeInitialRequest, "初始状态应该允许请求")
        
        // 记录请求
        await rateLimiter.recordRequest()
        
        // 获取状态
        let status = await rateLimiter.getStatus()
        XCTAssertEqual(status.currentRequests, 1, "应该记录一个请求")
        XCTAssertEqual(status.maxRequests, 35, "最大请求数应该是35")
    }
    
    /// 测试速率限制触发
    func testRateLimitTrigger() async {
        let rateLimiter = GlobalGeocodingRateLimiter.shared
        
        // 模拟达到限制
        for _ in 0..<35 {
            await rateLimiter.recordRequest()
        }
        
        let status = await rateLimiter.getStatus()
        XCTAssertEqual(status.currentRequests, 35, "应该记录35个请求")
        
        let canMakeRequest = await rateLimiter.canMakeRequest()
        XCTAssertFalse(canMakeRequest, "达到限制后不应该允许新请求")
    }
    
    /// 测试智能批次延迟
    func testSmartBatchDelay() async {
        let rateLimiter = GlobalGeocodingRateLimiter.shared
        
        // 低使用率
        let lowDelay = await rateLimiter.getSmartBatchDelay()
        XCTAssertEqual(lowDelay, 0.5, "低使用率应该返回0.5秒延迟")
        
        // 中等使用率
        for _ in 0..<20 {
            await rateLimiter.recordRequest()
        }
        let mediumDelay = await rateLimiter.getSmartBatchDelay()
        XCTAssertEqual(mediumDelay, 1.0, "中等使用率应该返回1.0秒延迟")
        
        // 高使用率
        for _ in 0..<10 {
            await rateLimiter.recordRequest()
        }
        let highDelay = await rateLimiter.getSmartBatchDelay()
        XCTAssertEqual(highDelay, 2.0, "高使用率应该返回2.0秒延迟")
    }
    
    /// 测试批量处理暂停检查
    func testBatchProcessingPause() async {
        let rateLimiter = GlobalGeocodingRateLimiter.shared
        
        // 初始状态不应该暂停
        let shouldNotPause = await rateLimiter.shouldPauseBatchProcessing()
        XCTAssertFalse(shouldNotPause, "初始状态不应该暂停批量处理")
        
        // 接近限制时应该暂停
        for _ in 0..<31 { // 35 - 5 + 1 = 31
            await rateLimiter.recordRequest()
        }
        let shouldPause = await rateLimiter.shouldPauseBatchProcessing()
        XCTAssertTrue(shouldPause, "接近限制时应该暂停批量处理")
    }
    
    /// 测试状态计算
    func testStatusCalculation() async {
        let rateLimiter = GlobalGeocodingRateLimiter.shared
        
        // 添加一些请求
        for _ in 0..<10 {
            await rateLimiter.recordRequest()
        }
        
        let status = await rateLimiter.getStatus()
        XCTAssertEqual(status.currentRequests, 10, "当前请求数应该是10")
        
        let expectedUtilization = Double(10) / Double(35) * 100
        XCTAssertEqual(status.utilizationPercentage, expectedUtilization, accuracy: 0.01, "使用率计算应该正确")
        
        XCTAssertFalse(status.isNearLimit, "10/35不应该接近限制")
        
        // 添加更多请求
        for _ in 0..<20 {
            await rateLimiter.recordRequest()
        }
        
        let newStatus = await rateLimiter.getStatus()
        XCTAssertTrue(newStatus.isNearLimit, "30/35应该接近限制")
    }
    
    /// 测试统计重置
    func testStatsReset() async {
        let rateLimiter = GlobalGeocodingRateLimiter.shared
        
        // 添加一些请求
        for _ in 0..<10 {
            await rateLimiter.recordRequest()
        }
        
        var status = await rateLimiter.getStatus()
        XCTAssertEqual(status.totalRequests, 10, "总请求数应该是10")
        
        // 重置统计
        await rateLimiter.resetStats()
        
        status = await rateLimiter.getStatus()
        XCTAssertEqual(status.totalRequests, 0, "重置后总请求数应该是0")
        XCTAssertEqual(status.throttledRequests, 0, "重置后限制请求数应该是0")
    }
}

/// 测试地址验证服务的速率限制集成
class AddressValidationRateLimitTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        Task {
            await GlobalGeocodingRateLimiter.shared.resetStats()
        }
    }
    
    /// 测试批量地址验证是否正确使用速率限制
    func testBatchAddressValidationRateLimit() async {
        let service = UnifiedAddressValidationService.shared
        
        // 准备测试地址
        let testAddresses = [
            "1 Apple Park Way, Cupertino, CA",
            "1600 Amphitheatre Parkway, Mountain View, CA",
            "1 Hacker Way, Menlo Park, CA"
        ]
        
        let startTime = Date()
        
        // 执行批量验证
        let results = await service.validateAddresses(testAddresses, batchSize: 2)
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // 验证结果
        XCTAssertEqual(results.count, testAddresses.count, "应该返回所有地址的验证结果")
        
        // 验证处理时间（应该包含速率限制延迟）
        XCTAssertGreaterThan(duration, 1.0, "批量处理应该包含延迟时间")
        
        // 验证速率限制器状态
        let status = await GlobalGeocodingRateLimiter.shared.getStatus()
        XCTAssertGreaterThan(status.totalRequests, 0, "应该记录API请求")
    }
}
