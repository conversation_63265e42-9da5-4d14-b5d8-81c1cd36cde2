//
//  GeocodingServiceTests.swift
//  NaviBatchTests
//
//  Created by Augment Agent
//

import Testing
import CoreLocation
import Combine
@testable import NaviBatch

/// GeocodingService 专项测试套件
struct GeocodingServiceTests {
    
    // MARK: - 地址验证测试
    
    @Test func testGeocodingServiceExists() async throws {
        // 测试GeocodingService单例存在
        let service = GeocodingService.shared
        #expect(type(of: service) == GeocodingService.self, "GeocodingService应该存在")
    }
    
    @Test func testGeocodingServiceType() async throws {
        // 测试GeocodingService类型
        let service = GeocodingService.shared
        #expect(type(of: service) == GeocodingService.self, "应该是GeocodingService类型")
    }
    
    @Test func testGeocodingResultCreation() async throws {
        // 测试GeocodingResult创建
        let testAddress = "123 Test Street, Glen Waverley, VIC 3150"
        let testCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)

        let result = GeocodingResult(
            address: testAddress,
            coordinate: testCoordinate,
            status: .success,
            attemptCount: 1
        )

        #expect(result.address == testAddress)
        #expect(result.coordinate?.latitude == testCoordinate.latitude)
        #expect(result.coordinate?.longitude == testCoordinate.longitude)
        #expect(result.status == .success)
        #expect(result.attemptCount == 1)
    }
    
    // MARK: - 地理编码状态测试

    @Test func testGeocodingStatusValues() async throws {
        // 测试GeocodingStatus枚举值
        let statuses: [GeocodingStatus] = [.success, .failed, .notFound, .networkError, .timeout, .invalidAddress]

        for status in statuses {
            // 验证状态值存在且可以比较
            #expect(status == status, "状态应该等于自身")
        }
    }
    
    // MARK: - 坐标验证测试

    @Test func testCoordinateValidation() async throws {
        // 测试坐标创建和验证
        let validCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 145.1628)

        #expect(validCoordinate.latitude == -37.8136)
        #expect(validCoordinate.longitude == 145.1628)

        // 测试坐标是否有效
        let isValid = CLLocationCoordinate2DIsValid(validCoordinate)
        #expect(isValid == true, "坐标应该是有效的")

        // 测试无效坐标
        let invalidCoordinate = CLLocationCoordinate2D(latitude: 200, longitude: 200)
        let isInvalid = CLLocationCoordinate2DIsValid(invalidCoordinate)
        #expect(isInvalid == false, "坐标应该是无效的")
    }
}


