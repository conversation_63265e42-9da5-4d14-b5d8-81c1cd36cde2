# YWE快递AI提示词优化文档

## 更新日期：2025-06-24

## 概述

基于YWE快递应用界面截图分析，完善了YWE的AI提示词，添加了智能地址分离功能，提升了地址识别的准确性和实用性。

## YWE界面特征分析

### 界面布局特点
- **界面语言**：中文界面（"派送任务"、"收件人"、"地址"、"派送成功"）
- **背景样式**：白色背景列表界面，卡片式布局
- **排序显示**：左侧 # + 数字（#1, #2, #3, #4...）
- **状态标识**：右侧绿色"派送成功"状态，黄色"派送图片待核验"警告

### 数据格式特征
- **运单号**：YWAUS + 15位数字（如：YWAUS010000147255）
- **收件人**：完整英文姓名（如：Tarun karra, Karthik Vemula）
- **地址格式**：美国地址格式，部分包含公寓号
- **地址示例**：
  - "2200 Cabrillo Path, LEANDER, TX 78641-3382, US"
  - "14801 Ronald W Reagan Blvd Apt 9109, LEANDER, TX 78641-4648, US"

## 智能地址分离功能

### 核心改进
1. **智能公寓号分离**：自动识别并分离公寓/单元信息
2. **双地址版本**：提供完整地址和纯街道地址两个版本
3. **Apple Maps兼容**：使用Apple Maps标准的街道简称格式
4. **地址标准化**：统一地址格式，提升geocoding准确性

### 地址分离规则
- **识别关键词**：Apt, Apartment, Unit, Suite, Room, Rm, Ste, # + 数字
- **分离示例**：
  ```
  原始：14801 Ronald W Reagan Blvd Apt 9109, Leander, TX, 78641
  完整：14801 Ronald W Reagan Blvd Apt 9109, Leander, TX, 78641
  街道：14801 Ronald W Reagan Blvd, Leander, TX, 78641
  ```

## AI提示词更新内容

### 1. FirebaseAIService.swift - createYWEPrompt()
```swift
📦 YWE 快递识别 - 智能地址分离提取：

🎯 核心信息：
1. 排序号：# + 数字 (#1, #2, #3, #4...)
2. 运单号：YWAUS + 15位数字 (如: YWAUS010000147255)
3. 收件人：完整英文姓名
4. 地址：智能分离公寓号码

🏠 地址智能分离规则：
如果地址包含公寓/单元信息，请提供两个版本：
- full_address: 完整地址（包含公寓号）
- address: 纯街道地址（不含公寓号）
```

### 2. GemmaVisionService.swift - createYWEPrompt()
同样的智能地址分离提示词，确保两个AI服务的一致性。

### 3. ImageAddressRecognizer.swift - createYWEPromptForDebug()
调试版本的提示词，用于开发者工具中的AI提示词预览功能。

## 地区特定提示词更新

### 修正地址格式分类
- **原来**：YWE被错误分类为澳洲地址格式
- **现在**：正确分类为美国地址格式
- **更新内容**：
  ```swift
  case .ywe:
      return baseUSAPrompt + """

      YWE 特征:
      - 追踪号码: YWAUS + 15位数字 (如: YWAUS010000147255)
      - 排序号码: # + 数字 (#1, #2, #3, #4...)
      - 收件人信息: 完整英文姓名
      - 美国地址格式，支持智能公寓号分离
      - 中文界面但地址为英文格式
      """
  ```

## 技术实现细节

### 智能地址分离算法
1. **公寓号检测**：使用正则表达式识别公寓号关键词
2. **地址分割**：将地址分为街道部分和公寓部分
3. **格式标准化**：统一使用Apple Maps兼容的简称格式
4. **双版本输出**：同时提供完整地址和纯街道地址

### Apple Maps兼容性
- **街道简称**：Dr, Ct, Blvd, Ave, St, Ln, Pl, Way
- **备选全称**：Drive, Court, Boulevard, Avenue, Street, Lane, Place, Way
- **州名缩写**：TX, CA, NY, FL等标准美国州名缩写
- **邮编格式**：5位数字或5+4位格式

## 更新的文件列表

### 主要更新文件
1. **NaviBatch/Services/FirebaseAIService.swift**
   - 更新 `createYWEPrompt()` 方法
   - 修正地区特定提示词中的YWE分类

2. **NaviBatch/Services/GemmaVisionService.swift**
   - 更新 `createYWEPrompt()` 方法
   - 修正地区特定提示词中的YWE分类

3. **NaviBatch/Views/Components/ImageAddressRecognizer.swift**
   - 更新 `createYWEPromptForDebug()` 方法

### 文档更新
4. **NaviBatch/Documentation/YWE_Address_Cleaning_Fix.md**
   - 重写为YWE AI提示词优化文档
   - 添加智能地址分离功能说明

## 测试建议

### 1. AI识别测试
- 使用YWE界面截图测试AI识别准确性
- 验证排序号、运单号、收件人信息的提取
- 测试智能地址分离功能

### 2. 地址格式测试
- 测试包含公寓号的地址分离
- 验证Apple Maps兼容的地址格式
- 检查geocoding准确性提升

### 3. 地址质量测试
- 对比更新前后的地址识别质量
- 测试geocoding成功率提升
- 验证地址标准化效果

## 预期效果

### 识别准确性提升
1. **排序号识别**：准确识别 # + 数字格式
2. **运单号识别**：精确提取YWAUS + 15位数字格式
3. **收件人识别**：完整提取英文姓名
4. **地址分离**：智能分离公寓号，提供双版本地址

### Geocoding准确性提升
1. **公寓号处理**：避免公寓号干扰geocoding
2. **地址标准化**：使用Apple Maps兼容格式
3. **坐标准确性**：提升地址定位精度
4. **错误率降低**：减少geocoding失败情况

## 版本信息

- **更新日期**：2025-06-24
- **更新版本**：v1.0.8+
- **影响范围**：YWE快递AI识别功能
- **兼容性**：向后兼容，不影响现有功能

## 总结

本次YWE AI提示词优化基于真实界面截图分析，添加了智能地址分离功能，修正了地址格式分类，显著提升了YWE快递地址识别的准确性和实用性。智能地址分离功能特别有助于解决包含公寓号的地址geocoding准确性问题，为用户提供更好的导航体验。

## 相关文档

- [LDS EPOD地址分离增强](./LDS_EPOD_Address_Separation_Enhancement.md)
- [GoFo AI提示词增强](./GoFo_AI_Prompt_Enhancement.md)
- [AI集成指南](./AI_Integration_README.md)
