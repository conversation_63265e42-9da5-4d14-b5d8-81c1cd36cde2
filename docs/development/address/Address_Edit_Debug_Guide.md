# 地址编辑功能调试指南

## 概述

当用户在bottom sheet中点击"edit address"后地址无法识别时，可以使用以下调试工具和方法来诊断问题。

## 已添加的调试功能

### 1. 启用Location日志

已修改 `LoggerConfig.swift`，临时启用了location类型的日志：

```swift
enabledLogTypes: Set<LogType> = [
    .error, .warning, .ai, .ocr, .location  // 临时启用location日志
]
```

### 2. 详细调试日志

在以下关键位置添加了详细的调试日志：

#### EnhancedAddressAutocomplete.swift
- 用户输入地址时的搜索触发
- 防抖机制的执行过程
- 地址翻译结果
- 搜索服务调用过程
- 地址选择和处理过程
- UniversalAddressProcessor调用结果

#### AddressEditBottomSheet.swift
- 用户选择地址时的回调处理
- 保存按钮点击事件
- 保存过程的详细步骤

#### LocaleAwareAddressSearchService.swift
- 搜索服务的初始化和配置
- 英文语言环境的设置
- MKLocalSearchCompleter的配置和执行
- 搜索结果的接收和过滤
- 错误处理的详细信息

### 3. 地址编辑调试工具

创建了专门的调试工具 `AddressEditDebugView.swift`，可以通过以下路径访问：

**菜单 → 开发者工具 → 地址编辑功能调试**

该工具提供：
- 独立的地址搜索测试
- 实时搜索结果显示
- 坐标获取测试
- 详细的调试日志查看

## 使用方法

### 步骤1：启用调试模式

1. 确保应用处于DEBUG模式
2. 打开应用，进入菜单
3. 找到"开发者工具"选项（仅在DEBUG模式下可见）

### 步骤2：使用地址编辑调试工具

1. 在开发者工具中选择"地址编辑功能调试"
2. 输入有问题的地址
3. 点击"开始测试"
4. 查看搜索结果和日志信息

### 步骤3：查看控制台日志

在Xcode控制台中查找以下标识的日志：

```
📍 [地址编辑调试] - 地址编辑相关的所有日志
🔍 [地址编辑调试] - 搜索相关的日志
🚀 [地址编辑调试] - 搜索服务相关的日志
🇺🇸 [地址编辑调试] - 语言环境相关的日志
💾 [地址编辑调试] - 保存相关的日志
```

### 步骤4：重现问题

1. 在实际的地址编辑界面中输入有问题的地址
2. 观察控制台日志输出
3. 记录任何错误信息或异常行为

## 常见问题诊断

### 问题1：搜索无结果

**日志标识：** `搜索完成: 找到 0 个结果`

**可能原因：**
- 网络连接问题
- Apple Maps API限制
- 地址格式不被识别
- 语言环境设置问题

**调试步骤：**
1. 检查网络连接
2. 查看是否有API限制错误
3. 尝试简化地址格式
4. 检查语言环境设置日志

### 问题2：搜索服务未响应

**日志标识：** `LocaleAwareAddressSearchService开始搜索` 但没有后续日志

**可能原因：**
- MKLocalSearchCompleter初始化失败
- 代理设置问题
- 系统权限问题

**调试步骤：**
1. 检查MKLocalSearchCompleter创建日志
2. 验证代理设置日志
3. 检查位置权限状态

### 问题3：地址选择后无响应

**日志标识：** `用户选择了搜索结果` 但没有坐标获取日志

**可能原因：**
- MKLocalSearch请求失败
- 坐标获取超时
- 回调处理异常

**调试步骤：**
1. 检查MKLocalSearch请求日志
2. 查看是否有超时错误
3. 验证回调链是否完整

## 日志分析示例

### 正常流程日志：
```
📍 [地址编辑调试] 用户输入地址: '123 Main St'
🔍 [地址编辑调试] 开始防抖延迟 500ms
🔍 [地址编辑调试] 防抖完成，开始处理搜索
🌍 [地址编辑调试] 地址翻译结果: '123 Main St' -> '123 Main St'
🚀 [地址编辑调试] LocaleAwareAddressSearchService开始搜索
🇺🇸 [地址编辑调试] 英文语言环境搜索完成: 3个原始结果
✅ [地址编辑调试] 有效结果1: 标题='123 Main St', 副标题='Anytown, CA'
🎯 [地址编辑调试] 用户选择结果: 123 Main St
✅ [地址编辑调试] 获取坐标成功: (37.7749, -122.4194)
```

### 异常流程日志：
```
📍 [地址编辑调试] 用户输入地址: 'invalid address'
🔍 [地址编辑调试] 开始防抖延迟 500ms
🚀 [地址编辑调试] LocaleAwareAddressSearchService开始搜索
❌ [地址编辑调试] 英文语言环境搜索失败: Network error
```

## 恢复正常模式

测试完成后，记得恢复LoggerConfig设置：

```swift
enabledLogTypes: Set<LogType> = [
    .error, .warning, .ai, .ocr  // 移除location日志
]
```

## 联系开发者

如果通过以上调试步骤仍无法解决问题，请提供：

1. 完整的控制台日志
2. 问题地址示例
3. 设备和系统版本信息
4. 网络环境描述

这将帮助开发者快速定位和解决问题。
