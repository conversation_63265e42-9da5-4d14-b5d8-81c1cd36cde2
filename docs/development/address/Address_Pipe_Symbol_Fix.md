# 地址管道符号修复文档

## 问题描述

在NaviBatch应用中，用户发现地址显示中包含管道符号（|）和其他元数据信息，如：
- `2800 Cabrillo Path, LEANDER, TX|`
- `14801 ronald w reagan blvd, LEANDER, TX|`

这些管道符号是内部处理时用于分离地址和元数据的标记，不应该在用户界面中显示。

## 问题根因

1. **数据存储问题**：在创建DeliveryPoint时，`originalAddress`字段没有被正确设置为清理后的地址
2. **显示逻辑问题**：`primaryAddress`计算属性优先使用`originalAddress`，如果该字段包含管道符号，就会直接显示给用户
3. **历史数据问题**：数据库中已存在的地址可能包含管道符号

## 修复方案

### 1. 修复数据创建逻辑

在所有创建DeliveryPoint的地方，确保`originalAddress`字段被设置为清理后的地址：

**修复的文件：**
- `NaviBatch/Services/DeliveryPointManager.swift`
- `NaviBatch/Managers/RouteManager.swift`
- `NaviBatch/Views/Components/DeliveryStatusManager.swift`
- `NaviBatch/Views/Components/AddDeliveryPointSheet.swift`

**修复内容：**
```swift
// 修复前
let deliveryPoint = DeliveryPoint(
    sort_number: sortNumber,
    latitude: result.coordinate?.latitude ?? 0,
    longitude: result.coordinate?.longitude ?? 0
)

// 修复后
let deliveryPoint = DeliveryPoint(
    sort_number: sortNumber,
    originalAddress: cleanAddress, // 🎯 设置清理后的原始地址，不包含管道符号
    latitude: result.coordinate?.latitude ?? 0,
    longitude: result.coordinate?.longitude ?? 0
)
```

### 2. 创建地址清理工具

**新增文件：**
- `NaviBatch/Utilities/AddressCleanupManager.swift` - 地址清理管理器
- `NaviBatch/Views/Developer/AddressCleanupView.swift` - 地址清理工具界面

**功能特性：**
- 检测需要清理的地址数量
- 批量清理地址中的管道符号和元数据
- 实时进度显示
- 安全的数据库操作

### 3. 集成到开发者工具

在开发者工具菜单中添加了"地址清理工具"选项，方便开发者和测试人员使用。

**修改文件：**
- `NaviBatch/Views/Development/DeveloperToolsView.swift`

## 清理规则

地址清理工具会移除以下内容：
1. 管道符号（|）后的所有元数据信息
2. 第三方排序号模式（如 ISORT:8, D90, D91等）
3. 多余的空格

**清理示例：**
```
清理前: "2800 Cabrillo Path, LEANDER, TX|APP:ywe|TRACK:123456"
清理后: "2800 Cabrillo Path, LEANDER, TX"
```

## 使用方法

### 对于开发者：
1. 打开应用的开发者工具
2. 选择"地址清理工具"
3. 点击"检查需要清理的地址"查看需要清理的数量
4. 点击"开始清理地址"执行清理操作

### 对于新数据：
修复后创建的所有新地址都会自动使用清理后的地址，不会再出现管道符号问题。

## 技术细节

### 地址清理正则表达式：
```swift
// 移除管道符号后的元数据
cleanedAddress = cleanedAddress.replacingOccurrences(
    of: "\\|[^|\\n]*",
    with: "",
    options: .regularExpression
)

// 移除第三方排序号
let sortPatterns = [
    "\\bISORT:\\d+\\b",  // ISORT:8
    "\\bD\\d+\\b",       // D90, D91, D146等
    "\\bSORT:\\d+\\b"    // SORT:2
]
```

### 数据安全：
- 清理操作只修改显示相关的字段
- 不影响坐标、状态等核心数据
- 支持批量处理，每10个地址保存一次
- 提供详细的进度反馈

## 测试验证

1. **新地址测试**：创建新的配送点，确认地址显示不包含管道符号
2. **清理工具测试**：使用清理工具处理现有数据，验证清理效果
3. **界面测试**：检查路线列表、地址详情等界面的地址显示

## 注意事项

1. 建议在执行清理操作前备份数据
2. 清理过程中请勿关闭应用
3. 清理操作不可撤销，请谨慎使用
4. 清理工具仅在DEBUG模式下的开发者工具中可用

## 界面优化

### 按钮对齐优化
在修复地址显示问题的同时，还优化了路线视图中按钮的对齐方式：

**问题**：当没有地址内容时（如"Add Start Point"、"Add New Address"、"Add End Point"按钮），文本与图标顶部对齐，导致视觉效果不佳。

**解决方案**：
- 添加动态对齐逻辑：`shouldCenterAlign = isAddStopRow`
- 当没有地址内容时，使用居中对齐（`.center`）
- 当有地址内容时，使用顶部对齐（`.top`）

**修改文件**：
- `NaviBatch/Views/Components/RouteBottomSheet.swift` - RoutePointRow组件

**效果**：
- 添加按钮的文本现在与图标垂直居中对齐，视觉效果更加平衡
- 有地址内容的行保持原有的顶部对齐，确保多行地址显示正常

## 更新日期

2025-06-20 - 初始修复完成
2025-06-20 - 添加按钮对齐优化
