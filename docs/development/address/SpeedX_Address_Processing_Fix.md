# SpeedX地址处理修复文档

## 🚨 问题描述

在处理SpeedX快递时，发现了一个严重的提示词冲突问题：

### 问题现象
```
🤖 AI: 🌍 GoFo地址移除国家: '1240 S Mayfair Ave, Daly City, CA, 94015, USA Myat Noe N... C' -> '1240 S Mayfair Ave, Daly City 94015, USA Myat Noe N... C'
```

### 问题分析
1. **GoFo逻辑混入SpeedX**：在处理SpeedX时错误地执行了GoFo的地址清理逻辑
2. **地址格式错误**：移除了"CA"但保留了"USA"，这与SpeedX的要求相反
3. **提示词隔离失效**：尽管实施了隔离架构，但地址后处理逻辑没有区分应用类型

## 🔧 根本原因

### FirebaseAIService.swift 第1117-1122行
```swift
// 🎯 GoFo地址清理：移除国家后缀
var cleanedAddress = address
if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
    cleanedAddress = addressWithoutCountry
    Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
}
```

**问题**：这段代码没有检查`appType`，导致所有快递都执行GoFo的地址清理逻辑。

### GemmaVisionService.swift 第1226-1231行
```swift
// 🎯 GoFo地址清理：移除国家后缀
var cleanedAddress = address
if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
    cleanedAddress = addressWithoutCountry
    Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
}
```

**问题**：同样的问题，没有应用类型检查。

## ✅ 解决方案

### 1. FirebaseAIService.swift 修复

#### 添加应用类型跟踪
```swift
// 🎯 当前处理的应用类型（用于地址清理逻辑）
private var currentAppType: DeliveryAppType?

private func getCurrentAppType() -> DeliveryAppType? {
    return currentAppType
}

private func setCurrentAppType(_ appType: DeliveryAppType) {
    currentAppType = appType
}
```

#### 添加SpeedX专用地址清理
```swift
// 🔴 SpeedX专用地址清理：移除USA但保留CA
private func cleanSpeedXAddress(_ address: String) -> String {
    var cleaned = address
    
    // 移除USA后缀，但保留CA州信息
    let patterns = [
        ", \\s*USA\\s*$",  // 末尾的USA
        "\\s*,\\s*USA\\s*,", // 中间的USA
        "^\\s*USA\\s*,\\s*"  // 开头的USA
    ]
    
    for pattern in patterns {
        cleaned = cleaned.replacingOccurrences(
            of: pattern,
            with: "",
            options: [.regularExpression, .caseInsensitive]
        )
    }
    
    // 清理多余的逗号和空格
    cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    cleaned = cleaned.replacingOccurrences(of: ",,", with: ",")
    cleaned = cleaned.replacingOccurrences(of: ", ,", with: ",")
    
    return cleaned
}
```

#### 修复地址清理逻辑
```swift
// 🎯 应用特定的地址清理逻辑
var cleanedAddress = address

// 🚨 重要：只对特定快递应用地址清理，避免冲突
if let currentAppType = getCurrentAppType() {
    switch currentAppType {
    case .gofo:
        // GoFo地址清理：移除国家后缀
        if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
            cleanedAddress = addressWithoutCountry
            Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
        }
    case .speedx:
        // SpeedX地址清理：移除USA但保留CA
        cleanedAddress = cleanSpeedXAddress(address)
        Logger.aiDebug("🔴 SpeedX地址清理: '\(address)' -> '\(cleanedAddress)'")
    default:
        // 其他快递保持原样
        break
    }
}
```

#### 在主要方法中设置应用类型
```swift
func extractAddressesFromImage(_ image: UIImage, appType: DeliveryAppType = .justPhoto, isPDFImage: Bool = false, isSegmentedImage: Bool = false) async throws -> GemmaAddressResult {
    // ...
    // 🎯 设置当前处理的应用类型
    setCurrentAppType(appType)
    // ...
}
```

### 2. GemmaVisionService.swift 修复

#### 修改parseGemmaResponse方法签名
```swift
private func parseGemmaResponse(_ jsonString: String, appType: DeliveryAppType = .justPhoto) -> (addresses: [String], confidence: Double, detectedAppType: DeliveryAppType?)? {
```

#### 更新所有调用点
```swift
if let result = parseGemmaResponse(jsonResponse, appType: appType) {
```

#### 添加相同的地址清理逻辑
```swift
// 🎯 应用特定的地址清理逻辑
var cleanedAddress = address

// 🚨 重要：只对特定快递应用地址清理，避免冲突
switch appType {
case .gofo:
    // GoFo地址清理：移除国家后缀
    if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
        cleanedAddress = addressWithoutCountry
        Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
    }
case .speedx:
    // SpeedX地址清理：移除USA但保留CA
    cleanedAddress = cleanSpeedXAddress(address)
    Logger.aiDebug("🔴 SpeedX地址清理: '\(address)' -> '\(cleanedAddress)'")
default:
    // 其他快递保持原样
    break
}
```

## 🎯 预期效果

### 修复前（错误）
```
🤖 AI: 🌍 GoFo地址移除国家: '1240 S Mayfair Ave, Daly City, CA, 94015, USA' -> '1240 S Mayfair Ave, Daly City 94015, USA'
```

### 修复后（正确）
```
🤖 AI: 🔴 SpeedX地址清理: '1240 S Mayfair Ave, Daly City, CA, 94015, USA' -> '1240 S Mayfair Ave, Daly City, CA, 94015'
```

## 📋 修改的文件

1. **NaviBatch/Services/FirebaseAIService.swift**
   - 添加应用类型跟踪机制
   - 添加SpeedX专用地址清理方法
   - 修复地址清理逻辑，添加应用类型检查
   - 在主要方法中设置当前应用类型

2. **NaviBatch/Services/GemmaVisionService.swift**
   - 修改parseGemmaResponse方法签名，添加appType参数
   - 更新所有调用点传递appType
   - 添加相同的应用类型特定地址清理逻辑
   - 添加SpeedX专用地址清理方法

## 🔍 测试验证

编译测试通过：
```bash
xcodebuild -project NaviBatch.xcodeproj -scheme NaviBatch -destination 'platform=iOS Simulator,name=iPhone 16 Pro' build
** BUILD SUCCEEDED **
```

## 🚀 部署说明

此修复确保了：
1. **提示词隔离**：每个快递应用只执行自己的地址清理逻辑
2. **SpeedX正确处理**：移除USA但保留CA州信息
3. **GoFo不受影响**：继续使用原有的国家移除逻辑
4. **其他快递保持原样**：不应用任何特殊清理逻辑

## 📝 注意事项

1. 此修复解决了提示词冲突的根本问题
2. 未来添加新快递时，需要在switch语句中添加相应的处理逻辑
3. 地址清理逻辑现在完全基于应用类型，确保了隔离性
