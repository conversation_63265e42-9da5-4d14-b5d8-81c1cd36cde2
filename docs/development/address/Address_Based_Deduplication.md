# 基于地址内容的智能去重策略

## 🎯 核心目标
**确保视频中的每一个地址都被捕获，相同地址只保留最佳帧**

## 📊 工作原理

### 1. 地址识别与分组
```
帧#1: "123 Main St" → 地址组A
帧#2: "123 Main St" → 地址组A  
帧#3: "456 Oak Ave" → 地址组B
帧#4: "123 Main St" → 地址组A
帧#5: "789 Pine Dr" → 地址组C
```

### 2. 每组选择最佳帧
```
地址组A: 帧#1,#2,#4 → 选择帧#2 (最高置信度)
地址组B: 帧#3 → 保留帧#3 (唯一帧)
地址组C: 帧#5 → 保留帧#5 (唯一帧)
```

### 3. 最终结果
```
原始: 5帧 → 最终: 3帧
✅ 3个不同地址 → 3个代表帧
🎯 100%地址覆盖率，0%地址遗漏
```

## 🔧 智能特性

### 地址标准化
- `"123 Main Street"` → `"123 MAIN ST"`
- `"123 Main St, CA 94015"` → `"123 MAIN ST"`
- `"123 MAIN ST USA"` → `"123 MAIN ST"`

### 最佳帧选择优先级
1. **OCR置信度最高**
2. **地址信息最完整**
3. **时间戳最新**

### 模糊匹配
- 85%相似度阈值
- 处理OCR识别误差
- 避免同一地址被误判为不同地址

## 📱 使用场景

### SpeedX视频处理
- **启用条件**: `DeliveryAppType.speedx`
- **去重模式**: `.addressBased`
- **效果**: 每个配送地址保留一帧

### 典型场景示例

#### 场景1: 用户停留3秒查看同一地址
```
输入: 90帧显示 "123 Main St"
输出: 1帧 (最高置信度的帧)
节省: 89帧 (98.9%去重率)
```

#### 场景2: 视频包含多个不同地址
```
输入: 90帧显示 7个不同地址
输出: 7帧 (每个地址1帧)
节省: 83帧 (92.2%去重率)
```

#### 场景3: 混合场景
```
输入: 100帧
- 地址A: 30帧 → 1帧
- 地址B: 25帧 → 1帧  
- 地址C: 20帧 → 1帧
- 地址D: 15帧 → 1帧
- 无地址: 10帧 → 3帧 (传统去重)
输出: 7帧总计
```

## 🎉 核心优势

- 🎯 **零地址遗漏**: 每个地址都有代表帧
- 🚀 **最大去重**: 相同地址只保留最佳帧
- 📊 **智能选择**: 基于OCR质量选择最佳帧
- ⚡ **高效处理**: 大幅减少冗余帧数
- 🔍 **完美适配**: 专为SpeedX配送场景优化

## 📝 日志输出示例

```
📍 开始基于地址内容的智能去重，共90帧...
🎯 目标：确保每个不同地址都有代表帧，相同地址只保留最佳帧

📊 地址分组统计:
   📍 识别到 7 个不同地址
   ❓ 无地址帧: 3 帧

📍 地址: 123 MAIN ST
   🔢 帧群组: [12, 13, 14, 15, 16] → 选择帧#14
   🏆 选择原因: 最高OCR置信度(0.96)

📍 地址去重完成: 90 → 10 帧
📊 统计信息:
   📍 不同地址数: 7
   🗑️ 移除重复帧: 80 (88.9%)
   ✅ 地址覆盖率: 100% (每个地址都有代表帧)
```

## 🔄 与其他去重模式的对比

| 去重模式 | 目标 | 适用场景 | 优势 |
|---------|------|----------|------|
| `imageSimilarity` | 图像相似度去重 | GoFo等视觉应用 | 快速，无需OCR |
| `ocrContent` | OCR内容去重 | 通用文本识别 | 精确，基于文本 |
| `addressBased` | 地址内容去重 | SpeedX配送场景 | 零遗漏，最优化 |

## 🚀 实现状态

- ✅ **已实现**: 基于地址内容的智能去重
- ✅ **已集成**: SpeedX应用类型自动启用
- ✅ **已测试**: 编译通过，无错误
- 🎯 **待验证**: 实际视频处理效果
