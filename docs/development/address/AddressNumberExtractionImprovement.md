# 地址门牌号提取优化改进

## 问题背景

在处理澳大利亚地址时，经常遇到复合门牌号格式，如：
- `23/567 Botanic Drive` - 单元号/主门牌号
- `23/560-567 Main Street` - 单元号/门牌号范围
- `M104/25 Collins Street` - 带字母前缀的复合格式

之前的系统在处理这些格式时存在问题：
- 提取 `23/567` 时会得到 `23` 而不是主要门牌号 `567`
- 导致与 Apple Maps 返回的地址产生巨大差异（如 23 vs 567，差异544）
- 被错误地判定为低置信度，影响地址验证成功率

## 解决方案

### 1. 优化门牌号提取逻辑

#### 修改的文件：
- `Services/UniversalAddressProcessor.swift`
- `Services/GeocodingService.swift`

#### 核心改进：
1. **智能识别复合门牌号**：使用正则表达式 `\\b[A-Za-z]*\\d+/([\\d-]+)\\b` 识别各种复合格式
2. **优先提取主要门牌号**：从 `/` 后面提取实际的门牌号
3. **支持范围地址**：对于 `560-567` 格式，提取起始号码 `560`
4. **兼容性保持**：对简单格式（如 `567 Main Street`）保持原有逻辑

### 2. 新增的函数

#### `extractMainStreetNumber(from:)` 
```swift
/// 提取主要街道号码 (从 "23/567" 提取 "567", 从 "M104/25" 提取 "25")
private func extractMainStreetNumber(from address: String) -> String? {
    let pattern = "\\b[A-Za-z]*\\d+/([\\d-]+)\\b"
    // ... 实现逻辑
}
```

#### 优化的 `extractNumbers(from:)`
```swift
/// 从地址中提取数字，智能处理复合门牌号格式
private func extractNumbers(from address: String) -> [Int] {
    // 首先尝试提取主要门牌号
    if let mainNumber = extractMainStreetNumber(from: address) {
        return [Int(mainNumber)].compactMap { $0 }
    }
    // 回退到原有逻辑
}
```

## 测试验证

### 测试用例覆盖：
- ✅ `23/567 Botanic Drive` → 提取 `567`
- ✅ `23/560-567 Main Street` → 提取 `560`
- ✅ `M104/25 Collins Street` → 提取 `25`
- ✅ `Unit 5/123 Smith Road` → 提取 `123`
- ✅ `Apt 2/456 High Street` → 提取 `456`
- ✅ `567 Simple Street` → 提取 `567`（兼容性）
- ✅ `123A Letter Street` → 提取 `123A`（字母后缀）

### 测试文件：
`Tests/AddressNumberExtractionTest.swift` - 包含完整的测试套件

## 预期效果

### 之前的问题：
```
📍 地址: 23/567 Botanic Drive, Glen Waverley VIC 3150
🚨 门牌号差异过大(544)，强制设为低置信度
🚨 原始(23) vs 返回(567)
🚫 原始地址置信度过低，跳过
```

### 改进后的效果：
```
📍 地址: 23/567 Botanic Drive, Glen Waverley VIC 3150
✅ 门牌号完全匹配: 567
✅ 最终置信度: 高
📍 获得坐标: (-37.893316, 145.166084)
```

## 影响范围

### 正面影响：
1. **提高地址验证成功率**：减少因门牌号误提取导致的验证失败
2. **改善用户体验**：减少"Apple Maps返回的地址与门牌号不匹配"的错误提示
3. **支持更多地址格式**：更好地处理澳大利亚常见的单元号/门牌号格式

### 兼容性：
- 保持对现有简单门牌号格式的完全兼容
- 不影响其他国家地址格式的处理
- 向后兼容，不会破坏现有功能

## 技术细节

### 正则表达式说明：
- `\\b[A-Za-z]*\\d+/([\\d-]+)\\b`
  - `\\b` - 词边界，确保匹配完整的门牌号
  - `[A-Za-z]*` - 可选的字母前缀（如 M, Unit, Apt）
  - `\\d+` - 一个或多个数字（单元号部分）
  - `/` - 斜杠分隔符
  - `([\\d-]+)` - 捕获组：主要门牌号（可能包含范围）
  - `\\b` - 词边界结束

### 范围地址处理：
对于 `560-567` 格式，提取起始号码 `560`，这通常是更准确的定位点。

## 新增功能：简化门牌号策略

### 问题场景：
对于 `145c Lilian Street` 这样的地址，Apple Maps 可能无法找到具体的门牌号，只返回街道信息。

### 解决方案：
新增了 `trySimplifiedHouseNumber` 策略，当原始地址失败时：

1. **简化带字母后缀的门牌号**：
   - `145c` → `145`
   - `23A` → `23`
   - `567B` → `567`

2. **尝试相近门牌号**：
   - 原门牌号 ±1, ±2, ±4 的变体
   - 例如：`145` → `143, 144, 146, 147, 141, 149`

### 实现细节：
```swift
/// 策略4: 尝试简化门牌号（处理 145c -> 145 的情况）
private func trySimplifiedHouseNumber(_ address: String) async -> GlobalGeocodingResult? {
    let candidates = generateSimplifiedHouseNumberCandidates(address)
    // 尝试每个候选地址...
}
```

### 测试验证：
- ✅ `145c Lilian Street` → 生成 `145 Lilian Street`
- ✅ `23A Collins Street` → 生成 `23 Collins Street`
- ✅ `567B High Street` → 生成 `567 High Street`

## 使用的模型

本次优化使用 **Claude Sonnet 4** 模型完成，包括：
- 问题分析和解决方案设计
- 代码实现和优化
- 测试用例编写和验证
- 文档编写

## 最新改进：智能门牌号缺失检测

### 问题场景：
用户反馈 `145c Lilian Street` 返回门牌号为空，但测试 `145a-z` 都应该返回 `145`。

### 根本原因：
系统按顺序尝试策略，如果原始地址返回了坐标（即使没有门牌号），就认为"成功"了，不会继续尝试简化策略。

### 解决方案：
新增 `checkForMissingHouseNumber` 检测逻辑：

1. **检测门牌号缺失**：
   - 原始地址有门牌号：`145c Lilian Street` ✓
   - 返回结果无门牌号：`Lilian St, Glen Waverley, VIC` ✗
   - 触发继续尝试策略

2. **智能策略切换**：
   ```swift
   // 原始地址成功但缺少门牌号
   if let incompleteResult = checkForMissingHouseNumber(result, originalAddress: originalAddress) {
       // 继续尝试简化门牌号策略
       if let betterResult = await trySimplifiedHouseNumber(originalAddress) {
           return betterResult  // 使用更完整的结果
       }
       return incompleteResult  // 回退到原始结果
   }
   ```

3. **预期效果**：
   - `145c Lilian Street` → 检测到门牌号缺失
   - 自动尝试 `145 Lilian Street` → 获得完整结果（包含门牌号 `145`）
   - 减少"Address validation issue"警告

### 测试验证：
- ✅ 复合门牌号提取：7/7 通过
- ✅ 地址验证改进：3/3 通过
- ✅ 简化门牌号生成：3/3 通过
- ✅ 门牌号缺失检测：4/4 通过（已优化检测逻辑）

### 检测逻辑优化：
修正了门牌号检测中的邮政编码干扰问题：

**问题**：
- `Lilian Street, Glen Waverley VIC 3150` 被误认为有门牌号（因为邮政编码 `3150`）

**解决方案**：
```swift
// 智能门牌号检测，排除邮政编码
private func extractHouseNumberFromAddress(_ address: String) -> String? {
    // 1. 移除邮政编码模式（VIC 3150, NSW 2000）
    let addressWithoutPostcode = removePostcodeFromAddress(address)

    // 2. 检测地址开头的门牌号模式
    let houseNumberPattern = "^\\s*(\\d+[a-zA-Z]?)\\s+"

    // 3. 检测复合门牌号模式（23/567）
    let compoundPattern = "^\\s*\\S+/([\\d-]+[a-zA-Z]?)\\s+"
}
```

**结果**：
- ✅ `145c Lilian Street` → 检测到门牌号 `145c`
- ✅ `Lilian Street, Glen Waverley VIC 3150` → 正确识别为无门牌号

## 后续建议

1. **监控效果**：观察批量地址导入的成功率变化
2. **收集反馈**：关注用户对地址验证准确性的反馈
3. **扩展支持**：如发现其他复合门牌号格式，可进一步扩展正则表达式
4. **性能优化**：如有需要，可考虑缓存正则表达式对象以提高性能
5. **用户提示优化**：当使用简化门牌号成功时，可以提示用户"找到了相近的地址"
6. **检测逻辑优化**：改进门牌号缺失检测的准确性
