# 地址库更新功能实现文档

## 概述

本文档描述了在用户点击地址搜索结果时，系统如何判断地址库、更新地址库并打印相关日志的实现。

## 功能特性

### 1. 地址库检查
- 当用户点击搜索结果时，系统首先检查地址库中是否已存在该地址
- 如果存在，直接使用缓存的坐标和信息
- 如果不存在，进行地理编码后保存到地址库

### 2. 地址库更新
- 新地址会自动保存到地址库
- 现有地址的使用次数会自动增加
- 支持不同的置信度级别和来源标记

### 3. 详细日志输出
- 完整的地址库操作日志
- 包含地址、坐标、置信度、来源等信息
- 便于调试和监控

## 实现细节

### 修改的文件

#### 1. EnhancedAddressAutocomplete.swift
- `selectSearchResult` 方法中添加地址库检查逻辑
- 在地址处理成功后保存到地址库
- 降级逻辑中也包含地址库更新

#### 2. AddressEditBottomSheet.swift
- `updateDeliveryPointWithCompleteAddressInfo` 方法中添加地址库保存
- `updateBasicAddressInfo` 方法中添加地址库保存
- 确保所有地址选择路径都会更新地址库

### 日志输出示例

```
🏠 EnhancedAddressAutocomplete - 检查地址库: 928 Gellert Boulevard, Daly City, 94015
🏠 EnhancedAddressAutocomplete - 地址库未命中，进行地理编码: 928 Gellert Boulevard, Daly City, 94015
🏠 EnhancedAddressAutocomplete - 保存新地址到地址库: 928 Gellert Boulevard, Daly City, 94015 -> (37.6879, -122.4702)
🏠 EnhancedAddressAutocomplete - ✅ 地址库更新完成: 928 Gellert Boulevard, Daly City, 94015
```

或者如果地址库命中：

```
🏠 EnhancedAddressAutocomplete - 检查地址库: 928 Gellert Boulevard, Daly City, 94015
🏠 EnhancedAddressAutocomplete - ✅ 地址库命中: 928 Gellert Boulevard, Daly City, 94015 -> (37.6879, -122.4702)
🏠 EnhancedAddressAutocomplete - 地址库信息: 使用次数=3, 置信度=0.95, 来源=manual
🏠 EnhancedAddressAutocomplete - 使用地址库数据完成地址选择: 928 Gellert Boulevard, Daly City, 94015
```

## 置信度级别

- **0.95**: 用户主动选择的地址（EnhancedAddressAutocomplete）
- **0.90**: 基本地址信息更新
- **0.85**: 降级逻辑处理的地址

## 来源类型

- **manual**: 用户手动选择或输入的地址
- **screenshot**: AI识别的地址
- **其他**: 根据具体场景设置

## 测试

创建了 `NaviBatchTests/AddressDatabaseUpdateTests.swift` 测试文件，包含：
- 地址库更新测试
- 使用次数更新测试
- 日志输出测试
- EnhancedAddressAutocomplete集成测试

## 使用方法

1. 用户在地址编辑界面输入地址
2. 系统显示搜索结果
3. 用户点击搜索结果
4. 系统自动检查地址库
5. 如果命中，使用缓存数据；如果未命中，进行地理编码并保存
6. 所有操作都会输出详细日志

## 性能优化

- 地址库命中时避免重复的地理编码请求
- 使用异步操作避免阻塞UI
- 智能的置信度管理
- 使用次数统计用于优化缓存策略

## 注意事项

- 确保 `UserAddressDatabase.shared.isEnabled` 为 true
- 日志输出使用 `Logger.info` 和 `logInfo` 函数
- 所有地址库操作都是异步的
- 支持多种地址处理策略的统一接口
