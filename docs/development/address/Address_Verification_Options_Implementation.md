# 地址验证策略优化实现

## 问题描述

用户反馈AI识别出了12个地址，但最终只显示了10个。通过日志分析发现，AI确实识别出了12个地址，但有2个地址在验证过程中失败被过滤掉了：

1. `10624 Pleasant Valley Circ, 95209` - 地理编码失败（kCLErrorDomain错误8）
2. `2763 Sand Castle Court, 95209` - 坐标与地址不匹配

## 解决方案

修改地址验证策略，默认包含所有AI识别的地址，即使验证失败也要保留。理由：
- 地址来源于截图，是真实存在的
- 地址对错由司机根据截图自行判断和纠正
- 不能遗漏任何地址，完整性比准确性更重要

### 实现细节

#### 1. 修改验证策略
```swift
// 地址验证选项 - 默认包含所有识别的地址，因为地址来源于截图，司机可自行纠正
private let includeUnverifiedAddresses = true // 始终包含验证失败的地址
```

#### 2. 修改验证失败处理逻辑
在三个地址验证失败的处理位置添加了条件判断：

1. **混合识别服务验证失败**
2. **Firebase AI验证失败**
3. **OCR验证失败**

现在默认包含所有识别的地址，即使验证失败也会将地址添加到结果中，使用默认坐标(0,0)并标记为低置信度(0.5)。

### 用户体验

1. **默认行为**：始终包含所有AI识别的地址，不再过滤验证失败的地址
2. **明确标识**：验证失败的地址会显示橙色的位置图标，表示坐标可能不准确
3. **低置信度**：验证失败的地址置信度设为50%，在UI中会显示相应的置信度标识
4. **司机纠正**：地址对错由司机根据原始截图自行判断和纠正

### 技术细节

- 验证失败的地址使用坐标(0,0)作为默认位置
- `hasValidCoordinate`标记为false，UI会显示相应的警告图标
- 置信度设为0.5，在UI中显示为50%
- 日志中会记录"地址验证失败但已包含（来自截图，司机可纠正）"

### 测试建议

1. 使用包含难以验证地址的图片进行测试
2. 确认所有AI识别的地址都被包含，无论验证是否成功
3. 确认验证失败的地址在UI中有适当的视觉提示
4. 验证司机可以根据截图手动纠正地址

## 序号重复问题修复

### 问题描述
用户反馈在处理多张图片时，出现了序号重复的问题（如两个地址都显示"GoFo: 6"）。

### 原因分析
AI在处理每张图片时，都会从自己的序号开始计算，导致：
- 第一张图片：GoFo: 1, 2, 3, 4, 5, 6
- 第二张图片：GoFo: 1, 2, 3, 4, 5, 6
- 合并后出现重复序号

### 解决方案
添加了`reassignSortNumbers()`函数，在所有图片处理完成后重新分配连续序号：

```swift
private func reassignSortNumbers() {
    var updatedAddresses: [(String, CLLocationCoordinate2D, Bool, Bool, Double)] = []
    var currentSortNumber = 1

    for (address, coordinate, isSelected, hasValidCoordinate, confidence) in recognizedAddresses {
        // 提取地址信息并重新分配连续序号
        let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
        var newAddress = separatedInfo.address

        // 添加新的连续序号
        if selectedAppType == .gofo {
            newAddress += "|SORT:\(currentSortNumber)"
        } else if !separatedInfo.thirdPartySortNumber.isEmpty {
            newAddress += "|SORT:\(currentSortNumber)"
        }

        // 保留其他信息（tracking、customer、time、app等）
        // ...

        currentSortNumber += 1
    }
}
```

### 调用位置
在三个处理完成的位置都添加了序号重新分配：
1. `processImages()` 完成后
2. `processImagesWithFirebaseAI()` 完成后
3. `processImagesWithOCRMode()` 完成后

## 坐标警告显示修复

### 问题描述
用户反馈确认导入后，有重复的sort_number，其中一个有坐标问题但没有提示司机。

### 解决方案
1. **添加坐标警告显示**：
   - 在MarkerView中添加`hasCoordinateWarning`参数
   - 有坐标问题的地址显示为橙色
   - 优先级：选中状态 > 坐标警告 > 分组状态

2. **全面的坐标检测**：
   ```swift
   let hasCoordinateWarning = (point.geocodingWarning != nil && !point.geocodingWarning!.isEmpty) ||
                            (LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown) != .valid ||
                            (point.latitude == 0 && point.longitude == 0) ||
                            point.latitude < -90 || point.latitude > 90 ||
                            point.longitude < -180 || point.longitude > 180
   ```

3. **更新所有MarkerView调用**：
   - RouteView.swift
   - DeliveryPointMapView.swift
   - DeliveryPointAnnotationView.swift
   - StartEndPinDemo.swift

## 总结

这个修改解决了三个关键问题：
1. **地址遗漏问题**：保留所有AI识别的地址，验证失败的地址标记为低置信度
2. **序号重复问题**：在多张图片处理完成后重新分配连续序号
3. **坐标警告显示**：有坐标问题的地址显示为橙色，司机可以立即识别

现在用户可以看到：
- 所有AI识别的地址（不遗漏）
- 连续的序号（GoFo: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12）
- 坐标有问题的地址显示为橙色警告
- 司机可以优先处理或验证有问题的地址

## 网络错误处理机制 (v1.0.7+)

### 问题分析
iPhone 12 mini 等设备在地理编码时经常遇到网络错误（kCLErrorDomain错误8），导致地址坐标显示为 (0.000000, 0.000000)。而 iPhone 16 Pro 等设备因有更完整的地址数据库，直接从本地获取坐标，无需网络请求。

### 解决方案

#### 1. 智能重试机制
```swift
// 网络错误使用更长的延迟时间
let baseDelay = status == .networkError ? 3.0 : 1.0
let delaySeconds = Double(newRequest.attemptCount) * baseDelay
```

#### 2. 用户友好的错误信息
```swift
if status == .networkError {
    finalErrorMessage = "网络连接不稳定，地址验证失败。建议：1) 检查网络连接 2) 稍后重试 3) 或手动更新坐标。当前显示为临时坐标 (0.000000, 0.000000)。"
}
```

#### 3. 网络错误恢复界面
- 新增 `NetworkErrorRecoveryView` 组件
- 自动检测网络错误导致的失败地址
- 提供重试、查看地址数据库状态、手动更新等解决方案
- 显示技术说明，帮助用户理解不同设备的差异

#### 4. 地址数据库同步建议
- 提示用户检查地址数据库状态
- 建议通过iCloud同步或手动导入地址数据库
- 显示数据库统计信息（总地址数、命中率、最常用地址等）

### 实现文件
- `NaviBatch/Services/GeocodingService.swift` - 核心重试逻辑
- `NaviBatch/Views/Components/NetworkErrorRecoveryView.swift` - 恢复界面
- `NaviBatch/Views/Components/ImageAddressRecognizer.swift` - 错误检测集成
