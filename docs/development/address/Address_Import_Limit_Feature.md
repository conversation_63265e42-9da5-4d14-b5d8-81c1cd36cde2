# 快递导入地址数量限制和订阅提示功能

## 🎯 **功能概述**

实现了快递导入时的智能地址数量限制检查，当非Pro用户选择超过20个地址时，在确认按钮前弹出选择对话框，提供订阅、导入前20个或取消的选择，避免用户被卡住的问题。

## ✅ **完成的功能实现**

### **1. 确认按钮前的权限检查** - 🔍 **核心功能**

#### **修改文件**: `NaviBatch/Views/Components/FileImportSheet.swift`

#### **主要修改**:

**优先权限检查逻辑**:
```swift
// 🎯 优先检查：非Pro用户且选择地址超过20个时的处理
let subscriptionManager = SubscriptionManager.shared
let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial

if isFreeUser && selectedAddresses.count > 20 {
    // 非Pro用户且选择地址超过20个，弹出选择对话框
    limitExceededInfo = (
        currentCount: 0, // 这里不关心当前路线的地址数
        remainingSlots: 20, // 免费用户限制为20个
        maxAllowed: 20,
        selectedCount: selectedAddresses.count,
        selectedAddresses: selectedAddresses
    )
    
    Task {
        await logInfo("FileImportSheet - 非Pro用户选择了\(selectedAddresses.count)个地址，超过20个限制，弹出选择对话框")
    }
    
    // 显示选择对话框
    showLimitExceededSheet = true
    return
}
```

### **2. 优化的选择对话框** - 💬 **用户体验**

#### **针对非Pro用户的专门信息显示**:

```swift
// 🎯 针对非Pro用户超过20个地址的情况，显示更清晰的信息
if currentCount == 0 && maxAllowed == 20 {
    // 这是我们新的逻辑：非Pro用户选择超过20个地址
    Text("免费版最多允许20个地址，当前已选择\(selectedCount)个地址。")
        .font(.body)
    
    Text("您选择了\(selectedCount)个地址，超出了\(selectedCount - 20)个。")
        .font(.body)
        .foregroundColor(.red)
    
    Text("升级到高级版可享受无限地址！")
        .font(.body)
        .bold()
        .padding(.top, 5)
}
```

#### **专门的按钮逻辑**:

```swift
// 🎯 针对非Pro用户超过20个地址的情况，优化按钮显示
if isFreeUser && currentCount == 0 && maxAllowed == 20 && selectedCount > 20 {
    // 非Pro用户选择超过20个地址的情况
    Button("导入前20个") {
        // 创建限制后的地址列表（前20个）
        let limitedAddresses = Array(selectedAddresses.prefix(20))
        onImportLimited(limitedAddresses)
        
        // 🚨 检查并提示问题地址
        Task {
            // 延迟一下让地址处理完成
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            await MainActor.run {
                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入（前20个）")
            }
        }
    }
    .foregroundColor(.blue)
}
```

## 🔄 **工作流程**

### **非Pro用户导入超过20个地址的流程**

1. **📱 用户导入快递图片** → AI识别出多个地址
2. **✅ 用户选择地址** → 选择了超过20个地址
3. **🔍 点击确认按钮** → 触发权限检查
4. **⚠️ 检测到超限** → 非Pro用户且选择超过20个地址
5. **💬 弹出选择对话框** → 显示三个选项：
   - **🎯 导入前20个** → 导入前20个地址，继续使用
   - **⬆️ 升级到高级版** → 打开订阅界面
   - **❌ 取消** → 返回地址选择界面

### **Pro用户或地址数量在限制内的流程**

1. **📱 用户导入快递图片** → AI识别出地址
2. **✅ 用户选择地址** → 选择地址数量在限制内
3. **🔍 点击确认按钮** → 通过权限检查
4. **✅ 直接导入** → 正常导入所有选择的地址

## 📊 **功能效果**

### **解决的问题**

| 问题 | 修改前 | 修改后 | 用户体验 |
|------|--------|--------|---------|
| **用户被卡住** | 导入后才发现超限，无法继续 | 确认前就检查，提供选择 | 🎯 避免卡住 |
| **操作不明确** | 不知道如何处理超限情况 | 清晰的三个选择选项 | 😊 操作明确 |
| **升级引导** | 没有明确的升级入口 | 直接提供升级按钮 | 💰 促进转化 |
| **部分导入** | 无法导入部分地址 | 可以选择导入前20个 | ⚡ 继续使用 |

### **用户选择场景**

#### **场景1：想要继续使用免费版**
- **选择**: "导入前20个"
- **结果**: 导入前20个地址，可以继续使用
- **体验**: 🎯 不被阻断，可以继续工作

#### **场景2：愿意升级到Pro版**
- **选择**: "升级到高级版"
- **结果**: 打开订阅界面，完成升级后可导入所有地址
- **体验**: 💰 顺畅的升级流程

#### **场景3：重新选择地址**
- **选择**: "取消"
- **结果**: 返回地址选择界面，可以重新选择20个以内的地址
- **体验**: 🔄 灵活的操作选择

## 🎯 **技术实现细节**

### **权限检查逻辑**

1. **优先检查** → 在所有其他检查之前进行
2. **精确判断** → 非Pro用户 + 非试用期 + 选择超过20个
3. **专门处理** → 使用特殊的参数配置
4. **日志记录** → 完整记录检查过程

### **对话框复用**

1. **复用现有组件** → 使用`FileImportAddressLimitExceededSheet`
2. **智能显示** → 根据参数显示不同的信息和按钮
3. **统一体验** → 保持与其他限制检查的一致性

### **按钮逻辑优化**

1. **条件判断** → 精确识别非Pro用户超过20个地址的情况
2. **固定文本** → 显示"导入前20个"而不是动态数字
3. **正确处理** → 确保导入前20个地址并触发问题地址检查

## 🚀 **预期效果**

### **用户体验提升**

1. **🎯 避免卡住** → 用户不会在确认按钮后被卡住
2. **😊 选择明确** → 三个清晰的选择选项
3. **⚡ 继续使用** → 可以选择导入部分地址继续工作
4. **💰 升级引导** → 自然的升级转化机会

### **业务价值**

1. **📈 用户留存** → 避免用户因为被卡住而流失
2. **💰 转化提升** → 在关键时刻提供升级选择
3. **🎯 体验优化** → 更流畅的导入流程
4. **📊 数据收集** → 可以统计用户的选择偏好

## 📋 **使用示例**

### **示例1：GoFo快递导入29个地址**

**用户操作**:
1. 导入GoFo快递图片，AI识别出29个地址
2. 全选所有地址
3. 点击"导入"按钮

**系统响应**:
```
弹出对话框：
标题：错误
内容：免费版最多允许20个地址，当前已选择29个地址。
      您选择了29个地址，超出了9个。
      升级到高级版可享受无限地址！

按钮：
- 导入前20个 (蓝色)
- 升级到高级版 (绿色，底部)
- 取消 (左上角)
```

**用户选择**:
- **选择"导入前20个"** → 导入前20个地址，继续使用
- **选择"升级到高级版"** → 打开订阅界面
- **选择"取消"** → 返回地址选择界面

### **示例2：Pro用户导入50个地址**

**用户操作**:
1. Pro用户导入快递图片，AI识别出50个地址
2. 全选所有地址
3. 点击"导入"按钮

**系统响应**:
- 直接导入所有50个地址
- 不弹出任何限制对话框
- 正常完成导入流程

## 📊 **总结**

### **实现的功能**

1. ✅ **确认前检查** → 在确认按钮前就检查用户权限
2. ✅ **智能对话框** → 针对不同情况显示合适的信息
3. ✅ **三个选择** → 订阅、导入前20个、取消
4. ✅ **避免卡住** → 用户永远不会被卡在确认按钮后
5. ✅ **升级引导** → 自然的升级转化机会

### **用户价值**

- **🎯 流畅体验** → 避免被卡住，总是有选择
- **😊 操作明确** → 清晰的选择选项和说明
- **⚡ 继续使用** → 可以选择部分导入继续工作
- **💰 升级便利** → 在需要时提供升级选择

---

**功能完成时间**: 2024-12-06  
**影响范围**: 快递导入确认流程和用户权限检查  
**用户体验**: 显著改善非Pro用户的导入体验，避免被卡住的问题
