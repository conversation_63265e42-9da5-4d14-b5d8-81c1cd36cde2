# SimpleAddressSheet 智能按钮优化

## 概述
根据用户建议，优化了SimpleAddressSheet的右侧按钮逻辑，将原来的Save和Close两个按钮简化为一个智能按钮，提升用户体验。

## 优化内容

### 1. 智能按钮逻辑
- **地址为空时**：显示"关闭"按钮，点击直接关闭表单
- **地址不为空时**：显示"保存"按钮，点击保存地址后自动关闭表单

### 2. 自动保存并关闭
- 用户点击搜索结果时，自动保存并关闭表单
- 用户选择地址簿中的地址时，自动保存并关闭表单
- 用户使用当前位置时，自动保存并关闭表单

### 3. 代码变更

#### 新增计算属性
```swift
// 智能按钮标题计算属性
private var smartButtonTitle: String {
    if address.isEmpty {
        return "close".localized
    } else {
        return "save".localized
    }
}
```

#### 简化toolbar逻辑
- 移除了原来的HStack包含Save和Close两个按钮
- 使用单个智能按钮，根据地址状态动态显示文字和行为
- 保持编辑模式的特殊处理逻辑不变

## 用户体验改进

### 优化前
- 用户需要在Save和Close两个按钮之间选择
- 操作步骤较多，容易混淆

### 优化后
- 只有一个按钮，操作更简洁
- 按钮文字智能变化，用户意图更明确
- 点击搜索结果自动完成保存和关闭，减少操作步骤

## 本地化支持
- 使用现有的"close"和"save"本地化键
- 支持中文、英文、泰文等多语言

## 兼容性
- 保持与现有代码的完全兼容
- 编辑模式的特殊逻辑保持不变
- 所有现有的自动保存逻辑继续工作

## 测试建议
1. 测试空地址状态下的关闭功能
2. 测试有地址状态下的保存功能
3. 测试搜索结果点击的自动保存关闭
4. 测试地址簿选择的自动保存关闭
5. 测试当前位置的自动保存关闭
6. 测试编辑模式的特殊逻辑

## 更新日期
2025-07-08

## 相关文件
- `NaviBatch/Views/Components/SimpleAddressSheet.swift`
- `NaviBatch/Localizations/*/Localizable.strings`
