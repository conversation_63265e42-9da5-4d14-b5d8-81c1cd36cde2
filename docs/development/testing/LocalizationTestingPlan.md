# NaviBatch 本地化测试计划

## 概述

本文档提供了 NaviBatch 应用本地化测试的详细计划，确保应用在所有支持的语言环境中都能正常工作并提供一致的用户体验。

## 测试目标

1. 验证所有用户界面文本都已正确本地化
2. 确保布局在不同语言下都能正确显示
3. 验证特殊字符和非拉丁字符的正确显示
4. 测试从右到左语言的布局
5. 确保格式化字符串（日期、时间、数字等）正确显示

## 支持的语言

测试应覆盖所有支持的语言：

- 英语 (en)
- 简体中文 (zh-Hans)
- 繁体中文 (zh-Hant)
- 日语 (ja)
- 阿拉伯语 (ar) - 从右到左
- 希伯来语 (he) - 从右到左
- 其他支持的语言

## 测试环境

- 设备：iPhone（不同屏幕尺寸）
- iOS 版本：最新版本和前一个主要版本
- 语言设置：系统语言和应用内语言设置

## 测试流程

### 1. 准备工作

1. 运行本地化验证脚本，确保所有语言文件包含所有必要的键
   ```bash
   ./NaviBatch/Scripts/LocalizationValidator.swift
   ```

2. 运行硬编码文本检查脚本，确保没有遗漏的硬编码文本
   ```bash
   ./NaviBatch/Scripts/LocalizationChecker.swift
   ```

3. 准备测试设备，确保可以轻松切换语言

### 2. 系统语言测试

对于每种支持的语言：

1. 将设备系统语言更改为测试语言
2. 启动应用，确保应用使用正确的语言
3. 验证应用是否遵循系统语言设置

### 3. 应用内语言测试

1. 将系统语言设置为英语
2. 启动应用，导航到语言设置
3. 更改应用语言设置
4. 验证应用界面是否立即更新为所选语言
5. 重启应用，确保语言设置保持不变

### 4. 功能测试

对于每种语言，测试以下关键功能：

#### 菜单和导航

- [ ] 主菜单项显示正确
- [ ] 导航标题显示正确
- [ ] 设置菜单显示正确
- [ ] 帮助和关于页面显示正确

#### 地址管理

- [ ] 添加地址界面文本正确
- [ ] 地址验证错误消息正确
- [ ] 地址详情页面显示正确
- [ ] 地址编辑功能正常工作

#### 路线管理

- [ ] 创建路线界面文本正确
- [ ] 路线详情页面显示正确
- [ ] 路线优化选项显示正确
- [ ] 路线导航指令正确

#### 配送管理

- [ ] 配送点管理界面文本正确
- [ ] 包裹信息显示正确
- [ ] 车辆位置选择正确
- [ ] 照片记录功能正常工作

#### 订阅

- [ ] 订阅页面文本正确
- [ ] 价格和计划信息正确
- [ ] 订阅条款和隐私政策链接正常工作

### 5. 布局测试

对于每种语言：

- [ ] 检查文本是否溢出或被截断
- [ ] 验证按钮和控件大小是否适当
- [ ] 确保弹出窗口和对话框正确显示
- [ ] 测试不同屏幕尺寸下的布局

### 6. 从右到左语言测试

对于阿拉伯语和希伯来语：

- [ ] 验证整体布局是否从右到左
- [ ] 确保导航方向正确
- [ ] 检查文本对齐是否正确
- [ ] 验证图标和控件位置是否适当调整

### 7. 格式化测试

对于每种语言：

- [ ] 验证日期格式是否符合本地习惯
- [ ] 检查时间格式是否正确
- [ ] 确保数字格式（包括千位分隔符和小数点）正确
- [ ] 测试货币格式是否正确

### 8. 特殊情况测试

- [ ] 测试非常长的文本字符串
- [ ] 测试包含特殊字符的文本
- [ ] 验证在语言切换过程中的应用行为
- [ ] 测试在低内存条件下的本地化性能

## 报告问题

发现的本地化问题应包含以下信息：

1. 问题描述
2. 测试语言
3. 问题位置（屏幕、视图或功能）
4. 重现步骤
5. 预期行为
6. 实际行为
7. 截图（如适用）

## 修复流程

1. 对于缺失的本地化键，更新相应的 Localizable.strings 文件
2. 对于布局问题，调整相应的视图约束
3. 对于格式化问题，确保使用正确的本地化格式化方法
4. 修复后，重新运行本地化验证脚本
5. 在所有支持的语言中重新测试修复的问题

## 定期维护

1. 每次添加新功能时运行本地化检查脚本
2. 定期审查本地化文件，确保所有文本都已本地化
3. 在主要版本发布前进行完整的本地化测试
4. 收集用户反馈，持续改进本地化质量
