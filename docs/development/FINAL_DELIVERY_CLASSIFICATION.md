# 最终配送应用分类结构

## 📋 最终分类方案

根据用户的实际需求和业务情况，最终确定的分类结构如下：

### 🇺🇸 美国快递组
**显示顺序**：Just Photo + Amazon Flex + iMile + GoFo + LDS EPOD + PIGGY + UNIUNI + YWE + SpeedX

1. **Just Photo** - 通用图片识别
2. **Amazon Flex** - 亚马逊配送
3. **iMile** - 国际快递（美国+澳洲）
4. **GoFo** - GoFo配送（排第一位）
5. **LDS EPOD** - LDS EPOD配送
6. **PIGGY** - PIGGY配送
7. **UNIUNI** - UNIUNI配送
8. **YWE** - YWE配送
9. **SpeedX** - SpeedX配送

### 🇦🇺 澳洲快递组
**显示顺序**：Just Photo + iMile

1. **Just Photo** - 通用图片识别
2. **iMile** - 国际快递（目前澳洲唯一选项）

## 🎯 关键设计决策

### 1. iMile的特殊处理
- **地区属性**：设为 `universal`（通用）
- **原因**：iMile同时服务美国和澳洲两个地区
- **AI提示词**：使用特殊的多地区识别逻辑
- **显示位置**：同时出现在美国和澳洲两个组中

### 2. GoFo的优先级
- **位置**：在美国快递组中排在第一位（Amazon Flex和iMile之后）
- **原因**：用户特别要求GoFo排第一

### 3. 澳洲快递的简化
- **当前状态**：只有iMile在实际使用
- **未来扩展**：其他快递公司代码保留，便于后续添加

## 🤖 AI提示词策略

### 美国快递提示词特征
```
🇺🇸 美国地址格式 + 具体快递公司特征
- 地址：Number Street, City, State, Zipcode
- 邮编：5位数字或5+4位格式
- 州名：CA, NY, TX, FL等缩写
```

### 澳洲快递提示词特征
```
🇦🇺 澳洲地址格式 + iMile特征
- 地址：Unit/Number Street, Suburb, State, Postcode
- 邮编：4位数字
- 州名：Victoria, NSW, QLD等
```

### iMile特殊处理
```
🌍 多地区自动识别
- 根据邮编格式判断地区（5位=美国，4位=澳洲）
- 自动应用对应地区的地址格式规则
- 支持两种地址格式的混合识别
```

## 📱 用户界面设计

### 分组显示
- **美国快递**：橙色主题 🟠
- **澳洲快递**：蓝色主题 🔵
- **每组都包含Just Photo**：方便用户选择通用识别

### 选择逻辑
1. 用户选择地区组（美国/澳洲）
2. 在对应组内选择具体快递公司
3. AI根据选择应用对应的提示词策略

## 🔧 技术实现要点

### 1. 枚举定义
```swift
// 美国快递
case amazonFlex = "amazon_flex"
case imile = "imile"           // iMile (美国+澳洲)
case gofo = "gofo"             // GoFo配送 (排第一)
case ldsEpod = "lds_epod"      // LDS EPOD配送
// ... 其他美国快递

// 澳洲快递 (目前只有iMile)
```

### 2. 地区分类逻辑
```swift
var region: DeliveryRegion {
    switch self {
    case .amazonFlex, .gofo, .ldsEpod, .piggy, .uniuni, .ywe, .speedx:
        return .usa
    case .imile:
        return .universal  // 特殊处理
    case .justPhoto, .manual:
        return .universal
    }
}
```

### 3. UI显示列表
```swift
// 美国快递组
apps: [.justPhoto, .amazonFlex, .imile, .gofo, .ldsEpod, .piggy, .uniuni, .ywe, .speedx]

// 澳洲快递组  
apps: [.justPhoto, .imile]
```

## 📈 优化效果

### 用户体验
- ✅ 清晰的地区分组
- ✅ GoFo优先显示
- ✅ iMile在两个地区都可选择
- ✅ 澳洲组简洁明了

### AI识别精度
- ✅ 地区特定的地址格式规则
- ✅ iMile的多地区自动识别
- ✅ 每个快递公司的特征优化

### 代码维护性
- ✅ 保留所有快递类型定义
- ✅ 灵活的地区分类系统
- ✅ 易于添加新快递公司

## 🎉 总结

这个分类方案完美平衡了：
1. **用户需求**：GoFo优先，iMile双地区服务
2. **业务现状**：澳洲目前只用iMile
3. **技术架构**：灵活可扩展的设计
4. **用户体验**：简洁直观的界面

---
*最终确认时间：2024年12月23日*
*版本：NaviBatch v1.0.8*
