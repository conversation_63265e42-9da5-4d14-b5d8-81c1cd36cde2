# NaviBatch 地址导入格式指南

## 📋 支持的文件格式

NaviBatch 支持以下三种文件格式导入地址：

- **CSV** (.csv) - 逗号分隔值文件
- **JSON** (.json) - JavaScript对象表示法文件
- **TXT** (.txt) - 纯文本文件

## 🔍 支持的字段

### 必需字段
- `address` - 完整地址（字符串）

### 可选字段
- `latitude` - 纬度（数字，-90到90）
- `longitude` - 经度（数字，-180到180）
- `unitNumber` - 单元/房间号（如：Unit 5, Suite 1205, Apt 12）
- `packageCount` - 包裹数量（数字，默认为1）
- `packageSize` - 包裹大小（Small, Medium, Large）
- `packageType` - 包裹类型（Box, Bag, Letter）
- `deliveryType` - 配送类型（Delivery, Pickup）
- `notes` - 备注信息
- `trackingNumber` - 追踪号码
- `accessInstructions` - 访问说明

## 📄 CSV格式

### 完整格式示例
```csv
Address,Latitude,Longitude,UnitNumber,PackageCount,PackageSize,PackageType,DeliveryType,Notes,TrackingNumber,AccessInstructions
"200 George Street, Sydney NSW 2000, Australia",-33.8688,151.2093,Suite 1205,2,Medium,Box,Delivery,Office building reception,TN123456789,Use main entrance
"42 Wallaby Way, Sydney NSW 2000, Australia",-33.8688,151.2093,,1,Small,Bag,Delivery,Please leave at front door,TN123456790,Ring doorbell twice
```

### 简化格式示例
```csv
Address
"200 George Street, Sydney NSW 2000, Australia"
"42 Wallaby Way, Sydney NSW 2000, Australia"
"380 Lonsdale Street, Melbourne VIC 3000, Australia"
```

### 带坐标格式示例
```csv
Address,Latitude,Longitude
"200 George Street, Sydney NSW 2000, Australia",-33.8688,151.2093
"42 Wallaby Way, Sydney NSW 2000, Australia",-33.8688,151.2093
```

## 📄 JSON格式

### 对象数组格式
```json
[
  {
    "address": "200 George Street, Sydney NSW 2000, Australia",
    "latitude": -33.8688,
    "longitude": 151.2093,
    "unitNumber": "Suite 1205",
    "packageCount": 2,
    "packageSize": "Medium",
    "packageType": "Box",
    "deliveryType": "Delivery",
    "notes": "Office building reception",
    "trackingNumber": "TN123456789",
    "accessInstructions": "Use main entrance"
  }
]
```

### 包装格式
```json
{
  "addresses": [
    {
      "address": "200 George Street, Sydney NSW 2000, Australia",
      "packageCount": 2,
      "notes": "Office building reception"
    }
  ]
}
```

### 字符串数组格式
```json
[
  "200 George Street, Sydney NSW 2000, Australia",
  "42 Wallaby Way, Sydney NSW 2000, Australia",
  "380 Lonsdale Street, Melbourne VIC 3000, Australia"
]
```

## 📄 TXT格式

### 每行一个地址（推荐）
```
200 George Street, Sydney NSW 2000, Australia
42 Wallaby Way, Sydney NSW 2000, Australia
380 Lonsdale Street, Melbourne VIC 3000, Australia
```

### 带坐标格式
```
200 George Street, Sydney NSW 2000, Australia,-33.8688,151.2093
42 Wallaby Way, Sydney NSW 2000, Australia,-33.8688,151.2093
```

## 📝 重要提示

### 地址格式建议
- 使用完整地址，包含街道号、街道名、郊区、州/省、邮编、国家
- 澳大利亚地址示例：`123 Collins Street, Melbourne VIC 3000, Australia`
- 美国地址示例：`123 Main Street, New York NY 10001, USA`
- 英国地址示例：`123 Oxford Street, London W1D 2HX, UK`

### 坐标说明
- 如果提供坐标，系统将优先使用提供的坐标
- 如果不提供坐标，系统将自动进行地理编码
- 坐标格式：纬度在前，经度在后
- 澳大利亚坐标范围：纬度 -44 到 -10，经度 113 到 154

### 包裹信息
- **包裹大小**：Small（小）, Medium（中）, Large（大）
- **包裹类型**：Box（盒子）, Bag（袋子）, Letter（信件）
- **配送类型**：Delivery（配送）, Pickup（取件）

### 文件编码
- 请使用 UTF-8 编码保存文件
- 支持中文、英文等多语言地址

## ⚠️ 常见问题

### 地址验证失败
- 确保地址格式正确且完整
- 检查邮编是否正确
- 确认地址真实存在

### 坐标无效
- 纬度范围：-90 到 90
- 经度范围：-180 到 180
- 确保坐标与地址匹配

### 文件解析错误
- 检查文件编码是否为 UTF-8
- 确认 CSV 文件逗号分隔正确
- 验证 JSON 文件语法正确

## 📞 技术支持

如果在导入过程中遇到问题，请检查：
1. 文件格式是否正确
2. 地址是否完整
3. 特殊字符是否正确转义
4. 文件大小是否在合理范围内（建议小于10MB）
