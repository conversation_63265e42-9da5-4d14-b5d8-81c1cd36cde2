# NaviBatch 本地化指南

## 概述

NaviBatch 应用支持多种语言，使用 Swift 的本地化机制和自定义的 LocalizationManager 来管理语言切换。本文档提供了如何在 NaviBatch 项目中正确使用本地化功能的指南。

## 支持的语言

NaviBatch 目前支持以下语言：

- 英语 (en)
- 简体中文 (zh-Hans)
- 繁体中文 (zh-Hant)
- 阿拉伯语 (ar)
- 荷兰语 (nl)
- 法语 (fr)
- 德语 (de)
- 希腊语 (el)
- 希伯来语 (he)
- 匈牙利语 (hu)
- 印尼语 (id)
- 意大利语 (it)
- 日语 (ja)
- 韩语 (ko)
- 马来语 (ms)
- 波兰语 (pl)
- 葡萄牙语 (pt)
- 罗马尼亚语 (ro)
- 俄语 (ru)
- 西班牙语 (es)
- 泰语 (th)
- 土耳其语 (tr)

## 本地化文件结构

本地化文件存储在以下目录：

```
NaviBatch/Localizations/{language_code}.lproj/Localizable.strings
```

例如：
- NaviBatch/Localizations/en.lproj/Localizable.strings (英语)
- NaviBatch/Localizations/zh-Hans.lproj/Localizable.strings (简体中文)

## 如何使用本地化

### 1. 在代码中使用本地化字符串

使用 `.localized` 扩展方法来获取本地化字符串：

```swift
// 简单文本
Text("hello".localized)

// 带参数的文本
Text("welcome_user".localized(with: username))

// 格式化字符串
Text(String(format: "points_count".localized, points))
```

### 2. 添加新的本地化键

当添加新的用户界面文本时，请遵循以下步骤：

1. 在 Localizable.strings 文件中添加新的键值对
2. 使用有意义的键名，遵循小写下划线命名法（例如：`user_profile`）
3. 按功能区域组织键（使用 `// MARK: - 区域名称` 注释）
4. 为所有支持的语言添加翻译

示例：

```
// MARK: - User Profile
"user_profile" = "User Profile";
"edit_profile" = "Edit Profile";
"change_password" = "Change Password";
```

### 3. 本地化格式化字符串

对于包含变量的字符串，使用 `%@`（对象）、`%d`（整数）、`%.1f`（浮点数）等占位符：

```
"greeting" = "Hello, %@!";
"points_earned" = "You earned %d points";
"distance_km" = "%.1f kilometers";
```

在代码中使用：

```swift
String(format: "greeting".localized, userName)
String(format: "points_earned".localized, points)
String(format: "distance_km".localized, distance)
```

### 4. 本地化分类

为了保持一致性和可维护性，请按以下类别组织本地化键：

- 通用 UI 元素（按钮、标签等）
- 导航和菜单
- 地址相关
- 路线相关
- 配送相关
- 分组相关
- 订阅相关
- 导入/导出
- 地图相关
- 错误和警告

## 本地化最佳实践

1. **永远不要使用硬编码文本**：所有用户可见的文本都应该使用本地化键。

2. **使用描述性键名**：键名应该清晰地描述文本的用途，而不是文本本身。

3. **保持一致性**：使用一致的命名约定和组织结构。

4. **添加注释**：对于复杂或可能引起混淆的字符串，添加注释说明用途。

5. **考虑文本长度**：不同语言的文本长度可能差异很大，确保 UI 能够适应。

6. **测试所有语言**：在添加新功能后，测试所有支持的语言。

7. **避免拼接字符串**：不要拼接本地化字符串，使用格式化字符串代替。

   错误示例：
   ```swift
   Text("hello".localized + " " + userName + "!")
   ```

   正确示例：
   ```swift
   Text("hello_user".localized(with: userName))
   ```

8. **处理复数形式**：不同语言有不同的复数规则，使用 `Localizable.stringsdict` 文件处理复数形式。

## 添加新语言

要添加新语言支持，请按照以下步骤操作：

1. 在 `LocalizationManager.swift` 的 `Language` 枚举中添加新语言。
2. 创建新的语言目录：`NaviBatch/Localizations/{language_code}.lproj/`
3. 复制 `Localizable.strings` 文件并翻译所有字符串。
4. 如果语言是从右到左的（如阿拉伯语），在 `Language` 枚举的 `isRightToLeft` 属性中添加该语言。

## 测试本地化

在提交更改之前，请确保：

1. 在所有支持的语言中测试新功能。
2. 检查文本是否正确显示，没有截断或溢出。
3. 验证格式化字符串是否正确显示变量。
4. 对于从右到左的语言，确保布局正确。

## 常见问题解决

### 文本未显示本地化版本

- 检查键名是否正确
- 确保已添加 `.localized` 扩展
- 验证本地化文件中是否存在该键

### 格式化字符串显示错误

- 确保使用正确的格式说明符（`%@`, `%d`, `%.1f` 等）
- 检查参数数量和类型是否匹配

### 布局问题

- 使用自动布局和灵活的布局约束
- 避免硬编码宽度
- 对于长文本，使用 `lineLimit` 或 `truncationMode`

## 联系人

如有关于本地化的问题，请联系项目负责人。
