# Apple Maps API 限制处理指南

## 🚦 问题概述

Apple Maps API 对地理编码请求有严格的速率限制：
- **限制**: 50 requests / 60 seconds
- **错误代码**: GEOErrorDomain Code=-3
- **影响**: 批量地址处理时可能触发限制

## ✅ 解决方案

### 1. 全局速率限制管理器

我们实现了 `GlobalGeocodingRateLimiter` 来统一管理所有API请求：

```swift
// 保守设置：35 requests / 60 seconds
private let maxRequests = 35
private let timeWindow: TimeInterval = 60
```

### 2. 智能批量处理

#### 批次大小优化
- **文件导入**: 5个地址/批次
- **批量粘贴**: 3个地址/批次  
- **网页下载**: 动态调整 (5-8个/批次)

#### 智能延迟策略
```swift
func getSmartBatchDelay() -> TimeInterval {
    if utilizationPercentage > 80% { return 2.0 }
    if utilizationPercentage > 50% { return 1.0 }
    return 0.5
}
```

### 3. 多层错误处理

#### 预防性检查
```swift
// 处理前检查
if await rateLimiter.shouldPauseBatchProcessing() {
    // 暂停30秒
    try? await Task.sleep(nanoseconds: 30_000_000_000)
}
```

#### 错误恢复
```swift
if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
    // 等待60秒后重试
    try? await Task.sleep(nanoseconds: 60_000_000_000)
}
```

## 📊 监控和反馈

### 实时状态监控
```swift
struct RateLimiterStatus {
    let currentRequests: Int      // 当前请求数
    let maxRequests: Int         // 最大允许请求数
    let utilizationPercentage: Double  // 使用率百分比
    let isNearLimit: Bool        // 是否接近限制
}
```

### 用户界面提示
- **警告显示**: 当使用率 > 80% 时显示警告
- **进度指示**: 显示处理进度和等待时间
- **友好提示**: 解释延迟原因和建议

## 🛠️ 技术实现

### 核心组件

1. **GlobalGeocodingRateLimiter**: 全局速率限制管理
2. **UniversalAddressProcessor**: 地址处理器（已集成限制）
3. **UnifiedAddressValidationService**: 验证服务（已集成限制）
4. **RateLimitWarningView**: 用户警告界面

### 集成点

- ✅ UniversalAddressProcessor
- ✅ UnifiedAddressValidationService  
- ✅ FileImportSheet
- ✅ BatchAddressInputSheet
- ✅ WebDownloadSheet

## 📱 用户体验优化

### 最佳实践建议

1. **分批处理**: 建议每批不超过10个地址
2. **错峰使用**: 避免在高峰时段大量处理
3. **耐心等待**: 系统会自动管理延迟，确保稳定性

### 错误信息优化

```
🚦 Apple Maps API速率限制
为了保护Apple Maps服务，我们需要暂时减慢处理速度。

• 这是正常的保护机制，不会影响数据质量
• 建议分批处理大量地址，每批不超过10个  
• 处理将在 60 秒后自动恢复
```

## 🧪 测试验证

### 单元测试覆盖
- ✅ 速率限制器基本功能
- ✅ 限制触发检测
- ✅ 智能延迟计算
- ✅ 批量处理暂停逻辑
- ✅ 统计信息管理

### 集成测试
- ✅ 批量地址验证流程
- ✅ 错误恢复机制
- ✅ 用户界面响应

## 📈 性能指标

### 改进效果
- **错误减少**: 预期减少80%的API限制错误
- **用户体验**: 提供清晰的进度和状态反馈
- **系统稳定性**: 防止API配额耗尽

### 监控指标
- 总请求数 (totalRequests)
- 限制次数 (throttledRequests)  
- 使用率 (utilizationPercentage)
- 平均延迟时间

## 🔧 故障排除

### 常见问题

**Q: 为什么处理速度变慢了？**
A: 这是为了防止触发Apple Maps API限制，确保所有地址都能成功处理。

**Q: 可以跳过延迟吗？**
A: 不建议，这可能导致API限制错误，影响后续处理。

**Q: 如何优化处理速度？**
A: 建议分批处理，每批5-10个地址，避免一次性处理大量地址。

### 调试信息

启用详细日志查看速率限制状态：
```swift
Logger.info("🚦 API使用率: \(status.utilizationPercentage)%", type: .location)
Logger.info("🚦 当前请求: \(status.currentRequests)/\(status.maxRequests)", type: .location)
```

## 📝 更新日志

### v1.0.0 (2025-06-05)
- ✅ 实现全局速率限制管理器
- ✅ 集成所有地理编码组件
- ✅ 添加用户界面警告
- ✅ 完善错误处理机制
- ✅ 添加单元测试覆盖
