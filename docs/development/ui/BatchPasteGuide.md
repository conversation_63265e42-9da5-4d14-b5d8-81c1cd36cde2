# NaviBatch 批量贴入格式指南

## 📋 批量贴入功能

NaviBatch 支持直接复制粘贴地址列表，无需创建文件。支持多种格式的地址数据。

## 🔍 支持的贴入格式

### 1. 简单地址列表（推荐）
每行一个完整地址，系统自动地理编码：

```
200 George Street, Sydney NSW 2000, Australia
42 Wallaby Way, Sydney NSW 2000, Australia
380 Lonsdale Street, Melbourne VIC 3000, Australia
75 Bondi Road, Bondi NSW 2026, Australia
12 Adelaide Street, Brisbane QLD 4000, Australia
```

### 2. 带坐标的地址列表
地址后跟坐标（逗号分隔）：

```
200 George Street, Sydney NSW 2000, Australia,-33.8688,151.2093
42 Wallaby Way, Sydney NSW 2000, Australia,-33.8688,151.2093
380 Lonsdale Street, Melbourne VIC 3000, Australia,-37.8136,144.9631
```

### 3. CSV格式数据
包含标题行的CSV数据：

```
Address,PackageCount,PackageSize,Notes
"200 George Street, Sydney NSW 2000, Australia",2,Medium,Office building
"42 Wallaby Way, Sydney NSW 2000, Australia",1,Small,Residential
"380 Lonsdale Street, Melbourne VIC 3000, Australia",3,Large,Business
```

### 4. 制表符分隔格式
从Excel或Google Sheets复制的数据：

```
Address	PackageCount	PackageSize	Notes
200 George Street, Sydney NSW 2000, Australia	2	Medium	Office building
42 Wallaby Way, Sydney NSW 2000, Australia	1	Small	Residential
380 Lonsdale Street, Melbourne VIC 3000, Australia	3	Large	Business
```

### 5. 带详细信息格式
使用特殊分隔符（|）的详细格式：

```
200 George Street, Sydney NSW 2000, Australia|2|Medium|Office building|TN123456789
42 Wallaby Way, Sydney NSW 2000, Australia|1|Small|Residential|TN123456790
380 Lonsdale Street, Melbourne VIC 3000, Australia|3|Large|Business|TN123456791
```

## 📝 字段映射说明

### CSV/制表符格式支持的列
- `Address` - 地址（必需）
- `Latitude` - 纬度
- `Longitude` - 经度
- `UnitNumber` - 单元号
- `PackageCount` - 包裹数量
- `PackageSize` - 包裹大小（Small/Medium/Large）
- `PackageType` - 包裹类型（Box/Bag/Letter）
- `DeliveryType` - 配送类型（Delivery/Pickup）
- `Notes` - 备注
- `TrackingNumber` - 追踪号码
- `OrderNumber` - 订单号码
- `AccessInstructions` - 访问说明

### 管道分隔符格式字段顺序
```
地址|包裹数量|包裹大小|备注|追踪号码|订单号码|访问说明
```

## 🎯 使用步骤

### 1. 准备数据
- 从Excel、Google Sheets或其他系统复制地址数据
- 确保地址格式完整正确
- 可选择包含额外信息（包裹数量、备注等）

### 2. 打开批量贴入
- 在NaviBatch中点击"批量贴入"按钮
- 或使用快捷键（如果有设置）

### 3. 粘贴数据
- 将复制的数据粘贴到文本框中
- 系统会自动检测数据格式
- 预览解析结果

### 4. 验证和导入
- 检查解析的地址是否正确
- 修正任何错误的地址
- 点击"导入"完成批量添加

## ⚡ 快速示例

### 从Excel复制
1. 在Excel中选择地址列
2. 复制（Ctrl+C）
3. 在NaviBatch中粘贴（Ctrl+V）

### 从Google Maps复制
1. 在Google Maps中搜索地址
2. 复制完整地址
3. 每行粘贴一个地址

### 从邮件或文档复制
1. 选择包含地址的文本
2. 复制到剪贴板
3. 在NaviBatch中粘贴并调整格式

## 📋 地址格式建议

### 澳大利亚地址
```
123 Collins Street, Melbourne VIC 3000, Australia
456 George Street, Sydney NSW 2000, Australia
789 Queen Street, Brisbane QLD 4000, Australia
```

### 美国地址
```
123 Main Street, New York NY 10001, USA
456 Broadway, Los Angeles CA 90210, USA
789 Oak Avenue, Chicago IL 60601, USA
```

### 英国地址
```
123 Oxford Street, London W1D 2HX, UK
456 High Street, Manchester M1 1AA, UK
789 King's Road, Edinburgh EH1 1AA, UK
```

## ⚠️ 注意事项

### 数据质量
- 确保地址完整且准确
- 包含邮编和国家信息
- 避免使用缩写（除非是标准缩写）

### 格式一致性
- 同一批数据使用相同的格式
- 保持列顺序一致
- 使用统一的分隔符

### 数量限制
- 建议单次导入不超过1000个地址
- 大量数据建议分批导入
- 注意系统性能和响应时间

### 特殊字符
- 地址中的逗号需要用引号包围（CSV格式）
- 避免使用特殊符号作为分隔符
- 确保文本编码正确

## 🔧 故障排除

### 地址无法识别
- 检查地址格式是否完整
- 确认地址真实存在
- 尝试使用更详细的地址描述

### 格式解析错误
- 检查分隔符是否正确
- 确认数据格式一致
- 移除多余的空行或字符

### 坐标验证失败
- 确认坐标在有效范围内
- 检查纬度经度顺序
- 验证坐标与地址匹配

## 💡 最佳实践

1. **先测试小批量**：首次使用时先导入少量地址测试
2. **保持数据备份**：导入前保存原始数据
3. **逐步验证**：导入后检查地址准确性
4. **使用标准格式**：遵循推荐的地址格式
5. **定期清理**：删除重复或无效的地址
