# NaviBatch 导入快速参考

## 🚀 最简单的导入方式

### 1. 纯地址列表（推荐新手）
```
200 George Street, Sydney NSW 2000, Australia
42 Wallaby Way, Sydney NSW 2000, Australia
380 Lonsdale Street, Melbourne VIC 3000, Australia
```

### 2. 从Excel复制粘贴
1. 选择Excel中的地址列
2. 复制（Ctrl+C）
3. 在NaviBatch中粘贴（Ctrl+V）

## 📊 支持的文件格式

| 格式 | 扩展名 | 用途 |
|------|--------|------|
| CSV | .csv | 表格数据，支持多字段 |
| JSON | .json | 结构化数据，程序导出 |
| TXT | .txt | 简单地址列表 |

## 🔧 必需 vs 可选字段

### ✅ 必需字段
- **Address** - 完整地址

### 🔧 可选字段
- **Latitude/Longitude** - 坐标（提供则跳过地理编码）
- **UnitNumber** - 单元号（如：Unit 5, Suite 1205）
- **PackageCount** - 包裹数量（默认1）
- **PackageSize** - 大小（Small/Medium/Large）
- **PackageType** - 类型（Box/Bag/Letter）
- **Notes** - 备注信息
- **TrackingNumber** - 追踪号码

## 📝 地址格式示例

### 🇦🇺 澳大利亚
```
123 Collins Street, Melbourne VIC 3000, Australia
456 George Street, Sydney NSW 2000, Australia
```

### 🇺🇸 美国
```
123 Main Street, New York NY 10001, USA
456 Broadway, Los Angeles CA 90210, USA
```

### 🇬🇧 英国
```
123 Oxford Street, London W1D 2HX, UK
456 High Street, Manchester M1 1AA, UK
```

## ⚡ 快速CSV模板

```csv
Address,PackageCount,PackageSize,Notes
"你的地址1",1,Medium,"备注1"
"你的地址2",2,Large,"备注2"
"你的地址3",1,Small,"备注3"
```

## 🎯 最佳实践

1. **地址要完整** - 包含街道、城市、邮编、国家
2. **先测试少量** - 首次使用先导入5-10个地址
3. **保持格式一致** - 同一批数据使用相同格式
4. **检查结果** - 导入后验证地址准确性

## ⚠️ 常见错误

❌ **地址不完整**
```
Collins Street  # 缺少门牌号、城市、邮编
```

✅ **正确格式**
```
123 Collins Street, Melbourne VIC 3000, Australia
```

❌ **坐标错误**
```
Address,Latitude,Longitude
"Sydney",200,300  # 坐标超出范围
```

✅ **正确坐标**
```
Address,Latitude,Longitude
"Sydney NSW, Australia",-33.8688,151.2093
```

## 🆘 遇到问题？

1. **地址无法识别** → 检查地址是否完整准确
2. **文件无法导入** → 确认文件格式和编码（UTF-8）
3. **坐标验证失败** → 检查坐标范围和格式
4. **批量贴入失败** → 确认数据格式一致性

## 📞 技术支持

- 查看完整指南：`ImportGuide.md`
- 批量贴入指南：`BatchPasteGuide.md`
- 示例文件：`SampleAddresses_Complete.csv`
