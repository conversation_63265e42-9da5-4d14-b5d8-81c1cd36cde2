# 路线优化界面增强

## 🎯 功能概述

根据用户要求，在路线优化结果界面添加了以下功能：
1. **第三方排序标签显示** - 在地址卡片中显示第三方排序号码
2. **取消优化按钮** - 让用户可以取消优化，恢复到原始顺序，但保持在当前界面

## ✅ 完成的修改

### 1. 本地化字符串添加

**文件**: `NaviBatch/Localizations/zh-Hans.lproj/Localizable.strings`
```
"restore_route" = "还原路线";
```

**文件**: `NaviBatch/Localizations/en.lproj/Localizable.strings`
```
"restore_route" = "Restore Route";
```

### 2. 路线优化结果界面增强

**文件**: `NaviBatch/Views/Components/OptimizationResultSheet.swift`

#### 2.1 导航栏按钮更新
- 在导航栏右侧添加了"还原路线"按钮
- 保持原有的"应用"按钮
- 使用 HStack 布局两个按钮

```swift
ToolbarItem(placement: .navigationBarTrailing) {
    HStack(spacing: 8) {
        // 还原路线按钮
        Button {
            restoreOriginalRoute()
        } label: {
            Label("restore_route".localized, systemImage: "arrow.counterclockwise")
        }
        .buttonStyle(.bordered)

        // 应用优化按钮
        Button {
            // 应用优化结果并标记为已优化
            markPointsAsOptimized()
            onApply()
            dismiss()
        } label: {
            Label("apply".localized, systemImage: "checkmark.circle.fill")
        }
        .buttonStyle(.borderedProminent)
    }
}
```

#### 2.2 还原路线功能实现
添加了 `restoreOriginalRoute()` 方法：
- 将所有配送点的 `sorted_number` 重置为 `sort_number`
- 移除所有点的优化标记 (`isOptimized = false`)
- 移除路线的优化状态
- 更新 ViewModel 数据
- 发送数据变化通知
- 自动关闭优化结果界面

#### 2.3 第三方排序标签显示
在地址信息区域添加第三方排序标签：
```swift
// 🎯 显示第三方排序标签
if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
    Label(thirdPartySortNumber, systemImage: "number.square")
        .font(.caption)
        .foregroundColor(.orange)
}
```

## 🎨 UI 设计特点

### 按钮样式
- **还原路线按钮**: 使用 `.bordered` 样式，橙色图标 `arrow.counterclockwise`
- **应用按钮**: 使用 `.borderedProminent` 样式，绿色图标 `checkmark.circle.fill`

### 第三方排序标签
- **图标**: `number.square` 系统图标
- **颜色**: 橙色 (`.orange`)
- **字体**: `.caption` 大小
- **位置**: 在包裹信息旁边显示

## 🔧 技术实现

### 数据流程
1. **还原操作**:
   - 重置 `sorted_number` → `sort_number`
   - 清除优化标记
   - 保存到数据库
   - 更新 ViewModel
   - 发送通知

2. **第三方标签显示**:
   - 检查 `thirdPartySortNumber` 是否存在
   - 在地址信息区域显示标签
   - 在序号框中优先显示第三方数字

### 现有功能保持
- 序号框中的第三方排序号显示逻辑保持不变
- 所有现有的优化、分组功能正常工作
- 导航、配送状态更新等功能不受影响

## 📱 用户体验

### 操作流程
1. 用户进行路线优化
2. 查看优化结果界面
3. 可以看到：
   - 第三方排序标签（如果存在）
   - 优化前后距离对比
   - 优化后的路线顺序
4. 用户可以选择：
   - **应用优化** - 确认使用优化结果
   - **还原路线** - 取消优化，恢复原始顺序
   - **取消** - 关闭界面不做任何更改

### 视觉反馈
- 第三方排序标签使用橙色突出显示
- 还原按钮使用逆时针箭头图标，直观表示"撤销"操作
- 按钮样式区分：边框样式 vs 填充样式

## 🌍 多语言支持

已添加中文和英文本地化支持：
- 中文: "还原路线"
- 英文: "Restore Route"

其他语言可以通过相同的本地化键 `restore_route` 进行扩展。

## 🔄 2025-06-23 界面优化更新

### 用户反馈
用户反馈希望将"取消优化"按钮放在"优化后路线顺序"标题行的右侧，点击后取消优化但保持在当前界面，而不是退回到 bottom sheet。

### 实施的改进

#### 3.1 按钮位置调整
- **移除导航栏中的还原路线按钮**，保持导航栏简洁
- **将取消优化按钮移动到标题行右侧**，更符合用户的操作习惯

#### 3.2 新增 `cancelOptimization()` 函数
- 复制原有的 `restoreOriginalRoute()` 逻辑
- **关键差异**: 不调用 `dismiss()`，保持在当前界面
- 用户可以在取消优化后继续查看界面内容

#### 3.3 代码实现
```swift
// 标题行布局
HStack {
    Text("optimized_route_order".localized)
        .font(.headline)

    Spacer()

    // 取消优化按钮
    Button {
        cancelOptimization()
    } label: {
        HStack(spacing: 4) {
            Image(systemName: "arrow.counterclockwise")
                .font(.system(size: 14))
            Text("restore_route".localized)
                .font(.subheadline)
        }
        .foregroundColor(.orange)
    }
    .buttonStyle(.plain)
}
```

### 用户体验改进
- ✅ **更直观的按钮位置** - 取消优化按钮紧邻相关内容
- ✅ **保持界面连续性** - 取消优化后不退出界面
- ✅ **简化导航栏** - 减少导航栏按钮数量，界面更清爽

## 🎉 总结

此次增强完全满足了用户的需求：
1. ✅ **第三方排序标签** - 在地址卡片中清晰显示
2. ✅ **取消优化按钮** - 提供便捷的优化撤销功能，位置更合理
3. ✅ **保持现有功能** - 不影响任何现有的路线优化逻辑
4. ✅ **良好的用户体验** - 直观的按钮设计和操作流程
5. ✅ **完整的本地化** - 支持中英文界面
6. ✅ **界面连续性** - 取消优化后保持在当前界面

用户现在可以在路线优化结果界面中看到第三方排序信息，并且可以随时取消优化恢复到原始状态，同时保持在当前界面继续操作，大大提升了使用的灵活性和便利性。
