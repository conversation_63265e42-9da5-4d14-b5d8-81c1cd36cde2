# PDF AI识别功能测试指南

## 🧪 测试目标

验证PDF AI识别功能的完整性和准确性，确保：
1. AI能正确识别PDF来源的图像
2. 使用专门的PDF提示词
3. 智能降级机制正常工作
4. 图像压缩和优化有效

## 📋 测试用例

### 1. PDF文本提取测试
**测试文件**: 包含可搜索文本的PDF地址列表
**预期结果**: 
- 直接从PDF文本提取地址
- 处理时间: 2-5秒
- 准确率: 95%+
- 日志显示: "PDF AI文本识别成功"

### 2. PDF图像处理测试
**测试文件**: 扫描生成的PDF（无文本层）
**预期结果**:
- 自动降级到图像处理
- AI识别图像来源为PDF
- 使用PDF专用提示词
- 日志显示: "PDF文本提取为空，使用图像处理"

### 3. 混合PDF测试
**测试文件**: 部分文本+部分图像的PDF
**预期结果**:
- 优先尝试文本提取
- 失败时降级到图像处理
- 智能选择最佳处理方式

## 🔍 测试步骤

### 步骤1: 准备测试文件
1. 创建包含地址列表的PDF文件
2. 确保包含不同格式的地址
3. 准备扫描版PDF作为对比

### 步骤2: 执行测试
1. 打开NaviBatch应用
2. 选择扫描功能
3. 选择PDF文件选择器（橙色按钮）
4. 选择测试PDF文件
5. 观察处理过程和结果

### 步骤3: 验证结果
1. 检查识别的地址数量和准确性
2. 验证处理时间是否符合预期
3. 查看日志确认使用的处理方式

## 📊 测试检查清单

### AI服务调用验证
- [ ] FirebaseAI收到isPDFImage=true参数
- [ ] 使用PDF专用提示词
- [ ] 提示词包含PDF上下文信息
- [ ] 备用服务也收到正确参数

### 处理流程验证
- [ ] PDF文本提取优先执行
- [ ] 文本为空时自动降级
- [ ] 图像压缩正常工作
- [ ] 错误处理机制有效

### 用户体验验证
- [ ] 进度显示准确
- [ ] 状态提示清晰
- [ ] 结果展示正确
- [ ] 性能表现良好

## 🐛 常见问题排查

### 问题1: PDF文本提取失败
**症状**: 总是降级到图像处理
**排查**: 检查PDF是否为扫描版本，文本层是否存在

### 问题2: AI识别准确率低
**症状**: 识别结果不准确
**排查**: 确认AI收到isPDFImage参数，检查提示词内容

### 问题3: 图像处理失败
**症状**: Firebase AI返回错误
**排查**: 检查图像压缩是否正常，文件大小是否超限

## 📝 测试报告模板

```
测试日期: ____
测试文件: ____
应用版本: ____

测试结果:
- PDF文本提取: ✅/❌
- 图像降级处理: ✅/❌
- AI参数传递: ✅/❌
- 识别准确率: ___%
- 处理时间: ___秒

问题记录:
1. ____
2. ____

改进建议:
1. ____
2. ____
```

## 🎯 成功标准

### 功能完整性
- ✅ PDF文本提取功能正常
- ✅ 图像降级机制有效
- ✅ AI参数传递正确
- ✅ 错误处理完善

### 性能指标
- ✅ 文本处理: < 5秒
- ✅ 图像处理: < 30秒
- ✅ 识别准确率: > 85%
- ✅ 内存使用: 合理范围

### 用户体验
- ✅ 界面响应流畅
- ✅ 状态提示清晰
- ✅ 错误信息友好
- ✅ 结果展示直观
