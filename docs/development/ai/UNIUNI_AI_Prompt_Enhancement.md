# UNIUNI 快递AI提示词智能地址分离功能增强

## 概述

我是基于 Claude Sonnet 4 模型的 Augment Agent。根据您提供的UNIUNI快递应用截图，我已经成功为UNIUNI快递实现了智能地址分离功能，该功能可以自动将包含公寓号的地址分离为两个版本，以提高地理编码的准确性。

## 截图分析

### 🖼️ UNIUNI界面特征

根据提供的截图，UNIUNI快递应用具有以下特征：

**界面布局**：
- 白色背景，简洁的卡片布局
- 中文界面（显示"路线号"）
- 左侧显示三位数字路线号
- 右侧显示配送详情

**数据格式**：
- **路线号**: 三位数字 (149, 150, 151, 152)
- **追踪号**: UUS + 16位数字 (如: UUS56D056436296171, UUS56G178373307651)
- **收件人**: 完整英文姓名 (Faith quick, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>)
- **地址**: 美国加州地址格式
  - "38623 ORCHARD ST,CHERRY VALLEY,CA,US,92223-3638"
  - "9210 Bonita Dr, CHERRY VALLEY, CA, US, 92223"
  - "8601 Oak Glen Rd, CHERRY VALLEY, CA, US, 92223"
  - "10081 AVENIDA MIRAVILLA CHERRY VALLEY CA US 92223"

**特殊标识**：
- 配送日期：2025-06-19
- 距离信息：19.3米外、20.0米外等
- 提醒信息："Suspected Apt/Suspecté Apt..."

## 实现的功能

### 🏠 智能地址分离规则

AI识别时会自动检测地址中的公寓/单元信息，并提供两个版本：
- **full_address**: 完整地址（包含公寓号）
- **address**: 纯街道地址（不含公寓号）

### 🔍 公寓号识别关键词

系统能够识别以下公寓号格式：
- Apt, Apartment
- Unit, Suite, Room, Rm, Ste
- 单独的 # 号码

### 📄 示例

**输入地址**: "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA, 92223"

**AI返回结果**:
```json
{
  "success": true,
  "deliveries": [
    {
      "sort_number": "149",
      "tracking_number": "UUS56D056436296171",
      "address": "38623 ORCHARD ST, CHERRY VALLEY, CA, 92223",
      "full_address": "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA, 92223",
      "customer": "Faith quick"
    }
  ]
}
```

## 技术实现

### 1. AI提示词更新

#### Firebase AI服务 (`NaviBatch/Services/FirebaseAIService.swift`)
- 更新了`createUniUniPrompt()`方法
- 添加了智能地址分离规则
- 新增了`full_address`字段支持
- 修正了地区分类（从澳洲改为美国）

#### Gemma AI服务 (`NaviBatch/Services/GemmaVisionService.swift`)
- 更新了`createUniUniPrompt()`方法
- 添加了相同的智能地址分离规则
- 新增了`full_address`字段支持
- 修正了地区分类（从澳洲改为美国）

#### 调试功能 (`NaviBatch/Views/Components/ImageAddressRecognizer.swift`)
- 更新了`createUniUniPromptForDebug()`方法
- 保持与实际AI服务一致的提示词

### 2. 地区分类修正

#### 修正前的问题
多个美国快递服务被错误地分类为澳洲快递：
- UNIUNI: 被分类为澳洲，但实际服务美国加州地址
- LDS EPOD: 被分类为澳洲，但实际是美国快递
- PIGGY: 被分类为澳洲，但实际是美国快递
- GoFo: 被分类为澳洲，但实际是美国快递
- SpeedX: 被分类为澳洲，但实际是美国快递

#### 修正后的配置
所有美国快递服务现在都正确使用`baseUSAPrompt`：

**UNIUNI**:
```swift
case .uniuni:
    return baseUSAPrompt + """
    UNIUNI 特征:
    - 追踪号码: UUS + 16位数字 (如: UUS56D056436296171)
    - 路线号码: 三位数字 (149, 150, 151, 152等)
    - 收件人信息: 完整英文姓名
    - 美国地址格式，支持智能公寓号分离
    - 中文界面但地址为英文格式
    - 主要服务加州地区 (CHERRY VALLEY, CA等)
    """
```

**其他修正的快递服务**:
- LDS EPOD: 现在使用美国地址格式，支持智能公寓号分离
- PIGGY: 现在使用美国地址格式，支持智能公寓号分离
- GoFo: 现在使用美国地址格式，支持智能公寓号分离
- SpeedX: 现在使用美国地址格式，支持智能公寓号分离

### 3. 追踪号格式更新

#### DeliveryAppType (`NaviBatch/Models/DeliveryAppType.swift`)
更新了追踪号格式示例，使其与截图中的实际格式一致：
```swift
case .uniuni:
    return "UUS + 16位数字 (如: UUS56D056436296171)"
```

## 地理编码策略配合

### 现有的Apple策略

NaviBatch已经有一个"策略4: 🏠 尝试移除公寓号（Apple策略）"在`UniversalAddressProcessor`中：

1. 检查地址是否包含公寓信息
2. 如果包含，会移除这些信息重新进行地理编码
3. 这与新的AI分离策略完美配合

### 工作流程

1. **AI识别**: 返回两个地址版本（full_address和address）
2. **数据存储**: 完整地址存储在`originalAddress`字段中
3. **地理编码**: 系统首先尝试完整地址，失败时自动移除公寓号重试
4. **坐标获取**: 获得准确的坐标后保存到地址库

## 向后兼容性

### 保持现有接口

- 所有现有调用方无需修改
- 通过现有的地址处理逻辑处理完整地址
- 新功能仅在UNIUNI识别时启用

### 渐进式增强

- 如果AI没有返回`full_address`字段，系统回退到原有逻辑
- 现有地址处理流程完全兼容
- 新功能专门针对UNIUNI快递优化

## 测试建议

### 1. UNIUNI地址识别测试

使用包含公寓号的UNIUNI截图测试：
- "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA"
- "9210 Bonita Dr, Unit 2, CHERRY VALLEY, CA"
- "8601 Oak Glen Rd, Suite 100, CHERRY VALLEY, CA"

### 2. 地理编码准确性测试

验证系统能够：
- 优先使用完整地址进行地理编码
- 在失败时自动回退到街道地址
- 获得准确的坐标结果

### 3. 界面识别测试

确认AI能够正确识别：
- 中文界面中的"路线号"标识
- 三位数字路线号 (149, 150, 151, 152)
- UUS格式追踪号
- 英文收件人姓名
- 美国加州地址格式

## 更新日期

2025-06-24:
- 初始实现，支持UNIUNI智能地址分离功能
- 修正UNIUNI地区分类错误（从澳洲改为美国）
- 全面修正所有美国快递服务的地区分类：
  - LDS EPOD: 澳洲 → 美国
  - PIGGY: 澳洲 → 美国
  - GoFo: 澳洲 → 美国
  - SpeedX: 澳洲 → 美国
  - UNIUNI: 澳洲 → 美国
- 为所有美国快递服务添加智能公寓号分离支持
- 更新追踪号格式示例，与实际截图保持一致

---

*此次更新不仅针对UNIUNI快递进行了地址识别优化，还全面修正了NaviBatch中所有美国快递服务的地区分类错误，确保AI提示词与DeliveryAppType.swift中的配置保持一致。*
