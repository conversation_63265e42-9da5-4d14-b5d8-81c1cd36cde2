# 🚨 AI识别问题排查指南

## 📋 **常见问题及解决方案**

### **1. 地理位置限制问题**

**症状：**
```
"User location is not supported for the API use"
"Provider returned error: User location is not supported"
```

**原因：** 某些AI模型对香港、中国大陆等地区有地理限制

**解决方案：**
1. **使用OCR模式**：在设置中切换到"OCR优先"或"仅OCR"模式
2. **使用VPN**：连接到美国、欧洲等支持的地区
3. **等待自动降级**：系统会自动降级到OCR模式

---

### **2. AI返回测试数据问题**

**症状：**
- 返回澳洲ROWVILLE地区的虚假地址
- 客户名显示为"New Customer", "Another Customer"
- 地址与实际图片内容不符

**原因：** AI模型没有正确识别图片，返回了预设的示例数据

**解决方案：**
1. **系统自动检测**：NaviBatch已添加测试数据检测，会自动拒绝并降级到OCR
2. **手动切换OCR**：在设置中选择"OCR优先"模式
3. **重新拍照**：确保图片清晰、光线充足

---

### **3. PHPickerViewControllerDelegate_Private错误**

**症状：**
```
PHPickerViewControllerDelegate_Private doesn't respond to _pickerDidPerformConfirmationAction
```

**原因：** iOS系统级别的警告，通常不影响功能

**解决方案：**
- **忽略此警告**：这是iOS内部警告，不影响应用功能
- **重启应用**：如果遇到图片选择问题，重启应用即可

---

## 🛠 **推荐设置**

### **香港用户推荐配置：**
```
识别模式: OCR优先
AI开关: 开启（作为备用）
置信度阈值: 0.7
降级行为: 自动OCR
```

### **其他地区用户推荐配置：**
```
识别模式: AI优先
AI开关: 开启
置信度阈值: 0.8
降级行为: 自动OCR
```

---

## 🔧 **手动解决步骤**

### **步骤1：检查网络连接**
- 确保网络连接稳定
- 尝试切换WiFi/移动数据

### **步骤2：切换识别模式**
1. 打开NaviBatch设置
2. 找到"AI识别设置"
3. 选择"OCR优先"或"仅OCR"

### **步骤3：重新处理图片**
1. 确保图片清晰
2. 光线充足
3. 文字可见

### **步骤4：联系支持**
如果问题持续，请提供：
- 错误日志
- 图片样本
- 设备信息
- 地理位置

---

## 📊 **性能对比**

| 模式 | 准确率 | 速度 | 离线支持 | 地理限制 |
|------|--------|------|----------|----------|
| AI优先 | 95%+ | 慢 | ❌ | ✅ |
| OCR优先 | 85% | 快 | ✅ | ❌ |
| 仅OCR | 80% | 最快 | ✅ | ❌ |

---

## 🎯 **最佳实践**

1. **图片质量**：确保图片清晰、文字可读
2. **光线条件**：避免反光、阴影
3. **应用选择**：正确选择配送应用类型
4. **网络环境**：使用稳定的网络连接
5. **备用方案**：始终启用OCR作为备用

---

## 🚀 **更新日志**

### **v2.1.0 - 2024/12/19**
- ✅ 添加测试数据检测机制
- ✅ 改进地理位置限制处理
- ✅ 优化AI失败自动降级
- ✅ 增强错误提示信息

### **v2.0.0 - 2024/12/18**
- ✅ 集成Gemma AI识别
- ✅ 添加混合识别模式
- ✅ 支持多种配送应用
- ✅ 实现智能降级机制
