# 🧪 NaviBatch AI功能测试指南

## 🎯 **测试目标**

确认AI地址识别功能正常工作，包括：
- ✅ AI模型调用
- ✅ 降级机制
- ✅ 日志输出
- ✅ 错误处理

---

## 📱 **测试步骤**

### **第1步：启动应用并打开图片识别**
1. 在Xcode中运行NaviBatch
2. 进入地址输入界面
3. 点击📷图片识别按钮
4. 选择一张包含地址的图片

### **第2步：观察控制台日志**
在Xcode控制台中，你应该看到以下日志：

```
🚀 ImageAddressRecognizer - 开始处理1张图片
⚙️ AI设置: 启用, 首选模型: gemma-3-27b
🚀 GemmaVisionService - 开始AI地址识别
📸 图片尺寸: 1080.0x1920.0
🖼️ IMAGE: 图片压缩完成，大小: 245KB
🤖 AI: 🤖 尝试Gemma模型 (1/3): google/gemma-3-27b-it:free
🌐 NETWORK: 📤 请求大小: 367KB
```

### **第3步：检查AI处理结果**

**成功情况下的日志：**
```
🌐 NETWORK: 📥 API响应时间: 3.24s
🤖 AI: ✅ Gemma识别成功！模型: google/gemma-3-27b-it:free, 用时: 3.45s
🤖 AI: 📊 识别结果: 2个地址, 置信度: 95%
🤖 AI: 🎉 识别完成！总共找到 2 个地址
```

**失败情况下的日志：**
```
🤖 AI: ❌ Gemma模型 google/gemma-3-27b-it:free 失败: API调用频率超限
🤖 AI: 🤖 尝试Gemma模型 (2/3): google/gemma-3-9b-it:free
🤖 AI: ⚠️ Gemma AI识别失败，降级到传统OCR
📷 OCR: 📷 开始OCR识别，图片尺寸: (1080.0, 1920.0)
```

---

## 🔍 **问题诊断**

### **问题1：没有看到任何AI日志**

**可能原因：**
- AI功能未启用
- 图片选择失败
- 代码执行路径问题

**解决方法：**
1. 检查AI设置是否启用
2. 确认选择了有效图片
3. 查看是否有错误日志

### **问题2：看到开始日志但没有API调用**

**可能原因：**
- 图片处理失败
- 网络连接问题
- API密钥问题

**解决方法：**
1. 检查图片格式和大小
2. 确认网络连接
3. 验证OpenRouter API密钥

### **问题3：API调用失败**

**常见错误码：**
- `429`: API调用频率超限
- `401`: API密钥无效
- `500`: 服务器错误

**解决方法：**
1. 等待一段时间后重试
2. 检查API密钥配置
3. 尝试其他Gemma模型

---

## 🛠 **调试技巧**

### **1. 使用Xcode控制台过滤**
在控制台搜索框中输入：
- `🚀` - 查看处理开始
- `🤖 AI` - 查看AI相关日志
- `❌` - 查看错误信息
- `✅` - 查看成功信息

### **2. 检查网络请求**
观察以下日志确认网络请求：
```
🌐 NETWORK: 📤 请求大小: XXXkB
🌐 NETWORK: 📥 API响应时间: X.XXs
📊 HTTP状态码: 200
```

### **3. 验证降级机制**
如果AI失败，应该看到：
```
🤖 AI: ⚠️ Gemma AI识别失败，降级到传统OCR
📷 OCR: 📷 开始OCR识别
```

---

## 📊 **性能基准**

### **正常性能指标**
- **图片处理时间**: 0.5-2秒
- **API响应时间**: 2-8秒
- **总处理时间**: 3-12秒
- **成功率**: >90%

### **异常情况**
- **响应时间>15秒**: 网络问题
- **连续失败**: API配额或密钥问题
- **无日志输出**: 代码执行问题

---

## 🎯 **测试用例**

### **测试用例1：正常配送截图**
- **输入**: 配送应用截图（包含完整地址）
- **期望**: AI成功识别地址
- **验证**: 检查识别的地址数量和准确性

### **测试用例2：模糊图片**
- **输入**: 模糊或低质量图片
- **期望**: AI尝试后降级到OCR
- **验证**: 确认降级机制正常工作

### **测试用例3：无地址图片**
- **输入**: 不包含地址的图片
- **期望**: 返回"未识别到任何地址"
- **验证**: 错误处理正确

### **测试用例4：网络断开**
- **输入**: 断开网络连接后测试
- **期望**: 快速降级到OCR
- **验证**: 降级时间<5秒

---

## 📝 **测试报告模板**

```
测试时间: [日期时间]
测试图片: [图片描述]
AI设置: [启用/禁用]
网络状态: [正常/异常]

结果:
- AI处理: [成功/失败]
- 识别地址数: [数量]
- 处理时间: [秒]
- 降级触发: [是/否]

日志摘要:
[关键日志信息]

问题:
[发现的问题]

建议:
[改进建议]
```

---

## 🚀 **快速测试命令**

如果你想快速验证AI功能是否工作，可以：

1. **运行应用**
2. **选择任意图片**
3. **在控制台搜索** `🚀 ImageAddressRecognizer`
4. **确认看到处理开始日志**

如果看到开始日志，说明AI功能已正确集成！

---

**现在去测试吧！所有的AI处理细节都会在日志中显示。**

*本测试指南使用Claude Sonnet 4模型完成*
