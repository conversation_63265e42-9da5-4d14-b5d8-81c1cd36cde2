# AI地址识别地理编码修复文档

## 📋 问题描述

### 问题1：地理编码逻辑不一致
- **AI扫描**：使用简单的 `CLGeocoder.geocodeAddressString()`
- **手动搜索**：使用复杂的 `UniversalAddressProcessor` 多策略处理
- **结果**：AI识别的地址经常获得 `0.000000, 0.000000` 坐标

### 问题2：排序逻辑受坐标影响
- **现象**：当AI识别的地址坐标获取失败时，"错误地址优先"逻辑会打乱原始的sort号码顺序
- **期望**：sort号码应该只根据AI识别的先后顺序，不受坐标对错影响

## 🔧 解决方案

### 第一步：统一地理编码逻辑

#### 修改文件：`SimpleAddressSheet.swift`
```swift
// 原来的简单版本
private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
    let geocoder = CLGeocoder()
    // 直接使用CLGeocoder，容易失败
}

// 修改后：使用与搜索界面相同的全球地址处理器
private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
    logInfo("🌍 SimpleAddressSheet - 使用全球地址处理器处理AI扫描地址: \(address)")

    // 使用与搜索界面相同的全球地址处理器
    let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(address)

    switch globalResult {
    case .success(_, let formattedAddress, let coordinate, let placemark, let strategy, let confidence):
        logInfo("🌍 SimpleAddressSheet - AI扫描地址处理成功: \(strategy) - \(formattedAddress) (置信度: \(confidence))")
        return coordinate

    case .failed(let address, let reason):
        logWarning("SimpleAddressSheet - AI扫描地址处理失败: \(reason)")

        // 回退到原始的CLGeocoder方法
        let geocoder = CLGeocoder()
        // ... 回退逻辑
    }
}
```

#### 修改文件：`ImageAddressRecognizer.swift`
```swift
// 同样的修改，使用UniversalAddressProcessor替代简单的CLGeocoder
```

### 第二步：修复排序逻辑

#### 核心思路
- **AI扫描地址**：保持原始的sort_number顺序，不受坐标错误影响
- **手动输入地址**：继续使用"错误地址优先"逻辑

#### 修改文件：`RouteView.swift`, `RouteViewModel.swift`, `RouteBottomSheet.swift`
```swift
// 原来的排序逻辑
private func sortDeliveryPoints(_ points: [DeliveryPoint], isOptimized: Bool) -> [DeliveryPoint] {
    return points.sorted { point1, point2 in
        // 1. 起点总是最前面
        // 2. 错误地址优先（所有地址都受影响）
        // 3. 按sort_number排序
    }
}

// 修改后的排序逻辑
private func sortDeliveryPoints(_ points: [DeliveryPoint], isOptimized: Bool) -> [DeliveryPoint] {
    return points.sorted { point1, point2 in
        // 1. 起点总是最前面

        // 2. 🎯 新逻辑：AI扫描地址保持原始顺序，不受坐标错误影响
        let point1IsAIScanned = point1.sourceApp != .manual
        let point2IsAIScanned = point2.sourceApp != .manual

        // 如果都是AI扫描的地址，直接按sort_number排序，不考虑错误状态
        if point1IsAIScanned && point2IsAIScanned {
            // 直接按序号排序，忽略坐标错误
        }

        // 如果都是手动输入的地址，使用错误地址优先逻辑
        if !point1IsAIScanned && !point2IsAIScanned {
            // 错误地址优先逻辑
        }

        // 3. 按sort_number或sorted_number排序
    }
}
```

## 🎯 修改的文件列表

1. **NaviBatch/Views/Components/SimpleAddressSheet.swift**
   - 修改 `geocodeAddress()` 方法
   - 使用 `UniversalAddressProcessor` 替代简单的 `CLGeocoder`

2. **NaviBatch/Views/Components/ImageAddressRecognizer.swift**
   - 修改 `geocodeAddress()` 方法
   - 使用相同的全球地址处理器

3. **NaviBatch/Views/RouteView.swift**
   - 修改地址排序逻辑（第1353-1392行）
   - AI扫描地址保持原始顺序

4. **NaviBatch/ViewModels/RouteViewModel.swift**
   - 修改 `sortDeliveryPoints()` 方法（第3192-3232行）
   - 统一排序逻辑

5. **NaviBatch/Views/Components/RouteBottomSheet.swift**
   - 修改 `sortDeliveryPoints()` 方法（第2871-2902行）
   - 保持一致的排序逻辑

## 📊 预期效果

### 地理编码改进
- **提高成功率**：AI识别的地址使用与手动搜索相同的多策略处理
- **更好的坐标**：减少 `0.000000, 0.000000` 的情况
- **智能回退**：失败时仍有CLGeocoder作为备用

### 排序逻辑改进
- **AI地址顺序稳定**：sort号码严格按照AI识别顺序，不受坐标影响
- **手动地址灵活**：继续使用错误地址优先，便于用户修复
- **混合场景处理**：AI和手动地址混合时的合理排序

## 🧪 测试建议

1. **地理编码测试**
   - 使用之前失败的地址测试新的地理编码逻辑
   - 对比手动搜索和AI扫描的坐标结果

2. **排序测试**
   - 创建包含坐标错误的AI扫描地址
   - 验证sort号码是否保持1,2,3,4...的顺序
   - 测试混合AI和手动地址的排序

## 📝 注意事项

- 修改后的排序逻辑依赖于 `sourceApp` 字段来区分AI扫描和手动输入
- 确保AI扫描时正确设置 `sourceApp` 为非 `.manual` 值
- 地理编码的改进可能会增加处理时间，但会提高准确性

## 🔄 更新时间
- 2025-06-23 - 初始修复实现
- 2025-06-24 - YWE AI提示词优化，添加智能地址分离功能

## 📦 YWE AI提示词优化 (2025-06-24)

### 更新内容
基于YWE快递应用界面截图分析，完善了YWE的AI提示词：

1. **界面特征识别**
   - 中文界面：派送任务、收件人、地址、派送成功
   - 排序格式：# + 数字 (#1, #2, #3, #4...)
   - 运单号格式：YWAUS + 15位数字

2. **智能地址分离功能**
   - 自动识别公寓/单元信息
   - 提供完整地址和纯街道地址两个版本
   - 提升geocoding准确性

3. **地址格式修正**
   - 修正YWE为美国地址格式（原来错误分类为澳洲）
   - 使用Apple Maps兼容的街道简称

### 更新文件
- `NaviBatch/Services/FirebaseAIService.swift` - createYWEPrompt()
- `NaviBatch/Services/GemmaVisionService.swift` - createYWEPrompt()
- `NaviBatch/Views/Components/ImageAddressRecognizer.swift` - createYWEPromptForDebug()
- `NaviBatch/Documentation/YWE_Address_Cleaning_Fix.md` - 文档更新

### 预期效果
- 提升YWE地址识别准确性
- 改善包含公寓号地址的geocoding成功率
- 统一地址格式标准
