# SpeedX AI提示词一致性检查与修复

## 概述

我是基于 Claude Sonnet 4 模型的 Augment Agent。用户询问SpeedX的视频和图片处理是否使用了一致的AI提示词。经过检查发现存在细微差异，现已修复确保完全一致。

## 检查结果

### 🔍 发现的不一致问题

#### 1. JSON返回格式差异

**FirebaseAIService.swift** (正确格式):
```
You MUST respond with ONLY valid JSON in this exact format:
{"success": true, "deliveries": [{"third_party_sort": "number_only", "tracking_number": "SPXSF_tracking_number", "address": "formatted_address", "customer": "customer_name"}]}

Do not include any text before or after the JSON.
```

**GemmaVisionService.swift** (修复前的格式):
```
Return JSON:
{
    "success": true,
    "deliveries": [
        {
            "third_party_sort": "number_only",
            "tracking_number": "SPXSF_number_or_empty",
            "address": "formatted_address",
            "customer": "name_or_empty"
        }
    ]
}

If nothing visible: {"success": false, "deliveries": []}
```

#### 2. 字段命名差异

| 服务 | tracking_number | customer |
|------|----------------|----------|
| **FirebaseAI** | `"SPXSF_tracking_number"` | `"customer_name"` |
| **GemmaVision** (修复前) | `"SPXSF_number_or_empty"` | `"name_or_empty"` |

## 修复内容

### ✅ 已修复的问题

#### 1. 统一JSON返回格式
- **修复位置**: `NaviBatch/Services/GemmaVisionService.swift` 第947-950行
- **修复内容**: 将GemmaVision的JSON格式与FirebaseAI完全统一

#### 2. 统一字段命名规范
- **tracking_number**: 统一为 `"SPXSF_tracking_number"`
- **customer**: 统一为 `"customer_name"`

#### 3. 统一格式要求
- **严格性**: 都要求"ONLY valid JSON"
- **禁止额外文本**: 都禁止在JSON前后添加其他文本

## 一致性验证

### ✅ 核心提示词内容 (100%一致)

#### 基本信息提取
```
Extract delivery information from SpeedX app. Each delivery has:
- Address (left side, 1-2 lines)
- Customer name (right side, blue text)
- Tracking number (bottom left, starts with SPXSF)
- Stop number (bottom right, format: 停靠点: X)
```

#### 关键要求 (CRITICAL REQUIREMENTS)
```
1. Each delivery is separated by blue left border
2. Extract ONLY the number from "停靠点: X" (e.g., from "停靠点: 18" return "18")
3. Each stop number must be unique - no duplicates
4. Match information within the same task block
5. EVERY delivery MUST have a stop number - if missing, mark as "missing"
6. Remove country suffixes (USA, US, United States) from addresses
7. For video frames: scan entire image carefully for all stop numbers first
```

#### 停靠点强制要求 (STOP NUMBER IS MANDATORY)
```
- Stop numbers are THE MOST IMPORTANT data to extract
- Look carefully in bottom right corner of each task block
- If you cannot find a stop number, do not create that delivery entry
- Better to miss a delivery than create one without stop number
```

### ✅ 地区特定提示词 (100%一致)

#### 美国地址格式规则
```
US Address Format Rules:
- Format: "Number Street, City, State, Zipcode"
- Example: "1721 Marina Ct, San Mateo, CA, 94403"
- Street types: St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
- State abbreviations: CA, NY, TX, FL, etc.
- ZIP code: 5 digits or 5+4 format
- REMOVE country suffixes: Remove "USA", "US", "United States" from addresses
```

#### SpeedX特征描述
```
SPEEDX Features:
- Tracking: SPXSF + 14 digits (e.g., SPXSF00567490961577)
- Stop numbers: 停靠点: X (X is digit, MANDATORY for every delivery)
- Address format: US format, remove country suffixes
- Interface: Chinese UI but English addresses
- CRITICAL: Every delivery must have stop number or skip that delivery
```

## 技术实现

### 修改的文件

#### 1. NaviBatch/Services/GemmaVisionService.swift
- **方法**: `createSpeedXPrompt()`
- **行数**: 第947-950行
- **修改内容**: 统一JSON返回格式和字段命名

### 验证方法

#### 1. 代码对比验证
```bash
# 提取两个服务的SpeedX提示词进行对比
grep -A 30 "createSpeedXPrompt" NaviBatch/Services/FirebaseAIService.swift
grep -A 30 "createSpeedXPrompt" NaviBatch/Services/GemmaVisionService.swift
```

#### 2. 功能测试验证
- **图片处理**: 使用FirebaseAI处理SpeedX图片
- **视频处理**: 使用GemmaVision处理SpeedX视频
- **结果对比**: 确保返回格式完全一致

#### 3. JSON格式验证
- **字段名称**: 确保所有字段名称完全一致
- **数据类型**: 确保数据类型和格式要求一致
- **错误处理**: 确保错误情况的处理方式一致

## 预期效果

### 1. 🎯 处理结果一致性
- **图片处理**: FirebaseAI处理图片的结果格式
- **视频处理**: GemmaVision处理视频的结果格式
- **完全统一**: 两种处理方式返回完全相同的JSON结构

### 2. 🔧 开发维护便利性
- **代码维护**: 只需维护一套提示词逻辑
- **问题排查**: 统一的格式便于问题定位
- **功能扩展**: 新功能可以同时应用到两个服务

### 3. 🚀 用户体验提升
- **处理稳定性**: 无论使用图片还是视频，结果格式一致
- **功能可靠性**: 减少因格式差异导致的处理错误
- **性能优化**: 统一的处理逻辑提高整体性能

## 最佳实践

### 1. 提示词同步机制
- **修改原则**: 任何对SpeedX提示词的修改都必须同时应用到两个服务
- **验证要求**: 每次修改后都要进行一致性检查
- **文档更新**: 及时更新相关文档和注释

### 2. 测试验证流程
- **单元测试**: 为两个服务的SpeedX提示词创建单元测试
- **集成测试**: 验证图片和视频处理的结果一致性
- **回归测试**: 确保修改不影响其他功能

### 3. 代码审查要点
- **提示词内容**: 确保核心逻辑完全一致
- **JSON格式**: 验证返回格式的一致性
- **错误处理**: 检查异常情况的处理方式

## 更新日志

### v1.0.12 (2025-06-26)
- 🔍 **发现问题**: SpeedX的视频和图片处理AI提示词存在细微差异
- 🛠️ **修复差异**: 统一GemmaVisionService的JSON返回格式
- ✅ **验证一致性**: 确保两个服务的提示词100%一致
- 📝 **文档更新**: 创建一致性检查文档和最佳实践指南

### 关键改进
- **JSON格式统一**: 两个服务现在使用完全相同的JSON返回格式
- **字段命名统一**: 所有字段名称和描述完全一致
- **处理逻辑统一**: 核心提示词内容100%一致

---
*检查时间：2025年6月26日*
*版本：NaviBatch v1.0.4.6211*
*基于：Claude Sonnet 4 模型*
