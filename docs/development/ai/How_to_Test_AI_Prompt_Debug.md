# 如何测试AI提示词调试功能

## 测试步骤

### 1. 准备工作
- 确保在Xcode中打开NaviBatch项目
- 连接iOS设备或启动iOS模拟器
- 打开Xcode的控制台窗口（View → Debug Area → Activate Console）

### 2. 启动应用
- 在Xcode中运行NaviBatch应用
- 等待应用完全加载

### 3. 进入扫描器界面
- 在应用主界面中找到扫描功能入口
- 点击进入扫描器界面（通常是相机图标或"扫描"按钮）

### 4. 测试AI提示词打印
- 在扫描器界面中，找到快递公司选择区域
- 依次点击不同的快递公司按钮，例如：
  - Amazon Flex
  - iMile
  - PIGGY
  - UNIUNI
  - GoFo
  - 等等

### 5. 查看控制台输出
- 每次点击快递公司按钮后，立即查看Xcode控制台
- 应该能看到类似以下格式的输出：

```
🔍 ==================== AI提示词调试 ====================
🔍 快递类型: Amazon Flex (amazonFlex)
🔍 地区: usa
🔍 ========================================================
🔥 Firebase AI 提示词:
📄 Look at this Amazon Flex delivery screenshot and extract ONLY what you can actually see.

CRITICAL: Look for the actual numbers in the image. In Amazon Flex screenshots, you will see:
- Numbers like: 8, 9, 10, 11, 12, 13, 19, etc. (these are the real sort numbers)
- Addresses like: "1762 Borden Street", "1721 MARINA CT APT D", etc.
- Time information like: "已预约 3:00 - 8:00 上午 今天" or "Scheduled 3:00 - 8:00 AM Today"

DO NOT make up sort numbers like D82, D83, D84 - these are fake!
ONLY extract the actual numbers you can see in the image.

🇺🇸 美国地址识别规则:
- 地址格式: "Number Street, City, State, Zipcode"
- 示例: "1721 Marina Court, San Mateo, CA, 94403"
- 重点识别美国州名缩写 (CA, NY, TX, FL等)
- 美国邮编格式: 5位数字或5+4位格式

AMAZON FLEX 特征:
- 排序号码: 简单数字 (2, 3, 4, 5, 6, 7, 8等)
- 追踪号码: Amazon风格追踪号
- 配送时间: "已预约 3:00 - 8:00 上午 今天" 或 "Scheduled 3:00 - 8:00 AM Today"
- 重点提取配送时间段信息

You MUST respond with ONLY valid JSON in this exact format:
{"success": true, "deliveries": [{"sort_number": "number_if_found", "tracking_number": "tracking_if_found", "address": "formatted_address", "customer": "name_if_found", "delivery_time": "time_if_found"}]}

Do not include any text before or after the JSON.
🔍 ========================================================
🤖 Gemma AI 提示词:
📄 [相同的提示词内容]
🔍 ========================================================
```

## 预期结果

### 成功标志
- 每次点击快递公司按钮都会在控制台输出对应的AI提示词
- 输出格式清晰，包含分隔线和标识符
- 不同快递公司显示不同的提示词内容
- Firebase AI和Gemma AI的提示词内容基本相同

### 验证要点
1. **快递类型识别正确**: 控制台显示的快递类型与点击的按钮一致
2. **地区信息正确**: 美国快递显示"usa"，澳洲快递显示"australia"
3. **提示词内容完整**: 包含基础提示词、地区特定规则和JSON格式要求
4. **特殊规则生效**: 例如iMile的测试数据过滤规则应该出现在提示词中

## 故障排除

### 如果没有看到输出
1. 确认Xcode控制台已打开并可见
2. 检查控制台过滤器设置，确保显示所有日志
3. 尝试清空控制台后重新点击按钮
4. 确认应用正在调试模式下运行

### 如果输出格式不正确
1. 检查代码是否正确编译
2. 确认没有其他错误影响功能执行
3. 重新构建并运行应用

### 如果提示词内容不符合预期
1. 检查对应快递公司的提示词创建方法
2. 验证地区特定规则是否正确应用
3. 确认快递公司的地区分类是否正确

## 测试建议

1. **系统性测试**: 依次测试所有支持的快递公司
2. **对比测试**: 比较不同快递公司的提示词差异
3. **地区测试**: 特别关注美国和澳洲快递的地区特定规则
4. **特殊情况测试**: 重点测试iMile的双地区支持和测试数据过滤

## 测试完成后

测试完成后，可以：
1. 截图保存控制台输出作为文档
2. 分析提示词内容，识别优化机会
3. 根据测试结果调整提示词内容
4. 更新相关文档
