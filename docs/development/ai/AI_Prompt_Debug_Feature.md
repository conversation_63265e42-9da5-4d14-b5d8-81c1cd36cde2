# AI提示词调试功能

## 功能概述

在NaviBatch扫描器界面中，当用户点击快递公司按钮时，系统会自动在控制台打印该快递公司对应的AI提示词，方便开发者调试和优化AI识别效果。

## 使用方法

1. 打开NaviBatch应用
2. 进入扫描器界面（点击扫描按钮）
3. 在快递公司选择区域，点击任意快递公司按钮
4. 查看Xcode控制台输出，会显示详细的AI提示词信息

## 控制台输出格式

```
🔍 ==================== AI提示词调试 ====================
🔍 快递类型: Amazon Flex (amazonFlex)
🔍 地区: usa
🔍 ========================================================
🔥 Firebase AI 提示词:
📄 [详细的Firebase AI提示词内容]
🔍 ========================================================
🤖 Gemma AI 提示词:
📄 [详细的Gemma AI提示词内容]
🔍 ========================================================
```

## 支持的快递公司

- **Amazon Flex**: 美国地区，专注于简单数字排序号和配送时间
- **iMile**: 支持美国和澳洲，包含测试数据过滤规则
- **LDS EPOD**: 追踪号格式 CNUSUP + 11位数字
- **PIGGY**: 支持两种追踪号格式（PG+11位数字 或 14位纯数字）
- **UNIUNI**: 追踪号格式 UUS + 16位数字，三位数排序号
- **GoFo**: GoFo特定格式
- **YWE**: YWE特定格式
- **SpeedX**: 追踪号格式 SPXSF + 14位数字，支持智能地址分离
- **通用识别**: 适用于各种快递应用界面

## 地区特定规则

### 美国地址格式
- 格式: "Number Street, City, State, Zipcode"
- 示例: "1721 Marina Court, San Mateo, CA, 94403"
- 州名缩写: CA, NY, TX, FL等
- 邮编: 5位数字或5+4位格式

### 澳洲地址格式
- 格式: "Number Street, Suburb, State, Postcode"
- 示例: "123 Collins Street, Melbourne, VIC, 3000"
- 州名缩写: VIC, NSW, QLD, WA, SA, TAS, NT, ACT
- 邮编: 4位数字

## 技术实现

### 核心方法
- `printAIPromptForAppType(_:)`: 主要的调试方法
- `createFirebaseAIPromptForDebug(appType:)`: 创建Firebase AI提示词
- `createGemmaPromptForDebug(appType:)`: 创建Gemma AI提示词

### 提示词构建流程
1. 根据快递类型选择基础提示词
2. 添加地区特定规则
3. 添加通用JSON格式要求
4. 输出完整提示词到控制台

## 调试用途

这个功能主要用于：
1. **提示词优化**: 查看当前提示词内容，识别改进点
2. **问题排查**: 当AI识别效果不佳时，检查提示词是否合适
3. **新快递公司集成**: 为新的快递公司设计合适的提示词
4. **地区适配**: 确保不同地区的地址格式规则正确

## 注意事项

- 此功能仅在开发环境中使用，不会影响生产环境
- 控制台输出可能包含大量文本，建议使用Xcode的搜索功能定位相关日志
- 提示词内容会根据快递公司和地区动态生成
- 输出的提示词与实际AI服务使用的提示词完全一致

## 更新日期

2025-06-23: 初始版本，支持所有当前快递公司的提示词调试
