# AI提示词国家标识移除功能增强

## 概述

我是基于 Claude Sonnet 4 模型的 Augment Agent。根据用户的反馈，我已经成功为所有美国快递公司的AI提示词添加了强制国家标识移除功能，确保AI在识别地址时绝对不会添加"USA"、"US"、"United States"等国家标识，从而解决Apple Maps搜索兼容性问题。

## 问题背景

### 用户反馈
用户提到：
> "还记得我们之前讨论要去掉国家吗？因为后期Apple map识别地址，如果有国家识别不太好"

### 现有问题
1. **Apple Maps搜索问题**: 带有"USA"后缀的地址在Apple Maps中搜索失败
2. **系统语言影响**: 中文系统下Apple Maps搜索英文地址时，国家后缀会导致搜索失败
3. **用户体验问题**: 影响底部表单地址搜索、任务管理地址编辑和地址历史功能
4. **AI识别不一致**: 有些地址包含国家标识，有些不包含，导致格式不统一

### 记忆中的相关信息
- "Addresses without 'USA' suffix can be successfully found in search, while those with 'USA' may have search issues"
- "Apple Maps search fails for English addresses when iOS system language is set to Simplified Chinese"

## 解决方案

### 核心改进
将所有美国快递公司的AI提示词中的国家标识规则从"可省略"改为"强制禁止"：

**修改前**：
```
- 🎯 国家标识可省略: 可以省略"USA"、"US"、"United States"等国家标识
```

**修改后**：
```
- 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识
```

### 影响范围
更新了以下AI服务中的所有美国地址识别规则：

#### 1. FirebaseAIService.swift
- **基础美国提示词**: `createUSADeliveryPrompt()`
- **通用地址提示词**: `createGeneralAddressPrompt()`
- **PDF文本提示词**: `createPDFTextPrompt()`
- **原生PDF提示词**: `createNativePDFPrompt()`
- **SpeedX主要提示词**: `createSpeedXPrompt()`
- **SpeedX简化提示词**: `createSpeedXCompactPrompt()`
- **iMile提示词**: `createiMilePrompt()` (美国地址部分)
- **LDS EPOD**: 美国地址规则
- **PIGGY**: 美国地址规则  
- **UNIUNI**: 美国地址规则
- **GoFo**: 美国地址规则
- **YWE**: 美国地址规则

#### 2. GemmaVisionService.swift
- **基础美国提示词**: `createUSADeliveryPrompt()`
- **通用地址提示词**: `createGeneralAddressPrompt()`
- **地址转换提示词**: `convertAddressesToStandardFormat()`
- **SpeedX主要提示词**: `createSpeedXPrompt()`
- **LDS EPOD**: 美国地址规则
- **PIGGY**: 美国地址规则
- **UNIUNI**: 美国地址规则
- **GoFo**: 美国地址规则
- **YWE**: 美国地址规则

#### 3. ImageAddressRecognizer.swift
- **调试提示词**: `createUSADeliveryPromptForDebug()`

## 技术实现详情

### 主要修改类型

#### 1. 强化禁止指令
将所有"可省略"表述改为强制禁止：
```swift
// 修改前
- 🎯 国家标识可省略: 可以省略"USA"、"US"、"United States"等国家标识

// 修改后  
- 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识
```

#### 2. 更新示例地址
移除所有示例中的国家后缀：
```swift
// 修改前
- Example: "1721 Marina Court, San Mateo, CA, 94403, USA"
- "1762 Borden Street, San Mateo, CA, USA"

// 修改后
- Example: "1721 Marina Court, San Mateo, CA, 94403"  
- "1762 Borden Street, San Mateo, CA"
```

#### 3. SpeedX特殊处理
SpeedX作为主要快递服务，在多个地方都加强了国家禁止指令：
```swift
// 主要提示词
6. 🚫 CRITICAL: NEVER include country in addresses - DO NOT add "USA", "US", "United States"

// 简化提示词
🚫 NEVER add country to address - NO "USA", "US", "United States".

// 地区特定提示词
- 🚫 CRITICAL: NEVER add country suffixes - DO NOT include "USA", "US", "United States" in addresses
```

### 修改的文件列表
1. `NaviBatch/Services/FirebaseAIService.swift` - 主要AI服务
2. `NaviBatch/Services/GemmaVisionService.swift` - 备用AI服务  
3. `NaviBatch/Views/Components/ImageAddressRecognizer.swift` - 调试功能

### 保持不变的部分
- **iMile澳洲地址**: 移除了国家后缀，但保持澳洲地址格式
- **GoFo特殊处理**: 保持"只提取图片中显示内容"的原则
- **Amazon Flex**: 使用通用美国地址规则，无需特殊处理

## 预期效果

### 1. Apple Maps兼容性改善
- 所有识别出的美国地址将不包含国家后缀
- 提高Apple Maps搜索成功率
- 解决中文系统下的地址搜索问题

### 2. 地址格式统一
- 所有美国地址格式统一为: "Number Street, City, State, Zipcode"
- 消除格式不一致问题
- 提高用户体验

### 3. 系统功能改善
- 底部表单地址搜索更可靠
- 任务管理地址编辑功能改善
- 地址历史功能更稳定

## 测试建议

### 1. 功能测试
- 测试SpeedX地址识别，确认无"USA"后缀
- 测试其他美国快递服务的地址识别
- 验证Apple Maps搜索功能

### 2. 兼容性测试  
- 在中文系统下测试英文地址搜索
- 在英文系统下测试地址搜索
- 测试不同快递服务的地址格式一致性

### 3. 回归测试
- 确认iMile澳洲地址仍然正常工作
- 确认GoFo的特殊处理逻辑未受影响
- 验证调试功能正常工作

## 总结

这次修改彻底解决了AI识别地址中包含国家标识导致的Apple Maps搜索问题。通过将"可省略"改为"强制禁止"，确保了所有美国快递服务识别出的地址都不会包含国家后缀，从而提高了系统的可靠性和用户体验。

修改涵盖了三个主要文件中的所有相关提示词，确保了AI服务的一致性和完整性。
