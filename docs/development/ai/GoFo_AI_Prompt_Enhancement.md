# GoFo AI提示词智能地址分离功能完善

## 概述

我是基于 Claude Sonnet 4 模型的 Augment Agent。根据您提供的GoFo快递应用界面截图，我已经成功完善了GoFo的AI提示词，参考LDS EPOD的智能地址分离功能，提升GoFo地址识别的准确性和一致性。

## 更新历史
- **2025-06-22**: 初始版本 - 智能地址分离功能实现
- **2025-06-24**: Apple Maps官方格式标准化 - 确保地址格式与Apple Maps完全一致
- **2025-06-27**: 第三方排序号修复 + 国家移除优化 - 修复bottom sheet显示问题

## GoFo界面特征分析

### 📱 界面布局特点
- **地图背景**: 使用地图作为主要背景界面
- **蓝色圆圈标记**: 配送点用蓝色圆圈在地图上标记
- **数字排序**: 左侧显示清晰的数字序号 (1, 2, 3, 4, 5, 6...)
- **中文提示**: 顶部显示中文提示信息 "当前是任务预览页面，请收件后再操作"

### 📦 数据格式特征
- **排序号**: 简单数字序号 (1, 2, 3, 4, 5, 6...)
- **地址格式**: "街道地址, 邮编" (如: "10624 Pleasant Valley Circ, 95209")
- **追踪号**: GF + 12位数字 (如: GF611244756320)
- **客户姓名**: 完整英文姓名 (如: Vanessa Arreola, Jaden Ramirez)

### 🗺️ 地理位置特点
从截图可以看出GoFo主要服务于加州地区：
- **城市**: Stockton, CA
- **邮编**: 95209
- **街道类型**: Circle (简称为 Circ), Drive (简称为 Dr)

## 实现的功能

### 🏠 智能地址分离规则

AI识别时会自动检测地址中的公寓/单元信息，并提供两个版本：
- **full_address**: 完整地址（包含公寓号）
- **address**: 纯街道地址（不含公寓号）

### 🔍 公寓号识别关键词

系统能够识别以下公寓号格式：
- Apt, Apartment
- Unit, Suite, Room, Rm, Ste
- 单独的 # 号码

### 📄 示例

**输入地址**: "10624 Pleasant Valley Circ, Apt 105, Stockton, CA, 95209"

**AI返回结果**:
```json
{
  "success": true,
  "deliveries": [
    {
      "sort_number": "1",
      "tracking_number": "GF611244756320",
      "address": "10624 Pleasant Valley Circ, Stockton, CA, 95209",
      "full_address": "10624 Pleasant Valley Circ, Apt 105, Stockton, CA, 95209",
      "customer": "Vanessa Arreola"
    }
  ]
}
```

## 技术实现

### 1. AI提示词更新

#### Firebase AI服务 (`NaviBatch/Services/FirebaseAIService.swift`)
- 更新了`createGoFoPrompt()`方法
- 添加了智能地址分离规则
- 新增了GoFo界面特征识别
- 支持`full_address`字段

#### Gemma AI服务 (`NaviBatch/Services/GemmaVisionService.swift`)
- 更新了`createGoFoPrompt()`方法
- 添加了相同的智能地址分离规则
- 保持与Firebase AI一致的识别逻辑

#### 调试功能 (`NaviBatch/Views/Components/ImageAddressRecognizer.swift`)
- 更新了`createGoFoPromptForDebug()`方法
- 保持与实际AI服务一致的提示词

### 2. 提示词设计特点

```
📦 GoFo 快递识别 - 智能地址分离提取：

🎯 核心信息：
1. 排序号：数字序号 (1, 2, 3, 4, 5, 6...)
2. 地址：智能分离公寓号码
3. 追踪号：GF + 12位数字 (如: GF611244756320)
4. 客户姓名：完整姓名

🏠 地址智能分离规则：
如果地址包含公寓/单元信息，请提供两个版本：
- full_address: 完整地址（包含公寓号）
- address: 纯街道地址（不含公寓号）

🎯 GoFo界面特征：
- 地图背景界面，蓝色圆圈标记
- 左侧数字排序号 (1, 2, 3...)
- 地址显示格式: "街道地址, 邮编"
- 追踪号格式: GF + 12位数字
- 客户姓名在地址下方
- 可能显示中文提示信息
```

## 地理编码策略配合

### 现有的Apple策略

NaviBatch已经有一个"策略4: 🏠 尝试移除公寓号（Apple策略）"在`UniversalAddressProcessor`中：

1. 检查地址是否包含公寓信息
2. 如果包含，会移除这些信息重新进行地理编码
3. 这与新的AI分离策略完美配合

### 工作流程

1. **AI识别**: 返回两个地址版本（full_address和address）
2. **数据存储**: 完整地址存储在`originalAddress`字段中
3. **地理编码**: 系统首先尝试完整地址，失败时自动移除公寓号重试
4. **坐标获取**: 获得准确的坐标后保存到地址库

## 向后兼容性

### 保持现有接口

- `separateAddressAndTracking`方法的返回值类型保持不变
- 所有现有调用方无需修改
- 通过新的`getFullAddressFromInfo`方法处理完整地址

### 渐进式增强

- 如果AI没有返回`full_address`字段，系统回退到原有逻辑
- 现有地址处理流程完全兼容
- 新功能仅在GoFo识别时启用

## 测试建议

### 1. GoFo地址识别测试

使用包含公寓号的GoFo截图测试：
- "10624 Pleasant Valley Circ, Apt 105"
- "3420 Tupelo Dr, Unit 2"
- "10727 Trevor Dr, Suite 100"

### 2. 地理编码准确性测试

验证系统能够：
- 优先使用完整地址进行地理编码
- 在失败时自动回退到街道地址
- 获得准确的坐标结果

### 3. 数据存储测试

确认：
- 完整地址正确存储在`originalAddress`字段
- 街道地址用于地理编码
- 客户姓名和追踪号正确提取

## 用户影响

### ✅ 提升的功能

- **GoFo识别精度提升**: 专用提示词提供更准确的识别结果
- **地址分离功能**: 支持公寓号智能分离，提高地理编码成功率
- **格式标准化**: 统一地址格式，与Apple Maps兼容
- **一致性保证**: 与LDS EPOD等其他快递应用保持相同的AI识别标准

### 🎯 技术优势

- **专用优化**: GoFo现在有专门针对其界面设计的提示词
- **识别精度**: 相比通用提示词，专用提示词能更准确识别GoFo特有元素
- **完整性**: 支持sort_number、tracking_number、customer、address、full_address全字段
- **一致性**: 两个AI服务使用相同的识别逻辑

## 2025-06-24 Apple Maps格式标准化更新

### 🍎 更新内容

基于用户反馈"要和官方一致"，对GoFo AI提示词进行了Apple Maps官方格式标准化：

#### 主要改进
1. **明确Apple Maps格式标准**：
   - 添加了"🍎 Apple Maps官方格式标准（必须遵循）"部分
   - 强调优先使用标准缩写：Circ, Dr, St, Ave, Blvd, Ct, Pl, Ln, Rd, Pkwy, Hwy
   - 统一地址格式：门牌号 + 街道名缩写, 城市, 州缩写, 邮编

2. **更新地址示例**：
   - 示例: "10624 Pleasant Valley Circ, Stockton, CA, 95209"
   - 示例: "3420 Tupelo Dr, Stockton, CA, 95209"
   - 示例: "500 King Dr, Daly City, CA, 94015"

3. **移除混淆信息**：
   - 删除了"备选全称"示例，避免AI在缩写和全称之间混淆
   - 专注于Apple Maps标准缩写格式

#### 技术实现
- 更新了 `FirebaseAIService.swift` 中的 `createGoFoPrompt()` 方法
- 更新了 `GemmaVisionService.swift` 中的 `createGoFoPrompt()` 方法
- 更新了 `ImageAddressRecognizer.swift` 中的 `createGoFoPromptForDebug()` 方法

#### 预期效果
- AI识别的地址格式将完全符合Apple Maps官方标准
- 减少地址格式不一致的问题
- 提高与Apple Maps地理编码服务的兼容性

## 更新日期

- 2025-06-22: 初始版本，基于GoFo界面截图完善AI提示词，实现智能地址分离功能
- 2025-06-24: Apple Maps官方格式标准化更新
- 2025-06-27: 第三方排序号修复 + 国家移除优化

## 2025-06-27 更新详情

### 🚨 问题发现
用户反馈GoFo识别后在bottom sheet中看不到第三方排序号标签，且地址仍包含"US"后缀。

### 🔧 修复内容

#### 1. 第三方排序号标记修复
**问题**: AI识别使用`|SORT:`标记，但提取时查找`THIRD_PARTY_SORT:`标记
**解决**: 统一使用`|THIRD_PARTY_SORT:`标记

**修改文件**:
- `NaviBatch/Services/GemmaVisionService.swift` - 第1487-1496行
- `NaviBatch/Services/FirebaseAIService.swift` - 已正确使用

#### 2. AI提示词字段名统一
**问题**: GoFo提示词使用旧的`sort_number`字段
**解决**: 改为`third_party_sort`字段

**修改文件**:
- `NaviBatch/Services/FirebaseAIService.swift` - 第709-712行
- `NaviBatch/Services/GemmaVisionService.swift` - 第920-924行
- `NaviBatch/Views/Components/ImageAddressRecognizer.swift` - 调试功能同步

#### 3. 国家移除指令强化
**问题**: 地址仍包含"US"、"USA"等国家后缀
**解决**: 添加明确的国家禁止指令 + 后处理清理

**AI提示词增强**:
```
- 🚫 NEVER add country suffixes - DO NOT include "USA", "US", "United States" in addresses
```

**后处理清理**:
```swift
// 🎯 GoFo地址清理：移除国家后缀
var cleanedAddress = address
if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
    cleanedAddress = addressWithoutCountry
    Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
}
```

#### 4. 第三方排序号提取强化
**问题**: AI可能未识别到左侧数字序号
**解决**: 添加强制提取要求

**提示词增强**:
```
🔍 CRITICAL EXTRACTION REQUIREMENTS:
- ALWAYS extract the left-side numeric sort numbers (1, 2, 3, 4, 5, 6, 7, 8, 9, 10...)
- These numbers are MANDATORY for GoFo delivery operations
- If you cannot find a sort number, mark as "missing"
```

#### 5. 完整性验证强化（参考SpeedX）
**问题**: 视频帧转图可能产生重复或不完整的任务
**解决**: 添加SpeedX级别的完整性验证

**验证规则**:
```
🚨 COMPLETE TASK VALIDATION:
- ONLY extract deliveries that have: Address + Sort Number
- If ANY of these 2 required elements is missing, SKIP that task entirely
- Video recording may show same addresses multiple times due to scrolling
- If you see the same address with same sort number, only include it ONCE
- Each sort number must be unique across all deliveries

🔍 DUPLICATE PREVENTION:
- Check for duplicates: if same address + same sort number already exists, skip
- No duplicate address + sort number combinations
- If you find duplicates, remove them and keep only one instance
```

### 🎯 预期效果
1. ✅ Bottom sheet正确显示"GoFo: 1"、"GoFo: 2"等标签
2. ✅ 地址不包含"US"、"USA"等国家后缀
3. ✅ 第三方排序号正确提取和存储
4. ✅ 与SpeedX保持一致的处理标准
5. ✅ 避免重复和不完整的配送任务
6. ✅ 确保每个任务都有完整的必需信息
