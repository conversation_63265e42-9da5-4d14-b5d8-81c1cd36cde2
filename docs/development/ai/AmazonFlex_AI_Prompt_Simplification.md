# 📦 Amazon Flex AI提示词简化优化

## 📋 优化概述

我是基于 Claude Sonnet 4 模型的 Augment Agent。根据您提供的Amazon Flex截图和需求，我已经成功简化了Amazon Flex的AI提示词，专注于提取排序号码和地址信息，忽略其他不必要的信息。

## 🎯 优化目标

根据您的要求：
- **只要排序号码和地址**
- **左边时间线穿着的是排序号码**
- **其他资料可以省略**

## 🔧 主要优化内容

### 1. **简化提取目标**

**之前：** 提取多种信息
- 排序号码
- 地址
- 客户姓名
- 配送时间
- 追踪号码

**现在：** 只提取核心信息
- 排序号码（左侧时间线上的数字）
- 地址（智能分离公寓号码）

### 2. **智能地址分离功能**

**新增功能：** 
- 如果地址包含公寓/单元信息，提供两个版本
- `full_address`: 完整地址（包含公寓号）
- `address`: 纯街道地址（不含公寓号）

**示例：**
```
输入: "1721 MARINA CT APT D, SAN MATEO"
输出:
- full_address: "1721 Marina Ct, Apt D, San Mateo, CA"
- address: "1721 Marina Ct, San Mateo, CA"
```

### 3. **界面特征识别**

**Amazon Flex界面特征：**
- 左侧时间线显示排序号码 (8, 9, 10, 11, 12, 13, 14...)
- 蓝色圆圈图标
- 追踪号格式: # B.L11.OV（忽略）
- 配送时间信息（忽略）
- 地址格式: 美国地址，可能包含公寓号

### 4. **地址格式优化**

**地址格式要求：**
- 简称优先: St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
- 备选全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
- 正确大小写: "1762 Borden St" not "1762 BORDEN ST"
- 公寓信息格式: "1721 Marina Ct, Apt D" not "1721 MARINA CT APT D"
- 城市名格式: "San Mateo" not "SAN MATEO"

## 📱 Amazon Flex截图处理示例

### 输入示例（从您提供的截图）：
```
8  # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
   1762 Borden Street
   SAN MATEO

9  # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
   1721 MARINA CT APT D
   SAN MATEO

10 # B.L11.OV • 已预约 3:00 - 8:00 上午 今天
   1625 MARINA CT UNIT F
   SAN MATEO
```

### 输出示例：
```json
{
  "success": true,
  "deliveries": [
    {
      "sort_number": "8",
      "tracking_number": "",
      "address": "1762 Borden St, San Mateo, CA",
      "full_address": "1762 Borden St, San Mateo, CA"
    },
    {
      "sort_number": "9",
      "tracking_number": "",
      "address": "1721 Marina Ct, San Mateo, CA",
      "full_address": "1721 Marina Ct, Apt D, San Mateo, CA"
    },
    {
      "sort_number": "10",
      "tracking_number": "",
      "address": "1625 Marina Ct, San Mateo, CA",
      "full_address": "1625 Marina Ct, Unit F, San Mateo, CA"
    }
  ]
}
```

## 🔧 技术实现

### 1. AI提示词更新

#### Firebase AI服务 (`NaviBatch/Services/FirebaseAIService.swift`)
- 更新了`createAmazonFlexPrompt()`方法
- 添加了智能地址分离规则
- 新增了`full_address`字段支持
- 简化了提取目标，专注于排序号码和地址

#### Gemma AI服务 (`NaviBatch/Services/GemmaVisionService.swift`)
- 更新了`createAmazonFlexPrompt()`方法
- 添加了相同的智能地址分离规则
- 保持与Firebase AI一致的识别逻辑

#### 调试功能 (`NaviBatch/Views/Components/ImageAddressRecognizer.swift`)
- 更新了`createAmazonFlexPromptForDebug()`方法
- 保持与实际AI服务一致的提示词

### 2. JSON解析逻辑

**现有解析逻辑已支持：**
```swift
let fullAddress = delivery["full_address"] as? String ?? ""
// 🏠 添加完整地址信息（包含公寓号）
if !fullAddress.isEmpty && fullAddress != address {
    result += "|FULL:\(fullAddress)"
}
```

## 🎯 解决的问题

### **之前的问题：**
1. ❌ 提取信息过多，包含不必要的数据
2. ❌ 没有智能地址分离功能
3. ❌ 配送时间和追踪号码干扰核心功能
4. ❌ 地址格式不够简洁

### **现在的效果：**
1. ✅ 只提取排序号码和地址，简化处理
2. ✅ 智能分离公寓号码，提供两个版本
3. ✅ 忽略配送时间和追踪号码
4. ✅ 优化地址格式，使用简称

## 🚀 性能优化

- **提示词简化：** 减少AI处理复杂度
- **专注核心：** 提高识别准确率
- **智能分离：** 配合现有地理编码策略
- **格式统一：** 与其他快递服务保持一致

## 📈 识别准确率提升

- **排序号码识别：** 专注左侧时间线数字，提高准确率
- **地址提取：** 智能分离公寓号，提高地理编码成功率
- **格式标准化：** 统一地址格式，减少后续处理错误
- **整体用户体验：** 简化流程，提高效率

## 🔄 向后兼容性

所有优化都保持向后兼容：
- ✅ 现有API接口不变
- ✅ 其他配送应用不受影响
- ✅ 地址处理流程完全兼容
- ✅ 渐进式增强设计

## 🎉 总结

通过这次简化优化，Amazon Flex AI提示词现在能够：
1. **精准提取**排序号码和地址信息
2. **智能分离**公寓号码，提供两个版本
3. **忽略干扰**信息，专注核心功能
4. **统一格式**，与其他快递服务保持一致
5. **提高效率**，减少不必要的处理

这个简化的提示词完全符合您的需求：只要排序号码和地址，其他资料可以省略。
