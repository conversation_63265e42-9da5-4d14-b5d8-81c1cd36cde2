# 配送应用类型优化文档

## 📋 优化概述

根据用户需求，我们将配送应用类型选择器进行了重大优化，从原来的单一列表改为按地区分组的方式，使AI提示词更加精准和高效。

## 🎯 优化目标

1. **简化分类结构**：按地区（美国/澳洲）+ 快递类型分组
2. **精准AI提示词**：根据地区和快递类型自动调整提示词
3. **更好的用户体验**：清晰的地区分组界面

## 🔄 主要变更

### 1. 数据模型优化 (`DeliveryAppType.swift`)

#### 新增地区分类枚举
```swift
enum DeliveryRegion: String, CaseIterable {
    case usa = "usa"           // 美国快递
    case australia = "australia"  // 澳洲快递
    case universal = "universal"  // 通用类型
}
```

#### 新增地区属性和方法
- `var region: DeliveryRegion` - 获取应用类型的地区分类
- `static func courierTypesForRegion(_ region: DeliveryRegion) -> [DeliveryAppType]` - 获取地区内的快递公司

### 2. UI界面优化 (`ImageAddressRecognizer.swift`)

#### 新的分组选择器
- **美国快递组**：Just Photo + Amazon Flex + iMile + GoFo + LDS EPOD + PIGGY + UNIUNI + YWE + SpeedX
- **澳洲快递组**：Just Photo + iMile

#### 界面特点
- 按地区分组显示，每组有不同的颜色主题
- 美国快递使用橙色主题 🟠
- 澳洲快递使用蓝色主题 🔵
- 每组都包含Just Photo选项，方便用户选择

### 3. AI提示词优化

#### 地区特定提示词结构
```
基础提示词 + 地区规则 + 快递特征
```

#### 美国快递提示词特征 🇺🇸
- **地址格式**：`"Number Street, City, State, Zipcode"`
- **示例**：`"1721 Marina Court, San Mateo, CA, 94403"`
- **重点**：美国州名缩写识别 (CA, NY, TX, FL等)
- **邮编**：5位数字或5+4位格式

#### 澳洲快递提示词特征 🇦🇺
- **地址格式**：`"Unit/Number Street, Suburb, State, Postcode"`
- **示例**：`"58/15 Fulham Road, Rowville, Victoria, 3178"`
- **重点**：澳洲州名识别 (Victoria, NSW, QLD, WA, SA等)
- **邮编**：4位数字

#### 快递特定特征
每个快递公司都有专门的特征识别：
- **Amazon Flex**：简单数字排序号 + 配送时间段
- **iMile**：多地区服务，自动识别美国/澳洲地址格式
- **GoFo**：GoFo追踪号 + 配送时间（美国快递排第一）
- **LDS EPOD**：CNUSUP追踪号 + 连续序号
- **PIGGY**：PG追踪号 + 连续序号
- **UNIUNI**：UUS追踪号 + 三位数排序号
- **YWE**：YWE追踪号 + 收件人信息
- **SpeedX**：SPXSF追踪号(14位数字) + 停靠点编号 + 智能地址分离

## 🚀 技术实现

### 1. 服务层更新
- `FirebaseAIService.swift` - 更新AI提示词生成逻辑
- `GemmaVisionService.swift` - 同步更新提示词逻辑

### 2. 提示词生成流程
```swift
private func createRegionSpecificPrompt(appType: DeliveryAppType) -> String {
    switch appType.region {
    case .usa:
        return createUSADeliveryPrompt(appType: appType)
    case .australia:
        return createAustraliaDeliveryPrompt(appType: appType)
    case .universal:
        return createUniversalPrompt(appType: appType)
    }
}
```

## 📊 优化效果

### 用户体验改进
1. **更清晰的分类**：用户可以快速找到对应地区的快递选项
2. **减少选择困难**：从9个选项减少到2个地区组
3. **更直观的界面**：颜色区分不同地区

### AI识别精度提升
1. **地区特定规则**：针对美国/澳洲地址格式优化
2. **快递特征识别**：每个快递公司的特定特征更精准
3. **减少误识别**：地区规则避免格式混淆

## 🔧 使用方法

### 用户操作流程
1. 打开扫描器界面
2. 选择地区分组（美国快递/澳洲快递）
3. 在对应地区选择具体快递公司或Just Photo
4. 上传图片进行识别

### 开发者扩展
如需添加新的快递公司：
1. 在`DeliveryAppType`枚举中添加新类型
2. 在对应的地区提示词方法中添加特征描述
3. 更新UI界面的应用列表

## 📝 注意事项

1. **Just Photo选项**：在每个地区组都有，适用于通用图片识别
2. **地区规则优先**：AI会优先按地区规则处理地址格式
3. **向后兼容**：现有的快递类型标识符保持不变
4. **测试验证**：建议使用不同地区的快递截图进行测试

## 🎉 总结

这次优化大大简化了用户的选择流程，同时提高了AI识别的精度。通过地区分组和特征化提示词，NaviBatch现在能够更准确地识别不同地区和快递公司的地址信息。

---
*更新时间：2024年12月23日*
*版本：NaviBatch v1.0.8*
