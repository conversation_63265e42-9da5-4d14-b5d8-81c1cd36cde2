# NaviBatch App Store 截图指南

## 📱 设备尺寸要求

### iPhone 截图尺寸
- **iPhone 6.7英寸** (iPhone 15 Pro Max, 16 Pro Max): 1320 x 2868 像素
- **iPhone 6.1英寸** (iPhone 15 Pro, 16 Pro): 1206 x 2622 像素  
- **iPhone 5.5英寸** (iPhone 8 Plus): 1242 x 2208 像素

## 📂 目录结构

```
AppStore_Screenshots/
├── iPhone_6.7_inch/          # iPhone Pro Max 截图
├── iPhone_6.1_inch/          # iPhone Pro 截图 (当前)
├── iPhone_5.5_inch/          # iPhone Plus 截图
└── README.md                 # 本文件
```

## 🎯 截图命名规范

每个设备目录下应包含以下截图（按App Store显示顺序）：

1. `01_main_screen.png` - 主界面/路线列表
2. `02_route_planning.png` - 路线规划界面
3. `03_address_input.png` - 地址输入界面
4. `04_map_view.png` - 地图视图
5. `05_optimization.png` - 路线优化界面
6. `06_navigation.png` - 导航界面
7. `07_delivery_tracking.png` - 配送跟踪
8. `08_subscription.png` - 订阅界面（可选）

## 📋 App Store 要求

- **最少截图数量**: 3张
- **最多截图数量**: 10张
- **推荐数量**: 5-7张
- **格式**: PNG 或 JPEG
- **颜色空间**: sRGB 或 P3

## 🎨 截图内容建议

1. **主界面** - 展示应用的核心功能
2. **地址输入** - 展示智能地址自动完成功能
3. **路线优化** - 展示优化前后的对比
4. **地图视图** - 展示清晰的路线规划
5. **配送功能** - 展示配送跟踪和确认功能

## 📝 注意事项

- 确保截图内容清晰、有代表性
- 避免显示个人信息或真实地址
- 使用测试数据，确保界面美观
- 截图应展示应用的核心价值