# NaviBatch 文档中心

NaviBatch是一个智能批量导航应用，专为快递员、外卖员等需要多点配送的用户设计。

## 📁 文档结构

### 🔧 开发文档 (`development/`)
- **AI相关** (`ai/`) - AI集成、提示优化、Firebase AI服务
- **地址处理** (`address/`) - 地址解析、地理编码、验证服务
- **性能优化** (`performance/`) - 内存管理、API限制、优化策略
- **用户界面** (`ui/`) - UI组件、深色模式、扫描器界面
- **测试调试** (`testing/`) - 测试指南、调试工具、问题排查

### 🔨 修复记录 (`fixes/`)
- **SpeedX修复** (`speedx/`) - SpeedX平台相关问题修复
- **本地化修复** (`localization/`) - 多语言支持、中文处理
- **地理编码修复** (`geocoding/`) - 地址解析、坐标转换问题
- **UI修复** (`ui/`) - 界面问题、交互优化

### 📱 发布管理 (`releases/`)
- **发布说明** (`notes/`) - 版本更新记录
- **App Store** (`app-store/`) - 应用商店相关文档

### 📢 营销推广 (`marketing/`)
- **推广策略** (`promotion/`) - 营销计划、内容策略
- **应用截图** (`screenshots/`) - App Store截图、宣传材料

### 📖 用户指南 (`guides/`)
- 隐私政策
- 使用条款
- 用户手册

### 🔌 API文档 (`api/`)
- API接口说明
- 集成指南

## 🚀 快速开始

1. **开发者**: 查看 `development/` 目录了解技术实现
2. **测试人员**: 参考 `development/testing/` 中的测试指南
3. **产品经理**: 查看 `releases/` 了解版本规划
4. **营销团队**: 参考 `marketing/` 中的推广材料

## 📋 最新更新

- 文档结构重新整理，按功能分类
- 修复记录统一管理
- 开发文档细分专业领域

## 🔗 相关链接

- [项目主页](../README.md)
- [开发日志](development/DEVELOPMENT_LOG.md)
- [API文档](api/)
- [用户指南](guides/)

---

*最后更新: 2025-08-04*
