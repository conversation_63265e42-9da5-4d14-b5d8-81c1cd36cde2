# 地理编码一致性修复

## 问题描述

用户反馈在编辑地址的bottom sheet中搜索地址时，某些地址无法找到结果，但AI识别时却能成功获得坐标。

**具体案例：**
- 地址：`500 King Drive, Apt 105, Daly City, 94015`
- 编辑界面：显示"未找到有效地址"
- AI识别：成功获得坐标 `(37.658013, -122.432541)`
- Apple Maps：搜索 `500 King Dr` 能找到结果

## 根本原因

苹果和AI部分获取坐标的逻辑不一致：

### 1. 编辑地址界面（修改前）
- 使用 `EnhancedAddressAutocomplete` 组件
- 调用 `LightweightAddressProcessor.shared.processAddress()`
- 只有基础的地理编码功能，容错能力有限
- 超时时间短（2秒），重试策略简单

### 2. AI识别时
- 使用 `UniversalAddressProcessor.shared.processGlobalAddress()`
- 有7种不同的处理策略：
  1. 原始地址优先策略
  2. 轻微清理后重试
  3. 智能地址简化策略
  4. 尝试简化门牌号（处理 145c -> 145 的情况）
  5. 使用MKLocalSearch（更适合POI和模糊搜索）
  6. 智能地址增强
  7. 最后的模糊匹配尝试

## 解决方案

将编辑地址界面的地理编码逻辑统一为使用 `UniversalAddressProcessor`，确保与AI识别保持一致。

### 修改内容

**文件：** `NaviBatch/Views/Components/EnhancedAddressAutocomplete.swift`

**修改前：**
```swift
// 🚀 使用轻量级地址处理器，避免UniversalAddressProcessor的复杂逻辑
let lightweightResult = await LightweightAddressProcessor.shared.processAddress(fullAddress)

switch lightweightResult {
case .success(_, let formattedAddress, let coordinate, _):
    // 处理成功结果
case .failed(let reason):
    // 处理失败，降级到原有逻辑
}
```

**修改后：**
```swift
// 🌍 使用UniversalAddressProcessor，与AI识别保持一致的逻辑
let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(fullAddress)

switch globalResult {
case .success(_, let formattedAddress, let coordinate, _, let strategy, _):
    Logger.info("🌍 全球地址处理成功: \(formattedAddress) (策略: \(strategy))")
    // 处理成功结果
case .failed(_, let reason):
    Logger.warning("🌍 全球地址处理失败: \(reason)，降级到原有逻辑")
    // 处理失败，降级到原有逻辑
}
```

## 预期效果

1. **提高地址识别成功率**：编辑界面现在使用与AI相同的强大地理编码逻辑
2. **保持一致性**：用户在不同界面中搜索相同地址会得到一致的结果
3. **更好的容错能力**：能处理各种地址格式变化，如缩写、格式差异等
4. **保留降级机制**：如果全球处理器失败，仍会降级到原有的搜索逻辑

## 测试建议

1. 在编辑地址界面搜索 `500 King Drive, Apt 105, Daly City, 94015`
2. 验证是否能找到有效地址并获得坐标
3. 对比AI识别的结果，确保坐标一致
4. 测试其他复杂地址格式，验证容错能力

## 🚨 **重大问题修复：公寓号导致地址串改**

### 发现的严重问题
从用户日志发现，地址被严重"串改"：
- **输入地址**：`500 King Drive, Apt 105, Daly City, 94015`
- **返回地址**：`500 Daly Ct, South San Francisco, CA, 94080`
- **问题**：完全不同的街道、城市和邮编！

### 根本原因
1. **公寓号干扰**：`Apt 105` 等公寓信息会严重干扰Apple的地理编码服务
2. **街道匹配逻辑过于宽松**：原有的 `isWesternStreetMatching` 方法存在严重缺陷
3. **城市验证不够严格**：城市不匹配时没有足够的惩罚机制

### 修复措施

#### 1. 新增公寓号移除策略
```swift
/// 策略4: 🏠 尝试移除公寓号（Apple策略）
private func tryRemoveApartmentInfo(_ address: String) async -> GlobalGeocodingResult? {
    // 检测并移除：Apt, Unit, Suite, Room, # 等公寓信息
    let apartmentPatterns = [
        "\\b(apt|apartment)\\s*[#:]?\\s*\\w+",
        "\\b(unit|ste|suite)\\s*[#:]?\\s*\\w+",
        "\\b(room|rm)\\s*[#:]?\\s*\\w+",
        "\\b#\\s*\\w+"
    ]
    // 移除公寓信息后重新地理编码
}
```

#### 2. 严格化街道匹配逻辑
```swift
/// 修复前：过于宽松的匹配
if original.contains(placemark) || placemark.contains(original) {
    return true  // ❌ 这会导致 "King Dr" 匹配 "Daly Ct"
}

/// 修复后：严格的核心匹配
let originalCore = extractStreetCore(original)  // "king"
let placemarkCore = extractStreetCore(placemark)  // "daly"
if originalCore != placemarkCore {
    return false  // ✅ 核心不匹配直接拒绝
}
```

#### 3. 增强城市验证
```swift
/// 修复前：城市不匹配只是不加分
if originalLower.contains(localityLower) {
    preservationScore += 2
}

/// 修复后：城市不匹配大幅扣分
if originalLower.contains(localityLower) {
    preservationScore += 2
} else {
    preservationScore -= 3  // 🚨 严重扣分
    Logger.warning("🚨 城市不匹配")
}
```

#### 4. 提高评分标准
```swift
/// 修复前：过于宽松的评分标准
if preservationScore >= 2 {
    return .medium  // 给西方地址更多机会
}

/// 修复后：更严格的评分标准
if preservationScore >= 1 {
    return baseConfidence
} else {
    Logger.warning("🚨 西方地址匹配度过低，拒绝结果")
    return .low  // ✅ 低匹配度直接拒绝
}
```

## 技术细节

- 保持了原有的地址库缓存机制
- 保留了降级到原有搜索逻辑的安全机制
- 增加了详细的日志记录，便于调试
- 修复了编译警告（未使用的变量）
- **新增8种处理策略**，包括公寓号移除策略
- **大幅提高地址验证的准确性**

## 影响范围

- 主要影响：编辑地址界面的搜索功能和AI地址验证
- 兼容性：完全向后兼容，不影响现有功能
- 性能：可能略微增加处理时间，但大幅提高了准确性
- **安全性**：防止地址被错误替换，避免配送错误
