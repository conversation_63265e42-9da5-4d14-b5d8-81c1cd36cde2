# NaviBatch 本地化工作流程

## 概述

本文档描述了 NaviBatch 应用的本地化工作流程，包括如何使用提供的脚本和工具来管理和维护多语言支持。

## 本地化文件结构

NaviBatch 的本地化文件存储在以下目录结构中：

```
NaviBatch/
  └── Localizations/
      ├── en.lproj/
      │   └── Localizable.strings
      ├── zh-Hans.lproj/
      │   └── Localizable.strings
      ├── ja.lproj/
      │   └── Localizable.strings
      └── ...
```

每种语言都有自己的 `.lproj` 目录，其中包含 `Localizable.strings` 文件。

## 本地化工作流程

### 1. 添加新的本地化键

当添加新功能或修改现有功能时，需要添加新的本地化键：

1. 首先在基准语言文件（英语）中添加新键：
   ```
   // 在 en.lproj/Localizable.strings 中
   "new_feature_title" = "New Feature";
   ```

2. 然后在中文文件中添加相应的翻译：
   ```
   // 在 zh-Hans.lproj/Localizable.strings 中
   "new_feature_title" = "新功能";
   ```

3. 运行更新脚本，将新键添加到所有其他语言文件中：
   ```bash
   swift Scripts/UpdateLocalizationFiles.swift
   ```

4. 运行验证脚本，确保所有语言文件包含所有键：
   ```bash
   swift Scripts/LocalizationValidator.swift
   ```

### 2. 检测硬编码文本

使用提供的脚本检测代码中的硬编码文本：

```bash
swift Scripts/LocalizationChecker.swift
```

这将扫描指定目录中的 Swift 文件，查找未本地化的硬编码文本，并提供建议的本地化键。

### 3. 更新现有翻译

要更新现有翻译：

1. 在相应的语言文件中修改翻译：
   ```
   // 在 ja.lproj/Localizable.strings 中
   "feature_title" = "新しい翻訳";
   ```

2. 运行验证脚本，确保没有引入错误：
   ```bash
   swift Scripts/LocalizationValidator.swift
   ```

### 4. 添加新语言

要添加新语言支持：

1. 在 `Localizations` 目录中创建新的语言目录：
   ```
   mkdir -p NaviBatch/Localizations/new-lang.lproj
   ```

2. 复制基准语言文件作为起点：
   ```
   cp NaviBatch/Localizations/en.lproj/Localizable.strings NaviBatch/Localizations/new-lang.lproj/
   ```

3. 在 `LocalizationManager.swift` 中添加新语言：
   ```swift
   enum Language: String, CaseIterable, Identifiable {
       // 现有语言...
       case newLang = "new-lang"
       
       // 更新其他属性...
   }
   ```

4. 翻译新语言文件中的所有键。

## 提供的脚本和工具

### 1. LocalizationValidator.swift

验证所有语言文件是否包含相同的键，并检查空值。

**用法**：
```bash
swift Scripts/LocalizationValidator.swift
```

**输出**：
- 每种语言的键数量
- 缺少的键列表
- 额外的键列表
- 空值列表

### 2. LocalizationChecker.swift

检测代码中的硬编码文本。

**用法**：
```bash
swift Scripts/LocalizationChecker.swift
```

**输出**：
- 包含硬编码文本的文件列表
- 每个文件中的硬编码文本位置和内容
- 建议的本地化键

### 3. UpdateLocalizationFiles.swift

从基准语言文件（英语）复制缺失的键到其他语言文件。

**用法**：
```bash
swift Scripts/UpdateLocalizationFiles.swift
```

**功能**：
- 读取基准语言文件中的所有键值对
- 读取每种目标语言文件中的现有键值对
- 将缺失的键添加到目标语言文件中，使用基准语言的值作为占位符
- 保留目标语言文件中的现有翻译
- 保留文件结构和注释

## 最佳实践

1. **始终使用本地化键**：不要在代码中使用硬编码文本。

   ```swift
   // 错误
   Text("Hello World")
   
   // 正确
   Text("hello_world".localized)
   ```

2. **使用有意义的键名**：键名应该描述文本的用途，而不是文本本身。

   ```
   // 好的键名
   "welcome_message" = "欢迎使用 NaviBatch！"
   
   // 不好的键名
   "welcome_to_navibatch" = "欢迎使用 NaviBatch！"
   ```

3. **按功能区域组织键**：使用 `// MARK: - 区域名称` 注释来组织键。

   ```
   // MARK: - Navigation
   "back" = "返回";
   "next" = "下一步";
   
   // MARK: - User Profile
   "profile_title" = "个人资料";
   ```

4. **定期运行验证脚本**：确保所有语言文件保持同步。

5. **在提交代码前检查硬编码文本**：使用 `LocalizationChecker.swift` 脚本。

6. **为格式化字符串提供上下文注释**：

   ```
   // 参数: %@ 是用户名
   "greeting" = "你好，%@！";
   
   // 参数: %d 是未读消息数量
   "unread_messages" = "你有 %d 条未读消息";
   ```

## 翻译流程

1. 首先在英语和中文文件中添加新键和翻译
2. 运行更新脚本将新键添加到所有其他语言文件中
3. 将其他语言文件发送给翻译人员进行翻译
4. 收到翻译后，更新相应的语言文件
5. 运行验证脚本确保所有文件都是有效的

## 常见问题解决

### 问题：本地化文本未显示

**可能的原因**：
- 键名拼写错误
- 未使用 `.localized` 扩展
- 语言文件中缺少该键

**解决方案**：
- 检查键名拼写
- 确保使用了 `.localized` 扩展
- 运行验证脚本检查键是否存在

### 问题：格式化字符串显示错误

**可能的原因**：
- 格式说明符不匹配
- 参数数量或类型不正确

**解决方案**：
- 确保使用正确的格式说明符（`%@`, `%d`, `%.1f` 等）
- 检查参数数量和类型

### 问题：从右到左语言显示问题

**可能的原因**：
- 未正确设置语言的 `isRightToLeft` 属性
- 布局约束不支持从右到左

**解决方案**：
- 在 `Language` 枚举中设置正确的 `isRightToLeft` 属性
- 使用支持从右到左的布局约束

## 联系人

如有关于本地化工作流程的问题，请联系项目负责人。
