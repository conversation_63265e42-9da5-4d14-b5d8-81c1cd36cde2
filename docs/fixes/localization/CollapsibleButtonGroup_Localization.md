# 可折叠按钮组本地化键值

## 概述

本文档列出了可折叠按钮组功能中使用的所有本地化键值，确保中英文界面的正确显示。

## 本地化键值列表

### 按钮名称
| 键值 | 中文 | 英文 |
|------|------|------|
| `collapsible_button_side_toggle` | 左右切换 | Side Toggle |
| `collapsible_button_map_mode` | 地图模式 | Map Mode |
| `collapsible_button_search` | 搜索定位 | Search Location |
| `collapsible_button_visibility` | 显示/隐藏 | Show/Hide |
| `collapsible_button_counter` | 订单计数 | Order Count |

### 按钮描述
| 键值 | 中文 | 英文 |
|------|------|------|
| `collapsible_button_side_toggle_description` | 切换按钮组显示位置 | Switch button group display position |
| `collapsible_button_map_mode_description` | 切换地图显示模式 | Switch map display mode |
| `collapsible_button_search_description` | 按编号搜索定位 | Search location by number |
| `collapsible_button_visibility_description` | 显示/隐藏已完成订单 | Show/hide completed orders |
| `collapsible_button_counter_description` | 显示订单总数 | Display order count |

### 自定义界面
| 键值 | 中文 | 英文 |
|------|------|------|
| `collapsible_button_customization_title` | 选择常用按钮 | Select Favorite Buttons |
| `collapsible_button_customization_subtitle` | 选择您希望在快捷栏中显示的按钮（最多选择4个） | Choose buttons you want to display in the quick bar (up to 4 buttons) |
| `collapsible_button_customization_navigation_title` | 自定义按钮 | Customize Buttons |

### 通用按钮
| 键值 | 中文 | 英文 |
|------|------|------|
| `cancel` | 取消 | Cancel |
| `save` | 保存 | Save |

## 文件位置

### 英文本地化文件
```
NaviBatch/Localizations/en.lproj/Localizable.strings
```

### 中文本地化文件
```
NaviBatch/Localizations/zh-Hans.lproj/Localizable.strings
```

## 使用方法

在Swift代码中使用 `.localized` 扩展方法：

```swift
// 获取本地化文本
let buttonTitle = "collapsible_button_side_toggle".localized
let buttonDescription = "collapsible_button_side_toggle_description".localized
```

## 添加新语言支持

如需添加新语言支持，请：

1. 在 `NaviBatch/Localizations/` 目录下创建对应的 `.lproj` 文件夹
2. 复制 `en.lproj/Localizable.strings` 文件到新语言文件夹
3. 翻译所有键值对应的文本
4. 确保键值名称保持不变，只翻译等号右侧的文本

## 验证本地化

可以使用项目中的本地化验证工具：

```swift
// 运行本地化验证
let report = LocalizationValidator.run()
print(report)
```

## 注意事项

1. **键值命名规范**：使用 `collapsible_button_` 前缀确保命名空间清晰
2. **文本长度**：考虑不同语言的文本长度差异，确保UI布局适应
3. **上下文**：提供清晰的描述文本帮助用户理解按钮功能
4. **一致性**：保持与应用其他部分的本地化风格一致

---

**更新日期**：2025-07-09  
**版本**：v1.0  
**作者**：Augment Agent (Claude Sonnet 4)
