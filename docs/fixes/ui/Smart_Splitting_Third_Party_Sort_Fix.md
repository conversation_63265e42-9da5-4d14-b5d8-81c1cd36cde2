# 智能分割第三方排序号修复

## 问题描述
用户反馈在使用智能图片分割功能处理长图时，第三方的sort number出现对不上的问题：
- 原本应该显示原始的第三方排序号（如"停靠点: 1"、"停靠点: 2"）
- 处理后变成了连续的"SpeedX: 1"、"SpeedX: 2"等
- 导致与原始第三方系统的排序号不匹配

## 根本原因分析

### 1. 智能分割流程
- 当图片高度 > 8000像素或高宽比 > 15:1时，触发智能分割
- 图片被分割为4000像素的片段，重叠300像素
- 每个片段独立进行AI识别
- 最后合并所有片段的识别结果

### 2. 序号重新分配问题
在 `ImageAddressRecognizer.swift` 的 `reassignSortNumbers()` 函数中：

**修复前的逻辑**：
```swift
if selectedAppType == .gofo {
    // GoFo使用连续序号
    newAddress += "|SORT:\(currentSortNumber)"
} else if selectedAppType == .uniuni && !separatedInfo.sortNumber.isEmpty {
    // 🎯 UNIUNI保留原始AI识别的序号（149,150,151,152）
    newAddress += "|SORT:\(separatedInfo.sortNumber)"
} else if !separatedInfo.sortNumber.isEmpty {
    // 其他应用使用连续序号 ❌ 问题所在
    newAddress += "|SORT:\(currentSortNumber)"
}
```

**问题**：
- 只有UNIUNI保留原始排序号
- SpeedX、LDS EPOD、PIGGY等其他第三方应用都被强制重新分配连续序号
- 导致原始的第三方排序号丢失

## 修复方案

### 1. 三个排序号系统设计
根据用户需求，建立了三个不同的排序号概念：

1. **sort_number**: 根据处理顺序添加的连续序号 (1, 2, 3, 4...)
2. **sorted_number**: 路线优化后的序号，初始时等于sort_number
3. **thirdPartySortNumber**: 严格按照AI从图片识别到的原始排序号，绝对不能修改

### 2. 修改序号分配逻辑
**修复后的逻辑**：
```swift
// 🎯 修正：三个排序号的正确处理逻辑
// 1. sort_number: 根据处理顺序添加连续序号 (1,2,3,4...)
// 2. sorted_number: 路线优化后的序号，初始等于sort_number
// 3. thirdPartySortNumber: AI识别的原始排序号，绝对不能修改

// 所有地址都使用连续的内部序号作为SORT标签
newAddress += "|SORT:\(currentSortNumber)"

// 如果有AI识别的第三方排序号，单独保存（不放在SORT标签中）
if !separatedInfo.thirdPartySortNumber.isEmpty {
    // 🎯 第三方排序号单独保存，绝对不修改原始值
    newAddress += "|THIRD_PARTY_SORT:\(separatedInfo.thirdPartySortNumber)"
    Logger.aiInfo("🎯 保存第三方原始排序号: \(selectedAppType.rawValue) -> \(separatedInfo.thirdPartySortNumber)")
}

Logger.aiInfo("🎯 分配内部连续序号: \(selectedAppType.rawValue) -> \(currentSortNumber)")
```

### 3. 修复效果
- ✅ **内部序号**: 所有地址都有连续的内部序号 (1, 2, 3, 4...)，用于路线优化
- ✅ **第三方排序号**: 严格保留AI识别的原始排序号，**绝对不修改**
  - **SpeedX**: 保留原始的"停靠点: 5"、"停靠点: 8"、"停靠点: 12"等（不是连续的1,2,3）
  - **UNIUNI**: 保留原始的"149"、"150"、"155"等（不是连续的1,2,3）
  - **LDS EPOD**: 保留原始的第三方排序号
  - **PIGGY**: 保留原始的第三方排序号
  - **GoFo**: 保留原始的第三方排序号
- ✅ **数据分离**: 内部管理和第三方显示完全分离，互不干扰
- ✅ **司机友好**: 司机看到的排序号与原始系统完全一致，不会搞混

## 测试验证

### 1. SpeedX长图测试
**测试步骤**：
1. 选择SpeedX应用类型
2. 上传一张高度>8000像素的长图，包含多个停靠点
3. 触发智能分割处理
4. 验证处理结果

**预期结果**：
```
修复前：
- SpeedX: 1 - 地址1  ❌ 丢失原始排序号
- SpeedX: 2 - 地址2  ❌ 丢失原始排序号
- SpeedX: 3 - 地址3  ❌ 丢失原始排序号

修复后（AI识别到的原始排序号可能不连续）：
- 停靠点: 5 - 地址1  ✅ 保留AI识别的原始排序号
- 停靠点: 8 - 地址2  ✅ 保留AI识别的原始排序号
- 停靠点: 12 - 地址3  ✅ 保留AI识别的原始排序号
```

**重要说明**：第三方排序号是会变的，不是固定的1,2,3...序列。我们要保留AI识别到的任何数字。

### 2. 其他第三方应用测试
- **UNIUNI**: 验证仍然保留原始排序号
- **LDS EPOD**: 验证保留原始排序号
- **PIGGY**: 验证保留原始排序号
- **GoFo**: 验证仍然使用连续序号

## 技术细节

### 1. 影响的文件
- `NaviBatch/Views/Components/ImageAddressRecognizer.swift`
  - 修改 `reassignSortNumbers()` 函数
  - 第1183-1196行

### 2. 日志输出
修复后会输出详细的日志信息：
```
🎯 保留第三方排序号: speedx -> 停靠点: 1
🎯 保留第三方排序号: speedx -> 停靠点: 2
🎯 使用连续排序号: gofo -> 1
🎯 使用连续排序号: gofo -> 2
```

### 3. 兼容性
- ✅ 向后兼容：不影响现有功能
- ✅ 智能分割：正常工作，保留第三方排序号
- ✅ 普通处理：正常工作，保留第三方排序号
- ✅ GoFo特殊处理：继续使用连续序号

## 验证要点

### 1. 第三方排序号保留
- [ ] SpeedX保留"停靠点: X"格式
- [ ] UNIUNI保留三位数字格式
- [ ] LDS EPOD保留原始排序号
- [ ] PIGGY保留原始排序号

### 2. 智能分割功能
- [ ] 长图正确分割为多个片段
- [ ] 每个片段正确识别地址和排序号
- [ ] 合并结果时保留原始排序号
- [ ] 去重功能正常工作

### 3. 显示效果
- [ ] 地址列表显示正确的第三方排序号
- [ ] 地图标记显示正确的排序号
- [ ] 路线优化界面显示正确的排序号

## 注意事项

1. **GoFo特殊处理**：GoFo仍然使用连续序号，因为这符合GoFo的业务需求
2. **日志监控**：通过日志可以监控排序号的分配情况
3. **性能影响**：修复不影响处理性能
4. **数据完整性**：确保所有地址信息（追踪号、客户名等）都得到保留

## 修复时间
- **修复日期**: 2025-06-25
- **修复版本**: 当前开发版本
- **测试状态**: 待验证
