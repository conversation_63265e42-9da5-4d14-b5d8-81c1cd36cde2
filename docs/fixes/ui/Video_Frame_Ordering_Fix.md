# 视频帧编号顺序修复

## 🎯 问题描述

用户发现Scanner界面显示的图片编号（1、2、3、4）不是按照视频帧保存的顺序展示的，怀疑是因为去重逻辑导致了顺序混乱。

### 原始问题
- 帧提取时没有保留原始时间顺序信息
- 去重后丢失了帧的原始编号
- 保存时使用简单的`index + 1`编号，无法反映真实的时间顺序

## 🔧 解决方案

### 1. 引入NumberedFrame结构

```swift
struct NumberedFrame {
    let image: UIImage
    let originalIndex: Int      // 原始提取顺序编号
    let timestamp: Double       // 视频时间戳
    let extractionTime: Date    // 提取时间
}
```

### 2. 更新处理流程

#### 帧提取阶段
- 从1开始为每帧分配原始编号
- 记录视频时间戳
- 即使提取失败也要增加编号，保持时间顺序

#### 去重阶段
- 保持原始编号不变
- 记录被去除的重复帧编号
- 输出详细的去重日志

#### 保存阶段
- 按原始编号排序后保存
- 文件名使用原始编号
- 生成包含编号信息的索引文件

### 3. 关键改进点

#### 🎯 编号一致性
```swift
// 提取时分配编号
var frameIndex = 1
let numberedFrame = NumberedFrame(
    image: uiImage,
    originalIndex: frameIndex,
    timestamp: currentTime,
    extractionTime: Date()
)
frameIndex += 1
```

#### 🔄 去重保序
```swift
// 去重时保持原始编号
if similarity < similarityThreshold {
    uniqueFrames.append(currentFrame)
    print("✅ 保留帧#\(currentFrame.originalIndex)")
} else {
    print("🗑️ 去除重复帧#\(currentFrame.originalIndex)")
}
```

#### 📁 排序保存
```swift
// 按原始编号排序保存
let sortedFrames = frames.sorted { $0.originalIndex < $1.originalIndex }
let fileName = String(format: "frame_%03d.jpg", numberedFrame.originalIndex)
```

## 📊 效果对比

### 修复前
- 显示编号：1, 2, 3, 4
- 实际顺序：可能是第5帧、第12帧、第18帧、第25帧
- 用户困惑：编号与实际时间顺序不符

### 修复后
- 显示编号：5, 12, 18, 25
- 实际顺序：严格按照视频时间顺序
- 用户清晰：编号直接反映原始提取顺序

## 🎯 技术细节

### 更新的方法
1. `extractDenseFrames` - 密集帧提取
2. `extractContentAwareFrames` - 智能内容感知提取
3. `extractSpeedXOptimizedFrames` - SpeedX优化提取
4. `removeRedundantFrames` - 去重处理
5. `saveFramesForDebugging` - 调试保存
6. `generateFrameIndex` - 索引生成

### 调试信息增强
```swift
struct FrameDebugInfo {
    let originalFrameIndices: [Int] // 新增：保留帧的原始编号
    // ... 其他字段
}
```

### 日志输出优化
```
🔢 保存顺序按原始编号排序: [5, 12, 18, 25]
💾 保存帧#5: frame_005.jpg (时间戳: 1.5s)
💾 保存帧#12: frame_012.jpg (时间戳: 3.6s)
```

## ✅ 验证方法

1. **文件名检查**：保存的文件名应该反映原始编号
2. **时间戳验证**：索引文件中的时间戳应该递增
3. **UI显示**：Scanner界面的编号应该按时间顺序
4. **日志分析**：查看去重日志确认编号正确性

## 🚀 用户体验改进

- **直观性**：编号直接对应视频时间顺序
- **可追溯性**：可以根据编号找到对应的视频时间点
- **调试友好**：文件名和日志都包含原始编号信息
- **一致性**：从提取到显示全程保持编号一致

## 📝 注意事项

1. **向后兼容**：现有的调用代码需要适配NumberedFrame结构
2. **性能影响**：排序操作对性能影响微乎其微
3. **存储空间**：每帧增加少量元数据，影响可忽略
4. **调试便利**：大大提升了问题排查效率

这个修复确保了视频帧的显示顺序严格按照原始提取的时间顺序，解决了用户反馈的编号混乱问题。
