# 重复调用修复文档

## 问题描述

用户发现日志中出现重复的内容，每一行都重复打印两次：

```
🤖 AI: [FirebaseAIService.processImageWithFirebaseAI(base64Image:appType:isPDFImage:isSegmentedImage:):830] 📝 Firebase AI提示词 (GoFo):
🤖 AI: [FirebaseAIService.processImageWithFirebaseAI(base64Image:appType:isPDFImage:isSegmentedImage:):830] 📝 Firebase AI提示词 (GoFo):
🤖 AI: [FirebaseAIService.processImageWithFirebaseAI(base64Image:appType:isPDFImage:isSegmentedImage:):831] 📄 提示词内容: ...
🤖 AI: [FirebaseAIService.processImageWithFirebaseAI(base64Image:appType:isPDFImage:isSegmentedImage:):831] 📄 提示词内容: ...
```

## 根本原因

通过代码分析发现，问题是**重复调用**而不是多线程或日志系统问题。

### 重复调用路径

1. **直接调用路径**：
   - `ImageAddressRecognizer.swift:1475` - 直接调用 `firebaseAIService.extractAddressesFromImage()`
   - `ImageAddressRecognizer.swift:2653` - `processImageWithFirebaseAI()` 中的调用
   - `ImageAddressRecognizer.swift:4824` - `processImageWithFirebaseAISplitting()` 中的调用

2. **间接调用路径**：
   - `HybridAddressRecognitionService` → `HybridAIService` → `FirebaseAIService`

### 问题场景

在某些情况下，同一个图片可能被处理两次：
- 用户触发图片处理
- 某个流程调用了直接的 `firebaseAIService.extractAddressesFromImage()`
- 同时或之后，另一个流程通过 `HybridAIService` 间接调用了同样的方法
- 结果：同一个图片被处理了两次，导致日志重复

## 解决方案

### 1. 添加重复调用检测机制

在 `FirebaseAIService` 中添加简单的重复调用检测：

```swift
class FirebaseAIService {
    // 🚫 重复调用检测 - 防止同一图片被处理多次
    private var processingImageHashes: Set<String> = []
    private let processingQueue = DispatchQueue(label: "firebase.ai.processing", attributes: .concurrent)
    
    private func processImageWithFirebaseAI(base64Image: String, appType: DeliveryAppType, isPDFImage: Bool = false, isSegmentedImage: Bool = false) async throws -> String {
        // 🚫 重复调用检测
        let imageHash = String(base64Image.prefix(100).hashValue)
        
        try processingQueue.sync {
            if processingImageHashes.contains(imageHash) {
                Logger.aiWarning("🚫 检测到重复调用，跳过处理相同图片: \(imageHash)")
                throw GemmaError.duplicateProcessing
            }
            processingImageHashes.insert(imageHash)
        }
        
        defer {
            // 处理完成后清理哈希，允许后续处理
            processingQueue.async(flags: .barrier) {
                self.processingImageHashes.remove(imageHash)
            }
        }
        
        // 原有的处理逻辑...
    }
}
```

### 2. 添加新的错误类型

在 `GemmaError` 枚举中添加 `duplicateProcessing` 错误类型：

```swift
enum GemmaError: Error, LocalizedError {
    // 现有错误类型...
    case duplicateProcessing
    
    var errorDescription: String? {
        switch self {
        // 其他错误描述...
        case .duplicateProcessing:
            return "检测到重复处理，已跳过相同图片"
        }
    }
}
```

### 3. 改进日志信息

添加图片哈希到日志中，便于调试：

```swift
Logger.aiInfo("🔥 高级服务 - 发送识别请求 (Hash: \(imageHash))")
```

## 技术细节

### 哈希生成策略

- 使用 `base64Image.prefix(100).hashValue` 生成图片哈希
- 只取前100个字符避免性能问题
- 使用 `hashValue` 生成唯一标识

### 线程安全

- 使用 `DispatchQueue` 确保哈希集合的线程安全
- 使用 `concurrent` 队列提高性能
- 使用 `barrier` 标志确保清理操作的原子性

### 内存管理

- 使用 `defer` 确保处理完成后清理哈希
- 避免哈希集合无限增长
- 允许相同图片的后续处理（如用户重新选择同一图片）

## 预期效果

修复后，重复调用将被检测并跳过，日志中不再出现重复内容：

```
🤖 AI: 🔥 高级服务 - 发送识别请求 (Hash: 123456789)
🤖 AI: 📝 Firebase AI提示词 (GoFo):
🤖 AI: 📄 提示词内容: ...
🤖 AI: 🚫 检测到重复调用，跳过处理相同图片: 123456789
```

## 测试建议

1. **正常处理测试**：确保单次图片处理正常工作
2. **重复调用测试**：验证重复调用被正确检测和跳过
3. **并发测试**：确保多线程环境下的安全性
4. **内存测试**：验证哈希集合正确清理，无内存泄漏

## 注意事项

- 这是一个简单的解决方案，专注于解决根本问题
- 不添加复杂的多层保护机制
- 保持代码简洁和可维护性
- 符合用户偏好的简单解决方案原则
