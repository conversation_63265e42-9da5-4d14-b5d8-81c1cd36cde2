# SpeedX第三方排序号字段名修复

## 问题发现

### 用户测试结果
用户测试SpeedX图片后发现，虽然跳过了OCR直接使用AI，但第三方排序号仍然有问题：

```json
// 第一个片段 - 正常
{
  "address": "393 Mandarin Dr Apt 3, Daly City, CA, 94015, USA",
  "sort_number": "1",  // ✅ 有值
  "tracking_number": "#SPXSFO078500669736"
}

// 第二个片段 - 问题
{
  "address": "784 King Drive, Daly City, CA, 94015, USA", 
  "sort_number": null,  // ❌ 缺失
  "tracking_number": "#SPXSFO078500675707"
}
```

### 根本原因分析

通过分析日志发现，问题不是图片清晰度，而是**AI提示词字段名不一致**：

1. **图片识别提示词**: 要求返回`"third_party_sort"`字段
2. **OCR文本处理提示词**: 要求返回`"sort_number"`字段  
3. **AI实际返回**: 使用了`"sort_number"`字段
4. **解析代码**: 兼容两种字段名，但优先使用`"third_party_sort"`

## 问题详细分析

### 提示词不一致

#### 图片识别提示词 (正确)
```swift
// FirebaseAIService.createSpeedXPrompt()
You MUST respond with ONLY valid JSON in this exact format:
{"success": true, "deliveries": [{"third_party_sort": "number_only", ...}]}
```

#### OCR文本处理提示词 (错误)
```swift
// FirebaseAIService.createOCRTextPrompt() - 修复前
{
  "success": true,
  "addresses": [
    {
      "address": "complete street address",
      "sort_number": "original_sort_format_if_found",  // ❌ 错误字段名
      ...
    }
  ]
}
```

### AI行为分析
- AI模型倾向于使用最近看到的字段名
- 由于OCR文本处理使用了`"sort_number"`，AI就返回这个字段名
- 导致解析时优先匹配失败，回退到兼容性处理

## 修复方案

### 1. 统一字段名为`third_party_sort`

```swift
// 修复后的OCR文本处理提示词
{
  "success": true,
  "addresses": [
    {
      "address": "complete street address with suburb, state, postcode",
      "third_party_sort": "number_only_from_停靠点_format",  // ✅ 正确字段名
      "tracking_number": "tracking number if found",
      "customer_name": "customer name if found",
      "package_count": "number if found",
      "access_instructions": "special instructions if found"
    }
  ]
}
```

### 2. 更新字段说明

```swift
// 修复前
3. Preserve original sort numbers exactly as they appear

// 修复后  
3. For third_party_sort: extract ONLY the number from "停靠点: X" format (e.g., "5", "8")
```

## 技术实现

### 修改的文件
- `NaviBatch/Services/FirebaseAIService.swift`
  - `createOCRTextPrompt()` 方法
  - 字段名从 `"sort_number"` 改为 `"third_party_sort"`
  - 更新字段说明文档

### 解析逻辑保持不变
```swift
// 解析代码已经支持兼容性处理
let thirdPartySortNumber = delivery["third_party_sort"] as? String ??
                         delivery["sort_number"] as? String ?? ""
```

## 预期效果

### 修复前
- AI返回 `"sort_number"` 字段
- 解析时先查找 `"third_party_sort"`（失败）
- 回退到 `"sort_number"`（成功但不一致）

### 修复后  
- AI返回 `"third_party_sort"` 字段
- 解析时直接匹配 `"third_party_sort"`（成功）
- 字段名完全一致，减少混淆

## 验证方法

### 测试步骤
1. 使用相同的SpeedX图片重新测试
2. 检查AI返回的JSON格式
3. 确认所有地址都有 `"third_party_sort"` 字段
4. 验证停靠点号码不再为 `null`

### 预期日志
```
🤖 AI: 📄 高级服务响应: {
  "success": true,
  "addresses": [
    {
      "address": "784 King Drive, Daly City, CA, 94015, USA",
      "third_party_sort": "14",  // ✅ 应该有值
      "tracking_number": "#SPXSFO078500675707"
    }
  ]
}
```

## 重要性说明

### 为什么这个修复很重要
1. **数据一致性**: 确保所有AI服务使用相同的字段名
2. **减少混淆**: 避免开发和调试时的字段名混乱  
3. **提高准确性**: AI更容易理解和遵循一致的格式要求
4. **用户体验**: 确保SpeedX的停靠点号码100%识别

### 影响范围
- 仅影响OCR文本处理的AI提示词
- 不影响图片直接识别的提示词
- 解析代码保持向后兼容

## 后续优化

### 可能的进一步改进
1. **统一所有AI提示词**: 确保所有快递应用都使用相同的字段名
2. **提示词模板化**: 创建统一的JSON格式模板
3. **字段验证**: 在解析时添加字段完整性检查

## 更新日志

### v1.0.8 (2025-06-25)
- 🔧 修复OCR文本处理提示词中的字段名不一致问题
- 🎯 统一使用 `"third_party_sort"` 字段名
- 📈 提高SpeedX停靠点号码识别的一致性
- ✅ 解决部分地址停靠点号码为null的问题
