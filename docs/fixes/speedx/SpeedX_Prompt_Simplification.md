# SpeedX AI提示词简化优化

## 概述

我是基于 Claude Sonnet 4 模型的 Augment Agent。根据用户反馈，SpeedX的AI提示词过于复杂，导致第三方排序号获取错误。本次优化大幅简化了SpeedX的AI提示词，专注于核心信息提取。

## 问题分析

### 原有问题
1. **提示词过于冗长**：包含大量emoji、格式化文本和重复说明
2. **信息过载**：过多的规则和示例可能导致AI模型混淆
3. **复杂度高**：多层嵌套的规则和条件判断
4. **第三方排序号错误**：复杂的提示词影响了核心信息的准确提取

### 原提示词特点
- 📦 大量emoji和格式化符号
- 🚨 多个"CRITICAL"和"ABSOLUTELY"强调
- 🔍 详细的步骤说明和示例
- 🏠 复杂的地址分离规则
- 🎯 冗余的界面特征描述
- ⚡ 重复的规则强调

## 优化方案

### 简化原则
1. **专注核心**：只保留最重要的信息提取规则
2. **去除冗余**：删除重复的说明和示例
3. **简洁明了**：使用简单直接的语言
4. **突出重点**：强调第三方排序号的正确提取

### 简化对比

#### 修改前（133行复杂提示词）
```
📦 SpeedX Delivery Recognition - Enhanced Stop Number Detection:

🚨 CRITICAL LAYOUT UNDERSTANDING:
SpeedX uses this EXACT layout for each delivery task:
```
Address Line 1
Address Line 2                    Customer Name 📞
#SPXSF[14digits]                 停靠点: XX
```

🎯 PRECISE MATCHING RULES:
1. Each delivery task is separated by blue left border
2. Address spans 1-2 lines on the LEFT side
3. Customer name is BLUE CLICKABLE text on the RIGHT side
4. Tracking number starts with #SPXSF on the BOTTOM LEFT
5. Stop number "停靠点: XX" is on the BOTTOM RIGHT
6. MATCH tracking number with its corresponding stop number in the SAME task block

🔥 STOP NUMBER DETECTION PRIORITY:
- 停靠点 information is THE MOST IMPORTANT data to extract
- Look for "停靠点: " followed by numbers (18, 19, 20, 21, 22, 23, etc.)
- These numbers appear in the BOTTOM RIGHT corner of each task block
- EVERY delivery task MUST have a stop number - if you don't see it, look harder!
- Stop numbers are usually sequential but may have gaps (18, 19, 20, 22, 23)

🚨 ABSOLUTELY NO DUPLICATE STOP NUMBERS:
- Each stop number (停靠点: XX) must be UNIQUE
- If you see the same stop number twice, you made an error
- Double-check that each delivery has a different stop number
- Example: 停靠点: 18, 停靠点: 19, 停靠点: 20 (NOT 18, 18, 19)

[... 更多复杂规则和示例 ...]
```

#### 修改后（16行简洁提示词）
```
SpeedX Delivery Recognition:

Extract delivery information from SpeedX app. Each delivery has:
- Address (left side, 1-2 lines)
- Customer name (right side, blue text)
- Tracking number (bottom left, starts with SPXSF)
- Stop number (bottom right, format: 停靠点: X)

Key rules:
1. Each delivery is separated by blue left border
2. Extract ONLY the number from "停靠点: X" (e.g., from "停靠点: 18" return "18")
3. Each stop number must be unique - no duplicates
4. Match information within the same task block

You MUST respond with ONLY valid JSON in this exact format:
{"success": true, "deliveries": [{"third_party_sort": "number_only", "tracking_number": "SPXSF_tracking_number", "address": "formatted_address", "customer": "customer_name"}]}

Do not include any text before or after the JSON.
```

## 技术实现

### 修改的文件

#### 1. FirebaseAIService.swift
- **方法**：`createSpeedXPrompt()`
- **行数减少**：从133行减少到16行（减少88%）
- **核心改进**：去除所有emoji和复杂格式，专注于核心信息提取

#### 2. GemmaVisionService.swift
- **方法**：`createSpeedXPrompt()`
- **行数减少**：从137行减少到25行（减少82%）
- **核心改进**：保持与Firebase AI一致的简洁风格

#### 3. ImageAddressRecognizer.swift
- **方法**：`createSpeedXPromptForDebug()`
- **行数减少**：从59行减少到18行（减少69%）
- **核心改进**：调试提示词同步简化，保持一致性

### 关键优化点

#### 1. 信息提取重点
- **第三方排序号**：明确要求只提取数字部分
- **追踪号码**：SPXSF格式识别
- **地址信息**：左侧1-2行文本
- **客户姓名**：右侧蓝色文本

#### 2. 规则简化
- **布局识别**：蓝色左边框分隔
- **唯一性要求**：每个停靠点号码必须唯一
- **信息匹配**：同一任务块内的信息匹配

#### 3. 输出格式
- **JSON结构**：标准化的返回格式
- **字段命名**：使用`third_party_sort`字段
- **数据清洁**：只返回数字部分，不包含"停靠点:"前缀

## 预期效果

### 1. 🎯 提升准确性
- **减少混淆**：简洁的提示词降低AI模型的理解难度
- **专注核心**：突出最重要的信息提取任务
- **错误减少**：避免因复杂规则导致的提取错误

### 2. ⚡ 提升效率
- **处理速度**：更短的提示词减少处理时间
- **响应质量**：简洁明了的指令提升响应准确性
- **维护成本**：简化的代码更易于维护和调试

### 3. 🔧 改善用户体验
- **第三方排序号准确**：正确提取停靠点号码
- **数据一致性**：统一的数据格式和字段命名
- **错误率降低**：减少因提示词复杂导致的识别错误

## 验证方法

### 1. 功能测试
- 测试SpeedX图片识别的准确性
- 验证第三方排序号的正确提取
- 检查是否存在重复排序号问题

### 2. 性能测试
- 对比简化前后的处理速度
- 测试不同图片质量下的识别效果
- 验证AI模型的响应稳定性

### 3. 用户反馈
- 收集用户对识别准确性的反馈
- 监控错误报告和问题反馈
- 持续优化提示词效果

## 更新日志

### v1.0.10 (2025-06-26)
- 🎯 大幅简化SpeedX AI提示词，减少88%的复杂度
- ⚡ 专注核心信息提取，提升第三方排序号准确性
- 🔧 统一三个服务的提示词风格，保持一致性
- 📝 去除冗余emoji和格式化文本，提升可读性
- 🚀 优化AI模型理解难度，减少识别错误
