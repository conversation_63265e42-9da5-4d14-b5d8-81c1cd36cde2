# SpeedX布局识别修复

## 问题描述

用户反馈SpeedX AI识别出现严重的布局混乱问题：

### 🚨 错误示例
```json
{
  "address": "61 Crestwood Dr ATP 27, Daly City, CA, 94015, USA",
  "sort_number": "停靠点：95",
  "tracking_number": "#SPXSF0078500668034",
  "customer_name": "<PERSON><PERSON><PERSON>... G",
  "package_count": "1个包裹"
},
{
  "address": "61 Crestwood Dr ATP 27, Daly City, CA, 94015, USA",
  "sort_number": "停靠点：96",  // ❌ 错误：应该是97
  "tracking_number": null,      // ❌ 错误：丢失追踪号
  "customer_name": null         // ❌ 错误：丢失客户名
}
```

### 🎯 正确应该是
```json
{
  "address": "61 Crestwood Dr ATP 27, Daly City, CA, 94015, USA",
  "sort_number": "停靠点：95",
  "tracking_number": "#SPXSF0078500668034",
  "customer_name": "<PERSON><PERSON><PERSON>... G"
},
{
  "address": "61 Crestwood Dr Apt 2, Daly City, CA, 94015, USA",
  "sort_number": "停靠点：97",
  "tracking_number": "#SPXSF0078500671719",
  "customer_name": "Selein Martin"
}
```

## 根本原因分析

### 1. SpeedX实际布局
```
┌─────────────────────────────────────────────────────┐
│ 61 Crestwood Dr ATP 27, Daly                      │
│ City,CA, 94015, USA              Dajalynn La... G 📞│
│ #SPXSF0078500668034                    停靠点: 95   │
├─────────────────────────────────────────────────────┤
│ 61 Crestwood Dr Apt 2, Daly                       │
│ City CA 94015 USA                 Selein Martin 📞│
│ #SPXSF0078500671719                    停靠点: 97   │
└─────────────────────────────────────────────────────┘
```

### 2. AI识别错误
- **布局理解不准确**：无法正确识别任务块边界
- **信息匹配错误**：追踪号和停靠点号来自不同块
- **地址重复**：同一地址被分配给不同停靠点

## 修复方案

### 1. 重新设计AI提示词

#### 修复前（模糊指令）
```swift
🚨 CRITICAL INSTRUCTIONS:
- READ EACH DELIVERY ITEM SEPARATELY
- LOOK FOR VISUAL SEPARATORS (blue borders, stop numbers)
```

#### 修复后（精确布局）
```swift
🚨 CRITICAL LAYOUT UNDERSTANDING:
SpeedX uses this EXACT layout for each delivery task:
```
Address Line 1
Address Line 2                    Customer Name 📞
#SPXSF[14digits]                 停靠点: XX
```

🎯 PRECISE MATCHING RULES:
1. Each delivery task is separated by blue left border
2. Address spans 1-2 lines on the LEFT side
3. Customer name is BLUE CLICKABLE text on the RIGHT side
4. Tracking number starts with #SPXSF on the BOTTOM LEFT
5. Stop number "停靠点: XX" is on the BOTTOM RIGHT
6. MATCH tracking number with its corresponding stop number in the SAME task block
```

### 2. 添加步骤化提取流程
```swift
🔍 STEP-BY-STEP EXTRACTION PROCESS:
1. Identify each blue-bordered task block
2. For each block, extract in this order:
   - Address (left side, 1-2 lines)
   - Customer name (right side, blue text)
   - Tracking number (bottom left, starts with #SPXSF)
   - Stop number (bottom right, 停靠点: XX)
3. Ensure tracking number and stop number are from the SAME block
4. Move to next block and repeat
```

### 3. 强化错误避免指南
```swift
🚨 COMMON MISTAKES TO AVOID:
- Taking tracking number from one block and stop number from another
- Repeating the same address for different stop numbers
- Missing customer names that appear in blue text
- Confusing apartment numbers (ATP, Apt) in addresses
```

## 技术实现

### 修改文件列表

1. **NaviBatch/Services/FirebaseAIService.swift**
   - 重写`createSpeedXPrompt()`方法
   - 添加精确的布局分析指令

2. **NaviBatch/Services/GemmaVisionService.swift**
   - 同步更新`createSpeedXPrompt()`方法
   - 保持两个AI服务的一致性

3. **NaviBatch/Views/Components/ImageAddressRecognizer.swift**
   - 更新`createSpeedXPromptForDebug()`方法
   - 同步调试提示词

## 预期效果

- ✅ **正确识别任务块边界**：每个蓝色边框为一个独立任务
- ✅ **准确匹配信息**：追踪号和停靠点号来自同一任务块
- ✅ **避免信息混合**：不同任务块的信息不会交叉
- ✅ **提高识别准确率**：减少SpeedX识别错误

## 测试验证

### 测试用例
使用用户提供的SpeedX截图进行测试：
- 包含多个任务块
- 每个任务块有不同的地址、客户名、追踪号、停靠点号
- 验证AI能否正确匹配每个任务块的信息

### 验收标准
- [ ] 每个停靠点号对应正确的追踪号
- [ ] 每个停靠点号对应正确的客户名
- [ ] 不同地址不会被重复分配
- [ ] 所有可见的任务块都被正确识别

## 注意事项

1. **布局一致性**：SpeedX的布局相对固定，但可能有细微变化
2. **文本识别**：客户名可能被截断（如"Dajalynn La... G"）
3. **地址格式**：注意ATP和Apt的区别
4. **追踪号格式**：确保SPXSF+14位数字的格式正确

## 后续优化

1. **监控识别效果**：收集用户反馈，持续优化
2. **扩展测试用例**：增加更多SpeedX布局变化的测试
3. **性能优化**：确保修复不影响识别速度
4. **文档更新**：及时更新相关技术文档
