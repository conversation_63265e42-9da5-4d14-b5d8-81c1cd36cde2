// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		7DA5C67E2DFA05C300EF118F /* FirebaseAI in Frameworks */ = {isa = PBXBuildFile; productRef = 7DA5C67D2DFA05C300EF118F /* FirebaseAI */; };
		7DA5C6802DFA05C300EF118F /* FirebaseCore in Frameworks */ = {isa = PBXBuildFile; productRef = 7DA5C67F2DFA05C300EF118F /* FirebaseCore */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7D7882692DB342620047215B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7D78824E2DB342600047215B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7D7882552DB342600047215B;
			remoteInfo = NaviBatch;
		};
		7D7882732DB342620047215B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7D78824E2DB342600047215B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7D7882552DB342600047215B;
			remoteInfo = NaviBatch;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7D24659A2DDCBD1F000BF848 /* NaviBatch.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NaviBatch.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7D24659B2DDCBD1F000BF848 /* NaviBatchTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NaviBatchTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7D24659C2DDCBD1F000BF848 /* NaviBatchUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NaviBatchUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		9650E7ED2E3B6C7C008A6B72 /* Exceptions for "NaviBatch" folder in "NaviBatch" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				"/Localized: Localizations/Localizable.strings.backup_20250603_125045",
				"CloudflareWorkers/navibatch-config/VERSION_UPDATE_DEPLOYMENT.md",
				Development_Docs/Duplicate_Stop_Number_Prevention.md,
				Development_Docs/Implementation_Summary_StopNumber_Priority.md,
				Development_Docs/Logging_System_Unification.md,
				Documentation/DatabaseSchemaIssue.md,
				Documentation/Implementation_Verification.md,
				Documentation/OpenRouter_Service_Issue_Analysis.md,
				"Documentation/Trial-Period-Issue-Analysis.md",
				"GoogleService-Info.plist.backup",
				Info.plist,
			);
			target = 7D7882552DB342600047215B /* NaviBatch */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7D7882582DB342600047215B /* NaviBatch */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				9650E7ED2E3B6C7C008A6B72 /* Exceptions for "NaviBatch" folder in "NaviBatch" target */,
			);
			path = NaviBatch;
			sourceTree = "<group>";
		};
		7D78826B2DB342620047215B /* NaviBatchTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NaviBatchTests;
			sourceTree = "<group>";
		};
		7D7882752DB342620047215B /* NaviBatchUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NaviBatchUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7D7882532DB342600047215B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DA5C6802DFA05C300EF118F /* FirebaseCore in Frameworks */,
				7DA5C67E2DFA05C300EF118F /* FirebaseAI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7882652DB342620047215B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D78826F2DB342620047215B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7D78824D2DB342600047215B = {
			isa = PBXGroup;
			children = (
				7D7882582DB342600047215B /* NaviBatch */,
				7D78826B2DB342620047215B /* NaviBatchTests */,
				7D7882752DB342620047215B /* NaviBatchUITests */,
				7D24659A2DDCBD1F000BF848 /* NaviBatch.app */,
				7D24659B2DDCBD1F000BF848 /* NaviBatchTests.xctest */,
				7D24659C2DDCBD1F000BF848 /* NaviBatchUITests.xctest */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7D7882552DB342600047215B /* NaviBatch */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D78827C2DB342620047215B /* Build configuration list for PBXNativeTarget "NaviBatch" */;
			buildPhases = (
				7D7882522DB342600047215B /* Sources */,
				7D7882532DB342600047215B /* Frameworks */,
				7D7882542DB342600047215B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7D7882582DB342600047215B /* NaviBatch */,
			);
			name = NaviBatch;
			packageProductDependencies = (
				7DA5C67D2DFA05C300EF118F /* FirebaseAI */,
				7DA5C67F2DFA05C300EF118F /* FirebaseCore */,
			);
			productName = NaviBatch;
			productReference = 7D24659A2DDCBD1F000BF848 /* NaviBatch.app */;
			productType = "com.apple.product-type.application";
		};
		7D7882672DB342620047215B /* NaviBatchTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D78827F2DB342620047215B /* Build configuration list for PBXNativeTarget "NaviBatchTests" */;
			buildPhases = (
				7D7882642DB342620047215B /* Sources */,
				7D7882652DB342620047215B /* Frameworks */,
				7D7882662DB342620047215B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7D78826A2DB342620047215B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7D78826B2DB342620047215B /* NaviBatchTests */,
			);
			name = NaviBatchTests;
			packageProductDependencies = (
			);
			productName = NaviBatchTests;
			productReference = 7D24659B2DDCBD1F000BF848 /* NaviBatchTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7D7882712DB342620047215B /* NaviBatchUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D7882822DB342620047215B /* Build configuration list for PBXNativeTarget "NaviBatchUITests" */;
			buildPhases = (
				7D78826E2DB342620047215B /* Sources */,
				7D78826F2DB342620047215B /* Frameworks */,
				7D7882702DB342620047215B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7D7882742DB342620047215B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7D7882752DB342620047215B /* NaviBatchUITests */,
			);
			name = NaviBatchUITests;
			packageProductDependencies = (
			);
			productName = NaviBatchUITests;
			productReference = 7D24659C2DDCBD1F000BF848 /* NaviBatchUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7D78824E2DB342600047215B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					7D7882552DB342600047215B = {
						CreatedOnToolsVersion = 16.2;
					};
					7D7882672DB342620047215B = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7D7882552DB342600047215B;
					};
					7D7882712DB342620047215B = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7D7882552DB342600047215B;
					};
				};
			};
			buildConfigurationList = 7D7882512DB342600047215B /* Build configuration list for PBXProject "NaviBatch" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
				nl,
				fr,
				de,
				el,
				he,
				hu,
				id,
				it,
				ja,
				ko,
				ms,
				pl,
				pt,
				ro,
				ru,
				es,
				th,
				tr,
				"zh-Hans",
				"zh-Hant",
				"zh-SG",
				"zh-CN",
				"zh-MO",
				"zh-TW",
				"zh-HK",
			);
			mainGroup = 7D78824D2DB342600047215B;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				7DA5C67C2DFA05C300EF118F /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7D78824D2DB342600047215B;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7D7882552DB342600047215B /* NaviBatch */,
				7D7882672DB342620047215B /* NaviBatchTests */,
				7D7882712DB342620047215B /* NaviBatchUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7D7882542DB342600047215B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7882662DB342620047215B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7882702DB342620047215B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7D7882522DB342600047215B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7882642DB342620047215B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D78826E2DB342620047215B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7D78826A2DB342620047215B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7D7882552DB342600047215B /* NaviBatch */;
			targetProxy = 7D7882692DB342620047215B /* PBXContainerItemProxy */;
		};
		7D7882742DB342620047215B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7D7882552DB342600047215B /* NaviBatch */;
			targetProxy = 7D7882732DB342620047215B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7D78827A2DB342620047215B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NaviBatch/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7D78827B2DB342620047215B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NaviBatch/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7D78827D2DB342620047215B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPSTORE_BUILD = 0;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = NaviBatch/NaviBatch.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 8022;
				DEVELOPMENT_ASSET_PATHS = "\"NaviBatch/Preview Content\"";
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NaviBatch/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.navigation";
				INFOPLIST_KEY_NSCameraUsageDescription = "NaviBatch needs to use the camera to scan address information";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "NaviBatch needs your location to show your position on the map and provide navigation even when the app is in the background.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "NaviBatch needs your location to show your position on the map and provide navigation.";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UIViewControllerBasedStatusBarAppearance = NO;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.15;
				OTHER_LDFLAGS = (
					"-Xlinker",
					"-interposable",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.navibatch.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		7D78827E2DB342620047215B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPSTORE_BUILD = 1;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = NaviBatch/NaviBatch.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 8022;
				DEVELOPMENT_ASSET_PATHS = "\"NaviBatch/Preview Content\"";
				ENABLE_PREVIEWS = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NaviBatch/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.navigation";
				INFOPLIST_KEY_NSCameraUsageDescription = "NaviBatch needs to use the camera to scan address information";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "NaviBatch needs your location to show your position on the map and provide navigation even when the app is in the background.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "NaviBatch needs your location to show your position on the map and provide navigation.";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UIViewControllerBasedStatusBarAppearance = NO;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.navibatch.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Osize";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		7D7882802DB342620047215B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-Xlinker",
					"-interposable",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.navibatch.app.tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NaviBatch.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NaviBatch";
			};
			name = Debug;
		};
		7D7882812DB342620047215B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navibatch.app.tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NaviBatch.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NaviBatch";
			};
			name = Release;
		};
		7D7882832DB342620047215B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "";
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"-Xlinker",
					"-interposable",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.navibatch.app.uiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_TARGET_NAME = NaviBatch;
			};
			name = Debug;
		};
		7D7882842DB342620047215B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navibatch.app.uiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_TARGET_NAME = NaviBatch;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7D7882512DB342600047215B /* Build configuration list for PBXProject "NaviBatch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D78827A2DB342620047215B /* Debug */,
				7D78827B2DB342620047215B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D78827C2DB342620047215B /* Build configuration list for PBXNativeTarget "NaviBatch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D78827D2DB342620047215B /* Debug */,
				7D78827E2DB342620047215B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D78827F2DB342620047215B /* Build configuration list for PBXNativeTarget "NaviBatchTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D7882802DB342620047215B /* Debug */,
				7D7882812DB342620047215B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D7882822DB342620047215B /* Build configuration list for PBXNativeTarget "NaviBatchUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D7882832DB342620047215B /* Debug */,
				7D7882842DB342620047215B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		7DA5C67C2DFA05C300EF118F /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.14.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		7DA5C67D2DFA05C300EF118F /* FirebaseAI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7DA5C67C2DFA05C300EF118F /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAI;
		};
		7DA5C67F2DFA05C300EF118F /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7DA5C67C2DFA05C300EF118F /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 7D78824E2DB342600047215B /* Project object */;
}
