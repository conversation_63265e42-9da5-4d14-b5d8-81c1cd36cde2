#!/bin/bash

# 增强版项目备份和还原功能安装脚本
# 自动安装备份和还原功能到新项目，并配置VS Code快捷键
# 用法: ./install_to_new_project.sh [目标项目路径]
#   无参数 - 使用当前目录作为目标路径
#   [路径] - 使用指定路径作为目标路径

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查参数
if [ $# -eq 0 ]; then
    # 如果没有提供参数，使用当前目录
    TARGET_DIR="$(pwd)"
    echo -e "${YELLOW}未提供目标路径，将使用当前目录: ${CYAN}$TARGET_DIR${NC}"
else
    TARGET_DIR="$1"
fi

# 检查目标目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo -e "${RED}错误: 目标目录 '$TARGET_DIR' 不存在${NC}"
    echo -e "请创建目录或提供正确的路径"
    exit 1
fi

echo -e "${CYAN}=== 增强版备份和还原功能安装 ===${NC}"
echo -e "${YELLOW}源目录: $SCRIPT_DIR${NC}"
echo -e "${YELLOW}目标项目: $TARGET_DIR${NC}"
echo ""

# 要复制的文件
FILES_TO_COPY=(
    "backup_project.sh"
    "restore_project.sh"
)

# 复制或创建文件到目标目录
echo -e "${YELLOW}复制或创建文件到目标目录...${NC}"
for file in "${FILES_TO_COPY[@]}"; do
    if [ -f "$SCRIPT_DIR/$file" ]; then
        # 检查源文件和目标文件是否相同
        if [ "$SCRIPT_DIR" == "$TARGET_DIR" ]; then
            echo -e "  ${YELLOW}源目录和目标目录相同，跳过复制 ${CYAN}$file${NC}"
            # 确保文件有执行权限
            chmod +x "$TARGET_DIR/$file"
        else
            echo -e "  复制 ${CYAN}$file${NC} 到 ${CYAN}$TARGET_DIR/${NC}"
            cp "$SCRIPT_DIR/$file" "$TARGET_DIR/"
            chmod +x "$TARGET_DIR/$file"
        fi
    else
        echo -e "  ${YELLOW}未找到 $file，将创建新文件${NC}"

        # 根据文件名创建相应的脚本
        if [ "$file" == "backup_project.sh" ]; then
            # 创建备份脚本
            cat > "$TARGET_DIR/$file" << 'EOF'
#!/bin/bash

# 通用项目备份脚本
# 备份整个项目目录，确保不会漏掉任何文件
#
# 用法: ./backup_project.sh [选项]
#   无参数     - 显示描述选择菜单
#   1         - 使用"修复"作为描述
#   2         - 使用"添加功能"作为描述
#   3         - 使用"完美"作为描述
#   [其他描述] - 使用自定义描述
#
# 示例:
#   ./backup_project.sh           # 显示描述选择菜单
#   ./backup_project.sh 1         # 创建描述为"修复"的备份
#   ./backup_project.sh 2         # 创建描述为"添加功能"的备份
#   ./backup_project.sh 3         # 创建描述为"完美"的备份
#   ./backup_project.sh "自定义描述" # 创建带自定义描述的备份

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 获取当前目录名称作为项目名称
PROJECT_DIR="$(pwd)"
PROJECT_NAME="$(basename "$PROJECT_DIR")"
BACKUP_DIR="$HOME/my_project_backups"
DATE_FORMAT=$(date +"%Y%m%d_%H%M%S")

# 预设描述选项
PRESET_1="修复"
PRESET_2="添加功能"
PRESET_3="完美"

echo -e "${CYAN}=== $PROJECT_NAME 项目备份工具 ===${NC}"
echo -e "${YELLOW}项目名称: $PROJECT_NAME${NC}"
echo -e "${YELLOW}项目目录: $PROJECT_DIR${NC}"
echo -e "${YELLOW}备份目录: $BACKUP_DIR${NC}"
echo ""

# 如果没有提供参数，显示描述选择菜单
if [ $# -eq 0 ]; then
    echo -e "${CYAN}=== $PROJECT_NAME 项目备份 ===${NC}"
    echo -e "${YELLOW}请选择备份描述:${NC}"
    echo ""
    echo -e "  1. ${GREEN}$PRESET_1${NC}"
    echo -e "  2. ${GREEN}$PRESET_2${NC}"
    echo -e "  3. ${GREEN}$PRESET_3${NC}"
    echo -e "  4. 自定义描述"
    echo ""
    echo -e "${YELLOW}请输入选项 (1-4):${NC}"

    read -r option

    case $option in
        1)
            DESCRIPTION="$PRESET_1"
            ;;
        2)
            DESCRIPTION="$PRESET_2"
            ;;
        3)
            DESCRIPTION="$PRESET_3"
            ;;
        4)
            echo -e "${YELLOW}请输入自定义描述:${NC}"
            read -r DESCRIPTION
            ;;
        *)
            echo -e "${YELLOW}无效选项，使用默认描述${NC}"
            DESCRIPTION="backup"
            ;;
    esac
# 如果提供了数字参数1-3，使用预设描述
elif [ "$1" = "1" ]; then
    DESCRIPTION="$PRESET_1"
elif [ "$1" = "2" ]; then
    DESCRIPTION="$PRESET_2"
elif [ "$1" = "3" ]; then
    DESCRIPTION="$PRESET_3"
# 否则使用提供的描述参数
else
    DESCRIPTION="$1"
fi

# 将描述中的空格替换为下划线
DESCRIPTION_FORMATTED=$(echo "$DESCRIPTION" | tr ' ' '_')

# 为项目创建单独的备份子目录
PROJECT_BACKUP_DIR="${BACKUP_DIR}/${PROJECT_NAME}"
BACKUP_FILENAME="${PROJECT_NAME}_${DATE_FORMAT}_${DESCRIPTION_FORMATTED}.zip"
FULL_BACKUP_PATH="${PROJECT_BACKUP_DIR}/${BACKUP_FILENAME}"

# 创建备份目录和项目子目录（如果不存在）
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${YELLOW}创建主备份目录: ${CYAN}$BACKUP_DIR${NC}"
    mkdir -p "$BACKUP_DIR"
fi

if [ ! -d "$PROJECT_BACKUP_DIR" ]; then
    echo -e "${YELLOW}创建项目备份目录: ${CYAN}$PROJECT_BACKUP_DIR${NC}"
    mkdir -p "$PROJECT_BACKUP_DIR"
fi

# 显示备份开始信息
echo -e "\n${CYAN}=== 开始备份 $PROJECT_NAME 项目 ===${NC}"
echo -e "${YELLOW}描述:${NC} $DESCRIPTION"
echo -e "${YELLOW}时间:${NC} $(date)"
echo -e "${YELLOW}源目录:${NC} $PROJECT_DIR"
echo -e "${YELLOW}目标文件:${NC} $FULL_BACKUP_PATH"

# 显示项目目录大小
echo -e "\n${YELLOW}项目目录大小:${NC}"
du -sh "$PROJECT_DIR"

# 创建临时目录，使用项目名称作为子目录
TEMP_DIR="/tmp/project_backup_$(date +%s)"
TEMP_PROJECT_DIR="$TEMP_DIR/$PROJECT_NAME"
echo -e "\n${YELLOW}创建临时目录:${NC} $TEMP_DIR"
rm -rf "$TEMP_DIR"  # 确保临时目录不存在
mkdir -p "$TEMP_PROJECT_DIR"

# 复制项目文件到临时目录中的项目名称子目录
echo -e "${YELLOW}复制项目文件到临时目录...${NC}"
cp -R "$PROJECT_DIR"/* "$TEMP_PROJECT_DIR"/

# 创建压缩备份文件，只排除少量明确的临时目录
echo -e "${YELLOW}创建压缩备份文件...${NC}"
cd "$TEMP_DIR"
zip -r "$FULL_BACKUP_PATH" "$PROJECT_NAME" \
    -x "*/__pycache__/*" \
    -x "*.pyc" \
    -x "*.pyo" \
    -x "*.pyd" \
    -x "*/node_modules/*" \
    -x "*/.git/*" \
    -x "*/.vscode/*" \
    -x "*/.idea/*" \
    -x "*/venv/*" \
    -x "*/env/*" \
    -x "*/dist/*" \
    -x "*/build/*"

# 清理临时目录
echo -e "${YELLOW}清理临时目录...${NC}"
rm -rf "$TEMP_DIR"

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}✓ 备份成功完成!${NC}"
    echo -e "${YELLOW}备份文件:${NC} $FULL_BACKUP_PATH"
    echo -e "${YELLOW}备份大小:${NC} $(du -h "$FULL_BACKUP_PATH" | cut -f1)"

    # 计算文件总数（使用更简单的方法）
    FILE_COUNT=$(unzip -l "$FULL_BACKUP_PATH" | wc -l)
    FILE_COUNT=$((FILE_COUNT - 5))  # 减去头部和尾部的行数
    echo -e "${YELLOW}备份文件中包含:${NC} $FILE_COUNT 个文件/目录"

    # 显示备份历史
    echo -e "\n${CYAN}最近15个备份:${NC}"
    echo -e "${YELLOW}大小\t日期\t\t\t描述${NC}"

    # 使用循环显示最近15个备份，格式化输出
    count=0
    for backup in $(ls -t "$PROJECT_BACKUP_DIR"/*.zip 2>/dev/null | head -15); do
        FILENAME=$(basename "$backup")
        FILESIZE=$(du -h "$backup" | cut -f1)
        DATE_PART=$(echo "$FILENAME" | grep -o "[0-9]\{8\}_[0-9]\{6\}")
        FORMATTED_DATE=$(date -j -f "%Y%m%d_%H%M%S" "$DATE_PART" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "$DATE_PART")
        DESCRIPTION=$(echo "$FILENAME" | sed -E "s/${PROJECT_NAME}_[0-9]{8}_[0-9]{6}_(.*)\.zip/\1/" | tr '_' ' ')

        # 高亮显示当前备份
        if [ "$FILENAME" = "$BACKUP_FILENAME" ]; then
            echo -e "${GREEN}$FILESIZE\t$FORMATTED_DATE\t$DESCRIPTION ${CYAN}(当前)${NC}"
        else
            echo -e "$FILESIZE\t$FORMATTED_DATE\t$DESCRIPTION"
        fi

        ((count++))
    done

    if [ $count -eq 0 ]; then
        echo -e "${YELLOW}没有找到备份文件${NC}"
    fi

    echo -e "\n${YELLOW}完成时间:${NC} $(date)"
else
    echo -e "\n${RED}✗ 备份失败!${NC}"
    exit 1
fi

# 直接退出，不等待按键
exit 0
EOF
            echo -e "  ${GREEN}已创建 backup_project.sh${NC}"
        elif [ "$file" == "restore_project.sh" ]; then
            # 创建还原脚本
            cat > "$TARGET_DIR/$file" << 'EOF'
#!/bin/bash

# 通用项目还原脚本
# 从备份ZIP文件还原项目，允许用户选择要还原的备份版本
# 还原前会删除当前项目中除了还原脚本以外的所有文件
#
# 用法: ./restore_project.sh
#
# 使用上下箭头键选择要还原的备份版本，按Enter确认

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 获取当前目录名称作为项目名称
PROJECT_DIR="$(pwd)"
PROJECT_NAME="$(basename "$PROJECT_DIR")"
BACKUP_DIR="$HOME/my_project_backups"
PROJECT_BACKUP_DIR="${BACKUP_DIR}/${PROJECT_NAME}"
RESTORE_SCRIPT="$(basename "$0")"
TEMP_DIR="/tmp/restore_${PROJECT_NAME}_$(date +%s)"

echo -e "${CYAN}=== $PROJECT_NAME 项目还原工具 ===${NC}"
echo -e "${BLUE}项目名称: $PROJECT_NAME${NC}"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}主备份目录: $BACKUP_DIR${NC}"
echo -e "${BLUE}项目备份目录: $PROJECT_BACKUP_DIR${NC}"
echo -e "${BLUE}临时目录: $TEMP_DIR${NC}"
echo ""

# 检查主备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${RED}错误: 主备份目录不存在: $BACKUP_DIR${NC}"
    exit 1
fi

# 检查项目备份目录是否存在
if [ ! -d "$PROJECT_BACKUP_DIR" ]; then
    echo -e "${YELLOW}警告: 项目备份目录不存在: $PROJECT_BACKUP_DIR${NC}"
    echo -e "${YELLOW}将创建项目备份目录${NC}"
    mkdir -p "$PROJECT_BACKUP_DIR"
fi

# 获取所有备份文件并按时间排序（最新的在前）
BACKUP_FILES=($(ls -t "$PROJECT_BACKUP_DIR"/*.zip 2>/dev/null))

# 检查是否有备份文件
if [ ${#BACKUP_FILES[@]} -eq 0 ]; then
    echo -e "${YELLOW}警告: 在 $PROJECT_BACKUP_DIR 中未找到 ${PROJECT_NAME} 的备份文件${NC}"
    echo -e "${YELLOW}您可以先使用 backup_project.sh 创建备份${NC}"
    read -p "是否继续? (y/n): " continue_without_backup
    if [[ ! $continue_without_backup =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}操作已取消${NC}"
        exit 0
    fi
    echo -e "${RED}警告: 继续操作将清空项目目录，但没有备份可以还原!${NC}"
    read -p "确认继续? (y/n): " confirm_continue
    if [[ ! $confirm_continue =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}操作已取消${NC}"
        exit 0
    fi
    # 创建一个空的临时目录作为源
    mkdir -p "$TEMP_DIR/empty"
    EXTRACTED_DIR="$TEMP_DIR/empty"
    BACKUP_FILENAME="无备份"
else
    # 正常流程，有备份文件
    echo -e "${GREEN}找到 ${#BACKUP_FILES[@]} 个备份文件${NC}"
fi

# 显示备份选择菜单
function show_menu {
    clear
    echo -e "${CYAN}=== $PROJECT_NAME 项目还原 ===${NC}"
    echo -e "${YELLOW}请选择要还原的备份版本:${NC}"
    echo ""

    # 计算当前页面应该显示的备份范围
    local page_size=15
    local start_idx=$((PAGE * page_size))
    local end_idx=$(( (PAGE + 1) * page_size - 1 ))

    # 确保结束索引不超过备份文件总数
    if [ $end_idx -ge $TOTAL ]; then
        end_idx=$((TOTAL - 1))
    fi

    # 显示页面信息
    if [ $TOTAL -gt $page_size ]; then
        local total_pages=$(( (TOTAL + page_size - 1) / page_size ))
        echo -e "${CYAN}第 $((PAGE + 1))/$total_pages 页 (共 $TOTAL 个备份)${NC}"
        echo -e "${YELLOW}使用PageUp/PageDown翻页${NC}"
        echo ""
    fi

    # 显示当前页面的备份
    for i in $(seq $start_idx $end_idx); do
        FILENAME=$(basename "${BACKUP_FILES[$i]}")
        FILESIZE=$(du -h "${BACKUP_FILES[$i]}" | cut -f1)
        DATE_PART=$(echo "$FILENAME" | grep -o "[0-9]\{8\}_[0-9]\{6\}")
        FORMATTED_DATE=$(date -j -f "%Y%m%d_%H%M%S" "$DATE_PART" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "$DATE_PART")
        DESCRIPTION=$(echo "$FILENAME" | sed -E "s/${PROJECT_NAME}_[0-9]{8}_[0-9]{6}_(.*)\.zip/\1/" | tr '_' ' ')

        if [ $i -eq $SELECTED ]; then
            echo -e "${GREEN}→ $((i+1)). $FORMATTED_DATE | $FILESIZE | $DESCRIPTION${NC}"
        else
            echo -e "  $((i+1)). $FORMATTED_DATE | $FILESIZE | $DESCRIPTION"
        fi
    done

    echo ""
    echo -e "${YELLOW}使用上下箭头键选择，Fn+上下箭头翻页，按Enter确认，按Ctrl+C取消${NC}"
}

# 如果有备份文件，显示选择菜单
if [ ${#BACKUP_FILES[@]} -gt 0 ]; then
    # 初始化选择为第一个（最新的）备份
    SELECTED=0
    TOTAL=${#BACKUP_FILES[@]}
    PAGE=0  # 初始化页码为0（第一页）

    # 计算页面大小和总页数
    PAGE_SIZE=15
    TOTAL_PAGES=$(( (TOTAL + PAGE_SIZE - 1) / PAGE_SIZE ))

    # 显示菜单并处理键盘输入
    show_menu

    # 使用read命令捕获键盘输入
    while true; do
        # 使用read命令捕获单个按键
        read -rsn1 key

        # 检查是否是特殊键序列（如箭头键、PageUp/PageDown等）
        if [[ $key == $'\x1b' ]]; then
            read -rsn2 key
            if [[ $key == "[A" ]]; then  # 上箭头
                ((SELECTED--))
                if [ $SELECTED -lt 0 ]; then
                    SELECTED=$((TOTAL-1))
                fi
                # 计算SELECTED所在的页面
                PAGE=$((SELECTED / PAGE_SIZE))
                show_menu
            elif [[ $key == "[B" ]]; then  # 下箭头
                ((SELECTED++))
                if [ $SELECTED -ge $TOTAL ]; then
                    SELECTED=0
                fi
                # 计算SELECTED所在的页面
                PAGE=$((SELECTED / PAGE_SIZE))
                show_menu
            elif [[ $key == "[5~" || $key == "[1;2A" ]]; then  # PageUp 或 Fn+上箭头
                # 向上翻页，但保持选择项在相对位置
                local old_relative_pos=$((SELECTED % PAGE_SIZE))
                ((PAGE--))
                if [ $PAGE -lt 0 ]; then
                    PAGE=$((TOTAL_PAGES-1))
                fi
                # 计算新的选择项
                SELECTED=$((PAGE * PAGE_SIZE + old_relative_pos))
                # 确保不超出范围
                if [ $SELECTED -ge $TOTAL ]; then
                    SELECTED=$((TOTAL-1))
                fi
                show_menu
            elif [[ $key == "[6~" || $key == "[1;2B" ]]; then  # PageDown 或 Fn+下箭头
                # 向下翻页，但保持选择项在相对位置
                local old_relative_pos=$((SELECTED % PAGE_SIZE))
                ((PAGE++))
                if [ $PAGE -ge $TOTAL_PAGES ]; then
                    PAGE=0
                fi
                # 计算新的选择项
                SELECTED=$((PAGE * PAGE_SIZE + old_relative_pos))
                # 确保不超出范围
                if [ $SELECTED -ge $TOTAL ]; then
                    SELECTED=$((TOTAL-1))
                fi
                show_menu
            fi
        elif [[ $key == "" ]]; then  # Enter键
            break
        fi
    done

    # 获取选择的备份文件
    SELECTED_BACKUP="${BACKUP_FILES[$SELECTED]}"
    BACKUP_FILENAME=$(basename "$SELECTED_BACKUP")

    # 确认还原
    clear
    echo -e "${CYAN}=== $PROJECT_NAME 项目还原 ===${NC}"
    echo -e "${YELLOW}您选择了以下备份:${NC}"
    echo -e "文件: ${GREEN}$BACKUP_FILENAME${NC}"
    echo -e "大小: ${GREEN}$(du -h "$SELECTED_BACKUP" | cut -f1)${NC}"
    echo ""
    # 直接继续执行，不再询问确认

    # 解压备份文件到临时目录
    echo -e "${BLUE}解压备份文件...${NC}"
    unzip -q "$SELECTED_BACKUP" -d "$TEMP_DIR"

    # 获取解压后的项目目录（应该是项目名称的目录）
    EXTRACTED_DIR="$TEMP_DIR/$PROJECT_NAME"

    # 检查项目目录是否存在
    if [ ! -d "$EXTRACTED_DIR" ]; then
        # 如果没有找到项目名称目录，尝试查找任何解压出的目录
        EXTRACTED_DIR=$(find "$TEMP_DIR" -type d -depth 1)

        if [ -z "$EXTRACTED_DIR" ]; then
            echo -e "${RED}错误: 无法找到解压后的目录${NC}"
            rm -rf "$TEMP_DIR"
            exit 1
        fi

        echo -e "${YELLOW}警告: 未找到预期的项目目录结构，使用找到的第一个目录: ${CYAN}$EXTRACTED_DIR${NC}"
    fi
else
    # 没有备份文件，已经在前面设置了EXTRACTED_DIR为空目录
    clear
    echo -e "${CYAN}=== $PROJECT_NAME 项目还原 ===${NC}"
    echo -e "${YELLOW}没有可用的备份文件，将清空项目目录${NC}"
    echo ""
fi

# 临时目录已在前面创建

# 删除当前项目中除了还原脚本以外的所有文件
echo -e "${BLUE}删除当前项目文件...${NC}"
find "$PROJECT_DIR" -mindepth 1 -not -name "$RESTORE_SCRIPT" -not -path "*/\.*" | xargs rm -rf

# 添加2秒延迟，让用户能够看到删除效果
echo -e "${YELLOW}等待2秒，观察删除效果...${NC}"
sleep 2

# 显示删除后的文件列表（只显示文件数量）
echo -e "${BLUE}删除后剩余文件数量: $(find "$PROJECT_DIR" -mindepth 1 | wc -l)${NC}"

# 复制解压后的文件到项目目录
echo -e "${BLUE}还原项目文件...${NC}"

# 保存当前脚本的内容
SCRIPT_CONTENT=$(cat "$PROJECT_DIR/$RESTORE_SCRIPT")

# 复制文件，保持目录结构，但排除还原脚本
echo -e "${BLUE}正在复制文件，保持原有目录结构...${NC}"
# 使用rsync保持目录结构并排除还原脚本
rsync -a --exclude="$RESTORE_SCRIPT" "$EXTRACTED_DIR/" "$PROJECT_DIR/"

# 如果备份中包含还原脚本，则不覆盖当前的脚本
if [ -f "$EXTRACTED_DIR/$RESTORE_SCRIPT" ]; then
    echo -e "${YELLOW}备份中包含还原脚本，但不会覆盖当前脚本${NC}"
fi

# 添加2秒延迟，让用户能够看到复制效果
echo -e "${YELLOW}等待2秒，观察复制效果...${NC}"
sleep 2

# 显示复制后的文件列表（只显示文件数量）
echo -e "${BLUE}复制后项目文件数量: $(find "$PROJECT_DIR" -mindepth 1 | wc -l)${NC}"

# 清理临时目录
echo -e "${BLUE}清理临时目录...${NC}"
rm -rf "$TEMP_DIR"

# 完成
echo -e "${GREEN}还原成功完成!${NC}"
echo -e "项目已从备份 ${CYAN}$BACKUP_FILENAME${NC} 还原"
echo -e "还原时间: $(date)"

# 直接退出，不等待按键
exit 0
EOF
            echo -e "  ${GREEN}已创建 restore_project.sh${NC}"
        fi

        # 设置执行权限
        chmod +x "$TARGET_DIR/$file"
    fi
done

# 设置执行权限（如果文件存在）
echo -e "${YELLOW}设置执行权限...${NC}"
if [ -f "$TARGET_DIR/backup_project.sh" ]; then
    chmod +x "$TARGET_DIR/backup_project.sh"
    echo -e "  已设置 ${CYAN}backup_project.sh${NC} 执行权限"
fi

if [ -f "$TARGET_DIR/restore_project.sh" ]; then
    chmod +x "$TARGET_DIR/restore_project.sh"
    echo -e "  已设置 ${CYAN}restore_project.sh${NC} 执行权限"
fi

# 创建.vscode目录和任务配置
echo -e "${YELLOW}创建VS Code任务配置...${NC}"
mkdir -p "$TARGET_DIR/.vscode"
cat > "$TARGET_DIR/.vscode/tasks.json" << 'EOF'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "备份项目",
            "type": "shell",
            "command": "${workspaceFolder}/backup_project.sh",
            "problemMatcher": [],
            "presentation": {
                "reveal": "always",
                "panel": "shared",
                "focus": true,
                "echo": true,
                "showReuseMessage": false,
                "clear": true
            },
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "label": "还原项目",
            "type": "shell",
            "command": "${workspaceFolder}/restore_project.sh",
            "problemMatcher": [],
            "presentation": {
                "reveal": "always",
                "panel": "shared",
                "focus": true,
                "echo": true,
                "showReuseMessage": false,
                "clear": true
            },
            "group": {
                "kind": "none"
            }
        }
    ]
}
EOF

# 配置VS Code全局快捷键
echo -e "${YELLOW}配置VS Code全局快捷键...${NC}"

# 确定VS Code的keybindings.json文件位置
if [ "$(uname)" == "Darwin" ]; then
    # macOS
    VSCODE_KEYBINDINGS="$HOME/Library/Application Support/Code/User/keybindings.json"
    VSCODE_INSIDERS_KEYBINDINGS="$HOME/Library/Application Support/Code - Insiders/User/keybindings.json"
elif [ "$(expr substr $(uname -s) 1 5)" == "Linux" ]; then
    # Linux
    VSCODE_KEYBINDINGS="$HOME/.config/Code/User/keybindings.json"
    VSCODE_INSIDERS_KEYBINDINGS="$HOME/.config/Code - Insiders/User/keybindings.json"
elif [ "$(expr substr $(uname -s) 1 10)" == "MINGW32_NT" ] || [ "$(expr substr $(uname -s) 1 10)" == "MINGW64_NT" ]; then
    # Windows
    VSCODE_KEYBINDINGS="$APPDATA/Code/User/keybindings.json"
    VSCODE_INSIDERS_KEYBINDINGS="$APPDATA/Code - Insiders/User/keybindings.json"
fi

# 确定使用哪个keybindings.json文件
KEYBINDINGS_FILE=""
if [ -f "$VSCODE_KEYBINDINGS" ]; then
    KEYBINDINGS_FILE="$VSCODE_KEYBINDINGS"
    echo -e "  找到VS Code快捷键配置: ${CYAN}$KEYBINDINGS_FILE${NC}"
elif [ -f "$VSCODE_INSIDERS_KEYBINDINGS" ]; then
    KEYBINDINGS_FILE="$VSCODE_INSIDERS_KEYBINDINGS"
    echo -e "  找到VS Code Insiders快捷键配置: ${CYAN}$KEYBINDINGS_FILE${NC}"
else
    # 如果文件不存在，创建一个新的
    KEYBINDINGS_FILE="$VSCODE_KEYBINDINGS"
    echo -e "  未找到现有快捷键配置，将创建新配置: ${CYAN}$KEYBINDINGS_FILE${NC}"
    mkdir -p "$(dirname "$KEYBINDINGS_FILE")"
    echo "[]" > "$KEYBINDINGS_FILE"
fi

# 备份原始keybindings.json
BACKUP_FILE="${KEYBINDINGS_FILE}.backup_$(date +%Y%m%d_%H%M%S)"
cp "$KEYBINDINGS_FILE" "$BACKUP_FILE"
echo -e "  已备份原始快捷键配置: ${CYAN}$BACKUP_FILE${NC}"

# 添加备份和还原快捷键
# 使用临时文件避免管道问题
TMP_FILE=$(mktemp)

# 检查文件是否为空或只包含空白字符
if [ ! -s "$KEYBINDINGS_FILE" ] || [ "$(cat "$KEYBINDINGS_FILE" | tr -d '[:space:]')" = "" ]; then
    # 文件为空，创建新的JSON数组
    cat > "$TMP_FILE" << EOF
[
    {
        "key": "ctrl+shift+b",
        "command": "workbench.action.tasks.runTask",
        "args": "备份项目"
    },
    {
        "key": "ctrl+shift+r",
        "command": "workbench.action.tasks.runTask",
        "args": "还原项目"
    }
]
EOF
else
    # 文件不为空，检查是否已经包含这些快捷键
    BACKUP_EXISTS=$(grep -c "备份项目" "$KEYBINDINGS_FILE" || echo "0")
    RESTORE_EXISTS=$(grep -c "还原项目" "$KEYBINDINGS_FILE" || echo "0")

    if [ "$BACKUP_EXISTS" -eq "0" ] && [ "$RESTORE_EXISTS" -eq "0" ]; then
        # 快捷键不存在，添加它们
        # 移除最后的 "]"，添加新的快捷键，然后添加 "]"
        sed '$ s/]/,/' "$KEYBINDINGS_FILE" > "$TMP_FILE"
        cat >> "$TMP_FILE" << EOF
    {
        "key": "ctrl+shift+b",
        "command": "workbench.action.tasks.runTask",
        "args": "备份项目"
    },
    {
        "key": "ctrl+shift+r",
        "command": "workbench.action.tasks.runTask",
        "args": "还原项目"
    }
]
EOF
    else
        # 快捷键已存在，不做更改
        cp "$KEYBINDINGS_FILE" "$TMP_FILE"
        echo -e "  ${YELLOW}快捷键已存在，跳过配置${NC}"
    fi
fi

# 将临时文件移动到keybindings.json
mv "$TMP_FILE" "$KEYBINDINGS_FILE"

echo -e "${GREEN}✓ VS Code快捷键配置完成!${NC}"
echo -e "${BLUE}已添加以下快捷键:${NC}"
echo -e "  - ${GREEN}Ctrl+Shift+B${NC}: 运行备份项目任务"
echo -e "  - ${GREEN}Ctrl+Shift+R${NC}: 运行还原项目任务"

echo -e "\n${GREEN}✓ 安装完成!${NC}"
echo -e "${YELLOW}备份和还原功能已安装到: $TARGET_DIR${NC}"
echo -e "${YELLOW}VS Code任务配置已创建: $TARGET_DIR/.vscode/tasks.json${NC}"
echo -e "${YELLOW}VS Code全局快捷键已配置: $KEYBINDINGS_FILE${NC}"
echo ""
echo -e "${CYAN}使用方法:${NC}"
echo -e "1. 备份项目: ${GREEN}./backup_project.sh${NC} 或按 ${GREEN}Ctrl+Shift+B${NC}"
echo -e "2. 还原项目: ${GREEN}./restore_project.sh${NC} 或按 ${GREEN}Ctrl+Shift+R${NC}"
echo -e "3. 在VS Code中: ${GREEN}按F1 -> 输入'Tasks: Run Task' -> 选择'备份项目'或'还原项目'${NC}"
echo ""
echo -e "${YELLOW}注意: 所有备份文件将保存在 ~/my_project_backups 目录下${NC}"

exit 0
