//
//  NaviBatchApp.swift
//  NaviBatch
//
//  Created by <PERSON> on 19/4/2025.
//

import SwiftUI
import SwiftData
import os.log
import CoreLocation
import Foundation
import Photos

// Add a logger
private let logger = os.Logger(subsystem: "com.jasonkwok.NaviBatch", category: "SwiftData")

// 统一日志管理 - 使用Logger类进行统一管理
func logInfo(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.info(message, type: .info, file: file, function: function, line: line)
}

func logError(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.error(message, type: .error, file: file, function: function, line: line)
}

func logWarning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.warning(message, type: .warning, file: file, function: function, line: line)
}



@main
struct NaviBatchApp: App {
    // 使用A<PERSON>Storage跟踪当前Schema版本
    @AppStorage("currentSchemaVersion") private var currentSchemaVersion: String = "1.0.0"

    // 启动流程状态管理
    @AppStorage("hasCompletedOnboarding") private var hasCompletedOnboarding: Bool = false
    @AppStorage("appLaunchCount") private var appLaunchCount: Int = 0
    @AppStorage("lastWelcomeVersion") private var lastWelcomeVersion: String = ""
    @State private var currentLaunchPhase: LaunchPhase = .splash

    // 当前应用支持的最新Schema版本
    private let latestSchemaVersion: String = "2.0.0"

    // 启动阶段枚举
    enum LaunchPhase {
        case splash
        case welcome
        case onboarding
        case main
    }

    // 试用推广管理器
    @StateObject private var trialPromotionManager = TrialPromotionManager.shared

    // 使用类来保存全局观察者，因为结构体不可变
    class AppState {
        static let shared = AppState()
        var globalDataChangeObserver: NSObjectProtocol? = nil
    }

    init() {
        // 🔥 初始化Firebase
        FirebaseConfig.shared.configure()

        // 设置沙盒错误过滤器
        setupSandboxErrorFilter()

        // 请求所有必要的权限
        requestPermissions()

        // 验证订阅状态
        validateSubscriptionOnStartup()

        // 初始化启动流程
        initializeLaunchFlow()

        // 检查是否需要运行数据库迁移测试
        #if DEBUG
        if CommandLine.arguments.contains("--test-migrations") {
            logInfo("检测到--test-migrations参数，将运行数据库迁移测试")
            Task {
                await DatabaseMigrationTester.runAllTests()
            }
        }
        #endif
    }

    // MARK: - 沙盒错误过滤

    /// 设置沙盒错误过滤器
    private func setupSandboxErrorFilter() {
        #if DEBUG
        // 在调试模式下，我们仍然显示这些错误，但会标记为已知问题
        logInfo("NaviBatchApp - 已设置沙盒错误过滤器（调试模式）")
        #else
        // 在生产模式下，这些错误会被完全过滤
        logInfo("NaviBatchApp - 已设置沙盒错误过滤器（生产模式）")
        #endif
    }

    // MARK: - 订阅验证

    /// 应用启动时验证订阅状态
    private func validateSubscriptionOnStartup() {
        #if DEBUG
        print("[DEBUG] NaviBatchApp - 应用启动时验证订阅状态")
        #endif

        // 延迟验证，确保应用完全启动
        Task {
            // 等待3秒，让应用完全初始化
            try? await Task.sleep(nanoseconds: 3_000_000_000)

            // 验证订阅收据
            SubscriptionManager.shared.validateSubscriptionReceipt()
        }
    }

    // 设置全局数据变化监听器
    private func setupGlobalDataChangeObserver() {
        // 移除旧的观察者（如果存在）
        if let observer = AppState.shared.globalDataChangeObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        // 注意：移除了RouteDataChanged监听器，因为RouteViewModel已经有自己的监听器
        // 避免重复处理导致无限循环
        logInfo("NaviBatchApp - 已移除全局RouteDataChanged监听器，避免与RouteViewModel重复处理")
    }

    // 请求所有必要的权限
    private func requestPermissions() {




        // 请求照片库权限
        PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
            let statusString: String
            switch status {
            case .authorized: statusString = "已授权"
            case .denied: statusString = "已拒绝"
            case .restricted: statusString = "受限制"
            case .notDetermined: statusString = "未决定"
            case .limited: statusString = "有限访问"
            @unknown default: statusString = "未知状态"
            }
            logInfo("NaviBatchApp - 照片库权限状态: \(statusString)")
        }

        // 请求相机权限
        AVCaptureDevice.requestAccess(for: .video) { granted in
            logInfo("NaviBatchApp - 相机权限状态: \(granted ? "已授权" : "未授权")")
        }

        // 请求位置权限并初始化LocationManager
        LocationManager.shared.requestLocationPermission()
        LocationManager.shared.startUpdatingLocation()

        // 🌍 改进的位置策略：优先使用真实位置，只在必要时使用默认位置
        #if targetEnvironment(simulator)
        // 设置一个标志，表明我们在模拟器中运行
        UserDefaults.standard.set(true, forKey: "IsRunningInSimulator")

        // 🔧 不再强制使用默认位置，让LocationManager根据权限状态智能选择
        // 如果用户授权了位置权限，使用真实位置
        // 如果用户拒绝了位置权限，自动回退到基于设备区域的默认位置
        UserDefaults.standard.set(false, forKey: "ForceUseDefaultLocation")

        logInfo("NaviBatchApp - 模拟器环境：将根据位置权限智能选择位置源")
        #else
        // 在真机上运行时，确保标志被重置
        UserDefaults.standard.set(false, forKey: "IsRunningInSimulator")
        UserDefaults.standard.set(false, forKey: "ForceUseDefaultLocation")
        #endif

        // 添加位置设置调试信息
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            LocationManager.shared.debugLocationSettings()
        }

        logInfo("NaviBatchApp - 已请求位置权限并初始化LocationManager")

        // 请求相机权限 - 在需要时自动请求
        // AVCaptureDevice.requestAccess(for: .video) { granted in
        //     logInfo("NaviBatchApp - 相机权限状态: \(granted ? "已授权" : "未授权")")
        // }
    }

    // 检查并执行数据库迁移
    private func checkAndPerformMigrationIfNeeded() {
        // 检查是否在预览环境中运行
        let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

        // 只在非预览环境中执行版本检查
        if !isPreview && currentSchemaVersion != latestSchemaVersion {
            logInfo("检测到Schema版本变更 (当前: \(currentSchemaVersion), 最新: \(latestSchemaVersion))")

            // 执行额外的迁移步骤（如果需要）
            Task { @MainActor in
                // 执行自定义迁移逻辑
                await performCustomMigrations()

                // 更新版本号
                currentSchemaVersion = latestSchemaVersion
                logInfo("Schema版本已更新为: \(latestSchemaVersion)")
            }
        }
    }

    // 执行自定义迁移
    @MainActor
    private func performCustomMigrations() async {
        logInfo("执行自定义数据迁移...")

        // 执行vehiclePosition字段迁移
        await DatabaseMigrator.migrateVehiclePositions(in: sharedModelContainer.mainContext)

        // 执行Route的isOptimized字段迁移
        await DatabaseMigrator.migrateRouteOptimizedFlag(in: sharedModelContainer.mainContext)

        logInfo("自定义数据迁移完成")
    }

    // 清空数据库的方法
    private func clearDatabase() {
        // 检查是否在预览环境中运行
        let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

        // 在预览环境中不执行清空操作
        if isPreview {
            logInfo("预览环境中不执行数据库清空操作")
            return
        }

        // 获取数据库文件路径
        if let url = sharedModelContainer.configurations.first?.url {
            // 检查是否是内存数据库
            if url.path == "/dev/null" {
                logInfo("当前使用内存数据库，无需清空")
                return
            }

            do {
                // 尝试删除数据库文件
                try FileManager.default.removeItem(at: url)
                logInfo("成功删除数据库文件: \(url.path)")

                // 移除成功提示Toast - 不再显示调试信息
            } catch {
                logError("删除数据库文件失败: \(error.localizedDescription)")
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            Group {
                switch currentLaunchPhase {
                case .splash:
                    SplashView {
                        advanceToNextPhase()
                    }
                case .welcome:
                    WelcomeView {
                        advanceToNextPhase()
                    }
                case .onboarding:
                    OnboardingView {
                        completeOnboarding()
                    }
                case .main:
                    ZStack {
                        // 只有在没有显示试用推广时才显示RouteView
                        if !trialPromotionManager.shouldShowPromotion {
                            RouteView()
                                .ignoresSafeArea(.all)
                                .localizedLayout() // 应用本地化布局修饰符
                                .overlay(
                                    // 版本更新提示弹窗
                                    Group {
                                        if AppUpdateService.shared.showUpdatePrompt,
                                           let updateInfo = AppUpdateService.shared.updateInfo {
                                            AppUpdatePromptView(updateInfo: updateInfo)
                                                .zIndex(1000)
                                        }
                                    }
                                )
                                .overlay(
                                    // 🎯 重新添加GlobalToastView用于显示复制成功等提示
                                    GlobalToastView()
                                        .zIndex(999)
                                )
                        }

                        // 试用推广页面 - 作为全屏模态显示
                        if trialPromotionManager.shouldShowPromotion {
                            TrialPromotionView()
                                .zIndex(2000) // 最高优先级
                        }
                    }
                .onAppear {
                    // 检查并执行数据库迁移
                    checkAndPerformMigrationIfNeeded()

                    // 清除不完整地址的缓存（修复之前错误缓存的地址）
                    GeocodingService.shared.clearIncompleteAddressCache()

                    // 生产环境移除调试代码
                    #if DEBUG
                    // 在应用程序启动时打印数据库内容（仅调试模式）
                    Task { @MainActor in
                        // 给一些时间让数据库初始化
                        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                        await DatabaseDebugger.printAllRoutes()

                        // 🧪 测试单位信息提取功能
                        DeliveryPoint.testUnitExtraction()

                        // 🔄 运行数据迁移，为现有地址提取单位信息
                        do {
                            let updatedCount = try DeliveryPoint.migrateUnitNumbers(context: sharedModelContainer.mainContext)
                            print("✅ 数据迁移完成：更新了 \(updatedCount) 个地址的单位信息")
                        } catch {
                            print("❌ 数据迁移失败: \(error.localizedDescription)")
                        }
                    }

                    #endif

                    // 添加全局数据变化监听器
                    setupGlobalDataChangeObserver()

                    // 添加对导航通知的监听
                    NotificationCenter.default.addObserver(
                        forName: Notification.Name("NavigateToRouteView"),
                        object: nil,
                        queue: .main
                    ) { _ in
                        // 当收到导航通知时，需要确保RouteView被正确激活
                        logInfo("NaviBatchApp - 收到导航通知，确保回到RouteView")

                        // 发送一个额外的通知，强制刷新RouteView
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            NotificationCenter.default.post(
                                name: Notification.Name("ForceRefreshRouteView"),
                                object: nil
                            )
                            logInfo("NaviBatchApp - 已发送强制刷新RouteView通知")

                            // 同时发送关闭菜单并显示路线的通知
                            NotificationCenter.default.post(
                                name: Notification.Name("CloseMenuAndShowRoute"),
                                object: nil
                            )
                            logInfo("NaviBatchApp - 已发送关闭菜单并显示路线通知")
                        }
                    }

                    // 添加对路线详情活跃状态通知的监听
                    NotificationCenter.default.addObserver(
                        forName: Notification.Name("EnsureRouteViewActive"),
                        object: nil,
                        queue: .main
                    ) { _ in
                        // 这个通知确保当用户选择路线后，RouteView能够正确显示
                        logInfo("NaviBatchApp - 收到路线详情活跃通知，确保RouteView正确显示")
                    }

                    // 添加对清空数据库通知的监听
                    NotificationCenter.default.addObserver(
                        forName: Notification.Name("ClearDatabase"),
                        object: nil,
                        queue: .main
                    ) { _ in
                        logInfo("NaviBatchApp - 收到清空数据库通知")
                        self.clearDatabase()
                    }

                    // 添加对语言更改通知的监听
                    NotificationCenter.default.addObserver(
                        forName: Notification.Name("LanguageChanged"),
                        object: nil,
                        queue: .main
                    ) { _ in
                        logInfo("NaviBatchApp - 收到语言更改通知")

                        // 移除重启提示Toast - 不再显示调试信息
                    }

                    // 检查是否显示试用推广页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        TrialPromotionManager.shared.checkShouldShowPromotion()
                    }
                }
                }
            }
            .animation(.easeInOut(duration: 0.5), value: currentLaunchPhase)
            .modelContainer(sharedModelContainer)
        }
    }

    // MARK: - 启动流程管理

    /// 初始化启动流程
    private func initializeLaunchFlow() {
        appLaunchCount += 1
        let currentAppVersion = AppEnvironment.appVersion

        #if DEBUG
        // 🔧 开发测试：取消注释下面这行可以重置引导状态，重新体验新手tour
        // hasCompletedOnboarding = false
        // lastWelcomeVersion = ""

        logInfo("NaviBatchApp - 应用启动次数: \(appLaunchCount)")
        logInfo("NaviBatchApp - 是否完成引导: \(hasCompletedOnboarding)")
        logInfo("NaviBatchApp - 当前版本: \(currentAppVersion)")
        logInfo("NaviBatchApp - 上次欢迎界面版本: \(lastWelcomeVersion)")
        #endif

        // 决定启动阶段
        if hasCompletedOnboarding {
            // 检查是否是当前版本的首次启动
            if shouldShowWelcomeForVersion(currentAppVersion) {
                logInfo("NaviBatchApp - 当前版本首次启动，显示欢迎界面供用户选择")
                currentLaunchPhase = .splash  // 先显示splash，然后到welcome让用户选择
            } else {
                // 当前版本非首次启动，splash后直接进入主界面
                logInfo("NaviBatchApp - 当前版本非首次启动，splash后直接进入主界面")
                currentLaunchPhase = .splash  // splash后直接到main
            }
        } else {
            // 首次使用，完整引导流程
            logInfo("NaviBatchApp - 首次使用，开始完整引导流程")
            currentLaunchPhase = .splash
        }
    }

    /// 检查是否需要为当前版本显示欢迎界面
    private func shouldShowWelcomeForVersion(_ currentVersion: String) -> Bool {
        // 如果从未显示过欢迎界面，或者版本发生了变化，则显示
        return lastWelcomeVersion.isEmpty || lastWelcomeVersion != currentVersion
    }

    /// 前进到下一个启动阶段
    private func advanceToNextPhase() {
        let currentAppVersion = AppEnvironment.appVersion

        withAnimation(.easeInOut(duration: 0.5)) {
            switch currentLaunchPhase {
            case .splash:
                if hasCompletedOnboarding {
                    // 已完成引导的用户
                    if shouldShowWelcomeForVersion(currentAppVersion) {
                        // 当前版本首次启动，显示欢迎界面让用户选择
                        currentLaunchPhase = .welcome
                        logInfo("NaviBatchApp - 当前版本首次启动，显示欢迎界面")
                    } else {
                        // 当前版本非首次启动，直接进入主界面
                        currentLaunchPhase = .main
                        logInfo("NaviBatchApp - 当前版本非首次启动，直接进入主界面")
                    }
                } else {
                    // 首次使用，继续到欢迎界面
                    currentLaunchPhase = .welcome
                    logInfo("NaviBatchApp - 首次使用，显示欢迎界面")
                }
            case .welcome:
                // 从欢迎界面点击按钮
                #if DEBUG
                logInfo("NaviBatchApp - 从欢迎界面前进，当前hasCompletedOnboarding: \(hasCompletedOnboarding)")
                #endif

                if hasCompletedOnboarding {
                    // 已完成过引导，记录版本并进入主界面
                    lastWelcomeVersion = currentAppVersion
                    currentLaunchPhase = .main
                    logInfo("NaviBatchApp - 版本欢迎界面完成，进入主界面")
                } else {
                    // 首次使用，继续到引导界面
                    currentLaunchPhase = .onboarding
                    logInfo("NaviBatchApp - 首次使用，进入引导界面")
                }
            case .onboarding:
                completeOnboarding()
            case .main:
                break
            }
        }
    }

    /// 完成引导流程
    private func completeOnboarding() {
        #if DEBUG
        logInfo("NaviBatchApp - 开始完成引导流程")
        logInfo("NaviBatchApp - 当前hasCompletedOnboarding: \(hasCompletedOnboarding)")
        #endif

        // 使用DispatchQueue.main.async确保在主线程上执行
        DispatchQueue.main.async { [self] in
            hasCompletedOnboarding = true
            // 记录当前版本，避免下次启动时重复显示欢迎界面
            lastWelcomeVersion = AppEnvironment.appVersion

            #if DEBUG
            logInfo("NaviBatchApp - 设置hasCompletedOnboarding为true")
            logInfo("NaviBatchApp - 设置lastWelcomeVersion为: \(lastWelcomeVersion)")

            // 强制同步UserDefaults
            UserDefaults.standard.synchronize()
            logInfo("NaviBatchApp - 已强制同步UserDefaults")

            // 验证设置是否成功
            let savedValue = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")
            logInfo("NaviBatchApp - UserDefaults中的hasCompletedOnboarding值: \(savedValue)")
            #endif

            withAnimation(.easeInOut(duration: 0.5)) {
                currentLaunchPhase = .main
            }

            #if DEBUG
            logInfo("NaviBatchApp - 引导流程完成，进入主界面")
            logInfo("NaviBatchApp - 最终hasCompletedOnboarding: \(hasCompletedOnboarding)")
            #endif
        }
    }
}

// MARK: - 全局数据库容器
private let sharedModelContainer: ModelContainer = {
#if DEBUG
    // 检查是否在预览环境中运行
    let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

    // 只在预览环境中使用内存数据库，模拟器中使用持久化存储
    if isPreview {
        print("[INFO] 检测到预览环境，使用内存数据库")
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
        return try! ModelContainer(for: schema, configurations: [config])
    }

    // 🔧 检查是否需要强制重建数据库
    if SafeDatabaseInitializer.shouldForceRecreate() {
        print("[WARNING] 检测到强制重建标志，使用安全初始化器")
        return SafeDatabaseInitializer.createSafeContainer()
    }
#endif
    // 正常运行时和模拟器中都用持久化数据库
    print("[INFO] 使用持久化数据库")

    // 创建模型配置 - 简化处理，直接使用Schema
    let modelConfiguration = ModelConfiguration(isStoredInMemoryOnly: false)
    let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])

    do {
        // 创建容器 - 让SwiftData自动处理字段变更
        let container = try ModelContainer(
            for: schema,
            configurations: [modelConfiguration]
        )

        #if DEBUG
        // 打印数据库路径（仅调试模式）
        if let url = container.configurations.first?.url {
            print("[INFO] 数据库路径: \(url.path)")
        }

        // 检查是否需要强制重新创建数据库（添加新字段）
        // 注意：这会删除所有现有数据！仅在开发阶段使用
        let forceRecreate = ProcessInfo.processInfo.arguments.contains("--force-recreate-db")
        if forceRecreate {
            print("[WARNING] 检测到 --force-recreate-db 参数，将重新创建数据库")
            ForceDBRecreation.recreateDatabase()
            return container // 返回重新创建的容器
        }
        #endif

        #if DEBUG
        // 在调试模式下测试新字段
        Task { @MainActor in
            print("[INFO] ✅ 数据库创建成功，SwiftData自动处理字段变更")

            // 测试时间字段是否可用
            await testTimeFieldsAvailabilityGlobal(in: container.mainContext)

            // 测试代码已移除 - 保持代码简洁
        }
        #endif

        return container
    } catch {
        print("[ERROR] 创建持久化数据库失败: \(error.localizedDescription)")
        print("[ERROR] 详细错误: \(error)")

        // 如果创建失败，尝试删除数据库文件并重新创建
        print("[WARNING] 尝试删除数据库文件并重新创建")

        // 获取数据库文件路径
        let url = URL.applicationSupportDirectory.appending(path: "default.store")

        do {
            // 尝试删除数据库文件
            try FileManager.default.removeItem(at: url)
            print("[INFO] 成功删除数据库文件: \(url.path)")

            // 重新创建容器，使用相同的Schema
            let newContainer = try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )
            print("[INFO] 成功重新创建数据库")
            return newContainer
        } catch {
            print("[ERROR] 删除或重新创建数据库失败: \(error.localizedDescription)")
            // 如果删除和重新创建失败，回退到内存数据库
            print("[WARNING] 回退到内存数据库")
            let fallbackConfig = ModelConfiguration(isStoredInMemoryOnly: true)
            let fallbackSchema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
            return try! ModelContainer(for: fallbackSchema, configurations: [fallbackConfig])
        }
    }
}()

// MARK: - 全局测试函数
@MainActor
func testTimeFieldsAvailabilityGlobal(in context: ModelContext) async {
    print("[INFO] 测试时间字段可用性...")

    do {
        // 创建一个测试点
        let testPoint = DeliveryPoint(
            sort_number: 999999,
            streetName: "测试地址_TimeFields",
            latitude: 0,
            longitude: 0
        )

        // 尝试设置时间字段
        testPoint.scheduledDeliveryTime = "测试时间"
        testPoint.deliveryTimeSlot = "测试时间段"
        testPoint.deliveryDate = "测试日期"

        context.insert(testPoint)
        try context.save()

        print("[SUCCESS] ✅ 时间字段测试成功 - 字段可正常使用")

        // 清理测试数据
        context.delete(testPoint)
        try context.save()

    } catch {
        print("[ERROR] ❌ 时间字段测试失败: \(error.localizedDescription)")
        print("[INFO] 这可能表示需要重建数据库")
    }
}