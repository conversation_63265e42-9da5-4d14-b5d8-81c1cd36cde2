import Foundation

/// 排序号和标签常量定义
/// 统一管理所有排序号相关的标识符，避免硬编码字符串导致的不一致
struct SortNumberConstants {

    // MARK: - 排序号标签

    /// 内部连续序号标签 - 用于sort_number字段
    /// 示例: "|SORT:113|"
    static let INTERNAL_SORT_TAG = "SORT"

    /// 第三方原始排序号标签 - 用于thirdPartySortNumber字段
    /// 示例: "|THIRD_PARTY_SORT:1|"
    static let THIRD_PARTY_SORT_TAG = "THIRD_PARTY_SORT"

    // MARK: - 其他信息标签

    /// 追踪号码标签
    /// 示例: "|TRACK:GF6145584384732|"
    static let TRACKING_TAG = "TRACK"

    /// 客户信息标签
    /// 示例: "|CUSTOMER:<PERSON>|"
    static let CUSTOMER_TAG = "CUSTOMER"

    /// 配送时间标签
    /// 示例: "|TIME:14:30|"
    static let TIME_TAG = "TIME"

    /// 应用类型标签
    /// 示例: "|APP:gofo|"
    static let APP_TAG = "APP"

    /// 完整地址标签（包含公寓号）
    /// 示例: "|FULL:123 Main St Apt 5|"
    static let FULL_ADDRESS_TAG = "FULL"

    /// 原始地址标签（AI扫描的未处理地址）
    /// 示例: "|ORIGINAL:90 Forest Grove Dr Apartamento 25, Daly City, CA... Rossana Al... G|"
    static let ORIGINAL_ADDRESS_TAG = "ORIGINAL"

    // MARK: - 标签分隔符

    /// 标签分隔符
    static let TAG_SEPARATOR = "|"

    /// 标签值分隔符
    static let TAG_VALUE_SEPARATOR = ":"

    // MARK: - 便利方法

    /// 构建内部排序号标签
    /// - Parameter sortNumber: 内部序号
    /// - Returns: 格式化的标签字符串
    static func internalSortTag(_ sortNumber: Int) -> String {
        return "\(TAG_SEPARATOR)\(INTERNAL_SORT_TAG)\(TAG_VALUE_SEPARATOR)\(sortNumber)"
    }

    /// 构建第三方排序号标签
    /// - Parameter thirdPartySortNumber: 第三方排序号
    /// - Returns: 格式化的标签字符串
    static func thirdPartySortTag(_ thirdPartySortNumber: String) -> String {
        return "\(TAG_SEPARATOR)\(THIRD_PARTY_SORT_TAG)\(TAG_VALUE_SEPARATOR)\(thirdPartySortNumber)"
    }

    /// 构建追踪号码标签
    /// - Parameter trackingNumber: 追踪号码
    /// - Returns: 格式化的标签字符串
    static func trackingTag(_ trackingNumber: String) -> String {
        return "\(TAG_SEPARATOR)\(TRACKING_TAG)\(TAG_VALUE_SEPARATOR)\(trackingNumber)"
    }

    /// 构建客户信息标签
    /// - Parameter customerName: 客户姓名
    /// - Returns: 格式化的标签字符串
    static func customerTag(_ customerName: String) -> String {
        return "\(TAG_SEPARATOR)\(CUSTOMER_TAG)\(TAG_VALUE_SEPARATOR)\(customerName)"
    }

    /// 构建配送时间标签
    /// - Parameter deliveryTime: 配送时间
    /// - Returns: 格式化的标签字符串
    static func timeTag(_ deliveryTime: String) -> String {
        return "\(TAG_SEPARATOR)\(TIME_TAG)\(TAG_VALUE_SEPARATOR)\(deliveryTime)"
    }

    /// 构建应用类型标签
    /// - Parameter appType: 应用类型
    /// - Returns: 格式化的标签字符串
    static func appTag(_ appType: String) -> String {
        return "\(TAG_SEPARATOR)\(APP_TAG)\(TAG_VALUE_SEPARATOR)\(appType)"
    }

    /// 构建完整地址标签
    /// - Parameter fullAddress: 完整地址
    /// - Returns: 格式化的标签字符串
    static func fullAddressTag(_ fullAddress: String) -> String {
        return "\(TAG_SEPARATOR)\(FULL_ADDRESS_TAG)\(TAG_VALUE_SEPARATOR)\(fullAddress)"
    }

    /// 构建原始地址标签
    /// - Parameter originalAddress: 原始未处理地址
    /// - Returns: 格式化的标签字符串
    static func originalAddressTag(_ originalAddress: String) -> String {
        return "\(TAG_SEPARATOR)\(ORIGINAL_ADDRESS_TAG)\(TAG_VALUE_SEPARATOR)\(originalAddress)"
    }
}

// MARK: - 使用示例和文档

/*
 使用示例:

 // 构建地址字符串
 var address = "123 Main Street"
 address += SortNumberConstants.internalSortTag(113)           // |SORT:113
 address += SortNumberConstants.thirdPartySortTag("1")         // |THIRD_PARTY_SORT:1
 address += SortNumberConstants.trackingTag("GF123456")        // |TRACK:GF123456
 address += SortNumberConstants.customerTag("John Doe")        // |CUSTOMER:John Doe

 // 结果: "123 Main Street|SORT:113|THIRD_PARTY_SORT:1|TRACK:GF123456|CUSTOMER:John Doe"

 数据流说明:
 1. AI识别 → 第三方排序号 "1"
 2. 系统分配 → 内部序号 113
 3. 标签构建 → |SORT:113|THIRD_PARTY_SORT:1|
 4. 数据解析 → sort_number=113, thirdPartySortNumber="1"
 5. UI显示 → 蓝色序号=113, 黄色标签="GoFo: 1"

 字段用途:
 - sort_number: 内部连续序号，用于系统管理和排序
 - sorted_number: 优化后序号，用于路线优化后的显示
 - thirdPartySortNumber: 第三方原始排序号，用于司机配送参考
 */
