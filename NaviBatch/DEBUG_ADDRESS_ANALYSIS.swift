import SwiftUI
import SwiftData
import CoreLocation

// 调试地址分析工具 - 专门用于分析53号和47号地址问题
struct AddressAnalysisDebugView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var analysisResults: [AddressAnalysisResult] = []
    @State private var isAnalyzing = false
    @State private var errorMessage = ""

    var body: some View {
        NavigationView {
            VStack {
                if isAnalyzing {
                    ProgressView("正在分析地址数据...")
                        .padding()
                } else if !analysisResults.isEmpty {
                    List(analysisResults, id: \.sortNumber) { result in
                        AddressAnalysisRow(result: result)
                    }
                } else {
                    Text("点击分析按钮开始检查地址数据")
                        .foregroundColor(.gray)
                        .padding()
                }

                if !errorMessage.isEmpty {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .padding()
                }

                But<PERSON>("分析地址数据") {
                    analyzeAddresses()
                }
                .padding()
                .disabled(isAnalyzing)
            }
            .navigationTitle("地址分析调试")
        }
    }

    private func analyzeAddresses() {
        isAnalyzing = true
        errorMessage = ""
        analysisResults = []

        Task {
            do {
                let descriptor = FetchDescriptor<DeliveryPoint>(
                    sortBy: [SortDescriptor(\.sort_number, order: .forward)]
                )
                let allPoints = try modelContext.fetch(descriptor)

                var results: [AddressAnalysisResult] = []

                for point in allPoints {
                    let result = AddressAnalysisResult(
                        sortNumber: point.sort_number,
                        displayNumber: point.displayNumber,
                        primaryAddress: point.primaryAddress,
                        originalAddress: point.originalAddress,
                        latitude: point.latitude,
                        longitude: point.longitude,
                        isInAustralia: isCoordinateInAustralia(
                            latitude: point.latitude,
                            longitude: point.longitude
                        ),
                        sourceApp: point.sourceApp.displayName,
                        geocodingWarning: point.geocodingWarning,
                        isOptimized: point.isOptimized,
                        coordinateValidated: point.coordinateValidated,
                        locationValidationStatus: point.locationValidationStatus
                    )
                    results.append(result)
                }

                await MainActor.run {
                    self.analysisResults = results
                    self.isAnalyzing = false

                    // 特别关注53号和47号地址
                    let problematicAddresses = results.filter { result in
                        (result.sortNumber == 53 || result.sortNumber == 47) && !result.isInAustralia
                    }

                    if !problematicAddresses.isEmpty {
                        print("🚨 发现问题地址:")
                        for addr in problematicAddresses {
                            print("📍 \(addr.sortNumber)号: \(addr.primaryAddress)")
                            print("   坐标: (\(addr.latitude), \(addr.longitude))")
                            print("   在澳洲: \(addr.isInAustralia)")
                            print("   来源: \(addr.sourceApp)")
                            print("   警告: \(addr.geocodingWarning ?? "无")")
                            print("   ---")
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "分析失败: \(error.localizedDescription)"
                    self.isAnalyzing = false
                }
            }
        }
    }

    private func isCoordinateInAustralia(latitude: Double, longitude: Double) -> Bool {
        // 澳大利亚大致边界
        return latitude >= -44.0 && latitude <= -10.0 &&
               longitude >= 113.0 && longitude <= 154.0
    }
}

struct AddressAnalysisResult {
    let sortNumber: Int
    let displayNumber: Int
    let primaryAddress: String
    let originalAddress: String?
    let latitude: Double
    let longitude: Double
    let isInAustralia: Bool
    let sourceApp: String
    let geocodingWarning: String?
    let isOptimized: Bool
    let coordinateValidated: Bool
    let locationValidationStatus: String
}

struct AddressAnalysisRow: View {
    let result: AddressAnalysisResult

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("\(result.sortNumber)号")
                    .font(.headline)
                    .foregroundColor(result.isInAustralia ? .primary : .red)

                Spacer()

                if !result.isInAustralia {
                    Text("⚠️ 不在澳洲")
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(4)
                }
            }

            Text(result.primaryAddress)
                .font(.body)
                .lineLimit(2)

            HStack {
                Text("坐标: (\(String(format: "%.6f", result.latitude)), \(String(format: "%.6f", result.longitude)))")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text(result.sourceApp)
                    .font(.caption)
                    .foregroundColor(.blue)
            }

            if let warning = result.geocodingWarning {
                Text("警告: \(warning)")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    AddressAnalysisDebugView()
}
