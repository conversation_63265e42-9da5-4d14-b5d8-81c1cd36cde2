<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>comgooglemaps</string>
		<string>comgooglemaps-x-callback</string>
	</array>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>NaviBatch needs camera access to scan addresses and take delivery confirmation photos</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>NaviBatch needs access to documents folder to import and export delivery data files</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>NaviBatch needs location access to optimize delivery routes and provide accurate navigation</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>NaviBatch needs permission to save delivery confirmation photos to your photo library</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>NaviBatch needs photo library access to save delivery confirmation photos and attach images to delivery reports</string>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict/>
	</dict>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>AccentColor</string>
		<key>UIImageName</key>
		<string>AppIcon</string>
		<key>UILaunchScreenMininumTime</key>
		<real>2.0</real>
	</dict>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
</dict>
</plist>
