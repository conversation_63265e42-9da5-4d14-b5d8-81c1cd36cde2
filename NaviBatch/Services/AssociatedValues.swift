import Foundation
import CoreLocation
import os.log

/// 关联值管理器 - 用于临时存储与对象关联的值
/// 处理Swift值类型和SwiftData/SwiftUI数据流的局限性
class AssociatedValues {
    /// 单例实例
    static let shared = AssociatedValues()
    
    /// 初始化为私有，确保只能通过单例访问
    private init() {}
    
    /// 存储CLPlacemark对象的字典（以UUID字符串为键）
    private var placemarkStorage: [String: CLPlacemark] = [:]
    
    /// 设置与ID关联的placemark
    /// - Parameters:
    ///   - id: 对象ID字符串
    ///   - placemark: 要存储的placemark对象
    func setPlacemark(for id: String, placemark: CLPlacemark) {
        placemarkStorage[id] = placemark
        logInfo("AssociatedValues - 已为ID \(id) 保存placemark信息")
    }
    
    /// 获取与ID关联的placemark
    /// - Parameter id: 对象ID字符串
    /// - Returns: 关联的placemark对象，如果不存在则返回nil
    func getPlacemark(for id: String) -> CLPlacemark? {
        let placemark = placemarkStorage[id]
        logInfo("AssociatedValues - 获取ID \(id) 的placemark信息: \(placemark != nil ? "成功" : "未找到")")
        return placemark
    }
    
    /// 移除与ID关联的placemark
    /// - Parameter id: 对象ID字符串
    func removePlacemark(for id: String) {
        placemarkStorage.removeValue(forKey: id)
        logInfo("AssociatedValues - 已移除ID \(id) 的placemark信息")
    }
    
    /// 清除所有存储的placemark
    func clearAllPlacemarks() {
        placemarkStorage.removeAll()
        logInfo("AssociatedValues - 已清除所有placemark信息")
    }
} 