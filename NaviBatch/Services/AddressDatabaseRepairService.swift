import Foundation
import SwiftData
import CoreLocation
import os.log

/// 地址数据库修复服务
/// 专门处理数据库中已存在的有问题的地址，如缺少州缩写的地址
class AddressDatabaseRepairService {
    static let shared = AddressDatabaseRepairService()
    
    private init() {}
    
    /// 修复数据库中缺少州缩写的地址
    /// - Parameter specificAddress: 指定要修复的地址，如果为nil则扫描所有地址
    func repairMissingStateAddresses(specificAddress: String? = nil) async {
        logInfo("🔧 AddressDatabaseRepairService - 开始修复缺少州缩写的地址")

        Task { @MainActor in
            do {
                let modelContext = DatabaseManager.shared.getPersistentContainer().mainContext

                // 查询需要修复的地址
                let problematicAddresses = try findProblematicAddresses(
                    in: modelContext,
                    specificAddress: specificAddress
                )

                logInfo("🔍 AddressDatabaseRepairService - 发现 \(problematicAddresses.count) 个需要修复的地址")

                var successCount = 0
                var failureCount = 0

                for validatedAddress in problematicAddresses {
                    let originalAddress = validatedAddress.originalAddress
                    logInfo("🔧 AddressDatabaseRepairService - 正在修复: \(originalAddress)")

                    // 使用AddressStateFixService修复地址
                    if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: originalAddress) {
                        // 更新数据库记录
                        await updateAddressRecord(
                            validatedAddress: validatedAddress,
                            fixedAddress: fixedAddress,
                            in: modelContext
                        )
                        successCount += 1
                        logInfo("✅ AddressDatabaseRepairService - 修复成功: \(originalAddress) -> \(fixedAddress)")
                    } else {
                        failureCount += 1
                        logInfo("❌ AddressDatabaseRepairService - 修复失败: \(originalAddress)")
                    }
                }

                // 保存更改
                try modelContext.save()

                logInfo("🎯 AddressDatabaseRepairService - 修复完成: 成功 \(successCount), 失败 \(failureCount)")

            } catch {
                logError("❌ AddressDatabaseRepairService - 修复过程出错: \(error.localizedDescription)")
            }
        }
    }
    
    /// 查找有问题的地址记录
    private func findProblematicAddresses(
        in modelContext: ModelContext,
        specificAddress: String?
    ) throws -> [ValidatedAddress] {
        
        if let specificAddress = specificAddress {
            // 查找特定地址
            let descriptor = FetchDescriptor<ValidatedAddress>(
                predicate: #Predicate<ValidatedAddress> { validatedAddress in
                    validatedAddress.originalAddress == specificAddress
                }
            )
            return try modelContext.fetch(descriptor)
        } else {
            // 查找所有可能有问题的地址
            let descriptor = FetchDescriptor<ValidatedAddress>()
            let allAddresses = try modelContext.fetch(descriptor)
            
            // 过滤出缺少州缩写的美国地址
            return allAddresses.filter { validatedAddress in
                needsStateFixing(validatedAddress.originalAddress)
            }
        }
    }
    
    /// 检查地址是否需要州缩写修复
    private func needsStateFixing(_ address: String) -> Bool {
        // 检查是否包含ZIP码（美国地址特征）
        let hasZipCode = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil
        
        // 检查是否已经包含州缩写
        let hasStateAbbreviation = hasUSStateAbbreviation(address)
        
        // 如果有ZIP码但没有州缩写，则需要修复
        return hasZipCode && !hasStateAbbreviation
    }
    
    /// 检查地址是否包含美国州缩写
    private func hasUSStateAbbreviation(_ address: String) -> Bool {
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY",
            "DC"
        ]
        
        for state in usStates {
            // 检查州缩写是否作为独立单词存在
            let pattern = "\\b\(state)\\b"
            if address.range(of: pattern, options: [.regularExpression, .caseInsensitive]) != nil {
                return true
            }
        }
        
        return false
    }
    
    /// 更新地址记录
    private func updateAddressRecord(
        validatedAddress: ValidatedAddress,
        fixedAddress: String,
        in modelContext: ModelContext
    ) async {
        // 验证修复后的地址
        if let newCoordinate = await geocodeFixedAddress(fixedAddress) {
            // 更新记录
            validatedAddress.originalAddress = fixedAddress
            validatedAddress.normalizedAddress = normalizeAddress(fixedAddress)
            validatedAddress.latitude = newCoordinate.latitude
            validatedAddress.longitude = newCoordinate.longitude
            validatedAddress.confidence = 0.95 // 修复后的地址置信度较高
            validatedAddress.lastUsedAt = Date()
            
            logInfo("📍 AddressDatabaseRepairService - 已更新坐标: (\(newCoordinate.latitude), \(newCoordinate.longitude))")
        } else {
            // 如果无法获取新坐标，只更新地址文本
            validatedAddress.originalAddress = fixedAddress
            validatedAddress.normalizedAddress = normalizeAddress(fixedAddress)
            validatedAddress.lastUsedAt = Date()
            
            logInfo("⚠️ AddressDatabaseRepairService - 只更新了地址文本，坐标保持不变")
        }
    }
    
    /// 对修复后的地址进行地理编码
    private func geocodeFixedAddress(_ address: String) async -> CLLocationCoordinate2D? {
        do {
            let geocoder = CLGeocoder()
            let placemarks = try await geocoder.geocodeAddressString(address)
            
            guard let placemark = placemarks.first,
                  let location = placemark.location else {
                return nil
            }
            
            return location.coordinate
        } catch {
            logInfo("⚠️ AddressDatabaseRepairService - 修复后地址地理编码失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 标准化地址（简化版本）
    private func normalizeAddress(_ address: String) -> String {
        return address
            .lowercased()
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 批量修复指定的地址列表
    func repairSpecificAddresses(_ addresses: [String]) async {
        logInfo("🔧 AddressDatabaseRepairService - 开始批量修复指定地址: \(addresses.count) 个")
        
        for address in addresses {
            await repairMissingStateAddresses(specificAddress: address)
        }
        
        logInfo("✅ AddressDatabaseRepairService - 批量修复完成")
    }
    
    /// 扫描并报告需要修复的地址
    func scanForProblematicAddresses() async -> [String] {
        logInfo("🔍 AddressDatabaseRepairService - 扫描需要修复的地址")

        return await MainActor.run {
            do {
                let modelContext = DatabaseManager.shared.getPersistentContainer().mainContext
                let problematicAddresses = try findProblematicAddresses(in: modelContext, specificAddress: nil)

                let addressList = problematicAddresses.map { $0.originalAddress }
                logInfo("📋 AddressDatabaseRepairService - 发现 \(addressList.count) 个需要修复的地址")

                return addressList
            } catch {
                logError("❌ AddressDatabaseRepairService - 扫描失败: \(error.localizedDescription)")
                return []
            }
        }
    }
}

// MARK: - 日志辅助函数
private func logInfo(_ message: String) {
    Logger.info(message, type: .data)
}

private func logError(_ message: String) {
    Logger.error(message, type: .data)
}
