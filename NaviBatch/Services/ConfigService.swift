import Foundation
import Combine

// MARK: - 配置数据模型
struct AppConfig: Codable {
    let ai: AIConfig
    let features: FeaturesConfig
    let version: String
    let minAppVersion: String
    let lastUpdated: String
    let updateAvailable: AppUpdateInfo? // 版本更新信息

    struct AIConfig: Codable {
        let openRouter: OpenRouterConfig

        struct OpenRouterConfig: Codable {
            let apiKey: String
            let baseURL: String
            let gemmaModels: [String]
            let timeout: Int
            let maxRetries: Int
            let modelConfigs: [String: ModelConfig]

            struct ModelConfig: Codable {
                let maxTokens: Int
                let temperature: Double
                let topP: Double
            }
        }
    }

    struct FeaturesConfig: Codable {
        let lookAround: LookAroundConfig
        let routeOptimization: RouteOptimizationConfig
        let subscription: SubscriptionConfig

        struct LookAroundConfig: Codable {
            let enabled: Bool
            let cacheEnabled: Bool
            let maxCacheSize: Int
        }

        struct RouteOptimizationConfig: Codable {
            let enabled: Bool
            let maxPoints: Int
            let algorithms: [String]
        }

        struct SubscriptionConfig: Codable {
            let enabled: Bool
            let trialDays: Int
            let features: SubscriptionFeatures

            struct SubscriptionFeatures: Codable {
                let unlimitedAddresses: Bool
                let advancedOptimization: Bool
                let exportFeatures: Bool
            }
        }
    }
}

// MARK: - 配置服务
@MainActor
class ConfigService: ObservableObject {
    static let shared = ConfigService()

    // 配置URL - Cloudflare Worker配置服务
    internal let configURL = "https://navibatch-config.jasonkwok2018.workers.dev/config"

    @Published var config: AppConfig?
    @Published var isLoading = false
    @Published var lastError: Error?

    private var cancellables = Set<AnyCancellable>()
    internal let cache = ConfigCache()

    private init() {
        // 🇭🇰 检测香港地区，优化配置策略
        detectHongKongRegion()

        // 启动时加载配置
        loadConfig()

        // 设置定期刷新（每30分钟）
        Timer.publish(every: 30 * 60, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.refreshConfig()
            }
            .store(in: &cancellables)
    }

    // 🇭🇰 检测香港地区
    private func detectHongKongRegion() {
        let timeZone = TimeZone.current.identifier
        let locale = Locale.current.identifier

        if timeZone.contains("Hong_Kong") || locale.contains("HK") {
            print("[ConfigService] 🇭🇰 检测到香港地区，优化配置策略")
            print("[ConfigService] 💡 建议优先使用OCR模式，AI服务可能受地理限制")
        }
    }

    // MARK: - 公共方法

    /// 加载配置（优先使用缓存）
    func loadConfig() {
        // 先尝试从缓存加载
        if let cachedConfig = cache.loadConfig() {
            self.config = cachedConfig
            print("[ConfigService] ✅ 已加载缓存配置")
            print("[ConfigService] 📊 缓存配置包含 \(cachedConfig.ai.openRouter.gemmaModels.count) 个模型")
        }

        // 然后异步刷新
        refreshConfig()
    }

    /// 强制刷新配置
    func refreshConfig() {
        guard !isLoading else { return }

        isLoading = true
        lastError = nil

        Task {
            do {
                let newConfig = try await fetchConfig()
                await MainActor.run {
                    self.config = newConfig
                    self.isLoading = false
                    self.cache.saveConfig(newConfig)
                    print("[ConfigService] 🚀 Cloudflare配置已更新")
                    print("[ConfigService] 🔑 API密钥前缀: \(String(newConfig.ai.openRouter.apiKey.prefix(20)))...")
                    print("[ConfigService] 🤖 可用模型: \(newConfig.ai.openRouter.gemmaModels.joined(separator: ", "))")
                    print("[ConfigService] 🌐 API URL: \(newConfig.ai.openRouter.baseURL)")

                    // 检查版本更新
                    if let updateInfo = newConfig.updateAvailable {
                        print("[ConfigService] 📱 检测到版本更新: \(updateInfo.latestVersion)")
                        AppUpdateService.shared.checkForUpdates(from: newConfig)
                    }
                }
            } catch {
                await MainActor.run {
                    self.lastError = error
                    self.isLoading = false
                    print("[ConfigService] 配置更新失败: \(error)")
                }
            }
        }
    }

    /// 🗑️ 清除缓存并强制重新获取配置
    func clearCacheAndRefresh() {
        cache.clearCache()
        config = nil
        print("[ConfigService] 🗑️ 已清除配置缓存，强制重新获取")
        refreshConfig()
    }

    // MARK: - 便捷访问方法

    /// 获取AI配置
    var aiConfig: AppConfig.AIConfig? {
        return config?.ai
    }

    /// 获取Gemma模型列表
    var gemmaModels: [String] {
        let models = config?.ai.openRouter.gemmaModels ?? [
            // 🚀 备用配置 - Gemma模型系列
            "google/gemma-3-27b-it:free",
            "google/gemma-3-12b-it:free",
            "google/gemma-3-9b-it:free"
        ]
        if config != nil {
            print("[ConfigService] 🤖 提供动态模型列表: \(models.joined(separator: ", "))")
        } else {
            print("[ConfigService] ⚠️ 使用备用模型列表: \(models.joined(separator: ", "))")
        }
        return models
    }

    /// 获取API密钥
    var apiKey: String {
        let key = config?.ai.openRouter.apiKey ?? "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"
        if !key.isEmpty {
            print("[ConfigService] 🔑 提供动态API密钥，前缀: \(String(key.prefix(20)))...")
        }
        return key
    }

    /// 获取基础URL
    var baseURL: String {
        return config?.ai.openRouter.baseURL ?? "https://openrouter.ai/api/v1/chat/completions"
    }

    /// 检查功能是否启用
    func isFeatureEnabled(_ feature: String) -> Bool {
        switch feature {
        case "lookAround":
            return config?.features.lookAround.enabled ?? true
        case "routeOptimization":
            return config?.features.routeOptimization.enabled ?? true
        case "subscription":
            return config?.features.subscription.enabled ?? true
        default:
            return true
        }
    }

    // MARK: - 私有方法

    private func fetchConfig() async throws -> AppConfig {
        guard let url = URL(string: configURL) else {
            throw ConfigError.invalidURL
        }

        // 🌐 优化网络请求配置
        var request = URLRequest(url: url)
        request.setValue(Bundle.main.appVersion, forHTTPHeaderField: "X-App-Version")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        request.timeoutInterval = 30  // 增加超时时间到30秒
        request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData

        // 🇭🇰 为香港地区优化的URLSession配置
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        config.waitsForConnectivity = true
        config.allowsCellularAccess = true

        let session = URLSession(configuration: config)

        print("[ConfigService] 🌐 尝试获取配置: \(configURL)")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            print("[ConfigService] ❌ 无效的HTTP响应")
            throw ConfigError.serverError
        }

        print("[ConfigService] 📊 HTTP状态码: \(httpResponse.statusCode)")

        guard 200...299 ~= httpResponse.statusCode else {
            print("[ConfigService] ❌ 服务器错误: \(httpResponse.statusCode)")
            throw ConfigError.serverError
        }

        let decodedConfig = try JSONDecoder().decode(AppConfig.self, from: data)
        print("[ConfigService] ✅ 配置解析成功")
        return decodedConfig
    }
}

// MARK: - 配置缓存
internal class ConfigCache {
    private let userDefaults = UserDefaults.standard
    private let configKey = "app_config_cache"
    private let timestampKey = "app_config_timestamp"
    private let cacheValidityDuration: TimeInterval = 24 * 60 * 60 // 24小时

    func saveConfig(_ config: AppConfig) {
        do {
            let data = try JSONEncoder().encode(config)
            userDefaults.set(data, forKey: configKey)
            userDefaults.set(Date().timeIntervalSince1970, forKey: timestampKey)
        } catch {
            print("[ConfigCache] 保存配置失败: \(error)")
        }
    }

    func loadConfig() -> AppConfig? {
        // 检查缓存是否过期
        let timestamp = userDefaults.double(forKey: timestampKey)
        let cacheAge = Date().timeIntervalSince1970 - timestamp

        guard cacheAge < cacheValidityDuration else {
            print("[ConfigCache] 缓存已过期")
            return nil
        }

        guard let data = userDefaults.data(forKey: configKey) else {
            return nil
        }

        do {
            return try JSONDecoder().decode(AppConfig.self, from: data)
        } catch {
            print("[ConfigCache] 加载缓存配置失败: \(error)")
            return nil
        }
    }

    /// 🗑️ 清除缓存
    func clearCache() {
        userDefaults.removeObject(forKey: configKey)
        userDefaults.removeObject(forKey: timestampKey)
        print("[ConfigCache] 🗑️ 缓存已清除")
    }
}

// MARK: - 错误类型
enum ConfigError: LocalizedError {
    case invalidURL
    case serverError
    case decodingError
    case invalidResponse

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "配置服务URL无效"
        case .serverError:
            return "配置服务器错误"
        case .decodingError:
            return "配置数据解析错误"
        case .invalidResponse:
            return "配置服务响应无效"
        }
    }
}

// MARK: - Bundle扩展
extension Bundle {
    var appVersion: String {
        return infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
}
