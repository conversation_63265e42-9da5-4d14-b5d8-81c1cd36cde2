import Foundation
import CoreLocation
import MapKit
import Combine
import os.log

// MARK: - 反向地理编码验证服务
class ReverseGeocodingValidationService {
    static let shared = ReverseGeocodingValidationService()

    // 🚨 地址验证模式配置 - 临时改为标准模式以解决导入地址的验证问题
    var validationMode: AddressValidationMode = .standard {
        didSet {
            // 自动保存到UserDefaults
            UserDefaults.standard.set(validationMode.rawValue, forKey: "AddressValidationMode")
        }
    }

    private init() {
        // 从UserDefaults加载设置
        loadSettings()
    }

    /// 从UserDefaults加载验证设置
    private func loadSettings() {
        if let savedMode = UserDefaults.standard.string(forKey: "AddressValidationMode"),
           let mode = AddressValidationMode(rawValue: savedMode) {
            validationMode = mode
        }
    }

    /// 执行反向地理编码验证并计算评分
    /// - Parameters:
    ///   - originalAddress: 原始地址
    ///   - coordinate: 坐标
    ///   - skipIfTrusted: 如果是可信来源（如Apple Maps成功地理编码），是否跳过反向验证
    /// - Returns: 验证结果包含评分和详细信息
    func validateAddressWithReverseGeocoding(
        originalAddress: String,
        coordinate: CLLocationCoordinate2D,
        skipIfTrusted: Bool = false
    ) async -> AddressValidationScore {

        // 🎯 如果是可信来源且允许跳过，直接返回高分
        if skipIfTrusted {
            Logger.info("🎯 跳过反向地理编码验证（可信来源）: \(originalAddress)", type: .location)
            Logger.info("📍 坐标: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)

            return AddressValidationScore(
                originalAddress: originalAddress,
                reverseGeocodedAddress: originalAddress, // 使用原始地址
                totalScore: 100.0, // 给予满分
                streetNumberValidation: .valid, // 使用 AddressValidationStatus 枚举
                streetNameValidation: .valid,
                postalCodeValidation: .valid,
                countryValidation: .valid,
                issues: [] // 无问题
            )
        }

        Logger.info("🔍 开始反向地理编码验证: \(originalAddress)", type: .location)
        Logger.info("📍 输入坐标: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)

        // 执行反向地理编码
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        do {
            // 🎯 反向地理编码，强制使用英文locale
            let placemarks = try await geocoder.reverseGeocodeLocation(
                location,
                preferredLocale: Locale(identifier: "en_US")
            )

            guard let placemark = placemarks.first else {
                Logger.warning("⚠️ 反向地理编码未返回结果: 坐标(\(coordinate.latitude), \(coordinate.longitude)) -> \(originalAddress)", type: .location)
                return createFailedValidationScore(originalAddress: originalAddress, reason: "无法进行反向地理编码")
            }

            // 构建反向地理编码得到的地址
            let reverseGeocodedAddress = buildAddressFromPlacemark(placemark)
            Logger.info("📍 反向地理编码结果: 坐标(\(coordinate.latitude), \(coordinate.longitude)) -> \(reverseGeocodedAddress)", type: .location)

            // 记录详细的placemark信息
            Logger.info("🏠 Placemark详情:", type: .location)
            Logger.info("  - 门牌号: '\(placemark.subThoroughfare ?? "无")'", type: .location)
            Logger.info("  - 街道: '\(placemark.thoroughfare ?? "无")'", type: .location)
            Logger.info("  - 区域: '\(placemark.locality ?? "无")'", type: .location)
            Logger.info("  - 行政区: '\(placemark.administrativeArea ?? "无")'", type: .location)
            Logger.info("  - 邮编: '\(placemark.postalCode ?? "无")'", type: .location)
            Logger.info("  - 国家: '\(placemark.country ?? "无")'", type: .location)
            Logger.info("  - 名称: '\(placemark.name ?? "无")'", type: .location)

            // 执行详细验证和评分
            return calculateValidationScore(
                originalAddress: originalAddress,
                reverseGeocodedAddress: reverseGeocodedAddress,
                placemark: placemark
            )

        } catch {
            Logger.error("❌ 反向地理编码失败: \(originalAddress), 错误: \(error.localizedDescription)", type: .location)
            return createFailedValidationScore(originalAddress: originalAddress, reason: "反向地理编码失败: \(error.localizedDescription)")
        }
    }

    /// 计算地址验证评分
    private func calculateValidationScore(
        originalAddress: String,
        reverseGeocodedAddress: String,
        placemark: CLPlacemark
    ) -> AddressValidationScore {

        Logger.info("", type: .location)
        Logger.info("🔍 ═══════════════════════════════════════════════════════════════════════════════", type: .location)
        Logger.info("📊 开始计算地址验证评分: \(originalAddress)", type: .location)
        Logger.info("🔍 ═══════════════════════════════════════════════════════════════════════════════", type: .location)

        // 解析原始地址组件
        let originalComponents = parseAddressComponents(originalAddress)

        // 解析反向地理编码地址组件
        let reverseComponents = AddressComponents(
            streetNumber: placemark.subThoroughfare ?? "",
            streetName: placemark.thoroughfare ?? "",
            suburb: placemark.locality ?? "",
            state: placemark.administrativeArea ?? "",
            postalCode: placemark.postalCode ?? "",
            country: placemark.country ?? ""
        )

        // 计算各项验证分数
        let streetNumberScore = validateStreetNumber(original: originalComponents.streetNumber, reverse: reverseComponents.streetNumber)
        let streetNameScore = validateStreetName(original: originalComponents.streetName, reverse: reverseComponents.streetName)
        let postalCodeScore = validatePostalCode(original: originalComponents.postalCode, reverse: reverseComponents.postalCode)
        let countryScore = validateCountry(original: originalComponents.country, reverse: reverseComponents.country)

        // 🚨 严格门牌号验证：差异超过±2直接失效
        let totalScore: Double

        // 检查门牌号是否差异过大（超过±5，对美国地址更宽松）
        let streetNumberDifference = getStreetNumberDifference(original: originalComponents.streetNumber, reverse: reverseComponents.streetNumber)
        let isUSAddress = originalAddress.lowercased().contains("ca") || originalAddress.lowercased().contains("california") ||
                         originalAddress.lowercased().contains("usa") || originalAddress.lowercased().contains("united states")
        let maxDifference = isUSAddress ? 5 : 2  // 美国地址允许更大差异
        let isStreetNumberCriticallyDifferent = streetNumberDifference > maxDifference

        if isStreetNumberCriticallyDifferent {
            // 🚨 门牌号差异超过阈值，直接失效，不使用加权计算
            totalScore = 0.0
            Logger.info("  🚨 门牌号差异过大 (差\(streetNumberDifference)号，阈值±\(maxDifference))，地址验证直接失效", type: .location)
        } else {
            // 门牌号差异在±2范围内，使用正常的验证模式计算
            switch validationMode {
            case .perfect:
                // 完美模式：所有组件都必须100分才能通过
                let allComponentsPerfect = streetNumberScore.score == 100 &&
                                          streetNameScore.score == 100 &&
                                          postalCodeScore.score == 100 &&
                                          countryScore.score == 100
                totalScore = allComponentsPerfect ? 100.0 : 0.0

            case .strict:
                // 严格模式：所有组件100分得100分，否则最高95分
                let allComponentsPerfect = streetNumberScore.score == 100 &&
                                          streetNameScore.score == 100 &&
                                          postalCodeScore.score == 100 &&
                                          countryScore.score == 100
                if allComponentsPerfect {
                    totalScore = 100.0
                } else {
                    let weightedScore = (streetNumberScore.score * 0.3) + // 门牌号 30%
                                       (streetNameScore.score * 0.4) +     // 街道名 40%
                                       (postalCodeScore.score * 0.2) +     // 邮编 20%
                                       (countryScore.score * 0.1)          // 国家 10%
                    totalScore = min(95.0, weightedScore) // 最高95分
                }

            case .standard:
                // 标准模式：使用加权计算
                totalScore = (streetNumberScore.score * 0.3) + // 门牌号 30%
                            (streetNameScore.score * 0.4) +     // 街道名 40%
                            (postalCodeScore.score * 0.2) +     // 邮编 20%
                            (countryScore.score * 0.1)          // 国家 10%
            }
        }

        // 收集验证问题
        var issues: [String] = []
        if streetNumberScore.score < 100 { issues.append(streetNumberScore.issue) }
        if streetNameScore.score < 100 { issues.append(streetNameScore.issue) }
        if postalCodeScore.score < 100 { issues.append(postalCodeScore.issue) }
        if countryScore.score < 100 { issues.append(countryScore.issue) }

        // 记录详细日志
        Logger.info("📋 地址验证评分详情:", type: .location)
        Logger.info("  📍 原始地址: \(originalAddress)", type: .location)
        Logger.info("  📍 反向地址: \(reverseGeocodedAddress)", type: .location)
        Logger.info("  🏠 门牌号验证: \(Int(streetNumberScore.score))分 - \(streetNumberScore.status.rawValue) (原始: '\(originalComponents.streetNumber)' vs 反向: '\(reverseComponents.streetNumber)')", type: .location)
        Logger.info("  🛣️ 街道名验证: \(Int(streetNameScore.score))分 - \(streetNameScore.status.rawValue) (原始: '\(originalComponents.streetName)' vs 反向: '\(reverseComponents.streetName)')", type: .location)
        Logger.info("  📮 邮编验证: \(Int(postalCodeScore.score))分 - \(postalCodeScore.status.rawValue) (原始: '\(originalComponents.postalCode)' vs 反向: '\(reverseComponents.postalCode)')", type: .location)
        Logger.info("  🌏 国家验证: \(Int(countryScore.score))分 - \(countryScore.status.rawValue) (原始: '\(originalComponents.country)' vs 反向: '\(reverseComponents.country)')", type: .location)
        Logger.info("  🎯 总分: \(Int(totalScore))分 - 地址: \(originalAddress)", type: .location)
        Logger.info("🔍 ═══════════════════════════════════════════════════════════════════════════════", type: .location)
        Logger.info("", type: .location)

        return AddressValidationScore(
            originalAddress: originalAddress,
            reverseGeocodedAddress: reverseGeocodedAddress,
            totalScore: totalScore,
            streetNumberValidation: streetNumberScore.status,
            streetNameValidation: streetNameScore.status,
            postalCodeValidation: postalCodeScore.status,
            countryValidation: countryScore.status,
            issues: issues
        )
    }

    /// 验证门牌号
    private func validateStreetNumber(original: String, reverse: String) -> ValidationResult {
        let normalizedOriginal = original.trimmingCharacters(in: .whitespacesAndNewlines)
        let normalizedReverse = reverse.trimmingCharacters(in: .whitespacesAndNewlines)

        if normalizedOriginal.isEmpty && normalizedReverse.isEmpty {
            return ValidationResult(score: 100, status: .valid, issue: "")
        }

        if normalizedOriginal.isEmpty || normalizedReverse.isEmpty {
            return ValidationResult(score: 0, status: .invalid, issue: "门牌号缺失")
        }

        // 完全匹配
        if normalizedOriginal == normalizedReverse {
            return ValidationResult(score: 100, status: .valid, issue: "")
        }

        // 处理单元号/公寓号格式 (如 "2/21" vs "21", "113/353" vs "353", "314/52-54" vs "52")
        if let mainNumber = extractMainStreetNumber(from: normalizedOriginal) {
            // 直接匹配主要号码
            if mainNumber == normalizedReverse {
                return ValidationResult(score: 90, status: .valid, issue: "")
            }

            // 检查范围地址 (如 "52-54" vs "52")
            if let rangeStart = extractRangeStartNumber(from: mainNumber) {
                if rangeStart == normalizedReverse {
                    return ValidationResult(score: 85, status: .valid, issue: "")
                }
            }

            // 检查反向地址是否在范围内
            if isNumberInRange(normalizedReverse, range: mainNumber) {
                return ValidationResult(score: 85, status: .valid, issue: "")
            }

            // 检查主要号码是否在反向地址的范围内 (如 "24" 是否在 "20-26" 范围内)
            if isNumberInRange(mainNumber, range: normalizedReverse) {
                return ValidationResult(score: 80, status: .valid, issue: "")
            }
        }

        // 处理字母后缀 (如 "7b" vs "7", "31a" vs "31")
        if let baseNumber = extractBaseStreetNumber(from: normalizedOriginal) {
            if baseNumber == normalizedReverse {
                return ValidationResult(score: 85, status: .valid, issue: "")
            }
        }

        // 反向检查：如果反向地址包含单元号
        if let mainNumber = extractMainStreetNumber(from: normalizedReverse) {
            if mainNumber == normalizedOriginal {
                return ValidationResult(score: 90, status: .valid, issue: "")
            }
        }

        if let baseNumber = extractBaseStreetNumber(from: normalizedReverse) {
            if baseNumber == normalizedOriginal {
                return ValidationResult(score: 85, status: .valid, issue: "")
            }
        }

        // 检查门牌号邻近性（对面/隔壁）
        if let proximityScore = checkStreetNumberProximity(original: normalizedOriginal, reverse: normalizedReverse) {
            return proximityScore
        }

        return ValidationResult(score: 0, status: .invalid, issue: "门牌号不匹配: '\(normalizedOriginal)' vs '\(normalizedReverse)'")
    }

    /// 提取主要街道号码 (从 "2/21" 提取 "21", 从 "113/353" 提取 "353", 从 "314/52-54" 提取 "52-54")
    func extractMainStreetNumber(from streetNumber: String) -> String? {
        if streetNumber.contains("/") {
            let parts = streetNumber.components(separatedBy: "/")
            return parts.last?.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return nil
    }

    /// 提取范围地址的起始号码 (从 "52-54" 提取 "52", 从 "10-12" 提取 "10")
    private func extractRangeStartNumber(from streetNumber: String) -> String? {
        if streetNumber.contains("-") {
            let parts = streetNumber.components(separatedBy: "-")
            return parts.first?.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return nil
    }

    /// 检查号码是否在范围内 (检查 "52" 或 "M104/25" 是否在 "52-54" 范围内)
    func isNumberInRange(_ number: String, range: String) -> Bool {
        guard range.contains("-") else { return false }

        let parts = range.components(separatedBy: "-")
        guard parts.count == 2,
              let startNum = Int(parts[0].trimmingCharacters(in: .whitespacesAndNewlines)),
              let endNum = Int(parts[1].trimmingCharacters(in: .whitespacesAndNewlines)) else {
            return false
        }

        // 从号码中提取数字部分（处理复合格式如 M104/25 -> 25）
        let checkNum: Int
        if let extractedNum = extractNumericPart(from: number) {
            checkNum = extractedNum
        } else {
            return false
        }

        return checkNum >= startNum && checkNum <= endNum
    }

    /// 提取基础街道号码 (从 "7b" 提取 "7", 从 "31a" 提取 "31")
    private func extractBaseStreetNumber(from streetNumber: String) -> String? {
        // 使用正则表达式提取数字部分
        let pattern = "^(\\d+)[a-zA-Z]*$"
        if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
            let range = NSRange(location: 0, length: streetNumber.count)
            if let match = regex.firstMatch(in: streetNumber, options: [], range: range) {
                if let numberRange = Range(match.range(at: 1), in: streetNumber) {
                    return String(streetNumber[numberRange])
                }
            }
        }
        return nil
    }

    /// 检查门牌号邻近性（对面/隔壁）- 🚨 严格验证模式
    private func checkStreetNumberProximity(original: String, reverse: String) -> ValidationResult? {
        // 提取数字部分进行比较
        let originalNumber = extractNumericPart(from: original)
        let reverseNumber = extractNumericPart(from: reverse)

        guard let origNum = originalNumber, let revNum = reverseNumber else {
            return nil
        }

        let difference = abs(origNum - revNum)

        switch difference {
        case 0:
            // 完全匹配（这种情况应该在前面已经处理了）
            return ValidationResult(score: 100, status: .valid, issue: "")

        case 1:
            // 差1号：通常是对面 - 🎯 司机可以处理
            return ValidationResult(score: 90, status: .warning, issue: "可能是对面地址 (差1号): '\(original)' vs '\(reverse)' - 司机可以找到")

        case 2:
            // 差2号：通常是隔壁 - 🎯 司机可以处理
            return ValidationResult(score: 85, status: .warning, issue: "可能是隔壁地址 (差2号): '\(original)' vs '\(reverse)' - 司机可以找到")

        case 3...10:
            // 差3-10号：需要司机注意 - ⚠️ 可能有风险
            return ValidationResult(score: 40, status: .warning, issue: "地址差异较大 (差\(difference)号): '\(original)' vs '\(reverse)' - 请司机仔细确认")

        case 11...50:
            // 差11-50号：高风险 - 🚨 很可能送错
            return ValidationResult(score: 20, status: .invalid, issue: "地址差异很大 (差\(difference)号): '\(original)' vs '\(reverse)' - 高风险，建议修正地址")

        default:
            // 差距太大：极高风险 - 🚨 肯定送错
            return ValidationResult(score: 0, status: .invalid, issue: "地址差异极大 (差\(difference)号): '\(original)' vs '\(reverse)' - 极高风险，必须修正地址")
        }
    }

    /// 从门牌号中提取数字部分
    private func extractNumericPart(from streetNumber: String) -> Int? {
        // 处理复合门牌号，如 "2/206" -> 206, "113/353" -> 353
        if let mainNumber = extractMainStreetNumber(from: streetNumber) {
            // 从主要号码中提取数字
            return extractSimpleNumber(from: mainNumber)
        }

        // 处理简单门牌号
        return extractSimpleNumber(from: streetNumber)
    }

    /// 从字符串中提取简单数字
    private func extractSimpleNumber(from text: String) -> Int? {
        // 处理范围地址，如 "52-54" -> 52
        if text.contains("-") {
            let parts = text.components(separatedBy: "-")
            if let firstPart = parts.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
                return Int(firstPart)
            }
        }

        // 处理带字母后缀的号码，如 "7b" -> 7
        let pattern = "^(\\d+)"
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)) {
            if let range = Range(match.range(at: 1), in: text) {
                return Int(String(text[range]))
            }
        }

        return nil
    }

    /// 计算门牌号差异（用于严格验证）- 正确处理范围地址和复合门牌号
    func getStreetNumberDifference(original: String, reverse: String) -> Int {
        // 提取主要门牌号（处理复合格式如 M104/25 -> 25）
        let originalMainNumber = extractMainStreetNumber(from: original) ?? original
        let reverseMainNumber = extractMainStreetNumber(from: reverse) ?? reverse

        // 检查是否有范围地址的情况
        if originalMainNumber.contains("-") {
            // 原始地址是范围地址（如 "1-5"），检查反向地址是否在范围内
            if isNumberInRange(reverseMainNumber, range: originalMainNumber) {
                return 0 // 在范围内，差异为0
            }
            // 不在范围内，计算到范围边界的最小距离
            return calculateDistanceToRange(number: reverseMainNumber, range: originalMainNumber)
        }

        if reverseMainNumber.contains("-") {
            // 反向地址是范围地址，检查原始地址是否在范围内
            if isNumberInRange(originalMainNumber, range: reverseMainNumber) {
                return 0 // 在范围内，差异为0
            }
            // 不在范围内，计算到范围边界的最小距离
            return calculateDistanceToRange(number: originalMainNumber, range: reverseMainNumber)
        }

        // 都是单个号码，使用原来的逻辑
        let originalNumber = extractNumericPart(from: originalMainNumber)
        let reverseNumber = extractNumericPart(from: reverseMainNumber)

        guard let origNum = originalNumber, let revNum = reverseNumber else {
            return 0 // 如果无法提取数字，认为差异为0（不触发严格验证）
        }

        return abs(origNum - revNum)
    }

    /// 计算号码到范围的最小距离
    private func calculateDistanceToRange(number: String, range: String) -> Int {
        guard range.contains("-") else {
            return 999 // 无法解析，返回大数值
        }

        // 从号码中提取数字部分（处理复合格式）
        let checkNum: Int
        if let extractedNum = extractNumericPart(from: number) {
            checkNum = extractedNum
        } else {
            return 999 // 无法解析，返回大数值
        }

        let parts = range.components(separatedBy: "-")
        guard parts.count == 2,
              let startNum = Int(parts[0].trimmingCharacters(in: .whitespacesAndNewlines)),
              let endNum = Int(parts[1].trimmingCharacters(in: .whitespacesAndNewlines)) else {
            return 999 // 无法解析，返回大数值
        }

        if checkNum < startNum {
            return startNum - checkNum // 距离起始号码的距离
        } else if checkNum > endNum {
            return checkNum - endNum // 距离结束号码的距离
        } else {
            return 0 // 在范围内
        }
    }

    /// 验证街道名
    private func validateStreetName(original: String, reverse: String) -> ValidationResult {
        let normalizedOriginal = normalizeStreetName(original)
        let normalizedReverse = normalizeStreetName(reverse)

        if normalizedOriginal.isEmpty && normalizedReverse.isEmpty {
            return ValidationResult(score: 100, status: .valid, issue: "")
        }

        if normalizedOriginal.isEmpty || normalizedReverse.isEmpty {
            return ValidationResult(score: 0, status: .invalid, issue: "街道名缺失")
        }

        // 计算相似度
        let similarity = calculateStreetNameSimilarity(normalizedOriginal, normalizedReverse)

        if similarity >= 0.9 {
            return ValidationResult(score: 100, status: .valid, issue: "")
        } else if similarity >= 0.7 {
            return ValidationResult(score: 80, status: .warning, issue: "街道名部分匹配: '\(original)' vs '\(reverse)'")
        } else {
            return ValidationResult(score: 0, status: .invalid, issue: "街道名不匹配: '\(original)' vs '\(reverse)'")
        }
    }

    /// 验证邮编
    private func validatePostalCode(original: String, reverse: String) -> ValidationResult {
        let normalizedOriginal = original.trimmingCharacters(in: .whitespacesAndNewlines)
        let normalizedReverse = reverse.trimmingCharacters(in: .whitespacesAndNewlines)

        if normalizedOriginal.isEmpty && normalizedReverse.isEmpty {
            return ValidationResult(score: 100, status: .valid, issue: "")
        }

        if normalizedOriginal.isEmpty || normalizedReverse.isEmpty {
            return ValidationResult(score: 50, status: .warning, issue: "邮编缺失")
        }

        if normalizedOriginal == normalizedReverse {
            return ValidationResult(score: 100, status: .valid, issue: "")
        } else {
            return ValidationResult(score: 0, status: .invalid, issue: "邮编不匹配: '\(normalizedOriginal)' vs '\(normalizedReverse)'")
        }
    }

    /// 验证国家
    private func validateCountry(original: String, reverse: String) -> ValidationResult {
        let normalizedOriginal = original.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        let normalizedReverse = reverse.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        // 澳大利亚的各种表示方式
        let australiaVariants = ["australia", "au", "aus"]

        let originalIsAustralia = australiaVariants.contains(normalizedOriginal)
        let reverseIsAustralia = australiaVariants.contains(normalizedReverse)

        if originalIsAustralia && reverseIsAustralia {
            return ValidationResult(score: 100, status: .valid, issue: "")
        } else if normalizedOriginal.isEmpty || normalizedReverse.isEmpty {
            return ValidationResult(score: 80, status: .warning, issue: "国家信息缺失")
        } else {
            return ValidationResult(score: 0, status: .invalid, issue: "国家不匹配: '\(original)' vs '\(reverse)'")
        }
    }

    /// 标准化街道名
    private func normalizeStreetName(_ streetName: String) -> String {
        var normalized = streetName.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        // 1. 处理撇号和特殊字符
        normalized = normalizeApostrophes(normalized)

        // 2. 创建街道类型映射表 - 将所有变体映射到统一的缩写
        let streetTypeMap = [
            // Street
            "street": "st", "st": "st",
            // Road
            "road": "rd", "rd": "rd",
            // Avenue
            "avenue": "ave", "ave": "ave",
            // Drive
            "drive": "dr", "dr": "dr",
            // Court
            "court": "ct", "ct": "ct",
            // Place
            "place": "pl", "pl": "pl",
            // Lane - 支持多种缩写
            "lane": "ln", "ln": "ln", "la": "ln",
            // Boulevard
            "boulevard": "blvd", "blvd": "blvd",
            // Close
            "close": "cl", "cl": "cl",
            // Crescent - 支持多种缩写
            "crescent": "cr", "cr": "cr", "cres": "cr",
            // Parade
            "parade": "pde", "pde": "pde",
            // Grove
            "grove": "gr", "gr": "gr"
        ]

        // 3. 使用正则表达式匹配街道类型并替换
        for (variant, standard) in streetTypeMap {
            // 匹配结尾的街道类型
            let pattern = "\\b\(variant)\\b$"
            normalized = normalized.replacingOccurrences(of: pattern, with: standard, options: .regularExpression)
        }

        return normalized
    }

    /// 标准化撇号和特殊字符
    private func normalizeApostrophes(_ streetName: String) -> String {
        var normalized = streetName

        // 处理各种撇号字符
        let apostropheVariants = ["'", "'", "'", "`", "´"]
        for variant in apostropheVariants {
            normalized = normalized.replacingOccurrences(of: variant, with: "'")
        }

        // 处理常见的撇号名称模式
        let apostrophePatterns = [
            // O'Sullivan, O'Brien, O'Connor 等
            ("o'([a-z])", "o$1"),
            // Mc'Donald -> McDonald
            ("mc'([a-z])", "mc$1"),
            // D'Angelo -> dangelo
            ("d'([a-z])", "d$1")
        ]

        for (pattern, replacement) in apostrophePatterns {
            normalized = normalized.replacingOccurrences(of: pattern, with: replacement, options: .regularExpression)
        }

        return normalized
    }

    /// 计算街道名相似度
    private func calculateStreetNameSimilarity(_ str1: String, _ str2: String) -> Double {
        let words1 = Set(str1.components(separatedBy: " ").filter { !$0.isEmpty })
        let words2 = Set(str2.components(separatedBy: " ").filter { !$0.isEmpty })

        let intersection = words1.intersection(words2)
        let union = words1.union(words2)

        return union.isEmpty ? 0.0 : Double(intersection.count) / Double(union.count)
    }

    /// 解析地址组件 - 支持国际地址格式
    private func parseAddressComponents(_ address: String) -> AddressComponents {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址特殊处理
            let hkComponents = AddressCountryDetector.parseHongKongAddress(address)

            // 从地址中提取区域信息
            let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            var suburb = ""
            var countryName = ""

            // 查找区域和国家信息
            for component in components {
                if component.contains("荃灣") || component.contains("荃湾") {
                    suburb = "荃灣"
                } else if component.contains("香港") || component.contains("Hong Kong") {
                    countryName = component
                }
            }

            return AddressComponents(
                streetNumber: hkComponents.streetNumber,
                streetName: hkComponents.streetName,
                suburb: suburb,
                state: "", // 香港不使用州/省
                postalCode: "", // 香港不使用邮编
                country: countryName
            )
        }

        // 其他国家使用原有逻辑
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        var streetNumber = ""
        var streetName = ""
        var suburb = ""
        var state = ""
        var postalCode = ""
        var country = ""

        if let firstComponent = components.first {
            let parts = firstComponent.components(separatedBy: " ")
            if let first = parts.first, first.rangeOfCharacter(from: .decimalDigits) != nil {
                streetNumber = first
                streetName = parts.dropFirst().joined(separator: " ")
            } else {
                streetName = firstComponent
            }
        }

        if components.count > 1 {
            suburb = components[1]
        }

        if components.count > 2 {
            let statePostal = components[2].components(separatedBy: " ")
            if statePostal.count >= 2 {
                state = statePostal[0]
                postalCode = statePostal[1]
            }
        }

        if components.count > 3 {
            country = components[3]
        }

        return AddressComponents(
            streetNumber: streetNumber,
            streetName: streetName,
            suburb: suburb,
            state: state,
            postalCode: postalCode,
            country: country
        )
    }

    /// 构建地址字符串
    private func buildAddressFromPlacemark(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        if let streetNumber = placemark.subThoroughfare, let streetName = placemark.thoroughfare {
            components.append("\(streetNumber) \(streetName)")
        } else if let streetName = placemark.thoroughfare {
            components.append(streetName)
        }

        if let suburb = placemark.locality {
            components.append(suburb)
        }

        if let state = placemark.administrativeArea, let postalCode = placemark.postalCode {
            components.append("\(state) \(postalCode)")
        }

        if let country = placemark.country {
            components.append(country)
        }

        return components.joined(separator: ", ")
    }

    /// 创建失败的验证结果
    private func createFailedValidationScore(originalAddress: String, reason: String) -> AddressValidationScore {
        return AddressValidationScore(
            originalAddress: originalAddress,
            reverseGeocodedAddress: "",
            totalScore: 0.0,
            streetNumberValidation: .unknown,
            streetNameValidation: .unknown,
            postalCodeValidation: .unknown,
            countryValidation: .unknown,
            issues: [reason]
        )
    }

    /// 测试函数 - 验证Glen Waverley地址
    func testGlenWaverleyAddress() async {
        let testAddress = "14 Kerferd Road, Glen Waverley, VIC 3150"
        let testCoordinate = CLLocationCoordinate2D(latitude: -37.8770, longitude: 145.1610)

        Logger.info("🧪 开始测试地址验证: \(testAddress)", type: .location)

        let result = await validateAddressWithReverseGeocoding(
            originalAddress: testAddress,
            coordinate: testCoordinate
        )

        Logger.info("🧪 测试结果:", type: .location)
        Logger.info("  📍 原始地址: \(result.originalAddress)", type: .location)
        Logger.info("  📍 反向地址: \(result.reverseGeocodedAddress)", type: .location)
        Logger.info("  🎯 总分: \(Int(result.totalScore))分", type: .location)
        Logger.info("  🏠 门牌号: \(result.streetNumberValidation.rawValue)", type: .location)
        Logger.info("  🛣️ 街道名: \(result.streetNameValidation.rawValue)", type: .location)
        Logger.info("  📮 邮编: \(result.postalCodeValidation.rawValue)", type: .location)
        Logger.info("  🌏 国家: \(result.countryValidation.rawValue)", type: .location)

        if !result.issues.isEmpty {
            Logger.info("  ⚠️ 问题: \(result.issues.joined(separator: "; "))", type: .location)
        }
    }

    /// 测试严格门牌号验证模式 - 差异超过±2直接失效
    func testStrictStreetNumberValidation() async {
        Logger.info("🚨 开始测试严格门牌号验证模式", type: .location)

        // 测试地址：不同门牌号差异的情况，包括连号地址
        let testCases = [
            ("55 Batten Street, Glen Waverley, VIC 3150, Australia", "5 Batten St, Glen Waverley, VIC 3150, Australia", 50), // 差50号 - 应该失效
            ("123 Collins Street, Melbourne, VIC 3000, Australia", "123 Collins St, Melbourne, VIC 3000, Australia", 0),   // 完全匹配
            ("45 Smith Road, Glen Waverley, VIC 3150, Australia", "47 Smith Rd, Glen Waverley, VIC 3150, Australia", 2),   // 差2号 - 应该通过
            ("10 Main Street, Melbourne, VIC 3000, Australia", "13 Main St, Melbourne, VIC 3000, Australia", 3),           // 差3号 - 应该失效
            ("20 Park Avenue, Melbourne, VIC 3000, Australia", "21 Park Ave, Melbourne, VIC 3000, Australia", 1),          // 差1号 - 应该通过

            // 连号地址测试案例
            ("1-5 Collins Street, Melbourne, VIC 3000, Australia", "2 Collins St, Melbourne, VIC 3000, Australia", 0),     // 连号内的2号 - 应该通过
            ("1-5 Collins Street, Melbourne, VIC 3000, Australia", "3 Collins St, Melbourne, VIC 3000, Australia", 0),     // 连号内的3号 - 应该通过
            ("1-5 Collins Street, Melbourne, VIC 3000, Australia", "4 Collins St, Melbourne, VIC 3000, Australia", 0),     // 连号内的4号 - 应该通过
            ("1-5 Collins Street, Melbourne, VIC 3000, Australia", "7 Collins St, Melbourne, VIC 3000, Australia", 2),     // 连号外的7号 (距离5号2个) - 应该通过
            ("1-5 Collins Street, Melbourne, VIC 3000, Australia", "8 Collins St, Melbourne, VIC 3000, Australia", 3),     // 连号外的8号 (距离5号3个) - 应该失效
            ("10 Main Street, Melbourne, VIC 3000, Australia", "8-12 Main St, Melbourne, VIC 3000, Australia", 0),        // 10号在8-12范围内 - 应该通过
            ("15 Main Street, Melbourne, VIC 3000, Australia", "8-12 Main St, Melbourne, VIC 3000, Australia", 3),        // 15号距离12号3个 - 应该失效

            // 复合门牌号测试案例（您提到的实际案例）
            ("M104/25 O'Sullivan Road, Glen Waverley, VIC 3150, Australia", "22-32 O'Sullivan Rd, Glen Waverley, VIC 3150, Australia", 0), // 25号在22-32范围内 - 应该通过
            ("A5/30 Collins Street, Melbourne, VIC 3000, Australia", "28-35 Collins St, Melbourne, VIC 3000, Australia", 0),              // 30号在28-35范围内 - 应该通过
            ("B12/40 Smith Road, Glen Waverley, VIC 3150, Australia", "35-38 Smith Rd, Glen Waverley, VIC 3150, Australia", 2),          // 40号距离38号2个 - 应该通过
            ("C3/50 Park Avenue, Melbourne, VIC 3000, Australia", "35-45 Park Ave, Melbourne, VIC 3000, Australia", 5),                  // 50号距离45号5个 - 应该失效
        ]

        for (original, reverse, expectedDifference) in testCases {
            Logger.info("", type: .location)
            Logger.info("🔍 测试案例: \(original) vs \(reverse) (预期差异: \(expectedDifference)号)", type: .location)

            // 模拟验证过程
            let originalComponents = parseAddressComponents(original)
            let reverseComponents = parseAddressComponents(reverse)

            let streetNumberScore = validateStreetNumber(original: originalComponents.streetNumber, reverse: reverseComponents.streetNumber)
            let streetNameScore = validateStreetName(original: originalComponents.streetName, reverse: reverseComponents.streetName)
            let postalCodeScore = validatePostalCode(original: originalComponents.postalCode, reverse: reverseComponents.postalCode)
            let countryScore = validateCountry(original: originalComponents.country, reverse: reverseComponents.country)

            // 检查门牌号差异
            let streetNumberDifference = getStreetNumberDifference(original: originalComponents.streetNumber, reverse: reverseComponents.streetNumber)
            let isStreetNumberCriticallyDifferent = streetNumberDifference > 2

            // 计算总分（使用新的严格验证逻辑）
            let totalScore: Double
            if isStreetNumberCriticallyDifferent {
                totalScore = 0.0
                Logger.info("  🚨 门牌号差异过大 (差\(streetNumberDifference)号)，地址验证直接失效", type: .location)
            } else {
                // 使用标准加权计算
                totalScore = (streetNumberScore.score * 0.3) + (streetNameScore.score * 0.4) + (postalCodeScore.score * 0.2) + (countryScore.score * 0.1)
                Logger.info("  ✅ 门牌号差异在可接受范围内 (差\(streetNumberDifference)号)，使用加权计算", type: .location)
            }

            let status = totalScore > 0 ? "✅ 通过" : "❌ 失效"
            Logger.info("  📊 验证结果: \(Int(totalScore))分 \(status)", type: .location)
            Logger.info("    门牌号: \(Int(streetNumberScore.score))分 (差异: \(streetNumberDifference)号)", type: .location)
            Logger.info("    街道名: \(Int(streetNameScore.score))分, 邮编: \(Int(postalCodeScore.score))分, 国家: \(Int(countryScore.score))分", type: .location)
        }

        Logger.info("", type: .location)
        Logger.info("🎯 测试结论:", type: .location)
        Logger.info("  🚨 门牌号差异 > ±2: 直接失效，防止送错地址", type: .location)
        Logger.info("  ✅ 门牌号差异 ≤ ±2: 使用正常验证逻辑", type: .location)
        Logger.info("  🎯 这确保了即使其他组件100分，门牌号差异过大也会被拒绝", type: .location)
    }
}

// MARK: - 验证配置
enum AddressValidationMode: String, CaseIterable {
    case standard = "standard"    // 标准模式：80分通过
    case strict = "strict"        // 严格模式：95分通过
    case perfect = "perfect"      // 完美模式：100分通过（所有组件都必须完美匹配）

    var threshold: Double {
        switch self {
        case .standard: return 80.0
        case .strict: return 95.0
        case .perfect: return 100.0
        }
    }

    var description: String {
        switch self {
        case .standard: return "标准验证"
        case .strict: return "严格验证"
        case .perfect: return "完美验证"
        }
    }

    var detailDescription: String {
        switch self {
        case .standard: return "80分通过 - 适合一般用途"
        case .strict: return "95分通过 - 推荐配送业务"
        case .perfect: return "100分通过 - 所有组件必须完美匹配"
        }
    }

    var riskDescription: String {
        switch self {
        case .standard: return "允许一定程度的地址差异，适合对地址精度要求不高的场景。"
        case .strict: return "要求高精度地址匹配，门牌号差1-2号可接受，适合配送业务。"
        case .perfect: return "要求完美匹配，任何差异都不允许，适合对地址精度要求极高的场景。"
        }
    }
}

// MARK: - 数据结构
struct AddressValidationScore {
    let originalAddress: String
    let reverseGeocodedAddress: String
    let totalScore: Double
    let streetNumberValidation: AddressValidationStatus
    let streetNameValidation: AddressValidationStatus
    let postalCodeValidation: AddressValidationStatus
    let countryValidation: AddressValidationStatus
    let issues: [String]
}

struct AddressComponents {
    let streetNumber: String
    let streetName: String
    let suburb: String
    let state: String
    let postalCode: String
    let country: String
}

struct ValidationResult {
    let score: Double
    let status: AddressValidationStatus
    let issue: String
}

// 地理编码结果状态
enum GeocodingStatus {
    case success                // 成功获取坐标
    case partialSuccess         // 成功但可能不准确（例如只找到城市级别）
    case failed                 // 地理编码失败
    case rateLimited            // 达到API速率限制
    case networkError           // 网络错误
    case timeout                // 请求超时
    case invalidAddress         // 无效地址格式
    case notFound               // 地址未找到

    // 用户友好的错误消息
    var message: String {
        switch self {
        case .success:
            return "geocoding_success".localized
        case .partialSuccess:
            return "geocoding_partial_success".localized
        case .failed:
            return "geocoding_failed".localized
        case .rateLimited:
            return "geocoding_rate_limited".localized
        case .networkError:
            return "geocoding_network_error".localized
        case .timeout:
            return "geocoding_timeout".localized
        case .invalidAddress:
            return "geocoding_invalid_address".localized
        case .notFound:
            return "geocoding_not_found".localized
        }
    }

    // 是否为错误状态
    var isError: Bool {
        switch self {
        case .success, .partialSuccess:
            return false
        default:
            return true
        }
    }

    // 是否需要重试
    var shouldRetry: Bool {
        switch self {
        case .rateLimited, .networkError, .timeout:
            return true
        default:
            return false
        }
    }
}

// 地址匹配检查结果
struct AddressMatchResult {
    let isMatch: Bool
    let correctedAddress: String
    let reason: String
    let confidence: Double // 0.0 - 1.0
    let modificationType: AddressModificationType
    let suggestions: [String]
}

// 地址验证结果
struct AddressValidationResult {
    let originalAddress: String
    let geocodedAddress: String
    let coordinate: CLLocationCoordinate2D
    let isModified: Bool
    let confidence: AddressConfidence
    let modificationType: AddressModificationType
    let suggestions: [String]
    let warningMessage: String?
    let placemark: CLPlacemark?  // 🏗️ 添加 placemark 字段用于结构化地址信息

    // 🏗️ 便利初始化方法（向后兼容）
    init(
        originalAddress: String,
        geocodedAddress: String,
        coordinate: CLLocationCoordinate2D,
        isModified: Bool,
        confidence: AddressConfidence,
        modificationType: AddressModificationType,
        suggestions: [String],
        warningMessage: String?,
        placemark: CLPlacemark? = nil
    ) {
        self.originalAddress = originalAddress
        self.geocodedAddress = geocodedAddress
        self.coordinate = coordinate
        self.isModified = isModified
        self.confidence = confidence
        self.modificationType = modificationType
        self.suggestions = suggestions
        self.warningMessage = warningMessage
        self.placemark = placemark
    }
}

// 地址置信度
enum AddressConfidence {
    case high       // 完全匹配
    case medium     // 轻微修正
    case low        // 重大修正
    case veryLow    // 可能完全错误

    var description: String {
        switch self {
        case .high: return "高置信度"
        case .medium: return "中等置信度"
        case .low: return "低置信度"
        case .veryLow: return "极低置信度"
        }
    }
}

// 地址修正类型
enum AddressModificationType {
    case none           // 无修正
    case minor          // 轻微修正（如拼写、缩写）
    case streetName     // 街道名修正
    case suburb         // 城市/郊区修正
    case major          // 重大修正

    var description: String {
        switch self {
        case .none: return "无修正"
        case .minor: return "轻微修正"
        case .streetName: return "街道名修正"
        case .suburb: return "城市/郊区修正"
        case .major: return "重大修正"
        }
    }
}

// 地理编码结果
struct GeocodingResult {
    let address: String
    let coordinate: CLLocationCoordinate2D?
    let status: GeocodingStatus
    let placemark: CLPlacemark?
    let errorMessage: String?
    let attemptCount: Int

    // 初始化成功结果
    init(address: String, coordinate: CLLocationCoordinate2D, placemark: CLPlacemark? = nil, status: GeocodingStatus = .success, attemptCount: Int = 1) {
        // 🛡️ 防护：确保地址是有效的字符串
        self.address = String(describing: address).trimmingCharacters(in: .whitespacesAndNewlines)
        self.coordinate = coordinate
        self.placemark = placemark
        self.status = status
        self.errorMessage = nil
        self.attemptCount = attemptCount
    }

    // 初始化失败结果
    init(address: String, status: GeocodingStatus, errorMessage: String? = nil, attemptCount: Int = 1) {
        // 🛡️ 防护：确保地址是有效的字符串
        self.address = String(describing: address).trimmingCharacters(in: .whitespacesAndNewlines)
        self.coordinate = nil
        self.placemark = nil
        self.status = status
        self.errorMessage = errorMessage ?? status.message
        self.attemptCount = attemptCount
    }

    // 是否成功
    var isSuccess: Bool {
        return coordinate != nil && !status.isError
    }
}

// 地理编码请求
struct GeocodingRequest: Identifiable, Equatable {
    let id = UUID()
    let address: String
    let priority: Int
    let attemptCount: Int
    let timestamp: Date
    let enhancedAddress: String?

    init(address: String, priority: Int = 0, attemptCount: Int = 0, enhancedAddress: String? = nil) {
        self.address = address
        self.priority = priority
        self.attemptCount = attemptCount
        self.timestamp = Date()
        self.enhancedAddress = enhancedAddress
    }

    // 创建增加尝试次数的新请求
    func withIncrementedAttempt() -> GeocodingRequest {
        return GeocodingRequest(
            address: address,
            priority: priority,
            attemptCount: attemptCount + 1,
            enhancedAddress: enhancedAddress
        )
    }

    static func == (lhs: GeocodingRequest, rhs: GeocodingRequest) -> Bool {
        return lhs.id == rhs.id
    }
}

// 超时错误
struct TimeoutError: Error, LocalizedError {
    let seconds: TimeInterval

    var errorDescription: String? {
        return "Operation timed out after \(seconds) seconds"
    }
}

// 地理编码服务
class GeocodingService {
    // 单例
    static let shared = GeocodingService()

    // 配置参数
    private let maxConcurrentRequests = 5
    private let requestInterval: TimeInterval = 0.2  // 200ms间隔，避免过快请求
    private let maxRetryAttempts = 3
    private let requestTimeout: TimeInterval = 5.0  // 优化：减少到5秒
    private let maxCacheSize = 500

    // 请求队列和状态
    private var requestQueue: [GeocodingRequest] = []
    private var activeRequests: Set<UUID> = []
    private var isProcessing = false
    private var timer: Timer?
    private var geocodingCache: [String: GeocodingResult] = [:]

    // 发布者
    private let resultSubject = PassthroughSubject<GeocodingResult, Never>()
    var resultPublisher: AnyPublisher<GeocodingResult, Never> {
        return resultSubject.eraseToAnyPublisher()
    }

    // 私有初始化方法
    private init() {}

    // 添加地理编码请求
    func geocodeAddress(_ address: String, priority: Int = 0) -> AnyPublisher<GeocodingResult, Never> {
        // 检查缓存
        if let cachedResult = geocodingCache[address] {
            Logger.info("使用缓存的地理编码结果: \(address)", type: .location)
            return Just(cachedResult).eraseToAnyPublisher()
        }

        // 🌍 使用全球地址处理器
        return Future { promise in
            Task {
                let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(address)

                let result: GeocodingResult
                switch globalResult {
                case .success(let originalAddress, let formattedAddress, let coordinate, let placemark, let strategy, _):
                    result = GeocodingResult(
                        address: originalAddress,
                        coordinate: coordinate,
                        placemark: placemark,
                        status: .success,
                        attemptCount: 1
                    )
                    Logger.info("🌍 全球地址处理成功: \(strategy) - \(formattedAddress)", type: .location)

                case .failed(let address, let reason):
                    result = GeocodingResult(
                        address: address,
                        status: .notFound,
                        errorMessage: reason,
                        attemptCount: 1
                    )
                    Logger.warning("🌍 全球地址处理失败: \(reason)", type: .location)
                }

                // 🛡️ 确保缓存操作在主线程上执行
                await MainActor.run {
                    // 缓存结果
                    self.geocodingCache[address] = result
                }
                promise(.success(result))
            }
        }
        .eraseToAnyPublisher()
    }

    // 批量地理编码
    func geocodeBatch(_ addresses: [String], priority: Int = 0) -> AnyPublisher<[GeocodingResult], Never> {
        // 创建一个结果收集器
        let resultCollector = PassthroughSubject<[GeocodingResult], Never>()
        var results: [GeocodingResult] = []
        var completedCount = 0

        // 为每个地址创建请求
        for address in addresses {
            geocodeAddress(address, priority: priority)
                .first() // 只取第一个结果
                .sink { result in
                    results.append(result)
                    completedCount += 1

                    // 当所有请求完成时，发送结果
                    if completedCount == addresses.count {
                        resultCollector.send(results)
                        resultCollector.send(completion: .finished)
                    }
                }
                .store(in: &cancellables)
        }

        return resultCollector.eraseToAnyPublisher()
    }

    // 🎯 Async版本的地理编码方法
    func geocodeAddressAsync(_ address: String, priority: Int = 0) async throws -> GeocodingResult {
        return await withCheckedContinuation { continuation in
            geocodeAddress(address, priority: priority)
                .first()
                .sink { result in
                    continuation.resume(returning: result)
                }
                .store(in: &cancellables)
        }
    }

    // 🎯 Async版本的批量地理编码方法
    func geocodeBatchAsync(_ addresses: [String], priority: Int = 0) async throws -> [GeocodingResult] {
        return await withCheckedContinuation { continuation in
            geocodeBatch(addresses, priority: priority)
                .first()
                .sink { results in
                    continuation.resume(returning: results)
                }
                .store(in: &cancellables)
        }
    }

    // 取消所有请求
    func cancelAllRequests() {
        requestQueue.removeAll()
        activeRequests.removeAll()
        stopProcessing()
    }

    // 清除缓存
    func clearCache() {
        geocodingCache.removeAll()
        Logger.info("已清除所有地理编码缓存", type: .location)
    }

    // 清除特定地址的缓存
    func clearCacheForAddress(_ address: String) {
        geocodingCache.removeValue(forKey: address)
        Logger.info("已清除地址缓存: \(address)", type: .location)
    }

    // 清除不完整地址的缓存（用于修复之前错误缓存的地址）
    func clearIncompleteAddressCache() {
        let keysToRemove = geocodingCache.keys.filter { address in
            !isAddressBasicallyComplete(address)
        }

        for key in keysToRemove {
            geocodingCache.removeValue(forKey: key)
        }

        if !keysToRemove.isEmpty {
            let removedCount = keysToRemove.count
            let removedKeys = keysToRemove.joined(separator: ", ")
            Logger.info("已清除\(removedCount)个不完整地址的缓存: \(removedKeys)", type: .location)
        }
    }

    // 严格验证地址（阻止自动修正）
    func strictValidateAddress(_ address: String) -> AnyPublisher<AddressValidationResult, Never> {
        return Future<AddressValidationResult, Never> { promise in
            Task {
                let result = await self.performStrictAddressValidation(address)
                promise(.success(result))
            }
        }
        .eraseToAnyPublisher()
    }

    // 验证地址并检测修正（保留原有功能）
    func validateAddress(_ address: String) -> AnyPublisher<AddressValidationResult, Never> {
        return Future<AddressValidationResult, Never> { promise in
            Task {
                let result = await self.performAddressValidation(address)
                promise(.success(result))
            }
        }
        .eraseToAnyPublisher()
    }

    // 执行严格地址验证（阻止自动修正）
    private func performStrictAddressValidation(_ address: String) async -> AddressValidationResult {
        // 🔍 调试：记录地址验证详情
        logInfo("🔍 开始严格验证地址: '\(address)'")

        // 首先进行格式验证
        let formatValidation = validateAddressFormat(address)
        if !formatValidation.isValid {
            logInfo("❌ 格式验证失败: \(formatValidation.errorMessage ?? "未知错误")")
            return AddressValidationResult(
                originalAddress: address,
                geocodedAddress: "",
                coordinate: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                isModified: false,
                confidence: .veryLow,
                modificationType: .none,
                suggestions: formatValidation.suggestions,
                warningMessage: formatValidation.errorMessage
            )
        }

        // 如果格式验证通过，进行地理编码但严格检查结果
        // 🚦 等待速率限制
        await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

        let geocoder = CLGeocoder()
        let melbourneRegion = createMelbourneRegion()

        // 🎯 使用智能地理编码候选地址
        let candidates = AddressStandardizer.generateGeocodingCandidates(address)
        logInfo("📋 生成候选地址: \(candidates)")

        for (index, candidate) in candidates.enumerated() {
            do {
                logInfo("GeocodingService - 尝试地理编码候选地址[\(index)]: '\(candidate)'")

                let placemarks = try await geocoder.geocodeAddressString(
                    candidate,
                    in: melbourneRegion,
                    preferredLocale: Locale(identifier: "en_AU")
                )

                if let placemark = placemarks.first,
                   let coordinate = placemark.location?.coordinate {

                    logInfo("GeocodingService - 候选地址[\(index)]成功获取坐标: \(coordinate)")

                    // 🔍 详细记录Apple Maps返回的地址组件
                    logInfo("📍 Apple Maps返回详情:")
                    logInfo("   - subThoroughfare (门牌号): '\(placemark.subThoroughfare ?? "nil")'")
                    logInfo("   - thoroughfare (街道): '\(placemark.thoroughfare ?? "nil")'")
                    logInfo("   - locality (城市): '\(placemark.locality ?? "nil")'")
                    logInfo("   - administrativeArea (州): '\(placemark.administrativeArea ?? "nil")'")
                    logInfo("   - postalCode (邮编): '\(placemark.postalCode ?? "nil")'")
                    logInfo("   - country (国家): '\(placemark.country ?? "nil")'")

                    // 构建地理编码后的地址
                    let geocodedAddress = buildAddressString(from: placemark)
                    logInfo("🏠 构建的完整地址: '\(geocodedAddress)'")

                    // 严格检查是否被修正（使用原始地址进行比较）
                    let isStrictMatch = isStrictAddressMatch(original: address, geocoded: geocodedAddress, placemark: placemark)

                    if !isStrictMatch.isMatch {
                        // 如果地址被修正，拒绝结果
                        logError("GeocodingService - 候选地址[\(index)]被修正，拒绝结果: \(isStrictMatch.reason)")
                        continue // 尝试下一个候选地址
                    }

                    // 地址匹配，返回成功结果
                    logInfo("GeocodingService - 候选地址[\(index)]验证成功")
                    return AddressValidationResult(
                        originalAddress: address,
                        geocodedAddress: geocodedAddress,
                        coordinate: coordinate,
                        isModified: candidate != address, // 如果使用了不同的候选地址，标记为已修改
                        confidence: .high,
                        modificationType: candidate != address ? .minor : .none,
                        suggestions: candidate != address ? ["地址已自动标准化：\(candidate)"] : [],
                        warningMessage: nil
                    )
                }

            } catch {
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    logError("🚨 GeocodingService - Apple Maps API限制触发 - \(error.localizedDescription)")
                    // 🎯 智能解析等待时间
                    let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                    await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                    logError("🚫 GeocodingService - Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)")
                    // 🎯 智能解析等待时间
                    let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                    await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                } else {
                    logError("GeocodingService - 候选地址[\(index)]地理编码失败: \(error.localizedDescription)")
                }
                continue // 尝试下一个候选地址
            }
        }

        // 所有候选地址都失败了
        logError("GeocodingService - 所有候选地址都无法地理编码")
        return AddressValidationResult(
            originalAddress: address,
            geocodedAddress: "",
            coordinate: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            isModified: false,
            confidence: .veryLow,
            modificationType: .none,
            suggestions: [
                "address_not_exist_or_incorrect_format".localized,
                "please_check_address_spelling".localized,
                "try_smaller_street_number".localized,
                "use_full_street_type_name".localized,
                "try_add_more_address_details".localized
            ],
            warningMessage: "cannot_find_address".localized
        )
    }

    // 执行地址验证
    private func performAddressValidation(_ address: String) async -> AddressValidationResult {
        // 🚦 等待速率限制
        await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

        let geocoder = CLGeocoder()
        let melbourneRegion = createMelbourneRegion()

        // 🎯 使用智能地理编码候选地址
        let candidates = AddressStandardizer.generateGeocodingCandidates(address)

        for (index, candidate) in candidates.enumerated() {
            do {
                logInfo("GeocodingService - 验证候选地址[\(index)]: '\(candidate)'")

                // 执行地理编码
                let placemarks = try await geocoder.geocodeAddressString(
                    candidate,
                    in: melbourneRegion,
                    preferredLocale: Locale(identifier: "en_AU")
                )

                if let placemark = placemarks.first,
                   let coordinate = placemark.location?.coordinate {

                    logInfo("GeocodingService - 验证候选地址[\(index)]成功获取坐标: \(coordinate)")

                    // 构建地理编码后的地址
                    let geocodedAddress = buildAddressString(from: placemark)

                    // 分析地址修正（使用原始地址进行比较）
                    let analysis = analyzeAddressModification(
                        original: address,
                        geocoded: geocodedAddress,
                        placemark: placemark
                    )

                    return AddressValidationResult(
                        originalAddress: address,
                        geocodedAddress: geocodedAddress,
                        coordinate: coordinate,
                        isModified: analysis.modificationType != .none || candidate != address,
                        confidence: analysis.confidence,
                        modificationType: analysis.modificationType,
                        suggestions: candidate != address ?
                            analysis.suggestions + ["地址已自动标准化：\(candidate)"] :
                            analysis.suggestions,
                        warningMessage: analysis.warningMessage
                    )
                }

            } catch {
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    logError("🚨 GeocodingService - Apple Maps API限制触发 - \(error.localizedDescription)")
                    // 🎯 智能解析等待时间
                    let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                    await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                    logError("🚫 GeocodingService - Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)")
                    // 🎯 智能解析等待时间
                    let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                    await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                } else {
                    logError("GeocodingService - 验证候选地址[\(index)]失败: \(error.localizedDescription)")
                }
                continue // 尝试下一个候选地址
            }
        }

        // 所有候选地址都失败了
        logError("GeocodingService - 所有验证候选地址都失败")
        return AddressValidationResult(
            originalAddress: address,
            geocodedAddress: "",
            coordinate: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            isModified: false,
            confidence: .veryLow,
            modificationType: .none,
            suggestions: [
                "cannot_find_address".localized,
                "please_check_spelling_or_add_details".localized,
                "try_smaller_street_number".localized,
                "use_full_street_type_name".localized
            ],
            warningMessage: "cannot_find_address_check_spelling".localized
        )
    }

    // 分析地址修正
    private func analyzeAddressModification(
        original: String,
        geocoded: String,
        placemark: CLPlacemark
    ) -> (confidence: AddressConfidence, modificationType: AddressModificationType, suggestions: [String], warningMessage: String?) {

        let normalizedOriginal = normalizeAddress(original)
        let normalizedGeocoded = normalizeAddress(geocoded)

        // 完全匹配
        if normalizedOriginal == normalizedGeocoded {
            return (.high, .none, [], nil)
        }

        // 分析修正类型
        let streetNumberOriginal = extractStreetNumber(from: original)
        let streetNumberGeocoded = extractStreetNumber(from: geocoded)

        let streetNameOriginal = extractStreetName(from: original)
        let streetNameGeocoded = extractStreetName(from: geocoded)

        let suburbOriginal = extractSuburb(from: original)
        let suburbGeocoded = placemark.locality ?? ""

        var modificationType: AddressModificationType = .none
        var confidence: AddressConfidence = .high
        var suggestions: [String] = []
        var warningMessage: String? = nil

        // 检查街道号码
        if streetNumberOriginal != streetNumberGeocoded {
            modificationType = .major
            confidence = .veryLow

            // 检查是否是系统返回了更大的号码（可能是街道最大号码）
            if let origNum = Int(streetNumberOriginal), let geoNum = Int(streetNumberGeocoded), geoNum > origNum {
                let difference = geoNum - origNum
                if difference > 10 {
                    warningMessage = "街道号码 '\(streetNumberOriginal)' 可能不存在，系统返回了 '\(streetNumberGeocoded)' - 建议尝试该街道的较小号码"
                    suggestions.append("尝试使用 \(streetNameOriginal) 街道的较小号码")
                } else {
                    warningMessage = "街道号码被修正：'\(streetNumberOriginal)' → '\(streetNumberGeocoded)'"
                }
            } else {
                warningMessage = "街道号码被修正：'\(streetNumberOriginal)' → '\(streetNumberGeocoded)'"
            }
        }
        // 检查街道名
        else if !isStreetNameSimilar(streetNameOriginal, streetNameGeocoded) {
            modificationType = .streetName
            confidence = .low
            warningMessage = "街道名被修正：'\(streetNameOriginal)' → '\(streetNameGeocoded)'"
            suggestions.append("请确认街道名是否正确：\(streetNameGeocoded)")
        }
        // 检查城市/郊区
        else if !isSuburbSimilar(suburbOriginal, suburbGeocoded) {
            modificationType = .suburb
            confidence = .medium
            warningMessage = "城市/郊区被修正：'\(suburbOriginal)' → '\(suburbGeocoded)'"
            suggestions.append("请确认城市/郊区是否正确：\(suburbGeocoded)")
        }
        // 轻微修正（拼写、缩写等）
        else if calculateSimilarity(normalizedOriginal, normalizedGeocoded) > 0.8 {
            modificationType = .minor
            confidence = .medium
            suggestions.append("地址已自动修正拼写或格式")
        }
        // 重大修正
        else {
            modificationType = .major
            confidence = .veryLow
            warningMessage = "地址被大幅修正，请确认是否正确"
            suggestions.append("建议重新输入完整准确的地址")
        }

        return (confidence, modificationType, suggestions, warningMessage)
    }

    // 构建地址字符串
    private func buildAddressString(from placemark: CLPlacemark) -> String {
        var components: [String] = []

        if let subThoroughfare = placemark.subThoroughfare {
            components.append(subThoroughfare)
        }
        if let thoroughfare = placemark.thoroughfare {
            components.append(thoroughfare)
        }
        if let locality = placemark.locality {
            components.append(locality)
        }
        if let administrativeArea = placemark.administrativeArea {
            components.append(administrativeArea)
        }
        if let postalCode = placemark.postalCode {
            components.append(postalCode)
        }
        if let country = placemark.country {
            components.append(country)
        }

        return components.joined(separator: ", ")
    }



    // 提取街道号码 - 支持国际地址格式和复合门牌号
    private func extractStreetNumber(from address: String) -> String {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址特殊处理
            let hkComponents = AddressCountryDetector.parseHongKongAddress(address)
            return hkComponents.streetNumber
        }

        // 首先尝试提取复合门牌号的主要部分（如 23/567 -> 567）
        if let mainNumber = extractMainStreetNumberFromAddress(address) {
            return mainNumber
        }

        // 其他国家使用原有逻辑，但要提取实际的数字部分
        let components = address.components(separatedBy: " ")
        for component in components {
            // 检查是否包含数字
            if component.rangeOfCharacter(from: .decimalDigits) != nil {
                return component.trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }

        return ""
    }

    // 提取街道名 - 支持国际地址格式
    private func extractStreetName(from address: String) -> String {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址特殊处理
            let hkComponents = AddressCountryDetector.parseHongKongAddress(address)
            return hkComponents.streetName
        }

        // 其他国家使用原有逻辑
        let components = address.components(separatedBy: ",")
        if let firstPart = components.first {
            let words = firstPart.components(separatedBy: " ")
            if words.count > 1 {
                return words.dropFirst().joined(separator: " ").trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        return ""
    }

    // 提取城市/郊区
    private func extractSuburb(from address: String) -> String {
        let components = address.components(separatedBy: ",")
        if components.count > 1 {
            return components[1].trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return ""
    }

    // 检查街道名相似性
    private func isStreetNameSimilar(_ name1: String, _ name2: String) -> Bool {
        let normalized1 = normalizeAddress(name1)
        let normalized2 = normalizeAddress(name2)

        // 完全匹配
        if normalized1 == normalized2 {
            return true
        }

        // 检查常见缩写
        let abbreviations = [
            ("street", "st"), ("road", "rd"), ("avenue", "ave"),
            ("close", "cl"), ("court", "ct"), ("drive", "dr"),
            ("place", "pl"), ("lane", "ln"), ("crescent", "cres")
        ]

        for (full, abbr) in abbreviations {
            if (normalized1.contains(full) && normalized2.contains(abbr)) ||
               (normalized1.contains(abbr) && normalized2.contains(full)) {
                return true
            }
        }

        // 计算相似度
        return calculateSimilarity(normalized1, normalized2) > 0.7
    }

    // 检查城市/郊区相似性
    private func isSuburbSimilar(_ suburb1: String, _ suburb2: String) -> Bool {
        let normalized1 = normalizeAddress(suburb1)
        let normalized2 = normalizeAddress(suburb2)

        return normalized1 == normalized2 || calculateSimilarity(normalized1, normalized2) > 0.8
    }

    // 计算字符串相似度（简单的Levenshtein距离）
    private func calculateSimilarity(_ str1: String, _ str2: String) -> Double {
        let len1 = str1.count
        let len2 = str2.count

        if len1 == 0 { return len2 == 0 ? 1.0 : 0.0 }
        if len2 == 0 { return 0.0 }

        let maxLen = max(len1, len2)
        let distance = levenshteinDistance(str1, str2)

        return 1.0 - Double(distance) / Double(maxLen)
    }

    // Levenshtein距离算法
    private func levenshteinDistance(_ str1: String, _ str2: String) -> Int {
        let arr1 = Array(str1)
        let arr2 = Array(str2)
        let len1 = arr1.count
        let len2 = arr2.count

        var matrix = Array(repeating: Array(repeating: 0, count: len2 + 1), count: len1 + 1)

        for i in 0...len1 {
            matrix[i][0] = i
        }

        for j in 0...len2 {
            matrix[0][j] = j
        }

        for i in 1...len1 {
            for j in 1...len2 {
                let cost = arr1[i-1] == arr2[j-1] ? 0 : 1
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      // deletion
                    matrix[i][j-1] + 1,      // insertion
                    matrix[i-1][j-1] + cost  // substitution
                )
            }
        }

        return matrix[len1][len2]
    }

    // 地址格式验证结果
    struct AddressFormatValidation {
        let isValid: Bool
        let errorMessage: String?
        let suggestions: [String]
    }

    // 严格地址匹配结果
    struct StrictAddressMatch {
        let isMatch: Bool
        let reason: String
        let modificationType: AddressModificationType
    }

    // 验证地址格式
    private func validateAddressFormat(_ address: String) -> AddressFormatValidation {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 基本长度检查
        if trimmedAddress.count < 5 {
            return AddressFormatValidation(
                isValid: false,
                errorMessage: "地址太短，请输入完整地址",
                suggestions: ["包含门牌号", "包含街道名", "包含城市名"]
            )
        }

        // 检查是否包含数字（门牌号）
        let hasNumber = trimmedAddress.rangeOfCharacter(from: .decimalDigits) != nil
        if !hasNumber {
            return AddressFormatValidation(
                isValid: false,
                errorMessage: "地址缺少门牌号",
                suggestions: ["请添加门牌号", "例如：123 Collins Street"]
            )
        }

        // 检查是否包含字母（街道名）
        let hasLetter = trimmedAddress.rangeOfCharacter(from: .letters) != nil
        if !hasLetter {
            return AddressFormatValidation(
                isValid: false,
                errorMessage: "地址缺少街道名",
                suggestions: ["请添加街道名", "例如：123 Collins Street"]
            )
        }

        // 检查地址组件
        let components = trimmedAddress.components(separatedBy: ",")
        if components.count < 2 {
            return AddressFormatValidation(
                isValid: false,
                errorMessage: "地址格式不完整，缺少城市或州信息",
                suggestions: [
                    "使用逗号分隔地址组件",
                    "例如：123 Collins Street, Melbourne, VIC 3000",
                    "至少包含：街道地址, 城市"
                ]
            )
        }

        // 检查第一部分（街道地址）
        let streetAddress = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
        let streetWords = streetAddress.components(separatedBy: " ").filter { !$0.isEmpty }

        if streetWords.count < 2 {
            return AddressFormatValidation(
                isValid: false,
                errorMessage: "街道地址不完整",
                suggestions: [
                    "包含门牌号和街道名",
                    "例如：123 Collins Street",
                    "或：45 Smith Road"
                ]
            )
        }

        // 检查门牌号格式
        let firstWord = streetWords[0]
        if !firstWord.contains(where: { $0.isNumber }) {
            return AddressFormatValidation(
                isValid: false,
                errorMessage: "门牌号格式不正确",
                suggestions: [
                    "门牌号应该在地址开头",
                    "例如：123 Collins Street",
                    "或：45A Smith Road"
                ]
            )
        }

        // 检查是否包含常见的无效内容
        let lowercaseAddress = trimmedAddress.lowercased()
        let invalidPatterns = ["test", "fake", "dummy", "example", "xxx", "yyy", "zzz"]

        for pattern in invalidPatterns {
            if lowercaseAddress.contains(pattern) {
                return AddressFormatValidation(
                    isValid: false,
                    errorMessage: "地址包含无效内容",
                    suggestions: ["请输入真实有效的地址"]
                )
            }
        }

        // 所有检查通过
        return AddressFormatValidation(
            isValid: true,
            errorMessage: nil,
            suggestions: []
        )
    }

    // 严格地址匹配检查
    private func isStrictAddressMatch(original: String, geocoded: String, placemark: CLPlacemark) -> StrictAddressMatch {
        // 提取原始地址的关键组件
        let originalComponents = parseAddressComponents(original)
        let geocodedComponents = parseAddressComponents(geocoded)

        // 检查门牌号
        if originalComponents.streetNumber != geocodedComponents.streetNumber {
            return StrictAddressMatch(
                isMatch: false,
                reason: "门牌号不匹配：'\(originalComponents.streetNumber)' vs '\(geocodedComponents.streetNumber)'",
                modificationType: .major
            )
        }

        // 检查街道名（允许常见缩写）
        if !isStreetNameEquivalent(originalComponents.streetName, geocodedComponents.streetName) {
            return StrictAddressMatch(
                isMatch: false,
                reason: "街道名不匹配：'\(originalComponents.streetName)' vs '\(geocodedComponents.streetName)'",
                modificationType: .streetName
            )
        }

        // 检查城市/郊区（允许一定的灵活性）
        if !isSuburbEquivalent(originalComponents.suburb, geocodedComponents.suburb) {
            return StrictAddressMatch(
                isMatch: false,
                reason: "城市/郊区不匹配：'\(originalComponents.suburb)' vs '\(geocodedComponents.suburb)'",
                modificationType: .suburb
            )
        }

        return StrictAddressMatch(
            isMatch: true,
            reason: "地址匹配",
            modificationType: .none
        )
    }

    // 地址组件结构
    struct AddressComponents {
        let streetNumber: String
        let streetName: String
        let suburb: String
        let state: String
        let postcode: String
    }

    // 解析地址组件
    private func parseAddressComponents(_ address: String) -> AddressComponents {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        // 解析街道地址
        let streetAddress = components.first ?? ""
        let streetWords = streetAddress.components(separatedBy: " ").filter { !$0.isEmpty }

        let streetNumber = streetWords.first ?? ""
        let streetName = streetWords.dropFirst().joined(separator: " ")

        // 解析其他组件
        let suburb = components.count > 1 ? components[1] : ""
        let state = components.count > 2 ? components[2] : ""
        let postcode = components.count > 3 ? components[3] : ""

        return AddressComponents(
            streetNumber: streetNumber,
            streetName: streetName,
            suburb: suburb,
            state: state,
            postcode: postcode
        )
    }

    // 检查街道名是否等效（考虑缩写）
    private func isStreetNameEquivalent(_ name1: String, _ name2: String) -> Bool {
        let normalized1 = normalizeStreetName(name1)
        let normalized2 = normalizeStreetName(name2)

        return normalized1 == normalized2
    }

    // 检查城市/郊区是否等效
    private func isSuburbEquivalent(_ suburb1: String, _ suburb2: String) -> Bool {
        let normalized1 = normalizeSuburb(suburb1)
        let normalized2 = normalizeSuburb(suburb2)

        // 完全匹配
        if normalized1 == normalized2 {
            return true
        }

        // 检查是否是已知的等效郊区
        let equivalentSuburbs = [
            ["glen waverley", "glenwaverley"],
            ["mount waverley", "mt waverley", "mountwaverley"],
            ["wheelers hill", "wheelershill"],
            ["melbourne", "melbourne city", "melbourne cbd"]
        ]

        for group in equivalentSuburbs {
            if group.contains(normalized1) && group.contains(normalized2) {
                return true
            }
        }

        return false
    }

    // 标准化街道名
    private func normalizeStreetName(_ name: String) -> String {
        var normalized = name.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        // 使用词边界匹配，避免误替换街道名中的部分字符
        // 例如避免将 "Creswick" 误认为包含 "Crescent"
        let streetTypeReplacements = [
            ("\\bstreet\\b", "st"),
            ("\\broad\\b", "rd"),
            ("\\bavenue\\b", "ave"),
            ("\\bdrive\\b", "dr"),
            ("\\bcourt\\b", "ct"),
            ("\\bplace\\b", "pl"),
            ("\\blane\\b", "ln"),
            ("\\bclose\\b", "cl"),
            ("\\bcrescent\\b", "cres"),
            ("\\bcircuit\\b", "cct"),
            ("\\bparade\\b", "pde"),
            ("\\bterrace\\b", "tce"),
            ("\\bhighway\\b", "hwy"),
            ("\\bmount\\b", "mt"),
            ("\\bboulevard\\b", "blvd"),
            ("\\bparkway\\b", "pkwy"),
            ("\\bcircle\\b", "cir"),
            ("\\bsquare\\b", "sq"),
            ("\\besplanade\\b", "espl")
        ]

        // 应用替换，使用正则表达式确保只替换完整的单词
        for (pattern, replacement) in streetTypeReplacements {
            normalized = normalized.replacingOccurrences(
                of: pattern,
                with: replacement,
                options: .regularExpression
            )
        }

        return normalized
    }

    // 标准化郊区名
    private func normalizeSuburb(_ suburb: String) -> String {
        return suburb.lowercased()
            .replacingOccurrences(of: "mount", with: "mt")
            .replacingOccurrences(of: " ", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 私有属性
    private var cancellables = Set<AnyCancellable>()

    // 添加请求到队列
    private func addToQueue(_ request: GeocodingRequest) {
        // 按优先级插入队列
        var inserted = false
        for (index, existingRequest) in requestQueue.enumerated() {
            if request.priority > existingRequest.priority {
                requestQueue.insert(request, at: index)
                inserted = true
                break
            }
        }

        if !inserted {
            requestQueue.append(request)
        }

        Logger.info("添加地理编码请求到队列: \(request.address), 优先级: \(request.priority), 队列长度: \(requestQueue.count)", type: .location)

        // 如果队列处理未启动，启动它
        if !isProcessing {
            startProcessing()
        }
    }

    // 启动队列处理
    private func startProcessing() {
        isProcessing = true

        // 创建定时器，定期处理队列
        timer = Timer.scheduledTimer(withTimeInterval: requestInterval, repeats: true) { [weak self] _ in
            self?.processNextRequest()
        }

        Logger.info("启动地理编码队列处理", type: .location)
    }

    // 停止队列处理
    private func stopProcessing() {
        timer?.invalidate()
        timer = nil
        isProcessing = false

        Logger.info("停止地理编码队列处理", type: .location)
    }

    // 处理下一个请求
    private func processNextRequest() {
        // 检查是否有活跃请求槽位
        guard activeRequests.count < maxConcurrentRequests else {
            return
        }

        // 检查队列是否为空
        guard !requestQueue.isEmpty else {
            // 如果队列为空且没有活跃请求，停止处理
            if activeRequests.isEmpty {
                stopProcessing()
            }
            return
        }

        // 取出下一个请求
        let request = requestQueue.removeFirst()

        // 标记为活跃
        activeRequests.insert(request.id)

        // 执行地理编码
        performGeocoding(request)
    }

    // 执行地理编码
    private func performGeocoding(_ request: GeocodingRequest) {
        Logger.info("执行地理编码: \(request.address), 尝试次数: \(request.attemptCount + 1)", type: .location)

        // 🌐 网络状态检查：如果是网络错误重试，记录额外信息
        if request.attemptCount > 0 {
            Logger.info("🔄 重试地理编码 (第\(request.attemptCount + 1)次尝试): \(request.address)", type: .location)
        }

        // 首先检查地址是否基本完整
        if !isAddressBasicallyComplete(request.address) {
            Logger.warning("地址格式不完整，拒绝地理编码: \(request.address)", type: .location)

            // 创建失败结果
            let result = GeocodingResult(
                address: request.address,
                status: .invalidAddress,
                errorMessage: "address_format_incomplete".localized,
                attemptCount: request.attemptCount + 1
            )

            // 移除活跃请求并缓存结果
            Task { @MainActor in
                _ = activeRequests.remove(request.id)
                // 🛡️ 确保缓存操作在主线程上执行
                self.geocodingCache[request.address] = result
            }

            // 发布结果
            resultSubject.send(result)
            return
        }

        // 使用增强的地址或原始地址
        let addressToUse = request.enhancedAddress ?? request.address

        // 创建地理编码器
        let geocoder = CLGeocoder()

        // 创建区域限制（墨尔本区域）
        let melbourneRegion = createMelbourneRegion()

        // 创建任务
        Task {
            do {
                // 添加超时处理
                let geocodingTask = Task {
                    // 检测地址国家以确定地理编码策略
                    let countryInfo = AddressCountryDetector.detectCountry(from: addressToUse)

                    // 🎯 强制使用英文语言环境，避免中文本地化
                    let englishLocales = [
                        Locale(identifier: "en_US"),
                        Locale(identifier: "en_GB"),
                        Locale(identifier: "en_AU"),
                        Locale(identifier: "en")
                    ]

                    var finalPlacemarks: [CLPlacemark]?
                    var lastError: Error?

                    // 尝试不同的英文语言环境，直到获得英文结果
                    for locale in englishLocales {
                        do {
                            let placemarks: [CLPlacemark]

                            if let country = countryInfo, country.code == "HK" {
                                // 香港地址：不使用区域限制
                                placemarks = try await geocoder.geocodeAddressString(
                                    addressToUse,
                                    in: nil,
                                    preferredLocale: locale
                                )
                            } else if let country = countryInfo, country.code == "US" {
                                // 🇺🇸 美国地址：不使用区域限制
                                Logger.info("🇺🇸 检测到美国地址，使用地理编码策略: \(addressToUse)", type: .location)
                                placemarks = try await geocoder.geocodeAddressString(
                                    addressToUse,
                                    in: nil,
                                    preferredLocale: locale
                                )
                            } else {
                                // 澳大利亚地址：使用原有逻辑
                                placemarks = try await geocoder.geocodeAddressString(
                                    addressToUse,
                                    in: melbourneRegion,
                                    preferredLocale: locale
                                )
                            }

                            // 检查返回的地址是否为英文
                            if let firstResult = placemarks.first,
                               let locality = firstResult.locality,
                               !AddressStandardizer.containsChineseCharacters(locality) {
                                finalPlacemarks = placemarks
                                Logger.info("✅ 使用 \(locale.identifier) 获得英文地址结果", type: .location)
                                break
                            } else {
                                Logger.info("⚠️ \(locale.identifier) 返回本地化地址，尝试下一个语言环境", type: .location)
                            }
                        } catch {
                            lastError = error
                            Logger.info("⚠️ \(locale.identifier) 地理编码失败: \(error.localizedDescription)", type: .location)
                        }
                    }

                    // 如果所有语言环境都失败，抛出最后一个错误
                    guard let placemarks = finalPlacemarks else {
                        throw lastError ?? NSError(domain: "GeocodingError", code: -1, userInfo: [NSLocalizedDescriptionKey: "所有语言环境都失败"])
                    }

                    return placemarks
                }

                // 设置超时
                let placemarks = try await withTimeout(seconds: requestTimeout) { try await geocodingTask.value }

                // 处理结果
                await handleGeocodingSuccess(request: request, placemarks: placemarks)
            } catch let timeoutError as TimeoutError {
                // 处理超时
                await handleGeocodingError(
                    request: request,
                    status: .timeout,
                    error: timeoutError
                )
            } catch let error as CLError {
                // 处理CoreLocation错误
                await handleCLError(request: request, error: error)
            } catch {
                // 处理其他错误
                await handleGeocodingError(
                    request: request,
                    status: .failed,
                    error: error
                )
            }
        }
    }

    // 处理地理编码成功
    private func handleGeocodingSuccess(request: GeocodingRequest, placemarks: [CLPlacemark]) async {
        // 移除活跃请求
        await MainActor.run { [self] in
            _ = activeRequests.remove(request.id)
        }

        // 检查是否有结果
        if placemarks.isEmpty {
            // 没有结果，但没有抛出错误
            let result = GeocodingResult(
                address: request.address,
                status: .notFound,
                attemptCount: request.attemptCount + 1
            )

            // 缓存结果并发布
            Task { @MainActor in
                self.geocodingCache[request.address] = result
            }

            // 发布结果
            resultSubject.send(result)

            Logger.warning("地理编码未返回结果: \(request.address)", type: .location)
            return
        }

        // 检查地址是否被自动修正
        for placemark in placemarks {
            if let location = placemark.location?.coordinate {
                // 🔄 坐标系转换：如果在国内，需要将WGS-84转换为GCJ-02
                let originalCoordinate = location
                let convertedCoordinate = originalCoordinate.smartConverted(fromWgs84: true)

                if originalCoordinate.isInChina() {
                    Logger.info("🌍 地理编码坐标转换: \(request.address)", type: .location)
                    Logger.info("   WGS-84: (\(originalCoordinate.latitude), \(originalCoordinate.longitude))", type: .location)
                    Logger.info("   GCJ-02: (\(convertedCoordinate.latitude), \(convertedCoordinate.longitude))", type: .location)
                }

                // 检查地址匹配度（使用转换后的坐标）
                let addressMatchResult = checkAddressMatch(
                    originalAddress: request.address,
                    placemark: placemark,
                    coordinate: convertedCoordinate
                )

                if !addressMatchResult.isMatch {
                    Logger.warning("地址被自动修正，拒绝结果: 原地址='\(request.address)', 修正后='\(addressMatchResult.correctedAddress)', 原因: \(addressMatchResult.reason)", type: .location)

                    // 创建失败结果，明确说明地址被修正
                    let result = GeocodingResult(
                        address: request.address,
                        status: .invalidAddress,
                        errorMessage: String(format: "address_auto_corrected_error".localized, addressMatchResult.correctedAddress),
                        attemptCount: request.attemptCount + 1
                    )

                    // 缓存结果并发布
                    Task { @MainActor in
                        self.geocodingCache[request.address] = result
                    }

                    // 发布结果
                    resultSubject.send(result)
                    return
                }
            }
        }

        // 检测地址国家以确定结果选择策略
        let countryInfo = AddressCountryDetector.detectCountry(from: request.address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址：优先选择香港的结果
            if let hkPlacemark = placemarks.first(where: {
                $0.country?.contains("香港") == true ||
                $0.country?.contains("Hong Kong") == true ||
                $0.administrativeArea?.contains("香港") == true
            }),
               let originalLocation = hkPlacemark.location?.coordinate {

                // 🔄 坐标系转换：香港地址也需要转换
                let convertedLocation = originalLocation.smartConverted(fromWgs84: true)

                Logger.info("🌍 香港地址坐标转换: \(request.address)", type: .location)
                Logger.info("   WGS-84: (\(originalLocation.latitude), \(originalLocation.longitude))", type: .location)
                Logger.info("   GCJ-02: (\(convertedLocation.latitude), \(convertedLocation.longitude))", type: .location)

                // 创建成功结果（使用转换后的坐标）
                let result = GeocodingResult(
                    address: request.address,
                    coordinate: convertedLocation,
                    placemark: hkPlacemark,
                    status: .success,
                    attemptCount: request.attemptCount + 1
                )

                // 缓存结果并发布
                Task { @MainActor in
                    self.geocodingCache[request.address] = result
                }

                // 发布结果
                resultSubject.send(result)

                Logger.info("地理编码成功(香港): \(request.address) -> (\(convertedLocation.latitude), \(convertedLocation.longitude)), 城市: \(hkPlacemark.locality ?? "未知")", type: .location)
                return
            }
        } else if let country = countryInfo, country.code == "US" {
            // 🇺🇸 美国地址：优先选择美国的结果
            Logger.info("🇺🇸 处理美国地址结果选择: \(request.address)", type: .location)

            if let usPlacemark = placemarks.first(where: { $0.country == "United States" }),
               let location = usPlacemark.location?.coordinate {

                // 🔄 坐标系转换：如果在国内，需要将WGS-84转换为GCJ-02
                let originalCoordinate = location
                let convertedLocation = originalCoordinate.smartConverted(fromWgs84: true)

                // 创建成功结果
                let result = GeocodingResult(
                    address: request.address,
                    coordinate: convertedLocation,
                    placemark: usPlacemark,
                    status: .success,
                    attemptCount: request.attemptCount + 1
                )

                // 缓存结果并发布
                Task { @MainActor in
                    self.geocodingCache[request.address] = result
                }

                // 发布结果
                resultSubject.send(result)

                Logger.info("✅ 地理编码成功(美国): \(request.address) -> (\(convertedLocation.latitude), \(convertedLocation.longitude)), 城市: \(usPlacemark.locality ?? "未知")", type: .location)
                return
            }
        } else {
            // 澳大利亚地址：使用原有逻辑
            // 优先选择Glen Waverley的结果
            let glenWaverleyPlacemarks = placemarks.filter {
                let locality = $0.locality?.lowercased() ?? ""
                return locality.contains("glen waverley") && $0.country == "Australia"
            }

            if let glenWaverleyPlacemark = glenWaverleyPlacemarks.first,
               let originalLocation = glenWaverleyPlacemark.location?.coordinate {

                // 🔄 坐标系转换：澳大利亚地址通常不需要转换，但为了一致性也进行检查
                let convertedLocation = originalLocation.smartConverted(fromWgs84: true)

                if originalLocation.isInChina() {
                    Logger.info("🌍 澳大利亚地址坐标转换: \(request.address)", type: .location)
                    Logger.info("   WGS-84: (\(originalLocation.latitude), \(originalLocation.longitude))", type: .location)
                    Logger.info("   GCJ-02: (\(convertedLocation.latitude), \(convertedLocation.longitude))", type: .location)
                }

                // 检查坐标是否在Glen Waverley范围内
                let isInGlenWaverley = isNearMelbourne(convertedLocation)

                // 创建成功结果（使用转换后的坐标）
                let result = GeocodingResult(
                    address: request.address,
                    coordinate: convertedLocation,
                    placemark: glenWaverleyPlacemark,
                    status: isInGlenWaverley ? .success : .partialSuccess,
                    attemptCount: request.attemptCount + 1
                )

                // 缓存结果并发布
                Task { @MainActor in
                    self.geocodingCache[request.address] = result
                }

                // 发布结果
                resultSubject.send(result)

                Logger.info("地理编码成功(Glen Waverley): \(request.address) -> (\(convertedLocation.latitude), \(convertedLocation.longitude))", type: .location)
                return
            }

            // 其次选择澳大利亚的结果
            if let australiaPlacemark = placemarks.first(where: { $0.country == "Australia" }),
               let location = australiaPlacemark.location?.coordinate {
                // 检查坐标是否在墨尔本附近
                let isNearMelbourne = isNearMelbourne(location)

                // 创建成功结果
                let result = GeocodingResult(
                    address: request.address,
                    coordinate: location,
                    placemark: australiaPlacemark,
                    status: isNearMelbourne ? .success : .partialSuccess,
                    attemptCount: request.attemptCount + 1
                )

                // 缓存结果并发布
                Task { @MainActor in
                    self.geocodingCache[request.address] = result
                }

                // 发布结果
                resultSubject.send(result)

                Logger.info("地理编码成功(澳大利亚): \(request.address) -> (\(location.latitude), \(location.longitude)), 城市: \(australiaPlacemark.locality ?? "未知")", type: .location)
                return
            }
        }

        // 最后使用第一个结果，但标记为可能不准确
        if let placemark = placemarks.first, let originalLocation = placemark.location?.coordinate {
            // 🔄 坐标系转换：统一处理所有地址的坐标转换
            let convertedLocation = originalLocation.smartConverted(fromWgs84: true)

            if originalLocation.isInChina() {
                Logger.info("🌍 通用地址坐标转换: \(request.address)", type: .location)
                Logger.info("   WGS-84: (\(originalLocation.latitude), \(originalLocation.longitude))", type: .location)
                Logger.info("   GCJ-02: (\(convertedLocation.latitude), \(convertedLocation.longitude))", type: .location)
            }

            // 根据国家确定状态
            let status: GeocodingStatus
            if let country = countryInfo, country.code == "HK" {
                // 香港地址：如果找到结果就认为成功
                status = .success
            } else {
                // 澳大利亚地址：检查坐标是否在墨尔本附近（使用转换后的坐标）
                let isNearMelbourne = isNearMelbourne(convertedLocation)
                status = isNearMelbourne ? .success : .partialSuccess
            }

            // 创建结果（使用转换后的坐标）
            let result = GeocodingResult(
                address: request.address,
                coordinate: convertedLocation,
                placemark: placemark,
                status: status,
                attemptCount: request.attemptCount + 1
            )

            // 缓存结果并发布
            Task { @MainActor in
                self.geocodingCache[request.address] = result
            }

            // 发布结果
            resultSubject.send(result)

            Logger.info("地理编码成功(通用): \(request.address) -> (\(convertedLocation.latitude), \(convertedLocation.longitude)), 国家: \(placemark.country ?? "未知"), 城市: \(placemark.locality ?? "未知")", type: .location)
            return
        }

        // 不应该到达这里，但以防万一
        let result = GeocodingResult(
            address: request.address,
            status: .notFound,
            attemptCount: request.attemptCount + 1
        )

        // 缓存结果并发布
        Task { @MainActor in
            self.geocodingCache[request.address] = result
        }

        // 发布结果
        resultSubject.send(result)

        Logger.warning("地理编码返回了结果但无法提取坐标: \(request.address)", type: .location)
    }

    // 处理CoreLocation错误
    private func handleCLError(request: GeocodingRequest, error: CLError) async {
        // 确定错误类型
        let status: GeocodingStatus
        var errorMessage = error.localizedDescription

        switch error.code {
        case .network:
            status = .networkError
            errorMessage = "network_error".localized
        case .denied:
            status = .failed
            errorMessage = "location_service_denied".localized
        case .geocodeFoundNoResult:
            status = .notFound
            errorMessage = "cannot_find_address_check_spelling".localized

            // 记录详细日志以便调试
            Logger.error("地理编码未找到结果: \(request.address), 错误代码: \(error.code.rawValue)", type: .location)

            // 尝试提供更多信息
            if let enhancedAddress = request.enhancedAddress {
                Logger.info("尝试使用增强地址: \(enhancedAddress)", type: .location)
            }
        case .geocodeFoundPartialResult:
            status = .partialSuccess
            errorMessage = "地址部分匹配，可能不准确，请检查地址详细信息"
        case .geocodeCanceled:
            status = .failed
            errorMessage = "地理编码请求被取消"
        case .locationUnknown:
            // kCLErrorLocationUnknown (错误8) - 可能是多个匹配结果导致的歧义
            status = .notFound  // 改为未找到，而不是网络错误
            errorMessage = "地址存在多个匹配结果，无法确定准确位置。请提供更详细的地址信息（如城市、州）。"
            Logger.warning("🔍 地理编码歧义 (错误8): \(request.address) - 可能存在多个匹配的地址，Apple Maps无法确定正确的位置", type: .location)
        default:
            status = .failed
            errorMessage = "地理编码失败，错误代码: \(error.code.rawValue)"
            Logger.error("未处理的CoreLocation错误: \(request.address), 错误代码: \(error.code.rawValue), 描述: \(error.localizedDescription)", type: .location)
        }

        // 处理错误
        await handleGeocodingError(request: request, status: status, error: error, customMessage: errorMessage)
    }

    // 处理地理编码错误
    private func handleGeocodingError(request: GeocodingRequest, status: GeocodingStatus, error: Error, customMessage: String? = nil) async {
        // 移除活跃请求
        await MainActor.run { [self] in
            _ = activeRequests.remove(request.id)
        }

        // 记录错误
        Logger.error("地理编码错误: \(request.address), 状态: \(status), 错误: \(error.localizedDescription)", type: .location)

        // 检查是否需要重试
        if status.shouldRetry && request.attemptCount < maxRetryAttempts {
            // 创建新的请求并重新加入队列
            let newRequest = request.withIncrementedAttempt()

            // 🌐 网络错误使用更长的延迟时间
            let baseDelay = status == .networkError ? 3.0 : 1.0
            let delaySeconds = Double(newRequest.attemptCount) * baseDelay

            // 延迟后重新加入队列
            _ = await MainActor.run { [self] in
                // 使用 Task 替代 Timer 以避免 Sendable 问题
                Task { @MainActor [weak self] in
                    try? await Task.sleep(nanoseconds: UInt64(delaySeconds * 1_000_000_000))
                    self?.addToQueue(newRequest)
                }
                return true
            }

            let retryReason = status == .networkError ? "网络问题" : "一般错误"
            Logger.info("🔄 地理编码将在\(delaySeconds)秒后重试(\(retryReason)): \(request.address), 尝试次数: \(newRequest.attemptCount)", type: .location)
            return
        }

        // 创建失败结果，为网络错误提供更友好的消息
        let finalErrorMessage: String
        if status == .networkError {
            finalErrorMessage = "网络连接不稳定，地址验证失败。建议：1) 检查网络连接 2) 稍后重试 3) 或手动更新坐标。当前显示为临时坐标 (0.000000, 0.000000)。"
        } else {
            finalErrorMessage = customMessage ?? error.localizedDescription
        }

        // 记录网络错误的详细信息，便于调试
        if status == .networkError {
            Logger.warning("🌐 网络地理编码最终失败: \(request.address), 尝试次数: \(request.attemptCount + 1), 设备可能网络信号不佳", type: .location)

            // 🎯 建议用户检查地址数据库状态
            Logger.info("💡 建议：如果其他设备有相同地址的坐标，可以通过iCloud同步或手动导入地址数据库", type: .location)
        }

        let result = GeocodingResult(
            address: request.address,
            status: status,
            errorMessage: finalErrorMessage,
            attemptCount: request.attemptCount + 1
        )

        // 缓存结果并发布
        Task { @MainActor in
            self.geocodingCache[request.address] = result
        }

        // 发布结果
        resultSubject.send(result)
    }

    // 缓存结果
    private func cacheResult(_ result: GeocodingResult) {
        // 🛡️ 防护：确保地址是有效的字符串
        guard !result.address.isEmpty else {
            Logger.warning("🛡️ GeocodingService - 跳过缓存空地址", type: .location)
            return
        }

        // 🛡️ 防护：确保地址是字符串类型（防止类型错误）
        let addressKey = String(describing: result.address)
        guard addressKey == result.address else {
            Logger.warning("🛡️ GeocodingService - 地址类型异常，跳过缓存: \(type(of: result.address))", type: .location)
            return
        }

        // 添加到缓存
        geocodingCache[result.address] = result

        // 如果缓存太大，移除一些旧条目
        if geocodingCache.count > maxCacheSize {
            let keysToRemove: [String] = Array(geocodingCache.keys.prefix(maxCacheSize / 5))
            for key in keysToRemove {
                geocodingCache.removeValue(forKey: key)
            }
            let removedCount = keysToRemove.count
            Logger.info("清理地理编码缓存，移除了\(removedCount)个旧条目", type: .location)
        }
    }

    // 简化地址以提高地理编码成功率（移除单元号/公寓号，但保留郊区信息）
    private func simplifyAddressForGeocoding(_ address: String) -> String {
        Logger.info("🔧 开始简化地址: \(address)", type: .location)

        // 解析地址组件
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        guard let firstComponent = components.first else {
            Logger.info("🔧 地址格式无效，返回原地址", type: .location)
            return address
        }

        // 检查第一部分是否包含单元号格式
        let parts = firstComponent.components(separatedBy: " ")
        guard let firstPart = parts.first else {
            Logger.info("🔧 无法解析门牌号，返回原地址", type: .location)
            return address
        }

        // 检查是否为单元号格式 (如 "404/23", "2/21", "113/353", "111/37")
        if firstPart.contains("/") {
            // 提取主要街道号码
            let unitParts = firstPart.components(separatedBy: "/")
            if let mainNumber = unitParts.last?.trimmingCharacters(in: .whitespacesAndNewlines) {
                // 重建地址，使用主要街道号码，但保留所有其他组件
                let streetName = parts.dropFirst().joined(separator: " ")
                let simplifiedFirstComponent = "\(mainNumber) \(streetName)"

                // 重建完整地址，保留郊区、州、邮编、国家信息
                var simplifiedComponents = [simplifiedFirstComponent]
                simplifiedComponents.append(contentsOf: components.dropFirst())
                let simplifiedAddress = simplifiedComponents.joined(separator: ", ")

                Logger.info("🔧 地址简化策略: 保留郊区信息以提高准确性", type: .location)
                Logger.info("🔧   原始: \(address)", type: .location)
                Logger.info("🔧   简化: \(simplifiedAddress)", type: .location)

                return simplifiedAddress
            }
        }

        Logger.info("🔧 无需简化，返回原地址", type: .location)
        return address
    }

    // 增强地址（添加区域信息）- 支持国际地址
    private func enhanceAddress(_ address: String) -> String? {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址增强
            return enhanceHongKongAddress(address)
        }

        // 🇺🇸 美国地址增强
        if let country = countryInfo, country.code == "US" {
            return enhanceUSAddress(address)
        }

        // 澳大利亚地址增强（原有逻辑）
        // 首先检查地址是否基本完整，避免对明显不完整的地址进行增强
        if !isAddressBasicallyComplete(address) {
            Logger.warning("地址过于简短或不完整，不进行增强: \(address)", type: .location)
            return nil
        }

        // 如果地址已经包含足够的信息，不需要增强
        let lowercasedAddress = address.lowercased()

        // 检查是否已包含Glen Waverley
        let hasGlenWaverley = lowercasedAddress.contains("glen waverley")

        // 检查是否包含其他墨尔本郊区
        let hasMelbourneSuburb = hasGlenWaverley ||
                                lowercasedAddress.contains("mount waverley") ||
                                lowercasedAddress.contains("mt waverley") ||
                                lowercasedAddress.contains("clayton") ||
                                lowercasedAddress.contains("oakleigh") ||
                                lowercasedAddress.contains("mulgrave") ||
                                lowercasedAddress.contains("wheelers hill") ||
                                lowercasedAddress.contains("chadstone")

        // 检查是否已包含其他区域信息
        let hasMelbourne = lowercasedAddress.contains("melbourne")
        let hasVIC = lowercasedAddress.contains("vic") || lowercasedAddress.contains("victoria")
        let hasAustralia = lowercasedAddress.contains("australia")
        let hasPostcode = lowercasedAddress.contains("3150") ||
                          lowercasedAddress.contains("3168") ||
                          lowercasedAddress.contains("3166") ||
                          lowercasedAddress.contains("3149")

        // 如果已经包含完整信息，不需要增强
        if (hasMelbourneSuburb || hasMelbourne) && hasVIC && hasAustralia {
            return nil
        }

        // 如果包含Glen Waverley但缺少其他信息
        if hasGlenWaverley && (!hasVIC || !hasAustralia) {
            return "\(address), VIC 3150, Australia"
        }

        // 如果包含墨尔本郊区但缺少其他信息
        if hasMelbourneSuburb && (!hasVIC || !hasAustralia) {
            return "\(address), VIC, Australia"
        }

        // 如果包含VIC但缺少其他信息
        if hasVIC && !hasAustralia {
            return "\(address), Australia"
        }

        // 如果包含邮编但缺少其他信息
        if hasPostcode && !hasVIC && !hasAustralia {
            return "\(address), VIC, Australia"
        }

        // 对于包含完整街道信息的地址，可以添加区域信息
        if hasCompleteStreetAddress(address) {
            if !hasMelbourneSuburb && !hasMelbourne && !hasVIC && !hasAustralia {
                return "\(address), Glen Waverley, VIC 3150, Australia"
            }

            if !hasMelbourne && !hasVIC && !hasAustralia {
                return "\(address), Melbourne, VIC, Australia"
            }
        }

        return nil
    }

    // 增强香港地址
    private func enhanceHongKongAddress(_ address: String) -> String? {
        let lowercasedAddress = address.lowercased()

        // 检查是否已包含香港标识
        let hasHongKong = lowercasedAddress.contains("hong kong") || lowercasedAddress.contains("香港")

        // 如果没有香港标识，添加Hong Kong
        if !hasHongKong {
            return "\(address), Hong Kong"
        }

        // 已经包含香港标识，不需要增强
        return nil
    }

    // 🇺🇸 增强美国地址
    private func enhanceUSAddress(_ address: String) -> String? {
        let lowercasedAddress = address.lowercased()

        // 检查是否已包含美国标识
        let hasUS = lowercasedAddress.contains("usa") ||
                   lowercasedAddress.contains("united states") ||
                   lowercasedAddress.contains("us,") ||
                   lowercasedAddress.hasSuffix("us")

        // 检查是否已包含州信息
        let hasState = lowercasedAddress.contains("ca,") ||
                      lowercasedAddress.contains("california") ||
                      lowercasedAddress.contains("ny,") ||
                      lowercasedAddress.contains("new york") ||
                      lowercasedAddress.contains("tx,") ||
                      lowercasedAddress.contains("texas") ||
                      lowercasedAddress.contains("fl,") ||
                      lowercasedAddress.contains("florida")

        // 检查是否包含邮编
        let hasZipCode = lowercasedAddress.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil

        Logger.info("🇺🇸 美国地址分析: \(address)", type: .location)
        Logger.info("  - 包含美国标识: \(hasUS)", type: .location)
        Logger.info("  - 包含州信息: \(hasState)", type: .location)
        Logger.info("  - 包含邮编: \(hasZipCode)", type: .location)

        // 🎯 特殊处理：Cherry Valley 地址
        if lowercasedAddress.contains("cherry valley") && !hasState {
            Logger.info("🇺🇸 检测到Cherry Valley地址，添加CA州信息", type: .location)
            if hasZipCode {
                return "\(address), CA, USA"
            } else {
                return "\(address), CA, USA, 92223"
            }
        }

        // 如果地址已经很完整，不需要增强
        if hasUS && hasState && hasZipCode {
            Logger.info("🇺🇸 美国地址已完整，无需增强", type: .location)
            return nil
        }

        // 如果缺少美国标识，添加USA
        if !hasUS && hasState {
            Logger.info("🇺🇸 添加USA标识", type: .location)
            return "\(address), USA"
        }

        // 如果是加州地址但缺少完整信息
        if lowercasedAddress.contains("ca") && !hasUS {
            Logger.info("🇺🇸 增强加州地址", type: .location)
            return "\(address), USA"
        }

        // 已经包含足够信息，不需要增强
        Logger.info("🇺🇸 美国地址无需增强", type: .location)
        return nil
    }

    // 简化的地址基本检查 - 只检查最基本的要求
    private func isAddressBasicallyComplete(_ address: String) -> Bool {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 地址不能为空且要有最小长度
        if trimmedAddress.count < 5 {
            return false
        }

        // 检查是否包含数字（门牌号）和字母（街道名）
        let hasNumber = trimmedAddress.rangeOfCharacter(from: .decimalDigits) != nil
        let hasLetters = trimmedAddress.rangeOfCharacter(from: .letters) != nil

        return hasNumber && hasLetters
    }

    // 检查是否包含完整的街道地址信息
    private func hasCompleteStreetAddress(_ address: String) -> Bool {
        let lowercasedAddress = address.lowercased()

        // 检查是否包含常见的街道类型
        let streetTypes = ["street", "road", "avenue", "drive", "court", "place", "lane", "way", "crescent", "grove", "parade"]
        let hasStreetType = streetTypes.contains { lowercasedAddress.contains($0) }

        // 检查是否包含数字（门牌号）
        let hasNumber = address.rangeOfCharacter(from: .decimalDigits) != nil

        return hasStreetType && hasNumber
    }

    // 创建墨尔本区域
    private func createMelbourneRegion() -> CLCircularRegion {
        // 墨尔本中心坐标
        let melbourneCenterCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)

        // 创建区域（半径100公里）
        return CLCircularRegion(
            center: melbourneCenterCoordinate,
            radius: 100000, // 100公里
            identifier: "Melbourne"
        )
    }

    // 检查坐标是否在墨尔本附近
    private func isNearMelbourne(_ coordinate: CLLocationCoordinate2D) -> Bool {
        // 墨尔本大致坐标范围
        let melbourneLatRange = (-38.5, -37.5)
        let melbourneLonRange = (144.5, 145.5)

        // Glen Waverley大致坐标范围（更精确）
        let glenWaverleyLatRange = (-37.92, -37.85)
        let glenWaverleyLonRange = (145.12, 145.20)

        // 检查是否在Glen Waverley范围内（优先）
        let isInGlenWaverley = coordinate.latitude >= glenWaverleyLatRange.0 && coordinate.latitude <= glenWaverleyLatRange.1 &&
                              coordinate.longitude >= glenWaverleyLonRange.0 && coordinate.longitude <= glenWaverleyLonRange.1

        // 检查是否在墨尔本范围内
        let isInMelbourne = coordinate.latitude >= melbourneLatRange.0 && coordinate.latitude <= melbourneLatRange.1 &&
                           coordinate.longitude >= melbourneLonRange.0 && coordinate.longitude <= melbourneLonRange.1

        // 检查是否在澳大利亚范围内
        let australiaLatRange = (-43.**********, -10.**********)
        let australiaLonRange = (113.*********, 153.*********)
        let isInAustralia = coordinate.latitude >= australiaLatRange.0 && coordinate.latitude <= australiaLatRange.1 &&
                           coordinate.longitude >= australiaLonRange.0 && coordinate.longitude <= australiaLonRange.1

        // 如果在Glen Waverley范围内，肯定是正确的
        if isInGlenWaverley {
            return true
        }

        // 如果在墨尔本范围内，也是正确的
        if isInMelbourne {
            return true
        }

        // 如果在澳大利亚范围内，可能是正确的
        if isInAustralia {
            // 使用区域检查作为备用方法
            let melbourneRegion = createMelbourneRegion()
            return melbourneRegion.contains(CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude).coordinate)
        }

        // 不在澳大利亚范围内，肯定不正确
        return false
    }

    // 检查地址是否被自动修正
    private func checkAddressMatch(originalAddress: String, placemark: CLPlacemark, coordinate: CLLocationCoordinate2D) -> AddressMatchResult {
        // 构建从placemark返回的地址
        let correctedAddress = buildAddressFromPlacemark(placemark)

        // 标准化地址以便比较
        let normalizedOriginal = normalizeAddress(originalAddress)
        let normalizedCorrected = normalizeAddress(correctedAddress)

        Logger.info("地址匹配检查: 原地址='\(normalizedOriginal)', 返回地址='\(normalizedCorrected)'", type: .location)

        // 检查关键组件是否匹配
        let streetNumberMatch = checkStreetNumberMatch(normalizedOriginal, normalizedCorrected)
        let streetNameMatch = checkStreetNameMatch(normalizedOriginal, normalizedCorrected)
        let suburbMatch = checkSuburbMatch(normalizedOriginal, normalizedCorrected)

        // 计算总体匹配度
        var confidence = 0.0
        var reasons: [String] = []

        if streetNumberMatch.isMatch {
            confidence += 0.3
        } else {
            reasons.append("门牌号不匹配: \(streetNumberMatch.reason)")
        }

        if streetNameMatch.isMatch {
            confidence += 0.4
        } else {
            reasons.append("街道名不匹配: \(streetNameMatch.reason)")
        }

        if suburbMatch.isMatch {
            confidence += 0.3
        } else {
            reasons.append("区域不匹配: \(suburbMatch.reason)")
        }

        // 设置匹配阈值
        let matchThreshold = 0.7 // 70%匹配度才认为是正确的
        let isMatch = confidence >= matchThreshold

        let reason = isMatch ? "address_match".localized : reasons.joined(separator: "; ")

        // 确定修正类型
        let modificationType: AddressModificationType
        if confidence >= 0.9 {
            modificationType = .none
        } else if confidence >= 0.7 {
            modificationType = .minor
        } else if !streetNameMatch.isMatch {
            modificationType = .streetName
        } else if !suburbMatch.isMatch {
            modificationType = .suburb
        } else {
            modificationType = .major
        }

        return AddressMatchResult(
            isMatch: isMatch,
            correctedAddress: correctedAddress,
            reason: reason,
            confidence: confidence,
            modificationType: modificationType,
            suggestions: isMatch ? [] : ["check_address_spelling".localized, "confirm_address_complete".localized]
        )
    }

    // 从placemark构建地址字符串
    private func buildAddressFromPlacemark(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        if let streetNumber = placemark.subThoroughfare {
            components.append(streetNumber)
        }

        if let streetName = placemark.thoroughfare {
            components.append(streetName)
        }

        if let locality = placemark.locality {
            components.append(locality)
        }

        if let state = placemark.administrativeArea {
            components.append(state)
        }

        if let postalCode = placemark.postalCode {
            components.append(postalCode)
        }

        if let country = placemark.country {
            components.append(country)
        }

        return components.joined(separator: ", ")
    }

    // 标准化地址以便比较
    private func normalizeAddress(_ address: String) -> String {
        return address.lowercased()
            .replacingOccurrences(of: "street", with: "st")
            .replacingOccurrences(of: "road", with: "rd")
            .replacingOccurrences(of: "avenue", with: "ave")
            .replacingOccurrences(of: "drive", with: "dr")
            .replacingOccurrences(of: "court", with: "ct")
            .replacingOccurrences(of: "place", with: "pl")
            .replacingOccurrences(of: "lane", with: "ln")
            .replacingOccurrences(of: "mount", with: "mt")
            .replacingOccurrences(of: "victoria", with: "vic")
            .replacingOccurrences(of: ",", with: " ")
            .replacingOccurrences(of: "  ", with: " ")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 检查门牌号匹配
    private func checkStreetNumberMatch(_ original: String, _ corrected: String) -> (isMatch: Bool, reason: String) {
        // 提取门牌号
        let originalNumber = extractStreetNumber(original)
        let correctedNumber = extractStreetNumber(corrected)

        if originalNumber == nil && correctedNumber == nil {
            return (true, "都没有门牌号")
        }

        if originalNumber == nil || correctedNumber == nil {
            return (false, "门牌号缺失")
        }

        let isMatch = originalNumber == correctedNumber
        let reason = isMatch ? "门牌号匹配" : "原门牌号: \(originalNumber!), 修正门牌号: \(correctedNumber!)"

        return (isMatch, reason)
    }

    // 检查街道名匹配
    private func checkStreetNameMatch(_ original: String, _ corrected: String) -> (isMatch: Bool, reason: String) {
        // 提取街道名
        let originalStreet = extractStreetName(original)
        let correctedStreet = extractStreetName(corrected)

        if originalStreet.isEmpty || correctedStreet.isEmpty {
            return (false, "街道名缺失")
        }

        // 检查是否包含相同的关键词
        let originalWords = Set(originalStreet.components(separatedBy: " ").filter { !$0.isEmpty })
        let correctedWords = Set(correctedStreet.components(separatedBy: " ").filter { !$0.isEmpty })

        // 计算交集
        let commonWords = originalWords.intersection(correctedWords)
        let similarity = Double(commonWords.count) / Double(max(originalWords.count, correctedWords.count))

        let isMatch = similarity >= 0.6 // 60%相似度
        let reason = isMatch ? "街道名匹配" : "原街道: '\(originalStreet)', 修正街道: '\(correctedStreet)'"

        return (isMatch, reason)
    }

    // 检查区域匹配
    private func checkSuburbMatch(_ original: String, _ corrected: String) -> (isMatch: Bool, reason: String) {
        // 检查是否包含相同的区域名
        let originalHasGlenWaverley = original.contains("glen waverley")
        let correctedHasGlenWaverley = corrected.contains("glen waverley")

        if originalHasGlenWaverley && correctedHasGlenWaverley {
            return (true, "都包含Glen Waverley")
        }

        if originalHasGlenWaverley != correctedHasGlenWaverley {
            return (false, "Glen Waverley不匹配")
        }

        // 检查其他区域
        let suburbs = ["mount waverley", "mt waverley", "clayton", "oakleigh", "mulgrave", "wheelers hill", "chadstone"]

        for suburb in suburbs {
            let originalHasSuburb = original.contains(suburb)
            let correctedHasSuburb = corrected.contains(suburb)

            if originalHasSuburb && correctedHasSuburb {
                return (true, "都包含\(suburb)")
            }

            if originalHasSuburb != correctedHasSuburb {
                return (false, "\(suburb)不匹配")
            }
        }

        return (true, "区域匹配")
    }

    // 提取门牌号 - 支持国际地址格式和复合门牌号
    private func extractStreetNumber(_ address: String) -> String? {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址特殊处理
            let hkComponents = AddressCountryDetector.parseHongKongAddress(address)
            return hkComponents.streetNumber.isEmpty ? nil : hkComponents.streetNumber
        }

        // 首先尝试提取复合门牌号的主要部分（如 23/567 -> 567）
        if let mainNumber = extractMainStreetNumberFromAddress(address) {
            return mainNumber
        }

        // 其他国家使用原有逻辑
        let words = address.components(separatedBy: " ").filter { !$0.isEmpty }

        for word in words {
            if let _ = Int(word) {
                return word
            }

            // 检查是否是门牌号+字母的组合（如"123A"）
            let numberPattern = "^\\d+[a-z]?$"
            if word.range(of: numberPattern, options: .regularExpression) != nil {
                return word
            }
        }

        return nil
    }

    /// 从地址中提取复合门牌号的主要部分
    private func extractMainStreetNumberFromAddress(_ address: String) -> String? {
        // 查找复合门牌号模式：数字/数字 或 字母数字/数字 或 数字/数字-数字
        let pattern = "\\b[A-Za-z]*\\d+/([\\d-]+)\\b"

        guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
            return nil
        }

        let range = NSRange(address.startIndex..<address.endIndex, in: address)

        if let match = regex.firstMatch(in: address, options: [], range: range),
           let mainNumberRange = Range(match.range(at: 1), in: address) {
            let mainNumber = String(address[mainNumberRange])

            // 如果是范围格式（如 560-567），返回第一个数字
            if mainNumber.contains("-") {
                let parts = mainNumber.components(separatedBy: "-")
                if let firstPart = parts.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
                    return firstPart
                }
            }

            return mainNumber
        }

        return nil
    }

    // 提取街道名 - 支持国际地址格式
    private func extractStreetName(_ address: String) -> String {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        if let country = countryInfo, country.code == "HK" {
            // 香港地址特殊处理
            let hkComponents = AddressCountryDetector.parseHongKongAddress(address)
            return hkComponents.streetName
        }

        // 其他国家使用原有逻辑
        let words = address.components(separatedBy: " ").filter { !$0.isEmpty }
        var streetWords: [String] = []
        var foundNumber = false

        for word in words {
            // 跳过门牌号
            if !foundNumber && (Int(word) != nil || word.range(of: "^\\d+[a-z]?$", options: .regularExpression) != nil) {
                foundNumber = true
                continue
            }

            // 如果遇到区域名，停止
            if word.contains("glen") || word.contains("waverley") || word.contains("mount") ||
               word.contains("clayton") || word.contains("oakleigh") || word.contains("vic") ||
               word.contains("australia") || word.range(of: "^\\d{4}$", options: .regularExpression) != nil {
                break
            }

            if foundNumber {
                streetWords.append(word)
            }
        }

        return streetWords.joined(separator: " ")
    }

    // 添加超时功能的辅助方法
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        return try await withThrowingTaskGroup(of: T.self) { group in
            // 添加主操作
            group.addTask {
                return try await operation()
            }

            // 添加超时任务
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                throw TimeoutError(seconds: seconds)
            }

            // 返回第一个完成的任务的结果
            let result = try await group.next()!

            // 取消其他任务
            group.cancelAll()

            return result
        }
    }
}
