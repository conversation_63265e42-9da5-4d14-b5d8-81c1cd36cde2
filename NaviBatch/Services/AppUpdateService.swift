import Foundation
import SwiftUI

// MARK: - 版本更新信息模型
struct AppUpdateInfo: Codable {
    let hasUpdate: Bool
    let currentVersion: String
    let latestVersion: String
    let forceUpdate: Bool
    let updateInfo: UpdateDetails
    let debugMode: Bool?

    struct UpdateDetails: Codable {
        let latestVersion: String
        let forceUpdate: Bool
        let updateTitle: String
        let updateSubtitle: String
        let updateNotes: [String]
        let appStoreURL: String
        let releaseDate: String
    }
}

// MARK: - 版本更新服务
@MainActor
class AppUpdateService: ObservableObject {
    static let shared = AppUpdateService()

    @Published var updateInfo: AppUpdateInfo?
    @Published var showUpdatePrompt = false
    @Published var isCheckingUpdate = false

    // 用户选择记录
    @AppStorage("skippedVersion") private var skippedVersion: String = ""
    @AppStorage("lastUpdateCheckDate") private var lastUpdateCheckDate: String = ""
    @AppStorage("updateReminderCount") private var updateReminderCount: Int = 0

    private init() {}

    // MARK: - 版本检查

    /// 检查版本更新（从ConfigService获取）
    func checkForUpdates(from config: AppConfig?) {
        guard let config = config,
              let updateAvailable = config.updateAvailable else {
            logInfo("AppUpdateService - 无版本更新信息")
            return
        }

        logInfo("AppUpdateService - 检测到版本更新信息: \(updateAvailable.latestVersion)")

        // 检查是否应该显示更新提示
        if shouldShowUpdatePrompt(for: updateAvailable) {
            self.updateInfo = updateAvailable
            self.showUpdatePrompt = true
            logInfo("AppUpdateService - 显示版本更新提示")
        }
    }

    /// 手动触发版本检查（用于测试）
    func triggerUpdateCheck(debugMode: Bool = false) {
        isCheckingUpdate = true

        Task {
            // 如果是调试模式，添加调试头部
            if debugMode {
                await ConfigService.shared.refreshConfigWithDebugMode()
            } else {
                ConfigService.shared.refreshConfig()
            }

            // 等待配置更新
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

            await MainActor.run {
                self.isCheckingUpdate = false

                // 检查更新
                self.checkForUpdates(from: ConfigService.shared.config)
            }
        }
    }

    // MARK: - 更新提示逻辑

    /// 判断是否应该显示更新提示
    private func shouldShowUpdatePrompt(for updateInfo: AppUpdateInfo) -> Bool {
        // 调试模式总是显示
        if updateInfo.debugMode == true {
            return true
        }

        // 强制更新总是显示
        if updateInfo.forceUpdate {
            return true
        }

        // 检查是否跳过了这个版本
        if skippedVersion == updateInfo.latestVersion {
            logInfo("AppUpdateService - 用户已跳过版本 \(updateInfo.latestVersion)")
            return false
        }

        // 检查提醒频率（避免过于频繁）
        let today = DateFormatter.yyyyMMdd.string(from: Date())
        if lastUpdateCheckDate == today && updateReminderCount >= 3 {
            logInfo("AppUpdateService - 今日已提醒3次，暂停提醒")
            return false
        }

        return true
    }

    // MARK: - 用户操作

    /// 立即更新
    func updateNow() {
        guard let updateInfo = updateInfo else { return }

        logInfo("AppUpdateService - 用户选择立即更新")

        // 打开App Store
        if let url = URL(string: updateInfo.updateInfo.appStoreURL) {
            UIApplication.shared.open(url)
        }

        // 关闭提示
        dismissUpdatePrompt()
    }

    /// 稍后提醒
    func remindLater() {
        logInfo("AppUpdateService - 用户选择稍后提醒")

        // 增加提醒次数
        let today = DateFormatter.yyyyMMdd.string(from: Date())
        if lastUpdateCheckDate != today {
            updateReminderCount = 1
            lastUpdateCheckDate = today
        } else {
            updateReminderCount += 1
        }

        // 关闭提示
        dismissUpdatePrompt()
    }

    /// 跳过此版本
    func skipThisVersion() {
        guard let updateInfo = updateInfo else { return }

        logInfo("AppUpdateService - 用户选择跳过版本 \(updateInfo.latestVersion)")

        // 记录跳过的版本
        skippedVersion = updateInfo.latestVersion

        // 关闭提示
        dismissUpdatePrompt()
    }

    /// 关闭更新提示
    private func dismissUpdatePrompt() {
        showUpdatePrompt = false
        updateInfo = nil
    }

    // MARK: - 开发者工具

    /// 重置更新状态（用于测试）
    func resetUpdateState() {
        skippedVersion = ""
        lastUpdateCheckDate = ""
        updateReminderCount = 0
        updateInfo = nil
        showUpdatePrompt = false
        logInfo("AppUpdateService - 已重置更新状态")
    }

    /// 模拟版本更新（用于测试）
    func simulateUpdate() {
        let mockUpdateInfo = AppUpdateInfo(
            hasUpdate: true,
            currentVersion: AppEnvironment.appVersion,
            latestVersion: "1.0.4",
            forceUpdate: false,
            updateInfo: AppUpdateInfo.UpdateDetails(
                latestVersion: "1.0.4",
                forceUpdate: false,
                updateTitle: "车道级·真境时代",
                updateSubtitle: "AI 空间建模 刷新导航视界",
                updateNotes: [
                    "🚀 全新版本更新提示功能",
                    "🎯 智能版本检查机制",
                    "✨ 优化用户体验",
                    "🔧 修复已知问题"
                ],
                appStoreURL: "https://apps.apple.com/app/navibatch/id6738046894",
                releaseDate: "2024-12-27"
            ),
            debugMode: true
        )

        self.updateInfo = mockUpdateInfo
        self.showUpdatePrompt = true
        logInfo("AppUpdateService - 模拟版本更新提示")
    }
}

// MARK: - 扩展：ConfigService 支持调试模式
extension ConfigService {
    /// 使用调试模式刷新配置
    func refreshConfigWithDebugMode() async {
        guard !isLoading else { return }

        isLoading = true
        lastError = nil

        do {
            let newConfig = try await fetchConfigWithDebugMode()
            await MainActor.run {
                ConfigService.shared.config = newConfig
                ConfigService.shared.isLoading = false
                ConfigService.shared.cache.saveConfig(newConfig)
                print("[ConfigService] 🧪 调试模式配置已更新")
            }
        } catch {
            await MainActor.run {
                ConfigService.shared.lastError = error
                ConfigService.shared.isLoading = false
                print("[ConfigService] 调试模式配置更新失败: \(error)")
            }
        }
    }

    /// 获取配置（调试模式）
    private func fetchConfigWithDebugMode() async throws -> AppConfig {
        guard let url = URL(string: ConfigService.shared.configURL) else {
            throw ConfigError.invalidURL
        }

        var request = URLRequest(url: url)
        request.setValue(Bundle.main.appVersion, forHTTPHeaderField: "X-App-Version")
        request.setValue("true", forHTTPHeaderField: "X-Debug-Mode") // 调试模式
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        request.timeoutInterval = 30
        request.cachePolicy = NSURLRequest.CachePolicy.reloadIgnoringLocalAndRemoteCacheData

        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        config.waitsForConnectivity = true
        config.allowsCellularAccess = true

        let session = URLSession(configuration: config)
        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ConfigError.invalidResponse
        }

        let decoder = JSONDecoder()
        return try decoder.decode(AppConfig.self, from: data)
    }
}

// MARK: - 日期格式化扩展
extension DateFormatter {
    static let yyyyMMdd: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()
}
