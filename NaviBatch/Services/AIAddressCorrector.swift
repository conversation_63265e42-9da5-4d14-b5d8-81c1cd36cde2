import Foundation
import CoreLocation

/// AI地址修复服务 - 🤖 使用AI智能修复无法验证的地址
actor AI<PERSON>ddressCorrector {
    static let shared = AIAddressCorrector()
    
    private let firebaseAIService = FirebaseAIService.shared
    private let addressVerificationService = AddressVerificationService.shared
    
    // 修复配置
    private let maxRetryAttempts = 2
    private let minConfidenceThreshold = 0.7
    private let autoFixConfidenceThreshold = 0.9 // 高置信度自动修复阈值
    
    private init() {}
    
    // MARK: - 单个地址修复
    
    /// 使用AI修复单个问题地址
    func correctAddress(_ problemAddress: ProblemAddress) async -> AICorrectionResult? {
        print("🤖 AI_CORRECTOR: 开始修复地址: \(problemAddress.originalAddress)")
        print("🤖 AI_CORRECTOR: 失败原因: \(problemAddress.failureReason.localizedDescription)")
        
        do {
            // 构建AI修复提示
            let prompt = buildCorrectionPrompt(for: problemAddress)
            
            // 调用AI服务进行修复
            let aiResponse = try await callAIForCorrection(prompt: prompt, originalAddress: problemAddress.originalAddress)
            
            // 解析AI响应
            if let correctionResult = parseAIResponse(aiResponse, originalAddress: problemAddress.originalAddress) {
                print("🤖 AI_CORRECTOR: AI修复建议: '\(problemAddress.originalAddress)' -> '\(correctionResult.correctedAddress)'")
                print("🤖 AI_CORRECTOR: 修复置信度: \(String(format: "%.1f%%", correctionResult.confidence * 100))")
                
                // 🎯 关键步骤：验证AI修复后的地址
                // 只有经过完整geocoding验证并返回有效坐标的地址才会被标记为修复成功
                // 这确保了只有真正可用的地址才会被保存到用户地址数据库
                let verificationResult = await addressVerificationService.verifyAndSearchAddress(correctionResult.correctedAddress)

                var finalResult = correctionResult
                finalResult.verificationResult = verificationResult

                // 🎯 检查是否为简单格式修复，可以自动应用
                finalResult.isAutoFixable = isSimpleFormatFix(
                    original: problemAddress.originalAddress,
                    corrected: correctionResult.correctedAddress,
                    confidence: correctionResult.confidence
                )

                if verificationResult.isValid {
                    print("🤖 AI_CORRECTOR: ✅ 修复成功，地址经geocoding验证通过: \(correctionResult.correctedAddress)")
                    if finalResult.isAutoFixable {
                        print("🤖 AI_CORRECTOR: 🚀 简单格式修复，可自动应用")
                    }
                } else {
                    print("🤖 AI_CORRECTOR: ❌ 修复后仍无法通过geocoding验证: \(correctionResult.correctedAddress)")
                }

                return finalResult
            } else {
                print("🤖 AI_CORRECTOR: ❌ 无法解析AI响应")
                return nil
            }
            
        } catch {
            print("🤖 AI_CORRECTOR: ❌ AI修复失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - 批量地址修复
    
    /// 批量修复问题地址
    func correctAddresses(_ problemAddresses: [ProblemAddress]) async -> [ProblemAddress] {
        print("🤖 AI_CORRECTOR: 开始批量修复 \(problemAddresses.count) 个问题地址")
        
        var correctedAddresses: [ProblemAddress] = []
        
        for (index, problemAddress) in problemAddresses.enumerated() {
            // 更新进度
            await MainActor.run {
                ProblemAddressCollector.shared.aiCorrectionProgress = Double(index) / Double(problemAddresses.count)
                ProblemAddressCollector.shared.currentProcessingAddress = problemAddress.originalAddress
            }
            
            // 跳过已经修复成功的地址
            if problemAddress.lastAICorrectionResult?.verificationResult?.isValid == true {
                correctedAddresses.append(problemAddress)
                continue
            }
            
            // 检查重试次数限制
            if problemAddress.aiCorrectionAttempts >= maxRetryAttempts {
                print("🤖 AI_CORRECTOR: 跳过地址（已达最大重试次数）: \(problemAddress.originalAddress)")
                correctedAddresses.append(problemAddress)
                continue
            }
            
            // 尝试AI修复
            if let correctionResult = await correctAddress(problemAddress) {
                var updatedAddress = problemAddress
                updatedAddress.aiCorrectionAttempts += 1
                updatedAddress.lastAICorrectionResult = correctionResult
                correctedAddresses.append(updatedAddress)
                
                // 更新统计
                await MainActor.run {
                    ProblemAddressCollector.shared.stats.aiCorrectionAttempts += 1
                    if correctionResult.verificationResult?.isValid == true {
                        ProblemAddressCollector.shared.stats.successfulCorrections += 1
                    }
                }
            } else {
                var updatedAddress = problemAddress
                updatedAddress.aiCorrectionAttempts += 1
                correctedAddresses.append(updatedAddress)
                
                await MainActor.run {
                    ProblemAddressCollector.shared.stats.aiCorrectionAttempts += 1
                }
            }
            
            // 添加延迟避免API频率限制
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        }
        
        await MainActor.run {
            ProblemAddressCollector.shared.aiCorrectionProgress = 1.0
            ProblemAddressCollector.shared.currentProcessingAddress = ""
        }
        
        print("🤖 AI_CORRECTOR: 批量修复完成")
        return correctedAddresses
    }
    
    // MARK: - AI提示构建
    
    private func buildCorrectionPrompt(for problemAddress: ProblemAddress) -> String {
        // 🎯 关键修复：在发送给AI之前清理地址
        let cleanedAddress = cleanAddressForAI(problemAddress.originalAddress)

        let basePrompt = """
        你是一个地址修复专家。请帮我修复这个地址，但只能做最基本的修正。

        原始地址: "\(cleanedAddress)"
        失败原因: \(problemAddress.failureReason.localizedDescription)
        地址来源: \(problemAddress.source.rawValue)

        🚨 严格限制：只能按以下规则修复地址，不得做其他修改：
        1. 只能修正错别字（如0/O, 1/I/l, 8/B, 6/G等OCR常见错误）
        2. 只能调整单元号和街道号码的排列顺序（如"12/1 Main St" -> "1/12 Main St"）
        3. 只能修正街道名称的简称（如"St" -> "Street", "Rd" -> "Road", "Ave" -> "Avenue"）

        ❌ 禁止的操作：
        - 不得添加城市、州、邮编等地区信息
        - 不得删除或替换街道名称
        - 不得修改门牌号码（除非明显是OCR错误）
        - 不得添加任何原地址中没有的信息
        - 不得大幅度重写地址

        """
        
        // 根据失败原因添加特定指导（保守修复）
        let specificGuidance = switch problemAddress.failureReason {
        case .geocodingFailed:
            "只修正明显的错别字，不要添加任何新信息。"
        case .invalidFormat:
            "只调整单元号和街道号码的排列，不要修改其他内容。"
        case .emptyAddress, .tooShort:
            "如果地址过短或为空，返回无法修复，不要猜测或添加信息。"
        case .noValidCoordinate:
            "只修正街道名称的简称，不要修改地理位置信息。"
        }
        
        let responseFormat = """

        请以JSON格式回复：
        {
            "corrected_address": "修复后的地址",
            "confidence": 0.95,
            "correction_reason": "修复原因说明"
        }

        ⚠️ 重要提醒：
        - 只有在确定是错别字、单元号排列或街道简称问题时才修复
        - 如果需要添加城市、州、邮编等信息才能修复，请返回无法修复
        - 如果不确定是否应该修复，请返回无法修复
        - 宁可不修复，也不要过度修改

        如果无法按规则修复，请返回：
        {
            "corrected_address": "",
            "confidence": 0.0,
            "correction_reason": "不符合修复规则，交由司机处理"
        }
        """
        
        return basePrompt + specificGuidance + responseFormat
    }

    // MARK: - 地址清理

    /// 清理地址，移除元数据信息，为AI处理做准备
    private func cleanAddressForAI(_ address: String) -> String {
        // 移除所有管道符号后的内容（元数据）
        let cleanedAddress = address.components(separatedBy: "|").first ?? address

        // 进一步清理
        let trimmed = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        print("🧹 AI_CORRECTOR: 地址清理: '\(address)' -> '\(trimmed)'")
        return trimmed
    }

    // MARK: - AI服务调用
    
    private func callAIForCorrection(prompt: String, originalAddress: String) async throws -> String {
        // 这里可以选择使用不同的AI服务
        // 优先使用Firebase AI，如果失败则尝试其他服务
        
        do {
            // 使用Firebase AI进行地址修复
            let response = try await firebaseAIService.generateText(prompt: prompt)
            return response
        } catch {
            print("🤖 AI_CORRECTOR: Firebase AI修复失败，尝试备用方案: \(error)")
            
            // 这里可以添加其他AI服务作为备用
            // 例如：OpenRouter、本地AI等
            
            throw error
        }
    }
    
    // MARK: - 响应解析
    
    private func parseAIResponse(_ response: String, originalAddress: String) -> AICorrectionResult? {
        // 🎯 关键修复：处理markdown格式的JSON响应
        let jsonString = extractJSONFromMarkdown(response)

        guard let data = jsonString.data(using: .utf8) else {
            print("🤖 AI_CORRECTOR: 无法转换响应为数据")
            return parseSimpleTextResponse(response, originalAddress: originalAddress)
        }

        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let correctedAddress = json["corrected_address"] as? String,
               let confidence = json["confidence"] as? Double,
               let reason = json["correction_reason"] as? String {

                // 验证修复结果
                guard !correctedAddress.isEmpty, confidence > 0 else {
                    print("🤖 AI_CORRECTOR: AI表示无法修复地址: \(reason)")
                    return nil
                }

                return AICorrectionResult(
                    correctedAddress: correctedAddress,
                    confidence: confidence,
                    correctionReason: reason,
                    modelUsed: "Firebase AI",
                    timestamp: Date()
                )
            }
        } catch {
            print("🤖 AI_CORRECTOR: JSON解析失败: \(error)")
        }

        // 如果JSON解析失败，尝试简单的文本解析
        return parseSimpleTextResponse(response, originalAddress: originalAddress)
    }

    /// 从markdown格式的响应中提取JSON内容
    private func extractJSONFromMarkdown(_ response: String) -> String {
        // 如果响应包含```json标记，提取其中的JSON内容
        if response.contains("```json") {
            let components = response.components(separatedBy: "```json")
            if components.count > 1 {
                let jsonPart = components[1].components(separatedBy: "```").first ?? ""
                let trimmed = jsonPart.trimmingCharacters(in: .whitespacesAndNewlines)
                print("🤖 AI_CORRECTOR: 从markdown中提取JSON: \(trimmed)")
                return trimmed
            }
        }

        // 如果响应包含```标记但没有json标识，尝试提取第一个代码块
        if response.contains("```") {
            let components = response.components(separatedBy: "```")
            if components.count > 1 {
                let possibleJSON = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                if possibleJSON.hasPrefix("{") && possibleJSON.hasSuffix("}") {
                    print("🤖 AI_CORRECTOR: 从代码块中提取JSON: \(possibleJSON)")
                    return possibleJSON
                }
            }
        }

        // 如果没有markdown标记，直接返回原响应
        return response
    }
    
    private func parseSimpleTextResponse(_ response: String, originalAddress: String) -> AICorrectionResult? {
        // 简单的文本解析逻辑
        let lines = response.components(separatedBy: .newlines)
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmed.isEmpty && trimmed != originalAddress && trimmed.count > 5 {
                return AICorrectionResult(
                    correctedAddress: trimmed,
                    confidence: 0.8, // 默认置信度
                    correctionReason: "文本解析修复",
                    modelUsed: "Firebase AI (文本模式)",
                    timestamp: Date()
                )
            }
        }
        
        return nil
    }

    // MARK: - 自动修复判断

    /// 判断是否为简单格式修复，可以自动应用而不需要用户确认
    private func isSimpleFormatFix(original: String, corrected: String, confidence: Double) -> Bool {
        // 高置信度是自动修复的前提
        guard confidence >= autoFixConfidenceThreshold else { return false }

        // 检查是否为简单的单元号格式修复
        if isUnitNumberFormatFix(original: original, corrected: corrected) {
            return true
        }

        // 检查是否为简单的标点符号修复
        if isPunctuationFix(original: original, corrected: corrected) {
            return true
        }

        // 检查是否为简单的大小写修复
        if isCaseFormatFix(original: original, corrected: corrected) {
            return true
        }

        // 检查是否为简单的空格修复
        if isSpacingFix(original: original, corrected: corrected) {
            return true
        }

        return false
    }

    /// 检查是否为单元号格式修复（如 "1/12" -> "Unit 1, 12"）
    private func isUnitNumberFormatFix(original: String, corrected: String) -> Bool {
        // 移除空格和标点符号进行比较
        let originalCleaned = original.replacingOccurrences(of: "[^a-zA-Z0-9]", with: "", options: .regularExpression)
        let correctedCleaned = corrected.replacingOccurrences(of: "[^a-zA-Z0-9]", with: "", options: .regularExpression)

        // 如果清理后的内容基本相同，且包含单元号相关词汇，则认为是格式修复
        if originalCleaned.lowercased() == correctedCleaned.lowercased() {
            let unitKeywords = ["unit", "apt", "apartment", "flat", "room", "suite"]
            let correctedLower = corrected.lowercased()
            return unitKeywords.contains { correctedLower.contains($0) }
        }

        return false
    }

    /// 检查是否为标点符号修复
    private func isPunctuationFix(original: String, corrected: String) -> Bool {
        let originalLettersNumbers = original.replacingOccurrences(of: "[^a-zA-Z0-9]", with: "", options: .regularExpression)
        let correctedLettersNumbers = corrected.replacingOccurrences(of: "[^a-zA-Z0-9]", with: "", options: .regularExpression)

        return originalLettersNumbers.lowercased() == correctedLettersNumbers.lowercased()
    }

    /// 检查是否为大小写修复
    private func isCaseFormatFix(original: String, corrected: String) -> Bool {
        return original.lowercased() == corrected.lowercased()
    }

    /// 检查是否为空格修复
    private func isSpacingFix(original: String, corrected: String) -> Bool {
        let originalNoSpaces = original.replacingOccurrences(of: " ", with: "")
        let correctedNoSpaces = corrected.replacingOccurrences(of: " ", with: "")

        return originalNoSpaces.lowercased() == correctedNoSpaces.lowercased()
    }
}
