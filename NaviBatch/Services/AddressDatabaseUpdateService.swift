import Foundation
import CoreLocation
import SwiftData

/// 地址库更新服务
/// 专门处理用户手动更正地址后的数据库更新，避免错误数据持久化
class AddressDatabaseUpdateService {
    static let shared = AddressDatabaseUpdateService()
    
    private init() {}
    
    /// 用户手动更正地址后更新地址库
    /// - Parameters:
    ///   - originalAddress: 原始错误地址
    ///   - correctedAddress: 用户更正后的地址
    ///   - coordinate: 正确的坐标
    func updateAddressAfterUserCorrection(
        originalAddress: String,
        correctedAddress: String,
        coordinate: CLLocationCoordinate2D
    ) async {
        logInfo("🔧 AddressDatabaseUpdateService - 用户更正地址开始")
        logInfo("🔧   原始地址: '\(originalAddress)'")
        logInfo("🔧   更正地址: '\(correctedAddress)'")
        logInfo("🔧   坐标: (\(coordinate.latitude), \(coordinate.longitude))")

        // 1. 清理地址中的元数据（使用内部方法）
        let cleanOriginal = cleanAddressMetadata(originalAddress)
        var cleanCorrected = cleanAddressMetadata(correctedAddress)

        logInfo("🔧   清理后原始地址: '\(cleanOriginal)'")
        logInfo("🔧   清理后更正地址: '\(cleanCorrected)'")

        // 2. 🎯 新增：检测并修复缺少州缩写的地址
        if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: cleanCorrected) {
            logInfo("🔧   检测到缺少州缩写，自动修复: '\(cleanCorrected)' -> '\(fixedAddress)'")
            cleanCorrected = fixedAddress
        }

        // 3. 如果原地址和更正地址不同，需要处理旧记录
        if cleanOriginal != cleanCorrected {
            logInfo("🔧   检测到地址变更，调用handleAddressChange")
            await handleAddressChange(
                from: cleanOriginal,
                to: cleanCorrected,
                coordinate: coordinate
            )
        } else {
            // 4. 地址相同，只是更新坐标精度
            logInfo("🔧   地址未变更，直接保存坐标精度提升")
            await UserAddressDatabase.shared.saveValidatedAddress(
                cleanCorrected,
                coordinate: coordinate,
                source: .manual,
                confidence: 1.0
            )
        }

        logInfo("🔧 AddressDatabaseUpdateService - 用户更正地址完成")
    }
    
    /// 处理地址变更（从错误地址到正确地址）
    private func handleAddressChange(
        from originalAddress: String,
        to correctedAddress: String,
        coordinate: CLLocationCoordinate2D
    ) async {
        logInfo("🔄 AddressDatabaseUpdateService - 处理地址变更: '\(originalAddress)' -> '\(correctedAddress)'")
        
        // 1. 保存正确的地址
        await UserAddressDatabase.shared.saveValidatedAddress(
            correctedAddress,
            coordinate: coordinate,
            source: .manual,
            confidence: 1.0
        )
        
        // 2. 标记或降低原地址的置信度（而不是删除，保留学习价值）
        await markAddressAsProblematic(originalAddress)
    }
    
    /// 标记地址为有问题的（降低置信度）
    private func markAddressAsProblematic(_ address: String) async {
        logInfo("⚠️ AddressDatabaseUpdateService - 标记问题地址: \(address)")

        // 通过降低置信度来标记问题地址，而不是删除
        // 这样可以保留学习数据，避免将来再次犯同样错误
        Task { @MainActor in
            do {
                let modelContext = DatabaseManager.shared.getPersistentContainer().mainContext
                    let descriptor = FetchDescriptor<ValidatedAddress>(
                        predicate: #Predicate<ValidatedAddress> { validatedAddress in
                            validatedAddress.originalAddress == address
                        }
                    )

                    let results = try modelContext.fetch(descriptor)
                    for result in results {
                        // 降低置信度到很低的水平，但不删除
                        result.confidence = 0.1
                        logInfo("🔻 AddressDatabaseUpdateService - 已降低地址置信度: \(address)")
                    }

                    try modelContext.save()
            } catch {
                logError("❌ AddressDatabaseUpdateService - 标记问题地址失败: \(error)")
            }
        }
    }
    
    /// 批量更新地址库（用于AI识别结果的优化）
    func batchUpdateFromAIResults(
        _ addresses: [(original: String, coordinate: CLLocationCoordinate2D)]
    ) async {
        logInfo("🤖 AddressDatabaseUpdateService - 批量更新AI识别结果: \(addresses.count)个地址")
        
        for (address, coordinate) in addresses {
            // 只有坐标有效才保存
            guard coordinate.latitude != 0 || coordinate.longitude != 0 else {
                continue
            }
            
            await UserAddressDatabase.shared.saveValidatedAddress(
                address,
                coordinate: coordinate,
                source: .screenshot, // 使用现有的source类型
                confidence: 0.85 // AI识别的置信度稍低于手动输入
            )
        }
    }
    
    /// 检查地址是否需要重新验证
    func shouldRevalidateAddress(_ address: String) async -> Bool {
        guard let result = await UserAddressDatabase.shared.getValidatedAddress(for: address) else {
            return true // 没有缓存，需要验证
        }
        
        // 如果置信度很低，需要重新验证
        if result.confidence < 0.5 {
            logInfo("🔍 AddressDatabaseUpdateService - 地址置信度低，需要重新验证: \(address)")
            return true
        }
        
        // 如果是很久以前的数据，也需要重新验证
        let daysSinceLastUsed = Calendar.current.dateComponents([.day], from: result.lastUsed, to: Date()).day ?? 0
        if daysSinceLastUsed > 30 {
            logInfo("📅 AddressDatabaseUpdateService - 地址数据过旧，需要重新验证: \(address)")
            return true
        }
        
        return false
    }

    /// 清理地址中的元数据（复制自UserAddressDatabase的私有方法）
    private func cleanAddressMetadata(_ address: String) -> String {
        return address
            .replacingOccurrences(of: #"\|SORT:\d+"#, with: "", options: .regularExpression)
            .replacingOccurrences(of: #"\|TRACK:[^|]+"#, with: "", options: .regularExpression)
            .replacingOccurrences(of: #"\|CUSTOMER:[^|]+"#, with: "", options: .regularExpression)
            .replacingOccurrences(of: #"\|APP:[^|]+"#, with: "", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - 日志辅助函数
private func logInfo(_ message: String) {
    print("[INFO] \(message)")
}

private func logError(_ message: String) {
    print("[ERROR] \(message)")
}
