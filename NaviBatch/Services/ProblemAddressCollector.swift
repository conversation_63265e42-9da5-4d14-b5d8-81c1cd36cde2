import Foundation
import CoreLocation

/// 问题地址数据模型
struct ProblemAddress: Identifiable, Codable {
    var id = UUID()
    let originalAddress: String
    let source: AddressSource
    let failureReason: FailureReason
    let timestamp: Date
    var aiCorrectionAttempts: Int = 0
    var lastAICorrectionResult: AICorrectionResult?
    
    enum FailureReason: String, Codable, CaseIterable {
        case geocodingFailed = "geocoding_failed"
        case invalidFormat = "invalid_format"
        case emptyAddress = "empty_address"
        case tooShort = "too_short"
        case noValidCoordinate = "no_valid_coordinate"
        
        var localizedDescription: String {
            switch self {
            case .geocodingFailed:
                return "geocoding_failed".localized
            case .invalidFormat:
                return "invalid_address_format".localized
            case .emptyAddress:
                return "empty_address".localized
            case .tooShort:
                return "address_too_short".localized
            case .noValidCoordinate:
                return "no_valid_coordinate".localized
            }
        }
    }
}

/// AI修复结果
struct AICorrectionResult {
    let correctedAddress: String
    let confidence: Double
    let correctionReason: String
    let modelUsed: String
    let timestamp: Date
    var verificationResult: AddressVerificationResult?
    var isAutoFixable: Bool = false // 是否为简单格式修复，可自动应用
}

// MARK: - Codable支持
extension AICorrectionResult: Codable {
    enum CodingKeys: String, CodingKey {
        case correctedAddress
        case confidence
        case correctionReason
        case modelUsed
        case timestamp
        case isVerified
        case verifiedCoordinate
        case isAutoFixable
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        correctedAddress = try container.decode(String.self, forKey: .correctedAddress)
        confidence = try container.decode(Double.self, forKey: .confidence)
        correctionReason = try container.decode(String.self, forKey: .correctionReason)
        modelUsed = try container.decode(String.self, forKey: .modelUsed)
        timestamp = try container.decode(Date.self, forKey: .timestamp)

        // 简化的验证结果存储
        let isVerified = try container.decodeIfPresent(Bool.self, forKey: .isVerified) ?? false
        if isVerified,
           let coordinateData = try container.decodeIfPresent(Data.self, forKey: .verifiedCoordinate),
           let coordinate = try? JSONDecoder().decode(CLLocationCoordinate2D.self, from: coordinateData) {
            verificationResult = AddressVerificationResult(
                originalAddress: correctedAddress,
                isValid: true,
                verifiedCoordinate: coordinate,
                alternativeAddresses: []
            )
        } else {
            verificationResult = nil
        }

        // 自动修复标记
        isAutoFixable = try container.decodeIfPresent(Bool.self, forKey: .isAutoFixable) ?? false
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(correctedAddress, forKey: .correctedAddress)
        try container.encode(confidence, forKey: .confidence)
        try container.encode(correctionReason, forKey: .correctionReason)
        try container.encode(modelUsed, forKey: .modelUsed)
        try container.encode(timestamp, forKey: .timestamp)

        // 简化的验证结果存储
        if let result = verificationResult {
            try container.encode(result.isValid, forKey: .isVerified)
            if let coordinate = result.verifiedCoordinate {
                let coordinateData = try JSONEncoder().encode(coordinate)
                try container.encode(coordinateData, forKey: .verifiedCoordinate)
            }
        } else {
            try container.encode(false, forKey: .isVerified)
        }

        // 自动修复标记
        try container.encode(isAutoFixable, forKey: .isAutoFixable)
    }
}

// MARK: - CLLocationCoordinate2D Codable支持
extension CLLocationCoordinate2D: Codable {
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let latitude = try container.decode(Double.self, forKey: .latitude)
        let longitude = try container.decode(Double.self, forKey: .longitude)
        self.init(latitude: latitude, longitude: longitude)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(latitude, forKey: .latitude)
        try container.encode(longitude, forKey: .longitude)
    }

    private enum CodingKeys: String, CodingKey {
        case latitude
        case longitude
    }
}

/// 问题地址收集器 - 🤖 智能地址修复系统的核心组件
@MainActor
class ProblemAddressCollector: ObservableObject {
    static let shared = ProblemAddressCollector()
    
    @Published var problemAddresses: [ProblemAddress] = []
    @Published var isProcessingAICorrections = false
    @Published var aiCorrectionProgress: Double = 0.0
    @Published var currentProcessingAddress: String = ""
    
    // 统计信息
    @Published var stats = CollectionStats()
    
    struct CollectionStats {
        var totalCollected: Int = 0
        var aiCorrectionAttempts: Int = 0
        var successfulCorrections: Int = 0
        var finalUserConfirmations: Int = 0
        
        var aiSuccessRate: Double {
            guard aiCorrectionAttempts > 0 else { return 0.0 }
            return Double(successfulCorrections) / Double(aiCorrectionAttempts)
        }
    }
    
    private init() {}
    
    // MARK: - 问题地址收集
    
    /// 收集无法验证的问题地址
    func collectProblemAddress(
        _ address: String,
        source: AddressSource,
        reason: ProblemAddress.FailureReason
    ) {
        // 避免重复收集相同的地址
        if problemAddresses.contains(where: { $0.originalAddress == address && $0.source == source }) {
            print("🤖 PROBLEM_COLLECTOR: 地址已存在，跳过收集: \(address)")
            return
        }
        
        let problemAddress = ProblemAddress(
            originalAddress: address,
            source: source,
            failureReason: reason,
            timestamp: Date()
        )
        
        problemAddresses.append(problemAddress)
        stats.totalCollected += 1
        
        print("🤖 PROBLEM_COLLECTOR: 收集问题地址: \(address) (来源: \(source.rawValue), 原因: \(reason.localizedDescription))")
        print("🤖 PROBLEM_COLLECTOR: 当前收集总数: \(problemAddresses.count)")
    }
    
    /// 批量收集问题地址
    func collectProblemAddresses(_ addresses: [(String, AddressSource, ProblemAddress.FailureReason)]) {
        for (address, source, reason) in addresses {
            collectProblemAddress(address, source: source, reason: reason)
        }
    }
    
    // MARK: - 状态管理
    
    /// 获取待处理的问题地址数量
    var pendingAddressesCount: Int {
        problemAddresses.filter { $0.lastAICorrectionResult?.verificationResult?.isValid != true }.count
    }
    
    /// 获取AI修复成功的地址数量
    var successfullyFixedCount: Int {
        problemAddresses.filter { $0.lastAICorrectionResult?.verificationResult?.isValid == true }.count
    }
    
    /// 清空所有问题地址
    func clearAllProblemAddresses() {
        problemAddresses.removeAll()
        print("🤖 PROBLEM_COLLECTOR: 已清空所有问题地址")
    }

    // MARK: - 🚨 用户提示功能

    /// 检查并提示用户问题地址（通用方法）
    /// - Parameter source: 地址来源描述，用于自定义提示信息
    func checkAndShowProblemAddresses(source: String = "地址导入") {
        let problemCount = problemAddresses.count

        if problemCount > 0 {
            print("🚨 ProblemAddressCollector - 发现 \(problemCount) 个问题地址，准备提示用户")

            // 创建问题地址列表
            let problemAddressList = problemAddresses
                .map { $0.originalAddress }
                .joined(separator: "\n• ")

            // 显示Alert提示用户
            let alertTitle = "address_problem_found".localized
            let alertMessage = String(format: "address_problem_details".localized, source, problemCount, problemAddressList)

            // 通过通知发送Alert信息
            NotificationCenter.default.post(
                name: Notification.Name("ShowProblemAddressAlert"),
                object: nil,
                userInfo: [
                    "title": alertTitle,
                    "message": alertMessage,
                    "problemCount": problemCount,
                    "source": source
                ]
            )

            print("🚨 已通知用户发现 \(problemCount) 个问题地址（来源：\(source)）")
        } else {
            print("✅ ProblemAddressCollector - 没有发现问题地址（来源：\(source)）")
        }
    }
    
    /// 移除指定的问题地址
    func removeProblemAddress(_ address: ProblemAddress) {
        problemAddresses.removeAll { $0.id == address.id }
        print("🤖 PROBLEM_COLLECTOR: 移除问题地址: \(address.originalAddress)")
    }
    
    /// 获取按来源分组的统计信息
    func getStatsBySource() -> [AddressSource: Int] {
        var stats: [AddressSource: Int] = [:]
        for address in problemAddresses {
            stats[address.source, default: 0] += 1
        }
        return stats
    }
    
    /// 获取按失败原因分组的统计信息
    func getStatsByFailureReason() -> [ProblemAddress.FailureReason: Int] {
        var stats: [ProblemAddress.FailureReason: Int] = [:]
        for address in problemAddresses {
            stats[address.failureReason, default: 0] += 1
        }
        return stats
    }
    
    // MARK: - 调试和日志
    
    /// 打印收集统计信息
    func printCollectionStats() {
        print("🤖 PROBLEM_COLLECTOR: === 收集统计 ===")
        print("🤖 PROBLEM_COLLECTOR: 总收集数: \(stats.totalCollected)")
        print("🤖 PROBLEM_COLLECTOR: 当前待处理: \(pendingAddressesCount)")
        print("🤖 PROBLEM_COLLECTOR: AI修复成功: \(successfullyFixedCount)")
        print("🤖 PROBLEM_COLLECTOR: AI成功率: \(String(format: "%.1f%%", stats.aiSuccessRate * 100))")
        
        let sourceStats = getStatsBySource()
        print("🤖 PROBLEM_COLLECTOR: 按来源分布:")
        for (source, count) in sourceStats {
            print("🤖 PROBLEM_COLLECTOR:   \(source.rawValue): \(count)")
        }
        
        let reasonStats = getStatsByFailureReason()
        print("🤖 PROBLEM_COLLECTOR: 按失败原因分布:")
        for (reason, count) in reasonStats {
            print("🤖 PROBLEM_COLLECTOR:   \(reason.localizedDescription): \(count)")
        }
    }
}
