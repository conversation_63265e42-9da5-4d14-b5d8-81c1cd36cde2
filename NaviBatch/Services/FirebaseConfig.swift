//
//  FirebaseConfig.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//

import Foundation
// Firebase Core import
import FirebaseCore

class FirebaseConfig {
    static let shared = FirebaseConfig()
    
    private init() {}
    
    func configure() {
        // 配置Firebase
        FirebaseApp.configure()

        Logger.aiInfo("🔥 Firebase已初始化")
    }
    
    func isFirebaseConfigured() -> Bool {
        return FirebaseApp.app() != nil
    }
}
