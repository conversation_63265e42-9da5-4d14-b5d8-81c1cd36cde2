import Foundation
import Network

/// 地理位置诊断服务
/// 用于检测AI服务的地理可用性
class GeographicDiagnosticService {
    static let shared = GeographicDiagnosticService()
    
    private init() {}
    
    // MARK: - 诊断结果
    struct DiagnosticResult {
        let location: String
        let gemmaAvailable: Bool
        let openRouterAvailable: Bool
        let recommendedAction: String
        let alternativeModels: [String]
    }
    
    // MARK: - 地理位置诊断
    func diagnoseGeographicAvailability() async -> DiagnosticResult {
        Logger.aiInfo("🌍 开始地理位置诊断...")
        
        // 检测用户位置（基于网络）
        let location = await detectUserLocation()
        
        // 测试OpenRouter连接
        let openRouterAvailable = await testOpenRouterConnection()
        
        // 测试Gemma模型可用性
        let gemmaAvailable = await testGemmaAvailability()
        
        // 生成建议
        let recommendation = generateRecommendation(
            location: location,
            gemmaAvailable: gemmaAvailable,
            openRouterAvailable: openRouterAvailable
        )
        
        let alternatives = getAlternativeModels(for: location)
        
        return DiagnosticResult(
            location: location,
            gemmaAvailable: gemmaAvailable,
            openRouterAvailable: openRouterAvailable,
            recommendedAction: recommendation,
            alternativeModels: alternatives
        )
    }
    
    // MARK: - 位置检测
    private func detectUserLocation() async -> String {
        // 简单的地理位置检测（基于时区和语言）
        let timeZone = TimeZone.current.identifier
        let locale = Locale.current.identifier
        
        Logger.aiInfo("🌍 检测到时区: \(timeZone), 语言: \(locale)")
        
        if timeZone.contains("Hong_Kong") || locale.contains("HK") {
            return "香港"
        } else if timeZone.contains("Shanghai") || timeZone.contains("Beijing") || locale.contains("CN") {
            return "中国大陆"
        } else if timeZone.contains("Taipei") || locale.contains("TW") {
            return "台湾"
        } else if timeZone.contains("Singapore") || locale.contains("SG") {
            return "新加坡"
        } else {
            return "其他地区"
        }
    }
    
    // MARK: - 连接测试
    private func testOpenRouterConnection() async -> Bool {
        do {
            let url = URL(string: "https://openrouter.ai/api/v1/models")!
            let (_, response) = try await URLSession.shared.data(from: url)
            
            if let httpResponse = response as? HTTPURLResponse {
                let available = httpResponse.statusCode == 200
                Logger.aiInfo("🔗 OpenRouter连接测试: \(available ? "成功" : "失败(\(httpResponse.statusCode))")")
                return available
            }
        } catch {
            Logger.aiError("🔗 OpenRouter连接测试失败: \(error)")
        }
        return false
    }
    
    private func testGemmaAvailability() async -> Bool {
        // 发送一个最小的测试请求到Gemma模型
        do {
            let apiKey = await ConfigService.shared.apiKey
            let url = URL(string: "https://openrouter.ai/api/v1/chat/completions")!
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let testBody: [String: Any] = [
                "model": "google/gemma-3-27b-it:free",
                "messages": [
                    ["role": "user", "content": "test"]
                ],
                "max_tokens": 1
            ]
            
            request.httpBody = try JSONSerialization.data(withJSONObject: testBody)
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                let available = httpResponse.statusCode != 403 && httpResponse.statusCode != 451
                Logger.aiInfo("🤖 Gemma可用性测试: \(available ? "可用" : "不可用(\(httpResponse.statusCode))")")
                return available
            }
        } catch {
            Logger.aiError("🤖 Gemma可用性测试失败: \(error)")
        }
        return false
    }
    
    // MARK: - 建议生成
    private func generateRecommendation(location: String, gemmaAvailable: Bool, openRouterAvailable: Bool) -> String {
        if !openRouterAvailable {
            return "网络连接问题，请检查网络设置"
        }
        
        if !gemmaAvailable {
            switch location {
            case "香港":
                return "Gemma模型在香港可能有限制，建议使用OCR模式或其他AI模型"
            case "中国大陆":
                return "Google AI服务在中国大陆不可用，建议使用OCR模式"
            default:
                return "Gemma模型在当前地区不可用，建议使用OCR模式"
            }
        }
        
        return "所有服务正常，可以使用AI模式"
    }
    
    private func getAlternativeModels(for location: String) -> [String] {
        switch location {
        case "香港", "台湾", "新加坡":
            return ["Claude (Anthropic)", "GPT-4 (OpenAI)", "OCR模式"]
        case "中国大陆":
            return ["OCR模式", "本地AI模型"]
        default:
            return ["Claude (Anthropic)", "GPT-4 (OpenAI)", "OCR模式"]
        }
    }
}

// MARK: - 诊断视图
import SwiftUI

struct GeographicDiagnosticView: View {
    @State private var diagnosticResult: GeographicDiagnosticService.DiagnosticResult?
    @State private var isLoading = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("地理位置诊断")
                .font(.title2)
                .fontWeight(.bold)
            
            if isLoading {
                ProgressView("正在诊断...")
                    .padding()
            } else if let result = diagnosticResult {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("📍 检测位置:")
                        Text(result.location)
                            .fontWeight(.medium)
                    }
                    
                    HStack {
                        Text("🔗 OpenRouter:")
                        Text(result.openRouterAvailable ? "✅ 可用" : "❌ 不可用")
                    }
                    
                    HStack {
                        Text("🤖 Gemma模型:")
                        Text(result.gemmaAvailable ? "✅ 可用" : "❌ 不可用")
                    }
                    
                    Divider()
                    
                    Text("💡 建议:")
                        .fontWeight(.medium)
                    Text(result.recommendedAction)
                        .foregroundColor(.secondary)
                    
                    if !result.alternativeModels.isEmpty {
                        Text("🔄 替代方案:")
                            .fontWeight(.medium)
                        ForEach(result.alternativeModels, id: \.self) { model in
                            Text("• \(model)")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            
            Button("开始诊断") {
                runDiagnostic()
            }
            .buttonStyle(.borderedProminent)
            .disabled(isLoading)
        }
        .padding()
    }
    
    private func runDiagnostic() {
        isLoading = true
        Task {
            let result = await GeographicDiagnosticService.shared.diagnoseGeographicAvailability()
            await MainActor.run {
                diagnosticResult = result
                isLoading = false
            }
        }
    }
}

#Preview("GeographicDiagnosticService") {
    GeographicDiagnosticView()
}
