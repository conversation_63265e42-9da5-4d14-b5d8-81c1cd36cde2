import Foundation
import CoreLocation

/// 加拿大地址处理器
class CanadianAddressProcessor: AddressProcessorProtocol {

    let countryCode = "CA"
    let countryName = "Canada"

    private let canadianProvinces = [
        "ON", "BC", "AB", "SK", "MB", "QC", "NB", "NS", "PE", "NL", "YT", "NT", "NU"
    ]

    private let canadianCities = [
        "toronto", "vancouver", "montreal", "calgary", "ottawa", "edmonton",
        "winnipeg", "quebec", "hamilton", "kitchener", "london", "victoria",
        "halifax", "oshawa", "windsor", "saskatoon", "st catharines",
        "regina", "sherbrooke", "kelowna", "barrie", "guelph", "kanata"
    ]

    func detectCountry(from address: String) -> Double {
        let lowercaseAddress = address.lowercased()
        var confidence: Double = 0.0

        // 检查加拿大省份缩写
        for province in canadianProvinces {
            if lowercaseAddress.range(of: "\\b\(province.lowercased())\\b", options: .regularExpression) != nil {
                confidence += 0.8
                break
            }
        }

        // 检查加拿大邮编格式 (A1A 1A1)
        if lowercaseAddress.range(of: "\\b[a-z]\\d[a-z]\\s*\\d[a-z]\\d\\b", options: .regularExpression) != nil {
            confidence += 0.9
        }

        // 检查加拿大城市
        for city in canadianCities {
            if lowercaseAddress.contains(city) {
                confidence += 0.6
                break
            }
        }

        // 检查"Canada"
        if lowercaseAddress.contains("canada") {
            confidence += 0.9
        }

        return min(confidence, 1.0)
    }

    func standardizeAddress(_ address: String) -> String {
        var standardized = address

        // 加拿大街道类型标准化
        let canadianStreetTypes = [
            "STREET": "ST", "AVENUE": "AVE", "ROAD": "RD", "DRIVE": "DR",
            "COURT": "CT", "PLACE": "PL", "LANE": "LN", "CRESCENT": "CRES",
            "TRAIL": "TRL", "BOULEVARD": "BLVD"
        ]

        for (fullForm, abbreviation) in canadianStreetTypes {
            let pattern = "\\b\(fullForm)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        return standardized.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    func validateAddressFormat(_ address: String) -> CountryAddressValidationResult {
        var issues: [String] = []
        var suggestions: [String] = []

        let hasProvince = canadianProvinces.contains { province in
            address.range(of: "\\b\(province)\\b", options: [.regularExpression, .caseInsensitive]) != nil
        }

        if !hasProvince {
            issues.append("缺少省份缩写")
            suggestions.append("请添加省份缩写（如ON, BC, AB等）")
        }

        let hasPostalCode = address.range(of: "\\b[a-z]\\d[a-z]\\s*\\d[a-z]\\d\\b", options: [.regularExpression, .caseInsensitive]) != nil
        if !hasPostalCode {
            issues.append("缺少邮编")
            suggestions.append("请添加加拿大邮编格式（如A1A 1A1）")
        }

        return CountryAddressValidationResult(
            isValid: issues.isEmpty,
            confidence: issues.isEmpty ? 1.0 : 0.5,
            issues: issues,
            suggestions: suggestions
        )
    }

    func extractAddressComponents(_ address: String) -> CountryAddressComponents {
        // 简化实现
        return CountryAddressComponents(
            streetNumber: nil, streetName: nil, unitNumber: nil,
            suburb: nil, city: nil, state: nil, postalCode: nil,
            country: countryName
        )
    }

    func formatForGeocoding(_ address: String) -> String {
        return standardizeAddress(address)
    }
}

/// 英国地址处理器
class UKAddressProcessor: AddressProcessorProtocol {

    let countryCode = "GB"
    let countryName = "United Kingdom"

    private let ukCities = [
        "london", "manchester", "birmingham", "glasgow", "liverpool",
        "leeds", "sheffield", "edinburgh", "bristol", "cardiff",
        "belfast", "newcastle", "nottingham", "plymouth", "stoke",
        "wolverhampton", "derby", "swansea", "southampton", "salford"
    ]

    func detectCountry(from address: String) -> Double {
        let lowercaseAddress = address.lowercased()
        var confidence: Double = 0.0

        // 检查英国邮编格式
        if lowercaseAddress.range(of: "[a-z]{1,2}\\d[a-z\\d]?\\s*\\d[a-z]{2}", options: .regularExpression) != nil {
            confidence += 0.9
        }

        // 检查英国城市
        for city in ukCities {
            if lowercaseAddress.contains(city) {
                confidence += 0.7
                break
            }
        }

        // 检查"United Kingdom", "UK", "England", "Scotland", "Wales"
        let ukIdentifiers = ["united kingdom", "uk", "england", "scotland", "wales", "northern ireland"]
        for identifier in ukIdentifiers {
            if lowercaseAddress.contains(identifier) {
                confidence += 0.9
                break
            }
        }

        return min(confidence, 1.0)
    }

    func standardizeAddress(_ address: String) -> String {
        var standardized = address

        // 英国街道类型标准化
        let ukStreetTypes = [
            "STREET": "ST", "ROAD": "RD", "AVENUE": "AVE", "LANE": "LN",
            "CLOSE": "CL", "DRIVE": "DR", "CRESCENT": "CRES", "GARDENS": "GDNS",
            "PLACE": "PL", "SQUARE": "SQ", "TERRACE": "TCE"
        ]

        for (fullForm, abbreviation) in ukStreetTypes {
            let pattern = "\\b\(fullForm)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        return standardized.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    func validateAddressFormat(_ address: String) -> CountryAddressValidationResult {
        var issues: [String] = []
        var suggestions: [String] = []

        let hasPostcode = address.range(of: "[a-z]{1,2}\\d[a-z\\d]?\\s*\\d[a-z]{2}", options: [.regularExpression, .caseInsensitive]) != nil
        if !hasPostcode {
            issues.append("缺少邮编")
            suggestions.append("请添加英国邮编格式（如SW1A 1AA）")
        }

        return CountryAddressValidationResult(
            isValid: issues.isEmpty,
            confidence: issues.isEmpty ? 1.0 : 0.5,
            issues: issues,
            suggestions: suggestions
        )
    }

    func extractAddressComponents(_ address: String) -> CountryAddressComponents {
        // 简化实现
        return CountryAddressComponents(
            streetNumber: nil, streetName: nil, unitNumber: nil,
            suburb: nil, city: nil, state: nil, postalCode: nil,
            country: countryName
        )
    }

    func formatForGeocoding(_ address: String) -> String {
        return standardizeAddress(address)
    }
}
