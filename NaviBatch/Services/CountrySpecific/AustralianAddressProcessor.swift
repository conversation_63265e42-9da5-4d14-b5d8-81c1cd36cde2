import Foundation
import CoreLocation

/// 澳大利亚地址处理器
/// 专门处理澳大利亚地址的检测、标准化和验证
class AustralianAddressProcessor: AddressProcessorProtocol {

    let countryCode = "AU"
    let countryName = "Australia"

    // MARK: - 澳大利亚州和领地缩写
    private let australianStates = [
        "VIC", "NSW", "QLD", "WA", "SA", "TAS", "ACT", "NT"
    ]

    // MARK: - 澳大利亚主要城市
    private let australianCities = [
        "melbourne", "sydney", "brisbane", "perth", "adelaide", "darwin",
        "hobart", "canberra", "gold coast", "newcastle", "wollongong",
        "geelong", "townsville", "cairns", "toowoomba", "ballarat",
        "bendigo", "albury", "launceston", "mackay", "rockhampton",
        "bunbury", "bundaberg", "coffs harbour", "wagga wagga",
        "hervey bay", "mildura", "shepparton", "port macquarie",
        "gladstone", "tamworth", "traralgon", "orange", "dubbo",
        "geraldton", "bowral", "bathurst", "nowra", "warrnambool",
        "kalgoorlie", "devonport", "ballina", "alice springs",
        // 墨尔本郊区
        "glen waverley", "clayton", "oakleigh", "box hill", "richmond",
        "st kilda", "south yarra", "toorak", "brighton", "camberwell",
        "hawthorn", "kew", "malvern", "prahran", "windsor", "fitzroy",
        "collingwood", "carlton", "brunswick", "northcote", "thornbury",
        "preston", "reservoir", "coburg", "essendon", "moonee ponds",
        "footscray", "williamstown", "altona", "werribee", "hoppers crossing",
        "point cook", "tarneit", "truganina", "caroline springs",
        "sunbury", "craigieburn", "broadmeadows", "tullamarine",
        "airport west", "keilor", "deer park", "st albans", "sunshine",
        "newport", "yarraville", "seddon", "kingsville", "west footscray",
        // 悉尼郊区
        "parramatta", "blacktown", "penrith", "liverpool", "campbelltown",
        "bankstown", "fairfield", "hornsby", "ryde", "ku-ring-gai",
        "warringah", "manly", "mosman", "north sydney", "willoughby",
        "lane cove", "hunters hill", "woollahra", "waverley", "randwick",
        "botany bay", "rockdale", "kogarah", "hurstville", "sutherland",
        "cronulla", "miranda", "caringbah", "menai", "engadine"
    ]

    // MARK: - 澳大利亚街道类型缩写
    private let australianStreetTypes = [
        "STREET": "ST", "ROAD": "RD", "AVENUE": "AVE", "DRIVE": "DR",
        "COURT": "CT", "PLACE": "PL", "LANE": "LN", "CLOSE": "CL",
        "CRESCENT": "CRES", "GROVE": "GR", "PARADE": "PDE",
        "TERRACE": "TCE", "HIGHWAY": "HWY", "CIRCUIT": "CCT",
        "BOULEVARD": "BLVD", "ESPLANADE": "ESP", "PROMENADE": "PROM"
    ]

    // MARK: - AddressProcessorProtocol Implementation

    func detectCountry(from address: String) -> Double {
        let lowercaseAddress = address.lowercased()
        var confidence: Double = 0.0

        // 1. 检查澳大利亚州缩写 (高权重)
        for state in australianStates {
            if lowercaseAddress.range(of: "\\b\(state.lowercased())\\b", options: .regularExpression) != nil {
                confidence += 0.8
                break
            }
        }

        // 2. 检查澳大利亚邮编格式 (中等权重)
        if lowercaseAddress.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil {
            confidence += 0.4 // 较低权重，因为4位数字很常见
        }

        // 3. 检查澳大利亚城市 (高权重)
        for city in australianCities {
            if lowercaseAddress.contains(city) {
                confidence += 0.7
                break
            }
        }

        // 4. 检查"Australia"或"AU" (高权重)
        if lowercaseAddress.contains("australia") || lowercaseAddress.hasSuffix(" au") {
            confidence += 0.9
        }

        // 5. 特殊检查：如果同时包含4位邮编和澳洲州，高度确信
        let hasAusState = australianStates.contains { state in
            lowercaseAddress.range(of: "\\b\(state.lowercased())\\b", options: .regularExpression) != nil
        }
        let has4DigitPostcode = lowercaseAddress.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil

        if hasAusState && has4DigitPostcode {
            confidence = max(confidence, 0.9)
        }

        return min(confidence, 1.0)
    }

    func standardizeAddress(_ address: String) -> String {
        var standardized = address

        // 应用澳大利亚标准缩写
        for (fullForm, abbreviation) in australianStreetTypes {
            let pattern = "\\b\(fullForm)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 标准化州缩写为大写
        for state in australianStates {
            let pattern = "\\b\(state.lowercased())\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: state,
                options: .regularExpression
            )
        }

        return standardized.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    func validateAddressFormat(_ address: String) -> CountryAddressValidationResult {
        var issues: [String] = []
        var suggestions: [String] = []
        var confidence: Double = 1.0

        // 检查是否包含州缩写
        let hasState = australianStates.contains { state in
            address.range(of: "\\b\(state)\\b", options: [.regularExpression, .caseInsensitive]) != nil
        }

        if !hasState {
            issues.append("缺少州缩写")
            suggestions.append("请添加州缩写（如VIC, NSW, QLD等）")
            confidence -= 0.3
        }

        // 检查邮编格式
        let hasPostcode = address.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil
        if !hasPostcode {
            issues.append("缺少邮编")
            suggestions.append("请添加4位数邮编")
            confidence -= 0.2
        }

        // 检查街道号码
        let hasStreetNumber = address.range(of: "^\\d+", options: .regularExpression) != nil
        if !hasStreetNumber {
            issues.append("缺少街道号码")
            suggestions.append("请在地址开头添加街道号码")
            confidence -= 0.2
        }

        return CountryAddressValidationResult(
            isValid: issues.isEmpty,
            confidence: max(confidence, 0.0),
            issues: issues,
            suggestions: suggestions
        )
    }

    func extractAddressComponents(_ address: String) -> CountryAddressComponents {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        var streetNumber: String?
        var streetName: String?
        var unitNumber: String?
        var suburb: String?
        var state: String?
        var postalCode: String?

        // 解析第一部分（街道地址）
        if let firstComponent = components.first {
            let streetParts = firstComponent.components(separatedBy: " ")

            // 提取街道号码
            if let firstPart = streetParts.first, firstPart.range(of: "^\\d+", options: .regularExpression) != nil {
                streetNumber = firstPart
                streetName = streetParts.dropFirst().joined(separator: " ")
            }

            // 检查单元号（澳洲格式：Unit 5, 1/23等）
            let unitPatterns = ["unit", "apt", "apartment", "suite", "\\d+/\\d+"]
            for pattern in unitPatterns {
                if let range = firstComponent.range(of: "\\b\(pattern)\\s*\\w+", options: [.regularExpression, .caseInsensitive]) {
                    unitNumber = String(firstComponent[range])
                    break
                }
            }
        }

        // 解析郊区
        if components.count > 1 {
            suburb = components[1]
        }

        // 解析州和邮编
        if components.count > 2 {
            let lastComponent = components[2]

            // 提取州
            for stateCode in australianStates {
                if lastComponent.range(of: "\\b\(stateCode)\\b", options: [.regularExpression, .caseInsensitive]) != nil {
                    state = stateCode
                    break
                }
            }

            // 提取邮编
            if let postcodeRange = lastComponent.range(of: "\\b\\d{4}\\b", options: .regularExpression) {
                postalCode = String(lastComponent[postcodeRange])
            }
        }

        return CountryAddressComponents(
            streetNumber: streetNumber,
            streetName: streetName,
            unitNumber: unitNumber,
            suburb: suburb,
            city: nil, // 澳洲通常使用suburb而不是city
            state: state,
            postalCode: postalCode,
            country: countryName
        )
    }

    func formatForGeocoding(_ address: String) -> String {
        // 对于澳洲地址，保持标准缩写通常效果更好
        return standardizeAddress(address)
    }
}
