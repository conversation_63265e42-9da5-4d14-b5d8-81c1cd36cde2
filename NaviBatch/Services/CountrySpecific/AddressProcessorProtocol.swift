import Foundation
import CoreLocation

/// 国家特定地址处理器协议
/// 为不同国家提供统一的地址处理接口
protocol AddressProcessorProtocol {

    /// 国家代码
    var countryCode: String { get }

    /// 国家名称
    var countryName: String { get }

    /// 检测地址是否属于该国家
    /// - Parameter address: 地址字符串
    /// - Returns: 检测置信度 (0.0-1.0)，0表示不匹配，1表示完全匹配
    func detectCountry(from address: String) -> Double

    /// 标准化地址格式
    /// - Parameter address: 原始地址
    /// - Returns: 标准化后的地址
    func standardizeAddress(_ address: String) -> String

    /// 验证地址格式
    /// - Parameter address: 地址字符串
    /// - Returns: 验证结果
    func validateAddressFormat(_ address: String) -> CountryAddressValidationResult

    /// 提取地址组件
    /// - Parameter address: 地址字符串
    /// - Returns: 结构化地址组件
    func extractAddressComponents(_ address: String) -> CountryAddressComponents

    /// 格式化用于地理编码
    /// - Parameter address: 原始地址
    /// - Returns: 优化后的地址
    func formatForGeocoding(_ address: String) -> String
}

/// 国家特定地址验证结果
struct CountryAddressValidationResult {
    let isValid: Bool
    let confidence: Double
    let issues: [String]
    let suggestions: [String]
}

/// 国家特定地址组件
struct CountryAddressComponents {
    let streetNumber: String?
    let streetName: String?
    let unitNumber: String?
    let suburb: String?
    let city: String?
    let state: String?
    let postalCode: String?
    let country: String?
}

/// 地址处理器工厂
class AddressProcessorFactory {

    private static let processors: [AddressProcessorProtocol] = [
        USAddressProcessor(),
        AustralianAddressProcessor(),
        CanadianAddressProcessor(),
        UKAddressProcessor()
    ]

    /// 获取最适合的地址处理器
    /// - Parameter address: 地址字符串
    /// - Returns: 最匹配的处理器和置信度
    static func getProcessor(for address: String) -> (processor: AddressProcessorProtocol?, confidence: Double) {
        var bestProcessor: AddressProcessorProtocol?
        var bestConfidence: Double = 0.0

        for processor in processors {
            let confidence = processor.detectCountry(from: address)
            if confidence > bestConfidence {
                bestConfidence = confidence
                bestProcessor = processor
            }
        }

        return (bestProcessor, bestConfidence)
    }

    /// 获取指定国家的处理器
    /// - Parameter countryCode: 国家代码
    /// - Returns: 对应的处理器
    static func getProcessor(for countryCode: String) -> AddressProcessorProtocol? {
        return processors.first { $0.countryCode.lowercased() == countryCode.lowercased() }
    }

    /// 获取所有支持的国家
    /// - Returns: 支持的国家列表
    static func getSupportedCountries() -> [String] {
        return processors.map { $0.countryCode }
    }
}

/// 统一地址处理服务
class UnifiedAddressProcessingService {

    /// 智能处理地址
    /// - Parameter address: 原始地址
    /// - Returns: 处理结果
    static func processAddress(_ address: String) -> AddressProcessingResult {
        let (processor, confidence) = AddressProcessorFactory.getProcessor(for: address)

        guard let processor = processor, confidence > 0.5 else {
            return AddressProcessingResult(
                originalAddress: address,
                standardizedAddress: address,
                countryCode: nil,
                confidence: 0.0,
                components: CountryAddressComponents(
                    streetNumber: nil, streetName: nil, unitNumber: nil,
                    suburb: nil, city: nil, state: nil, postalCode: nil, country: nil
                ),
                validationResult: CountryAddressValidationResult(
                    isValid: false,
                    confidence: 0.0,
                    issues: ["无法识别地址所属国家"],
                    suggestions: ["请检查地址格式"]
                )
            )
        }

        let standardized = processor.standardizeAddress(address)
        let components = processor.extractAddressComponents(standardized)
        let validation = processor.validateAddressFormat(standardized)

        return AddressProcessingResult(
            originalAddress: address,
            standardizedAddress: standardized,
            countryCode: processor.countryCode,
            confidence: confidence,
            components: components,
            validationResult: validation
        )
    }
}

/// 地址处理结果
struct AddressProcessingResult {
    let originalAddress: String
    let standardizedAddress: String
    let countryCode: String?
    let confidence: Double
    let components: CountryAddressComponents
    let validationResult: CountryAddressValidationResult
}
