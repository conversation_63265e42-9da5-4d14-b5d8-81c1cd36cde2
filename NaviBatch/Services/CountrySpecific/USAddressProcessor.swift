import Foundation
import CoreLocation

/// 美国地址处理器
/// 专门处理美国地址的检测、标准化和验证
class USAddressProcessor: AddressProcessorProtocol {

    let countryCode = "US"
    let countryName = "United States"

    // MARK: - 美国州缩写
    private let usStates = [
        "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
        "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
        "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
        "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
        "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
    ]

    // MARK: - 美国城市
    private let usCities = [
        "new york", "los angeles", "chicago", "houston", "phoenix", "philadelphia",
        "san antonio", "san diego", "dallas", "san jose", "austin", "jacksonville",
        "fort worth", "columbus", "charlotte", "san francisco", "indianapolis",
        "seattle", "denver", "washington", "boston", "el paso", "detroit",
        "nashville", "portland", "memphis", "oklahoma city", "las vegas",
        "louisville", "baltimore", "milwaukee", "albuquerque", "tucson",
        "fresno", "mesa", "sacramento", "atlanta", "kansas city", "colorado springs",
        "miami", "raleigh", "omaha", "long beach", "virginia beach", "oakland",
        "minneapolis", "tulsa", "arlington", "tampa", "new orleans", "wichita",
        "cleveland", "bakersfield", "aurora", "anaheim", "honolulu", "santa ana",
        "corpus christi", "riverside", "lexington", "stockton", "toledo",
        "saint paul", "newark", "greensboro", "plano", "henderson", "lincoln",
        "buffalo", "jersey city", "chula vista", "fort wayne", "orlando",
        "st petersburg", "chandler", "laredo", "norfolk", "durham", "madison",
        "lubbock", "irvine", "winston salem", "glendale", "garland", "hialeah",
        "reno", "chesapeake", "gilbert", "baton rouge", "irving", "scottsdale",
        "north las vegas", "fremont", "boise", "richmond", "san bernardino",
        "birmingham", "spokane", "rochester", "des moines", "modesto", "fayetteville",
        "tacoma", "oxnard", "fontana", "columbus", "montgomery", "moreno valley",
        "shreveport", "aurora", "yonkers", "akron", "huntington beach", "little rock",
        "augusta", "amarillo", "glendale", "mobile", "grand rapids", "salt lake city",
        "tallahassee", "huntsville", "grand prairie", "knoxville", "worcester",
        "newport news", "brownsville", "overland park", "santa clarita", "providence",
        "garden grove", "chattanooga", "oceanside", "jackson", "fort lauderdale",
        "santa rosa", "rancho cucamonga", "port st lucie", "tempe", "ontario",
        "vancouver", "cape coral", "sioux falls", "springfield", "peoria",
        "pembroke pines", "elk grove", "salem", "lancaster", "corona", "eugene",
        "palmdale", "salinas", "springfield", "pasadena", "fort collins",
        "hayward", "pomona", "cary", "rockford", "alexandria", "escondido",
        "billings", "sandy springs", "naperville", "macon", "lakewood",
        "pueblo", "high point", "west covina", "antioch", "inglewood",
        // 加州特定城市
        "pacifica", "daly city", "san mateo", "redwood city", "palo alto",
        "mountain view", "sunnyvale", "santa clara", "san rafael", "novato",
        "petaluma", "napa", "vallejo", "fairfield", "vacaville", "davis",
        "woodland", "roseville", "rocklin", "auburn", "grass valley",
        "nevada city", "truckee", "south lake tahoe", "placerville"
    ]

    // MARK: - USPS标准街道类型缩写
    private let uspsStreetTypes = [
        "STREET": "ST", "AVENUE": "AVE", "ROAD": "RD", "DRIVE": "DR",
        "COURT": "CT", "PLACE": "PL", "LANE": "LN", "CIRCLE": "CIR",
        "BOULEVARD": "BLVD", "PARKWAY": "PKWY", "HIGHWAY": "HWY",
        "TERRACE": "TER", "SQUARE": "SQ", "TRAIL": "TRL", "WAY": "WAY"
    ]

    // MARK: - AddressProcessorProtocol Implementation

    func detectCountry(from address: String) -> Double {
        let lowercaseAddress = address.lowercased()
        var confidence: Double = 0.0

        // 1. 检查美国州缩写 (高权重)
        for state in usStates {
            let patterns = [
                "\\b\(state.lowercased())\\b",           // 独立单词
                ",\\s*\(state.lowercased())\\s*$",       // 逗号后的州缩写（地址末尾）
                ",\\s*\(state.lowercased())\\s*,",       // 逗号间的州缩写
                "\\s+\(state.lowercased())\\s*$"         // 空格后的州缩写（地址末尾）
            ]

            for pattern in patterns {
                if lowercaseAddress.range(of: pattern, options: .regularExpression) != nil {
                    confidence += 0.8
                    break
                }
            }
            if confidence > 0 { break }
        }

        // 2. 检查美国邮编格式 (中等权重)
        if lowercaseAddress.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil {
            confidence += 0.6
        }

        // 3. 检查美国城市 (中等权重)
        for city in usCities {
            if lowercaseAddress.contains(city) {
                confidence += 0.5
                break
            }
        }

        // 4. 检查"United States"或"USA" (高权重)
        if lowercaseAddress.contains("united states") || lowercaseAddress.contains(" usa") {
            confidence += 0.9
        }

        return min(confidence, 1.0)
    }

    func standardizeAddress(_ address: String) -> String {
        var standardized = address

        // 应用USPS标准缩写
        for (fullForm, abbreviation) in uspsStreetTypes {
            let pattern = "\\b\(fullForm)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 标准化州缩写为大写
        for state in usStates {
            let pattern = "\\b\(state.lowercased())\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: state,
                options: .regularExpression
            )
        }

        return standardized.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    func validateAddressFormat(_ address: String) -> CountryAddressValidationResult {
        var issues: [String] = []
        var suggestions: [String] = []
        var confidence: Double = 1.0

        // 检查是否包含州缩写
        let hasState = usStates.contains { state in
            address.range(of: "\\b\(state)\\b", options: [.regularExpression, .caseInsensitive]) != nil
        }

        if !hasState {
            issues.append("缺少州缩写")
            suggestions.append("请添加州缩写（如CA, NY, TX等）")
            confidence -= 0.3
        }

        // 检查邮编格式
        let hasZip = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil
        if !hasZip {
            issues.append("缺少邮编")
            suggestions.append("请添加5位数邮编")
            confidence -= 0.2
        }

        // 检查街道号码
        let hasStreetNumber = address.range(of: "^\\d+", options: .regularExpression) != nil
        if !hasStreetNumber {
            issues.append("缺少街道号码")
            suggestions.append("请在地址开头添加街道号码")
            confidence -= 0.2
        }

        return CountryAddressValidationResult(
            isValid: issues.isEmpty,
            confidence: max(confidence, 0.0),
            issues: issues,
            suggestions: suggestions
        )
    }

    func extractAddressComponents(_ address: String) -> CountryAddressComponents {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        var streetNumber: String?
        var streetName: String?
        var unitNumber: String?
        var city: String?
        var state: String?
        var postalCode: String?

        // 解析第一部分（街道地址）
        if let firstComponent = components.first {
            let streetParts = firstComponent.components(separatedBy: " ")

            // 提取街道号码
            if let firstPart = streetParts.first, firstPart.range(of: "^\\d+", options: .regularExpression) != nil {
                streetNumber = firstPart
                streetName = streetParts.dropFirst().joined(separator: " ")
            }

            // 检查单元号
            let unitPatterns = ["apt", "apartment", "unit", "suite", "ste", "room", "rm", "#"]
            for pattern in unitPatterns {
                if let range = firstComponent.range(of: "\\b\(pattern)\\s*\\w+", options: [.regularExpression, .caseInsensitive]) {
                    unitNumber = String(firstComponent[range])
                    break
                }
            }
        }

        // 解析城市
        if components.count > 1 {
            city = components[1]
        }

        // 解析州和邮编
        if components.count > 2 {
            let lastComponent = components[2]

            // 提取州
            for stateCode in usStates {
                if lastComponent.range(of: "\\b\(stateCode)\\b", options: [.regularExpression, .caseInsensitive]) != nil {
                    state = stateCode
                    break
                }
            }

            // 提取邮编
            if let zipRange = lastComponent.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) {
                postalCode = String(lastComponent[zipRange])
            }
        }

        return CountryAddressComponents(
            streetNumber: streetNumber,
            streetName: streetName,
            unitNumber: unitNumber,
            suburb: nil, // 美国通常不使用suburb概念
            city: city,
            state: state,
            postalCode: postalCode,
            country: countryName
        )
    }

    func formatForGeocoding(_ address: String) -> String {
        // 对于美国地址，Apple Maps通常偏好完整形式
        var formatted = address

        // 将USPS缩写转换为完整形式以提高geocoding成功率
        let reverseMapping = Dictionary(uniqueKeysWithValues: uspsStreetTypes.map { ($1, $0) })

        for (abbreviation, fullForm) in reverseMapping {
            let pattern = "\\b\(abbreviation)\\b"
            formatted = formatted.replacingOccurrences(
                of: pattern,
                with: fullForm.capitalized,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        return formatted
    }
}
