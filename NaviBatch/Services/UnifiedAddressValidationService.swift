import Foundation
import CoreLocation
import MapKit

/// 统一地址验证服务
/// 为批量贴入、文件导入、网上下载等功能提供一致的地址验证逻辑
actor UnifiedAddressValidationService {
    static let shared = UnifiedAddressValidationService()

    // 🚦 使用全局速率限制管理器
    private let rateLimiter = GlobalGeocodingRateLimiter.shared

    // 🚀 优化：减少重复日志的静态变量
    #if DEBUG
    private static var loggedUnifiedChecks: Set<String> = []
    private static var loggedUnifiedHits: Set<String> = []
    private static var loggedUnifiedMisses: Set<String> = []
    #endif

    private init() {}

    // MARK: - 公共接口

    /// 验证单个地址 - 🚀 集成智能缓存系统
    /// - Parameters:
    ///   - address: 原始地址字符串
    ///   - existingCoordinate: 已有坐标（可选）
    /// - Returns: 验证结果
    func validateAddress(_ address: String, existingCoordinate: CLLocationCoordinate2D? = nil) async -> AddressValidationResult {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !trimmedAddress.isEmpty else {
            return AddressValidationResult(
                originalAddress: address,
                geocodedAddress: "",
                coordinate: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                isModified: false,
                confidence: .veryLow,
                modificationType: .none,
                suggestions: [],
                warningMessage: "empty_address".localized
            )
        }

        // 🚀 优化：减少重复的统一验证日志
        #if DEBUG
        if !Self.loggedUnifiedChecks.contains(trimmedAddress) {
            Self.loggedUnifiedChecks.insert(trimmedAddress)
            print("🚀 UNIFIED: 检查缓存: \(trimmedAddress)")

            // 限制缓存大小
            if Self.loggedUnifiedChecks.count > 50 {
                Self.loggedUnifiedChecks.removeAll()
            }
        }
        #endif

        if let validatedResult = await UserAddressDatabase.shared.getValidatedAddress(for: trimmedAddress) {
            #if DEBUG
            if !Self.loggedUnifiedHits.contains(trimmedAddress) {
                Self.loggedUnifiedHits.insert(trimmedAddress)
                print("🚀 UNIFIED: ✅ 使用数据库结果: \(trimmedAddress)")

                // 限制缓存大小
                if Self.loggedUnifiedHits.count > 50 {
                    Self.loggedUnifiedHits.removeAll()
                }
            }
            #endif
            return AddressValidationResult(
                originalAddress: trimmedAddress,
                geocodedAddress: trimmedAddress,
                coordinate: validatedResult.coordinate,
                isModified: false,
                confidence: mapCacheConfidenceToValidationConfidence(validatedResult.confidence),
                modificationType: .none,
                suggestions: [],
                warningMessage: nil
            )
        }
        #if DEBUG
        if !Self.loggedUnifiedMisses.contains(trimmedAddress) {
            Self.loggedUnifiedMisses.insert(trimmedAddress)
            print("🚀 UNIFIED: ❌ 缓存未命中，调用API: \(trimmedAddress)")

            // 限制缓存大小
            if Self.loggedUnifiedMisses.count > 50 {
                Self.loggedUnifiedMisses.removeAll()
            }
        }
        #endif

        // 如果已有坐标，验证坐标
        if let coordinate = existingCoordinate, coordinate.latitude != 0 || coordinate.longitude != 0 {
            return await validateAddressWithCoordinate(trimmedAddress, coordinate: coordinate)
        }

        // 翻译地址为英文以提高验证成功率
        let translatedAddress = AddressStandardizer.translateAddressToEnglish(trimmedAddress)

        // 需要地理编码，使用翻译后的地址
        return await validateAddressWithGeocoding(translatedAddress, originalAddress: trimmedAddress)
    }

    /// 批量验证地址 - 🚦 增强版本，支持智能速率限制
    /// - Parameters:
    ///   - addresses: 地址数组
    ///   - batchSize: 批处理大小
    ///   - progressCallback: 进度回调
    /// - Returns: 验证结果数组
    func validateAddresses(
        _ addresses: [String],
        batchSize: Int = 3, // 🚦 减少批次大小，更保守
        progressCallback: @escaping (Double) -> Void = { _ in }
    ) async -> [AddressValidationResult] {
        var results: [AddressValidationResult] = []
        let totalCount = addresses.count

        // 🚦 检查是否需要暂停处理
        if await rateLimiter.shouldPauseBatchProcessing() {
            Logger.warning("🚦 API使用率过高，暂停批量处理30秒", type: .location)
            try? await Task.sleep(nanoseconds: 30_000_000_000)
        }

        // 分批处理
        for batchStart in stride(from: 0, to: totalCount, by: batchSize) {
            let batchEnd = min(batchStart + batchSize, totalCount)
            let currentBatch = Array(addresses[batchStart..<batchEnd])

            // 更新进度
            let progress = Double(batchStart) / Double(totalCount)
            await MainActor.run {
                progressCallback(progress)
            }

            // 并行处理当前批次
            let batchResults = await withTaskGroup(of: (Int, AddressValidationResult).self, returning: [AddressValidationResult].self) { group in
                for (index, address) in currentBatch.enumerated() {
                    group.addTask {
                        let result = await self.validateAddress(address)
                        return (batchStart + index, result)
                    }
                }

                var batchResults: [(Int, AddressValidationResult)] = []
                for await result in group {
                    batchResults.append(result)
                }
                return batchResults.sorted { $0.0 < $1.0 }.map { $0.1 }
            }

            results.append(contentsOf: batchResults)

            // 🚦 智能批次间延迟，根据API使用率动态调整
            if batchEnd < totalCount {
                let smartDelay = await rateLimiter.getSmartBatchDelay()
                try? await Task.sleep(nanoseconds: UInt64(smartDelay * 1_000_000_000))
            }
        }

        // 最终进度更新
        await MainActor.run {
            progressCallback(1.0)
        }

        return results
    }



    // MARK: - 私有方法

    /// 验证已有坐标的地址
    private func validateAddressWithCoordinate(_ address: String, coordinate: CLLocationCoordinate2D) async -> AddressValidationResult {
        // 基础坐标验证
        let userLocation = LocationManager.shared.userLocation
        let coordinateValidation = DeliveryPoint.validateCoordinatesGlobally(
            latitude: coordinate.latitude,
            longitude: coordinate.longitude,
            userLocation: userLocation
        )

        if !coordinateValidation.isValid {
            return AddressValidationResult(
                originalAddress: address,
                geocodedAddress: address,
                coordinate: coordinate,
                isModified: false,
                confidence: .veryLow,
                modificationType: .none,
                suggestions: [],
                warningMessage: coordinateValidation.warning ?? "invalid_coordinates".localized
            )
        }

        // 反向地理编码验证 - 对于全球地址搜索成功的结果，跳过反向验证
        let validationScore = await ReverseGeocodingValidationService.shared.validateAddressWithReverseGeocoding(
            originalAddress: address,
            coordinate: coordinate,
            skipIfTrusted: true // 全球地址搜索成功的结果被认为是可信的
        )

        // 🎯 降低验证阈值，特别是对美国地址更宽松
        let validationThreshold: Double = {
            if address.lowercased().contains("ca") || address.lowercased().contains("california") ||
               address.lowercased().contains("usa") || address.lowercased().contains("united states") {
                return 50.0  // 美国地址使用更宽松的阈值
            } else {
                return 60.0  // 其他地址使用标准阈值
            }
        }()

        let isValid = validationScore.totalScore >= validationThreshold
        let warningMessage = coordinateValidation.validationStatus == .warning ?
            (coordinateValidation.warning ?? "coordinate_warning".localized) :
            (isValid ? nil : "address_validation_issue".localized)

        return AddressValidationResult(
            originalAddress: address,
            geocodedAddress: address,
            coordinate: coordinate,
            isModified: false,
            confidence: isValid ? .high : .low,
            modificationType: .none,
            suggestions: [],
            warningMessage: warningMessage
        )
    }

    /// 通过地理编码验证地址 - 🌍 使用全球地址处理器
    private func validateAddressWithGeocoding(_ address: String, originalAddress: String? = nil) async -> AddressValidationResult {
        let addressToReturn = originalAddress ?? address

        // 🌍 使用全球地址处理器进行地理编码
        let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(address)

        switch globalResult {
        case .success(_, let formattedAddress, let coordinate, let placemark, let strategy, let confidence):
            print("🌍 UnifiedAddressValidationService - 全球地址处理成功: \(strategy)")

            // 检查地址验证问题
            var warningMessage: String? = nil
            var finalConfidence = mapConfidenceLevel(confidence)

            // 检查是否缺少门牌号
            let originalHasNumber = addressToReturn.range(of: #"\d+"#, options: .regularExpression) != nil
            let placemarkHasNumber = placemark.subThoroughfare != nil && !placemark.subThoroughfare!.isEmpty

            if originalHasNumber && !placemarkHasNumber {
                warningMessage = "apple_maps_missing_house_number".localized
                finalConfidence = .low // 降低置信度
            }

            // 检查坐标有效性
            if coordinate.latitude == 0 && coordinate.longitude == 0 {
                warningMessage = "cannot_get_valid_coordinates".localized
                finalConfidence = .veryLow
            }

            // 🚀 静默保存到缓存
            if coordinate.latitude != 0 || coordinate.longitude != 0 {
                let cacheConfidence = mapValidationConfidenceToCacheConfidence(finalConfidence)
                print("🚀 UNIFIED: 保存到数据库: \(addressToReturn), 置信度: \(cacheConfidence)")
                await UserAddressDatabase.shared.saveValidatedAddress(
                    addressToReturn,
                    coordinate: coordinate,
                    source: .manual,
                    confidence: cacheConfidence
                )
            } else {
                print("🚀 UNIFIED: 坐标无效，跳过数据库保存: \(addressToReturn)")
            }

            // 🏗️ 创建包含 placemark 信息的验证结果
            let validationResult = AddressValidationResult(
                originalAddress: addressToReturn,
                geocodedAddress: formattedAddress,
                coordinate: coordinate,
                isModified: originalAddress != nil,
                confidence: finalConfidence,
                modificationType: originalAddress != nil ? .minor : .none,
                suggestions: [],
                warningMessage: warningMessage,
                placemark: placemark  // 🏗️ 传递 placemark 信息
            )

            return validationResult

        case .failed(_, let reason):
            print("🌍 UnifiedAddressValidationService - 全球地址处理失败: \(reason)")

            return AddressValidationResult(
                originalAddress: addressToReturn,
                geocodedAddress: address,
                coordinate: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                isModified: originalAddress != nil,
                confidence: .veryLow,
                modificationType: .none,
                suggestions: [],
                warningMessage: reason
            )
        }
    }

    /// 映射置信度级别
    private func mapConfidenceLevel(_ confidence: ConfidenceLevel) -> AddressConfidence {
        switch confidence {
        case .high: return .high
        case .medium: return .medium
        case .low: return .low
        }
    }

    /// 带重试的地理编码 - 支持国际地址
    private func geocodeAddressWithRetry(_ address: String, retryCount: Int = 3) async -> CLLocationCoordinate2D {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        // 根据国家信息增强地址和设置地理编码策略
        let enhancedAddress = enhanceAddressForCountry(address, countryInfo: countryInfo)
        let geocodingStrategy = getGeocodingStrategy(for: countryInfo)

        // 重试地理编码
        for attempt in 1...retryCount {
            // 🚦 使用全局速率限制
            await rateLimiter.waitForRateLimit()

            do {
                let geocoder = CLGeocoder()
                var placemarks: [CLPlacemark] = []

                do {
                    // 使用国家特定的地理编码策略
                    placemarks = try await geocoder.geocodeAddressString(
                        enhancedAddress,
                        in: geocodingStrategy.region,
                        preferredLocale: geocodingStrategy.locale
                    )
                } catch {
                    // 不使用区域限制重试
                    placemarks = try await geocoder.geocodeAddressString(
                        enhancedAddress,
                        in: nil,
                        preferredLocale: geocodingStrategy.locale
                    )
                }

                // 根据国家优先选择结果
                if let targetCountry = geocodingStrategy.prioritizeCountry,
                   let targetPlacemark = placemarks.first(where: { $0.country == targetCountry }),
                   let location = targetPlacemark.location?.coordinate {
                    return location
                } else if let placemark = placemarks.first,
                         let location = placemark.location?.coordinate {
                    return location
                }

            } catch {
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    // 速率限制错误
                    try? await Task.sleep(nanoseconds: 60_000_000_000)
                } else {
                    try? await Task.sleep(nanoseconds: UInt64(attempt) * 1_000_000_000)
                }
            }
        }

        return CLLocationCoordinate2D(latitude: 0, longitude: 0)
    }



    /// 地理编码策略结构
    private struct GeocodingStrategy {
        let region: CLCircularRegion?
        let locale: Locale
        let prioritizeCountry: String?
    }

    /// 根据国家获取地理编码策略
    private func getGeocodingStrategy(for countryInfo: AddressCountryDetector.CountryInfo?) -> GeocodingStrategy {
        guard let country = countryInfo else {
            // 全球策略：不限制区域，使用当前语言环境
            return GeocodingStrategy(
                region: nil,
                locale: Locale.current,
                prioritizeCountry: nil
            )
        }

        // 根据国家创建特定策略
        let region: CLCircularRegion?
        switch country.code {
        case "AU":
            // 澳大利亚：使用墨尔本区域
            let melbourneCenter = CLLocationCoordinate2D(latitude: -37.8796, longitude: 145.1631)
            region = CLCircularRegion(center: melbourneCenter, radius: country.searchRadius, identifier: "Australia")
        case "HK":
            // 香港：暂时不使用区域限制，避免地理编码错误
            // 让Apple API根据"Hong Kong"后缀自动识别
            region = nil
        default:
            // 其他国家：不使用区域限制，让Apple API自由搜索
            region = nil
        }

        return GeocodingStrategy(
            region: region,
            locale: country.locale,
            prioritizeCountry: country.name
        )
    }

    /// 根据国家增强地址信息
    private func enhanceAddressForCountry(_ address: String, countryInfo: AddressCountryDetector.CountryInfo?) -> String {
        guard let country = countryInfo else {
            // 无法识别国家，不做增强
            return address
        }

        switch country.code {
        case "AU":
            return enhanceAustralianAddress(address)
        case "HK":
            return enhanceHongKongAddress(address)
        case "US":
            return enhanceUSAddress(address)
        default:
            // 其他国家暂不增强
            return address
        }
    }

    /// 增强澳大利亚地址
    private func enhanceAustralianAddress(_ address: String) -> String {
        var enhancedAddress = address

        if !address.lowercased().contains("australia") {
            if address.lowercased().contains("glen waverley") {
                enhancedAddress = "\(address), VIC 3150, Australia"
            } else if address.lowercased().contains("mount waverley") || address.lowercased().contains("mt waverley") {
                enhancedAddress = "\(address), VIC 3149, Australia"
            } else if address.lowercased().contains("clayton") {
                enhancedAddress = "\(address), VIC 3168, Australia"
            } else if address.lowercased().contains("oakleigh") {
                enhancedAddress = "\(address), VIC 3166, Australia"
            } else {
                enhancedAddress = "\(address), Glen Waverley, VIC 3150, Australia"
            }
        }

        return enhancedAddress
    }

    /// 增强香港地址
    private func enhanceHongKongAddress(_ address: String) -> String {
        var enhancedAddress = address

        // 如果地址不包含香港标识，添加香港
        if !address.lowercased().contains("hong kong") && !address.contains("香港") {
            enhancedAddress = "\(address), Hong Kong"
        }

        return enhancedAddress
    }

    /// 增强美国地址
    private func enhanceUSAddress(_ address: String) -> String {
        var enhancedAddress = address

        // 如果地址不包含美国标识，添加USA
        if !address.lowercased().contains("usa") && !address.lowercased().contains("united states") {
            enhancedAddress = "\(address), USA"
        }

        return enhancedAddress
    }

    // MARK: - 速率限制 (已迁移到全局管理器)

    /// 获取标准化地址格式
    /// - Parameters:
    ///   - coordinate: 坐标
    ///   - fallbackAddress: 备用地址
    /// - Returns: 标准化的地址
    private func getStandardizedAddress(coordinate: CLLocationCoordinate2D, fallbackAddress: String) async -> String {
        // 尝试反向地理编码获取标准格式地址
        do {
            let geocoder = CLGeocoder()
            let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
            let placemarks = try await geocoder.reverseGeocodeLocation(location)

            if let placemark = placemarks.first {
                // 构建标准格式地址
                var addressComponents: [String] = []

                // 门牌号 + 街道名
                if let streetNumber = placemark.subThoroughfare,
                   let streetName = placemark.thoroughfare {
                    addressComponents.append("\(streetNumber) \(streetName)")
                } else if let streetName = placemark.thoroughfare {
                    addressComponents.append(streetName)
                }

                // 城市
                if let city = placemark.locality {
                    addressComponents.append(city)
                }

                // 州/省
                if let state = placemark.administrativeArea {
                    addressComponents.append(state)
                }

                // 邮编
                if let postalCode = placemark.postalCode {
                    addressComponents.append(postalCode)
                }

                // 国家
                if let country = placemark.country {
                    addressComponents.append(country)
                }

                if !addressComponents.isEmpty {
                    let standardizedAddress = addressComponents.joined(separator: ", ")
                    Logger.info("📍 反向地理编码获取标准地址: '\(fallbackAddress)' -> '\(standardizedAddress)'", type: .location)
                    return standardizedAddress
                }
            }
        } catch {
            Logger.warning("⚠️ 反向地理编码失败: \(error.localizedDescription)", type: .location)
        }

        // 如果反向地理编码失败，使用AddressStandardizer标准化原地址
        let standardized = AddressStandardizer.standardizeAddress(fallbackAddress)
        if standardized != fallbackAddress {
            Logger.info("🔧 使用AddressStandardizer标准化: '\(fallbackAddress)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 将缓存置信度转换为验证置信度
    private func mapCacheConfidenceToValidationConfidence(_ cacheConfidence: Float) -> AddressConfidence {
        switch cacheConfidence {
        case 0.9...1.0: return .high
        case 0.8..<0.9: return .medium
        case 0.6..<0.8: return .low
        default: return .veryLow
        }
    }

    /// 将验证置信度转换为缓存置信度
    private func mapValidationConfidenceToCacheConfidence(_ validationConfidence: AddressConfidence) -> Float {
        switch validationConfidence {
        case .high: return 0.95
        case .medium: return 0.85
        case .low: return 0.75
        case .veryLow: return 0.65
        }
    }
}

// MARK: - 扩展和辅助类型

/// 修改类型枚举
enum ModificationType {
    case none
    case standardized
    case enhanced
    case corrected
}

/// 地址置信度扩展
extension AddressConfidence {
    var rawValue: Double {
        switch self {
        case .high: return 0.8
        case .medium: return 0.6
        case .low: return 0.4
        case .veryLow: return 0.2
        }
    }

    static func from(_ value: Double) -> AddressConfidence {
        switch value {
        case 0.8...1.0: return .high
        case 0.6..<0.8: return .medium
        case 0.4..<0.6: return .low
        default: return .veryLow
        }
    }
}

/// 统一地址验证结果（扩展现有的 AddressValidationResult）
extension AddressValidationResult {
    /// 是否有警告
    var hasWarning: Bool {
        return warningMessage != nil && !warningMessage!.isEmpty
    }

    /// 是否可以导入（即使有警告也可以导入）
    var canImport: Bool {
        return !originalAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    /// 验证分数（基于置信度）
    var validationScore: Int {
        switch confidence {
        case .high: return 100
        case .medium: return 75
        case .low: return 50
        case .veryLow: return 25
        }
    }

    /// 是否有效（基于置信度）
    var isValid: Bool {
        return confidence == .high || confidence == .medium
    }

    /// 验证后的地址
    var validatedAddress: String {
        return geocodedAddress.isEmpty ? originalAddress : geocodedAddress
    }
}
