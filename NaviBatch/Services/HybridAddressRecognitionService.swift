import UIKit
import Vision
import Foundation

// MARK: - 混合地址识别服务
/// 结合OCR和AI的两阶段地址识别服务
/// 第一阶段：使用Apple Vision OCR提取文字
/// 第二阶段：使用Gemma AI分析和格式化地址
class HybridAddressRecognitionService {

    // MARK: - 依赖服务
    private let hybridAIService = HybridAIService.shared  // 🔥 新的混合AI服务（Firebase AI优先）
    private let gemmaService = GemmaVisionService.shared  // 保留用于地址转换等功能

    // MARK: - 识别模式
    enum RecognitionMode {
        case ocrFirst       // OCR优先
        case aiFirst        // 🔥 混合AI优先（Firebase AI → OpenRouter → OCR）
        case ocrOnly        // 仅OCR
        case aiOnly         // 🔥 仅混合AI（Firebase AI → OpenRouter）
    }

    // MARK: - 识别结果
    struct HybridRecognitionResult {
        let addresses: [String]
        let confidence: Double
        let processingTime: TimeInterval
        let methodUsed: String
        let ocrText: String?        // OCR提取的原始文字
        let modelUsed: String?      // AI模型名称
        let success: Bool
        let detectedAppType: DeliveryAppType?  // 🎯 AI检测到的应用类型

        // 详细信息
        let ocrProcessingTime: TimeInterval?
        let aiProcessingTime: TimeInterval?
        let totalCharactersRecognized: Int
    }

    // MARK: - 主要识别方法
    func recognizeAddresses(
        from image: UIImage,
        mode: RecognitionMode = .aiFirst,  // 默认使用AI优先模式
        appType: DeliveryAppType = .justPhoto,  // 🎯 新增：用户选择的应用类型
        isPDFImage: Bool = false,  // 🎯 新增：标识图像是否来自PDF
        progressCallback: ((String) -> Void)? = nil
    ) async throws -> HybridRecognitionResult {

        let startTime = Date()
        Logger.aiInfo("🚀 开始混合地址识别，模式: \(mode)")

        switch mode {
        case .ocrFirst:
            return try await recognizeWithOCRFirst(image: image, appType: appType, isPDFImage: isPDFImage, progressCallback: progressCallback, startTime: startTime)
        case .aiFirst:
            return try await recognizeWithAIFirst(image: image, appType: appType, isPDFImage: isPDFImage, progressCallback: progressCallback, startTime: startTime)
        case .ocrOnly:
            return try await recognizeWithOCROnly(image: image, progressCallback: progressCallback, startTime: startTime)
        case .aiOnly:
            return try await recognizeWithAIOnly(image: image, appType: appType, isPDFImage: isPDFImage, progressCallback: progressCallback, startTime: startTime)
        }
    }



    // MARK: - OCR优先识别
    private func recognizeWithOCRFirst(
        image: UIImage,
        appType: DeliveryAppType,
        isPDFImage: Bool,
        progressCallback: ((String) -> Void)?,
        startTime: Date
    ) async throws -> HybridRecognitionResult {

        // 第一阶段：OCR文字识别
        progressCallback?("analyzing_image_text".localized)
        let ocrStartTime = Date()

        let ocrText = try await extractTextWithVision(from: image)
        let ocrTime = Date().timeIntervalSince(ocrStartTime)

        Logger.aiInfo("📝 OCR识别完成，用时: \(String(format: "%.2f", ocrTime))s")
        Logger.aiDebug("OCR提取文字: \(ocrText)")

        // 检查OCR是否提取到有效文字
        if ocrText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            Logger.aiWarning("OCR未提取到文字，降级到AI视觉识别")
            progressCallback?("fallback_to_ocr".localized)
            return try await recognizeWithAIOnly(image: image, appType: appType, isPDFImage: isPDFImage, progressCallback: progressCallback, startTime: startTime)
        }

        // 第二阶段：AI地址分析
        progressCallback?("analyzing_image_content".localized)
        let aiStartTime = Date()

        let (addresses, detectedAppType) = try await analyzeTextForAddresses(ocrText, appType: appType)
        let aiTime = Date().timeIntervalSince(aiStartTime)

        let totalTime = Date().timeIntervalSince(startTime)

        Logger.aiInfo("✅ 混合识别完成，总用时: \(String(format: "%.2f", totalTime))s")
        Logger.aiInfo("📊 识别结果: \(addresses.count)个地址")

        return HybridRecognitionResult(
            addresses: addresses,
            confidence: 0.9, // OCR+AI组合的高置信度
            processingTime: totalTime,
            methodUsed: "OCR + AI",
            ocrText: ocrText,
            modelUsed: "Apple Vision + Gemma",
            success: true,
            detectedAppType: detectedAppType, // 🎯 修复：传递AI检测到的应用类型
            ocrProcessingTime: ocrTime,
            aiProcessingTime: aiTime,
            totalCharactersRecognized: ocrText.count
        )
    }

    // MARK: - AI优先识别（Firebase AI → OpenRouter → OCR）
    private func recognizeWithAIFirst(
        image: UIImage,
        appType: DeliveryAppType,
        isPDFImage: Bool,
        progressCallback: ((String) -> Void)?,
        startTime: Date
    ) async throws -> HybridRecognitionResult {

        progressCallback?("using_advanced_recognition".localized)

        do {
            // 🔥 使用新的混合AI服务（Firebase AI优先，OpenRouter备用）
            let aiResult = try await hybridAIService.extractAddressesFromImage(image, appType: appType, isPDFImage: isPDFImage)

            // 🚨 检查AI结果状态
            if aiResult.success {
                let totalTime = Date().timeIntervalSince(startTime)

                if aiResult.addresses.isEmpty {
                    // 🎯 AI正确拒绝测试数据，返回空结果（不降级到OCR）
                    Logger.aiInfo("🚫 AI正确拒绝测试数据，返回空结果（避免OCR提取测试数据）")
                    return HybridRecognitionResult(
                        addresses: [],
                        confidence: 1.0, // 高置信度：AI正确识别并拒绝了测试数据
                        processingTime: totalTime,
                        methodUsed: "Hybrid AI (Test Data Rejected)",
                        ocrText: nil,
                        modelUsed: aiResult.modelUsed,
                        success: true, // 这是成功的行为
                        detectedAppType: aiResult.detectedAppType,
                        ocrProcessingTime: nil,
                        aiProcessingTime: aiResult.processingTime,
                        totalCharactersRecognized: 0
                    )
                } else {
                    // 🎯 AI成功识别到真实地址
                    Logger.aiInfo("✅ AI成功识别到 \(aiResult.addresses.count) 个真实地址")
                    return HybridRecognitionResult(
                        addresses: aiResult.addresses,
                        confidence: aiResult.confidence,
                        processingTime: totalTime,
                        methodUsed: "Hybrid AI (Firebase → OpenRouter)",
                        ocrText: nil,
                        modelUsed: aiResult.modelUsed,
                        success: aiResult.success,
                        detectedAppType: aiResult.detectedAppType, // 🎯 传递AI检测到的应用类型
                        ocrProcessingTime: nil,
                        aiProcessingTime: aiResult.processingTime,
                        totalCharactersRecognized: 0
                    )
                }
            } else {
                // 🔄 AI真正失败（解析错误等），降级到OCR
                Logger.aiWarning("AI服务失败，降级到OCR")
                progressCallback?("fallback_to_ocr".localized)
                return try await recognizeWithOCROnly(image: image, progressCallback: progressCallback, startTime: startTime)
            }
        } catch {
            // 所有AI失败时降级到OCR
            Logger.aiWarning("所有AI服务都失败，降级到OCR: \(error)")
            progressCallback?("fallback_to_ocr".localized)
            return try await recognizeWithOCROnly(image: image, progressCallback: progressCallback, startTime: startTime)
        }
    }

    // MARK: - 仅OCR识别
    private func recognizeWithOCROnly(
        image: UIImage,
        progressCallback: ((String) -> Void)?,
        startTime: Date
    ) async throws -> HybridRecognitionResult {

        progressCallback?("recognizing_text".localized)

        let ocrText = try await extractTextWithVision(from: image)
        let addresses = extractAddressesFromText(ocrText)
        let totalTime = Date().timeIntervalSince(startTime)

        return HybridRecognitionResult(
            addresses: addresses,
            confidence: 0.7, // OCR单独使用的中等置信度
            processingTime: totalTime,
            methodUsed: "OCR Only",
            ocrText: ocrText,
            modelUsed: "Apple Vision",
            success: !addresses.isEmpty,
            detectedAppType: nil, // OCR模式不检测应用类型
            ocrProcessingTime: totalTime,
            aiProcessingTime: nil,
            totalCharactersRecognized: ocrText.count
        )
    }

    // MARK: - 仅AI识别（Firebase AI → OpenRouter）
    private func recognizeWithAIOnly(
        image: UIImage,
        appType: DeliveryAppType,
        isPDFImage: Bool,
        progressCallback: ((String) -> Void)?,
        startTime: Date
    ) async throws -> HybridRecognitionResult {

        do {
            // 🔥 使用新的混合AI服务（Firebase AI优先，OpenRouter备用）
            let aiResult = try await hybridAIService.extractAddressesFromImage(image, appType: appType, isPDFImage: isPDFImage)
            let totalTime = Date().timeIntervalSince(startTime)

            // 🚨 检查AI结果状态
            if !aiResult.success {
                Logger.aiError("AI模型解析失败")
                throw RecognitionError.aiFailed
            }

            if aiResult.addresses.isEmpty {
                // 🎯 AI正确拒绝测试数据，返回空结果
                Logger.aiInfo("🚫 AI正确拒绝测试数据，返回空结果")
                return HybridRecognitionResult(
                    addresses: [],
                    confidence: 1.0, // 高置信度：AI正确识别并拒绝了测试数据
                    processingTime: totalTime,
                    methodUsed: "Hybrid AI Only (Test Data Rejected)",
                    ocrText: nil,
                    modelUsed: aiResult.modelUsed,
                    success: true, // 这是成功的行为
                    detectedAppType: aiResult.detectedAppType,
                    ocrProcessingTime: nil,
                    aiProcessingTime: aiResult.processingTime,
                    totalCharactersRecognized: 0
                )
            }

            // 🎯 AI成功识别到真实地址
            return HybridRecognitionResult(
                addresses: aiResult.addresses,
                confidence: aiResult.confidence,
                processingTime: totalTime,
                methodUsed: "Hybrid AI Only (Firebase → OpenRouter)",
                ocrText: nil,
                modelUsed: aiResult.modelUsed,
                success: aiResult.success,
                detectedAppType: aiResult.detectedAppType, // 🎯 传递AI检测到的应用类型
                ocrProcessingTime: nil,
                aiProcessingTime: aiResult.processingTime,
                totalCharactersRecognized: 0
            )
        } catch {
            Logger.aiError("Hybrid AI Only模式失败: \(error)")
            throw error
        }
    }
}

// MARK: - OCR相关方法
extension HybridAddressRecognitionService {

    /// 使用Apple Vision框架提取图片中的文字
    private func extractTextWithVision(from image: UIImage) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            guard let cgImage = image.cgImage else {
                continuation.resume(throwing: RecognitionError.imageProcessingFailed)
                return
            }

            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }

                let recognizedStrings = request.results?.compactMap { result in
                    (result as? VNRecognizedTextObservation)?.topCandidates(1).first?.string
                } ?? []

                let fullText = recognizedStrings.joined(separator: "\n")
                continuation.resume(returning: fullText)
            }

            // 配置OCR请求
            request.recognitionLevel = .accurate
            request.usesLanguageCorrection = true

            // 支持多语言识别
            request.recognitionLanguages = ["zh-Hans", "zh-Hant", "en-US", "ja-JP", "ko-KR"]

            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

            do {
                try handler.perform([request])
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }

    /// 使用AI分析OCR提取的文字，识别其中的地址和追踪信息
    private func analyzeTextForAddresses(_ text: String, appType: DeliveryAppType) async throws -> (addresses: [String], detectedAppType: DeliveryAppType?) {
        Logger.aiInfo("🔍 开始分析OCR文字，长度: \(text.count)字符")
        Logger.aiInfo("📝 OCR原始文字: \(text)")

        // 🎯 直接让AI从原始OCR文字中提取地址和追踪信息
        // 只进行一次AI调用，避免重复处理
        do {
            Logger.aiInfo("🤖 使用AI从原始OCR文字中提取地址和追踪信息")
            let convertedAddresses = try await gemmaService.convertAddressesToAppleMapsFormat([text], appType: appType)
            Logger.aiInfo("🎯 AI提取结果: \(convertedAddresses)")

            let validAddresses = convertedAddresses.filter { !$0.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).isEmpty }

            if !validAddresses.isEmpty {
                // 🎯 从地址中提取检测到的应用类型
                let detectedAppType = extractDetectedAppTypeFromAddresses(validAddresses)
                Logger.aiInfo("🎯 从地址中提取到应用类型: \(detectedAppType?.rawValue ?? "nil")")
                return (addresses: validAddresses, detectedAppType: detectedAppType)
            } else {
                Logger.aiWarning("AI未提取到有效地址，使用简单OCR提取")
                let simpleAddresses = simpleOCRExtraction(text)
                return (addresses: simpleAddresses, detectedAppType: nil)
            }
        } catch {
            Logger.aiWarning("AI分析失败，使用简单OCR提取: \(error)")
            let simpleAddresses = simpleOCRExtraction(text)
            return (addresses: simpleAddresses, detectedAppType: nil)
        }
    }

    /// 从地址列表中提取检测到的应用类型
    private func extractDetectedAppTypeFromAddresses(_ addresses: [String]) -> DeliveryAppType? {
        for address in addresses {
            if address.contains("|APP:") {
                let components = address.components(separatedBy: "|APP:")
                if components.count >= 2 {
                    let appTypeString = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                    if let detectedAppType = DeliveryAppType(rawValue: appTypeString) {
                        return detectedAppType
                    }
                }
            }
        }
        return nil
    }

    /// 简单的OCR地址提取方法（不使用AI，避免重复调用）
    private func simpleOCRExtraction(_ text: String) -> [String] {
        Logger.aiInfo("🔍 使用简单OCR提取，避免重复AI调用")

        let extractedAddresses = extractAddressesFromText(text)

        Logger.aiInfo("🔍 OCR提取到的地址数量: \(extractedAddresses.count)")
        for (index, address) in extractedAddresses.enumerated() {
            Logger.aiInfo("  OCR地址 \(index + 1): \(address)")
        }

        return extractedAddresses
    }

    /// 简单的文字地址提取（备用方法）
    internal func extractAddressesFromText(_ text: String) -> [String] {
        let lines = text.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }

        var addresses: [String] = []
        var i = 0

        while i < lines.count {
            let currentLine = lines[i]

            // 检查当前行是否是地址的开始（包含街道号码）
            if isAddressStart(currentLine) {
                var fullAddress = currentLine

                // 检查下一行是否是地址的延续
                if i + 1 < lines.count {
                    let nextLine = lines[i + 1]
                    if isAddressContinuation(nextLine) {
                        // 合并地址行
                        fullAddress = "\(currentLine) \(nextLine)"
                        i += 1 // 跳过下一行，因为已经合并了
                    }
                }

                // 🇺🇸 Amazon Flex特殊处理：清理重复城市名
                fullAddress = cleanAmazonFlexAddress(fullAddress)

                // 验证合并后的地址是否有效
                if isLikelyAddress(fullAddress) {
                    addresses.append(fullAddress)
                }
            } else if isLikelyAddress(currentLine) {
                // 单行完整地址 - 也需要清理
                let cleanedAddress = cleanAmazonFlexAddress(currentLine)
                addresses.append(cleanedAddress)
            }

            i += 1
        }

        return addresses
    }

    /// 🇺🇸 清理Amazon Flex地址中的重复城市名和格式问题
    internal func cleanAmazonFlexAddress(_ address: String) -> String {
        var cleanedAddress = address

        // 常见的美国城市名列表（Amazon Flex常见配送城市）
        let commonUSCities = [
            "SAN MATEO", "SAN FRANCISCO", "SAN JOSE", "SAN DIEGO", "SAN ANTONIO",
            "LOS ANGELES", "NEW YORK", "CHICAGO", "HOUSTON", "PHOENIX",
            "PHILADELPHIA", "DALLAS", "AUSTIN", "JACKSONVILLE", "FORT WORTH",
            "COLUMBUS", "CHARLOTTE", "SEATTLE", "DENVER", "BOSTON",
            "DETROIT", "NASHVILLE", "MEMPHIS", "PORTLAND", "OKLAHOMA CITY",
            "LAS VEGAS", "LOUISVILLE", "BALTIMORE", "MILWAUKEE", "ALBUQUERQUE",
            "TUCSON", "FRESNO", "SACRAMENTO", "MESA", "KANSAS CITY",
            "ATLANTA", "LONG BEACH", "COLORADO SPRINGS", "RALEIGH", "MIAMI",
            "VIRGINIA BEACH", "OMAHA", "OAKLAND", "MINNEAPOLIS", "TULSA",
            "ARLINGTON", "TAMPA", "NEW ORLEANS", "WICHITA", "CLEVELAND"
        ]

        // 🔧 处理重复城市名问题
        // 例如: "SAN MATEO 1715 YORK AVE, SAN MATEO" → "1715 YORK AVE, SAN MATEO"
        for city in commonUSCities {
            // 匹配模式：城市名 + 地址 + 逗号 + 同样的城市名
            let duplicatePattern = "\(city)\\s+([^,]+),\\s*\(city)"
            if let regex = try? NSRegularExpression(pattern: duplicatePattern, options: .caseInsensitive) {
                let range = NSRange(cleanedAddress.startIndex..<cleanedAddress.endIndex, in: cleanedAddress)
                cleanedAddress = regex.stringByReplacingMatches(
                    in: cleanedAddress,
                    options: [],
                    range: range,
                    withTemplate: "$1, \(city)"
                )
            }

            // 处理开头重复的城市名
            // 例如: "SAN MATEO SAN MATEO 1715 YORK AVE" → "SAN MATEO 1715 YORK AVE"
            let startDuplicatePattern = "^\(city)\\s+\(city)\\s+"
            if let regex = try? NSRegularExpression(pattern: startDuplicatePattern, options: .caseInsensitive) {
                let range = NSRange(cleanedAddress.startIndex..<cleanedAddress.endIndex, in: cleanedAddress)
                cleanedAddress = regex.stringByReplacingMatches(
                    in: cleanedAddress,
                    options: [],
                    range: range,
                    withTemplate: "\(city) "
                )
            }
        }

        // 🔧 清理多余的空格和标点
        cleanedAddress = cleanedAddress
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression) // 多个空格变成一个
            .replacingOccurrences(of: ",,+", with: ",", options: .regularExpression) // 多个逗号变成一个
            .replacingOccurrences(of: "^,\\s*", with: "", options: .regularExpression) // 开头的逗号
            .replacingOccurrences(of: "\\s*,$", with: "", options: .regularExpression) // 结尾的逗号
            .trimmingCharacters(in: .whitespacesAndNewlines)

        return cleanedAddress
    }

    /// 判断是否是地址的开始（通常包含街道号码）
    internal func isAddressStart(_ text: String) -> Bool {
        // 澳洲地址格式
        let australianPatterns = [
            "^\\d+\\s+[A-Za-z]", // "1 Taylors", "19/1100 Wellington"
            "^Unit\\s+\\d+", // "Unit 58"
            "^\\d+/\\d+", // "19/1100"
            "^\\d+[A-Za-z]?\\s+[A-Za-z]" // "1A Main"
        ]

        // 🇺🇸 美国地址格式 (Amazon Flex等)
        let americanPatterns = [
            "^\\d+\\s+[A-Z]", // "24 N QUEBEC ST", "1715 YORK AVE"
            "^\\d+\\s+[A-Z]+\\s+[A-Z]", // "1794 SHOREVIEW AVE"
            "^\\d+\\s+[NSEW]\\s+[A-Z]", // "24 N QUEBEC" (方向缩写)
            "^\\d+\\s+[A-Z][a-z]+\\s+[A-Z]", // "1717 Nash DR" (混合大小写)
            "^\\d+\\s+[A-Z]+\\s+(ST|AVE|RD|DR|LN|CT|PL|BLVD|WAY|CIR)", // 美国街道类型缩写
            "^\\d+\\s+[A-Z]+\\s+(Street|Avenue|Road|Drive|Lane|Court|Place|Boulevard|Way|Circle)", // 完整街道类型
            "^\\d+-\\d+\\s+[A-Z]", // "123-125 Main St" (地址范围)
            "^\\d+[A-Z]\\s+[A-Z]", // "123A Main St" (带字母后缀)
        ]

        let allPatterns = australianPatterns + americanPatterns

        return allPatterns.contains { pattern in
            text.range(of: pattern, options: .regularExpression) != nil
        }
    }

    /// 判断是否是地址的延续（通常包含城市、州、邮编）
    internal func isAddressContinuation(_ text: String) -> Bool {
        // 澳洲地址格式
        let australianPatterns = [
            "Lane,.*Victoria.*\\d{4}", // "Lane,ROWVILLE,Victoria,3178,AUS"
            "Road,.*Victoria.*\\d{4}", // "Road,ROWVILLE,Victoria,3178,AUS"
            "Street,.*Victoria.*\\d{4}", // "Street,ROWVILLE,Victoria,3178,AUS"
            "Court,.*Victoria.*\\d{4}", // "Court,ROWVILLE,Victoria,3178,AUS"
            "Avenue,.*Victoria.*\\d{4}", // "Avenue,ROWVILLE,Victoria,3178,AUS"
            "Drive,.*Victoria.*\\d{4}", // "Drive,ROWVILLE,Victoria,3178,AUS"
            "Place,.*Victoria.*\\d{4}", // "Place,ROWVILLE,Victoria,3178,AUS"
            ",.*VIC.*\\d{4}", // 简化的VIC格式
            ",.*NSW.*\\d{4}", // NSW格式
            ",.*QLD.*\\d{4}", // QLD格式
            ",.*WA.*\\d{4}", // WA格式
            ",.*SA.*\\d{4}", // SA格式
            ",.*TAS.*\\d{4}", // TAS格式
            ",.*ACT.*\\d{4}", // ACT格式
            ",.*NT.*\\d{4}" // NT格式
        ]

        // 美国地址格式 - 支持Amazon Flex等美国配送应用
        let americanPatterns = [
            // 完整州名格式
            ".*California.*\\d{5}", // "SAN MATEO, California, 94401"
            ".*New York.*\\d{5}", // "NEW YORK, New York, 10001"
            ".*Texas.*\\d{5}", // "HOUSTON, Texas, 77001"
            ".*Florida.*\\d{5}", // "MIAMI, Florida, 33101"

            // 州名缩写格式 (最常见)
            ".*,\\s*CA\\s*\\d{5}", // "SAN MATEO, CA 94401"
            ".*,\\s*NY\\s*\\d{5}", // "NEW YORK, NY 10001"
            ".*,\\s*TX\\s*\\d{5}", // "HOUSTON, TX 77001"
            ".*,\\s*FL\\s*\\d{5}", // "MIAMI, FL 33101"
            ".*,\\s*WA\\s*\\d{5}", // "SEATTLE, WA 98101"
            ".*,\\s*OR\\s*\\d{5}", // "PORTLAND, OR 97201"
            ".*,\\s*NV\\s*\\d{5}", // "LAS VEGAS, NV 89101"
            ".*,\\s*AZ\\s*\\d{5}", // "PHOENIX, AZ 85001"
            ".*,\\s*CO\\s*\\d{5}", // "DENVER, CO 80201"
            ".*,\\s*IL\\s*\\d{5}", // "CHICAGO, IL 60601"
            ".*,\\s*MA\\s*\\d{5}", // "BOSTON, MA 02101"
            ".*,\\s*PA\\s*\\d{5}", // "PHILADELPHIA, PA 19101"
            ".*,\\s*OH\\s*\\d{5}", // "COLUMBUS, OH 43201"
            ".*,\\s*MI\\s*\\d{5}", // "DETROIT, MI 48201"
            ".*,\\s*GA\\s*\\d{5}", // "ATLANTA, GA 30301"
            ".*,\\s*NC\\s*\\d{5}", // "CHARLOTTE, NC 28201"
            ".*,\\s*VA\\s*\\d{5}", // "RICHMOND, VA 23218"
            ".*,\\s*MD\\s*\\d{5}", // "BALTIMORE, MD 21201"
            ".*,\\s*NJ\\s*\\d{5}", // "NEWARK, NJ 07101"
            ".*,\\s*CT\\s*\\d{5}", // "HARTFORD, CT 06101"

            // 简化格式 - 只有城市名（Amazon Flex常见）
            ".*SAN MATEO.*", // "SAN MATEO" (Amazon Flex截图中的格式)
            ".*LOS ANGELES.*", // "LOS ANGELES"
            ".*SAN FRANCISCO.*", // "SAN FRANCISCO"
            ".*NEW YORK.*", // "NEW YORK"
            ".*CHICAGO.*", // "CHICAGO"
            ".*HOUSTON.*", // "HOUSTON"
            ".*PHOENIX.*", // "PHOENIX"
            ".*PHILADELPHIA.*", // "PHILADELPHIA"
            ".*SAN ANTONIO.*", // "SAN ANTONIO"
            ".*SAN DIEGO.*", // "SAN DIEGO"
            ".*DALLAS.*", // "DALLAS"
            ".*SAN JOSE.*", // "SAN JOSE"
            ".*AUSTIN.*", // "AUSTIN"
            ".*JACKSONVILLE.*", // "JACKSONVILLE"
            ".*FORT WORTH.*", // "FORT WORTH"
            ".*COLUMBUS.*", // "COLUMBUS"
            ".*CHARLOTTE.*", // "CHARLOTTE"
            ".*SEATTLE.*", // "SEATTLE"
            ".*DENVER.*", // "DENVER"
            ".*BOSTON.*" // "BOSTON"
        ]

        let allPatterns = australianPatterns + americanPatterns

        return allPatterns.contains { pattern in
            text.range(of: pattern, options: .regularExpression) != nil
        }
    }

    /// 判断文字是否像地址
    internal func isLikelyAddress(_ text: String) -> Bool {
        // 地址特征关键词
        let addressKeywords = [
            // 中文
            "路", "街", "道", "巷", "弄", "号", "室", "楼", "层", "区", "市", "省", "县",
            "商场", "大厦", "中心", "广场", "花园", "小区", "村", "镇",

            // 英文 - 澳洲/通用格式
            "Road", "Street", "Avenue", "Lane", "Drive", "Court", "Place", "Square",
            "Building", "Tower", "Centre", "Center", "Mall", "Plaza", "Garden",
            "Apartment", "Unit", "Floor", "Shop", "Suite",

            // 🇺🇸 美国地址关键词 (Amazon Flex等)
            // 街道类型缩写 (最常见)
            "ST", "AVE", "RD", "DR", "LN", "CT", "PL", "BLVD", "WAY", "CIR",
            "PKWY", "TER", "LOOP", "PASS", "RUN", "TRL", "VW", "ALY", "BND", "BRG",
            "BYP", "CP", "CSWY", "CTR", "CV", "CRK", "CRST", "XING", "DL", "EST",
            "EXPY", "EXT", "FLD", "FLT", "FRD", "FRK", "FRY", "GDN", "GLN", "GRN",
            "GRV", "HBR", "HL", "HLW", "INLT", "IS", "JCT", "KNL", "LK", "LDG",
            "MDW", "ML", "MT", "NCK", "ORCH", "OVAL", "PARK", "PATH", "PIKE", "PNE",
            "PT", "RDG", "RIV", "ROW", "SQ", "STA", "STRM", "TER", "TRCE", "TUNL",
            "VLY", "VIS", "WALK", "WALL", "WL", "WY",

            // 街道类型完整形式
            "Boulevard", "Circle", "Parkway", "Terrace", "Loop", "Pass", "Run",
            "Trail", "View", "Alley", "Bend", "Bridge", "Bypass", "Camp", "Causeway",
            "Center", "Cove", "Creek", "Crest", "Crossing", "Dale", "Estate",
            "Expressway", "Extension", "Field", "Flat", "Ford", "Fork", "Ferry",
            "Garden", "Glen", "Green", "Grove", "Harbor", "Hill", "Hollow", "Inlet",
            "Island", "Junction", "Knoll", "Lake", "Landing", "Meadow", "Mill",
            "Mount", "Neck", "Orchard", "Oval", "Park", "Path", "Pike", "Pine",
            "Point", "Ridge", "River", "Row", "Square", "Station", "Stream",
            "Terrace", "Trace", "Tunnel", "Valley", "Vista", "Walk", "Wall", "Well", "Way",

            // 方向指示词
            "North", "South", "East", "West", "N", "S", "E", "W",
            "Northeast", "Northwest", "Southeast", "Southwest", "NE", "NW", "SE", "SW",

            // 美国常见城市名 (用于识别地址)
            "SAN MATEO", "SAN FRANCISCO", "SAN JOSE", "SAN DIEGO", "SAN ANTONIO",
            "LOS ANGELES", "NEW YORK", "CHICAGO", "HOUSTON", "PHOENIX", "PHILADELPHIA",
            "DALLAS", "AUSTIN", "JACKSONVILLE", "FORT WORTH", "COLUMBUS", "CHARLOTTE",
            "SEATTLE", "DENVER", "BOSTON", "DETROIT", "NASHVILLE", "MEMPHIS", "PORTLAND",
            "OKLAHOMA CITY", "LAS VEGAS", "LOUISVILLE", "BALTIMORE", "MILWAUKEE",
            "ALBUQUERQUE", "TUCSON", "FRESNO", "SACRAMENTO", "MESA", "KANSAS CITY",
            "ATLANTA", "LONG BEACH", "COLORADO SPRINGS", "RALEIGH", "MIAMI",
            "VIRGINIA BEACH", "OMAHA", "OAKLAND", "MINNEAPOLIS", "TULSA", "ARLINGTON",
            "TAMPA", "NEW ORLEANS", "WICHITA", "CLEVELAND"
        ]

        // 检查是否包含地址关键词
        let hasAddressKeyword = addressKeywords.contains { keyword in
            text.localizedCaseInsensitiveContains(keyword)
        }

        // 检查是否包含数字（门牌号等）
        let hasNumbers = text.rangeOfCharacter(from: .decimalDigits) != nil

        // 🇺🇸 特殊处理：美国地址可能更短，调整长度要求
        let reasonableLength: Bool
        if containsUSAddressPattern(text) {
            // 美国地址可以更短，如 "24 N QUEBEC ST"
            reasonableLength = text.count >= 8 && text.count <= 200
        } else {
            // 其他地址保持原有要求
            reasonableLength = text.count >= 10 && text.count <= 200
        }

        return hasAddressKeyword && hasNumbers && reasonableLength
    }

    /// 检查是否包含美国地址模式
    internal func containsUSAddressPattern(_ text: String) -> Bool {
        let usPatterns = [
            "\\d+\\s+[NSEW]\\s+[A-Z]+\\s+(ST|AVE|RD|DR)", // "24 N QUEBEC ST"
            "\\d+\\s+[A-Z]+\\s+(ST|AVE|RD|DR|LN|CT|PL)", // "1715 YORK AVE"
            "\\b(CA|NY|TX|FL|WA|OR|NV|AZ|CO|IL|MA|PA|OH|MI|GA|NC|VA|MD|NJ|CT)\\b", // 州名缩写
            "\\b\\d{5}(-\\d{4})?\\b" // 美国邮编格式
        ]

        return usPatterns.contains { pattern in
            text.range(of: pattern, options: .regularExpression) != nil
        }
    }


}

// MARK: - 错误定义
enum RecognitionError: Error, LocalizedError {
    case imageProcessingFailed
    case ocrFailed
    case aiFailed
    case noTextRecognized
    case noAddressesFound

    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed:
            return "image_processing_failed".localized
        case .ocrFailed:
            return "text_recognition_failed".localized
        case .aiFailed:
            return "ai_analysis_failed".localized
        case .noTextRecognized:
            return "no_text_recognized".localized
        case .noAddressesFound:
            return "no_addresses_found".localized
        }
    }
}


