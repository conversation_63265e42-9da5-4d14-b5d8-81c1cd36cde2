import Foundation
import CoreLocation
import MapKit

/// 全球地址处理器 - 支持175个国家的地址处理
/// 核心理念：信任Apple的地理编码能力，最小化地址修改
class UniversalAddressProcessor {

    static let shared = UniversalAddressProcessor()

    // 🚦 使用全局速率限制管理器
    private let rateLimiter = GlobalGeocodingRateLimiter.shared

    private init() {}

    // MARK: - 速率限制管理

    /// 等待直到可以发送请求（使用全局限制器）
    private func waitForRateLimit() async {
        await rateLimiter.waitForRateLimit()
    }

    // MARK: - 主要处理方法

    /// 处理任何国家的地址
    /// - Parameter address: 原始地址（任何语言）
    /// - Returns: 地理编码结果
    func processGlobalAddress(_ address: String) async -> GlobalGeocodingResult {
        // 输入验证
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedAddress.isEmpty else {
            Logger.warning("❌ 地址为空", type: .location)
            return GlobalGeocodingResult.failed(address: address, reason: "地址不能为空")
        }

        Logger.info("🌍 开始处理全球地址: \(trimmedAddress)", type: .location)

        // 保存原始地址，用于后续格式化
        let originalAddress = trimmedAddress

        // 🏠 优先检查是否包含公寓号，如果有则先尝试移除公寓号策略
        if containsApartmentInfo(originalAddress) {
            Logger.info("🏠 检测到公寓信息，优先尝试移除公寓号策略: \(originalAddress)", type: .location)
            if let result = await tryRemoveApartmentInfo(originalAddress) {
                // 验证移除公寓号后的结果是否更准确
                if isResultAccurate(result, originalAddress: originalAddress) {
                    Logger.info("✅ 移除公寓号策略成功且准确: \(originalAddress)", type: .location)
                    return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
                } else {
                    Logger.warning("⚠️ 移除公寓号策略成功但不够准确，继续尝试其他策略: \(originalAddress)", type: .location)
                }
            }
        }

        // 🎯 0. 优先使用Apple Maps风格的全球搜索（新增最高优先级策略）
        if let result = await tryGlobalMKLocalSearch(originalAddress) {
            Logger.info("✅ 全球MKLocalSearch成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        // 1. 原始地址优先策略
        if let result = await tryOriginalAddress(originalAddress) {
            // 验证原始地址结果的准确性
            if isResultAccurate(result, originalAddress: originalAddress) {
                // 检查是否缺少重要信息（门牌号）
                if let incompleteResult = checkForMissingHouseNumber(result, originalAddress: originalAddress) {
                    Logger.info("⚠️ 原始地址成功但缺少门牌号，继续尝试其他策略: \(originalAddress)", type: .location)
                    // 将不完整的结果保存为备选
                    let fallbackResult = incompleteResult

                    // 继续尝试简化门牌号策略
                    if let betterResult = await trySimplifiedHouseNumber(originalAddress) {
                        if isResultAccurate(betterResult, originalAddress: originalAddress) {
                            Logger.info("✅ 简化门牌号找到更好结果: \(originalAddress)", type: .location)
                            return enhanceResultWithOriginalDetails(betterResult, originalAddress: originalAddress)
                        }
                    }

                    // 如果简化策略也失败，返回原始结果
                    Logger.info("📍 使用原始结果（虽然缺少门牌号）: \(originalAddress)", type: .location)
                    return enhanceResultWithOriginalDetails(fallbackResult, originalAddress: originalAddress)
                }

                Logger.info("✅ 原始地址成功且准确: \(originalAddress)", type: .location)
                return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
            } else {
                Logger.warning("⚠️ 原始地址成功但不够准确，继续尝试其他策略: \(originalAddress)", type: .location)
            }
        }

        // 2. 轻微清理后重试
        if let result = await tryCleanedAddress(originalAddress) {
            Logger.info("✅ 清理后成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        // 3. 🎯 智能地址简化策略（新增）
        if let result = await trySimplifiedAddresses(originalAddress) {
            Logger.info("✅ 简化地址成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        // 4. 🏠 尝试移除公寓号（Apple策略：Apt, Unit, Room, Suite等会干扰地理编码）
        // 注意：如果前面已经尝试过公寓号移除策略，这里会跳过
        if !containsApartmentInfo(originalAddress) {
            if let result = await tryRemoveApartmentInfo(originalAddress) {
                if isResultAccurate(result, originalAddress: originalAddress) {
                    Logger.info("✅ 移除公寓号成功且准确: \(originalAddress)", type: .location)
                    return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
                } else {
                    Logger.warning("⚠️ 移除公寓号成功但不够准确: \(originalAddress)", type: .location)
                }
            }
        }

        // 5. 尝试简化门牌号（处理 145c -> 145 的情况）
        if let result = await trySimplifiedHouseNumber(originalAddress) {
            Logger.info("✅ 简化门牌号成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        // 6. 使用MKLocalSearch（更适合POI和模糊搜索）
        if let result = await tryLocalSearch(originalAddress) {
            Logger.info("✅ 本地搜索成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        // 7. 智能地址增强
        if let result = await tryEnhancedAddress(originalAddress) {
            Logger.info("✅ 增强地址成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        // 8. 最后的模糊匹配尝试
        if let result = await tryFuzzyMatching(originalAddress) {
            Logger.info("✅ 模糊匹配成功: \(originalAddress)", type: .location)
            return enhanceResultWithOriginalDetails(result, originalAddress: originalAddress)
        }

        Logger.warning("❌ 所有策略都失败: \(originalAddress)", type: .location)
        return GlobalGeocodingResult.failed(address: originalAddress, reason: "无法找到该地址")
    }

    /// 使用原始地址的详细信息增强结果
    private func enhanceResultWithOriginalDetails(_ result: GlobalGeocodingResult, originalAddress: String) -> GlobalGeocodingResult {
        switch result {
        case .success(let original, _, let coordinate, let placemark, let strategy, let confidence):
            // 尝试构建更好的格式化地址
            let enhancedAddress = buildEnhancedAddress(placemark: placemark, originalAddress: originalAddress)

            // 验证增强后的地址是否合理
            let finalConfidence = validateEnhancedAddress(
                enhancedAddress: enhancedAddress,
                originalAddress: originalAddress,
                placemark: placemark,
                baseConfidence: confidence
            )

            return GlobalGeocodingResult.success(
                originalAddress: original,
                formattedAddress: enhancedAddress,
                coordinate: coordinate,
                placemark: placemark,
                strategy: strategy,
                confidence: finalConfidence
            )
        case .failed:
            return result
        }
    }

    /// 验证增强后的地址合理性
    private func validateEnhancedAddress(
        enhancedAddress: String,
        originalAddress: String,
        placemark: CLPlacemark,
        baseConfidence: ConfidenceLevel
    ) -> ConfidenceLevel {

        // 🌏 地区特殊处理
        if isHongKongAddress(placemark) {
            return validateHongKongAddress(
                enhancedAddress: enhancedAddress,
                originalAddress: originalAddress,
                placemark: placemark,
                baseConfidence: baseConfidence
            )
        }

        // 🇺🇸 西方地址优化处理 - 为App Store审核优化
        if isWesternAddress(placemark) {
            return validateWesternAddress(
                enhancedAddress: enhancedAddress,
                originalAddress: originalAddress,
                placemark: placemark,
                baseConfidence: baseConfidence
            )
        }

        // 检查是否保留了重要的原始信息
        let originalComponents = extractAddressComponents(from: originalAddress)
        let enhancedComponents = extractAddressComponents(from: enhancedAddress)

        var preservationScore = 0

        // 检查重要组件是否被保留
        if let originalStreet = originalComponents.street,
           let enhancedStreet = enhancedComponents.street {
            // 改进的街道匹配逻辑，支持缩写形式
            if areStreetsMatching(originalStreet, enhancedStreet) {
                preservationScore += 2
            }
        }

        if let originalNumber = originalComponents.number,
           enhancedAddress.contains(originalNumber) {
            preservationScore += 1
        }

        // 检查是否有详细信息被正确保留
        if originalAddress.contains("楼") && enhancedAddress.contains("楼") {
            preservationScore += 1
        }

        if originalAddress.contains("室") && enhancedAddress.contains("室") {
            preservationScore += 1
        }

        // 根据保留程度调整置信度
        if preservationScore >= 4 {
            return baseConfidence
        } else if preservationScore >= 2 {
            return baseConfidence == .high ? .medium : baseConfidence
        } else {
            return .low
        }
    }

    /// 提取地址组件用于验证
    private func extractAddressComponents(from address: String) -> (street: String?, number: String?) {
        // 提取街道名称
        let streetPatterns = ["路", "街", "道", "大道", "avenue", "street", "road"]
        var street: String?

        for pattern in streetPatterns {
            if let range = address.range(of: pattern) {
                let beforePattern = String(address[..<range.upperBound])
                let words = beforePattern.components(separatedBy: .whitespacesAndNewlines)
                if let lastWord = words.last, lastWord.count > 1 {
                    street = lastWord
                    break
                }
            }
        }

        // 提取门牌号
        let numberRegex = try? NSRegularExpression(pattern: "\\d+", options: [])
        let range = NSRange(address.startIndex..<address.endIndex, in: address)
        let number = numberRegex?.firstMatch(in: address, options: [], range: range)
            .flatMap { Range($0.range, in: address) }
            .map { String(address[$0]) }

        return (street: street, number: number)
    }

    /// 检查两个街道名称是否匹配（支持缩写形式）
    private func areStreetsMatching(_ street1: String, _ street2: String) -> Bool {
        let s1 = street1.lowercased()
        let s2 = street2.lowercased()

        // 直接匹配
        if s1.contains(s2) || s2.contains(s1) {
            return true
        }

        // 街道类型缩写映射
        let streetAbbreviations = [
            "road": ["rd", "r"],
            "street": ["st", "str"],
            "avenue": ["ave", "av"],
            "boulevard": ["blvd", "bvd"],
            "drive": ["dr"],
            "lane": ["ln"],
            "court": ["ct"],
            "place": ["pl"],
            "circle": ["cir"],
            "way": ["wy"]
        ]

        // 检查是否是缩写匹配
        for (fullForm, abbreviations) in streetAbbreviations {
            // 检查 s1 是完整形式，s2 是缩写
            if s1.contains(fullForm) && abbreviations.contains(where: { s2.contains($0) }) {
                return true
            }
            // 检查 s2 是完整形式，s1 是缩写
            if s2.contains(fullForm) && abbreviations.contains(where: { s1.contains($0) }) {
                return true
            }
        }

        return false
    }

    /// 香港地址专用验证方法 - 考虑街道横跨多个地区的特殊性
    private func validateHongKongAddress(
        enhancedAddress: String,
        originalAddress: String,
        placemark: CLPlacemark,
        baseConfidence: ConfidenceLevel
    ) -> ConfidenceLevel {

        Logger.info("🇭🇰 香港地址验证: 原始(\(originalAddress)) -> 增强(\(enhancedAddress))", type: .location)

        var preservationScore = 0

        // 1. 检查门牌号是否保留（最重要）
        let originalComponents = extractAddressComponents(from: originalAddress)
        if let originalNumber = originalComponents.number,
           enhancedAddress.contains(originalNumber) {
            preservationScore += 3  // 门牌号匹配给高分
            Logger.info("✅ 香港地址门牌号匹配: \(originalNumber)", type: .location)
        }

        // 2. 检查街道名称（放宽标准，因为香港街道横跨多个地区）
        if let originalStreet = originalComponents.street,
           let thoroughfare = placemark.thoroughfare {

            // 香港街道名称匹配逻辑
            if isHongKongStreetMatching(originalStreet, thoroughfare) {
                preservationScore += 2
                Logger.info("✅ 香港街道匹配: \(originalStreet) ≈ \(thoroughfare)", type: .location)
            } else {
                Logger.info("⚠️ 香港街道不匹配: \(originalStreet) ≠ \(thoroughfare)", type: .location)
            }
        }

        // 3. 检查详细信息保留（楼层、房间等）
        if originalAddress.contains("楼") && enhancedAddress.contains("楼") {
            preservationScore += 1
        }

        if originalAddress.contains("室") && enhancedAddress.contains("室") {
            preservationScore += 1
        }

        if originalAddress.contains("座") && enhancedAddress.contains("座") {
            preservationScore += 1
        }

        Logger.info("🇭🇰 香港地址保留分数: \(preservationScore)", type: .location)

        // 香港地址的宽松评分标准
        if preservationScore >= 3 {
            // 门牌号匹配就基本可信
            return baseConfidence
        } else if preservationScore >= 1 {
            // 有部分匹配，降级但不完全拒绝
            return baseConfidence == .high ? .medium : baseConfidence
        } else {
            // 完全不匹配才设为低置信度
            return .low
        }
    }

    /// 香港街道名称匹配逻辑 - 考虑街道横跨多个地区
    private func isHongKongStreetMatching(_ originalStreet: String, _ placemarkStreet: String) -> Bool {
        let original = originalStreet.lowercased()
        let placemark = placemarkStreet.lowercased()

        // 直接匹配
        if original.contains(placemark) || placemark.contains(original) {
            return true
        }

        // 香港常见街道名称变体
        let hongKongStreetVariants = [
            "nathan": ["弥敦", "弥敦道"],
            "hennessy": ["轩尼诗", "轩尼诗道"],
            "queens": ["皇后", "皇后大道"],
            "des voeux": ["德辅", "德辅道"],
            "canton": ["广东", "广东道"],
            "chatham": ["漆咸", "漆咸道"],
            "austin": ["柯士甸", "柯士甸道"],
            "jordan": ["佐敦", "佐敦道"]
        ]

        // 检查是否是已知的街道变体
        for (english, chinese) in hongKongStreetVariants {
            if (original.contains(english) && chinese.contains(where: { placemark.contains($0) })) ||
               (placemark.contains(english) && chinese.contains(where: { original.contains($0) })) {
                return true
            }
        }

        // 检查是否包含相同的核心街道名称（去除"道"、"街"等后缀）
        let originalCore = original.replacingOccurrences(of: "道$", with: "", options: .regularExpression)
                                  .replacingOccurrences(of: "街$", with: "", options: .regularExpression)
                                  .replacingOccurrences(of: "路$", with: "", options: .regularExpression)

        let placemarkCore = placemark.replacingOccurrences(of: "道$", with: "", options: .regularExpression)
                                    .replacingOccurrences(of: "街$", with: "", options: .regularExpression)
                                    .replacingOccurrences(of: "路$", with: "", options: .regularExpression)

        if originalCore.count > 2 && placemarkCore.count > 2 &&
           (originalCore.contains(placemarkCore) || placemarkCore.contains(originalCore)) {
            return true
        }

        return false
    }

    /// 西方地址专用验证方法 - 为App Store审核优化
    private func validateWesternAddress(
        enhancedAddress: String,
        originalAddress: String,
        placemark: CLPlacemark,
        baseConfidence: ConfidenceLevel
    ) -> ConfidenceLevel {

        Logger.info("🇺🇸 西方地址验证: 原始(\(originalAddress)) -> 增强(\(enhancedAddress))", type: .location)

        var preservationScore = 0

        // 1. 检查门牌号是否保留（最重要）
        let originalComponents = extractAddressComponents(from: originalAddress)
        if let originalNumber = originalComponents.number,
           enhancedAddress.contains(originalNumber) {
            preservationScore += 3  // 门牌号匹配给高分
            Logger.info("✅ 西方地址门牌号匹配: \(originalNumber)", type: .location)
        }

        // 2. 检查街道名称（西方地址通常比较标准）
        if let originalStreet = originalComponents.street,
           let thoroughfare = placemark.thoroughfare {

            // 西方街道名称匹配逻辑（更宽松）
            if isWesternStreetMatching(originalStreet, thoroughfare) {
                preservationScore += 2
                Logger.info("✅ 西方街道匹配: \(originalStreet) ≈ \(thoroughfare)", type: .location)
            } else {
                Logger.info("⚠️ 西方街道不匹配: \(originalStreet) ≠ \(thoroughfare)", type: .location)
            }
        }

        // 3. 检查城市/州匹配（西方地址的重要组件）
        if let locality = placemark.locality {
            let originalLower = originalAddress.lowercased()
            let localityLower = locality.lowercased()

            if originalLower.contains(localityLower) {
                preservationScore += 2
                Logger.info("✅ 城市匹配: \(locality)", type: .location)
            } else {
                // 🚨 城市不匹配是严重问题，大幅扣分
                preservationScore -= 3
                Logger.warning("🚨 城市不匹配: 原始包含'\(extractCityFromAddress(originalAddress))', 返回'\(locality)'", type: .location)
            }
        }

        if let administrativeArea = placemark.administrativeArea,
           originalAddress.lowercased().contains(administrativeArea.lowercased()) {
            preservationScore += 1
            Logger.info("✅ 州/省匹配: \(administrativeArea)", type: .location)
        }

        Logger.info("🇺🇸 西方地址保留分数: \(preservationScore)", type: .location)

        // 🚨 修复：更严格的西方地址评分标准
        if preservationScore >= 4 {
            // 高匹配度，保持原置信度
            return baseConfidence
        } else if preservationScore >= 1 {
            // 中等匹配度，轻微降级
            return baseConfidence == .high ? .medium : baseConfidence
        } else {
            // 🚨 低匹配度，特别是城市不匹配时，应该拒绝
            Logger.warning("🚨 西方地址匹配度过低，拒绝结果", type: .location)
            return .low
        }
    }

    /// 西方街道名称匹配逻辑
    private func isWesternStreetMatching(_ originalStreet: String, _ placemarkStreet: String) -> Bool {
        let original = originalStreet.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        let placemark = placemarkStreet.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        Logger.info("🔍 西方街道匹配检查: '\(original)' vs '\(placemark)'", type: .location)

        // 🚨 修复：更严格的直接匹配逻辑
        // 只有当街道名称的核心部分真正匹配时才认为匹配
        if original == placemark {
            Logger.info("✅ 完全匹配", type: .location)
            return true
        }

        // 检查街道缩写匹配
        let streetAbbreviations = [
            "street": ["st", "str"],
            "avenue": ["ave", "av"],
            "road": ["rd"],
            "drive": ["dr"],
            "boulevard": ["blvd", "boul"],
            "lane": ["ln"],
            "court": ["ct"],
            "place": ["pl"],
            "circle": ["cir"],
            "way": ["wy"]
        ]

        // 🚨 修复：更严格的缩写匹配逻辑
        // 提取街道名称的核心部分（去除类型后缀）
        let originalCore = extractStreetCore(original)
        let placemarkCore = extractStreetCore(placemark)

        Logger.info("🔍 街道核心部分: '\(originalCore)' vs '\(placemarkCore)'", type: .location)

        // 核心部分必须匹配
        if originalCore.isEmpty || placemarkCore.isEmpty || originalCore != placemarkCore {
            Logger.info("❌ 街道核心部分不匹配", type: .location)
            return false
        }

        // 核心部分匹配后，检查类型缩写
        for (fullForm, abbreviations) in streetAbbreviations {
            let originalHasFullForm = original.contains(fullForm)
            let originalHasAbbrev = abbreviations.contains { original.contains($0) }
            let placemarkHasFullForm = placemark.contains(fullForm)
            let placemarkHasAbbrev = abbreviations.contains { placemark.contains($0) }

            if (originalHasFullForm && placemarkHasAbbrev) || (originalHasAbbrev && placemarkHasFullForm) {
                Logger.info("✅ 缩写匹配: \(fullForm)", type: .location)
                return true
            }
        }

        Logger.info("❌ 街道名称完全不匹配", type: .location)
        return false
    }

    /// 提取街道名称的核心部分（去除类型后缀）
    private func extractStreetCore(_ streetName: String) -> String {
        let core = streetName
            .replacingOccurrences(of: "\\b(street|st|avenue|ave|road|rd|drive|dr|boulevard|blvd|lane|ln|court|ct|place|pl|circle|cir|way|wy)\\b", with: "", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)
        return core
    }

    /// 从地址中提取城市名称
    private func extractCityFromAddress(_ address: String) -> String {
        // 简单的城市提取逻辑：通常在第一个逗号后
        let components = address.components(separatedBy: ",")
        if components.count >= 2 {
            return components[1].trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return ""
    }

    /// 构建增强的地址格式
    private func buildEnhancedAddress(placemark: CLPlacemark, originalAddress: String) -> String {
        // 检测是否是香港地址
        if isHongKongAddress(placemark) {
            return buildHongKongEnhancedAddress(placemark: placemark, originalAddress: originalAddress)
        }

        // 检测是否是中国大陆地址
        if isMainlandChinaAddress(placemark) {
            return buildMainlandChinaEnhancedAddress(placemark: placemark, originalAddress: originalAddress)
        }

        // 其他地区使用标准格式
        return formatStandardAddress(placemark)
    }

    /// 构建香港增强地址
    private func buildHongKongEnhancedAddress(placemark: CLPlacemark, originalAddress: String) -> String {
        // 基础地址组件
        var baseComponents: [String] = []

        // 提取区域信息
        if let locality = placemark.locality {
            baseComponents.append(locality)
        }

        // 提取街道信息
        if let thoroughfare = placemark.thoroughfare {
            baseComponents.append(thoroughfare)
        }

        // 提取门牌号
        if let subThoroughfare = placemark.subThoroughfare {
            baseComponents.append("\(subThoroughfare)號")
        }

        let baseAddress = baseComponents.joined(separator: "")

        // 尝试从原始地址中提取详细信息（楼层、房间等）
        let detailInfo = extractDetailedInfo(from: originalAddress, baseAddress: baseAddress)

        if !detailInfo.isEmpty {
            return baseAddress + detailInfo
        }

        return baseAddress
    }

    /// 构建中国大陆增强地址
    private func buildMainlandChinaEnhancedAddress(placemark: CLPlacemark, originalAddress: String) -> String {
        // 基础地址组件
        var baseComponents: [String] = []

        if let administrativeArea = placemark.administrativeArea {
            baseComponents.append(administrativeArea)
        }

        if let locality = placemark.locality {
            baseComponents.append(locality)
        }

        if let thoroughfare = placemark.thoroughfare {
            baseComponents.append(thoroughfare)
        }

        if let subThoroughfare = placemark.subThoroughfare {
            baseComponents.append("\(subThoroughfare)号")
        }

        let baseAddress = baseComponents.joined(separator: "")

        // 尝试从原始地址中提取详细信息
        let detailInfo = extractDetailedInfo(from: originalAddress, baseAddress: baseAddress)

        if !detailInfo.isEmpty {
            return baseAddress + detailInfo
        }

        return baseAddress
    }

    /// 从原始地址中提取详细信息（楼层、房间等）
    private func extractDetailedInfo(from originalAddress: String, baseAddress: String) -> String {
        // 寻找完整的详细信息模式：座+楼+室
        let fullPattern = "(\\d+座\\d+楼[A-Z]室)"

        if let regex = try? NSRegularExpression(pattern: fullPattern, options: []),
           let match = regex.firstMatch(in: originalAddress, options: [], range: NSRange(originalAddress.startIndex..<originalAddress.endIndex, in: originalAddress)),
           let matchRange = Range(match.range, in: originalAddress) {
            return String(originalAddress[matchRange])
        }

        // 如果没有完整模式，尝试提取部分信息
        var detailComponents: [String] = []

        // 提取座号
        if let seatMatch = extractFirstMatch(from: originalAddress, pattern: "\\d+座") {
            detailComponents.append(seatMatch)
        }

        // 提取楼层
        if let floorMatch = extractFirstMatch(from: originalAddress, pattern: "\\d+楼") {
            detailComponents.append(floorMatch)
        }

        // 提取房间
        if let roomMatch = extractFirstMatch(from: originalAddress, pattern: "[A-Z]室") {
            detailComponents.append(roomMatch)
        }

        return detailComponents.joined(separator: "")
    }

    /// 提取第一个匹配的模式
    private func extractFirstMatch(from text: String, pattern: String) -> String? {
        guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
            return nil
        }

        let range = NSRange(text.startIndex..<text.endIndex, in: text)

        if let match = regex.firstMatch(in: text, options: [], range: range),
           let matchRange = Range(match.range, in: text) {
            return String(text[matchRange])
        }

        return nil
    }

    // MARK: - 处理策略

    /// 策略1: 尝试原始地址（不做任何修改）
    private func tryOriginalAddress(_ address: String) async -> GlobalGeocodingResult? {
        return await geocodeWithCLGeocoder(address, strategy: "原始地址")
    }

    /// 策略2: 轻微清理（只去除多余空格和特殊字符）
    private func tryCleanedAddress(_ address: String) async -> GlobalGeocodingResult? {
        let cleaned = address
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .replacingOccurrences(of: "[\\n\\r\\t]", with: " ", options: .regularExpression)

        guard cleaned != address else { return nil }
        return await geocodeWithCLGeocoder(cleaned, strategy: "清理地址")
    }

    /// 策略3: 智能地址简化（新增）- 增强验证
    private func trySimplifiedAddresses(_ address: String) async -> GlobalGeocodingResult? {
        Logger.info("🎯 开始尝试简化地址策略: \(address)", type: .location)

        // 生成简化候选地址
        let candidates = AddressSimplifier.generateRecognitionCandidates(address)

        // 跳过原始地址（已经在前面尝试过）
        let simplifiedCandidates = Array(candidates.dropFirst())

        for (index, candidate) in simplifiedCandidates.enumerated() {
            Logger.info("🎯 尝试简化候选地址 \(index + 1): \(candidate)", type: .location)

            if let result = await geocodeWithCLGeocoder(candidate, strategy: "简化地址-\(index + 1)") {
                // 🚨 关键修复：验证简化地址结果是否与原始地址匹配
                if isResultAccurate(result, originalAddress: address) {
                    Logger.info("✅ 简化地址成功且准确: \(candidate)", type: .location)
                    return result
                } else {
                    Logger.warning("⚠️ 简化地址成功但不准确，跳过: \(candidate)", type: .location)
                    // 继续尝试下一个候选地址
                    continue
                }
            }
        }

        Logger.info("❌ 所有简化地址都失败或不准确", type: .location)
        return nil
    }

    /// 策略4: 🏠 尝试移除公寓号（Apple策略）
    private func tryRemoveApartmentInfo(_ address: String) async -> GlobalGeocodingResult? {
        Logger.info("🏠 开始尝试移除公寓号策略: \(address)", type: .location)

        // 检查是否包含公寓信息
        let apartmentPatterns = [
            "\\b(apt|apartment)\\s*[#:]?\\s*\\w+",
            "\\b(unit|ste|suite)\\s*[#:]?\\s*\\w+",
            "\\b(room|rm)\\s*[#:]?\\s*\\w+",
            "\\b#\\s*\\w+"
        ]

        var cleanedAddress = address
        var hasApartmentInfo = false

        for pattern in apartmentPatterns {
            let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            let range = NSRange(cleanedAddress.startIndex..<cleanedAddress.endIndex, in: cleanedAddress)

            if let match = regex?.firstMatch(in: cleanedAddress, options: [], range: range) {
                hasApartmentInfo = true
                if let matchRange = Range(match.range, in: cleanedAddress) {
                    let removedPart = String(cleanedAddress[matchRange])
                    cleanedAddress = cleanedAddress.replacingCharacters(in: matchRange, with: "")
                    Logger.info("🏠 移除公寓信息: '\(removedPart)'", type: .location)
                }
            }
        }

        if !hasApartmentInfo {
            Logger.info("🏠 地址不包含公寓信息，跳过此策略", type: .location)
            return nil
        }

        // 清理多余的空格和逗号
        cleanedAddress = cleanedAddress
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .replacingOccurrences(of: ",\\s*,", with: ",", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)

        Logger.info("🏠 移除公寓号后的地址: \(cleanedAddress)", type: .location)

        if let result = await geocodeWithCLGeocoder(cleanedAddress, strategy: "移除公寓号") {
            Logger.info("✅ 移除公寓号成功: \(cleanedAddress)", type: .location)
            return result
        }

        Logger.info("❌ 移除公寓号失败", type: .location)
        return nil
    }

    /// 策略5: 尝试简化门牌号（处理 145c -> 145 的情况）
    private func trySimplifiedHouseNumber(_ address: String) async -> GlobalGeocodingResult? {
        Logger.info("🏠 开始尝试简化门牌号策略: \(address)", type: .location)

        // 生成简化门牌号的候选地址
        let candidates = generateSimplifiedHouseNumberCandidates(address)

        for (index, candidate) in candidates.enumerated() {
            Logger.info("🏠 尝试简化门牌号候选 \(index + 1): \(candidate)", type: .location)

            if let result = await geocodeWithCLGeocoder(candidate, strategy: "简化门牌号-\(index + 1)") {
                Logger.info("✅ 简化门牌号成功: \(candidate)", type: .location)
                return result
            }
        }

        Logger.info("❌ 所有简化门牌号都失败", type: .location)
        return nil
    }

    /// 🎯 策略0: 使用全球MKLocalSearch（模拟Apple Maps界面搜索）
    private func tryGlobalMKLocalSearch(_ address: String) async -> GlobalGeocodingResult? {
        guard !address.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            Logger.warning("🚫 全球MKLocalSearch: 地址为空", type: .location)
            return nil
        }

        // 🚦 等待速率限制
        await waitForRateLimit()

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = address
        request.resultTypes = [.address] // 只搜索地址，不包括POI

        // 🌍 设置全球搜索区域（和SimpleAddressInputView相同）
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        request.region = globalRegion

        let search = MKLocalSearch(request: request)

        // 添加超时保护
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 10_000_000_000) // 10秒超时
            search.cancel()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            let response = try await search.start()

            guard let mapItem = response.mapItems.first else {
                Logger.warning("🚫 全球MKLocalSearch: 未找到搜索结果", type: .location)
                return nil
            }

            let coordinate = mapItem.placemark.coordinate

            // 验证坐标有效性
            guard isValidCoordinate(coordinate) else {
                Logger.warning("🚫 全球MKLocalSearch: 坐标无效 (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                return nil
            }

            // 🎯 使用反向地理编码获得英文地址格式
            let englishPlacemark = await getEnglishPlacemark(for: coordinate)
            let finalPlacemark = englishPlacemark ?? mapItem.placemark

            // 🎯 对全球搜索使用更宽松的置信度检查
            let confidence = evaluateAddressConfidence(placemark: finalPlacemark, originalAddress: address)

            // 全球搜索允许medium置信度，因为它模拟Apple Maps界面
            guard confidence != .low else {
                Logger.warning("🚫 全球MKLocalSearch置信度过低，跳过: \(formatPlacemark(finalPlacemark))", type: .location)
                return nil
            }

            let formattedAddress = formatPlacemark(finalPlacemark)

            Logger.info("✅ 全球MKLocalSearch成功: \(address) -> \(formattedAddress)", type: .location)
            Logger.info("📍 坐标: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
            Logger.info("🎯 置信度: \(confidence)", type: .location)

            return GlobalGeocodingResult.success(
                originalAddress: address,
                formattedAddress: formattedAddress,
                coordinate: coordinate,
                placemark: finalPlacemark,
                strategy: "全球MKLocalSearch",
                confidence: confidence
            )

        } catch {
            Logger.warning("🚫 全球MKLocalSearch失败: \(error.localizedDescription)", type: .location)
            return nil
        }
    }

    /// 策略6: 使用MKLocalSearch（适合POI和模糊搜索）
    private func tryLocalSearch(_ address: String) async -> GlobalGeocodingResult? {
        guard !address.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            Logger.warning("🚫 MKLocalSearch: 地址为空", type: .location)
            return nil
        }

        // 🚦 等待速率限制
        await waitForRateLimit()

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = address
        request.resultTypes = [.address, .pointOfInterest] // 限制搜索类型

        let search = MKLocalSearch(request: request)

        // 添加超时保护
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 8_000_000_000) // 8秒超时（比CLGeocoder短一些）
            search.cancel()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            let response = try await search.start()

            guard let mapItem = response.mapItems.first else {
                Logger.warning("🚫 MKLocalSearch: 未找到搜索结果", type: .location)
                return nil
            }

            let coordinate = mapItem.placemark.coordinate

            // 验证坐标有效性
            guard isValidCoordinate(coordinate) else {
                Logger.warning("🚫 MKLocalSearch: 坐标无效 (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                return nil
            }

            // 验证MKLocalSearch结果的置信度
            let confidence = evaluateAddressConfidence(placemark: mapItem.placemark, originalAddress: address)

            // MKLocalSearch通常用于POI搜索，所以稍微降低要求
            guard confidence != .low else {
                Logger.warning("🚫 MKLocalSearch置信度过低，跳过: \(formatPlacemark(mapItem.placemark))", type: .location)
                return nil
            }

            let formattedAddress = formatPlacemark(mapItem.placemark)

            Logger.info("🔍 MKLocalSearch成功: \(formattedAddress) (置信度: \(confidence))", type: .location)

            return GlobalGeocodingResult.success(
                originalAddress: address,
                formattedAddress: formattedAddress,
                coordinate: coordinate,
                placemark: mapItem.placemark,
                strategy: "MKLocalSearch",
                confidence: confidence
            )

        } catch {
            if error is CancellationError {
                Logger.warning("🚫 MKLocalSearch: 请求超时", type: .location)
            } else {
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    Logger.error("🚨 MKLocalSearch: Apple Maps API限制触发", type: .location)
                    await rateLimiter.emergencyWait()
                } else if nsError.domain == "MKErrorDomain" && nsError.code == 4 {
                    Logger.warning("🚫 MKLocalSearch: Apple Maps API限制 (MKErrorDomain 4)", type: .location)
                    await rateLimiter.emergencyWait()
                } else {
                    Logger.warning("🚫 MKLocalSearch失败: \(error.localizedDescription)", type: .location)
                }
            }
        }

        return nil
    }

    /// 策略7: 智能地址增强
    private func tryEnhancedAddress(_ address: String) async -> GlobalGeocodingResult? {
        let enhancedAddresses = generateEnhancedAddresses(address)

        for enhanced in enhancedAddresses {
            if let result = await geocodeWithCLGeocoder(enhanced, strategy: "增强地址") {
                return result
            }
        }

        return nil
    }

    /// 策略8: 模糊匹配
    private func tryFuzzyMatching(_ address: String) async -> GlobalGeocodingResult? {
        let fuzzyAddresses = generateFuzzyAddresses(address)

        for fuzzy in fuzzyAddresses {
            if let result = await geocodeWithCLGeocoder(fuzzy, strategy: "模糊匹配") {
                return result
            }
        }

        return nil
    }

    // MARK: - 英文地址处理

    /// 通过反向地理编码获得英文地标
    private func getEnglishPlacemark(for coordinate: CLLocationCoordinate2D) async -> CLPlacemark? {
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        // 添加超时保护
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 5_000_000_000) // 5秒超时
            geocoder.cancelGeocode()
        }

        defer {
            timeoutTask.cancel()
        }

        // 🎯 强制使用英文语言环境进行反向地理编码
        let englishLocales = [
            Locale(identifier: "en_US"),
            Locale(identifier: "en_GB"),
            Locale(identifier: "en")
        ]

        for locale in englishLocales {
            do {
                let placemarks = try await geocoder.reverseGeocodeLocation(
                    location,
                    preferredLocale: locale
                )

                if let placemark = placemarks.first,
                   let locality = placemark.locality,
                   !AddressStandardizer.containsChineseCharacters(locality) {
                    Logger.info("✅ 反向地理编码获得英文地址: \(locale.identifier)", type: .location)
                    return placemark
                }
            } catch {
                Logger.debug("⚠️ 反向地理编码失败 (\(locale.identifier)): \(error.localizedDescription)", type: .location)
            }
        }

        Logger.warning("⚠️ 无法获得英文反向地理编码结果", type: .location)
        return nil
    }

    // MARK: - 核心地理编码方法

    /// 使用CLGeocoder进行地理编码
    private func geocodeWithCLGeocoder(_ address: String, strategy: String) async -> GlobalGeocodingResult? {
        // 🚦 等待速率限制
        await waitForRateLimit()

        let geocoder = CLGeocoder()

        // 添加超时保护 - 优化为5秒
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 5_000_000_000) // 5秒超时
            geocoder.cancelGeocode()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            // 验证输入地址
            guard !address.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                Logger.warning("🚫 \(strategy): 地址为空", type: .location)
                return nil
            }

            // 🎯 强制使用英文语言环境，避免中文本地化
            // 设置多个英文语言环境作为备选，确保返回英文地址
            let englishLocales = [
                Locale(identifier: "en_US"),
                Locale(identifier: "en_GB"),
                Locale(identifier: "en_AU"),
                Locale(identifier: "en")
            ]

            var placemarks: [CLPlacemark]?
            var lastError: Error?

            // 尝试不同的英文语言环境，直到获得英文结果
            for locale in englishLocales {
                do {
                    let results = try await geocoder.geocodeAddressString(
                        address,
                        in: nil,
                        preferredLocale: locale
                    )

                    // 检查返回的地址是否为英文
                    if let firstResult = results.first,
                       let locality = firstResult.locality,
                       !AddressStandardizer.containsChineseCharacters(locality) {
                        placemarks = results
                        Logger.info("✅ 使用 \(locale.identifier) 获得英文地址结果", type: .location)
                        break
                    } else {
                        Logger.info("⚠️ \(locale.identifier) 返回本地化地址，尝试下一个语言环境", type: .location)
                    }
                } catch {
                    lastError = error
                    Logger.info("⚠️ \(locale.identifier) 地理编码失败: \(error.localizedDescription)", type: .location)
                }
            }

            // 如果所有语言环境都失败，抛出最后一个错误
            guard let finalPlacemarks = placemarks else {
                throw lastError ?? NSError(domain: "GeocodingError", code: -1, userInfo: [NSLocalizedDescriptionKey: "所有语言环境都失败"])
            }

            guard let placemark = finalPlacemarks.first,
                  let location = placemark.location else {
                Logger.warning("🚫 \(strategy): 未获得有效位置信息", type: .location)
                return nil
            }

            let coordinate = location.coordinate

            // 验证坐标有效性
            guard isValidCoordinate(coordinate) else {
                Logger.warning("🚫 \(strategy): 坐标无效 (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                return nil
            }

            // 验证地址的可信度
            let confidence = evaluateAddressConfidence(placemark: placemark, originalAddress: address)

            // 只接受中等以上置信度的结果
            guard confidence != .low else {
                Logger.warning("🚫 \(strategy)置信度过低，跳过: \(formatPlacemark(placemark))", type: .location)
                return nil
            }

            let formattedAddress = formatPlacemark(placemark)

            Logger.info("📍 \(strategy)成功: \(formattedAddress) (置信度: \(confidence))", type: .location)
            Logger.debug("📍 获得坐标: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)

            return GlobalGeocodingResult.success(
                originalAddress: address,
                formattedAddress: formattedAddress,
                coordinate: coordinate,
                placemark: placemark,
                strategy: strategy,
                confidence: confidence
            )

        } catch {
            if error is CancellationError {
                Logger.warning("🚫 \(strategy): 请求超时", type: .location)
            } else {
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    Logger.error("🚨 \(strategy): Apple Maps API限制触发 - \(error.localizedDescription)", type: .location)
                    // 🎯 智能解析等待时间
                    let waitTime = await rateLimiter.parseWaitTimeFromError(error)
                    await rateLimiter.emergencyWait(customWaitTime: waitTime)
                } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                    Logger.warning("🚫 \(strategy): Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)", type: .location)
                    // 🎯 智能解析等待时间
                    let waitTime = await rateLimiter.parseWaitTimeFromError(error)
                    await rateLimiter.emergencyWait(customWaitTime: waitTime)
                } else {
                    Logger.warning("🚫 \(strategy)失败: \(error.localizedDescription)", type: .location)
                }
            }
        }

        return nil
    }

    /// 评估地址置信度
    private func evaluateAddressConfidence(placemark: CLPlacemark, originalAddress: String) -> ConfidenceLevel {
        var score = 0

        // 检查是否有详细的地址组件
        if placemark.subThoroughfare != nil { score += 1 }  // 门牌号
        if placemark.thoroughfare != nil { score += 1 }     // 街道
        if placemark.locality != nil { score += 1 }         // 城市/区域
        if placemark.administrativeArea != nil { score += 1 } // 省/州
        if placemark.country != nil { score += 1 }          // 国家

        // 🇺🇸 西方地址优化：给予额外基础分
        if isWesternAddress(placemark) {
            score += 2  // 西方地址额外基础分，为App Store审核优化
            Logger.debug("🇺🇸 西方地址额外基础分: +2", type: .location) // 改为debug级别
        }

        // 检查地址组件是否与原始输入匹配
        let originalLower = originalAddress.lowercased()
        var matchScore = 0

        if let thoroughfare = placemark.thoroughfare?.lowercased(),
           originalLower.contains(thoroughfare) {
            matchScore += 2
        }

        if let locality = placemark.locality?.lowercased(),
           originalLower.contains(locality) {
            matchScore += 2
        }

        // 严格检查门牌号匹配
        if let subThoroughfare = placemark.subThoroughfare {
            Logger.debug("🔍 门牌号检查: 原始地址包含'\(subThoroughfare)'? \(originalAddress.contains(subThoroughfare))", type: .location) // 改为debug级别

            if originalAddress.contains(subThoroughfare) {
                matchScore += 3  // 精确匹配给高分
                Logger.debug("✅ 门牌号精确匹配: \(subThoroughfare)", type: .location) // 改为debug级别
            } else {
                // 检查是否是相近的门牌号（可能的推测）
                let originalNumbers = extractNumbers(from: originalAddress)
                Logger.info("🔍 提取的门牌号: 原始(\(originalNumbers)) vs 返回(\(subThoroughfare))", type: .location)

                if let placemarkNumber = Int(subThoroughfare),
                   let originalNumber = originalNumbers.first {

                    let difference = abs(placemarkNumber - originalNumber)
                    Logger.info("📊 门牌号差异计算: |\(originalNumber) - \(placemarkNumber)| = \(difference)", type: .location)

                    if difference == 0 {
                        matchScore += 3  // 完全匹配
                        Logger.info("✅ 门牌号完全匹配: \(originalNumber)", type: .location)
                    } else if difference <= 2 {
                        matchScore += 2  // 非常接近，可能是偶数/奇数问题
                        Logger.warning("⚠️ 门牌号轻微差异(\(difference)): 原始(\(originalNumber)) vs 返回(\(placemarkNumber))", type: .location)
                    } else if difference <= 10 {
                        matchScore += 1  // 相近门牌号，可能存在
                        Logger.warning("⚠️ 门牌号不完全匹配(\(difference)): 原始(\(originalNumber)) vs 返回(\(placemarkNumber))", type: .location)
                    } else {
                        // 🚨 门牌号差异过大，可能是Apple推测的地址
                        matchScore -= 3  // 大幅扣分
                        Logger.warning("🚨 门牌号差异过大(\(difference))，可能不存在: 原始(\(originalNumber)) vs 返回(\(placemarkNumber))", type: .location)
                        Logger.warning("🚨 这可能导致送货错误，建议用户核对地址", type: .location)
                        Logger.warning("🚨 原始地址: \(originalAddress)", type: .location)
                        Logger.warning("🚨 返回地址: \(placemark.name ?? "未知")", type: .location)
                    }
                } else {
                    matchScore -= 2  // 无法解析门牌号，扣分
                    Logger.warning("❌ 无法解析门牌号: 原始(\(originalNumbers)) vs 返回(\(subThoroughfare))", type: .location)
                }
            }
        } else {
            Logger.warning("⚠️ Apple Maps返回的地址没有门牌号信息", type: .location)
        }

        // 检查是否包含重要的地标信息
        let originalKeywords = extractKeywords(from: originalAddress)
        let placemarkName = placemark.name?.lowercased() ?? ""

        for keyword in originalKeywords {
            if placemarkName.contains(keyword) {
                matchScore += 1
            }
        }

        // 综合评分
        let totalScore = score + matchScore

        Logger.debug("📊 地址置信度评估: 基础分(\(score)) + 匹配分(\(matchScore)) = 总分(\(totalScore))", type: .location) // 改为debug级别

        // 🚨 特别检查：如果门牌号差异过大，强制降低置信度
        let originalNumbers = extractNumbers(from: originalAddress)
        if let subThoroughfare = placemark.subThoroughfare,
           let placemarkNumber = Int(subThoroughfare),
           let originalNumber = originalNumbers.first {

            let difference = abs(placemarkNumber - originalNumber)

            // 🇺🇸 西方地址更宽松的门牌号检查
            let threshold = isWesternAddress(placemark) ? 20 : 10  // 西方地址允许更大差异

            if difference > threshold {
                Logger.warning("🚨 门牌号差异过大(\(difference))，强制设为低置信度", type: .location)
                Logger.warning("🚨 强制低置信度原因: 原始(\(originalNumber)) vs 返回(\(placemarkNumber))", type: .location)
                Logger.warning("🚨 使用阈值: \(threshold) (西方地址: \(isWesternAddress(placemark)))", type: .location)
                return .low
            }
        }

        // 根据总分确定置信度
        let confidence: ConfidenceLevel

        // 🇺🇸 西方地址使用更宽松的评分标准
        if isWesternAddress(placemark) {
            if totalScore >= 6 {  // 西方地址降低高置信度要求
                confidence = .high
                Logger.debug("✅ 最终置信度: 高 (西方地址总分: \(totalScore) >= 6)", type: .location) // 改为debug级别
            } else if totalScore >= 3 {  // 西方地址降低中置信度要求
                confidence = .medium
                Logger.debug("⚠️ 最终置信度: 中 (西方地址总分: \(totalScore) >= 3)", type: .location) // 改为debug级别
            } else {
                confidence = .low
                Logger.warning("🚨  最终置信度: 低 (西方地址总分: \(totalScore) < 3)", type: .location)
            }
        } else {
            // 非西方地址使用原有标准
            if totalScore >= 8 {
                confidence = .high
                Logger.info("✅ 最终置信度: 高 (总分: \(totalScore) >= 8)", type: .location)
            } else if totalScore >= 5 {
                confidence = .medium
                Logger.info("⚠️ 最终置信度: 中 (总分: \(totalScore) >= 5)", type: .location)
            } else {
                confidence = .low
                Logger.warning("🚨  最终置信度: 低 (总分: \(totalScore) < 5)", type: .location)
            }
        }

        return confidence
    }

    /// 从地址中提取数字，智能处理复合门牌号格式
    private func extractNumbers(from address: String) -> [Int] {
        // 首先尝试提取主要门牌号（处理 23/567 格式）
        if let mainNumber = extractMainStreetNumber(from: address) {
            Logger.info("🔍 检测到复合门牌号格式，提取主要号码: \(mainNumber)", type: .location)
            if let number = Int(mainNumber) {
                return [number]
            }
            // 如果主要号码是范围格式（如 560-567），提取第一个数字
            if mainNumber.contains("-") {
                let parts = mainNumber.components(separatedBy: "-")
                if let firstPart = parts.first?.trimmingCharacters(in: .whitespacesAndNewlines),
                   let number = Int(firstPart) {
                    Logger.info("🔍 从范围地址提取起始号码: \(number)", type: .location)
                    return [number]
                }
            }
        }

        // 如果没有复合格式，使用原有逻辑提取所有数字
        let regex = try? NSRegularExpression(pattern: "\\d+", options: [])
        let range = NSRange(address.startIndex..<address.endIndex, in: address)

        guard let regex = regex else { return [] }

        let matches = regex.matches(in: address, options: [], range: range)
        return matches.compactMap { match in
            guard let range = Range(match.range, in: address) else { return nil }
            return Int(String(address[range]))
        }
    }

    /// 提取主要街道号码 (从 "23/567" 提取 "567", 从 "M104/25" 提取 "25", 从 "23/560-567" 提取 "560-567")
    private func extractMainStreetNumber(from address: String) -> String? {
        // 查找包含斜杠的门牌号模式，支持字母前缀（如 M104/25）
        let pattern = "\\b[A-Za-z]*\\d+/([\\d-]+)\\b"

        guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
            return nil
        }

        let range = NSRange(address.startIndex..<address.endIndex, in: address)

        if let match = regex.firstMatch(in: address, options: [], range: range),
           let mainNumberRange = Range(match.range(at: 1), in: address) {
            let mainNumber = String(address[mainNumberRange])
            Logger.info("🔍 从复合门牌号 '\(address)' 提取主要号码: '\(mainNumber)'", type: .location)
            return mainNumber
        }

        return nil
    }

    /// 提取地址中的关键词
    private func extractKeywords(from address: String) -> [String] {
        let keywords = ["广场", "大厦", "中心", "商场", "大楼", "plaza", "center", "mall", "building"]
        return keywords.filter { address.lowercased().contains($0.lowercased()) }
    }

    /// 移除地址中的Unit/Apt等内部编号，保留完整街道地址
    private func removeUnitNumber(from address: String) -> String {
        // Unit/Apt等关键词模式
        let unitPatterns = [
            "\\b(apt|apartment|unit|suite|ste|room|rm)\\s+[a-z0-9]+\\b",
            "\\b#\\s*[a-z0-9]+\\b",
            "\\bflat\\s+[a-z0-9]+\\b"
        ]

        var cleanAddress = address

        for pattern in unitPatterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
                let range = NSRange(cleanAddress.startIndex..<cleanAddress.endIndex, in: cleanAddress)
                cleanAddress = regex.stringByReplacingMatches(in: cleanAddress, options: [], range: range, withTemplate: "")
            }
        }

        // 清理多余的空格和逗号
        cleanAddress = cleanAddress.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        cleanAddress = cleanAddress.replacingOccurrences(of: ",\\s*,", with: ",", options: .regularExpression)
        cleanAddress = cleanAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleanAddress
    }

    /// 标准化街道地址用于比较
    private func normalizeStreetAddress(_ address: String) -> String {
        var normalized = address.lowercased()

        // 标准化街道类型缩写
        let streetTypeMap = [
            "street": "st", "road": "rd", "avenue": "ave", "drive": "dr",
            "court": "ct", "place": "pl", "lane": "ln", "circle": "cir",
            "boulevard": "blvd", "way": "way", "terrace": "ter"
        ]

        for (full, abbrev) in streetTypeMap {
            normalized = normalized.replacingOccurrences(of: "\\b\(full)\\b", with: abbrev, options: .regularExpression)
        }

        // 清理空格
        normalized = normalized.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        normalized = normalized.trimmingCharacters(in: .whitespacesAndNewlines)

        return normalized
    }

    /// 提取街道类型（如 Dr, St, Blvd）
    private func extractStreetType(_ street: String) -> String {
        let words = street.components(separatedBy: " ")
        if let lastWord = words.last?.lowercased() {
            // 常见街道类型
            let streetTypes = ["st", "street", "rd", "road", "ave", "avenue", "dr", "drive",
                             "ct", "court", "pl", "place", "ln", "lane", "cir", "circle",
                             "blvd", "boulevard", "way", "ter", "terrace"]
            if streetTypes.contains(lastWord) {
                return lastWord
            }
        }
        return ""
    }

    /// 检查街道类型是否兼容
    private func areStreetTypesCompatible(_ type1: String, _ type2: String) -> Bool {
        let normalized1 = normalizeStreetType(type1)
        let normalized2 = normalizeStreetType(type2)
        return normalized1 == normalized2
    }

    /// 标准化街道类型
    private func normalizeStreetType(_ type: String) -> String {
        let lowercased = type.lowercased()
        switch lowercased {
        case "street": return "st"
        case "road": return "rd"
        case "avenue": return "ave"
        case "drive": return "dr"
        case "court": return "ct"
        case "place": return "pl"
        case "lane": return "ln"
        case "circle": return "cir"
        case "boulevard": return "blvd"
        case "terrace": return "ter"
        default: return lowercased
        }
    }

    // MARK: - 结果验证方法

    /// 检查地址是否包含公寓信息
    private func containsApartmentInfo(_ address: String) -> Bool {
        let apartmentPatterns = [
            "\\b(apt|apartment)\\s*[#:]?\\s*\\w+",
            "\\b(unit|ste|suite)\\s*[#:]?\\s*\\w+",
            "\\b(room|rm)\\s*[#:]?\\s*\\w+",
            "\\b#\\s*\\w+"
        ]

        for pattern in apartmentPatterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
                let range = NSRange(address.startIndex..<address.endIndex, in: address)
                if regex.firstMatch(in: address, options: [], range: range) != nil {
                    return true
                }
            }
        }
        return false
    }

    /// 验证地理编码结果的准确性
    private func isResultAccurate(_ result: GlobalGeocodingResult, originalAddress: String) -> Bool {
        switch result {
        case .success(_, _, let coordinate, let placemark, _, let confidence):
            // 1. 检查置信度
            if confidence == .low {
                Logger.warning("🚨 结果置信度过低: \(confidence)", type: .location)
                return false
            }

            // 2. 检查坐标有效性
            if !isValidCoordinate(coordinate) {
                Logger.warning("🚨 坐标无效: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                return false
            }

            // 3. 严格的地址组件匹配
            return verifyAddressComponents(originalAddress: originalAddress, placemark: placemark)

        case .failed:
            return false
        }
    }

    /// 简化地址验证 - 不依赖placemark组件解析
    private func verifyAddressComponents(originalAddress: String, placemark: CLPlacemark) -> Bool {
        // 🎯 简化验证策略：主要检查坐标有效性，减少组件匹配的严格性

        // 基本信息记录
        Logger.info("🎯 地址验证: '\(originalAddress)'", type: .location)
        Logger.info("🎯 返回地址: '\(formatPlacemark(placemark))'", type: .location)

        // 🎯 只进行基本的合理性检查，不强制要求完全匹配
        // 这样可以避免因为placemark组件解析错误而拒绝有效地址

        // 检查是否有基本的地址信息
        let hasBasicInfo = placemark.thoroughfare != nil || placemark.locality != nil

        if !hasBasicInfo {
            Logger.warning("⚠️ 返回的地址信息不完整", type: .location)
            return false
        }

        // 🎯 宽松验证：只要有合理的地址信息就接受
        Logger.info("✅ 地址验证通过（宽松模式）", type: .location)
        return true
    }



    /// 检查结果是否缺少重要信息（如门牌号）
    private func checkForMissingHouseNumber(_ result: GlobalGeocodingResult, originalAddress: String) -> GlobalGeocodingResult? {
        switch result {
        case .success(_, _, _, let placemark, _, _):
            // 检查原始地址是否包含门牌号
            let originalNumbers = extractNumbers(from: originalAddress)
            let hasOriginalHouseNumber = !originalNumbers.isEmpty

            // 检查返回结果是否包含门牌号
            let hasReturnedHouseNumber = placemark.subThoroughfare != nil

            // 如果原始地址有门牌号但返回结果没有，认为是不完整的
            if hasOriginalHouseNumber && !hasReturnedHouseNumber {
                Logger.info("🔍 检测到门牌号缺失: 原始有门牌号(\(originalNumbers))，返回无门牌号", type: .location)
                return result // 返回结果，但标记为需要改进
            }

            return nil // 结果完整，无需继续尝试

        case .failed:
            return nil // 失败的结果不需要检查
        }
    }

    // MARK: - 地址解析方法

    /// 解析地址组件 - 修复版（按照正确的地址定义）
    private func parseAddressComponents(_ address: String) -> (street: String?, city: String?, postalCode: String?) {
        // 移除额外信息（如排序号、追踪号等）
        let cleanAddress = address.components(separatedBy: "|").first ?? address

        // 按逗号分割地址
        let components = cleanAddress.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        var street: String?
        var city: String?
        var postalCode: String?

        // 🎯 第一个组件是完整街道地址（包含号码和街道名）
        // 例如："500 King Dr" 而不是分离的号码和街道名
        if !components.isEmpty {
            let firstComponent = components[0]
            // 检查是否包含Unit/Apt等内部编号，如果有则移除
            street = removeUnitNumber(from: firstComponent)
        }

        // 查找邮编（通常是5位数字）
        for component in components {
            if let regex = try? NSRegularExpression(pattern: "^\\d{5}$", options: []) {
                let range = NSRange(component.startIndex..<component.endIndex, in: component)
                if regex.firstMatch(in: component, options: [], range: range) != nil {
                    postalCode = component
                    break
                }
            }
        }

        // 🎯 移除重复的街道解析（已在上面处理）

        // 查找城市（通常在邮编前面）
        if let postalIndex = components.firstIndex(where: { $0 == postalCode }) {
            if postalIndex > 1 {
                city = components[postalIndex - 1]
            }
        } else if components.count >= 2 {
            // 如果没有邮编，假设倒数第二个是城市
            city = components[components.count - 2]
        }

        return (street: street, city: city, postalCode: postalCode)
    }

    // MARK: - 地址增强方法

    /// 生成简化门牌号候选地址
    private func generateSimplifiedHouseNumberCandidates(_ address: String) -> [String] {
        var candidates: [String] = []

        // 检测带字母后缀的门牌号（如 145c, 23A）
        let houseNumberPattern = "\\b(\\d+)[a-zA-Z]\\b"

        guard let regex = try? NSRegularExpression(pattern: houseNumberPattern, options: []) else {
            return candidates
        }

        let range = NSRange(address.startIndex..<address.endIndex, in: address)
        let matches = regex.matches(in: address, options: [], range: range)

        for match in matches {
            if let numberRange = Range(match.range(at: 1), in: address) {
                let numberOnly = String(address[numberRange])

                // 替换原地址中的门牌号
                if let fullMatchRange = Range(match.range, in: address) {
                    let originalHouseNumber = String(address[fullMatchRange])
                    let simplifiedAddress = address.replacingOccurrences(of: originalHouseNumber, with: numberOnly)

                    if simplifiedAddress != address {
                        candidates.append(simplifiedAddress)
                        Logger.info("🏠 生成简化门牌号候选: \(originalHouseNumber) -> \(numberOnly)", type: .location)
                    }
                }
            }
        }

        // 如果没有找到带字母的门牌号，尝试相近门牌号
        if candidates.isEmpty {
            candidates.append(contentsOf: generateNearbyHouseNumbers(address))
        }

        return candidates
    }

    /// 生成相近门牌号候选（当原门牌号不存在时）
    private func generateNearbyHouseNumbers(_ address: String) -> [String] {
        var candidates: [String] = []

        // 提取原始门牌号
        let numbers = extractNumbers(from: address)
        guard let originalNumber = numbers.first else {
            return candidates
        }

        // 生成相近的门牌号（±2, ±4）
        let nearbyNumbers = [
            originalNumber - 2,
            originalNumber - 1,
            originalNumber + 1,
            originalNumber + 2,
            originalNumber - 4,
            originalNumber + 4
        ].filter { $0 > 0 } // 确保门牌号为正数

        for nearbyNumber in nearbyNumbers {
            let modifiedAddress = address.replacingOccurrences(
                of: "\\b\(originalNumber)\\b",
                with: "\(nearbyNumber)",
                options: .regularExpression
            )

            if modifiedAddress != address {
                candidates.append(modifiedAddress)
                Logger.info("🏠 生成相近门牌号候选: \(originalNumber) -> \(nearbyNumber)", type: .location)
            }
        }

        return candidates
    }

    /// 生成增强地址候选
    private func generateEnhancedAddresses(_ address: String) -> [String] {
        var candidates: [String] = []

        // 检测可能的国家/地区
        let detectedCountry = detectCountryFromAddress(address)

        // 根据国家添加常见后缀
        if let country = detectedCountry {
            candidates.append("\(address), \(country)")
        }

        // 添加常见的地址组件
        if !address.lowercased().contains("street") && !address.lowercased().contains("road") {
            candidates.append("\(address) Street")
            candidates.append("\(address) Road")
        }

        return candidates
    }

    /// 生成模糊匹配候选
    private func generateFuzzyAddresses(_ address: String) -> [String] {
        var candidates: [String] = []

        // 移除标点符号
        let noPunctuation = address.replacingOccurrences(of: "[^\\w\\s]", with: "", options: .regularExpression)
        if noPunctuation != address {
            candidates.append(noPunctuation)
        }

        // 尝试不同的分词组合
        let words = address.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        if words.count > 1 {
            // 只取前几个词
            candidates.append(words.prefix(3).joined(separator: " "))
            // 只取后几个词
            candidates.append(words.suffix(3).joined(separator: " "))
        }

        return candidates
    }

    // MARK: - 辅助方法

    /// 验证坐标的合理性
    /// - Parameter coordinate: 要验证的坐标
    /// - Returns: 坐标是否合理
    private func isValidCoordinate(_ coordinate: CLLocationCoordinate2D) -> Bool {
        // 基本有效性检查
        guard CLLocationCoordinate2DIsValid(coordinate) else {
            return false
        }

        // 检查是否为明显错误的坐标（如0,0或极端值）
        if coordinate.latitude == 0 && coordinate.longitude == 0 {
            return false
        }

        // 检查纬度范围（-90到90）
        guard coordinate.latitude >= -90 && coordinate.latitude <= 90 else {
            return false
        }

        // 检查经度范围（-180到180）
        guard coordinate.longitude >= -180 && coordinate.longitude <= 180 else {
            return false
        }

        return true
    }

    /// 检测地址中的国家信息
    private func detectCountryFromAddress(_ address: String) -> String? {
        let lowercased = address.lowercased()

        // 常见国家检测
        let countryPatterns = [
            "hong kong": "Hong Kong",
            "香港": "Hong Kong",
            "australia": "Australia",
            "澳大利亚": "Australia",
            "china": "China",
            "中国": "China",
            "taiwan": "Taiwan",
            "台湾": "Taiwan",
            "singapore": "Singapore",
            "新加坡": "Singapore"
        ]

        for (pattern, country) in countryPatterns {
            if lowercased.contains(pattern) {
                return country
            }
        }

        return nil
    }

    /// 格式化地标信息 - 根据地区返回本地化格式
    private func formatPlacemark(_ placemark: CLPlacemark) -> String {
        // 检测是否是香港地址
        if isHongKongAddress(placemark) {
            return formatHongKongAddress(placemark)
        }

        // 检测是否是中国大陆地址
        if isMainlandChinaAddress(placemark) {
            return formatMainlandChinaAddress(placemark)
        }

        // 🇺🇸 检测是否是美国地址
        if isUSAddress(placemark) {
            return formatUSAddress(placemark)
        }

        // 其他地区使用标准格式
        return formatStandardAddress(placemark)
    }

    /// 检测是否是香港地址
    private func isHongKongAddress(_ placemark: CLPlacemark) -> Bool {
        let country = placemark.country?.lowercased() ?? ""
        let adminArea = placemark.administrativeArea?.lowercased() ?? ""

        return country.contains("hong kong") ||
               country.contains("香港") ||
               adminArea.contains("hong kong") ||
               adminArea.contains("香港")
    }

    /// 🇺🇸 检测是否是美国地址
    private func isUSAddress(_ placemark: CLPlacemark) -> Bool {
        return placemark.isoCountryCode == "US"
    }

    /// 检测是否是西方地址（为App Store审核优化）
    private func isWesternAddress(_ placemark: CLPlacemark) -> Bool {
        let westernCountries = ["US", "CA", "GB", "AU", "NZ", "IE", "FR", "DE", "IT", "ES", "NL", "BE", "CH", "AT", "SE", "NO", "DK", "FI"]
        return westernCountries.contains(placemark.isoCountryCode ?? "")
    }

    /// 检测是否是中国大陆地址
    private func isMainlandChinaAddress(_ placemark: CLPlacemark) -> Bool {
        let country = placemark.country?.lowercased() ?? ""
        return (country.contains("china") || country.contains("中国")) &&
               !isHongKongAddress(placemark)
    }

    /// 格式化香港地址
    private func formatHongKongAddress(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        // 香港地址格式：区域 + 街道 + 门牌号
        if let administrativeArea = placemark.administrativeArea {
            // 移除"新界"等行政区划，保留具体区域
            let cleanArea = administrativeArea
                .replacingOccurrences(of: "新界", with: "")
                .replacingOccurrences(of: "九龍", with: "")
                .replacingOccurrences(of: "香港島", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)

            if !cleanArea.isEmpty {
                components.append(cleanArea)
            }
        }

        if let thoroughfare = placemark.thoroughfare {
            components.append(thoroughfare)
        }

        if let subThoroughfare = placemark.subThoroughfare {
            components.append("\(subThoroughfare)號")
        }

        return components.joined(separator: "")
    }

    /// 格式化中国大陆地址
    private func formatMainlandChinaAddress(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        // 中国大陆地址格式：省市 + 区县 + 街道 + 门牌号
        if let administrativeArea = placemark.administrativeArea {
            components.append(administrativeArea)
        }

        if let locality = placemark.locality {
            components.append(locality)
        }

        if let thoroughfare = placemark.thoroughfare {
            components.append(thoroughfare)
        }

        if let subThoroughfare = placemark.subThoroughfare {
            components.append("\(subThoroughfare)号")
        }

        return components.joined(separator: "")
    }

    /// 🇺🇸 格式化美国地址（强制英文格式）
    private func formatUSAddress(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        // 街道地址（门牌号 + 街道名，作为一个整体）
        var streetAddress = ""
        if let subThoroughfare = placemark.subThoroughfare {
            streetAddress = subThoroughfare
        }
        if let thoroughfare = placemark.thoroughfare {
            if !streetAddress.isEmpty {
                streetAddress += " " + thoroughfare
            } else {
                streetAddress = thoroughfare
            }
        }
        if !streetAddress.isEmpty {
            components.append(streetAddress)
        }

        // 城市 - 强制使用英文名称
        if let locality = placemark.locality {
            let englishLocality = convertChineseToEnglishCityName(locality)
            components.append(englishLocality)
        }

        // 州 - 使用州缩写
        if let administrativeArea = placemark.administrativeArea {
            components.append(administrativeArea) // 通常已经是缩写形式如 "CA"
        }

        // 邮编
        if let postalCode = placemark.postalCode {
            components.append(postalCode)
        }

        // 国家 - 强制使用 "United States" 或省略
        // 对于美国地址，通常不需要显示国家

        return components.joined(separator: ", ")
    }

    /// 将中文城市名转换为英文
    private func convertChineseToEnglishCityName(_ chineseName: String) -> String {
        // 常见的中文城市名映射
        let cityMapping: [String: String] = [
            "斯托克顿": "Stockton",
            "洛杉矶": "Los Angeles",
            "旧金山": "San Francisco",
            "圣何塞": "San Jose",
            "萨克拉门托": "Sacramento",
            "弗雷斯诺": "Fresno",
            "奥克兰": "Oakland",
            "长滩": "Long Beach",
            "贝克斯菲尔德": "Bakersfield",
            "阿纳海姆": "Anaheim",
            "圣地亚哥": "San Diego",
            "圣迭戈": "San Diego", // 圣地亚哥的另一种翻译
            "纽约": "New York",
            "芝加哥": "Chicago",
            "休斯顿": "Houston",
            "费城": "Philadelphia",
            "凤凰城": "Phoenix",
            "圣安东尼奥": "San Antonio",
            "达拉斯": "Dallas",
            "西雅图": "Seattle",
            "波士顿": "Boston"
        ]

        // 如果找到映射，返回英文名称
        if let englishName = cityMapping[chineseName] {
            Logger.info("🇺🇸 城市名中英转换: '\(chineseName)' -> '\(englishName)'", type: .location)
            return englishName
        }

        // 如果没有找到映射，返回原名称（可能已经是英文）
        return chineseName
    }

    /// 格式化标准地址（国际格式）
    private func formatStandardAddress(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        // 街道地址（门牌号 + 街道名，作为一个整体）
        var streetAddress = ""
        if let subThoroughfare = placemark.subThoroughfare {
            streetAddress = subThoroughfare
        }
        if let thoroughfare = placemark.thoroughfare {
            if !streetAddress.isEmpty {
                streetAddress += " " + thoroughfare
            } else {
                streetAddress = thoroughfare
            }
        }
        if !streetAddress.isEmpty {
            components.append(streetAddress)
        }
        if let locality = placemark.locality {
            components.append(locality)
        }
        if let administrativeArea = placemark.administrativeArea {
            components.append(administrativeArea)
        }
        if let country = placemark.country {
            components.append(country)
        }

        return components.joined(separator: ", ")
    }


}

// MARK: - 结果类型

/// 全球地理编码结果
enum GlobalGeocodingResult {
    case success(
        originalAddress: String,
        formattedAddress: String,
        coordinate: CLLocationCoordinate2D,
        placemark: CLPlacemark,
        strategy: String,
        confidence: ConfidenceLevel
    )
    case failed(address: String, reason: String)

    var isSuccess: Bool {
        switch self {
        case .success: return true
        case .failed: return false
        }
    }
}

/// 置信度级别
enum ConfidenceLevel {
    case high    // 原始地址或精确匹配
    case medium  // 轻微修改或MKLocalSearch
    case low     // 模糊匹配或大幅修改
}
