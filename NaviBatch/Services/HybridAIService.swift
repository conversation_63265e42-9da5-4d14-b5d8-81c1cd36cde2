//
//  HybridAIService.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//  混合AI服务：Firebase AI优先，OpenRouter备用
//

import Foundation
import UIKit

// MARK: - 混合AI服务（Firebase AI优先）
class HybridAIService {
    static let shared = HybridAIService()
    
    private let firebaseAIService = FirebaseAIService.shared
    private let gemmaService = GemmaVisionService.shared
    
    private init() {}
    
    // MARK: - 主要功能：智能AI识别（Firebase AI → OpenRouter → 失败）
    func extractAddressesFromImage(_ image: UIImage, appType: DeliveryAppType = .justPhoto, isPDFImage: Bool = false) async throws -> GemmaAddressResult {
        let startTime = Date()
        Logger.aiInfo("🔥 混合识别服务 - 开始智能识别")
        Logger.aiInfo("📋 优先级: 高级服务 → 备用服务 → 失败")
        
        // 第一优先级：高级服务（快速且准确）
        do {
            Logger.aiInfo("🔥 第一优先级：尝试高级服务")
            let firebaseResult = try await firebaseAIService.extractAddressesFromImage(image, appType: appType, isPDFImage: isPDFImage)

            // 检查高级服务结果是否有效
            if firebaseResult.success && !firebaseResult.addresses.isEmpty {
                let totalTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ 高级服务识别成功！用时: \(String(format: "%.2f", totalTime))s")

                return GemmaAddressResult(
                    addresses: firebaseResult.addresses,
                    confidence: firebaseResult.confidence,
                    processingTime: totalTime,
                    modelUsed: "Advanced-\(firebaseResult.modelUsed)",
                    rawResponse: firebaseResult.rawResponse,
                    success: true,
                    detectedAppType: firebaseResult.detectedAppType
                )
            } else {
                Logger.aiWarning("高级服务返回空结果，尝试备用服务")
            }
        } catch {
            Logger.aiWarning("高级服务失败，尝试备用服务: \(error.localizedDescription)")
        }
        
        // 第二优先级：备用服务
        do {
            Logger.aiInfo("🔄 第二优先级：尝试备用服务")
            let openRouterResult = try await gemmaService.extractAddressesFromImage(image, appType: appType, isPDFImage: isPDFImage)

            // 检查备用服务结果是否有效
            if openRouterResult.success && !openRouterResult.addresses.isEmpty {
                let totalTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ 备用服务识别成功！用时: \(String(format: "%.2f", totalTime))s")

                return GemmaAddressResult(
                    addresses: openRouterResult.addresses,
                    confidence: openRouterResult.confidence,
                    processingTime: totalTime,
                    modelUsed: "Backup-\(openRouterResult.modelUsed)",
                    rawResponse: openRouterResult.rawResponse,
                    success: true,
                    detectedAppType: openRouterResult.detectedAppType
                )
            } else {
                Logger.aiError("备用服务也返回空结果")
            }
        } catch {
            Logger.aiError("备用服务也失败: \(error.localizedDescription)")
        }
        
        // 所有识别服务都失败
        let totalTime = Date().timeIntervalSince(startTime)
        Logger.aiError("❌ 所有识别服务都失败，总用时: \(String(format: "%.2f", totalTime))s")
        
        throw GemmaError.allGemmaModelsFailed
    }
    
    // MARK: - 地址验证功能（使用OpenRouter）
    func validateAddresses(_ addresses: [String]) async throws -> [GemmaValidationResult] {
        // 地址验证仍使用OpenRouter，因为Firebase AI可能不支持此功能
        return try await gemmaService.validateAddresses(addresses)
    }
    
    // MARK: - 地址转换功能（使用OpenRouter）
    func convertAddressesToAppleMapsFormat(_ addresses: [String], appType: DeliveryAppType = .justPhoto) async throws -> [String] {
        // 地址转换仍使用OpenRouter，因为Firebase AI可能不支持此功能
        return try await gemmaService.convertAddressesToAppleMapsFormat(addresses, appType: appType)
    }

    // MARK: - 原生PDF处理方法
    func extractAddressesFromPDFNatively(_ pdfData: Data, appType: DeliveryAppType = .justPhoto) async throws -> GemmaAddressResult {
        let startTime = Date()
        Logger.aiInfo("📄 混合AI服务 - 开始原生PDF处理")
        Logger.aiInfo("📋 优先级: Firebase AI原生PDF → 图像处理备用")

        // 第一优先级：Firebase AI原生PDF处理
        do {
            Logger.aiInfo("📄 第一优先级：尝试Firebase AI原生PDF处理")
            let firebaseResult = try await firebaseAIService.extractAddressesFromPDFNatively(pdfData, appType: appType)

            // 检查原生PDF处理结果是否有效
            if firebaseResult.success && !firebaseResult.addresses.isEmpty {
                let totalTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ Firebase AI原生PDF处理成功！用时: \(String(format: "%.2f", totalTime))s")
                Logger.aiInfo("📊 识别结果: \(firebaseResult.addresses.count)个地址, 置信度: \(Int(firebaseResult.confidence * 100))%")

                return GemmaAddressResult(
                    addresses: firebaseResult.addresses,
                    confidence: firebaseResult.confidence,
                    processingTime: totalTime,
                    modelUsed: "Hybrid AI (Firebase Native PDF)",
                    rawResponse: firebaseResult.rawResponse,
                    success: true,
                    detectedAppType: firebaseResult.detectedAppType
                )
            } else {
                Logger.aiWarning("⚠️ Firebase AI原生PDF处理结果无效，尝试备用方案")
            }
        } catch {
            Logger.aiError("❌ Firebase AI原生PDF处理失败: \(error.localizedDescription)")
            Logger.aiInfo("🔄 降级到图像处理方案")
        }

        // 备用方案：转换为图像后处理
        Logger.aiInfo("📄 备用方案：PDF转图像处理")
        // 这里可以调用现有的图像处理逻辑
        // 暂时抛出错误，提示用户使用图像处理方案
        throw GemmaError.allGemmaModelsFailed
    }

    // MARK: - PDF文本处理方法
    func extractAddressesFromPDFText(_ pdfText: String, appType: DeliveryAppType = .justPhoto) async throws -> GemmaAddressResult {
        let startTime = Date()
        Logger.aiInfo("📄 混合AI服务 - 开始PDF文本识别")
        Logger.aiInfo("📋 优先级: 高级服务 → 备用服务 → 失败")

        // 第一优先级：高级服务（快速且准确）
        do {
            Logger.aiInfo("📄 第一优先级：尝试高级PDF文本服务")
            let firebaseResult = try await firebaseAIService.extractAddressesFromPDFText(pdfText, appType: appType)

            // 检查高级服务结果是否有效
            if firebaseResult.success && !firebaseResult.addresses.isEmpty {
                let totalTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ 高级PDF文本服务识别成功！用时: \(String(format: "%.2f", totalTime))s")

                return GemmaAddressResult(
                    addresses: firebaseResult.addresses,
                    confidence: firebaseResult.confidence,
                    processingTime: totalTime,
                    modelUsed: "Advanced-PDF-\(firebaseResult.modelUsed)",
                    rawResponse: firebaseResult.rawResponse,
                    success: true,
                    detectedAppType: firebaseResult.detectedAppType
                )
            } else {
                Logger.aiWarning("高级PDF文本服务返回空结果，尝试备用服务")
            }
        } catch {
            Logger.aiWarning("⚠️ 高级PDF文本服务失败: \(error.localizedDescription)")
        }

        // 第二优先级：备用服务（较慢但可靠）
        do {
            Logger.aiInfo("📄 第二优先级：尝试备用PDF文本服务")
            let gemmaResult = try await gemmaService.extractAddressesFromPDFText(pdfText, appType: appType)

            if gemmaResult.success && !gemmaResult.addresses.isEmpty {
                let totalTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ 备用PDF文本服务识别成功！用时: \(String(format: "%.2f", totalTime))s")

                return GemmaAddressResult(
                    addresses: gemmaResult.addresses,
                    confidence: gemmaResult.confidence,
                    processingTime: totalTime,
                    modelUsed: "Backup-PDF-\(gemmaResult.modelUsed)",
                    rawResponse: gemmaResult.rawResponse,
                    success: true,
                    detectedAppType: gemmaResult.detectedAppType
                )
            } else {
                Logger.aiWarning("备用PDF文本服务返回空结果")
            }
        } catch {
            Logger.aiError("❌ 备用PDF文本服务失败: \(error.localizedDescription)")
        }

        // 所有服务都失败
        let totalTime = Date().timeIntervalSince(startTime)
        Logger.aiError("❌ 所有PDF文本AI服务都失败")

        return GemmaAddressResult(
            addresses: [],
            confidence: 0.0,
            processingTime: totalTime,
            modelUsed: "none",
            rawResponse: "All PDF text AI services failed",
            success: false,
            detectedAppType: nil
        )
    }

    // MARK: - 服务状态检查
    func getServiceStatus() async -> (firebaseAI: Bool, openRouter: Bool) {
        var firebaseStatus = false
        var openRouterStatus = false
        
        // 检查Firebase AI状态
        do {
            // 创建一个小的测试图片
            let testImage = UIImage(systemName: "photo") ?? UIImage()
            _ = try await firebaseAIService.extractAddressesFromImage(testImage, appType: .justPhoto)
            firebaseStatus = true
        } catch {
            firebaseStatus = false
        }
        
        // 检查OpenRouter状态
        do {
            let testImage = UIImage(systemName: "photo") ?? UIImage()
            _ = try await gemmaService.extractAddressesFromImage(testImage, appType: DeliveryAppType.justPhoto)
            openRouterStatus = true
        } catch {
            openRouterStatus = false
        }
        
        return (firebaseAI: firebaseStatus, openRouter: openRouterStatus)
    }
}

// MARK: - 扩展：优先级配置
extension HybridAIService {
    
    // 获取当前优先级策略描述
    func getPriorityDescription() -> String {
        return """
        🔥 智能识别优先级：

        1️⃣ 高级服务 (快速，准确)
           - 使用先进识别算法
           - 响应速度快，无地理限制
           - 资源充足

        2️⃣ 备用服务 (稳定)
           - 多算法支持
           - 高精度识别
           - 作为高级服务的备用

        3️⃣ 失败处理
           - 如果所有服务都失败
           - 系统会提示使用基础模式

        💡 优势：
        - 成本优化
        - 可用性最高（双重备用）
        - 性能最优（智能降级）
        """
    }
}
