import Foundation
import UIKit

/// 快递应用提示词验证器 - 防止快递间相互干扰
class DeliveryAppPromptValidator {
    static let shared = DeliveryAppPromptValidator()

    private init() {}

    // MARK: - 提示词冲突检测

    /// 检测提示词是否存在冲突
    func validatePromptIsolation(for appTypes: [DeliveryAppType]) -> PromptValidationResult {
        var conflicts: [PromptConflict] = []
        var warnings: [String] = []

        // 检查每对快递应用的提示词冲突
        for i in 0..<appTypes.count {
            for j in (i+1)..<appTypes.count {
                let app1 = appTypes[i]
                let app2 = appTypes[j]

                if let conflict = detectConflict(between: app1, and: app2) {
                    conflicts.append(conflict)
                }
            }
        }

        // 检查通用警告
        warnings.append(contentsOf: checkGeneralWarnings(for: appTypes))

        return PromptValidationResult(
            isValid: conflicts.isEmpty,
            conflicts: conflicts,
            warnings: warnings,
            recommendations: generateRecommendations(for: conflicts)
        )
    }

    // MARK: - 冲突检测逻辑

    private func detectConflict(between app1: DeliveryAppType, and app2: DeliveryAppType) -> PromptConflict? {
        // 检测已知的冲突模式
        let knownConflicts: [(DeliveryAppType, DeliveryAppType, String)] = [
            (.speedx, .gofo, "地址格式处理冲突：SpeedX要求移除USA，GoFo要求保持原样"),
            (.speedx, .uniuni, "排序号格式冲突：SpeedX使用停靠点，UNIUNI使用路线号"),
            (.gofo, .amazonFlex, "地址提取策略冲突：GoFo强调只提取显示内容，Amazon Flex允许格式化"),
        ]

        for (conflictApp1, conflictApp2, description) in knownConflicts {
            if (app1 == conflictApp1 && app2 == conflictApp2) ||
               (app1 == conflictApp2 && app2 == conflictApp1) {
                return PromptConflict(
                    app1: app1,
                    app2: app2,
                    description: description,
                    severity: .medium
                )
            }
        }

        return nil
    }

    private func checkGeneralWarnings(for appTypes: [DeliveryAppType]) -> [String] {
        var warnings: [String] = []

        if appTypes.count > 5 {
            warnings.append("支持的快递应用过多，建议分批测试以确保准确性")
        }

        if appTypes.contains(.speedx) && appTypes.contains(.gofo) {
            warnings.append("SpeedX和GoFo同时使用时需要特别注意地址格式一致性")
        }

        return warnings
    }

    private func generateRecommendations(for conflicts: [PromptConflict]) -> [String] {
        var recommendations: [String] = []

        if !conflicts.isEmpty {
            recommendations.append("建议使用隔离式提示词架构，为每个快递创建独立的识别会话")
            recommendations.append("考虑实施A/B测试来验证不同快递的识别准确性")
            recommendations.append("建立快递应用专用的测试数据集")
        }

        return recommendations
    }

    // MARK: - 测试建议

    /// 生成测试建议
    func generateTestPlan(for appTypes: [DeliveryAppType]) -> TestPlan {
        var testCases: [TestCase] = []

        for appType in appTypes {
            testCases.append(contentsOf: generateTestCases(for: appType))
        }

        return TestPlan(
            appTypes: appTypes,
            testCases: testCases,
            estimatedDuration: calculateEstimatedDuration(for: testCases),
            priority: determinePriority(for: appTypes)
        )
    }

    private func generateTestCases(for appType: DeliveryAppType) -> [TestCase] {
        switch appType {
        case .speedx:
            return [
                TestCase(
                    name: "SpeedX停靠点识别",
                    description: "测试停靠点: X格式的正确识别",
                    expectedBehavior: "正确提取数字部分作为third_party_sort"
                ),
                TestCase(
                    name: "SpeedX地址格式化",
                    description: "测试USA后缀的正确移除",
                    expectedBehavior: "地址不包含USA后缀"
                )
            ]
        case .gofo:
            return [
                TestCase(
                    name: "GoFo原样提取",
                    description: "测试地址的原样提取，不添加额外信息",
                    expectedBehavior: "地址与图片显示完全一致"
                ),
                TestCase(
                    name: "GoFo排序号识别",
                    description: "测试左侧数字排序号的正确识别",
                    expectedBehavior: "准确提取左侧显示的数字"
                )
            ]
        default:
            return [
                TestCase(
                    name: "\(appType.displayName)基础识别",
                    description: "测试基本的地址和追踪号识别",
                    expectedBehavior: "正确识别地址和追踪号"
                )
            ]
        }
    }

    private func calculateEstimatedDuration(for testCases: [TestCase]) -> TimeInterval {
        return Double(testCases.count) * 300 // 每个测试用例5分钟
    }

    private func determinePriority(for appTypes: [DeliveryAppType]) -> TestPriority {
        if appTypes.contains(.speedx) && appTypes.contains(.gofo) {
            return .high
        } else if appTypes.count > 3 {
            return .medium
        } else {
            return .low
        }
    }
}

// MARK: - 数据模型

struct PromptValidationResult {
    let isValid: Bool
    let conflicts: [PromptConflict]
    let warnings: [String]
    let recommendations: [String]
}

struct PromptConflict {
    let app1: DeliveryAppType
    let app2: DeliveryAppType
    let description: String
    let severity: ConflictSeverity
}

enum ConflictSeverity {
    case low, medium, high
}

struct TestPlan {
    let appTypes: [DeliveryAppType]
    let testCases: [TestCase]
    let estimatedDuration: TimeInterval
    let priority: TestPriority
}

struct TestCase {
    let name: String
    let description: String
    let expectedBehavior: String
}

enum TestPriority {
    case low, medium, high
}
