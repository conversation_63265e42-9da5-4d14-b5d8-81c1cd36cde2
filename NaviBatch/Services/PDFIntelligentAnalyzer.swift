//
//  PDFIntelligentAnalyzer.swift
//  NaviBatch
//
//  Created by AI Assistant on 2025-06-23.
//  智能PDF分析器 - 分析PDF结构并计算最佳分批策略
//

import Foundation
import PDFKit
import UIKit

/// PDF分析结果
struct PDFAnalysisResult {
    let totalPages: Int
    let fileSize: Int64
    let estimatedAddressCount: Int
    let addressDensity: Double
    let recommendedBatchSize: Int
    let optimalBatchCount: Int
    let processingStrategy: ProcessingStrategy
    let estimatedProcessingTime: TimeInterval
    
    enum ProcessingStrategy {
        case direct           // 直接处理（小文件）
        case pageBased       // 基于页面分批
        case contentBased    // 基于内容分批
        case hybrid          // 混合策略
    }
}

/// PDF批次信息
struct PDFBatch {
    let id: UUID
    let batchNumber: Int
    let pdfData: Data
    let pageRange: Range<Int>?
    let estimatedAddressRange: Range<Int>
    let processingPriority: Int
    let strategy: PDFAnalysisResult.ProcessingStrategy
}

/// 智能PDF分析器
class PDFIntelligentAnalyzer {
    
    // MARK: - Constants
    
    /// AI处理能力基准
    private struct AIProcessingBenchmarks {
        static let optimalAddressCount = 30      // 最佳地址数量
        static let maxAddressCount = 50          // 最大地址数量
        static let optimalFileSize: Int64 = 2_000_000  // 2MB
        static let maxFileSize: Int64 = 5_000_000      // 5MB
        static let processingTimePerAddress: TimeInterval = 0.3  // 每个地址处理时间
        static let baseProcessingTime: TimeInterval = 10.0       // 基础处理时间
    }
    
    // MARK: - Public Methods
    
    /// 分析PDF并返回处理策略
    func analyzePDF(_ pdfData: Data) -> PDFAnalysisResult {
        Logger.info("🔍 开始分析PDF，大小: \(formatFileSize(Int64(pdfData.count)))")
        
        // 1. 基础信息提取
        let pageCount = extractPageCount(from: pdfData)
        let fileSize = Int64(pdfData.count)
        
        // 2. 地址数量估算
        let estimatedAddresses = estimateAddressCount(fileSize: fileSize, pages: pageCount)
        
        // 3. 处理策略决策
        let strategy = determineProcessingStrategy(
            addresses: estimatedAddresses,
            fileSize: fileSize,
            pages: pageCount
        )
        
        // 4. 批次大小计算
        let batchSize = calculateOptimalBatchSize(
            addresses: estimatedAddresses,
            strategy: strategy
        )
        
        // 5. 批次数量计算
        let batchCount = calculateBatchCount(
            addresses: estimatedAddresses,
            batchSize: batchSize
        )
        
        // 6. 处理时间估算
        let estimatedTime = estimateProcessingTime(
            addresses: estimatedAddresses,
            batchCount: batchCount
        )
        
        let result = PDFAnalysisResult(
            totalPages: pageCount,
            fileSize: fileSize,
            estimatedAddressCount: estimatedAddresses,
            addressDensity: Double(estimatedAddresses) / Double(max(pageCount, 1)),
            recommendedBatchSize: batchSize,
            optimalBatchCount: batchCount,
            processingStrategy: strategy,
            estimatedProcessingTime: estimatedTime
        )
        
        Logger.info("📊 PDF分析完成: \(estimatedAddresses)个地址, \(batchCount)个批次, 预计\(Int(estimatedTime))秒")
        
        return result
    }
    
    /// 创建处理批次
    func createBatches(from pdfData: Data, analysis: PDFAnalysisResult) -> [PDFBatch] {
        Logger.info("🔄 创建处理批次，策略: \(analysis.processingStrategy)")
        
        switch analysis.processingStrategy {
        case .direct:
            return createDirectBatch(pdfData: pdfData, analysis: analysis)
        case .pageBased:
            return createPageBasedBatches(pdfData: pdfData, analysis: analysis)
        case .contentBased:
            return createContentBasedBatches(pdfData: pdfData, analysis: analysis)
        case .hybrid:
            return createHybridBatches(pdfData: pdfData, analysis: analysis)
        }
    }
    
    // MARK: - Private Methods - PDF Analysis
    
    /// 提取PDF页数
    private func extractPageCount(from pdfData: Data) -> Int {
        // 创建临时文件来初始化PDFDocument
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString + ".pdf")

        do {
            try pdfData.write(to: tempURL)

            if let pdfDocument = PDFKit.PDFDocument(url: tempURL) {
                let pageCount = pdfDocument.pageCount
                Logger.debug("📄 PDF页数: \(pageCount)")

                // 清理临时文件
                try? FileManager.default.removeItem(at: tempURL)

                return pageCount
            } else {
                Logger.warning("⚠️ 无法解析PDF文档，默认为1页")
                try? FileManager.default.removeItem(at: tempURL)
                return 1
            }
        } catch {
            Logger.warning("⚠️ PDF页数提取失败: \(error)，默认为1页")
            try? FileManager.default.removeItem(at: tempURL)
            return 1
        }
    }
    
    /// 估算地址数量
    private func estimateAddressCount(fileSize: Int64, pages: Int) -> Int {
        // 基于文件大小和页数的启发式算法
        let fileSizeMB = Double(fileSize) / 1_000_000.0
        let baseEstimate: Double
        
        if pages == 1 {
            // 单页PDF - 基于文件大小
            baseEstimate = fileSizeMB * 25.0  // 每MB约25个地址
        } else {
            // 多页PDF - 基于页数
            baseEstimate = Double(pages) * 15.0  // 每页约15个地址
        }
        
        // 应用修正因子
        let correctionFactor = calculateCorrectionFactor(fileSize: fileSize, pages: pages)
        let finalEstimate = Int(baseEstimate * correctionFactor)
        
        Logger.debug("📊 地址数量估算: \(finalEstimate) (基础: \(Int(baseEstimate)), 修正: \(correctionFactor))")
        
        return max(finalEstimate, 1)
    }
    
    /// 计算修正因子
    private func calculateCorrectionFactor(fileSize: Int64, pages: Int) -> Double {
        let fileSizeMB = Double(fileSize) / 1_000_000.0
        let avgSizePerPage = fileSizeMB / Double(max(pages, 1))
        
        // 基于每页平均大小调整
        switch avgSizePerPage {
        case 0.0..<0.5:   return 0.7   // 小文件，可能地址密度低
        case 0.5..<1.0:   return 1.0   // 标准大小
        case 1.0..<2.0:   return 1.2   // 大文件，可能地址密度高
        default:          return 1.5   // 超大文件
        }
    }
    
    /// 确定处理策略
    private func determineProcessingStrategy(addresses: Int, fileSize: Int64, pages: Int) -> PDFAnalysisResult.ProcessingStrategy {
        // 小文件直接处理
        if addresses <= AIProcessingBenchmarks.optimalAddressCount && 
           fileSize <= AIProcessingBenchmarks.optimalFileSize {
            return .direct
        }
        
        // 多页PDF优先按页面分批
        if pages > 1 && pages <= 10 {
            return .pageBased
        }
        
        // 单页超长PDF按内容分批
        if pages == 1 {
            return .contentBased
        }
        
        // 复杂情况使用混合策略
        return .hybrid
    }
    
    /// 计算最佳批次大小
    private func calculateOptimalBatchSize(addresses: Int, strategy: PDFAnalysisResult.ProcessingStrategy) -> Int {
        switch strategy {
        case .direct:
            return addresses
        case .pageBased:
            return AIProcessingBenchmarks.optimalAddressCount
        case .contentBased:
            // 基于总地址数动态调整
            switch addresses {
            case 0...100:     return 35
            case 101...300:   return 30
            case 301...500:   return 25
            default:          return 20
            }
        case .hybrid:
            return AIProcessingBenchmarks.optimalAddressCount
        }
    }
    
    /// 计算批次数量
    private func calculateBatchCount(addresses: Int, batchSize: Int) -> Int {
        return (addresses + batchSize - 1) / batchSize
    }
    
    /// 估算处理时间
    private func estimateProcessingTime(addresses: Int, batchCount: Int) -> TimeInterval {
        let baseTime = AIProcessingBenchmarks.baseProcessingTime * Double(batchCount)
        let addressTime = AIProcessingBenchmarks.processingTimePerAddress * Double(addresses)
        let concurrencyFactor = batchCount > 1 ? 0.7 : 1.0  // 并发处理减少总时间
        
        return (baseTime + addressTime) * concurrencyFactor
    }
    
    // MARK: - Private Methods - Batch Creation
    
    /// 创建直接处理批次
    private func createDirectBatch(pdfData: Data, analysis: PDFAnalysisResult) -> [PDFBatch] {
        return [PDFBatch(
            id: UUID(),
            batchNumber: 1,
            pdfData: pdfData,
            pageRange: nil,
            estimatedAddressRange: 0..<analysis.estimatedAddressCount,
            processingPriority: 1,
            strategy: .direct
        )]
    }
    
    /// 创建基于页面的批次
    private func createPageBasedBatches(pdfData: Data, analysis: PDFAnalysisResult) -> [PDFBatch] {
        let pagesPerBatch = max(1, analysis.totalPages / analysis.optimalBatchCount)
        var batches: [PDFBatch] = []
        
        for batchIndex in 0..<analysis.optimalBatchCount {
            let startPage = batchIndex * pagesPerBatch
            let endPage = min(startPage + pagesPerBatch, analysis.totalPages)
            
            if startPage < analysis.totalPages {
                let addressStart = (analysis.estimatedAddressCount * startPage) / analysis.totalPages
                let addressEnd = (analysis.estimatedAddressCount * endPage) / analysis.totalPages
                
                batches.append(PDFBatch(
                    id: UUID(),
                    batchNumber: batchIndex + 1,
                    pdfData: pdfData,
                    pageRange: startPage..<endPage,
                    estimatedAddressRange: addressStart..<addressEnd,
                    processingPriority: batchIndex == 0 ? 1 : 2,
                    strategy: .pageBased
                ))
            }
        }
        
        return batches
    }
    
    /// 创建基于内容的批次
    private func createContentBasedBatches(pdfData: Data, analysis: PDFAnalysisResult) -> [PDFBatch] {
        var batches: [PDFBatch] = []
        let batchSize = analysis.recommendedBatchSize
        
        for batchIndex in 0..<analysis.optimalBatchCount {
            let addressStart = batchIndex * batchSize
            let addressEnd = min(addressStart + batchSize, analysis.estimatedAddressCount)
            
            batches.append(PDFBatch(
                id: UUID(),
                batchNumber: batchIndex + 1,
                pdfData: pdfData,
                pageRange: nil,
                estimatedAddressRange: addressStart..<addressEnd,
                processingPriority: batchIndex == 0 ? 1 : 2,
                strategy: .contentBased
            ))
        }
        
        return batches
    }
    
    /// 创建混合策略批次
    private func createHybridBatches(pdfData: Data, analysis: PDFAnalysisResult) -> [PDFBatch] {
        // 混合策略：优先按页面，必要时按内容
        if analysis.totalPages > 1 && analysis.totalPages <= 20 {
            return createPageBasedBatches(pdfData: pdfData, analysis: analysis)
        } else {
            return createContentBasedBatches(pdfData: pdfData, analysis: analysis)
        }
    }
    
    // MARK: - Utility Methods
    
    /// 格式化文件大小
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}
