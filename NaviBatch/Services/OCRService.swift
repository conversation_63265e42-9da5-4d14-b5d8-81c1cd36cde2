import Foundation
import Vision
import UIKit
import OSLog
// TODO: 添加Google ML Kit支持
// import MLKitTextRecognition
// import MLKitVision

// 🎯 OCR服务 - 支持多种OCR引擎
class OCRService {

    // OCR引擎类型
    enum OCREngine {
        case appleVision    // Apple Vision Framework (当前)
        case googleMLKit    // Google ML Kit (推荐)
        case hybrid         // 混合模式：优先Google ML Kit，失败时降级到Apple Vision
    }

    // 当前使用的OCR引擎
    private var currentEngine: OCREngine = .appleVision

    // OCR识别结果
    struct OCRResult {
        let text: String
        let confidence: Float
        let boundingBox: CGRect
    }

    // 完整的OCR响应
    struct OCRResponse {
        let results: [OCRResult]
        let fullText: String
        let confidence: Float
    }

    // 🎯 主要OCR识别方法 - 支持多引擎
    func recognizeText(from image: UIImage, engine: OCREngine? = nil) async throws -> OCRResponse {
        let selectedEngine = engine ?? currentEngine
        Logger.ocrInfo("🔍 开始OCR文本识别，使用引擎: \(selectedEngine)")
        Logger.ocrInfo("📏 图片信息: \(image.size.width)x\(image.size.height), 内存估算: \(Int(image.size.width * image.size.height * 4 / 1024 / 1024))MB")

        // 🎯 检查是否需要分段处理（适用于所有超长图像）
        let aspectRatio = image.size.height / image.size.width
        if aspectRatio > 30.0 {
            Logger.ocrInfo("🔧 启用分段OCR处理，高宽比=\(String(format: "%.1f", aspectRatio)):1")
            return try await recognizeTextWithSegmentation(from: image)
        }

        switch selectedEngine {
        case .appleVision:
            return try await recognizeTextWithAppleVision(from: image)
        case .googleMLKit:
            // TODO: 实现Google ML Kit OCR
            Logger.ocrInfo("⚠️ Google ML Kit暂未实现，降级到Apple Vision")
            return try await recognizeTextWithAppleVision(from: image)
        case .hybrid:
            // TODO: 实现混合模式
            Logger.ocrInfo("⚠️ 混合模式暂未实现，使用Apple Vision")
            return try await recognizeTextWithAppleVision(from: image)
        }
    }

    // 🎯 Apple Vision Framework OCR实现
    private func recognizeTextWithAppleVision(from image: UIImage) async throws -> OCRResponse {

        // 🎯 Apple Vision Framework最佳实践：检查图片尺寸限制
        let optimizedImage = try optimizeImageForOCR(image)

        guard let cgImage = optimizedImage.cgImage else {
            Logger.ocrError("❌ 无法获取CGImage")
            throw OCRError.invalidImage
        }

        Logger.ocrInfo("✅ CGImage获取成功: \(cgImage.width)x\(cgImage.height)")

        return try await withCheckedThrowingContinuation { continuation in
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    Logger.ocrError("❌ OCR识别失败: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                    return
                }

                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    Logger.ocrError("❌ 无法获取OCR观察结果")
                    continuation.resume(throwing: OCRError.noTextFound)
                    return
                }

                Logger.ocrInfo("📊 OCR原始观察结果: \(observations.count)个观察对象")

                let results = self.processObservations(observations)
                let response = self.createResponse(from: results)

                Logger.ocrInfo("✅ OCR识别完成: \(results.count)个文本块, 平均置信度: \(Int(response.confidence * 100))%")

                // 🔍 诊断信息：如果没有识别到文本，记录详细信息
                if results.isEmpty {
                    Logger.ocrInfo("🔍 OCR诊断: 原始观察\(observations.count)个，处理后\(results.count)个")
                    for (index, obs) in observations.prefix(3).enumerated() {
                        if let candidate = obs.topCandidates(1).first {
                            Logger.ocrInfo("🔍 观察\(index): '\(candidate.string)', 置信度: \(Int(candidate.confidence * 100))%")
                        }
                    }
                }

                continuation.resume(returning: response)
            }

            // 🎯 OCR优化配置
            request.recognitionLevel = .accurate  // 高精度识别

            // 🎯 支持中英文混合识别（SpeedX需要识别"停靠点"等中文）
            request.recognitionLanguages = ["zh-Hans", "en-US"]  // 支持简体中文和英文
            request.usesLanguageCorrection = true  // 启用语言校正
            request.automaticallyDetectsLanguage = true  // 启用自动语言检测

            // 🎯 针对扫描文档优化
            request.minimumTextHeight = 0.01  // 提高最小文字高度，过滤噪点

            // 🎯 使用最新的OCR版本和优化
            if #available(iOS 16.0, *) {
                request.revision = VNRecognizeTextRequestRevision3  // 使用最新版本

                // 🎯 iOS 16+ 专用优化
                if #available(iOS 17.0, *) {
                    // iOS 17的额外优化选项 - 添加快递相关词汇
                    request.customWords = ["SpeedX", "SPXSF", "SPXSFO", "停靠点", "个包裹", "Daly", "City"]  // 添加常见词汇
                }
            }

            // 执行OCR请求
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            do {
                try handler.perform([request])
            } catch {
                Logger.ocrError("❌ OCR请求执行失败: \(error.localizedDescription)")
                continuation.resume(throwing: error)
            }
        }
    }

    // 🎯 处理OCR观察结果
    private func processObservations(_ observations: [VNRecognizedTextObservation]) -> [OCRResult] {
        var results: [OCRResult] = []

        for observation in observations {
            guard let topCandidate = observation.topCandidates(1).first else { continue }

            let result = OCRResult(
                text: topCandidate.string,
                confidence: topCandidate.confidence,
                boundingBox: observation.boundingBox
            )

            results.append(result)
        }

        // 按照Y坐标排序（从上到下）
        results.sort { $0.boundingBox.origin.y > $1.boundingBox.origin.y }

        return results
    }

    // 🎯 创建OCR响应
    private func createResponse(from results: [OCRResult]) -> OCRResponse {
        let fullText = results.map { $0.text }.joined(separator: "\n")
        let averageConfidence = results.isEmpty ? 0.0 : results.map { $0.confidence }.reduce(0, +) / Float(results.count)

        return OCRResponse(
            results: results,
            fullText: fullText,
            confidence: averageConfidence
        )
    }

    // 🎯 格式化OCR文本用于AI处理
    func formatTextForAI(_ ocrResponse: OCRResponse) -> String {
        Logger.ocrInfo("📝 格式化OCR文本用于AI处理")

        var formattedText = "=== OCR提取的文本内容 ===\n\n"

        for (index, result) in ocrResponse.results.enumerated() {
            formattedText += "[\(index + 1)] \(result.text)\n"
        }

        formattedText += "\n=== 完整文本 ===\n"
        formattedText += ocrResponse.fullText

        return formattedText
    }

    // 🎯 SpeedX专用图片优化 - 基于Apple最佳实践
    private func optimizeImageForOCR(_ image: UIImage) throws -> UIImage {
        let imageSize = image.size
        let aspectRatio = imageSize.height / imageSize.width
        let totalPixels = imageSize.width * imageSize.height

        Logger.ocrInfo("🔧 SpeedX图片优化分析: 宽高比=\(String(format: "%.1f", aspectRatio)):1, 总像素=\(Int(totalPixels/1_000_000))M")

        // 🎯 PDF专用优化：针对长列表图片的特殊处理
        let maxDimension: CGFloat = 8192  // Apple推荐的最大尺寸
        let maxPixels: CGFloat = 50_000_000  // 约50M像素限制
        let maxAspectRatio: CGFloat = 30.0  // PDF长列表：检测超长图像

        var needsOptimization = false
        var optimizedSize = imageSize

        // 🎯 PDF特殊处理：对于超长图像，尝试分段处理
        if aspectRatio > maxAspectRatio {
            Logger.ocrInfo("⚠️ 检测到超长PDF图像，高宽比=\(String(format: "%.1f", aspectRatio)):1")
            Logger.ocrInfo("🔧 PDF分段处理: 将尝试切分图像以提高OCR识别率")

            // 对于PDF分段处理，我们仍需要一个合理的单段尺寸
            let targetWidth = imageSize.width  // 保持原始宽度
            let segmentHeight: CGFloat = 2000  // 每段2000像素高度
            let targetHeight = min(segmentHeight, imageSize.height * 0.2)

            optimizedSize = CGSize(width: targetWidth, height: targetHeight)
            needsOptimization = true
            Logger.ocrInfo("🔧 PDF分段优化: 保持宽度\(Int(targetWidth))，段高度\(Int(targetHeight))")
        }
        // 检查1: 单边尺寸过大（但不是超长图像的情况）
        else if imageSize.width > maxDimension || imageSize.height > maxDimension {
            Logger.ocrInfo("⚠️ 单边尺寸超限，需要缩放")
            let scale = min(maxDimension / imageSize.width, maxDimension / imageSize.height)
            optimizedSize = CGSize(width: imageSize.width * scale, height: imageSize.height * scale)
            needsOptimization = true
        }
        // 检查2: 总像素过多
        else if totalPixels > maxPixels {
            Logger.ocrInfo("⚠️ 总像素超限，需要压缩")
            let scale = sqrt(maxPixels / totalPixels)
            optimizedSize = CGSize(width: imageSize.width * scale, height: imageSize.height * scale)
            needsOptimization = true
        }

        if needsOptimization {
            Logger.ocrInfo("🔧 应用图片优化: \(Int(imageSize.width))x\(Int(imageSize.height)) → \(Int(optimizedSize.width))x\(Int(optimizedSize.height))")

            // 使用高质量重采样
            UIGraphicsBeginImageContextWithOptions(optimizedSize, false, 1.0)
            defer { UIGraphicsEndImageContext() }

            image.draw(in: CGRect(origin: .zero, size: optimizedSize))

            guard let resizedImage = UIGraphicsGetImageFromCurrentImageContext() else {
                Logger.ocrError("❌ 图片优化失败")
                throw OCRError.invalidImage
            }

            // 🎯 应用OCR增强处理
            let enhancedImage = enhanceImageForOCR(resizedImage)
            return enhancedImage

        } else {
            Logger.ocrInfo("✅ 图片尺寸合适，应用OCR增强")
            // 即使尺寸合适，也应用OCR增强
            let enhancedImage = enhanceImageForOCR(image)
            return enhancedImage
        }
    }

    // 🎯 增强图片以提高OCR识别率
    private func enhanceImageForOCR(_ image: UIImage) -> UIImage {
        Logger.ocrInfo("🎨 应用OCR图片增强处理")

        guard let cgImage = image.cgImage else {
            Logger.ocrError("❌ 无法获取CGImage进行增强")
            return image
        }

        // 创建Core Image上下文
        let context = CIContext()
        let ciImage = CIImage(cgImage: cgImage)

        // 🎯 增强对比度和亮度
        guard let contrastFilter = CIFilter(name: "CIColorControls") else {
            Logger.ocrError("❌ 无法创建对比度滤镜")
            return image
        }

        contrastFilter.setValue(ciImage, forKey: kCIInputImageKey)
        contrastFilter.setValue(2.0, forKey: kCIInputContrastKey)  // PDF：大幅增强对比度
        contrastFilter.setValue(0.1, forKey: kCIInputBrightnessKey)  // PDF：增加亮度
        contrastFilter.setValue(0.0, forKey: kCIInputSaturationKey)  // PDF：转为灰度，提高文字识别

        guard let contrastOutput = contrastFilter.outputImage else {
            Logger.ocrError("❌ 对比度增强失败")
            return image
        }

        // 🎯 锐化处理
        guard let sharpenFilter = CIFilter(name: "CIUnsharpMask") else {
            Logger.ocrError("❌ 无法创建锐化滤镜")
            return processedImageFromCIImage(contrastOutput, context: context, originalImage: image)
        }

        sharpenFilter.setValue(contrastOutput, forKey: kCIInputImageKey)
        sharpenFilter.setValue(1.0, forKey: kCIInputRadiusKey)  // PDF：增强锐化半径
        sharpenFilter.setValue(1.5, forKey: kCIInputIntensityKey)  // PDF：增强锐化强度
        sharpenFilter.setValue(1.0, forKey: kCIInputIntensityKey)  // 锐化强度

        guard let finalOutput = sharpenFilter.outputImage else {
            Logger.ocrError("❌ 锐化处理失败")
            return processedImageFromCIImage(contrastOutput, context: context, originalImage: image)
        }

        // 转换回UIImage
        let enhancedImage = processedImageFromCIImage(finalOutput, context: context, originalImage: image)
        Logger.ocrInfo("✅ OCR图片增强完成")

        return enhancedImage
    }

    // 🎯 从CIImage转换为UIImage
    private func processedImageFromCIImage(_ ciImage: CIImage, context: CIContext, originalImage: UIImage) -> UIImage {
        let extent = ciImage.extent
        guard let cgImage = context.createCGImage(ciImage, from: extent) else {
            Logger.ocrError("❌ CIImage转换失败，使用原图")
            return originalImage
        }

        return UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)
    }

    // 🎯 SpeedX专用OCR方法 - 针对性优化
    func recognizeTextForSpeedX(from image: UIImage) async throws -> OCRResponse {
        Logger.ocrInfo("🚀 SpeedX专用OCR识别开始")
        Logger.ocrInfo("📏 SpeedX图片信息: \(image.size.width)x\(image.size.height)")

        // 检查是否需要分段处理
        let aspectRatio = image.size.height / image.size.width
        if aspectRatio > 30.0 {
            Logger.ocrInfo("🔧 启用分段OCR处理，高宽比=\(String(format: "%.1f", aspectRatio)):1")
            return try await recognizeTextWithSegmentation(from: image)
        }

        // 使用专门优化的Apple Vision配置
        return try await recognizeTextWithAppleVision(from: image)
    }

    // 🎯 分段OCR处理 - 将超长图像切分为多个段落
    private func recognizeTextWithSegmentation(from image: UIImage) async throws -> OCRResponse {
        Logger.ocrInfo("🔧 开始分段OCR处理")

        let segmentHeight: CGFloat = 2000  // 每段高度
        let imageHeight = image.size.height
        let imageWidth = image.size.width
        let segmentCount = Int(ceil(imageHeight / segmentHeight))

        Logger.ocrInfo("📊 分段信息: 总高度\(Int(imageHeight))，分为\(segmentCount)段")

        var allResults: [OCRResult] = []
        var allTexts: [String] = []
        var totalConfidence: Float = 0
        var processedSegments = 0

        for i in 0..<segmentCount {
            let yOffset = CGFloat(i) * segmentHeight
            let actualHeight = min(segmentHeight, imageHeight - yOffset)

            Logger.ocrInfo("🔧 处理第\(i+1)/\(segmentCount)段: y=\(Int(yOffset)), h=\(Int(actualHeight))")

            // 裁剪图像段
            guard let segmentImage = cropImage(image, rect: CGRect(x: 0, y: yOffset, width: imageWidth, height: actualHeight)) else {
                Logger.ocrError("❌ 第\(i+1)段裁剪失败")
                continue
            }

            // 对段进行OCR
            do {
                let segmentResult = try await recognizeTextWithAppleVision(from: segmentImage)

                // 调整文本块的坐标（相对于原图）
                let adjustedResults = segmentResult.results.map { result in
                    let adjustedBoundingBox = CGRect(
                        x: result.boundingBox.origin.x,
                        y: result.boundingBox.origin.y + yOffset,
                        width: result.boundingBox.size.width,
                        height: result.boundingBox.size.height
                    )
                    return OCRResult(
                        text: result.text,
                        confidence: result.confidence,
                        boundingBox: adjustedBoundingBox
                    )
                }

                allResults.append(contentsOf: adjustedResults)
                allTexts.append(segmentResult.fullText)
                totalConfidence += segmentResult.confidence
                processedSegments += 1

                Logger.ocrInfo("✅ 第\(i+1)段完成: \(adjustedResults.count)个文本块")
            } catch {
                Logger.ocrError("❌ 第\(i+1)段OCR失败: \(error)")
            }
        }

        let averageConfidence = processedSegments > 0 ? totalConfidence / Float(processedSegments) : 0
        let combinedText = allTexts.joined(separator: "\n")
        Logger.ocrInfo("🎯 分段OCR完成: 总计\(allResults.count)个文本块，平均置信度\(Int(averageConfidence))%")

        return OCRResponse(
            results: allResults,
            fullText: combinedText,
            confidence: averageConfidence
        )
    }

    // 🎯 图像裁剪工具
    private func cropImage(_ image: UIImage, rect: CGRect) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        // 转换坐标系（UIKit坐标系转换为Core Graphics坐标系）
        let scale = image.scale
        let scaledRect = CGRect(
            x: rect.origin.x * scale,
            y: rect.origin.y * scale,
            width: rect.size.width * scale,
            height: rect.size.height * scale
        )

        guard let croppedCGImage = cgImage.cropping(to: scaledRect) else { return nil }
        return UIImage(cgImage: croppedCGImage, scale: scale, orientation: image.imageOrientation)
    }
}

// 🎯 OCR错误类型
enum OCRError: LocalizedError {
    case invalidImage
    case noTextFound
    case recognitionFailed

    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无效的图像"
        case .noTextFound:
            return "未找到文本"
        case .recognitionFailed:
            return "文本识别失败"
        }
    }
}
