import UIKit
import AVFoundation
import CoreImage
import Vision
import Accelerate
import CommonCrypto

/// 视频帧智能处理器
/// 直接处理视频帧，跳过长图转换，避免多此一举
class VideoToLongImageProcessor {

    // MARK: - 配置参数
    private let frameSkipCount: Int = 2 // 🎯 基于帧率提取：每2帧提取1帧（自适应视频帧率）
    private let frameExtractionInterval: Double = 0.1 // 保留用于兼容性
    private var similarityThreshold: Float = 0.95 // 🎯 SSIM相似度阈值：0.95表示95%相似才认为是重复帧
    private let overlapDetectionThreshold: Float = 0.7 // 重叠检测阈值
    // 🎯 移除maxFrames限制：让视频完整处理，不管多长时间

    // MARK: - 智能内容感知配置
    private let contentChangeThreshold: Float = 0.05 // 降低阈值，更敏感地检测变化

    // MARK: - 应用特定阈值配置
    private func getContentChangeThreshold(for appType: DeliveryAppType) -> Float {
        switch appType {
        case .speedx:
            return 0.012 // SpeedX使用超敏感阈值
        case .gofo:
            return 0.015 // GoFo使用敏感阈值，比SpeedX稍高但比默认低很多
        default:
            return 0.05  // 其他应用使用默认阈值
        }
    }
    private let adaptiveIntervalEnabled = true // 启用自适应间隔调整
    private let minInterval: Double = 0.05 // 🚀 最小提取间隔：快速变化时每0.05秒提取一帧
    private let maxInterval: Double = 0.3 // 🚀 最大间隔：即使静止也不超过0.3秒

    // MARK: - 提取模式枚举
    enum ExtractionMode {
        case timeBasedDense     // 按时长密集提取（原方式）
        case contentAware       // 智能内容感知提取（新方式）
        case hybrid            // 混合模式（结合两种方式）
        case frameRateBased    // 🚀 基于帧率提取（最新方式，自适应视频帧率）
    }

    // MARK: - 去重模式枚举
    enum DeduplicationMode {
        case imageSimilarity    // 基于图像相似度去重（原方式）
        case ocrContent        // 基于OCR内容去重（新方式，100%精准）
        case hybrid           // 混合模式：先OCR再图像去重
        case ocrForGroupingOnly // 🎯 新模式：OCR仅用于去重，AI Only分析代表帧
        case addressBased     // 🎯 最新模式：基于地址内容去重，每个地址保留一帧
        case perceptualHash   // 🚀 感知哈希模式：dHash + SSIM，速度最快
    }

    private let extractionMode: ExtractionMode = .frameRateBased // 🚀 使用基于帧率的提取，自适应视频帧率

    // 🎯 根据应用类型选择去重模式
    private func getDeduplicationMode(for appType: DeliveryAppType) -> DeduplicationMode {
        // 🚀 统一使用感知哈希模式：速度快、准确度高、内存占用低
        return .perceptualHash

        // 保留原有逻辑供参考（已停用）
        /*
        switch appType {
        case .speedx:
            return .addressBased // 🎯 SpeedX最新优化：基于地址内容去重，每个地址保留一帧
        case .gofo:
            return .imageSimilarity // 🚫 GoFo禁用OCR，使用图像相似度去重
        default:
            return .ocrContent // 其他应用使用OCR内容去重，确保100%精准
        }
        */
    }

    // MARK: - 调试和保存配置
    private let saveFramesForDebugging = true // 是否保存帧供调试
    private let maxSavedFrames = 50 // 最多保存50帧，避免占用过多存储空间

    // MARK: - 基于地址内容的智能去重配置
    private let enableAddressBasedDeduplication = true  // 🎯 启用基于地址内容的去重
    private let addressSimilarityThreshold = 0.85       // 地址相似度阈值（85%以上认为是同一地址）

    // MARK: - 重复帧检测配置（宽松捕获，AI精确去重）
    private let shortPauseThreshold = 15    // 🎯 15帧阈值：宽松捕获更多帧，依赖AI去重（约0.5秒停留）
    private let mediumPauseThreshold = 60   // 🔄 保留用于兼容性（已不使用）
    private let longPauseInterval = 30      // 🔄 保留用于兼容性（已不使用）

    // MARK: - 自适应阈值配置（SSIM算法优化）
    private let adaptiveThresholdEnabled = true // 启用自适应阈值调整
    private let minThreshold: Float = 0.90 // 🎯 SSIM最小阈值：90%相似度
    private let maxThreshold: Float = 0.98 // 🎯 SSIM最大阈值：98%相似度

    // MARK: - 处理结果
    struct ProcessingResult {
        let uniqueFrames: [NumberedFrame]  // 保留的不重复帧（带原始编号）
        let originalFrameCount: Int  // 原始帧数
        let duplicateFrameCount: Int // 重复帧数
        let uniqueFrameCount: Int    // 保留的唯一帧数
        let processingTime: TimeInterval
        let success: Bool
        let errorMessage: String?
        let frameAnalysisLog: [String] // 帧分析日志
        let savedFrameURLs: [URL]    // 保存的帧文件URL，可供下载检查
        let debugInfo: FrameDebugInfo // 调试信息
    }

    // MARK: - 带编号的帧结构
    struct NumberedFrame {
        let image: UIImage
        let originalIndex: Int      // 原始提取顺序编号
        let timestamp: Double       // 视频时间戳
        let extractionTime: Date    // 提取时间
        var ocrResult: OCRFrameResult? // OCR识别结果（保留兼容性）
        var dHash: String?          // Difference Hash（新方案）
        var ssimValue: Float?       // SSIM值（与参考帧比较）
    }

    // MARK: - OCR帧结果结构
    struct OCRFrameResult {
        let fullText: String        // 完整文本内容
        let addresses: [String]     // 识别到的地址
        let stopNumbers: [String]   // 停靠点号码
        let trackingNumbers: [String] // 快递单号
        let confidence: Float       // 整体置信度
        let textBlocks: Int         // 文本块数量
        let processingTime: TimeInterval // OCR处理时间
    }

    // MARK: - 调试信息
    struct FrameDebugInfo {
        let similarityScores: [Float] // 每帧的相似度分数
        let frameTimestamps: [Double] // 每帧的时间戳
        let processingLog: [String]   // 详细处理日志
        let duplicateFrameIndices: [Int] // 被去除的重复帧索引
        let originalFrameIndices: [Int] // 保留帧的原始编号
    }

    // MARK: - 主要处理方法
    func processVideoToLongImage(_ asset: AVAsset, appType: DeliveryAppType = .justPhoto, progressCallback: ((String, Double) -> Void)? = nil) async -> ProcessingResult {
        let startTime = Date()

        do {
            // 1. 根据配置选择帧提取方式
            progressCallback?(NSLocalizedString("extracting_video_frames", comment: ""), 0.1)
            let frames = try await extractFramesWithMode(from: asset, mode: extractionMode, appType: appType)

            guard !frames.isEmpty else {
                let emptyDebugInfo = FrameDebugInfo(
                    similarityScores: [],
                    frameTimestamps: [],
                    processingLog: [NSLocalizedString("unable_to_extract_video_frames", comment: "")],
                    duplicateFrameIndices: [],
                    originalFrameIndices: []
                )

                return ProcessingResult(
                    uniqueFrames: [],
                    originalFrameCount: 0,
                    duplicateFrameCount: 0,
                    uniqueFrameCount: 0,
                    processingTime: Date().timeIntervalSince(startTime),
                    success: false,
                    errorMessage: NSLocalizedString("unable_to_extract_frames", comment: ""),
                    frameAnalysisLog: emptyDebugInfo.processingLog,
                    savedFrameURLs: [],
                    debugInfo: emptyDebugInfo
                )
            }

            // 2. 根据应用类型选择去重模式并处理
            let selectedDeduplicationMode = getDeduplicationMode(for: appType)
            print("🎯 \(appType.displayName)使用去重模式: \(selectedDeduplicationMode)")
            let uniqueFrames: [NumberedFrame]
            switch selectedDeduplicationMode {
            case .imageSimilarity:
                progressCallback?(NSLocalizedString("analyzing_frame_similarity", comment: ""), 0.3)
                uniqueFrames = await removeRedundantFrames(frames)
            case .ocrContent:
                progressCallback?(NSLocalizedString("ocr_analyzing_all_frames", comment: ""), 0.2)
                let framesWithOCR = await performOCROnAllFrames(frames, appType: appType, progressCallback: progressCallback)
                progressCallback?(NSLocalizedString("ocr_content_deduplication", comment: ""), 0.4)
                uniqueFrames = await removeRedundantFramesBasedOnOCR(framesWithOCR)
            case .hybrid:
                progressCallback?(NSLocalizedString("ocr_analyzing_all_frames", comment: ""), 0.2)
                let framesWithOCR = await performOCROnAllFrames(frames, appType: appType, progressCallback: progressCallback)
                progressCallback?(NSLocalizedString("ocr_content_deduplication", comment: ""), 0.3)
                let ocrDeduped = await removeRedundantFramesBasedOnOCR(framesWithOCR)
                progressCallback?(NSLocalizedString("image_similarity_deduplication", comment: ""), 0.4)
                uniqueFrames = await removeRedundantFrames(ocrDeduped)
            case .ocrForGroupingOnly:
                progressCallback?(NSLocalizedString("ocr_fast_grouping", comment: ""), 0.2)
                let framesWithOCR = await performOCROnAllFrames(frames, appType: appType, progressCallback: progressCallback)
                progressCallback?(NSLocalizedString("intelligent_group_deduplication", comment: ""), 0.3)
                let ocrDeduped = await removeRedundantFramesBasedOnOCR(framesWithOCR)
                print("🎯 SpeedX优化模式: OCR去重完成 \(frames.count) → \(ocrDeduped.count) 帧")
                print("🚀 清除OCR结果，准备使用AI Only模式分析代表帧...")
                // 🎯 注意：清除OCR结果，后续将使用AI Only模式分析图片
                uniqueFrames = ocrDeduped.map { frame in
                    var cleanFrame = frame
                    cleanFrame.ocrResult = nil // 清除OCR结果，强制使用AI Only
                    return cleanFrame
                }
                print("✅ SpeedX优化: 将分析 \(uniqueFrames.count) 个代表帧（节省 \(frames.count - uniqueFrames.count) 帧的处理时间）")
            case .addressBased:
                progressCallback?(NSLocalizedString("ocr_analyzing_all_frames", comment: ""), 0.2)
                let framesWithOCR = await performOCROnAllFrames(frames, appType: appType, progressCallback: progressCallback)
                progressCallback?("基于地址内容智能去重...", 0.4)
                uniqueFrames = await removeRedundantFramesBasedOnAddress(framesWithOCR)
                print("🎯 地址去重模式: 完成 \(frames.count) → \(uniqueFrames.count) 帧")
                print("📍 确保每个不同地址都有代表帧，避免地址遗漏")
            case .perceptualHash:
                progressCallback?("🚀 感知哈希重复帧检测...", 0.3)
                uniqueFrames = detectDuplicateFramesWithPerceptualHash(frames)
                print("🚀 感知哈希去重模式: 完成 \(frames.count) → \(uniqueFrames.count) 帧")
                print("⚡ 使用dHash+SSIM算法，速度比OCR快50-100倍")
            }

            // 3. 检测滚动方向
            progressCallback?(NSLocalizedString("detecting_scroll_direction", comment: ""), 0.5)
            let scrollDirection = await detectScrollDirection(uniqueFrames)

            // 4. 智能拼接（现在不再使用，但保留代码以备后用）
            progressCallback?(NSLocalizedString("stitching_long_image", comment: ""), 0.7)
            let _ = await stitchFramesToLongImage(uniqueFrames, scrollDirection: scrollDirection)

            // 5. 保存帧供调试检查
            progressCallback?("保存帧文件供检查...", 0.9)
            let savedURLs = await saveFramesForDebugging(uniqueFrames)

            progressCallback?(NSLocalizedString("processing_complete", comment: ""), 1.0)

            // 6. 生成调试信息
            let debugInfo = FrameDebugInfo(
                similarityScores: [], // 将在去重过程中填充
                frameTimestamps: frames.map { $0.timestamp }, // 使用实际时间戳
                processingLog: [
                    "原始帧数: \(frames.count)",
                    "去重后帧数: \(uniqueFrames.count)",
                    "重复帧数: \(frames.count - uniqueFrames.count)",
                    "去重率: \(String(format: "%.1f", Double(frames.count - uniqueFrames.count) / Double(frames.count) * 100))%",
                    "保存帧数: \(savedURLs.count)",
                    "保留帧编号: \(uniqueFrames.map { $0.originalIndex })"
                ],
                duplicateFrameIndices: [], // 将在去重过程中填充
                originalFrameIndices: uniqueFrames.map { $0.originalIndex }
            )

            return ProcessingResult(
                uniqueFrames: uniqueFrames,
                originalFrameCount: frames.count,
                duplicateFrameCount: frames.count - uniqueFrames.count,
                uniqueFrameCount: uniqueFrames.count,
                processingTime: Date().timeIntervalSince(startTime),
                success: true,
                errorMessage: nil,
                frameAnalysisLog: debugInfo.processingLog,
                savedFrameURLs: savedURLs,
                debugInfo: debugInfo
            )

        } catch {
            let emptyDebugInfo = FrameDebugInfo(
                similarityScores: [],
                frameTimestamps: [],
                processingLog: ["处理失败: \(error.localizedDescription)"],
                duplicateFrameIndices: [],
                originalFrameIndices: []
            )

            return ProcessingResult(
                uniqueFrames: [],
                originalFrameCount: 0,
                duplicateFrameCount: 0,
                uniqueFrameCount: 0,
                processingTime: Date().timeIntervalSince(startTime),
                success: false,
                errorMessage: String(format: NSLocalizedString("processing_failed_error", comment: ""), error.localizedDescription),
                frameAnalysisLog: emptyDebugInfo.processingLog,
                savedFrameURLs: [],
                debugInfo: emptyDebugInfo
            )
        }
    }

    // MARK: - 统一帧提取入口
    private func extractFramesWithMode(from asset: AVAsset, mode: ExtractionMode, appType: DeliveryAppType) async throws -> [NumberedFrame] {
        switch mode {
        case .timeBasedDense:
            print("⏰ 使用时长密集提取模式")
            return try await extractDenseFrames(from: asset, appType: appType)
        case .contentAware:
            print("🧠 使用智能内容感知提取模式")
            return try await extractContentAwareFrames(from: asset, appType: appType)
        case .hybrid:
            print("🔄 使用混合提取模式")
            return try await extractHybridFrames(from: asset, appType: appType)
        case .frameRateBased:
            print("🚀 使用基于帧率的智能提取模式")
            return try await extractFramesBasedOnFrameRate(from: asset, appType: appType)
        }
    }

    // MARK: - 密集帧提取（原方式）
    private func extractDenseFrames(from asset: AVAsset, appType: DeliveryAppType) async throws -> [NumberedFrame] {
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero

        // 🎯 高分辨率优化：确保文字和数字清晰识别
        // 使用1500宽度以保证GoFo地图标记和SpeedX停靠点号码的清晰识别
        generator.maximumSize = CGSize(width: 1500, height: 2000) // 优化分辨率平衡质量和性能

        // 🔧 优化图片质量设置
        generator.apertureMode = .cleanAperture // 使用清洁光圈模式
        generator.videoComposition = nil // 不使用视频合成，保持原始质量

        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)

        var frames: [NumberedFrame] = []
        var currentTime: Double = 0
        var frameIndex = 1 // 🎯 从1开始编号，保持与UI一致

        while currentTime < durationSeconds {
            let time = CMTime(seconds: currentTime, preferredTimescale: 600)

            do {
                let cgImage = try await generator.image(at: time).image
                let uiImage = UIImage(cgImage: cgImage)

                // 🎯 创建带编号的帧
                let numberedFrame = NumberedFrame(
                    image: uiImage,
                    originalIndex: frameIndex,
                    timestamp: currentTime,
                    extractionTime: Date()
                )
                frames.append(numberedFrame)
                frameIndex += 1
            } catch {
                print("⚠️ 提取帧失败 at \(currentTime)s: \(error)")
            }

            currentTime += frameExtractionInterval
        }

        print("🎬 提取了 \(frames.count) 帧，总时长 \(durationSeconds) 秒")
        return frames
    }

    // MARK: - 智能内容感知帧提取
    private func extractContentAwareFrames(from asset: AVAsset, appType: DeliveryAppType) async throws -> [NumberedFrame] {
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero

        // 🎯 高分辨率优化：确保文字和数字清晰识别
        // 使用1500宽度以确保GoFo地图标记和文字的清晰识别
        generator.maximumSize = CGSize(width: 1500, height: 2000)
        generator.apertureMode = .cleanAperture
        generator.videoComposition = nil

        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)

        var frames: [NumberedFrame] = []
        var currentTime: Double = 0
        var currentInterval = frameExtractionInterval
        var previousFrame: UIImage?
        var frameIndex = 1 // 🎯 从1开始编号，保持与UI一致

        let appSpecificThreshold = getContentChangeThreshold(for: appType)
        print("🧠 开始智能内容感知帧提取，总时长: \(durationSeconds)秒")
        print("🎯 \(appType.displayName)专用阈值: \(appSpecificThreshold) (默认: \(contentChangeThreshold))")

        while currentTime < durationSeconds {
            let time = CMTime(seconds: currentTime, preferredTimescale: 600)

            do {
                let cgImage = try await generator.image(at: time).image
                let currentFrame = UIImage(cgImage: cgImage)

                // 如果是第一帧，直接添加
                if previousFrame == nil {
                    let numberedFrame = NumberedFrame(
                        image: currentFrame,
                        originalIndex: frameIndex,
                        timestamp: currentTime,
                        extractionTime: Date()
                    )
                    frames.append(numberedFrame)
                    previousFrame = currentFrame
                    frameIndex += 1
                    print("📸 添加第一帧#\(frameIndex-1) at \(String(format: "%.1f", currentTime))s")
                } else {
                    // 计算与前一帧的内容变化程度
                    let contentChange = await calculateContentChange(previousFrame!, currentFrame)

                    // 根据内容变化调整提取间隔
                    currentInterval = calculateAdaptiveInterval(contentChange: contentChange)

                    // 如果内容变化足够大，添加这一帧
                    let appSpecificThreshold = getContentChangeThreshold(for: appType)
                    if contentChange > appSpecificThreshold {
                        let numberedFrame = NumberedFrame(
                            image: currentFrame,
                            originalIndex: frameIndex,
                            timestamp: currentTime,
                            extractionTime: Date()
                        )
                        frames.append(numberedFrame)
                        previousFrame = currentFrame
                        print("📸 内容变化(\(String(format: "%.3f", contentChange)) > \(appSpecificThreshold)) 帧#\(frameIndex) at \(String(format: "%.1f", currentTime))s, 间隔: \(String(format: "%.1f", currentInterval))s")
                        frameIndex += 1
                    } else {
                        print("⏭️ 跳过静止帧 at \(String(format: "%.1f", currentTime))s, 变化: \(String(format: "%.3f", contentChange)) <= \(appSpecificThreshold)")
                        // 🔧 不再递增frameIndex，只有实际保存的帧才编号
                    }
                }

            } catch {
                print("⚠️ 提取帧失败 at \(currentTime)s: \(error)")
                // 🔧 失败的帧不编号
            }

            currentTime += currentInterval
        }

        print("🧠 智能提取完成: \(frames.count) 帧，平均间隔: \(String(format: "%.2f", durationSeconds / Double(frames.count)))秒")
        return frames
    }

    // MARK: - 内容变化计算
    private func calculateContentChange(_ frame1: UIImage, _ frame2: UIImage) async -> Float {
        // 缩小图片以提高计算速度
        let targetSize = CGSize(width: 100, height: 100)
        guard let resized1 = resizeImageForComparison(frame1, to: targetSize),
              let resized2 = resizeImageForComparison(frame2, to: targetSize) else {
            return 1.0 // 如果无法处理，假设有变化
        }

        // 计算像素差异
        guard let data1 = resized1.cgImage?.dataProvider?.data,
              let data2 = resized2.cgImage?.dataProvider?.data else {
            return 1.0
        }

        let bytes1 = CFDataGetBytePtr(data1)
        let bytes2 = CFDataGetBytePtr(data2)
        let length = CFDataGetLength(data1)

        var totalDifference: Int = 0
        for i in 0..<length {
            totalDifference += abs(Int(bytes1![i]) - Int(bytes2![i]))
        }

        // 归一化到0-1范围
        let maxPossibleDifference = length * 255
        return Float(totalDifference) / Float(maxPossibleDifference)
    }

    // MARK: - 自适应间隔计算（SpeedX优化版）
    private func calculateAdaptiveInterval(contentChange: Float) -> Double {
        if contentChange > 0.1 {
            // 明显变化：快速提取
            return minInterval
        } else if contentChange > 0.03 {
            // 微小变化：标准间隔（可能是滚动）
            return frameExtractionInterval
        } else {
            // 极小变化：适度增加间隔，但不要太大
            return min(maxInterval, frameExtractionInterval * 2)
        }
    }

    // MARK: - 混合模式帧提取（根据应用类型优化）
    private func extractHybridFrames(from asset: AVAsset, appType: DeliveryAppType) async throws -> [NumberedFrame] {
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)

        print("🔄 \(appType.displayName)混合模式: 视频时长 \(durationSeconds)秒")

        // 根据应用类型选择不同的处理策略
        switch appType {
        case .speedx:
            // SpeedX特殊处理：对于配送列表视频，使用保守策略
            if durationSeconds <= 60 {
                print("📱 \(appType.displayName)短视频：使用改进的内容感知模式")
                return try await extractSpeedXOptimizedFrames(from: asset, appType: appType)
            } else {
                print("🎬 \(appType.displayName)长视频：使用时长密集模式")
                return try await extractDenseFrames(from: asset, appType: appType)
            }
        case .gofo:
            // GoFo优化：使用标准内容感知模式
            print("📱 \(appType.displayName)：使用标准内容感知模式")
            return try await extractContentAwareFrames(from: asset, appType: appType)
        default:
            // 其他应用：使用通用混合策略
            if durationSeconds <= 60 {
                print("📱 \(appType.displayName)短视频：使用内容感知模式")
                return try await extractContentAwareFrames(from: asset, appType: appType)
            } else {
                print("🎬 \(appType.displayName)长视频：使用时长密集模式")
                return try await extractDenseFrames(from: asset, appType: appType)
            }
        }
    }

    // MARK: - SpeedX优化的帧提取
    private func extractSpeedXOptimizedFrames(from asset: AVAsset, appType: DeliveryAppType) async throws -> [NumberedFrame] {
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero
        // 🎯 高分辨率优化：确保SpeedX停靠号和GoFo地图标记清晰识别
        generator.maximumSize = CGSize(width: 1500, height: 2000)
        generator.apertureMode = .cleanAperture
        generator.videoComposition = nil

        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)

        var allFrames: [(image: UIImage, timestamp: Double)] = [] // 🔧 先收集所有帧，后统一编号

        print("🚀 \(appType.displayName)优化提取: 总时长 \(durationSeconds)秒")

        // 策略1：确保提取关键时间点
        let keyTimePoints = [
            0.0,                           // 开始
            durationSeconds * 0.25,        // 25%
            durationSeconds * 0.5,         // 50%
            durationSeconds * 0.75,        // 75%
            max(0, durationSeconds - 1.0)  // 结束前1秒
        ]

        for timePoint in keyTimePoints {
            let time = CMTime(seconds: timePoint, preferredTimescale: 600)
            do {
                let cgImage = try await generator.image(at: time).image
                let frame = UIImage(cgImage: cgImage)
                allFrames.append((image: frame, timestamp: timePoint))
                print("📸 \(appType.displayName)关键帧 at \(String(format: "%.1f", timePoint))s")
            } catch {
                print("⚠️ \(appType.displayName)关键帧提取失败 at \(timePoint)s: \(error)")
            }
        }

        // 策略2：快速滚动适配 - 超密集采样
        var currentTime: Double = 0
        let interval: Double = 0.6 // 🚀 终极快速滚动：每0.6秒采样，确保160个地址完整捕获

        while currentTime < durationSeconds {
            // 跳过已经提取的关键时间点
            let isKeyPoint = keyTimePoints.contains { abs($0 - currentTime) < 0.5 }
            if !isKeyPoint {
                let time = CMTime(seconds: currentTime, preferredTimescale: 600)
                do {
                    let cgImage = try await generator.image(at: time).image
                    let currentFrame = UIImage(cgImage: cgImage)

                    // 与最近的帧比较
                    if let lastFrame = allFrames.last {
                        let contentChange = await calculateContentChange(lastFrame.image, currentFrame)
                        if contentChange > 0.012 { // 🚀 终极敏感阈值：捕获最细微的滚动变化，确保160个地址完整
                            allFrames.append((image: currentFrame, timestamp: currentTime))

                            // 🚀 快速滚动检测：如果变化很大，说明用户在快速滚动
                            if contentChange > 0.08 {
                                print("🏃‍♂️ \(appType.displayName)快速滚动检测(\(String(format: "%.3f", contentChange))) at \(String(format: "%.1f", currentTime))s - 增加采样密度")
                                // 在快速滚动区域增加额外采样点
                                let extraTime = currentTime + 0.4
                                if extraTime < durationSeconds {
                                    let extraTimePoint = CMTime(seconds: extraTime, preferredTimescale: 600)
                                    do {
                                        let extraCgImage = try await generator.image(at: extraTimePoint).image
                                        let extraFrame = UIImage(cgImage: extraCgImage)
                                        allFrames.append((image: extraFrame, timestamp: extraTime))
                                        print("📸 \(appType.displayName)快速滚动补充帧 at \(String(format: "%.1f", extraTime))s")
                                    } catch {
                                        print("⚠️ \(appType.displayName)快速滚动补充帧失败 at \(extraTime)s")
                                    }
                                }
                            } else {
                                print("📸 \(appType.displayName)内容变化(\(String(format: "%.3f", contentChange))) at \(String(format: "%.1f", currentTime))s")
                            }
                        }
                    } else {
                        // 第一帧的情况
                        allFrames.append((image: currentFrame, timestamp: currentTime))
                    }
                } catch {
                    print("⚠️ SpeedX间隔帧提取失败 at \(currentTime)s: \(error)")
                }
            }

            currentTime += interval
        }

        // 🔧 按时间戳排序并统一编号
        let sortedFrames = allFrames.sorted { $0.timestamp < $1.timestamp }
        var frames: [NumberedFrame] = []

        for (index, frameData) in sortedFrames.enumerated() {
            let numberedFrame = NumberedFrame(
                image: frameData.image,
                originalIndex: index + 1, // 🎯 连续编号，从1开始
                timestamp: frameData.timestamp,
                extractionTime: Date()
            )
            frames.append(numberedFrame)
        }

        print("🎬 \(appType.displayName)优化提取完成: \(frames.count) 帧，编号1-\(frames.count)")
        print("🔢 时间戳范围: \(String(format: "%.1f", sortedFrames.first?.timestamp ?? 0))s - \(String(format: "%.1f", sortedFrames.last?.timestamp ?? 0))s")
        return frames
    }

    // MARK: - 图片缩放辅助方法
    private func resizeImageForComparison(_ image: UIImage, to targetSize: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(targetSize, false, 1.0)
        defer { UIGraphicsEndImageContext() }

        image.draw(in: CGRect(origin: .zero, size: targetSize))
        return UIGraphicsGetImageFromCurrentImageContext()
    }

    // MARK: - 智能帧群组去重（用户停留检测）
    private func removeRedundantFrames(_ frames: [NumberedFrame]) async -> [NumberedFrame] {
        guard frames.count > 1 else { return frames }

        print("🎯 开始智能帧群组去重，检测用户停留内容...")

        // 🎯 新逻辑：将相似帧分组，每组选择最佳代表帧
        var frameGroups: [[NumberedFrame]] = []
        var currentGroup: [NumberedFrame] = [frames[0]]

        for i in 1..<frames.count {
            let currentFrame = frames[i]
            let groupRepresentative = currentGroup[0] // 使用组内第一帧作为代表

            let similarity = await calculateFrameSimilarity(currentFrame.image, groupRepresentative.image)

            // 🎯 如果相似度高于阈值，说明是同一内容的重复帧（用户停留）
            if similarity >= similarityThreshold {
                currentGroup.append(currentFrame)
                print("📎 帧#\(currentFrame.originalIndex) 归入群组 (相似度: \(String(format: "%.3f", similarity)))")
            } else {
                // 新内容，结束当前组，开始新组
                frameGroups.append(currentGroup)
                currentGroup = [currentFrame]
                print("🆕 帧#\(currentFrame.originalIndex) 开始新群组 (相似度: \(String(format: "%.3f", similarity)))")
            }
        }

        // 添加最后一组
        frameGroups.append(currentGroup)

        // 🎯 从每组中选择最佳代表帧（选择组内中间位置的帧，通常质量最好）
        var uniqueFrames: [NumberedFrame] = []
        var totalDuplicates = 0

        for (groupIndex, group) in frameGroups.enumerated() {
            let groupDuration = group.count > 1 ? group.last!.timestamp - group.first!.timestamp : 0
            let selectedFrames = selectRepresentativeFrames(from: group, groupIndex: groupIndex, duration: groupDuration)

            uniqueFrames.append(contentsOf: selectedFrames)
            let duplicatesInGroup = group.count - selectedFrames.count
            totalDuplicates += duplicatesInGroup
        }

        let removalPercentage = Double(totalDuplicates) / Double(frames.count) * 100
        print("🔍 智能群组去重完成: \(frames.count) → \(uniqueFrames.count) 帧")
        print("📊 检测到 \(frameGroups.count) 个内容群组，移除 \(totalDuplicates) 个重复帧 (\(String(format: "%.1f", removalPercentage))%)")

        // 🎯 新逻辑统计分析
        let discardedGroups = frameGroups.filter { $0.count < shortPauseThreshold }
        let keptGroups = frameGroups.filter { $0.count >= shortPauseThreshold }

        print("📊 \(shortPauseThreshold)帧阈值去重统计:")
        print("   🗑️ 丢弃群组: \(discardedGroups.count) (<\(shortPauseThreshold)帧相同)")
        print("   ✅ 保留群组: \(keptGroups.count) (≥\(shortPauseThreshold)帧相同)")
        print("   📈 保留率: \(String(format: "%.1f", Double(keptGroups.count) / Double(frameGroups.count) * 100))%")

        // 🚨 保底机制：如果没有保留任何群组，降低阈值重新处理
        if keptGroups.isEmpty && !frameGroups.isEmpty {
            print("🚨 警告：\(shortPauseThreshold)帧阈值过严，没有保留任何群组！")
            print("🔄 启用保底机制：降低阈值到5帧重新处理")

            let fallbackThreshold = 5
            let fallbackKeptGroups = frameGroups.filter { $0.count >= fallbackThreshold }

            if !fallbackKeptGroups.isEmpty {
                print("✅ 保底机制成功：使用\(fallbackThreshold)帧阈值保留了\(fallbackKeptGroups.count)个群组")
                // 使用保底阈值重新选择代表帧
                for (groupIndex, group) in fallbackKeptGroups.enumerated() {
                    let duration = Double(group.count) * frameExtractionInterval
                    let representatives = selectRepresentativeFramesWithThreshold(from: group, groupIndex: groupIndex, duration: duration, threshold: fallbackThreshold)
                    uniqueFrames.append(contentsOf: representatives)
                }
            } else {
                print("❌ 保底机制失败：即使5帧阈值也没有保留任何群组")
                print("🔄 最终保底：保留最大的3个群组")
                let sortedGroups = frameGroups.sorted { $0.count > $1.count }
                let topGroups = Array(sortedGroups.prefix(3))
                for (groupIndex, group) in topGroups.enumerated() {
                    let representative = group[group.count / 2] // 选择中间帧
                    uniqueFrames.append(representative)
                    print("🔄 最终保底群组#\(groupIndex + 1): \(group.count)帧 → 保留1帧#\(representative.originalIndex)")
                }
            }
        }

        return uniqueFrames
    }

    // MARK: - OCR处理所有帧
    private func performOCROnAllFrames(_ frames: [NumberedFrame], appType: DeliveryAppType = .justPhoto, progressCallback: ((String, Double) -> Void)?) async -> [NumberedFrame] {
        print("🔍 开始对所有\(frames.count)帧进行OCR识别...")

        var framesWithOCR: [NumberedFrame] = []
        let ocrService = OCRService()

        for (index, frame) in frames.enumerated() {
            let progress = 0.2 + (Double(index) / Double(frames.count)) * 0.2 // OCR占20%进度
            let progressText = String(format: NSLocalizedString("ocr_processing_frame_progress", comment: ""), index + 1, frames.count)
            progressCallback?(progressText, progress)

            // 🚫 GoFo暂时禁用OCR处理 - 直接跳过OCR，只保留帧用于AI处理
            if appType == .gofo {
                print("🚫 GoFo OCR已禁用: 帧#\(frame.originalIndex) 跳过OCR处理，直接用于AI识别")
                framesWithOCR.append(frame) // 保留帧但不进行OCR
                continue
            }

            do {
                let startTime = Date()
                // 使用标准OCR方法处理所有应用类型
                let ocrResponse = try await ocrService.recognizeText(from: frame.image)
                let processingTime = Date().timeIntervalSince(startTime)

                // 解析OCR结果
                let ocrResult = parseOCRResult(ocrResponse, processingTime: processingTime)

                // 创建带OCR结果的帧
                var frameWithOCR = frame
                frameWithOCR.ocrResult = ocrResult
                framesWithOCR.append(frameWithOCR)

                print("✅ 帧#\(frame.originalIndex) OCR完成: \(ocrResult.textBlocks)个文本块, 置信度\(Int(ocrResult.confidence * 100))%")

            } catch {
                print("❌ 帧#\(frame.originalIndex) OCR失败: \(error)")
                // 即使OCR失败也保留帧，但没有OCR结果
                framesWithOCR.append(frame)
            }
        }

        print("🔍 OCR处理完成: \(framesWithOCR.count)帧，其中\(framesWithOCR.filter { $0.ocrResult != nil }.count)帧有OCR结果")
        return framesWithOCR
    }

    // MARK: - 计算帧间相似度
    private func calculateFrameSimilarity(_ image1: UIImage, _ image2: UIImage) async -> Float {
        guard let _ = image1.cgImage,
              let _ = image2.cgImage else {
            return 0.0
        }

        // 使用感知哈希算法进行更精确的相似度计算
        return await calculatePerceptualSimilarity(image1, image2)
    }

    // MARK: - 解析OCR结果
    private func parseOCRResult(_ ocrResponse: OCRService.OCRResponse, processingTime: TimeInterval) -> OCRFrameResult {
        let fullText = ocrResponse.fullText

        // 提取地址（简化版，可以进一步优化）
        let addresses = extractAddresses(from: fullText)

        // 提取停靠点号码
        let stopNumbers = extractStopNumbers(from: fullText)

        // 提取快递单号
        let trackingNumbers = extractTrackingNumbers(from: fullText)

        return OCRFrameResult(
            fullText: fullText,
            addresses: addresses,
            stopNumbers: stopNumbers,
            trackingNumbers: trackingNumbers,
            confidence: ocrResponse.confidence,
            textBlocks: ocrResponse.results.count,
            processingTime: processingTime
        )
    }

    // MARK: - 基于OCR内容智能群组去重
    internal func removeRedundantFramesBasedOnOCR(_ frames: [NumberedFrame]) async -> [NumberedFrame] {
        guard frames.count > 1 else { return frames }

        print("📝 开始基于OCR内容智能群组去重，共\(frames.count)帧...")

        // 🎯 新逻辑：将OCR内容相似的帧分组，每组选择最佳代表帧
        var frameGroups: [[NumberedFrame]] = []
        var processedFrames: Set<Int> = []

        for (index, frame) in frames.enumerated() {
            if processedFrames.contains(index) { continue }

            guard let ocrResult = frame.ocrResult else {
                // 没有OCR结果的帧单独成组
                frameGroups.append([frame])
                processedFrames.insert(index)
                continue
            }

            // 创建新组，以当前帧为起点
            var currentGroup: [NumberedFrame] = [frame]
            processedFrames.insert(index)

            // 查找与当前帧内容相似的其他帧
            for (otherIndex, otherFrame) in frames.enumerated() {
                if processedFrames.contains(otherIndex) { continue }

                guard let otherOCR = otherFrame.ocrResult else { continue }

                if isOCRContentSimilar(ocrResult, otherOCR) {
                    currentGroup.append(otherFrame)
                    processedFrames.insert(otherIndex)
                }
            }

            frameGroups.append(currentGroup)
        }

        // 🎯 从每组中选择最佳代表帧
        var uniqueFrames: [NumberedFrame] = []
        var totalDuplicates = 0

        for (groupIndex, group) in frameGroups.enumerated() {
            let groupDuration = group.count > 1 ? group.last!.timestamp - group.first!.timestamp : 0
            let selectedFrames = selectRepresentativeFramesForOCR(from: group, groupIndex: groupIndex, duration: groupDuration)

            uniqueFrames.append(contentsOf: selectedFrames)
            let duplicatesInGroup = group.count - selectedFrames.count
            totalDuplicates += duplicatesInGroup
        }

        let removalPercentage = Double(totalDuplicates) / Double(frames.count) * 100
        print("📝 OCR智能群组去重完成: \(frames.count) → \(uniqueFrames.count) 帧")
        print("📊 检测到 \(frameGroups.count) 个内容群组，移除 \(totalDuplicates) 个重复帧 (\(String(format: "%.1f", removalPercentage))%)")

        // 🎯 新逻辑统计分析
        let discardedGroups = frameGroups.filter { $0.count < shortPauseThreshold }
        let keptGroups = frameGroups.filter { $0.count >= shortPauseThreshold }

        print("📊 OCR \(shortPauseThreshold)帧阈值去重统计:")
        print("   🗑️ 丢弃群组: \(discardedGroups.count) (<\(shortPauseThreshold)帧相同)")
        print("   ✅ 保留群组: \(keptGroups.count) (≥\(shortPauseThreshold)帧相同)")
        print("   📈 保留率: \(String(format: "%.1f", Double(keptGroups.count) / Double(frameGroups.count) * 100))%")

        // 🚨 OCR保底机制：如果没有保留任何群组，降低阈值重新处理
        if keptGroups.isEmpty && !frameGroups.isEmpty {
            print("🚨 OCR警告：\(shortPauseThreshold)帧阈值过严，没有保留任何群组！")
            print("🔄 OCR保底机制：降低阈值到5帧重新处理")

            let fallbackThreshold = 5
            let fallbackKeptGroups = frameGroups.filter { $0.count >= fallbackThreshold }

            if !fallbackKeptGroups.isEmpty {
                print("✅ OCR保底机制成功：使用\(fallbackThreshold)帧阈值保留了\(fallbackKeptGroups.count)个群组")
                // 使用保底阈值重新选择代表帧
                for (groupIndex, group) in fallbackKeptGroups.enumerated() {
                    let duration = Double(group.count) * frameExtractionInterval
                    let representatives = selectRepresentativeFramesForOCRWithThreshold(from: group, groupIndex: groupIndex, duration: duration, threshold: fallbackThreshold)
                    uniqueFrames.append(contentsOf: representatives)
                }
            } else {
                print("❌ OCR保底机制失败：即使5帧阈值也没有保留任何群组")
                print("🔄 OCR最终保底：保留最大的3个群组")
                let sortedGroups = frameGroups.sorted { $0.count > $1.count }
                let topGroups = Array(sortedGroups.prefix(3))
                for (groupIndex, group) in topGroups.enumerated() {
                    let representative = group.max { frame1, frame2 in
                        let confidence1 = frame1.ocrResult?.confidence ?? 0
                        let confidence2 = frame2.ocrResult?.confidence ?? 0
                        return confidence1 < confidence2
                    } ?? group[group.count / 2]
                    uniqueFrames.append(representative)
                    print("🔄 OCR最终保底群组#\(groupIndex + 1): \(group.count)帧 → 保留1帧#\(representative.originalIndex)")
                }
            }
        }

        return uniqueFrames
    }

    // MARK: - 基于帧内容的智能去重（确保每个地址都被捕获）
    internal func removeRedundantFramesBasedOnAddress(_ frames: [NumberedFrame]) async -> [NumberedFrame] {
        guard frames.count > 1 else { return frames }

        print("📱 开始基于帧内容的智能去重，共\(frames.count)帧...")
        print("🎯 目标：确保视频中每个地址都被捕获，一帧可能包含多个地址")

        // 1. 创建地址集合来跟踪所有唯一地址
        var allUniqueAddresses: Set<String> = []
        var frameAddressMap: [Int: Set<String>] = [:]
        var framesWithoutAddress: [NumberedFrame] = []

        // 2. 分析每帧包含的地址
        for frame in frames {
            guard let ocrResult = frame.ocrResult, !ocrResult.addresses.isEmpty else {
                framesWithoutAddress.append(frame)
                continue
            }

            // 标准化该帧中的所有地址
            var frameAddresses: Set<String> = []
            for address in ocrResult.addresses {
                let normalizedAddress = normalizeAddress(address)
                frameAddresses.insert(normalizedAddress)
                allUniqueAddresses.insert(normalizedAddress)
            }

            frameAddressMap[frame.originalIndex] = frameAddresses
            print("📱 帧#\(frame.originalIndex): 包含\(frameAddresses.count)个地址")
        }

        print("📊 内容分析统计:")
        print("   📍 视频中总共发现 \(allUniqueAddresses.count) 个不同地址")
        print("   📱 有地址的帧: \(frameAddressMap.count) 帧")
        print("   ❓ 无地址帧: \(framesWithoutAddress.count) 帧")

        // 检测录制质量
        let addressFrameRatio = Double(frameAddressMap.count) / Double(frames.count)
        let confidences = frames.compactMap { $0.ocrResult?.confidence }
        let averageConfidence = confidences.isEmpty ? 0.0 : confidences.reduce(0.0) { $0 + Double($1) } / Double(confidences.count)

        print("📊 录制质量分析:")
        print("   📱 有效帧比例: \(String(format: "%.1f", addressFrameRatio * 100))%")
        print("   🎯 平均OCR置信度: \(String(format: "%.2f", averageConfidence))")

        // 质量警告
        if addressFrameRatio < 0.1 { // 少于10%的帧有地址
            print("⚠️  录制质量警告: 有效地址帧过少 (\(String(format: "%.1f", addressFrameRatio * 100))%)")
            print("💡 建议: 录制时请稍微放慢滚动速度，在每个地址区域停留1-2秒")
        }

        if averageConfidence < 0.5 { // OCR置信度过低
            print("⚠️  图像质量警告: OCR识别置信度较低 (\(String(format: "%.2f", averageConfidence)))")
            print("💡 建议: 请确保录制时屏幕清晰，避免快速滚动造成模糊")
        }

        // 3. 处理极端情况：如果没有任何地址信息
        if allUniqueAddresses.isEmpty {
            print("❌ 严重警告: 视频中未识别到任何地址信息！")
            print("💡 可能原因:")
            print("   • 滚动速度过快，导致所有帧都模糊")
            print("   • 视频内容不包含地址信息")
            print("   • OCR识别失败")
            print("🔄 建议操作:")
            print("   • 重新录制，放慢滚动速度")
            print("   • 在每个地址区域停留1-2秒")
            print("   • 确保屏幕亮度充足，文字清晰")
            print("📱 降级处理: 保留部分原始帧供手动处理")

            // 降级处理：返回一些分散的帧
            let fallbackFrames = await selectFallbackFrames(frames)
            return fallbackFrames
        }

        // 4. 使用贪心算法选择最少的帧来覆盖所有地址
        var selectedFrames: [NumberedFrame] = []
        var coveredAddresses: Set<String> = []
        var remainingFrames = frames.filter { frameAddressMap[$0.originalIndex] != nil }

        while coveredAddresses.count < allUniqueAddresses.count && !remainingFrames.isEmpty {
            // 找到能覆盖最多未覆盖地址的帧
            var bestFrame: NumberedFrame?
            var maxNewAddresses = 0
            var bestNewAddresses: Set<String> = []

            for frame in remainingFrames {
                guard let frameAddresses = frameAddressMap[frame.originalIndex] else { continue }

                let newAddresses = frameAddresses.subtracting(coveredAddresses)
                if newAddresses.count > maxNewAddresses {
                    maxNewAddresses = newAddresses.count
                    bestFrame = frame
                    bestNewAddresses = newAddresses
                }
                // 如果新地址数量相同，选择OCR置信度更高的帧
                else if newAddresses.count == maxNewAddresses && newAddresses.count > 0 {
                    let currentConfidence = frame.ocrResult?.confidence ?? 0
                    let bestConfidence = bestFrame?.ocrResult?.confidence ?? 0
                    if currentConfidence > bestConfidence {
                        bestFrame = frame
                        bestNewAddresses = newAddresses
                    }
                }
            }

            if let selectedFrame = bestFrame {
                selectedFrames.append(selectedFrame)
                coveredAddresses.formUnion(bestNewAddresses)
                remainingFrames.removeAll { $0.originalIndex == selectedFrame.originalIndex }

                print("✅ 选择帧#\(selectedFrame.originalIndex): 新增\(bestNewAddresses.count)个地址 (总覆盖:\(coveredAddresses.count)/\(allUniqueAddresses.count))")
                for address in bestNewAddresses {
                    print("   📍 新地址: \(address)")
                }
            } else {
                break // 没有更多有用的帧
            }
        }

        // 5. 处理没有地址的帧（使用传统去重方法）
        if !framesWithoutAddress.isEmpty {
            print("🔍 处理 \(framesWithoutAddress.count) 个无地址帧...")
            let dedupedNoAddress = await removeRedundantFrames(framesWithoutAddress)
            selectedFrames.append(contentsOf: dedupedNoAddress)
            print("   → 保留 \(dedupedNoAddress.count) 个无地址帧")
        }

        // 6. 按原始时间戳排序
        selectedFrames.sort { $0.timestamp < $1.timestamp }

        let totalDuplicates = frames.count - selectedFrames.count
        let removalPercentage = Double(totalDuplicates) / Double(frames.count) * 100
        let coveragePercentage = Double(coveredAddresses.count) / Double(allUniqueAddresses.count) * 100

        print("📱 帧内容去重完成: \(frames.count) → \(selectedFrames.count) 帧")
        print("📊 统计信息:")
        print("   📍 视频总地址数: \(allUniqueAddresses.count)")
        print("   ✅ 覆盖地址数: \(coveredAddresses.count)")
        print("   📊 地址覆盖率: \(String(format: "%.1f", coveragePercentage))%")
        print("   🗑️ 移除重复帧: \(totalDuplicates) (\(String(format: "%.1f", removalPercentage))%)")

        return selectedFrames
    }

    // MARK: - 地址标准化辅助函数
    private func normalizeAddress(_ address: String) -> String {
        // 移除多余空格，统一大小写，移除标点符号差异
        return address
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .lowercased()
            .replacingOccurrences(of: "apartment", with: "apt")
            .replacingOccurrences(of: "suite", with: "ste")
            .replacingOccurrences(of: "  ", with: " ") // 移除双空格
            .replacingOccurrences(of: "[^a-z0-9 ]", with: "", options: .regularExpression) // 移除特殊字符
    }

    // MARK: - 降级处理：当没有地址信息时的备用方案
    private func selectFallbackFrames(_ frames: [NumberedFrame]) async -> [NumberedFrame] {
        print("🔄 执行降级处理策略...")

        // 如果帧数很少，直接返回所有帧
        if frames.count <= 5 {
            print("   📱 帧数较少，保留所有 \(frames.count) 帧")
            return frames
        }

        // 选择分散的帧，确保覆盖整个时间范围
        let targetFrameCount = min(10, max(3, frames.count / 10)) // 保留3-10帧
        var selectedFrames: [NumberedFrame] = []

        let interval = frames.count / targetFrameCount
        for i in 0..<targetFrameCount {
            let index = min(i * interval, frames.count - 1)
            selectedFrames.append(frames[index])
        }

        print("   📱 降级处理完成: \(frames.count) → \(selectedFrames.count) 帧")
        print("   💡 这些帧可供您手动查看和处理")

        return selectedFrames
    }

    // MARK: - 感知相似度计算（更精确的算法）
    private func calculatePerceptualSimilarity(_ image1: UIImage, _ image2: UIImage) async -> Float {
        // 缩小到固定尺寸以提高计算速度和一致性
        let targetSize = CGSize(width: 64, height: 64)
        guard let resized1 = resizeImageForComparison(image1, to: targetSize),
              let resized2 = resizeImageForComparison(image2, to: targetSize) else {
            return 0.0
        }

        // 转换为灰度并获取像素数据
        guard let grayData1 = getGrayscalePixelData(from: resized1),
              let grayData2 = getGrayscalePixelData(from: resized2) else {
            return 0.0
        }

        // 计算结构相似性
        return calculateStructuralSimilarity(grayData1, grayData2, width: 64, height: 64)
    }

    // MARK: - 获取灰度像素数据
    private func getGrayscalePixelData(from image: UIImage) -> [UInt8]? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 1
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow

        var pixelData = [UInt8](repeating: 0, count: totalBytes)

        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        )

        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        return pixelData
    }

    // MARK: - 结构相似性计算
    private func calculateStructuralSimilarity(_ data1: [UInt8], _ data2: [UInt8], width: Int, height: Int) -> Float {
        guard data1.count == data2.count else { return 0.0 }

        let totalPixels = data1.count

        // 计算均值
        let mean1 = Float(data1.reduce(0, { $0 + Int($1) })) / Float(totalPixels)
        let mean2 = Float(data2.reduce(0, { $0 + Int($1) })) / Float(totalPixels)

        // 计算方差和协方差
        var variance1: Float = 0
        var variance2: Float = 0
        var covariance: Float = 0

        for i in 0..<totalPixels {
            let diff1 = Float(data1[i]) - mean1
            let diff2 = Float(data2[i]) - mean2

            variance1 += diff1 * diff1
            variance2 += diff2 * diff2
            covariance += diff1 * diff2
        }

        variance1 /= Float(totalPixels - 1)
        variance2 /= Float(totalPixels - 1)
        covariance /= Float(totalPixels - 1)

        // SSIM常数
        let c1: Float = 0.01 * 0.01 * 255 * 255
        let c2: Float = 0.03 * 0.03 * 255 * 255

        // 计算SSIM
        let numerator = (2 * mean1 * mean2 + c1) * (2 * covariance + c2)
        let denominator = (mean1 * mean1 + mean2 * mean2 + c1) * (variance1 + variance2 + c2)

        guard denominator > 0 else { return 0.0 }

        let ssim = numerator / denominator
        return max(0.0, min(1.0, ssim)) // 确保结果在[0,1]范围内
    }

    // MARK: - 相似度分析
    private func analyzeSimilarityDistribution(_ scores: [Float]) async {
        guard !scores.isEmpty else { return }

        let avgSimilarity = scores.reduce(0, +) / Float(scores.count)
        let maxSimilarity = scores.max() ?? 0
        let minSimilarity = scores.min() ?? 0

        // 🎯 SSIM相似度分析（针对新算法调整阈值范围）
        let highSimilarityCount = scores.filter { $0 > 0.95 }.count  // 极高相似度
        let mediumSimilarityCount = scores.filter { $0 > 0.85 && $0 <= 0.95 }.count  // 中等相似度
        let lowSimilarityCount = scores.filter { $0 <= 0.85 }.count  // 低相似度

        print("📊 SSIM相似度分析:")
        print("   平均相似度: \(String(format: "%.3f", avgSimilarity))")
        print("   相似度范围: \(String(format: "%.3f", minSimilarity)) - \(String(format: "%.3f", maxSimilarity))")
        print("   极高相似度(>0.95): \(highSimilarityCount)帧 - 可能是重复帧")
        print("   中等相似度(0.85-0.95): \(mediumSimilarityCount)帧 - 轻微变化")
        print("   低相似度(<0.85): \(lowSimilarityCount)帧 - 明显不同")

        // 🎯 SSIM算法的动态阈值优化建议
        if avgSimilarity > 0.98 {
            print("💡 优化建议: 视频重复度极高，建议提高阈值到0.97以获得更好的去重效果")
        } else if avgSimilarity < 0.85 {
            print("💡 优化建议: 视频变化较大，建议降低阈值到0.92以保留更多细节")
        } else {
            print("✅ 当前SSIM阈值(\(String(format: "%.3f", similarityThreshold)))适合此视频的特征")
        }

        // 🔧 自适应阈值调整
        if adaptiveThresholdEnabled {
            let suggestedThreshold = calculateOptimalThreshold(avgSimilarity: avgSimilarity, scores: scores)
            if abs(suggestedThreshold - similarityThreshold) > 0.02 {
                print("🔧 自适应调整: 建议将阈值从 \(String(format: "%.3f", similarityThreshold)) 调整到 \(String(format: "%.3f", suggestedThreshold))")
                // 注意：这里只是建议，实际调整需要在下次处理时应用
            }
        }
    }

    // MARK: - 计算最优阈值（SSIM算法优化）
    private func calculateOptimalThreshold(avgSimilarity: Float, scores: [Float]) -> Float {
        // 🎯 基于SSIM相似度和分布特征计算最优阈值
        var optimalThreshold = similarityThreshold

        if avgSimilarity > 0.98 {
            // 极高相似度视频，提高阈值以增强去重
            optimalThreshold = min(maxThreshold, avgSimilarity - 0.01)
        } else if avgSimilarity < 0.85 {
            // 低相似度视频，降低阈值以保留细节
            optimalThreshold = max(minThreshold, avgSimilarity + 0.08)
        } else {
            // 中等相似度，微调阈值
            optimalThreshold = avgSimilarity + 0.02
        }

        // 确保在SSIM合理范围内
        return max(minThreshold, min(maxThreshold, optimalThreshold))
    }

    // MARK: - 检测滚动方向
    enum ScrollDirection {
        case vertical
        case horizontal
        case unknown
    }

    private func detectScrollDirection(_ frames: [NumberedFrame]) async -> ScrollDirection {
        guard frames.count >= 2 else { return .unknown }

        // 简化实现：假设大多数录屏是垂直滚动
        // 可以通过分析连续帧的内容变化来更精确地检测
        return .vertical
    }

    // MARK: - 调试功能：保存帧供检查（按原始编号排序）
    private func saveFramesForDebugging(_ frames: [NumberedFrame]) async -> [URL] {
        guard saveFramesForDebugging else { return [] }

        var savedURLs: [URL] = []
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let debugFolder = documentsPath.appendingPathComponent("VideoFrames_Debug")

        // 创建调试文件夹
        try? FileManager.default.createDirectory(at: debugFolder, withIntermediateDirectories: true)

        // 清理旧文件
        try? FileManager.default.removeItem(at: debugFolder)
        try? FileManager.default.createDirectory(at: debugFolder, withIntermediateDirectories: true)

        // 🎯 按时间戳排序，确保时间顺序正确
        let sortedFrames = frames.sorted { $0.timestamp < $1.timestamp }
        let framesToSave = Array(sortedFrames.prefix(maxSavedFrames))
        print("📁 保存\(framesToSave.count)帧到: \(debugFolder.path)")
        print("🔢 保存顺序按时间戳排序，编号: \(framesToSave.map { $0.originalIndex })")
        print("⏰ 时间戳范围: \(String(format: "%.1f", framesToSave.first?.timestamp ?? 0))s - \(String(format: "%.1f", framesToSave.last?.timestamp ?? 0))s")

        for numberedFrame in framesToSave {
            // 🎯 使用原始编号作为文件名，保持一致性
            let fileName = String(format: "frame_%03d.jpg", numberedFrame.originalIndex)
            let fileURL = debugFolder.appendingPathComponent(fileName)

            if let imageData = numberedFrame.image.jpegData(compressionQuality: 0.8) {
                do {
                    try imageData.write(to: fileURL)
                    savedURLs.append(fileURL)
                    print("💾 保存帧#\(numberedFrame.originalIndex): \(fileName) (时间戳: \(String(format: "%.1f", numberedFrame.timestamp))s)")
                } catch {
                    print("❌ 保存帧#\(numberedFrame.originalIndex)失败: \(error)")
                }
            }
        }

        // 生成索引文件
        let indexContent = generateFrameIndex(savedURLs, frames: framesToSave)
        let indexURL = debugFolder.appendingPathComponent("frame_index.txt")
        try? indexContent.write(to: indexURL, atomically: true, encoding: .utf8)

        print("📋 生成帧索引文件: frame_index.txt")
        print("📂 调试文件夹路径: \(debugFolder.path)")

        return savedURLs
    }

    // 生成帧索引文件（包含时间戳信息）
    private func generateFrameIndex(_ frameURLs: [URL], frames: [NumberedFrame]) -> String {
        var content = "🎬 视频帧调试信息（按时间戳排序）\n"
        content += "生成时间: \(Date())\n"
        content += "总帧数: \(frameURLs.count)\n"
        content += "编号范围: 1-\(frameURLs.count) (连续编号)\n\n"

        for (index, numberedFrame) in frames.enumerated() {
            let url = frameURLs[index]
            content += "帧#\(numberedFrame.originalIndex): \(url.lastPathComponent)\n"
            content += "  时间戳: \(String(format: "%.2f", numberedFrame.timestamp))s\n"
            content += "  提取时间: \(numberedFrame.extractionTime)\n\n"
        }

        content += "\n📱 如何查看:\n"
        content += "1. 在Files应用中打开\n"
        content += "2. 导航到: On My iPhone > NaviBatch > VideoFrames_Debug\n"
        content += "3. 点击图片查看每帧内容\n"
        content += "4. 文件名中的数字现在是连续编号(1,2,3...)\n"
        content += "5. 编号顺序严格按照视频时间戳排序\n"

        return content
    }

    // MARK: - OCR内容提取辅助方法

    // 提取地址信息 - GoFo优化版
    internal func extractAddresses(from text: String) -> [String] {
        var addresses: [String] = []
        let lines = text.components(separatedBy: .newlines)

        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // 🎯 GoFo地址识别优化：支持更多地址格式和缩写
            if trimmed.count > 8 && isValidAddress(trimmed) {
                addresses.append(trimmed)
            }
        }

        return addresses
    }

    // 🎯 增强的地址验证方法 - 修复SpeedX地址识别问题
    private func isValidAddress(_ text: String) -> Bool {
        let upperText = text.uppercased()

        // 1. 检查常见地址关键词（完整形式）
        let fullKeywords = [
            "STREET", "AVENUE", "BOULEVARD", "DRIVE", "ROAD", "LANE", "WAY",
            "COURT", "PLACE", "CIRCLE", "APARTMENT", "UNIT", "SUITE", "HIGHWAY"
        ]

        // 2. 检查常见地址缩写（更全面的检查）
        let abbreviations = [
            " ST", " AVE", " BLVD", " DR", " RD", " LN", " WAY",
            " CT", " PL", " CIR", " APT", " UNIT", " STE", " HWY",
            "ST,", "AVE,", "BLVD,", "DR,", "RD,", "LN,", "WAY,",
            "CT,", "PL,", "CIR,", "APT,", "UNIT,", "STE,", "HWY,"
        ]

        // 3. 检查是否包含地址关键词
        let hasAddressKeyword = fullKeywords.contains { upperText.contains($0) } ||
                               abbreviations.contains { upperText.contains($0) }

        // 4. 检查是否包含数字（门牌号）
        let hasNumber = text.rangeOfCharacter(from: .decimalDigits) != nil

        // 5. 检查是否包含邮编（美国5位数字邮编，不一定在结尾）
        let hasZipCode = text.range(of: "\\b\\d{5}\\b", options: .regularExpression) != nil

        // 6. 检查是否包含美国州缩写（CA, NY, TX等）
        let usStates = ["AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"]
        let hasUSState = usStates.contains { state in
            upperText.contains(", \(state),") || upperText.contains(", \(state) ") || upperText.hasSuffix(", \(state)")
        }

        // 7. 过滤掉明显不是地址的内容
        let excludeKeywords = [
            "PREVIEW", "当前是任务预览页面", "请收件后再操作", "PRINT", "BACK", "NEXT",
            "GF", "TEP", "SHARRON", "MATT", "ANNA", "RORY", "ERIN", "MEGAN",
            "JOSEPH", "CIARA", "TERI", "DAWN", "TOM", "GABY", "DONNA", "JOSIE",
            "CHROME", "打开", "SPXSF", "#SPXSF"
        ]

        let hasExcludeKeyword = excludeKeywords.contains { upperText.contains($0) }

        // 🎯 新的地址判断逻辑：更宽松的条件
        // 地址必须：(有地址关键词 OR 有邮编 OR 有美国州缩写) AND 有数字 AND 不包含排除关键词 AND 长度合理
        let isValid = (hasAddressKeyword || hasZipCode || hasUSState) && hasNumber && !hasExcludeKeyword && text.count >= 8

        if isValid {
            print("✅ 识别为地址: \(text)")
        } else {
            print("❌ 跳过非地址: \(text) (关键词:\(hasAddressKeyword), 邮编:\(hasZipCode), 州:\(hasUSState), 数字:\(hasNumber), 排除:\(hasExcludeKeyword))")
        }

        return isValid
    }

    // 提取停靠点号码
    internal func extractStopNumbers(from text: String) -> [String] {
        var stopNumbers: [String] = []

        // SpeedX停靠点模式：停靠点: 数字
        let stopPattern = #"停靠点[:\s]*(\d+)"#
        if let regex = try? NSRegularExpression(pattern: stopPattern) {
            let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            for match in matches {
                if let range = Range(match.range(at: 1), in: text) {
                    stopNumbers.append(String(text[range]))
                }
            }
        }

        // 其他可能的停靠点模式
        let numberPattern = #"\b\d{1,3}\b"#
        if let regex = try? NSRegularExpression(pattern: numberPattern) {
            let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            for match in matches {
                if let range = Range(match.range, in: text) {
                    let number = String(text[range])
                    if let num = Int(number), num >= 1 && num <= 200 {
                        stopNumbers.append(number)
                    }
                }
            }
        }

        return Array(Set(stopNumbers)) // 去重
    }

    // 提取快递单号
    private func extractTrackingNumbers(from text: String) -> [String] {
        var trackingNumbers: [String] = []

        // 常见快递单号模式
        let patterns = [
            #"\b[A-Z0-9]{10,20}\b"#,  // 通用模式
            #"\b\d{12,15}\b"#,        // 纯数字单号
            #"\b[A-Z]{2}\d{9}[A-Z]{2}\b"# // 国际单号模式
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern) {
                let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
                for match in matches {
                    if let range = Range(match.range, in: text) {
                        trackingNumbers.append(String(text[range]))
                    }
                }
            }
        }

        return Array(Set(trackingNumbers)) // 去重
    }

    // 比较OCR内容相似度
    internal func isOCRContentSimilar(_ result1: OCRFrameResult, _ result2: OCRFrameResult) -> Bool {
        // 🎯 GoFo优化：更严格的去重逻辑，避免误判不同内容为重复

        // 1. 检查停靠点号码是否有重叠（必须有相同的停靠点才算重复）
        let stopNumbersIntersection = Set(result1.stopNumbers).intersection(Set(result2.stopNumbers))
        let stopNumbersMatch = stopNumbersIntersection.count > 0

        // 2. 检查快递单号是否有重叠（必须有相同的快递单号才算重复）
        let trackingNumbersIntersection = Set(result1.trackingNumbers).intersection(Set(result2.trackingNumbers))
        let trackingNumbersMatch = trackingNumbersIntersection.count > 0

        // 3. 检查地址完全匹配度（提高阈值，避免因邮编相同而误判）
        let addressSimilarity = calculateAddressSimilarity(result1.addresses, result2.addresses)
        let addressesHighlySimilar = addressSimilarity > 0.95 // 🎯 提高到95%相似度

        // 4. 检查整体文本相似度（提高阈值，避免因界面框架相似而误判）
        let textSimilarity = calculateTextSimilarity(result1.fullText, result2.fullText)
        let _ = textSimilarity > 0.95 // 🎯 提高到95%相似度

        // 🚀 更严格的判断：必须同时满足多个条件才认为是重复
        // 只有当停靠点相同 AND (地址高度相似 OR 快递单号相同) 时才认为重复
        let isReallyDuplicate = stopNumbersMatch && (addressesHighlySimilar || trackingNumbersMatch)

        // 📊 调试信息
        if isReallyDuplicate {
            print("🔍 检测到重复内容:")
            print("   停靠点重叠: \(stopNumbersIntersection)")
            print("   地址相似度: \(String(format: "%.3f", addressSimilarity))")
            print("   快递单号重叠: \(trackingNumbersIntersection)")
            print("   文本相似度: \(String(format: "%.3f", textSimilarity))")
        }

        return isReallyDuplicate
    }

    // 计算地址相似度
    private func calculateAddressSimilarity(_ addresses1: [String], _ addresses2: [String]) -> Double {
        guard !addresses1.isEmpty && !addresses2.isEmpty else { return 0.0 }

        var maxSimilarity = 0.0
        for addr1 in addresses1 {
            for addr2 in addresses2 {
                let similarity = calculateTextSimilarity(addr1, addr2)
                maxSimilarity = max(maxSimilarity, similarity)
            }
        }

        return maxSimilarity
    }

    // 计算文本相似度（简化版Levenshtein距离）
    private func calculateTextSimilarity(_ text1: String, _ text2: String) -> Double {
        let len1 = text1.count
        let len2 = text2.count

        guard len1 > 0 && len2 > 0 else { return 0.0 }

        // 简化版：计算公共子串比例
        let commonLength = longestCommonSubsequence(text1, text2)
        return Double(commonLength) / Double(max(len1, len2))
    }

    // 最长公共子序列
    private func longestCommonSubsequence(_ text1: String, _ text2: String) -> Int {
        let chars1 = Array(text1)
        let chars2 = Array(text2)
        let m = chars1.count
        let n = chars2.count

        var dp = Array(repeating: Array(repeating: 0, count: n + 1), count: m + 1)

        for i in 1...m {
            for j in 1...n {
                if chars1[i-1] == chars2[j-1] {
                    dp[i][j] = dp[i-1][j-1] + 1
                } else {
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
                }
            }
        }

        return dp[m][n]
    }

    // MARK: - 内部测试方法

    /// 测试OCR内容去重功能
    internal func testOCRContentDeduplication() {
        print("🧪 开始OCR内容去重功能测试...")

        // 创建测试用的OCR结果
        let ocrResult1 = OCRFrameResult(
            fullText: "393 Mandarin Drive Apt 3, Daly City, CA, 94015\n停靠点: 1\nSPXSF123456789",
            addresses: ["393 Mandarin Drive Apt 3, Daly City, CA, 94015"],
            stopNumbers: ["1"],
            trackingNumbers: ["SPXSF123456789"],
            confidence: 0.95,
            textBlocks: 3,
            processingTime: 0.5
        )

        let ocrResult2 = OCRFrameResult(
            fullText: "393 Mandarin Drive Apt 3, Daly City, CA, 94015\n停靠点: 1\nSPXSF123456789",
            addresses: ["393 Mandarin Drive Apt 3, Daly City, CA, 94015"],
            stopNumbers: ["1"],
            trackingNumbers: ["SPXSF123456789"],
            confidence: 0.93,
            textBlocks: 3,
            processingTime: 0.6
        )

        let ocrResult3 = OCRFrameResult(
            fullText: "397 Imperial Way Unit 2, Daly City, CA, 94015\n停靠点: 2\nSPXSF987654321",
            addresses: ["397 Imperial Way Unit 2, Daly City, CA, 94015"],
            stopNumbers: ["2"],
            trackingNumbers: ["SPXSF987654321"],
            confidence: 0.92,
            textBlocks: 3,
            processingTime: 0.4
        )

        // 测试内容相似度比较
        let isSimilar12 = isOCRContentSimilar(ocrResult1, ocrResult2)
        let isSimilar13 = isOCRContentSimilar(ocrResult1, ocrResult3)

        print("📊 测试结果:")
        print("   相同内容比较 (result1 vs result2): \(isSimilar12 ? "✅ 正确识别为相似" : "❌ 错误识别为不相似")")
        print("   不同内容比较 (result1 vs result3): \(isSimilar13 ? "❌ 错误识别为相似" : "✅ 正确识别为不相似")")

        // 测试文本提取
        let testText = """
        393 Mandarin Drive Apt 3, Daly City, CA, 94015
        停靠点: 5
        SPXSF123456789
        """

        let extractedStopNumbers = extractStopNumbers(from: testText)
        let extractedAddresses = extractAddresses(from: testText)

        print("📝 文本提取测试:")
        print("   停靠点提取: \(extractedStopNumbers) (期望: [\"5\"])")
        print("   地址提取: \(extractedAddresses.count)个地址")

        print("🎉 OCR内容去重功能测试完成!")
    }

    // MARK: - 智能拼接
    private func stitchFramesToLongImage(_ frames: [NumberedFrame], scrollDirection: ScrollDirection) async -> UIImage? {
        guard !frames.isEmpty else { return nil }
        guard let firstFrame = frames.first else { return nil }

        if frames.count == 1 {
            return firstFrame.image
        }

        let frameWidth = firstFrame.image.size.width
        let frameHeight = firstFrame.image.size.height

        // 估算重叠区域（可以优化为更精确的重叠检测）
        let overlapRatio: CGFloat = 0.2 // 假设20%重叠
        let effectiveHeight = frameHeight * (1 - overlapRatio)

        let totalHeight = frameHeight + effectiveHeight * CGFloat(frames.count - 1)
        let canvasSize = CGSize(width: frameWidth, height: totalHeight)

        UIGraphicsBeginImageContextWithOptions(canvasSize, false, firstFrame.image.scale)
        defer { UIGraphicsEndImageContext() }

        var currentY: CGFloat = 0

        for (index, numberedFrame) in frames.enumerated() {
            let drawRect = CGRect(x: 0, y: currentY, width: frameWidth, height: frameHeight)
            numberedFrame.image.draw(in: drawRect)

            if index == 0 {
                currentY += frameHeight
            } else {
                currentY += effectiveHeight
            }
        }

        let longImage = UIGraphicsGetImageFromCurrentImageContext()
        print("🖼️ 拼接完成: \(frames.count) 帧 → \(canvasSize)")

        return longImage
    }

    // MARK: - 辅助方法
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)
        defer { UIGraphicsEndImageContext() }

        image.draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }

    // MARK: - 智能代表帧选择

    /// 根据群组大小和停留时间智能选择代表帧
    /// - Parameters:
    ///   - group: 帧群组
    ///   - groupIndex: 群组索引
    ///   - duration: 停留时长
    /// - Returns: 选中的代表帧数组
    private func selectRepresentativeFrames(from group: [NumberedFrame], groupIndex: Int, duration: Double) -> [NumberedFrame] {
        let frameCount = group.count

        // 🎯 新逻辑：超过30张一样的取一张，没有30张以上一样的不要
        if frameCount < shortPauseThreshold {
            // 🗑️ 少于30帧相同的 → 丢弃（不保留）
            print("🗑️ 群组#\(groupIndex + 1): \(frameCount)帧 < \(shortPauseThreshold)帧阈值 → 丢弃")
            return []  // 返回空数组，丢弃这个群组
        } else {
            // ✅ 超过30张相同的 → 取一张（选择中间帧，通常质量最好）
            let representative = group[frameCount / 2]
            print("✅ 群组#\(groupIndex + 1): \(frameCount)帧 ≥ \(shortPauseThreshold)帧阈值 → 保留1帧#\(representative.originalIndex)")
            print("   📊 停留时长: \(String(format: "%.1f", duration))秒")
            return [representative]
        }
    }

    /// OCR专用的智能代表帧选择（基于置信度优化）
    /// - Parameters:
    ///   - group: 帧群组
    ///   - groupIndex: 群组索引
    ///   - duration: 停留时长
    /// - Returns: 选中的代表帧数组
    private func selectRepresentativeFramesForOCR(from group: [NumberedFrame], groupIndex: Int, duration: Double) -> [NumberedFrame] {
        let frameCount = group.count

        // 🎯 新逻辑：超过30张一样的取一张，没有30张以上一样的不要
        if frameCount < shortPauseThreshold {
            // 🗑️ 少于30帧相同的 → 丢弃（不保留）
            print("🗑️ OCR群组#\(groupIndex + 1): \(frameCount)帧 < \(shortPauseThreshold)帧阈值 → 丢弃")
            return []  // 返回空数组，丢弃这个群组
        } else {
            // ✅ 超过30张相同的 → 取一张（选择置信度最高的帧）
            let representative = group.max { frame1, frame2 in
                let confidence1 = frame1.ocrResult?.confidence ?? 0
                let confidence2 = frame2.ocrResult?.confidence ?? 0
                return confidence1 < confidence2
            } ?? group[frameCount / 2]

            if let ocrResult = representative.ocrResult {
                print("✅ OCR群组#\(groupIndex + 1): \(frameCount)帧 ≥ \(shortPauseThreshold)帧阈值 → 保留1帧#\(representative.originalIndex)")
                print("   📊 停留时长: \(String(format: "%.1f", duration))秒")
                print("   🏆 置信度: \(String(format: "%.2f", ocrResult.confidence))")
                print("   📍 地址: \(ocrResult.addresses.prefix(2).joined(separator: ", "))")
                print("   🔢 停靠点: \(ocrResult.stopNumbers.joined(separator: ", "))")
            }
            return [representative]
        }
    }

    /// 使用自定义阈值选择代表帧（保底机制专用）
    /// - Parameters:
    ///   - group: 帧群组
    ///   - groupIndex: 群组索引
    ///   - duration: 停留时长
    ///   - threshold: 自定义阈值
    /// - Returns: 选中的代表帧数组
    private func selectRepresentativeFramesWithThreshold(from group: [NumberedFrame], groupIndex: Int, duration: Double, threshold: Int) -> [NumberedFrame] {
        let frameCount = group.count

        if frameCount < threshold {
            // 少于自定义阈值的丢弃
            print("🗑️ 保底群组#\(groupIndex + 1): \(frameCount)帧 < \(threshold)帧阈值 → 丢弃")
            return []
        } else {
            // 超过自定义阈值的保留1帧
            let representative = group[frameCount / 2]
            print("✅ 保底群组#\(groupIndex + 1): \(frameCount)帧 ≥ \(threshold)帧阈值 → 保留1帧#\(representative.originalIndex)")
            return [representative]
        }
    }

    /// OCR专用：使用自定义阈值选择代表帧（保底机制专用）
    /// - Parameters:
    ///   - group: 帧群组
    ///   - groupIndex: 群组索引
    ///   - duration: 停留时长
    ///   - threshold: 自定义阈值
    /// - Returns: 选中的代表帧数组
    private func selectRepresentativeFramesForOCRWithThreshold(from group: [NumberedFrame], groupIndex: Int, duration: Double, threshold: Int) -> [NumberedFrame] {
        let frameCount = group.count

        if frameCount < threshold {
            // 少于自定义阈值的丢弃
            print("🗑️ OCR保底群组#\(groupIndex + 1): \(frameCount)帧 < \(threshold)帧阈值 → 丢弃")
            return []
        } else {
            // 超过自定义阈值的保留1帧（选择置信度最高的）
            let representative = group.max { frame1, frame2 in
                let confidence1 = frame1.ocrResult?.confidence ?? 0
                let confidence2 = frame2.ocrResult?.confidence ?? 0
                return confidence1 < confidence2
            } ?? group[frameCount / 2]

            print("✅ OCR保底群组#\(groupIndex + 1): \(frameCount)帧 ≥ \(threshold)帧阈值 → 保留1帧#\(representative.originalIndex)")
            return [representative]
        }
    }

    // MARK: - 感知哈希方法（新方案）

    /// 计算图片的Difference Hash
    /// - Parameter image: 输入图片
    /// - Returns: 64位哈希字符串
    private func calculateDifferenceHash(_ image: UIImage) -> String? {
        guard let cgImage = image.cgImage else { return nil }

        // 1. 缩放到9x8像素（比较相邻像素需要9列）
        let width = 9
        let height = 8
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(data: nil, width: width, height: height,
                               bitsPerComponent: 8, bytesPerRow: width,
                               space: colorSpace, bitmapInfo: CGImageAlphaInfo.none.rawValue)

        guard let context = context else { return nil }

        // 2. 绘制缩放后的灰度图
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let pixels = data.assumingMemoryBound(to: UInt8.self)

        // 3. 计算差值哈希
        var hash: UInt64 = 0
        var bitIndex = 0

        for y in 0..<height {
            for x in 0..<(width-1) {
                let currentPixel = pixels[y * width + x]
                let nextPixel = pixels[y * width + x + 1]

                if currentPixel > nextPixel {
                    hash |= (1 << bitIndex)
                }
                bitIndex += 1
            }
        }

        // 4. 转换为16进制字符串
        return String(format: "%016llx", hash)
    }

    /// 计算两个哈希值的汉明距离
    /// - Parameters:
    ///   - hash1: 第一个哈希值
    ///   - hash2: 第二个哈希值
    /// - Returns: 汉明距离（0-64）
    private func hammingDistance(_ hash1: String, _ hash2: String) -> Int {
        guard hash1.count == hash2.count else { return 64 }

        // 🎯 修复：直接转换为UInt64进行位运算
        guard let val1 = UInt64(hash1, radix: 16),
              let val2 = UInt64(hash2, radix: 16) else {
            return 64
        }

        let xor = val1 ^ val2
        return xor.nonzeroBitCount
    }

    /// 计算两张图片的SSIM相似度
    /// - Parameters:
    ///   - image1: 第一张图片
    ///   - image2: 第二张图片
    /// - Returns: SSIM值（0-1，1表示完全相同）
    private func calculateSSIM(_ image1: UIImage, _ image2: UIImage) -> Float {
        guard let cgImage1 = image1.cgImage,
              let cgImage2 = image2.cgImage else { return 0.0 }

        // 缩放到相同尺寸进行比较（64x64足够用于相似度检测）
        let size = CGSize(width: 64, height: 64)

        guard let resized1 = resizeImage(cgImage1, to: size),
              let resized2 = resizeImage(cgImage2, to: size) else { return 0.0 }

        // 转换为灰度数据
        guard let data1 = getGrayscaleData(from: resized1),
              let data2 = getGrayscaleData(from: resized2) else { return 0.0 }

        // 计算SSIM
        return computeSSIM(data1: data1, data2: data2, width: 64, height: 64)
    }

    /// 缩放CGImage到指定尺寸
    private func resizeImage(_ cgImage: CGImage, to size: CGSize) -> CGImage? {
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(data: nil,
                               width: Int(size.width),
                               height: Int(size.height),
                               bitsPerComponent: 8,
                               bytesPerRow: Int(size.width),
                               space: colorSpace,
                               bitmapInfo: CGImageAlphaInfo.none.rawValue)

        guard let context = context else { return nil }

        context.draw(cgImage, in: CGRect(origin: .zero, size: size))
        return context.makeImage()
    }

    /// 获取图片的灰度数据
    private func getGrayscaleData(from cgImage: CGImage) -> [Float]? {
        let width = cgImage.width
        let height = cgImage.height
        let colorSpace = CGColorSpaceCreateDeviceGray()

        var pixelData = [UInt8](repeating: 0, count: width * height)
        let context = CGContext(data: &pixelData,
                               width: width,
                               height: height,
                               bitsPerComponent: 8,
                               bytesPerRow: width,
                               space: colorSpace,
                               bitmapInfo: CGImageAlphaInfo.none.rawValue)

        guard let context = context else { return nil }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 转换为Float数组
        return pixelData.map { Float($0) / 255.0 }
    }

    /// 计算SSIM核心算法
    private func computeSSIM(data1: [Float], data2: [Float], width: Int, height: Int) -> Float {
        let totalPixels = width * height
        guard data1.count == totalPixels && data2.count == totalPixels else { return 0.0 }

        // SSIM常数
        let c1: Float = 0.01 * 0.01
        let c2: Float = 0.03 * 0.03

        // 计算均值
        let mean1 = data1.reduce(0, +) / Float(totalPixels)
        let mean2 = data2.reduce(0, +) / Float(totalPixels)

        // 计算方差和协方差
        var variance1: Float = 0
        var variance2: Float = 0
        var covariance: Float = 0

        for i in 0..<totalPixels {
            let diff1 = data1[i] - mean1
            let diff2 = data2[i] - mean2

            variance1 += diff1 * diff1
            variance2 += diff2 * diff2
            covariance += diff1 * diff2
        }

        variance1 /= Float(totalPixels - 1)
        variance2 /= Float(totalPixels - 1)
        covariance /= Float(totalPixels - 1)

        // 计算SSIM
        let numerator = (2 * mean1 * mean2 + c1) * (2 * covariance + c2)
        let denominator = (mean1 * mean1 + mean2 * mean2 + c1) * (variance1 + variance2 + c2)

        return numerator / denominator
    }

    // MARK: - 新的重复帧检测方法

    /// 使用感知哈希进行重复帧检测（替代OCR方法）
    /// - Parameter frames: 输入帧数组
    /// - Returns: 去重后的帧数组
    private func detectDuplicateFramesWithPerceptualHash(_ frames: [NumberedFrame]) -> [NumberedFrame] {
        let startTime = Date()
        print("🚀 开始感知哈希重复帧检测，共\(frames.count)帧")

        // 第一步：为所有帧计算dHash
        var framesWithHash: [NumberedFrame] = []
        var hashCalculationTime: TimeInterval = 0

        for (index, frame) in frames.enumerated() {
            let hashStart = Date()
            var frameWithHash = frame
            frameWithHash.dHash = calculateDifferenceHash(frame.image)
            hashCalculationTime += Date().timeIntervalSince(hashStart)
            framesWithHash.append(frameWithHash)

            if (index + 1) % 50 == 0 {
                print("📊 已计算\(index + 1)/\(frames.count)帧的dHash")
            }
        }

        print("⚡ dHash计算完成，耗时\(String(format: "%.2f", hashCalculationTime))秒")
        print("📈 平均每帧\(String(format: "%.1f", hashCalculationTime * 1000 / Double(frames.count)))毫秒")

        // 第二步：基于dHash进行群组分析
        let groupingStart = Date()
        let initialGroups = groupFramesByHash(framesWithHash)
        print("📊 初步群组分析完成，检测到\(initialGroups.count)个群组")

        // 第三步：🚫 暂时禁用时间合并，避免错误合并不同地址
        let frameGroups = initialGroups // 直接使用初步分组结果
        let groupingTime = Date().timeIntervalSince(groupingStart)
        print("📊 群组分析完成，共\(frameGroups.count)个相似帧群组，耗时\(String(format: "%.2f", groupingTime))秒")

        // 第四步：应用30帧阈值过滤
        let result = applyThresholdFiltering(frameGroups)

        let totalTime = Date().timeIntervalSince(startTime)
        print("🎯 感知哈希去重完成，总耗时\(String(format: "%.2f", totalTime))秒")
        print("⚡ 性能提升：比OCR方法快约\(String(format: "%.0f", max(1, totalTime > 0 ? 10.0 / totalTime : 50)))倍")

        return result
    }

    /// 基于dHash对帧进行群组分析
    private func groupFramesByHash(_ frames: [NumberedFrame]) -> [[NumberedFrame]] {
        var frameGroups: [[NumberedFrame]] = []
        var processedIndices = Set<Int>()

        // 🎯 SpeedX优化：平衡精度和召回率
        let hashSimilarityThreshold = 5 // 64位中允许5位不同，约7.8%差异（平衡设置）

        for (index, frame) in frames.enumerated() {
            if processedIndices.contains(index) { continue }

            guard let currentHash = frame.dHash else {
                // 没有哈希的帧单独成组
                frameGroups.append([frame])
                processedIndices.insert(index)
                continue
            }

            // 创建新组
            var currentGroup: [NumberedFrame] = [frame]
            processedIndices.insert(index)

            // 查找相似的帧
            for (otherIndex, otherFrame) in frames.enumerated() {
                if processedIndices.contains(otherIndex) { continue }

                guard let otherHash = otherFrame.dHash else { continue }

                let distance = hammingDistance(currentHash, otherHash)

                // 🎯 SpeedX优化：除了哈希相似，还要考虑时间相近性
                let timeDifference = abs(otherFrame.timestamp - frame.timestamp)
                let isHashSimilar = distance <= hashSimilarityThreshold
                let isTimeClose = timeDifference <= 8.0 // 回到8秒，平衡合并和分离

                // 只有哈希相似且时间相近的帧才归为一组
                if isHashSimilar && isTimeClose {
                    currentGroup.append(otherFrame)
                    processedIndices.insert(otherIndex)
                }
            }

            frameGroups.append(currentGroup)
        }

        return frameGroups
    }

    /// 合并时间上相近的群组（解决同一地址多次停留问题）
    private func mergeTemporallyCloseGroups(_ groups: [[NumberedFrame]]) -> [[NumberedFrame]] {
        guard groups.count > 1 else { return groups }

        // 按时间戳排序群组（使用每组的平均时间戳）
        let sortedGroups = groups.sorted { group1, group2 in
            let avgTime1 = group1.map { $0.timestamp }.reduce(0, +) / Double(group1.count)
            let avgTime2 = group2.map { $0.timestamp }.reduce(0, +) / Double(group2.count)
            return avgTime1 < avgTime2
        }

        var mergedGroups: [[NumberedFrame]] = []
        var currentGroup = sortedGroups[0]

        for i in 1..<sortedGroups.count {
            let nextGroup = sortedGroups[i]

            // 计算两个群组的时间间隔
            let currentAvgTime = currentGroup.map { $0.timestamp }.reduce(0, +) / Double(currentGroup.count)
            let nextAvgTime = nextGroup.map { $0.timestamp }.reduce(0, +) / Double(nextGroup.count)
            let timeGap = abs(nextAvgTime - currentAvgTime)

            // 🎯 更严格的合并条件：只合并真正相似的内容
            // 1. 时间间隔必须很小（<3秒）
            // 2. 两个群组都很小（<50帧，约1.4秒）
            // 3. 且两个群组的dHash要相似
            let shouldMerge = timeGap < 3.0 &&
                             currentGroup.count < 50 &&
                             nextGroup.count < 50 &&
                             areGroupsSimilar(currentGroup, nextGroup)

            if shouldMerge {
                print("🔗 合并相近群组: \(currentGroup.count)帧 + \(nextGroup.count)帧 (时间间隔: \(String(format: "%.1f", timeGap))秒)")
                currentGroup.append(contentsOf: nextGroup)
            } else {
                // 时间间隔太大或群组太大，不合并
                mergedGroups.append(currentGroup)
                currentGroup = nextGroup
            }
        }

        // 添加最后一个群组
        mergedGroups.append(currentGroup)

        print("🔗 时间合并统计: \(groups.count) → \(mergedGroups.count) 群组")
        return mergedGroups
    }

    /// 检测两个群组是否相似（基于dHash）
    private func areGroupsSimilar(_ group1: [NumberedFrame], _ group2: [NumberedFrame]) -> Bool {
        // 取每个群组的代表帧（中间帧）
        guard let hash1 = group1[group1.count / 2].dHash,
              let hash2 = group2[group2.count / 2].dHash else {
            return false
        }

        // 计算汉明距离（dHash是UInt64类型）
        guard let hash1UInt = UInt64(hash1, radix: 16),
              let hash2UInt = UInt64(hash2, radix: 16) else {
            return false
        }

        let hammingDistance = hash1UInt ^ hash2UInt
        let bitCount = hammingDistance.nonzeroBitCount

        // 如果汉明距离小于等于8，认为相似
        return bitCount <= 8
    }

    /// 应用30帧阈值过滤
    private func applyThresholdFiltering(_ frameGroups: [[NumberedFrame]]) -> [NumberedFrame] {
        // 🎯 详细分析群组分布
        print("📊 群组详细分析:")
        let sortedGroups = frameGroups.enumerated().sorted { $0.element.count > $1.element.count }

        print("📈 群组大小分布（前30个最大群组）:")
        for (originalIndex, group) in sortedGroups.prefix(30) {
            let timeRange = "\(String(format: "%.1f", group.first?.timestamp ?? 0))s-\(String(format: "%.1f", group.last?.timestamp ?? 0))s"
            let duration = (group.last?.timestamp ?? 0) - (group.first?.timestamp ?? 0)
            print("   群组#\(originalIndex + 1): \(group.count)帧 (\(timeRange), 持续\(String(format: "%.1f", duration))s)")
        }

        // 统计不同大小范围的群组数量
        let veryLarge = frameGroups.filter { $0.count >= 100 }.count
        let large = frameGroups.filter { $0.count >= 50 && $0.count < 100 }.count
        let medium = frameGroups.filter { $0.count >= 30 && $0.count < 50 }.count
        let small = frameGroups.filter { $0.count >= 10 && $0.count < 30 }.count
        let tiny = frameGroups.filter { $0.count < 10 }.count

        print("📊 群组大小统计:")
        print("   🔥 超大群组(≥100帧): \(veryLarge)个")
        print("   📈 大群组(50-99帧): \(large)个")
        print("   📊 中群组(30-49帧): \(medium)个 ← 这些会被保留")
        print("   📉 小群组(10-29帧): \(small)个 ← 这些会被丢弃")
        print("   🔸 微群组(<10帧): \(tiny)个 ← 这些会被丢弃")

        var uniqueFrames: [NumberedFrame] = []
        var totalDuplicates = 0

        for (groupIndex, group) in frameGroups.enumerated() {
            let frameCount = group.count

            if frameCount >= shortPauseThreshold {
                // ≥30帧相同 → 保留1帧（选择中间帧，通常质量最好）
                let representative = group[frameCount / 2]
                uniqueFrames.append(representative)
                totalDuplicates += frameCount - 1
                print("✅ 群组#\(groupIndex + 1): \(frameCount)帧 ≥ \(shortPauseThreshold)帧阈值 → 保留1帧#\(representative.originalIndex)")
            } else {
                // <30帧相同 → 丢弃
                totalDuplicates += frameCount
                print("🗑️ 群组#\(groupIndex + 1): \(frameCount)帧 < \(shortPauseThreshold)帧阈值 → 丢弃")
            }
        }

        // 统计信息
        let discardedGroups = frameGroups.filter { $0.count < shortPauseThreshold }
        let keptGroups = frameGroups.filter { $0.count >= shortPauseThreshold }

        print("📊 感知哈希\(shortPauseThreshold)帧阈值去重统计:")
        print("   🗑️ 丢弃群组: \(discardedGroups.count) (<\(shortPauseThreshold)帧相同)")
        print("   ✅ 保留群组: \(keptGroups.count) (≥\(shortPauseThreshold)帧相同)")
        print("   📈 保留率: \(String(format: "%.1f", Double(keptGroups.count) / Double(frameGroups.count) * 100))%")
        print("   🔄 去重效果: \(frameGroups.reduce(0) { $0 + $1.count }) → \(uniqueFrames.count) 帧")

        // 🎯 详细分析丢弃的群组
        print("🔍 丢弃群组详细分析:")
        let sortedDiscardedGroups = discardedGroups.sorted { $0.count > $1.count }
        for (index, group) in sortedDiscardedGroups.prefix(20).enumerated() {
            let timeRange = "\(String(format: "%.1f", group.first?.timestamp ?? 0))s-\(String(format: "%.1f", group.last?.timestamp ?? 0))s"
            let duration = (group.last?.timestamp ?? 0) - (group.first?.timestamp ?? 0)
            print("   丢弃#\(index + 1): \(group.count)帧 (\(timeRange), 持续\(String(format: "%.1f", duration))s)")
        }

        // 🎯 如果丢弃的群组中有较大的，可能是阈值设置问题
        let largeDiscardedGroups = discardedGroups.filter { $0.count >= 20 }
        if !largeDiscardedGroups.isEmpty {
            print("⚠️ 警告：发现\(largeDiscardedGroups.count)个较大的丢弃群组(≥20帧)，可能需要调整阈值")
        }

        // 保底机制
        if uniqueFrames.isEmpty && !frameGroups.isEmpty {
            print("🚨 警告：\(shortPauseThreshold)帧阈值过严，启用保底机制")
            return applyFallbackMechanism(frameGroups)
        }

        return uniqueFrames
    }

    /// 保底机制：确保至少保留一些帧
    private func applyFallbackMechanism(_ frameGroups: [[NumberedFrame]]) -> [NumberedFrame] {
        // 第一层保底：降低到5帧阈值
        let fallbackThreshold = 5
        let fallbackKeptGroups = frameGroups.filter { $0.count >= fallbackThreshold }

        if !fallbackKeptGroups.isEmpty {
            print("✅ 保底机制成功：使用\(fallbackThreshold)帧阈值保留了\(fallbackKeptGroups.count)个群组")
            var fallbackFrames: [NumberedFrame] = []
            for group in fallbackKeptGroups {
                let representative = group[group.count / 2]
                fallbackFrames.append(representative)
            }
            return fallbackFrames
        }

        // 第二层保底：保留最大的3个群组
        print("🔄 最终保底：保留最大的3个群组")
        let sortedGroups = frameGroups.sorted { $0.count > $1.count }
        let topGroups = Array(sortedGroups.prefix(3))

        var finalFrames: [NumberedFrame] = []
        for (index, group) in topGroups.enumerated() {
            let representative = group[group.count / 2]
            finalFrames.append(representative)
            print("🔄 最终保底群组#\(index + 1): \(group.count)帧 → 保留1帧#\(representative.originalIndex)")
        }

        return finalFrames
    }

    // MARK: - 基于帧率的智能提取方法

    /// 基于视频实际帧率进行智能帧提取
    /// - Parameters:
    ///   - asset: 视频资源
    ///   - appType: 应用类型
    /// - Returns: 提取的帧数组
    private func extractFramesBasedOnFrameRate(from asset: AVAsset, appType: DeliveryAppType) async throws -> [NumberedFrame] {
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)

        // 获取视频帧率
        let tracks = try await asset.loadTracks(withMediaType: .video)
        guard let videoTrack = tracks.first else {
            throw NSError(domain: "VideoProcessing", code: -1, userInfo: [NSLocalizedDescriptionKey: "No video track found"])
        }

        let frameRate = try await videoTrack.load(.nominalFrameRate)
        let totalFrames = Int(durationSeconds * Double(frameRate))

        print("📹 视频信息:")
        print("   ⏱️ 时长: \(String(format: "%.1f", durationSeconds))秒")
        print("   🎬 帧率: \(String(format: "%.1f", frameRate))fps")
        print("   📊 总帧数: \(totalFrames)帧")

        // 根据应用类型调整提取策略
        let skipFrames = getFrameSkipCount(for: appType, frameRate: frameRate)
        let expectedFrameCount = totalFrames / skipFrames

        print("🎯 提取策略: 每\(skipFrames)帧提取1帧")
        print("📈 预期提取: \(expectedFrameCount)帧")

        // 创建帧提取器
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.requestedTimeToleranceBefore = .zero
        imageGenerator.requestedTimeToleranceAfter = .zero

        var frames: [NumberedFrame] = []
        let frameInterval = 1.0 / Double(frameRate) * Double(skipFrames)
        var currentTime: Double = 0
        var frameIndex = 1

        while currentTime < durationSeconds {
            let time = CMTime(seconds: currentTime, preferredTimescale: 600)

            do {
                let cgImage = try await imageGenerator.image(at: time).image
                let uiImage = UIImage(cgImage: cgImage)

                let frame = NumberedFrame(
                    image: uiImage,
                    originalIndex: frameIndex,
                    timestamp: currentTime,
                    extractionTime: Date(),
                    ocrResult: nil,
                    dHash: nil,
                    ssimValue: nil
                )

                frames.append(frame)
                frameIndex += 1

                if frameIndex % 100 == 0 {
                    print("📸 已提取\(frameIndex)/\(expectedFrameCount)帧")
                }

            } catch {
                print("⚠️ 提取帧失败 at \(String(format: "%.2f", currentTime))s: \(error)")
            }

            currentTime += frameInterval
        }

        print("🎬 基于帧率提取完成: \(frames.count)帧")
        return frames
    }

    /// 根据应用类型和帧率确定跳帧数量
    private func getFrameSkipCount(for appType: DeliveryAppType, frameRate: Float) -> Int {
        switch appType {
        case .speedx:
            // 🎯 SpeedX优化：更密集提取，确保不遗漏任何地址
            if frameRate >= 60 {
                return 2 // 60fps → 每2帧取1帧 = 30fps（更密集）
            } else if frameRate >= 30 {
                return 1 // 30fps → 全部提取 = 30fps（最密集）
            } else {
                return 1 // 低帧率视频全部提取
            }
        case .gofo:
            // GoFo界面变化较慢，可以跳更多帧
            if frameRate >= 60 {
                return 6 // 60fps → 每6帧取1帧 = 10fps
            } else if frameRate >= 30 {
                return 3 // 30fps → 每3帧取1帧 = 10fps
            } else {
                return 2 // 低帧率视频每2帧取1帧
            }
        default:
            // 其他应用使用中等策略
            if frameRate >= 60 {
                return 4 // 60fps → 每4帧取1帧 = 15fps
            } else if frameRate >= 30 {
                return 2 // 30fps → 每2帧取1帧 = 15fps
            } else {
                return 1 // 低帧率视频全部提取
            }
        }
    }
}
