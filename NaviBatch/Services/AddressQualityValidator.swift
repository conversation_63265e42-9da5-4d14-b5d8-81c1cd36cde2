import Foundation
import CoreLocation
import SwiftData

/// 地址质量验证服务
/// 防止错误的缓存地址数据持续使用，提供质量检查和自动修正机制
class AddressQualityValidator {
    static let shared = AddressQualityValidator()
    
    private init() {}
    
    /// 验证缓存地址的质量
    /// - Parameters:
    ///   - address: 原始地址
    ///   - cachedResult: 缓存的验证结果
    /// - Returns: 是否应该使用缓存结果，如果返回false则需要重新地理编码
    func shouldUseCachedResult(
        for address: String,
        cachedResult: ValidatedAddressResult
    ) async -> Bool {
        logInfo("🔍 AddressQualityValidator - 开始验证缓存地址质量: \(address)")
        
        // 1. 检查数据新鲜度
        let daysSinceLastUsed = Calendar.current.dateComponents([.day], from: cachedResult.lastUsed, to: Date()).day ?? 0
        if daysSinceLastUsed > 30 {
            logInfo("📅 AddressQualityValidator - 数据过旧(\(daysSinceLastUsed)天)，需要重新验证")
            return false
        }

        // 2. 检查坐标合理性
        if !isCoordinateReasonable(cachedResult.coordinate) {
            logInfo("📍 AddressQualityValidator - 坐标不合理，需要重新验证")
            return false
        }

        // 3. 检查使用次数（如果使用次数很少，可能需要重新验证）
        if cachedResult.usageCount < 2 {
            logInfo("📊 AddressQualityValidator - 使用次数过少(\(cachedResult.usageCount))，需要重新验证")
            return false
        }
        
        // 4. 检查匹配类型（如果是模糊匹配，需要更谨慎）
        switch cachedResult.matchType {
        case .fuzzy(let similarity):
            if similarity < 0.8 {
                logInfo("📝 AddressQualityValidator - 模糊匹配相似度过低(\(similarity))，需要重新验证")
                return false
            }
        case .exact:
            // 精确匹配，质量较高
            break
        case .normalized:
            // 标准化匹配，质量中等
            break
        }
        
        // 5. 随机抽样验证（1%的概率进行重新验证，保持数据质量）
        if shouldPerformRandomValidation() {
            logInfo("🎲 AddressQualityValidator - 随机抽样验证，需要重新验证")
            return false
        }
        
        logInfo("✅ AddressQualityValidator - 缓存地址质量良好，可以使用")
        return true
    }
    
    /// 检查坐标是否合理
    private func isCoordinateReasonable(_ coordinate: CLLocationCoordinate2D) -> Bool {
        // 检查坐标是否在有效范围内
        guard coordinate.latitude >= -90 && coordinate.latitude <= 90 &&
              coordinate.longitude >= -180 && coordinate.longitude <= 180 else {
            return false
        }
        
        // 检查是否是默认坐标 (0,0)
        if coordinate.latitude == 0 && coordinate.longitude == 0 {
            return false
        }
        
        // 可以添加更多地理合理性检查
        // 例如：检查坐标是否在预期的国家/地区范围内
        
        return true
    }
    

    
    /// 是否应该进行随机验证（1%概率）
    private func shouldPerformRandomValidation() -> Bool {
        return Int.random(in: 1...100) == 1
    }
    
    /// 标记地址需要重新验证
    func markAddressForRevalidation(_ address: String) async {
        logInfo("🔄 AddressQualityValidator - 标记地址需要重新验证: \(address)")
        
        Task { @MainActor in
            do {
                let modelContext = DatabaseManager.shared.getPersistentContainer().mainContext
                    let descriptor = FetchDescriptor<ValidatedAddress>(
                        predicate: #Predicate<ValidatedAddress> { validatedAddress in
                            validatedAddress.originalAddress == address
                        }
                    )

                    let results = try modelContext.fetch(descriptor)
                    for result in results {
                        // 降低置信度，触发重新验证
                        result.confidence = 0.3
                        logInfo("🔻 AddressQualityValidator - 已降低地址置信度: \(address)")
                    }

                    try modelContext.save()
            } catch {
                logError("❌ AddressQualityValidator - 标记地址重新验证失败: \(error)")
            }
        }
    }
    
    /// 批量检查地址质量（简化版本）
    func performBatchQualityCheck() async {
        logInfo("🔍 AddressQualityValidator - 批量质量检查功能已简化")
        // 可以在未来需要时实现更复杂的批量检查逻辑
    }
}

// MARK: - 日志辅助函数
private func logInfo(_ message: String) {
    print("[INFO] \(message)")
}

private func logError(_ message: String) {
    print("[ERROR] \(message)")
}
