import Foundation
import CoreLocation
import SwiftData

/// 地址数据标准化服务
/// 用于确保数据库中的地址字段与Apple Maps返回的标准格式一致
class AddressDataStandardizer {
    static let shared = AddressDataStandardizer()

    private init() {}

    /// 标准化地址数据结构
    struct StandardizedAddressData {
        let fullAddress: String           // 完整地址（主要字段）
        let streetNumber: String?         // 门牌号
        let streetName: String?           // 街道名
        let city: String?                 // 城市
        let state: String?                // 州/省
        let postalCode: String?           // 邮编
        let country: String?              // 国家
        let coordinate: CLLocationCoordinate2D

        /// 是否所有字段都一致
        var isConsistent: Bool {
            // 检查国家字段是否与完整地址中的国家一致
            guard let country = country else { return false }
            return fullAddress.lowercased().contains(country.lowercased())
        }
    }

    /// 从Apple Maps Placemark标准化地址数据
    /// - Parameters:
    ///   - placemark: Apple Maps返回的地标
    ///   - originalAddress: 原始地址字符串
    /// - Returns: 标准化的地址数据
    func standardizeFromPlacemark(_ placemark: CLPlacemark, originalAddress: String? = nil) -> StandardizedAddressData {
        // 构建标准格式的完整地址
        var addressComponents: [String] = []

        // 门牌号 + 街道名
        var streetAddress = ""
        if let streetNumber = placemark.subThoroughfare,
           let streetName = placemark.thoroughfare {
            streetAddress = "\(streetNumber) \(streetName)"
            addressComponents.append(streetAddress)
        } else if let streetName = placemark.thoroughfare {
            streetAddress = streetName
            addressComponents.append(streetName)
        }

        // 城市
        if let city = placemark.locality {
            addressComponents.append(city)
        }

        // 州/省 + 邮编
        if let state = placemark.administrativeArea {
            if let postalCode = placemark.postalCode {
                addressComponents.append("\(state) \(postalCode)")
            } else {
                addressComponents.append(state)
            }
        }

        // 国家
        if let country = placemark.country {
            addressComponents.append(country)
        }

        let fullAddress = addressComponents.joined(separator: ", ")

        return StandardizedAddressData(
            fullAddress: fullAddress,
            streetNumber: placemark.subThoroughfare,
            streetName: placemark.thoroughfare,
            city: placemark.locality,
            state: placemark.administrativeArea,
            postalCode: placemark.postalCode,
            country: placemark.country,
            coordinate: placemark.location?.coordinate ?? CLLocationCoordinate2D(latitude: 0, longitude: 0)
        )
    }

    /// 验证并修复现有地址数据
    /// - Parameter address: 现有的SavedAddress对象
    /// - Returns: 修复后的地址数据，如果无法修复则返回nil
    func validateAndFixAddressData(_ address: SavedAddress) async -> StandardizedAddressData? {
        Logger.info("🔧 开始验证地址数据: \(address.address)", type: .location)

        // 使用坐标进行反向地理编码获取标准数据
        let coordinate = CLLocationCoordinate2D(latitude: address.latitude, longitude: address.longitude)

        do {
            let geocoder = CLGeocoder()
            let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
            let placemarks = try await geocoder.reverseGeocodeLocation(location)

            if let placemark = placemarks.first {
                let standardizedData = standardizeFromPlacemark(placemark, originalAddress: address.address)

                // 检查数据一致性
                if !standardizedData.isConsistent {
                    Logger.warning("⚠️ 地址数据不一致: 原始(\(address.address)) vs 标准(\(standardizedData.fullAddress))", type: .location)
                }

                Logger.info("✅ 地址数据标准化完成: \(standardizedData.fullAddress)", type: .location)
                return standardizedData
            }
        } catch {
            Logger.error("❌ 反向地理编码失败: \(error.localizedDescription)", type: .location)
        }

        return nil
    }

    /// 批量修复数据库中的地址数据
    /// - Parameter modelContext: SwiftData模型上下文
    /// - Returns: 修复统计信息
    func batchFixAddressData(modelContext: ModelContext) async -> AddressFixStatistics {
        Logger.info("🚀 开始批量修复地址数据", type: .location)

        var statistics = AddressFixStatistics()

        do {
            // 获取所有地址
            let descriptor = FetchDescriptor<SavedAddress>()
            let addresses = try modelContext.fetch(descriptor)
            statistics.totalAddresses = addresses.count

            Logger.info("📊 找到 \(addresses.count) 个地址需要检查", type: .location)

            for address in addresses {
                // 检查是否需要修复
                if needsFixing(address) {
                    statistics.addressesNeedingFix += 1

                    // 尝试修复
                    if let standardizedData = await validateAndFixAddressData(address) {
                        // 更新地址数据
                        updateAddressWithStandardizedData(address, standardizedData: standardizedData)
                        statistics.addressesFixed += 1

                        Logger.info("🔧 已修复地址: \(address.address) -> \(standardizedData.fullAddress)", type: .location)
                    } else {
                        statistics.addressesFailedToFix += 1
                        Logger.error("❌ 无法修复地址: \(address.address)", type: .location)
                    }
                } else {
                    statistics.addressesAlreadyCorrect += 1
                }

                // 添加延迟避免API限制
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
            }

            // 保存更改
            try modelContext.save()
            Logger.info("💾 批量修复完成，已保存到数据库", type: .location)

        } catch {
            Logger.error("❌ 批量修复失败: \(error.localizedDescription)", type: .location)
        }

        return statistics
    }

    /// 检查地址是否需要修复
    private func needsFixing(_ address: SavedAddress) -> Bool {
        // 检查常见的数据不一致问题
        let addressLower = address.address.lowercased()

        // 1. 检查国家字段不一致（如你的例子中Australia vs United States）
        if addressLower.contains("united states") && !addressLower.contains("australia") {
            // 这里可以添加更多检查逻辑
            return true
        }

        // 2. 检查格式问题（如多余逗号、缺少邮编等）
        if address.address.contains(", ,") || address.address.contains(",,") {
            return true
        }

        // 3. 检查门牌号格式问题
        if address.address.matches("^\\d+,\\s") { // 如 "105, Cedar Street"
            return true
        }

        return false
    }

    /// 使用标准化数据更新地址对象 - 简化版本，只更新核心字段
    private func updateAddressWithStandardizedData(_ address: SavedAddress, standardizedData: StandardizedAddressData) {
        // 🎯 只更新核心必要字段 - 简单且可靠
        address.address = standardizedData.fullAddress

        // 更新坐标（如果有差异）
        if abs(address.latitude - standardizedData.coordinate.latitude) > 0.000001 ||
           abs(address.longitude - standardizedData.coordinate.longitude) > 0.000001 {
            address.latitude = standardizedData.coordinate.latitude
            address.longitude = standardizedData.coordinate.longitude
            Logger.info("📍 坐标已更新: (\(address.latitude), \(address.longitude))", type: .location)
        }

        Logger.info("🎯 地址标准化完成: \(standardizedData.fullAddress)", type: .location)
    }
}

/// 地址修复统计信息
struct AddressFixStatistics {
    var totalAddresses: Int = 0
    var addressesNeedingFix: Int = 0
    var addressesFixed: Int = 0
    var addressesFailedToFix: Int = 0
    var addressesAlreadyCorrect: Int = 0

    /// 修复成功率
    var successRate: Double {
        guard addressesNeedingFix > 0 else { return 1.0 }
        return Double(addressesFixed) / Double(addressesNeedingFix)
    }

    /// 统计摘要
    var summary: String {
        return """
        📊 地址数据修复统计：
        • 总地址数：\(totalAddresses)
        • 需要修复：\(addressesNeedingFix)
        • 修复成功：\(addressesFixed)
        • 修复失败：\(addressesFailedToFix)
        • 已经正确：\(addressesAlreadyCorrect)
        • 成功率：\(String(format: "%.1f", successRate * 100))%
        """
    }
}

// MARK: - String扩展
extension String {
    func matches(_ pattern: String) -> Bool {
        return self.range(of: pattern, options: .regularExpression) != nil
    }
}
