import Foundation
import CoreLocation
import Combine
import SwiftUI
import os.log
import UIKit

/// 位置管理器
/// 负责获取和管理用户位置信息
class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    // 单例
    static let shared = LocationManager()

    // 位置管理器
    private var locationManager: CLLocationManager

    // 发布属性
    @Published var userLocation: CLLocationCoordinate2D?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    @Published var lastUpdateTime: Date?
    @Published var isUpdatingLocation: Bool = false

    // 有效范围（米）- 调整为更大值以方便开发
    let validRange: Double = 20_000_000 // 20000公里（基本上禁用距离限制）

    // 默认位置相关属性 - 使用空坐标，将在初始化时动态设置
    private var defaultAddress = ""
    private var defaultCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0) // 初始为空坐标，稍后会基于设备区域设置

    // 各区域默认坐标 - 支持国家代码和全名
    private let regionCoordinates: [String: CLLocationCoordinate2D] = [
        // 澳洲
        "australia": CLLocationCoordinate2D(latitude: -37.90219, longitude: 145.16034), // Glen Waverley, Melbourne
        "au": CLLocationCoordinate2D(latitude: -37.90219, longitude: 145.16034),

        // 美国
        "usa": CLLocationCoordinate2D(latitude: 40.7128, longitude: -74.0060),        // New York
        "us": CLLocationCoordinate2D(latitude: 40.7128, longitude: -74.0060),

        // 英国
        "uk": CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),          // London
        "gb": CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),

        // 法国
        "france": CLLocationCoordinate2D(latitude: 48.8566, longitude: 2.3522),       // Paris
        "fr": CLLocationCoordinate2D(latitude: 48.8566, longitude: 2.3522),

        // 新加坡
        "singapore": CLLocationCoordinate2D(latitude: 1.3521, longitude: 103.8198),   // Singapore
        "sg": CLLocationCoordinate2D(latitude: 1.3521, longitude: 103.8198),

        // 中国
        "china": CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),      // Shanghai
        "cn": CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),

        // 香港 - 修复关键问题
        "hongkong": CLLocationCoordinate2D(latitude: 22.3193, longitude: 114.1694),   // Hong Kong
        "hk": CLLocationCoordinate2D(latitude: 22.3193, longitude: 114.1694),         // 添加HK代码支持

        // 日本
        "japan": CLLocationCoordinate2D(latitude: 35.6762, longitude: 139.6503),      // Tokyo
        "jp": CLLocationCoordinate2D(latitude: 35.6762, longitude: 139.6503),

        // 加拿大
        "canada": CLLocationCoordinate2D(latitude: 43.6532, longitude: -79.3832),     // Toronto
        "ca": CLLocationCoordinate2D(latitude: 43.6532, longitude: -79.3832),

        // 德国
        "germany": CLLocationCoordinate2D(latitude: 52.5200, longitude: 13.4050),     // Berlin
        "de": CLLocationCoordinate2D(latitude: 52.5200, longitude: 13.4050),

        // 韩国
        "korea": CLLocationCoordinate2D(latitude: 37.5665, longitude: 126.9780),      // Seoul
        "kr": CLLocationCoordinate2D(latitude: 37.5665, longitude: 126.9780),

        // 台湾
        "taiwan": CLLocationCoordinate2D(latitude: 25.0330, longitude: 121.5654),     // Taipei
        "tw": CLLocationCoordinate2D(latitude: 25.0330, longitude: 121.5654),

        // 马来西亚
        "malaysia": CLLocationCoordinate2D(latitude: 3.1390, longitude: 101.6869),    // Kuala Lumpur
        "my": CLLocationCoordinate2D(latitude: 3.1390, longitude: 101.6869),

        // 泰国
        "thailand": CLLocationCoordinate2D(latitude: 13.7563, longitude: 100.5018),   // Bangkok
        "th": CLLocationCoordinate2D(latitude: 13.7563, longitude: 100.5018),

        // 印度
        "india": CLLocationCoordinate2D(latitude: 28.6139, longitude: 77.2090),       // New Delhi
        "in": CLLocationCoordinate2D(latitude: 28.6139, longitude: 77.2090),

        // 通用区域
        "europe": CLLocationCoordinate2D(latitude: 48.8566, longitude: 2.3522),       // Paris
        "asia": CLLocationCoordinate2D(latitude: 1.3521, longitude: 103.8198)         // Singapore
    ]

    // 当前选择的区域
    private var selectedRegion: String = "" {
        didSet {
            UserDefaults.standard.set(selectedRegion, forKey: "SelectedRegion")
        }
    }
    private var isUsingDefaultLocation = false
    private let logger = os.Logger(subsystem: "com.navibatch.app", category: "LocationManager")

    // 初始化方法
    override init() {
        // 先初始化CLLocationManager
        let manager = CLLocationManager()
        manager.desiredAccuracy = kCLLocationAccuracyBest
        manager.distanceFilter = 500 // 500米更新一次

        // 在调用super.init()之前初始化所有属性
        self.locationManager = manager

        super.init()

        // 初始化完成后设置代理
        manager.delegate = self

        // 初始化默认区域和坐标
        setupDefaultRegionBasedOnDeviceLocale()

        // 加载保存的区域设置（如果有）
        if let savedRegion = UserDefaults.standard.string(forKey: "SelectedRegion"),
           regionCoordinates[savedRegion] != nil {
            selectedRegion = savedRegion
            logger.info("已加载保存的区域设置: \(savedRegion)")
        }

        // 设置默认坐标和地址
        updateDefaultLocationForCurrentRegion()

        // 清理旧的强制位置标志
        UserDefaults.standard.removeObject(forKey: "ForceUseDefaultLocation")
    }

    // 基于设备区域设置默认区域 - 支持175个国家
    private func setupDefaultRegionBasedOnDeviceLocale() {
        // 如果已经设置了区域，则不再重新设置
        if !selectedRegion.isEmpty {
            return
        }

        // 获取设备的区域设置
        let locale = Locale.current

        // 使用兼容iOS 16+和旧版本的方式获取区域代码
        let regionCode: String
        if #available(iOS 16, *) {
            regionCode = locale.region?.identifier.lowercased() ?? ""
        } else {
            // 兼容旧版本
            regionCode = locale.regionCode?.lowercased() ?? ""
        }

        logger.info("设备区域: \(regionCode)")

        // 🌍 全球化策略：直接使用国家代码作为区域标识
        // 这样支持175个国家而不需要硬编码映射
        if !regionCode.isEmpty {
            selectedRegion = regionCode
            logger.info("根据设备设置，区域设为: \(regionCode)")
        } else {
            // 如果无法获取区域代码，使用默认值
            selectedRegion = "global"
            logger.warning("无法获取设备区域代码，使用全球默认设置")
        }
    }

    // 更新当前区域的默认坐标和地址 - 全球化支持
    private func updateDefaultLocationForCurrentRegion() {
        // 🌍 优先尝试从预定义区域获取坐标
        if let coordinate = regionCoordinates[selectedRegion] {
            defaultCoordinate = coordinate
            defaultAddress = getDefaultAddressForRegion(selectedRegion)
            logger.info("已更新默认位置为: \(self.defaultAddress), 坐标: \(coordinate.latitude), \(coordinate.longitude)")
            return
        }

        // 🗺️ 如果不在预定义区域中，使用动态地理编码获取国家首都坐标
        Task {
            await geocodeCountryCapital(for: selectedRegion)
        }
    }

    // 获取区域的默认地址描述
    private func getDefaultAddressForRegion(_ region: String) -> String {
        // 预定义区域的地址
        switch region.lowercased() {
        case "australia", "au":
            return "Melbourne, Australia"
        case "usa", "us":
            return "New York, USA"
        case "uk", "gb":
            return "London, UK"
        case "france", "fr":
            return "Paris, France"
        case "singapore", "sg":
            return "Singapore"
        case "china", "cn":
            return "Shanghai, China"
        case "hongkong", "hk":
            return "Hong Kong"
        case "japan", "jp":
            return "Tokyo, Japan"
        case "canada", "ca":
            return "Toronto, Canada"
        case "germany", "de":
            return "Berlin, Germany"
        case "korea", "kr":
            return "Seoul, South Korea"
        case "taiwan", "tw":
            return "Taipei, Taiwan"
        case "malaysia", "my":
            return "Kuala Lumpur, Malaysia"
        case "thailand", "th":
            return "Bangkok, Thailand"
        case "india", "in":
            return "New Delhi, India"
        default:
            // 🌍 对于其他175个国家，使用国家名称
            return getCountryNameFromCode(region)
        }
    }

    // 根据国家代码获取国家名称
    private func getCountryNameFromCode(_ countryCode: String) -> String {
        let locale = Locale.current
        if let countryName = locale.localizedString(forRegionCode: countryCode.uppercased()) {
            return countryName
        }
        return countryCode.uppercased()
    }

    // 动态地理编码获取国家首都坐标
    private func geocodeCountryCapital(for countryCode: String) async {
        let geocoder = CLGeocoder()
        let countryName = getCountryNameFromCode(countryCode)

        // 尝试地理编码国家名称以获取大致坐标
        do {
            let placemarks = try await geocoder.geocodeAddressString(countryName)

            if let placemark = placemarks.first,
               let location = placemark.location?.coordinate {

                await MainActor.run {
                    self.defaultCoordinate = location
                    self.defaultAddress = countryName
                    self.logger.info("动态获取国家坐标: \(countryName) -> (\(location.latitude), \(location.longitude))")
                }
            } else {
                await MainActor.run {
                    // 如果地理编码失败，使用全球默认坐标
                    self.useGlobalDefaultCoordinate()
                }
            }
        } catch {
            await MainActor.run {
                self.logger.error("地理编码国家失败: \(countryName), 错误: \(error.localizedDescription)")
                self.useGlobalDefaultCoordinate()
            }
        }
    }

    // 使用全球默认坐标（经纬度0,0附近的安全位置）
    private func useGlobalDefaultCoordinate() {
        defaultCoordinate = CLLocationCoordinate2D(latitude: 0.0, longitude: 0.0)
        defaultAddress = "Global Location"
        logger.info("使用全球默认坐标: (0.0, 0.0)")
    }

    // 获取当前区域的默认坐标
    func getDefaultCoordinateForCurrentRegion() -> CLLocationCoordinate2D {
        return regionCoordinates[selectedRegion] ?? defaultCoordinate
    }

    // 设置当前区域
    func setRegion(_ region: String) {
        // 检查区域是否有效（现在支持国家代码和全名）
        guard regionCoordinates[region.lowercased()] != nil else {
            logger.error("尝试设置无效区域: \(region)")
            return
        }

        selectedRegion = region.lowercased()
        logger.info("已设置区域为: \(region) -> \(self.selectedRegion)")

        // 如果正在使用默认位置，更新为新区域的默认坐标
        if isUsingDefaultLocation {
            userLocation = getDefaultCoordinateForCurrentRegion()
            lastUpdateTime = Date()

            // 通知位置更新
            NotificationCenter.default.post(name: .userLocationDidUpdate, object: nil)
        }
    }

    // 获取所有可用区域
    func getAvailableRegions() -> [String] {
        return Array(regionCoordinates.keys).sorted()
    }

    // 获取当前选择的区域
    func getCurrentRegion() -> String {
        return selectedRegion
    }

    // 调试方法：打印当前位置设置信息
    func debugLocationSettings() {
        logger.info("=== 🔍 位置设置调试信息 ===")
        logger.info("当前选择区域: \(self.selectedRegion)")

        // 使用兼容iOS 16的方式获取区域代码
        let regionCode: String
        if #available(iOS 16, *) {
            regionCode = Locale.current.region?.identifier ?? "未知"
        } else {
            regionCode = Locale.current.regionCode ?? "未知"
        }
        logger.info("设备区域代码: \(regionCode)")
        logger.info("是否使用默认位置: \(self.isUsingDefaultLocation)")
        logger.info("位置权限状态: \(self.authorizationStatus.rawValue)")
        logger.info("是否正在更新位置: \(self.isUpdatingLocation)")

        if let userLocation = self.userLocation {
            logger.info("当前用户位置: (\(userLocation.latitude), \(userLocation.longitude))")

            // 判断位置来源
            if self.isUsingDefaultLocation {
                logger.info("📍 位置来源: 默认位置（GPS失败回退）")
            } else {
                logger.info("📍 位置来源: GPS定位")
            }
        } else {
            logger.info("当前用户位置: 未设置")
        }

        let defaultCoord = getDefaultCoordinateForCurrentRegion()
        logger.info("当前区域默认坐标: (\(defaultCoord.latitude), \(defaultCoord.longitude))")
        logger.info("默认地址: \(self.defaultAddress)")

        // 检查区域映射
        if regionCoordinates[self.selectedRegion] != nil {
            logger.info("✅ 区域映射存在")
        } else {
            logger.warning("❌ 区域映射不存在，将使用动态地理编码")
        }

        // 检查位置服务状态 - 在后台队列执行以避免主线程阻塞
        Task {
            let locationServicesEnabled = await Task.detached {
                CLLocationManager.locationServicesEnabled()
            }.value

            logger.info("系统位置服务是否启用: \(locationServicesEnabled)")
        }

        // 检查强制使用默认位置标志
        let forceDefault = UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation")
        logger.info("是否强制使用默认位置: \(forceDefault)")

        // 检查保存的区域设置
        let savedRegion = UserDefaults.standard.string(forKey: "SelectedRegion") ?? "无"
        logger.info("保存的区域设置: \(savedRegion)")

        // 详细检查香港相关配置
        if regionCode.lowercased() == "hk" || self.selectedRegion.lowercased() == "hk" {
            logger.info("🇭🇰 香港用户特殊检查:")
            logger.info("  - 设备区域代码: \(regionCode)")
            logger.info("  - 当前选择区域: \(self.selectedRegion)")
            logger.info("  - HK坐标配置: \(self.regionCoordinates["hk"]?.latitude ?? 0), \(self.regionCoordinates["hk"]?.longitude ?? 0)")
            logger.info("  - 实际使用坐标: \(defaultCoord.latitude), \(defaultCoord.longitude)")

            if defaultCoord.latitude == -37.90219 && defaultCoord.longitude == 145.16034 {
                logger.error("❌ 检测到香港用户使用了澳洲坐标！")
            } else if defaultCoord.latitude == 22.3193 && defaultCoord.longitude == 114.1694 {
                logger.info("✅ 香港用户使用了正确的香港坐标")
            }
        }

        logger.info("=== 🔍 调试信息结束 ===")
    }

    // 新增：位置来源诊断方法
    func diagnoseLocationSource() -> String {
        if UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation") {
            return "强制默认位置（模拟器模式）"
        } else if isUsingDefaultLocation {
            return "默认位置（GPS失败回退）"
        } else if userLocation != nil {
            return "GPS定位"
        } else {
            return "未知"
        }
    }

    // 新增：重置区域设置方法
    func resetRegionSettings() {
        logger.info("🔄 重置区域设置")

        // 清除保存的区域设置
        UserDefaults.standard.removeObject(forKey: "SelectedRegion")

        // 重新初始化区域设置
        selectedRegion = ""
        setupDefaultRegionBasedOnDeviceLocale()
        updateDefaultLocationForCurrentRegion()

        // 如果正在使用默认位置，更新位置
        if isUsingDefaultLocation {
            let newCoordinate = getDefaultCoordinateForCurrentRegion()
            userLocation = newCoordinate
            lastUpdateTime = Date()

            logger.info("✅ 已重置并更新位置为: (\(newCoordinate.latitude), \(newCoordinate.longitude))")

            // 通知位置更新
            NotificationCenter.default.post(name: .userLocationDidUpdate, object: nil)
        }
    }

    // 🔧 新增：强制重新获取真实GPS位置
    func forceRefreshRealLocation() {
        logger.info("🔄 强制重新获取真实GPS位置")

        // 清除强制使用默认位置的标志
        UserDefaults.standard.removeObject(forKey: "ForceUseDefaultLocation")

        // 重置默认位置标志
        isUsingDefaultLocation = false

        // 如果有位置权限，重新开始位置更新
        if hasLocationPermission() {
            logger.info("有位置权限，开始重新获取GPS位置")
            locationManager.stopUpdatingLocation()
            locationManager.startUpdatingLocation()
            isUpdatingLocation = true
        } else {
            logger.warning("没有位置权限，请求权限后重新获取GPS位置")
            requestLocationPermission()
        }
    }

    // 请求位置权限
    func requestLocationPermission() {
        logger.info("请求位置权限")

        // 使用正确的API获取授权状态，根据iOS版本
        let status: CLAuthorizationStatus
        if #available(iOS 14.0, *) {
            status = locationManager.authorizationStatus
        } else {
            // iOS 14之前，使用已弃用的类方法
            status = CLLocationManager.authorizationStatus()
        }

        // 如果尚未确定权限，则请求权限
        if status == .notDetermined {
            logger.info("位置权限未确定，请求权限")
            // 请求权限 - 结果将通过 locationManagerDidChangeAuthorization 回调处理
            locationManager.requestWhenInUseAuthorization()
        }
        // 如果权限被拒绝，使用默认位置但不自动跳转设置
        else if status == .denied || status == .restricted {
            logger.warning("位置权限被拒绝或受限制，使用默认位置")
            useDefaultLocation()

            // 🔧 修复：不再自动跳转到系统设置，让用户通过UI横幅主动选择
            // 这样提供更好的用户体验，不会突然打断用户操作
        }
        // 如果已经有权限，则开始更新位置
        else if status == .authorizedWhenInUse || status == .authorizedAlways {
            logger.info("已有位置权限，开始更新位置")
            startUpdatingLocation()
        }
    }

    // 开始更新位置
    func startUpdatingLocation() {
        // 获取当前授权状态，根据iOS版本使用正确的API
        let status: CLAuthorizationStatus
        if #available(iOS 14.0, *) {
            status = locationManager.authorizationStatus
        } else {
            // iOS 14之前，使用已弃用的类方法
            status = CLLocationManager.authorizationStatus()
        }

        if status == .authorizedWhenInUse || status == .authorizedAlways {
            locationManager.startUpdatingLocation()
            isUpdatingLocation = true
            logger.info("开始位置更新")
        } else if status == .notDetermined {
            // 如果权限未确定，我们会请求权限，然后在回调中处理位置更新
            logger.warning("位置权限未确定，请求权限")
            // 权限请求将通过委托方法处理结果
            locationManager.requestWhenInUseAuthorization()
        } else {
            // 如果权限被拒绝，我们使用默认位置
            logger.warning("位置权限被拒绝，使用默认位置")
            useDefaultLocation()
        }
    }

    // 停止更新位置
    func stopUpdatingLocation() {
        locationManager.stopUpdatingLocation()
        isUpdatingLocation = false
    }

    // 获取一次位置
    func requestLocation() {
        guard hasLocationPermission() else {
            logger.warning("⚠️ 没有位置权限，无法请求位置更新")
            return
        }

        locationManager.requestLocation()
    }

    // 检查是否有位置权限
    func hasLocationPermission() -> Bool {
        let status: CLAuthorizationStatus
        if #available(iOS 14.0, *) {
            status = locationManager.authorizationStatus
        } else {
            status = CLLocationManager.authorizationStatus()
        }

        return status == .authorizedWhenInUse || status == .authorizedAlways
    }

    // 计算与用户位置的距离（米）
    func distanceFromUserLocation(_ coordinate: CLLocationCoordinate2D) -> Double? {
        guard let userLocation = userLocation else { return nil }

        let userLocationObj = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
        let targetLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        return userLocationObj.distance(from: targetLocation)
    }

    // 检查坐标是否在有效范围内
    func isWithinValidRange(_ coordinate: CLLocationCoordinate2D) -> Bool {
        guard let distance = distanceFromUserLocation(coordinate) else { return false }
        return distance <= validRange
    }

    // 获取坐标的国家
    func getCountryForCoordinate(_ coordinate: CLLocationCoordinate2D, completion: @escaping (String?) -> Void) {
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        // 🎯 反向地理编码，强制使用英文locale
        geocoder.reverseGeocodeLocation(location, preferredLocale: Locale(identifier: "en_US")) { placemarks, error in
            if let error = error {
                print("反向地理编码错误: \(error.localizedDescription)")
                completion(nil)
                return
            }

            if let country = placemarks?.first?.country {
                completion(country)
            } else {
                completion(nil)
            }
        }
    }

    // 检查两个坐标是否在同一国家
    func areCoordsInSameCountry(_ coord1: CLLocationCoordinate2D, _ coord2: CLLocationCoordinate2D, completion: @escaping (Bool) -> Void) {
        let group = DispatchGroup()
        var country1: String?
        var country2: String?

        group.enter()
        getCountryForCoordinate(coord1) { country in
            country1 = country
            group.leave()
        }

        group.enter()
        getCountryForCoordinate(coord2) { country in
            country2 = country
            group.leave()
        }

        group.notify(queue: .main) {
            let sameCountry = country1 != nil && country2 != nil && country1 == country2
            completion(sameCountry)
        }
    }

    // MARK: - 默认位置方法

    /// 强制使用默认位置 - 已弃用，现在使用iOS模拟器自定义位置
    /// 保留此函数以避免编译错误，但不再执行强制逻辑
    func forceUseDefaultLocation() {
        logger.info("forceUseDefaultLocation 已弃用 - 现在使用iOS模拟器的自定义位置功能")

        // 移除强制标志
        UserDefaults.standard.removeObject(forKey: "ForceUseDefaultLocation")

        // 不再强制设置位置，让系统使用模拟器的自定义位置
        isUsingDefaultLocation = false
    }

    /// 使用默认位置
    /// 当无法获取用户实际位置时（例如权限被拒绝或模拟器中未设置位置），自动使用默认位置
    func useDefaultLocation() {
        // 🔧 修复：在真实设备上，如果已有有效GPS位置，不要轻易覆盖
        #if !targetEnvironment(simulator)
        if !isUsingDefaultLocation && userLocation != nil {
            logger.info("真实设备已有GPS位置，跳过默认位置设置以避免位置跳转")
            return
        }
        #endif

        // 检查是否已经在使用默认位置，避免重复调用
        if isUsingDefaultLocation {
            return
        }

        isUsingDefaultLocation = true

        // 获取当前区域的默认坐标
        let regionCoordinate = getDefaultCoordinateForCurrentRegion()

        logger.warning("使用默认位置: 区域=\(self.selectedRegion), 坐标: (\(regionCoordinate.latitude), \(regionCoordinate.longitude))")

        // 设置默认位置
        userLocation = regionCoordinate
        lastUpdateTime = Date()

        // 🔧 修复：只在模拟器或首次启动时进行地理编码，避免真实设备位置跳转
        #if targetEnvironment(simulator)
        // 尝试地理编码默认地址以获取更精确的坐标
        geocodeDefaultAddress()
        #else
        logger.info("真实设备跳过地理编码，直接使用区域默认坐标")
        #endif

        // 通知位置更新
        NotificationCenter.default.post(name: .userLocationDidUpdate, object: nil)
    }

    /// 尝试地理编码默认地址以获取更精确的坐标
    private func geocodeDefaultAddress() {
        let geocoder = CLGeocoder()

        // 根据当前区域创建适当的地理编码区域和策略
        let (region, expectedCountry) = createGeocodingRegionForCurrentArea()

        // 🔧 修复：增加地理编码安全检查，避免错误的位置跳转
        logger.info("开始地理编码默认地址: \(self.defaultAddress), 预期国家: \(expectedCountry)")

        // 使用区域提示进行地理编码
        geocoder.geocodeAddressString(defaultAddress, in: region) { [weak self] placemarks, error in
            guard let self = self else { return }

            if let error = error {
                self.logger.error("默认地址地理编码失败: \(error.localizedDescription)")
                return
            }

            // 🎯 修复：严格验证地理编码结果，只接受预期国家的结果
            if let targetPlacemark = placemarks?.first(where: { $0.country == expectedCountry }),
               let location = targetPlacemark.location?.coordinate {

                self.logger.info("✅ 成功地理编码默认地址: \(self.defaultAddress) -> (\(location.latitude), \(location.longitude)), 国家: \(expectedCountry)")

                // 更新默认位置
                self.userLocation = location
                self.lastUpdateTime = Date()

                // 通知位置更新
                NotificationCenter.default.post(name: .userLocationDidUpdate, object: nil)
            } else if let firstPlacemark = placemarks?.first,
                      let _ = firstPlacemark.location?.coordinate {

                // 🚨 修复：如果地理编码返回了错误国家的结果，拒绝使用，保持区域默认坐标
                let actualCountry = firstPlacemark.country ?? "未知"
                self.logger.warning("❌ 地理编码返回了错误国家的结果，拒绝使用: \(self.defaultAddress) -> 预期: \(expectedCountry), 实际: \(actualCountry)")
                self.logger.info("保持使用区域默认坐标，避免位置跳转")

                // 不更新位置，保持原有的区域默认坐标
            } else {
                self.logger.warning("地理编码未返回有效结果，保持区域默认坐标")
            }
        }
    }

    /// 根据当前区域创建适当的地理编码区域和预期国家 - 支持175个国家
    private func createGeocodingRegionForCurrentArea() -> (CLCircularRegion?, String) {
        let coordinate = getDefaultCoordinateForCurrentRegion()
        let countryName = getExpectedCountryName(for: selectedRegion)

        // 🌍 为所有国家创建合适的地理编码区域
        if coordinate.latitude != 0.0 || coordinate.longitude != 0.0 {
            // 根据国家大小调整搜索半径
            let radius = getSearchRadiusForCountry(selectedRegion)
            let region = CLCircularRegion(center: coordinate, radius: radius, identifier: selectedRegion.uppercased())
            return (region, countryName)
        } else {
            // 如果没有有效坐标，不使用区域限制，让地理编码服务自由搜索
            return (nil, countryName)
        }
    }

    /// 获取国家的预期名称
    private func getExpectedCountryName(for countryCode: String) -> String {
        // 特殊映射处理
        switch countryCode.lowercased() {
        case "au", "australia":
            return "Australia"
        case "us", "usa":
            return "United States"
        case "gb", "uk":
            return "United Kingdom"
        case "cn", "china":
            return "China"
        case "hk", "hongkong":
            return "Hong Kong"
        case "jp", "japan":
            return "Japan"
        case "ca", "canada":
            return "Canada"
        case "fr", "france":
            return "France"
        case "de", "germany":
            return "Germany"
        case "sg", "singapore":
            return "Singapore"
        default:
            // 🌍 对于其他国家，使用系统本地化获取正确的国家名称
            return getCountryNameFromCode(countryCode)
        }
    }

    /// 根据国家大小获取搜索半径
    private func getSearchRadiusForCountry(_ countryCode: String) -> CLLocationDistance {
        switch countryCode.lowercased() {
        case "au", "us", "ca", "cn", "ru", "br", "in":
            // 大国使用较大半径
            return 100000 // 100公里
        case "hk", "sg", "mc", "va":
            // 小国/城市国家使用较小半径
            return 10000 // 10公里
        default:
            // 中等国家使用标准半径
            return 50000 // 50公里
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }

        // 移除强制默认位置检查 - 现在使用iOS模拟器的自定义位置

        // 记录详细的GPS位置信息
        let coordinate = location.coordinate
        logger.info("🛰️ GPS定位成功: (\(coordinate.latitude), \(coordinate.longitude))")
        logger.info("📍 定位精度: \(location.horizontalAccuracy)米")
        logger.info("⏰ 定位时间: \(location.timestamp)")

        // 获取设备区域代码用于对比
        let regionCode: String
        if #available(iOS 16, *) {
            regionCode = Locale.current.region?.identifier ?? "未知"
        } else {
            regionCode = Locale.current.regionCode ?? "未知"
        }
        logger.info("📱 设备区域代码: \(regionCode)")

        // 🌍 验证位置是否合理（避免使用明显错误的坐标）
        if isLocationReasonable(coordinate) {
            // 更新用户位置
            userLocation = coordinate
            lastUpdateTime = Date()

            // 重置默认位置标志，因为已经获取到真实位置
            if isUsingDefaultLocation {
                logger.info("✅ 已获取真实GPS位置，不再使用默认位置")
                isUsingDefaultLocation = false

                // 🔧 清除强制使用默认位置的标志（如果存在）
                UserDefaults.standard.removeObject(forKey: "ForceUseDefaultLocation")
            }

            logger.info("✅ 位置更新成功: (\(coordinate.latitude), \(coordinate.longitude))")
        } else {
            logger.warning("⚠️ 收到的位置坐标不合理，忽略此次更新: (\(coordinate.latitude), \(coordinate.longitude))")

            // 🔧 修复：只有在真正没有任何位置信息时才使用默认位置
            if userLocation == nil {
                logger.info("首次启动且无有效GPS坐标，使用默认位置")
                useDefaultLocation()
            } else {
                logger.info("保持现有位置信息，不使用默认位置")
            }
            return
        }

        // 如果只是请求一次位置，停止更新
        if !isUpdatingLocation {
            manager.stopUpdatingLocation()
        }

        // 通知位置更新
        NotificationCenter.default.post(name: .userLocationDidUpdate, object: nil)
    }

    // 验证位置是否合理
    private func isLocationReasonable(_ coordinate: CLLocationCoordinate2D) -> Bool {
        // 检查坐标是否有效
        guard CLLocationCoordinate2DIsValid(coordinate) else {
            return false
        }

        // 🔧 修复：只在模拟器环境下进行严格的默认坐标检测
        #if targetEnvironment(simulator)
        let latitude = coordinate.latitude
        let longitude = coordinate.longitude

        // 排除模拟器的默认测试坐标
        // 旧金山苹果总部附近 (37.7749, -122.4194) - 使用更精确的范围
        if abs(latitude - 37.7749) < 0.01 && abs(longitude - (-122.4194)) < 0.01 {
            logger.info("模拟器检测到默认坐标（旧金山），将使用区域默认位置")
            return false
        }

        // 其他明显的模拟器测试坐标可以在这里添加
        #else
        // 🎯 真实设备：接受所有有效的GPS坐标，不进行地理位置过滤
        logger.info("真实设备GPS坐标: (\(coordinate.latitude), \(coordinate.longitude)) - 直接接受")
        #endif

        return true
    }

    // 记录位置信息（仅用于调试）
    private func logLocationInfo(_ coordinate: CLLocationCoordinate2D) {
        // 记录位置信息
        logger.info("当前位置坐标: (\(coordinate.latitude), \(coordinate.longitude))")
    }

    // MARK: - CLLocationManagerDelegate

    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        guard manager === locationManager else {
            logger.warning("收到未知locationManager的授权变更通知")
            return
        }

        let status = manager.authorizationStatus
        logger.info("位置权限状态变更为: \(status.rawValue)")

        // 更新发布的值
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.authorizationStatus = status
        }

        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            // 在后台队列检查位置服务是否可用，避免主线程阻塞
            Task.detached { [weak self] in
                let isLocationServicesEnabled = CLLocationManager.locationServicesEnabled()

                await MainActor.run { [weak self] in
                    guard let self = self else { return }

                    if isLocationServicesEnabled {
                        self.logger.info("位置权限已授权且位置服务可用，开始更新位置")
                        self.locationManager.startUpdatingLocation()
                        self.isUpdatingLocation = true
                    } else {
                        self.logger.warning("位置权限已授权但位置服务不可用")
                        self.useDefaultLocation()
                    }
                }
            }
        case .denied, .restricted:
            logger.warning("位置权限被拒绝或受限制")
            stopUpdatingLocation()
            // 如果之前没有位置信息，则使用默认位置
            if self.userLocation == nil {
                self.useDefaultLocation()
            }
        case .notDetermined:
            logger.info("位置权限未确定")
            // 权限未确定状态下，我们已经在请求权限
        @unknown default:
            logger.warning("未知的授权状态")
            // 未知状态时使用默认位置
            self.useDefaultLocation()
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        logger.error("位置更新失败: \(error.localizedDescription)")

        // 🔧 修复：在真实设备上，如果已有有效位置，保持不变，避免跳转到默认位置
        #if !targetEnvironment(simulator)
        if userLocation != nil && !isUsingDefaultLocation {
            logger.info("真实设备保持最后已知的有效GPS位置，不使用默认位置")
            return
        }
        #endif

        // 检查具体的错误类型
        if let clError = error as? CLError {
            switch clError.code {
            case .locationUnknown:
                logger.warning("位置更新失败: 无法确定当前位置 (kCLErrorLocationUnknown)。这可能是暂时的。")
                // 如果从未收到过真实位置，使用默认位置
                if userLocation == nil {
                    useDefaultLocation()
                }
            case .denied:
                // 🔧 改进：检查真实的权限状态，而不是仅依赖错误码
                let actualStatus: CLAuthorizationStatus
                if #available(iOS 14.0, *) {
                    actualStatus = manager.authorizationStatus
                } else {
                    actualStatus = CLLocationManager.authorizationStatus()
                }

                if actualStatus == .denied || actualStatus == .restricted {
                    logger.error("位置更新失败: 权限确实被拒绝 (kCLErrorDenied)，实际权限状态: \(actualStatus.rawValue)")
                    // 只有在没有任何位置信息时才使用默认位置
                    if userLocation == nil {
                        useDefaultLocation()
                    }
                } else {
                    logger.warning("位置更新失败: 收到kCLErrorDenied但权限状态正常 (\(actualStatus.rawValue))，可能是其他原因")
                    // 权限正常但仍然失败，可能是GPS信号问题，如果没有位置则使用默认位置
                    if userLocation == nil {
                        useDefaultLocation()
                    }
                }
            case .network:
                logger.error("位置更新失败: 网络错误 (kCLErrorNetwork)。请检查网络连接。")
                // 网络错误时，如果从未收到过真实位置，使用默认位置
                if userLocation == nil {
                    useDefaultLocation()
                }
            default:
                logger.error("位置更新失败，错误码: \(clError.code.rawValue)。")
                // 其他错误时，如果从未收到过真实位置，使用默认位置
                if userLocation == nil {
                    useDefaultLocation()
                }
            }
        } else {
            // 未知错误时，如果从未收到过真实位置，使用默认位置
            if userLocation == nil {
                useDefaultLocation()
            }
        }
    }
}

// 通知名称扩展
extension Notification.Name {
    static let userLocationDidUpdate = Notification.Name("userLocationDidUpdate")
}

// 位置验证状态枚举
enum LocationValidationStatus: String, Codable {
    case valid = "valid"               // 有效范围内
    case warning = "warning"           // 超出范围但在同一国家
    case invalid = "invalid"           // 位于其他国家
    case unknown = "unknown"           // 未验证

    var localizedName: String {
        switch self {
        case .valid: return "location_status_valid".localized
        case .warning: return "location_status_warning".localized
        case .invalid: return "location_status_invalid".localized
        case .unknown: return "location_status_unknown".localized
        }
    }

    var color: Color {
        switch self {
        case .valid: return .green
        case .warning: return .orange
        case .invalid: return .red
        case .unknown: return .gray
        }
    }

    var iconName: String {
        switch self {
        case .valid: return "checkmark.circle"
        case .warning: return "exclamationmark.triangle"
        case .invalid: return "xmark.circle"
        case .unknown: return "questionmark.circle"
        }
    }
}
