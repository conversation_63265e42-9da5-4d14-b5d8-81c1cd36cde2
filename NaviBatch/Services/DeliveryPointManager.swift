import Foundation
import SwiftData
import CoreLocation
import Combine

/// DeliveryPoint管理器
/// 负责DeliveryPoint的创建、更新和批量处理
class DeliveryPointManager {
    // 单例
    static let shared = DeliveryPointManager()

    // 私有初始化方法
    private init() {}

    // 存储取消令牌的集合
    private var cancellables = Set<AnyCancellable>()

    // 创建新的DeliveryPoint
    func createDeliveryPoint(
        address: String,
        route: Route?,
        modelContext: ModelContext,
        userSelectedAppType: DeliveryAppType = .manual, // 🎯 新增：用户选择的应用类型
        completion: @escaping (DeliveryPoint?, Error?) -> Void
    ) {
        // 🎯 分离地址、追踪号码、Sort Number、第三方排序号、客户信息、时间信息和应用类型
        let (cleanAddress, trackingNumber, _, thirdPartySortNumber, customerName, deliveryTime, _) = self.separateAddressAndTracking(address)

        // 🏠 获取完整地址信息（包含公寓号）
        let fullAddress = self.getFullAddressFromInfo(address)

        // 🎯 使用用户选择的应用类型，而不是AI检测的类型
        let finalAppType = userSelectedAppType.rawValue
        print("🎯 DEBUG: 使用用户选择的应用类型: \(finalAppType) (\(userSelectedAppType.displayName))")

        // 🎯 使用async/await模式获取坐标
        Task {
            do {
                // 🔧 在地理编码前修复缺少州信息的地址
                let addressForGeocoding = await fixAddressStateIfNeeded(cleanAddress)
                if addressForGeocoding != cleanAddress {
                    Logger.info("🔧 DeliveryPointManager - 地理编码前地址修复: \(cleanAddress) -> \(addressForGeocoding)", type: .location)
                }

                let result = try await GeocodingService.shared.geocodeAddressAsync(addressForGeocoding)

                // 🎯 根据是否有第三方Sort Number来决定编号策略
                let sortNumber: Int
                let isOptimized: Bool

                if !thirdPartySortNumber.isEmpty {
                    // 🎯 有第三方Sort Number时，使用连续的内部编号，但标记为已优化
                    sortNumber = self.getNextSortNumber(for: route, in: modelContext)
                    isOptimized = true // 标记为已优化
                    Logger.info("🎯 有第三方Sort Number: \(thirdPartySortNumber) -> 使用内部编号: \(sortNumber)", type: .data)
                } else {
                    sortNumber = self.getNextSortNumber(for: route, in: modelContext)
                    isOptimized = false // 普通创建，未优化
                }

                // 🎯 修复：创建DeliveryPoint，通过结构化地址处理来正确设置各个字段
                // 🌍 后处理地址：移除国家但确保有州信息
                let processedAddress = await postProcessAddressForStorage(fullAddress ?? cleanAddress)

                let deliveryPoint = DeliveryPoint(
                    sort_number: sortNumber,
                    originalAddress: processedAddress, // 🏠 使用后处理的地址
                    latitude: result.coordinate?.latitude ?? 0,
                    longitude: result.coordinate?.longitude ?? 0
                )

                // 🎯 设置sorted_number（未优化前等于sort_number）
                deliveryPoint.sorted_number = sortNumber

                // 🎯 如果有第三方Sort Number，标记为已优化
                if isOptimized {
                    deliveryPoint.isOptimized = true // 标记为已优化
                }

                // 设置追踪号码、第三方Sort Number、客户信息
                if !trackingNumber.isEmpty {
                    deliveryPoint.trackingNumber = trackingNumber
                    Logger.info("🏷️ 设置追踪号码: \(trackingNumber) -> \(cleanAddress)", type: .data)
                }
                if !thirdPartySortNumber.isEmpty {
                    deliveryPoint.thirdPartySortNumber = thirdPartySortNumber
                    Logger.info("🏷️ 设置第三方Sort Number: \(thirdPartySortNumber) -> \(cleanAddress)", type: .data)
                }
                if !customerName.isEmpty {
                    deliveryPoint.customerName = customerName
                    Logger.info("👤 设置客户姓名: \(customerName) -> \(cleanAddress)", type: .data)
                }
                if !deliveryTime.isEmpty {
                    deliveryPoint.scheduledDeliveryTime = deliveryTime
                    Logger.info("⏰ 设置配送时间: \(deliveryTime) -> \(cleanAddress)", type: .data)
                }
                // 🎯 使用用户选择的应用类型设置sourceAppRaw
                print("🎯 DEBUG: 应用类型设置逻辑 - finalAppType: '\(finalAppType)', 地址: \(cleanAddress)")
                print("🎯 DEBUG: 条件检查:")
                print("🎯 DEBUG:   finalAppType != \"manual\" = \(finalAppType != "manual")")
                print("🎯 DEBUG:   finalAppType != \"just_photo\" = \(finalAppType != "just_photo")")
                let shouldSetAppType = finalAppType != "manual" && finalAppType != "just_photo"
                print("🎯 DEBUG:   最终条件结果 = \(shouldSetAppType)")

                if shouldSetAppType {
                    deliveryPoint.sourceAppRaw = finalAppType
                    print("🎯 DEBUG: ✅ 设置sourceAppRaw = '\(finalAppType)'")
                    Logger.info("📱 设置用户选择的应用类型: \(finalAppType) -> \(cleanAddress)", type: .data)
                } else {
                    // justPhoto或manual类型保持默认值（manual）
                    deliveryPoint.sourceAppRaw = DeliveryAppType.manual.rawValue
                    print("🎯 DEBUG: ❌ 保持默认值sourceAppRaw = 'manual'，原因: finalAppType='\(finalAppType)'")
                    Logger.info("📷 Just Photo识别，不设置第三方应用标签 -> \(cleanAddress)", type: .data)
                }

                // 🏗️ 异步处理地址结构化信息（避免在Task中捕获modelContext）
                self.processAddressStructureAsync(
                    address: cleanAddress,
                    deliveryPoint: deliveryPoint,
                    modelContext: modelContext
                )

                // 验证坐标是否在澳大利亚范围内
                _ = deliveryPoint.validateCoordinates()

                // 基于用户位置验证坐标
                if let userLocation = LocationManager.shared.userLocation {
                    deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                } else {
                    // 如果没有用户位置，使用默认位置
                    LocationManager.shared.useDefaultLocation()
                    // 再次尝试验证
                    if let defaultLocation = LocationManager.shared.userLocation {
                        deliveryPoint.validateLocationBasedOnUserPosition(defaultLocation)
                    }
                }

                // 设置地理编码警告
                switch result.status {
                case .success:
                    deliveryPoint.geocodingWarning = nil
                case .partialSuccess:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.partialMatch.rawValue
                case .failed:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.unknownError.rawValue
                case .rateLimited:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.apiLimitReached.rawValue
                case .networkError:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.networkError.rawValue
                case .timeout:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.networkError.rawValue
                case .invalidAddress:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.lowAccuracy.rawValue
                case .notFound:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.coordinateMissing.rawValue
                }

                // 如果有路线，添加到路线
                if let route = route {
                    route.points.append(deliveryPoint)
                }

                // 保存到数据库
                modelContext.insert(deliveryPoint)

                do {
                    try modelContext.save()
                    Logger.info("创建DeliveryPoint成功: \(cleanAddress) (追踪号: \(trackingNumber.isEmpty ? "无" : trackingNumber), 客户: \(customerName.isEmpty ? "无" : customerName), 应用: \(finalAppType))", type: .data)
                    completion(deliveryPoint, nil)
                } catch {
                    Logger.error("创建DeliveryPoint失败: \(error.localizedDescription)", type: .data)
                    completion(nil, error)
                }
            } catch {
                Logger.error("地理编码失败: \(error.localizedDescription)", type: .data)
                completion(nil, error)
            }
        }
    }

    // 批量创建DeliveryPoint - 按第三方排序号顺序分配系统序号
    func batchCreateDeliveryPoints(
        addresses: [String],
        route: Route?,
        modelContext: ModelContext,
        userSelectedAppType: DeliveryAppType = .manual, // 🎯 新增：用户选择的应用类型
        progressHandler: ((Double, Int, Int) -> Void)? = nil,
        completion: @escaping ([DeliveryPoint], [String]) -> Void
    ) {
        // 🎯 按第三方排序号顺序排序地址
        let sortedAddresses = sortAddressesByThirdPartySortNumber(addresses)
        Logger.info("🔄 按第三方排序号排序完成: \(addresses.count)个地址", type: .data)
        // 检查订阅限制
        if let route = route {
            let subscriptionManager = SubscriptionManager.shared
            let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute
            let currentCount = route.points.count
            let remainingSlots = maxAllowed - currentCount

            // 如果地址数量超过剩余槽位，截断地址列表
            if sortedAddresses.count > remainingSlots {
                Logger.info("地址数量(\(sortedAddresses.count))超过剩余槽位(\(remainingSlots))，将截断地址列表", type: .data)

                // 如果没有剩余槽位，直接返回
                if remainingSlots <= 0 {
                    Logger.info("没有剩余槽位，无法添加更多地址", type: .data)
                    completion([], ["已达到地址数量上限，无法添加更多地址"])
                    return
                }
            }
        }

        // 创建一个进度发布者
        let progressSubject = PassthroughSubject<(Double, Int, Int), Never>()

        // 如果提供了进度处理器，订阅进度发布者
        if let progressHandler = progressHandler {
            progressSubject
                .receive(on: DispatchQueue.main)
                .sink { progress, completed, total in
                    progressHandler(progress, completed, total)
                }
                .store(in: &cancellables)
        }

        // 🎯 使用async/await模式批量获取坐标
        Task {
            do {
                // 🔧 在批量地理编码前修复缺少州信息的地址
                var fixedAddresses: [String] = []
                for address in sortedAddresses {
                    let fixedAddress = await fixAddressStateIfNeeded(address)
                    if fixedAddress != address {
                        Logger.info("🔧 DeliveryPointManager - 批量地理编码前地址修复: \(address) -> \(fixedAddress)", type: .location)
                    }
                    fixedAddresses.append(fixedAddress)
                }

                let results = try await GeocodingService.shared.geocodeBatchAsync(fixedAddresses)
                var createdPoints: [DeliveryPoint] = []
                var failedAddresses: [String] = []

                // 获取下一个排序号
                let startSortNumber = self.getNextSortNumber(for: route, in: modelContext)

                // 获取用户位置
                let userLocation = LocationManager.shared.userLocation

                // 处理每个结果
                for (index, result) in results.enumerated() {
                    // 更新进度
                    let progress = Double(index + 1) / Double(results.count)
                    progressSubject.send((progress, index + 1, results.count))

                    // 🎯 分离地址、追踪号码、Sort Number、第三方排序号、客户信息、时间信息和应用类型
                    let (cleanAddress, trackingNumber, _, thirdPartySortNumber, customerName, deliveryTime, _) = self.separateAddressAndTracking(result.address)

                    // 🏠 获取完整地址信息（包含公寓号）
                    let fullAddress = self.getFullAddressFromInfo(result.address)

                    // 🎯 使用用户选择的应用类型，而不是AI检测的类型
                    let finalAppType = userSelectedAppType.rawValue
                    print("🎯 DEBUG: [批量] 使用用户选择的应用类型: \(finalAppType) (\(userSelectedAppType.displayName))")

                    // 🎯 修正：按顺序分配连续的sort_number，不使用第三方排序号
                    let sortNumber = startSortNumber + index
                    let isOptimized: Bool

                    if !thirdPartySortNumber.isEmpty {
                        isOptimized = true // 有第三方排序号，标记为已优化
                        Logger.info("🎯 第三方排序号: \(thirdPartySortNumber) -> 系统序号: \(sortNumber)", type: .data)
                    } else {
                        isOptimized = false // 普通创建，未优化
                    }

                    // 🎯 修复：创建DeliveryPoint，设置完整地址和追踪号码
                    // 🌍 后处理地址：移除国家但确保有州信息
                    let processedAddress = await postProcessAddressForStorage(fullAddress ?? cleanAddress)

                    let deliveryPoint = DeliveryPoint(
                        sort_number: sortNumber,
                        streetName: cleanAddress, // 设置清理后的地址
                        originalAddress: processedAddress, // 🏠 使用后处理的地址
                        latitude: result.coordinate?.latitude ?? 0,
                        longitude: result.coordinate?.longitude ?? 0
                    )

                    // 🎯 设置sorted_number（用于显示）
                    deliveryPoint.sorted_number = sortNumber

                    // 🎯 如果有第三方Sort Number，标记为已优化
                    if isOptimized {
                        deliveryPoint.isOptimized = true // 标记为已优化
                    }

                    // 设置追踪号码、第三方Sort Number、客户信息
                    if !trackingNumber.isEmpty {
                        deliveryPoint.trackingNumber = trackingNumber
                        Logger.info("🏷️ 设置追踪号码: \(trackingNumber) -> \(cleanAddress)", type: .data)
                    }
                    if !thirdPartySortNumber.isEmpty {
                        deliveryPoint.thirdPartySortNumber = thirdPartySortNumber
                        Logger.info("🏷️ 设置第三方Sort Number: \(thirdPartySortNumber) -> \(cleanAddress)", type: .data)
                    }
                    if !customerName.isEmpty {
                        deliveryPoint.customerName = customerName
                        Logger.info("👤 设置客户姓名: \(customerName) -> \(cleanAddress)", type: .data)
                    }
                    if !deliveryTime.isEmpty {
                        deliveryPoint.scheduledDeliveryTime = deliveryTime
                        Logger.info("⏰ 设置配送时间: \(deliveryTime) -> \(cleanAddress)", type: .data)
                    }
                    // 🎯 使用用户选择的应用类型设置sourceAppRaw
                    print("🎯 DEBUG: [批量] 应用类型设置逻辑 - finalAppType: '\(finalAppType)', 地址: \(cleanAddress)")
                    print("🎯 DEBUG: [批量] 条件检查:")
                    print("🎯 DEBUG: [批量]   finalAppType != \"manual\" = \(finalAppType != "manual")")
                    print("🎯 DEBUG: [批量]   finalAppType != \"just_photo\" = \(finalAppType != "just_photo")")
                    let shouldSetAppType = finalAppType != "manual" && finalAppType != "just_photo"
                    print("🎯 DEBUG: [批量]   最终条件结果 = \(shouldSetAppType)")

                    if shouldSetAppType {
                        deliveryPoint.sourceAppRaw = finalAppType
                        print("🎯 DEBUG: [批量] ✅ 设置sourceAppRaw = '\(finalAppType)'")
                        Logger.info("📱 设置用户选择的应用类型: \(finalAppType) -> \(cleanAddress)", type: .data)
                    } else {
                        // justPhoto或manual类型保持默认值（manual）
                        deliveryPoint.sourceAppRaw = DeliveryAppType.manual.rawValue
                        print("🎯 DEBUG: [批量] ❌ 保持默认值sourceAppRaw = 'manual'，原因: finalAppType='\(finalAppType)'")
                        Logger.info("📷 Just Photo识别，不设置第三方应用标签 -> \(cleanAddress)", type: .data)
                    }

                    // 验证坐标是否在澳大利亚范围内
                    _ = deliveryPoint.validateCoordinates()

                    // 基于用户位置验证坐标
                    if let userLoc = userLocation {
                        deliveryPoint.validateLocationBasedOnUserPosition(userLoc)
                    }

                    // 设置地理编码警告
                    switch result.status {
                    case .success:
                        deliveryPoint.geocodingWarning = nil
                    case .partialSuccess:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.partialMatch.rawValue
                    case .failed:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.unknownError.rawValue
                        failedAddresses.append(result.address)
                    case .rateLimited:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.apiLimitReached.rawValue
                        failedAddresses.append(result.address)
                    case .networkError:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.networkError.rawValue
                        failedAddresses.append(result.address)
                    case .timeout:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.networkError.rawValue
                        failedAddresses.append(result.address)
                    case .invalidAddress:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.lowAccuracy.rawValue
                    case .notFound:
                        deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.coordinateMissing.rawValue
                        failedAddresses.append(result.address)
                    }

                    // 如果有路线，添加到路线
                    if let route = route {
                        route.points.append(deliveryPoint)
                    }

                    // 添加到数据库
                    modelContext.insert(deliveryPoint)

                    // 添加到创建的点列表
                    createdPoints.append(deliveryPoint)
                }

                // 保存到数据库
                do {
                    try modelContext.save()
                    Logger.info("批量创建DeliveryPoint成功: \(createdPoints.count)个点", type: .data)
                } catch {
                    Logger.error("批量创建DeliveryPoint失败: \(error.localizedDescription)", type: .data)
                }

                // 完成回调
                completion(createdPoints, failedAddresses)
            } catch {
                Logger.error("批量地理编码失败: \(error.localizedDescription)", type: .data)
                completion([], [])
            }
        }
    }

    // 批量创建DeliveryPoint（从导入的地址和坐标）
    func batchCreateDeliveryPoints(
        addresses: [(String, CLLocationCoordinate2D)],
        route: Route?,
        modelContext: ModelContext,
        userSelectedAppType: DeliveryAppType = .manual, // 🎯 新增：用户选择的应用类型
        progressHandler: ((Double, Int, Int) -> Void)? = nil,
        completion: @escaping ([DeliveryPoint]) -> Void
    ) {
        // 检查订阅限制
        var addressesToProcess = addresses
        if let route = route {
            let subscriptionManager = SubscriptionManager.shared
            let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute
            let currentCount = route.points.count
            let remainingSlots = maxAllowed - currentCount

            // 如果地址数量超过剩余槽位，截断地址列表
            if addresses.count > remainingSlots {
                Logger.info("地址数量(\(addresses.count))超过剩余槽位(\(remainingSlots))，将截断地址列表", type: .data)
                addressesToProcess = Array(addresses.prefix(remainingSlots))

                // 如果没有剩余槽位，直接返回
                if remainingSlots <= 0 {
                    Logger.info("没有剩余槽位，无法添加更多地址", type: .data)
                    completion([])
                    return
                }
            }
        }

        // 创建一个进度发布者
        let progressSubject = PassthroughSubject<(Double, Int, Int), Never>()

        // 如果提供了进度处理器，订阅进度发布者
        if let progressHandler = progressHandler {
            progressSubject
                .receive(on: DispatchQueue.main)
                .sink { progress, completed, total in
                    progressHandler(progress, completed, total)
                }
                .store(in: &cancellables)
        }

        Task { @MainActor in
            var createdPoints: [DeliveryPoint] = []

            // 获取下一个排序号
            let startSortNumber = self.getNextSortNumber(for: route, in: modelContext)

            // 获取用户位置
            let userLocation = LocationManager.shared.userLocation

            // 处理每个地址
            for (index, addressData) in addressesToProcess.enumerated() {
                // 更新进度
                let progress = Double(index + 1) / Double(addressesToProcess.count)
                progressSubject.send((progress, index + 1, addressesToProcess.count))

                let (address, coordinate) = addressData

                // 🎯 分离地址、追踪号码、Sort Number、第三方排序号、客户信息、时间信息和应用类型
                let (cleanAddress, trackingNumber, _, thirdPartySortNumber, customerName, deliveryTime, _) = self.separateAddressAndTracking(address)

                // 🏠 获取完整地址信息（包含公寓号）
                let fullAddress = self.getFullAddressFromInfo(address)

                // 🎯 使用用户选择的应用类型，而不是AI检测的类型
                let finalAppType = userSelectedAppType.rawValue
                print("🎯 DEBUG: [批量2] 使用用户选择的应用类型: \(finalAppType) (\(userSelectedAppType.displayName))")

                // 🎯 根据是否有第三方Sort Number来决定编号策略
                let sortNumber: Int
                let isOptimized: Bool

                if !thirdPartySortNumber.isEmpty {
                    // 🎯 有第三方Sort Number时，使用连续的内部编号，但标记为已优化
                    sortNumber = startSortNumber + index
                    isOptimized = true // 标记为已优化
                    Logger.info("🎯 有第三方Sort Number: \(thirdPartySortNumber) -> 使用内部编号: \(sortNumber)", type: .data)
                } else {
                    sortNumber = startSortNumber + index
                    isOptimized = false // 普通创建，未优化
                }

                // 🎯 使用结构化地址创建DeliveryPoint
                // 🌍 后处理地址：移除国家但确保有州信息
                let processedAddress = await postProcessAddressForStorage(fullAddress ?? cleanAddress)

                let deliveryPoint = DeliveryPoint(
                    sort_number: sortNumber,
                    streetName: cleanAddress, // 设置清理后的地址
                    originalAddress: processedAddress, // 🏠 使用后处理的地址
                    coordinate: coordinate
                )

                // 🎯 设置sorted_number（用于显示）
                deliveryPoint.sorted_number = sortNumber

                // 🎯 如果有第三方Sort Number，标记为已优化
                if isOptimized {
                    deliveryPoint.isOptimized = true // 标记为已优化
                }

                // 设置追踪号码、第三方Sort Number、客户信息
                if !trackingNumber.isEmpty {
                    deliveryPoint.trackingNumber = trackingNumber
                    Logger.info("🏷️ 设置追踪号码: \(trackingNumber) -> \(cleanAddress)", type: .data)
                }
                if !thirdPartySortNumber.isEmpty {
                    deliveryPoint.thirdPartySortNumber = thirdPartySortNumber
                    Logger.info("🏷️ 设置第三方Sort Number: \(thirdPartySortNumber) -> \(cleanAddress)", type: .data)
                }
                if !customerName.isEmpty {
                    deliveryPoint.customerName = customerName
                    Logger.info("👤 设置客户姓名: \(customerName) -> \(cleanAddress)", type: .data)
                }
                if !deliveryTime.isEmpty {
                    deliveryPoint.scheduledDeliveryTime = deliveryTime
                    Logger.info("⏰ 设置配送时间: \(deliveryTime) -> \(cleanAddress)", type: .data)
                }
                // 🎯 使用用户选择的应用类型设置sourceAppRaw
                print("🎯 DEBUG: [批量2] 应用类型设置逻辑 - finalAppType: '\(finalAppType)', 地址: \(cleanAddress)")
                print("🎯 DEBUG: [批量2] 条件检查:")
                print("🎯 DEBUG: [批量2]   finalAppType != \"manual\" = \(finalAppType != "manual")")
                print("🎯 DEBUG: [批量2]   finalAppType != \"just_photo\" = \(finalAppType != "just_photo")")
                let shouldSetAppType = finalAppType != "manual" && finalAppType != "just_photo"
                print("🎯 DEBUG: [批量2]   最终条件结果 = \(shouldSetAppType)")

                if shouldSetAppType {
                    deliveryPoint.sourceAppRaw = finalAppType
                    print("🎯 DEBUG: [批量2] ✅ 设置sourceAppRaw = '\(finalAppType)'")
                    Logger.info("📱 设置用户选择的应用类型: \(finalAppType) -> \(cleanAddress)", type: .data)
                } else {
                    // justPhoto或manual类型保持默认值（manual）
                    deliveryPoint.sourceAppRaw = DeliveryAppType.manual.rawValue
                    print("🎯 DEBUG: [批量2] ❌ 保持默认值sourceAppRaw = 'manual'，原因: finalAppType='\(finalAppType)'")
                    Logger.info("📷 Just Photo识别，不设置第三方应用标签 -> \(cleanAddress)", type: .data)
                }

                // 执行反向地理编码验证并设置评分
                let validationScore = await ReverseGeocodingValidationService.shared.validateAddressWithReverseGeocoding(
                    originalAddress: cleanAddress,
                    coordinate: coordinate
                )

                // 设置验证分数和问题
                deliveryPoint.addressValidationScore = validationScore.totalScore
                if !validationScore.issues.isEmpty {
                    deliveryPoint.addressValidationIssues = validationScore.issues.joined(separator: "; ")
                }

                // 🚨 根据验证模式设置地理编码警告
                let validationService = ReverseGeocodingValidationService.shared
                let threshold = validationService.validationMode.threshold

                if validationScore.totalScore < threshold {
                    // 分数低于阈值，设置为低精度警告（需要人工确认）
                    deliveryPoint.setGeocodingWarning(.lowAccuracy)
                } else if validationScore.totalScore < 100 && validationService.validationMode != .standard {
                    // 严格模式下，分数在阈值-99之间，设置为部分匹配警告
                    deliveryPoint.setGeocodingWarning(.partialMatch)
                } else {
                    // 达到要求，清除警告
                    deliveryPoint.setGeocodingWarning(.none)
                }

                // 验证坐标是否在澳大利亚范围内
                _ = deliveryPoint.validateCoordinates()

                // 基于用户位置验证坐标
                if let userLoc = userLocation {
                    deliveryPoint.validateLocationBasedOnUserPosition(userLoc)
                } else {
                    // 如果没有用户位置，使用默认位置
                    LocationManager.shared.useDefaultLocation()
                    // 再次尝试验证
                    if let defaultLocation = LocationManager.shared.userLocation {
                        deliveryPoint.validateLocationBasedOnUserPosition(defaultLocation)
                    }
                }

                // 如果有路线，添加到路线
                if let route = route {
                    route.points.append(deliveryPoint)
                }

                // 添加到数据库
                modelContext.insert(deliveryPoint)

                // 添加到创建的点列表
                createdPoints.append(deliveryPoint)
            }

            // 保存到数据库
            do {
                try modelContext.save()
                Logger.info("批量创建DeliveryPoint成功: \(createdPoints.count)个点", type: .data)
            } catch {
                Logger.error("批量创建DeliveryPoint失败: \(error.localizedDescription)", type: .data)
            }

            // 完成回调
            completion(createdPoints)
        }
    }

    // 更新DeliveryPoint坐标
    func updateDeliveryPointCoordinates(
        deliveryPoint: DeliveryPoint,
        modelContext: ModelContext,
        completion: @escaping (Bool) -> Void
    ) {
        // 使用GeocodingService获取坐标
        GeocodingService.shared.geocodeAddress(deliveryPoint.primaryAddress)
            .first()
            .sink { result in
                // 更新坐标
                if let coordinate = result.coordinate {
                    deliveryPoint.latitude = coordinate.latitude
                    deliveryPoint.longitude = coordinate.longitude

                    // 验证坐标是否在澳大利亚范围内
                    _ = deliveryPoint.validateCoordinates()

                    // 基于用户位置验证坐标
                    if let userLocation = LocationManager.shared.userLocation {
                        deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                    } else {
                        // 如果没有用户位置，使用默认位置
                        LocationManager.shared.useDefaultLocation()
                        // 再次尝试验证
                        if let defaultLocation = LocationManager.shared.userLocation {
                            deliveryPoint.validateLocationBasedOnUserPosition(defaultLocation)
                        }
                    }
                }

                // 设置地理编码警告
                switch result.status {
                case .success:
                    deliveryPoint.geocodingWarning = nil
                case .partialSuccess:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.partialMatch.rawValue
                case .failed:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.unknownError.rawValue
                case .rateLimited:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.apiLimitReached.rawValue
                case .networkError:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.networkError.rawValue
                case .timeout:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.networkError.rawValue
                case .invalidAddress:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.lowAccuracy.rawValue
                case .notFound:
                    deliveryPoint.geocodingWarning = DeliveryPoint.GeocodingWarningType.coordinateMissing.rawValue
                }

                // 保存到数据库
                do {
                    try modelContext.save()
                    Logger.info("更新DeliveryPoint坐标成功: \(deliveryPoint.primaryAddress)", type: .data)
                    completion(true)
                } catch {
                    Logger.error("更新DeliveryPoint坐标失败: \(error.localizedDescription)", type: .data)
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    // 异步处理地址结构化信息（避免并发安全问题）
    private func processAddressStructureAsync(
        address: String,
        deliveryPoint: DeliveryPoint,
        modelContext: ModelContext
    ) {
        Task { @MainActor in
            do {
                let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(address)

                switch globalResult {
                case .success(_, _, _, let placemark, _, _):
                    // 使用 placemark 填充结构化地址字段
                    deliveryPoint.populateStructuredAddress(from: placemark)
                case .failed(_, _):
                    // 如果处理失败，尝试从完整地址中提取街道名称部分
                    let addressComponents = address.components(separatedBy: ",")
                    if let firstComponent = addressComponents.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
                        deliveryPoint.streetName = firstComponent
                    }
                }

                // 保存modelContext
                try modelContext.save()
            } catch {
                Logger.error("保存地址结构化信息失败: \(error.localizedDescription)", type: .data)
            }
        }
    }

    // 🎯 从Sort Code中提取数字部分 (如 "D90" -> 90, "D146" -> 146)
    private func extractNumberFromSortCode(_ sortCode: String) -> Int? {
        // 移除所有非数字字符，提取数字部分
        let numberString = sortCode.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return Int(numberString)
    }

    // 🎯 按第三方排序号顺序排序地址
    private func sortAddressesByThirdPartySortNumber(_ addresses: [String]) -> [String] {
        return addresses.sorted { address1, address2 in
            let info1 = separateAddressAndTracking(address1)
            let info2 = separateAddressAndTracking(address2)

            // 提取第三方排序号的数字部分
            let sort1 = extractNumberFromSortCode(info1.thirdPartySortNumber) ?? Int.max
            let sort2 = extractNumberFromSortCode(info2.thirdPartySortNumber) ?? Int.max

            // 有第三方排序号的排在前面，按数字大小排序
            if sort1 == Int.max && sort2 == Int.max {
                // 都没有第三方排序号，保持原顺序
                return false
            } else if sort1 == Int.max {
                // address1没有排序号，排在后面
                return false
            } else if sort2 == Int.max {
                // address2没有排序号，address1排在前面
                return true
            } else {
                // 都有排序号，按数字大小排序
                return sort1 < sort2
            }
        }
    }

    // 获取下一个排序号
    private func getNextSortNumber(for route: Route?, in modelContext: ModelContext) -> Int {
        guard let route = route else {
            return 1
        }

        // 获取路线中的最大排序号
        let maxSortNumber = route.points.map { $0.sort_number }.max() ?? 0

        // 返回下一个排序号
        return maxSortNumber + 1
    }

    // 🏷️ 分离地址、追踪号码、Sort Number、第三方排序号、客户信息、时间信息和应用类型的辅助函数
    func separateAddressAndTracking(_ addressWithInfo: String) -> (address: String, tracking: String, sortNumber: String, thirdPartySortNumber: String, customer: String, deliveryTime: String, appType: String) {
        var address = addressWithInfo
        var tracking = ""
        var sortNumber = ""
        var customer = ""
        var deliveryTime = ""
        var appType = "manual"
        var fullAddress = "" // 🏠 新增：完整地址（包含公寓号）

        // 🎯 提取内部排序号（SORT标签 - 用于sort_number）
        let sortTag = "\(SortNumberConstants.TAG_SEPARATOR)\(SortNumberConstants.INTERNAL_SORT_TAG)\(SortNumberConstants.TAG_VALUE_SEPARATOR)"
        if address.contains(sortTag) {
            let sortComponents = address.components(separatedBy: sortTag)
            if sortComponents.count >= 2 {
                address = sortComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let remainingAfterSort = sortComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查是否还有其他标签
                if remainingAfterSort.contains(SortNumberConstants.TAG_SEPARATOR) {
                    let nextPipeIndex = remainingAfterSort.firstIndex(of: Character(SortNumberConstants.TAG_SEPARATOR))!
                    sortNumber = String(remainingAfterSort[..<nextPipeIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                    // 将剩余部分重新添加到地址中进行后续处理
                    address += SortNumberConstants.TAG_SEPARATOR + String(remainingAfterSort[nextPipeIndex...])
                } else {
                    sortNumber = remainingAfterSort
                }
            }
        }

        // 🎯 提取第三方原始排序号（THIRD_PARTY_SORT标签 - 用于thirdPartySortNumber）
        var thirdPartySortNumber = ""
        let thirdPartySortTag = "\(SortNumberConstants.TAG_SEPARATOR)\(SortNumberConstants.THIRD_PARTY_SORT_TAG)\(SortNumberConstants.TAG_VALUE_SEPARATOR)"
        if address.contains(thirdPartySortTag) {
            let thirdPartySortComponents = address.components(separatedBy: thirdPartySortTag)
            if thirdPartySortComponents.count >= 2 {
                address = thirdPartySortComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let remainingAfterThirdPartySort = thirdPartySortComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查是否还有其他标签
                if remainingAfterThirdPartySort.contains(SortNumberConstants.TAG_SEPARATOR) {
                    let nextPipeIndex = remainingAfterThirdPartySort.firstIndex(of: Character(SortNumberConstants.TAG_SEPARATOR))!
                    thirdPartySortNumber = String(remainingAfterThirdPartySort[..<nextPipeIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                    // 将剩余部分重新添加到地址中进行后续处理
                    address += SortNumberConstants.TAG_SEPARATOR + String(remainingAfterThirdPartySort[nextPipeIndex...])
                } else {
                    thirdPartySortNumber = remainingAfterThirdPartySort
                }
            }
        }

        // 🏠 提取完整地址信息（新格式）
        if address.contains("|FULL:") {
            let fullComponents = address.components(separatedBy: "|FULL:")
            if fullComponents.count >= 2 {
                address = fullComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let remainingAfterFull = fullComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查是否还有其他标签
                if remainingAfterFull.contains("|") {
                    let nextPipeIndex = remainingAfterFull.firstIndex(of: "|")!
                    fullAddress = String(remainingAfterFull[..<nextPipeIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                    // 将剩余部分重新添加到地址中进行后续处理
                    address += "|" + String(remainingAfterFull[nextPipeIndex...])
                } else {
                    fullAddress = remainingAfterFull
                }
                Logger.info("🏠 提取到完整地址: \(fullAddress)", type: .data)
            }
        }

        // 🎯 提取时间信息（新格式）
        if address.contains("|TIME:") {
            let timeComponents = address.components(separatedBy: "|TIME:")
            if timeComponents.count >= 2 {
                address = timeComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let remainingAfterTime = timeComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查是否还有其他标签
                if remainingAfterTime.contains("|") {
                    let nextPipeIndex = remainingAfterTime.firstIndex(of: "|")!
                    deliveryTime = String(remainingAfterTime[..<nextPipeIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                    // 将剩余部分重新添加到地址中进行后续处理
                    address += "|" + String(remainingAfterTime[nextPipeIndex...])
                } else {
                    deliveryTime = remainingAfterTime
                }
                Logger.info("⏰ 提取到配送时间: \(deliveryTime)", type: .data)
            }
        }

        // 提取应用类型
        if address.contains("|APP:") {
            let appComponents = address.components(separatedBy: "|APP:")
            if appComponents.count >= 2 {
                address = appComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                appType = appComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }

        // 提取追踪号码
        if address.contains("|TRACK:") {
            let trackComponents = address.components(separatedBy: "|TRACK:")
            if trackComponents.count >= 2 {
                address = trackComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let remainingInfo = trackComponents[1]

                // 检查是否还有客户信息
                if remainingInfo.contains("|CUSTOMER:") {
                    let customerComponents = remainingInfo.components(separatedBy: "|CUSTOMER:")
                    if customerComponents.count >= 2 {
                        let trackingInfo = customerComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                        customer = customerComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)

                        // 🎯 分离Sort Number和Tracking Number
                        // 格式: "D90-6102424663603" -> sortNumber: "D90", tracking: "6102424663603"
                        if trackingInfo.contains("-") {
                            let parts = trackingInfo.components(separatedBy: "-")
                            if parts.count >= 2 {
                                sortNumber = parts[0].trimmingCharacters(in: .whitespacesAndNewlines)
                                tracking = parts[1].trimmingCharacters(in: .whitespacesAndNewlines)
                            } else {
                                tracking = trackingInfo
                            }
                        } else {
                            tracking = trackingInfo
                        }
                    }
                } else {
                    let trackingInfo = remainingInfo.trimmingCharacters(in: .whitespacesAndNewlines)

                    // 🎯 分离Sort Number和Tracking Number
                    // 格式: "D90-6102424663603" -> sortNumber: "D90", tracking: "6102424663603"
                    if trackingInfo.contains("-") {
                        let parts = trackingInfo.components(separatedBy: "-")
                        if parts.count >= 2 {
                            sortNumber = parts[0].trimmingCharacters(in: .whitespacesAndNewlines)
                            tracking = parts[1].trimmingCharacters(in: .whitespacesAndNewlines)
                        } else {
                            tracking = trackingInfo
                        }
                    } else {
                        tracking = trackingInfo
                    }
                }
            }
        }
        // 如果没有追踪信息但有客户信息
        else if address.contains("|CUSTOMER:") {
            let customerComponents = address.components(separatedBy: "|CUSTOMER:")
            if customerComponents.count >= 2 {
                address = customerComponents[0].trimmingCharacters(in: .whitespacesAndNewlines)
                customer = customerComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }

        // 🎯 最终清理：移除所有剩余的管道符号和元数据
        address = cleanAddressMetadata(address)

        return (address: address, tracking: tracking, sortNumber: sortNumber, thirdPartySortNumber: thirdPartySortNumber, customer: customer, deliveryTime: deliveryTime, appType: appType)
    }

    // 🌍 地址后处理：移除国家但确保有州信息，并优化为Apple Maps格式
    func postProcessAddressForStorage(_ address: String) async -> String {
        Logger.info("🌍 开始地址后处理: \(address)", type: .data)

        // 1. 首先移除国家后缀
        var processedAddress = AddressSimplifier.removeCountryName(address) ?? address
        Logger.info("🚫 移除国家后: \(processedAddress)", type: .data)

        // 2. 检查是否缺少州信息
        if let stateFixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: processedAddress) {
            processedAddress = stateFixedAddress
            Logger.info("✅ 添加州信息后: \(processedAddress)", type: .data)
        }

        // 3. 🏛️ 使用USPS标准格式化器优化地址格式（用于数据库存储）
        processedAddress = AppleMapsAddressFormatter.formatForDatabaseStorage(processedAddress)
        Logger.info("🏛️ USPS标准格式化后: \(processedAddress)", type: .data)

        // 4. 最终清理
        processedAddress = AddressSimplifier.cleanupAddress(processedAddress)

        Logger.info("🎯 地址后处理完成: \(address) -> \(processedAddress)", type: .data)
        return processedAddress
    }

    // MARK: - 地址州修复辅助方法

    /// 检查并修复缺少州信息的地址（用于地理编码前）
    /// - Parameter address: 原始地址
    /// - Returns: 修复后的地址（如果不需要修复则返回原地址）
    private func fixAddressStateIfNeeded(_ address: String) async -> String {
        // 使用 AddressStateFixService 检测并修复地址
        if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: address) {
            Logger.info("🔧 DeliveryPointManager - 地址州修复成功: \(address) -> \(fixedAddress)", type: .location)
            return fixedAddress
        }

        // 如果不需要修复，返回原地址
        return address
    }

    // 🏠 获取完整地址信息（包含公寓号）的辅助方法
    func getFullAddressFromInfo(_ addressWithInfo: String) -> String? {
        if addressWithInfo.contains("|FULL:") {
            let fullComponents = addressWithInfo.components(separatedBy: "|FULL:")
            if fullComponents.count >= 2 {
                let remainingAfterFull = fullComponents[1].trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查是否还有其他标签
                if remainingAfterFull.contains("|") {
                    let nextPipeIndex = remainingAfterFull.firstIndex(of: "|")!
                    return String(remainingAfterFull[..<nextPipeIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                } else {
                    return remainingAfterFull
                }
            }
        }
        return nil
    }

    // 🧹 清理地址中的元数据信息
    private func cleanAddressMetadata(_ address: String) -> String {
        var cleanedAddress = address

        // 🎯 关键：移除所有管道符号（|）后的元数据信息
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )

        // 移除第三方sort number模式（如 ISORT:8, D90, D91等）
        let sortPatterns = [
            "\\bISORT:\\d+\\b",  // ISORT:8
            "\\bD\\d+\\b",       // D90, D91, D146等
            "\\bSORT:\\d+\\b"    // SORT:2
        ]

        for pattern in sortPatterns {
            cleanedAddress = cleanedAddress.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )
        }

        // 清理多余的空格
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        )

        return cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}
