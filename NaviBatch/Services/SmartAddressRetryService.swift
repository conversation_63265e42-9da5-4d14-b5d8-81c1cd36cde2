import Foundation
import CoreLocation

/// 智能地址重试服务 - 🤖 协调问题地址收集、AI修复和用户确认的完整流程
@MainActor
class SmartAddressRetryService: ObservableObject {
    static let shared = SmartAddressRetryService()

    private let problemCollector = ProblemAddressCollector.shared
    private let aiCorrector = AIAddressCorrector.shared
    private let userAddressDB = UserAddressDatabase.shared

    @Published var isProcessing = false
    @Published var currentPhase: ProcessingPhase = .idle

    enum ProcessingPhase {
        case idle
        case collectingProblems
        case aiCorrection
        case userConfirmation
        case completed

        var description: String {
            switch self {
            case .idle:
                return NSLocalizedString("phase_idle", comment: "")
            case .collectingProblems:
                return NSLocalizedString("phase_collecting_problems", comment: "")
            case .aiCorrection:
                return NSLocalizedString("phase_ai_correction", comment: "")
            case .userConfirmation:
                return NSLocalizedString("phase_user_confirmation", comment: "")
            case .completed:
                return NSLocalizedString("phase_completed", comment: "")
            }
        }
    }



    private init() {}

    // MARK: - 主要处理流程

    /// 处理所有收集的问题地址
    /// - Parameter source: 触发处理的来源（用于日志记录）
    func processCollectedProblems(source: String) async {
        guard !isProcessing else {
            print("🤖 SMART_RETRY: 已在处理中，跳过重复请求")
            return
        }

        let problemAddresses = problemCollector.problemAddresses
        guard !problemAddresses.isEmpty else {
            print("🤖 SMART_RETRY: 没有问题地址需要处理")
            return
        }

        print("🤖 SMART_RETRY: 🎯 发现问题地址，提示用户手动处理，来源: \(source)")
        print("🤖 SMART_RETRY: 🎯 用户可以通过编辑地址来修复问题")
        print("🤖 SMART_RETRY: 待处理问题地址数量: \(problemAddresses.count)")

        isProcessing = true
        currentPhase = .userConfirmation

        // 🎯 新逻辑：不进行AI修复，直接提示用户
        await notifyUserAboutProblems(problemAddresses)

        // 完成处理
        currentPhase = .completed
        print("🤖 SMART_RETRY: 🎯 已提示用户处理问题地址，等待用户手动编辑")

        isProcessing = false
    }

    // MARK: - 用户通知

    /// 通知用户关于问题地址，不进行自动修复
    private func notifyUserAboutProblems(_ problemAddresses: [ProblemAddress]) async {
        await MainActor.run {
            // 设置问题地址状态，让UI显示提示
            for address in problemAddresses {
                print("🤖 SMART_RETRY: 💡 问题地址: \(address.originalAddress)")
                print("🤖 SMART_RETRY: 💡 失败原因: \(address.failureReason.localizedDescription)")
                print("🤖 SMART_RETRY: 💡 建议: 用户可以在地址列表中编辑此地址来修复问题")
            }

            // 这里可以设置一个Published属性来让UI显示问题地址提示
            // 例如：problemCollector.showProblemsAlert = true
            print("🤖 SMART_RETRY: 💡 总计 \(problemAddresses.count) 个地址需要用户确认或编辑")
        }
    }

    // MARK: - 结果分类和处理

    /// 后台自动处理AI修复结果，用户完全无感知 - 🚫 已禁用
    private func processResultsInBackground(_ correctedAddresses: [ProblemAddress]) async {
        // 🚫 此方法已禁用，不再自动修复地址
        print("🤖 SMART_RETRY: ⚠️ 自动修复功能已禁用，跳过后台处理")
        return

        // 以下代码已禁用，保留用于参考
        /*
        var successCount = 0
        var failureCount = 0

        for address in correctedAddresses {
            if let correctionResult = address.lastAICorrectionResult,
               let verificationResult = correctionResult.verificationResult,
               verificationResult.isValid,
               let coordinate = verificationResult.verifiedCoordinate {

                // 🎯 新增：检测并修复缺少州缩写的地址
                var finalCorrectedAddress = correctionResult.correctedAddress
                if let stateFixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: correctionResult.correctedAddress) {
                    finalCorrectedAddress = stateFixedAddress
                    print("🤖 SMART_RETRY: 🔧 检测到缺少州缩写，自动修复: '\(correctionResult.correctedAddress)' -> '\(stateFixedAddress)'")
                }

                // AI修复成功，保存到用户地址数据库
                // 🎯 修复：提高AI修复地址的置信度，特别是经过州缩写修复的地址
                let finalConfidence: Float = {
                    if finalCorrectedAddress != correctionResult.correctedAddress {
                        // 经过州缩写修复的地址，置信度更高
                        return Float(min(correctionResult.confidence * 0.9, 0.95))
                    } else {
                        // 普通AI修复，适度降低置信度
                        return Float(min(correctionResult.confidence * 0.8, 0.85))
                    }
                }()

                await userAddressDB.saveValidatedAddress(
                    finalCorrectedAddress,
                    coordinate: coordinate,
                    source: .aiCorrection, // 使用特殊的AI修复来源标记
                    confidence: finalConfidence
                )

                // 🎯 关键修复：将AI修复成功的地址添加到当前路线
                await addFixedAddressToCurrentRoute(
                    originalAddress: address.originalAddress,
                    correctedAddress: finalCorrectedAddress, // 使用最终修复后的地址
                    coordinate: coordinate
                )

                successCount += 1
                print("🤖 SMART_RETRY: ✅ 后台自动保存AI修复地址: '\(address.originalAddress)' -> '\(finalCorrectedAddress)'")

            } else {
                // AI修复失败，静默处理，交给现有错误提示逻辑
                failureCount += 1
                print("🤖 SMART_RETRY: ❌ AI修复失败，交给现有错误处理: '\(address.originalAddress)'")
            }
        }

        let totalProcessed = correctedAddresses.count
        let successRate = totalProcessed > 0 ? Double(successCount) / Double(totalProcessed) : 0.0

        print("🤖 SMART_RETRY: 处理完成 - AI修复成功: \(successCount), 失败: \(failureCount), 成功率: \(String(format: "%.1f%%", successRate * 100))")
        */
    }

    /// 将AI修复成功的地址添加到当前路线
    /// 🚨 注意：此功能可能导致重复地址，建议用户手动确认后添加
    private func addFixedAddressToCurrentRoute(
        originalAddress: String,
        correctedAddress: String,
        coordinate: CLLocationCoordinate2D
    ) async {
        // 🎯 临时禁用自动添加功能，避免重复地址问题
        // 用户可以通过问题地址界面手动添加修复后的地址
        print("🤖 SMART_RETRY: ⚠️ 自动添加功能已禁用，避免重复地址问题")
        print("🤖 SMART_RETRY: 修复地址: \(correctedAddress)")
        print("🤖 SMART_RETRY: 用户可通过问题地址界面手动添加此地址")
        return

        // 以下代码已禁用，保留用于参考
        /*
        await MainActor.run {
            // 获取当前路线视图模型
            let routeViewModel = RouteViewModel.shared

            // 检查是否有活跃的路线
            guard let currentRoute = routeViewModel.currentRoute else {
                print("🤖 SMART_RETRY: 没有活跃路线，跳过添加修复地址")
                return
            }

            // 检查是否有ModelContext
            guard let modelContext = routeViewModel.modelContext else {
                print("🤖 SMART_RETRY: 没有ModelContext，跳过添加修复地址")
                return
            }

            // 🎯 加强重复检查逻辑，避免重复添加
            let normalizedCorrectedAddress = correctedAddress.lowercased()
                .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
                .replacingOccurrences(of: "[,.]", with: "", options: .regularExpression)
                .trimmingCharacters(in: .whitespacesAndNewlines)

            // 检查是否已存在相同或相似的地址
            let existingPoint = currentRoute.points.first { point in
                let normalizedExisting = point.primaryAddress.lowercased()
                    .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
                    .replacingOccurrences(of: "[,.]", with: "", options: .regularExpression)
                    .trimmingCharacters(in: .whitespacesAndNewlines)

                // 完全匹配
                if normalizedExisting == normalizedCorrectedAddress {
                    return true
                }

                // 检查是否包含相同的街道地址（去掉城市、州、国家部分）
                let existingStreetPart = normalizedExisting.components(separatedBy: " ").prefix(4).joined(separator: " ")
                let correctedStreetPart = normalizedCorrectedAddress.components(separatedBy: " ").prefix(4).joined(separator: " ")

                return existingStreetPart == correctedStreetPart && existingStreetPart.count > 5
            }

            if existingPoint != nil {
                print("🤖 SMART_RETRY: 地址已存在于路线中，跳过添加: \(correctedAddress)")
                print("🤖 SMART_RETRY: 现有地址: \(existingPoint!.primaryAddress)")
                return
            }

            // 🎯 重要：保持原始地址的元数据信息（追踪号、客户信息等）
            // 但使用修复后的地址作为主要地址部分
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(originalAddress)

            // 构建最终地址：使用修复后的地址 + 原始元数据
            var finalAddress = correctedAddress
            if !separatedInfo.tracking.isEmpty {
                finalAddress += "|TRACK:\(separatedInfo.tracking)"
            }
            if !separatedInfo.customer.isEmpty {
                finalAddress += "|CUSTOMER:\(separatedInfo.customer)"
            }
            if !separatedInfo.sortNumber.isEmpty {
                finalAddress += "|SORT:\(separatedInfo.sortNumber)"
            }
            if !separatedInfo.appType.isEmpty && separatedInfo.appType != "manual" {
                finalAddress += "|APP:\(separatedInfo.appType)"
            }

            print("🤖 SMART_RETRY: 🎯 添加修复地址到路线: '\(finalAddress)'")
            print("🤖 SMART_RETRY: 🎯 坐标: (\(coordinate.latitude), \(coordinate.longitude))")

            // 使用DeliveryPointManager创建地址点并添加到路线
            DeliveryPointManager.shared.createDeliveryPoint(
                address: finalAddress,
                route: currentRoute,
                modelContext: modelContext,
                userSelectedAppType: .manual
            ) { deliveryPoint, error in
                if let error = error {
                    print("🤖 SMART_RETRY: ❌ 添加修复地址失败: \(error.localizedDescription)")
                } else if let deliveryPoint = deliveryPoint {
                    print("🤖 SMART_RETRY: ✅ AI修复地址已添加到当前路线: \(deliveryPoint.originalAddress ?? "未知地址")")
                }
            }
        }
        */
    }





    // MARK: - 状态管理

    /// 重置处理状态
    func resetProcessingState() {
        isProcessing = false
        currentPhase = .idle
        problemCollector.clearAllProblemAddresses()
        print("🤖 SMART_RETRY: 已重置处理状态")
    }

    /// 获取处理进度信息
    var progressInfo: String {
        switch currentPhase {
        case .idle:
            return NSLocalizedString("waiting_for_processing", comment: "")
        case .collectingProblems:
            return NSLocalizedString("collecting_problem_addresses", comment: "")
        case .aiCorrection:
            let progress = problemCollector.aiCorrectionProgress
            let current = problemCollector.currentProcessingAddress
            let progressText = NSLocalizedString("ai_correction_in_progress", comment: "")
            let currentText = NSLocalizedString("current_processing", comment: "")
            return "\(progressText) \(String(format: "%.0f%%", progress * 100))\n\(currentText): \(current)"
        case .userConfirmation:
            return NSLocalizedString("background_processing", comment: "")
        case .completed:
            return NSLocalizedString("processing_complete", comment: "")
        }
    }

    /// 检查是否有待处理的问题地址
    var hasPendingProblems: Bool {
        !problemCollector.problemAddresses.isEmpty
    }
}
