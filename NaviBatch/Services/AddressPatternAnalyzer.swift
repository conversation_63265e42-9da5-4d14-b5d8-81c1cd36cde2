import Foundation
import CoreLocation

/// 地址模式分析器 - 分析现有地址，提取地址模式用于预测缺失地址
class AddressPatternAnalyzer {

    /// 地址模式结构
    struct AddressPattern {
        let streetName: String          // 街道名称
        let baseNumber: Int             // 基础门牌号
        let increment: Int              // 递增步长（通常是1或2）
        let isOddSequence: Bool         // 是否为奇数序列
        let suffix: String              // 后缀（如Apt, Unit等）
        let city: String                // 城市
        let state: String               // 州
        let zipCode: String             // 邮编
        let confidence: Double          // 置信度 (0.0-1.0)
    }

    /// 预测地址结果
    struct PredictedAddress {
        let stopNumber: Int             // 停靠点序号
        let fullAddress: String         // 完整地址
        let confidence: Double          // 置信度
        let pattern: AddressPattern     // 使用的模式
    }

    /// 分析现有地址，提取地址模式
    /// - Parameter addresses: 现有地址数组 (格式: "地址|第三方排序号|追踪号")
    /// - Returns: 检测到的地址模式数组
    func analyzeAddressPatterns(from addresses: [String]) -> [AddressPattern] {
        var patterns: [AddressPattern] = []

        // 解析所有地址
        let parsedAddresses = addresses.compactMap { parseAddress($0) }

        // 按街道名称分组
        let groupedByStreet = Dictionary(grouping: parsedAddresses) { $0.streetName }

        for (streetName, streetAddresses) in groupedByStreet {
            if let pattern = analyzeStreetPattern(streetName: streetName, addresses: streetAddresses) {
                patterns.append(pattern)
            }
        }

        return patterns.sorted { $0.confidence > $1.confidence }
    }

    /// 基于模式预测缺失地址
    /// - Parameters:
    ///   - missingStopNumbers: 缺失的停靠点序号
    ///   - patterns: 检测到的地址模式
    /// - Returns: 预测的地址数组
    func predictMissingAddresses(missingStopNumbers: [Int], patterns: [AddressPattern]) -> [PredictedAddress] {
        var predictions: [PredictedAddress] = []

        for stopNumber in missingStopNumbers {
            if let prediction = predictAddressForStopNumber(stopNumber, patterns: patterns) {
                predictions.append(prediction)
            }
        }

        return predictions.sorted { $0.stopNumber < $1.stopNumber }
    }

    // MARK: - Private Methods

    /// 解析地址信息
    private func parseAddress(_ address: String) -> ParsedAddress? {
        let components = address.components(separatedBy: "|")
        guard components.count >= 2,
              let stopNumber = Int(components[1]) else {
            return nil
        }

        let fullAddress = components[0].trimmingCharacters(in: .whitespacesAndNewlines)

        // 提取地址组件
        guard let addressComponents = extractAddressComponents(from: fullAddress) else {
            return nil
        }

        return ParsedAddress(
            stopNumber: stopNumber,
            fullAddress: fullAddress,
            streetNumber: addressComponents.streetNumber,
            streetName: addressComponents.streetName,
            suffix: addressComponents.suffix,
            city: addressComponents.city,
            state: addressComponents.state,
            zipCode: addressComponents.zipCode
        )
    }

    /// 提取地址组件
    private func extractAddressComponents(from address: String) -> PatternAddressComponents? {
        // 正则表达式匹配地址格式
        let patterns = [
            // 格式1: "397 Imperial Way Apt 238, Daly City, CA, 94015"
            #"^(\d+)\s+([^,]+?)(?:\s+(Apt|Unit|Suite|#)\s*([^,]+?))?,\s*([^,]+),\s*([A-Z]{2}),?\s*(\d{5})"#,
            // 格式2: "25 Hyde court #2, Daly City, CA, 94015"
            #"^(\d+)\s+([^,#]+?)(?:\s*[#]\s*([^,]+?))?,\s*([^,]+),\s*([A-Z]{2}),?\s*(\d{5})"#,
            // 格式3: "70 Margate st., Daly City, CA, 94015"
            #"^(\d+)\s+([^,]+?),\s*([^,]+),\s*([A-Z]{2}),?\s*(\d{5})"#
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
               let match = regex.firstMatch(in: address, options: [], range: NSRange(location: 0, length: address.count)) {

                let streetNumber = Int(String(address[Range(match.range(at: 1), in: address)!])) ?? 0
                let streetName = String(address[Range(match.range(at: 2), in: address)!]).trimmingCharacters(in: .whitespacesAndNewlines)

                var suffix = ""
                var city = ""
                var state = ""
                var zipCode = ""

                if match.numberOfRanges >= 7 { // 完整格式
                    if match.range(at: 3).location != NSNotFound {
                        suffix = String(address[Range(match.range(at: 3), in: address)!]) + " " + String(address[Range(match.range(at: 4), in: address)!])
                    }
                    city = String(address[Range(match.range(at: 5), in: address)!])
                    state = String(address[Range(match.range(at: 6), in: address)!])
                    zipCode = String(address[Range(match.range(at: 7), in: address)!])
                } else if match.numberOfRanges >= 6 { // 简化格式
                    city = String(address[Range(match.range(at: 4), in: address)!])
                    state = String(address[Range(match.range(at: 5), in: address)!])
                    zipCode = String(address[Range(match.range(at: 6), in: address)!])
                }

                return PatternAddressComponents(
                    streetNumber: streetNumber,
                    streetName: streetName,
                    suffix: suffix,
                    city: city,
                    state: state,
                    zipCode: zipCode
                )
            }
        }

        return nil
    }

    /// 分析街道模式
    private func analyzeStreetPattern(streetName: String, addresses: [ParsedAddress]) -> AddressPattern? {
        guard addresses.count >= 2 else { return nil }

        // 按停靠点序号排序
        let sortedAddresses = addresses.sorted { $0.stopNumber < $1.stopNumber }

        // 分析门牌号模式
        let streetNumbers = sortedAddresses.map { $0.streetNumber }
        let stopNumbers = sortedAddresses.map { $0.stopNumber }

        // 检测递增模式
        guard let increment = detectIncrement(streetNumbers: streetNumbers, stopNumbers: stopNumbers) else {
            return nil
        }

        // 检测奇偶性
        let isOddSequence = streetNumbers.allSatisfy { $0 % 2 == 1 }

        // 使用第一个地址作为基础
        let baseAddress = sortedAddresses.first!

        // 计算置信度
        let confidence = calculatePatternConfidence(addresses: sortedAddresses, increment: increment)

        return AddressPattern(
            streetName: streetName,
            baseNumber: baseAddress.streetNumber,
            increment: increment,
            isOddSequence: isOddSequence,
            suffix: baseAddress.suffix,
            city: baseAddress.city,
            state: baseAddress.state,
            zipCode: baseAddress.zipCode,
            confidence: confidence
        )
    }

    /// 检测递增步长
    private func detectIncrement(streetNumbers: [Int], stopNumbers: [Int]) -> Int? {
        guard streetNumbers.count >= 2, stopNumbers.count >= 2 else { return nil }

        var increments: [Int] = []

        for i in 1..<streetNumbers.count {
            let streetDiff = streetNumbers[i] - streetNumbers[i-1]
            let stopDiff = stopNumbers[i] - stopNumbers[i-1]

            if stopDiff > 0 {
                let increment = streetDiff / stopDiff
                increments.append(increment)
            }
        }

        // 找到最常见的递增值
        let incrementCounts = Dictionary(grouping: increments, by: { $0 }).mapValues { $0.count }
        return incrementCounts.max(by: { $0.value < $1.value })?.key
    }

    /// 计算模式置信度
    private func calculatePatternConfidence(addresses: [ParsedAddress], increment: Int) -> Double {
        guard addresses.count >= 2 else { return 0.0 }

        var correctPredictions = 0
        let totalPredictions = addresses.count - 1

        for i in 1..<addresses.count {
            let expectedNumber = addresses[0].streetNumber + (addresses[i].stopNumber - addresses[0].stopNumber) * increment
            if expectedNumber == addresses[i].streetNumber {
                correctPredictions += 1
            }
        }

        return Double(correctPredictions) / Double(totalPredictions)
    }

    /// 为特定停靠点序号预测地址
    private func predictAddressForStopNumber(_ stopNumber: Int, patterns: [AddressPattern]) -> PredictedAddress? {
        guard let bestPattern = patterns.first else { return nil }

        // 基于模式计算预测的门牌号
        let predictedStreetNumber = bestPattern.baseNumber + (stopNumber - 1) * bestPattern.increment

        // 构建完整地址
        var fullAddress = "\(predictedStreetNumber) \(bestPattern.streetName)"

        if !bestPattern.suffix.isEmpty {
            fullAddress += " \(bestPattern.suffix)"
        }

        fullAddress += ", \(bestPattern.city), \(bestPattern.state), \(bestPattern.zipCode)"

        return PredictedAddress(
            stopNumber: stopNumber,
            fullAddress: fullAddress,
            confidence: bestPattern.confidence,
            pattern: bestPattern
        )
    }
}

// MARK: - Supporting Structures

private struct ParsedAddress {
    let stopNumber: Int
    let fullAddress: String
    let streetNumber: Int
    let streetName: String
    let suffix: String
    let city: String
    let state: String
    let zipCode: String
}

private struct PatternAddressComponents {
    let streetNumber: Int
    let streetName: String
    let suffix: String
    let city: String
    let state: String
    let zipCode: String
}
