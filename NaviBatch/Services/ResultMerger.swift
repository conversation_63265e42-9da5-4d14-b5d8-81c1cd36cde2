//
//  ResultMerger.swift
//  NaviBatch
//
//  Created by AI Assistant on 2025-06-23.
//  结果合并和验证系统 - 合并多个批次的处理结果
//

import Foundation

/// 合并结果
struct MergedResult {
    let addresses: [String]
    let totalProcessed: Int
    let uniqueAddresses: Int
    let duplicatesRemoved: Int
    let validation: PDFValidationResult
    let processingStats: ProcessingStats
}

/// PDF验证结果
struct PDFValidationResult {
    let isComplete: Bool
    let missingRanges: [Range<Int>]
    let duplicateCount: Int
    let sortNumberGaps: [Int]
    let qualityScore: Double
    let issues: [ValidationIssue]
}

/// 验证问题
struct ValidationIssue {
    let type: IssueType
    let description: String
    let severity: Severity
    
    enum IssueType {
        case missingAddresses
        case duplicateAddresses
        case sortNumberGap
        case invalidAddress
        case lowQuality
    }
    
    enum Severity {
        case low, medium, high, critical
    }
}

/// 处理统计
struct ProcessingStats {
    let totalBatches: Int
    let successfulBatches: Int
    let failedBatches: Int
    let totalProcessingTime: TimeInterval
    let averageProcessingTime: TimeInterval
    let addressesPerSecond: Double
}

/// 结果合并器
class ResultMerger {
    
    // MARK: - Public Methods
    
    /// 合并处理结果
    func mergeResults(_ results: [ProcessingResult]) -> MergedResult {
        Logger.info("🔄 开始合并\(results.count)个批次的处理结果")
        
        let startTime = Date()
        
        // 1. 提取所有地址
        let allAddresses = extractAllAddresses(from: results)
        Logger.debug("📊 提取到\(allAddresses.count)个地址")
        
        // 2. 去重处理
        let uniqueAddresses = removeDuplicates(allAddresses)
        let duplicatesRemoved = allAddresses.count - uniqueAddresses.count
        Logger.debug("🧹 去重后\(uniqueAddresses.count)个地址，移除\(duplicatesRemoved)个重复")
        
        // 3. 排序处理
        let sortedAddresses = sortAddresses(uniqueAddresses)
        Logger.debug("📋 地址排序完成")
        
        // 4. 验证完整性
        let validation = validateCompleteness(sortedAddresses, originalResults: results)
        Logger.debug("✅ 完整性验证完成，质量分数: \(validation.qualityScore)")
        
        // 5. 计算统计信息
        let stats = calculateStats(results)
        
        let processingTime = Date().timeIntervalSince(startTime)
        Logger.info("🏁 结果合并完成，用时: \(Int(processingTime * 1000))ms")
        
        return MergedResult(
            addresses: sortedAddresses,
            totalProcessed: allAddresses.count,
            uniqueAddresses: uniqueAddresses.count,
            duplicatesRemoved: duplicatesRemoved,
            validation: validation,
            processingStats: stats
        )
    }
    
    // MARK: - Private Methods - Address Processing
    
    /// 提取所有地址
    private func extractAllAddresses(from results: [ProcessingResult]) -> [String] {
        var allAddresses: [String] = []

        for result in results {
            if result.success {
                allAddresses.append(contentsOf: result.addresses)
            }
        }

        return allAddresses
    }
    
    /// 去重处理
    private func removeDuplicates(_ addresses: [String]) -> [String] {
        var seen = Set<String>()
        var uniqueAddresses: [String] = []

        for address in addresses {
            // 清理地址用于比较
            let cleanAddress = cleanAddressForComparison(address)

            if seen.insert(cleanAddress).inserted {
                uniqueAddresses.append(address)
            } else {
                Logger.debug("🔍 发现重复地址: \(address)")
            }
        }

        return uniqueAddresses
    }
    

    
    /// 清理地址用于比较
    private func cleanAddressForComparison(_ address: String) -> String {
        // 提取地址的核心部分进行比较
        let components = address.components(separatedBy: "|")
        let addressPart = components.first ?? address

        return addressPart
            .lowercased()
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .replacingOccurrences(of: "[,.]", with: "", options: .regularExpression)
            .replacingOccurrences(of: "apt", with: "apartment")
            .replacingOccurrences(of: "st", with: "street")
            .replacingOccurrences(of: "ave", with: "avenue")
            .replacingOccurrences(of: "blvd", with: "boulevard")
            .replacingOccurrences(of: "dr", with: "drive")
            .replacingOccurrences(of: "ct", with: "court")
            .replacingOccurrences(of: "ln", with: "lane")
            .replacingOccurrences(of: "rd", with: "road")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 排序地址
    private func sortAddresses(_ addresses: [String]) -> [String] {
        return addresses.sorted { $0 < $1 }
    }
    
    // MARK: - Private Methods - Validation
    
    /// 验证完整性
    private func validateCompleteness(_ addresses: [String], originalResults: [ProcessingResult]) -> PDFValidationResult {
        var issues: [ValidationIssue] = []

        // 1. 检查批次处理成功率
        let failedBatches = originalResults.filter { !$0.success }
        if !failedBatches.isEmpty {
            issues.append(ValidationIssue(
                type: .missingAddresses,
                description: "\(failedBatches.count)个批次处理失败",
                severity: .high
            ))
        }

        // 2. 计算质量分数
        let successRate = Double(originalResults.filter { $0.success }.count) / Double(originalResults.count)
        let qualityScore = successRate * 100.0
        
        return PDFValidationResult(
            isComplete: issues.filter { $0.severity == .critical || $0.severity == .high }.isEmpty,
            missingRanges: [],
            duplicateCount: 0,
            sortNumberGaps: [],
            qualityScore: qualityScore,
            issues: issues
        )
    }

    
    // MARK: - Private Methods - Statistics
    
    /// 计算处理统计
    func calculateStats(_ results: [ProcessingResult]) -> ProcessingStats {
        let successfulBatches = results.filter { $0.success }
        let failedBatches = results.filter { !$0.success }
        
        let totalTime = results.reduce(0) { $0 + $1.processingTime }
        let averageTime = totalTime / Double(results.count)
        
        let totalAddresses = successfulBatches.reduce(0) { $0 + $1.addresses.count }
        let addressesPerSecond = totalTime > 0 ? Double(totalAddresses) / totalTime : 0
        
        return ProcessingStats(
            totalBatches: results.count,
            successfulBatches: successfulBatches.count,
            failedBatches: failedBatches.count,
            totalProcessingTime: totalTime,
            averageProcessingTime: averageTime,
            addressesPerSecond: addressesPerSecond
        )
    }
}
