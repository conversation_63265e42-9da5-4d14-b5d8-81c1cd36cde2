//
//  BatchProcessingQueue.swift
//  NaviBatch
//
//  Created by AI Assistant on 2025-06-23.
//  批量处理队列系统 - 异步处理多个PDF批次
//

import Foundation
import Combine
import PDFKit
import UIKit

/// 处理结果
struct ProcessingResult {
    let batchId: UUID
    let batchNumber: Int
    let addresses: [String] // 简化为字符串数组
    let processingTime: TimeInterval
    let success: Bool
    let error: Error?

    init(batchId: UUID, batchNumber: Int, addresses: [String], processingTime: TimeInterval, success: Bool, error: Error? = nil) {
        self.batchId = batchId
        self.batchNumber = batchNumber
        self.addresses = addresses
        self.processingTime = processingTime
        self.success = success
        self.error = error
    }
}

/// 处理状态
enum ProcessingStatus {
    case idle
    case analyzing
    case processing
    case merging
    case completed
    case failed(Error)

    var displayText: String {
        switch self {
        case .idle: return "准备就绪"
        case .analyzing: return "分析PDF中..."
        case .processing: return "AI识别中..."
        case .merging: return "合并结果中..."
        case .completed: return "处理完成"
        case .failed: return "处理失败"
        }
    }
}

/// 批量处理队列
@MainActor
class BatchProcessingQueue: ObservableObject {

    // MARK: - Published Properties

    @Published var processingProgress: Double = 0.0
    @Published var currentBatch: Int = 0
    @Published var totalBatches: Int = 0
    @Published var processingStatus: ProcessingStatus = .idle
    @Published var statusMessage: String = ""
    @Published var results: [ProcessingResult] = []
    @Published var isProcessing: Bool = false

    // MARK: - Private Properties

    private let maxConcurrentBatches = 3  // 适度提升并发数，平衡速度和稳定性
    private var processingStartTime: Date?

    // MARK: - Public Methods

    /// 处理PDF批次
    func processPDF(_ pdfData: Data, appType: DeliveryAppType) async -> [String] {
        Logger.info("🚀 开始智能PDF批量处理，应用类型: \(appType.displayName)")

        // 1. 分析PDF
        processingStatus = .analyzing
        statusMessage = "正在分析PDF结构..."

        let analyzer = PDFIntelligentAnalyzer()
        let analysis = analyzer.analyzePDF(pdfData)

        // 2. 创建批次
        let batches = analyzer.createBatches(from: pdfData, analysis: analysis)

        // 3. 处理批次
        processingStatus = .processing
        let results = await processBatches(batches, appType: appType)

        // 4. 合并结果
        processingStatus = .merging
        statusMessage = "正在合并处理结果..."

        let merger = ResultMerger()
        let finalResult = merger.mergeResults(results)

        // 5. 完成处理
        processingStatus = .completed
        statusMessage = "处理完成！识别了\(finalResult.addresses.count)个地址"

        Logger.info("✅ PDF批量处理完成: \(finalResult.addresses.count)个地址")

        return finalResult.addresses
    }

    /// 处理多个批次
    private func processBatches(_ batches: [PDFBatch], appType: DeliveryAppType) async -> [ProcessingResult] {
        totalBatches = batches.count
        currentBatch = 0
        processingProgress = 0.0
        results = []
        isProcessing = true
        processingStartTime = Date()

        Logger.info("📦 开始处理\(totalBatches)个批次")

        var allResults: [ProcessingResult] = []

        // 分组处理，避免并发过多
        for batchGroup in batches.chunked(into: maxConcurrentBatches) {
            let groupResults = await withTaskGroup(of: ProcessingResult?.self) { group in
                for batch in batchGroup {
                    group.addTask {
                        await self.processSingleBatch(batch, appType: appType)
                    }
                }

                var results: [ProcessingResult] = []
                for await result in group {
                    if let result = result {
                        results.append(result)
                    }
                }
                return results
            }

            allResults.append(contentsOf: groupResults)

            // 更新进度
            currentBatch = allResults.count
            processingProgress = Double(currentBatch) / Double(totalBatches)

            let successCount = allResults.filter { $0.success }.count
            let elapsedTime = Date().timeIntervalSince(processingStartTime ?? Date())
            statusMessage = "已处理 \(currentBatch)/\(totalBatches) 批次 (成功: \(successCount), 用时: \(Int(elapsedTime))秒)"

            Logger.info("📊 批次进度: \(currentBatch)/\(totalBatches), 成功: \(successCount)")
        }

        isProcessing = false
        results = allResults

        let totalTime = Date().timeIntervalSince(processingStartTime ?? Date())
        Logger.info("🏁 所有批次处理完成，总用时: \(Int(totalTime))秒")

        return allResults
    }

    /// 处理单个批次 - 简化为OCR + AI流程
    private func processSingleBatch(_ batch: PDFBatch, appType: DeliveryAppType) async -> ProcessingResult? {
        let startTime = Date()
        Logger.info("🔄 开始处理批次 \(batch.batchNumber) - 使用OCR + AI流程")

        do {
            // 第一步：将PDF转换为图像
            let pdfImages = await convertPDFToImages(batch.pdfData)
            guard !pdfImages.isEmpty else {
                Logger.error("❌ PDF转图像失败")
                return nil
            }

            // 第二步：使用OCR提取所有文本
            var allOCRText = ""
            let ocrService = OCRService()

            for (index, image) in pdfImages.enumerated() {
                Logger.info("📄 正在OCR处理第\(index + 1)页...")
                let ocrResponse = try await ocrService.recognizeText(from: image)
                allOCRText += ocrResponse.fullText + "\n\n"
            }

            Logger.info("📝 OCR提取完成，总文本长度: \(allOCRText.count)字符")

            // 第三步：使用AI分析OCR文本
            let aiResult = try await FirebaseAIService.shared.extractAddressesFromPDFText(
                allOCRText,
                appType: appType
            )

            let processingTime = Date().timeIntervalSince(startTime)

            let result = ProcessingResult(
                batchId: batch.id,
                batchNumber: batch.batchNumber,
                addresses: aiResult.addresses,
                processingTime: processingTime,
                success: true
            )

            Logger.info("✅ 批次 \(batch.batchNumber) 处理成功: \(aiResult.addresses.count)个地址, 用时: \(Int(processingTime))秒")

            return result

        } catch {
            let processingTime = Date().timeIntervalSince(startTime)

            Logger.error("❌ 批次 \(batch.batchNumber) 处理失败: \(error)")

            return ProcessingResult(
                batchId: batch.id,
                batchNumber: batch.batchNumber,
                addresses: [],
                processingTime: processingTime,
                success: false,
                error: error
            )
        }
    }



    /// 将PDF转换为图像数组
    private func convertPDFToImages(_ pdfData: Data) async -> [UIImage] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var images: [UIImage] = []

                // 创建临时文件
                let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString + ".pdf")

                do {
                    try pdfData.write(to: tempURL)

                    guard let pdfDocument = PDFKit.PDFDocument(url: tempURL) else {
                        Logger.error("❌ 无法创建PDF文档")
                        continuation.resume(returning: [])
                        return
                    }

                    let pageCount = pdfDocument.pageCount
                    Logger.info("📄 PDF包含\(pageCount)页")

                    for pageIndex in 0..<pageCount {
                        if let page = pdfDocument.page(at: pageIndex) {
                            let pageRect = page.bounds(for: .mediaBox)

                            // 高质量渲染，确保OCR效果
                            let scale: CGFloat = 3.0 // 3倍分辨率确保清晰度
                            let scaledSize = CGSize(
                                width: pageRect.width * scale,
                                height: pageRect.height * scale
                            )

                            let renderer = UIGraphicsImageRenderer(size: scaledSize)
                            let image = renderer.image { context in
                                // 白色背景
                                UIColor.white.set()
                                context.fill(CGRect(origin: .zero, size: scaledSize))

                                // 缩放上下文
                                context.cgContext.scaleBy(x: scale, y: scale)

                                // 渲染PDF页面
                                page.draw(with: .mediaBox, to: context.cgContext)
                            }

                            images.append(image)
                            Logger.info("📄 已渲染第\(pageIndex + 1)页，尺寸: \(scaledSize.width)x\(scaledSize.height)")
                        }
                    }

                    // 清理临时文件
                    try? FileManager.default.removeItem(at: tempURL)

                } catch {
                    Logger.error("❌ PDF转图像失败: \(error)")
                    try? FileManager.default.removeItem(at: tempURL)
                }

                continuation.resume(returning: images)
            }
        }
    }

    /// 重置状态
    func reset() {
        processingProgress = 0.0
        currentBatch = 0
        totalBatches = 0
        processingStatus = .idle
        statusMessage = ""
        results = []
        isProcessing = false
        processingStartTime = nil
    }
}

// MARK: - Extensions

extension Array {
    /// 将数组分块
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}


