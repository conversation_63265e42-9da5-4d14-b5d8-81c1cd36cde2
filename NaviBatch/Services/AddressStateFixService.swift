import Foundation
import CoreLocation
import os.log

/// 地址州缩写修复服务
/// 专门处理缺少州缩写的美国地址，通过反向地理编码获取正确的州信息
class AddressStateFixService {
    static let shared = AddressStateFixService()

    private init() {}

    /// 检测并修复缺少州缩写的美国地址
    /// - Parameter address: 原始地址
    /// - Returns: 修复后的地址，如果不需要修复则返回nil
    func detectAndFixMissingState(for address: String) async -> String? {
        logInfo("🔍 AddressStateFixService - 检测地址是否缺少州缩写: \(address)")

        // 1. 检查是否是美国地址且缺少州缩写
        guard needsStateFixing(address) else {
            logInfo("✅ AddressStateFixService - 地址不需要修复: \(address)")
            return nil
        }

        // 2. 尝试通过反向地理编码获取州信息
        if let fixedAddress = await fixAddressWithGeocoding(address) {
            logInfo("✅ AddressStateFixService - 地址修复成功: \(address) -> \(fixedAddress)")
            return fixedAddress
        }

        // 3. 尝试通过ZIP码推断州信息
        if let fixedAddress = await fixAddressWithZipCode(address) {
            logInfo("✅ AddressStateFixService - 通过ZIP码修复成功: \(address) -> \(fixedAddress)")
            return fixedAddress
        }

        logInfo("❌ AddressStateFixService - 无法修复地址: \(address)")
        return nil
    }

    /// 检查地址是否需要州缩写修复
    private func needsStateFixing(_ address: String) -> Bool {
        // 检查是否包含ZIP码（美国地址特征）
        let hasZipCode = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil

        // 检查是否已经包含州缩写
        let hasStateAbbreviation = hasUSStateAbbreviation(address)

        // 如果有ZIP码但没有州缩写，则需要修复
        return hasZipCode && !hasStateAbbreviation
    }

    /// 检查地址是否包含美国州缩写
    private func hasUSStateAbbreviation(_ address: String) -> Bool {
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY",
            "DC"
        ]

        for state in usStates {
            // 检查州缩写是否作为独立单词存在
            let pattern = "\\b\(state)\\b"
            if address.range(of: pattern, options: [.regularExpression, .caseInsensitive]) != nil {
                return true
            }
        }

        return false
    }

    /// 通过地理编码修复地址
    private func fixAddressWithGeocoding(_ address: String) async -> String? {
        do {
            let geocoder = CLGeocoder()
            let placemarks = try await geocoder.geocodeAddressString(address)

            guard let placemark = placemarks.first,
                  let state = placemark.administrativeArea else {
                logInfo("⚠️ AddressStateFixService - 地理编码未返回州信息: \(address)")
                return nil
            }

            // 获取州缩写
            let stateAbbreviation = getStateAbbreviation(for: state)

            // 构建修复后的地址
            return buildFixedAddress(originalAddress: address, state: stateAbbreviation, placemark: placemark)

        } catch {
            logInfo("❌ AddressStateFixService - 地理编码失败: \(error.localizedDescription)")
            return nil
        }
    }

    /// 通过ZIP码推断州信息
    private func fixAddressWithZipCode(_ address: String) async -> String? {
        // 提取ZIP码
        guard let zipCode = extractZipCode(from: address) else {
            return nil
        }

        // 通过ZIP码获取州信息
        guard let state = getStateFromZipCode(zipCode) else {
            logInfo("⚠️ AddressStateFixService - 无法从ZIP码推断州信息: \(zipCode)")
            return nil
        }

        // 构建修复后的地址
        return insertStateIntoAddress(address, state: state)
    }

    /// 从地址中提取ZIP码
    private func extractZipCode(from address: String) -> String? {
        let zipPattern = "\\b(\\d{5}(-\\d{4})?)\\b"
        guard let range = address.range(of: zipPattern, options: .regularExpression) else {
            return nil
        }
        return String(address[range])
    }

    /// 根据ZIP码获取州缩写（简化版本，实际应用中可能需要更完整的数据库）
    private func getStateFromZipCode(_ zipCode: String) -> String? {
        let zip = String(zipCode.prefix(5))
        let zipInt = Int(zip) ?? 0

        // 简化的ZIP码到州的映射（部分常见范围）
        switch zipInt {
        case 90000...96199: return "CA"  // 加利福尼亚
        case 84000...84799: return "UT"  // 犹他州
        case 94000...94999: return "CA"  // 加利福尼亚（旧金山湾区）
        case 95000...95999: return "CA"  // 加利福尼亚（萨克拉门托等）
        case 10000...14999: return "NY"  // 纽约州
        case 75000...75999: return "TX"  // 德克萨斯州（达拉斯）
        case 77000...77999: return "TX"  // 德克萨斯州（休斯顿）
        case 33000...34999: return "FL"  // 佛罗里达州
        case 60000...60999: return "IL"  // 伊利诺伊州（芝加哥）
        default:
            logInfo("⚠️ AddressStateFixService - 未知ZIP码范围: \(zipCode)")
            return nil
        }
    }

    /// 获取州的标准缩写
    private func getStateAbbreviation(for stateName: String) -> String {
        let stateMap = [
            "California": "CA",
            "Utah": "UT",
            "New York": "NY",
            "Texas": "TX",
            "Florida": "FL",
            "Illinois": "IL"
            // 可以根据需要添加更多州
        ]

        return stateMap[stateName] ?? stateName
    }

    /// 构建修复后的地址 - 只添加州简称，不添加城市
    private func buildFixedAddress(originalAddress: String, state: String, placemark: CLPlacemark) -> String {
        // 🎯 用户要求：只添加州简称，不要添加城市或其他信息
        // 简单地在原地址中插入州简称
        return insertStateIntoAddress(originalAddress, state: state)
    }

    /// 构建街道地址部分
    private func buildStreetAddress(from placemark: CLPlacemark) -> String? {
        var streetComponents: [String] = []

        if let streetNumber = placemark.subThoroughfare {
            streetComponents.append(streetNumber)
        }

        if let streetName = placemark.thoroughfare {
            streetComponents.append(streetName)
        }

        return streetComponents.isEmpty ? nil : streetComponents.joined(separator: " ")
    }

    /// 将州信息插入到现有地址中
    private func insertStateIntoAddress(_ address: String, state: String) -> String {
        // 查找ZIP码位置
        guard let zipRange = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) else {
            return address
        }

        // 在ZIP码前插入州缩写
        let beforeZip = String(address[..<zipRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
        let zipAndAfter = String(address[zipRange.lowerBound...])

        return "\(beforeZip), \(state), \(zipAndAfter)"
    }
}

// MARK: - 日志辅助函数
private func logInfo(_ message: String) {
    Logger.info(message, type: .data)
}
