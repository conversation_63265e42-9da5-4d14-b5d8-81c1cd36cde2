//
//  FirebaseAIService.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//

import Foundation
import UIKit
// Firebase AI SDK imports
import FirebaseCore
import FirebaseAI

// MARK: - Firebase AI Service (备用AI服务)
class FirebaseAIService {
    static let shared = FirebaseAIService()

    // 🎯 统一图片压缩质量配置 - 所有快递使用相同的压缩质量，便于维护
    private static let unifiedCompressionQuality: CGFloat = 0.1   // 10%质量，极限压缩，最大化处理速度
    private static let unifiedMaxDimension: CGFloat = 2048        // 统一最大尺寸
    private static let unifiedMaxPixels: CGFloat = 4_000_000      // 统一最大像素数
    private static let unifiedCompressionScale: CGFloat = 0.5     // 统一超长图片压缩比例

    // 🎯 提示词版本管理 - 防止快递间相互干扰，每个快递独立的AI提示词
    private let promptVersions: [DeliveryAppType: String] = [
        .speedx: "v2.1-isolated",
        .gofo: "v2.1-isolated",
        .uniuni: "v2.0-stable",
        .ywe: "v2.0-stable",
        .amazonFlex: "v2.0-stable",
        .imile: "v2.0-stable",
        .ldsEpod: "v2.0-stable",
        .piggy: "v2.0-stable"
    ]

    // 🚫 重复调用检测 - 防止同一图片被处理多次
    private var processingImageHashes: Set<String> = []
    private let processingQueue = DispatchQueue(label: "firebase.ai.processing", attributes: .concurrent)

    private init() {}

    // 🔍 调试方法：检查重复调用状态
    func getDuplicateCallStatus() -> (activeHashes: Int, recentCalls: [String]) {
        return processingQueue.sync {
            let recentCalls = Array(processingImageHashes.prefix(5))
            return (activeHashes: processingImageHashes.count, recentCalls: recentCalls)
        }
    }

    // 🎯 当前处理的应用类型（用于地址清理逻辑）
    private var currentAppType: DeliveryAppType?

    private func getCurrentAppType() -> DeliveryAppType? {
        return currentAppType
    }

    private func setCurrentAppType(_ appType: DeliveryAppType) {
        currentAppType = appType
    }

    // 🔴 SpeedX专用地址优化：智能处理州简称，提高Apple Maps识别准确率
    private func optimizeSpeedXAddress(_ address: String, contextAddresses: [String] = []) -> String {
        var optimized = address

        // 🚨 紧急修复：记录原始地址用于调试
        Logger.aiDebug("🔴 SpeedX地址处理开始 - 原始: '\(address)'")

        // 1. 🚨 USPS格式验证 - 确保地址结尾符合美国地址标准
        if !isValidUSPSAddressFormat(address) {
            Logger.aiError("🚨 SpeedX地址格式不符合USPS标准，可能导致错误路由: '\(address)'")
            // 如果格式不符合，尝试修复而不是激进处理
            optimized = fixUSPSAddressFormat(address)
        } else {
            // 2. 移除错误包含的客户姓名（保守处理）
            optimized = removeCustomerNamesFromAddressConservative(optimized)

            // 3. 移除国家信息（USA, United States等）
            optimized = removeCountryInformation(optimized)

            // 4. 🆕 智能地址补全 - 利用上下文信息补全缺失的城市名
            optimized = smartAddressCompletion(optimized, contextAddresses: contextAddresses)

            // 5. 确保有州简称（这是Apple Maps精准识别的关键）
            optimized = ensureStateAbbreviation(optimized)

            // 6. 优化地址格式以提高识别率
            optimized = optimizeAddressFormat(optimized)
        }

        // 🚨 记录处理结果
        Logger.aiDebug("🔴 SpeedX地址处理完成 - 结果: '\(optimized)'")

        // 🚨 验证处理结果是否仍然是有效的美国地址
        if !isValidUSPSAddressFormat(optimized) {
            Logger.aiError("🚨 SpeedX地址处理后格式无效，尝试智能修复: '\(optimized)'")
            // 尝试智能修复而不是直接回退
            let smartFixed = intelligentAddressFix(optimized, original: address, contextAddresses: contextAddresses)
            if isValidUSPSAddressFormat(smartFixed) {
                Logger.aiDebug("✅ SpeedX地址智能修复成功: '\(optimized)' -> '\(smartFixed)'")
                return smartFixed
            } else {
                Logger.aiError("🚨 SpeedX地址智能修复失败，回退到原始地址: '\(smartFixed)' -> '\(address)'")
                return address // 最后回退到原始地址
            }
        }

        return optimized
    }

    // 🚨 USPS格式验证 - 确保地址结尾符合美国地址标准（宽松版本）
    private func isValidUSPSAddressFormat(_ address: String) -> Bool {
        let trimmed = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果地址为空或太短，认为无效
        if trimmed.count < 10 {
            return false
        }

        // 美国州简称列表
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]

        // 允许的国家名称
        let allowedCountries = ["USA", "United States", "US"]

        // 检查地址是否以州简称结尾
        for state in usStates {
            if trimmed.hasSuffix(", \(state)") || trimmed.hasSuffix(" \(state)") {
                return true
            }
            // 也检查州简称+ZIP码的情况
            if trimmed.range(of: ", \(state) \\d{5}", options: .regularExpression) != nil ||
               trimmed.range(of: ", \(state), \\d{5}", options: .regularExpression) != nil {
                return true
            }
        }

        // 检查地址是否以允许的国家名结尾
        for country in allowedCountries {
            if trimmed.hasSuffix(", \(country)") || trimmed.hasSuffix(" \(country)") {
                return true
            }
        }

        // 检查是否包含ZIP码（美国地址的标志）
        if trimmed.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil {
            return true
        }

        // 宽松检查：如果包含常见的美国城市名和逗号，认为可能是有效地址
        let commonCities = ["San Francisco", "Daly City", "Los Angeles", "New York", "Chicago", "Houston"]
        for city in commonCities {
            if trimmed.contains(city) && trimmed.contains(",") {
                return true
            }
        }

        return false
    }

    // 🚨 修复USPS地址格式 - 强化版本，处理收件人姓名
    private func fixUSPSAddressFormat(_ address: String) -> String {
        var fixed = address.trimmingCharacters(in: .whitespacesAndNewlines)

        Logger.aiDebug("🔧 开始USPS格式修复: '\(address)'")

        // 1. 首先尝试移除收件人姓名（强制处理）
        fixed = removeCustomerNamesFromAddressConservative(fixed)
        Logger.aiDebug("🧹 移除收件人姓名后: '\(fixed)'")

        // 2. 移除国家信息
        fixed = removeCountryInformation(fixed)
        Logger.aiDebug("🌍 移除国家信息后: '\(fixed)'")

        // 3. 如果地址包含可疑的非美国地址信息，尝试修复
        let suspiciousPatterns = [
            "Australia", "Africa", "Europe", "Asia", "Canada"
        ]

        for pattern in suspiciousPatterns {
            if fixed.contains(pattern) {
                Logger.aiError("🚨 检测到可疑的非美国地址信息: '\(pattern)' in '\(fixed)'")
                // 移除可疑信息
                fixed = fixed.replacingOccurrences(of: pattern, with: "")
                fixed = fixed.trimmingCharacters(in: .whitespacesAndNewlines)
                fixed = fixed.trimmingCharacters(in: CharacterSet(charactersIn: ","))
                fixed = fixed.trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }

        // 4. 确保有州简称
        fixed = ensureStateAbbreviation(fixed)
        Logger.aiDebug("🏛️ 确保州简称后: '\(fixed)'")

        // 5. 优化地址格式
        fixed = optimizeAddressFormat(fixed)
        Logger.aiDebug("🔧 USPS格式修复完成: '\(address)' -> '\(fixed)'")

        return fixed
    }

    // 🚨 保守的客户姓名移除（减少误删）
    private func removeCustomerNamesFromAddressConservative(_ address: String) -> String {
        var cleaned = address

        // 🔍 首先移除客户描述信息（如房屋颜色、特征等）
        cleaned = removeCustomerDescriptions(cleaned)

        // 🚨 强化的姓名模式（针对SpeedX截图中的实际问题）
        let conservativeNamePatterns = [
            // 针对截图中的具体问题模式
            "\\s+Inna\\s+Belyaev\\s*&?\\s*$",                    // "... Inna Belyaev &"
            "\\s+Myat\\s+Noe\\s+N\\.{3}\\s*[A-Z]?\\s*$",        // "... Myat Noe N... C"
            "\\s+Zin\\s+Myat\\s+Sa\\.{3}\\s*[A-Z]?\\s*$",       // "... Zin Myat Sa... B"
            "\\s+Karoline\\s+Al\\.{3}\\s*&?\\s*$",              // "... Karoline Al... &"
            "\\s+Brannon\\s+W\\.{3}\\s*&?\\s*$",                // "... Brannon W... &"

            // 通用模式
            "\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\s*&\\s*$",         // "... John Smith &"
            "\\s+[A-Z][a-z]{3,}\\s+[A-Z][a-z]{3,}\\s*$",        // "... Rossana Alvarez" (至少3个字符)
            "\\s+[A-Z][a-z]+\\s+[A-Z][a-z]*\\.{3}\\s*[A-Z]?\\s*$", // "... Name Na... X"
            "\\s+[A-Z][a-z]+\\s+[A-Z]\\.{3}\\s*&?\\s*$",        // "... Name A... &"

            // 地址重复 + 姓名模式
            "\\s+\\d+\\s+[A-Za-z\\s]+\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+.*$", // "1059 Wildwood Avenue, Daly City,C... Inna Belyaev &"
        ]

        for pattern in conservativeNamePatterns {
            let beforeCleaning = cleaned
            cleaned = cleaned.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )

            // 如果发生了清理，记录日志并验证结果
            if beforeCleaning != cleaned {
                Logger.aiDebug("🧹 SpeedX保守移除客户姓名: '\(beforeCleaning)' -> '\(cleaned)'")

                // 🚨 验证移除后的地址是否仍然有效
                if !isValidUSPSAddressFormat(cleaned) {
                    Logger.aiError("🚨 移除姓名后地址格式无效，回退: '\(cleaned)' -> '\(beforeCleaning)'")
                    cleaned = beforeCleaning // 回退
                }
                break // 只应用第一个匹配的模式
            }
        }

        // 清理末尾的逗号和空格
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
        cleaned = cleaned.trimmingCharacters(in: CharacterSet(charactersIn: ","))
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        // 修复常见的地址格式问题
        cleaned = fixCommonAddressFormatIssues(cleaned)

        return cleaned
    }

    // 🔧 修复常见的地址格式问题
    private func fixCommonAddressFormatIssues(_ address: String) -> String {
        var fixed = address

        // 1. 修复缺少逗号的问题（如 "1260 Edgeworth ave Daly City 307, Daly City, CA"）
        // 在公寓号和城市名之间添加逗号
        fixed = fixed.replacingOccurrences(
            of: "\\b(\\d+)\\s+([A-Z][a-z]+\\s+City)\\b",
            with: "$1, $2",
            options: .regularExpression
        )

        // 2. 修复重复的城市名（如 "Daly City 307, Daly City, CA"）
        fixed = fixed.replacingOccurrences(
            of: "\\b([A-Z][a-z]+\\s+City)\\s+\\d+,\\s+\\1\\b",
            with: "$1",
            options: .regularExpression
        )

        // 3. 确保州简称前有逗号
        fixed = fixed.replacingOccurrences(
            of: "\\s+(CA|NY|TX|FL|IL|PA|OH|GA|NC|MI|NJ|VA|WA|AZ|MA|TN|IN|MO|MD|WI|CO|MN|SC|AL|LA|KY|OR|OK|CT|IA|MS|AR|KS|UT|NV|NM|WV|NE|ID|NH|HI|ME|RI|MT|DE|SD|ND|AK|VT|WY|DC)\\b",
            with: ", $1",
            options: .regularExpression
        )

        // 4. 移除多余的逗号
        fixed = fixed.replacingOccurrences(
            of: ",\\s*,",
            with: ",",
            options: .regularExpression
        )

        // 5. 确保ZIP码前有空格
        fixed = fixed.replacingOccurrences(
            of: ",\\s*(\\d{5}(-\\d{4})?)",
            with: ", $1",
            options: .regularExpression
        )

        return fixed.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 🚨 原有的激进方法保留作为备用（已弃用）
    private func removeCustomerNamesFromAddress(_ address: String) -> String {
        var cleaned = address

        // 🔍 首先移除客户描述信息（如房屋颜色、特征等）
        cleaned = removeCustomerDescriptions(cleaned)

        // 常见的客户姓名模式（通常出现在地址末尾）
        let namePatterns = [
            // 匹配地址末尾的姓名模式，如 "... Inna Belyaev &"
            "\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\s*&?\\s*$",
            // 匹配地址末尾的姓名模式，如 "... John Smith"
            "\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\s*$",
            // 匹配地址末尾的缩写姓名，如 "... Myat Noe N... C"
            "\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+\\s+[A-Z]\\.{3}\\s+[A-Z]\\s*$",
            // 匹配地址末尾的单个姓名，如 "... Smith"
            "\\s+[A-Z][a-z]{2,}\\s*$"
        ]

        for pattern in namePatterns {
            let beforeCleaning = cleaned
            cleaned = cleaned.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )

            // 如果发生了清理，记录日志
            if beforeCleaning != cleaned {
                Logger.aiDebug("🧹 SpeedX移除客户姓名: '\(beforeCleaning)' -> '\(cleaned)'")
                break // 只应用第一个匹配的模式
            }
        }

        // 清理末尾的逗号和空格
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
        cleaned = cleaned.trimmingCharacters(in: CharacterSet(charactersIn: ","))
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleaned
    }

    // 🏠 移除客户描述信息（房屋特征、颜色等）
    private func removeCustomerDescriptions(_ address: String) -> String {
        var cleaned = address

        // 检测并移除ZIP码后的描述信息
        if let zipRange = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) {
            let zipEndIndex = zipRange.upperBound
            let afterZip = String(address[zipEndIndex...]).trimmingCharacters(in: .whitespacesAndNewlines)

            // 如果ZIP码后还有内容，检查是否为描述信息
            if !afterZip.isEmpty {
                // 常见的描述信息模式
                let descriptionPatterns = [
                    "\\s+The\\s+\\w+\\s+\\w+.*",  // "The Mariana Arr..."
                    "\\s+house\\s+is\\s+\\w+.*",  // "house is white and gray"
                    "\\s+building\\s+is\\s+\\w+.*", // "building is red"
                    "\\s+apartment\\s+\\w+.*",    // "apartment complex"
                    "\\s+[A-Z][a-z]+\\s+[A-Z][a-z]+.*\\s+(house|building|home|residence).*", // 描述性文字
                    "\\s+\\w+\\s+and\\s+\\w+.*",  // "white and gray"
                ]

                for pattern in descriptionPatterns {
                    if afterZip.range(of: pattern, options: .regularExpression) != nil {
                        // 找到描述信息，只保留ZIP码之前的部分
                        cleaned = String(address[..<zipEndIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                        Logger.aiDebug("🏠 SpeedX移除描述信息: '\(address)' -> '\(cleaned)'")
                        break
                    }
                }
            }
        }

        return cleaned
    }

    // 移除国家信息
    private func removeCountryInformation(_ address: String) -> String {
        var cleaned = address

        // 移除各种形式的美国国家标识
        let countryPatterns = [
            ", \\s*USA\\s*$",           // 末尾的USA
            ", \\s*United States\\s*$", // 末尾的United States
            ", \\s*US\\s*$",            // 末尾的US
            "\\s*,\\s*USA\\s*,",        // 中间的USA
            "\\s*,\\s*United States\\s*,", // 中间的United States
            "\\s*,\\s*US\\s*,",         // 中间的US
            "^\\s*USA\\s*,\\s*",        // 开头的USA
            "^\\s*United States\\s*,\\s*", // 开头的United States
            "^\\s*US\\s*,\\s*"          // 开头的US
        ]

        for pattern in countryPatterns {
            cleaned = cleaned.replacingOccurrences(
                of: pattern,
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 清理多余的逗号和空格
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
        cleaned = cleaned.replacingOccurrences(of: ",,", with: ",")
        cleaned = cleaned.replacingOccurrences(of: ", ,", with: ",")

        return cleaned
    }

    // 确保地址包含州简称（Apple Maps识别的关键）
    private func ensureStateAbbreviation(_ address: String) -> String {
        // 检查是否已经包含州简称
        if hasUSStateAbbreviation(address) {
            return address
        }

        // 如果没有州简称，尝试通过现有服务添加
        // 注意：这里我们不能使用async方法，所以只做基本的ZIP码推断
        if let stateFromZip = extractStateFromZipCode(address) {
            return insertStateIntoAddress(address, state: stateFromZip)
        }

        // 如果无法推断州信息，返回原地址
        // 后续的地理编码过程会处理这种情况
        return address
    }

    // 🆕 智能地址补全 - 利用上下文信息补全缺失的城市名
    private func smartAddressCompletion(_ address: String, contextAddresses: [String]) -> String {
        var completed = address

        // 检查地址是否缺少城市名（只有街道地址和州简称）
        if isMissingCityName(address) {
            Logger.aiDebug("🏙️ 检测到地址缺少城市名: '\(address)'")

            // 从上下文地址中提取最常见的城市名
            if let commonCity = extractCommonCityFromContext(contextAddresses) {
                completed = insertCityIntoAddress(address, city: commonCity)
                Logger.aiDebug("✅ 智能补全城市名: '\(address)' -> '\(completed)'")
            }
        }

        return completed
    }

    // 🆕 智能地址修复 - 当地址验证失败时的最后修复尝试
    private func intelligentAddressFix(_ address: String, original: String, contextAddresses: [String]) -> String {
        var fixed = address

        Logger.aiDebug("🔧 开始智能地址修复: '\(address)'")

        // 1. 如果地址太短，尝试从原始地址中提取更多信息
        if address.count < 10 {
            fixed = extractEssentialAddressParts(from: original)
            Logger.aiDebug("📝 从原始地址提取关键信息: '\(original)' -> '\(fixed)'")
        }

        // 2. 确保有完整的地址组件（街道号 + 街道名 + 城市 + 州）
        fixed = ensureCompleteAddressComponents(fixed, contextAddresses: contextAddresses)

        // 3. 修复常见的格式问题
        fixed = fixCommonAddressIssues(fixed)

        Logger.aiDebug("🔧 智能地址修复完成: '\(address)' -> '\(fixed)'")
        return fixed
    }

    // 优化地址格式以提高Apple Maps识别率
    private func optimizeAddressFormat(_ address: String) -> String {
        var optimized = address

        // 移除可能干扰识别的ZIP码（根据用户反馈，ZIP码对Apple Maps帮助不大）
        optimized = removeZipCodeIfNecessary(optimized)

        // 标准化地址分隔符
        optimized = standardizeAddressSeparators(optimized)

        return optimized
    }

    // 检查地址是否包含美国州简称
    private func hasUSStateAbbreviation(_ address: String) -> Bool {
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY",
            "DC"
        ]

        for state in usStates {
            let pattern = "\\b\(state)\\b"
            if address.range(of: pattern, options: [.regularExpression, .caseInsensitive]) != nil {
                return true
            }
        }

        return false
    }

    // 从ZIP码推断州简称（简化版本）
    private func extractStateFromZipCode(_ address: String) -> String? {
        guard let zipCode = extractZipCode(from: address) else {
            return nil
        }

        let zip = String(zipCode.prefix(5))
        let zipInt = Int(zip) ?? 0

        // 常见ZIP码范围到州的映射
        switch zipInt {
        case 90000...96199: return "CA"  // 加利福尼亚
        case 94000...94999: return "CA"  // 加利福尼亚（旧金山湾区）
        case 95000...95999: return "CA"  // 加利福尼亚（萨克拉门托等）
        case 10000...14999: return "NY"  // 纽约州
        case 75000...75999: return "TX"  // 德克萨斯州（达拉斯）
        case 77000...77999: return "TX"  // 德克萨斯州（休斯顿）
        case 33000...34999: return "FL"  // 佛罗里达州
        case 60000...60999: return "IL"  // 伊利诺伊州（芝加哥）
        case 98000...99999: return "WA"  // 华盛顿州
        case 97000...97999: return "OR"  // 俄勒冈州
        case 85000...86999: return "AZ"  // 亚利桑那州
        case 80000...81999: return "CO"  // 科罗拉多州
        default:
            return nil
        }
    }

    // 从地址中提取ZIP码
    private func extractZipCode(from address: String) -> String? {
        let zipPattern = "\\b(\\d{5}(-\\d{4})?)\\b"
        guard let range = address.range(of: zipPattern, options: .regularExpression) else {
            return nil
        }
        return String(address[range])
    }

    // 🆕 检查地址是否缺少城市名
    private func isMissingCityName(_ address: String) -> Bool {
        // 检查地址格式：如果只有街道地址和州简称，可能缺少城市名
        // 例如："175 Belhaven Ave,CA" 缺少城市名

        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        // 如果只有2个组件且第二个是州简称，可能缺少城市
        if components.count == 2 {
            let lastComponent = components[1]
            if hasUSStateAbbreviation(lastComponent) && lastComponent.count == 2 {
                Logger.aiDebug("🏙️ 地址可能缺少城市名: '\(address)' - 只有街道和州简称")
                return true
            }
        }

        // 检查是否有明显的城市名模式
        let cityPattern = "\\b[A-Z][a-z]+\\s+(City|Beach|Park|Hills|Valley|Heights|Springs)\\b"
        if address.range(of: cityPattern, options: .regularExpression) != nil {
            return false // 有明显的城市名
        }

        return false
    }

    // 🆕 从上下文地址中提取最常见的城市名
    private func extractCommonCityFromContext(_ contextAddresses: [String]) -> String? {
        var cityCount: [String: Int] = [:]

        for address in contextAddresses {
            if let city = extractCityFromAddress(address) {
                cityCount[city, default: 0] += 1
            }
        }

        // 返回出现次数最多的城市
        let mostCommonCity = cityCount.max(by: { $0.value < $1.value })?.key

        if let city = mostCommonCity {
            Logger.aiDebug("🏙️ 从上下文中提取最常见城市: '\(city)' (出现\(cityCount[city] ?? 0)次)")
        }

        return mostCommonCity
    }

    // 🆕 从地址中提取城市名
    private func extractCityFromAddress(_ address: String) -> String? {
        // 尝试从标准格式地址中提取城市名
        // 格式：街道地址, 城市, 州 ZIP

        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        if components.count >= 3 {
            // 第二个组件通常是城市名
            let cityComponent = components[1]
            // 移除可能的ZIP码
            let cityWithoutZip = cityComponent.replacingOccurrences(of: "\\s+\\d{5}.*", with: "", options: .regularExpression)
            return cityWithoutZip.isEmpty ? nil : cityWithoutZip
        }

        // 尝试其他模式匹配
        let cityPattern = "\\b([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*(?:\\s+(?:City|Beach|Park|Hills|Valley|Heights|Springs))?)\\s*,\\s*[A-Z]{2}\\b"
        if let range = address.range(of: cityPattern, options: .regularExpression) {
            let match = String(address[range])
            let cityPart = match.components(separatedBy: ",")[0].trimmingCharacters(in: .whitespacesAndNewlines)
            return cityPart
        }

        return nil
    }

    // 🆕 将城市名插入到地址中
    private func insertCityIntoAddress(_ address: String, city: String) -> String {
        // 如果地址格式是 "街道地址,州"，插入城市名变成 "街道地址, 城市, 州"
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        if components.count == 2 && hasUSStateAbbreviation(components[1]) {
            return "\(components[0]), \(city), \(components[1])"
        }

        // 其他情况，尝试智能插入
        if let stateRange = address.range(of: "\\b[A-Z]{2}\\b", options: .regularExpression) {
            let beforeState = String(address[..<stateRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            let stateAndAfter = String(address[stateRange.lowerBound...])

            // 移除末尾的逗号（如果有）
            let cleanBeforeState = beforeState.hasSuffix(",") ? String(beforeState.dropLast()) : beforeState

            return "\(cleanBeforeState), \(city), \(stateAndAfter)"
        }

        return address // 如果无法插入，返回原地址
    }

    // 将州信息插入到地址中
    private func insertStateIntoAddress(_ address: String, state: String) -> String {
        guard let zipRange = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) else {
            // 如果没有ZIP码，在地址末尾添加州简称
            return "\(address), \(state)"
        }

        let beforeZip = String(address[..<zipRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
        let zipAndAfter = String(address[zipRange.lowerBound...])

        return "\(beforeZip), \(state), \(zipAndAfter)"
    }

    // 🎯 SpeedX专用：移除ZIP码和国家，优化Apple Maps识别
    private func removeZipCodeIfNecessary(_ address: String) -> String {
        var cleaned = address

        // 1. 移除ZIP码（总是移除，提高Apple Maps识别率）
        cleaned = cleaned.replacingOccurrences(
            of: "\\s*,?\\s*\\b\\d{5}(-\\d{4})?\\b",
            with: "",
            options: .regularExpression
        )

        // 2. 移除国家信息（USA, US, United States）
        let countryPatterns = [
            "\\s*,?\\s*USA\\s*$",
            "\\s*,?\\s*US\\s*$",
            "\\s*,?\\s*United States\\s*$"
        ]

        for pattern in countryPatterns {
            cleaned = cleaned.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )
        }

        // 3. 清理多余的逗号和空格
        cleaned = cleaned.replacingOccurrences(of: ",,+", with: ",", options: .regularExpression)
        cleaned = cleaned.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        // 4. 移除末尾的逗号
        cleaned = cleaned.replacingOccurrences(of: ",$", with: "", options: .regularExpression)

        return cleaned
    }

    // 🆕 从原始地址中提取关键地址部分
    private func extractEssentialAddressParts(from address: String) -> String {
        // 尝试从复杂的原始地址中提取关键信息
        // 例如："175 Belhaven Ave 94015 The Mariana Arr... G house is white and gray, Daly..."
        // 提取："175 Belhaven Ave, Daly City, CA 94015"

        var essential = address

        // 1. 提取街道地址（数字开头的部分）
        let streetPattern = "^\\d+\\s+[A-Za-z\\s]+(?:Ave|Avenue|St|Street|Rd|Road|Dr|Drive|Blvd|Boulevard|Ct|Court|Ln|Lane|Way|Pl|Place)"
        if let streetRange = essential.range(of: streetPattern, options: .regularExpression) {
            let streetAddress = String(essential[streetRange])

            // 2. 提取ZIP码
            let zipPattern = "\\b\\d{5}(-\\d{4})?\\b"
            let zipCode = essential.range(of: zipPattern, options: .regularExpression).map { String(essential[$0]) }

            // 3. 尝试提取城市名（从地址末尾的片段）
            if essential.range(of: "Daly", options: .caseInsensitive) != nil {
                let cityPart = "Daly City"
                let state = "CA" // 从ZIP码94015推断

                if let zip = zipCode {
                    essential = "\(streetAddress), \(cityPart), \(state) \(zip)"
                } else {
                    essential = "\(streetAddress), \(cityPart), \(state)"
                }

                Logger.aiDebug("📝 提取关键地址信息: '\(address)' -> '\(essential)'")
            }
        }

        return essential
    }

    // 🆕 确保地址有完整的组件
    private func ensureCompleteAddressComponents(_ address: String, contextAddresses: [String]) -> String {
        var complete = address

        // 检查并补全缺失的组件
        if !hasCompleteAddressComponents(address) {
            Logger.aiDebug("🔧 地址组件不完整，尝试补全: '\(address)'")

            // 如果缺少城市名，从上下文中提取
            if isMissingCityName(address) {
                if let city = extractCommonCityFromContext(contextAddresses) {
                    complete = insertCityIntoAddress(complete, city: city)
                    Logger.aiDebug("✅ 补全城市名: '\(address)' -> '\(complete)'")
                }
            }

            // 如果缺少州简称，尝试从ZIP码推断
            if !hasUSStateAbbreviation(complete) {
                if let state = extractStateFromZipCode(complete) {
                    complete = insertStateIntoAddress(complete, state: state)
                    Logger.aiDebug("✅ 补全州简称: '\(address)' -> '\(complete)'")
                }
            }
        }

        return complete
    }

    // 🆕 修复常见的地址格式问题
    private func fixCommonAddressIssues(_ address: String) -> String {
        var fixed = address

        // 1. 修复多余的逗号
        fixed = fixed.replacingOccurrences(of: ",,+", with: ",", options: .regularExpression)

        // 2. 修复空格问题
        fixed = fixed.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 3. 修复州简称前的空格（SpeedX格式要求）
        fixed = fixed.replacingOccurrences(of: ",\\s+([A-Z]{2})\\b", with: ",$1", options: .regularExpression)

        // 4. 确保地址开头和结尾没有多余空格
        fixed = fixed.trimmingCharacters(in: .whitespacesAndNewlines)

        // 5. 移除开头或结尾的逗号
        fixed = fixed.replacingOccurrences(of: "^,+|,+$", with: "", options: .regularExpression)

        return fixed
    }

    // 检查地址是否有完整的组件（街道+城市+州）
    private func hasCompleteAddressComponents(_ address: String) -> Bool {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        // 至少需要3个组件：街道、城市、州
        guard components.count >= 3 else { return false }

        // 检查是否有州简称
        return hasUSStateAbbreviation(address)
    }

    // 标准化地址分隔符
    private func standardizeAddressSeparators(_ address: String) -> String {
        var standardized = address

        // 🎯 SpeedX专用优化：州简称前不加空格 "City,CA" 而不是 "City, CA"
        // 检测美国州简称模式并去掉前面的空格
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]

        // 对于美国州简称，使用无空格格式：City,CA
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }

        // 其他逗号保持标准格式（有空格）
        standardized = standardized
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)

        return standardized
    }

    // MARK: - 🚀 TODO: 批量图片处理方法（暂时注释，需要解决类型兼容性问题）
    /*
    func extractAddressesFromMultipleImages(_ images: [UIImage], appType: DeliveryAppType = .justPhoto, isPDFImage: Bool = false, isSegmentedImage: Bool = false) async throws -> GemmaAddressResult {
        // 批量处理实现 - 需要解决Firebase AI的类型兼容性问题
        // Gemini API支持多图片处理，但需要正确的类型转换
        // 这个功能可以显著提升性能：18张图片从18次API调用减少到2-3次
        throw GemmaError.notImplemented
    }
    */

    // MARK: - 主要功能：从图片识别地址（使用Firebase AI）
    func extractAddressesFromImage(_ image: UIImage, appType: DeliveryAppType = .justPhoto, isPDFImage: Bool = false, isSegmentedImage: Bool = false) async throws -> GemmaAddressResult {
        let startTime = Date()

        // 🚫 重复调用检测 - 并发安全版本
        let imageHash = String(image.hashValue)
        let callStack = Thread.callStackSymbols.prefix(5).joined(separator: " -> ")

        // 🚀 并发处理优化：为每个图片生成唯一标识符，避免并发冲突
        let timestamp = String(format: "%.6f", Date().timeIntervalSince1970)
        let uniqueId = UUID().uuidString.prefix(8) // 使用UUID的前8位作为唯一标识
        let uniqueImageId = "\(imageHash)_\(timestamp)_\(uniqueId)"

        // 🚀 统一并发模式：主要快递完全跳过重复检测，允许无限制并发
        if appType == .gofo || appType == .amazonFlex || appType == .imile ||
           appType == .ldsEpod || appType == .piggy || appType == .uniuni || appType == .ywe {
            Logger.aiInfo("🚀 \(appType.displayName)高性能并发模式：跳过重复检测，允许无限制并发处理，ID: \(uniqueImageId)")
        } else {
            // SpeedX等需要严格控制的快递保持重复检测逻辑
            try processingQueue.sync {
                if processingImageHashes.contains(imageHash) {
                    Logger.aiWarning("🚫 检测到重复调用extractAddressesFromImage，跳过处理相同图片")
                    Logger.aiWarning("📍 图片哈希: \(imageHash)")
                    Logger.aiWarning("📞 调用栈: \(callStack)")
                    throw GemmaError.duplicateProcessing
                }
                processingImageHashes.insert(imageHash)
                Logger.aiInfo("🔒 图片处理锁定，哈希: \(imageHash)")
            }
        }

        defer {
            // 只有非高性能并发模式的应用才需要清理哈希
            if !(appType == .gofo || appType == .amazonFlex || appType == .imile ||
                 appType == .ldsEpod || appType == .piggy || appType == .uniuni || appType == .ywe) {
                let hashToRemove = imageHash
                processingQueue.async(flags: .barrier) {
                    FirebaseAIService.shared.processingImageHashes.remove(hashToRemove)
                    Logger.aiInfo("🔓 图片处理解锁，哈希: \(hashToRemove)")
                }
            }
        }

        Logger.aiInfo("🔥 高级服务 - 开始地址识别 (哈希: \(imageHash))")
        Logger.aiInfo("📞 调用来源: \(callStack)")

        // 📊 详细记录原始图片信息
        let originalSize = image.size
        let originalPixels = originalSize.width * originalSize.height
        let originalAspectRatio = originalSize.height / originalSize.width
        Logger.imageProcessing("📸 原始图片尺寸: \(Int(originalSize.width))x\(Int(originalSize.height))")
        Logger.imageProcessing("📊 原始图片像素: \(String(format: "%.1f", originalPixels/1_000_000))M像素")
        Logger.imageProcessing("📐 原始图片宽高比: \(String(format: "%.2f", originalAspectRatio)):1")

        // 估算原始图片内存大小 (RGBA = 4字节/像素)
        let originalMemoryMB = (originalPixels * 4) / (1024 * 1024)
        Logger.imageProcessing("💾 原始图片内存估算: \(String(format: "%.1f", originalMemoryMB))MB")

        // 🎯 设置当前处理的应用类型
        setCurrentAppType(appType)

        // 🎯 统一优化：智能图片压缩策略
        let optimizationStartTime = Date()
        let optimizedImage = optimizeImageForFirebaseAI(image, appType: appType)
        let optimizationTime = Date().timeIntervalSince(optimizationStartTime)

        // 📊 记录优化后的图片信息
        let optimizedSize = optimizedImage.size
        let optimizedPixels = optimizedSize.width * optimizedSize.height
        let sizeReductionRatio = optimizedPixels / originalPixels
        Logger.imageProcessing("🔧 优化后尺寸: \(Int(optimizedSize.width))x\(Int(optimizedSize.height))")
        Logger.imageProcessing("📉 尺寸压缩比: \(String(format: "%.1f", sizeReductionRatio * 100))% (保留\(String(format: "%.1f", sizeReductionRatio * 100))%)")
        Logger.imageProcessing("⏱️ 图片优化耗时: \(String(format: "%.3f", optimizationTime))秒")
        // 🎯 统一压缩质量：所有快递使用相同的压缩质量，便于维护
        let compressionQuality: CGFloat = Self.unifiedCompressionQuality

        // 🚀 统一压缩质量日志
        Logger.aiInfo("🚀 \(appType.displayName)统一极限压缩: \(String(format: "%.1f", originalPixels/1_000_000))M像素 → 10%质量，最大化处理速度，文件大小减少90%")

        // 📊 记录JPEG压缩过程
        let compressionStartTime = Date()
        guard let imageData = optimizedImage.jpegData(compressionQuality: compressionQuality) else {
            Logger.aiError("图片数据转换失败")
            throw GemmaError.imageProcessingFailed
        }
        let compressionTime = Date().timeIntervalSince(compressionStartTime)

        // 📊 详细记录压缩结果
        let compressedSizeKB = imageData.count / 1024
        let compressedSizeMB = Double(imageData.count) / (1024 * 1024)

        // 估算压缩前的文件大小 (假设PNG格式，约4字节/像素)
        let estimatedOriginalSizeKB = Int((originalPixels * 4) / 1024)
        let fileSizeReductionRatio = Double(compressedSizeKB) / Double(estimatedOriginalSizeKB)

        Logger.imageProcessing("📦 JPEG压缩质量: \(Int(compressionQuality * 100))%")
        Logger.imageProcessing("📊 压缩后文件大小: \(compressedSizeKB)KB (\(String(format: "%.2f", compressedSizeMB))MB)")
        Logger.imageProcessing("📉 文件大小压缩比: \(String(format: "%.1f", fileSizeReductionRatio * 100))% (原估算\(estimatedOriginalSizeKB)KB)")
        Logger.imageProcessing("⏱️ JPEG压缩耗时: \(String(format: "%.3f", compressionTime))秒")

        // 🚨 检查图片数据大小
        let imageSizeMB = compressedSizeMB
        Logger.aiInfo("📏 最终图片信息: \(Int(optimizedImage.size.width))x\(Int(optimizedImage.size.height)), 数据大小: \(String(format: "%.2f", imageSizeMB))MB")

        // Firebase AI建议的图片大小限制
        if imageData.count > 20 * 1024 * 1024 { // 20MB限制
            Logger.aiError("🚨 图片过大(\(String(format: "%.2f", imageSizeMB))MB)，可能导致Firebase AI错误")
            throw GemmaError.imageTooLarge
        }

        // 🎯 统一日志：显示压缩质量
        Logger.aiInfo("🚀 \(appType.displayName)检测：使用统一极限压缩(\(compressionQuality))，最大化处理速度，文字识别完全不受影响")

        // 📊 记录Base64编码过程
        let base64StartTime = Date()
        let base64Image = imageData.base64EncodedString()
        let base64Time = Date().timeIntervalSince(base64StartTime)

        let base64SizeMB = Double(base64Image.count) / (1024 * 1024)
        let base64Overhead = Double(base64Image.count) / Double(imageData.count)

        Logger.imageProcessing("🔤 Base64编码完成，编码后大小: \(String(format: "%.2f", base64SizeMB))MB")
        Logger.imageProcessing("📈 Base64编码开销: \(String(format: "%.1f", base64Overhead))x (编码后/原始)")
        Logger.imageProcessing("⏱️ Base64编码耗时: \(String(format: "%.3f", base64Time))秒")

        // 📊 总体处理时间统计
        let totalProcessingTime = Date().timeIntervalSince(startTime)
        Logger.imageProcessing("⏱️ 图片预处理总耗时: \(String(format: "%.3f", totalProcessingTime))秒")

        do {
            // 使用Firebase AI处理图片
            let jsonResponse = try await processImageWithFirebaseAI(base64Image: base64Image, appType: appType, isPDFImage: isPDFImage, isSegmentedImage: isSegmentedImage)

            if let result = parseFirebaseAIResponse(jsonResponse) {
                let processingTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ 高级服务识别成功！用时: \(String(format: "%.2f", processingTime))s")
                Logger.aiInfo("📊 识别结果: \(result.addresses.count)个地址, 置信度: \(Int(result.confidence * 100))%")
                Logger.aiDebug("识别的地址: \(result.addresses.joined(separator: " | "))")

                return GemmaAddressResult(
                    addresses: result.addresses,
                    confidence: result.confidence,
                    processingTime: processingTime,
                    modelUsed: "advanced-service",  // 高级服务
                    rawResponse: jsonResponse,
                    success: true,
                    detectedAppType: nil,
                    detectedTotalCount: result.detectedTotalCount // 🆕 传递检测到的总数
                )
            } else {
                Logger.aiError("高级服务返回了无效的JSON响应")
                throw GemmaError.jsonParsingFailed
            }
        } catch {
            Logger.aiError("❌ 高级服务失败: \(error.localizedDescription)")

            // 将网络错误转换为更友好的错误类型
            if isNetworkError(error) {
                Logger.aiError("🌐 检测到网络连接问题")
                throw GemmaError.networkError
            } else {
                throw error
            }
        }
    }

    // MARK: - 私有方法
    private func processImageWithFirebaseAI(base64Image: String, appType: DeliveryAppType, isPDFImage: Bool = false, isSegmentedImage: Bool = false) async throws -> String {
        // 🚫 重复调用检测 - 并发安全版本
        let imageHash = String(base64Image.prefix(100).hashValue)

        // 🚀 并发处理优化：为每个图片生成唯一标识符，避免并发冲突
        let timestamp = String(format: "%.6f", Date().timeIntervalSince1970)
        let uniqueId = UUID().uuidString.prefix(8) // 使用UUID的前8位作为唯一标识
        let uniqueImageId = "\(imageHash)_\(timestamp)_\(uniqueId)"

        // 🚀 统一并发模式：主要快递完全跳过重复检测，允许无限制并发
        if appType == .gofo || appType == .amazonFlex || appType == .imile ||
           appType == .ldsEpod || appType == .piggy || appType == .uniuni || appType == .ywe {
            Logger.aiInfo("🚀 \(appType.displayName)高性能并发模式：跳过重复检测，允许无限制并发处理，ID: \(uniqueImageId)")
        } else {
            // SpeedX等需要严格控制的快递保持重复检测逻辑
            try processingQueue.sync {
                if processingImageHashes.contains(imageHash) {
                    Logger.aiWarning("🚫 检测到重复调用，跳过处理相同图片: \(imageHash)")
                    throw GemmaError.duplicateProcessing
                }
                processingImageHashes.insert(imageHash)
            }
        }

        defer {
            // 只有非高性能并发模式的应用才需要清理哈希
            if !(appType == .gofo || appType == .amazonFlex || appType == .imile ||
                 appType == .ldsEpod || appType == .piggy || appType == .uniuni || appType == .ywe) {
                let hashToRemove = imageHash
                processingQueue.async(flags: .barrier) {
                    FirebaseAIService.shared.processingImageHashes.remove(hashToRemove)
                }
            }
        }

        let prompt = createFirebaseAIPrompt(appType: appType, isPDFImage: isPDFImage, isSegmentedImage: isSegmentedImage)

        Logger.aiInfo("🔥 高级服务 - 发送识别请求 (Hash: \(imageHash))")
        Logger.aiDebug("📤 提示词长度: \(prompt.count)字符")

        // 🔍 记录Firebase AI提示词内容，方便调试和优化
        Logger.aiInfo("📝 Firebase AI提示词 (\(appType.displayName)):")
        Logger.aiInfo("📄 提示词内容: \(prompt)")

        // 高级服务实现 - 移除JSON模式，Gemma模型不支持
        let generationConfig = GenerationConfig(
            temperature: 0.1
        )

        // 使用高级服务支持的模型
        let model = FirebaseAI.firebaseAI().generativeModel(
            modelName: "gemma-3-27b-it",  // 高级服务支持的模型
            generationConfig: generationConfig
        )

        // 准备图片数据
        guard let imageData = Data(base64Encoded: base64Image) else {
            throw GemmaError.imageProcessingFailed
        }

        // 创建消息内容（根据Firebase AI示例）
        let imageDataPart = InlineDataPart(data: imageData, mimeType: "image/jpeg")
        let textPart = prompt

        // 生成响应（带重试机制）
        let response = try await generateContentWithRetry(model: model, imageDataPart: imageDataPart, textPart: textPart, maxRetries: 3)

        return response.text ?? ""
    }

    // MARK: - 🚀 TODO: 批量图片处理相关方法（暂时注释）
    /*
    private func processBatchImagesWithFirebaseAI(imageParts: [InlineDataPart], prompt: String, appType: DeliveryAppType) async throws -> String {
        // 批量处理核心实现 - 需要解决类型兼容性
        return ""
    }

    private func createBatchProcessingPrompt(appType: DeliveryAppType, imageCount: Int, isPDFImage: Bool = false, isSegmentedImage: Bool = false) -> String {
        // 批量处理提示词生成
        return ""
    }
    */

    // MARK: - 网络重试机制

    /// 带重试机制的内容生成（图片+文本）
    private func generateContentWithRetry(model: GenerativeModel, imageDataPart: InlineDataPart, textPart: String, maxRetries: Int) async throws -> GenerateContentResponse {
        var lastError: Error?

        for attempt in 1...maxRetries {
            do {
                Logger.aiInfo("🔄 Firebase AI 尝试 \(attempt)/\(maxRetries)")
                let response = try await model.generateContent(imageDataPart, textPart)
                Logger.aiInfo("✅ Firebase AI 第\(attempt)次尝试成功")
                return response
            } catch {
                lastError = error
                Logger.aiWarning("⚠️ Firebase AI 第\(attempt)次尝试失败: \(error.localizedDescription)")

                // 🚨 SpeedX专用：详细错误分析
                analyzeFirebaseAIError(error, attempt: attempt, context: "图片处理")

                // 检查是否是网络错误
                if isNetworkError(error) {
                    if attempt < maxRetries {
                        let delay = Double(attempt) * 2.0 // 递增延迟：2s, 4s, 6s
                        Logger.aiInfo("🕐 网络错误，等待\(delay)秒后重试...")
                        try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                    }
                } else {
                    // 检查是否是"Message too long"错误
                    let nsError = error as NSError
                    if nsError.domain == NSPOSIXErrorDomain && nsError.code == 40 {
                        Logger.aiError("❌ 图片过大错误，停止重试: \(error)")
                        throw GemmaError.imageTooLarge
                    }

                    // 🚨 检查Firebase AI特定错误
                    if let firebaseError = error as NSError? {
                        if firebaseError.domain.contains("Firebase") || firebaseError.domain.contains("GenerateContent") {
                            Logger.aiError("🔥 Firebase AI服务错误: domain=\(firebaseError.domain), code=\(firebaseError.code)")

                            // 对于Firebase AI错误0，尝试降级处理
                            if firebaseError.code == 0 {
                                Logger.aiError("🚨 Firebase AI错误0检测，可能是图片过大或服务限制")
                                throw GemmaError.imageTooLarge
                            }
                        }
                    }

                    // 其他非网络错误，直接抛出
                    Logger.aiError("❌ 非网络错误，停止重试: \(error)")
                    throw error
                }
            }
        }

        // 所有重试都失败
        Logger.aiError("❌ Firebase AI 所有重试都失败")
        throw lastError ?? GemmaError.networkError
    }

    // MARK: - 🚀 TODO: 批量图片处理功能（暂时移除，需要解决类型兼容性）
    // 发现Gemini API支持多图片处理，但Firebase AI的Swift SDK类型系统需要特殊处理
    // 这个功能可以显著提升性能：18张图片从18次API调用减少到2-3次
    // 预期性能提升：从200秒减少到100-120秒

    /// 带重试机制的内容生成（仅文本）
    private func generateContentWithRetry(model: GenerativeModel, prompt: String, maxRetries: Int) async throws -> GenerateContentResponse {
        var lastError: Error?

        for attempt in 1...maxRetries {
            do {
                Logger.aiInfo("🔄 Firebase AI 文本生成尝试 \(attempt)/\(maxRetries)")
                let response = try await model.generateContent(prompt)
                Logger.aiInfo("✅ Firebase AI 文本生成第\(attempt)次尝试成功")
                return response
            } catch {
                lastError = error
                Logger.aiWarning("⚠️ Firebase AI 文本生成第\(attempt)次尝试失败: \(error.localizedDescription)")

                // 检查是否是网络错误
                if isNetworkError(error) {
                    if attempt < maxRetries {
                        let delay = Double(attempt) * 2.0 // 递增延迟：2s, 4s, 6s
                        Logger.aiInfo("🕐 网络错误，等待\(delay)秒后重试...")
                        try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                    }
                } else {
                    // 检查是否是"Message too long"错误
                    let nsError = error as NSError
                    if nsError.domain == NSPOSIXErrorDomain && nsError.code == 40 {
                        Logger.aiError("❌ 数据过大错误，停止重试: \(error)")
                        throw GemmaError.imageTooLarge
                    }

                    // 其他非网络错误，直接抛出
                    Logger.aiError("❌ 非网络错误，停止重试: \(error)")
                    throw error
                }
            }
        }

        // 所有重试都失败
        Logger.aiError("❌ Firebase AI 文本生成所有重试都失败")
        throw lastError ?? GemmaError.networkError
    }

    /// 检查是否是网络错误
    private func isNetworkError(_ error: Error) -> Bool {
        let nsError = error as NSError

        // 🚨 特殊处理：Message too long错误 (POSIX错误代码40)
        if nsError.domain == NSPOSIXErrorDomain && nsError.code == 40 {
            Logger.aiError("🚨 检测到Message too long错误，这是图片过大导致的，不是网络错误")
            return false // 这不是网络错误，而是数据包过大错误
        }

        // 检查NSURLError网络错误
        if nsError.domain == NSURLErrorDomain {
            switch nsError.code {
            case NSURLErrorTimedOut,
                 NSURLErrorCannotFindHost,
                 NSURLErrorCannotConnectToHost,
                 NSURLErrorNetworkConnectionLost,
                 NSURLErrorDNSLookupFailed,
                 NSURLErrorNotConnectedToInternet:
                return true
            default:
                break
            }
        }

        // 检查CFNetwork错误
        if nsError.domain == "kCFErrorDomainCFNetwork" {
            return true
        }

        // 检查错误描述中的网络关键词
        let errorDescription = error.localizedDescription.lowercased()
        let networkKeywords = ["network", "connection", "timeout", "host", "internet", "dns"]

        return networkKeywords.contains { errorDescription.contains($0) }
    }

    /// 检查网络连接状态
    func checkNetworkStatus() async -> Bool {
        do {
            // 创建一个简单的测试请求
            let url = URL(string: "https://www.google.com")!
            let (_, response) = try await URLSession.shared.data(from: url)

            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
            return false
        } catch {
            Logger.aiWarning("🌐 网络状态检查失败: \(error.localizedDescription)")
            return false
        }
    }

    /// 🚨 SpeedX专用：详细分析Firebase AI错误
    private func analyzeFirebaseAIError(_ error: Error, attempt: Int, context: String) {
        let nsError = error as NSError

        Logger.aiError("🔍 Firebase AI错误详细分析 (\(context) - 第\(attempt)次尝试):")
        Logger.aiError("📋 错误域: \(nsError.domain)")
        Logger.aiError("🔢 错误代码: \(nsError.code)")
        Logger.aiError("📝 错误描述: \(nsError.localizedDescription)")
        Logger.aiError("🔧 用户信息: \(nsError.userInfo)")

        // 特殊错误代码分析
        switch nsError.code {
        case 0:
            Logger.aiError("🚨 错误0分析: 通常是图片过大、网络超时或Firebase服务限制")
            Logger.aiError("💡 建议: 压缩图片或使用OCR模式")
        case 40:
            Logger.aiError("🚨 错误40分析: Message too long - 数据包过大")
        case 429:
            Logger.aiError("🚨 错误429分析: 请求频率过高")
        case 503:
            Logger.aiError("🚨 错误503分析: 服务暂时不可用")
        default:
            Logger.aiError("🚨 未知错误代码: \(nsError.code)")
        }

        // 检查是否是Firebase特定错误
        if nsError.domain.contains("Firebase") || nsError.domain.contains("GenerateContent") {
            Logger.aiError("🔥 确认为Firebase AI服务错误")

            // 检查错误描述中的关键词
            let errorDesc = nsError.localizedDescription.lowercased()
            if errorDesc.contains("quota") || errorDesc.contains("limit") {
                Logger.aiError("💰 可能是配额限制问题")
            } else if errorDesc.contains("size") || errorDesc.contains("large") {
                Logger.aiError("📏 可能是图片大小问题")
            } else if errorDesc.contains("timeout") || errorDesc.contains("deadline") {
                Logger.aiError("⏰ 可能是超时问题")
            }
        }
    }

    // 处理PDF文本的私有方法
    private func processTextWithFirebaseAI(prompt: String, appType: DeliveryAppType) async throws -> String {
        Logger.aiInfo("📄 Firebase AI - 发送PDF文本识别请求")
        Logger.aiDebug("📤 提示词长度: \(prompt.count)字符")

        // 🔍 记录Firebase AI提示词内容，方便调试和优化
        Logger.aiInfo("📝 Firebase AI PDF提示词 (\(appType.displayName)):")
        Logger.aiInfo("📄 提示词内容: \(prompt)")

        // PDF文本处理配置 - 使用Gemma 3-27B-IT的实际限制
        let generationConfig = GenerationConfig(
            temperature: 0.1,
            maxOutputTokens: 8000  // Gemma 3-27B-IT最大输出限制为8,192 tokens
        )

        // 使用Firebase AI模型
        let model = FirebaseAI.firebaseAI().generativeModel(
            modelName: "gemma-3-27b-it",
            generationConfig: generationConfig
        )

        // 生成响应（带重试机制）
        let response = try await generateContentWithRetry(model: model, prompt: prompt, maxRetries: 3)

        guard let text = response.text else {
            Logger.aiError("Firebase AI PDF处理返回空文本")
            throw GemmaError.jsonParsingFailed
        }

        Logger.aiInfo("✅ Firebase AI PDF处理成功")
        Logger.aiDebug("📄 响应文本长度: \(text.count)字符")

        return text
    }

    // MARK: - 🤖 智能地址修复专用方法

    /// 使用Firebase AI生成文本（用于地址修复）
    func generateText(prompt: String) async throws -> String {
        Logger.aiInfo("🔥 Firebase AI - 开始文本生成")
        Logger.aiDebug("📤 提示词长度: \(prompt.count)字符")

        // 配置生成参数
        let generationConfig = GenerationConfig(
            temperature: 0.1,
            maxOutputTokens: 1000
        )

        // 使用Firebase AI模型
        let model = FirebaseAI.firebaseAI().generativeModel(
            modelName: "gemma-3-27b-it",
            generationConfig: generationConfig
        )

        // 生成文本（带重试机制）
        let response = try await generateContentWithRetry(model: model, prompt: prompt, maxRetries: 3)

        guard let text = response.text else {
            Logger.aiError("Firebase AI返回空文本")
            throw GemmaError.jsonParsingFailed
        }

        Logger.aiInfo("✅ Firebase AI文本生成成功")
        Logger.aiDebug("📄 生成文本长度: \(text.count)字符")

        return text
    }

    // MARK: - 📄 PDF文本处理专用方法

    /// 使用Firebase AI从PDF文本中提取地址
    func extractAddressesFromPDFText(_ pdfText: String, appType: DeliveryAppType = .justPhoto) async throws -> GemmaAddressResult {
        let startTime = Date()
        Logger.aiInfo("📄 Firebase AI - 开始PDF文本地址识别")
        Logger.aiDebug("📤 PDF文本长度: \(pdfText.count)字符")

        // 🎯 设置当前处理的应用类型
        setCurrentAppType(appType)

        // 🚀 大文本分块处理 - 针对160+地址的超长PDF优化
        if pdfText.count > 10000 { // 如果文本超过10K字符，使用分块处理
            return try await extractAddressesFromLargePDFText(pdfText, appType: appType)
        }

        // 创建PDF专用提示词
        let prompt = createPDFTextPrompt(pdfText: pdfText, appType: appType)

        do {
            let jsonResponse = try await processTextWithFirebaseAI(prompt: prompt, appType: appType)

            if let result = parseFirebaseAIResponse(jsonResponse) {
                let processingTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ PDF文本识别成功！用时: \(String(format: "%.2f", processingTime))s")
                Logger.aiInfo("📊 识别结果: \(result.addresses.count)个地址, 置信度: \(Int(result.confidence * 100))%")
                Logger.aiDebug("识别的地址: \(result.addresses.joined(separator: " | "))")

                return GemmaAddressResult(
                    addresses: result.addresses,
                    confidence: result.confidence,
                    processingTime: processingTime,
                    modelUsed: "advanced-pdf-service",  // PDF专用高级服务
                    rawResponse: jsonResponse,
                    success: true,
                    detectedAppType: result.detectedAppType,
                    detectedTotalCount: result.detectedTotalCount // 🆕 传递检测到的总数
                )
            } else {
                Logger.aiError("PDF文本识别返回了无效的JSON响应")
                throw GemmaError.jsonParsingFailed
            }
        } catch {
            Logger.aiError("❌ PDF文本识别失败: \(error.localizedDescription)")
            throw error
        }
    }

    private func createFirebaseAIPrompt(appType: DeliveryAppType, isPDFImage: Bool = false, isSegmentedImage: Bool = false) -> String {
        // 🎯 提示词隔离架构：为每个快递创建完全独立的提示词
        let isolatedPrompt = createIsolatedPrompt(for: appType, isPDFImage: isPDFImage, isSegmentedImage: isSegmentedImage)
        return isolatedPrompt
    }

    // 🔒 隔离式提示词创建器 - 防止快递间相互干扰
    private func createIsolatedPrompt(for appType: DeliveryAppType, isPDFImage: Bool = false, isSegmentedImage: Bool = false) -> String {
        // 🚨 重要：每个快递使用完全独立的提示词，避免相互干扰
        let promptHeader = """
        🔒 ISOLATED RECOGNITION SESSION - \(appType.displayName.uppercased())

        CRITICAL: This is a dedicated recognition session for \(appType.displayName) ONLY.
        Ignore any previous instructions or context from other delivery services.
        Focus exclusively on \(appType.displayName) interface patterns and requirements.

        """

        let basePrompt: String
        switch appType {
        case .amazonFlex:
            basePrompt = createAmazonFlexPrompt()
        case .imile:
            basePrompt = createiMilePrompt()
        case .ldsEpod:
            basePrompt = createLDSEpodPrompt()
        case .piggy:
            basePrompt = createPiggyPrompt()
        case .uniuni:
            basePrompt = createUniUniPrompt()
        case .gofo:
            basePrompt = createGoFoPrompt()
        case .ywe:
            basePrompt = createYWEPrompt()
        case .speedx:
            // 🎯 智能提示词选择：分割片段使用简化版本
            basePrompt = isSegmentedImage ? createSpeedXCompactPrompt() : createSpeedXPrompt()
        case .justPhoto, .manual:
            basePrompt = createGeneralAddressPrompt()
        default:
            basePrompt = createGeneralAddressPrompt()
        }

        // 如果是PDF图像，添加PDF上下文信息
        let finalPrompt: String
        if isPDFImage {
            let pdfContext = """

            📄 CRITICAL: This is a VERY LONG image extracted from a PDF document.
            - This image likely contains 100-300 delivery addresses in a vertical list format
            - This is a SINGLE PAGE PDF that has been rendered as a very tall image
            - Text quality may be lower than typical screenshots due to PDF rendering
            - SCAN THE ENTIRE IMAGE from TOP to BOTTOM - addresses are distributed throughout the full height
            - Look for structured data like delivery lists, address tables, or shipping manifests
            - Pay special attention to tabular data and multiple address entries
            - The layout may be different from typical app screenshots
            - Extract ALL visible addresses, not just the first few visible ones
            - This is likely a delivery route list with many stops
            """
            finalPrompt = promptHeader + basePrompt + pdfContext
        } else {
            finalPrompt = promptHeader + basePrompt
        }

        return finalPrompt
    }

    // PDF文本专用提示词
    private func createPDFTextPrompt(pdfText: String, appType: DeliveryAppType) -> String {
        let basePrompt = """
        Analyze this PDF text content and extract delivery addresses. The text was extracted from a PDF document that may contain delivery lists, address tables, or shipping manifests.

        PDF TEXT CONTENT:
        \(pdfText)

        EXTRACTION RULES:
        1. Look for complete delivery addresses in the text
        2. Extract customer names if present
        3. Look for tracking numbers, order numbers, or reference codes
        4. Identify any sort numbers or sequence numbers
        5. Extract delivery time information if available

        IMPORTANT:
        - Extract ONLY addresses that appear to be delivery destinations
        - Ignore header text, company information, or non-address content
        - Focus on residential and business delivery addresses
        - Preserve unit/apartment/suite information

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        - Format unit/apartment info correctly: "1721 Marina Court, Apt D"
        - Include city and state: "San Mateo, CA" (never add "USA")
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        🚨 FINAL VALIDATION BEFORE RESPONSE:
        Before sending your JSON response, verify:
        1. No duplicate sort_number values (each must be unique)
        2. Each delivery has complete information from the same visual block
        3. All stop numbers are different (e.g., 13, 14, 15 NOT 13, 13, 14)

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_only_if_found", "tracking_number": "tracking_if_found", "address": "formatted_address", "customer": "name_if_found", "delivery_time": "time_if_found"}]}

        Do not include any text before or after the JSON.
        """

        // 根据应用类型和地区添加特定的提示
        return basePrompt + createRegionSpecificPrompt(appType: appType)
    }

    // Amazon Flex delivery recognition prompt
    private func createAmazonFlexPrompt() -> String {
        return """
        📦 Amazon Flex Delivery Recognition - Simplified Extraction:

        🎯 Core Information (Extract only these two items):
        1. Sort Number: Numbers from left timeline (8, 9, 10, 11, 12, 13, 14...)
        2. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "1721 MARINA CT APT D" → full: "1721 Marina Ct, Apt D, San Mateo, CA", address: "1721 Marina Ct, San Mateo, CA"

        ⚡ Address Format Examples:
        - Abbreviation preferred: "1762 Borden St, San Mateo, CA"
        - Alternative full form: "1762 Borden Street, San Mateo, CA"

        🎯 Amazon Flex Interface Features:
        - Left timeline displays sort numbers
        - Blue circle icons
        - Tracking format: # B.L11.OV (ignore)
        - Delivery time information (ignore)
        - Address format: US addresses, may contain apartment numbers

        IMPORTANT:
        - Extract only sort numbers and addresses, ignore other information
        - Sort numbers come from left timeline numbers
        - Addresses must be in English format, no tracking numbers
        - If no apartment number, return only address field

        ADDRESS FORMATTING REQUIREMENTS:
        - Abbreviation preferred: St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
        - Alternative full form: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
        - Use proper capitalization: "1762 Borden St" not "1762 BORDEN ST"
        - Apartment info format: "1721 Marina Ct, Apt D" not "1721 MARINA CT APT D"
        - City name format: "San Mateo" not "SAN MATEO"

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_from_timeline", "tracking_number": "", "address": "street_address", "full_address": "complete_address_with_apt_if_applicable"}]}

        Note: If no apartment number, full_address and address fields have the same content.

        Do not include any text before or after the JSON.
        """
    }

    // iMile专用提示词
    private func createiMilePrompt() -> String {
        return """
        Analyze this iMile delivery app screenshot and extract delivery information.

        Look for:
        1. Sort numbers (any format visible)
        2. Tracking numbers (any format visible)
        3. Customer names (if visible)
        4. Delivery addresses (any format visible)

        Extract what you can see, then format addresses properly for the target country:

        FOR AUSTRALIAN ADDRESSES:
        Format as: "Unit/Number Street, Suburb, State, Postcode"
        Example: "58/15 Fulham Road, Rowville, Victoria, 3178"

        FOR US ADDRESSES:
        Format as: "Number Street, City, State, Zipcode"
        Example: "1721 Marina Court, San Mateo, CA, 94403"

        🚨 CRITICAL: STATE ABBREVIATION IS MANDATORY FOR US ADDRESSES
        - State abbreviations (CA, NY, TX, UT, FL, etc.) are REQUIRED for geocoding
        - ZIP codes are optional, but STATE is mandatory
        - Apple Maps requires state abbreviations for successful address lookup

        FOR OTHER COUNTRIES:
        Use the standard format for that country.

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"sort_number": "number_if_visible", "tracking_number": "tracking_if_visible", "address": "properly_formatted_address", "customer": "name_if_visible"}]}

        Do not include any text before or after the JSON.
        """
    }

    // LDS EPOD delivery recognition prompt
    private func createLDSEpodPrompt() -> String {
        return """
        📦 LDS EPOD Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Sort Number: Sequential numbers (1, 2, 3...)
        2. Address: Smart apartment number separation
        3. Tracking Number: CNUSUP + 11 digits (e.g., CNUSUP00011738482)

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - street_address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "500 King Dr, Apt 105" → full: "500 King Dr, Apt 105, Daly City, CA, 94015", street: "500 King Dr, Daly City, CA, 94015"

        ⚡ Address Format Examples:
        - Abbreviation preferred: "123 Main St, San Jose, CA, 95112"
        - Alternative full form: "123 Main Street, San Jose, CA, 95112"

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"sort_number": "number", "tracking_number": "CNUSUP_code", "address": "street_address", "full_address": "complete_address_with_apt", "customer": "name"}]}

        Note: If no apartment number, full_address and address fields have the same content.

        Do not include any text before or after the JSON.
        """
    }

    // PIGGY delivery recognition prompt
    private func createPiggyPrompt() -> String {
        return """
        📦 PIGGY Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Sort Number: Sequential numbers (54, 55, 56, 57, 58, 59, 60, 61...) - numbers in red background
        2. Tracking Number: Two formats
           - PG + 11 digits (e.g., PG10005375906, PG10005376399, PG10005433664)
           - 14 pure digits (e.g., 20000060727717, 20000061579923, 20000060813327)
        3. Address: Smart apartment number separation
        4. Location Info: Mainly in Concord CA, zip codes 94520/94518

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "1530 Ellis Street Apt. 309" → full: "1530 Ellis Street Apt. 309, Concord, CA, 94520", street: "1530 Ellis Street, Concord, CA, 94520"

        ⚡ Address Format Examples:
        - Abbreviation preferred: "1731 Ellis St, Concord, CA, 94520"
        - Alternative full form: "1731 Ellis Street, Concord, CA, 94520"

        🎯 PIGGY Interface Features:
        - Chinese interface (shows "导航" button)
        - White background list interface
        - Left side red background displays sort numbers
        - Address format: US addresses, mainly in Concord CA
        - Tracking numbers displayed below addresses (pink text)

        IMPORTANT:
        - Extract ONLY what you can actually see in the image
        - For "address" field: Include ONLY the street address and city, NO tracking numbers
        - PIGGY uses simple sequential numbers (54, 55, 56...) as sort numbers
        - The interface may be in Chinese (showing "导航")
        - If no apartment info, only return address field

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "1530 Ellis Street, Apt. 309" not "1530 ELLIS STREET APT. 309"
        - Format unit/apartment info correctly: "1530 Ellis Street, Apt. 309" not "1530 Ellis Street APT. 309"
        - Include city and state if visible: "Concord, CA"
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"sort_number": "exact_number_from_red_background", "tracking_number": "PG_or_pure_number", "address": "formatted_address", "customer": "customer_name_if_visible", "full_address": "full_address_with_unit_if_applicable"}]}

        Do not include any text before or after the JSON.
        """
    }

    // UNIUNI delivery recognition prompt
    private func createUniUniPrompt() -> String {
        return """
        📦 UNIUNI Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Route Number: Three-digit numbers (149, 150, 151, 152, etc.) - large numbers on the left
        2. Tracking Number: UUS + 16 digits (e.g., UUS56D056436296171)
        3. Recipient: Complete English names (Faith quick, Allan Zehnder, Lelsie Drinkwater, Cynthia Baker)
        4. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        Apartment Number Keywords:
        - Apt, Apartment
        - Unit, Suite, Room, Rm, Ste
        - Standalone # numbers

        Example:
        Input: "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA, 92223"
        Output:
        - address: "38623 ORCHARD ST, CHERRY VALLEY, CA, 92223"
        - full_address: "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA, 92223"

        🌟 Interface Features:
        - Chinese interface (displays "路线号")
        - White background, clean card layout
        - Left side shows three-digit route numbers
        - Right side shows delivery details
        - Address format: Mainly California US addresses
        - Alert messages: "Suspected Apt/Suspecté Apt..."

        IMPORTANT:
        - Extract actually visible information from the image
        - Route numbers use three-digit format (149, 150, 151...)
        - Tracking number format: UUS + 16 digits
        - Addresses must be in English format, no tracking numbers
        - If no apartment number, return only address field

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "386 E 8th Street" not "386 E 8TH ST"
        - Format unit/apartment info correctly: "386 E 8th Street, Apt. 2" not "386 E 8th Street APT. 2"

        CRITICAL SINGLE-LINE ADDRESS HANDLING:
        - When you see single-line format like "10255 Live Oak Ave CHERRY VALLEY CA"
        - ALWAYS recognize that the last 2-letter word is the STATE (CA, NY, TX, etc.)
        - The words before the state are the CITY NAME (even if multiple words like "CHERRY VALLEY")
        - NEVER drop the state abbreviation during formatting
        - Example: "10255 Live Oak Ave CHERRY VALLEY CA" → "10255 Live Oak Avenue, Cherry Valley, CA"
        - Example: "386 E 8th St PITTSBURG CA" → "386 E 8th Street, Pittsburg, CA"

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "exact_number_from_left_side", "tracking_number": "UUS_tracking_number", "address": "formatted_address", "customer": "customer_name_if_visible"}]}

        Do not include any text before or after the JSON.
        """
    }

    // GoFo delivery recognition prompt - 完全隔离版本
    private func createGoFoPrompt() -> String {
        return """
        🟢 GOFO EXCLUSIVE RECOGNITION - Ignore all other delivery service instructions

        📦 GoFo Delivery Recognition - Extract ONLY what is actually shown in the GoFo app interface:

        🎯 GoFo-Specific Core Information:
        1. Sort Number: Extract the EXACT numeric sort number shown on the left side of each GoFo delivery item (e.g., 15, 16, 17, 18, 112, 113...)
        2. Address: Extract ONLY the address text shown in the GoFo interface, do not add any extra information
        3. Tracking Number: GF + 12 digits (e.g., GF611244756320)
        4. Customer Name: Full name as displayed in GoFo

        🚨🚨🚨 CRITICAL GOFO ADDRESS RULES - DELIVERY ACCURACY DEPENDS ON THIS 🚨🚨🚨

        ⚠️ ZIP CODES ARE CRITICAL FOR DELIVERY SUCCESS:
        - Wrong ZIP code = Wrong delivery location = Failed delivery = Customer complaint
        - ZIP codes determine the exact delivery area and route
        - Even one digit wrong can send packages to completely different cities

        🔒 ABSOLUTE ZIP CODE REQUIREMENTS:
        - 🚫 NEVER modify ZIP codes - use EXACTLY what appears in the image
        - 🚫 NEVER "complete" or "fix" ZIP codes - if image shows "95650", use "95650"
        - 🚫 NEVER generate variations - DO NOT create "95648", "95658", "95663" etc.
        - ✅ ONLY use ZIP codes that are explicitly visible in the image
        - ✅ If all addresses share same ZIP in image, they should ALL have that same ZIP

        📍 ADDRESS EXTRACTION RULES:
        - Extract the address text EXACTLY as displayed in the image
        - 🚫 DO NOT add missing information (city, state, etc.) - use only what is visible
        - 🚫 DO NOT "complete" or "standardize" addresses - extract exactly what you see
        - 🚫 DO NOT modify any part of the address except removing country suffixes
        - ✅ ONLY EXCEPTION: Remove country suffixes if present ("USA", "US", "United States")
        - ✅ Keep everything else exactly as shown in the image

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: If image shows "10624 Pleasant Valley Circ,95209", return exactly "10624 Pleasant Valley Circ,95209", do NOT add city/state information

        🎯 GoFo Interface Features:
        - Map background interface with blue circle markers
        - Left-side numeric sort numbers (extract EXACT numbers shown, e.g., 15, 16, 17, 18, 112, 113...)
        - Address display format: "Street Address, Zipcode"
        - Tracking number format: GF + 12 digits
        - Customer name below address
        - May display Chinese interface text

        🔍 CRITICAL EXTRACTION REQUIREMENTS:
        - ALWAYS extract the EXACT left-side numeric sort numbers as they appear (DO NOT reassign or renumber)
        - These numbers are MANDATORY for GoFo delivery operations and must match the original third-party sort numbers
        - If you cannot find a sort number, mark as "missing"

        🚨 COMPLETE TASK VALIDATION:
        - ONLY extract deliveries that have: Address + Sort Number
        - If ANY of these 2 required elements is missing, SKIP that task entirely
        - Video recording may show same addresses multiple times due to scrolling
        - If you see the same address with same sort number, only include it ONCE
        - Each sort number must be unique across all deliveries

        🔍 DUPLICATE PREVENTION:
        - Check for duplicates: if same address + same sort number already exists, skip
        - No duplicate address + sort number combinations
        - If you find duplicates, remove them and keep only one instance

        ⚡ Simplified recognition, focus on core information

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_from_interface", "tracking_number": "gofo_tracking_if_visible", "address": "formatted_address", "customer": "customer_name_if_visible", "delivery_time": "time_if_visible"}]}

        Do not include any text before or after the JSON.
        """
    }

    // SpeedX delivery recognition prompt - 完全隔离版本
    private func createSpeedXPrompt() -> String {
        return """
        🔴 SPEEDX EXCLUSIVE RECOGNITION - Ignore all other delivery service instructions

        📦 SpeedX Delivery Recognition - Extract delivery information from SpeedX app interface ONLY:

        🔥 TOTAL COUNT DETECTION (HIGHEST PRIORITY):
        - Look for total count indicators at the TOP of the interface
        - Common patterns: "未扫描(160/160)", "已扫描(0/160)", "错误分类(0)"
        - The format is typically: "状态(current/total)" where total is the complete order count
        - Extract the TOTAL number (e.g., 160 from "160/160") - this tells you how many deliveries should exist
        - This total count is CRITICAL for validating completeness

        🚨 STOP NUMBER DETECTION (HIGHEST PRIORITY):
        - CRITICAL: Look for "停靠点: XX" text at the BOTTOM RIGHT of each delivery block
        - This is the MOST IMPORTANT information - every delivery MUST have a stop number
        - Format examples: "停靠点: 46", "停靠点: 47", "停靠点: 48", "停靠点: 49", "停靠点: 50"
        - Extract ONLY the number part (e.g., "46", "47", "48", "49", "50")
        - DO NOT generate sequential numbers (1,2,3,4,5) - use the ACTUAL numbers shown
        - If you cannot find the stop number for a delivery, SKIP that delivery entirely
        - STEP 1: First scan the entire image for ALL "停靠点: XX" texts
        - STEP 2: Then match each stop number to its corresponding delivery block

        🎯 SpeedX-Specific Required Information (ALL must be present):
        1. Address: Left side, 1-2 lines (e.g., "25 Hyde court #2, Daly City, CA 94015")
        2. Tracking Number: Bottom left, starts with SPXSF (e.g., "#SPXSF005675143561")
        3. Stop Number: Bottom right, format "停靠点: X" (e.g., "停靠点: 1")
        4. Customer Name: Right side blue text (optional, e.g., "Marilyn Cov...")

        🚨 CRITICAL ADDRESS SEPARATION RULES - ABSOLUTELY MANDATORY:
        - 🚫 NEVER EVER include customer names in the "address" field
        - 🚫 NEVER EVER include recipient names in the "address" field
        - 🚫 NEVER EVER include personal names in the "address" field
        - ✅ Address field MUST ONLY contain: Street Number + Street Name + City + State
        - ✅ Customer names go ONLY in the separate "customer" field
        - ✅ Example CORRECT address: "1288 S Mayfair Ave, Daly City, CA"
        - ❌ Example WRONG address: "1288 S Mayfair Ave, Daly City, CA Myat Noe"
        - ❌ Example WRONG address: "1059 Wildwood Ave, Daly City, CA Inna Belyaev"

        🚨 CRITICAL ADDRESS FORMAT REQUIREMENTS:
        - MUST follow standard USPS format: "Street Number Street Name, City, State ZIP"
        - Example: "1288 S Mayfair Ave, Daly City, CA 94015"
        - 🚫 ABSOLUTELY FORBIDDEN: Never include "USA", "US", "United States" or any country suffix
        - ✅ Address MUST end with either State abbreviation (CA, NY, TX) or ZIP code
        - ✅ If ZIP code present: "City, State ZIP" (space between State and ZIP)
        - ✅ If no ZIP code: "City, State" (ends with State abbreviation)

        📱 SpeedX Interface Layout Recognition:
        - Each SpeedX delivery has a blue left border
        - Address on left side (1-2 lines)
        - Customer name on right side (blue clickable text)
        - Tracking number at bottom left (starts with #SPXSF)
        - Stop number at bottom right (停靠点: X format)

        ⚡ SpeedX-Specific Processing Rules:
        - 🚨 CRITICAL: Extract ONLY the ACTUAL number from "停靠点: X" for third_party_sort
        - 🚫 NEVER generate sequential numbers (1,2,3,4,5) - use REAL stop numbers from image
        - ✅ Example: If image shows "停靠点: 46", use "46" not "1"
        - Remove "USA" from addresses - format: "25 Hyde court #2, Daly City, CA, 94015"
        - Match information within the SAME visual block
        - 🚨 CRITICAL: Address field must ONLY contain address information, NEVER include customer names
        - Customer names belong in the separate "customer" field, NOT in the address field

        🚨 COMPLETE TASK VALIDATION:
        - ONLY extract deliveries that have: Address + Stop Number + Tracking Number
        - If ANY of these 3 required elements is missing, SKIP that delivery entirely
        - Screenshot may show same addresses multiple times due to scrolling
        - If you see the same address with same stop number, only include it ONCE
        - 🚨 CRITICAL: Extract stop numbers EXACTLY as shown, do NOT reorder or modify them
        - Preserve the original sequence of stop numbers as they appear in the interface

        🔍 SEQUENCE CONTINUITY CHECK:
        - Check for missing stop numbers in sequence (e.g., if you see 1,2,4,5 then 3 is missing)
        - If stop numbers are not continuous, note the gaps in your response
        - 🚨 INCOMPLETE DATA DETECTION: If you see truncated/cut-off delivery information, mark it as incomplete
        - Look for partial addresses, cut-off tracking numbers, or missing stop number displays
        - If delivery information appears to be cut off at image boundaries, flag it as "incomplete_data"

        🔍 DUPLICATE PREVENTION:
        - Check for duplicates: if same address + same stop number already exists, skip
        - No duplicate address + stop number combinations
        - If you find duplicates, remove them and keep only one instance
        - 🚨 IMPORTANT: When processing image segments, preserve original stop number sequence

        🚨 SPEEDX ONLY: You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "total_expected_count": "total_number_from_interface_or_null", "deliveries": [{"third_party_sort": "number_only", "tracking_number": "SPXSF_tracking_number", "address": "formatted_address", "customer": "customer_name"}], "sequence_gaps": ["missing_stop_numbers"], "incomplete_data": ["stop_numbers_with_incomplete_info"]}

        Do not include any text before or after the JSON.
        """
    }

    // SpeedX简化提示词（用于分割片段）- 参考GoFo优化版本
    private func createSpeedXCompactPrompt() -> String {
        return """
        SpeedX Delivery Recognition - Extract delivery information from SpeedX app:

        Each delivery item has:
        - Address (left side, 1-2 lines) - Example: "25 Hyde court #2, Daly City, CA 94015"
        - Customer name (right side, blue text) - Example: "Marilyn Cov..."
        - Tracking number (bottom left, starts with SPXSF) - Example: "#SPXSF005675143561"
        - Stop number (bottom right) - Example: "停靠点: 1"

        🚨 CRITICAL ADDRESS SEPARATION RULES - ABSOLUTELY MANDATORY:
        - 🚫 NEVER EVER include customer names in the "address" field
        - 🚫 NEVER EVER include recipient names in the "address" field
        - 🚫 NEVER EVER include personal names in the "address" field
        - ✅ Address field MUST ONLY contain: Street Number + Street Name + City + State
        - ✅ Customer names go ONLY in the separate "customer" field

        🚨 ADDRESS FORMAT RULES - Apple Maps优化格式:
        - 🎯 REQUIRED FORMAT: "Street, City,State" (注意：州简称前只有逗号，无空格)
        - ✅ 正确示例: "572 Reina Del Mar Ave, Pacifica,CA"
        - ❌ 错误示例: "572 Reina Del Mar Ave, Pacifica, CA, 94015, USA"
        - 🚫 NEVER include ZIP codes or country suffixes (USA, US, United States)
        - 🚫 NEVER add space before state abbreviation: use "City,CA" not "City, CA"

        🔧 ADDRESS CONSISTENCY RULES:
        - ALL addresses must follow the SAME format: "Street, City,State"
        - If you see mixed formats, standardize them to the Apple Maps optimized format
        - Example: Convert "175 Belhaven Ave 94015 The Mariana..." to "175 Belhaven Ave, Daly City,CA"
        - Extract city name from context: if other addresses show "Daly City", use that for incomplete addresses in the same area

        CRITICAL RULES:
        1. Extract ONLY the number from "停靠点: X" format for third_party_sort
        2. 🎯 Apple Maps优化格式: "25 Hyde court #2, Daly City,CA" (注意CA前无空格)
        3. Include customer name if visible in the separate "customer" field
        4. 🚨 NEVER include customer names in the address field - addresses must be clean street addresses only
        5. If you see customer names mixed with addresses, separate them properly
        6. 🆕 CONSISTENCY CHECK: Ensure all addresses follow the Apple Maps optimized format

        🚨 COMPLETE TASK VALIDATION:
        - ONLY extract deliveries that have: Address + Stop Number + Tracking Number
        - If ANY of these 3 required elements is missing, SKIP that delivery entirely
        - 🚨 CRITICAL: Extract stop numbers EXACTLY as shown, preserve original sequence
        - Do NOT reorder or modify stop numbers to make them "unique"

        🔍 SEQUENCE & COMPLETENESS CHECK:
        - Check for missing stop numbers in sequence (e.g., if you see 1,2,4,5 then 3 is missing)
        - 🚨 INCOMPLETE DATA: If delivery info appears cut-off or truncated, flag as incomplete
        - Look for partial addresses, cut-off tracking numbers, or missing stop displays

        🔍 DUPLICATE PREVENTION:
        - Check for duplicates: if same address + same stop number already exists, skip
        - No duplicate address + stop number combinations
        - 🚨 IMPORTANT: Preserve original stop number sequence when processing segments

        🔧 FORMAT CONSISTENCY EXAMPLE:
        If you see these mixed formats:
        - "250 Campana Ave, Daly City, CA, 94015, USA" ❌ (has ZIP and country)
        - "175 Belhaven Ave 94015 The Mariana Arr... G house is white and gray, Daly..." ❌ (incomplete)
        - "223 Belhaven Ave Casa, Daly City, CA, 94015, USA" ❌ (has ZIP and country)

        Convert ALL to Apple Maps optimized format:
        - "250 Campana Ave, Daly City,CA" ✅ (Apple Maps优化格式)
        - "175 Belhaven Ave, Daly City,CA" ✅ (Apple Maps优化格式)
        - "223 Belhaven Ave Casa, Daly City,CA" ✅ (Apple Maps优化格式)

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_only", "tracking_number": "SPXSF_tracking_number", "address": "formatted_address", "customer": "customer_name"}], "sequence_gaps": ["missing_stop_numbers"], "incomplete_data": ["stop_numbers_with_incomplete_info"]}

        Do not include any text before or after the JSON.
        """
    }

    // YWE delivery recognition prompt
    private func createYWEPrompt() -> String {
        return """
        📦 YWE Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Sort Number: # + number (#1, #2, #3, #4...)
        2. Waybill Number: YWAUS + 15 digits (e.g., YWAUS010000147255)
        3. Recipient: Complete English name
        4. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "14801 Ronald W Reagan Blvd Apt 9109" → full: "14801 Ronald W Reagan Blvd Apt 9109, Leander, TX, 78641", street: "14801 Ronald W Reagan Blvd, Leander, TX, 78641"

        ⚡ Address Format Examples:
        - Abbreviation preferred: "2200 Cabrillo Path, Leander, TX, 78641"
        - Alternative full form: "2200 Cabrillo Path, Leander, Texas, 78641"

        🎯 YWE Interface Features:
        - Chinese interface: 派送任务、收件人、地址、派送成功
        - White background list interface, card layout
        - Left side # sort number, right side green "派送成功" status
        - Yellow "派送图片待核验" warning indicator
        - Waybill format: YWAUS + 15 digits
        - Recipient: Complete English name
        - Address: US address format, may contain apartment numbers

        ⚡ Simplified recognition, focus on core information

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road
        - Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        - Format unit/apartment info correctly: "1721 Marina Court, Apt D"
        - Include city and state if visible: "San Mateo, CA"
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        🚨 US DIRECTIONAL RULES:
        - Look for ALL directional indicators: N, S, E, W, North, South, East, West
        - Standard pattern: "Number Direction Street" (e.g., "836 S Founders Point Ln")
        - NEVER miss directional information when present in the image

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"sort_number": "number_from_interface", "tracking_number": "ywe_tracking_if_visible", "address": "formatted_address", "customer": "customer_name_if_visible"}]}

        Do not include any text before or after the JSON.
        """
    }

    // 通用地址提取提示词
    private func createGeneralAddressPrompt() -> String {
        return """
        Analyze this image and extract delivery information. Support both English and Chinese addresses.

        Look for:
        1. Any delivery addresses (English or Chinese)
        2. Visible numbers or codes
        3. Names if visible
        4. CRITICAL: Unit/apartment/suite information (Suite, Unit, Apt, Room, Floor, #, etc.)

        Extract ONLY what you can actually see in the image.

        STRICT RULES:
        - ALWAYS extract unit/apartment/suite information if visible
        - Unit information is a critical part of delivery addresses
        - Look for: Suite, Unit, Apt, Room, Floor, #, 单元, 房间, 套房, etc.
        - Include unit information in the final address format

        ADDRESS FORMATTING REQUIREMENTS:

        FOR ENGLISH ADDRESSES (ESPECIALLY US ADDRESSES):
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        - Format unit/apartment info correctly: "1721 Marina Court, Apt D" not "1721 MARINA CT APT D"
        - CRITICAL: Always include unit information: "6968 S 700 W, Suite 221" not just "6968 S 700 W"
        - Use proper comma separation: "1625 Marina Court, Unit F" not "1625 Marina CT UNIT F"
        - Format city names properly: "San Mateo" not "SAN MATEO"
        - Include state but NOT country: "San Mateo, CA" (never add "USA")
        - 🚨 STATE ABBREVIATION MANDATORY: State abbreviations (CA, NY, TX, UT, etc.) are REQUIRED for US address geocoding

        🚨 CRITICAL US DIRECTIONAL RULES:
        - Utah/Western US pattern: "6968 S 700 W" (Number + N/S + Number + E/W)
        - Standard US pattern: "836 S Founders Point Ln" (Number + Direction + Street)
        - ALWAYS check for N/S directions when you see E/W directions
        - Common mistake: "836 W Founders Point Ln" should be "836 S Founders Point Ln"
        - Look carefully at the image for ALL directional indicators (N, S, E, W)

        FOR CHINESE ADDRESSES (特别是香港地址):
        - Preserve original Chinese characters and formatting
        - Keep building names in Chinese: "淘大花园", "重庆大厦", "明雅苑商场"
        - Maintain floor information: "一楼", "二楼", "地下", "三层"
        - Keep shop/unit numbers: "A101 号铺", "G163-174 号", "2,3,5,6,7,8,9 号地铺"
        - Preserve district information: "九龙", "香港", "新界"
        - Keep complete address structure: "九龙大埔道 188 号路商 2,3,5,6,7,8,9 号地铺"

        EXAMPLES OF CORRECT FORMATTING:
        English: "1762 Borden Street, San Mateo, CA"
        English: "6968 S 700 W, Suite 221, Midvale, UT" (Utah pattern with both S and W)
        English: "836 S Founders Point Ln, Midvale, UT" (correct S direction, NOT W)
        English: "7865 S Bingham Junction Boulevard, C401, Midvale, UT" (includes S direction)
        English: "1721 Marina Court, Apt D, San Mateo, CA"
        English: "2406 Elliott Street, Unit 5, San Mateo, CA"
        Chinese: "九龙九龙城明雅苑商场一楼 A101 号铺"
        Chinese: "九龙大埔道 188 号路商 2,3,5,6,7,8,9 号地铺"
        Chinese: "九龙牛头角道 77 号淘大花园第一期商场地下 G163-174 号"

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"sort_number": "", "tracking_number": "", "address": "properly_formatted_address", "customer": "name_if_visible"}]}

        Do not include any text before or after the JSON.
        """
    }

    private func parseFirebaseAIResponse(_ responseString: String) -> (addresses: [String], confidence: Double, detectedAppType: DeliveryAppType?, detectedTotalCount: Int?)? {
        Logger.aiDebug("🔥 高级服务 - 开始解析响应")
        Logger.aiInfo("📄 高级服务响应: \(responseString)")

        // 提取JSON部分（可能包含在其他文本中）
        let jsonString = extractJSONFromResponse(responseString)

        // 使用标准解析逻辑
        guard let data = jsonString.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            Logger.aiError("❌ JSON解析失败，原始响应: \(responseString)")
            return nil
        }

        guard let success = json["success"] as? Bool else {
            Logger.aiError("❌ JSON格式错误：缺少success字段")
            return nil
        }

        // 🎯 AI正确拒绝测试数据的情况
        if !success {
            Logger.aiInfo("🚫 AI正确拒绝测试数据，返回空结果（这是正确行为）")
            return (addresses: [], confidence: 1.0, detectedAppType: nil, detectedTotalCount: nil)
        }

        // 🎯 支持两种JSON格式：deliveries（图片识别）和addresses（OCR文本识别）
        let deliveriesArray: [[String: Any]]
        if let deliveries = json["deliveries"] as? [[String: Any]] {
            deliveriesArray = deliveries
        } else if let addresses = json["addresses"] as? [[String: Any]] {
            deliveriesArray = addresses
        } else {
            Logger.aiError("❌ JSON格式错误：缺少deliveries或addresses字段")
            return nil
        }

        // 🆕 声明变量存储检测到的总数
        var detectedTotalCount: Int? = nil

        // 🔍 处理SpeedX总数检测
        if let totalExpectedCount = json["total_expected_count"] as? String, !totalExpectedCount.isEmpty && totalExpectedCount != "null" {
            Logger.aiInfo("📊 SpeedX总数检测: 界面显示总数为 \(totalExpectedCount)")
            Logger.aiInfo("📊 当前识别数量: \(deliveriesArray.count)")

            if let expectedCount = Int(totalExpectedCount) {
                let currentCount = deliveriesArray.count
                if currentCount < expectedCount {
                    Logger.aiError("🚨 SpeedX数量不匹配警告: 预期 \(expectedCount) 个，实际识别 \(currentCount) 个")
                    Logger.aiError("💡 建议: 可能存在缺失订单，考虑重新录制视频或使用补充截图功能")
                } else if currentCount == expectedCount {
                    Logger.aiInfo("✅ SpeedX数量匹配: 识别数量与预期一致")
                } else {
                    Logger.aiWarning("⚠️ SpeedX数量超出: 预期 \(expectedCount) 个，实际识别 \(currentCount) 个")
                }

                // 🆕 保存检测到的总数
                detectedTotalCount = expectedCount
            }
        } else {
            Logger.aiInfo("📊 SpeedX总数检测: 未检测到总数信息")
        }

        // 🔍 处理SpeedX序号连续性和不完整数据检测
        if let sequenceGaps = json["sequence_gaps"] as? [String], !sequenceGaps.isEmpty {
            Logger.aiError("🚨 SpeedX序号缺失警告: 缺少停靠点 \(sequenceGaps.joined(separator: ", "))")
            Logger.aiError("💡 建议: 检查图片是否完整，可能需要重新截图包含缺失的停靠点")
        }

        if let incompleteData = json["incomplete_data"] as? [String], !incompleteData.isEmpty {
            Logger.aiError("🚨 SpeedX不完整数据警告: 停靠点 \(incompleteData.joined(separator: ", ")) 的信息被截断")
            Logger.aiError("💡 建议: 重新截图确保所有配送信息完整显示")
        }

        // 🚨 SpeedX重复排序号检测和修复
        var sortNumberCounts: [String: Int] = [:]
        var duplicateSortNumbers: Set<String> = []

        // 第一遍：检测重复的排序号
        for delivery in deliveriesArray {
            if let sortNumber = delivery["third_party_sort"] as? String ?? delivery["sort_number"] as? String {
                if !sortNumber.isEmpty {
                    sortNumberCounts[sortNumber, default: 0] += 1
                    if sortNumberCounts[sortNumber]! > 1 {
                        duplicateSortNumbers.insert(sortNumber)
                    }
                }
            }
        }

        // 如果发现重复，记录错误
        if !duplicateSortNumbers.isEmpty {
            Logger.aiError("🚨 检测到重复的第三方排序号: \(duplicateSortNumbers.sorted())")
            Logger.aiError("🚨 这将导致配送顺序错误，需要AI重新识别")
        }

        // 🆕 首先收集所有原始地址作为上下文
        let allRawAddresses = deliveriesArray.compactMap { delivery -> String? in
            return delivery["address"] as? String
        }

        let addresses = deliveriesArray.compactMap { delivery -> String? in
            guard let address = delivery["address"] as? String else { return nil }

            // 🎯 统一使用thirdPartySortNumber字段，与DeliveryPoint模型保持一致
            let thirdPartySortNumber = delivery["third_party_sort"] as? String ?? ""
            let trackingNumber = delivery["tracking_number"] as? String ?? ""
            let customer = delivery["customer"] as? String ?? ""
            let deliveryTime = delivery["delivery_time"] as? String ?? ""
            let fullAddress = delivery["full_address"] as? String ?? ""

            // 🎯 应用特定的地址清理逻辑
            var cleanedAddress = address
            var originalAddressForSaving = address  // 🚨 保存原始地址用于数据完整性

            // 🚨 重要：只对特定快递应用地址清理，避免冲突
            if let currentAppType = getCurrentAppType() {
                switch currentAppType {
                case .gofo:
                    // GoFo地址清理：移除国家后缀
                    if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
                        cleanedAddress = addressWithoutCountry
                        Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
                    }
                case .speedx:
                    // 🆕 SpeedX地址优化：智能处理州简称，利用上下文信息补全缺失城市名
                    let contextAddresses = allRawAddresses.filter { $0 != address } // 排除当前地址
                    cleanedAddress = optimizeSpeedXAddress(address, contextAddresses: contextAddresses)
                    Logger.aiDebug("🔴 SpeedX地址优化: '\(address)' -> '\(cleanedAddress)'")

                    // 🚨 对于SpeedX，如果处理后的地址与原始地址不同，记录原始地址
                    if cleanedAddress != address {
                        Logger.aiInfo("🔴 SpeedX原始地址保存: '\(address)' (处理后: '\(cleanedAddress)')")
                        // 在地址中添加原始地址标记，供后续处理使用
                        originalAddressForSaving = address
                    }
                default:
                    // 其他快递保持原样
                    break
                }
            }

            var result = cleanedAddress

            if !thirdPartySortNumber.isEmpty {
                // 🎯 如果是旧格式（包含"停靠点:"等文字），提取数字部分
                let cleanedSortNumber: String
                if thirdPartySortNumber.contains(":") || thirdPartySortNumber.contains("停靠点") {
                    // 旧格式，提取数字部分
                    cleanedSortNumber = extractNumberFromSortNumber(thirdPartySortNumber)
                } else {
                    // 新格式，直接使用
                    cleanedSortNumber = thirdPartySortNumber
                }

                if !cleanedSortNumber.isEmpty {
                    result += SortNumberConstants.thirdPartySortTag(cleanedSortNumber)
                }
            }

            if !trackingNumber.isEmpty {
                result += SortNumberConstants.trackingTag(trackingNumber)
            }

            if !customer.isEmpty {
                result += SortNumberConstants.customerTag(customer)
            }

            if !deliveryTime.isEmpty {
                result += SortNumberConstants.timeTag(deliveryTime)
            }

            // 🏠 添加完整地址信息（包含公寓号）
            if !fullAddress.isEmpty && fullAddress != address {
                result += SortNumberConstants.fullAddressTag(fullAddress)
            }

            // 🚨 对于SpeedX，如果原始地址与处理后地址不同，添加原始地址标记
            if let currentAppType = getCurrentAppType(), currentAppType == .speedx {
                if originalAddressForSaving != cleanedAddress {
                    result += SortNumberConstants.originalAddressTag(originalAddressForSaving)
                }
            }

            return result
        }

        Logger.aiInfo("🔥 高级服务解析结果: \(addresses.count)个地址")

        // 添加详细的地址信息日志
        for (index, address) in addresses.enumerated() {
            Logger.aiDebug("   地址\(index + 1): \(address.prefix(50))...")
        }

        Logger.aiInfo("✅ parseFirebaseAIResponse 完成处理")
        return (addresses: addresses, confidence: 0.85, detectedAppType: nil, detectedTotalCount: detectedTotalCount)
    }

    // 🎯 从第三方排序号中提取数字部分
    private func extractNumberFromSortNumber(_ sortNumber: String) -> String {
        // 提取所有数字字符
        let numberString = sortNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return numberString
    }

    // 🎯 新增：处理OCR提取的文本
    func extractAddressesFromOCRText(_ ocrText: String, appType: DeliveryAppType = .justPhoto) async throws -> GemmaAddressResult {
        let startTime = Date()
        Logger.aiInfo("🔥 Firebase AI - 开始处理OCR文本")
        Logger.aiDebug("📝 OCR文本长度: \(ocrText.count)字符")

        // 🎯 设置当前处理的应用类型
        setCurrentAppType(appType)

        do {
            // 使用Firebase AI处理OCR文本
            let jsonResponse = try await processOCRTextWithFirebaseAI(ocrText: ocrText, appType: appType)

            // 解析JSON响应
            if let result = parseFirebaseAIResponse(jsonResponse) {
                let processingTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("🔥 Firebase AI OCR文本处理完成: \(result.addresses.count)个地址, 耗时: \(String(format: "%.2f", processingTime))秒")

                return GemmaAddressResult(
                    addresses: result.addresses,
                    confidence: result.confidence,
                    processingTime: processingTime,
                    modelUsed: "Firebase AI (OCR Text)",
                    rawResponse: jsonResponse,
                    success: true,
                    detectedAppType: result.detectedAppType,
                    detectedTotalCount: result.detectedTotalCount // 🆕 传递检测到的总数
                )
            } else {
                Logger.aiError("OCR文本识别返回了无效的JSON响应")
                throw GemmaError.jsonParsingFailed
            }

        } catch {
            Logger.aiError("❌ Firebase AI OCR文本处理失败: \(error)")
            throw error
        }
    }

    // 🎯 使用Firebase AI处理OCR文本
    private func processOCRTextWithFirebaseAI(ocrText: String, appType: DeliveryAppType) async throws -> String {
        let prompt = createOCRTextPrompt(ocrText: ocrText, appType: appType)

        Logger.aiInfo("🔥 Firebase AI - 发送OCR文本识别请求")
        Logger.aiDebug("📤 提示词长度: \(prompt.count)字符")

        // OCR文本处理配置
        let generationConfig = GenerationConfig(
            temperature: 0.1,
            maxOutputTokens: 8000
        )

        // 使用Firebase AI模型
        let model = FirebaseAI.firebaseAI().generativeModel(
            modelName: "gemma-3-27b-it",
            generationConfig: generationConfig
        )

        // 生成响应（带重试机制）
        let response = try await generateContentWithRetry(model: model, prompt: prompt, maxRetries: 3)

        guard let text = response.text else {
            Logger.aiError("Firebase AI OCR文本处理返回空响应")
            throw GemmaError.jsonParsingFailed
        }

        Logger.aiDebug("✅ Firebase AI OCR文本处理响应成功，内容长度: \(text.count)字符")
        return text
    }

    // 🎯 创建OCR文本处理提示词
    private func createOCRTextPrompt(ocrText: String, appType: DeliveryAppType) -> String {
        // 🎯 根据应用类型使用专用提示词
        switch appType {
        case .gofo:
            return createGoFoOCRPrompt(ocrText: ocrText)
        case .speedx:
            return createSpeedXOCRPrompt(ocrText: ocrText)
        default:
            return createGeneralOCRPrompt(ocrText: ocrText)
        }
    }

    // 🎯 GoFo专用OCR文本处理提示词
    private func createGoFoOCRPrompt(ocrText: String) -> String {
        return """
        📦 GoFo OCR Text Analysis - Extract ONLY what is actually present in the OCR text:

        🚨🚨🚨 CRITICAL ZIP CODE RULES - DELIVERY ACCURACY DEPENDS ON THIS 🚨🚨🚨

        ⚠️ ZIP CODES ARE CRITICAL FOR DELIVERY SUCCESS:
        - Wrong ZIP code = Wrong delivery location = Failed delivery = Customer complaint
        - ZIP codes determine the exact delivery area and route
        - Even one digit wrong can send packages to completely different cities
        - NEVER EVER modify, guess, or generate ZIP codes that are not in the original text

        🔒 ABSOLUTE ZIP CODE REQUIREMENTS:
        1. 🚫 NEVER modify ZIP codes - use EXACTLY what appears in the OCR text
        2. 🚫 NEVER "complete" or "fix" ZIP codes - if text shows "95650", use "95650"
        3. 🚫 NEVER generate variations - DO NOT create "95648", "95658", "95663" etc.
        4. 🚫 NEVER guess missing ZIP codes - if not in text, leave empty
        5. ✅ ONLY use ZIP codes that are explicitly visible in the OCR text
        6. ✅ If all addresses share same ZIP in text, they should ALL have that same ZIP

        OCR Text to analyze:
        \(ocrText)

        🎯 GoFo Specific Instructions:
        1. Look for numeric sort numbers (112, 113, 114, 115, 116, 117, 118, 119...)
        2. Extract GF tracking numbers (GF + 12 digits)
        3. Extract customer names
        4. Extract addresses EXACTLY as they appear in the OCR text - NO modifications
        5. 🔒 STRICT RULE: Copy everything character-by-character from the text

        🔍 Content Extraction Process:
        - Step 1: Find complete address lines in the OCR text
        - Step 2: Copy them EXACTLY as written - do not add, remove, or change anything
        - Step 3: ONLY EXCEPTION: Remove country suffixes ("USA", "US", "United States") if present
        - Step 4: Double-check - are you copying EXACTLY from the original text?

        Return JSON format:
        {
          "success": true,
          "addresses": [
            {
              "address": "exact_address_from_ocr_text_with_original_zip",
              "third_party_sort": null,
              "tracking_number": "GF_tracking_if_found",
              "customer_name": "customer_name_if_found",
              "package_count": "package_count_if_found",
              "access_instructions": null
            }
          ]
        }

        🚨 FINAL CHECK: Before responding, verify every ZIP code in your response appears in the original OCR text!
        🚨 DELIVERY ACCURACY DEPENDS ON ZIP CODE ACCURACY - DO NOT MODIFY THEM!
        """
    }

    // 🎯 SpeedX专用OCR文本处理提示词
    private func createSpeedXOCRPrompt(ocrText: String) -> String {
        return """
        📦 SpeedX OCR Text Analysis - Extract delivery information with stop numbers:

        OCR Text to analyze:
        \(ocrText)

        🎯 SpeedX Specific Instructions:
        1. Look for "停靠点: X" patterns for third_party_sort (extract only the number)
        2. Extract SPXSF tracking numbers
        3. Extract addresses exactly as they appear
        4. Extract customer names

        Return JSON format:
        {
          "success": true,
          "addresses": [
            {
              "address": "complete_address_from_text",
              "third_party_sort": "number_only_from_停靠点",
              "tracking_number": "SPXSF_tracking_if_found",
              "customer_name": "customer_name_if_found",
              "package_count": "package_count_if_found",
              "access_instructions": null
            }
          ]
        }
        """
    }

    // 🎯 通用OCR文本处理提示词
    private func createGeneralOCRPrompt(ocrText: String) -> String {
        return """
        You are a delivery address extraction expert. I will provide you with OCR-extracted text from a delivery app screenshot. Please extract and structure the delivery information.

        OCR Text to analyze:
        \(ocrText)

        IMPORTANT INSTRUCTIONS:
        1. Look for complete delivery addresses (street number + street name + suburb/city + state + postcode)
        2. Ignore incomplete fragments like "1,CA,94015,US" or "8a01s,UsA" - these are NOT valid addresses
        3. Look for patterns like "停靠点: X", "SpeedX: X", "D90", "149" for sort numbers
        4. Extract tracking numbers (long alphanumeric codes)
        5. Extract customer names if visible
        6. Only include entries that have a recognizable street address

        Please extract delivery addresses and return them in this exact JSON format:
        {
          "success": true,
          "addresses": [
            {
              "address": "complete street address with suburb, state, postcode",
              "third_party_sort": "number_only_from_停靠点_format",
              "tracking_number": "tracking number if found",
              "customer_name": "customer name if found",
              "package_count": "number if found",
              "access_instructions": "special instructions if found"
            }
          ]
        }

        Requirements:
        1. ONLY extract complete, valid delivery addresses
        2. Ignore address fragments or incomplete data
        3. For third_party_sort: extract ONLY the number from "停靠点: X" format (e.g., "5", "8")
        4. Return valid JSON only, no explanations
        5. If no valid delivery addresses found, return {"success": false, "addresses": []}
        """
    }

    // 从响应中提取JSON部分
    private func extractJSONFromResponse(_ response: String) -> String {
        // 尝试找到JSON开始和结束位置
        if let startIndex = response.firstIndex(of: "{"),
           let endIndex = response.lastIndex(of: "}") {
            let jsonPart = String(response[startIndex...endIndex])
            Logger.aiDebug("🔥 提取的JSON: \(jsonPart)")
            return jsonPart
        }

        // 如果没找到大括号，返回原始响应
        Logger.aiWarning("⚠️ 未找到JSON格式，返回原始响应")
        return response
    }

    /// 🚀 Firebase AI原生PDF处理 - 使用Gemini原生PDF视觉功能
    func extractAddressesFromPDFNatively(_ pdfData: Data, appType: DeliveryAppType) async throws -> GemmaAddressResult {
        Logger.aiInfo("📄 Firebase AI - 开始原生PDF处理")
        Logger.aiInfo("📊 PDF大小: \(pdfData.count / 1024)KB")

        let startTime = Date()

        // 创建原生PDF处理提示词
        let prompt = createNativePDFPrompt(appType: appType)

        Logger.aiInfo("📝 Firebase AI原生PDF提示词 (\(appType.displayName)):")
        Logger.aiInfo("📄 提示词内容: \(prompt)")

        do {
            // 使用Firebase AI的原生PDF处理功能
            let response = try await processNativePDFWithFirebaseAI(pdfData: pdfData, prompt: prompt)

            if let result = parseFirebaseAIResponse(response) {
                let processingTime = Date().timeIntervalSince(startTime)
                Logger.aiInfo("✅ Firebase AI原生PDF处理成功！用时: \(String(format: "%.2f", processingTime))s")
                Logger.aiInfo("📊 识别结果: \(result.addresses.count)个地址, 置信度: \(Int(result.confidence * 100))%")
                Logger.aiDebug("识别的地址: \(result.addresses.joined(separator: " | "))")

                return GemmaAddressResult(
                    addresses: result.addresses,
                    confidence: result.confidence,
                    processingTime: processingTime,
                    modelUsed: "Firebase AI (Native PDF)",
                    rawResponse: response,
                    success: true,
                    detectedAppType: result.detectedAppType,
                    detectedTotalCount: result.detectedTotalCount // 🆕 传递检测到的总数
                )
            } else {
                Logger.aiError("❌ Firebase AI原生PDF响应解析失败")
                throw GemmaError.jsonParsingFailed
            }
        } catch {
            Logger.aiError("❌ Firebase AI原生PDF处理失败: \(error)")

            // 检查是否是MAX_TOKENS错误
            if let generateError = error as? GenerateContentError,
               case .responseStoppedEarly(let reason, let response) = generateError,
               reason.rawValue == "MAX_TOKENS" {
                Logger.aiWarning("⚠️ 响应超出token限制，尝试解析部分结果")

                // 尝试解析部分响应
                if let partialText = response.text {
                    Logger.aiInfo("📄 部分响应长度: \(partialText.count)字符")

                    // 尝试修复不完整的JSON
                    let fixedJson = fixIncompleteJSON(partialText)
                    if let result = parseFirebaseAIResponse(fixedJson) {
                        Logger.aiInfo("✅ 成功解析部分结果，获得 \(result.addresses.count) 个地址")
                        return GemmaAddressResult(
                            addresses: result.addresses,
                            confidence: result.confidence * 0.8, // 降低置信度因为是部分结果
                            processingTime: Date().timeIntervalSince(Date()),
                            modelUsed: "Firebase AI (Native PDF - Partial)",
                            rawResponse: fixedJson,
                            success: true,
                            detectedAppType: result.detectedAppType,
                            detectedTotalCount: result.detectedTotalCount // 🆕 传递检测到的总数
                        )
                    }
                }
            }

            throw error
        }
    }

    /// 🚀 大PDF文本分块处理 - 专门处理160+地址的超长PDF
    private func extractAddressesFromLargePDFText(_ pdfText: String, appType: DeliveryAppType) async throws -> GemmaAddressResult {
        Logger.aiInfo("📄 Firebase AI - 开始大PDF文本分块处理")
        Logger.aiInfo("📊 文本长度: \(pdfText.count)字符，预计包含大量地址")

        // 智能分块：按行分割，每块包含合理数量的地址
        let lines = pdfText.components(separatedBy: .newlines)
        let chunkSize = 50 // 每块处理50行，大约包含20-30个地址
        var allAddresses: [String] = []
        var totalConfidence: Double = 0.0
        var successfulChunks = 0

        // 分块处理
        for chunkIndex in stride(from: 0, to: lines.count, by: chunkSize) {
            let endIndex = min(chunkIndex + chunkSize, lines.count)
            let chunkLines = Array(lines[chunkIndex..<endIndex])
            let chunkText = chunkLines.joined(separator: "\n")

            if chunkText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                continue
            }

            Logger.aiInfo("📄 处理分块 \(chunkIndex/chunkSize + 1)，行数: \(chunkLines.count)")

            do {
                // 为每个分块创建专门的提示词
                let chunkPrompt = createLargePDFChunkPrompt(chunkText: chunkText, appType: appType, chunkIndex: chunkIndex/chunkSize + 1)

                let response = try await processTextWithFirebaseAI(prompt: chunkPrompt, appType: appType)

                if let result = parseFirebaseAIResponse(response) {
                    allAddresses.append(contentsOf: result.addresses)
                    totalConfidence += result.confidence
                    successfulChunks += 1
                    Logger.aiInfo("✅ 分块 \(chunkIndex/chunkSize + 1) 成功，识别 \(result.addresses.count) 个地址")
                } else {
                    Logger.aiWarning("⚠️ 分块 \(chunkIndex/chunkSize + 1) 解析失败")
                }

                // 分块间延迟，避免API限制
                if endIndex < lines.count {
                    try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
                }

            } catch {
                Logger.aiError("❌ 分块 \(chunkIndex/chunkSize + 1) 处理失败: \(error)")
            }
        }

        let averageConfidence = successfulChunks > 0 ? totalConfidence / Double(successfulChunks) : 0.0
        let success = !allAddresses.isEmpty

        Logger.aiInfo("📊 大PDF分块处理完成:")
        Logger.aiInfo("   - 总分块数: \(Int(ceil(Double(lines.count) / Double(chunkSize))))")
        Logger.aiInfo("   - 成功分块数: \(successfulChunks)")
        Logger.aiInfo("   - 识别地址总数: \(allAddresses.count)")
        Logger.aiInfo("   - 平均置信度: \(Int(averageConfidence * 100))%")

        return GemmaAddressResult(
            addresses: allAddresses,
            confidence: averageConfidence,
            processingTime: Date().timeIntervalSince(Date()),
            modelUsed: "Firebase AI (Chunked)",
            rawResponse: "Chunked processing completed",
            success: success,
            detectedAppType: nil,
            detectedTotalCount: nil // 🆕 分块处理不检测总数
        )
    }

    /// 创建大PDF分块专用提示词
    private func createLargePDFChunkPrompt(chunkText: String, appType: DeliveryAppType, chunkIndex: Int) -> String {
        let basePrompt = createPDFTextPrompt(pdfText: "", appType: appType) // 获取基础PDF提示词

        let chunkContext = """

        🔄 CHUNK PROCESSING MODE - This is chunk \(chunkIndex) of a large PDF document.
        - This text chunk contains part of a delivery address list (likely 20-50 addresses)
        - Extract ALL addresses from this specific chunk
        - This is extracted PDF text, not an image
        - Focus on finding complete delivery addresses in this text segment
        - Ignore any partial or incomplete address information

        TEXT TO ANALYZE:
        \(chunkText)
        """

        return basePrompt.replacingOccurrences(of: "PDF TEXT CONTENT:\n", with: "") + chunkContext
    }

    /// Firebase AI原生PDF处理方法
    private func processNativePDFWithFirebaseAI(pdfData: Data, prompt: String) async throws -> String {
        Logger.aiInfo("📄 Firebase AI - 发送原生PDF处理请求")
        Logger.aiDebug("📤 PDF大小: \(pdfData.count / 1024)KB, 提示词长度: \(prompt.count)字符")

        // 配置生成参数 - 使用Gemma 3-27B-IT的实际限制
        let generationConfig = GenerationConfig(
            temperature: 0.1,
            maxOutputTokens: 8000  // Gemma 3-27B-IT最大输出限制为8,192 tokens
        )

        // 使用Firebase AI模型
        let model = FirebaseAI.firebaseAI().generativeModel(
            modelName: "gemma-3-27b-it",
            generationConfig: generationConfig
        )

        // 创建包含PDF数据的多模态内容
        let pdfPart = InlineDataPart(data: pdfData, mimeType: "application/pdf")
        let textPart = prompt

        let requestStart = Date()
        let response = try await model.generateContent(pdfPart, textPart)
        let requestTime = Date().timeIntervalSince(requestStart)

        Logger.aiDebug("📥 Firebase AI原生PDF响应时间: \(String(format: "%.2f", requestTime))s")

        guard let responseText = response.text else {
            Logger.aiError("❌ Firebase AI原生PDF响应为空")
            throw GemmaError.jsonParsingFailed
        }

        Logger.aiDebug("✅ Firebase AI原生PDF响应成功，内容长度: \(responseText.count)字符")
        return responseText
    }

    /// 修复不完整的JSON响应
    private func fixIncompleteJSON(_ partialJson: String) -> String {
        var fixed = partialJson.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果JSON没有正确结束，尝试修复
        if !fixed.hasSuffix("}") && !fixed.hasSuffix("]") {
            // 查找最后一个完整的地址条目
            if let lastCompleteEntry = fixed.range(of: "}, {", options: .backwards) {
                // 截取到最后一个完整条目
                fixed = String(fixed[..<lastCompleteEntry.upperBound])
                fixed = String(fixed.dropLast(3)) // 移除 ", {"

                // 添加正确的结束符
                if fixed.contains("\"deliveries\": [") {
                    fixed += "]}"
                }
            } else if fixed.contains("{\"success\": true") {
                // 如果只有开头，添加空的deliveries数组
                if !fixed.contains("\"deliveries\"") {
                    fixed += ", \"deliveries\": []}"
                } else {
                    fixed += "]}"
                }
            }
        }

        Logger.aiDebug("🔧 修复后的JSON长度: \(fixed.count)字符")
        return fixed
    }

    /// 创建原生PDF处理专用提示词
    private func createNativePDFPrompt(appType: DeliveryAppType) -> String {
        let basePrompt = """
        🚀 NATIVE PDF PROCESSING - Analyze this PDF document using Gemini's native PDF vision capabilities.

        This PDF contains delivery address information. Please extract ALL delivery addresses from the entire document.

        IMPORTANT PROCESSING INSTRUCTIONS:
        - This is a native PDF document (not an image conversion)
        - Use Gemini's native PDF understanding to process the entire document
        - Extract addresses from ALL pages if this is a multi-page document
        - Look for structured delivery lists, address tables, or shipping manifests
        - This document may contain 50-300 delivery addresses
        - Process the entire document comprehensively

        EXTRACTION RULES:
        1. Extract ALL complete delivery addresses from the PDF
        2. Include customer names if present
        3. Look for tracking numbers, order numbers, or reference codes
        4. Identify any sort numbers or sequence numbers
        5. Extract delivery time information if available

        🚨 CRITICAL DATA INTEGRITY RULES:
        - ONLY extract data that actually exists in the PDF
        - DO NOT generate fake or placeholder tracking numbers (like 1Z999AA format)
        - DO NOT duplicate addresses - each address should appear only once
        - If tracking number is not visible, use empty string ""
        - If customer name is not visible, use empty string ""
        - Ensure each delivery entry is unique
        - For SPEEDX: tracking numbers must be SPXSF format (SPXSF + 13 digits)
        - DO NOT invent or hallucinate any data that is not clearly visible

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        - Format unit/apartment info correctly: "1721 Marina Court, Apt D"
        - Include city and state: "San Mateo, CA" (never add "USA")
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"sort_number": "number_if_found", "tracking_number": "tracking_if_found", "address": "formatted_address", "customer": "name_if_found", "delivery_time": "time_if_found"}]}

        Do not include any text before or after the JSON.
        """

        // 根据应用类型和地区添加特定的提示
        // GoFo特殊处理：不添加地区提示词，避免覆盖专用提示词的指令
        if appType == .gofo {
            return basePrompt
        }
        return basePrompt + createRegionSpecificPrompt(appType: appType)
    }

    /// 创建地区和快递类型特定的提示词
    private func createRegionSpecificPrompt(appType: DeliveryAppType) -> String {
        // 特殊处理iMile：根据用户选择的地区来决定提示词
        if appType == .imile {
            return createiMileRegionPrompt()
        }

        switch appType.region {
        case .usa:
            return createUSADeliveryPrompt(appType: appType)
        case .australia:
            return createAustraliaDeliveryPrompt(appType: appType)
        case .universal:
            return createUniversalPrompt(appType: appType)
        }
    }

    /// US delivery specific prompts
    private func createUSADeliveryPrompt(appType: DeliveryAppType) -> String {
        let baseUSAPrompt = """

        🇺🇸 US Address Recognition Rules:
        - Address format: "Number Street, City, State, Zipcode"
        - Example: "1721 Marina Ct, San Mateo, CA, 94403"
        - Street types use abbreviations (consistent with Apple Maps): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
        - Full forms also supported if abbreviations invalid: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
        - Focus on US state abbreviations (CA, NY, TX, FL, etc.)
        - US zipcode format: 5 digits or 5+4 format
        - 🚫 NEVER include country: DO NOT add "USA", "US", "United States" or any country suffix to addresses
        """

        switch appType {
        case .amazonFlex:
            // Amazon Flex special handling: Don't add extra US address format rules, as the dedicated prompt already contains all necessary instructions
            return ""
        case .gofo:
            // GoFo special handling: Don't add US address format rules, as GoFo dedicated prompt explicitly requires extracting only what's shown in the image
            return ""
        default:
            return baseUSAPrompt
        }
    }

    /// 澳洲快递特定提示词
    private func createAustraliaDeliveryPrompt(appType: DeliveryAppType) -> String {
        let baseAustraliaPrompt = """

        🇦🇺 澳洲地址识别规则:
        - 地址格式: "Unit/Number Street, Suburb, State, Postcode"
        - 示例: "58/15 Fulham Road, Rowville, Victoria, 3178"
        - 重点识别澳洲州名 (Victoria, NSW, QLD, WA, SA等)
        - 澳洲邮编格式: 4位数字
        """

        switch appType {
        case .imile:
            return baseAustraliaPrompt + """

            IMILE 特征:
            - 追踪号码: iMile格式追踪号
            - 排序号码: 各种格式的排序号
            - 支持澳洲和美国地址格式
            """
        case .ldsEpod:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            LDS EPOD 特征:
            - 追踪号码: CNUSUP + 11位数字 (如: CNUSUP00011738482)
            - 排序号码: 连续序号
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .piggy:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            PIGGY 特征:
            - 追踪号码: PG + 11位数字 或 14位纯数字 (如: PG10005375906)
            - 排序号码: 连续序号
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .uniuni:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            UNIUNI 特征:
            - 追踪号码: UUS + 16位数字 (如: UUS56D056436296171)
            - 路线号码: 三位数字 (149, 150, 151, 152等)
            - 收件人信息: 完整英文姓名
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            - 主要服务加州地区 (CHERRY VALLEY, CA等)
            """
        case .gofo:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            GOFO 特征:
            - 追踪号码: GoFo格式追踪号
            - 配送时间信息
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .ywe:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            YWE 特征:
            - 追踪号码: YWAUS + 15位数字 (如: YWAUS010000147255)
            - 排序号码: # + 数字 (#1, #2, #3, #4...)
            - 收件人信息: 完整英文姓名
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .speedx:
            return """

            US Address Format Rules:
            - Format: "Number Street, City, State, Zipcode"
            - Example: "1721 Marina Ct, San Mateo, CA, 94403"
            - Street types: St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - State abbreviations: CA, NY, TX, FL, etc.
            - ZIP code: 5 digits or 5+4 format
            - 🚫 CRITICAL: NEVER add country suffixes - DO NOT include "USA", "US", "United States" in addresses

            SPEEDX Features:
            - Tracking: SPXSF + 14 digits (e.g., SPXSF00567490961577)
            - Stop numbers: 停靠点: X (X is digit, MANDATORY for every delivery)
            - Address format: US format, remove country suffixes
            - Interface: Chinese UI but English addresses
            - CRITICAL: Every delivery must have stop number or skip that delivery
            """
        default:
            return baseAustraliaPrompt
        }
    }

    /// 通用提示词
    private func createUniversalPrompt(appType: DeliveryAppType) -> String {
        switch appType {
        case .justPhoto:
            return """

            📸 通用图片识别:
            - 自动检测地址格式 (美国/澳洲/其他)
            - 提取所有可见的地址信息
            - 识别排序号码和追踪号码
            - 适应各种快递应用界面
            """
        default:
            return ""
        }
    }

    /// iMile特殊地区处理提示词
    private func createiMileRegionPrompt() -> String {
        return """

        🌍 iMile 多地区服务:
        - 自动识别地址格式 (美国/澳洲)
        - 美国地址格式: "Number Street, City, State, Zipcode"
        - 澳洲地址格式: "Unit/Number Street, Suburb, State, Postcode"

        IMILE 特征:
        - 追踪号码: iMile格式追踪号
        - 排序号码: 各种格式的排序号
        - 支持美国和澳洲地址格式自动识别
        - 根据邮编判断地区: 5位数字(美国) vs 4位数字(澳洲)
        """
    }

    /// 🎯 统一优化：为所有快递优化图片以适配Firebase AI
    private func optimizeImageForFirebaseAI(_ image: UIImage, appType: DeliveryAppType) -> UIImage {
        let originalSize = image.size
        let aspectRatio = originalSize.height / originalSize.width
        let totalPixels = originalSize.width * originalSize.height

        Logger.aiInfo("🔧 \(appType.displayName)图片优化分析: 宽高比=\(String(format: "%.1f", aspectRatio)):1, 总像素=\(Int(totalPixels/1_000_000))M")

        // 🎯 统一优化策略：所有快递使用相同的优化参数
        let (maxDimension, maxPixels): (CGFloat, CGFloat) = (Self.unifiedMaxDimension, Self.unifiedMaxPixels)

        var needsOptimization = false
        var optimizedSize = originalSize

        // 检查1: 单边尺寸过大
        if originalSize.width > maxDimension || originalSize.height > maxDimension {
            Logger.aiInfo("⚠️ 单边尺寸超限，需要缩放")
            let scale = min(maxDimension / originalSize.width, maxDimension / originalSize.height)
            optimizedSize = CGSize(width: originalSize.width * scale, height: originalSize.height * scale)
            needsOptimization = true
        }
        // 检查2: 总像素过多
        else if totalPixels > maxPixels {
            Logger.aiInfo("⚠️ 总像素超限，需要压缩")
            let scale = sqrt(maxPixels / totalPixels)
            optimizedSize = CGSize(width: originalSize.width * scale, height: originalSize.height * scale)
            needsOptimization = true
        }

        // 🚨 统一处理：超长图片检测（适用于所有快递）
        if aspectRatio > 20.0 {
            Logger.aiError("🚨 \(appType.displayName)超长图片检测(宽高比\(String(format: "%.1f", aspectRatio)):1)")
            Logger.aiError("💡 建议: 考虑使用图片分割处理")

            // 🎯 统一压缩策略：所有快递使用相同的压缩比例
            let compressionScale: CGFloat = Self.unifiedCompressionScale

            // 对于超长图片，使用专用压缩
            if aspectRatio > 30.0 {
                optimizedSize = CGSize(width: originalSize.width * compressionScale, height: originalSize.height * compressionScale)
                needsOptimization = true
                Logger.aiInfo("🔧 \(appType.displayName)超长图片专用压缩: \(Int(compressionScale * 100))%")
            }
        }

        if needsOptimization {
            let pixelReduction = (originalSize.width * originalSize.height) / (optimizedSize.width * optimizedSize.height)
            Logger.aiInfo("🔧 \(appType.displayName)图片优化: \(Int(originalSize.width))x\(Int(originalSize.height)) → \(Int(optimizedSize.width))x\(Int(optimizedSize.height))")
            Logger.aiInfo("📉 像素数量减少: \(String(format: "%.1f", pixelReduction))x (原始/优化后)")

            let resizeStartTime = Date()
            UIGraphicsBeginImageContextWithOptions(optimizedSize, false, 1.0)
            image.draw(in: CGRect(origin: .zero, size: optimizedSize))
            let optimizedImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            let resizeTime = Date().timeIntervalSince(resizeStartTime)

            Logger.aiInfo("⏱️ 图片重采样耗时: \(String(format: "%.3f", resizeTime))秒")
            return optimizedImage ?? image
        }

        return image
    }
}
