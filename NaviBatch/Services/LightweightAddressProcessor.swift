import Foundation
import CoreLocation
import MapKit

/// 轻量级地址处理器 - 专门用于交互式搜索，避免hang问题
class LightweightAddressProcessor {
    static let shared = LightweightAddressProcessor()

    private init() {}

    /// 轻量级地址处理 - 快速响应，短超时
    /// - Parameter address: 原始地址
    /// - Returns: 地理编码结果
    func processAddress(_ address: String) async -> LightweightGeocodingResult {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedAddress.isEmpty else {
            return .failed(reason: "地址不能为空")
        }

        Logger.info("🚀 轻量级地址处理: \(trimmedAddress)", type: .location)

        // 1. 首先尝试原始地址
        if let result = await tryQuickGeocode(trimmedAddress) {
            Logger.info("✅ 原始地址成功: \(trimmedAddress)", type: .location)
            return result
        }

        // 2. 尝试简单清理
        let cleanedAddress = cleanAddress(trimmedAddress)
        if cleanedAddress != trimmedAddress,
           let result = await tryQuickGeocode(cleanedAddress) {
            Logger.info("✅ 清理后成功: \(cleanedAddress)", type: .location)
            return result
        }

        // 3. 尝试MKLocalSearch作为备选
        if let result = await tryLocalSearch(trimmedAddress) {
            Logger.info("✅ 本地搜索成功: \(trimmedAddress)", type: .location)
            return result
        }

        Logger.warning("❌ 轻量级处理失败: \(trimmedAddress)", type: .location)
        return .failed(reason: "无法找到该地址")
    }

    // MARK: - 私有方法

    /// 快速地理编码 - 2秒超时
    private func tryQuickGeocode(_ address: String) async -> LightweightGeocodingResult? {
        // 🚦 等待速率限制
        await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

        let geocoder = CLGeocoder()

        // 2秒超时
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 2_000_000_000)
            geocoder.cancelGeocode()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            let placemarks = try await geocoder.geocodeAddressString(
                address,
                in: nil,
                preferredLocale: Locale(identifier: "en_US")
            )

            guard let placemark = placemarks.first,
                  let location = placemark.location else {
                return nil
            }

            let formattedAddress = formatPlacemark(placemark)

            return .success(
                originalAddress: address,
                formattedAddress: formattedAddress,
                coordinate: location.coordinate,
                placemark: placemark
            )

        } catch {
            if error is CancellationError {
                Logger.warning("🚀 地理编码超时: \(address)", type: .location)
            } else {
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    Logger.error("🚨 LightweightAddressProcessor - Apple Maps API限制触发 - \(error.localizedDescription)", type: .location)
                    // 🎯 智能解析等待时间
                    let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                    await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                    Logger.warning("🚫 LightweightAddressProcessor - Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)", type: .location)
                    // 🎯 智能解析等待时间
                    let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                    await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                } else {
                    Logger.warning("🚀 地理编码失败: \(error.localizedDescription)", type: .location)
                }
            }
            return nil
        }
    }

    /// 使用MKLocalSearch作为备选
    private func tryLocalSearch(_ address: String) async -> LightweightGeocodingResult? {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = address
        request.resultTypes = [.address]

        let search = MKLocalSearch(request: request)

        // 2秒超时
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 2_000_000_000)
            search.cancel()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            let response = try await search.start()

            guard let mapItem = response.mapItems.first else {
                return nil
            }

            let coordinate = mapItem.placemark.coordinate

            // 🎯 使用反向地理编码获取英文格式的地址，避免中文本地化
            // 🚦 等待速率限制
            await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

            let geocoder = CLGeocoder()
            let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

            do {
                let placemarks = try await geocoder.reverseGeocodeLocation(
                    location,
                    preferredLocale: Locale(identifier: "en_US")
                )

                if let englishPlacemark = placemarks.first {
                    let formattedAddress = formatPlacemark(englishPlacemark)

                    return .success(
                        originalAddress: address,
                        formattedAddress: formattedAddress,
                        coordinate: coordinate,
                        placemark: englishPlacemark
                    )
                }
            } catch {
                Logger.warning("🚀 反向地理编码失败，使用原始结果: \(error.localizedDescription)", type: .location)
            }

            // 如果反向地理编码失败，使用原始结果
            let formattedAddress = formatPlacemark(mapItem.placemark)

            return .success(
                originalAddress: address,
                formattedAddress: formattedAddress,
                coordinate: coordinate,
                placemark: mapItem.placemark
            )

        } catch {
            if error is CancellationError {
                Logger.warning("🚀 本地搜索超时: \(address)", type: .location)
            } else {
                Logger.warning("🚀 本地搜索失败: \(error.localizedDescription)", type: .location)
            }
            return nil
        }
    }

    /// 简单地址清理
    private func cleanAddress(_ address: String) -> String {
        return address
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .replacingOccurrences(of: "[\\n\\r\\t]", with: " ", options: .regularExpression)
    }

    /// 格式化地标信息
    private func formatPlacemark(_ placemark: CLPlacemark) -> String {
        var components: [String] = []

        // 门牌号 + 街道
        if let subThoroughfare = placemark.subThoroughfare,
           let thoroughfare = placemark.thoroughfare {
            components.append("\(subThoroughfare) \(thoroughfare)")
        } else if let thoroughfare = placemark.thoroughfare {
            components.append(thoroughfare)
        }

        // 城市
        if let locality = placemark.locality {
            components.append(locality)
        }

        // 州/省
        if let administrativeArea = placemark.administrativeArea {
            components.append(administrativeArea)
        }

        // 邮编
        if let postalCode = placemark.postalCode {
            components.append(postalCode)
        }

        // 国家
        if let country = placemark.country {
            components.append(country)
        }

        return components.joined(separator: ", ")
    }
}

// MARK: - 结果类型

/// 轻量级地理编码结果
enum LightweightGeocodingResult {
    case success(
        originalAddress: String,
        formattedAddress: String,
        coordinate: CLLocationCoordinate2D,
        placemark: CLPlacemark
    )
    case failed(reason: String)

    var isSuccess: Bool {
        switch self {
        case .success: return true
        case .failed: return false
        }
    }
}
