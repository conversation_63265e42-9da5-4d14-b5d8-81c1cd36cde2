import Foundation
import Photos
import UIKit
import Combine

/// 照片相册服务
/// 负责管理照片的保存、相册的创建和管理
class PhotoAlbumService {
    // 单例
    static let shared = PhotoAlbumService()
    
    // 私有初始化方法
    private init() {}
    
    // 取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 权限管理
    
    /// 检查照片库权限
    /// - Parameter completion: 回调，返回是否有权限
    func checkPhotoLibraryPermission(completion: @escaping (Bool) -> Void) {
        let status = PHPhotoLibrary.authorizationStatus()
        
        switch status {
        case .authorized, .limited:
            completion(true)
        case .denied, .restricted:
            completion(false)
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { status in
                DispatchQueue.main.async {
                    completion(status == .authorized || status == .limited)
                }
            }
        @unknown default:
            completion(false)
        }
    }
    
    // MARK: - 相册管理
    
    /// 获取或创建指定名称的相册
    /// - Parameters:
    ///   - albumName: 相册名称
    ///   - completion: 回调，返回相册对象或错误
    func getOrCreateAlbum(named albumName: String, completion: @escaping (PHAssetCollection?, Error?) -> Void) {
        // 首先检查权限
        checkPhotoLibraryPermission { hasPermission in
            if !hasPermission {
                completion(nil, NSError(domain: "PhotoAlbumService", code: 403, userInfo: [NSLocalizedDescriptionKey: "没有照片库访问权限"]))
                return
            }
            
            // 查找是否已存在同名相册
            let fetchOptions = PHFetchOptions()
            fetchOptions.predicate = NSPredicate(format: "title = %@", albumName)
            let collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .any, options: fetchOptions)
            
            // 如果已存在，直接返回
            if let existingAlbum = collections.firstObject {
                completion(existingAlbum, nil)
                return
            }
            
            // 创建新相册
            var albumPlaceholder: PHObjectPlaceholder?
            
            do {
                try PHPhotoLibrary.shared().performChangesAndWait {
                    let createAlbumRequest = PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: albumName)
                    albumPlaceholder = createAlbumRequest.placeholderForCreatedAssetCollection
                }
                
                // 获取新创建的相册
                if let placeholder = albumPlaceholder {
                    let fetchResult = PHAssetCollection.fetchAssetCollections(withLocalIdentifiers: [placeholder.localIdentifier], options: nil)
                    completion(fetchResult.firstObject, nil)
                } else {
                    completion(nil, NSError(domain: "PhotoAlbumService", code: 500, userInfo: [NSLocalizedDescriptionKey: "创建相册失败"]))
                }
            } catch {
                completion(nil, error)
            }
        }
    }
    
    // MARK: - 照片保存
    
    /// 保存照片到指定相册
    /// - Parameters:
    ///   - image: 要保存的图片
    ///   - albumName: 相册名称
    ///   - completion: 回调，返回保存路径或错误
    func savePhotoToAlbum(image: UIImage, albumName: String, completion: @escaping (String?, Error?) -> Void) {
        // 获取或创建相册
        getOrCreateAlbum(named: albumName) { album, error in
            if let error = error {
                completion(nil, error)
                return
            }
            
            guard let album = album else {
                completion(nil, NSError(domain: "PhotoAlbumService", code: 404, userInfo: [NSLocalizedDescriptionKey: "相册不存在"]))
                return
            }
            
            // 保存图片到照片库
            var assetPlaceholder: PHObjectPlaceholder?
            
            do {
                try PHPhotoLibrary.shared().performChangesAndWait {
                    let assetChangeRequest = PHAssetChangeRequest.creationRequestForAsset(from: image)
                    assetPlaceholder = assetChangeRequest.placeholderForCreatedAsset
                    
                    if let albumChangeRequest = PHAssetCollectionChangeRequest(for: album),
                       let placeholder = assetPlaceholder {
                        let fastEnumeration = NSArray(array: [placeholder])
                        albumChangeRequest.addAssets(fastEnumeration)
                    }
                }
                
                // 返回资源标识符作为路径
                if let placeholder = assetPlaceholder {
                    completion(placeholder.localIdentifier, nil)
                } else {
                    completion(nil, NSError(domain: "PhotoAlbumService", code: 500, userInfo: [NSLocalizedDescriptionKey: "保存照片失败"]))
                }
            } catch {
                completion(nil, error)
            }
        }
    }
    
    /// 根据路径获取照片
    /// - Parameters:
    ///   - localIdentifier: 照片的本地标识符
    ///   - completion: 回调，返回图片或错误
    func getPhotoByIdentifier(localIdentifier: String, completion: @escaping (UIImage?, Error?) -> Void) {
        let fetchOptions = PHFetchOptions()
        let assets = PHAsset.fetchAssets(withLocalIdentifiers: [localIdentifier], options: fetchOptions)
        
        if let asset = assets.firstObject {
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestImage(for: asset, targetSize: PHImageManagerMaximumSize, contentMode: .aspectFit, options: options) { image, info in
                if let image = image {
                    completion(image, nil)
                } else {
                    completion(nil, NSError(domain: "PhotoAlbumService", code: 404, userInfo: [NSLocalizedDescriptionKey: "无法获取照片"]))
                }
            }
        } else {
            completion(nil, NSError(domain: "PhotoAlbumService", code: 404, userInfo: [NSLocalizedDescriptionKey: "照片不存在"]))
        }
    }
    
    /// 生成相册名称
    /// - Parameters:
    ///   - routeName: 路线名称
    ///   - date: 日期（默认为当前日期）
    /// - Returns: 格式化的相册名称
    func generateAlbumName(routeName: String, date: Date = Date()) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        return "\(routeName)_\(dateString)"
    }
    
    /// 从路径加载照片（异步方法）
    /// - Parameter path: 照片路径（本地标识符）
    /// - Returns: 加载的图片，若加载失败则返回nil
    func loadPhotoFromPath(_ path: String) async throws -> UIImage? {
        // 确保路径不为空
        guard !path.isEmpty else {
            throw NSError(domain: "PhotoAlbumService", code: 400, userInfo: [NSLocalizedDescriptionKey: "照片路径为空"])
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            // 使用现有方法加载照片
            self.getPhotoByIdentifier(localIdentifier: path) { image, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let image = image {
                    continuation.resume(returning: image)
                } else {
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    /// 检查照片是否存在于相册中
    /// - Parameter path: 照片路径（本地标识符）
    /// - Returns: 如果照片存在返回true，否则返回false
    func photoExists(at path: String) async -> Bool {
        guard !path.isEmpty else {
            return false
        }
        
        let fetchOptions = PHFetchOptions()
        let assets = PHAsset.fetchAssets(withLocalIdentifiers: [path], options: fetchOptions)
        return assets.count > 0
    }
}
