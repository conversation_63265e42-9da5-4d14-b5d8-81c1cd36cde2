import Foundation
import CoreLocation

// MARK: - 地址验证服务
class AddressVerificationService {
    static let shared = AddressVerificationService()

    // 🤖 智能地址修复系统集成
    @MainActor
    private var problemCollector: ProblemAddressCollector {
        ProblemAddressCollector.shared
    }

    // 🚫 地址库配对开关 - 用于调试和测试
    public var isAddressDatabaseEnabled = true

    private init() {}

    // MARK: - 地址库控制

    /// 启用地址库配对
    public func enableAddressDatabase() {
        isAddressDatabaseEnabled = true
        Logger.aiInfo("✅ 地址库配对已启用")
    }

    /// 禁用地址库配对（用于调试和测试）
    public func disableAddressDatabase() {
        isAddressDatabaseEnabled = false
        Logger.aiInfo("🚫 地址库配对已禁用")
    }

    // MARK: - 地址验证和搜索

    /// 验证地址并搜索真实地址
    /// - Parameter source: 地址来源，用于问题地址收集
    func verifyAndSearchAddress(_ address: String, source: AddressSource = .screenshot) async -> AddressVerificationResult {
        Logger.aiInfo("🔍 开始验证地址: \(address)")

        // 🚫 检查地址库开关
        if !isAddressDatabaseEnabled {
            Logger.aiInfo("🚫 地址库配对已禁用，跳过数据库查询")
        } else {
            // 🚀 首先检查用户地址数据库
            if let validatedResult = await UserAddressDatabase.shared.getValidatedAddress(for: address) {
            // 🚨 特殊处理：AI修复的地址需要降低信任度
            if validatedResult.source == .aiCorrection {
                // AI修复的地址置信度较低，如果置信度过低则重新验证
                if validatedResult.confidence < 0.8 {
                    Logger.aiWarning("🤖 AI修复地址置信度过低(\(validatedResult.confidence))，重新验证: \(address)")
                    // 继续执行地理编码验证，不直接返回缓存结果
                } else {
                    Logger.aiInfo("✅ 使用AI修复的数据库地址验证结果: \(address) (置信度: \(validatedResult.confidence))")
                    return AddressVerificationResult(
                        originalAddress: address,
                        isValid: true,
                        verifiedCoordinate: validatedResult.coordinate,
                        alternativeAddresses: []
                    )
                }
            } else {
                Logger.aiInfo("✅ 使用数据库地址验证结果: \(address)")
                return AddressVerificationResult(
                    originalAddress: address,
                    isValid: true,
                    verifiedCoordinate: validatedResult.coordinate,
                    alternativeAddresses: []
                )
            }
        }
        }

        // 1. 尝试地理编码验证
        if let coordinate = await geocodeAddress(address) {
            Logger.aiInfo("✅ 地址验证成功: \(address)")

            // 🚫 检查地址库开关，决定是否保存
            if isAddressDatabaseEnabled {
                // 🚀 保存到用户地址数据库
                await UserAddressDatabase.shared.saveValidatedAddress(
                    address,
                    coordinate: coordinate,
                    source: source,
                    confidence: 0.9
                )
            } else {
                Logger.aiInfo("🚫 地址库配对已禁用，跳过保存到数据库")
            }

            return AddressVerificationResult(
                originalAddress: address,
                isValid: true,
                verifiedCoordinate: coordinate,
                alternativeAddresses: []
            )
        }

        // 2. 如果地理编码失败，尝试搜索相似地址
        Logger.aiWarning("❌ 地址验证失败，开始搜索相似地址: \(address)")
        let alternatives = await searchSimilarAddresses(address)

        // 🤖 如果不是手动输入的地址，且没有找到有效的候选地址，收集为问题地址
        if source != .manual && alternatives.isEmpty {
            await MainActor.run {
                problemCollector.collectProblemAddress(
                    address,
                    source: source,
                    reason: .geocodingFailed
                )
            }
            Logger.aiInfo("🤖 已收集问题地址: \(address) (来源: \(source.rawValue))")
        }

        return AddressVerificationResult(
            originalAddress: address,
            isValid: false,
            verifiedCoordinate: nil,
            alternativeAddresses: alternatives
        )
    }

    /// 批量验证地址
    func verifyAddresses(_ addresses: [String]) async -> [AddressVerificationResult] {
        var results: [AddressVerificationResult] = []

        for address in addresses {
            let result = await verifyAndSearchAddress(address)
            results.append(result)

            // 添加延迟避免API限制
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        }

        return results
    }

    // MARK: - 地理编码

    private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
        // 🚦 等待速率限制
        await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

        return await withCheckedContinuation { continuation in
            let geocoder = CLGeocoder()
            geocoder.geocodeAddressString(address) { placemarks, error in
                if let error = error {
                    let nsError = error as NSError
                    if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                        Logger.aiError("🚨 AddressVerificationService - Apple Maps API限制触发 - \(error.localizedDescription)")
                        // 在continuation中不能使用await，所以记录错误但不能调用emergencyWait
                        Task {
                            let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                            await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                        }
                    } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                        Logger.aiWarning("🚫 AddressVerificationService - Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)")
                        Task {
                            let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                            await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
                        }
                    } else {
                        Logger.aiError("地理编码失败: \(address) - \(error.localizedDescription)")
                    }
                    continuation.resume(returning: nil)
                    return
                }

                guard let placemark = placemarks?.first,
                      let location = placemark.location else {
                    Logger.aiError("地理编码无结果: \(address)")
                    continuation.resume(returning: nil)
                    return
                }

                let coordinate = location.coordinate
                // 🎯 减少日志噪音：只在调试模式下打印详细坐标信息
                #if DEBUG
                Logger.aiInfo("✅ 地理编码成功: \(address) -> \(coordinate)")
                #endif

                // 🚨 验证坐标准确性：通过地址组件匹配检查坐标是否对应输入地址
                let isAccurate = self.verifyCoordinateAccuracy(
                    originalAddress: address,
                    coordinate: coordinate,
                    placemark: placemark
                )

                if isAccurate {
                    // 🎯 成功时使用简洁日志
                    Logger.aiInfo("✅ 地址验证成功: \(address)")
                    continuation.resume(returning: coordinate)
                } else {
                    Logger.aiWarning("❌ 坐标准确性验证失败: \(address) - 坐标与地址不匹配")
                    continuation.resume(returning: nil)
                }
            }
        }
    }

    // MARK: - 坐标准确性验证

    /// 验证坐标是否准确对应输入的地址
    /// - Parameters:
    ///   - originalAddress: 原始输入地址
    ///   - coordinate: 地理编码返回的坐标
    ///   - placemark: 地理编码返回的地标信息
    /// - Returns: 坐标是否准确
    private func verifyCoordinateAccuracy(
        originalAddress: String,
        coordinate: CLLocationCoordinate2D,
        placemark: CLPlacemark
    ) -> Bool {

        // 1. 基本坐标有效性检查
        guard coordinate.latitude != 0 || coordinate.longitude != 0,
              coordinate.latitude >= -90 && coordinate.latitude <= 90,
              coordinate.longitude >= -180 && coordinate.longitude <= 180 else {
            Logger.aiWarning("❌ 坐标无效: \(coordinate)")
            return false
        }

        // 2. 检查是否为默认香港坐标（常见的错误坐标）
        let hongKongLat = 22.3193
        let hongKongLng = 114.1694
        let tolerance = 0.001

        if abs(coordinate.latitude - hongKongLat) < tolerance &&
           abs(coordinate.longitude - hongKongLng) < tolerance {
            Logger.aiWarning("❌ 检测到默认香港坐标，可能不准确: \(originalAddress)")
            return false
        }

        // 3. 地址组件匹配验证
        let addressComponents = parseAddressComponents(originalAddress)
        let placemarkComponents = extractPlacemarkComponents(placemark)

        // 检查关键组件是否匹配
        let hasStreetMatch = checkStreetMatch(
            original: addressComponents.street,
            placemark: placemarkComponents.street
        )

        let hasCityMatch = checkCityMatch(
            original: addressComponents.city,
            placemark: placemarkComponents.city
        )

        let hasPostalMatch = checkPostalMatch(
            original: addressComponents.postalCode,
            placemark: placemarkComponents.postalCode
        )

        // 4. 综合判断准确性
        let matchScore = calculateMatchScore(
            streetMatch: hasStreetMatch,
            cityMatch: hasCityMatch,
            postalMatch: hasPostalMatch,
            originalAddress: originalAddress
        )

        let isAccurate = matchScore >= 0.5 // 降低到50%匹配度阈值，更宽松

        // 🎯 只在验证失败时打印详细日志，减少日志噪音
        if !isAccurate {
            Logger.aiWarning("❌ 坐标准确性验证失败: \(originalAddress)")
            Logger.aiWarning("   街道匹配: \(hasStreetMatch), 城市匹配: \(hasCityMatch), 邮编匹配: \(hasPostalMatch)")
            Logger.aiWarning("   匹配分数: \(matchScore), 阈值: 0.5")
        } else {
            Logger.aiInfo("✅ 坐标准确性验证通过: \(originalAddress) (分数: \(matchScore))")
        }

        return isAccurate
    }

    // MARK: - 地址搜索

    private func searchSimilarAddresses(_ address: String) async -> [AlternativeAddress] {
        // 提取关键信息进行搜索
        let searchTerms = extractSearchTerms(from: address)
        var alternatives: [AlternativeAddress] = []

        // 搜索每个关键词组合
        for term in searchTerms {
            if let coordinate = await geocodeAddress(term) {
                let confidence = calculateConfidence(original: address, alternative: term)

                alternatives.append(AlternativeAddress(
                    address: term,
                    coordinate: coordinate,
                    confidence: confidence
                ))

                // 🚫 检查地址库开关，决定是否保存候选地址
                if isAddressDatabaseEnabled {
                    // 🚀 保存候选地址到用户地址数据库（置信度较低）
                    await UserAddressDatabase.shared.saveValidatedAddress(
                        term,
                        coordinate: coordinate,
                        source: .screenshot,
                        confidence: Float(confidence * 0.8) // 候选地址置信度降低
                    )
                } else {
                    Logger.aiInfo("🚫 地址库配对已禁用，跳过保存候选地址")
                }
            }
        }

        // 按置信度排序并去重
        alternatives = alternatives
            .sorted { $0.confidence > $1.confidence }
            .prefix(3) // 最多返回3个候选
            .compactMap { $0 }

        // 🎯 只在找到候选地址时打印日志
        if !alternatives.isEmpty {
            Logger.aiInfo("🔍 找到\(alternatives.count)个候选地址")
        }
        return Array(alternatives)
    }

    // MARK: - 🤖 智能地址修复系统集成方法

    /// 收集无效地址到问题地址收集器
    /// - Parameters:
    ///   - address: 无效的地址
    ///   - source: 地址来源
    ///   - reason: 失败原因
    func collectInvalidAddress(_ address: String, source: AddressSource, reason: ProblemAddress.FailureReason) {
        // 只收集非手动输入的地址
        guard source != .manual else {
            Logger.aiInfo("🤖 跳过手动输入地址的收集: \(address)")
            return
        }

        Task { @MainActor in
            problemCollector.collectProblemAddress(address, source: source, reason: reason)
            Logger.aiInfo("🤖 已收集问题地址: \(address) (来源: \(source.rawValue), 原因: \(reason.localizedDescription))")
        }
    }

    /// 批量验证地址，自动收集问题地址
    /// - Parameters:
    ///   - addresses: 要验证的地址列表
    ///   - source: 地址来源
    /// - Returns: 验证结果列表
    func batchVerifyAddresses(_ addresses: [String], source: AddressSource) async -> [AddressVerificationResult] {
        var results: [AddressVerificationResult] = []

        Logger.aiInfo("🤖 开始批量验证 \(addresses.count) 个地址，来源: \(source.rawValue)")

        for address in addresses {
            let result = await verifyAndSearchAddress(address, source: source)
            results.append(result)

            // 添加延迟避免API频率限制
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
        }

        let validCount = results.filter { $0.isValid }.count
        let invalidCount = results.count - validCount

        Logger.aiInfo("🤖 批量验证完成: 有效 \(validCount), 无效 \(invalidCount)")

        return results
    }

    private func extractSearchTerms(from address: String) -> [String] {
        var terms: [String] = []

        // 移除可能错误的街道名，保留核心信息
        let components = address.components(separatedBy: ", ")

        if components.count >= 3 {
            // 特殊处理香港公屋地址
            if address.contains("Estate") && address.contains("Hong Kong") {
                // 1. 尝试移除可疑的街道名（如 "Fuk Loi Estate Road"）
                let filteredComponents = components.filter { component in
                    !component.contains("Estate Road") && !component.contains("Estate Street")
                }

                if filteredComponents.count >= 3 {
                    terms.append(filteredComponents.joined(separator: ", "))
                }

                // 2. 只保留建筑物、邨、区域
                if components.count >= 4 {
                    let building = components[0]
                    let estate = components[1]
                    let district = components[components.count - 3]
                    let country = components[components.count - 1]

                    terms.append("\(building), \(estate), \(district), \(country)")
                    terms.append("\(estate), \(district), \(country)")
                }

                // 3. 尝试用邨名搜索
                if let estateComponent = components.first(where: { $0.contains("Estate") }) {
                    let district = components[components.count - 3]
                    let country = components[components.count - 1]
                    terms.append("\(estateComponent), \(district), \(country)")
                }
            }

            // 4. 通用搜索：只用区域和城市
            if components.count >= 2 {
                let district = components[components.count - 3]
                let region = components[components.count - 2]
                let country = components[components.count - 1]

                terms.append("\(district), \(region), \(country)")
            }
        }

        return terms.filter { !$0.isEmpty }
    }

    private func calculateConfidence(original: String, alternative: String) -> Double {
        // 简单的相似度计算
        let originalWords = Set(original.lowercased().components(separatedBy: CharacterSet.alphanumerics.inverted))
        let alternativeWords = Set(alternative.lowercased().components(separatedBy: CharacterSet.alphanumerics.inverted))

        let intersection = originalWords.intersection(alternativeWords)
        let union = originalWords.union(alternativeWords)

        return union.isEmpty ? 0.0 : Double(intersection.count) / Double(union.count)
    }

    // MARK: - 地址组件解析和匹配辅助方法

    private struct AddressComponents {
        let street: String?
        let city: String?
        let postalCode: String?
    }

    private func parseAddressComponents(_ address: String) -> AddressComponents {
        // 解析地址字符串，提取街道、城市、邮编
        let components = address.components(separatedBy: ", ")

        var street: String?
        var city: String?
        var postalCode: String?

        // 查找邮编（5位数字）
        for component in components {
            let trimmed = component.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmed.range(of: "^\\d{5}$", options: .regularExpression) != nil {
                postalCode = trimmed
                break
            }
        }

        // 第一个组件通常是街道地址
        if !components.isEmpty {
            street = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
        }

        // 🎯 改进城市识别逻辑：处理常见的地址格式
        if components.count >= 2 {
            for i in 1..<components.count {
                let component = components[i].trimmingCharacters(in: .whitespacesAndNewlines)

                // 跳过邮编
                if component.range(of: "^\\d{5}$", options: .regularExpression) != nil {
                    continue
                }

                // 跳过州缩写和国家
                let stateAbbreviations = ["AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY"]
                let commonCountries = ["USA", "United States", "US"]

                if stateAbbreviations.contains(component.uppercased()) ||
                   commonCountries.contains(component) {
                    continue
                }

                // 如果组件长度合理且不是纯数字，可能是城市
                if component.count >= 2 && component.count <= 50 &&
                   component.range(of: "^\\d+$", options: .regularExpression) == nil {
                    city = component
                    break
                }
            }
        }

        return AddressComponents(street: street, city: city, postalCode: postalCode)
    }

    private func extractPlacemarkComponents(_ placemark: CLPlacemark) -> AddressComponents {
        let street = [placemark.subThoroughfare, placemark.thoroughfare]
            .compactMap { $0 }
            .joined(separator: " ")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        let city = placemark.locality
        let postalCode = placemark.postalCode

        return AddressComponents(
            street: street.isEmpty ? nil : street,
            city: city,
            postalCode: postalCode
        )
    }

    private func checkStreetMatch(original: String?, placemark: String?) -> Bool {
        guard let original = original?.lowercased(),
              let placemark = placemark?.lowercased() else {
            return false
        }

        // 提取街道号码和街道名
        let originalNumbers = extractNumbers(from: original)
        let placemarkNumbers = extractNumbers(from: placemark)

        // 街道号码必须匹配
        guard !originalNumbers.isEmpty && originalNumbers == placemarkNumbers else {
            return false
        }

        // 检查街道名相似性
        let originalStreetName = removeNumbers(from: original).trimmingCharacters(in: .whitespacesAndNewlines)
        let placemarkStreetName = removeNumbers(from: placemark).trimmingCharacters(in: .whitespacesAndNewlines)

        return calculateStringSimilarity(originalStreetName, placemarkStreetName) >= 0.7
    }

    private func checkCityMatch(original: String?, placemark: String?) -> Bool {
        guard let original = original?.lowercased(),
              let placemark = placemark?.lowercased() else {
            return false
        }

        return calculateStringSimilarity(original, placemark) >= 0.8
    }

    private func checkPostalMatch(original: String?, placemark: String?) -> Bool {
        guard let original = original,
              let placemark = placemark else {
            return false
        }

        return original == placemark
    }

    private func calculateMatchScore(streetMatch: Bool, cityMatch: Bool, postalMatch: Bool, originalAddress: String) -> Double {
        var score = 0.0

        // 🎯 智能权重分配：根据地址格式调整权重
        let addressComponents = parseAddressComponents(originalAddress)
        let hasCity = addressComponents.city != nil && !addressComponents.city!.isEmpty
        let hasPostalCode = addressComponents.postalCode != nil && !addressComponents.postalCode!.isEmpty

        if streetMatch {
            // 街道匹配是最重要的，基础权重60%
            score += 0.6
        }

        if hasCity && cityMatch {
            // 如果原地址有城市信息且匹配，加20%
            score += 0.2
        } else if !hasCity {
            // 如果原地址没有城市信息，不扣分，给予20%基础分
            score += 0.2
        }

        if hasPostalCode && postalMatch {
            // 如果有邮编且匹配，加20%
            score += 0.2
        } else if !hasPostalCode {
            // 如果没有邮编信息，不扣分，给予20%基础分
            score += 0.2
        }

        return min(score, 1.0) // 确保不超过1.0
    }

    private func extractNumbers(from string: String) -> [String] {
        let regex = try! NSRegularExpression(pattern: "\\d+", options: [])
        let matches = regex.matches(in: string, options: [], range: NSRange(location: 0, length: string.count))

        return matches.map {
            String(string[Range($0.range, in: string)!])
        }
    }

    private func removeNumbers(from string: String) -> String {
        return string.replacingOccurrences(of: "\\d+", with: "", options: .regularExpression)
    }

    private func calculateStringSimilarity(_ str1: String, _ str2: String) -> Double {
        let separatorSet = CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters)
        let words1 = Set(str1.components(separatedBy: separatorSet).filter { !$0.isEmpty })
        let words2 = Set(str2.components(separatedBy: separatorSet).filter { !$0.isEmpty })

        let intersection = words1.intersection(words2)
        let union = words1.union(words2)

        return union.isEmpty ? 0.0 : Double(intersection.count) / Double(union.count)
    }

    // MARK: - 地理编码服务

    /// 从地址获取坐标（用于AI预测地址验证）
    /// - Parameter address: 需要获取坐标的地址
    /// - Returns: 地址对应的坐标
    func getCoordinateFromAddress(_ address: String) async throws -> CLLocationCoordinate2D {
        Logger.aiInfo("🌍 开始地理编码: \(address)")

        return try await withCheckedThrowingContinuation { continuation in
            let geocoder = CLGeocoder()

            geocoder.geocodeAddressString(address) { placemarks, error in
                if let error = error {
                    Logger.aiError("❌ 地理编码失败: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                    return
                }

                guard let placemark = placemarks?.first,
                      let location = placemark.location else {
                    Logger.aiError("❌ 未找到地址对应的坐标: \(address)")
                    continuation.resume(throwing: AddressVerificationError.coordinateNotFound)
                    return
                }

                let coordinate = location.coordinate
                Logger.aiInfo("✅ 地理编码成功: \(coordinate.latitude), \(coordinate.longitude)")
                continuation.resume(returning: coordinate)
            }
        }
    }
}

// MARK: - 错误类型

enum AddressVerificationError: Error, LocalizedError {
    case coordinateNotFound
    case geocodingFailed

    var errorDescription: String? {
        switch self {
        case .coordinateNotFound:
            return "无法找到地址对应的坐标"
        case .geocodingFailed:
            return "地理编码失败"
        }
    }
}

// MARK: - 数据模型

struct AddressVerificationResult {
    let originalAddress: String
    let isValid: Bool
    let verifiedCoordinate: CLLocationCoordinate2D?
    let alternativeAddresses: [AlternativeAddress]
}

struct AlternativeAddress {
    let address: String
    let coordinate: CLLocationCoordinate2D
    let confidence: Double
}
