import Foundation
import UIKit

// MARK: - OpenRouter API响应模型
struct OpenRouterResponse: Codable {
    let choices: [Choice]
    let usage: Usage?

    struct Choice: Codable {
        let message: Message
        let finishReason: String?

        enum CodingKeys: String, CodingKey {
            case message
            case finishReason = "finish_reason"
        }
    }

    struct Message: Codable {
        let role: String
        let content: String
    }

    struct Usage: Codable {
        let promptTokens: Int
        let completionTokens: Int
        let totalTokens: Int

        enum CodingKeys: String, CodingKey {
            case promptTokens = "prompt_tokens"
            case completionTokens = "completion_tokens"
            case totalTokens = "total_tokens"
        }
    }
}

// MARK: - Gemma地址识别结果
struct GemmaAddressResult {
    let addresses: [String]
    let confidence: Double
    let processingTime: TimeInterval
    let modelUsed: String
    let rawResponse: String
    let success: Bool
    let detectedAppType: DeliveryAppType?  // 🎯 AI检测到的应用类型
    var detectedTotalCount: Int?  // 🆕 AI检测到的总数
}

// MARK: - Gemma地址验证结果
struct GemmaValidationResult {
    let originalAddress: String
    let isValid: Bool
    let confidence: Double
    let optimizedAddress: String
    let issues: [String]
    let suggestions: [String]
}

// MARK: - Gemma专用AI服务（使用动态配置）
class GemmaVisionService {
    static let shared = GemmaVisionService()

    // 🎯 统一图片压缩质量配置 - 与FirebaseAI保持一致，便于维护
    private static let unifiedCompressionQuality: CGFloat = 0.1   // 10%质量，极限压缩，最大化处理速度

    private init() {}

    // 🔴 SpeedX专用地址优化：智能处理州简称，提高Apple Maps识别准确率
    private func optimizeSpeedXAddress(_ address: String) -> String {
        var optimized = address

        // 1. 移除国家信息（USA, United States等）
        optimized = removeCountryInformation(optimized)

        // 2. 确保有州简称（这是Apple Maps精准识别的关键）
        optimized = ensureStateAbbreviation(optimized)

        // 3. 优化地址格式以提高识别率
        optimized = optimizeAddressFormat(optimized)

        return optimized
    }

    // 移除国家信息
    private func removeCountryInformation(_ address: String) -> String {
        var cleaned = address

        // 移除各种形式的美国国家标识
        let countryPatterns = [
            ", \\s*USA\\s*$",           // 末尾的USA
            ", \\s*United States\\s*$", // 末尾的United States
            ", \\s*US\\s*$",            // 末尾的US
            "\\s*,\\s*USA\\s*,",        // 中间的USA
            "\\s*,\\s*United States\\s*,", // 中间的United States
            "\\s*,\\s*US\\s*,",         // 中间的US
            "^\\s*USA\\s*,\\s*",        // 开头的USA
            "^\\s*United States\\s*,\\s*", // 开头的United States
            "^\\s*US\\s*,\\s*"          // 开头的US
        ]

        for pattern in countryPatterns {
            cleaned = cleaned.replacingOccurrences(
                of: pattern,
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 清理多余的逗号和空格
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
        cleaned = cleaned.replacingOccurrences(of: ",,", with: ",")
        cleaned = cleaned.replacingOccurrences(of: ", ,", with: ",")

        return cleaned
    }

    // 确保地址包含州简称（Apple Maps识别的关键）
    private func ensureStateAbbreviation(_ address: String) -> String {
        // 检查是否已经包含州简称
        if hasUSStateAbbreviation(address) {
            return address
        }

        // 如果没有州简称，尝试通过ZIP码推断
        if let stateFromZip = extractStateFromZipCode(address) {
            return insertStateIntoAddress(address, state: stateFromZip)
        }

        return address
    }

    // 优化地址格式以提高Apple Maps识别率
    private func optimizeAddressFormat(_ address: String) -> String {
        var optimized = address

        // 移除可能干扰识别的ZIP码（根据用户反馈，ZIP码对Apple Maps帮助不大）
        optimized = removeZipCodeIfNecessary(optimized)

        // 标准化地址分隔符
        optimized = standardizeAddressSeparators(optimized)

        return optimized
    }

    // 检查地址是否包含美国州简称
    private func hasUSStateAbbreviation(_ address: String) -> Bool {
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY",
            "DC"
        ]

        for state in usStates {
            let pattern = "\\b\(state)\\b"
            if address.range(of: pattern, options: [.regularExpression, .caseInsensitive]) != nil {
                return true
            }
        }

        return false
    }

    // 从ZIP码推断州简称
    private func extractStateFromZipCode(_ address: String) -> String? {
        guard let zipCode = extractZipCode(from: address) else {
            return nil
        }

        let zip = String(zipCode.prefix(5))
        let zipInt = Int(zip) ?? 0

        switch zipInt {
        case 90000...96199: return "CA"
        case 94000...94999: return "CA"
        case 95000...95999: return "CA"
        case 10000...14999: return "NY"
        case 75000...75999: return "TX"
        case 77000...77999: return "TX"
        case 33000...34999: return "FL"
        case 60000...60999: return "IL"
        case 98000...99999: return "WA"
        case 97000...97999: return "OR"
        case 85000...86999: return "AZ"
        case 80000...81999: return "CO"
        default:
            return nil
        }
    }

    // 从地址中提取ZIP码
    private func extractZipCode(from address: String) -> String? {
        let zipPattern = "\\b(\\d{5}(-\\d{4})?)\\b"
        guard let range = address.range(of: zipPattern, options: .regularExpression) else {
            return nil
        }
        return String(address[range])
    }

    // 将州信息插入到地址中
    private func insertStateIntoAddress(_ address: String, state: String) -> String {
        guard let zipRange = address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) else {
            return "\(address), \(state)"
        }

        let beforeZip = String(address[..<zipRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
        let zipAndAfter = String(address[zipRange.lowerBound...])

        return "\(beforeZip), \(state), \(zipAndAfter)"
    }

    // 根据需要移除ZIP码
    private func removeZipCodeIfNecessary(_ address: String) -> String {
        if hasCompleteAddressComponents(address) {
            return address.replacingOccurrences(
                of: "\\s*,?\\s*\\b\\d{5}(-\\d{4})?\\b",
                with: "",
                options: .regularExpression
            ).trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return address
    }

    // 检查地址是否有完整的组件
    private func hasCompleteAddressComponents(_ address: String) -> Bool {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        guard components.count >= 3 else { return false }
        return hasUSStateAbbreviation(address)
    }

    // 标准化地址分隔符
    private func standardizeAddressSeparators(_ address: String) -> String {
        var standardized = address

        // 🎯 SpeedX专用优化：州简称前不加空格 "City,CA" 而不是 "City, CA"
        // 检测美国州简称模式并去掉前面的空格
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]

        // 对于美国州简称，使用无空格格式：City,CA
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }

        // 其他逗号保持标准格式（有空格）
        standardized = standardized
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)

        return standardized
    }

    // 动态获取配置（使用async方法避免并发问题）
    private func getApiKey() async -> String {
        await MainActor.run {
            let key = ConfigService.shared.apiKey
            if key.isEmpty {
                Logger.aiInfo("⚠️ 使用备用API密钥（配置服务失败）")
                return "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"
            } else {
                Logger.aiInfo("✅ 使用Cloudflare动态配置的API密钥")
                Logger.aiDebug("🔑 API密钥前缀: \(String(key.prefix(20)))...")
                return key
            }
        }
    }

    private func getBaseURL() async -> String {
        await MainActor.run {
            let url = ConfigService.shared.baseURL
            if url.isEmpty {
                Logger.aiInfo("⚠️ 使用备用API URL（配置服务失败）")
                return "https://openrouter.ai/api/v1/chat/completions"
            } else {
                Logger.aiInfo("✅ 使用Cloudflare动态配置的API URL")
                Logger.aiDebug("🌐 API URL: \(url)")
                return url
            }
        }
    }

    private func getGemmaModels() async -> [String] {
        await MainActor.run {
            let models = ConfigService.shared.gemmaModels
            if models.isEmpty {
                Logger.aiInfo("⚠️ 使用备用模型列表（配置服务失败）")
                let fallbackModels = getBackupModels()
                Logger.aiDebug("📋 备用模型: \(fallbackModels.joined(separator: ", "))")
                return fallbackModels
            } else {
                Logger.aiInfo("✅ 使用Cloudflare动态配置的模型列表")
                Logger.aiDebug("🤖 配置的模型: \(models.joined(separator: ", "))")

                Logger.aiDebug("🤖 使用配置的模型列表: \(models.joined(separator: ", "))")
                return models
            }
        }
    }

    // 🚀 获取后备模型列表（Gemma系列）
    private func getBackupModels() -> [String] {
        return [
            "google/gemma-3-27b-it:free",
            "google/gemma-3-12b-it:free",
            "google/gemma-3-9b-it:free"
        ]
    }

    // MARK: - 主要功能：从图片识别地址
    func extractAddressesFromImage(_ image: UIImage, appType: DeliveryAppType = .justPhoto, isPDFImage: Bool = false) async throws -> GemmaAddressResult {
        let startTime = Date()
        print("🚀 GemmaVisionService - 开始AI地址识别")
        print("📸 图片尺寸: \(image.size.width)x\(image.size.height)")

        Logger.aiInfo("🚀 开始AI地址识别")

        // 📊 详细记录原始图片信息
        let originalSize = image.size
        let originalPixels = originalSize.width * originalSize.height
        let originalAspectRatio = originalSize.height / originalSize.width
        Logger.imageProcessing("📸 原始图片尺寸: \(Int(originalSize.width))x\(Int(originalSize.height))")
        Logger.imageProcessing("📊 原始图片像素: \(String(format: "%.1f", originalPixels/1_000_000))M像素")
        Logger.imageProcessing("📐 原始图片宽高比: \(String(format: "%.2f", originalAspectRatio)):1")

        // 估算原始图片内存大小 (RGBA = 4字节/像素)
        let originalMemoryMB = (originalPixels * 4) / (1024 * 1024)
        Logger.imageProcessing("💾 原始图片内存估算: \(String(format: "%.1f", originalMemoryMB))MB")

        // 记录配置来源
        Logger.aiInfo("⚙️ 正在获取Cloudflare动态配置...")

        // 🎯 统一压缩质量：所有快递使用相同的压缩质量，便于维护（与FirebaseAI保持一致）
        let compressionQuality: CGFloat = Self.unifiedCompressionQuality

        // 📊 记录JPEG压缩过程
        let compressionStartTime = Date()
        guard let imageData = image.jpegData(compressionQuality: compressionQuality) else {
            Logger.aiError("图片数据转换失败")
            throw GemmaError.imageProcessingFailed
        }
        let compressionTime = Date().timeIntervalSince(compressionStartTime)

        // 📊 详细记录压缩结果
        let compressedSizeKB = imageData.count / 1024
        let compressedSizeMB = Double(imageData.count) / (1024 * 1024)

        // 估算压缩前的文件大小 (假设PNG格式，约4字节/像素)
        let estimatedOriginalSizeKB = Int((originalPixels * 4) / 1024)
        let fileSizeReductionRatio = Double(compressedSizeKB) / Double(estimatedOriginalSizeKB)

        Logger.aiInfo("📦 \(appType.displayName)使用压缩质量: \(Int(compressionQuality * 100))%")
        Logger.imageProcessing("📊 压缩后文件大小: \(compressedSizeKB)KB (\(String(format: "%.2f", compressedSizeMB))MB)")
        Logger.imageProcessing("📉 文件大小压缩比: \(String(format: "%.1f", fileSizeReductionRatio * 100))% (原估算\(estimatedOriginalSizeKB)KB)")
        Logger.imageProcessing("⏱️ JPEG压缩耗时: \(String(format: "%.3f", compressionTime))秒")

        // 📊 记录Base64编码过程
        let base64StartTime = Date()
        let base64Image = imageData.base64EncodedString()
        let base64Time = Date().timeIntervalSince(base64StartTime)

        let base64SizeMB = Double(base64Image.count) / (1024 * 1024)
        let base64Overhead = Double(base64Image.count) / Double(imageData.count)

        Logger.imageProcessing("🔤 Base64编码完成，编码后大小: \(String(format: "%.2f", base64SizeMB))MB")
        Logger.imageProcessing("📈 Base64编码开销: \(String(format: "%.1f", base64Overhead))x (编码后/原始)")
        Logger.imageProcessing("⏱️ Base64编码耗时: \(String(format: "%.3f", base64Time))秒")

        // 🎯 真正的备用模式：只有在主模型完全失败时才尝试备用模型
        let models = await getGemmaModels()

        for (index, model) in models.enumerated() {
            do {
                Logger.aiInfo("🤖 尝试Gemma模型 (\(index + 1)/\(models.count)): \(model)")
                let jsonResponse = try await processImageWithGemma(model, base64Image: base64Image, appType: appType, isPDFImage: isPDFImage)

                // 🎯 首先尝试解析响应
                if let result = parseGemmaResponse(jsonResponse, appType: appType) {
                    let processingTime = Date().timeIntervalSince(startTime)
                    let modelType = model.contains("deepseek") ? "DeepSeek" : "Gemma"
                    Logger.aiInfo("✅ \(modelType)识别成功！模型: \(model), 用时: \(String(format: "%.2f", processingTime))s")
                    Logger.aiInfo("📊 识别结果: \(result.addresses.count)个地址, 置信度: \(Int(result.confidence * 100))%")
                    Logger.aiDebug("识别的地址: \(result.addresses.joined(separator: " | "))")

                    return GemmaAddressResult(
                        addresses: result.addresses,
                        confidence: result.confidence,
                        processingTime: processingTime,
                        modelUsed: model,
                        rawResponse: jsonResponse,
                        success: true,
                        detectedAppType: nil, // 🎯 不再检测应用类型，由用户手动选择
                        detectedTotalCount: nil // 🆕 Gemma不检测总数
                    )
                } else {
                    // 🎯 如果是测试数据被拒绝，记录但不立即尝试下一个模型
                    Logger.aiError("模型 \(model) 返回了无效的JSON响应（可能是测试数据）")

                    // 🎯 只有在网络错误或API错误时才尝试备用模型
                    // 如果是测试数据问题，其他模型很可能也会返回类似结果
                    if index == 0 {
                        Logger.aiInfo("🎯 主模型返回测试数据，跳过备用模型，直接降级到OCR")
                        break
                    }
                }
            } catch {
                Logger.aiError("❌ Gemma模型 \(model) 失败: \(error.localizedDescription)")

                // 🎯 只有在网络/API错误时才尝试下一个模型
                if let gemmaError = error as? GemmaError {
                    switch gemmaError {
                    case .networkError, .apiError, .rateLimitExceeded, .geographicRestriction:
                        // 这些错误值得尝试备用模型
                        if index < models.count - 1 {
                            Logger.aiInfo("🚦 网络/API错误，等待3秒后尝试备用模型")
                            try? await Task.sleep(nanoseconds: 3_000_000_000) // 等待3秒
                        }
                        continue
                    default:
                        // 其他错误直接跳出
                        break
                    }
                } else {
                    // 未知错误，尝试备用模型
                    if index < models.count - 1 {
                        Logger.aiInfo("🚦 未知错误，等待3秒后尝试备用模型")
                        try? await Task.sleep(nanoseconds: 3_000_000_000) // 等待3秒
                    }
                    continue
                }
            }
        }

        let totalTime = Date().timeIntervalSince(startTime)
        Logger.aiError("💥 所有AI模型都失败了，总用时: \(String(format: "%.2f", totalTime))s")

        // 提供更友好的错误信息
        Logger.aiError("🔍 可能的解决方案:")
        Logger.aiError("1. 检查网络连接")
        Logger.aiError("2. 稍后重试")
        Logger.aiError("3. 切换到OCR模式")
        Logger.aiError("4. 尝试使用VPN（如果是地理位置限制）")

        throw GemmaError.allGemmaModelsFailed
    }

    // MARK: - 地址验证功能
    func validateAddresses(_ addresses: [String]) async throws -> [GemmaValidationResult] {
        let prompt = createValidationPrompt(addresses)
        let models = await getGemmaModels()

        for model in models {
            do {
                let jsonResponse = try await processTextWithGemma(model, prompt: prompt)
                if let results = parseValidationResponse(jsonResponse) {
                    return results
                }
            } catch {
                continue
            }
        }

        // 如果所有模型都失败，返回基础结果
        return addresses.map { address in
            GemmaValidationResult(
                originalAddress: address,
                isValid: true,
                confidence: 0.5,
                optimizedAddress: address,
                issues: [],
                suggestions: []
            )
        }
    }

    // MARK: - 地址格式转换功能（转换为Apple Maps可识别格式）
    func convertAddressesToAppleMapsFormat(_ addresses: [String], appType: DeliveryAppType = .justPhoto) async throws -> [String] {
        Logger.aiInfo("🔄 开始AI地址格式转换，地址数量: \(addresses.count)")

        let prompt = createAddressConversionPrompt(addresses, appType: appType)
        let models = await getGemmaModels()

        for (index, model) in models.enumerated() {
            do {
                Logger.aiInfo("🤖 尝试地址转换模型 (\(index + 1)/\(models.count)): \(model)")
                let jsonResponse = try await processTextWithGemma(model, prompt: prompt)

                if let convertedAddresses = parseAddressConversionResponse(jsonResponse) {
                    Logger.aiInfo("✅ 地址转换成功！模型: \(model)")
                    Logger.aiDebug("转换结果: \(convertedAddresses.joined(separator: " | "))")
                    return convertedAddresses
                } else {
                    Logger.aiError("模型 \(model) 返回了无效的转换响应")
                }
            } catch {
                Logger.aiError("❌ 地址转换模型 \(model) 失败: \(error.localizedDescription)")
                continue
            }
        }

        Logger.aiError("💥 所有模型地址转换都失败了，返回原始地址")
        return addresses // 如果转换失败，返回原始地址
    }

    // MARK: - PDF文本处理方法
    func extractAddressesFromPDFText(_ pdfText: String, appType: DeliveryAppType = .justPhoto) async throws -> GemmaAddressResult {
        let startTime = Date()
        print("📄 GemmaVisionService - 开始PDF文本AI地址识别")
        print("📄 PDF文本长度: \(pdfText.count)字符")

        Logger.aiInfo("📄 开始PDF文本AI地址识别")
        Logger.aiDebug("📄 PDF文本长度: \(pdfText.count)字符")

        // 记录配置来源
        Logger.aiInfo("⚙️ 正在获取Cloudflare动态配置...")

        // 🎯 真正的备用模式：只有在主模型完全失败时才尝试备用模型
        let models = await getGemmaModels()

        for (index, model) in models.enumerated() {
            do {
                Logger.aiInfo("🤖 尝试Gemma模型处理PDF文本 (\(index + 1)/\(models.count)): \(model)")
                let jsonResponse = try await processTextWithGemma(model, pdfText: pdfText, appType: appType)

                if let result = parseGemmaResponse(jsonResponse, appType: appType) {
                    let processingTime = Date().timeIntervalSince(startTime)
                    Logger.aiInfo("✅ PDF文本AI识别成功！用时: \(String(format: "%.2f", processingTime))s")
                    Logger.aiInfo("📊 识别结果: \(result.addresses.count)个地址, 置信度: \(Int(result.confidence * 100))%")
                    Logger.aiDebug("识别的地址: \(result.addresses.joined(separator: " | "))")

                    return GemmaAddressResult(
                        addresses: result.addresses,
                        confidence: result.confidence,
                        processingTime: processingTime,
                        modelUsed: model,
                        rawResponse: jsonResponse,
                        success: true,
                        detectedAppType: result.detectedAppType,
                        detectedTotalCount: nil // 🆕 Gemma不检测总数
                    )
                } else {
                    Logger.aiWarning("⚠️ 模型 \(model) PDF文本解析失败，尝试下一个模型")
                }
            } catch {
                Logger.aiWarning("⚠️ 模型 \(model) PDF文本处理失败: \(error.localizedDescription)")
                if index == models.count - 1 {
                    Logger.aiError("❌ 所有Gemma模型PDF文本处理都失败")
                    throw error
                }
            }
        }

        // 如果所有模型都失败，抛出错误
        throw GemmaError.allGemmaModelsFailed
    }

    // MARK: - 私有方法
    private func processImageWithGemma(_ model: String, base64Image: String, appType: DeliveryAppType, isPDFImage: Bool = false) async throws -> String {
        let prompt = createGemmaImagePrompt(appType: appType, isPDFImage: isPDFImage)

        // 🔍 记录提示词内容，方便调试和优化
        Logger.aiInfo("📝 使用的提示词 (\(appType.displayName)):")
        Logger.aiInfo("📄 提示词内容: \(prompt)")

        let requestBody: [String: Any] = [
            "model": model,
            "messages": [
                [
                    "role": "user",
                    "content": [
                        [
                            "type": "text",
                            "text": prompt
                        ],
                        [
                            "type": "image_url",
                            "image_url": [
                                "url": "data:image/jpeg;base64,\(base64Image)"
                            ]
                        ]
                    ]
                ]
            ],
            "max_tokens": 60000,  // 支持大批量地址识别（最多300个地址）
            "temperature": 0.1
        ]

        return try await makeGemmaRequest(requestBody)
    }

    // 处理PDF文本的私有方法
    private func processTextWithGemma(_ model: String, pdfText: String, appType: DeliveryAppType) async throws -> String {
        let prompt = createGemmaPDFTextPrompt(pdfText: pdfText, appType: appType)

        // 🔍 记录提示词内容，方便调试和优化
        Logger.aiInfo("📝 使用的PDF文本提示词 (\(appType.displayName)):")
        Logger.aiInfo("📄 提示词内容: \(prompt)")

        let requestBody: [String: Any] = [
            "model": model,
            "messages": [
                [
                    "role": "user",
                    "content": prompt
                ]
            ],
            "max_tokens": 60000,  // 支持大批量地址识别（最多300个地址）
            "temperature": 0.1
        ]

        return try await makeGemmaRequest(requestBody)
    }

    // 创建PDF文本专用提示词
    private func createGemmaPDFTextPrompt(pdfText: String, appType: DeliveryAppType) -> String {
        let basePrompt = """
        Analyze this PDF text content and extract delivery addresses. The text was extracted from a PDF document that may contain delivery lists, address tables, or shipping manifests.

        PDF TEXT CONTENT:
        \(pdfText)

        EXTRACTION RULES:
        1. Look for complete delivery addresses in the text
        2. Extract customer names if present
        3. Look for tracking numbers, order numbers, or reference codes
        4. Identify any sort numbers or sequence numbers
        5. Extract delivery time information if available

        IMPORTANT:
        - Extract ONLY addresses that appear to be delivery destinations
        - Ignore header text, company information, or non-address content
        - Focus on residential and business delivery addresses
        - Preserve unit/apartment/suite information

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        - Format unit/apartment info correctly: "1721 Marina Court, Apt D"
        - Include city and state: "San Mateo, CA" (never add "USA")
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        🚨 FINAL VALIDATION BEFORE RESPONSE:
        Before sending your JSON response, verify:
        1. No duplicate sort_number values (each must be unique)
        2. Each delivery has complete information from the same visual block
        3. All stop numbers are different (e.g., 13, 14, 15 NOT 13, 13, 14)

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_only_if_found", "tracking_number": "tracking_if_found", "address": "formatted_address", "customer": "name_if_found", "delivery_time": "time_if_found"}]}

        Do not include any text before or after the JSON.
        """

        // 根据应用类型和地区添加特定的提示
        // GoFo特殊处理：不添加地区提示词，避免覆盖专用提示词的指令
        if appType == .gofo {
            return basePrompt
        }
        return basePrompt + createRegionSpecificPrompt(appType: appType)
    }

    private func processTextWithGemma(_ model: String, prompt: String) async throws -> String {
        // 🔍 记录文本处理提示词内容，方便调试和优化
        Logger.aiInfo("📝 文本处理提示词:")
        Logger.aiInfo("📄 提示词内容: \(prompt)")

        let requestBody: [String: Any] = [
            "model": model,
            "messages": [
                [
                    "role": "user",
                    "content": prompt
                ]
            ],
            "max_tokens": 5000,  // 支持大批量地址转换
            "temperature": 0.1
        ]

        return try await makeGemmaRequest(requestBody)
    }

    private func makeGemmaRequest(_ requestBody: [String: Any]) async throws -> String {
        Logger.aiDebug("🌐 准备发送API请求到OpenRouter")

        let baseURL = await getBaseURL()
        let apiKey = await getApiKey()

        var request = URLRequest(url: URL(string: baseURL)!)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("NaviBatch-Gemma/1.0", forHTTPHeaderField: "HTTP-Referer")

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        let requestSize = request.httpBody?.count ?? 0
        Logger.aiDebug("📤 请求大小: \(requestSize / 1024)KB")

        let requestStart = Date()
        let (data, response) = try await URLSession.shared.data(for: request)
        let requestTime = Date().timeIntervalSince(requestStart)

        Logger.aiDebug("📥 API响应时间: \(String(format: "%.2f", requestTime))s")

        guard let httpResponse = response as? HTTPURLResponse else {
            Logger.aiError("无效的HTTP响应")
            throw GemmaError.networkError
        }

        Logger.aiDebug("📊 HTTP状态码: \(httpResponse.statusCode)")

        if httpResponse.statusCode == 429 {
            Logger.aiError("⏰ API调用频率超限，等待10秒后重试")
            try await Task.sleep(nanoseconds: 10_000_000_000) // 等待10秒
            throw GemmaError.rateLimitExceeded
        } else if httpResponse.statusCode == 403 {
            Logger.aiError("🚫 API访问被拒绝，可能是地理位置限制")
            // 尝试解析错误信息
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let error = errorData["error"] as? [String: Any],
               let message = error["message"] as? String {
                Logger.aiError("📍 错误详情: \(message)")
            }
            throw GemmaError.geographicRestriction
        } else if httpResponse.statusCode == 451 {
            Logger.aiError("🌍 服务在当前地区不可用")
            throw GemmaError.geographicRestriction
        } else if httpResponse.statusCode != 200 {
            Logger.aiError("❌ API错误，状态码: \(httpResponse.statusCode)")
            // 记录响应内容以便诊断
            if let responseString = String(data: data, encoding: .utf8) {
                Logger.aiError("📄 响应内容: \(responseString.prefix(200))")
            }
            throw GemmaError.apiError(httpResponse.statusCode)
        }

        let responseSize = data.count
        Logger.aiDebug("📥 响应大小: \(responseSize)字节")

        let apiResponse = try JSONDecoder().decode(OpenRouterResponse.self, from: data)
        let content = apiResponse.choices.first?.message.content ?? ""
        Logger.aiDebug("✅ API响应解析成功，内容长度: \(content.count)字符")

        return content
    }

    private func createGemmaImagePrompt(appType: DeliveryAppType = .justPhoto, isPDFImage: Bool = false) -> String {
        let basePrompt: String
        switch appType {
        case .amazonFlex:
            basePrompt = createAmazonFlexPrompt()
        case .imile:
            basePrompt = createiMilePrompt()
        case .ldsEpod:
            basePrompt = createLDSEpodPrompt()
        case .piggy:
            basePrompt = createPiggyPrompt()
        case .uniuni:
            basePrompt = createUniUniPrompt()
        case .gofo:
            basePrompt = createGoFoPrompt()
        case .ywe:
            basePrompt = createYWEPrompt()
        case .speedx:
            basePrompt = createSpeedXPrompt()
        case .justPhoto, .manual:
            basePrompt = createGeneralAddressPrompt()
        default:
            basePrompt = createGeneralAddressPrompt()
        }

        // 如果是PDF图像，添加PDF上下文信息
        if isPDFImage {
            let pdfContext = """

            📄 CRITICAL: This is a VERY LONG image extracted from a PDF document.
            - This image likely contains 100-300 delivery addresses in a vertical list format
            - This is a SINGLE PAGE PDF that has been rendered as a very tall image
            - Text quality may be lower than typical screenshots due to PDF rendering
            - SCAN THE ENTIRE IMAGE from TOP to BOTTOM - addresses are distributed throughout the full height
            - Look for structured data like delivery lists, address tables, or shipping manifests
            - Pay special attention to tabular data and multiple address entries
            - The layout may be different from typical app screenshots
            - Extract ALL visible addresses, not just the first few visible ones
            - This is likely a delivery route list with many stops
            """
            return basePrompt + pdfContext
        }

        // Special handling for apps that don't need region-specific prompts
        if appType == .gofo || appType == .amazonFlex {
            return basePrompt
        }
        return basePrompt + createRegionSpecificPrompt(appType: appType)
    }

    // Amazon Flex delivery recognition prompt - simplified extraction
    private func createAmazonFlexPrompt() -> String {
        return """
        📦 Amazon Flex Delivery Recognition - Simplified Extraction:

        🎯 Core Information (Extract only these two items):
        1. Sort Number: Numbers from left timeline (8, 9, 10, 11, 12, 13, 14...)
        2. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "1721 MARINA CT APT D" → full: "1721 Marina Ct, Apt D, San Mateo, CA", address: "1721 Marina Ct, San Mateo, CA"

        ⚡ 地址格式示例：
        - 简称优先: "1762 Borden St, San Mateo, CA"
        - 备选全称: "1762 Borden Street, San Mateo, CA"

        🎯 Amazon Flex界面特征：
        - 左侧时间线显示排序号码
        - 蓝色圆圈图标
        - 追踪号格式: # B.L11.OV（忽略）
        - 配送时间信息（忽略）
        - 地址格式: 美国地址，可能包含公寓号

        IMPORTANT:
        - 只提取排序号码和地址，忽略其他信息
        - 排序号码来自左侧时间线上的数字
        - 地址必须是英文格式，不包含追踪号
        - 如果没有公寓号，只返回address字段

        ADDRESS FORMATTING REQUIREMENTS:
        - 简称优先: St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
        - 备选全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
        - 使用正确大小写: "1762 Borden St" not "1762 BORDEN ST"
        - 公寓信息格式: "1721 Marina Ct, Apt D" not "1721 MARINA CT APT D"
        - 城市名格式: "San Mateo" not "SAN MATEO"

        Return JSON format:
        {
            "success": true,
            "deliveries": [
                {
                    "third_party_sort": "number_from_timeline",
                    "tracking_number": "",
                    "address": "street_address",
                    "full_address": "complete_address_with_apt_if_applicable"
                }
            ]
        }

        注意：如果没有公寓号，full_address和address字段内容相同。

        If no deliveries found, return: {"success": false, "deliveries": []}
        """
    }

    // iMile专用提示词 - 专注于地址和追踪号码提取
    private func createiMilePrompt() -> String {
        return """
        🚨 STOP! BEFORE DOING ANYTHING ELSE - READ THIS FIRST! 🚨

        ABSOLUTE RULE #1: NO TEST DATA ALLOWED
        If you see ANY of these words ANYWHERE in the image, immediately return:
        {"success": false, "deliveries": []}

        FORBIDDEN WORDS (ANY CAPITALIZATION):
        - amazon, Amazon Court, Fulham, Wellington, Taylors, ROWVILLE, rowville, Rowville
        - Margaret Johns, Sandra Tighe, Jenine Gray, Ann-Maree Mudie, Alison Rankcom
        - Any address with "3178" AND "Victoria" together
        - Any combination of the above

        DO NOT PROCEED IF YOU SEE ANY FORBIDDEN WORDS!

        ==========================================

        ONLY if the image contains NO forbidden words, then analyze this iMile delivery app screenshot.

        Look for REAL delivery information:
        1. Sort numbers with D prefix (D90, D91, etc.) - must be actually visible
        2. 12-digit tracking numbers - must be clearly readable
        3. Customer names - only if clearly visible (not guessed)
        4. Real Australian addresses - not test data

        STRICT REQUIREMENTS:
        - Extract ONLY what you can actually see in THIS specific image
        - NO examples, NO test data, NO generated information
        - If unsure about anything, return failure instead

        ADDRESS FORMAT (if real addresses found):
        "Unit/Number Street, Suburb, State, Postcode, Country"
        Example: "12/34 Smith Street, Melbourne, Victoria, 3000, AUS"

        FINAL CHECK BEFORE RESPONDING:
        1. Are these REAL addresses from the actual image?
        2. Do I see any forbidden test words?
        3. Am I 100% certain this is not test data?

        If ANY doubt exists, return: {"success": false, "deliveries": []}

        SUCCESS FORMAT (only if 100% certain):
        {
            "success": true,
            "deliveries": [
                {
                    "sort_number": "REAL_D_NUMBER",
                    "tracking_number": "REAL_12_DIGITS",
                    "address": "REAL_ADDRESS",
                    "customer": "REAL_NAME_OR_EMPTY"
                }
            ]
        }

        FAILURE FORMAT (if any doubt or forbidden words):
        {"success": false, "deliveries": []}
        """
    }

    // LDS EPOD专用提示词
    private func createLDSEpodPrompt() -> String {
        return """
        📦 LDS EPOD 快递识别 - 智能地址分离提取：

        🎯 核心信息：
        1. 排序号：数字序号 (1, 2, 3...)
        2. 地址：智能分离公寓号码
        3. 追踪号：CNUSUP + 11位数字 (如: CNUSUP00011738482)

        🏠 地址智能分离规则：
        如果地址包含公寓/单元信息，请提供两个版本：
        - full_address: 完整地址（包含公寓号）
        - address: 纯街道地址（不含公寓号）

        🔍 公寓号识别关键词：
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - 单独的 # 号码
        - 示例: "500 King Dr, Apt 105" → full: "500 King Dr, Apt 105, Daly City, CA, 94015", street: "500 King Dr, Daly City, CA, 94015"

        ⚡ 地址格式示例：
        - 简称优先: "123 Main St, San Jose, CA, 95112"
        - 备选全称: "123 Main Street, San Jose, CA, 95112"

        You MUST respond with ONLY valid JSON in this exact format:
        {
            "success": true,
            "deliveries": [
                {
                    "sort_number": "number_from_image",
                    "tracking_number": "CNUSUP_code_or_empty",
                    "address": "street_address_or_empty",
                    "full_address": "complete_address_with_apt_or_empty",
                    "customer": "customer_name_or_empty"
                }
            ]
        }

        注意：如果没有公寓号，full_address和address字段内容相同。

        If you cannot see any delivery information clearly, return: {"success": false, "deliveries": []}
        """
    }

    // PIGGY专用提示词
    private func createPiggyPrompt() -> String {
        return """
        📦 PIGGY 快递识别 - 智能地址分离提取：

        🎯 核心信息：
        1. 排序号：连续序号 (54, 55, 56, 57, 58, 59, 60, 61...) - 红色背景中的数字
        2. 追踪号：两种格式
           - PG + 11位数字 (如: PG10005375906, PG10005376399, PG10005433664)
           - 14位纯数字 (如: 20000060727717, 20000061579923, 20000060813327)
        3. 地址：智能分离公寓号码
        4. 位置信息：主要在Concord CA，邮编94520/94518

        🏠 地址智能分离规则：
        如果地址包含公寓/单元信息，请提供两个版本：
        - full_address: 完整地址（包含公寓号）
        - address: 纯街道地址（不含公寓号）

        🔍 公寓号识别关键词：
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - 单独的 # 号码
        - 示例: "1530 Ellis Street Apt. 309" → full: "1530 Ellis Street Apt. 309, Concord, CA, 94520", street: "1530 Ellis Street, Concord, CA, 94520"

        ⚡ 地址格式示例：
        - 简称优先: "1731 Ellis St, Concord, CA, 94520"
        - 备选全称: "1731 Ellis Street, Concord, CA, 94520"

        🎯 PIGGY界面特征：
        - 中文界面（显示"导航"按钮）
        - 白色背景列表界面
        - 左侧红色背景显示排序号
        - 地址格式：美国地址，主要在Concord CA
        - 追踪号显示在地址下方（粉红色文字）

        CRITICAL: Look for the actual information in the image. In PIGGY screenshots, you will see:
        - Sequential numbers like: 54, 55, 56, 57, 58, 59, 60, 61, 62 (these are in red background)
        - Two tracking number formats as described above
        - Addresses like: "1530 Ellis Street Apt. 309", "1731 Ellis St Apt 54", "1441 Detroit Ave Apt 263"
        - Location: Concord CA with zip codes 94520, 94518

        IMPORTANT RULES:
        - Extract ONLY what you can actually see in the image
        - PIGGY uses simple sequential numbers (54, 55, 56...) as sort numbers
        - The interface may be in Chinese (showing "导航")
        - For "address" field: Include ONLY the street address and city, NO tracking numbers
        - If no apartment info, only return address field

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "1530 Ellis Street, Apt. 309" not "1530 ELLIS STREET APT. 309"
        - Format unit/apartment info correctly: "1530 Ellis Street, Apt. 309" not "1530 Ellis Street APT. 309"
        - Include city and state if visible: "Concord, CA"
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        You MUST respond with ONLY valid JSON in this exact format:
        {
            "success": true,
            "deliveries": [
                {
                    "sort_number": "exact_number_from_red_background",
                    "tracking_number": "exact_PG_or_pure_number_or_empty",
                    "address": "properly_formatted_address_or_empty",
                    "customer": "customer_name_if_visible_or_empty",
                    "full_address": "full_address_with_unit_if_applicable"
                }
            ]
        }

        If you cannot see any delivery information clearly, return: {"success": false, "deliveries": []}
        """
    }

    // UNIUNI专用提示词
    private func createUniUniPrompt() -> String {
        return """
        📦 UNIUNI 快递识别 - 智能地址分离提取：

        🎯 核心信息：
        1. 路线号：三位数字 (149, 150, 151, 152等) - 左侧大号数字
        2. 追踪号：UUS + 16位数字 (如: UUS56D056436296171)
        3. 收件人：完整英文姓名 (Faith quick, Allan Zehnder, Lelsie Drinkwater, Cynthia Baker)
        4. 地址：智能分离公寓号码

        🏠 地址智能分离规则：
        如果地址包含公寓/单元信息，请提供两个版本：
        - full_address: 完整地址（包含公寓号）
        - address: 纯街道地址（不含公寓号）

        公寓号识别关键词：
        - Apt, Apartment
        - Unit, Suite, Room, Rm, Ste
        - 单独的 # 号码

        示例：
        输入: "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA, 92223"
        输出:
        - address: "38623 ORCHARD ST, CHERRY VALLEY, CA, 92223"
        - full_address: "38623 ORCHARD ST, Apt 105, CHERRY VALLEY, CA, 92223"

        🌟 界面特征：
        - 中文界面（显示"路线号"）
        - 白色背景，简洁卡片布局
        - 左侧显示三位数字路线号
        - 右侧显示配送详情
        - 地址格式：美国加州地址为主
        - 提醒信息："Suspected Apt/Suspecté Apt..."

        CRITICAL: 提取图片中实际可见的信息
        - 路线号使用三位数字格式 (149, 150, 151...)
        - 追踪号格式：UUS + 16位数字
        - 地址必须是英文格式，不包含追踪号
        - 如果没有公寓号，只返回address字段

        IMPORTANT RULES:
        - Extract ONLY what you can actually see in the image
        - UNIUNI uses three-digit numbers (105, 106, 107...) as sort numbers
        - The interface may be in Chinese (showing "路线号", "配送中", "配送失败")
        - For "address" field: Include ONLY the street address and city, NO tracking numbers

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        - Use proper capitalization: "386 E 8th Street" not "386 E 8TH ST"
        - Format unit/apartment info correctly: "386 E 8th Street, Apt. 2" not "386 E 8th Street APT. 2"

        CRITICAL SINGLE-LINE ADDRESS HANDLING:
        - When you see single-line format like "10255 Live Oak Ave CHERRY VALLEY CA"
        - ALWAYS recognize that the last 2-letter word is the STATE (CA, NY, TX, etc.)
        - The words before the state are the CITY NAME (even if multiple words like "CHERRY VALLEY")
        - NEVER drop the state abbreviation during formatting
        - Example: "10255 Live Oak Ave CHERRY VALLEY CA" → "10255 Live Oak Avenue, Cherry Valley, CA"
        - Example: "386 E 8th St PITTSBURG CA" → "386 E 8th Street, Pittsburg, CA"

        You MUST respond with ONLY valid JSON in this exact format:
        {
            "success": true,
            "deliveries": [
                {
                    "third_party_sort": "exact_number_from_left_side",
                    "tracking_number": "exact_UUS_tracking_number_or_empty",
                    "address": "properly_formatted_address_or_empty",
                    "customer": "customer_name_if_visible_or_empty"
                }
            ]
        }

        If you cannot see any delivery information clearly, return: {"success": false, "deliveries": []}
        """
    }

    // GoFo delivery recognition prompt
    private func createGoFoPrompt() -> String {
        return """
        📦 GoFo Delivery Recognition - Extract ONLY what is actually shown in the image:

        🎯 Core Information:
        1. Sort Number: Extract the EXACT numeric sort number shown on the left side of each delivery item (e.g., 15, 16, 17, 18, 112, 113...)
        2. Address: Extract ONLY the address text shown in the image, do not add any extra information
        3. Tracking Number: GF + 12 digits (e.g., GF611244756320)
        4. Customer Name: Full name

        🚨🚨🚨 CRITICAL ADDRESS RULES - DELIVERY ACCURACY DEPENDS ON THIS 🚨🚨🚨

        ⚠️ ZIP CODES ARE CRITICAL FOR DELIVERY SUCCESS:
        - Wrong ZIP code = Wrong delivery location = Failed delivery = Customer complaint
        - ZIP codes determine the exact delivery area and route
        - Even one digit wrong can send packages to completely different cities

        🔒 ABSOLUTE ZIP CODE REQUIREMENTS:
        - 🚫 NEVER modify ZIP codes - use EXACTLY what appears in the image
        - 🚫 NEVER "complete" or "fix" ZIP codes - if image shows "95650", use "95650"
        - 🚫 NEVER generate variations - DO NOT create "95648", "95658", "95663" etc.
        - ✅ ONLY use ZIP codes that are explicitly visible in the image
        - ✅ If all addresses share same ZIP in image, they should ALL have that same ZIP

        📍 ADDRESS EXTRACTION RULES:
        - Extract the address text EXACTLY as displayed in the image
        - 🚫 DO NOT add missing information (city, state, etc.) - use only what is visible
        - 🚫 DO NOT "complete" or "standardize" addresses - extract exactly what you see
        - 🚫 DO NOT modify any part of the address except removing country suffixes
        - ✅ ONLY EXCEPTION: Remove country suffixes if present ("USA", "US", "United States")
        - ✅ Keep everything else exactly as shown in the image

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: If image shows "10624 Pleasant Valley Circ,95209", return exactly "10624 Pleasant Valley Circ,95209", do NOT add city/state information

        🎯 GoFo Interface Features:
        - Map background interface with blue circle markers
        - Left-side numeric sort numbers (extract EXACT numbers shown, e.g., 15, 16, 17, 18, 112, 113...)
        - Address display format: "Street Address, Zipcode"
        - Tracking number format: GF + 12 digits
        - Customer name below address
        - May display Chinese interface text

        🔍 CRITICAL EXTRACTION REQUIREMENTS:
        - ALWAYS extract the EXACT left-side numeric sort numbers as they appear (DO NOT reassign or renumber)
        - These numbers are MANDATORY for GoFo delivery operations and must match the original third-party sort numbers
        - If you cannot find a sort number, mark as "missing"

        🚨 COMPLETE TASK VALIDATION:
        - ONLY extract deliveries that have: Address + Sort Number
        - If ANY of these 2 required elements is missing, SKIP that task entirely
        - Video recording may show same addresses multiple times due to scrolling
        - If you see the same address with same sort number, only include it ONCE
        - Each sort number must be unique across all deliveries

        🔍 DUPLICATE PREVENTION:
        - Check for duplicates: if same address + same sort number already exists, skip
        - No duplicate address + sort number combinations
        - If you find duplicates, remove them and keep only one instance

        ⚡ Simplified recognition, focus on core information

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_from_interface", "tracking_number": "gofo_tracking_if_visible", "address": "formatted_address", "customer": "customer_name_if_visible", "delivery_time": "time_if_visible"}]}

        Do not include any text before or after the JSON.
        """
    }

    // SpeedX专用提示词 - 增强停靠点识别
    private func createSpeedXPrompt() -> String {
        return """
        SpeedX Delivery Recognition:

        🚨 STOP NUMBER DETECTION (HIGHEST PRIORITY):
        - CRITICAL: Look for "停靠点: XX" text at the BOTTOM RIGHT of each delivery block
        - This is the MOST IMPORTANT information - every delivery MUST have a stop number
        - Format examples: "停靠点: 46", "停靠点: 47", "停靠点: 48", "停靠点: 49", "停靠点: 50"
        - Extract ONLY the number part (e.g., "46", "47", "48", "49", "50")
        - DO NOT generate sequential numbers (1,2,3,4,5) - use the ACTUAL numbers shown
        - If you cannot find the stop number for a delivery, SKIP that delivery entirely
        - STEP 1: First scan the entire image for ALL "停靠点: XX" texts
        - STEP 2: Then match each stop number to its corresponding delivery block

        Extract delivery information from SpeedX app. Each delivery has:
        - Address (left side, 1-2 lines)
        - Customer name (right side, blue text)
        - Tracking number (bottom left, starts with SPXSF)
        - Stop number (bottom right, format: 停靠点: X)

        🚨 CRITICAL ADDRESS SEPARATION RULES - ABSOLUTELY MANDATORY:
        - 🚫 NEVER EVER include customer names in the "address" field
        - 🚫 NEVER EVER include recipient names in the "address" field
        - 🚫 NEVER EVER include personal names in the "address" field
        - ✅ Address field MUST ONLY contain: Street Number + Street Name + City + State
        - ✅ Customer names go ONLY in the separate "customer" field
        - ✅ Example CORRECT address: "1288 S Mayfair Ave, Daly City, CA"
        - ❌ Example WRONG address: "1288 S Mayfair Ave, Daly City, CA Myat Noe"
        - ❌ Example WRONG address: "1059 Wildwood Ave, Daly City, CA Inna Belyaev"

        CRITICAL REQUIREMENTS:
        1. Each delivery is separated by blue left border
        2. 🚨 CRITICAL: Extract ONLY the ACTUAL number from "停靠点: X" (e.g., from "停靠点: 46" return "46")
        3. 🚫 NEVER generate sequential numbers (1,2,3,4,5) - use REAL stop numbers from image
        4. ✅ Example: If image shows "停靠点: 46", use "46" not "1"
        5. Each stop number must be unique - no duplicates
        6. 🚨 CRITICAL PAIRING: Match information within the EXACT SAME task block
        7. 🚫 CRITICAL: NEVER include country in addresses - DO NOT add "USA", "US", "United States"
        8. For video frames: scan entire image carefully for all stop numbers first

        🚨 COMPLETE TASK VALIDATION:
        - ONLY extract deliveries that have: Address + Stop Number + Tracking Number
        - If ANY of these 3 required elements is missing, SKIP that delivery entirely
        - Screenshot may show same addresses multiple times due to scrolling
        - If you see the same address with same stop number, only include it ONCE

        🔍 SEQUENCE CONTINUITY CHECK:
        - Check for missing stop numbers in sequence (e.g., if you see 1,2,4,5 then 3 is missing)
        - If stop numbers are not continuous, note the gaps in your response
        - 🚨 INCOMPLETE DATA DETECTION: If you see truncated/cut-off delivery information, mark it as incomplete
        - Look for partial addresses, cut-off tracking numbers, or missing stop number displays
        - If delivery information appears to be cut off at image boundaries, flag it as "incomplete_data"

        🔍 DUPLICATE PREVENTION:
        - Check for duplicates: if same address + same stop number already exists, skip
        - No duplicate address + stop number combinations
        - If you find duplicates, remove them and keep only one instance

        🚨 STOP NUMBER PAIRING RULES:
        - Stop numbers are THE MOST IMPORTANT data to extract
        - Look carefully in bottom right corner of each task block
        - MUST pair the stop number with the address in the SAME visual block
        - If address is in block A and stop number is in block A, they belong together
        - NEVER mix stop numbers from one block with addresses from another block
        - If you cannot find a stop number, do not create that delivery entry
        - Better to miss a delivery than create one with wrong stop number pairing

        🔍 STEP-BY-STEP EXTRACTION PROCESS:
        1. First, scan the entire image and identify ALL blue-bordered task blocks
        2. For each block, locate the "停靠点: X" text in the bottom right corner
        3. Extract the number X from that specific block
        4. Extract the address from the SAME block (left side)
        5. Verify each stop number is unique - NO DUPLICATES ALLOWED
        6. If you find duplicate stop numbers, re-examine the image more carefully

        🚨 FINAL VALIDATION BEFORE RESPONSE:
        Before sending your JSON response, verify:
        1. No duplicate third_party_sort values (each must be unique)
        2. Each delivery has complete information: Address + Stop Number + Tracking Number
        3. All stop numbers are different (e.g., 1, 2, 3, 4 NOT 1, 1, 2, 3)
        4. If ANY delivery is missing required elements, remove it entirely
        5. If you find duplicates, remove them and keep only one instance

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_only", "tracking_number": "SPXSF_tracking_number", "address": "formatted_address", "customer": "customer_name"}], "sequence_gaps": ["missing_stop_numbers"], "incomplete_data": ["stop_numbers_with_incomplete_info"]}

        Do not include any text before or after the JSON.
        """
    }

    // YWE专用提示词
    private func createYWEPrompt() -> String {
        return """
        📦 YWE 快递识别 - 智能地址分离提取：

        🎯 核心信息：
        1. 排序号：# + 数字 (#1, #2, #3, #4...)
        2. 运单号：YWAUS + 15位数字 (如: YWAUS010000147255)
        3. 收件人：完整英文姓名
        4. 地址：智能分离公寓号码

        🏠 地址智能分离规则：
        如果地址包含公寓/单元信息，请提供两个版本：
        - full_address: 完整地址（包含公寓号）
        - address: 纯街道地址（不含公寓号）

        🔍 公寓号识别关键词：
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - 单独的 # 号码
        - 示例: "14801 Ronald W Reagan Blvd Apt 9109" → full: "14801 Ronald W Reagan Blvd Apt 9109, Leander, TX, 78641", street: "14801 Ronald W Reagan Blvd, Leander, TX, 78641"

        ⚡ 地址格式示例：
        - 简称优先: "2200 Cabrillo Path, Leander, TX, 78641"
        - 备选全称: "2200 Cabrillo Path, Leander, Texas, 78641"

        🎯 YWE界面特征：
        - 中文界面：派送任务、收件人、地址、派送成功
        - 白色背景列表界面，卡片式布局
        - 左侧 # 排序号，右侧绿色"派送成功"状态
        - 黄色"派送图片待核验"警告标识
        - 运单号格式：YWAUS + 15位数字
        - 收件人：完整英文姓名
        - 地址：美国地址格式，可能包含公寓号

        ⚡ 简化识别，专注核心信息

        IMPORTANT RULES:
        - Extract ONLY what you can actually see in the image
        - For "address" field: Include ONLY the street address and city, NO tracking numbers
        - Focus on extracting complete, accurate addresses
        - YWE interface may be in Chinese or English
        - Pay attention to any YWE-specific UI elements and numbering systems

        ADDRESS FORMATTING REQUIREMENTS:
        - Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road
        - Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        - Format unit/apartment info correctly: "1721 Marina Court, Apt D"
        - Include city and state if visible: "San Mateo, CA"
        - 🚨 STATE ABBREVIATION MANDATORY: Always include state (CA, NY, TX, UT, etc.) for US addresses

        🚨 US DIRECTIONAL RULES:
        - Look for ALL directional indicators: N, S, E, W, North, South, East, West
        - Standard pattern: "Number Direction Street" (e.g., "836 S Founders Point Ln")
        - NEVER miss directional information when present in the image

        Return JSON format:
        {
            "success": true,
            "deliveries": [
                {
                    "sort_number": "number_from_interface_or_empty",
                    "tracking_number": "ywe_tracking_if_visible_or_empty",
                    "address": "formatted_address",
                    "customer": "customer_name_if_visible_or_empty"
                }
            ]
        }

        If you cannot see any delivery information clearly, return: {"success": false, "deliveries": []}
        """
    }

    // 通用地址提取提示词 - 简化版
    private func createGeneralAddressPrompt() -> String {
        return """
        Extract delivery addresses from this image.

        Look for:
        - Complete addresses (English or Chinese)
        - Unit/apartment numbers (Suite, Unit, Apt, #, 单元, 房间)
        - Customer names
        - Any visible numbers

        US Address format: "Street Number Street Name, Unit (if any), City, State, ZIP"
        Examples:
        - "7865 S Bingham Junction Boulevard, C401, Midvale, UT, 84047"
        - "836 S Founders Point Ln, Midvale, UT, 84047" (correct S direction)
        - "6968 S 700 W, Suite 221, Midvale, UT" (Utah pattern with both directions)

        Chinese addresses: Keep original format and characters

        Return JSON:
        {
            "success": true,
            "deliveries": [
                {
                    "sort_number": "",
                    "address": "formatted_address",
                    "customer": "name_if_visible"
                }
            ]
        }

        If no addresses: {"success": false, "deliveries": []}
        """
    }

    private func createValidationPrompt(_ addresses: [String]) -> String {
        let addressList = addresses.enumerated().map { "\($0.offset + 1). \($0.element)" }.joined(separator: "\n")

        return """
        验证以下配送地址列表，检查格式和完整性。请返回JSON格式结果。

        地址列表：
        \(addressList)

        请返回以下JSON格式：
        {
            "results": [
                {
                    "index": 1,
                    "original_address": "原始地址",
                    "is_valid": true,
                    "confidence": 0.9,
                    "optimized_address": "优化后地址",
                    "issues": ["问题1", "问题2"],
                    "suggestions": ["建议1", "建议2"]
                }
            ],
            "summary": {
                "total_addresses": 3,
                "valid_count": 2,
                "invalid_count": 1
            }
        }
        """
    }

    // 🎯 简化的地址转换提示词 - 专注于清理和标准化
    private func createAddressConversionPrompt(_ addresses: [String], appType: DeliveryAppType = .justPhoto) -> String {
        let addressList = addresses.enumerated().map { "\($0.offset + 1). \($0.element)" }.joined(separator: "\n")

        return """
        Clean and standardize these addresses for delivery app: \(appType.displayName)

        Addresses to clean:
        \(addressList)

        STANDARDIZATION RULES:
        1. Remove duplicate city names (e.g., "SAN MATEO 1715 YORK AVE, SAN MATEO" → "1715 York Avenue, San Mateo, CA")
        2. Combine multi-line addresses into single line
        3. Standardize abbreviations: ST→Street, AVE→Avenue, CT→Court, DR→Drive, LN→Lane, RD→Road, BLVD→Boulevard
        4. Use proper capitalization: "1762 Borden Street" not "1762 BORDEN ST"
        5. Format unit/apartment info correctly: "1721 Marina Court, Apt D" not "1721 Marina Court Apt D"
        6. Format city names: "San Mateo" not "SAN MATEO"
        7. Include state but NOT country: "San Mateo, CA" (never add "USA")
        8. Extract sort codes and tracking numbers if visible
        9. Keep all address components (unit numbers, building names, etc.)
        10. Remove extra spaces and normalize punctuation

        EXAMPLES:
        - "1721 MARINA CT APT D, SAN MATEO" → "1721 Marina Court, Apt D, San Mateo, CA"
        - "1625 Marina CT UNIT F, SAN MATEO" → "1625 Marina Court, Unit F, San Mateo, CA"
        - "2406 Elliott ST, SAN MATEO" → "2406 Elliott Street, San Mateo, CA"

        Return JSON:
        {
            "success": true,
            "converted_addresses": ["properly formatted address 1", "properly formatted address 2"],
            "tracking_numbers": ["", ""],
            "third_party_sort_numbers": ["", ""],
            "confidence": 0.95,
            "app_type": "\(appType.rawValue)"
        }
        """
    }

    private func parseGemmaResponse(_ jsonString: String, appType: DeliveryAppType = .justPhoto) -> (addresses: [String], confidence: Double, detectedAppType: DeliveryAppType?)? {
        Logger.aiDebug("🔍 开始解析Gemma响应，长度: \(jsonString.count)字符")
        Logger.aiInfo("📄 完整AI响应: \(jsonString)")  // 改为info级别，确保能看到

        // 尝试清理JSON字符串
        let cleanedJson = cleanJsonString(jsonString)
        Logger.aiDebug("🧹 清理后JSON: \(cleanedJson)")

        guard let data = cleanedJson.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            Logger.aiError("❌ JSON解析失败")
            return nil
        }

        Logger.aiDebug("✅ JSON解析成功: \(json)")

        // 支持新的简化JSON格式
        let success: Bool
        let addresses: [String]
        let confidence: Double
        let detectedAppType: DeliveryAppType? = nil // 🎯 不再检测应用类型

        // 🔍 处理SpeedX序号连续性和不完整数据检测
        if let sequenceGaps = json["sequence_gaps"] as? [String], !sequenceGaps.isEmpty {
            Logger.aiError("🚨 SpeedX序号缺失警告: 缺少停靠点 \(sequenceGaps.joined(separator: ", "))")
            Logger.aiError("💡 建议: 检查图片是否完整，可能需要重新截图包含缺失的停靠点")
        }

        if let incompleteData = json["incomplete_data"] as? [String], !incompleteData.isEmpty {
            Logger.aiError("🚨 SpeedX不完整数据警告: 停靠点 \(incompleteData.joined(separator: ", ")) 的信息被截断")
            Logger.aiError("💡 建议: 重新截图确保所有配送信息完整显示")
        }

        // 🎯 新的简化格式 - 支持我们的新提示词
        if let successFlag = json["success"] as? Bool,
           let deliveries = json["deliveries"] as? [[String: Any]] {
            success = successFlag
            confidence = 0.9 // 默认置信度

            addresses = deliveries.compactMap { delivery -> String? in
                guard let address = delivery["address"] as? String else { return nil }

                // 🎯 统一使用thirdPartySortNumber字段，与DeliveryPoint模型保持一致
                let thirdPartySortNumber = delivery["third_party_sort"] as? String ?? ""
                let trackingNumber = delivery["tracking_number"] as? String ?? ""
                let customer = delivery["customer"] as? String ?? ""
                let deliveryTime = delivery["delivery_time"] as? String ?? ""
                let fullAddress = delivery["full_address"] as? String ?? ""

                // 🎯 应用特定的地址清理逻辑
                var cleanedAddress = address

                // 🚨 重要：只对特定快递应用地址清理，避免冲突
                switch appType {
                case .gofo:
                    // GoFo地址清理：移除国家后缀
                    if let addressWithoutCountry = AddressSimplifier.removeCountryName(address) {
                        cleanedAddress = addressWithoutCountry
                        Logger.aiDebug("🌍 GoFo地址移除国家: '\(address)' -> '\(cleanedAddress)'")
                    }
                case .speedx:
                    // SpeedX地址优化：智能处理州简称，提高Apple Maps识别准确率
                    cleanedAddress = optimizeSpeedXAddress(address)
                    Logger.aiDebug("🔴 SpeedX地址优化: '\(address)' -> '\(cleanedAddress)'")
                default:
                    // 其他快递保持原样
                    break
                }

                // 🎯 组合信息，使用新的格式
                var result = cleanedAddress

                // 🎯 如果是旧格式（包含"停靠点:"等文字），提取数字部分
                if !thirdPartySortNumber.isEmpty {
                    let cleanedSortNumber: String
                    if thirdPartySortNumber.contains(":") || thirdPartySortNumber.contains("停靠点") {
                        // 旧格式，提取数字部分
                        cleanedSortNumber = extractNumberFromSortNumber(thirdPartySortNumber)
                    } else {
                        // 新格式，直接使用
                        cleanedSortNumber = thirdPartySortNumber
                    }

                    if !cleanedSortNumber.isEmpty {
                        result += "|THIRD_PARTY_SORT:\(cleanedSortNumber)"
                    }
                }

                // 添加tracking number
                if !trackingNumber.isEmpty {
                    result += "|TRACK:\(trackingNumber)"
                }

                // 添加客户名
                if !customer.isEmpty {
                    result += "|CUSTOMER:\(customer)"
                }

                // 🎯 添加时间信息
                if !deliveryTime.isEmpty {
                    result += "|TIME:\(deliveryTime)"
                }

                // 🏠 添加完整地址信息（包含公寓号）
                if !fullAddress.isEmpty && fullAddress != address {
                    result += "|FULL:\(fullAddress)"
                }

                return result
            }
        } else if let ok = json["ok"] as? Bool,
           let deliveries = json["deliveries"] as? [[String: Any]],
           let conf = json["conf"] as? Double {
            // 旧的带追踪信息格式（向后兼容）
            success = ok
            // 🎯 不再检测应用类型，由用户手动选择

            addresses = deliveries.compactMap { delivery -> String? in
                guard let addr = delivery["addr"] as? String else { return nil }
                let track = delivery["track"] as? String ?? ""
                let customer = delivery["customer"] as? String ?? ""

                // 组合所有信息（后续会在创建DeliveryPoint时分离处理）
                var result = addr
                if !track.isEmpty {
                    result += "|TRACK:\(track)"
                }
                if !customer.isEmpty {
                    result += "|CUSTOMER:\(customer)"
                }
                // 🎯 不再添加应用类型标签，由用户手动选择
                return result
            }
            confidence = conf
        } else if let ok = json["ok"] as? Bool,
                  let addrs = json["addrs"] as? [String],
                  let conf = json["conf"] as? Double {
            // 旧的紧凑格式（向后兼容）
            success = ok
            addresses = addrs
            confidence = conf
        } else if let oldSuccess = json["success"] as? Bool,
                  let oldAddresses = json["addresses"] as? [String],
                  let oldConfidence = json["confidence"] as? Double {
            // 最旧格式兼容
            success = oldSuccess
            addresses = oldAddresses
            confidence = oldConfidence
        } else {
            Logger.aiError("❌ JSON结构不符合预期")
            Logger.aiError("📊 实际JSON内容: \(json)")
            return nil
        }

        guard success else {
            Logger.aiError("❌ AI返回失败状态")
            return nil
        }

        // 🚨 检测是否为测试数据
        if isTestData(addresses) {
            Logger.aiError("🚨 检测到AI模型返回测试数据，拒绝使用")
            Logger.aiError("📍 测试数据特征: 包含ROWVILLE地址或通用客户名")
            return nil
        }

        Logger.aiInfo("🎯 解析结果: \(addresses.count)个地址, 置信度: \(confidence)")
        Logger.aiInfo("📋 识别的地址列表:")
        for (index, address) in addresses.enumerated() {
            Logger.aiInfo("  \(index + 1). \(address)")
        }
        return (addresses: addresses, confidence: confidence, detectedAppType: detectedAppType)
    }

    // 🎯 从第三方排序号中提取数字部分
    private func extractNumberFromSortNumber(_ sortNumber: String) -> String {
        // 提取所有数字字符
        let numberString = sortNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return numberString
    }

    // 清理JSON字符串
    private func cleanJsonString(_ jsonString: String) -> String {
        var cleaned = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除可能的markdown代码块标记
        cleaned = cleaned.replacingOccurrences(of: "```json", with: "")
        cleaned = cleaned.replacingOccurrences(of: "```", with: "")

        // 查找第一个{和最后一个}
        if let firstBrace = cleaned.firstIndex(of: "{"),
           let lastBrace = cleaned.lastIndex(of: "}") {
            cleaned = String(cleaned[firstBrace...lastBrace])
        }

        return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 🚨 检测是否为AI模型生成的测试数据或无关内容
    private func isTestData(_ addresses: [String]) -> Bool {
        // 检查是否为空或全部为空字符串
        let validAddresses = addresses.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
        if validAddresses.isEmpty {
            Logger.aiError("🚨 无效数据检测: 所有地址都为空")
            return true
        }

        // 检查重复内容（如果超过80%的内容相同，很可能是错误输出）
        let uniqueAddresses = Set(validAddresses)
        let duplicateRatio = 1.0 - (Double(uniqueAddresses.count) / Double(validAddresses.count))
        if duplicateRatio > 0.8 {
            Logger.aiError("🚨 重复内容检测: \(String(format: "%.1f", duplicateRatio * 100))% 内容重复")
            return true
        }

        let testDataIndicators = [
            // 澳洲ROWVILLE地址（常见测试数据）
            "ROWVILLE",
            "Fulham Road",
            "Amazon Court",
            "amazon crt",
            "rowville",

            // 常见AI生成的客户名（明显的测试数据）
            "New Customer",
            "Another Customer",
            "Yet Another Customer",
            "Test Customer",
            "Sample Customer",
            "Margaret johns",
            "Sandra Tighe",
            "Jenine Gray",
            "Ann-Maree Mudle",
            "John Smith",
            "Jane Doe",

            // 连续的测试地址模式
            "123 Main Street",
            "456 Oak Avenue",
            "789 Pine Lane",
            "101 Elm Drive",
            "222 Maple Street",
            "333 Birch Avenue",
            "444 Cedar Lane",

            // 测试邮编模式
            "3178, Australia", // ROWVILLE邮编
            "3180, Australia", // Springvale
            "3174, Australia", // Noble Park
            "3175, Australia", // Dandenong
            "Victoria,3178,AUS",
            "Victoria, 3178, Australia",

            // 🚨 文件名和无关内容检测
            ".pdf",
            ".doc",
            ".txt",
            ".jpg",
            ".png",
            "回帰分析",
            "regression",
            "analysis",
            "document",
            "file",
            "V1.1",
            "version",
        ]

        // 检查文件名模式（如果包含文件扩展名，很可能是错误输出）
        let fileNamePatterns = [".pdf", ".doc", ".txt", ".jpg", ".png", ".xlsx", ".ppt"]
        let containsFileExtension = validAddresses.contains { address in
            fileNamePatterns.contains { pattern in
                address.localizedCaseInsensitiveContains(pattern)
            }
        }

        if containsFileExtension {
            Logger.aiError("🚨 文件名检测: 检测到文件扩展名，疑似错误输出")
            return true
        }

        // 如果多个地址都包含测试数据特征，很可能是测试数据
        let testDataCount = validAddresses.filter { address in
            testDataIndicators.contains { indicator in
                address.localizedCaseInsensitiveContains(indicator)
            }
        }.count

        // 如果超过50%的地址包含测试数据特征，认为是测试数据
        let testDataRatio = Double(testDataCount) / Double(validAddresses.count)
        let isTest = testDataRatio > 0.5

        if isTest {
            Logger.aiError("🚨 测试数据检测: \(testDataCount)/\(validAddresses.count) 个地址包含测试特征 (比例: \(String(format: "%.1f", testDataRatio * 100))%)")
        }

        return isTest
    }

    private func parseValidationResponse(_ jsonString: String) -> [GemmaValidationResult]? {
        guard let data = jsonString.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let results = json["results"] as? [[String: Any]] else {
            return nil
        }

        return results.compactMap { result in
            guard let originalAddress = result["original_address"] as? String,
                  let isValid = result["is_valid"] as? Bool,
                  let confidence = result["confidence"] as? Double else {
                return nil
            }

            return GemmaValidationResult(
                originalAddress: originalAddress,
                isValid: isValid,
                confidence: confidence,
                optimizedAddress: result["optimized_address"] as? String ?? originalAddress,
                issues: result["issues"] as? [String] ?? [],
                suggestions: result["suggestions"] as? [String] ?? []
            )
        }
    }

    private func parseAddressConversionResponse(_ jsonString: String) -> [String]? {
        Logger.aiDebug("🔍 开始解析地址转换响应，长度: \(jsonString.count)字符")
        Logger.aiDebug("📄 转换响应: \(jsonString)")

        // 尝试清理JSON字符串
        let cleanedJson = cleanJsonString(jsonString)
        Logger.aiDebug("🧹 清理后转换JSON: \(cleanedJson)")

        guard let data = cleanedJson.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            Logger.aiError("❌ 转换JSON解析失败")
            return nil
        }

        Logger.aiDebug("✅ 转换JSON解析成功: \(json)")

        guard let success = json["success"] as? Bool, success,
              let convertedAddresses = json["converted_addresses"] as? [String],
              let confidence = json["confidence"] as? Double else {
            Logger.aiError("❌ 转换JSON结构不符合预期")
            return nil
        }

        // 检查是否有追踪号码信息
        if let trackingNumbers = json["tracking_numbers"] as? [String] {
            Logger.aiDebug("🏷️ 发现追踪号码信息: \(trackingNumbers)")

            // 获取应用类型（如果有的话）
            let appType = json["app_type"] as? String ?? ""

            // 🎯 检查是否有第三方排序号信息
            let thirdPartySortNumbers = json["third_party_sort_numbers"] as? [String] ?? []
            Logger.aiDebug("🏷️ 第三方排序号信息: \(thirdPartySortNumbers)")

            // 将追踪号码和第三方排序号附加到地址上
            let addressesWithTracking = zip(convertedAddresses, trackingNumbers).enumerated().map { index, element in
                let (address, tracking) = element
                var result = address

                // 添加追踪信息
                if !tracking.isEmpty {
                    result += SortNumberConstants.trackingTag(tracking)
                }

                // 🎯 添加第三方排序号信息（如果有的话）
                if index < thirdPartySortNumbers.count && !thirdPartySortNumbers[index].isEmpty {
                    let sortNumber = thirdPartySortNumbers[index]
                    // 🎯 GoFo修复：使用THIRD_PARTY_SORT标记，确保能被正确提取
                    let extractedNumber = extractNumberFromSortNumber(sortNumber)
                    if !extractedNumber.isEmpty {
                        result += SortNumberConstants.thirdPartySortTag(extractedNumber)
                        Logger.aiDebug("🏷️ 添加第三方排序号: \(extractedNumber) (原始: \(sortNumber))")
                    }
                }

                // 🎯 只有真正的第三方应用才添加APP标签，justPhoto不添加
                if !appType.isEmpty && appType != "just_photo" && appType != "manual" {
                    result += "|APP:\(appType)"
                    Logger.aiDebug("🏷️ 添加第三方应用标签: \(appType)")
                } else {
                    Logger.aiDebug("📷 Just Photo识别，不添加第三方应用标签")
                }

                return result
            }

            Logger.aiDebug("🎯 转换解析结果: \(addressesWithTracking.count)个地址（含追踪信息）, 置信度: \(confidence)")
            return addressesWithTracking
        }

        Logger.aiDebug("🎯 转换解析结果: \(convertedAddresses.count)个地址, 置信度: \(confidence)")
        return convertedAddresses
    }

    /// 创建地区和快递类型特定的提示词
    private func createRegionSpecificPrompt(appType: DeliveryAppType) -> String {
        // 特殊处理iMile：根据用户选择的地区来决定提示词
        if appType == .imile {
            return createiMileRegionPrompt()
        }

        switch appType.region {
        case .usa:
            return createUSADeliveryPrompt(appType: appType)
        case .australia:
            return createAustraliaDeliveryPrompt(appType: appType)
        case .universal:
            return createUniversalPrompt(appType: appType)
        }
    }

    /// 美国快递特定提示词
    private func createUSADeliveryPrompt(appType: DeliveryAppType) -> String {
        let baseUSAPrompt = """

        🇺🇸 美国地址识别规则:
        - 地址格式: "Number Street, City, State, Zipcode"
        - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
        - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
        - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
        - 重点识别美国州名缩写 (CA, NY, TX, FL等)
        - 美国邮编格式: 5位数字或5+4位格式
        - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识
        """

        switch appType {
        case .amazonFlex:
            return baseUSAPrompt + """

            AMAZON FLEX 特征:
            - 排序号码: 简单数字 (2, 3, 4, 5, 6, 7, 8等)
            - 追踪号码: Amazon风格追踪号
            - 配送时间: "已预约 3:00 - 8:00 上午 今天" 或 "Scheduled 3:00 - 8:00 AM Today"
            - 重点提取配送时间段信息
            """
        case .gofo:
            // GoFo特殊处理：不添加美国地址格式规则，因为GoFo专用提示词已经明确要求只识别图片中显示的内容
            return ""
        default:
            return baseUSAPrompt
        }
    }

    /// 澳洲快递特定提示词
    private func createAustraliaDeliveryPrompt(appType: DeliveryAppType) -> String {
        let baseAustraliaPrompt = """

        🇦🇺 澳洲地址识别规则:
        - 地址格式: "Unit/Number Street, Suburb, State, Postcode"
        - 示例: "58/15 Fulham Road, Rowville, Victoria, 3178"
        - 重点识别澳洲州名 (Victoria, NSW, QLD, WA, SA等)
        - 澳洲邮编格式: 4位数字
        """

        switch appType {
        case .imile:
            return baseAustraliaPrompt + """

            IMILE 特征:
            - 追踪号码: iMile格式追踪号
            - 排序号码: 各种格式的排序号
            - 支持澳洲和美国地址格式
            """
        case .ldsEpod:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            LDS EPOD 特征:
            - 追踪号码: CNUSUP + 11位数字 (如: CNUSUP00011738482)
            - 排序号码: 连续序号
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .piggy:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            PIGGY 特征:
            - 追踪号码: PG + 11位数字 或 14位纯数字 (如: PG10005375906)
            - 排序号码: 连续序号
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .uniuni:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            UNIUNI 特征:
            - 追踪号码: UUS + 16位数字 (如: UUS56D056436296171)
            - 路线号码: 三位数字 (149, 150, 151, 152等)
            - 收件人信息: 完整英文姓名
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            - 主要服务加州地区 (CHERRY VALLEY, CA等)
            """
        case .gofo:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            GOFO 特征:
            - 追踪号码: GoFo格式追踪号
            - 配送时间信息
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .ywe:
            return """

            🇺🇸 美国地址识别规则:
            - 地址格式: "Number Street, City, State, Zipcode"
            - 示例: "1721 Marina Ct, San Mateo, CA, 94403"
            - 街道类型使用简称 (与Apple Maps一致): St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - 如果简称无效也支持全称: Street, Avenue, Road, Drive, Court, Lane, Boulevard, Place, Way
            - 重点识别美国州名缩写 (CA, NY, TX, FL等)
            - 美国邮编格式: 5位数字或5+4位格式
            - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识

            YWE 特征:
            - 追踪号码: YWAUS + 15位数字 (如: YWAUS010000147255)
            - 排序号码: # + 数字 (#1, #2, #3, #4...)
            - 收件人信息: 完整英文姓名
            - 美国地址格式，支持智能公寓号分离
            - 中文界面但地址为英文格式
            """
        case .speedx:
            return """

            US Address Format Rules:
            - Format: "Number Street, City, State, Zipcode"
            - Example: "1721 Marina Ct, San Mateo, CA, 94403"
            - Street types: St, Ave, Rd, Dr, Ct, Ln, Blvd, Pl, Way
            - State abbreviations: CA, NY, TX, FL, etc.
            - ZIP code: 5 digits or 5+4 format
            - 🚫 CRITICAL: NEVER add country suffixes - DO NOT include "USA", "US", "United States" in addresses

            SPEEDX Features:
            - Tracking: SPXSF + 14 digits (e.g., SPXSF00567490961577)
            - Stop numbers: 停靠点: X (X is digit, MANDATORY for every delivery)
            - Address format: US format, remove country suffixes
            - Interface: Chinese UI but English addresses
            - CRITICAL: Every delivery must have stop number or skip that delivery
            """
        default:
            return baseAustraliaPrompt
        }
    }

    /// 通用提示词
    private func createUniversalPrompt(appType: DeliveryAppType) -> String {
        switch appType {
        case .justPhoto:
            return """

            📸 通用图片识别:
            - 自动检测地址格式 (美国/澳洲/其他)
            - 提取所有可见的地址信息
            - 识别排序号码和追踪号码
            - 适应各种快递应用界面
            """
        default:
            return ""
        }
    }

    /// iMile特殊地区处理提示词
    private func createiMileRegionPrompt() -> String {
        return """

        🌍 iMile 多地区服务:
        - 自动识别地址格式 (美国/澳洲)
        - 美国地址格式: "Number Street, City, State, Zipcode"
        - 澳洲地址格式: "Unit/Number Street, Suburb, State, Postcode"

        IMILE 特征:
        - 追踪号码: iMile格式追踪号
        - 排序号码: 各种格式的排序号
        - 支持美国和澳洲地址格式自动识别
        - 根据邮编判断地区: 5位数字(美国) vs 4位数字(澳洲)
        """
    }
}

// MARK: - Gemma专用错误
enum GemmaError: Error, LocalizedError {
    case imageProcessingFailed
    case networkError
    case apiError(Int)
    case rateLimitExceeded
    case allGemmaModelsFailed
    case jsonParsingFailed
    case geographicRestriction
    case imageTooLarge
    case duplicateProcessing

    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed:
            return "图片处理失败"
        case .networkError:
            return "网络连接错误，请检查网络设置"
        case .apiError(let code):
            return "Gemma API错误 (代码: \(code))"
        case .rateLimitExceeded:
            return "Gemma模型调用频率超限，请稍后重试"
        case .allGemmaModelsFailed:
            return "所有Gemma模型都不可用，请检查网络连接或稍后重试"
        case .jsonParsingFailed:
            return "JSON解析失败"
        case .geographicRestriction:
            return "Gemma模型在当前地区不可用，建议使用OCR模式"
        case .imageTooLarge:
            return "图片过大，请尝试压缩图片或使用OCR模式"
        case .duplicateProcessing:
            return "检测到重复处理，已跳过相同图片"
        }
    }
}
