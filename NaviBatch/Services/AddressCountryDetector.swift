import Foundation
import CoreLocation

/// 地址国家检测服务
/// 用于智能识别地址所属国家，支持全球地址验证
class AddressCountryDetector {

    /// 国家信息结构
    struct CountryInfo {
        let code: String
        let name: String
        let locale: Locale
        let postalCodePattern: String
        let searchRadius: CLLocationDistance

        static let australia = CountryInfo(
            code: "AU",
            name: "Australia",
            locale: Locale(identifier: "en_AU"),
            postalCodePattern: "\\b\\d{4}\\b",
            searchRadius: 50000
        )

        static let usa = CountryInfo(
            code: "US",
            name: "United States",
            locale: Locale(identifier: "en_US"),
            postalCodePattern: "\\b\\d{5}(-\\d{4})?\\b",
            searchRadius: 100000
        )

        static let hongkong = CountryInfo(
            code: "HK",
            name: "Hong Kong",
            locale: Locale(identifier: "en_HK"),
            postalCodePattern: "", // 香港不使用邮编
            searchRadius: 10000
        )

        static let canada = CountryInfo(
            code: "CA",
            name: "Canada",
            locale: Locale(identifier: "en_CA"),
            postalCodePattern: "\\b[A-Z]\\d[A-Z]\\s?\\d[A-Z]\\d\\b",
            searchRadius: 100000
        )

        static let uk = CountryInfo(
            code: "GB",
            name: "United Kingdom",
            locale: Locale(identifier: "en_GB"),
            postalCodePattern: "\\b[A-Z]{1,2}\\d[A-Z\\d]?\\s?\\d[A-Z]{2}\\b",
            searchRadius: 50000
        )
    }

    /// 检测地址所属国家
    /// - Parameter address: 地址字符串
    /// - Returns: 检测到的国家信息，如果无法确定则返回nil
    static func detectCountry(from address: String) -> CountryInfo? {
        let lowercaseAddress = address.lowercased()

        // 🔍 调试日志：记录检测过程
        Logger.info("🌍 开始地址国家检测: '\(address)'", type: .location)

        // 1. 检查明确的国家名称
        if let country = detectExplicitCountry(lowercaseAddress) {
            Logger.info("✅ 通过国家名称检测到: \(country.name) (\(country.code))", type: .location)
            return country
        }

        // 2. 检查特征性城市名称
        if let country = detectByCity(lowercaseAddress) {
            Logger.info("✅ 通过城市名称检测到: \(country.name) (\(country.code))", type: .location)
            return country
        }

        // 3. 检查邮编格式
        if let country = detectByPostalCode(address) {
            Logger.info("✅ 通过邮编格式检测到: \(country.name) (\(country.code))", type: .location)
            return country
        }

        // 4. 检查地址格式特征
        if let country = detectByAddressFormat(lowercaseAddress) {
            Logger.info("✅ 通过地址格式检测到: \(country.name) (\(country.code))", type: .location)
            return country
        }

        Logger.warning("⚠️ 无法检测地址国家，将使用全球策略: '\(address)'", type: .location)
        return nil // 无法确定，使用全球策略
    }

    /// 检测明确的国家名称
    private static func detectExplicitCountry(_ address: String) -> CountryInfo? {
        if address.contains("australia") || address.contains("澳大利亚") {
            return .australia
        }
        if address.contains("united states") || address.contains("usa") || address.contains("美国") {
            return .usa
        }
        if address.contains("hong kong") || address.contains("hongkong") || address.contains("香港") {
            return .hongkong
        }
        if address.contains("canada") || address.contains("加拿大") {
            return .canada
        }
        if address.contains("united kingdom") || address.contains("england") || address.contains("英国") {
            return .uk
        }
        return nil
    }

    /// 通过城市名称检测国家
    private static func detectByCity(_ address: String) -> CountryInfo? {
        // 澳大利亚城市
        let australianCities = ["melbourne", "sydney", "brisbane", "perth", "adelaide", "darwin", "hobart", "canberra", "glen waverley", "clayton", "oakleigh"]
        if australianCities.contains(where: { address.contains($0) }) {
            return .australia
        }

        // 美国城市（添加更多加州城市）
        let usCities = [
            "new york", "los angeles", "chicago", "houston", "phoenix", "philadelphia",
            "san antonio", "san diego", "dallas", "san jose", "cherry valley",
            "stockton", "daly city", "san mateo", "san francisco", "oakland",
            "sacramento", "fresno", "long beach", "santa ana", "anaheim",
            "bakersfield", "riverside", "santa clarita", "chula vista", "irvine"
        ]
        if usCities.contains(where: { address.contains($0) }) {
            return .usa
        }

        // 香港地区
        let hkAreas = ["tsuen wan", "荃湾", "central", "中环", "causeway bay", "铜锣湾", "mong kok", "旺角", "sha tin", "沙田", "kwun tong", "观塘"]
        if hkAreas.contains(where: { address.contains($0) }) {
            return .hongkong
        }

        // 加拿大城市
        let canadianCities = ["toronto", "vancouver", "montreal", "calgary", "ottawa", "edmonton", "winnipeg", "quebec"]
        if canadianCities.contains(where: { address.contains($0) }) {
            return .canada
        }

        // 英国城市
        let ukCities = ["london", "manchester", "birmingham", "glasgow", "liverpool", "leeds", "sheffield", "edinburgh"]
        if ukCities.contains(where: { address.contains($0) }) {
            return .uk
        }

        return nil
    }

    /// 通过邮编格式检测国家
    private static func detectByPostalCode(_ address: String) -> CountryInfo? {
        // 🚨 重要：按照从具体到通用的顺序检测，避免误匹配

        // 🇺🇸 特殊处理：常见美国邮编（特别是加州）
        let commonUSZipCodes = [
            "95209", "94015", "94403", "94401", "94402", "94404", // 加州常见邮编
            "90210", "90211", "90212", // 洛杉矶
            "10001", "10002", "10003", // 纽约
            "60601", "60602", "60603", // 芝加哥
            "77001", "77002", "77003"  // 休斯顿
        ]

        for zipCode in commonUSZipCodes {
            if address.contains(zipCode) {
                return .usa
            }
        }

        // 美国：5位数字或5+4格式（优先检测，避免被4位数字模式误匹配）
        if address.range(of: CountryInfo.usa.postalCodePattern, options: .regularExpression) != nil {
            return .usa
        }

        // 加拿大：字母数字字母 数字字母数字格式
        if address.range(of: CountryInfo.canada.postalCodePattern, options: .regularExpression) != nil {
            return .canada
        }

        // 英国：复杂的邮编格式
        if address.range(of: CountryInfo.uk.postalCodePattern, options: .regularExpression) != nil {
            return .uk
        }

        // 澳大利亚：4位数字（最后检测，避免误匹配5位数字的美国邮编）
        if address.range(of: CountryInfo.australia.postalCodePattern, options: .regularExpression) != nil {
            return .australia
        }

        return nil
    }

    /// 通过地址格式特征检测国家
    private static func detectByAddressFormat(_ address: String) -> CountryInfo? {
        // 香港地址特征：楼层+室号格式
        if address.contains("楼") && address.contains("室") {
            return .hongkong
        }
        if address.range(of: "\\d+/f|floor \\d+", options: .regularExpression) != nil {
            return .hongkong
        }

        // 香港地址特征：号+楼+室的组合
        if address.range(of: "\\d+号.*楼.*室", options: .regularExpression) != nil {
            return .hongkong
        }

        // 香港地址特征：道路名称模式
        let hkRoadPatterns = ["道", "街", "路", "里", "坊", "徑", "巷"]
        if hkRoadPatterns.contains(where: { address.contains($0) }) && address.contains("号") {
            return .hongkong
        }

        // 澳大利亚州缩写
        let ausStates = ["vic", "nsw", "qld", "wa", "sa", "tas", "act", "nt"]
        if ausStates.contains(where: { address.contains($0) }) {
            return .australia
        }

        // 美国州缩写（部分常见的）- 修复检测逻辑
        let usStates = ["ca", "ny", "tx", "fl", "il", "pa", "oh", "ga", "nc", "mi", "wa", "or", "az", "co", "nv", "ut"]
        for state in usStates {
            // 检查州缩写是否作为独立单词存在（支持逗号分隔）
            let patterns = [
                "\\b\(state)\\b",           // 独立单词
                ",\\s*\(state)\\s*$",       // 逗号后的州缩写（地址末尾）
                ",\\s*\(state)\\s*,",       // 逗号间的州缩写
                "\\s+\(state)\\s*$"         // 空格后的州缩写（地址末尾）
            ]

            for pattern in patterns {
                if address.range(of: pattern, options: [.regularExpression, .caseInsensitive]) != nil {
                    return .usa
                }
            }
        }

        return nil
    }

    /// 解析香港地址组件
    /// - Parameter address: 香港地址
    /// - Returns: 解析后的地址组件 (streetNumber, streetName, building, floor, room)
    static func parseHongKongAddress(_ address: String) -> (streetNumber: String, streetName: String, building: String, floor: String, room: String) {
        var streetNumber = ""
        var streetName = ""
        var building = ""
        var floor = ""
        var room = ""

        // 香港地址格式：荃湾海盛路3号荃新天地2座15楼A室
        // 模式1: 区域+道路名+号码+建筑名+座+楼+室
        if let match = address.range(of: "([^\\d]*?)([\\d-]+)号([^\\d]*?)(\\d*)座?(\\d*)楼([^\\s]*)室?", options: .regularExpression) {
            let matchedString = String(address[match])

            // 使用正则表达式提取各部分
            let pattern = "([^\\d]*?)([\\d-]+)号([^\\d]*?)(\\d*)座?(\\d*)楼([^\\s]*)室?"
            if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
                let nsString = matchedString as NSString
                let results = regex.matches(in: matchedString, options: [], range: NSRange(location: 0, length: nsString.length))

                if let match = results.first {
                    if match.numberOfRanges >= 7 {
                        let roadArea = nsString.substring(with: match.range(at: 1))
                        streetNumber = nsString.substring(with: match.range(at: 2))
                        building = nsString.substring(with: match.range(at: 3))
                        let tower = nsString.substring(with: match.range(at: 4))
                        floor = nsString.substring(with: match.range(at: 5))
                        room = nsString.substring(with: match.range(at: 6))

                        // 提取街道名（去除区域前缀）
                        if let roadMatch = roadArea.range(of: "[路道街里坊徑巷]", options: .regularExpression) {
                            let roadEndIndex = roadArea.index(roadMatch.upperBound, offsetBy: 0)
                            let fullRoadName = String(roadArea[..<roadEndIndex])

                            // 去除区域前缀（如"荃湾"）
                            let regionPrefixes = ["荃湾", "荃灣", "中环", "中環", "铜锣湾", "銅鑼灣", "旺角", "尖沙咀"]
                            var cleanedRoadName = fullRoadName

                            for prefix in regionPrefixes {
                                if cleanedRoadName.hasPrefix(prefix) {
                                    cleanedRoadName = String(cleanedRoadName.dropFirst(prefix.count))
                                    break
                                }
                            }

                            streetName = cleanedRoadName
                        }

                        // 组合建筑信息
                        if !building.isEmpty {
                            if !tower.isEmpty {
                                building = "\(building)\(tower)座"
                            }
                        }
                    }
                }
            }
        }

        // 如果正则匹配失败，使用简单解析
        if streetNumber.isEmpty {
            // 查找号码
            if let numberMatch = address.range(of: "\\d+号", options: .regularExpression) {
                let numberString = String(address[numberMatch])
                streetNumber = numberString.replacingOccurrences(of: "号", with: "")
            }

            // 查找道路名
            let roadPatterns = ["路", "道", "街", "里", "坊", "徑", "巷"]
            for pattern in roadPatterns {
                if let roadMatch = address.range(of: "[^\\s]*\(pattern)", options: .regularExpression) {
                    let fullRoadName = String(address[roadMatch])

                    // 去除区域前缀（如"荃湾"）
                    let regionPrefixes = ["荃湾", "荃灣", "中环", "中環", "铜锣湾", "銅鑼灣", "旺角", "尖沙咀"]
                    var cleanedRoadName = fullRoadName

                    for prefix in regionPrefixes {
                        if cleanedRoadName.hasPrefix(prefix) {
                            cleanedRoadName = String(cleanedRoadName.dropFirst(prefix.count))
                            break
                        }
                    }

                    streetName = cleanedRoadName
                    break
                }
            }

            // 查找楼层
            if let floorMatch = address.range(of: "\\d+楼", options: .regularExpression) {
                let floorString = String(address[floorMatch])
                floor = floorString.replacingOccurrences(of: "楼", with: "")
            }

            // 查找房间
            if let roomMatch = address.range(of: "\\d*[A-Z]*室", options: .regularExpression) {
                let roomString = String(address[roomMatch])
                room = roomString.replacingOccurrences(of: "室", with: "")
            }
        }

        return (streetNumber: streetNumber, streetName: streetName, building: building, floor: floor, room: room)
    }
}
