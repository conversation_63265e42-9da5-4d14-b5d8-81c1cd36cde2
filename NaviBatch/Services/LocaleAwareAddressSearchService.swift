//
//  LocaleAwareAddressSearchService.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/25.
//  语言环境感知的地址搜索服务 - 解决系统语言影响Apple Maps搜索的问题
//

import Foundation
import MapKit
import CoreLocation

/// 语言环境感知的地址搜索服务
/// 解决简体中文系统下搜索英文地址失效的问题
class LocaleAwareAddressSearchService: NSObject {

    static let shared = LocaleAwareAddressSearchService()

    private override init() {
        super.init()
    }

    // MARK: - 主要搜索方法

    /// 执行语言环境感知的地址搜索 - 直接返回MKMapItem
    /// - Parameters:
    ///   - query: 搜索查询
    ///   - completion: 完成回调，返回MKMapItem数组
    func performSearchWithMapItems(query: String, completion: @escaping ([MKMapItem]) -> Void) {
        Logger.info("🌍 [地址编辑调试] LocaleAwareAddressSearchService开始搜索: '\(query)'", type: .location)

        // 检查查询是否为空
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            Logger.warning("⚠️ [地址编辑调试] 搜索查询为空", type: .location)
            completion([])
            return
        }

        // 预处理地址，移除可能影响搜索的后缀
        Logger.info("🔧 [地址编辑调试] 开始预处理地址", type: .location)
        let processedQuery = preprocessAddress(query)
        Logger.info("🔧 [地址编辑调试] 地址预处理完成: '\(query)' -> '\(processedQuery)'", type: .location)

        // 使用英文语言环境进行搜索
        Logger.info("🇺🇸 [地址编辑调试] 调用performDirectMKLocalSearch", type: .location)
        performDirectMKLocalSearch(processedQuery, completion: completion)
    }

    /// 执行语言环境感知的地址搜索 - 兼容旧接口
    /// - Parameters:
    ///   - query: 搜索查询
    ///   - completion: 完成回调
    func performSearch(query: String, completion: @escaping ([MKLocalSearchCompletion]) -> Void) {
        // 调用新的方法，然后转换结果
        performSearchWithMapItems(query: query) { mapItems in
            // 确保在后台线程进行转换，避免阻塞UI
            DispatchQueue.global(qos: .userInitiated).async {
                let completions = mapItems.compactMap { mapItem in
                    self.convertMapItemToCompletion(mapItem)
                }

                // 回到主线程返回结果
                DispatchQueue.main.async {
                    completion(completions)
                }
            }
        }
    }

    /// 将MKMapItem转换为MKLocalSearchCompletion格式 - 智能处理中文本地化
    private func convertMapItemToCompletion(_ mapItem: MKMapItem) -> MKLocalSearchCompletion? {
        let placemark = mapItem.placemark

        // 构建标题（通常是门牌号和街道名）- 优先使用英文组件
        var titleComponents: [String] = []

        if let subThoroughfare = placemark.subThoroughfare, !containsChineseCharacters(subThoroughfare) {
            titleComponents.append(subThoroughfare)
        }

        if let thoroughfare = placemark.thoroughfare, !containsChineseCharacters(thoroughfare) {
            titleComponents.append(thoroughfare)
        }

        // 如果没有英文街道信息，使用mapItem.name（通常是英文的）
        let title = titleComponents.isEmpty ? (mapItem.name ?? "Unknown Address") : titleComponents.joined(separator: " ")

        // 构建副标题（城市、州、国家）- 智能处理中文本地化
        var subtitleComponents: [String] = []

        // 处理城市名 - 直接使用placemark中的locality（现在通过反向地理编码已经是英文）
        if let locality = placemark.locality {
            subtitleComponents.append(locality)
        }

        // 处理州/省 - 通常州代码是英文的，保留
        if let administrativeArea = placemark.administrativeArea {
            if containsChineseCharacters(administrativeArea) {
                // 中文州名映射（如果需要）
                let chineseToEnglish = [
                    "密苏里州": "MO",
                    "加利福尼亚州": "CA",
                    "纽约州": "NY"
                ]
                let englishState = chineseToEnglish[administrativeArea] ?? administrativeArea
                subtitleComponents.append(englishState)
                Logger.info("🔄 [地址转换] 州名转换: '\(administrativeArea)' -> '\(englishState)'", type: .location)
            } else {
                subtitleComponents.append(administrativeArea)
            }
        }

        // 邮编通常是数字，直接使用
        if let postalCode = placemark.postalCode {
            subtitleComponents.append(postalCode)
        }

        // 处理国家名
        if let country = placemark.country {
            if containsChineseCharacters(country) {
                let chineseToEnglish = [
                    "美国": "United States",
                    "中国": "China",
                    "加拿大": "Canada",
                    "澳大利亚": "Australia",
                    "英国": "United Kingdom",
                    "法国": "France",
                    "德国": "Germany",
                    "日本": "Japan"
                ]
                let englishCountry = chineseToEnglish[country] ?? country
                if englishCountry != "United States" { // 美国地址通常不显示国家
                    subtitleComponents.append(englishCountry)
                }
                Logger.info("🔄 [地址转换] 国家名转换: '\(country)' -> '\(englishCountry)'", type: .location)
            } else if country != "United States" {
                subtitleComponents.append(country)
            }
        }

        let subtitle = subtitleComponents.joined(separator: ", ")

        // 🇨🇳 检查是否包含中文字符
        let fullText = "\(title) \(subtitle)"
        let hasChinese = fullText.range(of: "\\p{Script=Han}", options: .regularExpression) != nil

        if hasChinese {
            // 🔧 修复：对于澳大利亚等非中国地区，不过滤中文本地化结果
            let country = mapItem.placemark.country?.lowercased() ?? ""
            let isNonChineseCountry = !["中国", "china", "中华人民共和国"].contains(country)

            if isNonChineseCountry {
                Logger.info("🌍 [地址编辑调试] 保留非中国地区的中文本地化结果: '\(title)' | '\(subtitle)' | 国家: '\(mapItem.placemark.country ?? "未知")'", type: .location)
                // 继续处理，不过滤
            } else {
                Logger.info("🇨🇳 [地址编辑调试] 过滤掉中国地区的中文结果: '\(title)' | '\(subtitle)'", type: .location)
                return nil
            }
        }

        // 创建模拟的completion对象
        let mockCompletion = MockLocalSearchCompletion()
        mockCompletion.title = title
        mockCompletion.subtitle = subtitle

        // 🎯 存储坐标信息，避免重复地理编码
        mockCompletion.storedCoordinate = mapItem.placemark.coordinate

        Logger.info("🔧 [地址编辑调试] 创建模拟completion: '\(title)' | '\(subtitle)' | 坐标: (\(mapItem.placemark.coordinate.latitude), \(mapItem.placemark.coordinate.longitude))", type: .location)

        return mockCompletion
    }

    /// 执行直接地址搜索（用于获取坐标）
    /// - Parameters:
    ///   - address: 地址字符串
    ///   - completion: 完成回调
    func performDirectSearch(address: String, completion: @escaping (Result<CLLocationCoordinate2D, Error>) -> Void) {
        Logger.info("🎯 开始直接地址搜索: '\(address)'", type: .location)

        // 预处理地址
        let processedAddress = preprocessAddress(address)

        // 创建搜索请求
        let searchRequest = MKLocalSearch.Request()
        searchRequest.naturalLanguageQuery = processedAddress

        // 设置全球搜索区域
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        searchRequest.region = globalRegion

        let search = MKLocalSearch(request: searchRequest)
        search.start { response, error in
            DispatchQueue.main.async {
                if let error = error {
                    Logger.error("🎯 直接搜索失败: \(error.localizedDescription)", type: .location)
                    completion(.failure(error))
                    return
                }

                guard let response = response,
                      let mapItem = response.mapItems.first else {
                    Logger.warning("🎯 直接搜索无结果", type: .location)
                    completion(.failure(AddressSearchError.noResults))
                    return
                }

                let coordinate = mapItem.placemark.coordinate
                Logger.info("🎯 直接搜索成功: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                completion(.success(coordinate))
            }
        }
    }

    // MARK: - 私有方法

    /// 检查字符串是否包含中文字符
    private func containsChineseCharacters(_ text: String) -> Bool {
        return text.range(of: "\\p{Script=Han}", options: .regularExpression) != nil
    }

    /// 从地址中提取邮编 - 改进版，避免误识别门牌号
    private func extractZipCode(from address: String) -> String? {
        // 邮编通常在地址的末尾，可能前面有逗号或空格
        // 匹配模式：逗号或空格后的5位数字（可选4位扩展）
        let zipPattern = "[,\\s]+(\\d{5}(-\\d{4})?)\\s*$"

        if let range = address.range(of: zipPattern, options: .regularExpression) {
            let matchedText = String(address[range])
            // 提取数字部分，移除前面的逗号和空格
            let cleanZip = matchedText.replacingOccurrences(of: "[,\\s]+", with: "", options: .regularExpression)
                .trimmingCharacters(in: .whitespacesAndNewlines)
            return cleanZip
        }

        // 如果上面的模式没匹配到，尝试匹配地址末尾的5位数字
        let simpleZipPattern = "\\b(\\d{5}(-\\d{4})?)\\s*$"
        if let range = address.range(of: simpleZipPattern, options: .regularExpression) {
            return String(address[range]).trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return nil
    }

    /// 根据邮编获取城市和州信息
    private func getCityStateFromZip(_ zipCode: String) -> (city: String, state: String)? {
        // 常见的邮编映射表 - 可以根据需要扩展
        let zipMappings: [String: (city: String, state: String)] = [
            "64030": ("Grandview", "MO"),
            "64111": ("Kansas City", "MO"),
            "64108": ("Kansas City", "MO"),
            "95603": ("Auburn", "CA"),
            "94014": ("Daly City", "CA"),
            "94015": ("Daly City", "CA"),
            "10001": ("New York", "NY"),
            "10002": ("New York", "NY"),
            "90210": ("Beverly Hills", "CA"),
            "77001": ("Houston", "TX"),
            "60601": ("Chicago", "IL")
        ]

        // 提取5位数邮编（忽略+4扩展）
        let baseZip = String(zipCode.prefix(5))

        if let mapping = zipMappings[baseZip] {
            return (city: mapping.city, state: mapping.state)
        }

        return nil
    }

    /// 直接使用MKLocalSearch进行搜索 - 返回MKMapItem
    private func performDirectMKLocalSearch(_ query: String, completion: @escaping ([MKMapItem]) -> Void) {
        Logger.info("🇺🇸 [地址编辑调试] LocaleAwareAddressSearchService开始直接MKLocalSearch: '\(query)'", type: .location)
        Logger.info("🚀 [地址编辑调试] 跳过MKLocalSearchCompleter，直接使用MKLocalSearch避免超时问题", type: .location)

        // 直接使用MKLocalSearch进行搜索
        Task {
            await performDirectMKLocalSearchAsync(query: query, completion: completion)
        }
    }

    /// 直接使用MKLocalSearch进行搜索的异步方法 - 强制英文结果
    private func performDirectMKLocalSearchAsync(query: String, completion: @escaping ([MKMapItem]) -> Void) async {
        Logger.info("🎯 [地址搜索] 开始搜索: '\(query)'", type: .location)

        // 🌍 使用安全的方式设置英文环境 - 不修改系统设置
        Logger.info("🌍 [地址搜索] 使用英文语言环境进行搜索", type: .location)

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = query
        request.resultTypes = [.address]

        // 🌍 根据地址格式智能设置搜索区域
        let searchRegion = createSearchRegionForQuery(query)
        request.region = searchRegion
        Logger.info("🌍 [地址搜索] 设置搜索区域: \(searchRegion.center.latitude), \(searchRegion.center.longitude)", type: .location)

        // 🔍 详细调试信息
        Logger.info("🔍 [地址搜索调试] 搜索请求详情:", type: .location)
        Logger.info("🔍 [地址搜索调试] - 查询: '\(query)'", type: .location)
        Logger.info("🔍 [地址搜索调试] - 结果类型: \(request.resultTypes)", type: .location)
        Logger.info("🔍 [地址搜索调试] - 搜索区域: center=(\(searchRegion.center.latitude), \(searchRegion.center.longitude)), span=(\(searchRegion.span.latitudeDelta), \(searchRegion.span.longitudeDelta))", type: .location)
        Logger.info("🔍 [地址搜索调试] - 系统语言: \(Locale.current.identifier)", type: .location)
        Logger.info("🔍 [地址搜索调试] - 设备区域: \(Locale.current.region?.identifier ?? "未知")", type: .location)

        let search = MKLocalSearch(request: request)

        do {
            let response = try await search.start()
            Logger.info("🔄 [地址搜索] 找到 \(response.mapItems.count) 个原始结果", type: .location)

            // 🔍 详细调试：打印所有原始结果
            for (index, mapItem) in response.mapItems.enumerated() {
                let placemark = mapItem.placemark
                Logger.info("🔍 [地址搜索调试] 原始结果[\(index)]: name='\(mapItem.name ?? "nil")', thoroughfare='\(placemark.thoroughfare ?? "nil")', locality='\(placemark.locality ?? "nil")', country='\(placemark.country ?? "nil")'", type: .location)
            }

            // 🌍 智能处理：将中文本地化结果转换为英文格式（Swift 6并发安全版本）
            let processedResults = await withTaskGroup(of: MKMapItem.self, returning: [MKMapItem].self) { group in
                var results: [MKMapItem] = []
                var conversionsInProgress = 0
                let maxConcurrentConversions = 3

                for mapItem in response.mapItems {
                    let placemark = mapItem.placemark

                    // 🔍 详细调试信息
                    Logger.info("🔍 [地址搜索] 处理结果: \(mapItem.name ?? "Unknown")", type: .location)

                    // 检查是否包含中文
                    let componentsToCheck = [
                        mapItem.name,
                        placemark.name,
                        placemark.thoroughfare,
                        placemark.subThoroughfare,
                        placemark.locality,
                        placemark.subLocality,
                        placemark.administrativeArea,
                        placemark.subAdministrativeArea,
                        placemark.country
                    ].compactMap { $0 }

                    let fullText = componentsToCheck.joined(separator: " ")
                    let hasChinese = fullText.range(of: "\\p{Script=Han}", options: .regularExpression) != nil

                    if hasChinese && conversionsInProgress < maxConcurrentConversions {
                        Logger.info("🔄 [地址搜索] 发现中文本地化结果，尝试转换为英文", type: .location)
                        conversionsInProgress += 1

                        // 🎯 使用TaskGroup进行并发安全的转换
                        group.addTask {
                            if let englishMapItem = await self.convertToEnglishMapItem(mapItem) {
                                return englishMapItem
                            } else {
                                return mapItem // 转换失败，保持原始结果
                            }
                        }
                    } else {
                        if hasChinese {
                            Logger.info("⚠️ [地址搜索] 跳过转换（达到并发限制），保持原始中文结果", type: .location)
                        } else {
                            Logger.info("✅ [地址搜索] 已是英文结果: \(mapItem.name ?? "Unknown")", type: .location)
                        }
                        results.append(mapItem)
                    }
                }

                // 收集TaskGroup的结果
                for await result in group {
                    results.append(result)
                }

                return results
            }

            Logger.info("✅ [地址搜索] 处理完成: \(processedResults.count) 个结果", type: .location)

            await MainActor.run {
                Logger.info("✅ [地址搜索] 处理完成: \(processedResults.count) 个结果", type: .location)

                // 记录前几个结果
                for (index, mapItem) in processedResults.prefix(3).enumerated() {
                    let title = mapItem.name ?? "Unknown"
                    let subtitle = mapItem.placemark.title ?? ""
                    Logger.info("📍 [地址搜索] 结果\(index+1): \(title) | \(subtitle)", type: .location)
                }

                completion(processedResults)
            }

        } catch {

            await MainActor.run {
                Logger.error("❌ [地址搜索] 搜索失败: \(error.localizedDescription)", type: .location)
                completion([])
            }
        }
    }



    /// 备用搜索方法（使用MKLocalSearchCompleter）
    private func performSearchWithCompleter(_ query: String, completion: @escaping ([MKLocalSearchCompletion]) -> Void) {
        Logger.info("🔄 [地址搜索] 使用备用搜索方法: '\(query)'", type: .location)

        let completer = MKLocalSearchCompleter()
        let delegate = LocaleAwareSearchDelegate { results in
            completion(results)
        }

        delegate.setCompleter(completer)
        completer.delegate = delegate
        completer.resultTypes = .address

        // 设置全球搜索区域
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        completer.region = globalRegion

        // 执行搜索
        completer.queryFragment = query

        // 保持对delegate的强引用，防止被释放
        objc_setAssociatedObject(completer, "delegate", delegate, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
    }

    /// 智能预处理地址 - 利用邮编信息提高搜索精度
    private func preprocessAddress(_ address: String) -> String {
        var processed = address

        Logger.info("🔧 [地址预处理] 开始处理: '\(address)'", type: .location)

        // 移除常见的国家后缀
        let countryPatterns = [
            ", USA", " USA", ", United States", " United States",
            ", Australia", " Australia", ", AU", " AU",
            ", Canada", " Canada", ", CA", " CA"
        ]

        for pattern in countryPatterns {
            processed = processed.replacingOccurrences(of: pattern, with: "", options: .caseInsensitive)
        }

        // 🎯 智能处理：如果有邮编，尝试利用邮编信息增强搜索
        if let zipCode = extractZipCode(from: processed) {
            Logger.info("🔧 [地址预处理] 发现邮编: \(zipCode)", type: .location)

            if let cityState = getCityStateFromZip(zipCode) {
                Logger.info("🔧 [地址预处理] 邮编映射成功: \(zipCode) -> \(cityState.city), \(cityState.state)", type: .location)

                // 提取街道部分（移除邮编和可能的城市州信息）
                let streetPart = processed.replacingOccurrences(
                    of: "\\s*,?\\s*\\b\\d{5}(-\\d{4})?\\b.*",
                    with: "",
                    options: .regularExpression
                ).trimmingCharacters(in: .whitespacesAndNewlines)

                // 构建增强的查询：街道 + 城市 + 州
                processed = "\(streetPart), \(cityState.city), \(cityState.state)"
                Logger.info("🔧 [地址预处理] 构建增强查询: '\(processed)'", type: .location)
            } else {
                Logger.info("🔧 [地址预处理] 邮编未在映射表中，保持原始格式", type: .location)
                // 如果邮编不在映射表中，保留完整地址（包括邮编）
                // 不使用AppleMapsAddressFormatter，避免移除邮编
            }
        } else {
            Logger.info("🔧 [地址预处理] 未发现邮编，使用标准格式化", type: .location)
            // 没有邮编时，使用标准格式化
            processed = AppleMapsAddressFormatter.formatForAppleMaps(processed)
        }

        // 清理多余的空格和标点
        processed = processed.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        processed = processed.replacingOccurrences(of: ",\\s*$", with: "", options: .regularExpression)
        processed = processed.trimmingCharacters(in: .whitespacesAndNewlines)

        Logger.info("🔧 [地址预处理] 处理完成: '\(address)' -> '\(processed)'", type: .location)
        return processed
    }

    /// 🎯 根据查询内容和用户区域智能创建搜索区域
    private func createSearchRegionForQuery(_ query: String) -> MKCoordinateRegion {
        let locationManager = LocationManager.shared
        let currentRegion = locationManager.getCurrentRegion().lowercased()

        Logger.info("🌍 [地址搜索] 当前用户区域: \(currentRegion)", type: .location)
        Logger.info("🔍 [地址搜索] 分析查询: '\(query)'", type: .location)

        // 🎯 根据地址格式检测目标国家
        let detectedCountry = detectCountryFromAddress(query)
        Logger.info("🔍 [地址搜索] 检测到的国家: \(detectedCountry ?? "未检测到")", type: .location)

        // 🌍 优先使用检测到的国家，其次使用用户设置的区域
        let targetCountry = detectedCountry ?? currentRegion
        Logger.info("🎯 [地址搜索] 目标搜索国家: \(targetCountry)", type: .location)

        // 根据目标国家设置搜索区域
        let searchRegion: MKCoordinateRegion

        switch targetCountry {
        case "us", "usa", "united states":
            // 美国搜索区域
            searchRegion = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 39.8283, longitude: -98.5795), // 美国中心
                span: MKCoordinateSpan(latitudeDelta: 25.0, longitudeDelta: 50.0)
            )
            Logger.info("🇺🇸 [地址搜索] 使用美国搜索区域", type: .location)

        case "au", "australia":
            // 澳洲搜索区域
            searchRegion = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: -25.2744, longitude: 133.7751), // 澳洲中心
                span: MKCoordinateSpan(latitudeDelta: 30.0, longitudeDelta: 40.0)
            )
            Logger.info("🇦🇺 [地址搜索] 使用澳洲搜索区域", type: .location)

        case "ca", "canada":
            // 加拿大搜索区域
            searchRegion = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 56.1304, longitude: -106.3468), // 加拿大中心
                span: MKCoordinateSpan(latitudeDelta: 35.0, longitudeDelta: 60.0)
            )
            Logger.info("🇨🇦 [地址搜索] 使用加拿大搜索区域", type: .location)

        default:
            // 全球搜索区域（后备选项）
            searchRegion = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
            )
            Logger.info("🌍 [地址搜索] 使用全球搜索区域 (未知国家: \(targetCountry))", type: .location)
        }

        Logger.info("🔍 [地址搜索调试] 最终搜索区域: center=(\(searchRegion.center.latitude), \(searchRegion.center.longitude)), span=(\(searchRegion.span.latitudeDelta), \(searchRegion.span.longitudeDelta))", type: .location)
        return searchRegion
    }

    /// 🔍 从地址字符串检测国家
    private func detectCountryFromAddress(_ address: String) -> String? {
        let lowercaseAddress = address.lowercased()

        // 检测美国邮编格式 (5位数字或5+4格式)
        if address.range(of: "\\b\\d{5}(-\\d{4})?\\b", options: .regularExpression) != nil {
            Logger.info("🔍 [地址检测] 发现美国邮编格式", type: .location)
            return "us"
        }

        // 检测澳洲邮编格式 (4位数字)
        if address.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil {
            // 进一步检查是否包含澳洲州代码
            let australianStates = ["nsw", "vic", "qld", "wa", "sa", "tas", "act", "nt"]
            if australianStates.contains(where: { lowercaseAddress.contains($0) }) {
                Logger.info("🔍 [地址检测] 发现澳洲邮编+州代码格式", type: .location)
                return "au"
            }
        }

        // 检测加拿大邮编格式 (字母数字字母 数字字母数字)
        if address.range(of: "\\b[A-Za-z]\\d[A-Za-z]\\s?\\d[A-Za-z]\\d\\b", options: .regularExpression) != nil {
            Logger.info("🔍 [地址检测] 发现加拿大邮编格式", type: .location)
            return "ca"
        }

        // 检测明确的国家名称
        if lowercaseAddress.contains("usa") || lowercaseAddress.contains("united states") {
            Logger.info("🔍 [地址检测] 发现美国国家名称", type: .location)
            return "us"
        }

        if lowercaseAddress.contains("australia") || lowercaseAddress.contains(" au") {
            Logger.info("🔍 [地址检测] 发现澳洲国家名称", type: .location)
            return "au"
        }

        if lowercaseAddress.contains("canada") {
            Logger.info("🔍 [地址检测] 发现加拿大国家名称", type: .location)
            return "ca"
        }

        Logger.info("🔍 [地址检测] 未检测到明确的国家格式", type: .location)
        return nil
    }

    /// 根据用户当前区域创建搜索区域 - 保留旧方法以兼容
    private func createSearchRegionForCurrentUser() -> MKCoordinateRegion {
        return createSearchRegionForQuery("")
    }

    /// 🎯 使用反向地理编码将中文本地化结果转换为英文（优化版本，减少hang风险）
    private func convertToEnglishMapItem(_ mapItem: MKMapItem) async -> MKMapItem? {
        let coordinate = mapItem.placemark.coordinate
        Logger.info("🔄 [地址转换] 开始反向地理编码转换: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)

        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        // 🚀 优化：添加超时保护，避免hang
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 3_000_000_000) // 3秒超时
            geocoder.cancelGeocode()
        }

        defer {
            timeoutTask.cancel()
        }

        // 🎯 优化：只尝试最相关的locale，减少请求次数
        let primaryLocale = Locale(identifier: "en_AU") // 澳大利亚用户优先使用en_AU

        do {
            let placemarks = try await geocoder.reverseGeocodeLocation(
                location,
                preferredLocale: primaryLocale
            )

            if let englishPlacemark = placemarks.first,
               let locality = englishPlacemark.locality,
               !containsChineseCharacters(locality) {
                Logger.info("✅ [地址转换] 获得英文地址: \(locality)", type: .location)

                // 创建新的MKMapItem，使用英文placemark
                let englishMapItem = MKMapItem(placemark: MKPlacemark(placemark: englishPlacemark))
                englishMapItem.name = mapItem.name // 保持原始名称
                return englishMapItem
            } else {
                Logger.info("⚠️ [地址转换] 仍然返回中文结果，保持原始", type: .location)
                return mapItem
            }
        } catch {
            Logger.warning("⚠️ [地址转换] 反向地理编码失败: \(error.localizedDescription)，保持原始结果", type: .location)
            return mapItem
        }
    }
}

// MARK: - 搜索代理

private class LocaleAwareSearchDelegate: NSObject, MKLocalSearchCompleterDelegate {
    private let completion: ([MKLocalSearchCompletion]) -> Void
    private var hasCompleted = false
    private weak var completer: MKLocalSearchCompleter?

    init(completion: @escaping ([MKLocalSearchCompletion]) -> Void) {
        self.completion = completion
        super.init()

        // 简单的超时保护
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
            if !self.hasCompleted {
                Logger.warning("⏰ [地址搜索] 搜索超时", type: .location)
                self.hasCompleted = true
                self.completion([])
            }
        }
    }

    func setCompleter(_ completer: MKLocalSearchCompleter) {
        self.completer = completer
    }

    func completerDidUpdateResults(_ completer: MKLocalSearchCompleter) {
        guard !hasCompleted else { return }
        hasCompleted = true

        let results = completer.results
        Logger.info("📍 [地址搜索] 找到 \(results.count) 个结果", type: .location)

        // 过滤掉包含中文字符的结果
        let englishResults = filterValidResults(results)
        Logger.info("✅ [地址搜索] 过滤后 \(englishResults.count) 个英文结果", type: .location)

        completion(englishResults)
    }

    func completer(_ completer: MKLocalSearchCompleter, didFailWithError error: Error) {
        guard !hasCompleted else { return }
        hasCompleted = true

        Logger.error("❌ [地址搜索] 搜索失败: \(error.localizedDescription)", type: .location)
        completion([])
    }




    /// 从MKMapItem创建模拟的MKLocalSearchCompletion
    private func createMockCompletion(from mapItem: MKMapItem) -> MKLocalSearchCompletion? {
        let placemark = mapItem.placemark

        // 构建标题（通常是门牌号和街道名）
        var titleComponents: [String] = []

        if let subThoroughfare = placemark.subThoroughfare {
            titleComponents.append(subThoroughfare)
        }

        if let thoroughfare = placemark.thoroughfare {
            titleComponents.append(thoroughfare)
        }

        let title = titleComponents.isEmpty ? (mapItem.name ?? "Unknown") : titleComponents.joined(separator: " ")

        // 构建副标题（城市、州、国家）
        var subtitleComponents: [String] = []

        if let locality = placemark.locality {
            subtitleComponents.append(locality)
        }

        if let administrativeArea = placemark.administrativeArea {
            subtitleComponents.append(administrativeArea)
        }

        if let postalCode = placemark.postalCode {
            subtitleComponents.append(postalCode)
        }

        if let country = placemark.country, country != "United States" {
            subtitleComponents.append(country)
        }

        let subtitle = subtitleComponents.joined(separator: ", ")

        // 🚫 检查是否包含中文字符，如果包含则过滤掉
        let fullText = "\(title) \(subtitle)"
        let hasChinese = fullText.range(of: "\\p{Script=Han}", options: .regularExpression) != nil

        if hasChinese {
            return nil
        }

        // 创建模拟的completion对象
        let mockCompletion = MockLocalSearchCompletion()
        mockCompletion.title = title
        mockCompletion.subtitle = subtitle

        return mockCompletion
    }




    /// 过滤有效的搜索结果 - 只保留英文结果
    private func filterValidResults(_ results: [MKLocalSearchCompletion]) -> [MKLocalSearchCompletion] {
        return results.filter { result in
            let fullText = "\(result.title) \(result.subtitle)"
            // 只保留不包含中文字符的结果
            return fullText.range(of: "\\p{Script=Han}", options: .regularExpression) == nil
        }
    }
}

// MARK: - 错误定义

enum AddressSearchError: LocalizedError {
    case noResults
    case invalidAddress
    case networkError

    var errorDescription: String? {
        switch self {
        case .noResults:
            return "No search results found"
        case .invalidAddress:
            return "Invalid address format"
        case .networkError:
            return "Network error occurred"
        }
    }
}
