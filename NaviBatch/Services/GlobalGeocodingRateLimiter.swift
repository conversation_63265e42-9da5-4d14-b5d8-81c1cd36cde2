import Foundation

/// 全局地理编码速率限制管理器
/// 确保所有地理编码请求都遵循Apple Maps API限制
actor GlobalGeocodingRateLimiter {
    static let shared = GlobalGeocodingRateLimiter()

    // 🚦 速率限制配置 - 匹配Apple Maps实际限制
    private var requestTimes: [Date] = []
    private let maxRequests = 45 // Apple Maps限制是50/60秒，设置45留余量
    private let timeWindow: TimeInterval = 60.0 // 🎯 修正：Apple Maps是60秒窗口
    private let emergencyWaitTime: TimeInterval = 2.0 // 默认紧急等待时间

    // 📊 统计信息
    private var totalRequests = 0
    private var throttledRequests = 0

    private init() {}

    // MARK: - 公共接口

    /// 检查是否可以发送请求
    func canMakeRequest() -> Bool {
        cleanupOldRequests()
        return requestTimes.count < maxRequests
    }

    /// 记录请求时间
    func recordRequest() {
        requestTimes.append(Date())
        totalRequests += 1
    }

    /// 计算需要等待的时间
    func timeUntilNextRequest() -> TimeInterval {
        guard !requestTimes.isEmpty else { return 0 }
        let oldestRequest = requestTimes.first!
        let timeElapsed = Date().timeIntervalSince(oldestRequest)
        return max(0, timeWindow - timeElapsed)
    }

    /// 等待直到可以发送请求 - 修复无限循环版本
    func waitForRateLimit() async {
        var loopCount = 0
        let maxLoops = 100 // 防止无限循环的安全机制

        while !canMakeRequest() {
            loopCount += 1

            // 🚨 安全机制：防止无限循环
            if loopCount > maxLoops {
                Logger.error("🚨 检测到无限循环，强制退出 (循环次数: \(loopCount))", type: .location)
                // 强制清空请求历史，重新开始
                requestTimes.removeAll()
                break
            }

            let waitTime = max(timeUntilNextRequest(), 0.1) // 🔧 修复：最小等待0.1秒，避免0秒等待
            let actualWaitTime = min(waitTime, 3.0) // 最多等待3秒
            throttledRequests += 1

            // 🔧 修复：只在实际需要等待时打印日志，避免spam
            if actualWaitTime > 0.05 { // 只有等待时间超过50ms才打印
                Logger.info("🚦 处理速度优化：等待 \(String(format: "%.1f", actualWaitTime)) 秒 (已优化: \(throttledRequests)次)", type: .location)
            }

            // 🔧 修复：确保总是有实际的等待时间
            if actualWaitTime > 0 {
                // 分段等待，每0.5秒检查一次
                let segments = Int(actualWaitTime / 0.5)
                for _ in 0..<segments {
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                    if Task.isCancelled { return }
                }

                // 等待剩余时间
                let remainingTime = actualWaitTime - Double(segments) * 0.5
                if remainingTime > 0 {
                    try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
                }
            } else {
                // 🔧 修复：即使计算出的等待时间为0，也要等待最小时间避免无限循环
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒最小等待
            }

            // 🔧 修复：每次循环后强制清理过期请求
            cleanupOldRequests()
        }
        recordRequest()
    }

    /// 紧急等待（当检测到Apple Maps限制错误时）
    func emergencyWait() async {
        await emergencyWait(customWaitTime: nil)
    }

    /// 智能紧急等待（根据Apple Maps返回的实际等待时间）
    func emergencyWait(customWaitTime: TimeInterval?) async {
        throttledRequests += 1

        let waitTime = customWaitTime ?? emergencyWaitTime
        Logger.warning("🚨 检测到Apple Maps API限制，等待 \(Int(waitTime)) 秒", type: .location)

        // 清空请求历史，重新开始计数
        requestTimes.removeAll()

        try? await Task.sleep(nanoseconds: UInt64(waitTime * 1_000_000_000))
    }

    /// 🎯 从Apple Maps错误中解析等待时间
    func parseWaitTimeFromError(_ error: Error) -> TimeInterval? {
        let nsError = error as NSError

        // 检查是否是Apple Maps限流错误
        guard nsError.domain == "GEOErrorDomain" && nsError.code == -3 else {
            return nil
        }

        // 尝试从userInfo中提取timeUntilReset
        if let timeUntilReset = nsError.userInfo["timeUntilReset"] as? TimeInterval {
            Logger.info("🎯 从Apple Maps错误中解析到等待时间: \(Int(timeUntilReset)) 秒", type: .location)
            return timeUntilReset
        }

        // 尝试从details中解析
        if let details = nsError.userInfo["details"] as? [[String: Any]] {
            for detail in details {
                if let timeUntilReset = detail["timeUntilReset"] as? TimeInterval {
                    Logger.info("🎯 从Apple Maps错误详情中解析到等待时间: \(Int(timeUntilReset)) 秒", type: .location)
                    return timeUntilReset
                }
            }
        }

        return nil
    }

    /// 获取当前状态信息
    func getStatus() -> RateLimiterStatus {
        cleanupOldRequests()
        return RateLimiterStatus(
            currentRequests: requestTimes.count,
            maxRequests: maxRequests,
            totalRequests: totalRequests,
            throttledRequests: throttledRequests,
            timeWindow: timeWindow
        )
    }

    /// 重置统计信息
    func resetStats() {
        totalRequests = 0
        throttledRequests = 0
    }

    // MARK: - 私有方法

    /// 清理过期的请求记录 - 增强版本
    private func cleanupOldRequests() {
        let now = Date()
        let originalCount = requestTimes.count
        requestTimes = requestTimes.filter { now.timeIntervalSince($0) < timeWindow }

        // 🔧 调试：记录清理情况
        let cleanedCount = originalCount - requestTimes.count
        if cleanedCount > 0 {
            Logger.debug("🧹 清理了 \(cleanedCount) 个过期请求，剩余 \(requestTimes.count) 个", type: .location)
        }

        // 🔧 安全机制：如果请求时间数组异常大，强制清理
        if requestTimes.count > maxRequests * 2 {
            Logger.warning("🚨 请求时间数组异常大 (\(requestTimes.count))，强制清理", type: .location)
            requestTimes.removeAll()
        }
    }
}

// MARK: - 状态结构体

/// 速率限制器状态信息
struct RateLimiterStatus {
    let currentRequests: Int
    let maxRequests: Int
    let totalRequests: Int
    let throttledRequests: Int
    let timeWindow: TimeInterval

    var utilizationPercentage: Double {
        return Double(currentRequests) / Double(maxRequests) * 100
    }

    var isNearLimit: Bool {
        return utilizationPercentage > 80
    }
}

// MARK: - 扩展方法

extension GlobalGeocodingRateLimiter {
    /// 批量处理时的智能延迟 - 优化版本
    /// 根据当前使用率动态调整延迟时间
    func getSmartBatchDelay() -> TimeInterval {
        let status = getStatus()

        if status.isNearLimit {
            return 0.5 // 优化：接近限制时短暂延迟
        } else if status.utilizationPercentage > 70 {
            return 0.2 // 优化：高使用率时轻微延迟
        } else {
            return 0.1 // 优化：低使用率时最小延迟
        }
    }

    /// 检查是否应该暂停批量处理
    func shouldPauseBatchProcessing() -> Bool {
        let status = getStatus()
        return status.currentRequests >= maxRequests - 5 // 留出5个请求的缓冲
    }
}
