/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/
"language_settings" = "Language Settings";
"system_language" = "System Language";
"system_language_section" = "System Settings";
"languages" = "Languages";
"language_info_title" = "About Language Settings";
"language_info_description" = "After changing the language setting, the app will display text in the selected language. Some content may require restarting the app to fully apply the new language setting.";
"restart_required" = "Restart Required";
"restart_app_message" = "To fully apply language changes, please restart the app.";
"restart_now" = "Restart Now";
"restart_later" = "Restart Later";
"close" = "Close";
"cancel" = "Cancel";
"save" = "Save";
"edit" = "Edit";
"delete" = "Delete";
"done" = "Done";
"next" = "Next";
"back" = "Back";
"confirm" = "Confirm";
"error" = "Error";
"success" = "Success";
"subscription_success_title" = "🎉 Subscription Successful!";
"subscription_success_pro_monthly" = "Congratulations! You've successfully subscribed to NaviBatch Pro Monthly. Now you can enjoy unlimited addresses, one-click navigation, and all premium features.";
"subscription_success_pro_yearly" = "Congratulations! You've successfully subscribed to NaviBatch Pro Yearly. Now you can enjoy unlimited addresses, one-click navigation, and all premium features, plus save 30% on costs.";
"subscription_success_expert" = "Congratulations! You've successfully subscribed to NaviBatch Expert. Now you can enjoy all premium features including unlimited addresses, one-click navigation, and smart route optimization.";
"subscription_success_features" = "✨ Unlocked Features:\n• Unlimited addresses\n• One-click batch navigation\n• Smart route optimization\n• Advanced export features";
"subscription_success_action" = "Start Using Premium Features";
"warning" = "Warning";
"unknown_error" = "Unknown Error";
"loading" = "Loading...";
"search" = "Search";
"settings" = "Settings";
"help" = "Help";
"about" = "About";
"menu" = "Menu";
"understand" = "I Understand";
"navigation" = "Navigation";
"start_navigation" = "Start Navigation";
"subscription" = "Subscription";
"upgrade_to_pro" = "Upgrade to Pro";
"upgrade_description" = "One-click navigation grouping, up to 60x faster than manual operation";
"restore_purchases" = "Restore Purchases";

// App Update
"app_update_title" = "NaviBatch";
"app_update_subtitle" = "Bug fixes and improvements";
"app_update_notes" = "Bug fixes and improvements";
"current_version" = "Current Version";
"latest_version" = "Latest Version";
"update_content" = "What's New";
"update_now" = "Update Now";
"remind_later" = "Remind Later";
"skip_version" = "Skip This Version";
"force_update_message" = "This version contains important updates and requires immediate update";
"restore_purchases_success" = "🎉 Purchases Restored Successfully!";
"restore_purchases_success_message" = "Your subscription has been successfully restored. You can now enjoy all premium features.";
"learn_more" = "Learn More";
"upgrade_your_plan" = "Upgrade Your Plan";
"one_click_navigation_description" = "One-click navigation grouping, save time and fuel";
"current_plan" = "Current Plan";
"upgrade" = "Upgrade";
"maybe_later" = "Maybe Later";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Free";
"pro_tier_price" = "$29.99/month";
"expert_tier_price" = "$20.83/month";
"free_tier_description" = "Perfect for individuals and small businesses with few stops";
"pro_tier_description" = "For users who need one-click navigation grouping and unlimited addresses";
"expert_tier_description" = "Same as Pro, but save 50% with annual plan";
"route_optimization" = "Route Optimization";
"unlimited_routes" = "Unlimited Routes";
"unlimited_optimizations" = "Unlimited Optimizations";
"max_15_addresses" = "Max 15 Addresses Per Route";
"save_fuel_30" = "Save Up To 30% Fuel";
"unlimited_addresses" = "Unlimited addresses";
"one_click_navigation" = "⚡ One-Click Navigation Grouping - Up To 60x Faster ⚡";
"package_finder" = "Package Location Finder";
"annual_savings" = "Annual Plan Savings";
"switched_to_free" = "Switched to Free Plan";
"switched_to_subscription" = "Switched to Subscription Plan";
"unlimited_stops" = "Unlimited Stops";
"plan_as_many_stops_as_needed" = "Add as many delivery points as you need";
"expires_in_days" = "Expires in %@ days";
"trial_expires_in_days" = "Trial expires in %@ days";
"expired" = "Expired";
"subscription_expires_on" = "Expires on %@";
"subscription_active_until" = "Active until %@";
"enter_or_search_address" = "Enter or Search Address";
"search_results_count" = "Search results: %d";
"no_matching_addresses" = "No valid addresses found";
"search_address_failed" = "Search Address Failed: %@";
"address_search_no_response" = "Address Search No Response";
"cannot_get_address_coordinates" = "Cannot get address coordinates";

"cannot_get_coordinates_retry" = "Cannot get address coordinates, please enter manually or retry";

"image_address_recognition" = "Scanner";
"scanner" = "Scanner";
"select_images" = "Select Images";
"select_multiple_images" = "Multiple Selection Supported";
"delivery_app_screenshot_description" = "Select your courier service and upload screenshots or videos.";
"select_delivery_app_type" = "Select Delivery Service";
"select_delivery_screenshots" = "Select Delivery App Screenshots";
"select_delivery_media" = "Select Delivery App Screenshots/Videos";
"select_delivery_media_pdf" = "Select Delivery App Screenshots/Videos/PDFs";
"select_photos" = "Select Photos";
"select_photos_videos" = "Select Photos & Videos";
"united_states_delivery" = "United States Delivery";
"intelligent_text_analysis" = "Intelligent text analysis...";
"address_verification" = "Address verification...";
"processing_image_segment" = "Processing image segment";
"splitting_image" = "Splitting image for processing...";
"processing_segment" = "Processing segment";
"merging_results" = "Merging results...";
"intelligent_splitting" = "Intelligent image splitting...";
"analyzing_image_segment" = "Analyzing image segment";
"merging_analysis_results" = "Merging analysis results...";
"merging_processing_results" = "Merging processing results...";
"smart_split_complete" = "Smart split processing complete";
"australia_delivery" = "Australia Delivery";
"universal_delivery" = "Universal";
"hong_kong_service_restriction" = "Hong Kong Service Restriction";
"hong_kong_service_restriction_message" = "The service provider has restricted access to advanced recognition services in Hong Kong. This is a geographic restriction policy. We recommend using OCR mode, which is optimized for Hong Kong users and works equally well.";
"advanced_recognition_service_restriction" = "Advanced Recognition Service Restriction";
"advanced_recognition_service_restriction_message" = "The current region has frequency restrictions on advanced recognition services, which are intermittently available. You can try backup services or use basic recognition mode for a stable experience.";
"use_ocr_mode" = "Use OCR Mode";
"learn_ocr_advantages" = "Learn OCR Advantages";
"try_backup_service" = "Try Backup Service";
"use_basic_mode" = "Use Basic Mode";
"tap_to_select_from_photos" = "Tap to select from Photos";
"select_pdf_files" = "Select PDF Files";
"tap_to_select_from_files" = "Tap to select from Files";
"loading_pdf_files" = "Loading PDF files...";
"pdf_selection_failed" = "PDF file selection failed";
"no_pdf_pages_extracted" = "No pages could be extracted from PDF";
"ai_processing_pdf_text" = "Processing PDF text";
"ai_processing_pdf_native" = "Processing PDF document";
"processing_pdf_document" = "Processing PDF document";
"processing_pdf_text" = "Processing PDF text";
"extracting_pdf_pages" = "Extracting PDF pages";
"supported_delivery_apps" = "Supports screenshots from:";
"amazon_flex_imile_etc" = "Just Photo • SpeedX • GoFo • UNIUNI • Amazon Flex • iMile • LDS EPOD • PIGGY • YWE";
"amazon_flex_imile_video_support" = "Just Photo • Amazon Flex • iMile (Screenshots & Videos)";
"amazon_flex_imile_pdf_support" = "Just Photo • Amazon Flex • iMile (Screenshots, Videos & PDFs)";
"processing_images" = "Processing images...";
"processing_image_progress" = "Processing image %d of %d";
"recognizing_text" = "Recognizing text...";
"geocoding_addresses" = "Getting address coordinates...";
"analyzing_image_content" = "Analyzing image content...";
"analyzing_image_text" = "Analyzing image text...";
"intelligent_analysis" = "Intelligent analysis...";
"analyzing_image_similarity" = "Analyzing image similarity";
"processing_image_frames" = "Processing image frames";
"calculating_frame_similarity" = "Calculating frame similarity";
"fallback_to_ocr" = "Using alternative recognition...";
"using_advanced_recognition" = "🔥 Using advanced recognition...";
"recognition_failed" = "Recognition failed";
"recognition_complete" = "Recognition Complete";
"no_text_recognized" = "No text recognized";
"loading_images" = "Loading images...";
"loading_media" = "Loading media files...";
"analyzing_images" = "Analyzing images...";
"processing_complete" = "Processing complete";
"processing" = "Processing...";
"processing_images" = "Processing images...";
"processing_image_progress" = "Processing image %d/%d";
"processing_media_progress" = "Processing media %d/%d";
"processing_batch_progress" = "Processing batch %d/%d";
"deduplicating_overlapping_areas" = "Removing duplicates from overlapping areas...";
"found_addresses_need_confirmation" = "Found %d addresses that need confirmation. You can edit these addresses in the address list to fix issues.";
"fixing_addresses_progress" = "Fixing addresses %d/%d";
"address_fix_complete" = "Address Fix Complete";
"address_fix_result" = "Successfully fixed %d addresses, %d addresses failed to fix";
"batch_progress" = "Batch: %d/%d";
"waiting_between_batches" = "Waiting between batches...";
"processing_image_complete_waiting" = "Completed image %d, waiting %d seconds before continuing...";
"processing_failed" = "Processing failed";
"retry" = "Retry";
"ocr_processing_complete" = "OCR processing complete";
"validating_addresses" = "Validating addresses...";
"model_used" = "Model used: %@";
"no_images_loaded" = "No images loaded";
"no_media_loaded" = "No media files loaded";
"no_addresses_found" = "No valid addresses found";
"image_recognition_failed" = "Image recognition failed";
"image_recognition_error" = "Image recognition error: %@";
"text_recognition_failed" = "Text recognition failed";
"address_parsing_failed" = "Address parsing failed";
"select_addresses_to_add" = "Select addresses to add";
"recognized_addresses" = "Recognized Addresses";
"address_coordinates" = "Address Coordinates";
"toggle_address_selection" = "Toggle address selection";
"remove_address" = "Remove address";
"confirm_selected_addresses" = "Confirm selected addresses";
"no_addresses_selected" = "No addresses selected";
"image_processing_cancelled" = "Image processing cancelled";
"unsupported_image_format" = "Unsupported image format";
"image_too_large" = "Image file too large";
"image_recognition_permission_required" = "Photo library access permission required";
"ocr_language_detection" = "Auto-detect language";
"improve_image_quality" = "Please ensure image is clear and text is visible";
"address_validation_in_progress" = "Validating addresses...";
"batch_address_import" = "Batch Address Import";
"validated_addresses_count" = "Validated %d addresses";
"addresses_with_issues" = "%d addresses have issues";
"all" = "All";
"selected_statuses" = "Selected";
"select_all" = "Select All";
"import_selected" = "Import Selected";
"validating_addresses" = "Validating addresses...";
"empty_address" = "Empty address";
"invalid_coordinates" = "Invalid coordinates";
"coordinate_warning" = "Coordinate warning";
"address_validation_issue" = "Address validation issue";
"cannot_get_coordinates" = "Cannot get address coordinates";
"no_importable_addresses" = "No importable addresses, please check address limits";
"free_version_max_addresses" = "Free version allows maximum %d addresses.";
"valid" = "Valid";
"with_issues" = "With Issues";
"low_confidence_address" = "Low confidence address validation";
"address_validation_failed" = "Address validation failed";
"apple_maps_missing_house_number" = "Apple Maps returned address is missing house number information";
"cannot_get_valid_coordinates" = "Cannot get valid coordinates";
"include_unverified_addresses" = "Include Unverified Addresses";
"include_unverified_addresses_description" = "When enabled, includes addresses that failed geocoding (may be inaccurate)";
"address_mismatch_with_house_number" = "Apple Maps returned address does not match house number";
"address_validation_failed_title" = "Address Validation Failed";
"address_validation_failed_message" = "The following addresses have issues and cannot be optimized:";
"address_validation_failed_fix_message" = "Please correct the coordinates of these addresses before optimization.";
"fix_addresses" = "Fix Addresses";
"geocoding_success" = "Address coordinates retrieved successfully";
"geocoding_partial_success" = "Address coordinates may be inaccurate, please check";
"geocoding_failed" = "Unable to get address coordinates";
"geocoding_rate_limited" = "Too many map service requests, please try again later";
"geocoding_network_error" = "Network connection error, please check network";
"geocoding_timeout" = "Address query timeout, please try again later";
"geocoding_invalid_address" = "Invalid address format, please check";
"geocoding_not_found" = "Address not found, please check spelling or add more details";
"address_auto_corrected_error" = "Address does not exist or is incomplete. System attempted to correct to: '%@'. Please check and enter complete correct address.";
"address_match" = "Address match";
"check_address_spelling" = "Please check address spelling";
"confirm_address_complete" = "Confirm address is complete";
"current_addresses_remaining" = "Currently have %d addresses, can only add %d more.";
"can_import_selected" = "You selected %d addresses, which can be imported.";
"selected_exceeds_limit" = "You selected %d addresses, exceeding the limit by %d.";
"selected_addresses_all_importable" = "You've selected %d addresses, all can be imported.";
"upgrade_for_unlimited_addresses" = "Upgrade to Pro for unlimited addresses!";
"current_route_address_limit" = "Current route has %d addresses, can only add %d more, for a maximum total of %d addresses.";
"import_all_addresses" = "Import All Addresses";
"import_first_n" = "Import First %d Addresses";
"import_selected_addresses" = "Import Selected Addresses";
"upgrade_to_premium" = "Upgrade to Premium";
"batch_add_addresses" = "Batch Add Addresses";
"batch_address_input_placeholder" = "Enter or paste addresses, one per line. Maximum 35 addresses.";
"search_address" = "Search Address";
"no_saved_addresses" = "No Saved Addresses";
"no_saved_addresses_description" = "You haven't saved any addresses yet, or there are no addresses matching your filter criteria";
"select_address_book" = "Select Address Book";
"routes" = "Routes";
"address_book" = "Address Book";
"address_history" = "Address History";
"saved_routes" = "Saved Routes";
"manage_your_routes" = "Manage your route planning";
"manage_your_addresses" = "Manage your frequently used addresses";
"manage_address_history" = "Manage address history database";
"statistics" = "Statistics";
"total_addresses" = "Total Addresses";
"frequently_used" = "Frequently Used";
"confidence" = "Confidence";
"usage_count" = "Usage Count";
"last_used" = "Last Used";
"source" = "Source";
"created_at" = "Created At";
"address_copied" = "Address Copied";
"trigger_search" = "Trigger Search";
"clear_all_addresses" = "Clear All Addresses";
"no_saved_addresses_title" = "No Address History";
"no_saved_addresses_message" = "Address history will appear here when you use address features";
"preferences" = "Preferences";
"set_custom_start_point" = "Set Custom Start Point";
"current_start_point" = "Current start point: %@";
"support" = "Support";
"contact_us" = "Contact Us";
"contact_us_description" = "Have questions or suggestions? Feel free to contact us!";
"help_center" = "Help Center";
"quick_actions" = "Quick Actions";
"main_features" = "Main Features";
"support_help" = "Support & Help";
"customize_app_settings" = "Customize app settings";
"unlock_all_features" = "Unlock all features";
"get_help_support" = "Get help and support";
"app_info_version" = "App info and version";
"dev_tools" = "Dev Tools";
"debug_testing_tools" = "Debug and testing tools";
"version" = "Version";
"addresses" = "Addresses";
"limited_to_20_addresses" = "Limited to 20 addresses";
"all_premium_features" = "All premium features";
"open_subscription_view" = "Open Subscription View";
"open_subscription_view_description" = "Skip intermediate layer, directly show SubscriptionView";
"restore_purchases_failed" = "Restore Purchases Failed: %@";
"rate_us" = "Rate Us";
"rate_us_description" = "Your feedback is important to us and helps us improve the app!";
"share_app" = "Share App";
"share_app_text" = "Try NaviBatch, an amazing route planning app!";
"about_app" = "About App";
"developer_tools" = "Developer Tools";
"coordinate_debug_tool" = "Coordinate Debug Tool";
"batch_fix_addresses" = "Batch Fix Addresses";
"clear_database" = "Clear Driver Address Database";
"clear_database_confirmation" = "This will delete all historical address records in the driver address database. This action cannot be undone. Are you sure you want to continue?";
"confirm_clear" = "Confirm";
"version_info" = "Version %@ (%@)";
"current_system_language" = "Current System Language";
"reset_to_system_language" = "Reset to System Language";
"language" = "Language";
"address" = "Address";
"coordinates" = "Coordinates";
"distance_from_current_location" = "Distance from Current Location";
"address_info" = "Address Information";
"update_coordinates" = "Update Coordinates";
"update_coordinates_required" = "Update Coordinates (Required)";
"address_modified_update_required" = "Address has been modified, please click 'Update Coordinates' to get new location information";
"fix_address" = "Fix Address";
"prompt" = "Prompt";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Please modify the address before updating coordinates";
"coordinates_update_success" = "Coordinates updated successfully";
"coordinates_update_failure" = "Failed to update coordinates";
"save_failure" = "Save failed: %@";
"no_saved_addresses_title" = "No Saved Addresses";
"no_saved_addresses_message" = "You haven't saved any addresses yet";
"add_new_address" = "Add New Address";
"address_title" = "Address";
"add" = "Add";
"refresh" = "Refresh";
"notes" = "Notes";
"address_details" = "Address Details";
"favorite" = "Favorite";
"edit_address" = "Edit Address";
"confirm_delete" = "Confirm Delete";
"delete_address_confirmation" = "Are you sure you want to delete this address? This action cannot be undone.";
"address_marker" = "Address";
"address_label" = "Address:";
"notes_label" = "Notes:";
"created_at_label" = "Created At:";
"open_in_maps" = "Open in Maps";
"copy_address" = "Copy Address";
"address_details_title" = "Address Details";
"start_end_point" = "Start/End Point";
"start_point" = "Start Point";
"end_point" = "End Point";
"route_info" = "Route Information";
"route_sheet_backup_button" = "Route Info";
"route_sheet_backup_button_tooltip" = "Show route information";
"address_count" = "Address Count";
"address_count_format" = "%d Addresses";
"points_count_format" = "%d Points";
"additional_points_format" = "+%d Points";
"export_route" = "Export Route";
"navigate" = "Navigate";
"address_list" = "Address List";
"no_addresses" = "No Addresses";
"no_addresses_message" = "This route has no addresses yet";
"address_point_start" = "Start Point";
"address_point_stop" = "Stop Point";
"address_point_end" = "End Point";
"route_name" = "Route Name";
"new_route" = "New Route";
"saved_route" = "Saved Route";
"plan_route" = "Plan Route";
"clear_all" = "Clear";
"restore" = "Restore Order";
"restore_route" = "Restore";
"sort_by_third_party" = "3rd Party";
"save_third_party_sort" = "Save";
"avoid" = "Avoid:";
"toll_roads" = "Toll Roads";
"highways" = "Highways";
"processing_addresses" = "Processing addresses...";
"same_start_end_point" = "You have set the same address as both start and end point";
"add_start_point" = "Add Start Point";
"swipe_left_to_delete" = "Long press to delete";
"add_end_point" = "Add End Point";
"enter_and_select_address" = "Enter and select address";
"current_search_text" = "Current search text: %@";
"add_address" = "Add Address";
"selected_coordinates" = "Selected Coordinates";
"company_name_optional" = "Company Name (Optional)";
"url_optional" = "URL (Optional)";
"favorite_address" = "Favorite Address";
"set_as_start_and_end" = "Set as both Start and End Point";
"image_recognition" = "3rd Party";
"batch_paste" = "Batch Paste";
"file_import" = "File Import";
"web_download" = "Web Download";
"saving" = "Saving...";
"delivery_point_management" = "Management";
"information_category" = "Information Category";
"package_info" = "Package Information";
"vehicle_position" = "Vehicle Position";
"delivery_info" = "Delivery Information";
"take_photo_record" = "Proof of Delivery";
"update_status" = "Update Status";
"edit_address_button" = "Edit Address";
"access_instructions" = "Access Instructions";
"add_access_instructions" = "Add access instructions...";
"package_count" = "Package Count";
"package_count_format" = "%d packages";
"packages" = "packages";
"package_unit" = "pcs";
"package_size" = "Package Size";
"package_type" = "Package Type";
"mark_as_important" = "Mark as Important";
"priority_delivery" = "Priority Delivery";
"priority_level" = "Priority Level";
"priority_1" = "Priority 1";
"priority_2" = "Priority 2";
"priority_3" = "Priority 3";
"no_priority" = "No Priority";
"no_priority_short" = "None";
"set_priority" = "Set Priority";
"select_package_position" = "Select Package Position in Vehicle";
"vehicle_area" = "Vehicle Area";
"left_right_position" = "Left/Right Position";
"vehicle_position_front" = "Front";
"vehicle_position_middle" = "Middle";
"vehicle_position_back" = "Back";
"vehicle_position_left" = "Left";
"vehicle_position_right" = "Right";
"vehicle_position_floor" = "Floor";
"vehicle_position_shelf" = "Shelf";
"height_position" = "Height Position";
"vehicle_position_none" = "No Position Selected";
"delivery_type" = "Delivery Type";
"delivery_status" = "Delivery Status";
"order_info" = "Order Information";
"order_information" = "Order Information";
"order_number" = "Order Number";
"enter_order_number" = "Enter order number";
"tracking_number" = "Tracking Number";
"enter_tracking_number" = "Enter tracking number";
"tracking_info" = "Tracking Information";
"tracking_information" = "Tracking Information";
"time_info" = "Time Information";
"time_information" = "Time Information";
"estimated_arrival_time" = "Estimated Arrival Time";
"anytime" = "Anytime";
"stop_time" = "Stop Time";
"minutes_format" = "%d minutes";
"photo_record" = "Delivery Proof";
"door_number_photo" = "Door Number Photo";
"package_label_photo" = "Package Label Photo";
"placement_photo" = "Placement Photo";
"door_number_desc" = "Take a clear photo of the door number or street number, ensuring digits/letters are visible";
"package_label_desc" = "Take a photo of the package label, ensuring recipient information is clearly visible";
"placement_desc" = "Take a photo of where the package was finally placed";
"photo_captured" = "Photo Captured";
"photo_captured_options" = "Photo captured, would you like to continue with the next photo or finish the current one?";
"continue_to_next_photo" = "Continue to Next Photo - %@";
"retake" = "Retake Photo";
"tap_to_capture" = "Tap to capture";
"flash_auto" = "Auto Flash";
"flash_on" = "Flash On";
"flash_off" = "Flash Off";
"photo_record_completed" = "Delivery Proof Completed";
"photo_confirmation" = "Photo Confirmation";
"ok" = "OK";
"complete_photo_capture" = "Complete Photos";
"photo_instructions" = "Tap on each photo card to capture. All photos must be completed.";
"photo_options" = "Photo Options";
"view_photo" = "View Photo";
"retake_photo" = "Retake Photo";
"saving_photos" = "Saving photos...";
"completed" = "Completed";
"not_taken" = "Not Taken";
"route_options" = "Route Options";

"optimize_route" = "Optimize Route";
"optimizing" = "Optimizing...";
"optimization_complete" = "Optimization Complete";
"route_optimization_results" = "Results";

"before_optimization" = "Before Optimization";
"after_optimization" = "After Optimization";
"auto_group" = "Auto Group";
"optimized_route_order" = "Order";
"apply" = "Apply";
"kilometers" = "kilometers";
"street_number_issue_warning" = "⚠️ Large street number difference! This may cause delivery to wrong address and penalties. Please verify address immediately";
"address_validation_critical" = "Critical Address Validation Issue";
"street_number_difference_high_risk" = "Large street number difference, high risk";
"delete_address" = "Delete Address";
"street" = "Street";
"city" = "City";
"state" = "State/Province";
"country" = "Country";
"postal_code" = "Postal Code";
"phone" = "Phone";
"email" = "Email";
"website" = "Website";
"company" = "Company";
"latitude" = "Latitude";
"longitude" = "Longitude";
"geocoding_error" = "Geocoding Error";
"address_validation" = "Address Validation";
"invalid_addresses" = "Invalid Addresses";
"fix_addresses" = "Fix Addresses";
"route" = "Route";
"select_address_point" = "Select Address Points";
"select_delivery_points" = "Select Delivery Points";
"create_delivery_route" = "Create Delivery Group";
"view_saved_routes" = "View Saved Groups";
"create_route" = "Create Route";
"create_new_route" = "Create New Route";
"edit_route" = "Edit Route";
"delete_route" = "Delete Route";
"route_details" = "Route Details";
"selected_addresses" = "Selected %d Addresses";
"reached_limit" = "Limit Reached";
"can_select_more" = "Can select %d more";
"navigate_button" = "GO";
"create_group" = "Create Group";
"waypoints" = "Waypoints";
"total_distance" = "Total Distance";
"estimated_time" = "Estimated Time";
"route_summary" = "Route Summary";
"route_saved" = "Route Saved";
"route_optimized" = "Route Optimized";
"optimizing_route" = "Optimizing Route...";
"completed_percent" = "Completed %d%%";
"processing_points" = "Processing: %d/%d";
"estimated_remaining_time" = "Estimated Time Remaining: %@";
"delivery" = "Delivery";
"delivery_confirmation" = "Delivery Confirmation";
"take_photo" = "Take Photo";
"signature" = "Signature";
"delivery_notes" = "Delivery Notes";
"delivered" = "Delivered";
"not_delivered" = "Not Delivered";
"delivery_time" = "Delivery Time";
"delivery_date" = "Delivery Date";
"package_details" = "Package Details";
"package_id" = "Package ID";
"package_weight" = "Package Weight";
"package_dimensions" = "Package Dimensions";
"recipient_name" = "Recipient Name";
"recipient_phone" = "Recipient Phone";
"groups" = "Groups";
"saved_groups" = "Saved Groups";
"edit_group" = "Edit Group";
"delete_group" = "Delete Group";
"group_name" = "Group Name";
"group_details" = "Group Details";
"auto_grouping" = "Auto Grouping";
"group_by" = "Group By";
"add_to_group" = "Add to Group";
"remove_from_group" = "Remove from Group";
"group_created" = "Group Created";
"default_group_name_format" = "Group %d";
"auto_grouping_completed" = "Auto Grouping Completed";
"auto_grouping_in_progress" = "Auto Grouping in Progress...";
"create_group_every_14_addresses" = "Create a group for every 14 addresses";
"create_delivery_group" = "Create Delivery Group";
"enter_group_name" = "Enter Group Name";
"selected_delivery_points" = "Selected Delivery Points";
"drag_to_adjust_order" = "Drag to adjust order";
"free_plan" = "FREE";
"pro_plan" = "PRO";
"expert_plan" = "Expert Plan";
"monthly" = "Monthly Plan";
"yearly" = "Yearly Plan";
"subscribe" = "Subscribe";
"manage_subscription" = "Manage Subscription";
"subscription_benefits" = "Subscription Benefits";
"free_trial" = "Free Trial";
"price_per_month" = "%@ per month";
"price_per_year" = "%@ per year";
"save_percent" = "Save %@%";
"subscription_terms" = "Subscription Terms";
"privacy_policy" = "Privacy Policy";
"terms_of_service" = "Terms of Service";
"third_party_sort_label" = "%@: %@";
"feature_comparison" = "Feature Comparison";
"addresses_per_route" = "Addresses per Route";
"max_20_addresses" = "20 addresses";
"fuel_savings" = "Fuel Savings";
"up_to_30_percent" = "Up to 30%";
"choose_subscription_plan" = "Choose Subscription Plan";
"monthly_plan" = "Monthly Plan";
"yearly_plan" = "Yearly Plan";
"/month_suffix" = "/month";
"/year_suffix" = "/year";
"save_30_percent" = "Save 30%";
"free_trial_7_days_cancel_anytime" = "Includes 60-day free trial, cancel anytime";
"subscription_auto_renew_notice" = "Subscription automatically renews unless canceled at least 24 hours before the end of the current period.";
"and" = "and";
"subscription_exclusive" = "Premium Only";
"free_version_optimization_limit" = "Free Version Optimization Limit";
"free_version_supports_max_addresses" = "Free version supports up to %d addresses.";
"current_route_contains_addresses" = "Current route contains %d addresses, exceeding the free version limit.";
"upgrade_to_pro_unlimited_addresses" = "Upgrade to Pro for unlimited addresses and one-click navigation grouping!";
"continue_optimization" = "Continue Optimization";
"upgrade_unlock_one_click_navigation" = "Upgrade to Unlock One-Click Navigation - 14x Faster";
"learn_one_click_navigation_grouping" = "Learn About One-Click Navigation Grouping";
"toggle_subscription_status" = "Toggle Subscription Status";

// SavedGroups related strings
"no_saved_groups" = "No Saved Groups";
"select_points_create_groups" = "Select delivery points to create groups";
"clear_all_groups" = "Clear All Groups";
"clear_all_groups_confirmation" = "Are you sure you want to clear all groups? This action cannot be undone.";
"clear_all_groups_button" = "Clear";
"remaining_groups_locked" = "%d more groups available";
"remaining_groups_count" = "%d remaining";
"group_number_format" = "Group %d";
"upgrade_to_unlock_all_groups" = "Upgrade to Pro to unlock all group navigation features";
"upgrade_now" = "Upgrade Now";
"toggle_subscription_description" = "Switch between free and pro versions (for development testing only)";
"product_info_unavailable" = "Unable to get product information, please try again later";
"purchase_failed" = "Purchase failed: %@";
"upgrade_to_pro_version" = "Upgrade to Pro";
"unlock_all_premium_features" = "Unlock all premium features";
"first_7_days_free_cancel_anytime" = "First 14 days free, cancel anytime.";
"free_version_can_group_addresses" = "Free version can group %d addresses";
"reached_free_version_limit" = "Reached free version limit";
"creating_groups" = "Creating groups...";
"payment_terms_notice" = "Payment will be charged to your Apple ID account at confirmation of purchase. Subscription automatically renews unless it is canceled at least 24 hours before the end of the current period.";
"terms_of_use" = "Terms of Use";
"product_load_failed_check_connection" = "Unable to load product information, please ensure your device is connected to the internet and logged into the App Store";
"product_load_failed" = "Product loading failed: %@";
"verify_receipt" = "Verify Receipt";
"one_click_navigation_short" = "One-Click Navigation";
"save_30_percent_fuel" = "Save 30% Fuel";
"monthly_short" = "Monthly";
"yearly_short" = "Yearly";
"upgrade_now" = "Upgrade Now";
"test_environment_pro_activated" = "🧪 Test Environment: Pro Version Activated\nYou can now experience all premium features!";
"payment_terms_notice_detailed" = "Payment will be charged to your Apple ID account at confirmation of purchase. Subscription automatically renews unless it is canceled at least 24 hours before the end of the current period. You can manage and cancel your subscriptions in App Store settings.";
"step_screenshot" = "Step %d Screenshot";
"previous_step" = "Previous";
"next_step" = "Next";
"each_address_takes_3_5_seconds" = "Each address takes 3-5 seconds to add";
"need_repeat_14_times" = "Need to repeat the same operation 14 times";
"navigation_order_often_confused" = "Navigation order often gets confused";
"error_prone_need_redo" = "Error-prone, need to redo operations";
"address_order_reversed_manual_adjust" = "Address order reversed, manual adjustment needed";
"one_click_add_all" = "One click, add all";
"smart_grouping_auto_sorting" = "Smart grouping, automatic sorting";
"maintain_correct_visit_order" = "Maintain correct visit order";
"zero_errors_zero_repetition" = "Zero errors, zero repetition";
"import" = "Import";
"export" = "Export";
"import_addresses" = "Import Addresses";
"export_addresses" = "Export Addresses";
"import_from_file" = "Import from File";
"export_to_file" = "Export to File";
"file_format" = "File Format";
"csv_format" = "CSV Format";
"excel_format" = "Excel Format";
"json_format" = "JSON Format";
"import_success" = "Successfully imported %d addresses, all with valid coordinates.";
"export_success" = "Export Successful";
"import_error" = "Import Error";
"export_error" = "Export Error";
"navigation_app" = "Navigation App";
"apple_maps" = "Apple Maps";
"app_preferences" = "App Preferences";
"distance_unit" = "Distance Unit";
"current_language" = "Current Language";
"location_settings" = "Location Settings";
"current_region" = "Current Region";
"reset_location_settings" = "Reset Location Settings";
"reset_location_settings_description" = "This will reset your location region settings and may help fix address validation issues.";
"reset" = "Reset";
"info" = "Info";
"contact_us_header" = "Contact Us";
"contact_us_subheader" = "Have questions or suggestions? We'd love to help!";
"contact_options" = "Contact Options";
"email_us" = "Email Us";
"contact_form" = "Contact Form";
"contact_and_support" = "Contact & Support";
"common_questions" = "Common Questions";
"how_to_use" = "How to Use";
"subscription_faq" = "Subscription FAQ";
"navigation_help" = "Navigation Help";
"troubleshooting" = "Troubleshooting";
"help_howto_content" = "NaviBatch is a powerful route planning app that helps you optimize delivery routes, saving time and fuel. You can add multiple addresses, automatically optimize route order, and navigate to Apple Maps with one click.";
"help_subscription_content" = "NaviBatch offers both free and pro versions. The free version supports up to 20 addresses, while the pro version provides unlimited addresses and one-click group navigation features.";
"help_navigation_content" = "NaviBatch uses Apple Maps for navigation. You can navigate to each address individually, or use the grouping feature to navigate multiple addresses at once.";
"help_troubleshooting_content" = "If you encounter issues, please first ensure your device has network connectivity and location permissions are granted. If problems persist, please contact our support team.";
"actions" = "Actions";
"legal" = "Legal";
"show_look_around" = "Show View";
"hide_look_around" = "Hide View";
"map" = "Map";
"map_type" = "Map Type";
"standard" = "Standard";
"satellite" = "Satellite";
"hybrid" = "Hybrid";
"show_traffic" = "Show Traffic";
"current_location" = "Current Location";
"directions" = "Directions";
"distance_to" = "Distance to";
"eta" = "ETA";
"look_around" = "Look Around";
"locating_to_glen_waverley" = "Locating to Glen Waverley";
"network_error" = "Network Error";
"location_error" = "Location Error";
"permission_denied" = "Permission Denied";
"location_permission_required" = "Location Permission Required";
"camera_permission_required" = "Camera Permission Required";
"photo_library_permission_required" = "Photo Library Permission Required";
"please_try_again" = "Please Try Again";
"something_went_wrong" = "Something Went Wrong";
"invalid_input" = "Invalid Input";
"required_field" = "Required Field";
"no_internet_connection" = "No Internet Connection";
"server_error" = "Server Error";
"timeout_error" = "Request Timeout";
"data_not_found" = "Data Not Found";
"selection_limit_reached" = "Selection Limit Reached";
"selection_limit_description" = "You can select a maximum of %d addresses, you have selected %d";
"location_status_valid" = "Valid Range";
"address_validation_unknown" = "Unverified";
"address_validation_valid" = "Valid";
"address_validation_invalid" = "Invalid";
"address_validation_warning" = "Warning";
"address_validation_mismatch" = "Mismatch";
"device_not_support_scanning" = "Device does not support native scanning";
"requires_ios16_a12_chip" = "Requires iOS 16+ and A12 chip or later";
"debug_info" = "Debug Info:";
"address_confirmation" = "Address Confirmation";
"continue_scanning" = "Continue Scanning";
"confirm_add" = "Confirm Add";
"cannot_get_coordinates_scan_retry" = "Cannot get address coordinates, please enter manually or scan again";
"unknown_country" = "Unknown Country";
"unknown_city" = "Unknown City";
"please_enter_valid_address" = "Please enter at least one valid address";
"please_select_valid_address" = "Please select a valid address";
"add_address_failed" = "Failed to add address: %@";
"location_permission_required_for_current_location" = "Location permission is required to use current location. Please allow location access in Settings.";
"cannot_get_current_location_check_settings" = "Cannot get current location, please ensure location services are enabled.";
"cannot_get_current_location_address" = "Cannot get address information for current location.";
"get_current_location_failed" = "Failed to get current location: %@";
"location_status_warning" = "Warning Range";
"location_status_invalid" = "Invalid Location";
"location_status_unknown" = "Unknown Status";
"coordinates_origin_point" = "Invalid: Zero coordinates (0,0)";
"coordinates_invalid_nan" = "Invalid: Not a number";
"coordinates_out_of_range" = "Invalid: Coordinates out of valid range";
"coordinates_far_from_user" = "Warning: Location is far from your current position";
"coordinates_ocean" = "Warning: Location may be in ocean or uninhabited area";
"free_address_limit" = "Free Version Address Limit";
"address_count_limit" = "Address Count Limit";
"selected_addresses_can_import" = "You've selected %d addresses, and can import them.";
"selected_addresses_exceeds" = "You've selected %d addresses, exceeding the limit by %d.";
"import_success_with_warnings" = "Successfully imported %d addresses, %d with valid coordinates and %d with warnings.\n\nAddresses with warnings are marked and can be fixed manually after import.";
"supported_formats" = "Supported Formats";
"supported_format_csv" = "• CSV files: Address column should contain complete addresses";
"supported_format_json" = "• JSON data: Array containing address fields";
"supported_format_text" = "• Plain text: One address per line";
"download_history" = "Download History";
"input_address_data_url" = "Enter address data URL";
"import_result" = "Import Result";
"downloading" = "Downloading...";
"processing_data" = "Processing data...";
"google_drive_download_failed" = "Google Drive Download Failed";
"second_attempt_invalid_data" = "Second download attempt returned invalid data";

// Address History related
"confidence" = "Confidence";
"usage_count" = "Usage Count";
"last_used" = "Last Used";
"source" = "Source";
"created_at" = "Created At";
"statistics" = "Statistics";
"address_copied" = "Address Copied";
"cannot_parse_json" = "Cannot parse JSON data, please check file format";
"cannot_parse_json_with_error" = "Cannot parse JSON data: %@";
"cannot_read_file" = "Cannot read file: %@";
"failed" = "Failed";
"no_valid_addresses" = "No valid addresses found";
"supports_file_types" = "Supports CSV, TXT, and JSON files";
"tap_to_select_file" = "Tap to select file";
"input_company_name" = "Enter company name (optional)";
"imported_addresses_count" = "%d Addresses imported";
"excel_format_not_supported" = "Excel format is not supported";
"import_failed" = "Import Failed";
"free_version_address_limit" = "Free version allows maximum %d addresses.";
"current_address_count" = "Currently have %d addresses, can only add %d more.";
"upgrade_to_premium_unlimited" = "Upgrade to Premium for unlimited addresses!";
"route_address_limit" = "Current route has %d addresses, can only add %d more, for a total of %d maximum.";
"free_version_limit" = "Free Version Address Limit";
"import_all_n" = "Import All %d";
"cannot_import" = "Cannot Import";
"select_at_least_one" = "Please select at least one address";
"no_valid_addresses_found" = "No valid addresses found";
"import_success_all_valid" = "Successfully imported %d addresses, all with valid coordinates.";
"import_success_some_warnings" = "Successfully imported %d addresses, %d with valid coordinates and %d with warnings.";
"company_format" = "Company: %@";
"added_from_web_download" = "Added from web download";
"invalid_csv_row" = "Invalid CSV row";
"distance_warning" = "Distance from current location exceeds 200 km";
"not_in_australia" = "Coordinates not in Australia";
"invalid_address_data" = "Invalid address data";
"distance_warning_confirm" = "Address is far away, please confirm";
"coordinates_missing" = "Address coordinates missing";
"low_accuracy_address" = "Low accuracy address";
"address_partial_match" = "Address partially matches, may be inaccurate";
"address_outside_region" = "Address is outside target region";
"api_limit_reached" = "Map API request limit reached, please try again later";
"address_not_exist_or_incorrect_format" = "Address does not exist or format is incorrect";
"please_check_address_spelling" = "Please check address spelling";
"try_smaller_street_number" = "If street number is large, try using a smaller number on the same street";
"use_full_street_type_name" = "Use full street type name (e.g., 'Lane' instead of 'La')";
"try_add_more_address_details" = "Try adding more address details";
"cannot_find_address" = "Cannot find this address";
"please_check_spelling_or_add_details" = "Please check spelling or add more details";
"cannot_find_address_check_spelling" = "Cannot find address, please check spelling or add more details";
"address_not_set" = "Address not set";
"address_format_incomplete" = "Address format is incomplete or contains errors, please check and correct";
"location_service_denied" = "Location service denied, please check app permission settings";
"no_saved_groups" = "No Saved Groups";
"select_points_create_groups" = "Select delivery points to create groups";
"navigate_to_these_points" = "Navigate to These Points";
"confirm_remove_address" = "Are you sure you want to remove address \"%@\" from the group?";
"confirm_remove_this_address" = "Are you sure you want to remove this address from the group?";
"addresses_count" = "%d Addresses";
"no_saved_routes" = "No Saved Routes";
"no_saved_routes_description" = "You haven't saved any routes yet";
"all_routes" = "All Routes";
"address_count_format_simple" = "%d Addresses";
"delete_all_routes" = "Delete All Routes";
"navigate_to_all_points" = "Navigate to All Points";
"confirm_navigate_to_route" = "Are you sure you want to navigate to all points in route \"%@\"?";
"temp_navigation_group" = "Temporary Navigation Group";
"route_management" = "Route Management";
"manage_routes" = "Manage Routes";
"route_addresses" = "Route Addresses";
"no_addresses_in_route" = "This route has no addresses";
"must_keep_one_route" = "Must keep at least one route";
"confirm_delete_route" = "Are you sure you want to delete route \"%@\"? This action cannot be undone.";
"confirm_delete_all_routes" = "Confirm Delete All Routes";
"confirm_delete_all_routes_message" = "Are you sure you want to delete all routes? This action cannot be undone.";
"delete_all" = "Delete All";
"address_information" = "Address Information";
"group_belonging" = "Group Membership";
"view_map" = "View Map";
"delete_delivery_point" = "Delete Delivery Point";
"delivery_point_details" = "Delivery Point Details";
"confirm_deletion" = "Confirm Deletion";
"delete_delivery_point_confirmation" = "Are you sure you want to delete this delivery point? This action cannot be undone.";
"delivery_photos" = "Delivery Photos";
"view_delivery_photos" = "View Delivery Photos";
"no_photos_taken" = "No Photos Taken";
"take_photos" = "Take Photos";
"loading_photos" = "Loading photos...";
"photo_not_found" = "Photo Not Found";
"photo_deleted" = "Photo Has Been Deleted";
"share_photos" = "Share Photos";
"photo_capture_title" = "Photo Confirmation";
"door_number_photo_title" = "Door/Street Number Photo";
"package_label_photo_title" = "Package Label Photo";
"placement_photo_title" = "Placement Photo";
"door_number_photo_desc" = "Take a clear photo of the door number, ensuring digits/letters are visible";
"package_label_photo_desc" = "Take a photo of the package label, ensuring recipient information is clearly visible";
"placement_photo_desc" = "Take a photo of where the package was placed";
"swipe_to_switch" = "Swipe to switch photo type";
"photos_will_be_saved_to" = "Photos will be saved to album:";
"complete_photos" = "Complete Photos";
"photo_save_success" = "Photos saved successfully";
"photo_save_failure" = "Failed to save photo";
"no_photos_found" = "No Photos Found";
"photos_deleted_or_not_taken" = "Photos may have been deleted or not taken yet";
"share_photo" = "Share Photo";
"photo_capture_preview" = "Preview Mode - Camera Simulation";
"photo_capture_close" = "Close";
"camera_start_failed" = "Camera Start Failed";
"camera_start_failed_retry" = "Unable to start camera, please try again";
"camera_init_failed" = "Camera Initialization Failed";
"camera_access_failed" = "Cannot access camera";
"photo_processing_failed" = "Photo Capture Failed";
"photo_processing_failed_retry" = "Unable to process photo, please try again";
"photo_capture_progress" = "Progress: %d/%d";
"photo_captured_continue" = "Photo captured, continuing to %@";
"pending" = "Pending";
"in_progress" = "In Progress";
"select_delivery_status" = "Select Status";
"select_failure_reason" = "Select Failure Reason";
"delivery_status_pending" = "Pending";
"delivery_status_in_progress" = "In Progress";
"delivery_status_completed" = "Completed";
"delivery_status_failed" = "Failed";
"failure_reason_not_at_home" = "Customer Not At Home";
"failure_reason_wrong_address" = "Wrong Address";
"failure_reason_no_access" = "No Access to Location";
"failure_reason_rejected" = "Package Rejected";
"failure_reason_other" = "Other Reason";
"enter_custom_reason" = "Enter Custom Reason";
"custom_reason_placeholder" = "Please describe the reason...";
"custom_reason_required" = "Please enter a specific reason";
"failure_reason_required" = "Please select a failure reason";
"delivery_type_delivery" = "Delivery";
"delivery_type_pickup" = "Pickup";
"delivery_order_first" = "First";
"delivery_order_auto" = "Auto";
"delivery_order_last" = "Last";
"package_size_small" = "Small";
"package_size_medium" = "Medium";
"package_size_large" = "Large";
"package_type" = "Package Type";
"package_type_box" = "Box";
"package_type_bag" = "Bag";
"package_type_letter" = "Letter";
"one_click_navigation_grouping" = "One-Click Navigation Grouping";
"speed_60x_faster" = "60x Faster";
"goodbye_manual_address_adding" = "Say goodbye to tedious manual address adding";
"watch_detailed_demo" = "Watch Detailed Demo";
"upgrade_to_pro_now" = "Upgrade to Pro Now";
"free_trial_7_days" = "14-day free trial, cancel anytime";
"traditional_vs_navibatch_pro" = "Traditional Method vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "← Swipe left/right to view full comparison →";
"traditional_method" = "Traditional Method";
"drivers_get_lost_affect_efficiency" = "Drivers get lost, affecting delivery efficiency";
"repetitive_operations_waste_time" = "Repetitive operations waste significant time";
"total_time_60_seconds" = "Total time: 60 seconds";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Optimize routes, reduce driving distance";
"improve_delivery_efficiency_accuracy" = "Improve delivery efficiency and accuracy";
"speed_boost_60x" = "60x speed boost ⚡";
"total_time_1_second" = "Total time: 1 second";
"time_comparison" = "Time Comparison";
"traditional_method_problems" = "Traditional method problems:";
"each_address_3_5_seconds_14_total_60" = "• Each address takes 3-5 seconds, 35 addresses take about 2 minutes";
"repetitive_operations_cause_fatigue" = "• Repetitive operations easily cause user fatigue and errors";
"address_order_reversed_last_becomes_first" = "• Address order is reversed, last added address becomes first navigation point";
"need_manual_reverse_adding_takes_longer" = "• Need to manually add addresses in reverse order, taking even longer";
"navibatch_advantages" = "NaviBatch advantages:";
"add_14_addresses_1_second_60x_faster" = "• Add 35 addresses in just 1 second, 60x speed improvement";
"auto_maintain_correct_order_no_adjustment" = "• Automatically maintain correct visit order, no manual adjustment needed";
"zero_error_rate_no_repetition" = "• Zero error rate, no repetitive operations";
"save_59_seconds" = "Save 59 seconds!";
"speed_boost_60x_simple" = "60x speed boost";
"seconds_format" = "%d seconds";
"actual_benefits_one_click_navigation" = "Actual Benefits of Using One-Click Navigation Grouping";
"daily_savings" = "Daily Savings";
"daily_savings_value" = "30+ minutes";
"daily_savings_description" = "Reduce route setup time";
"monthly_savings" = "Monthly Savings";
"monthly_savings_value" = "15+ hours";
"monthly_savings_description" = "Can be used for increased deliveries or rest";
"fuel_savings_value" = "Up to 30%";
"fuel_savings_description" = "Reduce driving distance through route optimization";
"income_increase" = "Income Increase";
"income_increase_value" = "20-25%";
"income_increase_description" = "Through increased daily delivery volume";
"trial" = "Trial";
"days_left" = "days left";
"free_plan_description" = "Up to 20 addresses";
"pro_plan_active" = "Pro plan active";
"expert_plan_active" = "Expert plan active";
"trial_active" = "Trial period active";
"trial_expires_on" = "Trial expires on %@";
"address_validation_mode" = "Address Validation Mode";
"validation_description" = "Choose the strictness level for address validation. Strict mode is recommended for delivery businesses to ensure address accuracy.";
"current_settings" = "Current Settings";
"validation_mode_format" = "Validation Mode: %@";
"threshold_score_format" = "Pass Threshold: %d points";
"validation_example" = "Validation Example";
"original_address_example" = "Original Address: 55 Batten Street, Glen Waverley, VIC 3150";
"reverse_address_example" = "Reverse Address: 5 Batten St, Glen Waverley, VIC 3150";
"house_number_difference" = "House Number Difference: 50 (Extremely High Risk)";
"result_label" = "Result:";
"may_pass_warning" = "May Pass ⚠️";
"will_not_pass" = "Will Not Pass ❌";
"real_case_example" = "Real Case Example";
"real_case_description" = "Based on the real address validation case you provided. Both strict and perfect modes will reject this high-risk address.";
"address_validation_settings" = "Address Validation Settings";
"clear" = "Clear";
"view_details" = "View Details";
"create_test_data" = "Create Test Data";
"manual_snapshot" = "Manual Snapshot";
"start_location_updates" = "Start Location Updates";
"stop_location_updates" = "Stop Location Updates";
"user_location_marker_test" = "User Location Marker Test";
"location_animation_control" = "Location & Animation Control";
"current_location_format" = "Current Location: %@, %@";
"waiting_for_location" = "Waiting for location information...";
"diagnostic_tools" = "Diagnostic Tools";
"storekit_diagnostics" = "StoreKit Diagnostics";
"subscription_function_test" = "Subscription Function Test";
"localization_test" = "Localization Test";
"address_validation_demo" = "Address Validation Demo";
"localization_tools" = "Localization Tools";
"coordinate_debug_tools" = "Coordinate Debug Tools";
"smart_abbreviation_expansion_test" = "Smart Abbreviation Expansion Test";
"subscription_restore_diagnostics" = "Subscription Restore Diagnostics";
"batch_address_import_test" = "Batch Address Import Test";
"test_import_1000_addresses_memory" = "Test importing 1000 addresses memory usage";
"map_rendering_test" = "Map Rendering Test";
"test_map_display_markers_memory" = "Test map displaying large number of markers memory usage";
"select_test_language" = "Select Test Language";
"discover_60x_speed_boost" = "Discover 60x Speed Boost";
"see_60x_speed_demo" = "See 60x Speed Demo";
"free_vs_pro_comparison" = "Free vs Pro Comparison";
"our_free_beats_competitors_paid" = "Our FREE plan beats competitors' PAID plans!";
"features" = "Features";
"up_to_20" = "Up to 20";
"unlimited" = "Unlimited";
"smart_optimization" = "Smart Route Optimization";
"up_to_20_percent" = "Up to 20%";
"free_tier_grouping_limit" = "Up to 10 addresses";
"pro_tier_unlimited_grouping" = "Unlimited grouping";
"free_tier_navigation_limit" = "1 group (up to 10 addresses)";
"pro_tier_unlimited_navigation" = "Multiple groups (unlimited)";
"file_not_found" = "File Not Found";
"sample_file_not_available" = "Sample file is temporarily unavailable";
"file_copy_failed" = "Failed to prepare sample file for sharing";

// AI Configuration strings
"recognition_settings" = "Recognition Settings";
"recognition_mode" = "Recognition Mode";
"enable_advanced_recognition" = "Enable Advanced Recognition";
"algorithm_selection" = "Algorithm Selection";
"accuracy_threshold" = "Accuracy Threshold";
"fallback_options" = "Fallback Options";
"auto_fallback_to_basic" = "Auto fallback to basic recognition";
"max_retry_attempts" = "Max retry attempts";
"configuration_info" = "Configuration Info";
"view_recognition_logs" = "View Recognition Logs";
"reset_to_defaults" = "Reset to Defaults";
"unknown_algorithm" = "Unknown Algorithm";
"enabled" = "Enabled";
"disabled" = "Disabled";
"advanced_recognition" = "Advanced Recognition";
"algorithm" = "Algorithm";

// Rate Limit Warning strings
"api_rate_limit" = "API Rate Limit";
"please_wait_and_try_again" = "Please wait and try again";
"rate_limit_protection_message" = "To protect Apple Maps service, we need to temporarily slow down processing speed.";
"rate_limit_normal_protection" = "• This is a normal protection mechanism and will not affect data quality";
"rate_limit_batch_suggestion" = "• Recommend processing large amounts of addresses in batches, no more than 10 per batch";
"rate_limit_auto_resume" = "• Processing will automatically resume in %d seconds";
"continue_waiting" = "Continue Waiting";

// Simple Loading Messages
"importing_addresses_please_wait" = "Importing addresses, please wait...";
"processing_addresses" = "Processing addresses...";
"please_keep_app_open" = "Please keep the app open";
"progress_with_rate_limit" = "Processing slowed due to rate limit - %.0f%% complete";

// Video Processing Messages
"extracting_video_frames" = "Extracting video frames...";
"analyzing_frame_similarity" = "Analyzing frame similarity...";
"detecting_scroll_direction" = "Detecting scroll direction...";
"stitching_long_image" = "Stitching long image...";
"processing_complete" = "Processing complete";
"unable_to_extract_frames" = "Unable to extract frames from video";
"stitching_failed" = "Stitching failed";
"processing_failed_error" = "Processing failed: %@";
"address_processing_failed_retry" = "Address processing failed: %@. Please retry or enter the complete address manually.";
"address_processing_timeout" = "Address processing timeout, please retry or enter address manually";
"address_has_problem_fix" = "This address has a problem: %@. Please correct the address or get coordinates again.";
"address_problem_found" = "Address Problem Found";
"address_problem_details" = "During %@ process, the following %d addresses could not get accurate coordinates, which may affect navigation accuracy:\n\n• %@\n\nWe recommend checking the spelling or format of these addresses.";

// Address problem reasons
"geocoding_failed" = "Geocoding failed";
"invalid_address_format" = "Invalid address format";
"empty_address" = "Empty address";
"address_too_short" = "Address too short";
"no_valid_coordinate" = "Cannot get valid coordinates";

// Smart Address Retry Service Phases
"phase_idle" = "Idle";
"phase_collecting_problems" = "Collecting problem addresses";
"phase_ai_correction" = "AI smart correction";
"phase_user_confirmation" = "Waiting for user confirmation";
"phase_completed" = "Processing completed";

// Progress Information
"waiting_for_processing" = "Waiting for processing";
"collecting_problem_addresses" = "Collecting problem addresses...";
"ai_correction_in_progress" = "AI correction in progress...";
"current_processing" = "Current";
"background_processing" = "Background processing...";

// Batch Processing
"batch" = "batch";

// Detailed Progress Messages
"addresses_processed" = "Addresses Processed";
"current_batch" = "Current Batch";
"successful" = "Successful";
"warnings" = "Warnings";
"estimated_time_remaining" = "Estimated Time Remaining";
"currently_processing" = "Currently Processing";
"minutes" = "minutes";
"seconds" = "seconds";
"api_rate_limit_info" = "Processing speed is optimized to ensure accurate results";
"geocoding_accuracy_info" = "Each address is verified for precise GPS coordinates";

// Welcome Screen
"welcome_to" = "Welcome to";
"delivery_driver_smart_assistant" = "Smart Navigation Assistant for Delivery Drivers";
"addresses_batch_processing" = "addresses";
"batch_processing" = "Batch Processing";
"hours_daily_savings" = "hours";
"daily_savings" = "Daily Savings";
"efficiency_improvement" = "efficiency";
"improvement_rate" = "Improvement Rate";
"smart_scanning" = "Smart Scanning";
"smart_scanning_description" = "Photo recognition of addresses, automatically extracts delivery information";
"batch_navigation" = "Batch Navigation";
"batch_navigation_description" = "Add multiple addresses with one click, intelligently plan optimal routes";
"time_saving" = "Time Saving";
"time_saving_description" = "Save 2+ hours daily, improve delivery efficiency by 300%";
"professional_tools" = "Professional Tools";
"professional_tools_description" = "Professional navigation tools tailored for delivery drivers";
"for_best_experience_permissions" = "For the best experience, NaviBatch requires the following permissions:";
"location_permission" = "Location";
"camera_permission" = "Camera";
"photos_permission" = "Photos";
"start_using_navibatch" = "Start Using NaviBatch";

// Onboarding Screen
"step_progress_format" = "Step %d of %d";
"photo_scan_addresses" = "Photo Scan Addresses";
"photo_scan_description" = "Use camera to scan delivery documents, AI automatically recognizes address information";
"photo_scan_instruction" = "Click camera button, aim at delivery documents and take photo";
"batch_add_addresses" = "Batch Add Addresses";
"batch_add_description" = "Add multiple delivery addresses at once, supports manual input and file import";
"batch_add_instruction" = "Can add up to 35 addresses for batch processing";
"smart_route_optimization" = "Smart Route Optimization";
"route_optimization_description" = "AI algorithm automatically plans optimal delivery routes, saving time and fuel";
"route_optimization_instruction" = "Click optimize button, system automatically calculates best route";
"one_click_batch_navigation" = "One-Click Batch Navigation";
"batch_navigation_description" = "Directly launch navigation app, deliver in optimized order";
"batch_navigation_instruction" = "Select addresses and click GO button to start navigation";
"operation_tips" = "Operation Tips";
"previous_step" = "Previous";
"next_step" = "Next";
"start_using" = "Start Using";
"skip_guide" = "Skip Guide";

// Splash Screen
"app_tagline" = "Batch Navigation, Efficient Delivery";
"initializing" = "Initializing...";

// Sample File Download
"download_sample_files" = "Download Sample Files";
"sample_files_help_text" = "Download example files to see the correct format";
"sample_file_format" = "Sample %@";

// Look Around Preview strings
"look_around_ios_16_required" = "Look Around is available on iOS 16 and later";
"no_look_around_data" = "No Look Around data available for this location";
"try_other_address" = "Please try another address";
"enter_address" = "Enter address";

// Subscription Alert strings
"later" = "Later";
"free_user_grouping_limit_message" = "You've reached the free version limit of 10 addresses for grouping. Upgrade to Pro for unlimited grouping and one-click navigation!";
"upgrade_to_unlock_auto_grouping" = "Upgrade to Pro to unlock auto grouping feature";
"free_user_grouping_limit_reached" = "You've reached the free version limit of %d addresses for grouping. Upgrade to Pro for unlimited grouping!";
"upgrade_to_unlock_more_grouping" = "Upgrade to Pro to unlock unlimited grouping";
"free_user_max_groups_reached" = "You've reached the free version limit of %d groups. Upgrade to Pro for unlimited groups!";

// Quick Location strings
"quick_locate" = "Quick Locate";
"enter_number" = "Enter Number";
"enter_number_to_locate" = "Enter number to locate";
"please_enter_number" = "Number";
"cancel" = "Cancel";
"locate" = "Locate";
"number_not_found" = "Number %d not found";

// Optimization Mode Selection
"optimization_mode_title" = "Select Optimization Mode";
"optimization_mode_smart_title" = "Smart Optimization";
"optimization_mode_smart_subtitle" = "Automatically test all delivery points as starting points to find the shortest route";
"optimization_mode_manual_title" = "Manual Start Point Selection";
"optimization_mode_manual_subtitle" = "Choose driver location or specific delivery point";

// Start Point Selection
"start_point_selection_title" = "Select Starting Point";
"start_point_driver_location" = "Driver Current Location";
"start_point_current_gps" = "Use current GPS location";
"start_point_default_location" = "Use default location";
"start_point_delivery_points_section" = "Select delivery point as starting position";
"start_point_sort_number" = "Sort: %@";

// App Store Update Notes v1.0.8
"update_v108_title" = "NaviBatch v1.0.8 - Auto Delivery Classification";
"update_v108_subtitle" = "Automatic regional classification system";

// Main Features
"update_v108_feature_regional_classification" = "Auto Regional Classification";
"update_v108_feature_ai_enhancement" = "Enhanced Recognition System";
"update_v108_feature_user_experience" = "Improved User Experience";

// Regional Classification
"update_v108_us_delivery" = "US Delivery: SpeedX, GoFo, UNIUNI, Amazon Flex, iMile, LDS EPOD, PIGGY, YWE";
"update_v108_au_delivery" = "Australia Delivery: iMile (primary service)";
"update_v108_auto_detect" = "Auto-detect US/Australia address formats";

// Recognition Enhancement
"update_v108_region_rules" = "Region-specific address rules (US 5-digit vs AU 4-digit postcodes)";
"update_v108_company_features" = "Delivery company-specific features";
"update_v108_imile_support" = "iMile dual-region support";

// User Experience
"update_v108_gofo_priority" = "GoFo prioritized in US delivery group";
"update_v108_clean_interface" = "Cleaner interface with regional grouping";
"update_v108_faster_selection" = "Faster delivery app selection";

// Improvements
"update_v108_improvements_title" = "IMPROVEMENTS:";
"update_v108_better_detection" = "Better address format detection";
"update_v108_reduced_confusion" = "Reduced selection confusion";
"update_v108_enhanced_accuracy" = "Enhanced recognition accuracy for regional addresses";
"update_v108_optimized_workflow" = "Optimized delivery workflow";

"update_v108_perfect_for" = "Perfect for delivery drivers using multiple platforms!";

// Database Status
"database_status" = "Database Status";
"total_addresses" = "Total Addresses";
"hit_rate" = "Hit Rate";
"database_size" = "Database Size";
"most_used_addresses" = "Most Used Addresses";
"frequently_used" = "Frequently Used";

// Country Detection and Selection
"detecting_location" = "Detecting your location...";
"detected_in_country" = "Detected in %@";
"delivery_services_for_country" = "%@ Delivery Services";
"confirm_country" = "Confirm";
"change_country" = "Change";
"select_region" = "Select Region";
"popular_regions" = "Popular Regions";
"all_regions" = "All Regions";
"supported_services_count" = "%d supported services";
"low_confidence_detection" = "Low detection confidence, please confirm";

// Country Names
"country_united_states" = "United States";
"country_australia" = "Australia";
"country_canada" = "Canada";
"country_united_kingdom" = "United Kingdom";
"country_germany" = "Germany";
"country_france" = "France";
"country_japan" = "Japan";
"country_china" = "China";

// Collapsible Button Group
"collapsible_button_side_toggle" = "Side Toggle";
"collapsible_button_map_mode" = "Map Mode";
"collapsible_button_search" = "Search Location";
"collapsible_button_visibility" = "Show/Hide";
"collapsible_button_counter" = "Order Count";

"collapsible_button_side_toggle_description" = "Switch button group display position";
"collapsible_button_map_mode_description" = "Switch map display mode";
"collapsible_button_search_description" = "Search location by number";
"collapsible_button_visibility_description" = "Show/hide completed orders";
"collapsible_button_counter_description" = "Display order count";

"collapsible_button_customization_title" = "Select Favorite Buttons";
"collapsible_button_customization_subtitle" = "Choose buttons you want to display in the quick bar (up to 4 buttons)";
"collapsible_button_customization_navigation_title" = "Customize Buttons";

// Trial Promotion strings
"trial_promotion_title_1" = "Unlock Pro Features";
"trial_promotion_title_2" = "60x Faster Delivery";
"trial_promotion_title_3" = "Save 30% on Fuel Costs";
"trial_promotion_subtitle_1" = "Smart navigation tool designed for delivery drivers";
"trial_promotion_subtitle_2" = "Stop adding addresses one by one - batch navigate with one click";
"trial_promotion_feature_unlimited_addresses" = "Unlimited Addresses";
"trial_promotion_feature_unlimited_addresses_desc" = "No more 20-address limit, support bulk deliveries";
"trial_promotion_feature_60x_speed" = "60x Speed Boost";
"trial_promotion_feature_60x_speed_desc" = "Add all addresses to navigation with one tap";
"trial_promotion_feature_smart_optimization" = "Smart Route Optimization";
"trial_promotion_feature_smart_optimization_desc" = "AI calculates shortest paths, reduces detours";
"trial_promotion_feature_unlimited_groups" = "Unlimited Groups";
"trial_promotion_feature_unlimited_groups_desc" = "Create groups by area or time for better organization";
"trial_promotion_start_trial" = "Start 60-Day Free Trial";
"trial_promotion_terms" = "Subscription terms apply. Cancel anytime.";

// Group Detail View
"optimize_group_route" = "Optimize Group Route";
"route_optimization_failed" = "Route Optimization Failed";
"optimizing_route" = "Optimizing...";
"route_optimization_result" = "Route Optimization Result";
"route_optimization_success" = "Route optimization successful! Saved %.1f km distance (%.1f%%)";
"route_already_optimal" = "Current route is already optimal, no adjustment needed.";

// Delivery Service
"delivery_service" = " Delivery";

// Map Mode Selector
"map_mode_selector_title" = "Map Mode";
"map_mode_driving" = "Driving";
"map_mode_satellite" = "Satellite";
"done" = "Done";

// Scanner Interface Localization
"scanner" = "Scanner";
"detected_in_australia" = "Detected in Australia";
"australia" = "Australia";
"just_photo" = "Just Photo";
"imile" = "iMile";
"select_photos" = "Select Photos";
"select_videos" = "Select Videos";
"select_pdfs" = "Select PDFs";
"photo_recognition" = "Photo Recognition";
"video_to_long_image" = "Video to Long Image";
"pdf_recognition" = "PDF Recognition";
"upgrade_unlock" = "Upgrade to Unlock";
"confirm" = "Confirm";
"change" = "Change";

// Image Analysis Related
"start_analysis" = "Start Analysis";
"analyze_selected_images" = "Analyze selected images";
"image_viewer" = "Image Viewer";
"close" = "Close";
"delete_image" = "Delete Image";

// Route Export Related
"copy_route_numbers" = "Copy";
"copied" = "Copied!";
"copied_route_numbers_success" = "Copied %d route numbers to clipboard";

// Third Party Number Edit Related
"edit_third_party_number" = "Edit Third Party Number";
"edit_third_party_number_description" = "Edit %@ sort number";
"sort_number" = "Sort Number";
"enter_sort_number" = "Enter sort number";
"duplicate_third_party_number" = "Duplicate Third Party Number";
"confirm_save" = "Confirm Save";
"duplicate_number_message" = "Third party number '%@' is already used by the following addresses:\n\n%@\n\nAre you sure you want to use the duplicate number?";

// OCR Processing Status
"extracting_ocr_text" = "Extracting OCR text...";
"switching_processing_method" = "Switching processing method...";

// Sort Order Options
"sort_options_title" = "Sort Options";
"sort_order_entry" = "Entry";
"sort_order_optimized" = "Optimized";
"sort_order_speedx" = "SpeedX";
"sort_order_third_party" = "3rd Party";

// SpeedX Sequence Check
"speedx_sequence_check" = "SpeedX Sequence Check";
"missing_stops" = "Missing Stops:";
"incomplete_stops" = "Incomplete Stops:";
"stops_not_found" = "Stops %@ not found";
"stops_truncated" = "Stops %@ information truncated";
"supplement_photo" = "Supplement Photo:";
"supplement_photo_description" = "Upload screenshots containing missing stops, system will only process missing numbers %@";
"ai_prediction" = "AI Smart Prediction:";
"ai_prediction_description" = "Based on existing address patterns, AI can predict missing stop addresses";
"analyzing_pattern" = "Analyzing address patterns...";
"analysis_failed" = "Analysis failed: %@";
"cannot_identify_pattern" = "Cannot identify address pattern";
"cannot_predict_address" = "Cannot predict missing addresses";
"suggestions" = "Suggestions:";
"try_ai_prediction" = "• Try AI smart prediction for missing addresses";
"retake_photo_include_all" = "• Retake photo to include all stops";
"check_missing_info" = "• Check if missing relevant information";
"process_in_batches" = "• Can process in batches and combine later";
"supplement_photo_recommended" = "Supplement Photo (Recommended)";
"ai_predict_address" = "AI Smart Predict Address";
"retake_photo_restart" = "Retake Photo (Start Over)";
"retake_photo" = "Retake Photo";
"continue_use" = "Continue";
"and_more_stops" = "%@ and %d more";

// Detailed Missing Analysis
"detailed_missing_analysis" = "🔍 Detailed Missing Analysis:";
"consecutive_missing_range" = "📍 Consecutive Missing Range:";
"scattered_missing_points" = "📍 Scattered Missing Points:";
"missing_statistics" = "📊 Missing Statistics:";
"total_missing" = "Total Missing: %d stops";
"coverage_rate" = "Coverage Rate: %.1f%% (%d/%d)";
"risk_level_low" = "Risk Level: Low ✅";
"risk_level_medium" = "Risk Level: Medium ⚠️";
"risk_level_high" = "Risk Level: High ❌";
"consecutive_stops_impact" = "%d consecutive stops";
"area_coverage_impact" = "Impact: Entire area may be uncovered";
"start_area" = "Start area";
"middle_area" = "Middle area";
"end_area" = "End area";

// Recommended Actions
"recommended_actions_priority" = "🎯 Recommended Actions (By Priority):";
"action_ai_prediction" = "1️⃣ 【AI Smart Prediction】- Fastest Solution";
"action_targeted_retake" = "2️⃣ 【Targeted Retake】- Precise Solution";
"action_complete_retake" = "3️⃣ 【Complete Retake】- Safe Solution";
"professional_tips" = "💡 Professional Tips:";

// AI Prediction Details
"ai_prediction_success_rate" = "Success Rate: ~85%, suitable for regular patterns";
"ai_prediction_estimate" = "Estimated Addition: %d-%d addresses";
"ai_based_on_patterns" = "Predict missing addresses based on identified address patterns";

// Targeted Retake Details
"targeted_retake_focus" = "Focus on %@ area (consecutive missing)";
"targeted_retake_suggestion" = "Suggestion: Slow down scrolling, stay in that area for 2-3 seconds";
"targeted_retake_preserve" = "System will only process missing numbers, preserving existing results";

// Complete Retake Details
"complete_retake_description" = "Re-record complete video";
"complete_retake_suggestion" = "Suggestion: Stay in each area for 1-2 seconds, ensure clarity";
"complete_retake_risk" = "Risk: May duplicate work";

// Professional Tips
"consecutive_missing_cause" = "%@ consecutive missing may be caused by scrolling too fast";
"try_ai_first_tip" = "Suggest trying AI prediction first to save time";
"supplement_focus_tip" = "If retaking needed, focus on missing intervals";

// Total Count Validation
"total_count_detected" = "Total count detected: %d";
"count_mismatch_warning" = "Count mismatch: Expected %d, actual %d";
"count_perfect_match" = "Count match: Recognition complete ✅";
"missing_orders_suggestion" = "Possible missing orders, suggest checking video completeness";

// Video Processing Localization
"ocr_analyzing_all_frames" = "🔍 OCR analyzing all frames...";
"ocr_content_deduplication" = "📝 OCR content deduplication...";
"image_similarity_deduplication" = "🖼️ Image similarity deduplication...";
"ocr_fast_grouping" = "🚀 OCR fast grouping...";
"intelligent_group_deduplication" = "📝 Intelligent group deduplication...";
"ocr_processing_frame_progress" = "🔍 OCR processing frame %d/%d...";
"unable_to_extract_video_frames" = "Unable to extract video frames";

// Image Order Control
"reverse" = "Reverse";

// Address Limit Error Messages
"free_version_max_addresses_single" = "Free version allows up to %d addresses. Please upgrade to Pro for unlimited addresses!";
"free_version_max_addresses_batch" = "Free version allows up to %d addresses. Currently have %d addresses, can only add %d more.\nYou selected %d addresses, exceeding the limit by %d.\n\nUpgrade to Pro for unlimited addresses!";
"paid_version_max_addresses_batch" = "Current route has %d addresses, can only add %d more, maximum %d addresses total.\nYou selected %d addresses, exceeding the limit by %d.";

