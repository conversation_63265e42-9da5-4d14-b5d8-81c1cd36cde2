/*
  InfoPlist.strings
  NaviBatch

  Created on 2023-11-16.

*/

// 权限描述
"NSLocationWhenInUseUsageDescription" = "NaviBatch needs to use location services to get your current location and show your position on the map for navigation";
"NSCameraUsageDescription" = "Navi<PERSON><PERSON> needs to use the camera to scan addresses and take delivery photos";
"NSPhotoLibraryUsageDescription" = "Navi<PERSON><PERSON> needs to access the photo library to save delivery photos";
"NSPhotoLibraryAddUsageDescription" = "NaviBatch needs to access the photo library to save delivery photos";
"NSDocumentsFolderUsageDescription" = "NaviBatch needs to access the documents folder to import and export address files";
