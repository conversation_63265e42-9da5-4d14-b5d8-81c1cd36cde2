/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "언어 설정";
"system_language" = "시스템 언어";
"system_language_section" = "시스템 설정";
"languages" = "언어";
"language_info_title" = "언어 설정 정보";
"language_info_description" = "언어 설정을 변경하면 앱이 선택한 언어로 텍스트를 표시합니다. 일부 콘텐츠는 새 언어 설정을 완전히 적용하기 위해 앱을 다시 시작해야 할 수 있습니다.";
"restart_required" = "다시 시작 필요";
"restart_app_message" = "언어 변경 사항을 완전히 적용하려면 앱을 다시 시작하세요.";
"restart_now" = "지금 다시 시작";
"restart_later" = "나중에 다시 시작";

// MARK: - Common UI Elements
"close" = "닫기";
"cancel" = "취소";
"save" = "저장";
"edit" = "편집";
"delete" = "삭제";
"done" = "완료";
"next" = "다음";
"back" = "뒤로";
"confirm" = "확인";
"error" = "오류";
"success" = "성공";
"warning" = "경고";
"loading" = "로딩 중...";
"search" = "검색";
"settings" = "설정";
"help" = "도움말";
"about" = "정보";
"menu" = "메뉴";
"understand" = "이해했습니다";

// MARK: - Navigation
"navigation" = "네비게이션";
"start_navigation" = "네비게이션 시작";

// MARK: - Subscription
"subscription" = "구독";
"upgrade_to_pro" = "프로로 업그레이드";
"upgrade_description" = "원클릭 내비게이션 그룹화, 수동 조작보다 60배 빠름";
"restore_purchases" = "구매 복원";
"learn_more" = "더 알아보기";
"upgrade_your_plan" = "플랜 업그레이드";
"one_click_navigation_description" = "원클릭으로 주소를 그룹화하여 시간과 연료를 절약";
"current_plan" = "현재 플랜";
"upgrade" = "업그레이드";
"maybe_later" = "나중에";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "무료";
"pro_tier_price" = "$29.99/월";
"expert_tier_price" = "$249.99/년";
"free_tier_description" = "개인 및 소규모 비즈니스, 많은 정차점이 필요하지 않은 경우";
"pro_tier_description" = "원클릭 내비게이션 그룹화와 무제한 주소가 필요한 사용자";
"expert_tier_description" = "Pro와 동일하지만 연간 플랜으로 31% 절약";
"route_optimization" = "경로 최적화";
"unlimited_routes" = "무제한 경로";
"unlimited_optimizations" = "무제한 최적화";
"max_15_addresses" = "경로당 최대 15개의 주소";
"save_fuel_30" = "연료 최대 30% 절약";
"unlimited_addresses" = "✨ 무제한 주소 ✨";
"one_click_navigation" = "⚡ 원클릭 내비게이션 그룹화 - 60배 빠름 ⚡";
"package_finder" = "패키지 위치 찾기";
"annual_savings" = "연간 절약";
"switched_to_free" = "무료 플랜으로 전환됨";
"switched_to_subscription" = "구독 플랜으로 전환됨";
"unlimited_stops" = "무제한 정차점";
"plan_as_many_stops_as_needed" = "제한 없이 원하는 수만큼 배송 지점 추가";

// MARK: - Address Input
"enter_or_search_address" = "주소 입력 또는 검색";
"search_results_count" = "검색 결과: %d";
"no_matching_addresses" = "일치하는 주소를 찾을 수 없음";
"search_address_failed" = "주소 검색 실패: %@";
"address_search_no_response" = "주소 검색 응답 없음";
"cannot_get_address_coordinates" = "주소 좌표를 가져올 수 없음";
"speech_recognizer_unavailable" = "음성 인식기를 사용할 수 없음";
"microphone_permission_denied" = "마이크 권한이 거부됨";
"speech_recognition_permission_denied" = "음성 인식 권한이 거부됨";
"listening" = "듣는 중...";
"recording_failed" = "녹음 시작 실패: %@";
"cannot_get_coordinates_retry" = "주소 좌표를 가져올 수 없습니다. 수동으로 입력하거나 다시 시도하세요";
"cannot_create_recognition_request" = "인식 요청을 생성할 수 없음";

// MARK: - Saved Address Picker
"search_address" = "주소 검색";
"no_saved_addresses" = "저장된 주소 없음";
"no_saved_addresses_description" = "아직 주소를 저장하지 않았거나 필터 조건과 일치하는 주소가 없습니다";
"select_address_book" = "주소록 선택";

// MARK: - Menu
"menu" = "메뉴";
"routes" = "경로";
"address_book" = "주소록";
"saved_routes" = "저장된 경로";
"manage_your_routes" = "경로 계획 관리";
"manage_your_addresses" = "자주 사용하는 주소 관리";
"settings" = "설정";
"preferences" = "환경설정";
"set_custom_start_point" = "사용자 지정 시작점 설정";
"current_start_point" = "현재 시작점: %@";
"support" = "지원";
"contact_us" = "문의하기";
"contact_us_description" = "질문이나 제안이 있으신가요? 언제든지 문의해 주세요!";
"help_center" = "도움말 센터";
"subscription" = "구독";
"upgrade_to_pro" = "프로로 업그레이드";
"upgrade_description" = "원클릭 내비게이션 그룹화, 수동 조작보다 60배 빠름";
"open_subscription_view" = "구독 화면 열기";
"open_subscription_view_description" = "중간 계층을 건너뛰고 구독 화면 직접 표시";
"restore_purchases_failed" = "구매 복원 실패: %@";
"about" = "정보";
"rate_us" = "평가하기";
"rate_us_description" = "귀하의 피드백은 중요하며 앱을 개선하는 데 도움이 됩니다!";
"share_app" = "앱 공유";
"share_app_text" = "NaviBatch, 놀라운 경로 계획 앱을 사용해 보세요!";
"about_app" = "앱 정보";
"developer_tools" = "개발자 도구";
"coordinate_debug_tool" = "좌표 디버그 도구";
"batch_fix_addresses" = "일괄 주소 수정";
"clear_database" = "데이터베이스 지우기";
"clear_database_confirmation" = "이 작업은 경로, 주소 및 그룹을 포함한 모든 데이터를 삭제합니다. 이 작업은 되돌릴 수 없습니다. 계속하시겠습니까?";
"confirm_clear" = "지우기 확인";
"version_info" = "버전 %@ (%@)";
"current_system_language" = "현재 시스템 언어";
"reset_to_system_language" = "시스템 언어로 재설정";
"language" = "언어";
"language_settings" = "언어 설정";

// MARK: - Address Edit
"address" = "주소";
"coordinates" = "좌표";
"distance_from_current_location" = "현재 위치에서의 거리";
"address_info" = "주소 정보";
"update_coordinates" = "좌표 업데이트";
"fix_address" = "주소 수정";
"prompt" = "프롬프트";
"confirm" = "확인";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "좌표를 업데이트하기 전에 주소를 수정해 주세요";
"coordinates_update_success" = "좌표가 성공적으로 업데이트됨";
"coordinates_update_failure" = "좌표 업데이트 실패";
"save_failure" = "저장 실패: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "저장된 주소 없음";
"no_saved_addresses_message" = "아직 저장된 주소가 없습니다";
"add_new_address" = "새 주소 추가";
"address_title" = "주소";
"add" = "추가";
"refresh" = "새로고침";
"notes" = "메모";
"address_details" = "주소 상세정보";
"favorite" = "즐겨찾기";
"edit_address" = "주소 편집";
"cancel" = "취소";
"save" = "저장";
"confirm_delete" = "삭제 확인";
"delete" = "삭제";
"delete_address_confirmation" = "이 주소를 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.";
"edit" = "편집";
"address_marker" = "주소";
"address_label" = "주소:";
"notes_label" = "메모:";
"created_at_label" = "생성 시간:";
"open_in_maps" = "지도에서 열기";
"copy_address" = "주소 복사";
"address_details_title" = "주소 상세정보";

// MARK: - Route Detail
"start_end_point" = "시작/종료 지점";
"start_point" = "시작 지점";
"end_point" = "종료 지점";
"route_info" = "경로 정보";
"address_count" = "주소 수";
"address_count_format" = "%d개 주소";
"points_count_format" = "%d개 지점";
"additional_points_format" = "+%d개 지점";
"export_route" = "경로 내보내기";
"navigate" = "네비게이션";
"address_list" = "주소 목록";
"no_addresses" = "주소 없음";
"no_addresses_message" = "이 경로에는 아직 주소가 없습니다";

// MARK: - Route Bottom Sheet
"address_point_start" = "시작 지점";
"address_point_stop" = "정차 지점";
"address_point_end" = "종료 지점";
"route_name" = "경로 이름";
"save" = "저장";
"new_route" = "새 경로";
"saved_route" = "저장된 경로";
"edit" = "편집";
"loading" = "로딩 중...";
"plan_route" = "경로 계획";
"clear_all" = "모두 지우기";
"avoid" = "회피:";
"toll_roads" = "유료 도로";
"highways" = "고속도로";
"processing_addresses" = "주소 처리 중...";
"same_start_end_point" = "같은 주소를 시작 및 종료 지점으로 설정했습니다";
"add_start_point" = "시작 지점 추가";
"swipe_left_to_delete" = "← 좌측으로 스와이프하여 삭제";
"delete" = "삭제";
"add_new_address" = "새 주소 추가";
"add_end_point" = "종료 지점 추가";

// MARK: - Simple Address Sheet
"address_title" = "주소";
"enter_and_select_address" = "주소 입력 및 선택";
"current_search_text" = "현재 검색 텍스트: %@";
"search_results_count" = "검색 결과: %d";
"no_matching_addresses" = "일치하는 주소를 찾을 수 없음";
"add_address" = "주소 추가";
"edit_address" = "주소 편집";
"selected_coordinates" = "선택된 좌표";
"company_name_optional" = "회사명 (선택사항)";
"url_optional" = "URL (선택사항)";
"favorite_address" = "즐겨찾는 주소";
"set_as_start_and_end" = "시작 및 종료 지점으로 설정";
"address_book" = "주소록";
"batch_paste" = "일괄 붙여넣기";
"file_import" = "파일 가져오기";
"web_download" = "웹 다운로드";
"cancel" = "취소";
"saving" = "저장 중...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "배송 지점 관리";
"information_category" = "정보 카테고리";
"address_info" = "주소 정보";
"package_info" = "패키지 정보";
"vehicle_position" = "차량 위치";
"delivery_info" = "배송 정보";
"navigation" = "네비게이션";
"take_photo_record" = "사진 촬영";
"update_status" = "상태 업데이트";
"done" = "완료";
"edit_address_button" = "주소 편집";
"coordinates" = "좌표";
"access_instructions" = "접근 안내";
"add_access_instructions" = "접근 안내 추가...";
"package_count" = "패키지 수";
"package_size" = "패키지 크기";
"package_type" = "패키지 유형";
"mark_as_important" = "중요로 표시";
"select_package_position" = "차량 내 패키지 위치 선택";
"vehicle_area" = "차량 영역";
"left_right_position" = "좌/우 위치";
"vehicle_position_front" = "앞부분";
"vehicle_position_middle" = "중간부분";
"vehicle_position_back" = "뒷부분";
"vehicle_position_left" = "왼쪽";
"vehicle_position_right" = "오른쪽";
"vehicle_position_floor" = "아래부분";
"vehicle_position_shelf" = "위부분";
"height_position" = "높이 위치";
"delivery_type" = "배송 유형";
"delivery_status" = "배송 상태";
"order_info" = "주문 정보";
"order_information" = "주문 정보";
"order_number" = "주문 번호";
"enter_order_number" = "주문 번호 입력";
"tracking_number" = "추적 번호";
"enter_tracking_number" = "추적 번호 입력";
"time_info" = "시간 정보";
"time_information" = "시간 정보";
"estimated_arrival_time" = "예상 도착 시간";
"anytime" = "언제든지";
"stop_time" = "정차 시간";
"minutes_format" = "%d 분";
"photo_record" = "사진 기록";
"door_number_photo" = "문 번호 사진";
"package_label_photo" = "패키지 라벨 사진";
"placement_photo" = "배치 사진";
"door_number_desc" = "문 번호나 도로 번호를 명확하게 촬영하여 숫자/문자가 보이도록 하세요";
"package_label_desc" = "패키지 라벨을 촬영하여 수령인 정보가 명확하게 보이도록 하세요";
"placement_desc" = "패키지의 최종 배치 위치를 촬영하세요";
"photo_captured" = "사진이 촬영되었습니다";
"photo_captured_options" = "사진이 촬영되었습니다. 다음 사진을 계속 촬영하시겠습니까, 아니면 현재 사진을 완료하시겠습니까?";
"continue_to_next_photo" = "다음 사진 계속 촬영 - %@";
"retake" = "다시 촬영";
"tap_to_capture" = "탭하여 촬영";
"flash_auto" = "자동 플래시";
"flash_on" = "플래시 켜기";
"flash_off" = "플래시 끄기";
"photo_record_completed" = "사진 기록 완료";
"photo_confirmation" = "사진 촬영 확인";
"error" = "오류";
"ok" = "확인";
"complete_photo_capture" = "사진 촬영 완료";
"tap_to_capture" = "탭하여 촬영";
"photo_instructions" = "각 사진 카드를 탭하여 촬영하세요. 모든 사진을 완료해야 합니다.";
"photo_options" = "사진 옵션";
"view_photo" = "사진 보기";
"retake_photo" = "다시 촬영";
"saving_photos" = "사진 저장 중...";
"completed" = "완료";
"not_taken" = "촬영되지 않음";
"route_options" = "경로 옵션";
"avoid_tolls" = "유료 도로 회피";
"avoid_highways" = "고속도로 회피";
"optimize_route" = "경로 최적화";
"optimizing" = "최적화 중...";
"optimization_complete" = "최적화 완료";
"route_optimization_results" = "경로 최적화 결과";
"route_planning_options" = "경로 계획 옵션";
"before_optimization" = "최적화 전";
"after_optimization" = "최적화 후";
"auto_group" = "자동 그룹화";
"optimized_route_order" = "최적화된 경로 순서";
"apply" = "적용";
"kilometers" = "킬로미터";

// MARK: - Addresses
"addresses" = "주소";
"add_address" = "주소 추가";
"edit_address" = "주소 편집";
"delete_address" = "주소 삭제";
"address_details" = "주소 상세정보";
"street" = "도로명";
"city" = "시/군/구";
"state" = "시/도";
"country" = "국가";
"postal_code" = "우편번호";
"phone" = "전화번호";
"email" = "이메일";
"website" = "웹사이트";
"company" = "회사";
"notes" = "메모";
"coordinates" = "좌표";
"latitude" = "위도";
"longitude" = "경도";
"geocoding_error" = "지오코딩 오류";
"address_validation" = "주소 유효성 검사";
"invalid_addresses" = "유효하지 않은 주소";
"fix_addresses" = "주소 수정";

// MARK: - Routes
"route" = "경로";
"routes" = "경로";
"select_address_point" = "주소 지점 선택";
"select_delivery_points" = "배송 지점 선택";
"create_delivery_route" = "배송 경로 생성";
"view_saved_routes" = "저장된 경로 보기";
"create_route" = "경로 생성";
"edit_route" = "경로 편집";
"delete_route" = "경로 삭제";
"route_name" = "경로 이름";
"route_details" = "경로 상세정보";
"selected_addresses" = "%d개 주소가 선택됨";
"reached_limit" = "상한선에 도달";
"can_select_more" = "%d개 더 선택 가능";
"navigate_button" = "네비게이션";
"create_group" = "그룹 생성";
"start_point" = "시작 지점";
"end_point" = "종료 지점";
"waypoints" = "경유 지점";
"total_distance" = "총 거리";
"estimated_time" = "예상 시간";
"route_summary" = "경로 요약";
"route_options" = "경로 옵션";
"route_saved" = "경로 저장됨";
"route_optimized" = "경로 최적화됨";
"optimizing_route" = "경로 최적화 중...";
"completed_percent" = "%d%% 완료";
"processing_points" = "처리 중: %d/%d";
"estimated_remaining_time" = "예상 남은 시간: %@";

// MARK: - Delivery
"delivery" = "배송";
"delivery_confirmation" = "배송 확인";
"take_photo" = "사진 찍기";
"signature" = "서명";
"delivery_notes" = "배송 메모";
"delivery_status" = "배송 상태";
"delivered" = "배송 완료";
"not_delivered" = "배송되지 않음";
"delivery_time" = "배송 시간";
"delivery_date" = "배송 날짜";
"package_details" = "패키지 상세정보";
"package_id" = "패키지 ID";
"package_weight" = "패키지 무게";
"package_dimensions" = "패키지 크기";
"recipient_name" = "수령인 이름";
"recipient_phone" = "수령인 전화번호";

// MARK: - Groups
"groups" = "그룹";
"saved_groups" = "저장된 그룹";
"create_group" = "그룹 생성";
"edit_group" = "그룹 편집";
"delete_group" = "그룹 삭제";
"group_name" = "그룹 이름";
"group_details" = "그룹 상세정보";
"auto_grouping" = "자동 그룹화";
"group_by" = "그룹화 기준";
"add_to_group" = "그룹에 추가";
"remove_from_group" = "그룹에서 제거";
"group_created" = "그룹이 생성됨";
"default_group_name_format" = "그룹%d";
"auto_grouping_completed" = "자동 그룹화 완료";
"auto_grouping_in_progress" = "자동 그룹화 진행 중...";
"create_group_every_14_addresses" = "14개 주소마다 그룹 생성";
"create_delivery_group" = "배송 그룹 생성";
"enter_group_name" = "그룹 이름 입력";
"selected_delivery_points" = "선택된 배송 지점";
"drag_to_adjust_order" = "드래그하여 순서 조정";

// MARK: - Subscription
"subscription" = "구독";
"free_plan" = "무료 플랜";
"pro_plan" = "프로 플랜";
"expert_plan" = "전문가 플랜";
"monthly" = "월간";
"yearly" = "연간";
"subscribe" = "구독하기";
"upgrade" = "업그레이드";
"upgrade_to_pro" = "프로로 업그레이드";
"manage_subscription" = "구독 관리";
"restore_purchases" = "구매 복원";
"subscription_benefits" = "구독 혜택";
"free_trial" = "무료 평가판";
"price_per_month" = "월 %@";
"price_per_year" = "연 %@";
"save_percent" = "%@% 절약";
"current_plan" = "현재 플랜";
"subscription_terms" = "구독 약관";
"privacy_policy" = "개인정보 처리방침";
"terms_of_service" = "서비스 약관";

// MARK: - Import/Export
"import" = "가져오기";
"export" = "내보내기";
"import_addresses" = "주소 가져오기";
"export_addresses" = "주소 내보내기";
"import_from_file" = "파일에서 가져오기";
"export_to_file" = "파일로 내보내기";
"file_format" = "파일 형식";
"csv_format" = "CSV 형식";
"excel_format" = "Excel 형식";
"json_format" = "JSON 형식";
"import_success" = "%d개 주소를 성공적으로 가져왔습니다. 모든 주소에 유효한 좌표가 있습니다.";
"export_success" = "내보내기 성공";
"import_error" = "가져오기 오류";
"export_error" = "내보내기 오류";

// MARK: - Navigation
"navigate" = "네비게이션";

// MARK: - Look Around
"show_look_around" = "실경 보기";
"hide_look_around" = "실경 숨기기";

// MARK: - Map
"map" = "지도";
"map_type" = "지도 유형";
"standard" = "표준";
"satellite" = "위성";
"hybrid" = "하이브리드";
"show_traffic" = "교통 상황 표시";
"current_location" = "현재 위치";
"directions" = "길찾기";
"distance_to" = "거리";
"eta" = "예상 도착 시간";
"look_around" = "둘러보기";
"locating_to_glen_waverley" = "Glen Waverley로 위치 설정 중";

// MARK: - Errors and Warnings
"network_error" = "네트워크 오류";
"location_error" = "위치 오류";
"permission_denied" = "권한 거부됨";
"location_permission_required" = "위치 권한 필요";
"camera_permission_required" = "카메라 권한 필요";
"photo_library_permission_required" = "사진 라이브러리 권한 필요";
"please_try_again" = "다시 시도하세요";
"something_went_wrong" = "문제가 발생했습니다";
"invalid_input" = "잘못된 입력";
"required_field" = "필수 필드";
"no_internet_connection" = "인터넷 연결 없음";
"server_error" = "서버 오류";
"timeout_error" = "시간 초과 오류";
"data_not_found" = "데이터를 찾을 수 없음";
"selection_limit_reached" = "선택 제한에 도달했습니다";
"selection_limit_description" = "최대 %d개의 주소를 선택할 수 있으며, 현재 %d개가 선택되었습니다";

// MARK: - Location Validation Status
"location_status_valid" = "유효 범위";
"location_status_warning" = "경고 범위";
"location_status_invalid" = "유효하지 않은 위치";
"location_status_unknown" = "알 수 없는 상태";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "유효하지 않음: 영점 좌표 (0,0)";
"coordinates_invalid_nan" = "유효하지 않음: 비숫자 좌표";
"coordinates_out_of_range" = "유효하지 않음: 좌표가 유효 범위를 벗어남";
"coordinates_far_from_user" = "경고: 위치가 현재 위치에서 멀리 떨어져 있음";
"coordinates_ocean" = "경고: 위치가 바다나 무인 지역에 있을 수 있음";

// MARK: - Batch Address Input
"batch_add_addresses" = "일괄 주소 추가";
"batch_address_input_placeholder" = "주소를 입력하거나 붙여넣으세요. 한 줄에 하나씩. 최대 35개 주소.";
"free_address_limit" = "무료 버전 주소 제한";
"address_count_limit" = "주소 수 제한";
"free_version_max_addresses" = "무료 버전은 최대 %d개 주소까지 허용됩니다.";
"current_addresses_remaining" = "현재 %d개 주소가 있으며, %d개 주소만 더 추가할 수 있습니다.";
"current_route_address_limit" = "현재 경로에 %d개 주소가 있으며, %d개 주소만 더 추가할 수 있어 총 최대 %d개 주소입니다.";
"selected_addresses_can_import" = "%d개 주소를 선택했습니다. 이 주소들을 가져올 수 있습니다.";
"selected_addresses_exceeds" = "%d개 주소를 선택했지만, 추가 가능한 수를 %d개 초과했습니다.";
"selected_addresses_all_importable" = "%d개 주소를 선택했습니다. 모두 가져올 수 있습니다.";
"upgrade_for_unlimited_addresses" = "프리미엄으로 업그레이드하여 무제한 주소를 이용하세요!";
"import_first_n_addresses" = "처음 %d개만 가져오기";
"import_all_addresses" = "모든 주소 가져오기";
"import_selected_addresses" = "선택된 주소 가져오기";
"no_importable_addresses" = "가져올 수 있는 주소가 없습니다. 주소 제한을 확인하세요";
"please_enter_valid_address" = "최소 하나의 유효한 주소를 입력하세요";

// MARK: - File Import
"import_success" = "%d개 주소를 성공적으로 가져왔습니다. 모든 주소에 유효한 좌표가 있습니다.";
"import_success_with_warnings" = "成功导入 %d 个地址，其中 %d 个地址坐标正常，%d 个地址有警告。\n\n有警告的地址已标记，可以在导入后手动修复。";

// MARK: - Web Download
"web_download" = "웹 다운로드";
"supported_formats" = "지원되는 형식";
"supported_format_csv" = "• CSV 파일: 주소 열에는 완전한 주소가 포함되어야 함";
"supported_format_json" = "• JSON 데이터: 주소 필드를 포함하는 배열";
"supported_format_text" = "• 일반 텍스트: 한 줄에 하나의 주소";
"download_history" = "다운로드 기록";
"upgrade_to_premium" = "프리미엄으로 업그레이드";
"input_address_data_url" = "주소 데이터 URL 입력";
"import_result" = "가져오기 결과";
"import_addresses" = "주소 가져오기";
"downloading" = "다운로드 중...";
"processing_data" = "데이터 처리 중...";
"google_drive_download_failed" = "Google Drive 다운로드 실패";
"second_attempt_invalid_data" = "두 번째 다운로드 시도에서 유효하지 않은 데이터 반환";
"cannot_parse_json" = "JSON 데이터를 구문 분석할 수 없습니다. 파일 형식을 확인하세요";
"cannot_parse_json_with_error" = "JSON 데이터를 구문 분석할 수 없음: %@";
"cannot_get_address_coordinates" = "주소 좌표를 가져올 수 없음";
"cannot_read_file" = "파일을 읽을 수 없음: %@";
"success" = "성공";
"warning" = "경고";
"failed" = "배송 실패";
"no_matching_addresses" = "일치하는 주소를 찾을 수 없음";
"no_valid_addresses" = "유효한 주소를 찾을 수 없음";
"confirm" = "확인";
"processing_addresses" = "주소 처리 중...";
"supports_file_types" = "CSV, TXT, JSON 파일 지원";
"tap_to_select_file" = "탭하여 파일 선택";
"import_addresses" = "주소 가져오기";
"company_name_optional" = "회사명 (선택사항)";
"input_company_name" = "회사명 입력 (선택사항)";
"imported_addresses_count" = "%d개 주소를 가져왔습니다";
"select_all" = "모두 선택";
"excel_format_not_supported" = "Excel 형식은 지원되지 않음";
"no_matching_addresses" = "일치하는 주소를 찾을 수 없음";

// MARK: - Import Limits
"import_failed" = "가져오기 실패";
"no_importable_addresses" = "가져올 수 있는 주소가 없습니다. 주소 제한을 확인하세요";
"free_version_address_limit" = "무료 버전은 최대 %d개 주소까지 허용됩니다.";
"current_address_count" = "현재 %d개 주소가 있으며, %d개 주소만 더 추가할 수 있습니다.";
"can_import_selected" = "%d개 주소를 선택했습니다. 이 주소들을 가져올 수 있습니다.";
"selected_exceeds_limit" = "%d개 주소를 선택했지만, 추가 가능한 수를 %d개 초과했습니다.";
"upgrade_to_premium_unlimited" = "프리미엄으로 업그레이드하여 무제한 주소를 이용하세요!";
"route_address_limit" = "현재 경로에 %d개 주소가 있으며, %d개 주소만 더 추가할 수 있어 총 최대 %d개 주소입니다.";
"free_version_limit" = "무료 버전 주소 제한";
"address_count_limit" = "주소 수 제한";
"import_selected_addresses" = "선택된 주소 가져오기";
"import_first_n" = "처음 %d개만 가져오기";
"import_all_n" = "모든 %d개 가져오기";
"cannot_import" = "가져올 수 없음";
"select_at_least_one" = "최소 하나의 주소를 선택하세요";

// MARK: - Import Results
"no_valid_addresses_found" = "유효한 주소를 찾을 수 없음";
"import_success_all_valid" = "%d개 주소를 성공적으로 가져왔습니다. 모든 주소 좌표가 정상입니다.";
"import_success_some_warnings" = "%d개 주소를 성공적으로 가져왔습니다. %d개 주소의 좌표는 정상이고, %d개 주소의 좌표를 가져올 수 없었습니다.";

// MARK: - Warnings
"invalid_csv_row" = "유효하지 않은 CSV 행";
"distance_warning" = "현재 위치에서 200km 이상 떨어짐";
"not_in_australia" = "좌표가 호주 범위 내에 있지 않음";
"cannot_get_coordinates" = "주소 좌표를 가져올 수 없음";
"empty_address" = "빈 주소";
"invalid_address_data" = "유효하지 않은 주소 데이터";

// MARK: - Saved Groups
"saved_groups" = "저장된 그룹";
"no_saved_groups" = "저장된 그룹 없음";
"select_points_create_groups" = "배송 지점을 선택하고 그룹을 생성하여 쉽게 관리하세요";
"group_name" = "그룹 이름";
"group_details" = "그룹 상세정보";
"navigate_to_these_points" = "이 지점들로 네비게이션";
"confirm_remove_address" = "그룹에서 주소 \"%@\"를 제거하시겠습니까?";
"confirm_remove_this_address" = "그룹에서 이 주소를 제거하시겠습니까?";
"addresses_count" = "%d개 주소";
"no_saved_routes" = "저장된 경로 없음";
"no_saved_routes_description" = "아직 저장된 경로가 없습니다";
"all_routes" = "모든 경로";
"address_count_format_simple" = "%d개 주소";
"delete_all_routes" = "모든 경로 삭제";
"navigate_to_all_points" = "모든 지점으로 네비게이션";
"confirm_navigate_to_route" = "경로 \"%@\"의 모든 지점으로 네비게이션하시겠습니까?";
"temp_navigation_group" = "임시 네비게이션 그룹";

// MARK: - Route Management
"route_management" = "경로 관리";
"route_info" = "경로 정보";
"route_name" = "경로 이름";
"route_addresses" = "경로 주소";
"no_addresses_in_route" = "이 경로에는 주소가 없습니다";
"must_keep_one_route" = "최소 하나의 경로는 유지해야 합니다";
"confirm_delete_route" = "경로 \"%@\"를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.";
"confirm_delete_all_routes" = "모든 경로 삭제 확인";
"confirm_delete_all_routes_message" = "모든 경로를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.";
"delete_all" = "모두 삭제";

// MARK: - Navigation Buttons
"navigate" = "네비게이션";

// MARK: - GroupDetailView
"points_count_format" = "%d개 지점";

// MARK: - DeliveryPointDetailView
"address_information" = "주소 정보";
"group_belonging" = "소속 그룹";
"view_map" = "지도 보기";
"delivery_status" = "배송 상태";
"notes" = "메모";
"delete_delivery_point" = "배송 지점 삭제";
"delivery_point_details" = "배송 지점 상세정보";
"confirm_deletion" = "삭제 확인";
"delete_delivery_point_confirmation" = "이 배송 지점을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.";

// MARK: - Delivery Photos
"delivery_photos" = "배송 사진";
"view_delivery_photos" = "배송 사진 보기";
"no_photos_taken" = "아직 사진을 촬영하지 않음";
"take_photos" = "사진 촬영";
"loading_photos" = "사진 로딩 중...";
"photo_not_found" = "사진을 찾을 수 없음";
"photo_deleted" = "사진이 삭제됨";
"door_number_photo" = "문 번호 사진";
"package_label_photo" = "패키지 라벨 사진";
"placement_photo" = "배치 사진";
"share_photos" = "사진 공유";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "사진 촬영 확인";
"door_number_photo_title" = "도로 번호/문 번호 사진";
"package_label_photo_title" = "패키지 라벨 사진";
"placement_photo_title" = "배치 위치 사진";
"door_number_photo_desc" = "문 번호를 명확하게 촬영하여 숫자/문자가 보이도록 하세요";
"package_label_photo_desc" = "패키지 라벨을 촬영하여 수령인 정보가 명확하게 보이도록 하세요";
"placement_photo_desc" = "패키지의 최종 배치 위치를 촬영하세요";
"swipe_to_switch" = "스와이프하여 사진 유형 전환";
"complete_photos" = "사진 촬영 완료";
"saving_photos" = "사진 저장 중...";
"photo_save_success" = "사진 저장 성공";
"photo_save_failure" = "사진 저장 실패";
"retake_photo" = "다시 촬영";
"no_photos_found" = "사진을 찾을 수 없음";
"photos_deleted_or_not_taken" = "사진이 삭제되었거나 아직 촬영되지 않았을 수 있음";
"share_photo" = "사진 공유";
"photo_capture_preview" = "미리보기 모드 - 카메라 시뮬레이션";
"photo_capture_close" = "닫기";
"camera_start_failed" = "카메라 시작 실패";
"camera_start_failed_retry" = "카메라를 시작할 수 없습니다. 다시 시도하세요";
"camera_init_failed" = "카메라 초기화 실패";
"camera_access_failed" = "카메라에 액세스할 수 없음";
"photo_processing_failed" = "사진 촬영 실패";
"photo_processing_failed_retry" = "사진 처리를 완료할 수 없습니다. 다시 시도하세요";
"photo_capture_progress" = "진행률: %d/%d";
"photo_captured_continue" = "촬영 완료, %@ 계속 촬영";
"loading_photos" = "사진 로딩 중...";
"cancel" = "취소";

// MARK: - Delivery Status
"pending" = "배송 대기";
"in_progress" = "배송 중";
"completed" = "완료";
"failed" = "배송 실패";
"update_status" = "상태 업데이트";
"select_delivery_status" = "배송 상태 선택";
"select_failure_reason" = "실패 원인 선택";
"delivered" = "배송 완료";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "고객 부재";
"failure_reason_wrong_address" = "주소 오류";
"failure_reason_no_access" = "위치에 접근할 수 없음";
"failure_reason_rejected" = "패키지 수령 거부";
"failure_reason_other" = "기타 원인";
"enter_custom_reason" = "구체적인 원인 입력";
"custom_reason_placeholder" = "구체적인 원인을 설명하세요...";
"custom_reason_required" = "구체적인 원인을 입력하세요";
"failure_reason_required" = "실패 원인을 선택하세요";

// MARK: - Address Validation
"address_validation_failed" = "주소 검증 실패";


"0" = "14天免費試用，隨時可取消";
