/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/

/*


*/

// MARK: - Language Settings
"language_settings" = "语言设置";
"system_language" = "系统语言";
"system_language_section" = "系统设置";
"languages" = "语言";
"language_info_title" = "关于语言设置";
"language_info_description" = "更改语言设置后，应用将使用所选语言显示文本。某些内容可能需要重启应用才能完全应用新的语言设置。";
"restart_required" = "需要重启应用";
"restart_app_message" = "要完全应用语言更改，请重启应用。";
"restart_now" = "立即重启";
"restart_later" = "稍后重启";

// MARK: - Common UI Elements
"close" = "关闭";
"cancel" = "取消";
"save" = "保存";
"edit" = "编辑";
"delete" = "删除";
"done" = "完成";
"next" = "下一步";
"back" = "返回";
"confirm" = "确认";
"error" = "错误";
"success" = "成功";
"warning" = "警告";
"unknown_error" = "未知错误";
"loading" = "加载中...";
"search" = "搜索";
"settings" = "设置";
"help" = "帮助";
"about" = "关于";
"menu" = "菜单";
"understand" = "我明白了";

// MARK: - Navigation
"navigation" = "导航";
"start_navigation" = "开始导航";

// MARK: - Subscription
"subscription" = "订阅";
"upgrade_to_pro" = "升级至专业版";
"upgrade_description" = "一键导航分组地址，最多快60倍于手动操作";
"restore_purchases" = "恢复购买";
"learn_more" = "了解更多";
"upgrade_your_plan" = "升级您的计划";
"one_click_navigation_description" = "一键导航分组地址，节省时间和燃油";
"current_plan" = "当前计划";
"upgrade" = "升级";
"maybe_later" = "稍后再说";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "免费";
"pro_tier_price" = "$29.99/月";
"expert_tier_price" = "$249.99/年";
"free_tier_description" = "适合个人和小型企业，不需要大量停靠点";
"pro_tier_description" = "适合需要一键导航分组和无限地址的用户";
"expert_tier_description" = "与Pro相同，但通过年度计划节省31%";
"route_optimization" = "路线优化";
"unlimited_routes" = "无限路线";
"unlimited_optimizations" = "无限优化次数";
"max_15_addresses" = "每条路线最多15个地址";
"save_fuel_30" = "节省燃油最多30%";
"unlimited_addresses" = "无限地址";
"one_click_navigation" = "⚡ 一键导航分组 - 最多快60倍 ⚡";
"package_finder" = "包裹位置查找器";
"annual_savings" = "年度计划节省费用";
"switched_to_free" = "已切换至免费版";
"switched_to_subscription" = "已切换至订阅版";
"unlimited_stops" = "无限停靠点";
"plan_as_many_stops_as_needed" = "添加任意数量的配送点，不受限制";
"expires_in_days" = "%@ 天后到期";
"trial_expires_in_days" = "试用期 %@ 天后到期";
"expired" = "已过期";
"subscription_expires_on" = "%@ 到期";
"subscription_active_until" = "有效期至 %@";

// MARK: - Address Input
"enter_or_search_address" = "输入或搜索地址";
"search_results_count" = "搜索结果: %d";
"no_matching_addresses" = "未找到有效地址";
"search_address_failed" = "搜索地址失败: %@";
"address_search_no_response" = "地址搜索无响应";
"cannot_get_address_coordinates" = "无法获取地址坐标";
"speech_recognizer_unavailable" = "语音识别器不可用";
"microphone_permission_denied" = "麦克风权限未授权";
"speech_recognition_permission_denied" = "语音识别权限未授权";
"listening" = "正在聆听...";
"recording_failed" = "开始录音失败: %@";
"cannot_get_coordinates_retry" = "无法获取地址坐标，请手动输入或重试";
"cannot_create_recognition_request" = "无法创建识别请求";

// MARK: - Voice Recognition Languages
"voice_language_system" = "跟随系统语言";
"voice_language_english" = "English";
"voice_language_chinese_simplified" = "简体中文";
"voice_language_chinese_traditional" = "繁體中文";
"voice_language_arabic" = "العربية";
"voice_language_dutch" = "Nederlands";
"voice_language_french" = "Français";
"voice_language_german" = "Deutsch";
"voice_language_greek" = "Ελληνικά";
"voice_language_hebrew" = "עברית";
"voice_language_hungarian" = "Magyar";
"voice_language_indonesian" = "Bahasa Indonesia";
"voice_language_italian" = "Italiano";
"voice_language_japanese" = "日本語";
"voice_language_korean" = "한국어";
"voice_language_malay" = "Bahasa Melayu";
"voice_language_polish" = "Polski";
"voice_language_portuguese" = "Português";
"voice_language_romanian" = "Română";
"voice_language_russian" = "Русский";
"voice_language_spanish" = "Español";
"voice_language_thai" = "ไทย";
"voice_language_turkish" = "Türkçe";
"voice_input_language" = "语音输入语言";
"select_voice_language" = "选择语音识别语言";
"voice_language_settings" = "语音语言设置";
"speak_address_prompt" = "请说出地址...";
"voice_recognition_error" = "语音识别出错: %@";
"voice_recognition_failed" = "语音识别失败";
"voice_recognition_timeout" = "语音识别超时";
"voice_input_cancelled" = "语音输入已取消";
"cannot_create_recognition_request" = "无法创建识别请求";
"speech_recognition_permission_restricted" = "语音识别权限受限制";
"speech_recognition_permission_not_determined" = "语音识别权限未确定";
"speech_recognition_rate_limited" = "语音识别服务达到使用限制，请稍后再试";

// MARK: - Image Recognition
"image_address_recognition" = "图片识别地址";
"select_images" = "选择图片";
"select_multiple_images" = "支持多选";
"processing_images" = "正在处理图片...";
"processing_image_progress" = "正在处理第 %d 张图片，共 %d 张";
"recognizing_text" = "正在识别文字...";
"geocoding_addresses" = "正在获取地址坐标...";
"recognition_complete" = "识别完成";
"no_text_recognized" = "未识别到文字";
"no_addresses_found" = "未找到有效地址";
"image_recognition_failed" = "图片识别失败";
"image_recognition_error" = "图片识别出错: %@";
"text_recognition_failed" = "文字识别失败";
"address_parsing_failed" = "地址解析失败";
"select_addresses_to_add" = "选择要添加的地址";
"recognized_addresses" = "识别到的地址";
"address_coordinates" = "地址坐标";
"toggle_address_selection" = "切换地址选择";
"remove_address" = "移除地址";
"confirm_selected_addresses" = "确认选中的地址";
"no_addresses_selected" = "未选择地址";
"image_processing_cancelled" = "图片处理已取消";
"unsupported_image_format" = "不支持的图片格式";
"image_too_large" = "图片文件过大";
"image_recognition_permission_required" = "需要照片库访问权限";
"ocr_language_detection" = "自动检测语言";
"improve_image_quality" = "请确保图片清晰，文字可见";
"address_validation_in_progress" = "正在验证地址...";
"batch_address_import" = "批量地址导入";
"validated_addresses_count" = "已验证 %d 个地址";
"addresses_with_issues" = "%d addresses have issues"; // TODO: Translate to zh-CN
"select_all" = "全选";
"import_selected" = "导入选中";
"validating_addresses" = "正在验证地址...";
"empty_address" = "空地址";
"invalid_coordinates" = "地址坐标无效";
"coordinate_warning" = "坐标警告";
"address_validation_issue" = "地址验证存在问题";
"cannot_get_coordinates" = "无法获取地址坐标";
"no_importable_addresses" = "没有可导入的地址，请检查地址限制";
"free_version_max_addresses" = "免费版最多允许%d个地址。";
"valid" = "有效";
"with_issues" = "有问题";
"low_confidence_address" = "地址验证置信度较低";
"address_validation_failed" = "地址验证失败";
"current_addresses_remaining" = "当前已有%d个地址，只能再添加%d个地址。";
"can_import_selected" = "您选择了%d个地址，可以导入这些地址。";
"selected_exceeds_limit" = "您选择了%d个地址，超出可添加数量%d个。";
"selected_addresses_all_importable" = "您选择了%d个地址，可以全部导入。";
"upgrade_for_unlimited_addresses" = "升级到高级版可享受无限地址！";
"current_route_address_limit" = "当前路线已有%d个地址，只能再添加%d个地址，总计最多%d个地址。";
"import_all_addresses" = "导入所有地址";
"import_first_n" = "仅导入前%d个";
"import_selected_addresses" = "导入已选择的地址";
"upgrade_to_premium" = "升级到高级版";
"batch_add_addresses" = "批量添加地址";
"batch_address_input_placeholder" = "请输入或粘贴地址，每行一个地址。最多35个地址。";
"no_addresses_selected" = "未选择地址";

// MARK: - Saved Address Picker
"search_address" = "搜索地址";
"no_saved_addresses" = "没有保存的地址";
"no_saved_addresses_description" = "您还没有保存任何地址，或没有符合筛选条件的地址";
"select_address_book" = "选择地址簿";

// MARK: - Menu
"menu" = "菜单";
"routes" = "路线";
"address_book" = "地址簿";
"saved_routes" = "已保存的路线";
"manage_your_routes" = "管理您的路线";
"manage_your_addresses" = "管理您的地址";
"settings" = "设置";
"preferences" = "偏好设置";
"set_custom_start_point" = "设置自定义起点";
"current_start_point" = "当前起点: %@";
"support" = "支持";
"contact_us" = "联系我们";
"contact_us_description" = "有问题或建议？给我们发送邮件";
"help_center" = "帮助中心";
"subscription" = "订阅";
"upgrade_to_pro" = "升级至专业版";
"upgrade_description" = "一键导航分组地址，最多快60倍于手动操作";

// MARK: - Modern Menu
"quick_actions" = "快速操作";
"main_features" = "主要功能";
"support_help" = "支持与帮助";
"customize_app_settings" = "自定义应用设置";
"unlock_all_features" = "解锁所有功能";
"get_help_support" = "获取帮助和支持";
"app_info_version" = "应用信息和版本";
"dev_tools" = "开发工具";
"debug_testing_tools" = "调试和测试工具";
"upgrade" = "升级";
"version" = "版本";
"addresses" = "地址";
"limited_to_20_addresses" = "限制20个地址";
"unlimited_addresses" = "无限地址";
"all_premium_features" = "所有高级功能";
"open_subscription_view" = "直接打开订阅视图";
"open_subscription_view_description" = "跳过中间层，直接显示 SubscriptionView";
"restore_purchases_failed" = "恢复购买失败: %@";
"about" = "关于";
"rate_us" = "给我们评分";
"rate_us_description" = "您的评价对我们非常重要，帮助我们打造更好的应用！";
"share_app" = "分享应用";
"share_app_text" = "试试 NaviBatch 这款超棒的路线规划应用！";
"about_app" = "关于应用";
"developer_tools" = "开发者工具";
"coordinate_debug_tool" = "坐标调试工具";
"batch_fix_addresses" = "批量修正地址";
"clear_database" = "清空数据库";
"clear_database_confirmation" = "这将删除所有数据，包括路线、地址和分组。此操作无法撤销，确定要继续吗？";
"confirm_clear" = "确认清空";
"version_info" = "版本 %@ (%@)";
"current_system_language" = "当前系统语言";
"reset_to_system_language" = "重置为系统语言";
"language" = "语言";
"language_settings" = "语言设置";

// MARK: - Address Edit
"address" = "地址";
"coordinates" = "坐标";
"distance_from_current_location" = "与当前位置的距离";
"address_info" = "地址信息";
"update_coordinates" = "更新坐标";
"fix_address" = "修正地址";
"prompt" = "提示";
"confirm" = "确认";
"kilometers_format" = "%.1f 公里";
"meters_format" = "%.0f 米";
"modify_address_first" = "请先修改地址";
"coordinates_update_success" = "坐标更新成功";
"coordinates_update_failure" = "坐标更新失败";
"save_failure" = "保存失败: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "没有保存的地址";
"no_saved_addresses_message" = "您还没有保存任何地址";
"add_new_address" = "添加新地址";
"address_title" = "地址";
"add" = "添加";
"refresh" = "刷新";
"notes" = "备注";
"address_details" = "地址详情";
"favorite" = "收藏";
"edit_address" = "编辑地址";
"cancel" = "取消";
"save" = "保存";
"confirm_delete" = "确认删除";
"delete" = "删除";
"delete_address_confirmation" = "确定要删除这个地址吗？此操作无法撤销。";
"edit" = "编辑";
"address_marker" = "地址";
"address_label" = "地址:";
"notes_label" = "备注:";
"created_at_label" = "创建时间:";
"open_in_maps" = "在地图中打开";
"copy_address" = "复制地址";
"address_details_title" = "地址详情";

// MARK: - Route Detail
"start_end_point" = "起点/终点";
"start_point" = "起点";
"end_point" = "终点";
"route_info" = "路线信息";
"address_count" = "地址数量";
"address_count_format" = "%d 个地址";
"points_count_format" = "%d 个点";
"additional_points_format" = "+%d 个点";
"export_route" = "导出路线";
"navigate" = "导航";
"address_list" = "地址列表";
"no_addresses" = "没有地址";
"no_addresses_message" = "此路线尚未添加任何地址";

// MARK: - Route Bottom Sheet
"address_point_start" = "起点";
"address_point_stop" = "停靠点";
"address_point_end" = "终点";
"route_name" = "路线名称";
"save" = "保存";
"new_route" = "新建路线";
"saved_route" = "已保存路线";
"edit" = "编辑";
"loading" = "加载中...";
"plan_route" = "计划路线";
"clear_all" = "全部清除";
"avoid" = "避免:";
"toll_roads" = "收费公路";
"highways" = "高速公路";
"processing_addresses" = "正在处理地址...";
"same_start_end_point" = "您已将同一地址设为起点和终点";
"add_start_point" = "添加起点";
"swipe_left_to_delete" = "← 左滑删除地址";
"delete" = "删除";
"add_new_address" = "添加新地址";
"add_end_point" = "添加终点";

// MARK: - Simple Address Sheet
"address_title" = "地址";
"enter_and_select_address" = "输入并选择地址";
"current_search_text" = "当前搜索文本: %@";
"search_results_count" = "搜索结果: %d";
"no_matching_addresses" = "未找到有效地址";
"add_address" = "添加地址";
"edit_address" = "编辑地址";
"selected_coordinates" = "已选择坐标";
"company_name_optional" = "公司名称（可选）";
"url_optional" = "网址（可选）";
"favorite_address" = "收藏地址";
"set_as_start_and_end" = "同时设为起点和终点";
"address_book" = "地址簿";
"batch_paste" = "批量贴上";
"file_import" = "文件导入";
"web_download" = "线上下载";
"cancel" = "取消";
"saving" = "保存中...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "配送点管理";
"information_category" = "信息类别";
"address_info" = "地址信息";
"package_info" = "包裹信息";
"vehicle_position" = "车辆位置";
"delivery_info" = "配送信息";
"navigation" = "导航";
"take_photo_record" = "配送证明";
"update_status" = "更新状态";
"done" = "完成";
"edit_address_button" = "编辑地址";
"coordinates" = "坐标";
"access_instructions" = "访问说明";
"add_access_instructions" = "添加访问说明...";
"package_count" = "包裹数量";
"packages" = "个包裹";
"package_unit" = "个";
"package_size" = "包裹大小";
"package_type" = "包裹类型";
"mark_as_important" = "标记为重要包裹";
"priority_delivery" = "Priority Delivery"; // TODO: Translate to zh-CN
"priority_level" = "Priority Level"; // TODO: Translate to zh-CN
"priority_1" = "Priority 1"; // TODO: Translate to zh-CN
"priority_2" = "Priority 2"; // TODO: Translate to zh-CN
"priority_3" = "Priority 3"; // TODO: Translate to zh-CN
"no_priority" = "No Priority"; // TODO: Translate to zh-CN
"no_priority_short" = "None"; // TODO: Translate to zh-CN
"set_priority" = "Set Priority"; // TODO: Translate to zh-CN
"select_package_position" = "选择包裹在车辆中的位置";
"vehicle_area" = "车辆区域";
"left_right_position" = "左右位置";
"vehicle_position_front" = "前部";
"vehicle_position_middle" = "中部";
"vehicle_position_back" = "后部";
"vehicle_position_left" = "左";
"vehicle_position_right" = "右";
"vehicle_position_floor" = "下部";
"vehicle_position_shelf" = "上部";
"height_position" = "高度位置";
"vehicle_position_none" = "No Position Selected"; // TODO: Translate to zh-CN
"delivery_type" = "配送类型";
"delivery_status" = "配送状态";
"order_info" = "订单信息";
"order_information" = "订单信息";
"order_number" = "订单号码";
"enter_order_number" = "输入订单号";
"tracking_number" = "追踪号码";
"enter_tracking_number" = "输入追踪号";
"tracking_info" = "Tracking Information"; // TODO: Translate to zh-CN
"tracking_information" = "Tracking Information"; // TODO: Translate to zh-CN
"time_info" = "时间信息";
"time_information" = "时间信息";
"estimated_arrival_time" = "预计到达时间";
"anytime" = "随时";
"stop_time" = "停留时间";
"minutes_format" = "%d 分钟";
"photo_record" = "交付凭证";
"door_number_photo" = "门牌号照片";
"package_label_photo" = "包裹标签照片";
"placement_photo" = "放置位置照片";
"door_number_desc" = "请拍摄清晰的门牌号或街道号码，确保数字/字母可见";
"package_label_desc" = "请拍摄包裹标签，确保收件人信息清晰可见";
"placement_desc" = "请拍摄包裹最终放置的位置";
"photo_captured" = "照片已拍摄";
"photo_captured_options" = "照片已拍摄，您想继续拍下一张照片还是完成当前照片？";
"continue_to_next_photo" = "继续拍下一张 - %@";
"retake" = "重新拍摄";
"tap_to_capture" = "点击拍摄";
"flash_auto" = "自动闪光";
"flash_on" = "开启闪光";
"flash_off" = "关闭闪光";
"photo_record_completed" = "配送证明完成";
"photo_confirmation" = "拍照确认";
"error" = "错误";
"ok" = "确定";
"complete_photo_capture" = "完成拍照";
"tap_to_capture" = "点击拍摄";
"photo_instructions" = "点击每个照片卡片进行拍摄。所有照片必须完成。";
"photo_options" = "照片选项";
"view_photo" = "查看照片";
"retake_photo" = "重新拍摄";
"saving_photos" = "正在保存照片...";
"completed" = "已完成";
"not_taken" = "未拍摄";
"route_options" = "路线选项";
"avoid_tolls" = "避免收费公路";
"avoid_highways" = "避免高速公路";
"optimize_route" = "优化路线";
"optimizing" = "优化中...";
"optimization_complete" = "优化完成";
"route_optimization_results" = "路线优化结果";
"route_planning_options" = "路线规划选项";
"before_optimization" = "优化前";
"after_optimization" = "优化后";
"auto_group" = "自动分组";
"optimized_route_order" = "优化后路线顺序";
"apply" = "应用";
"kilometers" = "公里";

// MARK: - Address Validation Warnings
"street_number_issue_warning" = "⚠️ 门牌号差异过大！这可能导致送错地址被罚款，请立即核实地址";
"address_validation_critical" = "地址验证严重问题";
"street_number_difference_high_risk" = "门牌号差异过大，高风险";

// MARK: - Addresses
"addresses" = "地址";
"add_address" = "添加地址";
"edit_address" = "编辑地址";
"delete_address" = "删除地址";
"address_details" = "地址详情";
"street" = "街道";
"city" = "城市";
"state" = "州/省";
"country" = "国家";
"postal_code" = "邮政编码";
"phone" = "电话";
"email" = "电子邮件";
"website" = "网站";
"company" = "公司";
"notes" = "备注";
"coordinates" = "坐标";
"latitude" = "纬度";
"longitude" = "经度";
"geocoding_error" = "地理编码错误";
"address_validation" = "地址验证";
"invalid_addresses" = "无效地址";
"fix_addresses" = "修复地址";

// MARK: - Routes
"route" = "路线";
"routes" = "路线";
"select_address_point" = "选择地址点";
"select_delivery_points" = "选择配送点";
"create_delivery_route" = "创建配送分组";
"view_saved_routes" = "查看已保存的分组";
"create_route" = "创建路线";
"edit_route" = "编辑路线";
"delete_route" = "删除路线";
"route_name" = "路线名称";
"route_details" = "路线详情";
"selected_addresses" = "已选择 %d 个地址";
"reached_limit" = "已达上限";
"can_select_more" = "还可选%d个";
"navigate_button" = "导航";
"create_group" = "创建分组";
"start_point" = "起点";
"end_point" = "终点";
"waypoints" = "途经点";
"total_distance" = "总距离";
"estimated_time" = "预计时间";
"route_summary" = "路线摘要";
"route_options" = "路线选项";
"route_saved" = "路线已保存";
"route_optimized" = "路线已优化";
"optimizing_route" = "正在优化路线...";
"completed_percent" = "已完成 %d%%";
"processing_points" = "处理中: %d/%d";
"estimated_remaining_time" = "预计剩余时间: %@";

// MARK: - Delivery
"delivery" = "配送";
"delivery_confirmation" = "配送确认";
"take_photo" = "拍照";
"signature" = "签名";
"delivery_notes" = "配送备注";
"delivery_status" = "配送状态";
"delivered" = "已送达";
"not_delivered" = "未配送";
"delivery_time" = "配送时间";
"delivery_date" = "配送日期";
"package_details" = "包裹详情";
"package_id" = "包裹ID";
"package_weight" = "包裹重量";
"package_dimensions" = "包裹尺寸";
"recipient_name" = "收件人姓名";
"recipient_phone" = "收件人电话";

// MARK: - Groups
"groups" = "分组";
"saved_groups" = "已保存的分组";
"create_group" = "创建分组";
"edit_group" = "编辑分组";
"delete_group" = "删除分组";
"group_name" = "分组名称";
"group_details" = "分组详情";
"auto_grouping" = "自动分组";
"group_by" = "分组依据";
"add_to_group" = "添加到分组";
"remove_from_group" = "从分组中移除";
"group_created" = "分组已创建";
"default_group_name_format" = "分组%d";
"auto_grouping_completed" = "自动分组已完成";
"auto_grouping_in_progress" = "自动分组进行中...";
"create_group_every_14_addresses" = "每14个地址创建一个分组";
"create_delivery_group" = "创建配送分组";
"enter_group_name" = "输入分组名称";
"selected_delivery_points" = "已选择的配送点";
"drag_to_adjust_order" = "拖动可调整顺序";

// MARK: - Subscription
"subscription" = "订阅";
"free_plan" = "免费版";
"pro_plan" = "专业版";
"expert_plan" = "专家版";
"monthly" = "月度计划";
"yearly" = "年度计划";
"subscribe" = "订阅";
"upgrade" = "升级";
"upgrade_to_pro" = "升级至专业版";
"manage_subscription" = "管理订阅";
"restore_purchases" = "恢复购买";
"subscription_benefits" = "订阅权益";
"free_trial" = "免费试用";
"price_per_month" = "每月 %@";
"price_per_year" = "每年 %@";
"save_percent" = "节省 %@%";
"current_plan" = "当前计划";
"subscription_terms" = "订阅条款";
"privacy_policy" = "隐私政策";
"terms_of_service" = "服务条款";

// MARK: - Subscription View Specific
"feature_comparison" = "功能对比";
"addresses_per_route" = "每条路线地址数";
"max_20_addresses" = "20个";
"fuel_savings" = "燃油节省";
"up_to_30_percent" = "最多30%";
"choose_subscription_plan" = "选择订阅计划";
"monthly_plan" = "月度计划";
"yearly_plan" = "年度计划";
"/month_suffix" = "/月";
"/year_suffix" = "/年";
"save_30_percent" = "节省30%";
"free_trial_7_days_cancel_anytime" = "包含14天免费试用，随时可取消";
"subscription_auto_renew_notice" = "订阅将在试用期结束后自动续费，除非在试用期结束前取消。";
"and" = "和";
"subscription_exclusive" = "订阅专享";
"free_version_optimization_limit" = "免费版优化限制";
"free_version_supports_max_addresses" = "免费版仅支持最多%d个地址。";
"current_route_contains_addresses" = "当前路线包含%d个地址，超出了免费版限制。";
"upgrade_to_pro_unlimited_addresses" = "升级到高级版可享受无限地址和一键导航分组功能！";
"continue_optimization" = "继续优化";
"upgrade_unlock_one_click_navigation" = "升级解锁一键导航 - 快14x";
"learn_one_click_navigation_grouping" = "了解一键导航分组功能";
"toggle_subscription_status" = "切换订阅状态";
"toggle_subscription_description" = "在免费版和专业版之间切换（仅用于开发测试）";
"product_info_unavailable" = "无法获取产品信息，请稍后再试";
"purchase_failed" = "购买失败: %@";
"upgrade_to_pro_version" = "升级到Pro版";
"unlock_all_premium_features" = "解锁所有高级功能";
"first_7_days_free_cancel_anytime" = "前14天免费，随时可取消。";
"payment_terms_notice" = "购买确认后将从您的Apple ID账户扣费。订阅将自动续费，除非在当前周期结束前至少24小时取消。";
"terms_of_use" = "使用条款";
"product_load_failed_check_connection" = "无法加载产品信息，请确保您的设备已连接到互联网并已登录 App Store";
"product_load_failed" = "加载产品失败: %@";
"verify_receipt" = "验证收据";
"one_click_navigation_short" = "一键导航";
"save_30_percent_fuel" = "节省30%燃油";
"monthly_short" = "月度";
"yearly_short" = "年度";
"upgrade_now" = "立即升级";
"test_environment_pro_activated" = "测试环境：已激活 Pro 版本";
"payment_terms_notice_detailed" = "购买确认后将从您的Apple ID账户扣费。订阅将自动续费，除非在当前周期结束前至少24小时取消。您可以在App Store设置中管理和取消订阅。";
"step_screenshot" = "步骤 %d 截图";
"previous_step" = "上一步";
"next_step" = "下一步";
"each_address_takes_3_5_seconds" = "每个地址需要3-5秒添加";
"need_repeat_14_times" = "需要重复14次相同操作";
"navigation_order_often_confused" = "导航顺序经常混乱";
"error_prone_need_redo" = "容易出错，需要重新操作";
"address_order_reversed_manual_adjust" = "地址顺序颠倒，需手动调整";
"one_click_add_all" = "一次点击，全部添加";
"smart_grouping_auto_sorting" = "智能分组，自动排序";
"maintain_correct_visit_order" = "保持正确访问顺序";
"zero_errors_zero_repetition" = "零错误，零重复操作";

// MARK: - Import/Export
"import" = "导入";
"export" = "导出";
"import_addresses" = "导入地址";
"export_addresses" = "导出地址";
"import_from_file" = "从文件导入";
"export_to_file" = "导出到文件";
"file_format" = "文件格式";
"csv_format" = "CSV 格式";
"excel_format" = "Excel 格式";
"json_format" = "JSON 格式";
"import_success" = "成功导入 %d 个地址，所有地址坐标正常。";
"export_success" = "导出成功";
"import_error" = "导入错误";
"export_error" = "导出错误";

// MARK: - Navigation
"navigate" = "导航";
"navigation_app" = "导航应用";
"apple_maps" = "苹果地图";

// MARK: - Settings
"app_preferences" = "应用偏好设置";
"distance_unit" = "距离单位";
"current_language" = "当前语言";
"info" = "信息";

// MARK: - Contact and Support
"contact_us_header" = "联系我们";
"contact_us_subheader" = "有问题或建议？我们很乐意为您提供帮助！";
"contact_options" = "联系方式";
"email_us" = "发送邮件";
"contact_form" = "联系表单";
"contact_and_support" = "联系与支持";
"common_questions" = "常见问题";
"how_to_use" = "如何使用";
"subscription_faq" = "订阅常见问题";
"navigation_help" = "导航帮助";
"troubleshooting" = "故障排除";
"help_howto_content" = "NaviBatch是一款强大的路线规划应用，帮助您优化配送路线，节省时间和燃油。您可以添加多个地址，自动优化路线顺序，并一键导航到Apple Maps。";
"help_subscription_content" = "NaviBatch提供免费版和专业版。免费版支持最多20个地址，专业版提供无限地址和一键分组导航功能。";
"help_navigation_content" = "NaviBatch使用Apple Maps进行导航。您可以单独导航到每个地址，或使用分组功能一次性导航多个地址。";
"help_troubleshooting_content" = "如果遇到问题，请首先确保您的设备有网络连接，并且已授权位置权限。如果问题仍然存在，请联系我们的支持团队。";

// MARK: - About App
"actions" = "操作";
"legal" = "法律信息";
"terms_of_use" = "使用条款";

// MARK: - Look Around
"show_look_around" = "查看实景图";
"hide_look_around" = "隐藏实景图";

// MARK: - Map
"map" = "地图";
"map_type" = "地图类型";
"standard" = "标准";
"satellite" = "卫星";
"hybrid" = "混合";
"show_traffic" = "显示交通状况";
"current_location" = "当前位置";
"directions" = "路线指引";
"distance_to" = "距离";
"eta" = "预计到达时间";
"look_around" = "环视";
"locating_to_glen_waverley" = "定位到格伦韦弗利";

// MARK: - Errors and Warnings
"network_error" = "网络错误";
"location_error" = "位置错误";
"permission_denied" = "权限被拒绝";
"location_permission_required" = "需要位置权限";
"camera_permission_required" = "需要相机权限";
"photo_library_permission_required" = "需要照片库权限";
"please_try_again" = "请重试";
"something_went_wrong" = "出现错误";
"invalid_input" = "无效输入";
"required_field" = "必填字段";
"no_internet_connection" = "无网络连接";
"server_error" = "服务器错误";
"timeout_error" = "请求超时";
"data_not_found" = "未找到数据";
"selection_limit_reached" = "已达到选择限制";
"selection_limit_description" = "您最多可以选择%d个地址。当前已选择%d个地址";

// MARK: - Location Validation Status
"location_status_valid" = "有效范围";

// MARK: - Address Validation Status
"address_validation_unknown" = "未验证";
"address_validation_valid" = "有效";
"address_validation_invalid" = "无效";
"address_validation_warning" = "警告";
"address_validation_mismatch" = "不匹配";

// MARK: - Address Scanner
"device_not_support_scanning" = "设备不支持原生扫描";
"requires_ios16_a12_chip" = "需要iOS 16+和A12芯片以上设备";
"debug_info" = "调试信息:";
"address_confirmation" = "地址确认";
"continue_scanning" = "继续扫描";
"confirm_add" = "确认添加";
"cannot_get_coordinates_scan_retry" = "无法获取地址坐标，请手动输入或重新扫描";
"unknown_country" = "未知国家";
"unknown_city" = "未知城市";
"please_enter_valid_address" = "请输入至少一个有效地址";
"please_select_valid_address" = "请选择有效地址";
"add_address_failed" = "添加地址失败: %@";
"location_permission_required_for_current_location" = "需要位置权限才能使用当前位置。请在设置中允许位置访问。";
"cannot_get_current_location_check_settings" = "无法获取当前位置，请确保位置服务已开启。";
"cannot_get_current_location_address" = "无法获取当前位置的地址信息。";
"get_current_location_failed" = "获取当前位置失败: %@";
"location_status_warning" = "警告范围";
"location_status_invalid" = "无效位置";
"location_status_unknown" = "未知状态";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "无效：零坐标 (0,0)";
"coordinates_invalid_nan" = "无效：非数字坐标";
"coordinates_out_of_range" = "无效：坐标超出有效范围";
"coordinates_far_from_user" = "警告：位置距离您当前位置较远";
"coordinates_ocean" = "警告：位置可能在海洋或无人区";

// MARK: - Batch Address Input
"batch_add_addresses" = "批量添加地址";
"batch_address_input_placeholder" = "请输入或粘贴地址，每行一个地址。最多35个地址。";
"free_address_limit" = "免费版地址限制";
"address_count_limit" = "地址数量限制";
"free_version_max_addresses" = "免费版最多允许%d个地址。";
"current_addresses_remaining" = "当前已有%d个地址，只能再添加%d个地址。";
"current_route_address_limit" = "当前路线已有%d个地址，只能再添加%d个地址，总计最多%d个地址。";
"selected_addresses_can_import" = "您选择了%d个地址，可以导入这些地址。";
"selected_addresses_exceeds" = "您选择了%d个地址，超出可添加数量%d个。";
"selected_addresses_all_importable" = "您选择了%d个地址，可以全部导入。";
"upgrade_for_unlimited_addresses" = "升级到高级版可享受无限地址！";
"import_all_addresses" = "导入所有地址";
"import_selected_addresses" = "导入已选择的地址";
"no_importable_addresses" = "没有可导入的地址，请检查地址限制";
"please_enter_valid_address" = "请输入至少一个有效地址";

// MARK: - File Import
"import_success" = "成功导入 %d 个地址，所有地址坐标正常。";
"import_success_with_warnings" = "成功导入 %d 个地址，其中 %d 个地址坐标正常，%d 个地址有警告。\n\n有警告的地址已标记，可以在导入后手动修复。";

// MARK: - Web Download
"web_download" = "线上下载";
"supported_formats" = "支持的格式";
"supported_format_csv" = "• CSV文件: 地址列应包含完整地址";
"supported_format_json" = "• JSON数据: 包含地址字段的数组";
"supported_format_text" = "• 纯文本: 每行一个地址";
"download_history" = "下载历史";
"upgrade_to_premium" = "升级到高级版";
"input_address_data_url" = "输入地址数据URL";
"import_result" = "导入结果";
"import_addresses" = "导入地址";
"downloading" = "正在下载...";
"processing_data" = "正在处理数据...";
"google_drive_download_failed" = "Google Drive下载失败";
"second_attempt_invalid_data" = "二次尝试下载返回无效数据";
"cannot_parse_json" = "无法解析JSON数据，请检查文件格式";
"cannot_parse_json_with_error" = "无法解析JSON数据：%@";
"cannot_get_address_coordinates" = "无法获取地址坐标";
"cannot_read_file" = "无法读取文件：%@";
"success" = "成功";
"warning" = "警告";
"failed" = "派送失败";
"no_matching_addresses" = "未找到有效地址";
"no_valid_addresses" = "未找到有效地址";
"confirm" = "确认";
"processing_addresses" = "正在处理地址...";
"supports_file_types" = "支持CSV、TXT和JSON文件";
"tap_to_select_file" = "点击选择文件";
"import_addresses" = "导入地址";
"company_name_optional" = "公司名称（可选）";
"input_company_name" = "输入公司名称（可选）";
"imported_addresses_count" = "已导入 %d 个地址";
"select_all" = "全选";
"excel_format_not_supported" = "不支持Excel格式";
"no_matching_addresses" = "未找到有效地址";

// MARK: - Import Limits
"import_failed" = "导入失败";
"no_importable_addresses" = "没有可导入的地址，请检查地址限制";
"free_version_address_limit" = "免费版最多允许%d个地址。";
"current_address_count" = "当前已有%d个地址，只能再添加%d个地址。";
"can_import_selected" = "您选择了%d个地址，可以导入这些地址。";
"selected_exceeds_limit" = "您选择了%d个地址，超出可添加数量%d个。";
"upgrade_to_premium_unlimited" = "升级到高级版可享受无限地址！";
"route_address_limit" = "当前路线已有%d个地址，只能再添加%d个地址，总计最多%d个地址。";
"free_version_limit" = "免费版地址限制";
"address_count_limit" = "地址数量限制";
"import_selected_addresses" = "导入已选择的地址";
"import_first_n" = "仅导入前%d个";
"import_all_n" = "导入全部%d个";
"cannot_import" = "无法导入";
"select_at_least_one" = "请至少选择一个地址";

// MARK: - Import Results
"no_valid_addresses_found" = "未找到有效地址";
"import_success_all_valid" = "成功导入 %d 个地址，所有地址坐标正常。";
"import_success_some_warnings" = "成功导入 %d 个地址，其中 %d 个地址坐标正常，%d 个地址有警告。";

// MARK: - Additional Import Keys
"company_format" = "公司: %@";
"added_from_web_download" = "从线上下载添加";
"current_addresses_remaining" = "当前已有%d个地址，只能再添加%d个地址。";
"free_version_max_addresses" = "免费版最多允许%d个地址。";

// MARK: - Warnings
"invalid_csv_row" = "无效的CSV行";
"distance_warning" = "距离当前位置超过200公里";
"not_in_australia" = "坐标不在澳大利亚范围内";
"cannot_get_coordinates" = "无法获取地址坐标";
"empty_address" = "空地址";
"invalid_address_data" = "无效的地址数据";
"address_validation_issue" = "地址验证存在问题";
"invalid_coordinates" = "地址坐标无效";
"distance_warning_confirm" = "地址距离较远，请确认";
"coordinates_missing" = "地址坐标缺失";
"low_accuracy_address" = "地址精度较低";
"address_partial_match" = "地址部分匹配，可能不准确";
"address_outside_region" = "地址在目标区域外";
"api_limit_reached" = "地图API请求限制，稍后再试";
"address_not_exist_or_incorrect_format" = "地址不存在或格式不正确";
"please_check_address_spelling" = "请检查地址拼写";
"try_smaller_street_number" = "如果街道号码较大，尝试使用该街道的较小号码";
"use_full_street_type_name" = "确保使用完整的街道类型名称（如 'Lane' 而不是 'La'）";
"try_add_more_address_details" = "尝试添加更多地址详细信息";
"cannot_find_address" = "无法找到该地址";
"please_check_spelling_or_add_details" = "请检查地址拼写或添加更多详细信息";
"cannot_find_address_check_spelling" = "无法找到该地址，请检查拼写或添加更多详细信息";
"address_not_set" = "Address not set"; // TODO: Translate to zh-CN
"address_format_incomplete" = "地址格式不完整或包含错误信息，请检查并修正";
"location_service_denied" = "位置服务被拒绝，请检查应用权限设置";

// MARK: - Saved Groups
"saved_groups" = "已保存的分组";
"no_saved_groups" = "没有保存的分组";
"select_points_create_groups" = "选择配送点并创建分组以便于管理";
"group_name" = "分组名称";
"group_details" = "分组详情";
"navigate_to_these_points" = "导航到这些点";
"confirm_remove_address" = "确定要从分组中移除地址 \"%@\" 吗？";
"confirm_remove_this_address" = "确定要从分组中移除此地址吗？";
"addresses_count" = "%d 个地址";
"no_saved_routes" = "没有已保存的路线";
"no_saved_routes_description" = "您还没有保存任何路线";
"all_routes" = "所有路线";
"address_count_format_simple" = "%d 个地址";
"delete_all_routes" = "删除所有路线";
"navigate_to_all_points" = "导航到所有点";
"confirm_navigate_to_route" = "确定要导航到路线\"%@\"中的所有点吗？";
"temp_navigation_group" = "临时导航分组";

// MARK: - Route Management
"route_management" = "路线管理";
"route_info" = "路线信息";
"route_name" = "路线名称";
"route_addresses" = "路线地址";
"no_addresses_in_route" = "此路线没有地址";
"must_keep_one_route" = "必须至少保留一条路线";
"confirm_delete_route" = "确定要删除路线\"%@\"吗？此操作无法撤销。";
"confirm_delete_all_routes" = "确认删除所有路线";
"confirm_delete_all_routes_message" = "确定要删除所有路线吗？此操作无法撤销。";
"delete_all" = "删除所有";

// MARK: - Navigation Buttons
"navigate" = "导航";

// MARK: - GroupDetailView
"points_count_format" = "%d 个点";

// MARK: - DeliveryPointDetailView
"address_information" = "地址信息";
"group_belonging" = "所属分组";
"view_map" = "查看地图";
"delivery_status" = "配送状态";
"notes" = "备注";
"delete_delivery_point" = "删除配送点";
"delivery_point_details" = "配送点详情";
"confirm_deletion" = "确认删除";
"delete_delivery_point_confirmation" = "确定要删除这个配送点吗？此操作无法撤销。";

// MARK: - Delivery Photos
"delivery_photos" = "配送照片";
"view_delivery_photos" = "查看配送照片";
"no_photos_taken" = "尚未拍摄照片";
"take_photos" = "拍摄照片";
"loading_photos" = "正在加载照片...";
"photo_not_found" = "照片未找到";
"photo_deleted" = "照片已被删除";
"door_number_photo" = "门牌号照片";
"package_label_photo" = "包裹标签照片";
"placement_photo" = "放置位置照片";
"share_photos" = "分享照片";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "拍照确认";
"door_number_photo_title" = "道路号码/门牌照片";
"package_label_photo_title" = "包裹标签照片";
"placement_photo_title" = "放置位置照片";
"door_number_photo_desc" = "请拍摄清晰的门牌号，确保数字/字母可见";
"package_label_photo_desc" = "请拍摄包裹标签，确保收件人信息清晰可见";
"placement_photo_desc" = "请拍摄包裹最终放置的位置";
"swipe_to_switch" = "滑动切换照片类型";
"photos_will_be_saved_to" = "照片将保存到相册：";
"complete_photos" = "完成拍照";
"saving_photos" = "正在保存照片...";
"photo_save_success" = "照片保存成功";
"photo_save_failure" = "保存照片失败";
"retake_photo" = "重新拍摄";
"no_photos_found" = "未找到照片";
"photos_deleted_or_not_taken" = "可能照片已被删除或尚未拍摄";
"share_photo" = "分享照片";
"photo_capture_preview" = "预览模式 - 相机模拟";
"photo_capture_close" = "关闭";
"camera_start_failed" = "相机启动失败";
"camera_start_failed_retry" = "无法启动相机，请重试";
"camera_init_failed" = "相机初始化失败";
"camera_access_failed" = "无法访问相机";
"photo_processing_failed" = "拍照失败";
"photo_processing_failed_retry" = "无法完成照片处理，请重试";
"photo_capture_progress" = "进度: %d/%d";
"photo_captured_continue" = "已拍摄完成，继续拍摄%@";
"loading_photos" = "正在加载照片...";
"cancel" = "取消";

// MARK: - Delivery Status
"pending" = "待派送";
"in_progress" = "派送中";
"completed" = "已完成";
"failed" = "派送失败";
"update_status" = "更新状态";
"select_delivery_status" = "选择配送状态";
"select_failure_reason" = "选择失败原因";
"delivered" = "已送达";
"delivery_status_pending" = "Pending"; // TODO: Translate to zh-CN
"delivery_status_in_progress" = "In Progress"; // TODO: Translate to zh-CN
"delivery_status_completed" = "Completed"; // TODO: Translate to zh-CN
"delivery_status_failed" = "Failed"; // TODO: Translate to zh-CN

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "客户不在家";
"failure_reason_wrong_address" = "地址错误";
"failure_reason_no_access" = "无法进入位置";
"failure_reason_rejected" = "包裹被拒收";
"failure_reason_other" = "其他原因";
"enter_custom_reason" = "输入具体原因";
"custom_reason_placeholder" = "请描述具体原因...";
"custom_reason_required" = "请输入具体原因";
"failure_reason_required" = "请选择失败原因";

// MARK: - Delivery Types
"delivery_type_delivery" = "Delivery"; // TODO: Translate to zh-CN
"delivery_type_pickup" = "Pickup"; // TODO: Translate to zh-CN

// MARK: - Delivery Order
"delivery_order_first" = "First"; // TODO: Translate to zh-CN
"delivery_order_auto" = "Auto"; // TODO: Translate to zh-CN
"delivery_order_last" = "Last"; // TODO: Translate to zh-CN

// MARK: - Package Sizes
"package_size_small" = "Small"; // TODO: Translate to zh-CN
"package_size_medium" = "Medium"; // TODO: Translate to zh-CN
"package_size_large" = "Large"; // TODO: Translate to zh-CN

// MARK: - Package Types
"package_type_box" = "Box"; // TODO: Translate to zh-CN
"package_type_bag" = "Bag"; // TODO: Translate to zh-CN
"package_type_letter" = "Letter"; // TODO: Translate to zh-CN

// MARK: - Address Validation
"address_validation_failed" = "地址验证失败";

// MARK: - One Click Navigation Promo
"one_click_navigation_grouping" = "一键导航分组";
"speed_60x_faster" = "快60倍";
"goodbye_manual_address_adding" = "从此告别繁琐的手动添加地址";
"watch_detailed_demo" = "观看详细演示";
"upgrade_to_pro_now" = "立即升级到Pro版";
"free_trial_7_days" = "14天免费试用，随时可取消";
"traditional_vs_navibatch_pro" = "传统方式 vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "← 左右滑动查看完整对比 →";

// MARK: - Traditional Method Pain Points
"traditional_method" = "传统方式";
"each_address_takes_3_5_seconds" = "每个地址需要3-5秒添加";
"need_repeat_14_times" = "需要重复14次相同操作";
"navigation_order_often_confused" = "导航顺序经常混乱";
"error_prone_need_redo" = "容易出错，需要重新操作";
"address_order_reversed_manual_adjust" = "地址顺序颠倒，需手动调整";
"drivers_get_lost_affect_efficiency" = "司机容易迷路，影响配送效率";
"repetitive_operations_waste_time" = "重复操作浪费大量时间";
"total_time_60_seconds" = "总耗时: 60秒";

// MARK: - NaviBatch Pro Solutions
"navibatch_pro" = "NaviBatch Pro";
"one_click_add_all" = "一次点击，全部添加";
"smart_grouping_auto_sorting" = "智能分组，自动排序";
"maintain_correct_visit_order" = "保持正确访问顺序";
"zero_errors_zero_repetition" = "零错误，零重复操作";
"optimize_routes_reduce_distance" = "优化路线，减少行驶距离";
"improve_delivery_efficiency_accuracy" = "提升配送效率和准确性";
"speed_boost_60x" = "速度提升60倍 ⚡";
"total_time_1_second" = "总耗时: 1秒";

// MARK: - Time Comparison View
"time_comparison" = "时间对比";
"traditional_method_problems" = "传统方式的问题:";
"each_address_3_5_seconds_14_total_60" = "• 每个地址需要3-5秒，14个地址约需60秒";
"repetitive_operations_cause_fatigue" = "• 重复操作容易导致用户疲劳和出错";
"address_order_reversed_last_becomes_first" = "• 地址顺序颠倒，最后添加的地址成为首个导航点";
"need_manual_reverse_adding_takes_longer" = "• 需要手动反向添加地址，耗时更长";
"navibatch_advantages" = "NaviBatch的优势:";
"add_14_addresses_1_second_60x_faster" = "• 一次性添加14个地址仅需1秒，速度提升60倍";
"auto_maintain_correct_order_no_adjustment" = "• 自动保持正确的访问顺序，无需手动调整";
"zero_error_rate_no_repetition" = "• 零错误率，无需重复操作";
"save_59_seconds" = "节省59秒！";
"speed_boost_60x_simple" = "速度提升60倍";
"seconds_format" = "%d秒";

// MARK: - Quantified Value Card
"actual_benefits_one_click_navigation" = "使用一键导航分组的实际收益";
"daily_savings" = "每日节省";
"daily_savings_value" = "30+ 分钟";
"daily_savings_description" = "减少路线设置时间";
"monthly_savings" = "每月节省";
"monthly_savings_value" = "15+ 小时";
"monthly_savings_description" = "可用于增加配送量或休息";
"fuel_savings" = "燃油节省";
"fuel_savings_value" = "最多30%";
"fuel_savings_description" = "通过优化路线减少行驶距离";
"income_increase" = "收入提升";
"income_increase_value" = "20-25%";
"income_increase_description" = "通过增加每日配送量";

// MARK: - Subscription Status
"free_plan" = "免费版";
"pro_plan" = "专业版";
"expert_plan" = "专家版";
"free_trial" = "免费试用";
"trial" = "试用";
"days_left" = "天剩余";
"free_plan_description" = "最多20个地址";
"pro_plan_active" = "专业版已激活";
"expert_plan_active" = "专家版已激活";
"trial_active" = "试用期进行中";
"trial_expires_on" = "试用期至 %@";

// MARK: - Upgrade to Premium
"upgrade_to_premium" = "升级到高级版";

// MARK: - Address Validation Settings
"address_validation_mode" = "Address Validation Mode"; // TODO: Translate to zh-CN
"validation_description" = "Choose the strictness level for address validation. Strict mode is recommended for delivery businesses to ensure address accuracy."; // TODO: Translate to zh-CN
"current_settings" = "Current Settings"; // TODO: Translate to zh-CN
"validation_mode_format" = "Validation Mode: %@"; // TODO: Translate to zh-CN
"threshold_score_format" = "Pass Threshold: %d points"; // TODO: Translate to zh-CN
"validation_example" = "Validation Example"; // TODO: Translate to zh-CN
"original_address_example" = "Original Address: 55 Batten Street, Glen Waverley, VIC 3150"; // TODO: Translate to zh-CN
"reverse_address_example" = "Reverse Address: 5 Batten St, Glen Waverley, VIC 3150"; // TODO: Translate to zh-CN
"house_number_difference" = "House Number Difference: 50 (Extremely High Risk)"; // TODO: Translate to zh-CN
"result_label" = "Result:"; // TODO: Translate to zh-CN
"may_pass_warning" = "May Pass ⚠️"; // TODO: Translate to zh-CN
"will_not_pass" = "Will Not Pass ❌"; // TODO: Translate to zh-CN
"real_case_example" = "Real Case Example"; // TODO: Translate to zh-CN
"real_case_description" = "Based on the real address validation case you provided. Both strict and perfect modes will reject this high-risk address."; // TODO: Translate to zh-CN
"address_validation_settings" = "Address Validation Settings"; // TODO: Translate to zh-CN

// MARK: - Developer Tools
"close" = "关闭";
"clear" = "Clear"; // TODO: Translate to zh-CN
"view_details" = "View Details"; // TODO: Translate to zh-CN
"create_test_data" = "Create Test Data"; // TODO: Translate to zh-CN
"manual_snapshot" = "Manual Snapshot"; // TODO: Translate to zh-CN
"start_location_updates" = "Start Location Updates"; // TODO: Translate to zh-CN
"stop_location_updates" = "Stop Location Updates"; // TODO: Translate to zh-CN
"user_location_marker_test" = "User Location Marker Test"; // TODO: Translate to zh-CN
"location_animation_control" = "Location & Animation Control"; // TODO: Translate to zh-CN
"current_location_format" = "Current Location: %@, %@"; // TODO: Translate to zh-CN
"waiting_for_location" = "Waiting for location information..."; // TODO: Translate to zh-CN
"diagnostic_tools" = "Diagnostic Tools"; // TODO: Translate to zh-CN
"storekit_diagnostics" = "StoreKit Diagnostics"; // TODO: Translate to zh-CN
"subscription_function_test" = "Subscription Function Test"; // TODO: Translate to zh-CN
"localization_test" = "Localization Test"; // TODO: Translate to zh-CN
"address_validation_demo" = "Address Validation Demo"; // TODO: Translate to zh-CN
"localization_tools" = "Localization Tools"; // TODO: Translate to zh-CN
"coordinate_debug_tools" = "Coordinate Debug Tools"; // TODO: Translate to zh-CN
"smart_abbreviation_expansion_test" = "Smart Abbreviation Expansion Test"; // TODO: Translate to zh-CN
"subscription_restore_diagnostics" = "Subscription Restore Diagnostics"; // TODO: Translate to zh-CN
"batch_address_import_test" = "Batch Address Import Test"; // TODO: Translate to zh-CN
"test_import_1000_addresses_memory" = "Test importing 1000 addresses memory usage"; // TODO: Translate to zh-CN
"map_rendering_test" = "Map Rendering Test"; // TODO: Translate to zh-CN
"test_map_display_markers_memory" = "Test map displaying large number of markers memory usage"; // TODO: Translate to zh-CN
"select_test_language" = "Select Test Language"; // TODO: Translate to zh-CN

// MARK: - OneClick Promo Buttons
"discover_60x_speed_boost" = "Discover 60x Speed Boost"; // TODO: Translate to zh-CN
"see_60x_speed_demo" = "See 60x Speed Demo"; // TODO: Translate to zh-CN

// MARK: - Free vs Pro Comparison
"free_vs_pro_comparison" = "Free vs Pro Comparison"; // TODO: Translate to zh-CN
"our_free_beats_competitors_paid" = "Our FREE plan beats competitors' PAID plans!"; // TODO: Translate to zh-CN
"features" = "Features"; // TODO: Translate to zh-CN
"free_plan" = "免费版";
"pro_plan" = "专业版";
"up_to_20" = "Up to 20"; // TODO: Translate to zh-CN
"unlimited" = "Unlimited"; // TODO: Translate to zh-CN
"smart_optimization" = "Smart Route Optimization"; // TODO: Translate to zh-CN
"up_to_20_percent" = "Up to 20%"; // TODO: Translate to zh-CN
"up_to_30_percent" = "最多30%";

// MARK: - Sample File Download
"file_not_found" = "文件未找到";
"sample_file_not_available" = "示例文件暂时不可用";
"file_copy_failed" = "准备示例文件分享失败";



// MARK: - Extra keys (not in English base)
// These keys exist in this language but not in English base file
// Consider removing or adding to English base if needed
// "clearing" = "清除中...";
