/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "语言设置";
"system_language" = "系统语言";
"system_language_section" = "系统设置";
"languages" = "语言";
"language_info_title" = "关于语言设置";
"language_info_description" = "更改语言设置后，应用将使用所选语言显示文本。某些内容可能需要重启应用才能完全应用新的语言设置。";
"restart_required" = "需要重启应用";
"restart_app_message" = "要完全应用语言更改，请重启应用。";
"restart_now" = "立即重启";
"restart_later" = "稍后重启";
"close" = "关闭";
"cancel" = "取消";
"save" = "保存";
"edit" = "编辑";
"delete" = "删除";
"done" = "完成";
"next" = "下一步";
"back" = "返回";
"confirm" = "确认";
"error" = "错误";
"success" = "成功";
"subscription_success_title" = "🎉 订阅成功！";
"subscription_success_pro_monthly" = "恭喜您成功订阅NaviBatch Pro月度计划！现在您可以享受无限地址、一键导航等所有高级功能。";
"subscription_success_pro_yearly" = "恭喜您成功订阅NaviBatch Pro年度计划！现在您可以享受无限地址、一键导航等所有高级功能，还节省了30%的费用。";
"subscription_success_expert" = "恭喜您成功订阅NaviBatch Expert计划！现在您可以享受所有高级功能，包括无限地址、一键导航、智能路线优化等。";
"subscription_success_features" = "✨ 已解锁功能：\n• 无限地址数量\n• 一键批量导航\n• 智能路线优化\n• 高级导出功能";
"subscription_success_action" = "立即体验高级功能";
"warning" = "警告";
"unknown_error" = "未知错误";
"loading" = "加载中...";
"search" = "搜索";
"settings" = "设置";
"help" = "帮助";
"about" = "关于";
"menu" = "菜单";
"understand" = "我明白了";
"navigation" = "导航";
"start_navigation" = "开始导航";
"subscription" = "订阅";
"upgrade_to_pro" = "升级至专业版";
"upgrade_description" = "一键导航分组地址，最多快60倍于手动操作";
"restore_purchases" = "恢复购买";

// App Update
"app_update_title" = "NaviBatch";
"app_update_subtitle" = "错误修复和改进";
"app_update_notes" = "错误修复和改进";
"current_version" = "当前版本";
"latest_version" = "最新版本";
"update_content" = "更新内容";
"update_now" = "立即更新";
"remind_later" = "稍后提醒";
"skip_version" = "跳过此版本";
"force_update_message" = "此版本包含重要更新，需要立即更新";
"restore_purchases_success" = "🎉 购买恢复成功！";
"restore_purchases_success_message" = "您的订阅已成功恢复，现在可以享受所有高级功能。";
"learn_more" = "了解更多";
"upgrade_your_plan" = "升级您的计划";
"one_click_navigation_description" = "一键导航分组地址，节省时间和燃油";
"current_plan" = "当前计划";
"upgrade" = "升级";
"maybe_later" = "稍后再说";
"free_tier_name" = "入门版";
"pro_tier_name" = "专业版";
"expert_tier_name" = "专家版";
"free_tier_price" = "免费";
"pro_tier_price" = "$29.99/月";
"expert_tier_price" = "$59.99/年";
"free_tier_description" = "适合个人和小型企业，不需要大量停靠点";
"pro_tier_description" = "适合需要一键导航分组和无限地址的用户";
"expert_tier_description" = "与Pro相同，但通过年度计划节省83%";
"route_optimization" = "路线优化";
"unlimited_routes" = "无限路线";
"unlimited_optimizations" = "无限优化次数";
"max_15_addresses" = "每条路线最多15个地址";
"save_fuel_30" = "节省燃油最多30%";
"unlimited_addresses" = "无限地址";
"one_click_navigation" = "⚡ 一键导航分组 - 最多快60倍 ⚡";
"package_finder" = "包裹位置查找器";
"annual_savings" = "年度计划节省费用";
"switched_to_free" = "已切换至免费版";
"switched_to_subscription" = "已切换至订阅版";
"unlimited_stops" = "无限停靠点";
"plan_as_many_stops_as_needed" = "添加任意数量的配送点，不受限制";
"expires_in_days" = "%@ 天后到期";
"trial_expires_in_days" = "试用期 %@ 天后到期";
"expired" = "已过期";
"subscription_expires_on" = "%@ 到期";
"subscription_active_until" = "有效期至 %@";
"enter_or_search_address" = "输入或搜索地址";
"search_results_count" = "搜索结果: %d";
"no_matching_addresses" = "未找到有效地址";
"search_address_failed" = "搜索地址失败: %@";
"address_search_no_response" = "地址搜索无响应";
"cannot_get_address_coordinates" = "无法获取地址坐标";

"cannot_get_coordinates_retry" = "无法获取地址坐标，请手动输入或重试";

"image_address_recognition" = "扫描器";
"select_images" = "选择图片";
"select_multiple_images" = "支持多选";
"united_states_delivery" = "美国快递";
"intelligent_text_analysis" = "智能分析文本中...";
"address_verification" = "地址验证中...";
"processing_image_segment" = "正在处理图片段";
"splitting_image" = "正在分割处理图片...";
"processing_segment" = "正在处理片段";
"merging_results" = "正在合并结果...";
"intelligent_splitting" = "正在智能分割图片...";
"analyzing_image_segment" = "正在分析图片片段";
"analyzing_image_similarity" = "正在分析图片相似度";
"processing_image_frames" = "正在处理图片帧";
"calculating_frame_similarity" = "正在计算帧相似度";
"merging_analysis_results" = "正在合并分析结果...";
"merging_processing_results" = "正在合并处理结果...";
"smart_split_complete" = "智能分割处理完成";
"australia_delivery" = "澳洲快递";
"universal_delivery" = "通用";
"hong_kong_service_restriction" = "香港地区服务限制";
"hong_kong_service_restriction_message" = "服务提供商已禁止中国香港地区访问高级识别服务。这是服务提供商的地理位置限制政策。建议使用OCR模式，它专为香港用户优化，效果同样出色。";
"advanced_recognition_service_restriction" = "高级识别服务限制";
"advanced_recognition_service_restriction_message" = "当前地区对高级识别服务有频率限制，服务间歇性可用。您可以尝试备用服务，或使用基础识别模式获得稳定体验。";
"use_ocr_mode" = "使用OCR模式";
"learn_ocr_advantages" = "了解OCR优势";
"try_backup_service" = "尝试备用服务";
"use_basic_mode" = "使用基础模式";
"processing_images" = "正在处理图片...";
"processing_image_progress" = "正在处理第 %d 张图片，共 %d 张";
"recognizing_text" = "正在识别文字...";
"geocoding_addresses" = "正在获取地址坐标...";
"analyzing_image_content" = "正在分析图片内容...";
"analyzing_image_text" = "正在分析图片文字...";
"fallback_to_ocr" = "使用备用识别方式...";
"using_advanced_recognition" = "🔥 使用高级识别...";
"recognition_complete" = "识别完成";
"no_text_recognized" = "未识别到文字";
"loading_images" = "正在加载图片...";
"analyzing_images" = "正在分析图片...";
"intelligent_analysis" = "正在智能分析...";
"processing_complete" = "处理完成";
"processing_images" = "正在处理图片...";
"processing_image_progress" = "正在处理第%d/%d张图片";
"processing_image_complete_waiting" = "处理完成第%d张，等待%d秒后继续...";
"processing_failed" = "处理失败";
"retry" = "重试";
"ocr_processing_complete" = "OCR处理完成";
"validating_addresses" = "验证地址...";
"model_used" = "使用模型: %@";
"no_images_loaded" = "未加载到图片";
"image_processing_failed" = "图片处理失败";
"intelligent_analysis_failed" = "智能分析失败";
"no_addresses_found" = "未找到有效地址";
"approximate_location" = "大概位置";
"image_recognition_failed" = "图片识别失败";
"image_recognition_error" = "图片识别出错: %@";
"text_recognition_failed" = "文字识别失败";
"address_parsing_failed" = "地址解析失败";
"select_addresses_to_add" = "选择要添加的地址";
"recognized_addresses" = "识别到的地址";
"address_coordinates" = "地址坐标";
"toggle_address_selection" = "切换地址选择";
"remove_address" = "移除地址";
"confirm_selected_addresses" = "确认选中的地址";
"no_addresses_selected" = "未选择地址";
"image_processing_cancelled" = "图片处理已取消";
"unsupported_image_format" = "不支持的图片格式";
"image_too_large" = "图片文件过大";
"image_recognition_permission_required" = "需要照片库访问权限";
"ocr_language_detection" = "自动检测语言";
"improve_image_quality" = "请确保图片清晰，文字可见";
"address_validation_in_progress" = "正在验证地址...";
"batch_address_import" = "批量地址导入";
"validated_addresses_count" = "已验证 %d 个地址";
"addresses_with_issues" = "%d addresses have issues";
"select_all" = "全选";
"import_selected" = "导入选中";
"validating_addresses" = "正在验证地址...";
"empty_address" = "空地址";
"invalid_coordinates" = "地址坐标无效";
"coordinate_warning" = "坐标警告";
"address_validation_issue" = "地址验证存在问题";
"cannot_get_coordinates" = "无法获取地址坐标";
"no_importable_addresses" = "没有可导入的地址，请检查地址限制";
"free_version_max_addresses" = "免费版最多允许%d个地址。";
"valid" = "有效";
"with_issues" = "有问题";
"low_confidence_address" = "地址验证置信度较低";
"address_validation_failed" = "地址验证失败";
"apple_maps_missing_house_number" = "Apple Maps返回的地址缺少门牌号信息";
"cannot_get_valid_coordinates" = "无法获取有效坐标";
"address_mismatch_with_house_number" = "Apple Maps返回的地址与门牌号不匹配";
"address_validation_failed_title" = "地址验证失败";
"address_validation_failed_message" = "以下地址存在问题，无法进行路线优化：";
"address_validation_failed_fix_message" = "请修正这些地址的坐标后再进行优化。";
"fix_addresses" = "修复地址";
"geocoding_success" = "地址坐标获取成功";
"geocoding_partial_success" = "地址坐标可能不准确，请检查";
"geocoding_failed" = "无法获取地址坐标";
"geocoding_rate_limited" = "地图服务请求过多，请稍后再试";
"geocoding_network_error" = "网络连接错误，请检查网络";
"geocoding_timeout" = "地址查询超时，请稍后再试";
"geocoding_invalid_address" = "地址格式无效，请检查";
"geocoding_not_found" = "找不到该地址，请检查拼写或添加更多详细信息";
"address_auto_corrected_error" = "地址不存在或不完整。系统尝试修正为：'%@'。请检查并输入完整正确的地址。";
"address_match" = "地址匹配";
"check_address_spelling" = "请检查地址拼写";
"confirm_address_complete" = "确认地址是否完整";
"current_addresses_remaining" = "当前已有%d个地址，只能再添加%d个地址。";
"can_import_selected" = "您选择了%d个地址，可以导入这些地址。";
"selected_exceeds_limit" = "您选择了%d个地址，超出可添加数量%d个。";
"selected_addresses_all_importable" = "您选择了%d个地址，可以全部导入。";
"upgrade_for_unlimited_addresses" = "升级到高级版可享受无限地址！";
"current_route_address_limit" = "当前路线已有%d个地址，只能再添加%d个地址，总计最多%d个地址。";
"import_all_addresses" = "导入所有地址";
"import_first_n" = "仅导入前%d个";
"import_selected_addresses" = "导入已选择的地址";
"upgrade_to_premium" = "升级到高级版";
"batch_add_addresses" = "批量添加地址";
"batch_address_input_placeholder" = "请输入或粘贴地址，每行一个地址。最多35个地址。";
"search_address" = "搜索地址";
"no_saved_addresses" = "没有保存的地址";
"no_saved_addresses_description" = "您还没有保存任何地址，或没有符合筛选条件的地址";
"select_address_book" = "选择地址簿";
"routes" = "路线";
"address_book" = "地址簿";
"saved_routes" = "已保存的路线";
"manage_your_routes" = "管理您的路线";
"manage_your_addresses" = "管理您的地址";
"preferences" = "偏好设置";
"set_custom_start_point" = "设置自定义起点";
"current_start_point" = "当前起点: %@";
"support" = "支持";
"contact_us" = "联系我们";
"contact_us_description" = "有问题或建议？给我们发送邮件";
"help_center" = "帮助中心";
"quick_actions" = "快速操作";
"main_features" = "主要功能";
"support_help" = "支持与帮助";
"customize_app_settings" = "自定义应用设置";
"unlock_all_features" = "解锁所有功能";
"get_help_support" = "获取帮助和支持";
"app_info_version" = "应用信息和版本";
"dev_tools" = "开发工具";
"debug_testing_tools" = "调试和测试工具";
"version" = "版本";
"addresses" = "地址";
"limited_to_20_addresses" = "限制20个地址";
"all_premium_features" = "所有高级功能";
"open_subscription_view" = "直接打开订阅视图";
"open_subscription_view_description" = "跳过中间层，直接显示 SubscriptionView";
"restore_purchases_failed" = "恢复购买失败: %@";
"rate_us" = "给我们评分";
"rate_us_description" = "您的评价对我们非常重要，帮助我们打造更好的应用！";
"share_app" = "分享应用";
"share_app_text" = "试试 NaviBatch 这款超棒的路线规划应用！";
"about_app" = "关于应用";
"developer_tools" = "开发者工具";
"coordinate_debug_tool" = "坐标调试工具";
"batch_fix_addresses" = "批量修正地址";
"clear_database" = "清空数据库";
"clear_database_confirmation" = "这将删除所有数据，包括路线、地址和分组。此操作无法撤销，确定要继续吗？";
"confirm_clear" = "确认";
"version_info" = "版本 %@ (%@)";
"current_system_language" = "当前系统语言";
"reset_to_system_language" = "重置为系统语言";
"language" = "语言";
"address" = "地址";
"coordinates" = "坐标";
"distance_from_current_location" = "与当前位置的距离";
"address_info" = "地址信息";
"update_coordinates" = "更新坐标";
"fix_address" = "修正地址";
"prompt" = "提示";
"kilometers_format" = "%.1f 公里";
"meters_format" = "%.0f 米";
"modify_address_first" = "请先修改地址";
"coordinates_update_success" = "坐标更新成功";
"coordinates_update_failure" = "坐标更新失败";
"save_failure" = "保存失败: %@";
"no_saved_addresses_title" = "没有保存的地址";
"no_saved_addresses_message" = "您还没有保存任何地址";
"add_new_address" = "添加新地址";
"address_title" = "地址";
"add" = "添加";
"refresh" = "刷新";
"notes" = "备注";
"address_details" = "地址详情";
"favorite" = "收藏";
"edit_address" = "编辑地址";
"confirm_delete" = "确认删除";
"delete_address_confirmation" = "确定要删除这个地址吗？此操作无法撤销。";
"address_marker" = "地址";
"address_label" = "地址:";
"notes_label" = "备注:";
"created_at_label" = "创建时间:";
"open_in_maps" = "在地图中打开";
"copy_address" = "复制地址";
"address_details_title" = "地址详情";
"start_end_point" = "起点/终点";
"start_point" = "起点";
"end_point" = "终点";
"route_info" = "路线信息";
"route_sheet_backup_button" = "路线信息";
"route_sheet_backup_button_tooltip" = "显示路线信息";
"address_count" = "地址数量";
"address_count_format" = "%d 个地址";
"points_count_format" = "%d 个点";
"additional_points_format" = "+%d 个点";
"export_route" = "导出路线";
"navigate" = "导航";
"address_list" = "地址列表";
"no_addresses" = "没有地址";
"no_addresses_message" = "此路线尚未添加任何地址";
"address_point_start" = "起点";
"address_point_stop" = "停靠点";
"address_point_end" = "终点";
"route_name" = "路线名称";
"new_route" = "新建路线";
"saved_route" = "已保存路线";
"plan_route" = "计划路线";
"clear_all" = "全部清除";
"restore" = "恢复顺序";
"avoid" = "避免:";
"toll_roads" = "收费公路";
"highways" = "高速公路";
"processing_addresses" = "正在处理地址...";
"same_start_end_point" = "您已将同一地址设为起点和终点";
"add_start_point" = "添加起点";
"swipe_left_to_delete" = "长按删除";
"add_end_point" = "添加终点";
"enter_and_select_address" = "输入并选择地址";
"current_search_text" = "当前搜索文本: %@";
"add_address" = "添加地址";
"selected_coordinates" = "已选择坐标";
"company_name_optional" = "公司名称（可选）";
"url_optional" = "网址（可选）";
"favorite_address" = "收藏地址";
"set_as_start_and_end" = "同时设为起点和终点";
"batch_paste" = "批量贴上";
"file_import" = "文件导入";
"web_download" = "线上下载";
"saving" = "保存中...";
"delivery_point_management" = "配送点管理";
"information_category" = "信息类别";
"package_info" = "包裹信息";
"vehicle_position" = "车辆位置";
"delivery_info" = "配送信息";
"take_photo_record" = "配送证明";
"update_status" = "更新状态";
"edit_address_button" = "编辑地址";
"access_instructions" = "访问说明";
"add_access_instructions" = "添加访问说明...";
"package_count" = "包裹数量";
"package_count_format" = "%d个包裹";
"packages" = "个包裹";
"package_unit" = "个";
"package_size" = "包裹大小";
"package_type" = "包裹类型";
"mark_as_important" = "标记为重要包裹";
"priority_delivery" = "优先配送";
"priority_level" = "优先级别";
"priority_1" = "优先级 1";
"priority_2" = "优先级 2";
"priority_3" = "优先级 3";
"no_priority" = "无优先级";
"no_priority_short" = "无";
"set_priority" = "设置优先级";
"select_package_position" = "选择包裹在车辆中的位置";
"vehicle_area" = "车辆区域";
"left_right_position" = "左右位置";
"vehicle_position_front" = "前部";
"vehicle_position_middle" = "中部";
"vehicle_position_back" = "后部";
"vehicle_position_left" = "左";
"vehicle_position_right" = "右";
"vehicle_position_floor" = "下部";
"vehicle_position_shelf" = "上部";
"height_position" = "高度位置";
"vehicle_position_none" = "未选择位置";
"delivery_type" = "配送类型";
"delivery_status" = "配送状态";
"order_info" = "订单信息";
"order_information" = "订单信息";
"order_number" = "订单号码";
"enter_order_number" = "输入订单号";
"tracking_number" = "追踪号码";
"enter_tracking_number" = "输入追踪号";
"tracking_info" = "追踪信息";
"tracking_information" = "追踪信息";
"time_info" = "时间信息";
"time_information" = "时间信息";
"estimated_arrival_time" = "预计到达时间";
"anytime" = "随时";
"stop_time" = "停留时间";
"minutes_format" = "%d 分钟";
"photo_record" = "交付凭证";
"door_number_photo" = "门牌号照片";
"package_label_photo" = "包裹标签照片";
"placement_photo" = "放置位置照片";
"door_number_desc" = "请拍摄清晰的门牌号或街道号码，确保数字/字母可见";
"package_label_desc" = "请拍摄包裹标签，确保收件人信息清晰可见";
"placement_desc" = "请拍摄包裹最终放置的位置";
"photo_captured" = "照片已拍摄";
"photo_captured_options" = "照片已拍摄，您想继续拍下一张照片还是完成当前照片？";
"continue_to_next_photo" = "继续拍下一张 - %@";
"retake" = "重新拍摄";
"tap_to_capture" = "点击拍摄";
"flash_auto" = "自动闪光";
"flash_on" = "开启闪光";
"flash_off" = "关闭闪光";
"photo_record_completed" = "配送证明完成";
"photo_confirmation" = "拍照确认";
"ok" = "确定";
"complete_photo_capture" = "完成拍照";
"photo_instructions" = "点击每个照片卡片进行拍摄。所有照片必须完成。";
"photo_options" = "照片选项";
"view_photo" = "查看照片";
"retake_photo" = "重新拍摄";
"saving_photos" = "正在保存照片...";
"completed" = "已完成";
"not_taken" = "未拍摄";
"route_options" = "路线选项";
"avoid_tolls" = "避免收费公路";
"avoid_highways" = "避免高速公路";
"optimize_route" = "优化路线";
"optimizing" = "优化中...";
"optimization_complete" = "优化完成";
"route_optimization_results" = "结果";
"route_planning_options" = "路线规划选项";
"before_optimization" = "优化前";
"after_optimization" = "优化后";
"auto_group" = "自动分组";
"optimized_route_order" = "顺序";
"apply" = "应用";
"kilometers" = "公里";
"street_number_issue_warning" = "⚠️ 门牌号差异过大！这可能导致送错地址被罚款，请立即核实地址";
"address_validation_critical" = "地址验证严重问题";
"street_number_difference_high_risk" = "门牌号差异过大，高风险";
"delete_address" = "删除地址";
"street" = "街道";
"city" = "城市";
"state" = "州/省";
"country" = "国家";
"postal_code" = "邮政编码";
"phone" = "电话";
"email" = "电子邮件";
"website" = "网站";
"company" = "公司";
"latitude" = "纬度";
"longitude" = "经度";
"geocoding_error" = "地理编码错误";
"address_validation" = "地址验证";
"invalid_addresses" = "无效地址";
"fix_addresses" = "修复地址";
"route" = "路线";
"select_address_point" = "选择地址点";
"select_delivery_points" = "选择配送点";
"create_delivery_route" = "创建配送分组";
"view_saved_routes" = "查看已保存的分组";
"create_route" = "创建路线";
"create_new_route" = "创建新路线";
"edit_route" = "编辑路线";
"delete_route" = "删除路线";
"route_details" = "路线详情";
"selected_addresses" = "已选择 %d 个地址";
"reached_limit" = "已达上限";
"can_select_more" = "还可选%d个";
"navigate_button" = "导航";
"create_group" = "创建分组";
"waypoints" = "途经点";
"total_distance" = "总距离";
"estimated_time" = "预计时间";
"route_summary" = "路线摘要";
"route_saved" = "路线已保存";
"route_optimized" = "路线已优化";
"optimizing_route" = "正在优化路线...";
"completed_percent" = "已完成 %d%%";
"processing_points" = "处理中: %d/%d";
"estimated_remaining_time" = "预计剩余时间: %@";
"delivery" = "配送";
"pickup" = "自取";
"delivery_confirmation" = "配送确认";
"take_photo" = "拍照";
"signature" = "签名";
"delivery_notes" = "配送备注";
"delivered" = "已送达";
"not_delivered" = "未配送";
"delivery_time" = "配送时间";
"delivery_date" = "配送日期";
"package_details" = "包裹详情";
"package_id" = "包裹ID";
"package_weight" = "包裹重量";
"package_dimensions" = "包裹尺寸";
"recipient_name" = "收件人姓名";
"recipient_phone" = "收件人电话";
"groups" = "分组";
"saved_groups" = "已保存的分组";
"edit_group" = "编辑分组";
"delete_group" = "删除分组";
"group_name" = "分组名称";
"group_details" = "分组详情";
"auto_grouping" = "自动分组";
"group_by" = "分组依据";
"add_to_group" = "添加到分组";
"remove_from_group" = "从分组中移除";
"group_created" = "分组已创建";
"default_group_name_format" = "分组%d";
"auto_grouping_completed" = "自动分组已完成";
"auto_grouping_in_progress" = "自动分组进行中...";
"create_group_every_14_addresses" = "每14个地址创建一个分组";
"create_delivery_group" = "创建配送分组";
"enter_group_name" = "输入分组名称";
"selected_delivery_points" = "已选择的配送点";
"drag_to_adjust_order" = "拖动可调整顺序";
"free_plan" = "免费版";
"pro_plan" = "专业版";
"expert_plan" = "专家版";
"monthly" = "月度计划";
"yearly" = "年度计划";
"subscribe" = "订阅";
"manage_subscription" = "管理订阅";
"subscription_benefits" = "订阅权益";
"free_trial" = "免费试用";
"price_per_month" = "每月 %@";
"price_per_year" = "每年 %@";
"save_percent" = "节省 %@%";
"subscription_terms" = "订阅条款";
"privacy_policy" = "隐私政策";
"terms_of_service" = "服务条款";
"feature_comparison" = "功能对比";
"addresses_per_route" = "每条路线地址数";
"third_party_sort_label" = "%@: %@";
"max_20_addresses" = "20个";
"fuel_savings" = "燃油节省";
"up_to_30_percent" = "最多30%";
"choose_subscription_plan" = "选择订阅计划";
"monthly_plan" = "月度计划";
"yearly_plan" = "年度计划";
"/month_suffix" = "/月";
"/year_suffix" = "/年";
"save_30_percent" = "节省30%";
"free_trial_7_days_cancel_anytime" = "包含60天免费试用，随时可取消";
"subscription_auto_renew_notice" = "订阅将在试用期结束后自动续费，除非在试用期结束前取消。";
"and" = "和";
"subscription_exclusive" = "订阅专享";
"free_version_optimization_limit" = "免费版优化限制";
"free_version_supports_max_addresses" = "免费版仅支持最多%d个地址。";
"current_route_contains_addresses" = "当前路线包含%d个地址，超出了免费版限制。";
"upgrade_to_pro_unlimited_addresses" = "升级到高级版可享受无限地址、无限制分组和多组导航功能！";
"continue_optimization" = "继续优化";
"upgrade_unlock_one_click_navigation" = "升级解锁一键导航 - 快14x";
"learn_one_click_navigation_grouping" = "了解一键导航分组功能";
"toggle_subscription_status" = "切换订阅状态";
"toggle_subscription_description" = "在免费版和专业版之间切换（仅用于开发测试）";
"product_info_unavailable" = "无法获取产品信息，请稍后再试";
"purchase_failed" = "购买失败: %@";
"upgrade_to_pro_version" = "升级到Pro版";
"unlock_all_premium_features" = "解锁所有高级功能";
"first_7_days_free_cancel_anytime" = "前14天免费，随时可取消。";
"free_version_can_group_addresses" = "免费版可分组 %d 个地址";
"reached_free_version_limit" = "已达免费版限制";
"creating_groups" = "正在创建分组...";
"free_version_grouping_feature" = "免费版一键分组功能";
"free_version_grouping_description" = "体验智能分组，最多可将10个地址自动分组";
"upgrade_for_unlimited_grouping" = "升级Pro版享受无限制分组功能";
"upgrade_to_unlock_auto_grouping" = "升级到Pro版解锁自动分组功能";
"free_user_grouping_limit_reached" = "您已达到免费版%d个地址分组限制。升级到Pro版享受无限制分组！";
"upgrade_to_unlock_more_grouping" = "升级到Pro版解锁无限制分组功能";
"free_user_max_groups_reached" = "您已达到免费版%d个分组限制。升级到Pro版享受无限制分组！";
"free_tier_grouping_limit" = "最多10个地址";
"pro_tier_unlimited_grouping" = "无限制分组";
"auto_grouping" = "一键智能分组";
"free_tier_navigation_limit" = "1组导航(最多10个地址)";
"pro_tier_unlimited_navigation" = "多组导航(无限制)";
"payment_terms_notice" = "购买确认后将从您的Apple ID账户扣费。订阅将自动续费，除非在当前周期结束前至少24小时取消。";
"terms_of_use" = "使用条款";
"product_load_failed_check_connection" = "无法加载产品信息，请确保您的设备已连接到互联网并已登录 App Store";
"product_load_failed" = "加载产品失败: %@";
"verify_receipt" = "验证收据";
"one_click_navigation_short" = "一键导航";
"save_30_percent_fuel" = "节省30%燃油";
"monthly_short" = "月度";
"yearly_short" = "年度";
"upgrade_now" = "立即升级";
"test_environment_pro_activated" = "🧪 测试环境：Pro版本已激活\n现在可以体验所有高级功能！";
"payment_terms_notice_detailed" = "购买确认后将从您的Apple ID账户扣费。订阅将自动续费，除非在当前周期结束前至少24小时取消。您可以在App Store设置中管理和取消订阅。";
"step_screenshot" = "步骤 %d 截图";
"previous_step" = "上一步";
"next_step" = "下一步";
"each_address_takes_3_5_seconds" = "每个地址需要3-5秒添加";
"need_repeat_14_times" = "需要重复14次相同操作";
"navigation_order_often_confused" = "导航顺序经常混乱";
"error_prone_need_redo" = "容易出错，需要重新操作";
"address_order_reversed_manual_adjust" = "地址顺序颠倒，需手动调整";
"one_click_add_all" = "一次点击，全部添加";
"smart_grouping_auto_sorting" = "智能分组，自动排序";
"maintain_correct_visit_order" = "保持正确访问顺序";
"zero_errors_zero_repetition" = "零错误，零重复操作";
"import" = "导入";
"export" = "导出";
"import_addresses" = "导入地址";
"export_addresses" = "导出地址";
"import_from_file" = "从文件导入";
"export_to_file" = "导出到文件";
"file_format" = "文件格式";
"csv_format" = "CSV 格式";
"excel_format" = "Excel 格式";
"json_format" = "JSON 格式";
"import_success" = "成功导入 %d 个地址，所有地址坐标正常。";
"export_success" = "导出成功";
"import_error" = "导入错误";
"export_error" = "导出错误";
"navigation_app" = "导航应用";
"apple_maps" = "苹果地图";
"app_preferences" = "应用偏好设置";
"distance_unit" = "距离单位";
"current_language" = "当前语言";
"info" = "信息";
"contact_us_header" = "联系我们";
"contact_us_subheader" = "有问题或建议？我们很乐意为您提供帮助！";
"contact_options" = "联系方式";
"email_us" = "发送邮件";
"contact_form" = "联系表单";
"contact_and_support" = "联系与支持";
"common_questions" = "常见问题";
"how_to_use" = "如何使用";
"subscription_faq" = "订阅常见问题";
"navigation_help" = "导航帮助";
"troubleshooting" = "故障排除";
"help_howto_content" = "NaviBatch是一款强大的路线规划应用，帮助您优化配送路线，节省时间和燃油。您可以添加多个地址，自动优化路线顺序，并一键导航到Apple Maps。";
"help_subscription_content" = "NaviBatch提供免费版和专业版。免费版支持最多20个地址，专业版提供无限地址和一键分组导航功能。";
"help_navigation_content" = "NaviBatch使用Apple Maps进行导航。您可以单独导航到每个地址，或使用分组功能一次性导航多个地址。";
"help_troubleshooting_content" = "如果遇到问题，请首先确保您的设备有网络连接，并且已授权位置权限。如果问题仍然存在，请联系我们的支持团队。";
"actions" = "操作";
"legal" = "法律信息";
"show_look_around" = "查看实景图";
"hide_look_around" = "隐藏实景图";
"map" = "地图";
"map_type" = "地图类型";
"standard" = "标准";
"satellite" = "卫星";
"hybrid" = "混合";
"show_traffic" = "显示交通状况";
"current_location" = "当前位置";
"directions" = "路线指引";
"distance_to" = "距离";
"eta" = "预计到达时间";
"look_around" = "环视";
"locating_to_glen_waverley" = "定位到格伦韦弗利";
"network_error" = "网络错误";
"location_error" = "位置错误";
"permission_denied" = "权限被拒绝";
"location_permission_required" = "需要位置权限";
"camera_permission_required" = "需要相机权限";
"photo_library_permission_required" = "需要照片库权限";
"please_try_again" = "请重试";
"something_went_wrong" = "出现错误";
"invalid_input" = "无效输入";
"required_field" = "必填字段";
"no_internet_connection" = "无网络连接";
"server_error" = "服务器错误";
"timeout_error" = "请求超时";
"data_not_found" = "未找到数据";
"selection_limit_reached" = "已达到选择限制";
"selection_limit_description" = "您最多可以选择%d个地址。当前已选择%d个地址";
"location_status_valid" = "有效范围";
"address_validation_unknown" = "未验证";
"address_validation_valid" = "有效";
"address_validation_invalid" = "无效";
"address_validation_warning" = "警告";
"address_validation_mismatch" = "不匹配";
"device_not_support_scanning" = "设备不支持原生扫描";
"requires_ios16_a12_chip" = "需要iOS 16+和A12芯片以上设备";
"debug_info" = "调试信息:";
"address_confirmation" = "地址确认";
"continue_scanning" = "继续扫描";
"confirm_add" = "确认添加";
"cannot_get_coordinates_scan_retry" = "无法获取地址坐标，请手动输入或重新扫描";
"unknown_country" = "未知国家";
"unknown_city" = "未知城市";
"please_enter_valid_address" = "请输入至少一个有效地址";
"please_select_valid_address" = "请选择有效地址";
"add_address_failed" = "添加地址失败: %@";
"location_permission_required_for_current_location" = "需要位置权限才能使用当前位置。请在设置中允许位置访问。";
"cannot_get_current_location_check_settings" = "无法获取当前位置，请确保位置服务已开启。";
"cannot_get_current_location_address" = "无法获取当前位置的地址信息。";
"get_current_location_failed" = "获取当前位置失败: %@";
"location_status_warning" = "警告范围";
"location_status_invalid" = "无效位置";
"location_status_unknown" = "未知状态";
"coordinates_origin_point" = "无效：零坐标 (0,0)";
"coordinates_invalid_nan" = "无效：非数字坐标";
"coordinates_out_of_range" = "无效：坐标超出有效范围";
"coordinates_far_from_user" = "警告：位置距离您当前位置较远";
"coordinates_ocean" = "警告：位置可能在海洋或无人区";
"free_address_limit" = "免费版地址限制";
"address_count_limit" = "地址数量限制";
"selected_addresses_can_import" = "您选择了%d个地址，可以导入这些地址。";
"selected_addresses_exceeds" = "您选择了%d个地址，超出可添加数量%d个。";
"import_success_with_warnings" = "成功导入 %d 个地址，其中 %d 个地址坐标正常，%d 个地址有警告。\n\n有警告的地址已标记，可以在导入后手动修复。";
"supported_formats" = "支持的格式";
"supported_format_csv" = "• CSV文件: 地址列应包含完整地址";
"supported_format_json" = "• JSON数据: 包含地址字段的数组";
"supported_format_text" = "• 纯文本: 每行一个地址";
"download_history" = "下载历史";
"input_address_data_url" = "输入地址数据URL";
"import_result" = "导入结果";
"downloading" = "正在下载...";
"processing_data" = "正在处理数据...";
"google_drive_download_failed" = "Google Drive 下载失败";
"second_attempt_invalid_data" = "二次尝试下载返回无效数据";
"cannot_parse_json" = "无法解析JSON数据，请检查文件格式";
"cannot_parse_json_with_error" = "无法解析JSON数据：%@";
"cannot_read_file" = "无法读取文件：%@";
"failed" = "派送失败";
"no_valid_addresses" = "未找到有效地址";
"supports_file_types" = "支持CSV、TXT和JSON文件";
"tap_to_select_file" = "点击选择文件";
"input_company_name" = "输入公司名称（可选）";
"imported_addresses_count" = "已导入 %d 个地址";
"excel_format_not_supported" = "不支持Excel格式";
"import_failed" = "导入失败";
"free_version_address_limit" = "免费版最多允许%d个地址。";
"current_address_count" = "当前已有%d个地址，只能再添加%d个地址。";
"upgrade_to_premium_unlimited" = "升级到高级版可享受无限地址！";
"route_address_limit" = "当前路线已有%d个地址，只能再添加%d个地址，总计最多%d个地址。";
"free_version_limit" = "免费版地址限制";
"import_all_n" = "导入全部%d个";
"cannot_import" = "无法导入";
"select_at_least_one" = "请至少选择一个地址";
"no_valid_addresses_found" = "未找到有效地址";
"import_success_all_valid" = "成功导入 %d 个地址，所有地址坐标正常。";
"import_success_some_warnings" = "成功导入 %d 个地址，其中 %d 个地址坐标正常，%d 个地址有警告。";
"company_format" = "公司: %@";
"added_from_web_download" = "从线上下载添加";
"invalid_csv_row" = "无效的CSV行";
"distance_warning" = "距离当前位置超过200公里";
"not_in_australia" = "坐标不在澳大利亚范围内";
"invalid_address_data" = "无效的地址数据";
"distance_warning_confirm" = "地址距离较远，请确认";
"coordinates_missing" = "地址坐标缺失";
"low_accuracy_address" = "地址精度较低";
"address_partial_match" = "地址部分匹配，可能不准确";
"address_outside_region" = "地址在目标区域外";
"api_limit_reached" = "地图API请求限制，稍后再试";
"address_not_exist_or_incorrect_format" = "地址不存在或格式不正确";
"please_check_address_spelling" = "请检查地址拼写";
"try_smaller_street_number" = "如果街道号码较大，尝试使用该街道的较小号码";
"use_full_street_type_name" = "确保使用完整的街道类型名称（如 'Lane' 而不是 'La'）";
"try_add_more_address_details" = "尝试添加更多地址详细信息";
"cannot_find_address" = "无法找到该地址";
"please_check_spelling_or_add_details" = "请检查地址拼写或添加更多详细信息";
"cannot_find_address_check_spelling" = "无法找到该地址，请检查拼写或添加更多详细信息";
"address_not_set" = "未设置地址";
"address_format_incomplete" = "地址格式不完整或包含错误信息，请检查并修正";
"location_service_denied" = "位置服务被拒绝，请检查应用权限设置";
"no_saved_groups" = "没有保存的分组";
"select_points_create_groups" = "选择配送点并创建分组以便于管理";
"navigate_to_these_points" = "导航到这些点";
"confirm_remove_address" = "确定要从分组中移除地址 \"%@\" 吗？";
"confirm_remove_this_address" = "确定要从分组中移除此地址吗？";
"addresses_count" = "%d 个地址";
"no_saved_routes" = "没有已保存的路线";
"no_saved_routes_description" = "您还没有保存任何路线";
"all_routes" = "所有路线";
"address_count_format_simple" = "%d 个地址";
"delete_all_routes" = "删除所有路线";
"navigate_to_all_points" = "导航到所有点";
"confirm_navigate_to_route" = "确定要导航到路线\"%@\"中的所有点吗？";
"temp_navigation_group" = "临时导航分组";
"route_management" = "路线管理";
"manage_routes" = "管理路线";
"route_addresses" = "路线地址";
"no_addresses_in_route" = "此路线没有地址";
"must_keep_one_route" = "必须至少保留一条路线";
"confirm_delete_route" = "确定要删除路线\"%@\"吗？此操作无法撤销。";
"confirm_delete_all_routes" = "确认删除所有路线";
"confirm_delete_all_routes_message" = "确定要删除所有路线吗？此操作无法撤销。";
"delete_all" = "删除所有";
"address_information" = "地址信息";
"group_belonging" = "所属分组";
"view_map" = "查看地图";
"delete_delivery_point" = "删除配送点";
"delivery_point_details" = "配送点详情";
"confirm_deletion" = "确认删除";
"delete_delivery_point_confirmation" = "确定要删除这个配送点吗？此操作无法撤销。";
"delivery_photos" = "配送照片";
"view_delivery_photos" = "查看配送照片";
"no_photos_taken" = "尚未拍摄照片";
"take_photos" = "拍摄照片";
"loading_photos" = "正在加载照片...";
"photo_not_found" = "照片未找到";
"photo_deleted" = "照片已被删除";
"share_photos" = "分享照片";
"photo_capture_title" = "拍照确认";
"door_number_photo_title" = "道路号码/门牌照片";
"package_label_photo_title" = "包裹标签照片";
"placement_photo_title" = "放置位置照片";
"door_number_photo_desc" = "请拍摄清晰的门牌号，确保数字/字母可见";
"package_label_photo_desc" = "请拍摄包裹标签，确保收件人信息清晰可见";
"placement_photo_desc" = "请拍摄包裹最终放置的位置";
"swipe_to_switch" = "滑动切换照片类型";
"photos_will_be_saved_to" = "照片将保存到相册：";
"complete_photos" = "完成拍照";
"photo_save_success" = "照片保存成功";
"photo_save_failure" = "保存照片失败";
"no_photos_found" = "未找到照片";
"photos_deleted_or_not_taken" = "可能照片已被删除或尚未拍摄";
"share_photo" = "分享照片";
"photo_capture_preview" = "预览模式 - 相机模拟";
"photo_capture_close" = "关闭";
"camera_start_failed" = "相机启动失败";
"camera_start_failed_retry" = "无法启动相机，请重试";
"camera_init_failed" = "相机初始化失败";
"camera_access_failed" = "无法访问相机";
"photo_processing_failed" = "拍照失败";
"photo_processing_failed_retry" = "无法完成照片处理，请重试";
"photo_capture_progress" = "进度: %d/%d";
"photo_captured_continue" = "已拍摄完成，继续拍摄%@";
"pending" = "待派送";
"in_progress" = "派送中";
"select_delivery_status" = "选择配送状态";
"select_failure_reason" = "选择失败原因";
"delivery_status_pending" = "待配送";
"delivery_status_in_progress" = "配送中";
"delivery_status_completed" = "已完成";
"delivery_status_failed" = "配送失败";
"failure_reason_not_at_home" = "客户不在家";
"failure_reason_wrong_address" = "地址错误";
"failure_reason_no_access" = "无法进入位置";
"failure_reason_rejected" = "包裹被拒收";
"failure_reason_other" = "其他原因";
"enter_custom_reason" = "输入具体原因";
"custom_reason_placeholder" = "请描述具体原因...";
"custom_reason_required" = "请输入具体原因";
"failure_reason_required" = "请选择失败原因";
"delivery_type_delivery" = "配送";
"delivery_type_pickup" = "自取";
"delivery_order_first" = "优先";
"delivery_order_auto" = "自动";
"delivery_order_last" = "最后";
"package_size_small" = "小";
"package_size_medium" = "中";
"package_size_large" = "大";
"package_type_box" = "箱子";
"package_type_bag" = "袋子";
"package_type_letter" = "信件";
"one_click_navigation_grouping" = "一键导航分组";
"speed_60x_faster" = "快60倍";
"goodbye_manual_address_adding" = "从此告别繁琐的手动添加地址";
"watch_detailed_demo" = "观看详细演示";
"upgrade_to_pro_now" = "立即升级到Pro版";
"free_trial_7_days" = "14天免费试用，随时可取消";
"traditional_vs_navibatch_pro" = "传统方式 vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "← 左右滑动查看完整对比 →";
"traditional_method" = "传统方式";
"drivers_get_lost_affect_efficiency" = "司机容易迷路，影响配送效率";
"repetitive_operations_waste_time" = "重复操作浪费大量时间";
"total_time_60_seconds" = "总耗时: 60秒";
"navibatch_pro" = "NaviBatch 专业版";
"optimize_routes_reduce_distance" = "优化路线，减少行驶距离";
"improve_delivery_efficiency_accuracy" = "提升配送效率和准确性";
"speed_boost_60x" = "速度提升60倍 ⚡";
"total_time_1_second" = "总耗时: 1秒";
"time_comparison" = "时间对比";
"traditional_method_problems" = "传统方式的问题:";
"each_address_3_5_seconds_14_total_60" = "• 每个地址需要3-5秒，35个地址约需2分钟";
"repetitive_operations_cause_fatigue" = "• 重复操作容易导致用户疲劳和出错";
"address_order_reversed_last_becomes_first" = "• 地址顺序颠倒，最后添加的地址成为首个导航点";
"need_manual_reverse_adding_takes_longer" = "• 需要手动反向添加地址，耗时更长";
"navibatch_advantages" = "NaviBatch 的优势:";
"add_14_addresses_1_second_60x_faster" = "• 一次性添加35个地址仅需1秒，速度提升60倍";
"auto_maintain_correct_order_no_adjustment" = "• 自动保持正确的访问顺序，无需手动调整";
"zero_error_rate_no_repetition" = "• 零错误率，无需重复操作";
"save_59_seconds" = "节省59秒！";
"speed_boost_60x_simple" = "速度提升60倍";
"seconds_format" = "%d秒";
"actual_benefits_one_click_navigation" = "使用一键导航分组的实际收益";
"daily_savings" = "每日节省";
"daily_savings_value" = "30+ 分钟";
"daily_savings_description" = "减少路线设置时间";
"monthly_savings" = "每月节省";
"monthly_savings_value" = "15+ 小时";
"monthly_savings_description" = "可用于增加配送量或休息";
"fuel_savings_value" = "最多30%";
"fuel_savings_description" = "通过优化路线减少行驶距离";
"income_increase" = "收入提升";
"income_increase_value" = "20-25%";
"income_increase_description" = "通过增加每日配送量";
"trial" = "试用";
"days_left" = "天剩余";
"free_plan_description" = "最多20个地址";
"pro_plan_active" = "专业版已激活";
"expert_plan_active" = "专家版已激活";
"trial_active" = "试用期进行中";
"trial_expires_on" = "试用期至 %@";
"address_validation_mode" = "地址验证模式";
"validation_description" = "选择地址验证的严格程度。建议配送企业使用严格模式以确保地址准确性。";
"current_settings" = "当前设置";
"validation_mode_format" = "验证模式: %@";
"threshold_score_format" = "通过阈值: %d 分";
"validation_example" = "验证示例";
"original_address_example" = "原始地址: 55 Batten Street, Glen Waverley, VIC 3150";
"reverse_address_example" = "反向地址: 5 Batten St, Glen Waverley, VIC 3150";
"house_number_difference" = "门牌号差异: 50 (极高风险)";
"result_label" = "结果:";
"may_pass_warning" = "可能通过 ⚠️";
"will_not_pass" = "不会通过 ❌";
"real_case_example" = "真实案例示例";
"real_case_description" = "基于您提供的真实地址验证案例。严格模式和完美模式都会拒绝这个高风险地址。";
"address_validation_settings" = "地址验证设置";
"clear" = "清除";
"view_details" = "查看详情";
"create_test_data" = "创建测试数据";
"manual_snapshot" = "手动快照";
"start_location_updates" = "开始位置更新";
"stop_location_updates" = "停止位置更新";
"user_location_marker_test" = "用户位置标记测试";
"location_animation_control" = "位置和动画控制";
"current_location_format" = "当前位置: %@, %@";
"waiting_for_location" = "等待位置信息...";
"diagnostic_tools" = "诊断工具";
"storekit_diagnostics" = "StoreKit 诊断";
"subscription_function_test" = "订阅功能测试";
"localization_test" = "本地化测试";
"address_validation_demo" = "地址验证演示";
"localization_tools" = "本地化工具";
"coordinate_debug_tools" = "坐标调试工具";
"smart_abbreviation_expansion_test" = "智能缩写扩展测试";
"subscription_restore_diagnostics" = "订阅恢复诊断";
"batch_address_import_test" = "批量地址导入测试";
"test_import_1000_addresses_memory" = "测试导入1000个地址的内存使用";
"map_rendering_test" = "地图渲染测试";
"test_map_display_markers_memory" = "测试地图显示大量标记的内存使用";
"select_test_language" = "选择测试语言";
"discover_60x_speed_boost" = "发现60倍速度提升";
"see_60x_speed_demo" = "查看60倍速度演示";
"free_vs_pro_comparison" = "免费版 vs 专业版对比";
"our_free_beats_competitors_paid" = "我们的免费版超越竞争对手的付费版！";
"features" = "功能";
"up_to_20" = "最多20个";
"unlimited" = "无限制";
"smart_optimization" = "智能路线优化";
"up_to_20_percent" = "最多20%";
"file_not_found" = "文件未找到";
"sample_file_not_available" = "示例文件暂时不可用";
"file_copy_failed" = "准备示例文件分享失败";

// SavedGroups related strings
"no_saved_groups" = "暂无已保存分组";
"select_points_create_groups" = "选择配送点创建分组";
"remaining_groups_locked" = "还有 %d 个分组";
"upgrade_to_unlock_all_groups" = "升级到专业版以解锁所有分组导航功能";
"upgrade_now" = "立即升级";

// AI Configuration strings
"recognition_settings" = "识别设置";

// Trial Promotion strings
"trial_promotion_title_1" = "开启 Pro 版本，";
"trial_promotion_title_2" = "体验 60x 速度提升";
"trial_promotion_title_3" = "无限地址导航";
"trial_promotion_subtitle_1" = "无限地址添加，智能路线优化，一键分组导航";
"trial_promotion_subtitle_2" = "让您的配送效率提升 60 倍";
"trial_promotion_feature_unlimited_addresses" = "无限地址";
"trial_promotion_feature_unlimited_addresses_desc" = "添加任意数量的配送地址";
"trial_promotion_feature_60x_speed" = "60x 速度提升";
"trial_promotion_feature_60x_speed_desc" = "一键分组导航，极速配送";
"trial_promotion_feature_smart_optimization" = "智能路线优化";
"trial_promotion_feature_smart_optimization_desc" = "AI 算法优化最短路径";
"trial_promotion_feature_unlimited_groups" = "无限分组";
"trial_promotion_feature_unlimited_groups_desc" = "创建多个配送分组";
"trial_promotion_start_trial" = "开始 60 天免费试用";
"trial_promotion_terms" = "需要订阅相关条款。随时可以取消。";
"trial_promotion_terms_details" = "60天免费试用期结束后，将自动续订为年度订阅。您可以在试用期内随时取消，不会产生任何费用。取消后仍可使用至试用期结束。";
"recognition_mode" = "识别模式";
"enable_advanced_recognition" = "启用高级识别";
"algorithm_selection" = "算法选择";
"accuracy_threshold" = "准确度阈值";
"fallback_options" = "备用选项";
"auto_fallback_to_basic" = "自动降级到基础识别";
"max_retry_attempts" = "最大重试次数";
"configuration_info" = "配置信息";
"view_recognition_logs" = "查看识别日志";
"reset_to_defaults" = "重置为默认设置";
"unknown_algorithm" = "未知算法";
"enabled" = "启用";
"disabled" = "禁用";
"advanced_recognition" = "高级识别";
"algorithm" = "算法";

// Rate Limit Warning strings
"api_rate_limit" = "API速率限制";
"please_wait_and_try_again" = "请稍等片刻后再试";
"rate_limit_protection_message" = "为了保护Apple Maps服务，我们需要暂时减慢处理速度。";
"rate_limit_normal_protection" = "• 这是正常的保护机制，不会影响数据质量";
"rate_limit_batch_suggestion" = "• 建议分批处理大量地址，每批不超过10个";
"rate_limit_auto_resume" = "• 处理将在 %d 秒后自动恢复";
"continue_waiting" = "继续等待";

// Simple Loading Messages
"importing_addresses_please_wait" = "正在导入地址，请稍候...";
"processing_addresses" = "正在处理地址...";
"please_keep_app_open" = "请保持应用开启";
"progress_with_rate_limit" = "因速率限制处理变慢 - 已完成 %.0f%%";

// Welcome Screen
"welcome_to" = "欢迎使用";
"delivery_driver_smart_assistant" = "配送司机的智能导航助手";
"addresses_batch_processing" = "个地址";
"batch_processing" = "批量处理";
"hours_daily_savings" = "小时";
"daily_savings" = "每日节省";
"efficiency_improvement" = "效率";
"improvement_rate" = "提升幅度";
"smart_scanning" = "智能扫描";
"smart_scanning_description" = "拍照识别地址，自动提取配送信息";
"batch_navigation" = "批量导航";
"batch_navigation_description" = "一键添加多个地址，智能规划最优路线";
"time_saving" = "节省时间";
"time_saving_description" = "每天节省2小时，提升配送效率300%";
"professional_tools" = "专业工具";
"professional_tools_description" = "为配送司机量身定制的专业导航工具";
"for_best_experience_permissions" = "为了提供最佳体验，NaviBatch需要以下权限：";
"location_permission" = "位置";
"camera_permission" = "相机";
"photos_permission" = "照片";
"start_using_navibatch" = "开始使用 NaviBatch";

// Onboarding Screen
"step_progress_format" = "第 %d 步，共 %d 步";
"photo_scan_addresses" = "拍照扫描地址";
"photo_scan_description" = "使用相机扫描配送单据，AI自动识别地址信息";
"photo_scan_instruction" = "点击相机按钮，对准配送单据拍照";
"batch_add_addresses" = "批量添加地址";
"batch_add_description" = "一次性添加多个配送地址，支持手动输入和文件导入";
"batch_add_instruction" = "可添加最多35个地址进行批量处理";
"smart_route_optimization" = "智能路线优化";
"route_optimization_description" = "AI算法自动规划最优配送路线，节省时间和油费";
"route_optimization_instruction" = "点击优化按钮，系统自动计算最佳路线";
"one_click_batch_navigation" = "一键批量导航";
"batch_navigation_description" = "直接启动导航应用，按优化顺序逐个配送";
"batch_navigation_instruction" = "选择地址后点击GO按钮开始导航";
"operation_tips" = "操作提示";
"previous_step" = "上一步";
"next_step" = "下一步";
"start_using" = "开始使用";
"skip_guide" = "跳过引导";

// App Store 更新说明 v1.0.8
"update_v108_title" = "NaviBatch v1.0.8 - 自动配送分类";
"update_v108_subtitle" = "自动地区分类系统";

// 主要功能
"update_v108_feature_regional_classification" = "自动地区分类";
"update_v108_feature_ai_enhancement" = "增强识别功能";
"update_v108_feature_user_experience" = "改进用户体验";

// 地区分类
"update_v108_us_delivery" = "美国快递：Amazon Flex、iMile、GoFo、LDS EPOD、PIGGY、UNIUNI、YWE、SpeedX";
"update_v108_au_delivery" = "澳洲快递：iMile（主要服务）";
"update_v108_auto_detect" = "自动检测美国/澳洲地址格式";

// 识别增强
"update_v108_region_rules" = "地区特定地址规则（美国5位邮编 vs 澳洲4位邮编）";
"update_v108_company_features" = "快递公司专属特征识别";
"update_v108_imile_support" = "iMile双地区支持";

// 用户体验
"update_v108_gofo_priority" = "GoFo在美国快递组优先显示";
"update_v108_clean_interface" = "地区分组界面更清晰";
"update_v108_faster_selection" = "快递应用选择更快速";

// 改进内容
"update_v108_improvements_title" = "改进内容：";
"update_v108_better_detection" = "更好的地址格式检测";
"update_v108_reduced_confusion" = "减少选择困惑";
"update_v108_enhanced_accuracy" = "提升地区地址识别准确性";
"update_v108_optimized_workflow" = "优化配送工作流程";

"update_v108_perfect_for" = "完美适配多平台配送司机！";

// 数据库状态
"database_status" = "数据库状态";
"total_addresses" = "总地址数";
"hit_rate" = "命中率";
"database_size" = "数据库大小";
"most_used_addresses" = "最常用地址";
"frequently_used" = "常用地址";

// 国家检测和选择
"detecting_location" = "正在检测您的位置...";
"detected_in_country" = "检测到您在%@";

// 地址处理错误消息
"address_processing_failed_retry" = "地址处理失败：%@。请重试或手动输入完整地址。";
"address_processing_timeout" = "地址处理超时，请重试或手动输入地址";
"address_has_problem_fix" = "此地址存在问题：%@。请修正地址或重新获取坐标。";
"address_problem_found" = "发现地址问题";
"address_problem_details" = "在%@过程中，以下 %d 个地址无法获取准确坐标，可能影响导航精度：\n\n• %@\n\n建议您检查这些地址的拼写或格式。";

// 地址问题原因
"geocoding_failed" = "地理编码失败";
"invalid_address_format" = "地址格式无效";
"empty_address" = "地址为空";
"address_too_short" = "地址过短";
"no_valid_coordinate" = "无法获取有效坐标";
"delivery_services_for_country" = "%@快递服务";
"confirm_country" = "确认";
"change_country" = "更改";
"select_region" = "选择地区";
"popular_regions" = "常用地区";
"all_regions" = "所有地区";
"supported_services_count" = "%d个支持的服务";
"low_confidence_detection" = "检测置信度较低，建议确认";

// 国家名称
"country_united_states" = "美国";
"country_australia" = "澳大利亚";
"country_canada" = "加拿大";
"country_united_kingdom" = "英国";
"country_germany" = "德国";
"country_france" = "法国";
"country_japan" = "日本";
"country_china" = "中国";

// 扫描器相关
"select_delivery_app_type" = "选择快递服务";
"delivery_service" = "快递";

// 图片分析相关
"start_analysis" = "开始分析";
"analyze_selected_images" = "分析选中的图片";
"image_viewer" = "图片查看器";
"close" = "关闭";
"delete_image" = "删除图片";

// 地图模式选择器
"map_mode_selector_title" = "地图模式";
"map_mode_standard" = "标准";
"map_mode_satellite" = "卫星";
"map_mode_traffic" = "交通";
"map_mode_hybrid" = "混合";

// 路线导出相关
"copy_route_numbers" = "复制路线号";
"copied" = "已复制!";
"copied_route_numbers_success" = "已复制%d个路线号到剪贴板";

// 第三方号码编辑相关
"edit_third_party_number" = "编辑第三方排序号";
"edit_third_party_number_description" = "修改 %@ 的排序号";
"sort_number" = "排序号";
"enter_sort_number" = "输入排序号";
"duplicate_third_party_number" = "重复的第三方号码";
"confirm_save" = "确认保存";
"duplicate_number_message" = "第三方号码 '%@' 已被以下地址使用：\n\n%@\n\n确定要使用重复的号码吗？";

// 排序选项
"sort_order_entry" = "添加顺序";
"sort_order_optimized" = "优化顺序";
"sort_order_speedx" = "SpeedX顺序";
"sort_order_third_party" = "第三方顺序";

// SpeedX序列检查
"speedx_sequence_check" = "SpeedX序列检查";
"missing_stops" = "缺失的停靠点:";
"incomplete_stops" = "不完整的停靠点:";
"stops_not_found" = "停靠点 %@ 未找到";
"stops_truncated" = "停靠点 %@ 信息被截断";
"supplement_photo" = "补充截图:";
"supplement_photo_description" = "上传包含缺失停靠点 %@ 的截图，系统将只处理缺失的号码";
"ai_prediction" = "AI智能预测:";
"ai_prediction_description" = "基于现有地址模式，AI可以预测缺失停靠点的地址";
"analyzing_pattern" = "正在分析地址模式...";
"analysis_failed" = "分析失败: %@";
"cannot_identify_pattern" = "无法识别地址模式";
"cannot_predict_address" = "无法预测缺失地址";
"suggestions" = "建议:";
"try_ai_prediction" = "• 尝试AI智能预测缺失地址";
"retake_photo_include_all" = "• 重新截图确保包含所有停靠点";
"check_missing_info" = "• 确保图片没有遗漏相关信息";
"process_in_batches" = "• 可以分段截图然后合并处理";
"supplement_photo_recommended" = "补充截图 (推荐)";
"ai_predict_address" = "AI智能预测地址";
"retake_photo_restart" = "重新截图 (全部重新开始)";
"retake_photo" = "重新截图";
"continue_use" = "继续使用";
"and_more_stops" = "%@ 等%d个";

// 详细缺失分析
"detailed_missing_analysis" = "🔍 详细缺失分析：";
"consecutive_missing_range" = "📍 连续缺失区间：";
"scattered_missing_points" = "📍 零散缺失点：";
"missing_statistics" = "📊 缺失统计：";
"total_missing" = "总缺失：%d个停靠点";
"coverage_rate" = "覆盖率：%.1f%% (%d/%d)";
"risk_level_low" = "风险等级：低 ✅";
"risk_level_medium" = "风险等级：中等 ⚠️";
"risk_level_high" = "风险等级：高 ❌";
"consecutive_stops_impact" = "%d个连续停靠点";
"area_coverage_impact" = "影响：可能整个区域未覆盖";
"start_area" = "起始区域";
"middle_area" = "中段区域";
"end_area" = "末尾区域";

// 推荐操作
"recommended_actions_priority" = "🎯 推荐操作（按优先级）：";
"action_ai_prediction" = "1️⃣ 【AI智能预测】- 最快解决方案";
"action_targeted_retake" = "2️⃣ 【定向补拍】- 精准解决方案";
"action_complete_retake" = "3️⃣ 【完整重拍】- 保险方案";
"professional_tips" = "💡 专业提示：";

// AI预测详情
"ai_prediction_success_rate" = "成功率：~85%，适用于规律分布";
"ai_prediction_estimate" = "预计补充：%d-%d个地址";
"ai_based_on_patterns" = "基于已识别地址模式预测缺失地址";

// 定向补拍详情
"targeted_retake_focus" = "重点拍摄%@区域（连续缺失）";
"targeted_retake_suggestion" = "建议：放慢滚动，在该区域停留2-3秒";
"targeted_retake_preserve" = "系统将只处理缺失号码，保留现有结果";

// 完整重拍详情
"complete_retake_description" = "重新录制完整视频";
"complete_retake_suggestion" = "建议：每个区域停留1-2秒，确保清晰度";
"complete_retake_risk" = "风险：可能重复工作";

// 专业提示
"consecutive_missing_cause" = "%@连续缺失可能是滚动过快导致";
"try_ai_first_tip" = "建议先尝试AI预测，节省时间";
"supplement_focus_tip" = "如需补拍，重点关注缺失区间";

// 总数验证相关
"total_count_detected" = "检测到总数：%d";
"count_mismatch_warning" = "数量不匹配：预期%d个，实际%d个";
"count_perfect_match" = "数量匹配：识别完整 ✅";
"missing_orders_suggestion" = "可能存在缺失订单，建议检查视频完整性";

// 路线优化相关
"restore_route" = "还原";
"sort_by_third_party" = "第三方";
"save_third_party_sort" = "保存";