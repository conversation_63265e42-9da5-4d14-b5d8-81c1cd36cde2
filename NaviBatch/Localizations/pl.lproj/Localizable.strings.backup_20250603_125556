/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Ustawienia Języka";
"system_language" = "Język Systemu";
"system_language_section" = "Ustawienia Systemu";
"languages" = "Język";
"language_info_title" = "O Ustawieniach Języka";
"language_info_description" = "Po zmianie ustawień języka aplikacja będzie wyświetlać tekst w wybranym języku. Niektóre treści mogą wymagać ponownego uruchomienia aplikacji, aby w pełni zastosować nowe ustawienia języka.";
"restart_required" = "Wymagane Ponowne Uruchomienie Aplikacji";
"restart_app_message" = "Aby w pełni zastosować zmiany języka, uruchom ponownie aplikację.";
"restart_now" = "Uruchom Ponownie Teraz";
"restart_later" = "Uruchom Ponownie Później";

// MARK: - Common UI Elements
"close" = "Zamknij";
"cancel" = "Anuluj";
"save" = "Zapisz";
"edit" = "Edytuj";
"delete" = "Usuń";
"done" = "Gotowe";
"next" = "Dalej";
"back" = "Wstecz";
"confirm" = "Potwierdź";
"error" = "Błąd";
"success" = "Sukces";
"warning" = "Ostrzeżenie";
"loading" = "Ładowanie...";
"search" = "Szukaj";
"settings" = "Ustawienia";
"help" = "Pomoc";
"about" = "O Aplikacji";
"menu" = "Menu";
"understand" = "Rozumiem";

// MARK: - Navigation
"navigation" = "Nawigacja";
"start_navigation" = "Rozpocznij Nawigację";

// MARK: - Subscription
"subscription" = "Subskrypcja";
"upgrade_to_pro" = "Przejdź na Pro";
"upgrade_description" = "Grupowanie nawigacji jednym kliknięciem, 60x szybsze niż operacja ręczna";
"restore_purchases" = "Przywróć Zakupy";
"learn_more" = "Dowiedz Się Więcej";
"upgrade_your_plan" = "Ulepsz Swój Plan";
"one_click_navigation_description" = "Grupowanie adresów nawigacji jednym kliknięciem, oszczędza czas i paliwo";
"current_plan" = "Obecny Plan";
"upgrade" = "Ulepsz";
"maybe_later" = "Później";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Darmowy";
"pro_tier_price" = "$29.99/miesiąc";
"expert_tier_price" = "$249.99/rok";
"free_tier_description" = "Odpowiedni dla osób prywatnych i małych firm, nie wymaga dużej liczby przystanków";
"pro_tier_description" = "Odpowiedni dla użytkowników potrzebujących grupowania nawigacji jednym kliknięciem i nieograniczonej liczby adresów";
"expert_tier_description" = "Tak samo jak Pro, ale oszczędza 31% dzięki planowi rocznemu";
"route_optimization" = "Optymalizacja Trasy";
"unlimited_routes" = "Nieograniczone Trasy";
"unlimited_optimizations" = "Nieograniczone Optymalizacje";
"max_15_addresses" = "Maksymalnie 15 adresów na trasę";
"save_fuel_30" = "Oszczędza do 30% paliwa";
"unlimited_addresses" = "✨ Nieograniczone Adresy ✨";
"one_click_navigation" = "⚡ Grupowanie Nawigacji Jednym Kliknięciem - 60x Szybsze ⚡";
"package_finder" = "Lokalizator Paczek";
"annual_savings" = "Oszczędności Planu Rocznego";
"switched_to_free" = "Przełączono na Plan Darmowy";
"switched_to_subscription" = "Przełączono na Plan Subskrypcji";
"unlimited_stops" = "Nieograniczone Przystanki";
"plan_as_many_stops_as_needed" = "Dodaj dowolną liczbę punktów dostawy bez ograniczeń";

// MARK: - Address Input
"enter_or_search_address" = "Wprowadź lub wyszukaj adres";
"search_results_count" = "Wyniki wyszukiwania: %d";
"no_matching_addresses" = "Nie znaleziono pasujących adresów";
"search_address_failed" = "Wyszukiwanie adresu nie powiodło się: %@";
"address_search_no_response" = "Wyszukiwanie adresu nie odpowiada";
"cannot_get_address_coordinates" = "Nie można uzyskać współrzędnych adresu";
"speech_recognizer_unavailable" = "Rozpoznawanie mowy niedostępne";
"microphone_permission_denied" = "Uprawnienia mikrofonu nie zostały przyznane";
"speech_recognition_permission_denied" = "Uprawnienia rozpoznawania mowy nie zostały przyznane";
"listening" = "Słucham...";
"recording_failed" = "Nie udało się rozpocząć nagrywania: %@";
"cannot_get_coordinates_retry" = "Nie można uzyskać współrzędnych adresu, wprowadź ręcznie lub spróbuj ponownie";
"cannot_create_recognition_request" = "Nie można utworzyć żądania rozpoznawania";

// MARK: - Saved Address Picker
"search_address" = "Szukaj Adresu";
"no_saved_addresses" = "Brak Zapisanych Adresów";
"no_saved_addresses_description" = "Nie zapisałeś jeszcze żadnych adresów lub nie ma adresów spełniających kryteria filtrowania";
"select_address_book" = "Wybierz Książkę Adresową";

// MARK: - Menu
"menu" = "Menu";
"routes" = "Trasa";
"address_book" = "Książka Adresowa";
"saved_routes" = "Zapisane Trasy";
"manage_your_routes" = "Zarządzaj planowaniem tras";
"manage_your_addresses" = "Zarządzaj często używanymi adresami";
"settings" = "Ustawienia";
"preferences" = "Preferencje";
"set_custom_start_point" = "Ustaw Niestandardowy Punkt Startowy";
"current_start_point" = "Obecny punkt startowy: %@";
"support" = "Wsparcie";
"contact_us" = "Skontaktuj się z Nami";
"contact_us_description" = "Masz pytania lub sugestie? Skontaktuj się z nami!";
"help_center" = "Centrum Pomocy";
"subscription" = "Subskrypcja";
"upgrade_to_pro" = "Przejdź na Pro";
"upgrade_description" = "Grupowanie nawigacji jednym kliknięciem, 60x szybsze niż operacja ręczna";
"open_subscription_view" = "Otwórz Widok Subskrypcji Bezpośrednio";
"open_subscription_view_description" = "Pomiń warstwę pośrednią, pokaż SubscriptionView bezpośrednio";
"restore_purchases_failed" = "Przywracanie zakupów nie powiodło się: %@";
"about" = "O Aplikacji";
"rate_us" = "Oceń Nas";
"rate_us_description" = "Twoja opinia jest dla nas ważna i pomaga nam ulepszać aplikację!";
"share_app" = "Udostępnij Aplikację";
"share_app_text" = "Wypróbuj NaviBatch, niesamowitą aplikację do planowania tras!";
"about_app" = "O Aplikacji";
"developer_tools" = "Narzędzia Deweloperskie";
"coordinate_debug_tool" = "Narzędzie Debugowania Współrzędnych";
"batch_fix_addresses" = "Napraw Adresy Wsadowo";
"clear_database" = "Wyczyść Bazę Danych";
"clear_database_confirmation" = "To usunie wszystkie dane, w tym trasy, adresy i grupy. Ta akcja nie może zostać cofnięta. Czy na pewno chcesz kontynuować?";
"confirm_clear" = "Potwierdź Wyczyszczenie";
"version_info" = "Wersja %@ (%@)";
"current_system_language" = "Obecny Język Systemu";
"reset_to_system_language" = "Resetuj do Języka Systemu";
"language" = "Język";
"language_settings" = "Ustawienia Języka";

// MARK: - Address Edit
"address" = "Adres";
"coordinates" = "Współrzędne";
"distance_from_current_location" = "Odległość od Obecnej Lokalizacji";
"address_info" = "Informacje o Adresie";
"update_coordinates" = "Aktualizuj Współrzędne";
"fix_address" = "Napraw Adres";
"prompt" = "Monit";
"confirm" = "Potwierdź";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Zmodyfikuj adres przed aktualizacją współrzędnych";
"coordinates_update_success" = "Współrzędne zaktualizowane pomyślnie";
"coordinates_update_failure" = "Nie udało się zaktualizować współrzędnych";
"save_failure" = "Zapisywanie nie powiodło się: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Brak Zapisanych Adresów";
"no_saved_addresses_message" = "Nie zapisałeś jeszcze żadnych adresów";
"add_new_address" = "Dodaj Nowy Adres";
"address_title" = "Adres";
"add" = "Dodaj";
"refresh" = "Odśwież";
"notes" = "Notatki";
"address_details" = "Szczegóły Adresu";
"favorite" = "Ulubione";
"edit_address" = "Edytuj Adres";
"cancel" = "Anuluj";
"save" = "Zapisz";
"confirm_delete" = "Potwierdź Usunięcie";
"delete" = "Usuń";
"delete_address_confirmation" = "Czy na pewno chcesz usunąć ten adres? Ta operacja nie może zostać cofnięta.";
"edit" = "Edytuj";
"address_marker" = "Adres";
"address_label" = "Adres:";
"notes_label" = "Notatki:";
"created_at_label" = "Czas utworzenia:";
"open_in_maps" = "Otwórz w Mapie";
"copy_address" = "Kopiuj Adres";
"address_details_title" = "Szczegóły Adresu";

// MARK: - Route Detail
"start_end_point" = "Punkt Start/Koniec";
"start_point" = "Punkt Startowy";
"end_point" = "Punkt Końcowy";
"route_info" = "Informacje o Trasie";
"address_count" = "Liczba Adresów";
"address_count_format" = "%d adresów";
"points_count_format" = "%d punktów";
"additional_points_format" = "+%d punktów";
"export_route" = "Eksportuj Trasę";
"navigate" = "Nawiguj";
"address_list" = "Lista Adresów";
"no_addresses" = "Brak Adresów";
"no_addresses_message" = "Ta trasa nie ma jeszcze adresów";

// MARK: - Route Bottom Sheet
"address_point_start" = "Punkt Startowy";
"address_point_stop" = "Punkt Postoju";
"address_point_end" = "Punkt Końcowy";
"route_name" = "Nazwa Trasy";
"save" = "Zapisz";
"new_route" = "Nowa Trasa";
"saved_route" = "Zapisana Trasa";
"edit" = "Edytuj";
"loading" = "Ładowanie...";
"plan_route" = "Planuj Trasę";
"clear_all" = "Wyczyść Wszystko";
"avoid" = "Unikaj:";
"toll_roads" = "Drogi Płatne";
"highways" = "Autostrady";
"processing_addresses" = "Przetwarzanie adresów...";
"same_start_end_point" = "Ustawiłeś ten sam adres jako punkt startowy i końcowy";
"add_start_point" = "Dodaj Punkt Startowy";
"swipe_left_to_delete" = "← Przesuń w lewo, aby usunąć";
"delete" = "Usuń";
"add_new_address" = "Dodaj Nowy Adres";
"add_end_point" = "Dodaj Punkt Końcowy";

// MARK: - Simple Address Sheet
"address_title" = "Adres";
"enter_and_select_address" = "Wprowadź i wybierz adres";
"current_search_text" = "Obecny tekst wyszukiwania: %@";
"search_results_count" = "Wyniki wyszukiwania: %d";
"no_matching_addresses" = "Nie znaleziono pasujących adresów";
"add_address" = "Dodaj Adres";
"edit_address" = "Edytuj Adres";
"selected_coordinates" = "Wybrane Współrzędne";
"company_name_optional" = "Nazwa Firmy (Opcjonalne)";
"url_optional" = "URL (Opcjonalne)";
"favorite_address" = "Ulubiony Adres";
"set_as_start_and_end" = "Ustaw jako Punkt Startowy i Końcowy";
"address_book" = "Książka Adresowa";
"batch_paste" = "Wklej Wsadowo";
"file_import" = "Import Pliku";
"web_download" = "Pobieranie z Internetu";
"cancel" = "Anuluj";
"saving" = "Zapisywanie...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Zarządzanie Punktami Dostawy";
"information_category" = "Kategoria Informacji";
"address_info" = "Informacje o Adresie";
"package_info" = "Informacje o Paczce";
"vehicle_position" = "Pozycja Pojazdu";
"delivery_info" = "Informacje o Dostawie";
"navigation" = "Nawigacja";
"take_photo_record" = "Zrób Zapis Fotograficzny";
"update_status" = "Aktualizuj Status";
"done" = "Gotowe";
"edit_address_button" = "Edytuj Adres";
"coordinates" = "Współrzędne";
"access_instructions" = "Instrukcje Dostępu";
"add_access_instructions" = "Dodaj instrukcje dostępu...";
"package_count" = "Liczba Paczek";
"package_size" = "Rozmiar Paczki";
"package_type" = "Typ Paczki";
"mark_as_important" = "Oznacz jako Ważne";
"select_package_position" = "Wybierz Pozycję Paczki w Pojeździe";
"vehicle_area" = "Obszar Pojazdu";
"left_right_position" = "Pozycja Lewa/Prawa";
"vehicle_position_front" = "Przód";
"vehicle_position_middle" = "Środek";
"vehicle_position_back" = "Tył";
"vehicle_position_left" = "Lewo";
"vehicle_position_right" = "Prawo";
"vehicle_position_floor" = "Dół";
"vehicle_position_shelf" = "Góra";
"height_position" = "Pozycja Wysokości";
"delivery_type" = "Typ Dostawy";
"delivery_status" = "Status Dostawy";
"order_info" = "Informacje o Zamówieniu";
"order_information" = "Informacje o Zamówieniu";
"order_number" = "Numer Zamówienia";
"enter_order_number" = "Wprowadź numer zamówienia";
"tracking_number" = "Numer Śledzenia";
"enter_tracking_number" = "Wprowadź numer śledzenia";
"time_info" = "Informacje o Czasie";
"time_information" = "Informacje o Czasie";
"estimated_arrival_time" = "Szacowany Czas Przybycia";
"anytime" = "Kiedykolwiek";
"stop_time" = "Czas Postoju";
"minutes_format" = "%d minut";
"photo_record" = "Zapis Fotograficzny";
"door_number_photo" = "Zdjęcie Numeru Drzwi";
"package_label_photo" = "Zdjęcie Etykiety Paczki";
"placement_photo" = "Zdjęcie Umieszczenia";
"door_number_desc" = "Zrób wyraźne zdjęcie numeru domu lub ulicy, upewnij się, że cyfry/litery są widoczne";
"package_label_desc" = "Zrób zdjęcie etykiety paczki, upewnij się, że informacje o odbiorcy są wyraźnie widoczne";
"placement_desc" = "Zrób zdjęcie ostatecznego miejsca umieszczenia paczki";
"photo_captured" = "Zdjęcie zostało zrobione";
"photo_captured_options" = "Zdjęcie zostało zrobione, czy chcesz kontynuować z następnym zdjęciem czy zakończyć obecne zdjęcie?";
"continue_to_next_photo" = "Kontynuuj z następnym - %@";
"retake" = "Zrób Ponownie";
"tap_to_capture" = "Dotknij, aby Zrobić Zdjęcie";
"flash_auto" = "Auto Lampa";
"flash_on" = "Włącz Lampę";
"flash_off" = "Wyłącz Lampę";
"photo_record_completed" = "Zapis Fotograficzny Ukończony";
"photo_confirmation" = "Potwierdzenie Zdjęcia";
"error" = "Błąd";
"ok" = "OK";
"complete_photo_capture" = "Ukończ Robienie Zdjęć";
"tap_to_capture" = "Dotknij, aby Zrobić Zdjęcie";
"photo_instructions" = "Dotknij każdą kartę zdjęcia, aby zrobić zdjęcie. Wszystkie zdjęcia muszą być ukończone.";
"photo_options" = "Opcje Zdjęć";
"view_photo" = "Zobacz Zdjęcie";
"retake_photo" = "Zrób Ponownie";
"saving_photos" = "Zapisywanie zdjęć...";
"completed" = "Ukończone";
"not_taken" = "Nie Zrobione";
"route_options" = "Opcje Trasy";
"avoid_tolls" = "Unikaj Dróg Płatnych";
"avoid_highways" = "Unikaj Autostrad";
"optimize_route" = "Optymalizuj Trasę";
"optimizing" = "Optymalizowanie...";
"optimization_complete" = "Optymalizacja Ukończona";

// MARK: - Addresses
"addresses" = "Adres";
"add_address" = "Dodaj Adres";
"edit_address" = "Edytuj Adres";
"delete_address" = "删除地址";
"address_details" = "Szczegóły Adresu";
"street" = "Ulica";
"city" = "Miasto";
"state" = "Województwo";
"country" = "Kraj";
"postal_code" = "Kod Pocztowy";
"phone" = "Telefon";
"email" = "E-mail";
"website" = "Strona Internetowa";
"company" = "Firma";
"notes" = "Notatki";
"coordinates" = "Współrzędne";
"latitude" = "Szerokość Geograficzna";
"longitude" = "Długość Geograficzna";
"geocoding_error" = "Błąd Geokodowania";
"address_validation" = "Weryfikacja Adresu";
"invalid_addresses" = "Nieprawidłowy Adres";
"fix_addresses" = "Napraw Adres";

// MARK: - Routes
"route" = "Trasa";
"routes" = "Trasa";
"select_address_point" = "Wybierz Punkt Adresowy";
"select_delivery_points" = "Wybierz Punkt Dostawy";
"create_delivery_route" = "Utwórz Trasę Dostawy";
"view_saved_routes" = "Zobacz Zapisane Trasy";
"create_route" = "Utwórz Trasę";
"edit_route" = "Edytuj Trasę";
"delete_route" = "Usuń Trasę";
"route_name" = "Nazwa Trasy";
"route_details" = "Szczegóły Trasy";
"selected_addresses" = "Wybrano %d adresów";
"reached_limit" = "Osiągnięto Limit";
"can_select_more" = "Można wybrać jeszcze %d";
"navigate_button" = "Nawigacja";
"create_group" = "Utwórz Grupę";
"start_point" = "Punkt Startowy";
"end_point" = "Punkt Końcowy";
"waypoints" = "Punkty Pośrednie";
"total_distance" = "Całkowita Odległość";
"estimated_time" = "Szacowany Czas";
"route_summary" = "Podsumowanie Trasy";
"route_options" = "Opcje Trasy";
"route_saved" = "Trasa Zapisana";
"route_optimized" = "Trasa Zoptymalizowana";
"optimizing_route" = "Optymalizowanie Trasy...";
"completed_percent" = "Ukończono %d%%";
"processing_points" = "Przetwarzanie: %d/%d";
"estimated_remaining_time" = "Szacowany Pozostały Czas: %@";

// MARK: - Delivery
"delivery" = "Dostawa";
"delivery_confirmation" = "Potwierdzenie Dostawy";
"take_photo" = "Zrób Zdjęcie";
"signature" = "Podpis";
"delivery_notes" = "Notatki Dostawy";
"delivery_status" = "Status Dostawy";
"delivered" = "Dostarczone";
"not_delivered" = "Nie Dostarczone";
"delivery_time" = "Czas Dostawy";
"delivery_date" = "Data Dostawy";
"package_details" = "Szczegóły Paczki";
"package_id" = "ID Paczki";
"package_weight" = "Waga Paczki";
"package_dimensions" = "Wymiary Paczki";
"recipient_name" = "Imię Odbiorcy";
"recipient_phone" = "Telefon Odbiorcy";

// MARK: - Groups
"groups" = "Grupa";
"saved_groups" = "Zapisane Grupy";
"create_group" = "Utwórz Grupę";
"edit_group" = "Edytuj Grupę";
"delete_group" = "Usuń Grupę";
"group_name" = "Nazwa Grupy";
"group_details" = "Szczegóły Grupy";
"auto_grouping" = "Auto Grupowanie";
"group_by" = "Grupuj Według";
"add_to_group" = "Dodaj do Grupy";
"remove_from_group" = "Usuń z Grupy";
"group_created" = "Grupa Utworzona";
"default_group_name_format" = "Grupa%d";
"auto_grouping_completed" = "Auto Grupowanie Ukończone";
"auto_grouping_in_progress" = "Auto Grupowanie w Toku...";
"create_group_every_14_addresses" = "Utwórz grupę dla każdych 14 adresów";
"create_delivery_group" = "Utwórz Grupę Dostawy";
"enter_group_name" = "Wprowadź nazwę grupy";
"selected_delivery_points" = "Wybrane punkty dostawy";
"drag_to_adjust_order" = "Przeciągnij, aby dostosować kolejność";

// MARK: - Subscription
"subscription" = "Subskrypcja";
"free_plan" = "Wersja Darmowa";
"pro_plan" = "Wersja Pro";
"expert_plan" = "Wersja Expert";
"monthly" = "Plan Miesięczny";
"yearly" = "Plan Roczny";
"subscribe" = "Subskrypcja";
"upgrade" = "Ulepsz";
"upgrade_to_pro" = "Przejdź na Pro";
"manage_subscription" = "Zarządzaj Subskrypcją";
"restore_purchases" = "Przywróć Zakupy";
"subscription_benefits" = "Korzyści Subskrypcji";
"free_trial" = "Darmowa Próba";
"price_per_month" = "%@ miesięcznie";
"price_per_year" = "%@ rocznie";
"save_percent" = "Oszczędź %@%";
"current_plan" = "Obecny Plan";
"subscription_terms" = "Warunki Subskrypcji";
"privacy_policy" = "Polityka Prywatności";
"terms_of_service" = "Warunki Usługi";

// MARK: - Import/Export
"import" = "Import";
"export" = "Eksport";
"import_addresses" = "Importuj Adresy";
"export_addresses" = "Eksportuj Adresy";
"import_from_file" = "Importuj z Pliku";
"export_to_file" = "Eksportuj do Pliku";
"file_format" = "Format Pliku";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Pomyślnie zaimportowano %d adresów, wszystkie z prawidłowymi współrzędnymi.";
"export_success" = "Eksport Pomyślny";
"import_error" = "Błąd Importu";
"export_error" = "Błąd Eksportu";

// MARK: - Navigation
"navigate" = "Nawiguj";

// MARK: - Look Around
"show_look_around" = "Zobacz Widok Ulicy";
"hide_look_around" = "Ukryj Widok Ulicy";

// MARK: - Map
"map" = "Mapa";
"map_type" = "Typ Mapy";
"standard" = "Standardowa";
"satellite" = "Satelitarna";
"hybrid" = "Hybrydowa";
"show_traffic" = "Pokaż Ruch Drogowy";
"current_location" = "Obecna Lokalizacja";
"directions" = "Wskazówki Trasy";
"distance_to" = "Odległość";
"eta" = "Szacowany Czas Przybycia";
"look_around" = "Rozejrzyj się";
"locating_to_glen_waverley" = "Lokalizowanie do Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Błąd Sieci";
"location_error" = "Błąd Lokalizacji";
"permission_denied" = "Uprawnienia Odrzucone";
"location_permission_required" = "Wymagane Uprawnienia Lokalizacji";
"camera_permission_required" = "Wymagane Uprawnienia Kamery";
"photo_library_permission_required" = "Wymagane Uprawnienia Biblioteki Zdjęć";
"please_try_again" = "Spróbuj Ponownie";
"something_went_wrong" = "Wystąpił Błąd";
"invalid_input" = "Nieprawidłowe Dane";
"required_field" = "Pole Wymagane";
"no_internet_connection" = "Brak Połączenia z Internetem";
"server_error" = "Błąd Serwera";
"timeout_error" = "Przekroczono Limit Czasu Żądania";
"data_not_found" = "Nie Znaleziono Danych";
"selection_limit_reached" = "Osiągnięto Limit Wyboru";
"selection_limit_description" = "Możesz wybrać maksymalnie %d adresów, wybrałeś %d";

// MARK: - Location Validation Status
"location_status_valid" = "Prawidłowy Zakres";
"location_status_warning" = "Zakres Ostrzeżenia";
"location_status_invalid" = "Nieprawidłowa Lokalizacja";
"location_status_unknown" = "Nieznany Status";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Nieprawidłowe: Współrzędne zerowe (0,0)";
"coordinates_invalid_nan" = "Nieprawidłowe: Współrzędne nienumeryczne";
"coordinates_out_of_range" = "Nieprawidłowe: Współrzędne poza prawidłowym zakresem";
"coordinates_far_from_user" = "Ostrzeżenie: Lokalizacja daleko od obecnej pozycji";
"coordinates_ocean" = "Ostrzeżenie: Lokalizacja może być w oceanie lub obszarze bezludnym";

// MARK: - Batch Address Input
"batch_add_addresses" = "Dodaj Adresy Wsadowo";
"batch_address_input_placeholder" = "Wprowadź lub wklej adresy, jeden adres na linię. Maksymalnie 35 adresów.";
"free_address_limit" = "Limit Adresów Wersji Darmowej";
"address_count_limit" = "Limit Liczby Adresów";
"free_version_max_addresses" = "Wersja darmowa pozwala na maksymalnie %d adresów.";
"current_addresses_remaining" = "Obecnie masz %d adresów, możesz dodać jeszcze %d adresów.";
"current_route_address_limit" = "Obecna trasa ma %d adresów, możesz dodać jeszcze %d adresów, łącznie maksymalnie %d adresów.";
"selected_addresses_can_import" = "Wybrałeś %d adresów, można zaimportować te adresy.";
"selected_addresses_exceeds" = "Wybrałeś %d adresów, przekraczając dozwoloną liczbę o %d.";
"selected_addresses_all_importable" = "Wybrałeś %d adresów, wszystkie można zaimportować.";
"upgrade_for_unlimited_addresses" = "Przejdź na wersję premium dla nieograniczonej liczby adresów!";
"import_first_n_addresses" = "Importuj tylko pierwsze %d";
"import_all_addresses" = "Importuj wszystkie adresy";
"import_selected_addresses" = "Importuj wybrane adresy";
"no_importable_addresses" = "Brak adresów do importu, sprawdź limity adresów";
"please_enter_valid_address" = "Wprowadź co najmniej jeden prawidłowy adres";

// MARK: - File Import
"import_success" = "Pomyślnie zaimportowano %d adresów, wszystkie z prawidłowymi współrzędnymi.";
"import_success_with_warnings" = "Pomyślnie zaimportowano %d adresów, %d adresów z prawidłowymi współrzędnymi, %d adresów z ostrzeżeniami.\n\nAdresy z ostrzeżeniami zostały oznaczone i można je naprawić ręcznie po imporcie.";

// MARK: - Web Download
"web_download" = "Pobieranie z Internetu";
"supported_formats" = "Obsługiwane Formaty";
"supported_format_csv" = "• Pliki CSV: Kolumna adresów powinna zawierać pełne adresy";
"supported_format_json" = "• Dane JSON: Tablica zawierająca pola adresów";
"supported_format_text" = "• Zwykły tekst: Jeden adres na linię";
"download_history" = "Historia Pobierania";
"upgrade_to_premium" = "Przejdź na Premium";
"input_address_data_url" = "Wprowadź URL danych adresowych";
"import_result" = "Wynik Importu";
"import_addresses" = "Importuj Adresy";
"downloading" = "Pobieranie...";
"processing_data" = "Przetwarzanie danych...";
"google_drive_download_failed" = "Pobieranie z Google Drive nie powiodło się";
"second_attempt_invalid_data" = "Druga próba pobierania zwróciła nieprawidłowe dane";
"cannot_parse_json" = "Nie można przeanalizować danych JSON, sprawdź format pliku";
"cannot_parse_json_with_error" = "Nie można przeanalizować danych JSON: %@";
"cannot_get_address_coordinates" = "Nie można uzyskać współrzędnych adresu";
"cannot_read_file" = "Nie można odczytać pliku: %@";
"success" = "Sukces";
"warning" = "Ostrzeżenie";
"failed" = "Dostawa Nieudana";
"no_matching_addresses" = "Nie znaleziono pasujących adresów";
"no_valid_addresses" = "Nie znaleziono prawidłowych adresów";
"confirm" = "Potwierdź";
"processing_addresses" = "Przetwarzanie adresów...";
"supports_file_types" = "Obsługuje pliki CSV, TXT i JSON";
"tap_to_select_file" = "Dotknij, aby wybrać plik";
"import_addresses" = "Importuj Adresy";
"company_name_optional" = "Nazwa Firmy (Opcjonalne)";
"input_company_name" = "Wprowadź nazwę firmy (opcjonalne)";
"imported_addresses_count" = "Zaimportowano %d adresów";
"select_all" = "Zaznacz Wszystkie";
"excel_format_not_supported" = "Format Excel nie jest obsługiwany";
"no_matching_addresses" = "Nie znaleziono pasujących adresów";

// MARK: - Import Limits
"import_failed" = "Import Nieudany";
"no_importable_addresses" = "Brak adresów do importu, sprawdź limity adresów";
"free_version_address_limit" = "Wersja darmowa pozwala na maksymalnie %d adresów.";
"current_address_count" = "Obecnie masz %d adresów, możesz dodać jeszcze %d adresów.";
"can_import_selected" = "Wybrałeś %d adresów, można zaimportować te adresy.";
"selected_exceeds_limit" = "Wybrałeś %d adresów, przekraczając dozwoloną liczbę o %d.";
"upgrade_to_premium_unlimited" = "Przejdź na wersję premium dla nieograniczonej liczby adresów!";
"route_address_limit" = "Obecna trasa ma %d adresów, możesz dodać jeszcze %d adresów, łącznie maksymalnie %d adresów.";
"free_version_limit" = "Limit Adresów Wersji Darmowej";
"address_count_limit" = "Limit Liczby Adresów";
"import_selected_addresses" = "Importuj wybrane adresy";
"import_first_n" = "Importuj tylko pierwsze %d";
"import_all_n" = "Importuj wszystkie %d";
"cannot_import" = "Nie można zaimportować";
"select_at_least_one" = "Wybierz co najmniej jeden adres";

// MARK: - Import Results
"no_valid_addresses_found" = "Nie znaleziono prawidłowych adresów";
"import_success_all_valid" = "Pomyślnie zaimportowano %d adresów, wszystkie współrzędne adresów są prawidłowe.";
"import_success_some_warnings" = "Pomyślnie zaimportowano %d adresów, %d adresów z prawidłowymi współrzędnymi, %d adresów nie może uzyskać współrzędnych.";

// MARK: - Warnings
"invalid_csv_row" = "Nieprawidłowy wiersz CSV";
"distance_warning" = "Odległość przekracza 200km od obecnej lokalizacji";
"not_in_australia" = "Współrzędne nie są w zakresie Australii";
"cannot_get_coordinates" = "Nie można uzyskać współrzędnych adresu";
"empty_address" = "Pusty adres";
"invalid_address_data" = "Nieprawidłowe dane adresu";

// MARK: - Saved Groups
"saved_groups" = "Zapisane Grupy";
"no_saved_groups" = "Brak zapisanych grup";
"select_points_create_groups" = "Wybierz punkty dostawy i utwórz grupy dla łatwego zarządzania";
"group_name" = "Nazwa Grupy";
"group_details" = "Szczegóły Grupy";
"navigate_to_these_points" = "Nawiguj do tych punktów";
"confirm_remove_address" = "Czy na pewno chcesz usunąć adres \"%@\" z grupy?";
"confirm_remove_this_address" = "Czy na pewno chcesz usunąć ten adres z grupy?";
"addresses_count" = "%d adresów";
"no_saved_routes" = "Brak zapisanych tras";
"no_saved_routes_description" = "Nie zapisałeś jeszcze żadnych tras";
"all_routes" = "Wszystkie Trasy";
"address_count_format_simple" = "%d adresów";
"delete_all_routes" = "Usuń Wszystkie Trasy";
"navigate_to_all_points" = "Nawiguj do wszystkich punktów";
"confirm_navigate_to_route" = "Czy na pewno chcesz nawigować do wszystkich punktów w trasie \"%@\"?";
"temp_navigation_group" = "Tymczasowa Grupa Nawigacyjna";

// MARK: - Route Management
"route_management" = "Zarządzanie Trasami";
"route_info" = "Informacje o Trasie";
"route_name" = "Nazwa Trasy";
"route_addresses" = "Adresy Trasy";
"no_addresses_in_route" = "Ta trasa nie ma adresów";
"must_keep_one_route" = "Musisz zachować co najmniej jedną trasę";
"confirm_delete_route" = "Czy na pewno chcesz usunąć trasę \"%@\"? Ta operacja nie może zostać cofnięta.";
"confirm_delete_all_routes" = "Potwierdź Usunięcie Wszystkich Tras";
"confirm_delete_all_routes_message" = "Czy na pewno chcesz usunąć wszystkie trasy? Ta operacja nie może zostać cofnięta.";
"delete_all" = "Usuń Wszystkie";

// MARK: - Navigation Buttons
"navigate" = "Nawiguj";

// MARK: - GroupDetailView
"points_count_format" = "%d punktów";

// MARK: - DeliveryPointDetailView
"address_information" = "Informacje o Adresie";
"group_belonging" = "Grupa Przynależności";
"view_map" = "Zobacz Mapę";
"delivery_status" = "Status Dostawy";
"notes" = "Notatki";
"delete_delivery_point" = "Usuń Punkt Dostawy";
"delivery_point_details" = "Szczegóły Punktu Dostawy";
"confirm_deletion" = "Potwierdź Usunięcie";
"delete_delivery_point_confirmation" = "Czy na pewno chcesz usunąć ten punkt dostawy? Ta operacja nie może zostać cofnięta.";

// MARK: - Delivery Photos
"delivery_photos" = "Zdjęcia Dostawy";
"view_delivery_photos" = "Zobacz Zdjęcia Dostawy";
"no_photos_taken" = "Jeszcze nie zrobiono zdjęć";
"take_photos" = "Zrób Zdjęcia";
"loading_photos" = "Ładowanie zdjęć...";
"photo_not_found" = "Zdjęcie nie znalezione";
"photo_deleted" = "Zdjęcie zostało usunięte";
"door_number_photo" = "Zdjęcie Numeru Drzwi";
"package_label_photo" = "Zdjęcie Etykiety Paczki";
"placement_photo" = "Zdjęcie Umieszczenia";
"share_photos" = "Udostępnij Zdjęcie";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Potwierdzenie Zdjęcia";
"door_number_photo_title" = "Zdjęcie Numeru Drogi/Domu";
"package_label_photo_title" = "Zdjęcie Etykiety Paczki";
"placement_photo_title" = "Zdjęcie Lokalizacji Umieszczenia";
"door_number_photo_desc" = "Zrób wyraźne zdjęcie numeru domu, upewnij się, że cyfry/litery są widoczne";
"package_label_photo_desc" = "Zrób zdjęcie etykiety paczki, upewnij się, że informacje o odbiorcy są wyraźnie widoczne";
"placement_photo_desc" = "Zrób zdjęcie ostatecznego miejsca umieszczenia paczki";
"swipe_to_switch" = "Przesuń, aby przełączyć typ zdjęcia";
"photos_will_be_saved_to" = "Zdjęcia zostaną zapisane w albumie:";
"complete_photos" = "Ukończ Robienie Zdjęć";
"saving_photos" = "Zapisywanie zdjęć...";
"photo_save_success" = "Zdjęcie zapisane pomyślnie";
"photo_save_failure" = "Zapisywanie zdjęcia nie powiodło się";
"retake_photo" = "Zrób Ponownie";
"no_photos_found" = "Zdjęcie nie znalezione";
"photos_deleted_or_not_taken" = "Zdjęcie mogło zostać usunięte lub jeszcze nie zostało zrobione";
"share_photo" = "Udostępnij Zdjęcie";
"photo_capture_preview" = "Tryb podglądu - symulacja kamery";
"photo_capture_close" = "Zamknij";
"camera_start_failed" = "Uruchomienie kamery nie powiodło się";
"camera_start_failed_retry" = "Nie można uruchomić kamery, spróbuj ponownie";
"camera_init_failed" = "Inicjalizacja kamery nie powiodła się";
"camera_access_failed" = "Nie można uzyskać dostępu do kamery";
"photo_processing_failed" = "Robienie zdjęcia nie powiodło się";
"photo_processing_failed_retry" = "Nie można ukończyć przetwarzania zdjęcia, spróbuj ponownie";
"photo_capture_progress" = "Postęp: %d/%d";
"photo_captured_continue" = "Zdjęcie ukończone, kontynuuj z %@";
"loading_photos" = "Ładowanie zdjęć...";
"cancel" = "Anuluj";

// MARK: - Delivery Status
"pending" = "Oczekuje na Dostawę";
"in_progress" = "W Trakcie Dostawy";
"completed" = "Ukończone";
"failed" = "Dostawa Nieudana";
"update_status" = "Aktualizuj Status";
"select_delivery_status" = "Wybierz Status Dostawy";
"select_failure_reason" = "Wybierz Przyczynę Niepowodzenia";
"delivered" = "Dostarczone";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Klient nie ma w domu";
"failure_reason_wrong_address" = "Błędny adres";
"failure_reason_no_access" = "Nie można dostać się do lokalizacji";
"failure_reason_rejected" = "Paczka została odrzucona";
"failure_reason_other" = "Inny powód";
"enter_custom_reason" = "Wprowadź konkretny powód";
"custom_reason_placeholder" = "Opisz konkretny powód...";
"custom_reason_required" = "Wprowadź konkretny powód";
"failure_reason_required" = "Wybierz przyczynę niepowodzenia";

// MARK: - Address Validation
"address_validation_failed" = "Weryfikacja adresu nie powiodła się";

