/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Paramètres de langue";
"system_language" = "Langue du système";
"system_language_section" = "Paramètres système";
"languages" = "Langues";
"language_info_title" = "À propos des paramètres de langue";
"language_info_description" = "Après avoir modifié les paramètres de langue, l'application affichera du texte dans la langue sélectionnée. Certains contenus peuvent nécessiter un redémarrage de l'application pour appliquer complètement les nouveaux paramètres de langue.";
"restart_required" = "Redémarrage nécessaire";
"restart_app_message" = "Pour appliquer complètement les changements de langue, veuillez redémarrer l'application.";
"restart_now" = "Redémarrer maintenant";
"restart_later" = "Redémarrer plus tard";

// MARK: - Common UI Elements
"close" = "Fermer";
"cancel" = "Annuler";
"save" = "Enregistrer";
"edit" = "Modifier";
"delete" = "Supprimer";
"done" = "Terminé";
"next" = "Suivant";
"back" = "Retour";
"confirm" = "Confirmer";
"error" = "Erreur";
"success" = "Succès";
"warning" = "Avertissement";
"loading" = "Chargement...";
"search" = "Rechercher";
"settings" = "Paramètres";
"help" = "Aide";
"about" = "À propos";
"menu" = "Menu";
"understand" = "Je comprends";

// MARK: - Navigation
"navigation" = "Navigation";
"start_navigation" = "Commencer la navigation";

// MARK: - Subscription
"subscription" = "Abonnement";
"upgrade_to_pro" = "Passer à Pro";
"upgrade_description" = "Regroupement de navigation en un clic, 60 fois plus rapide que l'opération manuelle";
"restore_purchases" = "Restaurer les achats";
"learn_more" = "En savoir plus";
"upgrade_your_plan" = "Mettez à niveau votre forfait";
"one_click_navigation_description" = "Regroupement de navigation en un clic, économisez du temps et du carburant";
"current_plan" = "Forfait actuel";
"upgrade" = "Mettre à niveau";
"maybe_later" = "Peut-être plus tard";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratuit";
"pro_tier_price" = "29,99$/mois";
"expert_tier_price" = "249,99$/an";
"free_tier_description" = "Parfait pour les particuliers et les petites entreprises avec peu d'arrêts";
"pro_tier_description" = "Pour les utilisateurs qui ont besoin d'un regroupement de navigation en un clic et d'adresses illimitées";
"expert_tier_description" = "Comme Pro, mais économisez 31% avec le forfait annuel";
"route_optimization" = "Optimisation d'itinéraire";
"unlimited_routes" = "Itinéraires illimités";
"unlimited_optimizations" = "Optimisations illimitées";
"max_15_addresses" = "Max. 15 adresses par itinéraire";
"save_fuel_30" = "Économisez jusqu'à 30% de carburant";
"unlimited_addresses" = "✨ Adresses illimitées ✨";
"one_click_navigation" = "⚡ Regroupement de navigation en un clic - 60 fois plus rapide ⚡";
"package_finder" = "Localisateur de colis";
"annual_savings" = "Économies annuelles";
"switched_to_free" = "Passage au forfait gratuit";
"switched_to_subscription" = "Passage au forfait d'abonnement";
"unlimited_stops" = "Arrêts illimités";
"plan_as_many_stops_as_needed" = "Ajoutez autant de points de livraison que nécessaire sans restrictions";

// MARK: - Address Input
"enter_or_search_address" = "Entrez ou recherchez une adresse";
"search_results_count" = "Résultats de recherche : %d";
"no_matching_addresses" = "Aucune adresse correspondante trouvée";
"search_address_failed" = "Échec de la recherche d'adresse : %@";
"address_search_no_response" = "Aucune réponse lors de la recherche d'adresse";
"cannot_get_address_coordinates" = "Impossible d'obtenir les coordonnées de l'adresse";
"speech_recognizer_unavailable" = "Reconnaissance vocale non disponible";
"microphone_permission_denied" = "Permission du microphone refusée";
"speech_recognition_permission_denied" = "Permission de reconnaissance vocale refusée";
"listening" = "Écoute en cours...";
"recording_failed" = "Échec de l'enregistrement : %@";
"cannot_get_coordinates_retry" = "Impossible d'obtenir les coordonnées de l'adresse, veuillez entrer manuellement ou réessayer";
"cannot_create_recognition_request" = "Impossible de créer une demande de reconnaissance";

// MARK: - Saved Address Picker
"search_address" = "Rechercher une adresse";
"no_saved_addresses" = "Aucune adresse enregistrée";
"no_saved_addresses_description" = "Vous n'avez pas encore enregistré d'adresses, ou il n'y a pas d'adresses correspondant à vos critères de filtrage";
"select_address_book" = "Sélectionner le carnet d'adresses";

// MARK: - Menu
"menu" = "Menu";
"routes" = "Itinéraires";
"address_book" = "Carnet d'adresses";
"saved_routes" = "Itinéraires enregistrés";
"manage_your_routes" = "Gérez votre planification d'itinéraire";
"manage_your_addresses" = "Gérez vos adresses fréquemment utilisées";
"settings" = "Paramètres";
"preferences" = "Préférences";
"set_custom_start_point" = "Définir un point de départ personnalisé";
"current_start_point" = "Point de départ actuel : %@";
"support" = "Support";
"contact_us" = "Nous contacter";
"contact_us_description" = "Vous avez des questions ou des suggestions ? N'hésitez pas à nous contacter !";
"help_center" = "Centre d'aide";
"subscription" = "Abonnement";
"upgrade_to_pro" = "Passer à Pro";
"upgrade_description" = "Regroupement de navigation en un clic, 60 fois plus rapide que l'opération manuelle";
"open_subscription_view" = "Ouvrir la vue d'abonnement";
"open_subscription_view_description" = "Ignorer la couche intermédiaire, afficher directement SubscriptionView";
"restore_purchases_failed" = "Échec de la restauration des achats : %@";
"about" = "À propos";
"rate_us" = "Évaluez-nous";
"rate_us_description" = "Votre avis est important pour nous et nous aide à améliorer l'application !";
"share_app" = "Partager l'application";
"share_app_text" = "Essayez NaviBatch, une application de planification d'itinéraire incroyable !";
"about_app" = "À propos de l'application";
"developer_tools" = "Outils de développement";
"coordinate_debug_tool" = "Outil de débogage de coordonnées";
"batch_fix_addresses" = "Correction d'adresses par lot";
"clear_database" = "Effacer la base de données";
"clear_database_confirmation" = "Cela supprimera toutes les données, y compris les itinéraires, les adresses et les groupes. Cette action ne peut pas être annulée. Êtes-vous sûr de vouloir continuer ?";
"confirm_clear" = "Confirmer l'effacement";
"version_info" = "Version %@ (%@)";
"current_system_language" = "Langue système actuelle";
"reset_to_system_language" = "Réinitialiser à la langue du système";
"language" = "Langue";
"language_settings" = "Paramètres de langue";

// MARK: - Address Edit
"address" = "Adresse";
"coordinates" = "Coordonnées";
"distance_from_current_location" = "Distance depuis l'emplacement actuel";
"address_info" = "Informations sur l'adresse";
"update_coordinates" = "Mettre à jour les coordonnées";
"fix_address" = "Corriger l'adresse";
"prompt" = "Invite";
"confirm" = "Confirmer";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Veuillez modifier l'adresse avant de mettre à jour les coordonnées";
"coordinates_update_success" = "Coordonnées mises à jour avec succès";
"coordinates_update_failure" = "Échec de la mise à jour des coordonnées";
"save_failure" = "Échec de l'enregistrement : %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Aucune adresse enregistrée";
"no_saved_addresses_message" = "Vous n'avez pas encore enregistré d'adresses";
"add_new_address" = "Ajouter une nouvelle adresse";
"address_title" = "Adresse";
"add" = "Ajouter";
"refresh" = "Actualiser";
"notes" = "Notes";
"address_details" = "Détails de l'adresse";
"favorite" = "Favori";
"edit_address" = "Modifier l'adresse";
"cancel" = "Annuler";
"save" = "Enregistrer";
"confirm_delete" = "Confirmer la suppression";
"delete" = "Supprimer";
"delete_address_confirmation" = "Êtes-vous sûr de vouloir supprimer cette adresse ? Cette action ne peut pas être annulée.";
"edit" = "Modifier";
"address_marker" = "Adresse";
"address_label" = "Adresse :";
"notes_label" = "Notes :";
"created_at_label" = "Créé le :";
"open_in_maps" = "Ouvrir dans Plans";
"copy_address" = "Copier l'adresse";
"address_details_title" = "Détails de l'adresse";

// MARK: - Route Detail
"start_end_point" = "Point de départ/arrivée";
"start_point" = "Point de départ";
"end_point" = "Point d'arrivée";
"route_info" = "Informations sur l'itinéraire";
"address_count" = "Nombre d'adresses";
"address_count_format" = "%d adresses";
"points_count_format" = "%d points";
"additional_points_format" = "+%d points";
"export_route" = "Exporter l'itinéraire";
"navigate" = "Naviguer";
"address_list" = "Liste d'adresses";
"no_addresses" = "Aucune adresse";
"no_addresses_message" = "Cet itinéraire n'a pas encore d'adresses";

// MARK: - Route Bottom Sheet
"address_point_start" = "Point de départ";
"address_point_stop" = "Point d'arrêt";
"address_point_end" = "Point d'arrivée";
"route_name" = "Nom de l'itinéraire";
"save" = "Enregistrer";
"new_route" = "Nouvel itinéraire";
"saved_route" = "Itinéraire enregistré";
"edit" = "Modifier";
"loading" = "Chargement...";
"plan_route" = "Planifier l'itinéraire";
"clear_all" = "Tout effacer";
"avoid" = "Éviter :";
"toll_roads" = "Routes à péage";
"highways" = "Autoroutes";
"processing_addresses" = "Traitement des adresses...";
"same_start_end_point" = "Vous avez défini la même adresse comme point de départ et d'arrivée";
"add_start_point" = "Ajouter un point de départ";
"swipe_left_to_delete" = "← Glissez vers la gauche pour supprimer";
"delete" = "Supprimer";
"add_new_address" = "Ajouter une nouvelle adresse";
"add_end_point" = "Ajouter un point d'arrivée";

// MARK: - Simple Address Sheet
"address_title" = "Adresse";
"enter_and_select_address" = "Entrez et sélectionnez une adresse";
"current_search_text" = "Texte de recherche actuel : %@";
"search_results_count" = "Résultats de recherche : %d";
"no_matching_addresses" = "Aucune adresse correspondante trouvée";
"add_address" = "Ajouter une adresse";
"edit_address" = "Modifier l'adresse";
"selected_coordinates" = "Coordonnées sélectionnées";
"company_name_optional" = "Nom de l'entreprise (Optionnel)";
"url_optional" = "URL (Optionnel)";
"favorite_address" = "Adresse favorite";
"set_as_start_and_end" = "Définir comme point de départ et d'arrivée";
"address_book" = "Carnet d'adresses";
"batch_paste" = "Collage par lot";
"file_import" = "Importation de fichier";
"web_download" = "Téléchargement web";
"cancel" = "Annuler";
"saving" = "Enregistrement en cours...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Gestion des points de livraison";
"information_category" = "Catégorie d'information";
"address_info" = "Informations sur l'adresse";
"package_info" = "Informations sur le colis";
"vehicle_position" = "Position du véhicule";
"delivery_info" = "Informations de livraison";
"navigation" = "Navigation";
"take_photo_record" = "Prendre une photo";
"update_status" = "Mettre à jour le statut";
"done" = "Terminé";
"edit_address_button" = "Modifier l'adresse";
"coordinates" = "Coordonnées";
"access_instructions" = "Instructions d'accès";
"add_access_instructions" = "Ajouter des instructions d'accès...";
"package_count" = "Nombre de colis";
"package_size" = "Taille du colis";
"package_type" = "Type de colis";
"mark_as_important" = "Marquer comme important";
"select_package_position" = "Sélectionner la position du colis dans le véhicule";
"vehicle_area" = "Zone du véhicule";
"left_right_position" = "Position gauche/droite";
"vehicle_position_front" = "Avant";
"vehicle_position_middle" = "Milieu";
"vehicle_position_back" = "Arrière";
"vehicle_position_left" = "Gauche";
"vehicle_position_right" = "Droite";
"vehicle_position_floor" = "Bas";
"vehicle_position_shelf" = "Haut";
"height_position" = "Position en hauteur";
"delivery_type" = "Type de livraison";
"delivery_status" = "Statut de livraison";
"order_info" = "Informations sur la commande";
"order_information" = "Informations de commande";
"order_number" = "Numéro de commande";
"enter_order_number" = "Entrez le numéro de commande";
"tracking_number" = "Numéro de suivi";
"enter_tracking_number" = "Entrez le numéro de suivi";
"time_info" = "Informations temporelles";
"time_information" = "Informations temporelles";
"estimated_arrival_time" = "Heure d'arrivée estimée";
"anytime" = "N'importe quand";
"stop_time" = "Temps d'arrêt";
"minutes_format" = "%d minutes";
"photo_record" = "Enregistrement photo";
"door_number_photo" = "Photo du numéro de porte";
"package_label_photo" = "Photo de l'étiquette du colis";
"placement_photo" = "Photo de placement";
"door_number_desc" = "Veuillez prendre une photo claire du numéro de porte ou de rue, assurez-vous que les chiffres/lettres sont visibles";
"package_label_desc" = "Veuillez photographier l'étiquette du colis, assurez-vous que les informations du destinataire sont clairement visibles";
"placement_desc" = "Veuillez photographier l'emplacement final de placement du colis";
"photo_captured" = "Photo prise";
"photo_captured_options" = "Photo prise, voulez-vous continuer avec la photo suivante ou terminer la photo actuelle ?";
"continue_to_next_photo" = "Continuer avec la photo suivante - %@";
"retake" = "Reprendre la photo";
"tap_to_capture" = "Appuyer pour capturer";
"flash_auto" = "Flash automatique";
"flash_on" = "Flash activé";
"flash_off" = "Flash désactivé";
"photo_record_completed" = "Enregistrement photo terminé";
"photo_confirmation" = "Confirmation de photo";
"error" = "Erreur";
"ok" = "OK";
"complete_photo_capture" = "Terminer les photos";
"tap_to_capture" = "Appuyer pour capturer";
"photo_instructions" = "Appuyez sur chaque carte photo pour capturer. Toutes les photos doivent être terminées.";
"photo_options" = "Options de photo";
"view_photo" = "Voir la photo";
"retake_photo" = "Reprendre la photo";
"saving_photos" = "Enregistrement des photos...";
"completed" = "Terminé";
"not_taken" = "Non prise";
"route_options" = "Options d'itinéraire";
"avoid_tolls" = "Éviter les péages";
"avoid_highways" = "Éviter les autoroutes";
"optimize_route" = "Optimiser l'itinéraire";
"optimizing" = "Optimisation en cours...";
"optimization_complete" = "Optimisation terminée";
"route_optimization_results" = "Résultats d'optimisation d'itinéraire";
"route_planning_options" = "Options de planification d'itinéraire";
"before_optimization" = "Avant optimisation";
"after_optimization" = "Après optimisation";
"auto_group" = "Groupement automatique";
"optimized_route_order" = "Ordre d'itinéraire optimisé";
"apply" = "Appliquer";
"kilometers" = "kilomètres";

// MARK: - Addresses
"addresses" = "Adresses";
"add_address" = "Ajouter une adresse";
"edit_address" = "Modifier l'adresse";
"delete_address" = "Supprimer l'adresse";
"address_details" = "Détails de l'adresse";
"street" = "Rue";
"city" = "Ville";
"state" = "État/Province";
"country" = "Pays";
"postal_code" = "Code postal";
"phone" = "Téléphone";
"email" = "Email";
"website" = "Site web";
"company" = "Entreprise";
"notes" = "Notes";
"coordinates" = "Coordonnées";
"latitude" = "Latitude";
"longitude" = "Longitude";
"geocoding_error" = "Erreur de géocodage";
"address_validation" = "Validation d'adresse";
"invalid_addresses" = "Adresses invalides";
"fix_addresses" = "Corriger les adresses";

// MARK: - Routes
"route" = "Itinéraire";
"routes" = "Itinéraires";
"select_address_point" = "Sélectionner un point d'adresse";
"select_delivery_points" = "Sélectionner des points de livraison";
"create_delivery_route" = "Créer un itinéraire de livraison";
"view_saved_routes" = "Voir les itinéraires enregistrés";
"create_route" = "Créer un itinéraire";
"edit_route" = "Modifier l'itinéraire";
"delete_route" = "Supprimer l'itinéraire";
"route_name" = "Nom de l'itinéraire";
"route_details" = "Détails de l'itinéraire";
"selected_addresses" = "%d adresses sélectionnées";
"reached_limit" = "Limite atteinte";
"can_select_more" = "Peut encore sélectionner %d";
"navigate_button" = "Navigation";
"create_group" = "Créer un groupe";
"start_point" = "Point de départ";
"end_point" = "Point d'arrivée";
"waypoints" = "Points de passage";
"total_distance" = "Distance totale";
"estimated_time" = "Temps estimé";
"route_summary" = "Résumé de l'itinéraire";
"route_options" = "Options d'itinéraire";
"route_saved" = "Itinéraire enregistré";
"route_optimized" = "Itinéraire optimisé";
"optimizing_route" = "Optimisation de l'itinéraire...";
"completed_percent" = "%d%% terminé";
"processing_points" = "Traitement : %d/%d";
"estimated_remaining_time" = "Temps restant estimé : %@";

// MARK: - Delivery
"delivery" = "Livraison";
"delivery_confirmation" = "Confirmation de livraison";
"take_photo" = "Prendre une photo";
"signature" = "Signature";
"delivery_notes" = "Notes de livraison";
"delivery_status" = "Statut de livraison";
"delivered" = "Livré";
"not_delivered" = "Non livré";
"delivery_time" = "Heure de livraison";
"delivery_date" = "Date de livraison";
"package_details" = "Détails du colis";
"package_id" = "ID du colis";
"package_weight" = "Poids du colis";
"package_dimensions" = "Dimensions du colis";
"recipient_name" = "Nom du destinataire";
"recipient_phone" = "Téléphone du destinataire";

// MARK: - Groups
"groups" = "Groupes";
"saved_groups" = "Groupes enregistrés";
"create_group" = "Créer un groupe";
"edit_group" = "Modifier le groupe";
"delete_group" = "Supprimer le groupe";
"group_name" = "Nom du groupe";
"group_details" = "Détails du groupe";
"auto_grouping" = "Regroupement automatique";
"group_by" = "Grouper par";
"add_to_group" = "Ajouter au groupe";
"remove_from_group" = "Retirer du groupe";
"group_created" = "Groupe créé";
"default_group_name_format" = "Groupe %d";
"auto_grouping_completed" = "Regroupement automatique terminé";
"auto_grouping_in_progress" = "Regroupement automatique en cours...";
"create_group_every_14_addresses" = "Créer un groupe pour chaque 14 adresses";
"create_delivery_group" = "Créer un groupe de livraison";
"enter_group_name" = "Entrer le nom du groupe";
"selected_delivery_points" = "Points de livraison sélectionnés";
"drag_to_adjust_order" = "Glisser pour ajuster l'ordre";

// MARK: - Subscription
"subscription" = "Abonnement";
"free_plan" = "Version gratuite";
"pro_plan" = "Version Pro";
"expert_plan" = "Version Expert";
"monthly" = "Mensuel";
"yearly" = "Annuel";
"subscribe" = "S'abonner";
"upgrade" = "Mettre à niveau";
"upgrade_to_pro" = "Passer à Pro";
"manage_subscription" = "Gérer l'abonnement";
"restore_purchases" = "Restaurer les achats";
"subscription_benefits" = "Avantages de l'abonnement";
"free_trial" = "Essai gratuit";
"price_per_month" = "%@ par mois";
"price_per_year" = "%@ par an";
"save_percent" = "Économisez %@%";
"current_plan" = "Forfait actuel";
"subscription_terms" = "Conditions d'abonnement";
"privacy_policy" = "Politique de confidentialité";
"terms_of_service" = "Conditions d'utilisation";

// MARK: - Import/Export
"import" = "Importer";
"export" = "Exporter";
"import_addresses" = "Importer des adresses";
"export_addresses" = "Exporter des adresses";
"import_from_file" = "Importer depuis un fichier";
"export_to_file" = "Exporter vers un fichier";
"file_format" = "Format de fichier";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Importation réussie de %d adresses, toutes avec des coordonnées valides.";
"export_success" = "Exportation réussie";
"import_error" = "Erreur d'importation";
"export_error" = "Erreur d'exportation";

// MARK: - Navigation
"navigate" = "Naviguer";

// MARK: - Look Around
"show_look_around" = "Afficher Look Around";
"hide_look_around" = "Masquer Look Around";

// MARK: - Map
"map" = "Carte";
"map_type" = "Type de carte";
"standard" = "Standard";
"satellite" = "Satellite";
"hybrid" = "Hybride";
"show_traffic" = "Afficher le trafic";
"current_location" = "Emplacement actuel";
"directions" = "Itinéraire";
"distance_to" = "Distance jusqu'à";
"eta" = "Heure d'arrivée estimée";
"look_around" = "Observer les environs";
"locating_to_glen_waverley" = "Localisation vers Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Erreur de réseau";
"location_error" = "Erreur de localisation";
"permission_denied" = "Permission refusée";
"location_permission_required" = "Permission de localisation requise";
"camera_permission_required" = "Permission de caméra requise";
"photo_library_permission_required" = "Permission de photothèque requise";
"please_try_again" = "Veuillez réessayer";
"something_went_wrong" = "Une erreur s'est produite";
"invalid_input" = "Entrée invalide";
"required_field" = "Champ obligatoire";
"no_internet_connection" = "Pas de connexion Internet";
"server_error" = "Erreur de serveur";
"timeout_error" = "Délai d'attente dépassé";
"data_not_found" = "Données non trouvées";
"selection_limit_reached" = "Limite de sélection atteinte";
"selection_limit_description" = "Vous pouvez sélectionner un maximum de %d adresses, vous avez sélectionné %d";

// MARK: - Location Validation Status
"location_status_valid" = "Plage valide";
"location_status_warning" = "Plage d'avertissement";
"location_status_invalid" = "Position invalide";
"location_status_unknown" = "État inconnu";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Invalide : Coordonnées zéro (0,0)";
"coordinates_invalid_nan" = "Invalide : Coordonnées non numériques";
"coordinates_out_of_range" = "Invalide : Coordonnées hors de la plage valide";
"coordinates_far_from_user" = "Avertissement : La position est loin de votre emplacement actuel";
"coordinates_ocean" = "Avertissement : La position peut être dans l'océan ou une zone inhabitée";

// MARK: - Batch Address Input
"batch_add_addresses" = "Ajouter des adresses par lot";
"batch_address_input_placeholder" = "Veuillez entrer ou coller des adresses, une adresse par ligne. Maximum 35 adresses.";
"free_address_limit" = "Limite d'adresses de la version gratuite";
"address_count_limit" = "Limite du nombre d'adresses";
"free_version_max_addresses" = "La version gratuite permet maximum %d adresses.";
"current_addresses_remaining" = "Vous avez actuellement %d adresses, ne pouvez ajouter que %d de plus.";
"current_route_address_limit" = "L'itinéraire actuel a %d adresses, ne peut ajouter que %d de plus, maximum total de %d adresses.";
"selected_addresses_can_import" = "Vous avez sélectionné %d adresses, elles peuvent être importées.";
"selected_addresses_exceeds" = "Vous avez sélectionné %d adresses, dépassant la limite de %d.";
"selected_addresses_all_importable" = "Vous avez sélectionné %d adresses, toutes peuvent être importées.";
"upgrade_for_unlimited_addresses" = "Passez à la version premium pour des adresses illimitées !";
"import_first_n_addresses" = "Importer seulement les %d premiers";
"import_all_addresses" = "Importer toutes les adresses";
"import_selected_addresses" = "Importer les adresses sélectionnées";
"no_importable_addresses" = "Aucune adresse à importer, veuillez vérifier la limite d'adresses";
"please_enter_valid_address" = "Veuillez entrer au moins une adresse valide";

// MARK: - File Import
"import_success" = "Importation réussie de %d adresses, toutes avec des coordonnées valides.";
"import_success_with_warnings" = "Importation réussie de %d adresses, dont %d ont des coordonnées normales, %d ont des avertissements.\n\nLes adresses avec avertissements sont marquées et peuvent être corrigées manuellement après l'importation.";

// MARK: - Web Download
"web_download" = "Téléchargement web";
"supported_formats" = "Formats pris en charge";
"supported_format_csv" = "• Fichiers CSV : La colonne d'adresses doit contenir des adresses complètes";
"supported_format_json" = "• Données JSON : Tableau contenant des champs d'adresses";
"supported_format_text" = "• Texte brut : Une adresse par ligne";
"download_history" = "Historique des téléchargements";
"upgrade_to_premium" = "Passer à la version premium";
"input_address_data_url" = "Entrer l'URL des données d'adresses";
"import_result" = "Résultat d'importation";
"import_addresses" = "Importer des adresses";
"downloading" = "Téléchargement...";
"processing_data" = "Traitement des données...";
"google_drive_download_failed" = "Échec du téléchargement Google Drive";
"second_attempt_invalid_data" = "La deuxième tentative de téléchargement a renvoyé des données invalides";
"cannot_parse_json" = "Impossible d'analyser les données JSON, veuillez vérifier le format du fichier";
"cannot_parse_json_with_error" = "Impossible d'analyser les données JSON : %@";
"cannot_get_address_coordinates" = "Impossible d'obtenir les coordonnées de l'adresse";
"cannot_read_file" = "Impossible de lire le fichier : %@";
"success" = "Succès";
"warning" = "Avertissement";
"failed" = "Échec de livraison";
"no_matching_addresses" = "Aucune adresse correspondante trouvée";
"no_valid_addresses" = "Aucune adresse valide trouvée";
"confirm" = "Confirmer";
"processing_addresses" = "Traitement des adresses...";
"supports_file_types" = "Prend en charge les fichiers CSV, TXT et JSON";
"tap_to_select_file" = "Appuyer pour sélectionner un fichier";
"import_addresses" = "Importer des adresses";
"company_name_optional" = "Nom de l'entreprise (Optionnel)";
"input_company_name" = "Entrer le nom de l'entreprise (optionnel)";
"imported_addresses_count" = "%d adresses importées";
"select_all" = "Tout sélectionner";
"excel_format_not_supported" = "Format Excel non pris en charge";
"no_matching_addresses" = "Aucune adresse correspondante trouvée";

// MARK: - Import Limits
"import_failed" = "Échec d'importation";
"no_importable_addresses" = "Aucune adresse à importer, veuillez vérifier la limite d'adresses";
"free_version_address_limit" = "La version gratuite permet maximum %d adresses.";
"current_address_count" = "Vous avez actuellement %d adresses, ne pouvez ajouter que %d de plus.";
"can_import_selected" = "Vous avez sélectionné %d adresses, elles peuvent être importées.";
"selected_exceeds_limit" = "Vous avez sélectionné %d adresses, dépassant la limite de %d.";
"upgrade_to_premium_unlimited" = "Passez à la version premium pour des adresses illimitées !";
"route_address_limit" = "L'itinéraire actuel a %d adresses, ne peut ajouter que %d de plus, maximum total de %d adresses.";
"free_version_limit" = "Limite d'adresses de la version gratuite";
"address_count_limit" = "Limite du nombre d'adresses";
"import_selected_addresses" = "Importer les adresses sélectionnées";
"import_first_n" = "Importer seulement les %d premiers";
"import_all_n" = "Importer toutes les %d";
"cannot_import" = "Impossible d'importer";
"select_at_least_one" = "Veuillez sélectionner au moins une adresse";

// MARK: - Import Results
"no_valid_addresses_found" = "Aucune adresse valide trouvée";
"import_success_all_valid" = "Importation réussie de %d adresses, toutes les coordonnées d'adresses sont normales.";
"import_success_some_warnings" = "Importation réussie de %d adresses, dont %d ont des coordonnées normales, %d n'ont pas pu obtenir de coordonnées.";

// MARK: - Warnings
"invalid_csv_row" = "Ligne CSV invalide";
"distance_warning" = "Distance de l'emplacement actuel supérieure à 200 km";
"not_in_australia" = "Les coordonnées ne sont pas dans la zone australienne";
"cannot_get_coordinates" = "Impossible d'obtenir les coordonnées de l'adresse";
"empty_address" = "Adresse vide";
"invalid_address_data" = "Données d'adresse invalides";

// MARK: - Saved Groups
"saved_groups" = "Groupes enregistrés";
"no_saved_groups" = "Aucun groupe enregistré";
"select_points_create_groups" = "Sélectionnez des points de livraison et créez des groupes pour faciliter la gestion";
"group_name" = "Nom du groupe";
"group_details" = "Détails du groupe";
"navigate_to_these_points" = "Naviguer vers ces points";
"confirm_remove_address" = "Êtes-vous sûr de vouloir supprimer l'adresse \"%@\" du groupe ?";
"confirm_remove_this_address" = "Êtes-vous sûr de vouloir supprimer cette adresse du groupe ?";
"addresses_count" = "%d adresses";
"no_saved_routes" = "Aucun itinéraire enregistré";
"no_saved_routes_description" = "Vous n'avez encore enregistré aucun itinéraire";
"all_routes" = "Tous les itinéraires";
"address_count_format_simple" = "%d adresses";
"delete_all_routes" = "Supprimer tous les itinéraires";
"navigate_to_all_points" = "Naviguer vers tous les points";
"confirm_navigate_to_route" = "Êtes-vous sûr de vouloir naviguer vers tous les points de l'itinéraire \"%@\" ?";
"temp_navigation_group" = "Groupe de navigation temporaire";

// MARK: - Route Management
"route_management" = "Gestion d'itinéraires";
"route_info" = "Informations sur l'itinéraire";
"route_name" = "Nom de l'itinéraire";
"route_addresses" = "Adresses d'itinéraire";
"no_addresses_in_route" = "Cet itinéraire n'a pas d'adresses";
"must_keep_one_route" = "Doit conserver au moins un itinéraire";
"confirm_delete_route" = "Êtes-vous sûr de vouloir supprimer l'itinéraire \"%@\" ? Cette action ne peut pas être annulée.";
"confirm_delete_all_routes" = "Confirmer la suppression de tous les itinéraires";
"confirm_delete_all_routes_message" = "Êtes-vous sûr de vouloir supprimer tous les itinéraires ? Cette action ne peut pas être annulée.";
"delete_all" = "Supprimer tout";

// MARK: - Navigation Buttons
"navigate" = "Naviguer";

// MARK: - GroupDetailView
"points_count_format" = "%d points";

// MARK: - DeliveryPointDetailView
"address_information" = "Informations d'adresse";
"group_belonging" = "Groupe d'appartenance";
"view_map" = "Voir la carte";
"delivery_status" = "Statut de livraison";
"notes" = "Notes";
"delete_delivery_point" = "Supprimer le point de livraison";
"delivery_point_details" = "Détails du point de livraison";
"confirm_deletion" = "Confirmer la suppression";
"delete_delivery_point_confirmation" = "Êtes-vous sûr de vouloir supprimer ce point de livraison ? Cette action ne peut pas être annulée.";

// MARK: - Delivery Photos
"delivery_photos" = "Photos de livraison";
"view_delivery_photos" = "Voir les photos de livraison";
"no_photos_taken" = "Aucune photo prise encore";
"take_photos" = "Prendre des photos";
"loading_photos" = "Chargement des photos...";
"photo_not_found" = "Photo non trouvée";
"photo_deleted" = "La photo a été supprimée";
"door_number_photo" = "Photo du numéro de porte";
"package_label_photo" = "Photo de l'étiquette du colis";
"placement_photo" = "Photo de placement";
"share_photos" = "Partager la photo";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Confirmation de photo";
"door_number_photo_title" = "Photo de numéro de rue/porte";
"package_label_photo_title" = "Photo d'étiquette de colis";
"placement_photo_title" = "Photo d'emplacement de placement";
"door_number_photo_desc" = "Veuillez prendre une photo claire du numéro de porte, assurez-vous que les chiffres/lettres sont visibles";
"package_label_photo_desc" = "Veuillez photographier l'étiquette du colis, assurez-vous que les informations du destinataire sont clairement visibles";
"placement_photo_desc" = "Veuillez photographier l'emplacement final de placement du colis";
"swipe_to_switch" = "Glisser pour changer le type de photo";
"complete_photos" = "Terminer les photos";
"saving_photos" = "Enregistrement des photos...";
"photo_save_success" = "Photo enregistrée avec succès";
"photo_save_failure" = "Échec de l'enregistrement de la photo";
"retake_photo" = "Reprendre la photo";
"no_photos_found" = "Aucune photo trouvée";
"photos_deleted_or_not_taken" = "Les photos ont peut-être été supprimées ou n'ont pas encore été prises";
"share_photo" = "Partager la photo";
"photo_capture_preview" = "Mode aperçu - Simulation de caméra";
"photo_capture_close" = "Fermer";
"camera_start_failed" = "Échec du démarrage de la caméra";
"camera_start_failed_retry" = "Impossible de démarrer la caméra, veuillez réessayer";
"camera_init_failed" = "Échec de l'initialisation de la caméra";
"camera_access_failed" = "Impossible d'accéder à la caméra";
"photo_processing_failed" = "Échec de la prise de photo";
"photo_processing_failed_retry" = "Impossible de terminer le traitement de la photo, veuillez réessayer";
"photo_capture_progress" = "Progression : %d/%d";
"photo_captured_continue" = "Capture terminée, continuer avec %@";
"loading_photos" = "Chargement des photos...";
"cancel" = "Annuler";

// MARK: - Delivery Status
"pending" = "En attente de livraison";
"in_progress" = "En cours de livraison";
"completed" = "Terminé";
"failed" = "Échec de livraison";
"update_status" = "Mettre à jour le statut";
"select_delivery_status" = "Sélectionner le statut de livraison";
"select_failure_reason" = "Sélectionner la raison de l'échec";
"delivered" = "Livré";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Client absent";
"failure_reason_wrong_address" = "Adresse incorrecte";
"failure_reason_no_access" = "Impossible d'accéder à l'emplacement";
"failure_reason_rejected" = "Colis refusé";
"failure_reason_other" = "Autres raisons";
"enter_custom_reason" = "Entrer la raison spécifique";
"custom_reason_placeholder" = "Veuillez décrire la raison spécifique...";
"custom_reason_required" = "Veuillez entrer la raison spécifique";
"failure_reason_required" = "Veuillez sélectionner la raison de l'échec";

// MARK: - Address Validation
"address_validation_failed" = "Échec de la validation d'adresse";


"0" = "14天免費試用，隨時可取消";
