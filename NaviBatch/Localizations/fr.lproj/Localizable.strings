/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Paramètres de langue";
"system_language" = "Langue du système";
"system_language_section" = "Paramètres système";
"languages" = "Langues";
"language_info_title" = "À propos des paramètres de langue";
"language_info_description" = "Après avoir modifié les paramètres de langue, l'application affichera du texte dans la langue sélectionnée. Certains contenus peuvent nécessiter un redémarrage de l'application pour appliquer complètement les nouveaux paramètres de langue.";
"restart_required" = "Redémarrage nécessaire";
"restart_app_message" = "Pour appliquer complètement les changements de langue, veuillez redémarrer l'application.";
"restart_now" = "Redémarrer maintenant";
"restart_later" = "Redémarrer plus tard";
"close" = "Fermer";
"cancel" = "Annuler";
"save" = "Enregistrer";
"edit" = "Modifier";
"delete" = "Supprimer";
"done" = "Terminé";
"next" = "Suivant";
"back" = "Retour";
"confirm" = "Confirmer";
"error" = "Erreur";
"success" = "Succès";
"warning" = "Avertissement";
"unknown_error" = "Erreur inconnue";
"loading" = "Chargement...";
"search" = "Rechercher";
"settings" = "Paramètres";
"help" = "Aide";
"about" = "À propos";
"menu" = "Menu";
"understand" = "Je comprends";
"navigation" = "Navigation";
"start_navigation" = "Commencer la navigation";
"subscription" = "Abonnement";
"upgrade_to_pro" = "Passer à Pro";
"upgrade_description" = "Regroupement de navigation en un clic, 60 fois plus rapide que l'opération manuelle";
"restore_purchases" = "Restaurer les achats";
"learn_more" = "En savoir plus";
"upgrade_your_plan" = "Mettez à niveau votre forfait";
"one_click_navigation_description" = "Regroupement de navigation en un clic, économisez du temps et du carburant";
"current_plan" = "Forfait actuel";
"upgrade" = "Mettre à niveau";
"maybe_later" = "Peut-être plus tard";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratuit";
"pro_tier_price" = "9,99$/mois";
"expert_tier_price" = "59,99$/an";
"free_tier_description" = "Parfait pour les particuliers et les petites entreprises avec peu d'arrêts";
"pro_tier_description" = "Pour les utilisateurs qui ont besoin d'un regroupement de navigation en un clic et d'adresses illimitées";
"expert_tier_description" = "Comme Pro, mais économisez 50% avec le forfait annuel";
"route_optimization" = "Optimisation d'itinéraire";
"unlimited_routes" = "Itinéraires illimités";
"unlimited_optimizations" = "Optimisations illimitées";
"max_15_addresses" = "Max. 15 adresses par itinéraire";
"save_fuel_30" = "Économisez jusqu'à 30% de carburant";
"unlimited_addresses" = "✨ Adresses illimitées ✨";
"one_click_navigation" = "⚡ Regroupement de navigation en un clic - 60 fois plus rapide ⚡";
"package_finder" = "Localisateur de colis";
"annual_savings" = "Économies annuelles";
"switched_to_free" = "Passage au forfait gratuit";
"switched_to_subscription" = "Passage au forfait d'abonnement";
"unlimited_stops" = "Arrêts illimités";
"plan_as_many_stops_as_needed" = "Ajoutez autant de points de livraison que nécessaire sans restrictions";
"expires_in_days" = "Expire dans  jours";
"trial_expires_in_days" = "L'essai expire dans  jours";
"expired" = "Expiré";
"subscription_expires_on" = "L'abonnement expire le %@";
"subscription_active_until" = "Abonnement actif jusqu'au %@";
"enter_or_search_address" = "Entrez ou recherchez une adresse";
"search_results_count" = "Résultats de recherche : %d";
"no_matching_addresses" = "Aucune adresse correspondante trouvée";
"search_address_failed" = "Échec de la recherche d'adresse : %@";
"address_search_no_response" = "Aucune réponse lors de la recherche d'adresse";
"cannot_get_address_coordinates" = "Impossible d'obtenir les coordonnées de l'adresse";

"cannot_get_coordinates_retry" = "Impossible d'obtenir les coordonnées de l'adresse, veuillez entrer manuellement ou réessayer";

"image_address_recognition" = "Reconnaissance d'adresses par image";
"select_images" = "Sélectionner des images";
"select_multiple_images" = "Sélection multiple prise en charge";
"delivery_app_screenshot_description" = "Sélectionnez votre service de livraison et téléchargez des captures d'écran ou des vidéos.";
"select_delivery_screenshots" = "Sélectionner les captures d'écran d'applications de livraison";
"supported_delivery_apps" = "Prend en charge les captures d'écran de :";
"amazon_flex_imile_etc" = "Amazon Flex • iMile • UberEats • DoorDash • Et plus";
"processing_images" = "Traitement des images...";
"processing_image_progress" = "Traitement de l'image %d sur %d";
"recognizing_text" = "Reconnaissance du texte...";
"geocoding_addresses" = "Obtention des coordonnées d'adresses...";
"recognition_complete" = "Reconnaissance terminée";
"no_text_recognized" = "Aucun texte reconnu";
"no_addresses_found" = "Aucune adresse valide trouvée";
"image_recognition_failed" = "Échec de la reconnaissance d'image";
"image_recognition_error" = "Erreur de reconnaissance d'image : %@";
"text_recognition_failed" = "Échec de la reconnaissance de texte";
"address_parsing_failed" = "Échec de l'analyse d'adresse";
"select_addresses_to_add" = "Sélectionner les adresses à ajouter";
"recognized_addresses" = "Adresses reconnues";
"address_coordinates" = "Coordonnées d'adresse";
"toggle_address_selection" = "Basculer la sélection d'adresse";
"remove_address" = "Supprimer l'adresse";
"confirm_selected_addresses" = "Confirmer les adresses sélectionnées";
"no_addresses_selected" = "Aucune adresse sélectionnée";
"image_processing_cancelled" = "Traitement d'image annulé";
"unsupported_image_format" = "Format d'image non pris en charge";
"image_too_large" = "Fichier image trop volumineux";
"image_recognition_permission_required" = "Autorisation d'accès à la bibliothèque de photos requise";
"ocr_language_detection" = "Détection automatique de la langue";
"improve_image_quality" = "Assurez-vous que l'image est claire et que le texte est visible";
"address_validation_in_progress" = "Validation des adresses...";
"batch_address_import" = "Importation en lot d'adresses";
"validated_addresses_count" = "%d adresses validées";
"addresses_with_issues" = "%d adresses ont des problèmes";
"select_all" = "Tout sélectionner";
"import_selected" = "Importer les sélectionnées";
"validating_addresses" = "Validation des adresses...";
"empty_address" = "Adresse vide";
"invalid_coordinates" = "Coordonnées invalides";
"coordinate_warning" = "Avertissement de coordonnées";
"address_validation_issue" = "Problème de validation d'adresse";
"cannot_get_coordinates" = "Impossible d'obtenir les coordonnées de l'adresse";
"no_importable_addresses" = "Aucune adresse à importer, veuillez vérifier la limite d'adresses";
"free_version_max_addresses" = "La version gratuite permet maximum %d adresses.";
"valid" = "Valide";
"with_issues" = "Avec problèmes";
"low_confidence_address" = "Adresse à faible confiance";
"address_validation_failed" = "Échec de la validation d'adresse";
"current_addresses_remaining" = "Vous avez actuellement %d adresses, ne pouvez ajouter que %d de plus.";
"can_import_selected" = "Vous avez sélectionné %d adresses, elles peuvent être importées.";
"selected_exceeds_limit" = "Vous avez sélectionné %d adresses, dépassant la limite de %d.";
"selected_addresses_all_importable" = "Vous avez sélectionné %d adresses, toutes peuvent être importées.";
"upgrade_for_unlimited_addresses" = "Passez à la version premium pour des adresses illimitées !";
"current_route_address_limit" = "L'itinéraire actuel a %d adresses, ne peut ajouter que %d de plus, maximum total de %d adresses.";
"import_all_addresses" = "Importer toutes les adresses";
"import_first_n" = "Importer seulement les %d premiers";
"import_selected_addresses" = "Importer les adresses sélectionnées";
"upgrade_to_premium" = "Passer à la version premium";
"batch_add_addresses" = "Ajouter des adresses par lot";
"batch_address_input_placeholder" = "Veuillez entrer ou coller des adresses, une adresse par ligne. Maximum 35 adresses.";
"search_address" = "Rechercher une adresse";
"no_saved_addresses" = "Aucune adresse enregistrée";
"no_saved_addresses_description" = "Vous n'avez pas encore enregistré d'adresses, ou il n'y a pas d'adresses correspondant à vos critères de filtrage";
"select_address_book" = "Sélectionner le carnet d'adresses";
"routes" = "Itinéraires";
"address_book" = "Carnet d'adresses";
"saved_routes" = "Itinéraires enregistrés";
"manage_your_routes" = "Gérez votre planification d'itinéraire";
"manage_your_addresses" = "Gérez vos adresses fréquemment utilisées";
"preferences" = "Préférences";
"set_custom_start_point" = "Définir un point de départ personnalisé";
"current_start_point" = "Point de départ actuel : %@";
"support" = "Support";
"contact_us" = "Nous contacter";
"contact_us_description" = "Vous avez des questions ou des suggestions ? N'hésitez pas à nous contacter !";
"help_center" = "Centre d'aide";
"quick_actions" = "Actions rapides";
"main_features" = "Fonctionnalités principales";
"support_help" = "Support et aide";
"customize_app_settings" = "Personnaliser les paramètres de l'application";
"unlock_all_features" = "Débloquer toutes les fonctionnalités";
"get_help_support" = "Obtenir de l'aide et du support";
"app_info_version" = "Informations et version de l'application";
"dev_tools" = "Outils de développement";
"debug_testing_tools" = "Outils de débogage et de test";
"version" = "Version";
"addresses" = "Adresses";
"limited_to_20_addresses" = "Limité à 20 adresses";
"all_premium_features" = "Toutes les fonctionnalités premium";
"open_subscription_view" = "Ouvrir la vue d'abonnement";
"open_subscription_view_description" = "Ignorer la couche intermédiaire, afficher directement SubscriptionView";
"restore_purchases_failed" = "Échec de la restauration des achats : %@";
"rate_us" = "Évaluez-nous";
"rate_us_description" = "Votre avis est important pour nous et nous aide à améliorer l'application !";
"share_app" = "Partager l'application";
"share_app_text" = "Essayez NaviBatch, une application de planification d'itinéraire incroyable !";
"about_app" = "À propos de l'application";
"developer_tools" = "Outils de développement";
"coordinate_debug_tool" = "Outil de débogage de coordonnées";
"batch_fix_addresses" = "Correction d'adresses par lot";
"clear_database" = "Effacer la base de données";
"clear_database_confirmation" = "Cela supprimera toutes les données, y compris les itinéraires, les adresses et les groupes. Cette action ne peut pas être annulée. Êtes-vous sûr de vouloir continuer ?";
"confirm_clear" = "Confirmer";
"version_info" = "Version %@ (%@)";
"current_system_language" = "Langue système actuelle";
"reset_to_system_language" = "Réinitialiser à la langue du système";
"language" = "Langue";
"address" = "Adresse";
"coordinates" = "Coordonnées";
"distance_from_current_location" = "Distance depuis l'emplacement actuel";
"address_info" = "Informations sur l'adresse";
"update_coordinates" = "Mettre à jour les coordonnées";
"fix_address" = "Corriger l'adresse";
"prompt" = "Invite";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Veuillez modifier l'adresse avant de mettre à jour les coordonnées";
"coordinates_update_success" = "Coordonnées mises à jour avec succès";
"coordinates_update_failure" = "Échec de la mise à jour des coordonnées";
"save_failure" = "Échec de l'enregistrement : %@";
"no_saved_addresses_title" = "Aucune adresse enregistrée";
"no_saved_addresses_message" = "Vous n'avez pas encore enregistré d'adresses";
"add_new_address" = "Ajouter une nouvelle adresse";
"address_title" = "Adresse";
"add" = "Ajouter";
"refresh" = "Actualiser";
"notes" = "Notes";
"address_details" = "Détails de l'adresse";
"favorite" = "Favori";
"edit_address" = "Modifier l'adresse";
"confirm_delete" = "Confirmer la suppression";
"delete_address_confirmation" = "Êtes-vous sûr de vouloir supprimer cette adresse ? Cette action ne peut pas être annulée.";
"address_marker" = "Adresse";
"address_label" = "Adresse :";
"notes_label" = "Notes :";
"created_at_label" = "Créé le :";
"open_in_maps" = "Ouvrir dans Plans";
"copy_address" = "Copier l'adresse";
"address_details_title" = "Détails de l'adresse";
"start_end_point" = "Point de départ/arrivée";
"start_point" = "Point de départ";
"end_point" = "Point d'arrivée";
"route_info" = "Informations sur l'itinéraire";
"address_count" = "Nombre d'adresses";
"address_count_format" = "%d adresses";
"points_count_format" = "%d points";
"additional_points_format" = "+%d points";
"export_route" = "Exporter l'itinéraire";
"navigate" = "Naviguer";
"address_list" = "Liste d'adresses";
"no_addresses" = "Aucune adresse";
"no_addresses_message" = "Cet itinéraire n'a pas encore d'adresses";
"address_point_start" = "Point de départ";
"address_point_stop" = "Point d'arrêt";
"address_point_end" = "Point d'arrivée";
"route_name" = "Nom de l'itinéraire";
"new_route" = "Nouvel itinéraire";
"saved_route" = "Itinéraire enregistré";
"plan_route" = "Planifier l'itinéraire";
"clear_all" = "Tout effacer";
"restore" = "Restaurer l'ordre";
"avoid" = "Éviter :";
"toll_roads" = "Routes à péage";
"highways" = "Autoroutes";
"processing_addresses" = "Traitement des adresses...";
"same_start_end_point" = "Vous avez défini la même adresse comme point de départ et d'arrivée";
"add_start_point" = "Ajouter un point de départ";
"swipe_left_to_delete" = "Appui long pour supprimer";
"add_end_point" = "Ajouter un point d'arrivée";
"enter_and_select_address" = "Entrez et sélectionnez une adresse";
"current_search_text" = "Texte de recherche actuel : %@";
"add_address" = "Ajouter une adresse";
"selected_coordinates" = "Coordonnées sélectionnées";
"company_name_optional" = "Nom de l'entreprise (Optionnel)";
"url_optional" = "URL (Optionnel)";
"favorite_address" = "Adresse favorite";
"set_as_start_and_end" = "Définir comme point de départ et d'arrivée";
"batch_paste" = "Collage par lot";
"file_import" = "Importation de fichier";
"web_download" = "Téléchargement web";
"saving" = "Enregistrement en cours...";
"delivery_point_management" = "Gestion des points de livraison";
"information_category" = "Catégorie d'information";
"package_info" = "Informations sur le colis";
"vehicle_position" = "Position du véhicule";
"delivery_info" = "Informations de livraison";
"take_photo_record" = "Prendre une photo";
"update_status" = "Mettre à jour le statut";
"edit_address_button" = "Modifier l'adresse";
"access_instructions" = "Instructions d'accès";
"add_access_instructions" = "Ajouter des instructions d'accès...";
"package_count" = "Nombre de colis";
"packages" = "colis";
"package_unit" = "pcs";
"package_size" = "Taille du colis";
"package_type" = "Type de colis";
"mark_as_important" = "Marquer comme important";
"priority_delivery" = "Livraison prioritaire";
"priority_level" = "Niveau de priorité";
"priority_1" = "Priorité 1";
"priority_2" = "Priorité 2";
"priority_3" = "Priorité 3";
"no_priority" = "Aucune priorité";
"no_priority_short" = "Aucune";
"set_priority" = "Définir la priorité";
"select_package_position" = "Sélectionner la position du colis dans le véhicule";
"vehicle_area" = "Zone du véhicule";
"left_right_position" = "Position gauche/droite";
"vehicle_position_front" = "Avant";
"vehicle_position_middle" = "Milieu";
"vehicle_position_back" = "Arrière";
"vehicle_position_left" = "Gauche";
"vehicle_position_right" = "Droite";
"vehicle_position_floor" = "Bas";
"vehicle_position_shelf" = "Haut";
"height_position" = "Position en hauteur";
"vehicle_position_none" = "Aucune position sélectionnée";
"delivery_type" = "Type de livraison";
"delivery_status" = "Statut de livraison";
"order_info" = "Informations sur la commande";
"order_information" = "Informations de commande";
"order_number" = "Numéro de commande";
"enter_order_number" = "Entrez le numéro de commande";
"tracking_number" = "Numéro de suivi";
"enter_tracking_number" = "Entrez le numéro de suivi";
"tracking_info" = "Informations de suivi";
"tracking_information" = "Informations de suivi";
"time_info" = "Informations temporelles";
"time_information" = "Informations temporelles";
"estimated_arrival_time" = "Heure d'arrivée estimée";
"anytime" = "N'importe quand";
"stop_time" = "Temps d'arrêt";
"minutes_format" = "%d minutes";
"photo_record" = "Enregistrement photo";
"door_number_photo" = "Photo du numéro de porte";
"package_label_photo" = "Photo de l'étiquette du colis";
"placement_photo" = "Photo de placement";
"door_number_desc" = "Veuillez prendre une photo claire du numéro de porte ou de rue, assurez-vous que les chiffres/lettres sont visibles";
"package_label_desc" = "Veuillez photographier l'étiquette du colis, assurez-vous que les informations du destinataire sont clairement visibles";
"placement_desc" = "Veuillez photographier l'emplacement final de placement du colis";
"photo_captured" = "Photo prise";
"photo_captured_options" = "Photo prise, voulez-vous continuer avec la photo suivante ou terminer la photo actuelle ?";
"continue_to_next_photo" = "Continuer avec la photo suivante - %@";
"retake" = "Reprendre la photo";
"tap_to_capture" = "Appuyer pour capturer";
"flash_auto" = "Flash automatique";
"flash_on" = "Flash activé";
"flash_off" = "Flash désactivé";
"photo_record_completed" = "Enregistrement photo terminé";
"photo_confirmation" = "Confirmation de photo";
"ok" = "OK";
"complete_photo_capture" = "Terminer les photos";
"photo_instructions" = "Appuyez sur chaque carte photo pour capturer. Toutes les photos doivent être terminées.";
"photo_options" = "Options de photo";
"view_photo" = "Voir la photo";
"retake_photo" = "Reprendre la photo";
"saving_photos" = "Enregistrement des photos...";
"completed" = "Terminé";
"not_taken" = "Non prise";
"route_options" = "Options d'itinéraire";
"avoid_tolls" = "Éviter les péages";
"avoid_highways" = "Éviter les autoroutes";
"optimize_route" = "Optimiser l'itinéraire";
"optimizing" = "Optimisation en cours...";
"optimization_complete" = "Optimisation terminée";
"route_optimization_results" = "Résultats";
"route_planning_options" = "Options de planification d'itinéraire";
"before_optimization" = "Avant optimisation";
"after_optimization" = "Après optimisation";
"auto_group" = "Groupement automatique";
"optimized_route_order" = "Ordre";
"apply" = "Appliquer";
"kilometers" = "kilomètres";
"street_number_issue_warning" = "⚠️ Grande différence de numéro de rue ! Cela peut causer une livraison à une mauvaise adresse et des pénalités. Veuillez vérifier l'adresse immédiatement";
"address_validation_critical" = "Problème critique de validation d'adresse";
"street_number_difference_high_risk" = "Grande différence de numéro de rue, risque élevé";
"delete_address" = "Supprimer l'adresse";
"street" = "Rue";
"city" = "Ville";
"state" = "État/Province";
"country" = "Pays";
"postal_code" = "Code postal";
"phone" = "Téléphone";
"email" = "Email";
"website" = "Site web";
"company" = "Entreprise";
"latitude" = "Latitude";
"longitude" = "Longitude";
"geocoding_error" = "Erreur de géocodage";
"address_validation" = "Validation d'adresse";
"invalid_addresses" = "Adresses invalides";
"fix_addresses" = "Corriger les adresses";
"route" = "Itinéraire";
"select_address_point" = "Sélectionner un point d'adresse";
"select_delivery_points" = "Sélectionner des points de livraison";
"create_delivery_route" = "Créer un itinéraire de livraison";
"view_saved_routes" = "Voir les itinéraires enregistrés";
"create_route" = "Créer un itinéraire";
"edit_route" = "Modifier l'itinéraire";
"delete_route" = "Supprimer l'itinéraire";
"route_details" = "Détails de l'itinéraire";
"selected_addresses" = "%d adresses sélectionnées";
"reached_limit" = "Limite atteinte";
"can_select_more" = "Peut encore sélectionner %d";
"navigate_button" = "Navigation";
"create_group" = "Créer un groupe";
"waypoints" = "Points de passage";
"total_distance" = "Distance totale";
"estimated_time" = "Temps estimé";
"route_summary" = "Résumé de l'itinéraire";
"route_saved" = "Itinéraire enregistré";
"route_optimized" = "Itinéraire optimisé";
"optimizing_route" = "Optimisation de l'itinéraire...";
"completed_percent" = "%d%% terminé";
"processing_points" = "Traitement : %d/%d";
"estimated_remaining_time" = "Temps restant estimé : %@";
"delivery" = "Livraison";
"delivery_confirmation" = "Confirmation de livraison";
"take_photo" = "Prendre une photo";
"signature" = "Signature";
"delivery_notes" = "Notes de livraison";
"delivered" = "Livré";
"not_delivered" = "Non livré";
"delivery_time" = "Heure de livraison";
"delivery_date" = "Date de livraison";
"package_details" = "Détails du colis";
"package_id" = "ID du colis";
"package_weight" = "Poids du colis";
"package_dimensions" = "Dimensions du colis";
"recipient_name" = "Nom du destinataire";
"recipient_phone" = "Téléphone du destinataire";
"groups" = "Groupes";
"saved_groups" = "Groupes enregistrés";
"edit_group" = "Modifier le groupe";
"delete_group" = "Supprimer le groupe";
"group_name" = "Nom du groupe";
"group_details" = "Détails du groupe";
"auto_grouping" = "Regroupement automatique";
"group_by" = "Grouper par";
"add_to_group" = "Ajouter au groupe";
"remove_from_group" = "Retirer du groupe";
"group_created" = "Groupe créé";
"default_group_name_format" = "Groupe %d";
"auto_grouping_completed" = "Regroupement automatique terminé";
"auto_grouping_in_progress" = "Regroupement automatique en cours...";
"create_group_every_14_addresses" = "Créer un groupe pour chaque 14 adresses";
"create_delivery_group" = "Créer un groupe de livraison";
"enter_group_name" = "Entrer le nom du groupe";
"selected_delivery_points" = "Points de livraison sélectionnés";
"drag_to_adjust_order" = "Glisser pour ajuster l'ordre";
"free_plan" = "Version gratuite";
"pro_plan" = "Version Pro";
"expert_plan" = "Version Expert";
"monthly" = "Mensuel";
"yearly" = "Annuel";
"subscribe" = "S'abonner";
"manage_subscription" = "Gérer l'abonnement";
"subscription_benefits" = "Avantages de l'abonnement";
"free_trial" = "Essai gratuit";
"price_per_month" = "%@ par mois";
"price_per_year" = "%@ par an";
"save_percent" = "Économisez %@%";
"subscription_terms" = "Conditions d'abonnement";
"privacy_policy" = "Politique de confidentialité";
"terms_of_service" = "Conditions d'utilisation";
"feature_comparison" = "Comparaison des fonctionnalités";
"addresses_per_route" = "Adresses par itinéraire";
"max_20_addresses" = "20 adresses";
"fuel_savings" = "Économies de carburant";
"up_to_30_percent" = "Jusqu'à 30%";
"choose_subscription_plan" = "Choisir un plan d'abonnement";
"monthly_plan" = "Plan mensuel";
"yearly_plan" = "Plan annuel";
"/month_suffix" = "/mois";
"/year_suffix" = "/an";
"save_30_percent" = "Économisez 30%";
"free_trial_7_days_cancel_anytime" = "Inclut un essai gratuit de 14 jours, annulez à tout moment";
"subscription_auto_renew_notice" = "L'abonnement se renouvelle automatiquement sauf s'il est annulé au moins 24 heures avant la fin de la période en cours.";
"and" = "et";
"subscription_exclusive" = "Premium uniquement";
"free_version_optimization_limit" = "Limite d'optimisation de la version gratuite";
"free_version_supports_max_addresses" = "La version gratuite prend en charge jusqu'à %d adresses.";
"current_route_contains_addresses" = "L'itinéraire actuel contient %d adresses, dépassant la limite de la version gratuite.";
"upgrade_to_pro_unlimited_addresses" = "Passez à Pro pour des adresses illimitées et un regroupement de navigation en un clic !";
"continue_optimization" = "Continuer l'optimisation";
"upgrade_unlock_one_click_navigation" = "Mettre à niveau pour débloquer la navigation en un clic - 14x plus rapide";
"learn_one_click_navigation_grouping" = "En savoir plus sur le regroupement de navigation en un clic";
"toggle_subscription_status" = "Basculer le statut d'abonnement";
"toggle_subscription_description" = "Basculer entre les versions gratuite et pro (pour les tests de développement uniquement)";
"product_info_unavailable" = "Impossible d'obtenir les informations sur le produit, veuillez réessayer plus tard";
"purchase_failed" = "Échec de l'achat : %@";
"upgrade_to_pro_version" = "Passer à Pro";
"unlock_all_premium_features" = "Débloquez toutes les fonctionnalités premium";
"first_7_days_free_cancel_anytime" = "Les 14 premiers jours gratuits, annulez à tout moment.";
"upgrade_to_unlock_auto_grouping" = "Passez à Pro pour débloquer la fonction de regroupement automatique";
"free_user_grouping_limit_reached" = "Vous avez atteint la limite de la version gratuite de %d adresses pour le regroupement. Passez à Pro pour un regroupement illimité !";
"upgrade_to_unlock_more_grouping" = "Passez à Pro pour débloquer un regroupement illimité";
"free_user_max_groups_reached" = "Vous avez atteint la limite de la version gratuite de %d groupes. Passez à Pro pour des groupes illimités !";
"payment_terms_notice" = "Le paiement sera débité de votre compte Apple ID lors de la confirmation de l'achat. L'abonnement se renouvelle automatiquement sauf s'il est annulé au moins 24 heures avant la fin de la période en cours.";
"terms_of_use" = "Conditions d'utilisation";
"product_load_failed_check_connection" = "Impossible de charger les informations sur le produit, veuillez vous assurer que votre appareil est connecté à Internet et connecté à l'App Store";
"product_load_failed" = "Échec du chargement du produit : %@";
"verify_receipt" = "Vérifier le reçu";
"one_click_navigation_short" = "Navigation en un clic";
"save_30_percent_fuel" = "Économisez 30% de carburant";
"monthly_short" = "Mensuel";
"yearly_short" = "Annuel";
"upgrade_now" = "Mettre à niveau maintenant";
"test_environment_pro_activated" = "Environnement de test : Version Pro activée";
"payment_terms_notice_detailed" = "Le paiement sera débité de votre compte Apple ID lors de la confirmation de l'achat. L'abonnement se renouvelle automatiquement sauf s'il est annulé au moins 24 heures avant la fin de la période en cours. Vous pouvez gérer et annuler vos abonnements dans les paramètres de l'App Store.";
"step_screenshot" = "Capture d'écran de l'étape %d";
"previous_step" = "Précédent";
"next_step" = "Suivant";
"each_address_takes_3_5_seconds" = "Chaque adresse prend 3-5 secondes à ajouter";
"need_repeat_14_times" = "Besoin de répéter la même opération 14 fois";
"navigation_order_often_confused" = "L'ordre de navigation est souvent confus";
"error_prone_need_redo" = "Sujet aux erreurs, besoin de refaire les opérations";
"address_order_reversed_manual_adjust" = "Ordre des adresses inversé, ajustement manuel nécessaire";
"one_click_add_all" = "Un clic, tout ajouter";
"smart_grouping_auto_sorting" = "Groupement intelligent, tri automatique";
"maintain_correct_visit_order" = "Maintenir l'ordre de visite correct";
"zero_errors_zero_repetition" = "Zéro erreur, zéro répétition";
"import" = "Importer";
"export" = "Exporter";
"import_addresses" = "Importer des adresses";
"export_addresses" = "Exporter des adresses";
"import_from_file" = "Importer depuis un fichier";
"export_to_file" = "Exporter vers un fichier";
"file_format" = "Format de fichier";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Importation réussie de %d adresses, toutes avec des coordonnées valides.";
"export_success" = "Exportation réussie";
"import_error" = "Erreur d'importation";
"export_error" = "Erreur d'exportation";
"navigation_app" = "Application de navigation";
"apple_maps" = "Apple Plans";
"app_preferences" = "Préférences de l'application";
"distance_unit" = "Unité de distance";
"current_language" = "Langue actuelle";
"info" = "Informations";
"contact_us_header" = "Contactez-nous";
"contact_us_subheader" = "Vous avez des questions ou des suggestions ? Nous serions ravis de vous aider !";
"contact_options" = "Options de contact";
"email_us" = "Envoyez-nous un email";
"contact_form" = "Formulaire de contact";
"contact_and_support" = "Contact et support";
"common_questions" = "Questions fréquentes";
"how_to_use" = "Comment utiliser";
"subscription_faq" = "FAQ sur l'abonnement";
"navigation_help" = "Aide à la navigation";
"troubleshooting" = "Dépannage";
"help_howto_content" = "NaviBatch est une puissante application de planification d'itinéraires qui vous aide à optimiser les itinéraires de livraison, économisant du temps et du carburant. Vous pouvez ajouter plusieurs adresses, optimiser automatiquement l'ordre de l'itinéraire et naviguer vers Apple Plans en un clic.";
"help_subscription_content" = "NaviBatch propose des versions gratuite et pro. La version gratuite prend en charge jusqu'à 20 adresses, tandis que la version pro offre des adresses illimitées et des fonctionnalités de navigation groupée en un clic.";
"help_navigation_content" = "NaviBatch utilise Apple Plans pour la navigation. Vous pouvez naviguer vers chaque adresse individuellement, ou utiliser la fonction de groupement pour naviguer vers plusieurs adresses à la fois.";
"help_troubleshooting_content" = "Si vous rencontrez des problèmes, assurez-vous d'abord que votre appareil dispose d'une connectivité réseau et que les autorisations de localisation sont accordées. Si les problèmes persistent, veuillez contacter notre équipe de support.";
"actions" = "Actions";
"legal" = "Légal";
"show_look_around" = "Afficher Look Around";
"hide_look_around" = "Masquer Look Around";
"map" = "Carte";
"map_type" = "Type de carte";
"standard" = "Standard";
"satellite" = "Satellite";
"hybrid" = "Hybride";
"show_traffic" = "Afficher le trafic";
"current_location" = "Emplacement actuel";
"directions" = "Itinéraire";
"distance_to" = "Distance jusqu'à";
"eta" = "Heure d'arrivée estimée";
"look_around" = "Observer les environs";
"locating_to_glen_waverley" = "Localisation vers Glen Waverley";
"network_error" = "Erreur de réseau";
"location_error" = "Erreur de localisation";
"permission_denied" = "Permission refusée";
"location_permission_required" = "Permission de localisation requise";
"camera_permission_required" = "Permission de caméra requise";
"photo_library_permission_required" = "Permission de photothèque requise";
"please_try_again" = "Veuillez réessayer";
"something_went_wrong" = "Une erreur s'est produite";
"invalid_input" = "Entrée invalide";
"required_field" = "Champ obligatoire";
"no_internet_connection" = "Pas de connexion Internet";
"server_error" = "Erreur de serveur";
"timeout_error" = "Délai d'attente dépassé";
"data_not_found" = "Données non trouvées";
"selection_limit_reached" = "Limite de sélection atteinte";
"selection_limit_description" = "Vous pouvez sélectionner un maximum de %d adresses, vous avez sélectionné %d";
"location_status_valid" = "Plage valide";
"address_validation_unknown" = "Non vérifié";
"address_validation_valid" = "Valide";
"address_validation_invalid" = "Invalide";
"address_validation_warning" = "Avertissement";
"address_validation_mismatch" = "Non correspondant";
"device_not_support_scanning" = "L'appareil ne prend pas en charge le scan natif";
"requires_ios16_a12_chip" = "Nécessite iOS 16+ et puce A12 ou ultérieure";
"debug_info" = "Informations de débogage :";
"address_confirmation" = "Confirmation d'adresse";
"continue_scanning" = "Continuer le scan";
"confirm_add" = "Confirmer l'ajout";
"cannot_get_coordinates_scan_retry" = "Impossible d'obtenir les coordonnées d'adresse, veuillez entrer manuellement ou scanner à nouveau";
"unknown_country" = "Pays inconnu";
"unknown_city" = "Ville inconnue";
"please_enter_valid_address" = "Veuillez entrer au moins une adresse valide";
"please_select_valid_address" = "Veuillez sélectionner une adresse valide";
"add_address_failed" = "Échec de l'ajout d'adresse";
"location_permission_required_for_current_location" = "Autorisation de localisation requise pour obtenir la position actuelle";
"cannot_get_current_location_check_settings" = "Impossible d'obtenir la position actuelle, veuillez vérifier les paramètres";
"cannot_get_current_location_address" = "Impossible d'obtenir l'adresse de la position actuelle";
"get_current_location_failed" = "Échec de l'obtention de la position actuelle";
"location_status_warning" = "Plage d'avertissement";
"location_status_invalid" = "Position invalide";
"location_status_unknown" = "État inconnu";
"coordinates_origin_point" = "Invalide : Coordonnées zéro (0,0)";
"coordinates_invalid_nan" = "Invalide : Coordonnées non numériques";
"coordinates_out_of_range" = "Invalide : Coordonnées hors de la plage valide";
"coordinates_far_from_user" = "Avertissement : La position est loin de votre emplacement actuel";
"coordinates_ocean" = "Avertissement : La position peut être dans l'océan ou une zone inhabitée";
"free_address_limit" = "Limite d'adresses de la version gratuite";
"address_count_limit" = "Limite du nombre d'adresses";
"selected_addresses_can_import" = "Vous avez sélectionné %d adresses, elles peuvent être importées.";
"selected_addresses_exceeds" = "Vous avez sélectionné %d adresses, dépassant la limite de %d.";
"import_success_with_warnings" = "Importation réussie de %d adresses, dont %d ont des coordonnées normales, %d ont des avertissements.\n\nLes adresses avec avertissements sont marquées et peuvent être corrigées manuellement après l'importation.";
"supported_formats" = "Formats pris en charge";
"supported_format_csv" = "• Fichiers CSV : La colonne d'adresses doit contenir des adresses complètes";
"supported_format_json" = "• Données JSON : Tableau contenant des champs d'adresses";
"supported_format_text" = "• Texte brut : Une adresse par ligne";
"download_history" = "Historique des téléchargements";
"input_address_data_url" = "Entrer l'URL des données d'adresses";
"import_result" = "Résultat d'importation";
"downloading" = "Téléchargement...";
"processing_data" = "Traitement des données...";
"google_drive_download_failed" = "Échec du téléchargement Google Drive";
"second_attempt_invalid_data" = "La deuxième tentative de téléchargement a renvoyé des données invalides";
"cannot_parse_json" = "Impossible d'analyser les données JSON, veuillez vérifier le format du fichier";
"cannot_parse_json_with_error" = "Impossible d'analyser les données JSON : %@";
"cannot_read_file" = "Impossible de lire le fichier : %@";
"failed" = "Échec de livraison";
"no_valid_addresses" = "Aucune adresse valide trouvée";
"supports_file_types" = "Prend en charge les fichiers CSV, TXT et JSON";
"tap_to_select_file" = "Appuyer pour sélectionner un fichier";
"input_company_name" = "Entrer le nom de l'entreprise (optionnel)";
"imported_addresses_count" = "%d adresses importées";
"excel_format_not_supported" = "Format Excel non pris en charge";
"import_failed" = "Échec d'importation";
"free_version_address_limit" = "La version gratuite permet maximum %d adresses.";
"current_address_count" = "Vous avez actuellement %d adresses, ne pouvez ajouter que %d de plus.";
"upgrade_to_premium_unlimited" = "Passez à la version premium pour des adresses illimitées !";
"route_address_limit" = "L'itinéraire actuel a %d adresses, ne peut ajouter que %d de plus, maximum total de %d adresses.";
"free_version_limit" = "Limite d'adresses de la version gratuite";
"import_all_n" = "Importer toutes les %d";
"cannot_import" = "Impossible d'importer";
"select_at_least_one" = "Veuillez sélectionner au moins une adresse";
"no_valid_addresses_found" = "Aucune adresse valide trouvée";
"import_success_all_valid" = "Importation réussie de %d adresses, toutes les coordonnées d'adresses sont normales.";
"import_success_some_warnings" = "Importation réussie de %d adresses, dont %d ont des coordonnées normales, %d n'ont pas pu obtenir de coordonnées.";
"company_format" = "Format d'entreprise";
"added_from_web_download" = "Ajouté depuis le téléchargement web";
"invalid_csv_row" = "Ligne CSV invalide";
"distance_warning" = "Distance de l'emplacement actuel supérieure à 200 km";
"not_in_australia" = "Les coordonnées ne sont pas dans la zone australienne";
"invalid_address_data" = "Données d'adresse invalides";
"distance_warning_confirm" = "La distance depuis la position actuelle dépasse 200 km, continuer ?";
"coordinates_missing" = "Coordonnées manquantes";
"low_accuracy_address" = "Adresse de faible précision";
"address_partial_match" = "Correspondance partielle d'adresse";
"address_outside_region" = "Adresse en dehors de la région";
"api_limit_reached" = "Limite d'API atteinte";
"address_not_exist_or_incorrect_format" = "L'adresse n'existe pas ou le format est incorrect";
"please_check_address_spelling" = "Veuillez vérifier l'orthographe de l'adresse";
"try_smaller_street_number" = "Essayez avec un numéro de rue plus petit";
"use_full_street_type_name" = "Utilisez le nom complet du type de rue";
"try_add_more_address_details" = "Essayez d'ajouter plus de détails d'adresse";
"cannot_find_address" = "Impossible de trouver l'adresse";
"please_check_spelling_or_add_details" = "Veuillez vérifier l'orthographe ou ajouter plus de détails";
"cannot_find_address_check_spelling" = "Impossible de trouver l'adresse, vérifiez l'orthographe";
"address_not_set" = "Adresse non définie";
"address_format_incomplete" = "Format d'adresse incomplet";
"location_service_denied" = "Service de localisation refusé";
"no_saved_groups" = "Aucun groupe enregistré";
"select_points_create_groups" = "Sélectionnez des points de livraison et créez des groupes pour faciliter la gestion";
"navigate_to_these_points" = "Naviguer vers ces points";
"confirm_remove_address" = "Êtes-vous sûr de vouloir supprimer l'adresse \"%@\" du groupe ?";
"confirm_remove_this_address" = "Êtes-vous sûr de vouloir supprimer cette adresse du groupe ?";
"addresses_count" = "%d adresses";
"no_saved_routes" = "Aucun itinéraire enregistré";
"no_saved_routes_description" = "Vous n'avez encore enregistré aucun itinéraire";
"all_routes" = "Tous les itinéraires";
"address_count_format_simple" = "%d adresses";
"delete_all_routes" = "Supprimer tous les itinéraires";
"navigate_to_all_points" = "Naviguer vers tous les points";
"confirm_navigate_to_route" = "Êtes-vous sûr de vouloir naviguer vers tous les points de l'itinéraire \"%@\" ?";
"temp_navigation_group" = "Groupe de navigation temporaire";
"route_management" = "Gestion d'itinéraires";
"route_addresses" = "Adresses d'itinéraire";
"no_addresses_in_route" = "Cet itinéraire n'a pas d'adresses";
"must_keep_one_route" = "Doit conserver au moins un itinéraire";
"confirm_delete_route" = "Êtes-vous sûr de vouloir supprimer l'itinéraire \"%@\" ? Cette action ne peut pas être annulée.";
"confirm_delete_all_routes" = "Confirmer la suppression de tous les itinéraires";
"confirm_delete_all_routes_message" = "Êtes-vous sûr de vouloir supprimer tous les itinéraires ? Cette action ne peut pas être annulée.";
"delete_all" = "Supprimer tout";
"address_information" = "Informations d'adresse";
"group_belonging" = "Groupe d'appartenance";
"view_map" = "Voir la carte";
"delete_delivery_point" = "Supprimer le point de livraison";
"delivery_point_details" = "Détails du point de livraison";
"confirm_deletion" = "Confirmer la suppression";
"delete_delivery_point_confirmation" = "Êtes-vous sûr de vouloir supprimer ce point de livraison ? Cette action ne peut pas être annulée.";
"delivery_photos" = "Photos de livraison";
"view_delivery_photos" = "Voir les photos de livraison";
"no_photos_taken" = "Aucune photo prise encore";
"take_photos" = "Prendre des photos";
"loading_photos" = "Chargement des photos...";
"photo_not_found" = "Photo non trouvée";
"photo_deleted" = "La photo a été supprimée";
"share_photos" = "Partager la photo";
"photo_capture_title" = "Confirmation de photo";
"door_number_photo_title" = "Photo de numéro de rue/porte";
"package_label_photo_title" = "Photo d'étiquette de colis";
"placement_photo_title" = "Photo d'emplacement de placement";
"door_number_photo_desc" = "Veuillez prendre une photo claire du numéro de porte, assurez-vous que les chiffres/lettres sont visibles";
"package_label_photo_desc" = "Veuillez photographier l'étiquette du colis, assurez-vous que les informations du destinataire sont clairement visibles";
"placement_photo_desc" = "Veuillez photographier l'emplacement final de placement du colis";
"swipe_to_switch" = "Glisser pour changer le type de photo";
"photos_will_be_saved_to" = "Les photos seront enregistrées dans";
"complete_photos" = "Terminer les photos";
"photo_save_success" = "Photo enregistrée avec succès";
"photo_save_failure" = "Échec de l'enregistrement de la photo";
"no_photos_found" = "Aucune photo trouvée";
"photos_deleted_or_not_taken" = "Les photos ont peut-être été supprimées ou n'ont pas encore été prises";
"share_photo" = "Partager la photo";
"photo_capture_preview" = "Mode aperçu - Simulation de caméra";
"photo_capture_close" = "Fermer";
"camera_start_failed" = "Échec du démarrage de la caméra";
"camera_start_failed_retry" = "Impossible de démarrer la caméra, veuillez réessayer";
"camera_init_failed" = "Échec de l'initialisation de la caméra";
"camera_access_failed" = "Impossible d'accéder à la caméra";
"photo_processing_failed" = "Échec de la prise de photo";
"photo_processing_failed_retry" = "Impossible de terminer le traitement de la photo, veuillez réessayer";
"photo_capture_progress" = "Progression : %d/%d";
"photo_captured_continue" = "Capture terminée, continuer avec %@";
"pending" = "En attente de livraison";
"in_progress" = "En cours de livraison";
"select_delivery_status" = "Sélectionner le statut de livraison";
"select_failure_reason" = "Sélectionner la raison de l'échec";
"delivery_status_pending" = "En attente";
"delivery_status_in_progress" = "En cours";
"delivery_status_completed" = "Terminé";
"delivery_status_failed" = "Échec";
"failure_reason_not_at_home" = "Client absent";
"failure_reason_wrong_address" = "Adresse incorrecte";
"failure_reason_no_access" = "Impossible d'accéder à l'emplacement";
"failure_reason_rejected" = "Colis refusé";
"failure_reason_other" = "Autres raisons";
"enter_custom_reason" = "Entrer la raison spécifique";
"custom_reason_placeholder" = "Veuillez décrire la raison spécifique...";
"custom_reason_required" = "Veuillez entrer la raison spécifique";
"failure_reason_required" = "Veuillez sélectionner la raison de l'échec";
"delivery_type_delivery" = "Livraison";
"delivery_type_pickup" = "Collecte";
"delivery_order_first" = "Premier";
"delivery_order_auto" = "Automatique";
"delivery_order_last" = "Dernier";
"package_size_small" = "Petit";
"package_size_medium" = "Moyen";
"package_size_large" = "Grand";
"package_type_box" = "Boîte";
"package_type_bag" = "Sac";
"package_type_letter" = "Lettre";
"one_click_navigation_grouping" = "Navigation en un clic et groupement";
"speed_60x_faster" = "60x plus rapide";
"goodbye_manual_address_adding" = "Adieu à l'ajout manuel d'adresses";
"watch_detailed_demo" = "Voir la démonstration détaillée";
"upgrade_to_pro_now" = "Passer à Pro maintenant";
"free_trial_7_days" = "Essai gratuit de 7 jours";
"traditional_vs_navibatch_pro" = "Méthode traditionnelle vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "Glissez pour voir la comparaison complète";
"traditional_method" = "Méthode traditionnelle";
"drivers_get_lost_affect_efficiency" = "Les conducteurs se perdent, affectant l'efficacité";
"repetitive_operations_waste_time" = "Les opérations répétitives font perdre du temps";
"total_time_60_seconds" = "Temps total : 60 secondes";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Optimiser les itinéraires, réduire la distance";
"improve_delivery_efficiency_accuracy" = "Améliorer l'efficacité et la précision de livraison";
"speed_boost_60x" = "Augmentation de vitesse 60x";
"total_time_1_second" = "Temps total : 1 seconde";
"time_comparison" = "Comparaison de temps";
"traditional_method_problems" = "Problèmes de la méthode traditionnelle";
"each_address_3_5_seconds_14_total_60" = "Chaque adresse 3-5 secondes, 14 adresses total 60 secondes";
"repetitive_operations_cause_fatigue" = "Les opérations répétitives causent de la fatigue";
"address_order_reversed_last_becomes_first" = "Ordre des adresses inversé, le dernier devient le premier";
"need_manual_reverse_adding_takes_longer" = "Besoin d'inversion manuelle, l'ajout prend plus de temps";
"navibatch_advantages" = "Avantages de NaviBatch";
"add_14_addresses_1_second_60x_faster" = "Ajouter 14 adresses en 1 seconde, 60x plus rapide";
"auto_maintain_correct_order_no_adjustment" = "Maintenir automatiquement l'ordre correct, aucun ajustement";
"zero_error_rate_no_repetition" = "Taux d'erreur zéro, aucune répétition";
"save_59_seconds" = "Économise 59 secondes";
"speed_boost_60x_simple" = "Augmentation de vitesse 60x";
"seconds_format" = "%d secondes";
"actual_benefits_one_click_navigation" = "Avantages réels de la navigation en un clic";
"daily_savings" = "Économies quotidiennes";
"daily_savings_value" = "59 secondes";
"daily_savings_description" = "Économise 59 secondes pour chaque 14 adresses";
"monthly_savings" = "Économies mensuelles";
"monthly_savings_value" = "30 minutes";
"monthly_savings_description" = "Basé sur 30 itinéraires par mois";
"fuel_savings_value" = "30%";
"fuel_savings_description" = "L'optimisation d'itinéraires réduit la consommation de carburant";
"income_increase" = "Augmentation des revenus";
"income_increase_value" = "15%";
"income_increase_description" = "Plus de livraisons par jour = plus de revenus";
"trial" = "Essai";
"days_left" = "jours restants";
"free_plan_description" = "Plan gratuit actif";
"pro_plan_active" = "Plan Pro actif";
"expert_plan_active" = "Plan Expert actif";
"trial_active" = "Essai actif";
"trial_expires_on" = "L'essai expire le %@";
"address_validation_mode" = "Mode de validation d'adresses";
"validation_description" = "Contrôle la rigueur de la validation d'adresses";
"current_settings" = "Paramètres actuels";
"validation_mode_format" = "Mode : %@";
"threshold_score_format" = "Seuil : %.1f";
"validation_example" = "Exemple de validation";
"original_address_example" = "Adresse originale : 123 Main St";
"reverse_address_example" = "Adresse inversée : 125 Main St";
"house_number_difference" = "Différence de numéro de maison : 2";
"result_label" = "Résultat :";
"may_pass_warning" = "Peut passer (avertissement)";
"will_not_pass" = "Ne passera pas";
"real_case_example" = "Exemple de cas réel";
"real_case_description" = "Basé sur des données réelles de validation d'adresses";
"address_validation_settings" = "Paramètres de validation d'adresses";
"clear" = "Effacer";
"view_details" = "Voir les détails";
"create_test_data" = "Créer des données de test";
"manual_snapshot" = "Capture manuelle";
"start_location_updates" = "Démarrer les mises à jour de localisation";
"stop_location_updates" = "Arrêter les mises à jour de localisation";
"user_location_marker_test" = "Test de marqueur de localisation utilisateur";
"location_animation_control" = "Contrôle d'animation de localisation";
"current_location_format" = "Position actuelle : %.6f, %.6f";
"waiting_for_location" = "En attente de localisation...";
"diagnostic_tools" = "Outils de diagnostic";
"storekit_diagnostics" = "Diagnostics StoreKit";
"subscription_function_test" = "Test de fonction d'abonnement";
"localization_test" = "Test de localisation";
"address_validation_demo" = "Démonstration de validation d'adresses";
"localization_tools" = "Outils de localisation";
"coordinate_debug_tools" = "Outils de débogage de coordonnées";
"smart_abbreviation_expansion_test" = "Test d'expansion intelligente d'abréviations";
"subscription_restore_diagnostics" = "Diagnostics de restauration d'abonnement";
"batch_address_import_test" = "Test d'importation en lot d'adresses";
"test_import_1000_addresses_memory" = "Test : Importer 1000 adresses (mémoire)";
"map_rendering_test" = "Test de rendu de carte";
"test_map_display_markers_memory" = "Test : Afficher les marqueurs sur la carte (mémoire)";
"select_test_language" = "Sélectionner la langue de test";
"discover_60x_speed_boost" = "Découvrez l'augmentation de vitesse 60x";
"see_60x_speed_demo" = "Voir la démonstration de vitesse 60x";
"free_vs_pro_comparison" = "Comparaison Gratuit vs Pro";
"our_free_beats_competitors_paid" = "Notre plan gratuit surpasse les plans payants de la concurrence";
"features" = "Fonctionnalités";
"up_to_20" = "Jusqu'à 20";
"unlimited" = "Illimité";
"smart_optimization" = "Optimisation intelligente";
"up_to_20_percent" = "Jusqu'à 20%";
"free_tier_grouping_limit" = "Jusqu'à 10 adresses";
"pro_tier_unlimited_grouping" = "Groupement illimité";
"free_tier_navigation_limit" = "1 groupe (jusqu'à 10 adresses)";
"pro_tier_unlimited_navigation" = "Groupes multiples (illimité)";
"file_not_found" = "Fichier non trouvé";
"sample_file_not_available" = "Fichier d'exemple non disponible";
"file_copy_failed" = "Échec de la copie de fichier";

// PDF Processing
"ai_processing_pdf_text" = "Traitement du texte PDF";
"ai_processing_pdf_native" = "Traitement du document PDF";
"processing_pdf_document" = "Traitement du document PDF";
"processing_pdf_text" = "Traitement du texte PDF";
"select_pdf_files" = "Sélectionner les fichiers PDF";
"tap_to_select_from_files" = "Appuyez pour sélectionner depuis les fichiers";
"loading_pdf_files" = "Chargement des fichiers PDF...";
"pdf_selection_failed" = "Échec de la sélection du fichier PDF";
"no_pdf_pages_extracted" = "Aucune page extraite du PDF";
"extracting_pdf_pages" = "Extraction des pages PDF";

// 地图模式选择器
"map_mode_selector_title" = "Mode Carte";
"map_mode_standard" = "Standard";
"map_mode_satellite" = "Satellite";
"map_mode_traffic" = "Trafic";
"map_mode_hybrid" = "Hybride";

// Service de livraison
"delivery_service" = " Service de livraison";