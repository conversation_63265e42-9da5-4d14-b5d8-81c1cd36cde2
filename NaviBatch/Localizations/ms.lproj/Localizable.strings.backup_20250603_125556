/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Tetapan Bahasa";
"system_language" = "Bahasa Sistem";
"system_language_section" = "Tetapan Sistem";
"languages" = "Bahasa";
"language_info_title" = "Mengenai Tetapan Bahasa";
"language_info_description" = "Selepas menukar tetapan bahasa, aplikasi akan memaparkan teks dalam bahasa yang dipilih. Sesetengah kandungan mungkin memerlukan aplikasi dimulakan semula untuk menggunakan tetapan bahasa baharu sepenuhnya.";
"restart_required" = "Perlu Mulakan Semula Aplikasi";
"restart_app_message" = "Untuk menggunakan perubahan bahasa sepenuhnya, sila mulakan semula aplikasi.";
"restart_now" = "Mulakan Semula Sekarang";
"restart_later" = "Mulakan <PERSON>";

// MARK: - Common UI Elements
"close" = "Tutup";
"cancel" = "Batal";
"save" = "Simpan";
"edit" = "Edit";
"delete" = "Padam";
"done" = "Selesai";
"next" = "Seterusnya";
"back" = "Kembali";
"confirm" = "Sahkan";
"error" = "Ralat";
"success" = "Berjaya";
"warning" = "Amaran";
"loading" = "Memuatkan...";
"search" = "Cari";
"settings" = "Tetapan";
"help" = "Bantuan";
"about" = "Mengenai";
"menu" = "Menu";
"understand" = "Saya Faham";

// MARK: - Navigation
"navigation" = "Navigasi";
"start_navigation" = "Mula Navigasi";

// MARK: - Subscription
"subscription" = "Langganan";
"upgrade_to_pro" = "Naik Taraf ke Pro";
"upgrade_description" = "Kumpulan navigasi satu klik, 60x lebih pantas daripada operasi manual";
"restore_purchases" = "Pulihkan Pembelian";
"learn_more" = "Ketahui Lebih Lanjut";
"upgrade_your_plan" = "Naik Taraf Pelan Anda";
"one_click_navigation_description" = "Kumpulan alamat navigasi satu klik, jimat masa dan bahan api";
"current_plan" = "Pelan Semasa";
"upgrade" = "Naik Taraf";
"maybe_later" = "Kemudian";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Percuma";
"pro_tier_price" = "$29.99/bulan";
"expert_tier_price" = "$249.99/tahun";
"free_tier_description" = "Sesuai untuk individu dan perniagaan kecil, tidak memerlukan banyak perhentian";
"pro_tier_description" = "Sesuai untuk pengguna yang memerlukan kumpulan navigasi satu klik dan alamat tanpa had";
"expert_tier_description" = "Sama seperti Pro, tetapi jimat 31% dengan pelan tahunan";
"route_optimization" = "Pengoptimuman Laluan";
"unlimited_routes" = "Laluan Tanpa Had";
"unlimited_optimizations" = "Pengoptimuman Tanpa Had";
"max_15_addresses" = "Maksimum 15 alamat setiap laluan";
"save_fuel_30" = "Jimat bahan api sehingga 30%";
"unlimited_addresses" = "✨ Alamat Tanpa Had ✨";
"one_click_navigation" = "⚡ Kumpulan Navigasi Satu Klik - 60x Lebih Pantas ⚡";
"package_finder" = "Pencari Lokasi Pakej";
"annual_savings" = "Penjimatan Pelan Tahunan";
"switched_to_free" = "Bertukar ke Pelan Percuma";
"switched_to_subscription" = "Bertukar ke Pelan Langganan";
"unlimited_stops" = "Perhentian Tanpa Had";
"plan_as_many_stops_as_needed" = "Tambah sebarang bilangan titik penghantaran tanpa had";

// MARK: - Address Input
"enter_or_search_address" = "Masukkan atau cari alamat";
"search_results_count" = "Hasil carian: %d";
"no_matching_addresses" = "Tiada alamat yang sepadan ditemui";
"search_address_failed" = "Carian alamat gagal: %@";
"address_search_no_response" = "Carian alamat tiada respons";
"cannot_get_address_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"speech_recognizer_unavailable" = "Pengecam suara tidak tersedia";
"microphone_permission_denied" = "Kebenaran mikrofon tidak dibenarkan";
"speech_recognition_permission_denied" = "Kebenaran pengecaman suara tidak dibenarkan";
"listening" = "Sedang mendengar...";
"recording_failed" = "Gagal memulakan rakaman: %@";
"cannot_get_coordinates_retry" = "Tidak dapat memperoleh koordinat alamat, sila masukkan secara manual atau cuba lagi";
"cannot_create_recognition_request" = "Tidak dapat mencipta permintaan pengecaman";

// MARK: - Saved Address Picker
"search_address" = "Cari Alamat";
"no_saved_addresses" = "Tiada Alamat Tersimpan";
"no_saved_addresses_description" = "Anda belum menyimpan sebarang alamat, atau tiada alamat yang memenuhi kriteria penapisan";
"select_address_book" = "Pilih Buku Alamat";

// MARK: - Menu
"menu" = "Menu";
"routes" = "Laluan";
"address_book" = "Buku Alamat";
"saved_routes" = "Laluan Tersimpan";
"manage_your_routes" = "Urus perancangan laluan anda";
"manage_your_addresses" = "Urus alamat yang kerap digunakan";
"settings" = "Tetapan";
"preferences" = "Keutamaan";
"set_custom_start_point" = "Tetapkan Titik Mula Tersuai";
"current_start_point" = "Titik mula semasa: %@";
"support" = "Sokongan";
"contact_us" = "Hubungi Kami";
"contact_us_description" = "Ada soalan atau cadangan? Jangan ragu untuk menghubungi kami!";
"help_center" = "Pusat Bantuan";
"subscription" = "Langganan";
"upgrade_to_pro" = "Naik Taraf ke Pro";
"upgrade_description" = "Kumpulan navigasi satu klik, 60x lebih pantas daripada operasi manual";
"open_subscription_view" = "Buka Paparan Langganan Terus";
"open_subscription_view_description" = "Langkau lapisan tengah, paparkan SubscriptionView terus";
"restore_purchases_failed" = "Gagal memulihkan pembelian: %@";
"about" = "Mengenai";
"rate_us" = "Nilai Kami";
"rate_us_description" = "Maklum balas anda penting bagi kami dan membantu kami menambah baik aplikasi!";
"share_app" = "Kongsi Aplikasi";
"share_app_text" = "Cuba NaviBatch, aplikasi perancangan laluan yang menakjubkan!";
"about_app" = "Mengenai Aplikasi";
"developer_tools" = "Alat Pembangun";
"coordinate_debug_tool" = "Alat Debug Koordinat";
"batch_fix_addresses" = "Betulkan Alamat Secara Berkelompok";
"clear_database" = "Kosongkan Pangkalan Data";
"clear_database_confirmation" = "Ini akan memadamkan semua data, termasuk laluan, alamat, dan kumpulan. Tindakan ini tidak boleh dibuat asal. Adakah anda pasti mahu meneruskan?";
"confirm_clear" = "Sahkan Kosongkan";
"version_info" = "Versi %@ (%@)";
"current_system_language" = "Bahasa Sistem Semasa";
"reset_to_system_language" = "Tetapkan Semula ke Bahasa Sistem";
"language" = "Bahasa";
"language_settings" = "Tetapan Bahasa";

// MARK: - Address Edit
"address" = "Alamat";
"coordinates" = "Koordinat";
"distance_from_current_location" = "Jarak dari Lokasi Semasa";
"address_info" = "Maklumat Alamat";
"update_coordinates" = "Kemas Kini Koordinat";
"fix_address" = "Betulkan Alamat";
"prompt" = "Gesaan";
"confirm" = "Sahkan";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Sila ubah suai alamat sebelum mengemas kini koordinat";
"coordinates_update_success" = "Koordinat berjaya dikemas kini";
"coordinates_update_failure" = "Gagal mengemas kini koordinat";
"save_failure" = "Gagal menyimpan: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Tiada Alamat Tersimpan";
"no_saved_addresses_message" = "Anda belum menyimpan sebarang alamat lagi";
"add_new_address" = "Tambah Alamat Baharu";
"address_title" = "Alamat";
"add" = "Tambah";
"refresh" = "Muat Semula";
"notes" = "Nota";
"address_details" = "Butiran Alamat";
"favorite" = "Kegemaran";
"edit_address" = "Edit Alamat";
"cancel" = "Batal";
"save" = "Simpan";
"confirm_delete" = "Sahkan Padam";
"delete" = "Padam";
"delete_address_confirmation" = "Adakah anda pasti mahu memadamkan alamat ini? Tindakan ini tidak boleh dibuat asal.";
"edit" = "Edit";
"address_marker" = "Alamat";
"address_label" = "Alamat:";
"notes_label" = "Nota:";
"created_at_label" = "Masa Dicipta:";
"open_in_maps" = "Buka dalam Peta";
"copy_address" = "Salin Alamat";
"address_details_title" = "Butiran Alamat";

// MARK: - Route Detail
"start_end_point" = "Titik Mula/Tamat";
"start_point" = "Titik Mula";
"end_point" = "Titik Tamat";
"route_info" = "Maklumat Laluan";
"address_count" = "Bilangan Alamat";
"address_count_format" = "%d alamat";
"points_count_format" = "%d titik";
"additional_points_format" = "+%d titik";
"export_route" = "Eksport Laluan";
"navigate" = "Navigasi";
"address_list" = "Senarai Alamat";
"no_addresses" = "Tiada Alamat";
"no_addresses_message" = "Laluan ini belum mempunyai alamat lagi";

// MARK: - Route Bottom Sheet
"address_point_start" = "Titik Mula";
"address_point_stop" = "Titik Perhentian";
"address_point_end" = "Titik Tamat";
"route_name" = "Nama Laluan";
"save" = "Simpan";
"new_route" = "Laluan Baharu";
"saved_route" = "Laluan Tersimpan";
"edit" = "Edit";
"loading" = "Memuatkan...";
"plan_route" = "Rancang Laluan";
"clear_all" = "Kosongkan Semua";
"avoid" = "Elakkan:";
"toll_roads" = "Jalan Tol";
"highways" = "Lebuh Raya";
"processing_addresses" = "Memproses alamat...";
"same_start_end_point" = "Anda telah menetapkan alamat yang sama sebagai titik mula dan tamat";
"add_start_point" = "Tambah Titik Mula";
"swipe_left_to_delete" = "← Leret ke kiri untuk padam";
"delete" = "Padam";
"add_new_address" = "Tambah Alamat Baharu";
"add_end_point" = "Tambah Titik Tamat";

// MARK: - Simple Address Sheet
"address_title" = "Alamat";
"enter_and_select_address" = "Masukkan dan pilih alamat";
"current_search_text" = "Teks carian semasa: %@";
"search_results_count" = "Hasil carian: %d";
"no_matching_addresses" = "Tiada alamat yang sepadan ditemui";
"add_address" = "Tambah Alamat";
"edit_address" = "Edit Alamat";
"selected_coordinates" = "Koordinat Terpilih";
"company_name_optional" = "Nama Syarikat (Pilihan)";
"url_optional" = "URL (Pilihan)";
"favorite_address" = "Alamat Kegemaran";
"set_as_start_and_end" = "Tetapkan sebagai Titik Mula dan Tamat";
"address_book" = "Buku Alamat";
"batch_paste" = "Tampal Berkelompok";
"file_import" = "Import Fail";
"web_download" = "Muat Turun Web";
"cancel" = "Batal";
"saving" = "Menyimpan...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Pengurusan Titik Penghantaran";
"information_category" = "Kategori Maklumat";
"address_info" = "Maklumat Alamat";
"package_info" = "Maklumat Pakej";
"vehicle_position" = "Kedudukan Kenderaan";
"delivery_info" = "Maklumat Penghantaran";
"navigation" = "Navigasi";
"take_photo_record" = "Ambil Rekod Foto";
"update_status" = "Kemas Kini Status";
"done" = "Selesai";
"edit_address_button" = "Edit Alamat";
"coordinates" = "Koordinat";
"access_instructions" = "Arahan Akses";
"add_access_instructions" = "Tambah arahan akses...";
"package_count" = "Bilangan Pakej";
"package_size" = "Saiz Pakej";
"package_type" = "Jenis Pakej";
"mark_as_important" = "Tandakan sebagai Penting";
"select_package_position" = "Pilih Kedudukan Pakej dalam Kenderaan";
"vehicle_area" = "Kawasan Kenderaan";
"left_right_position" = "Kedudukan Kiri/Kanan";
"vehicle_position_front" = "Bahagian Hadapan";
"vehicle_position_middle" = "Bahagian Tengah";
"vehicle_position_back" = "Bahagian Belakang";
"vehicle_position_left" = "Kiri";
"vehicle_position_right" = "Kanan";
"vehicle_position_floor" = "Bahagian Bawah";
"vehicle_position_shelf" = "Bahagian Atas";
"height_position" = "Kedudukan Ketinggian";
"delivery_type" = "Jenis Penghantaran";
"delivery_status" = "Status Penghantaran";
"order_info" = "Maklumat Pesanan";
"order_information" = "Maklumat Pesanan";
"order_number" = "Nombor Pesanan";
"enter_order_number" = "Masukkan nombor pesanan";
"tracking_number" = "Nombor Penjejakan";
"enter_tracking_number" = "Masukkan nombor penjejakan";
"time_info" = "Maklumat Masa";
"time_information" = "Maklumat Masa";
"estimated_arrival_time" = "Anggaran Masa Tiba";
"anytime" = "Bila-bila Masa";
"stop_time" = "Masa Perhentian";
"minutes_format" = "%d minit";
"photo_record" = "Rekod Foto";
"door_number_photo" = "Foto Nombor Pintu";
"package_label_photo" = "Foto Label Pakej";
"placement_photo" = "Foto Penempatan";
"door_number_desc" = "Sila ambil foto nombor pintu atau nombor jalan yang jelas, pastikan nombor/huruf kelihatan";
"package_label_desc" = "Sila ambil foto label pakej, pastikan maklumat penerima kelihatan dengan jelas";
"placement_desc" = "Sila ambil foto kedudukan akhir penempatan pakej";
"photo_captured" = "Foto telah diambil";
"photo_captured_options" = "Foto telah diambil, adakah anda mahu meneruskan mengambil foto seterusnya atau selesaikan foto semasa?";
"continue_to_next_photo" = "Teruskan ambil seterusnya - %@";
"retake" = "Ambil Semula";
"tap_to_capture" = "Ketik untuk Ambil";
"flash_auto" = "Denyar Auto";
"flash_on" = "Hidupkan Denyar";
"flash_off" = "Matikan Denyar";
"photo_record_completed" = "Rekod Foto Selesai";
"photo_confirmation" = "Pengesahan Ambil Foto";
"error" = "Ralat";
"ok" = "OK";
"complete_photo_capture" = "Selesai Ambil Foto";
"tap_to_capture" = "Ketik untuk Ambil";
"photo_instructions" = "Ketik setiap kad foto untuk mengambil gambar. Semua foto mesti diselesaikan.";
"photo_options" = "Pilihan Foto";
"view_photo" = "Lihat Foto";
"retake_photo" = "Ambil Semula";
"saving_photos" = "Menyimpan foto...";
"completed" = "Selesai";
"not_taken" = "Belum Diambil";
"route_options" = "Pilihan Laluan";
"avoid_tolls" = "Elakkan Jalan Tol";
"avoid_highways" = "Elakkan Lebuh Raya";
"optimize_route" = "Optimumkan Laluan";
"optimizing" = "Mengoptimumkan...";
"optimization_complete" = "Pengoptimuman Selesai";

// MARK: - Addresses
"addresses" = "Alamat";
"add_address" = "Tambah Alamat";
"edit_address" = "Edit Alamat";
"delete_address" = "Padam Alamat";
"address_details" = "Butiran Alamat";
"street" = "Jalan";
"city" = "Bandar";
"state" = "Negeri";
"country" = "Negara";
"postal_code" = "Poskod";
"phone" = "Telefon";
"email" = "E-mel";
"website" = "Laman Web";
"company" = "Syarikat";
"notes" = "Nota";
"coordinates" = "Koordinat";
"latitude" = "Latitud";
"longitude" = "Longitud";
"geocoding_error" = "Ralat Geokod";
"address_validation" = "Pengesahan Alamat";
"invalid_addresses" = "Alamat Tidak Sah";
"fix_addresses" = "Betulkan Alamat";

// MARK: - Routes
"route" = "Laluan";
"routes" = "Laluan";
"select_address_point" = "Pilih Titik Alamat";
"select_delivery_points" = "Pilih Titik Penghantaran";
"create_delivery_route" = "Cipta Laluan Penghantaran";
"view_saved_routes" = "Lihat Laluan Tersimpan";
"create_route" = "Cipta Laluan";
"edit_route" = "Edit Laluan";
"delete_route" = "Padam Laluan";
"route_name" = "Nama Laluan";
"route_details" = "Butiran Laluan";
"selected_addresses" = "%d alamat dipilih";
"reached_limit" = "Telah mencapai had";
"can_select_more" = "Boleh pilih %d lagi";
"navigate_button" = "Navigasi";
"create_group" = "Cipta Kumpulan";
"start_point" = "Titik Mula";
"end_point" = "Titik Tamat";
"waypoints" = "Titik Laluan";
"total_distance" = "Jumlah Jarak";
"estimated_time" = "Anggaran Masa";
"route_summary" = "Ringkasan Laluan";
"route_options" = "Pilihan Laluan";
"route_saved" = "Laluan Disimpan";
"route_optimized" = "Laluan Dioptimumkan";
"optimizing_route" = "Mengoptimumkan Laluan...";
"completed_percent" = "Selesai %d%%";
"processing_points" = "Memproses: %d/%d";
"estimated_remaining_time" = "Anggaran Masa Berbaki: %@";

// MARK: - Delivery
"delivery" = "Penghantaran";
"delivery_confirmation" = "Pengesahan Penghantaran";
"take_photo" = "Ambil Foto";
"signature" = "Tandatangan";
"delivery_notes" = "Nota Penghantaran";
"delivery_status" = "Status Penghantaran";
"delivered" = "Telah Dihantar";
"not_delivered" = "Belum Dihantar";
"delivery_time" = "Masa Penghantaran";
"delivery_date" = "Tarikh Penghantaran";
"package_details" = "Butiran Pakej";
"package_id" = "ID Pakej";
"package_weight" = "Berat Pakej";
"package_dimensions" = "Dimensi Pakej";
"recipient_name" = "Nama Penerima";
"recipient_phone" = "Telefon Penerima";

// MARK: - Groups
"groups" = "Kumpulan";
"saved_groups" = "Kumpulan Tersimpan";
"create_group" = "Cipta Kumpulan";
"edit_group" = "Edit Kumpulan";
"delete_group" = "Padam Kumpulan";
"group_name" = "Nama Kumpulan";
"group_details" = "Butiran Kumpulan";
"auto_grouping" = "Kumpulan Auto";
"group_by" = "Kumpulan Mengikut";
"add_to_group" = "Tambah ke Kumpulan";
"remove_from_group" = "Keluarkan dari Kumpulan";
"group_created" = "Kumpulan Dicipta";
"default_group_name_format" = "Kumpulan%d";
"auto_grouping_completed" = "Kumpulan Auto Selesai";
"auto_grouping_in_progress" = "Kumpulan Auto Sedang Berjalan...";
"create_group_every_14_addresses" = "Cipta kumpulan untuk setiap 14 alamat";
"create_delivery_group" = "Cipta Kumpulan Penghantaran";
"enter_group_name" = "Masukkan nama kumpulan";
"selected_delivery_points" = "Titik penghantaran terpilih";
"drag_to_adjust_order" = "Seret untuk laraskan susunan";

// MARK: - Subscription
"subscription" = "Langganan";
"free_plan" = "Versi Percuma";
"pro_plan" = "Versi Pro";
"expert_plan" = "Versi Pakar";
"monthly" = "Pelan Bulanan";
"yearly" = "Pelan Tahunan";
"subscribe" = "Langganan";
"upgrade" = "Naik Taraf";
"upgrade_to_pro" = "Naik Taraf ke Pro";
"manage_subscription" = "Urus Langganan";
"restore_purchases" = "Pulihkan Pembelian";
"subscription_benefits" = "Faedah Langganan";
"free_trial" = "Percubaan Percuma";
"price_per_month" = "%@ sebulan";
"price_per_year" = "%@ setahun";
"save_percent" = "Jimat %@%";
"current_plan" = "Pelan Semasa";
"subscription_terms" = "Terma Langganan";
"privacy_policy" = "Dasar Privasi";
"terms_of_service" = "Terma Perkhidmatan";

// MARK: - Import/Export
"import" = "Import";
"export" = "Eksport";
"import_addresses" = "Import Alamat";
"export_addresses" = "Eksport Alamat";
"import_from_file" = "Import dari Fail";
"export_to_file" = "Eksport ke Fail";
"file_format" = "Format Fail";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Berjaya mengimport %d alamat, semuanya dengan koordinat yang sah.";
"export_success" = "Eksport Berjaya";
"import_error" = "Ralat Import";
"export_error" = "Ralat Eksport";

// MARK: - Navigation
"navigate" = "Navigasi";

// MARK: - Look Around
"show_look_around" = "Lihat Imej Sebenar";
"hide_look_around" = "Sembunyikan Imej Sebenar";

// MARK: - Map
"map" = "Peta";
"map_type" = "Jenis Peta";
"standard" = "Standard";
"satellite" = "Satelit";
"hybrid" = "Hibrid";
"show_traffic" = "Tunjukkan Status Trafik";
"current_location" = "Lokasi Semasa";
"directions" = "Panduan Laluan";
"distance_to" = "Jarak";
"eta" = "Anggaran Masa Tiba";
"look_around" = "Lihat Sekeliling";
"locating_to_glen_waverley" = "Mencari ke Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Ralat Rangkaian";
"location_error" = "Ralat Lokasi";
"permission_denied" = "Kebenaran Ditolak";
"location_permission_required" = "Memerlukan Kebenaran Lokasi";
"camera_permission_required" = "Memerlukan Kebenaran Kamera";
"photo_library_permission_required" = "Memerlukan Kebenaran Perpustakaan Foto";
"please_try_again" = "Sila Cuba Lagi";
"something_went_wrong" = "Berlaku Ralat";
"invalid_input" = "Input Tidak Sah";
"required_field" = "Medan Wajib";
"no_internet_connection" = "Tiada Sambungan Rangkaian";
"server_error" = "Ralat Pelayan";
"timeout_error" = "Permintaan Tamat Masa";
"data_not_found" = "Data Tidak Ditemui";
"selection_limit_reached" = "Had Pemilihan Dicapai";
"selection_limit_description" = "Anda boleh memilih maksimum %d alamat, anda telah memilih %d";

// MARK: - Location Validation Status
"location_status_valid" = "Julat Sah";
"location_status_warning" = "Julat Amaran";
"location_status_invalid" = "Lokasi Tidak Sah";
"location_status_unknown" = "Status Tidak Diketahui";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Tidak sah: Koordinat sifar (0,0)";
"coordinates_invalid_nan" = "Tidak sah: Koordinat bukan nombor";
"coordinates_out_of_range" = "Tidak sah: Koordinat melebihi julat sah";
"coordinates_far_from_user" = "Amaran: Lokasi jauh dari lokasi semasa anda";
"coordinates_ocean" = "Amaran: Lokasi mungkin di laut atau kawasan tidak berpenghuni";

// MARK: - Batch Address Input
"batch_add_addresses" = "Tambah Alamat Secara Berkelompok";
"batch_address_input_placeholder" = "Sila masukkan atau tampal alamat, satu alamat setiap baris. Maksimum 35 alamat.";
"free_address_limit" = "Had Alamat Versi Percuma";
"address_count_limit" = "Had Bilangan Alamat";
"free_version_max_addresses" = "Versi percuma membenarkan maksimum %d alamat.";
"current_addresses_remaining" = "Kini terdapat %d alamat, hanya boleh menambah %d alamat lagi.";
"current_route_address_limit" = "Laluan semasa mempunyai %d alamat, hanya boleh menambah %d alamat lagi, jumlah maksimum %d alamat.";
"selected_addresses_can_import" = "Anda telah memilih %d alamat, boleh import alamat ini.";
"selected_addresses_exceeds" = "Anda telah memilih %d alamat, melebihi bilangan yang boleh ditambah sebanyak %d.";
"selected_addresses_all_importable" = "Anda telah memilih %d alamat, semua boleh diimport.";
"upgrade_for_unlimited_addresses" = "Naik taraf ke versi premium untuk alamat tanpa had!";
"import_first_n_addresses" = "Hanya import %d yang pertama";
"import_all_addresses" = "Import semua alamat";
"import_selected_addresses" = "Import alamat yang dipilih";
"no_importable_addresses" = "Tiada alamat yang boleh diimport, sila semak had alamat";
"please_enter_valid_address" = "Sila masukkan sekurang-kurangnya satu alamat yang sah";

// MARK: - File Import
"import_success" = "Berjaya mengimport %d alamat, semuanya dengan koordinat yang sah.";
"import_success_with_warnings" = "成功导入 %d 个地址，其中 %d 个地址坐标正常，%d 个地址有警告。\n\n有警告的地址已标记，可以在导入后手动修复。";

// MARK: - Web Download
"web_download" = "Muat Turun Web";
"supported_formats" = "Format yang Disokong";
"supported_format_csv" = "• Fail CSV: Lajur alamat harus mengandungi alamat lengkap";
"supported_format_json" = "• Data JSON: Array yang mengandungi medan alamat";
"supported_format_text" = "• Teks biasa: Satu alamat setiap baris";
"download_history" = "Sejarah Muat Turun";
"upgrade_to_premium" = "Naik Taraf ke Premium";
"input_address_data_url" = "Masukkan URL data alamat";
"import_result" = "Hasil Import";
"import_addresses" = "Import Alamat";
"downloading" = "Sedang memuat turun...";
"processing_data" = "Sedang memproses data...";
"google_drive_download_failed" = "Muat turun Google Drive gagal";
"second_attempt_invalid_data" = "Percubaan kedua muat turun mengembalikan data tidak sah";
"cannot_parse_json" = "Tidak dapat mengurai data JSON, sila semak format fail";
"cannot_parse_json_with_error" = "Tidak dapat mengurai data JSON: %@";
"cannot_get_address_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"cannot_read_file" = "Tidak dapat membaca fail: %@";
"success" = "Berjaya";
"warning" = "Amaran";
"failed" = "Penghantaran Gagal";
"no_matching_addresses" = "Tiada alamat yang sepadan ditemui";
"no_valid_addresses" = "Tiada alamat sah ditemui";
"confirm" = "Sahkan";
"processing_addresses" = "Memproses alamat...";
"supports_file_types" = "Menyokong fail CSV, TXT dan JSON";
"tap_to_select_file" = "Ketik untuk pilih fail";
"import_addresses" = "Import Alamat";
"company_name_optional" = "Nama Syarikat (Pilihan)";
"input_company_name" = "Masukkan nama syarikat (pilihan)";
"imported_addresses_count" = "Telah import %d alamat";
"select_all" = "Pilih Semua";
"excel_format_not_supported" = "Format Excel tidak disokong";
"no_matching_addresses" = "Tiada alamat yang sepadan ditemui";

// MARK: - Import Limits
"import_failed" = "Import Gagal";
"no_importable_addresses" = "Tiada alamat yang boleh diimport, sila semak had alamat";
"free_version_address_limit" = "Versi percuma membenarkan maksimum %d alamat.";
"current_address_count" = "Kini terdapat %d alamat, hanya boleh menambah %d alamat lagi.";
"can_import_selected" = "Anda telah memilih %d alamat, boleh import alamat ini.";
"selected_exceeds_limit" = "Anda telah memilih %d alamat, melebihi bilangan yang boleh ditambah sebanyak %d.";
"upgrade_to_premium_unlimited" = "Naik taraf ke versi premium untuk alamat tanpa had!";
"route_address_limit" = "Laluan semasa mempunyai %d alamat, hanya boleh menambah %d alamat lagi, jumlah maksimum %d alamat.";
"free_version_limit" = "Had Alamat Versi Percuma";
"address_count_limit" = "Had Bilangan Alamat";
"import_selected_addresses" = "Import alamat yang dipilih";
"import_first_n" = "Hanya import %d yang pertama";
"import_all_n" = "Import semua %d";
"cannot_import" = "Tidak dapat import";
"select_at_least_one" = "Sila pilih sekurang-kurangnya satu alamat";

// MARK: - Import Results
"no_valid_addresses_found" = "Tiada alamat sah ditemui";
"import_success_all_valid" = "Berjaya import %d alamat, semua koordinat alamat normal.";
"import_success_some_warnings" = "Berjaya import %d alamat, %d alamat koordinat normal, %d alamat tidak dapat memperoleh koordinat.";

// MARK: - Warnings
"invalid_csv_row" = "Baris CSV tidak sah";
"distance_warning" = "Jarak melebihi 200km dari lokasi semasa";
"not_in_australia" = "Koordinat tidak dalam julat Australia";
"cannot_get_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"empty_address" = "Alamat kosong";
"invalid_address_data" = "Data alamat tidak sah";

// MARK: - Saved Groups
"saved_groups" = "Kumpulan Tersimpan";
"no_saved_groups" = "Tiada kumpulan tersimpan";
"select_points_create_groups" = "Pilih titik penghantaran dan cipta kumpulan untuk pengurusan mudah";
"group_name" = "Nama Kumpulan";
"group_details" = "Butiran Kumpulan";
"navigate_to_these_points" = "Navigasi ke titik-titik ini";
"confirm_remove_address" = "Adakah anda pasti mahu mengeluarkan alamat \"%@\" dari kumpulan?";
"confirm_remove_this_address" = "Adakah anda pasti mahu mengeluarkan alamat ini dari kumpulan?";
"addresses_count" = "%d alamat";
"no_saved_routes" = "Tiada laluan tersimpan";
"no_saved_routes_description" = "Anda belum menyimpan sebarang laluan";
"all_routes" = "Semua Laluan";
"address_count_format_simple" = "%d alamat";
"delete_all_routes" = "Padam Semua Laluan";
"navigate_to_all_points" = "Navigasi ke semua titik";
"confirm_navigate_to_route" = "Adakah anda pasti mahu navigasi ke semua titik dalam laluan \"%@\"?";
"temp_navigation_group" = "Kumpulan Navigasi Sementara";

// MARK: - Route Management
"route_management" = "Pengurusan Laluan";
"route_info" = "Maklumat Laluan";
"route_name" = "Nama Laluan";
"route_addresses" = "Alamat Laluan";
"no_addresses_in_route" = "Laluan ini tiada alamat";
"must_keep_one_route" = "Mesti mengekalkan sekurang-kurangnya satu laluan";
"confirm_delete_route" = "Adakah anda pasti mahu memadam laluan \"%@\"? Tindakan ini tidak boleh dibuat asal.";
"confirm_delete_all_routes" = "Sahkan Padam Semua Laluan";
"confirm_delete_all_routes_message" = "Adakah anda pasti mahu memadam semua laluan? Tindakan ini tidak boleh dibuat asal.";
"delete_all" = "Padam Semua";

// MARK: - Navigation Buttons
"navigate" = "Navigasi";

// MARK: - GroupDetailView
"points_count_format" = "%d titik";

// MARK: - DeliveryPointDetailView
"address_information" = "Maklumat Alamat";
"group_belonging" = "Kumpulan Kepunyaan";
"view_map" = "Lihat Peta";
"delivery_status" = "Status Penghantaran";
"notes" = "Nota";
"delete_delivery_point" = "Padam Titik Penghantaran";
"delivery_point_details" = "Butiran Titik Penghantaran";
"confirm_deletion" = "Sahkan Padam";
"delete_delivery_point_confirmation" = "Adakah anda pasti mahu memadam titik penghantaran ini? Tindakan ini tidak boleh dibuat asal.";

// MARK: - Delivery Photos
"delivery_photos" = "Foto Penghantaran";
"view_delivery_photos" = "Lihat Foto Penghantaran";
"no_photos_taken" = "Belum mengambil foto";
"take_photos" = "Ambil Foto";
"loading_photos" = "Sedang memuatkan foto...";
"photo_not_found" = "Foto tidak ditemui";
"photo_deleted" = "Foto telah dipadam";
"door_number_photo" = "Foto Nombor Pintu";
"package_label_photo" = "Foto Label Pakej";
"placement_photo" = "Foto Penempatan";
"share_photos" = "Kongsi Foto";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Pengesahan Ambil Foto";
"door_number_photo_title" = "Foto Nombor Jalan/Pintu";
"package_label_photo_title" = "Foto Label Pakej";
"placement_photo_title" = "Foto Lokasi Penempatan";
"door_number_photo_desc" = "Sila ambil foto nombor pintu yang jelas, pastikan nombor/huruf kelihatan";
"package_label_photo_desc" = "Sila ambil foto label pakej, pastikan maklumat penerima kelihatan dengan jelas";
"placement_photo_desc" = "Sila ambil foto kedudukan akhir penempatan pakej";
"swipe_to_switch" = "Leret untuk tukar jenis foto";
"complete_photos" = "Selesai Ambil Foto";
"saving_photos" = "Menyimpan foto...";
"photo_save_success" = "Foto berjaya disimpan";
"photo_save_failure" = "Gagal menyimpan foto";
"retake_photo" = "Ambil Semula";
"no_photos_found" = "Foto tidak ditemui";
"photos_deleted_or_not_taken" = "Foto mungkin telah dipadam atau belum diambil";
"share_photo" = "Kongsi Foto";
"photo_capture_preview" = "Mod pratonton - simulasi kamera";
"photo_capture_close" = "Tutup";
"camera_start_failed" = "Kamera gagal dimulakan";
"camera_start_failed_retry" = "Tidak dapat memulakan kamera, sila cuba lagi";
"camera_init_failed" = "Kamera gagal diinisialisasi";
"camera_access_failed" = "Tidak dapat mengakses kamera";
"photo_processing_failed" = "Gagal mengambil foto";
"photo_processing_failed_retry" = "Tidak dapat menyelesaikan pemprosesan foto, sila cuba lagi";
"photo_capture_progress" = "Kemajuan: %d/%d";
"photo_captured_continue" = "Selesai mengambil, teruskan ambil %@";
"loading_photos" = "Sedang memuatkan foto...";
"cancel" = "Batal";

// MARK: - Delivery Status
"pending" = "Menunggu Penghantaran";
"in_progress" = "Sedang Menghantar";
"completed" = "Selesai";
"failed" = "Penghantaran Gagal";
"update_status" = "Kemas Kini Status";
"select_delivery_status" = "Pilih Status Penghantaran";
"select_failure_reason" = "Pilih Sebab Kegagalan";
"delivered" = "Telah Dihantar";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Pelanggan tidak di rumah";
"failure_reason_wrong_address" = "Alamat salah";
"failure_reason_no_access" = "Tidak dapat masuk ke lokasi";
"failure_reason_rejected" = "Pakej ditolak";
"failure_reason_other" = "Sebab lain";
"enter_custom_reason" = "Masukkan sebab khusus";
"custom_reason_placeholder" = "Sila huraikan sebab khusus...";
"custom_reason_required" = "Sila masukkan sebab khusus";
"failure_reason_required" = "Sila pilih sebab kegagalan";

// MARK: - Address Validation
"address_validation_failed" = "Pengesahan alamat gagal";

