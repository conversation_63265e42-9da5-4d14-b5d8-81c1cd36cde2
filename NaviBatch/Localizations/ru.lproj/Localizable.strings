/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Настройки языка";
"system_language" = "Системный язык";
"system_language_section" = "Системные настройки";
"languages" = "Языки";
"language_info_title" = "О настройках языка";
"language_info_description" = "После изменения языковых настроек приложение будет отображать текст на выбранном языке. Для некоторого контента может потребоваться перезапуск приложения для полного применения новых языковых настроек.";
"restart_required" = "Требуется перезапуск";
"restart_app_message" = "Для полного применения изменений языка, пожалуйста, перезапустите приложение.";
"restart_now" = "Перезапустить сейчас";
"restart_later" = "Перезапустить позже";
"close" = "Закрыть";
"cancel" = "Отмена";
"save" = "Сохранить";
"edit" = "Редактировать";
"delete" = "Удалить";
"done" = "Готово";
"next" = "Далее";
"back" = "Назад";
"confirm" = "Подтвердить";
"error" = "Ошибка";
"success" = "Успешно";
"warning" = "Предупреждение";
"unknown_error" = "Неизвестная ошибка";
"loading" = "Загрузка...";
"search" = "Поиск";
"settings" = "Настройки";
"help" = "Помощь";
"about" = "О приложении";
"menu" = "Меню";
"understand" = "Понятно";
"navigation" = "Навигация";
"start_navigation" = "Начать навигацию";
"subscription" = "Подписка";
"upgrade_to_pro" = "Обновить до Pro";
"upgrade_description" = "Группировка навигации в один клик, в 60 раз быстрее ручной операции";
"restore_purchases" = "Восстановить покупки";
"learn_more" = "Узнать больше";
"upgrade_your_plan" = "Обновить ваш план";
"one_click_navigation_description" = "Группировка адресов в один клик для экономии времени и топлива";
"current_plan" = "Текущий план";
"upgrade" = "Обновить";
"maybe_later" = "Возможно позже";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Бесплатно";
"pro_tier_price" = "$9.99/месяц";
"expert_tier_price" = "$59.99/год";
"free_tier_description" = "Подходит для частных лиц и малого бизнеса с небольшим количеством точек остановки";
"pro_tier_description" = "Для пользователей, которым требуется навигация в один клик и неограниченное количество адресов";
"expert_tier_description" = "То же, что и Pro, но с экономией 50% при годовом плане";
"route_optimization" = "Оптимизация маршрута";
"unlimited_routes" = "Неограниченное количество маршрутов";
"unlimited_optimizations" = "Неограниченные оптимизации";
"max_15_addresses" = "Максимум 15 адресов на маршрут";
"save_fuel_30" = "Экономия топлива до 30%";
"unlimited_addresses" = "✨ Неограниченное количество адресов ✨";
"one_click_navigation" = "⚡ Навигация в один клик - в 60 раз быстрее ⚡";
"package_finder" = "Поиск посылок";
"annual_savings" = "Годовая экономия";
"switched_to_free" = "Переключено на бесплатный план";
"switched_to_subscription" = "Переключено на план с подпиской";
"unlimited_stops" = "Неограниченные остановки";
"plan_as_many_stops_as_needed" = "Добавляйте любое количество точек доставки без ограничений";
"expires_in_days" = "Истекает через %d дней";
"trial_expires_in_days" = "Пробный период истекает через %d дней";
"expired" = "Истёк";
"subscription_expires_on" = "Подписка истекает %@";
"subscription_active_until" = "Подписка активна до %@";
"enter_or_search_address" = "Введите или найдите адрес";
"search_results_count" = "Результаты поиска: %d";
"no_matching_addresses" = "Соответствующих адресов не найдено";
"search_address_failed" = "Ошибка поиска адреса: %@";
"address_search_no_response" = "Нет ответа при поиске адреса";
"cannot_get_address_coordinates" = "Не удается получить координаты адреса";

"cannot_get_coordinates_retry" = "Не удается получить координаты адреса. Пожалуйста, введите вручную или повторите попытку";

"image_address_recognition" = "Распознавание адресов из изображений";
"select_images" = "Выбрать изображения";
"select_multiple_images" = "Поддерживается множественный выбор";
"processing_images" = "Обработка изображений...";
"processing_image_progress" = "Обработка изображения %d из %d";
"recognizing_text" = "Распознавание текста...";
"geocoding_addresses" = "Получение координат адресов...";
"recognition_complete" = "Распознавание завершено";
"no_text_recognized" = "Текст не распознан";
"no_addresses_found" = "Действительные адреса не найдены";
"image_recognition_failed" = "Распознавание изображения не удалось";
"image_recognition_error" = "Ошибка распознавания изображения: %@";
"text_recognition_failed" = "Распознавание текста не удалось";
"address_parsing_failed" = "Анализ адреса не удался";
"select_addresses_to_add" = "Выберите адреса для добавления";
"recognized_addresses" = "Распознанные адреса";
"address_coordinates" = "Координаты адреса";
"toggle_address_selection" = "Переключить выбор адреса";
"remove_address" = "Удалить адрес";
"confirm_selected_addresses" = "Подтвердить выбранные адреса";
"no_addresses_selected" = "Адреса не выбраны";
"image_processing_cancelled" = "Обработка изображения отменена";
"unsupported_image_format" = "Неподдерживаемый формат изображения";
"image_too_large" = "Файл изображения слишком большой";
"image_recognition_permission_required" = "Требуется разрешение доступа к галерее фотографий";
"ocr_language_detection" = "Автоматическое определение языка";
"improve_image_quality" = "Убедитесь, что изображение четкое и текст видимый";
"address_validation_in_progress" = "Валидация адресов в процессе...";
"batch_address_import" = "Пакетный импорт адресов";
"validated_addresses_count" = "%d адресов проверено";
"addresses_with_issues" = "%d адресов с проблемами";
"select_all" = "Выбрать все";
"import_selected" = "Импортировать выбранные";
"validating_addresses" = "Проверка адресов...";
"empty_address" = "Пустой адрес";
"invalid_coordinates" = "Недействительные координаты";
"coordinate_warning" = "Предупреждение о координатах";
"address_validation_issue" = "Проблема валидации адреса";
"cannot_get_coordinates" = "Не удается получить координаты адреса";
"no_importable_addresses" = "Нет адресов для импорта, проверьте ограничения адресов";
"free_version_max_addresses" = "Бесплатная версия позволяет максимум %d адресов.";
"valid" = "Действительный";
"with_issues" = "С проблемами";
"low_confidence_address" = "Валидация адреса с низкой уверенностью";
"address_validation_failed" = "Проверка адреса не удалась";
"current_addresses_remaining" = "В настоящее время у вас %d адресов, можно добавить еще %d адресов.";
"can_import_selected" = "Вы выбрали %d адресов, можно импортировать эти адреса.";
"selected_exceeds_limit" = "Вы выбрали %d адресов, превышая допустимое количество на %d.";
"selected_addresses_all_importable" = "Вы выбрали %d адресов, все можно импортировать.";
"upgrade_for_unlimited_addresses" = "Обновитесь до премиум для неограниченных адресов!";
"current_route_address_limit" = "Текущий маршрут имеет %d адресов, можно добавить еще %d адресов, всего максимум %d адресов.";
"import_all_addresses" = "Импортировать все адреса";
"import_first_n" = "Импортировать только первые %d";
"import_selected_addresses" = "Импортировать выбранные адреса";
"upgrade_to_premium" = "Обновиться до премиум";
"batch_add_addresses" = "Массовое добавление адресов";
"batch_address_input_placeholder" = "Введите или вставьте адреса, по одному адресу на строку. Максимум 35 адресов.";
"search_address" = "Поиск адреса";
"no_saved_addresses" = "Нет сохраненных адресов";
"no_saved_addresses_description" = "У вас еще нет сохраненных адресов, или ни один адрес не соответствует условиям фильтра";
"select_address_book" = "Выберите адресную книгу";
"routes" = "Маршруты";
"address_book" = "Адресная книга";
"saved_routes" = "Сохраненные маршруты";
"manage_your_routes" = "Управляйте планированием маршрутов";
"manage_your_addresses" = "Управляйте часто используемыми адресами";
"preferences" = "Предпочтения";
"set_custom_start_point" = "Установить пользовательскую начальную точку";
"current_start_point" = "Текущая начальная точка: %@";
"support" = "Поддержка";
"contact_us" = "Свяжитесь с нами";
"contact_us_description" = "Есть вопросы или предложения? Не стесняйтесь обращаться!";
"help_center" = "Центр помощи";
"quick_actions" = "Быстрые действия";
"main_features" = "Основные функции";
"support_help" = "Поддержка и помощь";
"customize_app_settings" = "Настроить параметры приложения";
"unlock_all_features" = "Разблокировать все функции";
"get_help_support" = "Получить помощь и поддержку";
"app_info_version" = "Информация о приложении и версия";
"dev_tools" = "Инструменты разработчика";
"debug_testing_tools" = "Инструменты отладки и тестирования";
"version" = "Версия";
"addresses" = "Адреса";
"limited_to_20_addresses" = "Ограничено 20 адресами";
"all_premium_features" = "Все премиум функции";
"open_subscription_view" = "Открыть окно подписки";
"open_subscription_view_description" = "Пропустить промежуточный слой и показать окно подписки";
"restore_purchases_failed" = "Не удалось восстановить покупки: %@";
"rate_us" = "Оцените нас";
"rate_us_description" = "Ваш отзыв важен для нас и помогает улучшить приложение!";
"share_app" = "Поделиться приложением";
"share_app_text" = "Попробуйте NaviBatch, удивительное приложение для планирования маршрутов!";
"about_app" = "О приложении";
"developer_tools" = "Инструменты разработчика";
"coordinate_debug_tool" = "Инструмент отладки координат";
"batch_fix_addresses" = "Пакетное исправление адресов";
"clear_database" = "Очистить базу данных";
"clear_database_confirmation" = "Это удалит все данные, включая маршруты, адреса и группы. Это действие нельзя отменить. Вы уверены, что хотите продолжить?";
"confirm_clear" = "Подтвердить очистку";
"version_info" = "Версия %@ (%@)";
"current_system_language" = "Текущий системный язык";
"reset_to_system_language" = "Сбросить на системный язык";
"language" = "Язык";
"address" = "Адрес";
"coordinates" = "Координаты";
"distance_from_current_location" = "Расстояние от текущего местоположения";
"address_info" = "Информация об адресе";
"update_coordinates" = "Обновить координаты";
"fix_address" = "Исправить адрес";
"prompt" = "Подсказка";
"kilometers_format" = "%.1f км";
"meters_format" = "%.0f м";
"modify_address_first" = "Пожалуйста, измените адрес перед обновлением координат";
"coordinates_update_success" = "Координаты успешно обновлены";
"coordinates_update_failure" = "Не удалось обновить координаты";
"save_failure" = "Ошибка сохранения: %@";
"no_saved_addresses_title" = "Нет сохраненных адресов";
"no_saved_addresses_message" = "Вы еще не сохранили ни одного адреса";
"add_new_address" = "Добавить новый адрес";
"address_title" = "Адрес";
"add" = "Добавить";
"refresh" = "Обновить";
"notes" = "Примечания";
"address_details" = "Детали адреса";
"favorite" = "Избранное";
"edit_address" = "Редактировать адрес";
"confirm_delete" = "Подтвердить удаление";
"delete_address_confirmation" = "Вы уверены, что хотите удалить этот адрес? Это действие нельзя отменить.";
"address_marker" = "Адрес";
"address_label" = "Адрес:";
"notes_label" = "Примечания:";
"created_at_label" = "Создано:";
"open_in_maps" = "Открыть в картах";
"copy_address" = "Копировать адрес";
"address_details_title" = "Детали адреса";
"start_end_point" = "Начальная/конечная точка";
"start_point" = "Начальная точка";
"end_point" = "Конечная точка";
"route_info" = "Информация о маршруте";
"address_count" = "Количество адресов";
"address_count_format" = "%d адресов";
"points_count_format" = "%d точек";
"additional_points_format" = "+%d точек";
"export_route" = "Экспорт маршрута";
"navigate" = "Навигация";
"address_list" = "Список адресов";
"no_addresses" = "Нет адресов";
"no_addresses_message" = "У этого маршрута еще нет адресов";
"address_point_start" = "Начальная точка";
"address_point_stop" = "Точка остановки";
"address_point_end" = "Конечная точка";
"route_name" = "Название маршрута";
"new_route" = "Новый маршрут";
"saved_route" = "Сохраненный маршрут";
"plan_route" = "Планировать маршрут";
"clear_all" = "Очистить все";
"avoid" = "Избегать:";
"toll_roads" = "Платные дороги";
"highways" = "Автомагистрали";
"processing_addresses" = "Обработка адресов...";
"same_start_end_point" = "Вы установили один и тот же адрес в качестве начальной и конечной точки";
"add_start_point" = "Добавить начальную точку";
"swipe_left_to_delete" = "Долгое нажатие для удаления";
"add_end_point" = "Добавить конечную точку";
"enter_and_select_address" = "Введите и выберите адрес";
"current_search_text" = "Текущий текст поиска: %@";
"add_address" = "Добавить адрес";
"selected_coordinates" = "Выбранные координаты";
"company_name_optional" = "Название компании (необязательно)";
"url_optional" = "URL (необязательно)";
"favorite_address" = "Избранный адрес";
"set_as_start_and_end" = "Установить как начальную и конечную точку";
"batch_paste" = "Пакетная вставка";
"file_import" = "Импорт файла";
"web_download" = "Загрузка из интернета";
"saving" = "Сохранение...";
"delivery_point_management" = "Управление точками доставки";
"information_category" = "Категория информации";
"package_info" = "Информация о посылке";
"vehicle_position" = "Положение транспортного средства";
"delivery_info" = "Информация о доставке";
"take_photo_record" = "Сделать фото";
"update_status" = "Обновить статус";
"edit_address_button" = "Редактировать адрес";
"access_instructions" = "Инструкции по доступу";
"add_access_instructions" = "Добавить инструкции по доступу...";
"package_count" = "Количество посылок";
"package_count_format" = "%d посылок";
"packages" = "посылки";
"package_unit" = "шт";
"package_size" = "Размер посылки";
"package_type" = "Тип посылки";
"mark_as_important" = "Отметить как важное";
"priority_delivery" = "Приоритетная доставка";
"priority_level" = "Уровень приоритета";
"priority_1" = "Приоритет 1";
"priority_2" = "Приоритет 2";
"priority_3" = "Приоритет 3";
"no_priority" = "Без приоритета";
"no_priority_short" = "Нет";
"set_priority" = "Установить приоритет";
"select_package_position" = "Выберите положение посылки в транспортном средстве";
"vehicle_area" = "Область транспортного средства";
"left_right_position" = "Положение слева/справа";
"vehicle_position_front" = "Передняя часть";
"vehicle_position_middle" = "Средняя часть";
"vehicle_position_back" = "Задняя часть";
"vehicle_position_left" = "Слева";
"vehicle_position_right" = "Справа";
"vehicle_position_floor" = "Нижняя часть";
"vehicle_position_shelf" = "Верхняя часть";
"height_position" = "Высотное положение";
"vehicle_position_none" = "Позиция не выбрана";
"delivery_type" = "Тип доставки";
"delivery_status" = "Статус доставки";
"order_info" = "Информация о заказе";
"order_information" = "Информация о заказе";
"order_number" = "Номер заказа";
"enter_order_number" = "Введите номер заказа";
"tracking_number" = "Номер отслеживания";
"enter_tracking_number" = "Введите номер отслеживания";
"tracking_info" = "Информация об отслеживании";
"tracking_information" = "Информация об отслеживании";
"time_info" = "Информация о времени";
"time_information" = "Информация о времени";
"estimated_arrival_time" = "Ожидаемое время прибытия";
"anytime" = "В любое время";
"stop_time" = "Время остановки";
"minutes_format" = "%d минут";
"photo_record" = "Фотозапись";
"door_number_photo" = "Фото номера двери";
"package_label_photo" = "Фото этикетки посылки";
"placement_photo" = "Фото размещения";
"door_number_desc" = "Сделайте четкое фото номера дома или улицы, убедитесь, что цифры/буквы видны";
"package_label_desc" = "Сделайте фото этикетки посылки, убедитесь, что информация получателя четко видна";
"placement_desc" = "Сделайте фото окончательного места размещения посылки";
"photo_captured" = "Фото сделано";
"photo_captured_options" = "Фото сделано, хотите продолжить со следующим фото или завершить текущее?";
"continue_to_next_photo" = "Продолжить со следующим - %@";
"retake" = "Переснять";
"tap_to_capture" = "Нажмите для съемки";
"flash_auto" = "Автовспышка";
"flash_on" = "Включить вспышку";
"flash_off" = "Выключить вспышку";
"photo_record_completed" = "Фотозапись завершена";
"photo_confirmation" = "Подтверждение фото";
"ok" = "ОК";
"complete_photo_capture" = "Завершить фотосъемку";
"photo_instructions" = "Нажмите на каждую карточку фото для съемки. Все фото должны быть завершены.";
"photo_options" = "Параметры фото";
"view_photo" = "Просмотр фото";
"retake_photo" = "Переснять";
"saving_photos" = "Сохранение фото...";
"completed" = "Завершено";
"not_taken" = "Не сделано";
"route_options" = "Параметры маршрута";
"avoid_tolls" = "Избегать платных дорог";
"avoid_highways" = "Избегать автомагистралей";
"optimize_route" = "Оптимизировать маршрут";
"optimizing" = "Оптимизация...";
"optimization_complete" = "Оптимизация завершена";
"route_optimization_results" = "Результаты";
"route_planning_options" = "Параметры планирования маршрута";
"before_optimization" = "До оптимизации";
"after_optimization" = "После оптимизации";
"auto_group" = "Автогруппировка";
"optimized_route_order" = "Порядок";
"apply" = "Применить";
"kilometers" = "километры";
"street_number_issue_warning" = "⚠️ Большая разница в номере улицы! Это может привести к доставке по неправильному адресу и штрафам. Проверьте адрес немедленно";
"address_validation_critical" = "Критическая проблема валидации адреса";
"street_number_difference_high_risk" = "Большая разница в номере улицы, высокий риск";
"delete_address" = "Удалить адрес";
"street" = "Улица";
"city" = "Город";
"state" = "Область/штат";
"country" = "Страна";
"postal_code" = "Почтовый индекс";
"phone" = "Телефон";
"email" = "Эл. почта";
"website" = "Веб-сайт";
"company" = "Компания";
"latitude" = "Широта";
"longitude" = "Долгота";
"geocoding_error" = "Ошибка геокодирования";
"address_validation" = "Проверка адреса";
"invalid_addresses" = "Недействительные адреса";
"fix_addresses" = "Исправить адреса";
"route" = "Маршрут";
"select_address_point" = "Выберите адресную точку";
"select_delivery_points" = "Выберите точки доставки";
"create_delivery_route" = "Создать маршрут доставки";
"view_saved_routes" = "Просмотр сохраненных маршрутов";
"create_route" = "Создать маршрут";
"edit_route" = "Редактировать маршрут";
"delete_route" = "Удалить маршрут";
"route_details" = "Детали маршрута";
"selected_addresses" = "Выбрано %d адресов";
"reached_limit" = "Достигнут лимит";
"can_select_more" = "Можно выбрать еще %d";
"navigate_button" = "Навигация";
"create_group" = "Создать группу";
"waypoints" = "Промежуточные точки";
"total_distance" = "Общее расстояние";
"estimated_time" = "Расчетное время";
"route_summary" = "Сводка по маршруту";
"route_saved" = "Маршрут сохранен";
"route_optimized" = "Маршрут оптимизирован";
"optimizing_route" = "Оптимизация маршрута...";
"completed_percent" = "%d%% завершено";
"processing_points" = "Обработка: %d/%d";
"estimated_remaining_time" = "Расчетное оставшееся время: %@";
"delivery" = "Доставка";
"delivery_confirmation" = "Подтверждение доставки";
"take_photo" = "Сделать фото";
"signature" = "Подпись";
"delivery_notes" = "Примечания к доставке";
"delivered" = "Доставлено";
"not_delivered" = "Не доставлено";
"delivery_time" = "Время доставки";
"delivery_date" = "Дата доставки";
"package_details" = "Детали посылки";
"package_id" = "ID посылки";
"package_weight" = "Вес посылки";
"package_dimensions" = "Размеры посылки";
"recipient_name" = "Имя получателя";
"recipient_phone" = "Телефон получателя";
"groups" = "Группы";
"saved_groups" = "Сохраненные группы";
"edit_group" = "Редактировать группу";
"delete_group" = "Удалить группу";
"group_name" = "Название группы";
"group_details" = "Детали группы";
"auto_grouping" = "Автоматическая группировка";
"group_by" = "Группировать по";
"add_to_group" = "Добавить в группу";
"remove_from_group" = "Удалить из группы";
"group_created" = "Группа создана";
"default_group_name_format" = "Группа%d";
"auto_grouping_completed" = "Автоматическая группировка завершена";
"auto_grouping_in_progress" = "Выполняется автоматическая группировка...";
"create_group_every_14_addresses" = "Создать группу для каждых 14 адресов";
"create_delivery_group" = "Создать группу доставки";
"enter_group_name" = "Введите название группы";
"selected_delivery_points" = "Выбранные точки доставки";
"drag_to_adjust_order" = "Перетащите для изменения порядка";
"free_plan" = "Бесплатный план";
"pro_plan" = "План Pro";
"expert_plan" = "План Expert";
"monthly" = "Ежемесячно";
"yearly" = "Ежегодно";
"subscribe" = "Подписаться";
"manage_subscription" = "Управление подпиской";
"subscription_benefits" = "Преимущества подписки";
"free_trial" = "Бесплатный период";
"price_per_month" = "%@ в месяц";
"price_per_year" = "%@ в год";
"save_percent" = "Сэкономьте %@%";
"subscription_terms" = "Условия подписки";
"privacy_policy" = "Политика конфиденциальности";
"terms_of_service" = "Условия использования";
"feature_comparison" = "Сравнение функций";
"addresses_per_route" = "Адресов на маршрут";
"max_20_addresses" = "Максимум 20 адресов";
"fuel_savings" = "Экономия топлива";
"up_to_30_percent" = "До 30%";
"choose_subscription_plan" = "Выберите план подписки";
"monthly_plan" = "Месячный план";
"yearly_plan" = "Годовой план";
"/month_suffix" = "/месяц";
"/year_suffix" = "/год";
"save_30_percent" = "Сэкономьте 30%";
"free_trial_7_days_cancel_anytime" = "7 дней бесплатной пробной версии, отмените в любое время";
"subscription_auto_renew_notice" = "Подписка автоматически продлевается";
"and" = "и";
"subscription_exclusive" = "Эксклюзивно для подписчиков";
"free_version_optimization_limit" = "Бесплатная версия с ограниченной оптимизацией";
"free_version_supports_max_addresses" = "Бесплатная версия поддерживает максимальное количество адресов";
"current_route_contains_addresses" = "Текущий маршрут содержит адреса";
"upgrade_to_pro_unlimited_addresses" = "Обновитесь до Pro для неограниченного количества адресов";
"continue_optimization" = "Продолжить оптимизацию";
"upgrade_unlock_one_click_navigation" = "Обновитесь, чтобы разблокировать навигацию одним кликом";
"learn_one_click_navigation_grouping" = "Узнайте о навигации и группировке одним кликом";
"toggle_subscription_status" = "Переключить статус подписки";
"toggle_subscription_description" = "Переключение между подпиской и бесплатной версией для тестирования";
"product_info_unavailable" = "Информация о продукте недоступна";
"purchase_failed" = "Покупка не удалась: %@";
"upgrade_to_pro_version" = "Обновиться до Pro версии";
"unlock_all_premium_features" = "Разблокировать все премиум функции";
"first_7_days_free_cancel_anytime" = "Первые 7 дней бесплатно, отмените в любое время";
"free_version_can_group_addresses" = "Бесплатная версия может группировать %d адресов";
"reached_free_version_limit" = "Достигнут лимит бесплатной версии";
"creating_groups" = "Создание групп...";
"payment_terms_notice" = "Оплата будет списана с вашего Apple ID аккаунта после подтверждения покупки. Подписка автоматически продлевается, если не отменена как минимум за 24 часа до окончания текущего периода.";
"terms_of_use" = "Условия использования";
"product_load_failed_check_connection" = "Не удалось загрузить информацию о продукте, убедитесь, что устройство подключено к интернету и App Store";
"product_load_failed" = "Не удалось загрузить продукт: %@";
"verify_receipt" = "Проверить чек";
"one_click_navigation_short" = "Навигация одним кликом";
"save_30_percent_fuel" = "Сэкономьте 30% топлива";
"monthly_short" = "Месячный";
"yearly_short" = "Годовой";
"upgrade_now" = "Обновиться сейчас";
"test_environment_pro_activated" = "Тестовая среда: Pro активирован";
"payment_terms_notice_detailed" = "Оплата будет списана с вашего Apple ID аккаунта после подтверждения покупки. Подписка автоматически продлевается, если не отменена как минимум за 24 часа до окончания текущего периода. Подписками можно управлять и отменять их в настройках App Store.";
"step_screenshot" = "Скриншот шага %d";
"previous_step" = "Предыдущий";
"next_step" = "Следующий";
"each_address_takes_3_5_seconds" = "Каждый адрес занимает 3-5 секунд для добавления";
"need_repeat_14_times" = "Нужно повторить одну и ту же операцию 14 раз";
"navigation_order_often_confused" = "Порядок навигации часто путается";
"error_prone_need_redo" = "Склонно к ошибкам, нужно переделывать операции";
"address_order_reversed_manual_adjust" = "Порядок адресов обратный, требуется ручная настройка";
"one_click_add_all" = "Один клик, добавить все";
"smart_grouping_auto_sorting" = "Умная группировка, автоматическая сортировка";
"maintain_correct_visit_order" = "Поддерживать правильный порядок посещения";
"zero_errors_zero_repetition" = "Ноль ошибок, ноль повторений";
"import" = "Импорт";
"export" = "Экспорт";
"import_addresses" = "Импорт адресов";
"export_addresses" = "Экспорт адресов";
"import_from_file" = "Импорт из файла";
"export_to_file" = "Экспорт в файл";
"file_format" = "Формат файла";
"csv_format" = "Формат CSV";
"excel_format" = "Формат Excel";
"json_format" = "Формат JSON";
"import_success" = "Успешно импортировано %d адресов, все с действительными координатами.";
"export_success" = "Экспорт успешно выполнен";
"import_error" = "Ошибка импорта";
"export_error" = "Ошибка экспорта";
"navigation_app" = "Навигационное приложение";
"apple_maps" = "Apple Карты";
"app_preferences" = "Настройки приложения";
"distance_unit" = "Единица расстояния";
"current_language" = "Текущий язык";
"info" = "Информация";
"contact_us_header" = "Свяжитесь с нами";
"contact_us_subheader" = "Есть вопросы или предложения? Мы рады помочь!";
"contact_options" = "Варианты связи";
"email_us" = "Напишите нам";
"contact_form" = "Форма обратной связи";
"contact_and_support" = "Контакты и поддержка";
"common_questions" = "Часто задаваемые вопросы";
"how_to_use" = "Как использовать";
"subscription_faq" = "FAQ по подписке";
"navigation_help" = "Помощь по навигации";
"troubleshooting" = "Устранение неполадок";
"help_howto_content" = "NaviBatch - это мощное приложение для планирования маршрутов, которое помогает оптимизировать маршруты доставки, экономя время и топливо. Вы можете добавлять несколько адресов, автоматически оптимизировать порядок маршрута и переходить к Apple Maps одним кликом.";
"help_subscription_content" = "NaviBatch предлагает бесплатную и pro версии. Бесплатная версия поддерживает до 20 адресов, в то время как pro версия предоставляет неограниченное количество адресов и функции групповой навигации одним кликом.";
"help_navigation_content" = "NaviBatch использует Apple Maps для навигации. Вы можете навигировать к каждому адресу отдельно или использовать функцию группировки для навигации к нескольким адресам одновременно.";
"help_troubleshooting_content" = "Если у вас возникли проблемы, сначала убедитесь, что ваше устройство имеет сетевое подключение и разрешения на определение местоположения предоставлены. Если проблемы продолжаются, обратитесь к нашей команде поддержки.";
"actions" = "Действия";
"legal" = "Правовая информация";
"show_look_around" = "Показать обзор улицы";
"hide_look_around" = "Скрыть обзор улицы";
"map" = "Карта";
"map_type" = "Тип карты";
"standard" = "Стандартная";
"satellite" = "Спутник";
"hybrid" = "Гибридная";
"show_traffic" = "Показать движение";
"current_location" = "Текущее местоположение";
"directions" = "Маршрут";
"distance_to" = "Расстояние до";
"eta" = "Расчетное время прибытия";
"look_around" = "Осмотреться";
"locating_to_glen_waverley" = "Определение местоположения Glen Waverley";
"network_error" = "Ошибка сети";
"location_error" = "Ошибка местоположения";
"permission_denied" = "Доступ запрещен";
"location_permission_required" = "Требуется разрешение на определение местоположения";
"camera_permission_required" = "Требуется разрешение на доступ к камере";
"photo_library_permission_required" = "Требуется разрешение на доступ к фотографиям";
"please_try_again" = "Пожалуйста, попробуйте еще раз";
"something_went_wrong" = "Что-то пошло не так";
"invalid_input" = "Неверный ввод";
"required_field" = "Обязательное поле";
"no_internet_connection" = "Нет подключения к интернету";
"server_error" = "Ошибка сервера";
"timeout_error" = "Ошибка тайм-аута";
"data_not_found" = "Данные не найдены";
"selection_limit_reached" = "Достигнут предел выбора";
"selection_limit_description" = "Вы можете выбрать максимум %d адресов, вы выбрали %d адресов";
"location_status_valid" = "Действительный диапазон";
"address_validation_unknown" = "Не проверен";
"address_validation_valid" = "Действительный";
"address_validation_invalid" = "Недействительный";
"address_validation_warning" = "Предупреждение";
"address_validation_mismatch" = "Не совпадает";
"device_not_support_scanning" = "Устройство не поддерживает нативное сканирование";
"requires_ios16_a12_chip" = "Требуется iOS 16+ и чип A12 или новее";
"debug_info" = "Отладочная информация:";
"address_confirmation" = "Подтверждение адреса";
"continue_scanning" = "Продолжить сканирование";
"confirm_add" = "Подтвердить добавление";
"cannot_get_coordinates_scan_retry" = "Не удается получить координаты адреса, введите вручную или отсканируйте снова";
"unknown_country" = "Неизвестная страна";
"unknown_city" = "Неизвестный город";
"please_enter_valid_address" = "Введите хотя бы один действительный адрес";
"please_select_valid_address" = "Выберите действительный адрес";
"add_address_failed" = "Не удалось добавить адрес";
"location_permission_required_for_current_location" = "Требуется разрешение на определение местоположения для получения текущего местоположения";
"cannot_get_current_location_check_settings" = "Не удается получить текущее местоположение, проверьте настройки";
"cannot_get_current_location_address" = "Не удается получить адрес текущего местоположения";
"get_current_location_failed" = "Не удалось получить текущее местоположение";
"location_status_warning" = "Диапазон предупреждения";
"location_status_invalid" = "Недействительное местоположение";
"location_status_unknown" = "Неизвестный статус";
"coordinates_origin_point" = "Недействительно: нулевые координаты (0,0)";
"coordinates_invalid_nan" = "Недействительно: нечисловые координаты";
"coordinates_out_of_range" = "Недействительно: координаты вне действительного диапазона";
"coordinates_far_from_user" = "Предупреждение: Местоположение далеко от вашего текущего местоположения";
"coordinates_ocean" = "Предупреждение: Местоположение может быть в океане или необитаемой зоне";
"free_address_limit" = "Ограничение адресов бесплатной версии";
"address_count_limit" = "Ограничение количества адресов";
"selected_addresses_can_import" = "Вы выбрали %d адресов, можно импортировать эти адреса.";
"selected_addresses_exceeds" = "Вы выбрали %d адресов, превышая допустимое количество на %d.";
"import_success_with_warnings" = "Успешно импортировано %d адресов, %d адресов с нормальными координатами, %d адресов с предупреждениями.\n\nАдреса с предупреждениями отмечены и могут быть исправлены вручную после импорта.";
"supported_formats" = "Поддерживаемые форматы";
"supported_format_csv" = "• CSV файлы: Столбец адресов должен содержать полные адреса";
"supported_format_json" = "• JSON данные: Массив, содержащий поля адресов";
"supported_format_text" = "• Простой текст: Один адрес на строку";
"download_history" = "История загрузок";
"input_address_data_url" = "Введите URL данных адресов";
"import_result" = "Результат импорта";
"downloading" = "Загрузка...";
"processing_data" = "Обработка данных...";
"google_drive_download_failed" = "Загрузка Google Drive не удалась";
"second_attempt_invalid_data" = "Вторая попытка загрузки вернула недействительные данные";
"cannot_parse_json" = "Не удается разобрать JSON данные, проверьте формат файла";
"cannot_parse_json_with_error" = "Не удается разобрать JSON данные: %@";
"cannot_read_file" = "Не удается прочитать файл: %@";
"failed" = "Доставка не удалась";
"no_valid_addresses" = "Не найдено действительных адресов";
"supports_file_types" = "Поддерживает файлы CSV, TXT и JSON";
"tap_to_select_file" = "Нажмите для выбора файла";
"input_company_name" = "Введите название компании (необязательно)";
"imported_addresses_count" = "Импортировано %d адресов";
"excel_format_not_supported" = "Формат Excel не поддерживается";
"import_failed" = "Импорт не удался";
"free_version_address_limit" = "Бесплатная версия позволяет максимум %d адресов.";
"current_address_count" = "В настоящее время у вас %d адресов, можно добавить еще %d адресов.";
"upgrade_to_premium_unlimited" = "Обновитесь до премиум для неограниченных адресов!";
"route_address_limit" = "Текущий маршрут имеет %d адресов, можно добавить еще %d адресов, всего максимум %d адресов.";
"free_version_limit" = "Ограничение адресов бесплатной версии";
"import_all_n" = "Импортировать все %d";
"cannot_import" = "Не удается импортировать";
"select_at_least_one" = "Выберите хотя бы один адрес";
"no_valid_addresses_found" = "Не найдено действительных адресов";
"import_success_all_valid" = "Успешно импортировано %d адресов, все координаты адресов нормальные.";
"import_success_some_warnings" = "Успешно импортировано %d адресов, %d адресов с нормальными координатами, %d адресов не могут получить координаты.";
"company_format" = "Формат компании";
"added_from_web_download" = "Добавлено из веб-загрузки";
"invalid_csv_row" = "Недействительная строка CSV";
"distance_warning" = "Расстояние превышает 200км от текущего местоположения";
"not_in_australia" = "Координаты не в пределах Австралии";
"invalid_address_data" = "Недействительные данные адреса";
"distance_warning_confirm" = "Расстояние от текущего местоположения превышает 200 км, продолжить?";
"coordinates_missing" = "Координаты отсутствуют";
"low_accuracy_address" = "Адрес с низкой точностью";
"address_partial_match" = "Частичное совпадение адреса";
"address_outside_region" = "Адрес вне региона";
"api_limit_reached" = "Достигнут лимит API";
"address_not_exist_or_incorrect_format" = "Адрес не существует или неправильный формат";
"please_check_address_spelling" = "Проверьте правописание адреса";
"try_smaller_street_number" = "Попробуйте меньший номер улицы";
"use_full_street_type_name" = "Используйте полное название типа улицы";
"try_add_more_address_details" = "Попробуйте добавить больше деталей адреса";
"cannot_find_address" = "Не удается найти адрес";
"please_check_spelling_or_add_details" = "Проверьте правописание или добавьте детали";
"cannot_find_address_check_spelling" = "Не удается найти адрес, проверьте правописание";
"address_not_set" = "Адрес не установлен";
"address_format_incomplete" = "Формат адреса неполный";
"location_service_denied" = "Служба определения местоположения отклонена";
"no_saved_groups" = "Нет сохраненных групп";
"select_points_create_groups" = "Выберите точки доставки и создайте группы для удобного управления";
"navigate_to_these_points" = "Навигация к этим точкам";
"confirm_remove_address" = "Вы уверены, что хотите удалить адрес \"%@\" из группы?";
"confirm_remove_this_address" = "Вы уверены, что хотите удалить этот адрес из группы?";
"addresses_count" = "%d адресов";
"no_saved_routes" = "Нет сохраненных маршрутов";
"no_saved_routes_description" = "Вы еще не сохранили ни одного маршрута";
"all_routes" = "Все маршруты";
"address_count_format_simple" = "%d адресов";
"delete_all_routes" = "Удалить все маршруты";
"navigate_to_all_points" = "Навигация ко всем точкам";
"confirm_navigate_to_route" = "Вы уверены, что хотите навигировать ко всем точкам в маршруте \"%@\"?";
"temp_navigation_group" = "Временная навигационная группа";
"route_management" = "Управление маршрутами";
"route_addresses" = "Адреса маршрута";
"no_addresses_in_route" = "У этого маршрута нет адресов";
"must_keep_one_route" = "Должен быть сохранен хотя бы один маршрут";
"confirm_delete_route" = "Вы уверены, что хотите удалить маршрут \"%@\"? Это действие нельзя отменить.";
"confirm_delete_all_routes" = "Подтвердить удаление всех маршрутов";
"confirm_delete_all_routes_message" = "Вы уверены, что хотите удалить все маршруты? Это действие нельзя отменить.";
"delete_all" = "Удалить все";
"address_information" = "Информация об адресе";
"group_belonging" = "Принадлежащая группа";
"view_map" = "Просмотр карты";
"delete_delivery_point" = "Удалить точку доставки";
"delivery_point_details" = "Детали точки доставки";
"confirm_deletion" = "Подтвердить удаление";
"delete_delivery_point_confirmation" = "Вы уверены, что хотите удалить эту точку доставки? Это действие нельзя отменить.";
"delivery_photos" = "Фото доставки";
"view_delivery_photos" = "Просмотр фото доставки";
"no_photos_taken" = "Фото еще не сделаны";
"take_photos" = "Сделать фото";
"loading_photos" = "Загрузка фото...";
"photo_not_found" = "Фото не найдено";
"photo_deleted" = "Фото было удалено";
"share_photos" = "Поделиться фото";
"photo_capture_title" = "Подтверждение фото";
"door_number_photo_title" = "Фото номера дороги/дома";
"package_label_photo_title" = "Фото этикетки посылки";
"placement_photo_title" = "Фото места размещения";
"door_number_photo_desc" = "Сделайте четкое фото номера дома, убедитесь, что цифры/буквы видны";
"package_label_photo_desc" = "Сделайте фото этикетки посылки, убедитесь, что информация получателя четко видна";
"placement_photo_desc" = "Сделайте фото окончательного места размещения посылки";
"swipe_to_switch" = "Проведите для переключения типа фото";
"photos_will_be_saved_to" = "Фотографии будут сохранены в";
"complete_photos" = "Завершить фотосъемку";
"photo_save_success" = "Фото успешно сохранено";
"photo_save_failure" = "Не удалось сохранить фото";
"no_photos_found" = "Фото не найдено";
"photos_deleted_or_not_taken" = "Фото могло быть удалено или еще не сделано";
"share_photo" = "Поделиться фото";
"photo_capture_preview" = "Режим предварительного просмотра - симуляция камеры";
"photo_capture_close" = "Закрыть";
"camera_start_failed" = "Не удалось запустить камеру";
"camera_start_failed_retry" = "Не удается запустить камеру, попробуйте еще раз";
"camera_init_failed" = "Не удалось инициализировать камеру";
"camera_access_failed" = "Не удается получить доступ к камере";
"photo_processing_failed" = "Не удалось сделать фото";
"photo_processing_failed_retry" = "Не удается завершить обработку фото, попробуйте еще раз";
"photo_capture_progress" = "Прогресс: %d/%d";
"photo_captured_continue" = "Фото завершено, продолжить с %@";
"pending" = "Ожидает доставки";
"in_progress" = "В процессе доставки";
"select_delivery_status" = "Выберите статус доставки";
"select_failure_reason" = "Выберите причину неудачи";
"delivery_status_pending" = "Ожидает";
"delivery_status_in_progress" = "В процессе";
"delivery_status_completed" = "Завершено";
"delivery_status_failed" = "Не удалось";
"failure_reason_not_at_home" = "Клиент не дома";
"failure_reason_wrong_address" = "Неверный адрес";
"failure_reason_no_access" = "Не удается попасть в место";
"failure_reason_rejected" = "Посылка была отклонена";
"failure_reason_other" = "Другая причина";
"enter_custom_reason" = "Введите конкретную причину";
"custom_reason_placeholder" = "Опишите конкретную причину...";
"custom_reason_required" = "Введите конкретную причину";
"failure_reason_required" = "Выберите причину неудачи";
"delivery_type_delivery" = "Доставка";
"delivery_type_pickup" = "Самовывоз";
"delivery_order_first" = "Первый";
"delivery_order_auto" = "Автоматический";
"delivery_order_last" = "Последний";
"package_size_small" = "Маленький";
"package_size_medium" = "Средний";
"package_size_large" = "Большой";
"package_type_box" = "Коробка";
"package_type_bag" = "Сумка";
"package_type_letter" = "Письмо";
"one_click_navigation_grouping" = "Навигация и группировка одним кликом";
"speed_60x_faster" = "В 60 раз быстрее";
"goodbye_manual_address_adding" = "Прощай, ручное добавление адресов";
"watch_detailed_demo" = "Посмотреть подробное демо";
"upgrade_to_pro_now" = "Обновиться до Pro сейчас";
"free_trial_7_days" = "7 дней бесплатной пробной версии";
"traditional_vs_navibatch_pro" = "Традиционный метод против NaviBatch Pro";
"swipe_to_view_full_comparison" = "Проведите для просмотра полного сравнения";
"traditional_method" = "Традиционный метод";
"drivers_get_lost_affect_efficiency" = "Водители теряются, влияет на эффективность";
"repetitive_operations_waste_time" = "Повторяющиеся операции тратят время";
"total_time_60_seconds" = "Общее время: 60 секунд";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Оптимизировать маршруты, сократить расстояние";
"improve_delivery_efficiency_accuracy" = "Улучшить эффективность и точность доставки";
"speed_boost_60x" = "Ускорение в 60 раз";
"total_time_1_second" = "Общее время: 1 секунда";
"time_comparison" = "Сравнение времени";
"traditional_method_problems" = "Проблемы традиционного метода";
"each_address_3_5_seconds_14_total_60" = "Каждый адрес 3-5 секунд, 14 адресов всего 60 секунд";
"repetitive_operations_cause_fatigue" = "Повторяющиеся операции вызывают усталость";
"address_order_reversed_last_becomes_first" = "Порядок адресов обратный, последний становится первым";
"need_manual_reverse_adding_takes_longer" = "Нужно ручное обращение, добавление занимает больше времени";
"navibatch_advantages" = "Преимущества NaviBatch";
"add_14_addresses_1_second_60x_faster" = "Добавить 14 адресов за 1 секунду, в 60 раз быстрее";
"auto_maintain_correct_order_no_adjustment" = "Автоматически поддерживать правильный порядок, без настройки";
"zero_error_rate_no_repetition" = "Нулевая частота ошибок, без повторений";
"save_59_seconds" = "Сэкономить 59 секунд";
"speed_boost_60x_simple" = "Ускорение в 60 раз";
"seconds_format" = "%d секунд";
"actual_benefits_one_click_navigation" = "Реальные преимущества навигации одним кликом";
"daily_savings" = "Ежедневная экономия";
"daily_savings_value" = "59 секунд";
"daily_savings_description" = "Сэкономьте 59 секунд на каждые 14 адресов";
"monthly_savings" = "Месячная экономия";
"monthly_savings_value" = "30 минут";
"monthly_savings_description" = "На основе 30 маршрутов в месяц";
"fuel_savings_value" = "30%";
"fuel_savings_description" = "Оптимизация маршрутов снижает расход топлива";
"income_increase" = "Увеличение дохода";
"income_increase_value" = "15%";
"income_increase_description" = "Больше доставок в день = больше дохода";
"trial" = "Пробная версия";
"days_left" = "дней осталось";
"free_plan_description" = "Бесплатный план - До 20 адресов";
"pro_plan_active" = "План Pro активен";
"expert_plan_active" = "План Expert активен";
"trial_active" = "Пробная версия активна";
"trial_expires_on" = "Пробная версия истекает %@";
"address_validation_mode" = "Режим валидации адресов";
"validation_description" = "Контролирует строгость валидации адресов";
"current_settings" = "Текущие настройки";
"validation_mode_format" = "Режим: %@";
"threshold_score_format" = "Порог: %.1f";
"validation_example" = "Пример валидации";
"original_address_example" = "Исходный адрес: 123 Main St";
"reverse_address_example" = "Обратный адрес: 125 Main St";
"house_number_difference" = "Разница номера дома: 2";
"result_label" = "Результат:";
"may_pass_warning" = "Может пройти (предупреждение)";
"will_not_pass" = "Не пройдет";
"real_case_example" = "Пример реального случая";
"real_case_description" = "На основе реальных данных валидации адресов";
"address_validation_settings" = "Настройки валидации адресов";
"clear" = "Очистить";
"view_details" = "Посмотреть детали";
"create_test_data" = "Создать тестовые данные";
"manual_snapshot" = "Ручной снимок";
"start_location_updates" = "Начать обновления местоположения";
"stop_location_updates" = "Остановить обновления местоположения";
"user_location_marker_test" = "Тест маркера местоположения пользователя";
"location_animation_control" = "Управление анимацией местоположения";
"current_location_format" = "Текущее местоположение: %.6f, %.6f";
"waiting_for_location" = "Ожидание местоположения...";
"diagnostic_tools" = "Диагностические инструменты";
"storekit_diagnostics" = "Диагностика StoreKit";
"subscription_function_test" = "Тест функции подписки";
"localization_test" = "Тест локализации";
"address_validation_demo" = "Демо валидации адресов";
"localization_tools" = "Инструменты локализации";
"coordinate_debug_tools" = "Инструменты отладки координат";
"smart_abbreviation_expansion_test" = "Тест умного расширения сокращений";
"subscription_restore_diagnostics" = "Диагностика восстановления подписки";
"batch_address_import_test" = "Тест пакетного импорта адресов";
"test_import_1000_addresses_memory" = "Тест: импорт 1000 адресов (память)";
"map_rendering_test" = "Тест рендеринга карты";
"test_map_display_markers_memory" = "Тест: отображение маркеров на карте (память)";
"select_test_language" = "Выбрать тестовый язык";
"discover_60x_speed_boost" = "Откройте для себя ускорение в 60 раз";
"see_60x_speed_demo" = "Посмотреть демо скорости 60x";
"free_vs_pro_comparison" = "Сравнение бесплатной и Pro версий";
"our_free_beats_competitors_paid" = "Наш бесплатный план превосходит платные планы конкурентов";
"features" = "Функции";
"up_to_20" = "До 20";
"unlimited" = "Неограниченно";
"smart_optimization" = "Умная оптимизация";
"up_to_20_percent" = "До 20%";
"free_tier_grouping_limit" = "До 10 адресов";
"pro_tier_unlimited_grouping" = "Неограниченная группировка";
"free_tier_navigation_limit" = "1 группа (до 10 адресов)";
"pro_tier_unlimited_navigation" = "Несколько групп (неограниченно)";
"file_not_found" = "Файл не найден";
"sample_file_not_available" = "Образец файла недоступен";
"file_copy_failed" = "Не удалось скопировать файл";

// 地图模式选择器
"map_mode_selector_title" = "Режим карты";
"map_mode_standard" = "Стандартный";
"map_mode_satellite" = "Спутник";
"map_mode_traffic" = "Трафик";
"map_mode_hybrid" = "Гибридный";