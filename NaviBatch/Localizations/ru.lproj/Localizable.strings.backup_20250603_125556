/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Настройки языка";
"system_language" = "Системный язык";
"system_language_section" = "Системные настройки";
"languages" = "Языки";
"language_info_title" = "О настройках языка";
"language_info_description" = "После изменения языковых настроек приложение будет отображать текст на выбранном языке. Для некоторого контента может потребоваться перезапуск приложения для полного применения новых языковых настроек.";
"restart_required" = "Требуется перезапуск";
"restart_app_message" = "Для полного применения изменений языка, пожалуйста, перезапустите приложение.";
"restart_now" = "Перезапустить сейчас";
"restart_later" = "Перезапустить позже";

// MARK: - Common UI Elements
"close" = "Закрыть";
"cancel" = "Отмена";
"save" = "Сохранить";
"edit" = "Редактировать";
"delete" = "Удалить";
"done" = "Готово";
"next" = "Далее";
"back" = "Назад";
"confirm" = "Подтвердить";
"error" = "Ошибка";
"success" = "Успешно";
"warning" = "Предупреждение";
"loading" = "Загрузка...";
"search" = "Поиск";
"settings" = "Настройки";
"help" = "Помощь";
"about" = "О приложении";
"menu" = "Меню";
"understand" = "Понятно";

// MARK: - Navigation
"navigation" = "Навигация";
"start_navigation" = "Начать навигацию";

// MARK: - Subscription
"subscription" = "Подписка";
"upgrade_to_pro" = "Обновить до Pro";
"upgrade_description" = "Группировка навигации в один клик, в 60 раз быстрее ручной операции";
"restore_purchases" = "Восстановить покупки";
"learn_more" = "Узнать больше";
"upgrade_your_plan" = "Обновить ваш план";
"one_click_navigation_description" = "Группировка адресов в один клик для экономии времени и топлива";
"current_plan" = "Текущий план";
"upgrade" = "Обновить";
"maybe_later" = "Возможно позже";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Бесплатно";
"pro_tier_price" = "$29.99/месяц";
"expert_tier_price" = "$249.99/год";
"free_tier_description" = "Подходит для частных лиц и малого бизнеса с небольшим количеством точек остановки";
"pro_tier_description" = "Для пользователей, которым требуется навигация в один клик и неограниченное количество адресов";
"expert_tier_description" = "То же, что и Pro, но с экономией 31% при годовом плане";
"route_optimization" = "Оптимизация маршрута";
"unlimited_routes" = "Неограниченное количество маршрутов";
"unlimited_optimizations" = "Неограниченные оптимизации";
"max_15_addresses" = "Максимум 15 адресов на маршрут";
"save_fuel_30" = "Экономия топлива до 30%";
"unlimited_addresses" = "✨ Неограниченное количество адресов ✨";
"one_click_navigation" = "⚡ Навигация в один клик - в 60 раз быстрее ⚡";
"package_finder" = "Поиск посылок";
"annual_savings" = "Годовая экономия";
"switched_to_free" = "Переключено на бесплатный план";
"switched_to_subscription" = "Переключено на план с подпиской";
"unlimited_stops" = "Неограниченные остановки";
"plan_as_many_stops_as_needed" = "Добавляйте любое количество точек доставки без ограничений";

// MARK: - Address Input
"enter_or_search_address" = "Введите или найдите адрес";
"search_results_count" = "Результаты поиска: %d";
"no_matching_addresses" = "Соответствующих адресов не найдено";
"search_address_failed" = "Ошибка поиска адреса: %@";
"address_search_no_response" = "Нет ответа при поиске адреса";
"cannot_get_address_coordinates" = "Не удается получить координаты адреса";
"speech_recognizer_unavailable" = "Распознаватель речи недоступен";
"microphone_permission_denied" = "Доступ к микрофону запрещен";
"speech_recognition_permission_denied" = "Доступ к распознаванию речи запрещен";
"listening" = "Слушаю...";
"recording_failed" = "Не удалось начать запись: %@";
"cannot_get_coordinates_retry" = "Не удается получить координаты адреса. Пожалуйста, введите вручную или повторите попытку";
"cannot_create_recognition_request" = "Не удается создать запрос распознавания";

// MARK: - Saved Address Picker
"search_address" = "Поиск адреса";
"no_saved_addresses" = "Нет сохраненных адресов";
"no_saved_addresses_description" = "У вас еще нет сохраненных адресов, или ни один адрес не соответствует условиям фильтра";
"select_address_book" = "Выберите адресную книгу";

// MARK: - Menu
"menu" = "Меню";
"routes" = "Маршруты";
"address_book" = "Адресная книга";
"saved_routes" = "Сохраненные маршруты";
"manage_your_routes" = "Управляйте планированием маршрутов";
"manage_your_addresses" = "Управляйте часто используемыми адресами";
"settings" = "Настройки";
"preferences" = "Предпочтения";
"set_custom_start_point" = "Установить пользовательскую начальную точку";
"current_start_point" = "Текущая начальная точка: %@";
"support" = "Поддержка";
"contact_us" = "Свяжитесь с нами";
"contact_us_description" = "Есть вопросы или предложения? Не стесняйтесь обращаться!";
"help_center" = "Центр помощи";
"subscription" = "Подписка";
"upgrade_to_pro" = "Обновить до Pro";
"upgrade_description" = "Группировка навигации в один клик, в 60 раз быстрее ручной операции";
"open_subscription_view" = "Открыть окно подписки";
"open_subscription_view_description" = "Пропустить промежуточный слой и показать окно подписки";
"restore_purchases_failed" = "Не удалось восстановить покупки: %@";
"about" = "О приложении";
"rate_us" = "Оцените нас";
"rate_us_description" = "Ваш отзыв важен для нас и помогает улучшить приложение!";
"share_app" = "Поделиться приложением";
"share_app_text" = "Попробуйте NaviBatch, удивительное приложение для планирования маршрутов!";
"about_app" = "О приложении";
"developer_tools" = "Инструменты разработчика";
"coordinate_debug_tool" = "Инструмент отладки координат";
"batch_fix_addresses" = "Пакетное исправление адресов";
"clear_database" = "Очистить базу данных";
"clear_database_confirmation" = "Это удалит все данные, включая маршруты, адреса и группы. Это действие нельзя отменить. Вы уверены, что хотите продолжить?";
"confirm_clear" = "Подтвердить очистку";
"version_info" = "Версия %@ (%@)";
"current_system_language" = "Текущий системный язык";
"reset_to_system_language" = "Сбросить на системный язык";
"language" = "Язык";
"language_settings" = "Настройки языка";

// MARK: - Address Edit
"address" = "Адрес";
"coordinates" = "Координаты";
"distance_from_current_location" = "Расстояние от текущего местоположения";
"address_info" = "Информация об адресе";
"update_coordinates" = "Обновить координаты";
"fix_address" = "Исправить адрес";
"prompt" = "Подсказка";
"confirm" = "Подтвердить";
"kilometers_format" = "%.1f км";
"meters_format" = "%.0f м";
"modify_address_first" = "Пожалуйста, измените адрес перед обновлением координат";
"coordinates_update_success" = "Координаты успешно обновлены";
"coordinates_update_failure" = "Не удалось обновить координаты";
"save_failure" = "Ошибка сохранения: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Нет сохраненных адресов";
"no_saved_addresses_message" = "Вы еще не сохранили ни одного адреса";
"add_new_address" = "Добавить новый адрес";
"address_title" = "Адрес";
"add" = "Добавить";
"refresh" = "Обновить";
"notes" = "Примечания";
"address_details" = "Детали адреса";
"favorite" = "Избранное";
"edit_address" = "Редактировать адрес";
"cancel" = "Отмена";
"save" = "Сохранить";
"confirm_delete" = "Подтвердить удаление";
"delete" = "Удалить";
"delete_address_confirmation" = "Вы уверены, что хотите удалить этот адрес? Это действие нельзя отменить.";
"edit" = "Редактировать";
"address_marker" = "Адрес";
"address_label" = "Адрес:";
"notes_label" = "Примечания:";
"created_at_label" = "Создано:";
"open_in_maps" = "Открыть в картах";
"copy_address" = "Копировать адрес";
"address_details_title" = "Детали адреса";

// MARK: - Route Detail
"start_end_point" = "Начальная/конечная точка";
"start_point" = "Начальная точка";
"end_point" = "Конечная точка";
"route_info" = "Информация о маршруте";
"address_count" = "Количество адресов";
"address_count_format" = "%d адресов";
"points_count_format" = "%d точек";
"additional_points_format" = "+%d точек";
"export_route" = "Экспорт маршрута";
"navigate" = "Навигация";
"address_list" = "Список адресов";
"no_addresses" = "Нет адресов";
"no_addresses_message" = "У этого маршрута еще нет адресов";

// MARK: - Route Bottom Sheet
"address_point_start" = "Начальная точка";
"address_point_stop" = "Точка остановки";
"address_point_end" = "Конечная точка";
"route_name" = "Название маршрута";
"save" = "Сохранить";
"new_route" = "Новый маршрут";
"saved_route" = "Сохраненный маршрут";
"edit" = "Редактировать";
"loading" = "Загрузка...";
"plan_route" = "Планировать маршрут";
"clear_all" = "Очистить все";
"avoid" = "Избегать:";
"toll_roads" = "Платные дороги";
"highways" = "Автомагистрали";
"processing_addresses" = "Обработка адресов...";
"same_start_end_point" = "Вы установили один и тот же адрес в качестве начальной и конечной точки";
"add_start_point" = "Добавить начальную точку";
"swipe_left_to_delete" = "← Смахните влево для удаления";
"delete" = "Удалить";
"add_new_address" = "Добавить новый адрес";
"add_end_point" = "Добавить конечную точку";

// MARK: - Simple Address Sheet
"address_title" = "Адрес";
"enter_and_select_address" = "Введите и выберите адрес";
"current_search_text" = "Текущий текст поиска: %@";
"search_results_count" = "Результаты поиска: %d";
"no_matching_addresses" = "Соответствующих адресов не найдено";
"add_address" = "Добавить адрес";
"edit_address" = "Редактировать адрес";
"selected_coordinates" = "Выбранные координаты";
"company_name_optional" = "Название компании (необязательно)";
"url_optional" = "URL (необязательно)";
"favorite_address" = "Избранный адрес";
"set_as_start_and_end" = "Установить как начальную и конечную точку";
"address_book" = "Адресная книга";
"batch_paste" = "Пакетная вставка";
"file_import" = "Импорт файла";
"web_download" = "Загрузка из интернета";
"cancel" = "Отмена";
"saving" = "Сохранение...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Управление точками доставки";
"information_category" = "Категория информации";
"address_info" = "Информация об адресе";
"package_info" = "Информация о посылке";
"vehicle_position" = "Положение транспортного средства";
"delivery_info" = "Информация о доставке";
"navigation" = "Навигация";
"take_photo_record" = "Сделать фото";
"update_status" = "Обновить статус";
"done" = "Готово";
"edit_address_button" = "Редактировать адрес";
"coordinates" = "Координаты";
"access_instructions" = "Инструкции по доступу";
"add_access_instructions" = "Добавить инструкции по доступу...";
"package_count" = "Количество посылок";
"package_size" = "Размер посылки";
"package_type" = "Тип посылки";
"mark_as_important" = "Отметить как важное";
"select_package_position" = "Выберите положение посылки в транспортном средстве";
"vehicle_area" = "Область транспортного средства";
"left_right_position" = "Положение слева/справа";
"vehicle_position_front" = "Передняя часть";
"vehicle_position_middle" = "Средняя часть";
"vehicle_position_back" = "Задняя часть";
"vehicle_position_left" = "Слева";
"vehicle_position_right" = "Справа";
"vehicle_position_floor" = "Нижняя часть";
"vehicle_position_shelf" = "Верхняя часть";
"height_position" = "Высотное положение";
"delivery_type" = "Тип доставки";
"delivery_status" = "Статус доставки";
"order_info" = "Информация о заказе";
"order_information" = "Информация о заказе";
"order_number" = "Номер заказа";
"enter_order_number" = "Введите номер заказа";
"tracking_number" = "Номер отслеживания";
"enter_tracking_number" = "Введите номер отслеживания";
"time_info" = "Информация о времени";
"time_information" = "Информация о времени";
"estimated_arrival_time" = "Ожидаемое время прибытия";
"anytime" = "В любое время";
"stop_time" = "Время остановки";
"minutes_format" = "%d минут";
"photo_record" = "Фотозапись";
"door_number_photo" = "Фото номера двери";
"package_label_photo" = "Фото этикетки посылки";
"placement_photo" = "Фото размещения";
"door_number_desc" = "Сделайте четкое фото номера дома или улицы, убедитесь, что цифры/буквы видны";
"package_label_desc" = "Сделайте фото этикетки посылки, убедитесь, что информация получателя четко видна";
"placement_desc" = "Сделайте фото окончательного места размещения посылки";
"photo_captured" = "Фото сделано";
"photo_captured_options" = "Фото сделано, хотите продолжить со следующим фото или завершить текущее?";
"continue_to_next_photo" = "Продолжить со следующим - %@";
"retake" = "Переснять";
"tap_to_capture" = "Нажмите для съемки";
"flash_auto" = "Автовспышка";
"flash_on" = "Включить вспышку";
"flash_off" = "Выключить вспышку";
"photo_record_completed" = "Фотозапись завершена";
"photo_confirmation" = "Подтверждение фото";
"error" = "Ошибка";
"ok" = "ОК";
"complete_photo_capture" = "Завершить фотосъемку";
"tap_to_capture" = "Нажмите для съемки";
"photo_instructions" = "Нажмите на каждую карточку фото для съемки. Все фото должны быть завершены.";
"photo_options" = "Параметры фото";
"view_photo" = "Просмотр фото";
"retake_photo" = "Переснять";
"saving_photos" = "Сохранение фото...";
"completed" = "Завершено";
"not_taken" = "Не сделано";
"route_options" = "Параметры маршрута";
"avoid_tolls" = "Избегать платных дорог";
"avoid_highways" = "Избегать автомагистралей";
"optimize_route" = "Оптимизировать маршрут";
"optimizing" = "Оптимизация...";
"optimization_complete" = "Оптимизация завершена";
"route_optimization_results" = "Результаты оптимизации маршрута";
"route_planning_options" = "Параметры планирования маршрута";
"before_optimization" = "До оптимизации";
"after_optimization" = "После оптимизации";
"auto_group" = "Автогруппировка";
"optimized_route_order" = "Оптимизированный порядок маршрута";
"apply" = "Применить";
"kilometers" = "километры";

// MARK: - Addresses
"addresses" = "Адреса";
"add_address" = "Добавить адрес";
"edit_address" = "Редактировать адрес";
"delete_address" = "Удалить адрес";
"address_details" = "Детали адреса";
"street" = "Улица";
"city" = "Город";
"state" = "Область/штат";
"country" = "Страна";
"postal_code" = "Почтовый индекс";
"phone" = "Телефон";
"email" = "Эл. почта";
"website" = "Веб-сайт";
"company" = "Компания";
"notes" = "Примечания";
"coordinates" = "Координаты";
"latitude" = "Широта";
"longitude" = "Долгота";
"geocoding_error" = "Ошибка геокодирования";
"address_validation" = "Проверка адреса";
"invalid_addresses" = "Недействительные адреса";
"fix_addresses" = "Исправить адреса";

// MARK: - Routes
"route" = "Маршрут";
"routes" = "Маршруты";
"select_address_point" = "Выберите адресную точку";
"select_delivery_points" = "Выберите точки доставки";
"create_delivery_route" = "Создать маршрут доставки";
"view_saved_routes" = "Просмотр сохраненных маршрутов";
"create_route" = "Создать маршрут";
"edit_route" = "Редактировать маршрут";
"delete_route" = "Удалить маршрут";
"route_name" = "Название маршрута";
"route_details" = "Детали маршрута";
"selected_addresses" = "Выбрано %d адресов";
"reached_limit" = "Достигнут лимит";
"can_select_more" = "Можно выбрать еще %d";
"navigate_button" = "Навигация";
"create_group" = "Создать группу";
"start_point" = "Начальная точка";
"end_point" = "Конечная точка";
"waypoints" = "Промежуточные точки";
"total_distance" = "Общее расстояние";
"estimated_time" = "Расчетное время";
"route_summary" = "Сводка по маршруту";
"route_options" = "Параметры маршрута";
"route_saved" = "Маршрут сохранен";
"route_optimized" = "Маршрут оптимизирован";
"optimizing_route" = "Оптимизация маршрута...";
"completed_percent" = "%d%% завершено";
"processing_points" = "Обработка: %d/%d";
"estimated_remaining_time" = "Расчетное оставшееся время: %@";

// MARK: - Delivery
"delivery" = "Доставка";
"delivery_confirmation" = "Подтверждение доставки";
"take_photo" = "Сделать фото";
"signature" = "Подпись";
"delivery_notes" = "Примечания к доставке";
"delivery_status" = "Статус доставки";
"delivered" = "Доставлено";
"not_delivered" = "Не доставлено";
"delivery_time" = "Время доставки";
"delivery_date" = "Дата доставки";
"package_details" = "Детали посылки";
"package_id" = "ID посылки";
"package_weight" = "Вес посылки";
"package_dimensions" = "Размеры посылки";
"recipient_name" = "Имя получателя";
"recipient_phone" = "Телефон получателя";

// MARK: - Groups
"groups" = "Группы";
"saved_groups" = "Сохраненные группы";
"create_group" = "Создать группу";
"edit_group" = "Редактировать группу";
"delete_group" = "Удалить группу";
"group_name" = "Название группы";
"group_details" = "Детали группы";
"auto_grouping" = "Автоматическая группировка";
"group_by" = "Группировать по";
"add_to_group" = "Добавить в группу";
"remove_from_group" = "Удалить из группы";
"group_created" = "Группа создана";
"default_group_name_format" = "Группа%d";
"auto_grouping_completed" = "Автоматическая группировка завершена";
"auto_grouping_in_progress" = "Выполняется автоматическая группировка...";
"create_group_every_14_addresses" = "Создать группу для каждых 14 адресов";
"create_delivery_group" = "Создать группу доставки";
"enter_group_name" = "Введите название группы";
"selected_delivery_points" = "Выбранные точки доставки";
"drag_to_adjust_order" = "Перетащите для изменения порядка";

// MARK: - Subscription
"subscription" = "Подписка";
"free_plan" = "Бесплатный план";
"pro_plan" = "План Pro";
"expert_plan" = "План Expert";
"monthly" = "Ежемесячно";
"yearly" = "Ежегодно";
"subscribe" = "Подписаться";
"upgrade" = "Обновить";
"upgrade_to_pro" = "Обновить до Pro";
"manage_subscription" = "Управление подпиской";
"restore_purchases" = "Восстановить покупки";
"subscription_benefits" = "Преимущества подписки";
"free_trial" = "Бесплатный период";
"price_per_month" = "%@ в месяц";
"price_per_year" = "%@ в год";
"save_percent" = "Сэкономьте %@%";
"current_plan" = "Текущий план";
"subscription_terms" = "Условия подписки";
"privacy_policy" = "Политика конфиденциальности";
"terms_of_service" = "Условия использования";

// MARK: - Import/Export
"import" = "Импорт";
"export" = "Экспорт";
"import_addresses" = "Импорт адресов";
"export_addresses" = "Экспорт адресов";
"import_from_file" = "Импорт из файла";
"export_to_file" = "Экспорт в файл";
"file_format" = "Формат файла";
"csv_format" = "Формат CSV";
"excel_format" = "Формат Excel";
"json_format" = "Формат JSON";
"import_success" = "Успешно импортировано %d адресов, все с действительными координатами.";
"export_success" = "Экспорт успешно выполнен";
"import_error" = "Ошибка импорта";
"export_error" = "Ошибка экспорта";

// MARK: - Navigation
"navigate" = "Навигация";

// MARK: - Look Around
"show_look_around" = "Показать обзор улицы";
"hide_look_around" = "Скрыть обзор улицы";

// MARK: - Map
"map" = "Карта";
"map_type" = "Тип карты";
"standard" = "Стандартная";
"satellite" = "Спутник";
"hybrid" = "Гибридная";
"show_traffic" = "Показать движение";
"current_location" = "Текущее местоположение";
"directions" = "Маршрут";
"distance_to" = "Расстояние до";
"eta" = "Расчетное время прибытия";
"look_around" = "Осмотреться";
"locating_to_glen_waverley" = "Определение местоположения Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Ошибка сети";
"location_error" = "Ошибка местоположения";
"permission_denied" = "Доступ запрещен";
"location_permission_required" = "Требуется разрешение на определение местоположения";
"camera_permission_required" = "Требуется разрешение на доступ к камере";
"photo_library_permission_required" = "Требуется разрешение на доступ к фотографиям";
"please_try_again" = "Пожалуйста, попробуйте еще раз";
"something_went_wrong" = "Что-то пошло не так";
"invalid_input" = "Неверный ввод";
"required_field" = "Обязательное поле";
"no_internet_connection" = "Нет подключения к интернету";
"server_error" = "Ошибка сервера";
"timeout_error" = "Ошибка тайм-аута";
"data_not_found" = "Данные не найдены";
"selection_limit_reached" = "Достигнут предел выбора";
"selection_limit_description" = "Вы можете выбрать максимум %d адресов, вы выбрали %d адресов";

// MARK: - Location Validation Status
"location_status_valid" = "Действительный диапазон";
"location_status_warning" = "Диапазон предупреждения";
"location_status_invalid" = "Недействительное местоположение";
"location_status_unknown" = "Неизвестный статус";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Недействительно: нулевые координаты (0,0)";
"coordinates_invalid_nan" = "Недействительно: нечисловые координаты";
"coordinates_out_of_range" = "Недействительно: координаты вне действительного диапазона";
"coordinates_far_from_user" = "Предупреждение: Местоположение далеко от вашего текущего местоположения";
"coordinates_ocean" = "Предупреждение: Местоположение может быть в океане или необитаемой зоне";

// MARK: - Batch Address Input
"batch_add_addresses" = "Массовое добавление адресов";
"batch_address_input_placeholder" = "Введите или вставьте адреса, по одному адресу на строку. Максимум 35 адресов.";
"free_address_limit" = "Ограничение адресов бесплатной версии";
"address_count_limit" = "Ограничение количества адресов";
"free_version_max_addresses" = "Бесплатная версия позволяет максимум %d адресов.";
"current_addresses_remaining" = "В настоящее время у вас %d адресов, можно добавить еще %d адресов.";
"current_route_address_limit" = "Текущий маршрут имеет %d адресов, можно добавить еще %d адресов, всего максимум %d адресов.";
"selected_addresses_can_import" = "Вы выбрали %d адресов, можно импортировать эти адреса.";
"selected_addresses_exceeds" = "Вы выбрали %d адресов, превышая допустимое количество на %d.";
"selected_addresses_all_importable" = "Вы выбрали %d адресов, все можно импортировать.";
"upgrade_for_unlimited_addresses" = "Обновитесь до премиум для неограниченных адресов!";
"import_first_n_addresses" = "Импортировать только первые %d";
"import_all_addresses" = "Импортировать все адреса";
"import_selected_addresses" = "Импортировать выбранные адреса";
"no_importable_addresses" = "Нет адресов для импорта, проверьте ограничения адресов";
"please_enter_valid_address" = "Введите хотя бы один действительный адрес";

// MARK: - File Import
"import_success" = "Успешно импортировано %d адресов, все с действительными координатами.";
"import_success_with_warnings" = "Успешно импортировано %d адресов, %d адресов с нормальными координатами, %d адресов с предупреждениями.\n\nАдреса с предупреждениями отмечены и могут быть исправлены вручную после импорта.";

// MARK: - Web Download
"web_download" = "Загрузка из интернета";
"supported_formats" = "Поддерживаемые форматы";
"supported_format_csv" = "• CSV файлы: Столбец адресов должен содержать полные адреса";
"supported_format_json" = "• JSON данные: Массив, содержащий поля адресов";
"supported_format_text" = "• Простой текст: Один адрес на строку";
"download_history" = "История загрузок";
"upgrade_to_premium" = "Обновиться до премиум";
"input_address_data_url" = "Введите URL данных адресов";
"import_result" = "Результат импорта";
"import_addresses" = "Импорт адресов";
"downloading" = "Загрузка...";
"processing_data" = "Обработка данных...";
"google_drive_download_failed" = "Загрузка Google Drive не удалась";
"second_attempt_invalid_data" = "Вторая попытка загрузки вернула недействительные данные";
"cannot_parse_json" = "Не удается разобрать JSON данные, проверьте формат файла";
"cannot_parse_json_with_error" = "Не удается разобрать JSON данные: %@";
"cannot_get_address_coordinates" = "Не удается получить координаты адреса";
"cannot_read_file" = "Не удается прочитать файл: %@";
"success" = "Успешно";
"warning" = "Предупреждение";
"failed" = "Доставка не удалась";
"no_matching_addresses" = "Соответствующих адресов не найдено";
"no_valid_addresses" = "Не найдено действительных адресов";
"confirm" = "Подтвердить";
"processing_addresses" = "Обработка адресов...";
"supports_file_types" = "Поддерживает файлы CSV, TXT и JSON";
"tap_to_select_file" = "Нажмите для выбора файла";
"import_addresses" = "Импорт адресов";
"company_name_optional" = "Название компании (необязательно)";
"input_company_name" = "Введите название компании (необязательно)";
"imported_addresses_count" = "Импортировано %d адресов";
"select_all" = "Выбрать все";
"excel_format_not_supported" = "Формат Excel не поддерживается";
"no_matching_addresses" = "Соответствующих адресов не найдено";

// MARK: - Import Limits
"import_failed" = "Импорт не удался";
"no_importable_addresses" = "Нет адресов для импорта, проверьте ограничения адресов";
"free_version_address_limit" = "Бесплатная версия позволяет максимум %d адресов.";
"current_address_count" = "В настоящее время у вас %d адресов, можно добавить еще %d адресов.";
"can_import_selected" = "Вы выбрали %d адресов, можно импортировать эти адреса.";
"selected_exceeds_limit" = "Вы выбрали %d адресов, превышая допустимое количество на %d.";
"upgrade_to_premium_unlimited" = "Обновитесь до премиум для неограниченных адресов!";
"route_address_limit" = "Текущий маршрут имеет %d адресов, можно добавить еще %d адресов, всего максимум %d адресов.";
"free_version_limit" = "Ограничение адресов бесплатной версии";
"address_count_limit" = "Ограничение количества адресов";
"import_selected_addresses" = "Импортировать выбранные адреса";
"import_first_n" = "Импортировать только первые %d";
"import_all_n" = "Импортировать все %d";
"cannot_import" = "Не удается импортировать";
"select_at_least_one" = "Выберите хотя бы один адрес";

// MARK: - Import Results
"no_valid_addresses_found" = "Не найдено действительных адресов";
"import_success_all_valid" = "Успешно импортировано %d адресов, все координаты адресов нормальные.";
"import_success_some_warnings" = "Успешно импортировано %d адресов, %d адресов с нормальными координатами, %d адресов не могут получить координаты.";

// MARK: - Warnings
"invalid_csv_row" = "Недействительная строка CSV";
"distance_warning" = "Расстояние превышает 200км от текущего местоположения";
"not_in_australia" = "Координаты не в пределах Австралии";
"cannot_get_coordinates" = "Не удается получить координаты адреса";
"empty_address" = "Пустой адрес";
"invalid_address_data" = "Недействительные данные адреса";

// MARK: - Saved Groups
"saved_groups" = "Сохраненные группы";
"no_saved_groups" = "Нет сохраненных групп";
"select_points_create_groups" = "Выберите точки доставки и создайте группы для удобного управления";
"group_name" = "Название группы";
"group_details" = "Детали группы";
"navigate_to_these_points" = "Навигация к этим точкам";
"confirm_remove_address" = "Вы уверены, что хотите удалить адрес \"%@\" из группы?";
"confirm_remove_this_address" = "Вы уверены, что хотите удалить этот адрес из группы?";
"addresses_count" = "%d адресов";
"no_saved_routes" = "Нет сохраненных маршрутов";
"no_saved_routes_description" = "Вы еще не сохранили ни одного маршрута";
"all_routes" = "Все маршруты";
"address_count_format_simple" = "%d адресов";
"delete_all_routes" = "Удалить все маршруты";
"navigate_to_all_points" = "Навигация ко всем точкам";
"confirm_navigate_to_route" = "Вы уверены, что хотите навигировать ко всем точкам в маршруте \"%@\"?";
"temp_navigation_group" = "Временная навигационная группа";

// MARK: - Route Management
"route_management" = "Управление маршрутами";
"route_info" = "Информация о маршруте";
"route_name" = "Название маршрута";
"route_addresses" = "Адреса маршрута";
"no_addresses_in_route" = "У этого маршрута нет адресов";
"must_keep_one_route" = "Должен быть сохранен хотя бы один маршрут";
"confirm_delete_route" = "Вы уверены, что хотите удалить маршрут \"%@\"? Это действие нельзя отменить.";
"confirm_delete_all_routes" = "Подтвердить удаление всех маршрутов";
"confirm_delete_all_routes_message" = "Вы уверены, что хотите удалить все маршруты? Это действие нельзя отменить.";
"delete_all" = "Удалить все";

// MARK: - Navigation Buttons
"navigate" = "Навигация";

// MARK: - GroupDetailView
"points_count_format" = "%d точек";

// MARK: - DeliveryPointDetailView
"address_information" = "Информация об адресе";
"group_belonging" = "Принадлежащая группа";
"view_map" = "Просмотр карты";
"delivery_status" = "Статус доставки";
"notes" = "Примечания";
"delete_delivery_point" = "Удалить точку доставки";
"delivery_point_details" = "Детали точки доставки";
"confirm_deletion" = "Подтвердить удаление";
"delete_delivery_point_confirmation" = "Вы уверены, что хотите удалить эту точку доставки? Это действие нельзя отменить.";

// MARK: - Delivery Photos
"delivery_photos" = "Фото доставки";
"view_delivery_photos" = "Просмотр фото доставки";
"no_photos_taken" = "Фото еще не сделаны";
"take_photos" = "Сделать фото";
"loading_photos" = "Загрузка фото...";
"photo_not_found" = "Фото не найдено";
"photo_deleted" = "Фото было удалено";
"door_number_photo" = "Фото номера двери";
"package_label_photo" = "Фото этикетки посылки";
"placement_photo" = "Фото размещения";
"share_photos" = "Поделиться фото";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Подтверждение фото";
"door_number_photo_title" = "Фото номера дороги/дома";
"package_label_photo_title" = "Фото этикетки посылки";
"placement_photo_title" = "Фото места размещения";
"door_number_photo_desc" = "Сделайте четкое фото номера дома, убедитесь, что цифры/буквы видны";
"package_label_photo_desc" = "Сделайте фото этикетки посылки, убедитесь, что информация получателя четко видна";
"placement_photo_desc" = "Сделайте фото окончательного места размещения посылки";
"swipe_to_switch" = "Проведите для переключения типа фото";
"complete_photos" = "Завершить фотосъемку";
"saving_photos" = "Сохранение фото...";
"photo_save_success" = "Фото успешно сохранено";
"photo_save_failure" = "Не удалось сохранить фото";
"retake_photo" = "Переснять";
"no_photos_found" = "Фото не найдено";
"photos_deleted_or_not_taken" = "Фото могло быть удалено или еще не сделано";
"share_photo" = "Поделиться фото";
"photo_capture_preview" = "Режим предварительного просмотра - симуляция камеры";
"photo_capture_close" = "Закрыть";
"camera_start_failed" = "Не удалось запустить камеру";
"camera_start_failed_retry" = "Не удается запустить камеру, попробуйте еще раз";
"camera_init_failed" = "Не удалось инициализировать камеру";
"camera_access_failed" = "Не удается получить доступ к камере";
"photo_processing_failed" = "Не удалось сделать фото";
"photo_processing_failed_retry" = "Не удается завершить обработку фото, попробуйте еще раз";
"photo_capture_progress" = "Прогресс: %d/%d";
"photo_captured_continue" = "Фото завершено, продолжить с %@";
"loading_photos" = "Загрузка фото...";
"cancel" = "Отмена";

// MARK: - Delivery Status
"pending" = "Ожидает доставки";
"in_progress" = "В процессе доставки";
"completed" = "Завершено";
"failed" = "Доставка не удалась";
"update_status" = "Обновить статус";
"select_delivery_status" = "Выберите статус доставки";
"select_failure_reason" = "Выберите причину неудачи";
"delivered" = "Доставлено";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Клиент не дома";
"failure_reason_wrong_address" = "Неверный адрес";
"failure_reason_no_access" = "Не удается попасть в место";
"failure_reason_rejected" = "Посылка была отклонена";
"failure_reason_other" = "Другая причина";
"enter_custom_reason" = "Введите конкретную причину";
"custom_reason_placeholder" = "Опишите конкретную причину...";
"custom_reason_required" = "Введите конкретную причину";
"failure_reason_required" = "Выберите причину неудачи";

// MARK: - Address Validation
"address_validation_failed" = "Проверка адреса не удалась";


"0" = "14天免費試用，隨時可取消";
