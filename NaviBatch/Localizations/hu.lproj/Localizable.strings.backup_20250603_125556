/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Nyelvi beállítások";
"system_language" = "Rendszer nyelv";
"system_language_section" = "Rendszer beállítások";
"languages" = "Nyelvek";
"language_info_title" = "A nyelvi beállításokról";
"language_info_description" = "A nyelvi beállítás módosítása után az alkalmazás a kiválasztott nyelven jeleníti meg a szöveget. Egyes tartalmak esetében szükség lehet az alkalmazás újraindítására az új nyelvi beállítás teljes alkalmazásához.";
"restart_required" = "Újraindítás szükséges";
"restart_app_message" = "A nyelvi változtatások teljes alkalmazásához ké<PERSON>, indítsa újra az alkalmazást.";
"restart_now" = "Újraindítás most";
"restart_later" = "Újraindítás később";

// MARK: - Common UI Elements
"close" = "Bezárás";
"cancel" = "Mégse";
"save" = "Mentés";
"edit" = "Szerkesztés";
"delete" = "Törlés";
"done" = "Kész";
"next" = "Következő";
"back" = "Vissza";
"confirm" = "Megerősítés";
"error" = "Hiba";
"success" = "Sikeres";
"warning" = "Figyelmeztetés";
"loading" = "Betöltés...";
"search" = "Keresés";
"settings" = "Beállítások";
"help" = "Súgó";
"about" = "Névjegy";
"menu" = "Menü";
"understand" = "Megértem";

// MARK: - Navigation
"navigation" = "Navigáció";
"start_navigation" = "Navigáció indítása";

// MARK: - Subscription
"subscription" = "Előfizetés";
"upgrade_to_pro" = "Frissítés Pro verzióra";
"upgrade_description" = "Egy kattintásos navigációs csoportosítás, 60x gyorsabb a kézi műveletnél";
"restore_purchases" = "Vásárlások visszaállítása";
"learn_more" = "Tudjon meg többet";
"upgrade_your_plan" = "Frissítse tervét";
"one_click_navigation_description" = "Egy kattintásos navigációs csoportosítás címek, időt és üzemanyagot takarít meg";
"current_plan" = "Jelenlegi terv";
"upgrade" = "Frissítés";
"maybe_later" = "Talán később";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Ingyenes";
"pro_tier_price" = "$29.99/hó";
"expert_tier_price" = "$249.99/év";
"free_tier_description" = "Tökéletes magánszemélyek és kis vállalkozások számára, kevés megállóval";
"pro_tier_description" = "Azoknak a felhasználóknak, akiknek egy kattintásos navigációs csoportosításra és korlátlan címekre van szükségük";
"expert_tier_description" = "Ugyanaz, mint a Pro, de 31%-ot takarít meg az éves tervvel";
"route_optimization" = "Útvonal optimalizálás";
"unlimited_routes" = "Korlátlan útvonalak";
"unlimited_optimizations" = "Korlátlan optimalizálások";
"max_15_addresses" = "Maximum 15 cím útvonalonként";
"save_fuel_30" = "Akár 30% üzemanyag megtakarítás";
"unlimited_addresses" = "✨ Korlátlan címek ✨";
"one_click_navigation" = "⚡ Egy kattintásos navigációs csoportosítás - Akár 60x gyorsabb ⚡";
"package_finder" = "Csomag helymeghatározó";
"annual_savings" = "Éves terv megtakarítások";
"switched_to_free" = "Váltás ingyenes tervre";
"switched_to_subscription" = "Váltás előfizetéses tervre";
"unlimited_stops" = "Korlátlan megállók";
"plan_as_many_stops_as_needed" = "Annyi kézbesítési pontot adjon hozzá, amennyire szüksége van";

// MARK: - Address Input
"enter_or_search_address" = "Cím megadása vagy keresése";
"search_results_count" = "Keresési eredmények: %d";
"no_matching_addresses" = "Nem található megfelelő cím";
"search_address_failed" = "Cím keresése sikertelen: %@";
"address_search_no_response" = "Cím keresése nem válaszol";
"cannot_get_address_coordinates" = "Nem lehet megszerezni a cím koordinátáit";
"speech_recognizer_unavailable" = "Beszédfelismerő nem elérhető";
"microphone_permission_denied" = "Mikrofon engedély megtagadva";
"speech_recognition_permission_denied" = "Beszédfelismerés engedély megtagadva";
"listening" = "Hallgatás...";
"recording_failed" = "Felvétel indítása sikertelen: %@";
"cannot_get_coordinates_retry" = "Nem lehet megszerezni a cím koordinátáit, kérjük adja meg kézzel vagy próbálja újra";
"cannot_create_recognition_request" = "Nem lehet felismerési kérést létrehozni";

// MARK: - Saved Address Picker
"search_address" = "Cím keresése";
"no_saved_addresses" = "Nincsenek mentett címek";
"no_saved_addresses_description" = "Még nem mentett címeket, vagy nincsenek a szűrési feltételeknek megfelelő címek";
"select_address_book" = "Címjegyzék kiválasztása";

// MARK: - Menu
"menu" = "Menü";
"routes" = "Útvonalak";
"address_book" = "Címjegyzék";
"saved_routes" = "Mentett útvonalak";
"manage_your_routes" = "Útvonaltervezés kezelése";
"manage_your_addresses" = "Gyakran használt címek kezelése";
"settings" = "Beállítások";
"preferences" = "Beállítások";
"set_custom_start_point" = "Egyéni kiindulópont beállítása";
"current_start_point" = "Jelenlegi kiindulópont: %@";
"support" = "Támogatás";
"contact_us" = "Kapcsolat";
"contact_us_description" = "Kérdései vagy javaslatai vannak? Nyugodtan lépjen kapcsolatba velünk!";
"help_center" = "Súgó központ";
"subscription" = "Előfizetés";
"upgrade_to_pro" = "Frissítés Pro verzióra";
"upgrade_description" = "Egy kattintásos navigációs csoportosítás, 60x gyorsabb a kézi műveletnél";
"open_subscription_view" = "Előfizetési nézet közvetlen megnyitása";
"open_subscription_view_description" = "Köztes rétegek kihagyása, SubscriptionView közvetlen megjelenítése";
"restore_purchases_failed" = "Vásárlások visszaállítása sikertelen: %@";
"about" = "Névjegy";
"rate_us" = "Értékeljen minket";
"rate_us_description" = "Visszajelzése fontos számunkra és segít az alkalmazás fejlesztésében!";
"share_app" = "Alkalmazás megosztása";
"share_app_text" = "Próbálja ki a NaviBatch-et, egy fantasztikus útvonaltervező alkalmazást!";
"about_app" = "Az alkalmazásról";
"developer_tools" = "Fejlesztői eszközök";
"coordinate_debug_tool" = "Koordináta hibakereső eszköz";
"batch_fix_addresses" = "Címek tömeges javítása";
"clear_database" = "Adatbázis törlése";
"clear_database_confirmation" = "Ez törli az összes adatot, beleértve az útvonalakat, címeket és csoportokat. Ez a művelet nem vonható vissza. Biztos, hogy folytatni szeretné?";
"confirm_clear" = "Törlés megerősítése";
"version_info" = "Verzió %@ (%@)";
"current_system_language" = "Jelenlegi rendszer nyelv";
"reset_to_system_language" = "Visszaállítás rendszer nyelvre";
"language" = "Nyelv";
"language_settings" = "Nyelvi beállítások";

// MARK: - Address Edit
"address" = "Cím";
"coordinates" = "Koordináták";
"distance_from_current_location" = "Távolság a jelenlegi helytől";
"address_info" = "Cím információ";
"update_coordinates" = "Koordináták frissítése";
"fix_address" = "Cím javítása";
"prompt" = "Figyelmeztetés";
"confirm" = "Megerősítés";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Kérjük módosítsa a címet a koordináták frissítése előtt";
"coordinates_update_success" = "Koordináták sikeresen frissítve";
"coordinates_update_failure" = "Koordináták frissítése sikertelen";
"save_failure" = "Mentés sikertelen: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Nincsenek mentett címek";
"no_saved_addresses_message" = "Még nem mentett címeket";
"add_new_address" = "Új cím hozzáadása";
"address_title" = "Cím";
"add" = "Hozzáadás";
"refresh" = "Frissítés";
"notes" = "Megjegyzések";
"address_details" = "Cím részletei";
"favorite" = "Kedvencek";
"edit_address" = "Cím szerkesztése";
"cancel" = "Mégse";
"save" = "Mentés";
"confirm_delete" = "Törlés megerősítése";
"delete" = "Törlés";
"delete_address_confirmation" = "Biztos, hogy törölni szeretné ezt a címet? Ez a művelet nem vonható vissza.";
"edit" = "Szerkesztés";
"address_marker" = "Cím";
"address_label" = "Cím:";
"notes_label" = "Megjegyzések:";
"created_at_label" = "Létrehozva:";
"open_in_maps" = "Megnyitás térképen";
"copy_address" = "Cím másolása";
"address_details_title" = "Cím részletei";

// MARK: - Route Detail
"start_end_point" = "Kiindulási/Végpont";
"start_point" = "Kiindulópont";
"end_point" = "Végpont";
"route_info" = "Útvonal információ";
"address_count" = "Címek száma";
"address_count_format" = "%d cím";
"points_count_format" = "%d pont";
"additional_points_format" = "+%d pont";
"export_route" = "Útvonal exportálása";
"navigate" = "Navigálás";
"address_list" = "Címlista";
"no_addresses" = "Nincsenek címek";
"no_addresses_message" = "Ez az útvonal még nem tartalmaz címeket";

// MARK: - Route Bottom Sheet
"address_point_start" = "Kiindulópont";
"address_point_stop" = "Megálló pont";
"address_point_end" = "Végpont";
"route_name" = "Útvonal neve";
"save" = "Mentés";
"new_route" = "Új útvonal";
"saved_route" = "Mentett útvonal";
"edit" = "Szerkesztés";
"loading" = "Betöltés...";
"plan_route" = "Útvonal tervezése";
"clear_all" = "Összes törlése";
"avoid" = "Kerülendő:";
"toll_roads" = "Fizetős utak";
"highways" = "Autópályák";
"processing_addresses" = "Címek feldolgozása...";
"same_start_end_point" = "Ugyanazt a címet állította be kiindulási és végpontként";
"add_start_point" = "Kiindulópont hozzáadása";
"swipe_left_to_delete" = "← Balra húzás törléshez";
"delete" = "Törlés";
"add_new_address" = "Új cím hozzáadása";
"add_end_point" = "Végpont hozzáadása";

// MARK: - Simple Address Sheet
"address_title" = "Cím";
"enter_and_select_address" = "Cím megadása és kiválasztása";
"current_search_text" = "Jelenlegi keresési szöveg: %@";
"search_results_count" = "Search results: %d";
"no_matching_addresses" = "No matching addresses found";
"add_address" = "Cím hozzáadása";
"edit_address" = "Cím szerkesztése";
"selected_coordinates" = "Kiválasztott koordináták";
"company_name_optional" = "Cégnév (opcionális)";
"url_optional" = "URL (opcionális)";
"favorite_address" = "Kedvenc cím";
"set_as_start_and_end" = "Beállítás kiindulási és végpontként";
"address_book" = "Címjegyzék";
"batch_paste" = "Tömeges beillesztés";
"file_import" = "Fájl importálás";
"web_download" = "Webes letöltés";
"cancel" = "Mégse";
"saving" = "Mentés...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Kézbesítési pont kezelés";
"information_category" = "Információ kategória";
"address_info" = "Cím információ";
"package_info" = "Csomag információ";
"vehicle_position" = "Jármű pozíció";
"delivery_info" = "Kézbesítési információ";
"navigation" = "Navigálás";
"take_photo_record" = "Fénykép készítése";
"update_status" = "Állapot frissítése";
"done" = "Kész";
"edit_address_button" = "Cím szerkesztése";
"coordinates" = "Koordináták";
"access_instructions" = "Hozzáférési utasítások";
"add_access_instructions" = "Hozzáférési utasítások hozzáadása...";
"package_count" = "Csomagok száma";
"package_size" = "Csomag mérete";
"package_type" = "Csomag típusa";
"mark_as_important" = "Megjelölés fontosként";
"select_package_position" = "Csomag pozíciójának kiválasztása a járműben";
"vehicle_area" = "Jármű terület";
"left_right_position" = "Bal/jobb pozíció";
"vehicle_position_front" = "Elülső";
"vehicle_position_middle" = "Középső";
"vehicle_position_back" = "Hátsó";
"vehicle_position_left" = "Bal";
"vehicle_position_right" = "Jobb";
"vehicle_position_floor" = "Alsó";
"vehicle_position_shelf" = "Felső";
"height_position" = "Magassági pozíció";
"delivery_type" = "Kézbesítés típusa";
"delivery_status" = "Kézbesítési állapot";
"order_info" = "Rendelés információ";
"order_information" = "Rendelés információ";
"order_number" = "Rendelésszám";
"enter_order_number" = "Rendelésszám megadása";
"tracking_number" = "Követési szám";
"enter_tracking_number" = "Követési szám megadása";
"time_info" = "Idő információ";
"time_information" = "Idő információ";
"estimated_arrival_time" = "Becsült érkezési idő";
"anytime" = "Bármikor";
"stop_time" = "Megállási idő";
"minutes_format" = "%d perc";
"photo_record" = "Fénykép felvétel";
"door_number_photo" = "Házszám fénykép";
"package_label_photo" = "Csomag címke fénykép";
"placement_photo" = "Elhelyezési fénykép";
"door_number_desc" = "Készítsen tiszta fényképet a házszámról vagy utcaszámról, biztosítva, hogy a számok/betűk láthatóak legyenek";
"package_label_desc" = "Készítsen fényképet a csomag címkéjéről, biztosítva, hogy a címzett információi tisztán láthatóak legyenek";
"placement_desc" = "Készítsen fényképet a csomag végső elhelyezési helyéről";
"photo_captured" = "Fénykép elkészítve";
"photo_captured_options" = "Fénykép elkészítve, szeretné folytatni a következő fényképpel vagy befejezni a jelenlegit?";
"continue_to_next_photo" = "Folytatás a következő fényképpel - %@";
"retake" = "Újrafényképezés";
"tap_to_capture" = "Koppintás a fényképezéshez";
"flash_auto" = "Automatikus vaku";
"flash_on" = "Vaku bekapcsolása";
"flash_off" = "Vaku kikapcsolása";
"photo_record_completed" = "Fénykép felvétel befejezve";
"photo_confirmation" = "Fényképezés megerősítése";
"error" = "Hiba";
"ok" = "OK";
"complete_photo_capture" = "Fényképezés befejezése";
"tap_to_capture" = "Koppintás a fényképezéshez";
"photo_instructions" = "Koppintson minden fénykép kártyára a fényképezéshez. Minden fényképet be kell fejezni.";
"photo_options" = "Fénykép opciók";
"view_photo" = "Fénykép megtekintése";
"retake_photo" = "Újrafényképezés";
"saving_photos" = "Fényképek mentése...";
"completed" = "Befejezve";
"not_taken" = "Nem készült";
"route_options" = "Útvonal opciók";
"avoid_tolls" = "Fizetős utak kerülése";
"avoid_highways" = "Autópályák kerülése";
"optimize_route" = "Útvonal optimalizálása";
"optimizing" = "Optimalizálás...";
"optimization_complete" = "Optimalizálás befejezve";

// MARK: - Addresses
"addresses" = "Cím";
"add_address" = "Cím hozzáadása";
"edit_address" = "Cím szerkesztése";
"delete_address" = "Cím törlése";
"address_details" = "Cím részletei";
"street" = "Utca";
"city" = "Város";
"state" = "Állam/Megye";
"country" = "Ország";
"postal_code" = "Irányítószám";
"phone" = "Telefon";
"email" = "E-mail";
"website" = "Weboldal";
"company" = "Cég";
"notes" = "Megjegyzések";
"coordinates" = "Koordináták";
"latitude" = "Szélesség";
"longitude" = "Hosszúság";
"geocoding_error" = "Geokódolási hiba";
"address_validation" = "Cím ellenőrzés";
"invalid_addresses" = "Érvénytelen címek";
"fix_addresses" = "Címek javítása";

// MARK: - Routes
"route" = "Útvonalak";
"routes" = "Útvonalak";
"select_address_point" = "Címpont kiválasztása";
"select_delivery_points" = "Kézbesítési pontok kiválasztása";
"create_delivery_route" = "Kézbesítési útvonal létrehozása";
"view_saved_routes" = "Mentett útvonalak megtekintése";
"create_route" = "Útvonal létrehozása";
"edit_route" = "Útvonal szerkesztése";
"delete_route" = "Útvonal törlése";
"route_name" = "Útvonal neve";
"route_details" = "Útvonal részletei";
"selected_addresses" = "%d cím kiválasztva";
"reached_limit" = "Elérte a limitet";
"can_select_more" = "Még %d választható";
"navigate_button" = "Navigálás";
"create_group" = "Csoport létrehozása";
"start_point" = "Kiindulópont";
"end_point" = "Végpont";
"waypoints" = "Köztes pontok";
"total_distance" = "Teljes távolság";
"estimated_time" = "Becsült idő";
"route_summary" = "Útvonal összefoglaló";
"route_options" = "Útvonal opciók";
"route_saved" = "Útvonal mentve";
"route_optimized" = "Útvonal optimalizálva";
"optimizing_route" = "Útvonal optimalizálása...";
"completed_percent" = "%d%% befejezve";
"processing_points" = "Feldolgozás: %d/%d";
"estimated_remaining_time" = "Becsült hátralévő idő: %@";

// MARK: - Delivery
"delivery" = "Kézbesítés";
"delivery_confirmation" = "Kézbesítés megerősítése";
"take_photo" = "Fényképezés";
"signature" = "Aláírás";
"delivery_notes" = "Kézbesítési megjegyzések";
"delivery_status" = "Kézbesítési állapot";
"delivered" = "Kézbesítve";
"not_delivered" = "Nem kézbesítve";
"delivery_time" = "Kézbesítési idő";
"delivery_date" = "Kézbesítési dátum";
"package_details" = "Csomag részletei";
"package_id" = "Csomag ID";
"package_weight" = "Csomag súlya";
"package_dimensions" = "Csomag méretei";
"recipient_name" = "Címzett neve";
"recipient_phone" = "Címzett telefonja";

// MARK: - Groups
"groups" = "Csoportok";
"saved_groups" = "Mentett csoportok";
"create_group" = "Csoport létrehozása";
"edit_group" = "Csoport szerkesztése";
"delete_group" = "Csoport törlése";
"group_name" = "Csoport neve";
"group_details" = "Csoport részletei";
"auto_grouping" = "Automatikus csoportosítás";
"group_by" = "Csoportosítás alapja";
"add_to_group" = "Hozzáadás csoporthoz";
"remove_from_group" = "Eltávolítás csoportból";
"group_created" = "Csoport létrehozva";
"default_group_name_format" = "Csoport %d";
"auto_grouping_completed" = "Automatikus csoportosítás befejezve";
"auto_grouping_in_progress" = "Automatikus csoportosítás folyamatban...";
"create_group_every_14_addresses" = "Csoport létrehozása minden 14 címhez";
"create_delivery_group" = "Kézbesítési csoport létrehozása";
"enter_group_name" = "Csoportnév megadása";
"selected_delivery_points" = "Kiválasztott kézbesítési pontok";
"drag_to_adjust_order" = "Húzással módosítható a sorrend";

// MARK: - Subscription
"subscription" = "Előfizetés";
"free_plan" = "Ingyenes verzió";
"pro_plan" = "Pro verzió";
"expert_plan" = "Expert verzió";
"monthly" = "Havi terv";
"yearly" = "Éves terv";
"subscribe" = "Előfizetés";
"upgrade" = "Frissítés";
"upgrade_to_pro" = "Frissítés Pro verzióra";
"manage_subscription" = "Előfizetés kezelése";
"restore_purchases" = "Vásárlások visszaállítása";
"subscription_benefits" = "Előfizetési előnyök";
"free_trial" = "Ingyenes próbaverzió";
"price_per_month" = "%@ havonta";
"price_per_year" = "%@ évente";
"save_percent" = "%@% megtakarítás";
"current_plan" = "Jelenlegi terv";
"subscription_terms" = "Előfizetési feltételek";
"privacy_policy" = "Adatvédelmi irányelvek";
"terms_of_service" = "Szolgáltatási feltételek";

// MARK: - Import/Export
"import" = "Importálás";
"export" = "Exportálás";
"import_addresses" = "Címek importálása";
"export_addresses" = "Címek exportálása";
"import_from_file" = "Importálás fájlból";
"export_to_file" = "Exportálás fájlba";
"file_format" = "Fájlformátum";
"csv_format" = "CSV formátum";
"excel_format" = "Excel formátum";
"json_format" = "JSON formátum";
"import_success" = "Sikeresen importálva %d cím, mind érvényes koordinátákkal.";
"export_success" = "Exportálás sikeres";
"import_error" = "Importálási hiba";
"export_error" = "Exportálási hiba";

// MARK: - Navigation
"navigate" = "Navigálás";

// MARK: - Look Around
"show_look_around" = "Nézet megjelenítése";
"hide_look_around" = "Nézet elrejtése";

// MARK: - Map
"map" = "Térkép";
"map_type" = "Térkép típusa";
"standard" = "Normál";
"satellite" = "Műhold";
"hybrid" = "Hibrid";
"show_traffic" = "Forgalom megjelenítése";
"current_location" = "Jelenlegi helyzet";
"directions" = "Útvonal útmutatás";
"distance_to" = "Távolság";
"eta" = "Becsült érkezési idő";
"look_around" = "Körülnézés";
"locating_to_glen_waverley" = "Glen Waverley-re pozicionálás";

// MARK: - Errors and Warnings
"network_error" = "Hálózati hiba";
"location_error" = "Helyzet hiba";
"permission_denied" = "Engedély megtagadva";
"location_permission_required" = "Helyzet engedély szükséges";
"camera_permission_required" = "Kamera engedély szükséges";
"photo_library_permission_required" = "Fotótár engedély szükséges";
"please_try_again" = "Kérjük próbálja újra";
"something_went_wrong" = "Valami hiba történt";
"invalid_input" = "Érvénytelen bemenet";
"required_field" = "Kötelező mező";
"no_internet_connection" = "Nincs internetkapcsolat";
"server_error" = "Szerver hiba";
"timeout_error" = "Kérés időtúllépés";
"data_not_found" = "Adat nem található";
"selection_limit_reached" = "Kiválasztási limit elérve";
"selection_limit_description" = "Maximum %d címet választhat ki, %d-t választott ki";

// MARK: - Location Validation Status
"location_status_valid" = "Érvényes tartomány";
"location_status_warning" = "Figyelmeztetési tartomány";
"location_status_invalid" = "Érvénytelen helyzet";
"location_status_unknown" = "Ismeretlen állapot";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Érvénytelen: nulla koordináták (0,0)";
"coordinates_invalid_nan" = "Érvénytelen: nem numerikus koordináták";
"coordinates_out_of_range" = "Érvénytelen: koordináták az érvényes tartományon kívül";
"coordinates_far_from_user" = "Figyelmeztetés: a helyzet távol van a jelenlegi helyzetétől";
"coordinates_ocean" = "Figyelmeztetés: a helyzet óceánban vagy lakatlan területen lehet";

// MARK: - Batch Address Input
"batch_add_addresses" = "Címek tömeges hozzáadása";
"batch_address_input_placeholder" = "Kérjük adja meg vagy illessze be a címeket, soronként egy címet. Maximum 35 cím.";
"free_address_limit" = "Ingyenes verzió cím korlát";
"address_count_limit" = "Cím mennyiség korlát";
"free_version_max_addresses" = "Az ingyenes verzió maximum %d címet engedélyez.";
"current_addresses_remaining" = "Jelenleg %d cím van, csak %d címet lehet még hozzáadni.";
"current_route_address_limit" = "A jelenlegi útvonal %d címet tartalmaz, csak %d címet lehet még hozzáadni, összesen maximum %d címet.";
"selected_addresses_can_import" = "%d címet választott ki, ezek a címek importálhatók.";
"selected_addresses_exceeds" = "%d címet választott ki, %d-dal meghaladja a hozzáadható mennyiséget.";
"selected_addresses_all_importable" = "%d címet választott ki, mind importálható.";
"upgrade_for_unlimited_addresses" = "Frissítsen a prémium verzióra korlátlan címekért!";
"import_first_n_addresses" = "Csak az első %d importálása";
"import_all_addresses" = "Összes cím importálása";
"import_selected_addresses" = "Kiválasztott címek importálása";
"no_importable_addresses" = "Nincsenek importálható címek, kérjük ellenőrizze a cím korlátokat";
"please_enter_valid_address" = "Kérjük adjon meg legalább egy érvényes címet";

// MARK: - File Import
"import_success" = "Sikeresen importálva %d cím, mind érvényes koordinátákkal.";
"import_success_with_warnings" = "Sikeresen importálva %d cím, ebből %d cím koordinátái normálisak, %d címnél figyelmeztetések vannak.\n\nA figyelmeztetéssel rendelkező címek meg vannak jelölve, az importálás után kézzel javíthatók.";

// MARK: - Web Download
"web_download" = "Webes letöltés";
"supported_formats" = "Támogatott formátumok";
"supported_format_csv" = "• CSV fájlok: a cím oszlopnak teljes címeket kell tartalmaznia";
"supported_format_json" = "• JSON adatok: cím mezőket tartalmazó tömb";
"supported_format_text" = "• Egyszerű szöveg: soronként egy cím";
"download_history" = "Letöltési előzmények";
"upgrade_to_premium" = "Frissítés prémium verzióra";
"input_address_data_url" = "Cím adat URL megadása";
"import_result" = "Importálási eredmény";
"import_addresses" = "Címek importálása";
"downloading" = "Letöltés...";
"processing_data" = "Adatok feldolgozása...";
"google_drive_download_failed" = "Google Drive letöltés sikertelen";
"second_attempt_invalid_data" = "Második letöltési kísérlet érvénytelen adatokat adott vissza";
"cannot_parse_json" = "Nem lehet elemezni a JSON adatokat, kérjük ellenőrizze a fájl formátumot";
"cannot_parse_json_with_error" = "Nem lehet elemezni a JSON adatokat: %@";
"cannot_get_address_coordinates" = "Nem lehet megszerezni a cím koordinátáit";
"cannot_read_file" = "Nem lehet olvasni a fájlt: %@";
"success" = "Sikeres";
"warning" = "Figyelmeztetés";
"failed" = "Kézbesítés sikertelen";
"no_matching_addresses" = "No matching addresses found";
"no_valid_addresses" = "Nem találhatók érvényes címek";
"confirm" = "Megerősítés";
"processing_addresses" = "Címek feldolgozása...";
"supports_file_types" = "Támogatja a CSV, TXT és JSON fájlokat";
"tap_to_select_file" = "Koppintson a fájl kiválasztásához";
"import_addresses" = "Címek importálása";
"company_name_optional" = "Cégnév (opcionális)";
"input_company_name" = "Cégnév megadása (opcionális)";
"imported_addresses_count" = "%d cím importálva";
"select_all" = "Összes kiválasztása";
"excel_format_not_supported" = "Excel formátum nem támogatott";
"no_matching_addresses" = "Nem található megfelelő cím";

// MARK: - Import Limits
"import_failed" = "Importálás sikertelen";
"no_importable_addresses" = "Nincsenek importálható címek, kérjük ellenőrizze a cím korlátokat";
"free_version_address_limit" = "Az ingyenes verzió maximum %d címet engedélyez.";
"current_address_count" = "Jelenleg %d cím van, csak %d címet lehet még hozzáadni.";
"can_import_selected" = "%d címet választott ki, ezek a címek importálhatók.";
"selected_exceeds_limit" = "%d címet választott ki, %d-dal meghaladja a hozzáadható mennyiséget.";
"upgrade_to_premium_unlimited" = "Frissítsen a prémium verzióra korlátlan címekért!";
"route_address_limit" = "A jelenlegi útvonal %d címet tartalmaz, csak %d címet lehet még hozzáadni, összesen maximum %d címet.";
"free_version_limit" = "Ingyenes verzió cím korlát";
"address_count_limit" = "Cím mennyiség korlát";
"import_selected_addresses" = "Kiválasztott címek importálása";
"import_first_n" = "Csak az első %d importálása";
"import_all_n" = "Mind a %d importálása";
"cannot_import" = "Nem lehet importálni";
"select_at_least_one" = "Kérjük válasszon ki legalább egy címet";

// MARK: - Import Results
"no_valid_addresses_found" = "Nem találhatók érvényes címek";
"import_success_all_valid" = "Sikeresen importálva %d cím, minden cím koordinátái normálisak.";
"import_success_some_warnings" = "Sikeresen importálva %d cím, ebből %d cím koordinátái normálisak, %d címnél nem lehet koordinátákat szerezni.";

// MARK: - Warnings
"invalid_csv_row" = "Érvénytelen CSV sor";
"distance_warning" = "200 km-nél távolabb a jelenlegi helyzettől";
"not_in_australia" = "Koordináták nem Ausztrália területén";
"cannot_get_coordinates" = "Nem lehet megszerezni a cím koordinátáit";
"empty_address" = "Üres cím";
"invalid_address_data" = "Érvénytelen cím adatok";

// MARK: - Saved Groups
"saved_groups" = "Mentett csoportok";
"no_saved_groups" = "Nincsenek mentett csoportok";
"select_points_create_groups" = "Válasszon kézbesítési pontokat és hozzon létre csoportokat a könnyebb kezeléshez";
"group_name" = "Csoport neve";
"group_details" = "Csoport részletei";
"navigate_to_these_points" = "Navigálás ezekhez a pontokhoz";
"confirm_remove_address" = "Biztos, hogy el szeretné távolítani a \"%@\" címet a csoportból?";
"confirm_remove_this_address" = "Biztos, hogy el szeretné távolítani ezt a címet a csoportból?";
"addresses_count" = "%d cím";
"no_saved_routes" = "Nincsenek mentett útvonalak";
"no_saved_routes_description" = "Még nem mentett útvonalakat";
"all_routes" = "Összes útvonal";
"address_count_format_simple" = "%d cím";
"delete_all_routes" = "Összes útvonal törlése";
"navigate_to_all_points" = "Navigálás minden ponthoz";
"confirm_navigate_to_route" = "Biztos, hogy navigálni szeretne a \"%@\" útvonal minden pontjához?";
"temp_navigation_group" = "Ideiglenes navigációs csoport";

// MARK: - Route Management
"route_management" = "Útvonal kezelés";
"route_info" = "Útvonal információ";
"route_name" = "Útvonal neve";
"route_addresses" = "Útvonal címek";
"no_addresses_in_route" = "Ez az útvonal nem tartalmaz címeket";
"must_keep_one_route" = "Legalább egy útvonalat meg kell tartani";
"confirm_delete_route" = "Biztos, hogy törölni szeretné a \"%@\" útvonalat? Ez a művelet nem vonható vissza.";
"confirm_delete_all_routes" = "Összes útvonal törlésének megerősítése";
"confirm_delete_all_routes_message" = "Biztos, hogy törölni szeretné az összes útvonalat? Ez a művelet nem vonható vissza.";
"delete_all" = "Összes törlése";

// MARK: - Navigation Buttons
"navigate" = "Navigálás";

// MARK: - GroupDetailView
"points_count_format" = "%d pont";

// MARK: - DeliveryPointDetailView
"address_information" = "Cím információ";
"group_belonging" = "Tartozó csoport";
"view_map" = "Térkép megtekintése";
"delivery_status" = "Kézbesítési állapot";
"notes" = "Megjegyzések";
"delete_delivery_point" = "Kézbesítési pont törlése";
"delivery_point_details" = "Kézbesítési pont részletei";
"confirm_deletion" = "Törlés megerősítése";
"delete_delivery_point_confirmation" = "Biztos, hogy törölni szeretné ezt a kézbesítési pontot? Ez a művelet nem vonható vissza.";

// MARK: - Delivery Photos
"delivery_photos" = "Kézbesítési fényképek";
"view_delivery_photos" = "Kézbesítési fényképek megtekintése";
"no_photos_taken" = "Még nem készültek fényképek";
"take_photos" = "Fényképek készítése";
"loading_photos" = "Fényképek betöltése...";
"photo_not_found" = "Fénykép nem található";
"photo_deleted" = "Fénykép törölve";
"door_number_photo" = "Házszám fénykép";
"package_label_photo" = "Csomag címke fénykép";
"placement_photo" = "Elhelyezési fénykép";
"share_photos" = "Fénykép megosztása";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Fényképezés megerősítése";
"door_number_photo_title" = "Utcaszám/házszám fénykép";
"package_label_photo_title" = "Csomag címke fénykép";
"placement_photo_title" = "Elhelyezési hely fénykép";
"door_number_photo_desc" = "Készítsen tiszta fényképet a házszámról, biztosítva, hogy a számok/betűk láthatóak legyenek";
"package_label_photo_desc" = "Készítsen fényképet a csomag címkéjéről, biztosítva, hogy a címzett információi tisztán láthatóak legyenek";
"placement_photo_desc" = "Készítsen fényképet a csomag végső elhelyezési helyéről";
"swipe_to_switch" = "Csúsztassa a fénykép típusának váltásához";
"complete_photos" = "Fényképezés befejezése";
"saving_photos" = "Fényképek mentése...";
"photo_save_success" = "Fénykép sikeresen mentve";
"photo_save_failure" = "Fénykép mentése sikertelen";
"retake_photo" = "Újrafényképezés";
"no_photos_found" = "Nem találhatók fényképek";
"photos_deleted_or_not_taken" = "Lehetséges, hogy a fényképek törölve lettek vagy még nem készültek";
"share_photo" = "Fénykép megosztása";
"photo_capture_preview" = "Előnézeti mód - kamera szimuláció";
"photo_capture_close" = "Bezárás";
"camera_start_failed" = "Kamera indítása sikertelen";
"camera_start_failed_retry" = "Nem lehet elindítani a kamerát, kérjük próbálja újra";
"camera_init_failed" = "Kamera inicializálása sikertelen";
"camera_access_failed" = "Nem lehet hozzáférni a kamerához";
"photo_processing_failed" = "Fényképezés sikertelen";
"photo_processing_failed_retry" = "Nem lehet befejezni a fénykép feldolgozását, kérjük próbálja újra";
"photo_capture_progress" = "Haladás: %d/%d";
"photo_captured_continue" = "Fényképezés befejezve, folytatás %@-val";
"loading_photos" = "Fényképek betöltése...";
"cancel" = "Mégse";

// MARK: - Delivery Status
"pending" = "Kézbesítésre vár";
"in_progress" = "Kézbesítés alatt";
"completed" = "Befejezve";
"failed" = "Kézbesítés sikertelen";
"update_status" = "Állapot frissítése";
"select_delivery_status" = "Kézbesítési állapot kiválasztása";
"select_failure_reason" = "Sikertelenség okának kiválasztása";
"delivered" = "Kézbesítve";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Ügyfél nincs otthon";
"failure_reason_wrong_address" = "Hibás cím";
"failure_reason_no_access" = "Nem lehet bejutni a helyre";
"failure_reason_rejected" = "Csomag elutasítva";
"failure_reason_other" = "Egyéb okok";
"enter_custom_reason" = "Konkrét ok megadása";
"custom_reason_placeholder" = "Kérjük írja le a konkrét okot...";
"custom_reason_required" = "Kérjük adja meg a konkrét okot";
"failure_reason_required" = "Kérjük válassza ki a sikertelenség okát";

// MARK: - Address Validation
"address_validation_failed" = "Cím ellenőrzés sikertelen";

