/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Nyelvi beállítások";
"system_language" = "Rendszer nyelv";
"system_language_section" = "Rendszer beállítások";
"languages" = "Nyelvek";
"language_info_title" = "A nyelvi beállításokról";
"language_info_description" = "A nyelvi beállítás módosítása után az alkalmazás a kiválasztott nyelven jeleníti meg a szöveget. Egyes tartalmak esetében szükség lehet az alkalmazás újraindítására az új nyelvi beállítás teljes alkalmazásához.";
"restart_required" = "Újraindítás szükséges";
"restart_app_message" = "A nyelvi változtatások teljes alkalmazásához kér<PERSON>, indítsa újra az alkalmazást.";
"restart_now" = "Újraindítás most";
"restart_later" = "Újraindítás később";
"close" = "Bezárás";
"cancel" = "Mégse";
"save" = "Mentés";
"edit" = "Szerkesztés";
"delete" = "Törlés";
"done" = "Kész";
"next" = "Következő";
"back" = "Vissza";
"confirm" = "Megerősítés";
"error" = "Hiba";
"success" = "Sikeres";
"warning" = "Figyelmeztetés";
"unknown_error" = "Ismeretlen hiba";
"loading" = "Betöltés...";
"search" = "Keresés";
"settings" = "Beállítások";
"help" = "Súgó";
"about" = "Névjegy";
"menu" = "Menü";
"understand" = "Megértem";
"navigation" = "Navigálás";
"start_navigation" = "Navigáció indítása";
"subscription" = "Előfizetés";
"upgrade_to_pro" = "Frissítés Pro verzióra";
"upgrade_description" = "Egy kattintásos navigációs csoportosítás, 60x gyorsabb a kézi műveletnél";
"restore_purchases" = "Vásárlások visszaállítása";
"learn_more" = "Tudjon meg többet";
"upgrade_your_plan" = "Frissítse tervét";
"one_click_navigation_description" = "Egy kattintásos navigációs csoportosítás címek, időt és üzemanyagot takarít meg";
"current_plan" = "Jelenlegi terv";
"upgrade" = "Frissítés";
"maybe_later" = "Talán később";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Ingyenes";
"pro_tier_price" = "$9.99/hó";
"expert_tier_price" = "$59.99/év";
"free_tier_description" = "Tökéletes magánszemélyek és kis vállalkozások számára, kevés megállóval";
"pro_tier_description" = "Azoknak a felhasználóknak, akiknek egy kattintásos navigációs csoportosításra és korlátlan címekre van szükségük";
"expert_tier_description" = "Ugyanaz, mint a Pro, de 50%-ot takarít meg az éves tervvel";
"route_optimization" = "Útvonal optimalizálás";
"unlimited_routes" = "Korlátlan útvonalak";
"unlimited_optimizations" = "Korlátlan optimalizálások";
"max_15_addresses" = "Maximum 15 cím útvonalonként";
"save_fuel_30" = "Akár 30% üzemanyag megtakarítás";
"unlimited_addresses" = "✨ Korlátlan címek ✨";
"one_click_navigation" = "⚡ Egy kattintásos navigációs csoportosítás - Akár 60x gyorsabb ⚡";
"package_finder" = "Csomag helymeghatározó";
"annual_savings" = "Éves terv megtakarítások";
"switched_to_free" = "Váltás ingyenes tervre";
"switched_to_subscription" = "Váltás előfizetéses tervre";
"unlimited_stops" = "Korlátlan megállók";
"plan_as_many_stops_as_needed" = "Annyi kézbesítési pontot adjon hozzá, amennyire szüksége van";
"expires_in_days" = "Lejár %d nap múlva";
"trial_expires_in_days" = "Próbaidőszak lejár %d nap múlva";
"expired" = "Lejárt";
"subscription_expires_on" = "Előfizetés lejár: %@";
"subscription_active_until" = "Előfizetés aktív: %@-ig";
"enter_or_search_address" = "Cím megadása vagy keresése";
"search_results_count" = "Search results: %d";
"no_matching_addresses" = "Nem található megfelelő cím";
"search_address_failed" = "Cím keresése sikertelen: %@";
"address_search_no_response" = "Cím keresése nem válaszol";
"cannot_get_address_coordinates" = "Nem lehet megszerezni a cím koordinátáit";

"cannot_get_coordinates_retry" = "Nem lehet megszerezni a cím koordinátáit, kérjük adja meg kézzel vagy próbálja újra";

"image_address_recognition" = "Címfelismerés képből";
"select_images" = "Képek kiválasztása";
"select_multiple_images" = "Többszörös kiválasztás támogatott";
"processing_images" = "Képek feldolgozása...";
"processing_image_progress" = "Kép feldolgozása %d / %d";
"recognizing_text" = "Szöveg felismerése...";
"geocoding_addresses" = "Címkoordináták lekérése...";
"recognition_complete" = "Felismerés befejezve";
"no_text_recognized" = "Nem ismert fel szöveg";
"no_addresses_found" = "Nem találhatók érvényes címek";
"image_recognition_failed" = "Képfelismerés sikertelen";
"image_recognition_error" = "Képfelismerési hiba: %@";
"text_recognition_failed" = "Szövegfelismerés sikertelen";
"address_parsing_failed" = "Címelemzés sikertelen";
"select_addresses_to_add" = "Hozzáadandó címek kiválasztása";
"recognized_addresses" = "Felismert címek";
"address_coordinates" = "Cím koordinátái";
"toggle_address_selection" = "Címkiválasztás váltása";
"remove_address" = "Cím eltávolítása";
"confirm_selected_addresses" = "Kiválasztott címek megerősítése";
"no_addresses_selected" = "Nincsenek kiválasztott címek";
"image_processing_cancelled" = "Képfeldolgozás megszakítva";
"unsupported_image_format" = "Nem támogatott képformátum";
"image_too_large" = "Képfájl túl nagy";
"image_recognition_permission_required" = "Fotótár hozzáférési engedély szükséges";
"ocr_language_detection" = "Automatikus nyelvfelismerés";
"improve_image_quality" = "Győződjön meg róla, hogy a kép tiszta és a szöveg látható";
"address_validation_in_progress" = "Címek ellenőrzése...";
"batch_address_import" = "Tömeges címimportálás";
"validated_addresses_count" = "%d cím ellenőrizve";
"addresses_with_issues" = "%d címnél problémák vannak";
"select_all" = "Összes kiválasztása";
"import_selected" = "Kiválasztottak importálása";
"validating_addresses" = "Címek ellenőrzése...";
"empty_address" = "Üres cím";
"invalid_coordinates" = "Érvénytelen koordináták";
"coordinate_warning" = "Koordináta figyelmeztetés";
"address_validation_issue" = "Címellenőrzési probléma";
"cannot_get_coordinates" = "Nem lehet megszerezni a cím koordinátáit";
"no_importable_addresses" = "Nincsenek importálható címek, kérjük ellenőrizze a cím korlátokat";
"free_version_max_addresses" = "Az ingyenes verzió maximum %d címet engedélyez.";
"valid" = "Érvényes";
"with_issues" = "Problémákkal";
"low_confidence_address" = "Alacsony megbízhatóságú címellenőrzés";
"address_validation_failed" = "Cím ellenőrzés sikertelen";
"current_addresses_remaining" = "Jelenleg %d cím van, csak %d címet lehet még hozzáadni.";
"can_import_selected" = "%d címet választott ki, ezek a címek importálhatók.";
"selected_exceeds_limit" = "%d címet választott ki, %d-dal meghaladja a hozzáadható mennyiséget.";
"selected_addresses_all_importable" = "%d címet választott ki, mind importálható.";
"upgrade_for_unlimited_addresses" = "Frissítsen a prémium verzióra korlátlan címekért!";
"current_route_address_limit" = "A jelenlegi útvonal %d címet tartalmaz, csak %d címet lehet még hozzáadni, összesen maximum %d címet.";
"import_all_addresses" = "Összes cím importálása";
"import_first_n" = "Csak az első %d importálása";
"import_selected_addresses" = "Kiválasztott címek importálása";
"upgrade_to_premium" = "Frissítés prémium verzióra";
"batch_add_addresses" = "Címek tömeges hozzáadása";
"batch_address_input_placeholder" = "Kérjük adja meg vagy illessze be a címeket, soronként egy címet. Maximum 35 cím.";
"search_address" = "Cím keresése";
"no_saved_addresses" = "Nincsenek mentett címek";
"no_saved_addresses_description" = "Még nem mentett címeket, vagy nincsenek a szűrési feltételeknek megfelelő címek";
"select_address_book" = "Címjegyzék kiválasztása";
"routes" = "Útvonalak";
"address_book" = "Címjegyzék";
"saved_routes" = "Mentett útvonalak";
"manage_your_routes" = "Útvonaltervezés kezelése";
"manage_your_addresses" = "Gyakran használt címek kezelése";
"preferences" = "Beállítások";
"set_custom_start_point" = "Egyéni kiindulópont beállítása";
"current_start_point" = "Jelenlegi kiindulópont: %@";
"support" = "Támogatás";
"contact_us" = "Kapcsolat";
"contact_us_description" = "Kérdései vagy javaslatai vannak? Nyugodtan lépjen kapcsolatba velünk!";
"help_center" = "Súgó központ";
"quick_actions" = "Gyors műveletek";
"main_features" = "Főbb funkciók";
"support_help" = "Támogatás és segítség";
"customize_app_settings" = "Alkalmazás beállításainak testreszabása";
"unlock_all_features" = "Összes funkció feloldása";
"get_help_support" = "Segítség és támogatás kérése";
"app_info_version" = "Alkalmazás információ és verzió";
"dev_tools" = "Fejlesztői eszközök";
"debug_testing_tools" = "Hibakeresési és tesztelési eszközök";
"version" = "Verzió";
"addresses" = "Cím";
"limited_to_20_addresses" = "20 címre korlátozva";
"all_premium_features" = "Összes prémium funkció";
"open_subscription_view" = "Előfizetési nézet közvetlen megnyitása";
"open_subscription_view_description" = "Köztes rétegek kihagyása, SubscriptionView közvetlen megjelenítése";
"restore_purchases_failed" = "Vásárlások visszaállítása sikertelen: %@";
"rate_us" = "Értékeljen minket";
"rate_us_description" = "Visszajelzése fontos számunkra és segít az alkalmazás fejlesztésében!";
"share_app" = "Alkalmazás megosztása";
"share_app_text" = "Próbálja ki a NaviBatch-et, egy fantasztikus útvonaltervező alkalmazást!";
"about_app" = "Az alkalmazásról";
"developer_tools" = "Fejlesztői eszközök";
"coordinate_debug_tool" = "Koordináta hibakereső eszköz";
"batch_fix_addresses" = "Címek tömeges javítása";
"clear_database" = "Adatbázis törlése";
"clear_database_confirmation" = "Ez törli az összes adatot, beleértve az útvonalakat, címeket és csoportokat. Ez a művelet nem vonható vissza. Biztos, hogy folytatni szeretné?";
"confirm_clear" = "Törlés megerősítése";
"version_info" = "Verzió %@ (%@)";
"current_system_language" = "Jelenlegi rendszer nyelv";
"reset_to_system_language" = "Visszaállítás rendszer nyelvre";
"language" = "Nyelv";
"address" = "Cím";
"coordinates" = "Koordináták";
"distance_from_current_location" = "Távolság a jelenlegi helytől";
"address_info" = "Cím információ";
"update_coordinates" = "Koordináták frissítése";
"fix_address" = "Cím javítása";
"prompt" = "Figyelmeztetés";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Kérjük módosítsa a címet a koordináták frissítése előtt";
"coordinates_update_success" = "Koordináták sikeresen frissítve";
"coordinates_update_failure" = "Koordináták frissítése sikertelen";
"save_failure" = "Mentés sikertelen: %@";
"no_saved_addresses_title" = "Nincsenek mentett címek";
"no_saved_addresses_message" = "Még nem mentett címeket";
"add_new_address" = "Új cím hozzáadása";
"address_title" = "Cím";
"add" = "Hozzáadás";
"refresh" = "Frissítés";
"notes" = "Megjegyzések";
"address_details" = "Cím részletei";
"favorite" = "Kedvencek";
"edit_address" = "Cím szerkesztése";
"confirm_delete" = "Törlés megerősítése";
"delete_address_confirmation" = "Biztos, hogy törölni szeretné ezt a címet? Ez a művelet nem vonható vissza.";
"address_marker" = "Cím";
"address_label" = "Cím:";
"notes_label" = "Megjegyzések:";
"created_at_label" = "Létrehozva:";
"open_in_maps" = "Megnyitás térképen";
"copy_address" = "Cím másolása";
"address_details_title" = "Cím részletei";
"start_end_point" = "Kiindulási/Végpont";
"start_point" = "Kiindulópont";
"end_point" = "Végpont";
"route_info" = "Útvonal információ";
"address_count" = "Címek száma";
"address_count_format" = "%d cím";
"points_count_format" = "%d pont";
"additional_points_format" = "+%d pont";
"export_route" = "Útvonal exportálása";
"navigate" = "Navigálás";
"address_list" = "Címlista";
"no_addresses" = "Nincsenek címek";
"no_addresses_message" = "Ez az útvonal még nem tartalmaz címeket";
"address_point_start" = "Kiindulópont";
"address_point_stop" = "Megálló pont";
"address_point_end" = "Végpont";
"route_name" = "Útvonal neve";
"new_route" = "Új útvonal";
"saved_route" = "Mentett útvonal";
"plan_route" = "Útvonal tervezése";
"clear_all" = "Összes törlése";
"avoid" = "Kerülendő:";
"toll_roads" = "Fizetős utak";
"highways" = "Autópályák";
"processing_addresses" = "Címek feldolgozása...";
"same_start_end_point" = "Ugyanazt a címet állította be kiindulási és végpontként";
"add_start_point" = "Kiindulópont hozzáadása";
"swipe_left_to_delete" = "Hosszú nyomás a törléshez";
"add_end_point" = "Végpont hozzáadása";
"enter_and_select_address" = "Cím megadása és kiválasztása";
"current_search_text" = "Jelenlegi keresési szöveg: %@";
"add_address" = "Cím hozzáadása";
"selected_coordinates" = "Kiválasztott koordináták";
"company_name_optional" = "Cégnév (opcionális)";
"url_optional" = "URL (opcionális)";
"favorite_address" = "Kedvenc cím";
"set_as_start_and_end" = "Beállítás kiindulási és végpontként";
"batch_paste" = "Tömeges beillesztés";
"file_import" = "Fájl importálás";
"web_download" = "Webes letöltés";
"saving" = "Mentés...";
"delivery_point_management" = "Kézbesítési pont kezelés";
"information_category" = "Információ kategória";
"package_info" = "Csomag információ";
"vehicle_position" = "Jármű pozíció";
"delivery_info" = "Kézbesítési információ";
"take_photo_record" = "Fénykép készítése";
"update_status" = "Állapot frissítése";
"edit_address_button" = "Cím szerkesztése";
"access_instructions" = "Hozzáférési utasítások";
"add_access_instructions" = "Hozzáférési utasítások hozzáadása...";
"package_count" = "Csomagok száma";
"packages" = "csomagok";
"package_unit" = "db";
"package_size" = "Csomag mérete";
"package_type" = "Csomag típusa";
"mark_as_important" = "Megjelölés fontosként";
"priority_delivery" = "Prioritásos kézbesítés";
"priority_level" = "Prioritási szint";
"priority_1" = "1. prioritás";
"priority_2" = "2. prioritás";
"priority_3" = "3. prioritás";
"no_priority" = "Nincs prioritás";
"no_priority_short" = "Nincs";
"set_priority" = "Prioritás beállítása";
"select_package_position" = "Csomag pozíciójának kiválasztása a járműben";
"vehicle_area" = "Jármű terület";
"left_right_position" = "Bal/jobb pozíció";
"vehicle_position_front" = "Elülső";
"vehicle_position_middle" = "Középső";
"vehicle_position_back" = "Hátsó";
"vehicle_position_left" = "Bal";
"vehicle_position_right" = "Jobb";
"vehicle_position_floor" = "Alsó";
"vehicle_position_shelf" = "Felső";
"height_position" = "Magassági pozíció";
"vehicle_position_none" = "Nincs pozíció kiválasztva";
"delivery_type" = "Kézbesítés típusa";
"delivery_status" = "Kézbesítési állapot";
"order_info" = "Rendelés információ";
"order_information" = "Rendelés információ";
"order_number" = "Rendelésszám";
"enter_order_number" = "Rendelésszám megadása";
"tracking_number" = "Követési szám";
"enter_tracking_number" = "Követési szám megadása";
"tracking_info" = "Követési információ";
"tracking_information" = "Követési információ";
"time_info" = "Idő információ";
"time_information" = "Idő információ";
"estimated_arrival_time" = "Becsült érkezési idő";
"anytime" = "Bármikor";
"stop_time" = "Megállási idő";
"minutes_format" = "%d perc";
"photo_record" = "Fénykép felvétel";
"door_number_photo" = "Házszám fénykép";
"package_label_photo" = "Csomag címke fénykép";
"placement_photo" = "Elhelyezési fénykép";
"door_number_desc" = "Készítsen tiszta fényképet a házszámról vagy utcaszámról, biztosítva, hogy a számok/betűk láthatóak legyenek";
"package_label_desc" = "Készítsen fényképet a csomag címkéjéről, biztosítva, hogy a címzett információi tisztán láthatóak legyenek";
"placement_desc" = "Készítsen fényképet a csomag végső elhelyezési helyéről";
"photo_captured" = "Fénykép elkészítve";
"photo_captured_options" = "Fénykép elkészítve, szeretné folytatni a következő fényképpel vagy befejezni a jelenlegit?";
"continue_to_next_photo" = "Folytatás a következő fényképpel - %@";
"retake" = "Újrafényképezés";
"tap_to_capture" = "Koppintás a fényképezéshez";
"flash_auto" = "Automatikus vaku";
"flash_on" = "Vaku bekapcsolása";
"flash_off" = "Vaku kikapcsolása";
"photo_record_completed" = "Fénykép felvétel befejezve";
"photo_confirmation" = "Fényképezés megerősítése";
"ok" = "OK";
"complete_photo_capture" = "Fényképezés befejezése";
"photo_instructions" = "Koppintson minden fénykép kártyára a fényképezéshez. Minden fényképet be kell fejezni.";
"photo_options" = "Fénykép opciók";
"view_photo" = "Fénykép megtekintése";
"retake_photo" = "Újrafényképezés";
"saving_photos" = "Fényképek mentése...";
"completed" = "Befejezve";
"not_taken" = "Nem készült";
"route_options" = "Útvonal opciók";
"avoid_tolls" = "Fizetős utak kerülése";
"avoid_highways" = "Autópályák kerülése";
"optimize_route" = "Útvonal optimalizálása";
"optimizing" = "Optimalizálás...";
"optimization_complete" = "Optimalizálás befejezve";
"route_optimization_results" = "Eredmények";
"route_planning_options" = "Útvonaltervezési lehetőségek";
"before_optimization" = "Optimalizálás előtt";
"after_optimization" = "Optimalizálás után";
"auto_group" = "Automatikus csoportosítás";
"optimized_route_order" = "Sorrend";
"apply" = "Alkalmazás";
"kilometers" = "kilométer";
"street_number_issue_warning" = "⚠️ Nagy különbség a házszámban! Ez rossz címre történő kézbesítést és büntetéseket okozhat. Kérjük, azonnal ellenőrizze a címet";
"address_validation_critical" = "Kritikus címellenőrzési probléma";
"street_number_difference_high_risk" = "Nagy házszám különbség, magas kockázat";
"delete_address" = "Cím törlése";
"street" = "Utca";
"city" = "Város";
"state" = "Állam/Megye";
"country" = "Ország";
"postal_code" = "Irányítószám";
"phone" = "Telefon";
"email" = "E-mail";
"website" = "Weboldal";
"company" = "Cég";
"latitude" = "Szélesség";
"longitude" = "Hosszúság";
"geocoding_error" = "Geokódolási hiba";
"address_validation" = "Cím ellenőrzés";
"invalid_addresses" = "Érvénytelen címek";
"fix_addresses" = "Címek javítása";
"route" = "Útvonalak";
"select_address_point" = "Címpont kiválasztása";
"select_delivery_points" = "Kézbesítési pontok kiválasztása";
"create_delivery_route" = "Kézbesítési útvonal létrehozása";
"view_saved_routes" = "Mentett útvonalak megtekintése";
"create_route" = "Útvonal létrehozása";
"edit_route" = "Útvonal szerkesztése";
"delete_route" = "Útvonal törlése";
"route_details" = "Útvonal részletei";
"selected_addresses" = "%d cím kiválasztva";
"reached_limit" = "Elérte a limitet";
"can_select_more" = "Még %d választható";
"navigate_button" = "Navigálás";
"create_group" = "Csoport létrehozása";
"waypoints" = "Köztes pontok";
"total_distance" = "Teljes távolság";
"estimated_time" = "Becsült idő";
"route_summary" = "Útvonal összefoglaló";
"route_saved" = "Útvonal mentve";
"route_optimized" = "Útvonal optimalizálva";
"optimizing_route" = "Útvonal optimalizálása...";
"completed_percent" = "%d%% befejezve";
"processing_points" = "Feldolgozás: %d/%d";
"estimated_remaining_time" = "Becsült hátralévő idő: %@";
"delivery" = "Kézbesítés";
"delivery_confirmation" = "Kézbesítés megerősítése";
"take_photo" = "Fényképezés";
"signature" = "Aláírás";
"delivery_notes" = "Kézbesítési megjegyzések";
"delivered" = "Kézbesítve";
"not_delivered" = "Nem kézbesítve";
"delivery_time" = "Kézbesítési idő";
"delivery_date" = "Kézbesítési dátum";
"package_details" = "Csomag részletei";
"package_id" = "Csomag ID";
"package_weight" = "Csomag súlya";
"package_dimensions" = "Csomag méretei";
"recipient_name" = "Címzett neve";
"recipient_phone" = "Címzett telefonja";
"groups" = "Csoportok";
"saved_groups" = "Mentett csoportok";
"edit_group" = "Csoport szerkesztése";
"delete_group" = "Csoport törlése";
"group_name" = "Csoport neve";
"group_details" = "Csoport részletei";
"auto_grouping" = "Automatikus csoportosítás";
"group_by" = "Csoportosítás alapja";
"add_to_group" = "Hozzáadás csoporthoz";
"remove_from_group" = "Eltávolítás csoportból";
"group_created" = "Csoport létrehozva";
"default_group_name_format" = "Csoport %d";
"auto_grouping_completed" = "Automatikus csoportosítás befejezve";
"auto_grouping_in_progress" = "Automatikus csoportosítás folyamatban...";
"create_group_every_14_addresses" = "Csoport létrehozása minden 14 címhez";
"create_delivery_group" = "Kézbesítési csoport létrehozása";
"enter_group_name" = "Csoportnév megadása";
"selected_delivery_points" = "Kiválasztott kézbesítési pontok";
"drag_to_adjust_order" = "Húzással módosítható a sorrend";
"free_plan" = "Ingyenes verzió";
"pro_plan" = "Pro verzió";
"expert_plan" = "Expert verzió";
"monthly" = "Havi terv";
"yearly" = "Éves terv";
"subscribe" = "Előfizetés";
"manage_subscription" = "Előfizetés kezelése";
"subscription_benefits" = "Előfizetési előnyök";
"free_trial" = "Ingyenes próbaverzió";
"price_per_month" = "%@ havonta";
"price_per_year" = "%@ évente";
"save_percent" = "%@% megtakarítás";
"subscription_terms" = "Előfizetési feltételek";
"privacy_policy" = "Adatvédelmi irányelvek";
"terms_of_service" = "Szolgáltatási feltételek";
"feature_comparison" = "Funkció összehasonlítás";
"addresses_per_route" = "Címek útvonalonként";
"max_20_addresses" = "Maximum 20 cím";
"fuel_savings" = "Üzemanyag megtakarítás";
"up_to_30_percent" = "Akár 30%";
"choose_subscription_plan" = "Előfizetési terv kiválasztása";
"monthly_plan" = "Havi terv";
"yearly_plan" = "Éves terv";
"/month_suffix" = "/hó";
"/year_suffix" = "/év";
"save_30_percent" = "30% megtakarítás";
"free_trial_7_days_cancel_anytime" = "7 napos ingyenes próba, bármikor lemondható";
"subscription_auto_renew_notice" = "Az előfizetés automatikusan megújul";
"and" = "és";
"subscription_exclusive" = "Előfizetők számára kizárólagos";
"free_version_optimization_limit" = "Az ingyenes verzió optimalizálása korlátozott";
"free_version_supports_max_addresses" = "Az ingyenes verzió maximum címeket támogat";
"current_route_contains_addresses" = "A jelenlegi útvonal címeket tartalmaz";
"upgrade_to_pro_unlimited_addresses" = "Frissítsen Pro verzióra korlátlan címekért";
"continue_optimization" = "Optimalizálás folytatása";
"upgrade_unlock_one_click_navigation" = "Frissítés az egy kattintásos navigáció feloldásához";
"learn_one_click_navigation_grouping" = "Ismerje meg az egy kattintásos navigációt és csoportosítást";
"toggle_subscription_status" = "Előfizetési állapot váltása";
"toggle_subscription_description" = "Váltás előfizetéses és ingyenes között tesztelési célokra";
"product_info_unavailable" = "Termék információ nem elérhető";
"purchase_failed" = "Vásárlás sikertelen: %@";
"upgrade_to_pro_version" = "Frissítés Pro verzióra";
"unlock_all_premium_features" = "Összes prémium funkció feloldása";
"first_7_days_free_cancel_anytime" = "Az első 7 nap ingyenes, bármikor lemondható";
"payment_terms_notice" = "A fizetés az Apple ID fiókjából kerül levonásra a vásárlás megerősítésekor. Az előfizetés automatikusan megújul, kivéve, ha legalább 24 órával a jelenlegi időszak vége előtt lemondja.";
"terms_of_use" = "Használati feltételek";
"product_load_failed_check_connection" = "Termék információ betöltése sikertelen, ellenőrizze, hogy az eszköz csatlakozik az internethez és az App Store-hoz";
"product_load_failed" = "Termék betöltése sikertelen: %@";
"verify_receipt" = "Nyugta ellenőrzése";
"one_click_navigation_short" = "Egy kattintásos navigáció";
"save_30_percent_fuel" = "30% üzemanyag megtakarítás";
"monthly_short" = "Havi";
"yearly_short" = "Éves";
"upgrade_now" = "Frissítés most";
"test_environment_pro_activated" = "Teszt környezet: Pro aktiválva";
"payment_terms_notice_detailed" = "A fizetés az Apple ID fiókjából kerül levonásra a vásárlás megerősítésekor. Az előfizetés automatikusan megújul, kivéve, ha legalább 24 órával a jelenlegi időszak vége előtt lemondja. Az előfizetéseket az App Store beállításaiban lehet kezelni és lemondani.";
"step_screenshot" = "%d. lépés képernyőkép";
"previous_step" = "Előző";
"next_step" = "Következő";
"each_address_takes_3_5_seconds" = "Minden cím hozzáadása 3-5 másodpercet vesz igénybe";
"need_repeat_14_times" = "Ugyanazt a műveletet 14-szer kell megismételni";
"navigation_order_often_confused" = "A navigációs sorrend gyakran összezavarodik";
"error_prone_need_redo" = "Hibára hajlamos, újra kell csinálni a műveleteket";
"address_order_reversed_manual_adjust" = "Címsorrend megfordítva, kézi beállítás szükséges";
"one_click_add_all" = "Egy kattintás, összes hozzáadása";
"smart_grouping_auto_sorting" = "Intelligens csoportosítás, automatikus rendezés";
"maintain_correct_visit_order" = "Helyes látogatási sorrend fenntartása";
"zero_errors_zero_repetition" = "Nulla hiba, nulla ismétlés";
"import" = "Importálás";
"export" = "Exportálás";
"import_addresses" = "Címek importálása";
"export_addresses" = "Címek exportálása";
"import_from_file" = "Importálás fájlból";
"export_to_file" = "Exportálás fájlba";
"file_format" = "Fájlformátum";
"csv_format" = "CSV formátum";
"excel_format" = "Excel formátum";
"json_format" = "JSON formátum";
"import_success" = "Sikeresen importálva %d cím, mind érvényes koordinátákkal.";
"export_success" = "Exportálás sikeres";
"import_error" = "Importálási hiba";
"export_error" = "Exportálási hiba";
"navigation_app" = "Navigációs alkalmazás";
"apple_maps" = "Apple Térképek";
"app_preferences" = "Alkalmazás beállítások";
"distance_unit" = "Távolság egység";
"current_language" = "Jelenlegi nyelv";
"info" = "Információ";
"contact_us_header" = "Lépjen kapcsolatba velünk";
"contact_us_subheader" = "Kérdései vagy javaslatai vannak? Szívesen segítünk!";
"contact_options" = "Kapcsolatfelvételi lehetőségek";
"email_us" = "Küldjön nekünk e-mailt";
"contact_form" = "Kapcsolatfelvételi űrlap";
"contact_and_support" = "Kapcsolat és támogatás";
"common_questions" = "Gyakori kérdések";
"how_to_use" = "Hogyan használjuk";
"subscription_faq" = "Előfizetés GYIK";
"navigation_help" = "Navigációs segítség";
"troubleshooting" = "Hibaelhárítás";
"help_howto_content" = "A NaviBatch egy hatékony útvonaltervező alkalmazás, amely segít optimalizálni a kézbesítési útvonalakat, időt és üzemanyagot takarítva meg. Több címet adhat hozzá, automatikusan optimalizálhatja az útvonal sorrendjét, és egy kattintással navigálhat az Apple Térképekhez.";
"help_subscription_content" = "A NaviBatch ingyenes és pro verziókat kínál. Az ingyenes verzió legfeljebb 20 címet támogat, míg a pro verzió korlátlan címeket és egy kattintásos csoportos navigációs funkciókat biztosít.";
"help_navigation_content" = "A NaviBatch az Apple Térképeket használja navigációhoz. Minden címhez külön-külön navigálhat, vagy használhatja a csoportosítási funkciót több cím egyidejű navigálásához.";
"help_troubleshooting_content" = "Ha problémákba ütközik, először győződjön meg róla, hogy eszköze rendelkezik hálózati kapcsolattal és a helymeghatározási engedélyek meg vannak adva. Ha a problémák továbbra is fennállnak, kérjük, lépjen kapcsolatba támogatási csapatunkkal.";
"actions" = "Műveletek";
"legal" = "Jogi";
"show_look_around" = "Nézet megjelenítése";
"hide_look_around" = "Nézet elrejtése";
"map" = "Térkép";
"map_type" = "Térkép típusa";
"standard" = "Normál";
"satellite" = "Műhold";
"hybrid" = "Hibrid";
"show_traffic" = "Forgalom megjelenítése";
"current_location" = "Jelenlegi helyzet";
"directions" = "Útvonal útmutatás";
"distance_to" = "Távolság";
"eta" = "Becsült érkezési idő";
"look_around" = "Körülnézés";
"locating_to_glen_waverley" = "Glen Waverley-re pozicionálás";
"network_error" = "Hálózati hiba";
"location_error" = "Helyzet hiba";
"permission_denied" = "Engedély megtagadva";
"location_permission_required" = "Helyzet engedély szükséges";
"camera_permission_required" = "Kamera engedély szükséges";
"photo_library_permission_required" = "Fotótár engedély szükséges";
"please_try_again" = "Kérjük próbálja újra";
"something_went_wrong" = "Valami hiba történt";
"invalid_input" = "Érvénytelen bemenet";
"required_field" = "Kötelező mező";
"no_internet_connection" = "Nincs internetkapcsolat";
"server_error" = "Szerver hiba";
"timeout_error" = "Kérés időtúllépés";
"data_not_found" = "Adat nem található";
"selection_limit_reached" = "Kiválasztási limit elérve";
"selection_limit_description" = "Maximum %d címet választhat ki, %d-t választott ki";
"location_status_valid" = "Érvényes tartomány";
"address_validation_unknown" = "Nem ellenőrzött";
"address_validation_valid" = "Érvényes";
"address_validation_invalid" = "Érvénytelen";
"address_validation_warning" = "Figyelmeztetés";
"address_validation_mismatch" = "Nem egyezik";
"device_not_support_scanning" = "Az eszköz nem támogatja a natív szkennelést";
"requires_ios16_a12_chip" = "iOS 16+ és A12 chip vagy újabb szükséges";
"debug_info" = "Hibakeresési információ:";
"address_confirmation" = "Cím megerősítése";
"continue_scanning" = "Szkennelés folytatása";
"confirm_add" = "Hozzáadás megerősítése";
"cannot_get_coordinates_scan_retry" = "Nem lehet megszerezni a cím koordinátáit, kérjük adja meg kézzel vagy szkennelje újra";
"unknown_country" = "Ismeretlen ország";
"unknown_city" = "Ismeretlen város";
"please_enter_valid_address" = "Kérjük adjon meg legalább egy érvényes címet";
"please_select_valid_address" = "Kérjük válasszon érvényes címet";
"add_address_failed" = "Cím hozzáadása sikertelen";
"location_permission_required_for_current_location" = "Helymeghatározási engedély szükséges a jelenlegi hely lekéréséhez";
"cannot_get_current_location_check_settings" = "Nem lehet megszerezni a jelenlegi helyet, kérjük ellenőrizze a beállításokat";
"cannot_get_current_location_address" = "Nem lehet megszerezni a jelenlegi hely címét";
"get_current_location_failed" = "Jelenlegi hely lekérése sikertelen";
"location_status_warning" = "Figyelmeztetési tartomány";
"location_status_invalid" = "Érvénytelen helyzet";
"location_status_unknown" = "Ismeretlen állapot";
"coordinates_origin_point" = "Érvénytelen: nulla koordináták (0,0)";
"coordinates_invalid_nan" = "Érvénytelen: nem numerikus koordináták";
"coordinates_out_of_range" = "Érvénytelen: koordináták az érvényes tartományon kívül";
"coordinates_far_from_user" = "Figyelmeztetés: a helyzet távol van a jelenlegi helyzetétől";
"coordinates_ocean" = "Figyelmeztetés: a helyzet óceánban vagy lakatlan területen lehet";
"free_address_limit" = "Ingyenes verzió cím korlát";
"address_count_limit" = "Cím mennyiség korlát";
"selected_addresses_can_import" = "%d címet választott ki, ezek a címek importálhatók.";
"selected_addresses_exceeds" = "%d címet választott ki, %d-dal meghaladja a hozzáadható mennyiséget.";
"import_success_with_warnings" = "Sikeresen importálva %d cím, ebből %d cím koordinátái normálisak, %d címnél figyelmeztetések vannak.\n\nA figyelmeztetéssel rendelkező címek meg vannak jelölve, az importálás után kézzel javíthatók.";
"supported_formats" = "Támogatott formátumok";
"supported_format_csv" = "• CSV fájlok: a cím oszlopnak teljes címeket kell tartalmaznia";
"supported_format_json" = "• JSON adatok: cím mezőket tartalmazó tömb";
"supported_format_text" = "• Egyszerű szöveg: soronként egy cím";
"download_history" = "Letöltési előzmények";
"input_address_data_url" = "Cím adat URL megadása";
"import_result" = "Importálási eredmény";
"downloading" = "Letöltés...";
"processing_data" = "Adatok feldolgozása...";
"google_drive_download_failed" = "Google Drive letöltés sikertelen";
"second_attempt_invalid_data" = "Második letöltési kísérlet érvénytelen adatokat adott vissza";
"cannot_parse_json" = "Nem lehet elemezni a JSON adatokat, kérjük ellenőrizze a fájl formátumot";
"cannot_parse_json_with_error" = "Nem lehet elemezni a JSON adatokat: %@";
"cannot_read_file" = "Nem lehet olvasni a fájlt: %@";
"failed" = "Kézbesítés sikertelen";
"no_valid_addresses" = "Nem találhatók érvényes címek";
"supports_file_types" = "Támogatja a CSV, TXT és JSON fájlokat";
"tap_to_select_file" = "Koppintson a fájl kiválasztásához";
"input_company_name" = "Cégnév megadása (opcionális)";
"imported_addresses_count" = "%d cím importálva";
"excel_format_not_supported" = "Excel formátum nem támogatott";
"import_failed" = "Importálás sikertelen";
"free_version_address_limit" = "Az ingyenes verzió maximum %d címet engedélyez.";
"current_address_count" = "Jelenleg %d cím van, csak %d címet lehet még hozzáadni.";
"upgrade_to_premium_unlimited" = "Frissítsen a prémium verzióra korlátlan címekért!";
"route_address_limit" = "A jelenlegi útvonal %d címet tartalmaz, csak %d címet lehet még hozzáadni, összesen maximum %d címet.";
"free_version_limit" = "Ingyenes verzió cím korlát";
"import_all_n" = "Mind a %d importálása";
"cannot_import" = "Nem lehet importálni";
"select_at_least_one" = "Kérjük válasszon ki legalább egy címet";
"no_valid_addresses_found" = "Nem találhatók érvényes címek";
"import_success_all_valid" = "Sikeresen importálva %d cím, minden cím koordinátái normálisak.";
"import_success_some_warnings" = "Sikeresen importálva %d cím, ebből %d cím koordinátái normálisak, %d címnél nem lehet koordinátákat szerezni.";
"company_format" = "Vállalati formátum";
"added_from_web_download" = "Webes letöltésből hozzáadva";
"invalid_csv_row" = "Érvénytelen CSV sor";
"distance_warning" = "200 km-nél távolabb a jelenlegi helyzettől";
"not_in_australia" = "Koordináták nem Ausztrália területén";
"invalid_address_data" = "Érvénytelen cím adatok";
"distance_warning_confirm" = "A jelenlegi helytől való távolság meghaladja a 200 km-t, folytatja?";
"coordinates_missing" = "Koordináták hiányoznak";
"low_accuracy_address" = "Alacsony pontosságú cím";
"address_partial_match" = "Cím részleges egyezés";
"address_outside_region" = "Cím a régión kívül";
"api_limit_reached" = "API limit elérve";
"address_not_exist_or_incorrect_format" = "A cím nem létezik vagy helytelen a formátum";
"please_check_address_spelling" = "Kérjük ellenőrizze a cím helyesírását";
"try_smaller_street_number" = "Próbáljon kisebb házszámot";
"use_full_street_type_name" = "Használja az utca típusának teljes nevét";
"try_add_more_address_details" = "Próbáljon több cím részletet hozzáadni";
"cannot_find_address" = "Nem található a cím";
"please_check_spelling_or_add_details" = "Kérjük ellenőrizze a helyesírást vagy adjon hozzá részleteket";
"cannot_find_address_check_spelling" = "Nem található a cím, ellenőrizze a helyesírást";
"address_not_set" = "Cím nincs beállítva";
"address_format_incomplete" = "Cím formátum hiányos";
"location_service_denied" = "Helymeghatározási szolgáltatás megtagadva";
"no_saved_groups" = "Nincsenek mentett csoportok";
"select_points_create_groups" = "Válasszon kézbesítési pontokat és hozzon létre csoportokat a könnyebb kezeléshez";
"navigate_to_these_points" = "Navigálás ezekhez a pontokhoz";
"confirm_remove_address" = "Biztos, hogy el szeretné távolítani a \"%@\" címet a csoportból?";
"confirm_remove_this_address" = "Biztos, hogy el szeretné távolítani ezt a címet a csoportból?";
"addresses_count" = "%d cím";
"no_saved_routes" = "Nincsenek mentett útvonalak";
"no_saved_routes_description" = "Még nem mentett útvonalakat";
"all_routes" = "Összes útvonal";
"address_count_format_simple" = "%d cím";
"delete_all_routes" = "Összes útvonal törlése";
"navigate_to_all_points" = "Navigálás minden ponthoz";
"confirm_navigate_to_route" = "Biztos, hogy navigálni szeretne a \"%@\" útvonal minden pontjához?";
"temp_navigation_group" = "Ideiglenes navigációs csoport";
"route_management" = "Útvonal kezelés";
"route_addresses" = "Útvonal címek";
"no_addresses_in_route" = "Ez az útvonal nem tartalmaz címeket";
"must_keep_one_route" = "Legalább egy útvonalat meg kell tartani";
"confirm_delete_route" = "Biztos, hogy törölni szeretné a \"%@\" útvonalat? Ez a művelet nem vonható vissza.";
"confirm_delete_all_routes" = "Összes útvonal törlésének megerősítése";
"confirm_delete_all_routes_message" = "Biztos, hogy törölni szeretné az összes útvonalat? Ez a művelet nem vonható vissza.";
"delete_all" = "Összes törlése";
"address_information" = "Cím információ";
"group_belonging" = "Tartozó csoport";
"view_map" = "Térkép megtekintése";
"delete_delivery_point" = "Kézbesítési pont törlése";
"delivery_point_details" = "Kézbesítési pont részletei";
"confirm_deletion" = "Törlés megerősítése";
"delete_delivery_point_confirmation" = "Biztos, hogy törölni szeretné ezt a kézbesítési pontot? Ez a művelet nem vonható vissza.";
"delivery_photos" = "Kézbesítési fényképek";
"view_delivery_photos" = "Kézbesítési fényképek megtekintése";
"no_photos_taken" = "Még nem készültek fényképek";
"take_photos" = "Fényképek készítése";
"loading_photos" = "Fényképek betöltése...";
"photo_not_found" = "Fénykép nem található";
"photo_deleted" = "Fénykép törölve";
"share_photos" = "Fénykép megosztása";
"photo_capture_title" = "Fényképezés megerősítése";
"door_number_photo_title" = "Utcaszám/házszám fénykép";
"package_label_photo_title" = "Csomag címke fénykép";
"placement_photo_title" = "Elhelyezési hely fénykép";
"door_number_photo_desc" = "Készítsen tiszta fényképet a házszámról, biztosítva, hogy a számok/betűk láthatóak legyenek";
"package_label_photo_desc" = "Készítsen fényképet a csomag címkéjéről, biztosítva, hogy a címzett információi tisztán láthatóak legyenek";
"placement_photo_desc" = "Készítsen fényképet a csomag végső elhelyezési helyéről";
"swipe_to_switch" = "Csúsztassa a fénykép típusának váltásához";
"photos_will_be_saved_to" = "A fényképek mentésre kerülnek ide";
"complete_photos" = "Fényképezés befejezése";
"photo_save_success" = "Fénykép sikeresen mentve";
"photo_save_failure" = "Fénykép mentése sikertelen";
"no_photos_found" = "Nem találhatók fényképek";
"photos_deleted_or_not_taken" = "Lehetséges, hogy a fényképek törölve lettek vagy még nem készültek";
"share_photo" = "Fénykép megosztása";
"photo_capture_preview" = "Előnézeti mód - kamera szimuláció";
"photo_capture_close" = "Bezárás";
"camera_start_failed" = "Kamera indítása sikertelen";
"camera_start_failed_retry" = "Nem lehet elindítani a kamerát, kérjük próbálja újra";
"camera_init_failed" = "Kamera inicializálása sikertelen";
"camera_access_failed" = "Nem lehet hozzáférni a kamerához";
"photo_processing_failed" = "Fényképezés sikertelen";
"photo_processing_failed_retry" = "Nem lehet befejezni a fénykép feldolgozását, kérjük próbálja újra";
"photo_capture_progress" = "Haladás: %d/%d";
"photo_captured_continue" = "Fényképezés befejezve, folytatás %@-val";
"pending" = "Kézbesítésre vár";
"in_progress" = "Kézbesítés alatt";
"select_delivery_status" = "Kézbesítési állapot kiválasztása";
"select_failure_reason" = "Sikertelenség okának kiválasztása";
"delivery_status_pending" = "Függőben";
"delivery_status_in_progress" = "Folyamatban";
"delivery_status_completed" = "Befejezve";
"delivery_status_failed" = "Sikertelen";
"failure_reason_not_at_home" = "Ügyfél nincs otthon";
"failure_reason_wrong_address" = "Hibás cím";
"failure_reason_no_access" = "Nem lehet bejutni a helyre";
"failure_reason_rejected" = "Csomag elutasítva";
"failure_reason_other" = "Egyéb okok";
"enter_custom_reason" = "Konkrét ok megadása";
"custom_reason_placeholder" = "Kérjük írja le a konkrét okot...";
"custom_reason_required" = "Kérjük adja meg a konkrét okot";
"failure_reason_required" = "Kérjük válassza ki a sikertelenség okát";
"delivery_type_delivery" = "Kézbesítés";
"delivery_type_pickup" = "Átvétel";
"delivery_order_first" = "Első";
"delivery_order_auto" = "Automatikus";
"delivery_order_last" = "Utolsó";
"package_size_small" = "Kicsi";
"package_size_medium" = "Közepes";
"package_size_large" = "Nagy";
"package_type_box" = "Doboz";
"package_type_bag" = "Táska";
"package_type_letter" = "Levél";
"one_click_navigation_grouping" = "Egy kattintásos navigáció és csoportosítás";
"speed_60x_faster" = "60x gyorsabb";
"goodbye_manual_address_adding" = "Búcsú a kézi címhozzáadástól";
"watch_detailed_demo" = "Részletes bemutató megtekintése";
"upgrade_to_pro_now" = "Frissítés Pro verzióra most";
"free_trial_7_days" = "7 napos ingyenes próba";
"traditional_vs_navibatch_pro" = "Hagyományos módszer vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "Csúsztassa a teljes összehasonlítás megtekintéséhez";
"traditional_method" = "Hagyományos módszer";
"drivers_get_lost_affect_efficiency" = "A sofőrök eltévednek, befolyásolja a hatékonyságot";
"repetitive_operations_waste_time" = "Az ismétlődő műveletek időpazarlás";
"total_time_60_seconds" = "Teljes idő: 60 másodperc";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Útvonalak optimalizálása, távolság csökkentése";
"improve_delivery_efficiency_accuracy" = "Kézbesítési hatékonyság és pontosság javítása";
"speed_boost_60x" = "60x sebességnövelés";
"total_time_1_second" = "Teljes idő: 1 másodperc";
"time_comparison" = "Idő összehasonlítás";
"traditional_method_problems" = "Hagyományos módszer problémái";
"each_address_3_5_seconds_14_total_60" = "Minden cím 3-5 másodperc, 14 cím összesen 60 másodperc";
"repetitive_operations_cause_fatigue" = "Az ismétlődő műveletek fáradtságot okoznak";
"address_order_reversed_last_becomes_first" = "Címsorrend megfordítva, az utolsó lesz az első";
"need_manual_reverse_adding_takes_longer" = "Kézi megfordítás szükséges, a hozzáadás tovább tart";
"navibatch_advantages" = "NaviBatch előnyei";
"add_14_addresses_1_second_60x_faster" = "14 cím hozzáadása 1 másodperc alatt, 60x gyorsabb";
"auto_maintain_correct_order_no_adjustment" = "Automatikusan fenntartja a helyes sorrendet, nincs szükség beállításra";
"zero_error_rate_no_repetition" = "Nulla hibaarány, nincs ismétlés";
"save_59_seconds" = "59 másodperc megtakarítás";
"speed_boost_60x_simple" = "60x sebességnövelés";
"seconds_format" = "%d másodperc";
"actual_benefits_one_click_navigation" = "Az egy kattintásos navigáció valós előnyei";
"daily_savings" = "Napi megtakarítás";
"daily_savings_value" = "59 másodperc";
"daily_savings_description" = "59 másodpercet takarít meg minden 14 címnél";
"monthly_savings" = "Havi megtakarítás";
"monthly_savings_value" = "30 perc";
"monthly_savings_description" = "Havi 30 útvonal alapján";
"fuel_savings_value" = "30%";
"fuel_savings_description" = "Az útvonal optimalizálás csökkenti az üzemanyag-fogyasztást";
"income_increase" = "Bevétel növekedés";
"income_increase_value" = "15%";
"income_increase_description" = "Több kézbesítés naponta = több bevétel";
"trial" = "Próba";
"days_left" = "nap van hátra";
"free_plan_description" = "Ingyenes terv - Legfeljebb 20 cím";
"pro_plan_active" = "Pro terv aktív";
"expert_plan_active" = "Szakértő terv aktív";
"trial_active" = "Próba aktív";
"trial_expires_on" = "A próba lejár: %@";
"address_validation_mode" = "Címellenőrzési mód";
"validation_description" = "Szabályozza a címellenőrzés szigorúságát";
"current_settings" = "Jelenlegi beállítások";
"validation_mode_format" = "Mód: %@";
"threshold_score_format" = "Küszöb: %.1f";
"validation_example" = "Ellenőrzési példa";
"original_address_example" = "Eredeti cím: 123 Main St";
"reverse_address_example" = "Fordított cím: 125 Main St";
"house_number_difference" = "Házszám különbség: 2";
"result_label" = "Eredmény:";
"may_pass_warning" = "Átmehet (figyelmeztetés)";
"will_not_pass" = "Nem megy át";
"real_case_example" = "Valós eset példa";
"real_case_description" = "Valós címellenőrzési adatokon alapul";
"address_validation_settings" = "Címellenőrzési beállítások";
"clear" = "Törlés";
"view_details" = "Részletek megtekintése";
"create_test_data" = "Teszt adatok létrehozása";
"manual_snapshot" = "Kézi pillanatkép";
"start_location_updates" = "Helymeghatározás frissítések indítása";
"stop_location_updates" = "Helymeghatározás frissítések leállítása";
"user_location_marker_test" = "Felhasználói hely jelölő teszt";
"location_animation_control" = "Hely animáció vezérlés";
"current_location_format" = "Jelenlegi hely: %.6f, %.6f";
"waiting_for_location" = "Helymeghatározásra várva...";
"diagnostic_tools" = "Diagnosztikai eszközök";
"storekit_diagnostics" = "StoreKit diagnosztika";
"subscription_function_test" = "Előfizetési funkció teszt";
"localization_test" = "Lokalizációs teszt";
"address_validation_demo" = "Címellenőrzési bemutató";
"localization_tools" = "Lokalizációs eszközök";
"coordinate_debug_tools" = "Koordináta hibakeresési eszközök";
"smart_abbreviation_expansion_test" = "Intelligens rövidítés kibontási teszt";
"subscription_restore_diagnostics" = "Előfizetés visszaállítási diagnosztika";
"batch_address_import_test" = "Tömeges címimportálási teszt";
"test_import_1000_addresses_memory" = "Teszt: 1000 cím importálása (memória)";
"map_rendering_test" = "Térkép renderelési teszt";
"test_map_display_markers_memory" = "Teszt: Jelölők megjelenítése térképen (memória)";
"select_test_language" = "Teszt nyelv kiválasztása";
"discover_60x_speed_boost" = "Fedezze fel a 60x sebességnövelést";
"see_60x_speed_demo" = "60x sebesség bemutató megtekintése";
"free_vs_pro_comparison" = "Ingyenes vs Pro összehasonlítás";
"our_free_beats_competitors_paid" = "Ingyenes tervünk felülmúlja a versenytársak fizetős terveit";
"features" = "Funkciók";
"up_to_20" = "Legfeljebb 20";
"unlimited" = "Korlátlan";
"smart_optimization" = "Intelligens optimalizálás";
"up_to_20_percent" = "Akár 20%";
"file_not_found" = "Fájl nem található";
"sample_file_not_available" = "Minta fájl nem elérhető";
"file_copy_failed" = "Fájl másolása sikertelen";
