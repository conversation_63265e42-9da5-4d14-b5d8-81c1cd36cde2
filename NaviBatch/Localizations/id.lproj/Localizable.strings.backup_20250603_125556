/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Pengaturan Bahasa";
"system_language" = "Bahasa Sistem";
"system_language_section" = "Pengaturan Sistem";
"languages" = "Bahasa";
"language_info_title" = "Tentang Pengaturan Bahasa";
"language_info_description" = "Setelah mengubah pengaturan bahasa, aplikasi akan menampilkan teks dalam bahasa yang dipilih. Beberapa konten mungkin memerlukan restart aplikasi untuk menerapkan pengaturan bahasa baru sepenuhnya.";
"restart_required" = "Perlu Restart Aplikasi";
"restart_app_message" = "Untuk menerapkan perubahan bahasa sepenuhnya, silakan restart aplikasi.";
"restart_now" = "Restart Sekarang";
"restart_later" = "Restart Nanti";

// MARK: - Common UI Elements
"close" = "Tutup";
"cancel" = "Batal";
"save" = "Simpan";
"edit" = "Edit";
"delete" = "Hapus";
"done" = "Selesai";
"next" = "Selanjutnya";
"back" = "Kembali";
"confirm" = "Konfirmasi";
"error" = "Error";
"success" = "Berhasil";
"warning" = "Peringatan";
"loading" = "Memuat...";
"search" = "Cari";
"settings" = "Pengaturan";
"help" = "Bantuan";
"about" = "Tentang";
"menu" = "Menu";
"understand" = "Saya Mengerti";

// MARK: - Navigation
"navigation" = "Navigasi";
"start_navigation" = "Mulai Navigasi";

// MARK: - Subscription
"subscription" = "Berlangganan";
"upgrade_to_pro" = "Upgrade ke Pro";
"upgrade_description" = "Pengelompokan navigasi satu klik, 60x lebih cepat dari operasi manual";
"restore_purchases" = "Pulihkan Pembelian";
"learn_more" = "Pelajari Lebih Lanjut";
"upgrade_your_plan" = "Upgrade Paket Anda";
"one_click_navigation_description" = "Pengelompokan alamat navigasi satu klik, hemat waktu dan bahan bakar";
"current_plan" = "Paket Saat Ini";
"upgrade" = "Upgrade";
"maybe_later" = "Nanti Saja";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratis";
"pro_tier_price" = "$29.99/bulan";
"expert_tier_price" = "$249.99/tahun";
"free_tier_description" = "Cocok untuk individu dan bisnis kecil, tidak memerlukan banyak perhentian";
"pro_tier_description" = "Untuk pengguna yang membutuhkan pengelompokan navigasi satu klik dan alamat tak terbatas";
"expert_tier_description" = "Sama dengan Pro, tetapi hemat 31% dengan paket tahunan";
"route_optimization" = "Optimalisasi Rute";
"unlimited_routes" = "Rute Tak Terbatas";
"unlimited_optimizations" = "Optimalisasi Tak Terbatas";
"max_15_addresses" = "Maksimal 15 alamat per rute";
"save_fuel_30" = "Hemat bahan bakar hingga 30%";
"unlimited_addresses" = "✨ Alamat Tak Terbatas ✨";
"one_click_navigation" = "⚡ Pengelompokan Navigasi Satu Klik - 60x Lebih Cepat ⚡";
"package_finder" = "Pencari Lokasi Paket";
"annual_savings" = "Penghematan Paket Tahunan";
"switched_to_free" = "Beralih ke Paket Gratis";
"switched_to_subscription" = "Beralih ke Paket Berlangganan";
"unlimited_stops" = "Perhentian Tak Terbatas";
"plan_as_many_stops_as_needed" = "Tambahkan titik pengiriman sebanyak yang diperlukan tanpa batasan";

// MARK: - Address Input
"enter_or_search_address" = "Masukkan atau cari alamat";
"search_results_count" = "Hasil pencarian: %d";
"no_matching_addresses" = "Tidak ditemukan alamat yang cocok";
"search_address_failed" = "Gagal mencari alamat: %@";
"address_search_no_response" = "Pencarian alamat tidak merespons";
"cannot_get_address_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"speech_recognizer_unavailable" = "Pengenal suara tidak tersedia";
"microphone_permission_denied" = "Izin mikrofon ditolak";
"speech_recognition_permission_denied" = "Izin pengenalan suara ditolak";
"listening" = "Mendengarkan...";
"recording_failed" = "Gagal memulai perekaman: %@";
"cannot_get_coordinates_retry" = "Tidak dapat memperoleh koordinat alamat, silakan masukkan secara manual atau coba lagi";
"cannot_create_recognition_request" = "Tidak dapat membuat permintaan pengenalan";

// MARK: - Saved Address Picker
"search_address" = "Cari Alamat";
"no_saved_addresses" = "Tidak Ada Alamat Tersimpan";
"no_saved_addresses_description" = "Anda belum menyimpan alamat apapun, atau tidak ada alamat yang sesuai dengan kriteria filter";
"select_address_book" = "Pilih Buku Alamat";

// MARK: - Menu
"menu" = "Menu";
"routes" = "Rute";
"address_book" = "Buku Alamat";
"saved_routes" = "Rute Tersimpan";
"manage_your_routes" = "Kelola perencanaan rute Anda";
"manage_your_addresses" = "Kelola alamat yang sering digunakan";
"settings" = "Pengaturan";
"preferences" = "Preferensi";
"set_custom_start_point" = "Atur Titik Awal Kustom";
"current_start_point" = "Titik awal saat ini: %@";
"support" = "Dukungan";
"contact_us" = "Hubungi Kami";
"contact_us_description" = "Punya pertanyaan atau saran? Jangan ragu untuk menghubungi kami!";
"help_center" = "Pusat Bantuan";
"subscription" = "Berlangganan";
"upgrade_to_pro" = "Upgrade ke Pro";
"upgrade_description" = "Pengelompokan navigasi satu klik, 60x lebih cepat dari operasi manual";
"open_subscription_view" = "Buka langsung tampilan berlangganan";
"open_subscription_view_description" = "Lewati lapisan tengah, tampilkan SubscriptionView langsung";
"restore_purchases_failed" = "Gagal memulihkan pembelian: %@";
"about" = "Tentang";
"rate_us" = "Beri Nilai";
"rate_us_description" = "Umpan balik Anda penting bagi kami dan membantu kami meningkatkan aplikasi!";
"share_app" = "Bagikan Aplikasi";
"share_app_text" = "Coba NaviBatch, aplikasi perencanaan rute yang luar biasa!";
"about_app" = "Tentang Aplikasi";
"developer_tools" = "Alat Pengembang";
"coordinate_debug_tool" = "Alat Debug Koordinat";
"batch_fix_addresses" = "Perbaiki Alamat Massal";
"clear_database" = "Hapus Database";
"clear_database_confirmation" = "Ini akan menghapus semua data, termasuk rute, alamat, dan grup. Tindakan ini tidak dapat dibatalkan. Apakah Anda yakin ingin melanjutkan?";
"confirm_clear" = "Konfirmasi Hapus";
"version_info" = "Versi %@ (%@)";
"current_system_language" = "Bahasa Sistem Saat Ini";
"reset_to_system_language" = "Reset ke Bahasa Sistem";
"language" = "Bahasa";
"language_settings" = "Pengaturan Bahasa";

// MARK: - Address Edit
"address" = "Alamat";
"coordinates" = "Koordinat";
"distance_from_current_location" = "Jarak dari Lokasi Saat Ini";
"address_info" = "Informasi Alamat";
"update_coordinates" = "Perbarui Koordinat";
"fix_address" = "Perbaiki Alamat";
"prompt" = "Peringatan";
"confirm" = "Konfirmasi";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Silakan ubah alamat sebelum memperbarui koordinat";
"coordinates_update_success" = "Koordinat berhasil diperbarui";
"coordinates_update_failure" = "Gagal memperbarui koordinat";
"save_failure" = "Gagal menyimpan: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Tidak Ada Alamat Tersimpan";
"no_saved_addresses_message" = "Anda belum menyimpan alamat apapun";
"add_new_address" = "Tambah Alamat Baru";
"address_title" = "Alamat";
"add" = "Tambah";
"refresh" = "Segarkan";
"notes" = "Catatan";
"address_details" = "Detail Alamat";
"favorite" = "Favorit";
"edit_address" = "Edit Alamat";
"cancel" = "Batal";
"save" = "Simpan";
"confirm_delete" = "Konfirmasi Hapus";
"delete" = "Hapus";
"delete_address_confirmation" = "Yakin ingin menghapus alamat ini? Tindakan ini tidak dapat dibatalkan.";
"edit" = "Edit";
"address_marker" = "Alamat";
"address_label" = "Alamat:";
"notes_label" = "Catatan:";
"created_at_label" = "Dibuat:";
"open_in_maps" = "Buka di Peta";
"copy_address" = "Salin Alamat";
"address_details_title" = "Detail Alamat";

// MARK: - Route Detail
"start_end_point" = "Titik Awal/Akhir";
"start_point" = "Titik Awal";
"end_point" = "Titik Akhir";
"route_info" = "Informasi Rute";
"address_count" = "Jumlah Alamat";
"address_count_format" = "%d alamat";
"points_count_format" = "%d titik";
"additional_points_format" = "+%d titik";
"export_route" = "Ekspor Rute";
"navigate" = "Navigasi";
"address_list" = "Daftar Alamat";
"no_addresses" = "Tidak Ada Alamat";
"no_addresses_message" = "Rute ini belum memiliki alamat";

// MARK: - Route Bottom Sheet
"address_point_start" = "Titik Awal";
"address_point_stop" = "Titik Berhenti";
"address_point_end" = "Titik Akhir";
"route_name" = "Nama Rute";
"save" = "Simpan";
"new_route" = "Rute Baru";
"saved_route" = "Rute Tersimpan";
"edit" = "Edit";
"loading" = "Memuat...";
"plan_route" = "Rencanakan Rute";
"clear_all" = "Hapus Semua";
"avoid" = "Hindari:";
"toll_roads" = "Jalan Tol";
"highways" = "Jalan Raya";
"processing_addresses" = "Memproses alamat...";
"same_start_end_point" = "Anda telah menetapkan alamat yang sama sebagai titik awal dan akhir";
"add_start_point" = "Tambah Titik Awal";
"swipe_left_to_delete" = "← Geser kiri untuk menghapus";
"delete" = "Hapus";
"add_new_address" = "Tambah Alamat Baru";
"add_end_point" = "Tambah Titik Akhir";

// MARK: - Simple Address Sheet
"address_title" = "Alamat";
"enter_and_select_address" = "Masukkan dan pilih alamat";
"current_search_text" = "Teks pencarian saat ini: %@";
"search_results_count" = "Hasil pencarian: %d";
"no_matching_addresses" = "Tidak ditemukan alamat yang cocok";
"add_address" = "Tambah Alamat";
"edit_address" = "Edit Alamat";
"selected_coordinates" = "Koordinat Terpilih";
"company_name_optional" = "Nama Perusahaan (Opsional)";
"url_optional" = "URL (Opsional)";
"favorite_address" = "Alamat Favorit";
"set_as_start_and_end" = "Atur sebagai Titik Awal dan Akhir";
"address_book" = "Buku Alamat";
"batch_paste" = "Tempel Massal";
"file_import" = "Impor File";
"web_download" = "Unduh Web";
"cancel" = "Batal";
"saving" = "Menyimpan...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Manajemen Titik Pengiriman";
"information_category" = "Kategori Informasi";
"address_info" = "Informasi Alamat";
"package_info" = "Informasi Paket";
"vehicle_position" = "Posisi Kendaraan";
"delivery_info" = "Informasi Pengiriman";
"navigation" = "Navigasi";
"take_photo_record" = "Ambil Foto Rekaman";
"update_status" = "Perbarui Status";
"done" = "Selesai";
"edit_address_button" = "Edit Alamat";
"coordinates" = "Koordinat";
"access_instructions" = "Instruksi Akses";
"add_access_instructions" = "Tambah instruksi akses...";
"package_count" = "Jumlah Paket";
"package_size" = "Ukuran Paket";
"package_type" = "Jenis Paket";
"mark_as_important" = "Tandai sebagai Penting";
"select_package_position" = "Pilih Posisi Paket di Kendaraan";
"vehicle_area" = "Area Kendaraan";
"left_right_position" = "Posisi Kiri/Kanan";
"vehicle_position_front" = "Depan";
"vehicle_position_middle" = "Tengah";
"vehicle_position_back" = "Belakang";
"vehicle_position_left" = "Kiri";
"vehicle_position_right" = "Kanan";
"vehicle_position_floor" = "Bawah";
"vehicle_position_shelf" = "Atas";
"height_position" = "Posisi Tinggi";
"delivery_type" = "Jenis Pengiriman";
"delivery_status" = "Status Pengiriman";
"order_info" = "Informasi Pesanan";
"order_information" = "Informasi Pesanan";
"order_number" = "Nomor Pesanan";
"enter_order_number" = "Masukkan nomor pesanan";
"tracking_number" = "Nomor Pelacakan";
"enter_tracking_number" = "Masukkan nomor pelacakan";
"time_info" = "Informasi Waktu";
"time_information" = "Informasi Waktu";
"estimated_arrival_time" = "Perkiraan Waktu Tiba";
"anytime" = "Kapan Saja";
"stop_time" = "Waktu Berhenti";
"minutes_format" = "%d menit";
"photo_record" = "Rekaman Foto";
"door_number_photo" = "Foto Nomor Pintu";
"package_label_photo" = "Foto Label Paket";
"placement_photo" = "Foto Penempatan";
"door_number_desc" = "Ambil foto nomor rumah atau jalan yang jelas, pastikan angka/huruf terlihat";
"package_label_desc" = "Ambil foto label paket, pastikan informasi penerima terlihat jelas";
"placement_desc" = "Ambil foto posisi akhir penempatan paket";
"photo_captured" = "Foto telah diambil";
"photo_captured_options" = "Foto telah diambil, apakah Anda ingin melanjutkan ke foto berikutnya atau menyelesaikan foto saat ini?";
"continue_to_next_photo" = "Lanjutkan ke foto berikutnya - %@";
"retake" = "Ambil Ulang";
"tap_to_capture" = "Ketuk untuk mengambil foto";
"flash_auto" = "Flash Otomatis";
"flash_on" = "Nyalakan Flash";
"flash_off" = "Matikan Flash";
"photo_record_completed" = "Rekaman Foto Selesai";
"photo_confirmation" = "Konfirmasi Foto";
"error" = "Error";
"ok" = "OK";
"complete_photo_capture" = "Selesai Mengambil Foto";
"tap_to_capture" = "Ketuk untuk mengambil foto";
"photo_instructions" = "Ketuk setiap kartu foto untuk mengambil foto. Semua foto harus diselesaikan.";
"photo_options" = "Opsi Foto";
"view_photo" = "Lihat Foto";
"retake_photo" = "Ambil Ulang";
"saving_photos" = "Menyimpan foto...";
"completed" = "Selesai";
"not_taken" = "Belum Diambil";
"route_options" = "Opsi Rute";
"avoid_tolls" = "Hindari Jalan Tol";
"avoid_highways" = "Hindari Jalan Raya";
"optimize_route" = "Optimalkan Rute";
"optimizing" = "Mengoptimalkan...";
"optimization_complete" = "Optimalisasi Selesai";

// MARK: - Addresses
"addresses" = "Alamat";
"add_address" = "Tambah Alamat";
"edit_address" = "Edit Alamat";
"delete_address" = "Hapus Alamat";
"address_details" = "Detail Alamat";
"street" = "Jalan";
"city" = "Kota";
"state" = "Negara Bagian/Provinsi";
"country" = "Negara";
"postal_code" = "Kode Pos";
"phone" = "Telepon";
"email" = "Email";
"website" = "Website";
"company" = "Perusahaan";
"notes" = "Catatan";
"coordinates" = "Koordinat";
"latitude" = "Lintang";
"longitude" = "Bujur";
"geocoding_error" = "Error Geocoding";
"address_validation" = "Validasi Alamat";
"invalid_addresses" = "Alamat Tidak Valid";
"fix_addresses" = "Perbaiki Alamat";

// MARK: - Routes
"route" = "Rute";
"routes" = "Rute";
"select_address_point" = "Pilih Titik Alamat";
"select_delivery_points" = "Pilih Titik Pengiriman";
"create_delivery_route" = "Buat Rute Pengiriman";
"view_saved_routes" = "Lihat Rute Tersimpan";
"create_route" = "Buat Rute";
"edit_route" = "Edit Rute";
"delete_route" = "Hapus Rute";
"route_name" = "Nama Rute";
"route_details" = "Detail Rute";
"selected_addresses" = "%d alamat dipilih";
"reached_limit" = "Mencapai batas";
"can_select_more" = "Masih bisa pilih %d";
"navigate_button" = "Navigasi";
"create_group" = "Buat Grup";
"start_point" = "Titik Awal";
"end_point" = "Titik Akhir";
"waypoints" = "Titik Transit";
"total_distance" = "Total Jarak";
"estimated_time" = "Perkiraan Waktu";
"route_summary" = "Ringkasan Rute";
"route_options" = "Opsi Rute";
"route_saved" = "Rute Tersimpan";
"route_optimized" = "Rute Dioptimalkan";
"optimizing_route" = "Mengoptimalkan Rute...";
"completed_percent" = "Selesai %d%%";
"processing_points" = "Memproses: %d/%d";
"estimated_remaining_time" = "Perkiraan Waktu Tersisa: %@";

// MARK: - Delivery
"delivery" = "Pengiriman";
"delivery_confirmation" = "Konfirmasi Pengiriman";
"take_photo" = "Ambil Foto";
"signature" = "Tanda Tangan";
"delivery_notes" = "Catatan Pengiriman";
"delivery_status" = "Status Pengiriman";
"delivered" = "Terkirim";
"not_delivered" = "Belum Terkirim";
"delivery_time" = "Waktu Pengiriman";
"delivery_date" = "Tanggal Pengiriman";
"package_details" = "Detail Paket";
"package_id" = "ID Paket";
"package_weight" = "Berat Paket";
"package_dimensions" = "Dimensi Paket";
"recipient_name" = "Nama Penerima";
"recipient_phone" = "Telepon Penerima";

// MARK: - Groups
"groups" = "Grup";
"saved_groups" = "Grup Tersimpan";
"create_group" = "Buat Grup";
"edit_group" = "Edit Grup";
"delete_group" = "Hapus Grup";
"group_name" = "Nama Grup";
"group_details" = "Detail Grup";
"auto_grouping" = "Pengelompokan Otomatis";
"group_by" = "Kelompokkan Berdasarkan";
"add_to_group" = "Tambah ke Grup";
"remove_from_group" = "Hapus dari Grup";
"group_created" = "Grup Dibuat";
"default_group_name_format" = "Grup %d";
"auto_grouping_completed" = "Pengelompokan Otomatis Selesai";
"auto_grouping_in_progress" = "Pengelompokan Otomatis Sedang Berlangsung...";
"create_group_every_14_addresses" = "Buat grup untuk setiap 14 alamat";
"create_delivery_group" = "Buat Grup Pengiriman";
"enter_group_name" = "Masukkan Nama Grup";
"selected_delivery_points" = "Titik Pengiriman Terpilih";
"drag_to_adjust_order" = "Seret untuk menyesuaikan urutan";

// MARK: - Subscription
"subscription" = "Berlangganan";
"free_plan" = "Paket Gratis";
"pro_plan" = "Paket Pro";
"expert_plan" = "Paket Expert";
"monthly" = "Paket Bulanan";
"yearly" = "Paket Tahunan";
"subscribe" = "Berlangganan";
"upgrade" = "Upgrade";
"upgrade_to_pro" = "Upgrade ke Pro";
"manage_subscription" = "Kelola Berlangganan";
"restore_purchases" = "Pulihkan Pembelian";
"subscription_benefits" = "Manfaat Berlangganan";
"free_trial" = "Uji Coba Gratis";
"price_per_month" = "%@ per bulan";
"price_per_year" = "%@ per tahun";
"save_percent" = "Hemat %@%";
"current_plan" = "Paket Saat Ini";
"subscription_terms" = "Syarat Berlangganan";
"privacy_policy" = "Kebijakan Privasi";
"terms_of_service" = "Syarat Layanan";

// MARK: - Import/Export
"import" = "Impor";
"export" = "Ekspor";
"import_addresses" = "Impor Alamat";
"export_addresses" = "Ekspor Alamat";
"import_from_file" = "Impor dari File";
"export_to_file" = "Ekspor ke File";
"file_format" = "Format File";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Berhasil mengimpor %d alamat, semuanya dengan koordinat yang valid.";
"export_success" = "Ekspor Berhasil";
"import_error" = "Error Impor";
"export_error" = "Error Ekspor";

// MARK: - Navigation
"navigate" = "Navigasi";

// MARK: - Look Around
"show_look_around" = "Tampilkan Look Around";
"hide_look_around" = "Sembunyikan Look Around";

// MARK: - Map
"map" = "Peta";
"map_type" = "Jenis Peta";
"standard" = "Standar";
"satellite" = "Satelit";
"hybrid" = "Hybrid";
"show_traffic" = "Tampilkan Lalu Lintas";
"current_location" = "Lokasi Saat Ini";
"directions" = "Petunjuk Arah";
"distance_to" = "Jarak";
"eta" = "Perkiraan Waktu Tiba";
"look_around" = "Look Around";
"locating_to_glen_waverley" = "Mencari lokasi ke Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Error Jaringan";
"location_error" = "Error Lokasi";
"permission_denied" = "Izin Ditolak";
"location_permission_required" = "Diperlukan Izin Lokasi";
"camera_permission_required" = "Diperlukan Izin Kamera";
"photo_library_permission_required" = "Diperlukan Izin Galeri Foto";
"please_try_again" = "Silakan Coba Lagi";
"something_went_wrong" = "Terjadi Kesalahan";
"invalid_input" = "Input Tidak Valid";
"required_field" = "Field Wajib";
"no_internet_connection" = "Tidak Ada Koneksi Internet";
"server_error" = "Error Server";
"timeout_error" = "Timeout Permintaan";
"data_not_found" = "Data Tidak Ditemukan";
"selection_limit_reached" = "Batas Seleksi Tercapai";
"selection_limit_description" = "Anda dapat memilih maksimal %d alamat, Anda telah memilih %d";

// MARK: - Location Validation Status
"location_status_valid" = "Rentang Valid";
"location_status_warning" = "Rentang Peringatan";
"location_status_invalid" = "Lokasi Tidak Valid";
"location_status_unknown" = "Status Tidak Diketahui";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Tidak Valid: koordinat nol (0,0)";
"coordinates_invalid_nan" = "Tidak Valid: koordinat non-numerik";
"coordinates_out_of_range" = "Tidak Valid: koordinat di luar rentang valid";
"coordinates_far_from_user" = "Peringatan: lokasi jauh dari posisi Anda saat ini";
"coordinates_ocean" = "Peringatan: lokasi mungkin di laut atau area tak berpenghuni";

// MARK: - Batch Address Input
"batch_add_addresses" = "Tambah Alamat Massal";
"batch_address_input_placeholder" = "Silakan masukkan atau tempel alamat, satu alamat per baris. Maksimal 35 alamat.";
"free_address_limit" = "Batas Alamat Versi Gratis";
"address_count_limit" = "Batas Jumlah Alamat";
"free_version_max_addresses" = "Versi gratis maksimal mengizinkan %d alamat.";
"current_addresses_remaining" = "Saat ini sudah ada %d alamat, hanya bisa menambah %d alamat lagi.";
"current_route_address_limit" = "Rute saat ini sudah memiliki %d alamat, hanya bisa menambah %d alamat lagi, total maksimal %d alamat.";
"selected_addresses_can_import" = "Anda memilih %d alamat, alamat ini dapat diimpor.";
"selected_addresses_exceeds" = "Anda memilih %d alamat, melebihi jumlah yang dapat ditambahkan sebanyak %d.";
"selected_addresses_all_importable" = "Anda memilih %d alamat, semua dapat diimpor.";
"upgrade_for_unlimited_addresses" = "Upgrade ke versi premium untuk alamat tak terbatas!";
"import_first_n_addresses" = "Hanya impor %d pertama";
"import_all_addresses" = "Impor Semua Alamat";
"import_selected_addresses" = "Impor Alamat Terpilih";
"no_importable_addresses" = "Tidak ada alamat yang dapat diimpor, silakan periksa batas alamat";
"please_enter_valid_address" = "Silakan masukkan setidaknya satu alamat yang valid";

// MARK: - File Import
"import_success" = "Berhasil mengimpor %d alamat, semuanya dengan koordinat yang valid.";
"import_success_with_warnings" = "Berhasil mengimpor %d alamat, di mana %d alamat koordinatnya normal, %d alamat memiliki peringatan.\n\nAlamat dengan peringatan telah ditandai, dapat diperbaiki secara manual setelah impor.";

// MARK: - Web Download
"web_download" = "Unduh Web";
"supported_formats" = "Format yang Didukung";
"supported_format_csv" = "• File CSV: kolom alamat harus berisi alamat lengkap";
"supported_format_json" = "• Data JSON: array yang berisi field alamat";
"supported_format_text" = "• Teks biasa: satu alamat per baris";
"download_history" = "Riwayat Unduhan";
"upgrade_to_premium" = "Upgrade ke Versi Premium";
"input_address_data_url" = "Masukkan URL Data Alamat";
"import_result" = "Hasil Impor";
"import_addresses" = "Impor Alamat";
"downloading" = "Mengunduh...";
"processing_data" = "Memproses data...";
"google_drive_download_failed" = "Gagal mengunduh Google Drive";
"second_attempt_invalid_data" = "Percobaan kedua mengunduh mengembalikan data tidak valid";
"cannot_parse_json" = "Tidak dapat mengurai data JSON, silakan periksa format file";
"cannot_parse_json_with_error" = "Tidak dapat mengurai data JSON: %@";
"cannot_get_address_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"cannot_read_file" = "Tidak dapat membaca file: %@";
"success" = "Berhasil";
"warning" = "Peringatan";
"failed" = "Pengiriman Gagal";
"no_matching_addresses" = "Tidak ditemukan alamat yang cocok";
"no_valid_addresses" = "Tidak ditemukan alamat yang valid";
"confirm" = "Konfirmasi";
"processing_addresses" = "Memproses alamat...";
"supports_file_types" = "Mendukung file CSV, TXT dan JSON";
"tap_to_select_file" = "Ketuk untuk memilih file";
"import_addresses" = "Impor Alamat";
"company_name_optional" = "Nama Perusahaan (Opsional)";
"input_company_name" = "Masukkan nama perusahaan (opsional)";
"imported_addresses_count" = "Telah mengimpor %d alamat";
"select_all" = "Pilih Semua";
"excel_format_not_supported" = "Format Excel tidak didukung";
"no_matching_addresses" = "Tidak ditemukan alamat yang cocok";

// MARK: - Import Limits
"import_failed" = "Impor Gagal";
"no_importable_addresses" = "Tidak ada alamat yang dapat diimpor, silakan periksa batas alamat";
"free_version_address_limit" = "Versi gratis maksimal mengizinkan %d alamat.";
"current_address_count" = "Saat ini sudah ada %d alamat, hanya bisa menambah %d alamat lagi.";
"can_import_selected" = "Anda memilih %d alamat, alamat ini dapat diimpor.";
"selected_exceeds_limit" = "Anda memilih %d alamat, melebihi jumlah yang dapat ditambahkan sebanyak %d.";
"upgrade_to_premium_unlimited" = "Upgrade ke versi premium untuk alamat tak terbatas!";
"route_address_limit" = "Rute saat ini sudah memiliki %d alamat, hanya bisa menambah %d alamat lagi, total maksimal %d alamat.";
"free_version_limit" = "Batas Alamat Versi Gratis";
"address_count_limit" = "Batas Jumlah Alamat";
"import_selected_addresses" = "Impor Alamat Terpilih";
"import_first_n" = "Hanya impor %d pertama";
"import_all_n" = "Impor semua %d";
"cannot_import" = "Tidak dapat mengimpor";
"select_at_least_one" = "Silakan pilih setidaknya satu alamat";

// MARK: - Import Results
"no_valid_addresses_found" = "Tidak ditemukan alamat yang valid";
"import_success_all_valid" = "Berhasil mengimpor %d alamat, semua koordinat alamat normal.";
"import_success_some_warnings" = "Berhasil mengimpor %d alamat, di mana %d alamat koordinatnya normal, %d alamat tidak dapat memperoleh koordinat.";

// MARK: - Warnings
"invalid_csv_row" = "Baris CSV tidak valid";
"distance_warning" = "Jarak lebih dari 200 km dari lokasi saat ini";
"not_in_australia" = "Koordinat tidak dalam wilayah Australia";
"cannot_get_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"empty_address" = "Alamat kosong";
"invalid_address_data" = "Data alamat tidak valid";

// MARK: - Saved Groups
"saved_groups" = "Grup Tersimpan";
"no_saved_groups" = "Tidak ada grup tersimpan";
"select_points_create_groups" = "Pilih titik pengiriman dan buat grup untuk memudahkan pengelolaan";
"group_name" = "Nama Grup";
"group_details" = "Detail Grup";
"navigate_to_these_points" = "Navigasi ke titik-titik ini";
"confirm_remove_address" = "Yakin ingin menghapus alamat \"%@\" dari grup?";
"confirm_remove_this_address" = "Yakin ingin menghapus alamat ini dari grup?";
"addresses_count" = "%d alamat";
"no_saved_routes" = "Tidak ada rute tersimpan";
"no_saved_routes_description" = "Anda belum menyimpan rute apapun";
"all_routes" = "Semua Rute";
"address_count_format_simple" = "%d alamat";
"delete_all_routes" = "Hapus Semua Rute";
"navigate_to_all_points" = "Navigasi ke Semua Titik";
"confirm_navigate_to_route" = "Yakin ingin navigasi ke semua titik dalam rute \"%@\"?";
"temp_navigation_group" = "Grup Navigasi Sementara";

// MARK: - Route Management
"route_management" = "Manajemen Rute";
"route_info" = "Informasi Rute";
"route_name" = "Nama Rute";
"route_addresses" = "Alamat Rute";
"no_addresses_in_route" = "Rute ini tidak memiliki alamat";
"must_keep_one_route" = "Harus menyimpan setidaknya satu rute";
"confirm_delete_route" = "Yakin ingin menghapus rute \"%@\"? Tindakan ini tidak dapat dibatalkan.";
"confirm_delete_all_routes" = "Konfirmasi Hapus Semua Rute";
"confirm_delete_all_routes_message" = "Yakin ingin menghapus semua rute? Tindakan ini tidak dapat dibatalkan.";
"delete_all" = "Hapus Semua";

// MARK: - Navigation Buttons
"navigate" = "Navigasi";

// MARK: - GroupDetailView
"points_count_format" = "%d titik";

// MARK: - DeliveryPointDetailView
"address_information" = "Informasi Alamat";
"group_belonging" = "Grup yang Dimiliki";
"view_map" = "Lihat Peta";
"delivery_status" = "Status Pengiriman";
"notes" = "Catatan";
"delete_delivery_point" = "Hapus Titik Pengiriman";
"delivery_point_details" = "Detail Titik Pengiriman";
"confirm_deletion" = "Konfirmasi Hapus";
"delete_delivery_point_confirmation" = "Yakin ingin menghapus titik pengiriman ini? Tindakan ini tidak dapat dibatalkan.";

// MARK: - Delivery Photos
"delivery_photos" = "Foto Pengiriman";
"view_delivery_photos" = "Lihat Foto Pengiriman";
"no_photos_taken" = "Belum mengambil foto";
"take_photos" = "Ambil Foto";
"loading_photos" = "Memuat foto...";
"photo_not_found" = "Foto tidak ditemukan";
"photo_deleted" = "Foto telah dihapus";
"door_number_photo" = "Foto Nomor Pintu";
"package_label_photo" = "Foto Label Paket";
"placement_photo" = "Foto Penempatan";
"share_photos" = "Bagikan Foto";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Konfirmasi Foto";
"door_number_photo_title" = "Foto Nomor Jalan/Rumah";
"package_label_photo_title" = "Foto Label Paket";
"placement_photo_title" = "Foto Lokasi Penempatan";
"door_number_photo_desc" = "Ambil foto nomor rumah yang jelas, pastikan angka/huruf terlihat";
"package_label_photo_desc" = "Ambil foto label paket, pastikan informasi penerima terlihat jelas";
"placement_photo_desc" = "Ambil foto posisi akhir penempatan paket";
"swipe_to_switch" = "Geser untuk beralih jenis foto";
"complete_photos" = "Selesai Mengambil Foto";
"saving_photos" = "Menyimpan foto...";
"photo_save_success" = "Foto berhasil disimpan";
"photo_save_failure" = "Gagal menyimpan foto";
"retake_photo" = "Ambil Ulang";
"no_photos_found" = "Foto tidak ditemukan";
"photos_deleted_or_not_taken" = "Mungkin foto telah dihapus atau belum diambil";
"share_photo" = "Bagikan Foto";
"photo_capture_preview" = "Mode Pratinjau - Simulasi Kamera";
"photo_capture_close" = "Tutup";
"camera_start_failed" = "Gagal memulai kamera";
"camera_start_failed_retry" = "Tidak dapat memulai kamera, silakan coba lagi";
"camera_init_failed" = "Gagal menginisialisasi kamera";
"camera_access_failed" = "Tidak dapat mengakses kamera";
"photo_processing_failed" = "Gagal mengambil foto";
"photo_processing_failed_retry" = "Tidak dapat menyelesaikan pemrosesan foto, silakan coba lagi";
"photo_capture_progress" = "Progres: %d/%d";
"photo_captured_continue" = "Pengambilan selesai, lanjutkan mengambil %@";
"loading_photos" = "Memuat foto...";
"cancel" = "Batal";

// MARK: - Delivery Status
"pending" = "Menunggu Pengiriman";
"in_progress" = "Sedang Dikirim";
"completed" = "Selesai";
"failed" = "Pengiriman Gagal";
"update_status" = "Perbarui Status";
"select_delivery_status" = "Pilih Status Pengiriman";
"select_failure_reason" = "Pilih Alasan Kegagalan";
"delivered" = "Terkirim";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Pelanggan tidak di rumah";
"failure_reason_wrong_address" = "Alamat salah";
"failure_reason_no_access" = "Tidak dapat masuk ke lokasi";
"failure_reason_rejected" = "Paket ditolak";
"failure_reason_other" = "Alasan lain";
"enter_custom_reason" = "Masukkan alasan spesifik";
"custom_reason_placeholder" = "Silakan jelaskan alasan spesifik...";
"custom_reason_required" = "Silakan masukkan alasan spesifik";
"failure_reason_required" = "Silakan pilih alasan kegagalan";

// MARK: - Address Validation
"address_validation_failed" = "Validasi alamat gagal";

