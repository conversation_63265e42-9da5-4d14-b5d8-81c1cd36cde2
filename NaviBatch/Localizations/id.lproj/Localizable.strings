/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Pengaturan Bahasa";
"system_language" = "Bahasa Sistem";
"system_language_section" = "Pengaturan Sistem";
"languages" = "Bahasa";
"language_info_title" = "Tentang Pengaturan Bahasa";
"language_info_description" = "Setelah mengubah pengaturan bahasa, aplikasi akan menampilkan teks dalam bahasa yang dipilih. Beberapa konten mungkin memerlukan restart aplikasi untuk menerapkan pengaturan bahasa baru sepenuhnya.";
"restart_required" = "Perlu Restart Aplikasi";
"restart_app_message" = "Untuk menerapkan perubahan bahasa sepenuhnya, silakan restart aplikasi.";
"restart_now" = "Restart Sekarang";
"restart_later" = "Restart Nanti";
"close" = "Tutup";
"cancel" = "Batal";
"save" = "Simpan";
"edit" = "Edit";
"delete" = "Hapus";
"done" = "Selesai";
"next" = "Selanjutnya";
"back" = "Kembali";
"confirm" = "Konfirmasi";
"error" = "Error";
"success" = "Berhasil";
"warning" = "Peringatan";
"unknown_error" = "Kesalahan tidak dikenal";
"loading" = "Memuat...";
"search" = "Cari";
"settings" = "Pengaturan";
"help" = "Bantuan";
"about" = "Tentang";
"menu" = "Menu";
"understand" = "Saya Mengerti";
"navigation" = "Navigasi";
"start_navigation" = "Mulai Navigasi";
"subscription" = "Berlangganan";
"upgrade_to_pro" = "Upgrade ke Pro";
"upgrade_description" = "Pengelompokan navigasi satu klik, 60x lebih cepat dari operasi manual";
"restore_purchases" = "Pulihkan Pembelian";
"learn_more" = "Pelajari Lebih Lanjut";
"upgrade_your_plan" = "Upgrade Paket Anda";
"one_click_navigation_description" = "Pengelompokan alamat navigasi satu klik, hemat waktu dan bahan bakar";
"current_plan" = "Paket Saat Ini";
"upgrade" = "Upgrade";
"maybe_later" = "Nanti Saja";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratis";
"pro_tier_price" = "$9.99/bulan";
"expert_tier_price" = "$59.99/tahun";
"free_tier_description" = "Cocok untuk individu dan bisnis kecil, tidak memerlukan banyak perhentian";
"pro_tier_description" = "Untuk pengguna yang membutuhkan pengelompokan navigasi satu klik dan alamat tak terbatas";
"expert_tier_description" = "Sama dengan Pro, tetapi hemat 31% dengan paket tahunan";
"route_optimization" = "Optimalisasi Rute";
"unlimited_routes" = "Rute Tak Terbatas";
"unlimited_optimizations" = "Optimalisasi Tak Terbatas";
"max_15_addresses" = "Maksimal 15 alamat per rute";
"save_fuel_30" = "Hemat bahan bakar hingga 30%";
"unlimited_addresses" = "✨ Alamat Tak Terbatas ✨";
"one_click_navigation" = "⚡ Pengelompokan Navigasi Satu Klik - 60x Lebih Cepat ⚡";
"package_finder" = "Pencari Lokasi Paket";
"annual_savings" = "Penghematan Paket Tahunan";
"switched_to_free" = "Beralih ke Paket Gratis";
"switched_to_subscription" = "Beralih ke Paket Berlangganan";
"unlimited_stops" = "Perhentian Tak Terbatas";
"plan_as_many_stops_as_needed" = "Tambahkan titik pengiriman sebanyak yang diperlukan tanpa batasan";
"expires_in_days" = "Berakhir dalam %d hari";
"trial_expires_in_days" = "Masa percobaan berakhir dalam %d hari";
"expired" = "Berakhir";
"subscription_expires_on" = "Langganan berakhir pada %@";
"subscription_active_until" = "Langganan aktif hingga %@";
"enter_or_search_address" = "Masukkan atau cari alamat";
"search_results_count" = "Hasil pencarian: %d";
"no_matching_addresses" = "Tidak ditemukan alamat yang cocok";
"search_address_failed" = "Gagal mencari alamat: %@";
"address_search_no_response" = "Pencarian alamat tidak merespons";
"cannot_get_address_coordinates" = "Tidak dapat memperoleh koordinat alamat";

"cannot_get_coordinates_retry" = "Tidak dapat memperoleh koordinat alamat, silakan masukkan secara manual atau coba lagi";

"image_address_recognition" = "Pengenalan alamat dari gambar";
"select_images" = "Pilih gambar";
"select_multiple_images" = "Pemilihan ganda didukung";
"processing_images" = "Memproses gambar...";
"processing_image_progress" = "Memproses gambar %d dari %d";
"recognizing_text" = "Mengenali teks...";
"geocoding_addresses" = "Mendapatkan koordinat alamat...";
"recognition_complete" = "Pengenalan selesai";
"no_text_recognized" = "Tidak ada teks yang dikenali";
"no_addresses_found" = "Tidak ditemukan alamat yang valid";
"image_recognition_failed" = "Pengenalan gambar gagal";
"image_recognition_error" = "Kesalahan pengenalan gambar: %@";
"text_recognition_failed" = "Pengenalan teks gagal";
"address_parsing_failed" = "Parsing alamat gagal";
"select_addresses_to_add" = "Pilih alamat untuk ditambahkan";
"recognized_addresses" = "Alamat yang dikenali";
"address_coordinates" = "Koordinat alamat";
"toggle_address_selection" = "Beralih pemilihan alamat";
"remove_address" = "Hapus alamat";
"confirm_selected_addresses" = "Konfirmasi alamat yang dipilih";
"no_addresses_selected" = "Tidak ada alamat yang dipilih";
"image_processing_cancelled" = "Pemrosesan gambar dibatalkan";
"unsupported_image_format" = "Format gambar tidak didukung";
"image_too_large" = "File gambar terlalu besar";
"image_recognition_permission_required" = "Diperlukan izin akses galeri foto";
"ocr_language_detection" = "Deteksi bahasa otomatis";
"improve_image_quality" = "Pastikan gambar jelas dan teks terlihat";
"address_validation_in_progress" = "Memvalidasi alamat...";
"batch_address_import" = "Impor alamat massal";
"validated_addresses_count" = "%d alamat divalidasi";
"addresses_with_issues" = "%d alamat memiliki masalah";
"select_all" = "Pilih Semua";
"import_selected" = "Impor yang dipilih";
"validating_addresses" = "Memvalidasi alamat...";
"empty_address" = "Alamat kosong";
"invalid_coordinates" = "Koordinat tidak valid";
"coordinate_warning" = "Peringatan koordinat";
"address_validation_issue" = "Masalah validasi alamat";
"cannot_get_coordinates" = "Tidak dapat memperoleh koordinat alamat";
"no_importable_addresses" = "Tidak ada alamat yang dapat diimpor, silakan periksa batas alamat";
"free_version_max_addresses" = "Versi gratis maksimal mengizinkan %d alamat.";
"valid" = "Valid";
"with_issues" = "Dengan masalah";
"low_confidence_address" = "Validasi alamat kepercayaan rendah";
"address_validation_failed" = "Validasi alamat gagal";
"current_addresses_remaining" = "Saat ini sudah ada %d alamat, hanya bisa menambah %d alamat lagi.";
"can_import_selected" = "Anda memilih %d alamat, alamat ini dapat diimpor.";
"selected_exceeds_limit" = "Anda memilih %d alamat, melebihi jumlah yang dapat ditambahkan sebanyak %d.";
"selected_addresses_all_importable" = "Anda memilih %d alamat, semua dapat diimpor.";
"upgrade_for_unlimited_addresses" = "Upgrade ke versi premium untuk alamat tak terbatas!";
"current_route_address_limit" = "Rute saat ini sudah memiliki %d alamat, hanya bisa menambah %d alamat lagi, total maksimal %d alamat.";
"import_all_addresses" = "Impor Semua Alamat";
"import_first_n" = "Hanya impor %d pertama";
"import_selected_addresses" = "Impor Alamat Terpilih";
"upgrade_to_premium" = "Upgrade ke Versi Premium";
"batch_add_addresses" = "Tambah Alamat Massal";
"batch_address_input_placeholder" = "Silakan masukkan atau tempel alamat, satu alamat per baris. Maksimal 35 alamat.";
"search_address" = "Cari Alamat";
"no_saved_addresses" = "Tidak Ada Alamat Tersimpan";
"no_saved_addresses_description" = "Anda belum menyimpan alamat apapun, atau tidak ada alamat yang sesuai dengan kriteria filter";
"select_address_book" = "Pilih Buku Alamat";
"routes" = "Rute";
"address_book" = "Buku Alamat";
"saved_routes" = "Rute Tersimpan";
"manage_your_routes" = "Kelola perencanaan rute Anda";
"manage_your_addresses" = "Kelola alamat yang sering digunakan";
"preferences" = "Preferensi";
"set_custom_start_point" = "Atur Titik Awal Kustom";
"current_start_point" = "Titik awal saat ini: %@";
"support" = "Dukungan";
"contact_us" = "Hubungi Kami";
"contact_us_description" = "Punya pertanyaan atau saran? Jangan ragu untuk menghubungi kami!";
"help_center" = "Pusat Bantuan";
"quick_actions" = "Tindakan cepat";
"main_features" = "Fitur utama";
"support_help" = "Dukungan dan bantuan";
"customize_app_settings" = "Sesuaikan pengaturan aplikasi";
"unlock_all_features" = "Buka semua fitur";
"get_help_support" = "Dapatkan bantuan dan dukungan";
"app_info_version" = "Info dan versi aplikasi";
"dev_tools" = "Alat pengembang";
"debug_testing_tools" = "Alat debug dan pengujian";
"version" = "Versi";
"addresses" = "Alamat";
"limited_to_20_addresses" = "Terbatas pada 20 alamat";
"all_premium_features" = "Semua fitur premium";
"open_subscription_view" = "Buka langsung tampilan berlangganan";
"open_subscription_view_description" = "Lewati lapisan tengah, tampilkan SubscriptionView langsung";
"restore_purchases_failed" = "Gagal memulihkan pembelian: %@";
"rate_us" = "Beri Nilai";
"rate_us_description" = "Umpan balik Anda penting bagi kami dan membantu kami meningkatkan aplikasi!";
"share_app" = "Bagikan Aplikasi";
"share_app_text" = "Coba NaviBatch, aplikasi perencanaan rute yang luar biasa!";
"about_app" = "Tentang Aplikasi";
"developer_tools" = "Alat Pengembang";
"coordinate_debug_tool" = "Alat Debug Koordinat";
"batch_fix_addresses" = "Perbaiki Alamat Massal";
"clear_database" = "Hapus Database";
"clear_database_confirmation" = "Ini akan menghapus semua data, termasuk rute, alamat, dan grup. Tindakan ini tidak dapat dibatalkan. Apakah Anda yakin ingin melanjutkan?";
"confirm_clear" = "Konfirmasi Hapus";
"version_info" = "Versi %@ (%@)";
"current_system_language" = "Bahasa Sistem Saat Ini";
"reset_to_system_language" = "Reset ke Bahasa Sistem";
"language" = "Bahasa";
"address" = "Alamat";
"coordinates" = "Koordinat";
"distance_from_current_location" = "Jarak dari Lokasi Saat Ini";
"address_info" = "Informasi Alamat";
"update_coordinates" = "Perbarui Koordinat";
"fix_address" = "Perbaiki Alamat";
"prompt" = "Peringatan";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Silakan ubah alamat sebelum memperbarui koordinat";
"coordinates_update_success" = "Koordinat berhasil diperbarui";
"coordinates_update_failure" = "Gagal memperbarui koordinat";
"save_failure" = "Gagal menyimpan: %@";
"no_saved_addresses_title" = "Tidak Ada Alamat Tersimpan";
"no_saved_addresses_message" = "Anda belum menyimpan alamat apapun";
"add_new_address" = "Tambah Alamat Baru";
"address_title" = "Alamat";
"add" = "Tambah";
"refresh" = "Segarkan";
"notes" = "Catatan";
"address_details" = "Detail Alamat";
"favorite" = "Favorit";
"edit_address" = "Edit Alamat";
"confirm_delete" = "Konfirmasi Hapus";
"delete_address_confirmation" = "Yakin ingin menghapus alamat ini? Tindakan ini tidak dapat dibatalkan.";
"address_marker" = "Alamat";
"address_label" = "Alamat:";
"notes_label" = "Catatan:";
"created_at_label" = "Dibuat:";
"open_in_maps" = "Buka di Peta";
"copy_address" = "Salin Alamat";
"address_details_title" = "Detail Alamat";
"start_end_point" = "Titik Awal/Akhir";
"start_point" = "Titik Awal";
"end_point" = "Titik Akhir";
"route_info" = "Informasi Rute";
"address_count" = "Jumlah Alamat";
"address_count_format" = "%d alamat";
"points_count_format" = "%d titik";
"additional_points_format" = "+%d titik";
"export_route" = "Ekspor Rute";
"navigate" = "Navigasi";
"address_list" = "Daftar Alamat";
"no_addresses" = "Tidak Ada Alamat";
"no_addresses_message" = "Rute ini belum memiliki alamat";
"address_point_start" = "Titik Awal";
"address_point_stop" = "Titik Berhenti";
"address_point_end" = "Titik Akhir";
"route_name" = "Nama Rute";
"new_route" = "Rute Baru";
"saved_route" = "Rute Tersimpan";
"plan_route" = "Rencanakan Rute";
"clear_all" = "Hapus Semua";
"avoid" = "Hindari:";
"toll_roads" = "Jalan Tol";
"highways" = "Jalan Raya";
"processing_addresses" = "Memproses alamat...";
"same_start_end_point" = "Anda telah menetapkan alamat yang sama sebagai titik awal dan akhir";
"add_start_point" = "Tambah Titik Awal";
"swipe_left_to_delete" = "Tekan lama untuk menghapus";
"add_end_point" = "Tambah Titik Akhir";
"enter_and_select_address" = "Masukkan dan pilih alamat";
"current_search_text" = "Teks pencarian saat ini: %@";
"add_address" = "Tambah Alamat";
"selected_coordinates" = "Koordinat Terpilih";
"company_name_optional" = "Nama Perusahaan (Opsional)";
"url_optional" = "URL (Opsional)";
"favorite_address" = "Alamat Favorit";
"set_as_start_and_end" = "Atur sebagai Titik Awal dan Akhir";
"batch_paste" = "Tempel Massal";
"file_import" = "Impor File";
"web_download" = "Unduh Web";
"saving" = "Menyimpan...";
"delivery_point_management" = "Manajemen Titik Pengiriman";
"information_category" = "Kategori Informasi";
"package_info" = "Informasi Paket";
"vehicle_position" = "Posisi Kendaraan";
"delivery_info" = "Informasi Pengiriman";
"take_photo_record" = "Ambil Foto Rekaman";
"update_status" = "Perbarui Status";
"edit_address_button" = "Edit Alamat";
"access_instructions" = "Instruksi Akses";
"add_access_instructions" = "Tambah instruksi akses...";
"package_count" = "Jumlah Paket";
"packages" = "paket";
"package_unit" = "pcs";
"package_size" = "Ukuran Paket";
"package_type" = "Jenis Paket";
"mark_as_important" = "Tandai sebagai Penting";
"priority_delivery" = "Pengiriman prioritas";
"priority_level" = "Tingkat prioritas";
"priority_1" = "Prioritas 1";
"priority_2" = "Prioritas 2";
"priority_3" = "Prioritas 3";
"no_priority" = "Tanpa prioritas";
"no_priority_short" = "Tidak ada";
"set_priority" = "Atur prioritas";
"select_package_position" = "Pilih Posisi Paket di Kendaraan";
"vehicle_area" = "Area Kendaraan";
"left_right_position" = "Posisi Kiri/Kanan";
"vehicle_position_front" = "Depan";
"vehicle_position_middle" = "Tengah";
"vehicle_position_back" = "Belakang";
"vehicle_position_left" = "Kiri";
"vehicle_position_right" = "Kanan";
"vehicle_position_floor" = "Bawah";
"vehicle_position_shelf" = "Atas";
"height_position" = "Posisi Tinggi";
"vehicle_position_none" = "Tidak ada posisi dipilih";
"delivery_type" = "Jenis Pengiriman";
"delivery_status" = "Status Pengiriman";
"order_info" = "Informasi Pesanan";
"order_information" = "Informasi Pesanan";
"order_number" = "Nomor Pesanan";
"enter_order_number" = "Masukkan nomor pesanan";
"tracking_number" = "Nomor Pelacakan";
"enter_tracking_number" = "Masukkan nomor pelacakan";
"tracking_info" = "Info pelacakan";
"tracking_information" = "Informasi pelacakan";
"time_info" = "Informasi Waktu";
"time_information" = "Informasi Waktu";
"estimated_arrival_time" = "Perkiraan Waktu Tiba";
"anytime" = "Kapan Saja";
"stop_time" = "Waktu Berhenti";
"minutes_format" = "%d menit";
"photo_record" = "Rekaman Foto";
"door_number_photo" = "Foto Nomor Pintu";
"package_label_photo" = "Foto Label Paket";
"placement_photo" = "Foto Penempatan";
"door_number_desc" = "Ambil foto nomor rumah atau jalan yang jelas, pastikan angka/huruf terlihat";
"package_label_desc" = "Ambil foto label paket, pastikan informasi penerima terlihat jelas";
"placement_desc" = "Ambil foto posisi akhir penempatan paket";
"photo_captured" = "Foto telah diambil";
"photo_captured_options" = "Foto telah diambil, apakah Anda ingin melanjutkan ke foto berikutnya atau menyelesaikan foto saat ini?";
"continue_to_next_photo" = "Lanjutkan ke foto berikutnya - %@";
"retake" = "Ambil Ulang";
"tap_to_capture" = "Ketuk untuk mengambil foto";
"flash_auto" = "Flash Otomatis";
"flash_on" = "Nyalakan Flash";
"flash_off" = "Matikan Flash";
"photo_record_completed" = "Rekaman Foto Selesai";
"photo_confirmation" = "Konfirmasi Foto";
"ok" = "OK";
"complete_photo_capture" = "Selesai Mengambil Foto";
"photo_instructions" = "Ketuk setiap kartu foto untuk mengambil foto. Semua foto harus diselesaikan.";
"photo_options" = "Opsi Foto";
"view_photo" = "Lihat Foto";
"retake_photo" = "Ambil Ulang";
"saving_photos" = "Menyimpan foto...";
"completed" = "Selesai";
"not_taken" = "Belum Diambil";
"route_options" = "Opsi Rute";
"avoid_tolls" = "Hindari Jalan Tol";
"avoid_highways" = "Hindari Jalan Raya";
"optimize_route" = "Optimalkan Rute";
"optimizing" = "Mengoptimalkan...";
"optimization_complete" = "Optimalisasi Selesai";
"route_optimization_results" = "Hasil optimasi rute";
"route_planning_options" = "Opsi perencanaan rute";
"before_optimization" = "Sebelum optimasi";
"after_optimization" = "Setelah optimasi";
"auto_group" = "Grup otomatis";
"optimized_route_order" = "Urutan rute yang dioptimalkan";
"apply" = "Terapkan";
"kilometers" = "kilometer";
"street_number_issue_warning" = "⚠️ Perbedaan besar pada nomor jalan! Ini dapat menyebabkan pengiriman ke alamat yang salah dan denda. Silakan periksa alamat segera";
"address_validation_critical" = "Masalah validasi alamat kritis";
"street_number_difference_high_risk" = "Perbedaan nomor jalan besar, risiko tinggi";
"delete_address" = "Hapus Alamat";
"street" = "Jalan";
"city" = "Kota";
"state" = "Negara Bagian/Provinsi";
"country" = "Negara";
"postal_code" = "Kode Pos";
"phone" = "Telepon";
"email" = "Email";
"website" = "Website";
"company" = "Perusahaan";
"latitude" = "Lintang";
"longitude" = "Bujur";
"geocoding_error" = "Error Geocoding";
"address_validation" = "Validasi Alamat";
"invalid_addresses" = "Alamat Tidak Valid";
"fix_addresses" = "Perbaiki Alamat";
"route" = "Rute";
"select_address_point" = "Pilih Titik Alamat";
"select_delivery_points" = "Pilih Titik Pengiriman";
"create_delivery_route" = "Buat Rute Pengiriman";
"view_saved_routes" = "Lihat Rute Tersimpan";
"create_route" = "Buat Rute";
"edit_route" = "Edit Rute";
"delete_route" = "Hapus Rute";
"route_details" = "Detail Rute";
"selected_addresses" = "%d alamat dipilih";
"reached_limit" = "Mencapai batas";
"can_select_more" = "Masih bisa pilih %d";
"navigate_button" = "Navigasi";
"create_group" = "Buat Grup";
"waypoints" = "Titik Transit";
"total_distance" = "Total Jarak";
"estimated_time" = "Perkiraan Waktu";
"route_summary" = "Ringkasan Rute";
"route_saved" = "Rute Tersimpan";
"route_optimized" = "Rute Dioptimalkan";
"optimizing_route" = "Mengoptimalkan Rute...";
"completed_percent" = "Selesai %d%%";
"processing_points" = "Memproses: %d/%d";
"estimated_remaining_time" = "Perkiraan Waktu Tersisa: %@";
"delivery" = "Pengiriman";
"delivery_confirmation" = "Konfirmasi Pengiriman";
"take_photo" = "Ambil Foto";
"signature" = "Tanda Tangan";
"delivery_notes" = "Catatan Pengiriman";
"delivered" = "Terkirim";
"not_delivered" = "Belum Terkirim";
"delivery_time" = "Waktu Pengiriman";
"delivery_date" = "Tanggal Pengiriman";
"package_details" = "Detail Paket";
"package_id" = "ID Paket";
"package_weight" = "Berat Paket";
"package_dimensions" = "Dimensi Paket";
"recipient_name" = "Nama Penerima";
"recipient_phone" = "Telepon Penerima";
"groups" = "Grup";
"saved_groups" = "Grup Tersimpan";
"edit_group" = "Edit Grup";
"delete_group" = "Hapus Grup";
"group_name" = "Nama Grup";
"group_details" = "Detail Grup";
"auto_grouping" = "Pengelompokan Otomatis";
"group_by" = "Kelompokkan Berdasarkan";
"add_to_group" = "Tambah ke Grup";
"remove_from_group" = "Hapus dari Grup";
"group_created" = "Grup Dibuat";
"default_group_name_format" = "Grup %d";
"auto_grouping_completed" = "Pengelompokan Otomatis Selesai";
"auto_grouping_in_progress" = "Pengelompokan Otomatis Sedang Berlangsung...";
"create_group_every_14_addresses" = "Buat grup untuk setiap 14 alamat";
"create_delivery_group" = "Buat Grup Pengiriman";
"enter_group_name" = "Masukkan Nama Grup";
"selected_delivery_points" = "Titik Pengiriman Terpilih";
"drag_to_adjust_order" = "Seret untuk menyesuaikan urutan";
"free_plan" = "Paket Gratis";
"pro_plan" = "Paket Pro";
"expert_plan" = "Paket Expert";
"monthly" = "Paket Bulanan";
"yearly" = "Paket Tahunan";
"subscribe" = "Berlangganan";
"manage_subscription" = "Kelola Berlangganan";
"subscription_benefits" = "Manfaat Berlangganan";
"free_trial" = "Uji Coba Gratis";
"price_per_month" = "%@ per bulan";
"price_per_year" = "%@ per tahun";
"save_percent" = "Hemat %@%";
"subscription_terms" = "Syarat Berlangganan";
"privacy_policy" = "Kebijakan Privasi";
"terms_of_service" = "Syarat Layanan";
"feature_comparison" = "Perbandingan fitur";
"addresses_per_route" = "Alamat per rute";
"max_20_addresses" = "Maksimal 20 alamat";
"fuel_savings" = "Penghematan bahan bakar";
"up_to_30_percent" = "Hingga 30%";
"choose_subscription_plan" = "Pilih paket langganan";
"monthly_plan" = "Paket bulanan";
"yearly_plan" = "Paket tahunan";
"/month_suffix" = "/bulan";
"/year_suffix" = "/tahun";
"save_30_percent" = "Hemat 30%";
"free_trial_7_days_cancel_anytime" = "Uji coba gratis 7 hari, batalkan kapan saja";
"subscription_auto_renew_notice" = "Langganan diperpanjang otomatis";
"and" = "dan";
"subscription_exclusive" = "Eksklusif untuk pelanggan";
"free_version_optimization_limit" = "Versi gratis terbatas optimasi";
"free_version_supports_max_addresses" = "Versi gratis mendukung alamat maksimal";
"current_route_contains_addresses" = "Rute saat ini berisi alamat";
"upgrade_to_pro_unlimited_addresses" = "Upgrade ke Pro untuk alamat tak terbatas";
"continue_optimization" = "Lanjutkan optimasi";
"upgrade_unlock_one_click_navigation" = "Upgrade untuk membuka navigasi satu klik";
"learn_one_click_navigation_grouping" = "Pelajari navigasi dan pengelompokan satu klik";
"toggle_subscription_status" = "Beralih status langganan";
"toggle_subscription_description" = "Beralih antara langganan dan gratis untuk tujuan pengujian";
"product_info_unavailable" = "Info produk tidak tersedia";
"purchase_failed" = "Pembelian gagal: %@";
"upgrade_to_pro_version" = "Upgrade ke versi Pro";
"unlock_all_premium_features" = "Buka semua fitur premium";
"first_7_days_free_cancel_anytime" = "7 hari pertama gratis, batalkan kapan saja";
"payment_terms_notice" = "Pembayaran akan dibebankan ke akun Apple ID Anda saat konfirmasi pembelian. Langganan diperpanjang otomatis kecuali dibatalkan setidaknya 24 jam sebelum akhir periode saat ini.";
"terms_of_use" = "Syarat penggunaan";
"product_load_failed_check_connection" = "Gagal memuat info produk, pastikan perangkat terhubung ke internet dan App Store";
"product_load_failed" = "Gagal memuat produk: %@";
"verify_receipt" = "Verifikasi tanda terima";
"one_click_navigation_short" = "Navigasi satu klik";
"save_30_percent_fuel" = "Hemat 30% bahan bakar";
"monthly_short" = "Bulanan";
"yearly_short" = "Tahunan";
"upgrade_now" = "Upgrade sekarang";
"test_environment_pro_activated" = "Lingkungan uji: Pro diaktifkan";
"payment_terms_notice_detailed" = "Pembayaran akan dibebankan ke akun Apple ID Anda saat konfirmasi pembelian. Langganan diperpanjang otomatis kecuali dibatalkan setidaknya 24 jam sebelum akhir periode saat ini. Langganan dapat dikelola dan dibatalkan di pengaturan App Store.";
"step_screenshot" = "Screenshot langkah %d";
"previous_step" = "Sebelumnya";
"next_step" = "Selanjutnya";
"each_address_takes_3_5_seconds" = "Setiap alamat membutuhkan 3-5 detik untuk ditambahkan";
"need_repeat_14_times" = "Perlu mengulangi operasi yang sama 14 kali";
"navigation_order_often_confused" = "Urutan navigasi sering membingungkan";
"error_prone_need_redo" = "Rentan kesalahan, perlu mengulang operasi";
"address_order_reversed_manual_adjust" = "Urutan alamat terbalik, perlu penyesuaian manual";
"one_click_add_all" = "Satu klik, tambahkan semua";
"smart_grouping_auto_sorting" = "Pengelompokan cerdas, penyortiran otomatis";
"maintain_correct_visit_order" = "Pertahankan urutan kunjungan yang benar";
"zero_errors_zero_repetition" = "Nol kesalahan, nol pengulangan";
"import" = "Impor";
"export" = "Ekspor";
"import_addresses" = "Impor Alamat";
"export_addresses" = "Ekspor Alamat";
"import_from_file" = "Impor dari File";
"export_to_file" = "Ekspor ke File";
"file_format" = "Format File";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Berhasil mengimpor %d alamat, semuanya dengan koordinat yang valid.";
"export_success" = "Ekspor Berhasil";
"import_error" = "Error Impor";
"export_error" = "Error Ekspor";
"navigation_app" = "Aplikasi navigasi";
"apple_maps" = "Apple Maps";
"app_preferences" = "Preferensi aplikasi";
"distance_unit" = "Unit jarak";
"current_language" = "Bahasa saat ini";
"info" = "Info";
"contact_us_header" = "Hubungi kami";
"contact_us_subheader" = "Punya pertanyaan atau saran? Kami senang membantu!";
"contact_options" = "Opsi kontak";
"email_us" = "Kirim email kepada kami";
"contact_form" = "Formulir kontak";
"contact_and_support" = "Kontak dan dukungan";
"common_questions" = "Pertanyaan umum";
"how_to_use" = "Cara menggunakan";
"subscription_faq" = "FAQ langganan";
"navigation_help" = "Bantuan navigasi";
"troubleshooting" = "Pemecahan masalah";
"help_howto_content" = "NaviBatch adalah aplikasi perencanaan rute yang kuat yang membantu Anda mengoptimalkan rute pengiriman, menghemat waktu dan bahan bakar. Anda dapat menambahkan beberapa alamat, mengoptimalkan urutan rute secara otomatis, dan menavigasi ke Apple Maps dengan satu klik.";
"help_subscription_content" = "NaviBatch menawarkan versi gratis dan pro. Versi gratis mendukung hingga 20 alamat, sedangkan versi pro menyediakan alamat tak terbatas dan fitur navigasi grup satu klik.";
"help_navigation_content" = "NaviBatch menggunakan Apple Maps untuk navigasi. Anda dapat menavigasi ke setiap alamat secara terpisah, atau menggunakan fitur pengelompokan untuk menavigasi ke beberapa alamat sekaligus.";
"help_troubleshooting_content" = "Jika Anda mengalami masalah, pastikan terlebih dahulu bahwa perangkat Anda memiliki koneksi jaringan dan izin lokasi telah diberikan. Jika masalah berlanjut, silakan hubungi tim dukungan kami.";
"actions" = "Tindakan";
"legal" = "Hukum";
"show_look_around" = "Tampilkan Look Around";
"hide_look_around" = "Sembunyikan Look Around";
"map" = "Peta";
"map_type" = "Jenis Peta";
"standard" = "Standar";
"satellite" = "Satelit";
"hybrid" = "Hybrid";
"show_traffic" = "Tampilkan Lalu Lintas";
"current_location" = "Lokasi Saat Ini";
"directions" = "Petunjuk Arah";
"distance_to" = "Jarak";
"eta" = "Perkiraan Waktu Tiba";
"look_around" = "Look Around";
"locating_to_glen_waverley" = "Mencari lokasi ke Glen Waverley";
"network_error" = "Error Jaringan";
"location_error" = "Error Lokasi";
"permission_denied" = "Izin Ditolak";
"location_permission_required" = "Diperlukan Izin Lokasi";
"camera_permission_required" = "Diperlukan Izin Kamera";
"photo_library_permission_required" = "Diperlukan Izin Galeri Foto";
"please_try_again" = "Silakan Coba Lagi";
"something_went_wrong" = "Terjadi Kesalahan";
"invalid_input" = "Input Tidak Valid";
"required_field" = "Field Wajib";
"no_internet_connection" = "Tidak Ada Koneksi Internet";
"server_error" = "Error Server";
"timeout_error" = "Timeout Permintaan";
"data_not_found" = "Data Tidak Ditemukan";
"selection_limit_reached" = "Batas Seleksi Tercapai";
"selection_limit_description" = "Anda dapat memilih maksimal %d alamat, Anda telah memilih %d";
"location_status_valid" = "Rentang Valid";
"address_validation_unknown" = "Tidak divalidasi";
"address_validation_valid" = "Valid";
"address_validation_invalid" = "Tidak valid";
"address_validation_warning" = "Peringatan";
"address_validation_mismatch" = "Tidak cocok";
"device_not_support_scanning" = "Perangkat tidak mendukung pemindaian asli";
"requires_ios16_a12_chip" = "Memerlukan iOS 16+ dan chip A12 atau lebih baru";
"debug_info" = "Info debug:";
"address_confirmation" = "Konfirmasi alamat";
"continue_scanning" = "Lanjutkan pemindaian";
"confirm_add" = "Konfirmasi tambah";
"cannot_get_coordinates_scan_retry" = "Tidak dapat memperoleh koordinat alamat, silakan masukkan secara manual atau pindai ulang";
"unknown_country" = "Negara tidak dikenal";
"unknown_city" = "Kota tidak dikenal";
"please_enter_valid_address" = "Silakan masukkan setidaknya satu alamat yang valid";
"please_select_valid_address" = "Silakan pilih alamat yang valid";
"add_address_failed" = "Gagal menambahkan alamat";
"location_permission_required_for_current_location" = "Izin lokasi diperlukan untuk mendapatkan lokasi saat ini";
"cannot_get_current_location_check_settings" = "Tidak dapat memperoleh lokasi saat ini, silakan periksa pengaturan";
"cannot_get_current_location_address" = "Tidak dapat memperoleh alamat lokasi saat ini";
"get_current_location_failed" = "Gagal mendapatkan lokasi saat ini";
"location_status_warning" = "Rentang Peringatan";
"location_status_invalid" = "Lokasi Tidak Valid";
"location_status_unknown" = "Status Tidak Diketahui";
"coordinates_origin_point" = "Tidak Valid: koordinat nol (0,0)";
"coordinates_invalid_nan" = "Tidak Valid: koordinat non-numerik";
"coordinates_out_of_range" = "Tidak Valid: koordinat di luar rentang valid";
"coordinates_far_from_user" = "Peringatan: lokasi jauh dari posisi Anda saat ini";
"coordinates_ocean" = "Peringatan: lokasi mungkin di laut atau area tak berpenghuni";
"free_address_limit" = "Batas Alamat Versi Gratis";
"address_count_limit" = "Batas Jumlah Alamat";
"selected_addresses_can_import" = "Anda memilih %d alamat, alamat ini dapat diimpor.";
"selected_addresses_exceeds" = "Anda memilih %d alamat, melebihi jumlah yang dapat ditambahkan sebanyak %d.";
"import_success_with_warnings" = "Berhasil mengimpor %d alamat, di mana %d alamat koordinatnya normal, %d alamat memiliki peringatan.\n\nAlamat dengan peringatan telah ditandai, dapat diperbaiki secara manual setelah impor.";
"supported_formats" = "Format yang Didukung";
"supported_format_csv" = "• File CSV: kolom alamat harus berisi alamat lengkap";
"supported_format_json" = "• Data JSON: array yang berisi field alamat";
"supported_format_text" = "• Teks biasa: satu alamat per baris";
"download_history" = "Riwayat Unduhan";
"input_address_data_url" = "Masukkan URL Data Alamat";
"import_result" = "Hasil Impor";
"downloading" = "Mengunduh...";
"processing_data" = "Memproses data...";
"google_drive_download_failed" = "Gagal mengunduh Google Drive";
"second_attempt_invalid_data" = "Percobaan kedua mengunduh mengembalikan data tidak valid";
"cannot_parse_json" = "Tidak dapat mengurai data JSON, silakan periksa format file";
"cannot_parse_json_with_error" = "Tidak dapat mengurai data JSON: %@";
"cannot_read_file" = "Tidak dapat membaca file: %@";
"failed" = "Pengiriman Gagal";
"no_valid_addresses" = "Tidak ditemukan alamat yang valid";
"supports_file_types" = "Mendukung file CSV, TXT dan JSON";
"tap_to_select_file" = "Ketuk untuk memilih file";
"input_company_name" = "Masukkan nama perusahaan (opsional)";
"imported_addresses_count" = "Telah mengimpor %d alamat";
"excel_format_not_supported" = "Format Excel tidak didukung";
"import_failed" = "Impor Gagal";
"free_version_address_limit" = "Versi gratis maksimal mengizinkan %d alamat.";
"current_address_count" = "Saat ini sudah ada %d alamat, hanya bisa menambah %d alamat lagi.";
"upgrade_to_premium_unlimited" = "Upgrade ke versi premium untuk alamat tak terbatas!";
"route_address_limit" = "Rute saat ini sudah memiliki %d alamat, hanya bisa menambah %d alamat lagi, total maksimal %d alamat.";
"free_version_limit" = "Batas Alamat Versi Gratis";
"import_all_n" = "Impor semua %d";
"cannot_import" = "Tidak dapat mengimpor";
"select_at_least_one" = "Silakan pilih setidaknya satu alamat";
"no_valid_addresses_found" = "Tidak ditemukan alamat yang valid";
"import_success_all_valid" = "Berhasil mengimpor %d alamat, semua koordinat alamat normal.";
"import_success_some_warnings" = "Berhasil mengimpor %d alamat, di mana %d alamat koordinatnya normal, %d alamat tidak dapat memperoleh koordinat.";
"company_format" = "Format perusahaan";
"added_from_web_download" = "Ditambahkan dari unduhan web";
"invalid_csv_row" = "Baris CSV tidak valid";
"distance_warning" = "Jarak lebih dari 200 km dari lokasi saat ini";
"not_in_australia" = "Koordinat tidak dalam wilayah Australia";
"invalid_address_data" = "Data alamat tidak valid";
"distance_warning_confirm" = "Jarak dari lokasi saat ini melebihi 200 km, lanjutkan?";
"coordinates_missing" = "Koordinat hilang";
"low_accuracy_address" = "Alamat akurasi rendah";
"address_partial_match" = "Kecocokan alamat sebagian";
"address_outside_region" = "Alamat di luar wilayah";
"api_limit_reached" = "Batas API tercapai";
"address_not_exist_or_incorrect_format" = "Alamat tidak ada atau format salah";
"please_check_address_spelling" = "Silakan periksa ejaan alamat";
"try_smaller_street_number" = "Coba dengan nomor jalan yang lebih kecil";
"use_full_street_type_name" = "Gunakan nama lengkap jenis jalan";
"try_add_more_address_details" = "Coba tambahkan lebih banyak detail alamat";
"cannot_find_address" = "Tidak dapat menemukan alamat";
"please_check_spelling_or_add_details" = "Silakan periksa ejaan atau tambahkan detail";
"cannot_find_address_check_spelling" = "Tidak dapat menemukan alamat, periksa ejaan";
"address_not_set" = "Alamat tidak diatur";
"address_format_incomplete" = "Format alamat tidak lengkap";
"location_service_denied" = "Layanan lokasi ditolak";
"no_saved_groups" = "Tidak ada grup tersimpan";
"select_points_create_groups" = "Pilih titik pengiriman dan buat grup untuk memudahkan pengelolaan";
"navigate_to_these_points" = "Navigasi ke titik-titik ini";
"confirm_remove_address" = "Yakin ingin menghapus alamat \"%@\" dari grup?";
"confirm_remove_this_address" = "Yakin ingin menghapus alamat ini dari grup?";
"addresses_count" = "%d alamat";
"no_saved_routes" = "Tidak ada rute tersimpan";
"no_saved_routes_description" = "Anda belum menyimpan rute apapun";
"all_routes" = "Semua Rute";
"address_count_format_simple" = "%d alamat";
"delete_all_routes" = "Hapus Semua Rute";
"navigate_to_all_points" = "Navigasi ke Semua Titik";
"confirm_navigate_to_route" = "Yakin ingin navigasi ke semua titik dalam rute \"%@\"?";
"temp_navigation_group" = "Grup Navigasi Sementara";
"route_management" = "Manajemen Rute";
"route_addresses" = "Alamat Rute";
"no_addresses_in_route" = "Rute ini tidak memiliki alamat";
"must_keep_one_route" = "Harus menyimpan setidaknya satu rute";
"confirm_delete_route" = "Yakin ingin menghapus rute \"%@\"? Tindakan ini tidak dapat dibatalkan.";
"confirm_delete_all_routes" = "Konfirmasi Hapus Semua Rute";
"confirm_delete_all_routes_message" = "Yakin ingin menghapus semua rute? Tindakan ini tidak dapat dibatalkan.";
"delete_all" = "Hapus Semua";
"address_information" = "Informasi Alamat";
"group_belonging" = "Grup yang Dimiliki";
"view_map" = "Lihat Peta";
"delete_delivery_point" = "Hapus Titik Pengiriman";
"delivery_point_details" = "Detail Titik Pengiriman";
"confirm_deletion" = "Konfirmasi Hapus";
"delete_delivery_point_confirmation" = "Yakin ingin menghapus titik pengiriman ini? Tindakan ini tidak dapat dibatalkan.";
"delivery_photos" = "Foto Pengiriman";
"view_delivery_photos" = "Lihat Foto Pengiriman";
"no_photos_taken" = "Belum mengambil foto";
"take_photos" = "Ambil Foto";
"loading_photos" = "Memuat foto...";
"photo_not_found" = "Foto tidak ditemukan";
"photo_deleted" = "Foto telah dihapus";
"share_photos" = "Bagikan Foto";
"photo_capture_title" = "Konfirmasi Foto";
"door_number_photo_title" = "Foto Nomor Jalan/Rumah";
"package_label_photo_title" = "Foto Label Paket";
"placement_photo_title" = "Foto Lokasi Penempatan";
"door_number_photo_desc" = "Ambil foto nomor rumah yang jelas, pastikan angka/huruf terlihat";
"package_label_photo_desc" = "Ambil foto label paket, pastikan informasi penerima terlihat jelas";
"placement_photo_desc" = "Ambil foto posisi akhir penempatan paket";
"swipe_to_switch" = "Geser untuk beralih jenis foto";
"photos_will_be_saved_to" = "Foto akan disimpan ke";
"complete_photos" = "Selesai Mengambil Foto";
"photo_save_success" = "Foto berhasil disimpan";
"photo_save_failure" = "Gagal menyimpan foto";
"no_photos_found" = "Foto tidak ditemukan";
"photos_deleted_or_not_taken" = "Mungkin foto telah dihapus atau belum diambil";
"share_photo" = "Bagikan Foto";
"photo_capture_preview" = "Mode Pratinjau - Simulasi Kamera";
"photo_capture_close" = "Tutup";
"camera_start_failed" = "Gagal memulai kamera";
"camera_start_failed_retry" = "Tidak dapat memulai kamera, silakan coba lagi";
"camera_init_failed" = "Gagal menginisialisasi kamera";
"camera_access_failed" = "Tidak dapat mengakses kamera";
"photo_processing_failed" = "Gagal mengambil foto";
"photo_processing_failed_retry" = "Tidak dapat menyelesaikan pemrosesan foto, silakan coba lagi";
"photo_capture_progress" = "Progres: %d/%d";
"photo_captured_continue" = "Pengambilan selesai, lanjutkan mengambil %@";
"pending" = "Menunggu Pengiriman";
"in_progress" = "Sedang Dikirim";
"select_delivery_status" = "Pilih Status Pengiriman";
"select_failure_reason" = "Pilih Alasan Kegagalan";
"delivery_status_pending" = "Tertunda";
"delivery_status_in_progress" = "Sedang berlangsung";
"delivery_status_completed" = "Selesai";
"delivery_status_failed" = "Gagal";
"failure_reason_not_at_home" = "Pelanggan tidak di rumah";
"failure_reason_wrong_address" = "Alamat salah";
"failure_reason_no_access" = "Tidak dapat masuk ke lokasi";
"failure_reason_rejected" = "Paket ditolak";
"failure_reason_other" = "Alasan lain";
"enter_custom_reason" = "Masukkan alasan spesifik";
"custom_reason_placeholder" = "Silakan jelaskan alasan spesifik...";
"custom_reason_required" = "Silakan masukkan alasan spesifik";
"failure_reason_required" = "Silakan pilih alasan kegagalan";
"delivery_type_delivery" = "Pengiriman";
"delivery_type_pickup" = "Penjemputan";
"delivery_order_first" = "Pertama";
"delivery_order_auto" = "Otomatis";
"delivery_order_last" = "Terakhir";
"package_size_small" = "Kecil";
"package_size_medium" = "Sedang";
"package_size_large" = "Besar";
"package_type_box" = "Kotak";
"package_type_bag" = "Tas";
"package_type_letter" = "Surat";
"one_click_navigation_grouping" = "Navigasi dan pengelompokan satu klik";
"speed_60x_faster" = "60x lebih cepat";
"goodbye_manual_address_adding" = "Selamat tinggal penambahan alamat manual";
"watch_detailed_demo" = "Tonton demo detail";
"upgrade_to_pro_now" = "Upgrade ke Pro sekarang";
"free_trial_7_days" = "Uji coba gratis 7 hari";
"traditional_vs_navibatch_pro" = "Metode tradisional vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "Geser untuk melihat perbandingan lengkap";
"traditional_method" = "Metode tradisional";
"drivers_get_lost_affect_efficiency" = "Pengemudi tersesat, mempengaruhi efisiensi";
"repetitive_operations_waste_time" = "Operasi berulang membuang waktu";
"total_time_60_seconds" = "Total waktu: 60 detik";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Optimalkan rute, kurangi jarak";
"improve_delivery_efficiency_accuracy" = "Tingkatkan efisiensi dan akurasi pengiriman";
"speed_boost_60x" = "Peningkatan kecepatan 60x";
"total_time_1_second" = "Total waktu: 1 detik";
"time_comparison" = "Perbandingan waktu";
"traditional_method_problems" = "Masalah metode tradisional";
"each_address_3_5_seconds_14_total_60" = "Setiap alamat 3-5 detik, 14 alamat total 60 detik";
"repetitive_operations_cause_fatigue" = "Operasi berulang menyebabkan kelelahan";
"address_order_reversed_last_becomes_first" = "Urutan alamat terbalik, yang terakhir menjadi pertama";
"need_manual_reverse_adding_takes_longer" = "Perlu pembalikan manual, penambahan memakan waktu lebih lama";
"navibatch_advantages" = "Keunggulan NaviBatch";
"add_14_addresses_1_second_60x_faster" = "Tambahkan 14 alamat dalam 1 detik, 60x lebih cepat";
"auto_maintain_correct_order_no_adjustment" = "Otomatis mempertahankan urutan yang benar, tanpa penyesuaian";
"zero_error_rate_no_repetition" = "Tingkat kesalahan nol, tanpa pengulangan";
"save_59_seconds" = "Hemat 59 detik";
"speed_boost_60x_simple" = "Peningkatan kecepatan 60x";
"seconds_format" = "%d detik";
"actual_benefits_one_click_navigation" = "Manfaat nyata navigasi satu klik";
"daily_savings" = "Penghematan harian";
"daily_savings_value" = "59 detik";
"daily_savings_description" = "Hemat 59 detik untuk setiap 14 alamat";
"monthly_savings" = "Penghematan bulanan";
"monthly_savings_value" = "30 menit";
"monthly_savings_description" = "Berdasarkan 30 rute per bulan";
"fuel_savings_value" = "30%";
"fuel_savings_description" = "Optimasi rute mengurangi konsumsi bahan bakar";
"income_increase" = "Peningkatan pendapatan";
"income_increase_value" = "15%";
"income_increase_description" = "Lebih banyak pengiriman per hari = lebih banyak pendapatan";
"trial" = "Percobaan";
"days_left" = "hari tersisa";
"free_plan_description" = "Paket gratis - Hingga 20 alamat";
"pro_plan_active" = "Paket Pro aktif";
"expert_plan_active" = "Paket Ahli aktif";
"trial_active" = "Percobaan aktif";
"trial_expires_on" = "Percobaan berakhir pada %@";
"address_validation_mode" = "Mode validasi alamat";
"validation_description" = "Mengontrol tingkat keketatan validasi alamat";
"current_settings" = "Pengaturan saat ini";
"validation_mode_format" = "Mode: %@";
"threshold_score_format" = "Ambang: %.1f";
"validation_example" = "Contoh validasi";
"original_address_example" = "Alamat asli: 123 Main St";
"reverse_address_example" = "Alamat terbalik: 125 Main St";
"house_number_difference" = "Perbedaan nomor rumah: 2";
"result_label" = "Hasil:";
"may_pass_warning" = "Mungkin lolos (peringatan)";
"will_not_pass" = "Tidak akan lolos";
"real_case_example" = "Contoh kasus nyata";
"real_case_description" = "Berdasarkan data validasi alamat nyata";
"address_validation_settings" = "Pengaturan validasi alamat";
"clear" = "Hapus";
"view_details" = "Lihat detail";
"create_test_data" = "Buat data uji";
"manual_snapshot" = "Snapshot manual";
"start_location_updates" = "Mulai pembaruan lokasi";
"stop_location_updates" = "Hentikan pembaruan lokasi";
"user_location_marker_test" = "Uji penanda lokasi pengguna";
"location_animation_control" = "Kontrol animasi lokasi";
"current_location_format" = "Lokasi saat ini: %.6f, %.6f";
"waiting_for_location" = "Menunggu lokasi...";
"diagnostic_tools" = "Alat diagnostik";
"storekit_diagnostics" = "Diagnostik StoreKit";
"subscription_function_test" = "Uji fungsi langganan";
"localization_test" = "Uji lokalisasi";
"address_validation_demo" = "Demo validasi alamat";
"localization_tools" = "Alat lokalisasi";
"coordinate_debug_tools" = "Alat debug koordinat";
"smart_abbreviation_expansion_test" = "Uji ekspansi singkatan cerdas";
"subscription_restore_diagnostics" = "Diagnostik pemulihan langganan";
"batch_address_import_test" = "Uji impor alamat massal";
"test_import_1000_addresses_memory" = "Uji: impor 1000 alamat (memori)";
"map_rendering_test" = "Uji rendering peta";
"test_map_display_markers_memory" = "Uji: tampilkan penanda di peta (memori)";
"select_test_language" = "Pilih bahasa uji";
"discover_60x_speed_boost" = "Temukan peningkatan kecepatan 60x";
"see_60x_speed_demo" = "Lihat demo kecepatan 60x";
"free_vs_pro_comparison" = "Perbandingan gratis vs Pro";
"our_free_beats_competitors_paid" = "Paket gratis kami mengalahkan paket berbayar pesaing";
"features" = "Fitur";
"up_to_20" = "Hingga 20";
"unlimited" = "Tidak terbatas";
"smart_optimization" = "Optimasi cerdas";
"up_to_20_percent" = "Hingga 20%";
"file_not_found" = "File tidak ditemukan";
"sample_file_not_available" = "File contoh tidak tersedia";
"file_copy_failed" = "Gagal menyalin file";
