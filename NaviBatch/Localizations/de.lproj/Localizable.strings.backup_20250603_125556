/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Spracheinstellungen";
"system_language" = "Systemsprache";
"system_language_section" = "Systemeinstellungen";
"languages" = "Sprachen";
"language_info_title" = "Über Spracheinstellungen";
"language_info_description" = "Nach Änderung der Spracheinstellung wird die App Text in der ausgewählten Sprache anzeigen. Einige Inhalte erfordern möglicherweise einen Neustart der App, um die neue Spracheinstellung vollständig zu übernehmen.";
"restart_required" = "Neustart erforderlich";
"restart_app_message" = "Um die Sprachänderungen vollständig zu übernehmen, starten Sie bitte die App neu.";
"restart_now" = "Jetzt neu starten";
"restart_later" = "Später neu starten";

// MARK: - Common UI Elements
"close" = "Schließen";
"cancel" = "Abbrechen";
"save" = "Speichern";
"edit" = "Bearbeiten";
"delete" = "Löschen";
"done" = "Fertig";
"next" = "Weiter";
"back" = "Zurück";
"confirm" = "Bestätigen";
"error" = "Fehler";
"success" = "Erfolg";
"warning" = "Warnung";
"loading" = "Wird geladen...";
"search" = "Suchen";
"settings" = "Einstellungen";
"help" = "Hilfe";
"about" = "Über";
"menu" = "Menü";
"understand" = "Ich verstehe";

// MARK: - Navigation
"navigation" = "Navigation";
"start_navigation" = "Navigation starten";

// MARK: - Subscription
"subscription" = "Abonnement";
"upgrade_to_pro" = "Auf Pro upgraden";
"upgrade_description" = "Ein-Klick-Navigationsgruppierung, 60x schneller als manuelle Bedienung";
"restore_purchases" = "Käufe wiederherstellen";
"learn_more" = "Mehr erfahren";
"upgrade_your_plan" = "Upgrade Ihres Plans";
"one_click_navigation_description" = "Ein-Klick-Navigationsgruppierung, spart Zeit und Kraftstoff";
"current_plan" = "Aktueller Plan";
"upgrade" = "Upgraden";
"maybe_later" = "Vielleicht später";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Kostenlos";
"pro_tier_price" = "$29.99/Monat";
"expert_tier_price" = "$249.99/Jahr";
"free_tier_description" = "Perfekt für Einzelpersonen und kleine Unternehmen mit wenigen Stopps";
"pro_tier_description" = "Für Benutzer, die Ein-Klick-Navigationsgruppierung und unbegrenzte Adressen benötigen";
"expert_tier_description" = "Wie Pro, aber sparen Sie 31% mit dem Jahresplan";
"route_optimization" = "Routenoptimierung";
"unlimited_routes" = "Unbegrenzte Routen";
"unlimited_optimizations" = "Unbegrenzte Optimierungen";
"max_15_addresses" = "Max. 15 Adressen pro Route";
"save_fuel_30" = "Bis zu 30% Kraftstoff sparen";
"unlimited_addresses" = "✨ Unbegrenzte Adressen ✨";
"one_click_navigation" = "⚡ Ein-Klick-Navigationsgruppierung - 60x schneller ⚡";
"package_finder" = "Paket-Finder";
"annual_savings" = "Jahresplan-Ersparnis";
"switched_to_free" = "Zum kostenlosen Plan gewechselt";
"switched_to_subscription" = "Zum Abonnementplan gewechselt";
"unlimited_stops" = "Unbegrenzte Haltestellen";
"plan_as_many_stops_as_needed" = "Fügen Sie beliebig viele Lieferpunkte ohne Einschränkungen hinzu";

// MARK: - Address Input
"enter_or_search_address" = "Adresse eingeben oder suchen";
"search_results_count" = "Suchergebnisse: %d";
"no_matching_addresses" = "Keine passenden Adressen gefunden";
"search_address_failed" = "Adressensuche fehlgeschlagen: %@";
"address_search_no_response" = "Keine Antwort bei der Adressensuche";
"cannot_get_address_coordinates" = "Kann Adresskoordinaten nicht abrufen";
"speech_recognizer_unavailable" = "Spracherkennung nicht verfügbar";
"microphone_permission_denied" = "Mikrofonberechtigung verweigert";
"speech_recognition_permission_denied" = "Spracherkennungsberechtigung verweigert";
"listening" = "Höre zu...";
"recording_failed" = "Aufnahme fehlgeschlagen: %@";
"cannot_get_coordinates_retry" = "Kann Adresskoordinaten nicht abrufen, bitte manuell eingeben oder erneut versuchen";
"cannot_create_recognition_request" = "Kann Erkennungsanfrage nicht erstellen";

// MARK: - Saved Address Picker
"search_address" = "Adresse suchen";
"no_saved_addresses" = "Keine gespeicherten Adressen";
"no_saved_addresses_description" = "Sie haben noch keine Adressen gespeichert, oder es gibt keine Adressen, die Ihren Filterkriterien entsprechen";
"select_address_book" = "Adressbuch auswählen";

// MARK: - Menu
"menu" = "Menü";
"routes" = "Routen";
"address_book" = "Adressbuch";
"saved_routes" = "Gespeicherte Routen";
"manage_your_routes" = "Verwalten Sie Ihre Routenplanung";
"manage_your_addresses" = "Verwalten Sie Ihre häufig verwendeten Adressen";
"settings" = "Einstellungen";
"preferences" = "Präferenzen";
"set_custom_start_point" = "Benutzerdefinierten Startpunkt festlegen";
"current_start_point" = "Aktueller Startpunkt: %@";
"support" = "Support";
"contact_us" = "Kontakt";
"contact_us_description" = "Haben Sie Fragen oder Vorschläge? Kontaktieren Sie uns!";
"help_center" = "Hilfecenter";
"subscription" = "Abonnement";
"upgrade_to_pro" = "Auf Pro upgraden";
"upgrade_description" = "Ein-Klick-Navigationsgruppierung, 60x schneller als manuelle Bedienung";
"open_subscription_view" = "Abonnement-Ansicht öffnen";
"open_subscription_view_description" = "Zwischenschicht überspringen, direkt SubscriptionView anzeigen";
"restore_purchases_failed" = "Wiederherstellung der Käufe fehlgeschlagen: %@";
"about" = "Über";
"rate_us" = "Bewerten Sie uns";
"rate_us_description" = "Ihr Feedback ist wichtig für uns und hilft uns, die App zu verbessern!";
"share_app" = "App teilen";
"share_app_text" = "Probieren Sie NaviBatch, eine tolle Routenplanungs-App!";
"about_app" = "Über die App";
"developer_tools" = "Entwicklertools";
"coordinate_debug_tool" = "Koordinaten-Debug-Tool";
"batch_fix_addresses" = "Adressen stapelweise korrigieren";
"clear_database" = "Datenbank löschen";
"clear_database_confirmation" = "Dadurch werden alle Daten gelöscht, einschließlich Routen, Adressen und Gruppen. Diese Aktion kann nicht rückgängig gemacht werden. Sind Sie sicher, dass Sie fortfahren möchten?";
"confirm_clear" = "Löschen bestätigen";
"version_info" = "Version %@ (%@)";
"current_system_language" = "Aktuelle Systemsprache";
"reset_to_system_language" = "Auf Systemsprache zurücksetzen";
"language" = "Sprache";
"language_settings" = "Spracheinstellungen";

// MARK: - Address Edit
"address" = "Adresse";
"coordinates" = "Koordinaten";
"distance_from_current_location" = "Entfernung vom aktuellen Standort";
"address_info" = "Adressinformationen";
"update_coordinates" = "Koordinaten aktualisieren";
"fix_address" = "Adresse korrigieren";
"prompt" = "Aufforderung";
"confirm" = "Bestätigen";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Bitte ändern Sie die Adresse, bevor Sie die Koordinaten aktualisieren";
"coordinates_update_success" = "Koordinaten erfolgreich aktualisiert";
"coordinates_update_failure" = "Aktualisierung der Koordinaten fehlgeschlagen";
"save_failure" = "Speichern fehlgeschlagen: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Keine gespeicherten Adressen";
"no_saved_addresses_message" = "Sie haben noch keine Adressen gespeichert";
"add_new_address" = "Neue Adresse hinzufügen";
"address_title" = "Adresse";
"add" = "Hinzufügen";
"refresh" = "Aktualisieren";
"notes" = "Notizen";
"address_details" = "Adressdetails";
"favorite" = "Favorit";
"edit_address" = "Adresse bearbeiten";
"cancel" = "Abbrechen";
"save" = "Speichern";
"confirm_delete" = "Löschen bestätigen";
"delete" = "Löschen";
"delete_address_confirmation" = "Sind Sie sicher, dass Sie diese Adresse löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"edit" = "Bearbeiten";
"address_marker" = "Adresse";
"address_label" = "Adresse:";
"notes_label" = "Notizen:";
"created_at_label" = "Erstellt am:";
"open_in_maps" = "In Karten öffnen";
"copy_address" = "Adresse kopieren";
"address_details_title" = "Adressdetails";

// MARK: - Route Detail
"start_end_point" = "Start/Endpunkt";
"start_point" = "Startpunkt";
"end_point" = "Endpunkt";
"route_info" = "Routeninformationen";
"address_count" = "Anzahl der Adressen";
"address_count_format" = "%d Adressen";
"points_count_format" = "%d Punkte";
"additional_points_format" = "+%d Punkte";
"export_route" = "Route exportieren";
"navigate" = "Navigieren";
"address_list" = "Adressliste";
"no_addresses" = "Keine Adressen";
"no_addresses_message" = "Diese Route hat noch keine Adressen";

// MARK: - Route Bottom Sheet
"address_point_start" = "Startpunkt";
"address_point_stop" = "Haltepunkt";
"address_point_end" = "Endpunkt";
"route_name" = "Routenname";
"save" = "Speichern";
"new_route" = "Neue Route";
"saved_route" = "Gespeicherte Route";
"edit" = "Bearbeiten";
"loading" = "Wird geladen...";
"plan_route" = "Route planen";
"clear_all" = "Alles löschen";
"avoid" = "Vermeiden:";
"toll_roads" = "Mautstraßen";
"highways" = "Autobahnen";
"processing_addresses" = "Adressen werden verarbeitet...";
"same_start_end_point" = "Sie haben dieselbe Adresse sowohl als Start- als auch als Endpunkt festgelegt";
"add_start_point" = "Startpunkt hinzufügen";
"swipe_left_to_delete" = "← Nach links wischen zum Löschen";
"delete" = "Löschen";
"add_new_address" = "Neue Adresse hinzufügen";
"add_end_point" = "Endpunkt hinzufügen";

// MARK: - Simple Address Sheet
"address_title" = "Adresse";
"enter_and_select_address" = "Adresse eingeben und auswählen";
"current_search_text" = "Aktueller Suchtext: %@";
"search_results_count" = "Suchergebnisse: %d";
"no_matching_addresses" = "Keine passenden Adressen gefunden";
"add_address" = "Adresse hinzufügen";
"edit_address" = "Adresse bearbeiten";
"selected_coordinates" = "Ausgewählte Koordinaten";
"company_name_optional" = "Firmenname (Optional)";
"url_optional" = "URL (Optional)";
"favorite_address" = "Favoritadresse";
"set_as_start_and_end" = "Als Start- und Endpunkt festlegen";
"address_book" = "Adressbuch";
"batch_paste" = "Stapeleinfügung";
"file_import" = "Datei importieren";
"web_download" = "Web-Download";
"cancel" = "Abbrechen";
"saving" = "Wird gespeichert...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Lieferpunktverwaltung";
"information_category" = "Informationskategorie";
"address_info" = "Adressinformationen";
"package_info" = "Paketinformationen";
"vehicle_position" = "Fahrzeugposition";
"delivery_info" = "Lieferinformationen";
"navigation" = "Navigation";
"take_photo_record" = "Foto aufnehmen";
"update_status" = "Status aktualisieren";
"done" = "Fertig";
"edit_address_button" = "Adresse bearbeiten";
"coordinates" = "Koordinaten";
"access_instructions" = "Zugangsanweisungen";
"add_access_instructions" = "Zugangsanweisungen hinzufügen...";
"package_count" = "Paketanzahl";
"package_size" = "Paketgröße";
"package_type" = "Pakettyp";
"mark_as_important" = "Als wichtig markieren";
"select_package_position" = "Paketposition im Fahrzeug auswählen";
"vehicle_area" = "Fahrzeugbereich";
"left_right_position" = "Links/Rechts Position";
"vehicle_position_front" = "Vorne";
"vehicle_position_middle" = "Mitte";
"vehicle_position_back" = "Hinten";
"vehicle_position_left" = "Links";
"vehicle_position_right" = "Rechts";
"vehicle_position_floor" = "Unten";
"vehicle_position_shelf" = "Oben";
"height_position" = "Höhenposition";
"delivery_type" = "Liefertyp";
"delivery_status" = "Lieferstatus";
"order_info" = "Bestellinformationen";
"order_information" = "Bestellinformationen";
"order_number" = "Bestellnummer";
"enter_order_number" = "Bestellnummer eingeben";
"tracking_number" = "Sendungsnummer";
"enter_tracking_number" = "Sendungsnummer eingeben";
"time_info" = "Zeitinformationen";
"time_information" = "Zeitinformationen";
"estimated_arrival_time" = "Voraussichtliche Ankunftszeit";
"anytime" = "Jederzeit";
"stop_time" = "Stoppzeit";
"minutes_format" = "%d Minuten";
"photo_record" = "Fotodokumentation";
"door_number_photo" = "Foto der Hausnummer";
"package_label_photo" = "Foto des Paketetiketts";
"placement_photo" = "Ablagefoto";
"door_number_desc" = "Bitte machen Sie ein klares Foto der Hausnummer oder Straßennummer, stellen Sie sicher, dass Zahlen/Buchstaben sichtbar sind";
"package_label_desc" = "Bitte fotografieren Sie das Paketetikett, stellen Sie sicher, dass die Empfängerinformationen klar sichtbar sind";
"placement_desc" = "Bitte fotografieren Sie die endgültige Platzierung des Pakets";
"photo_captured" = "Foto aufgenommen";
"photo_captured_options" = "Foto aufgenommen, möchten Sie mit dem nächsten Foto fortfahren oder das aktuelle Foto abschließen?";
"continue_to_next_photo" = "Weiter zum nächsten Foto - %@";
"retake" = "Erneut aufnehmen";
"tap_to_capture" = "Zum Aufnehmen tippen";
"flash_auto" = "Automatischer Blitz";
"flash_on" = "Blitz ein";
"flash_off" = "Blitz aus";
"photo_record_completed" = "Fotodokumentation abgeschlossen";
"photo_confirmation" = "Fotobestätigung";
"error" = "Fehler";
"ok" = "OK";
"complete_photo_capture" = "Fotoaufnahme abschließen";
"tap_to_capture" = "Zum Aufnehmen tippen";
"photo_instructions" = "Tippen Sie auf jede Fotokarte zum Aufnehmen. Alle Fotos müssen abgeschlossen werden.";
"photo_options" = "Fotooptionen";
"view_photo" = "Foto anzeigen";
"retake_photo" = "Erneut aufnehmen";
"saving_photos" = "Fotos werden gespeichert...";
"completed" = "Abgeschlossen";
"not_taken" = "Nicht aufgenommen";
"route_options" = "Routenoptionen";
"avoid_tolls" = "Mautstraßen vermeiden";
"avoid_highways" = "Autobahnen vermeiden";
"optimize_route" = "Route optimieren";
"optimizing" = "Optimiere...";
"optimization_complete" = "Optimierung abgeschlossen";
"route_optimization_results" = "Routenoptimierungsergebnisse";
"route_planning_options" = "Routenplanungsoptionen";
"before_optimization" = "Vor Optimierung";
"after_optimization" = "Nach Optimierung";
"auto_group" = "Auto-Gruppierung";
"optimized_route_order" = "Optimierte Routenreihenfolge";
"apply" = "Anwenden";
"kilometers" = "Kilometer";

// MARK: - Addresses
"addresses" = "Adressen";
"add_address" = "Adresse hinzufügen";
"edit_address" = "Adresse bearbeiten";
"delete_address" = "Adresse löschen";
"address_details" = "Adressdetails";
"street" = "Straße";
"city" = "Stadt";
"state" = "Bundesland";
"country" = "Land";
"postal_code" = "Postleitzahl";
"phone" = "Telefon";
"email" = "E-Mail";
"website" = "Webseite";
"company" = "Firma";
"notes" = "Notizen";
"coordinates" = "Koordinaten";
"latitude" = "Breitengrad";
"longitude" = "Längengrad";
"geocoding_error" = "Geokodierungsfehler";
"address_validation" = "Adressvalidierung";
"invalid_addresses" = "Ungültige Adressen";
"fix_addresses" = "Adressen korrigieren";

// MARK: - Routes
"route" = "Route";
"routes" = "Routen";
"select_address_point" = "Adresspunkt auswählen";
"select_delivery_points" = "Lieferpunkte auswählen";
"create_delivery_route" = "Lieferroute erstellen";
"view_saved_routes" = "Gespeicherte Routen anzeigen";
"create_route" = "Route erstellen";
"edit_route" = "Route bearbeiten";
"delete_route" = "Route löschen";
"route_name" = "Routenname";
"route_details" = "Routendetails";
"selected_addresses" = "%d Adressen ausgewählt";
"reached_limit" = "Limit erreicht";
"can_select_more" = "Noch %d auswählbar";
"navigate_button" = "Navigation";
"create_group" = "Gruppe erstellen";
"start_point" = "Startpunkt";
"end_point" = "Endpunkt";
"waypoints" = "Wegpunkte";
"total_distance" = "Gesamtentfernung";
"estimated_time" = "Geschätzte Zeit";
"route_summary" = "Routenzusammenfassung";
"route_options" = "Routenoptionen";
"route_saved" = "Route gespeichert";
"route_optimized" = "Route optimiert";
"optimizing_route" = "Route wird optimiert...";
"completed_percent" = "%d%% abgeschlossen";
"processing_points" = "Verarbeitung: %d/%d";
"estimated_remaining_time" = "Geschätzte verbleibende Zeit: %@";

// MARK: - Delivery
"delivery" = "Lieferung";
"delivery_confirmation" = "Lieferbestätigung";
"take_photo" = "Foto aufnehmen";
"signature" = "Unterschrift";
"delivery_notes" = "Lieferhinweise";
"delivery_status" = "Lieferstatus";
"delivered" = "Zugestellt";
"not_delivered" = "Nicht zugestellt";
"delivery_time" = "Lieferzeit";
"delivery_date" = "Lieferdatum";
"package_details" = "Paketdetails";
"package_id" = "Paket-ID";
"package_weight" = "Paketgewicht";
"package_dimensions" = "Paketabmessungen";
"recipient_name" = "Name des Empfängers";
"recipient_phone" = "Telefon des Empfängers";

// MARK: - Groups
"groups" = "Gruppen";
"saved_groups" = "Gespeicherte Gruppen";
"create_group" = "Gruppe erstellen";
"edit_group" = "Gruppe bearbeiten";
"delete_group" = "Gruppe löschen";
"group_name" = "Gruppenname";
"group_details" = "Gruppendetails";
"auto_grouping" = "Automatische Gruppierung";
"group_by" = "Gruppieren nach";
"add_to_group" = "Zur Gruppe hinzufügen";
"remove_from_group" = "Aus Gruppe entfernen";
"group_created" = "Gruppe erstellt";
"default_group_name_format" = "Gruppe %d";
"auto_grouping_completed" = "Automatische Gruppierung abgeschlossen";
"auto_grouping_in_progress" = "Automatische Gruppierung läuft...";
"create_group_every_14_addresses" = "Für jeweils 14 Adressen eine Gruppe erstellen";
"create_delivery_group" = "Liefergruppe erstellen";
"enter_group_name" = "Gruppennamen eingeben";
"selected_delivery_points" = "Ausgewählte Lieferpunkte";
"drag_to_adjust_order" = "Ziehen zum Anpassen der Reihenfolge";

// MARK: - Subscription
"subscription" = "Abonnement";
"free_plan" = "Kostenlose Version";
"pro_plan" = "Pro-Version";
"expert_plan" = "Experten-Version";
"monthly" = "Monatlich";
"yearly" = "Jährlich";
"subscribe" = "Abonnieren";
"upgrade" = "Upgraden";
"upgrade_to_pro" = "Auf Pro upgraden";
"manage_subscription" = "Abonnement verwalten";
"restore_purchases" = "Käufe wiederherstellen";
"subscription_benefits" = "Abonnementvorteile";
"free_trial" = "Kostenlose Testversion";
"price_per_month" = "%@ pro Monat";
"price_per_year" = "%@ pro Jahr";
"save_percent" = "%@% sparen";
"current_plan" = "Aktueller Plan";
"subscription_terms" = "Abonnementbedingungen";
"privacy_policy" = "Datenschutzrichtlinie";
"terms_of_service" = "Nutzungsbedingungen";

// MARK: - Import/Export
"import" = "Importieren";
"export" = "Exportieren";
"import_addresses" = "Adressen importieren";
"export_addresses" = "Adressen exportieren";
"import_from_file" = "Aus Datei importieren";
"export_to_file" = "In Datei exportieren";
"file_format" = "Dateiformat";
"csv_format" = "CSV-Format";
"excel_format" = "Excel-Format";
"json_format" = "JSON-Format";
"import_success" = "Erfolgreich %d Adressen importiert, alle mit gültigen Koordinaten.";
"export_success" = "Export erfolgreich";
"import_error" = "Importfehler";
"export_error" = "Exportfehler";

// MARK: - Navigation
"navigate" = "Navigieren";

// MARK: - Look Around
"show_look_around" = "Look Around anzeigen";
"hide_look_around" = "Look Around ausblenden";

// MARK: - Map
"map" = "Karte";
"map_type" = "Kartentyp";
"standard" = "Standard";
"satellite" = "Satellit";
"hybrid" = "Hybrid";
"show_traffic" = "Verkehr anzeigen";
"current_location" = "Aktueller Standort";
"directions" = "Wegbeschreibung";
"distance_to" = "Entfernung zu";
"eta" = "Voraussichtliche Ankunftszeit";
"look_around" = "Umschauen";
"locating_to_glen_waverley" = "Standortbestimmung nach Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Netzwerkfehler";
"location_error" = "Standortfehler";
"permission_denied" = "Berechtigung verweigert";
"location_permission_required" = "Standortberechtigung erforderlich";
"camera_permission_required" = "Kamerazugriff erforderlich";
"photo_library_permission_required" = "Fotobibliotheksberechtigung erforderlich";
"please_try_again" = "Bitte versuchen Sie es erneut";
"something_went_wrong" = "Etwas ist schief gelaufen";
"invalid_input" = "Ungültige Eingabe";
"required_field" = "Pflichtfeld";
"no_internet_connection" = "Keine Internetverbindung";
"server_error" = "Serverfehler";
"timeout_error" = "Zeitüberschreitung";
"data_not_found" = "Daten nicht gefunden";
"selection_limit_reached" = "Auswahlgrenze erreicht";
"selection_limit_description" = "Sie können maximal %d Adressen auswählen, Sie haben %d ausgewählt";

// MARK: - Location Validation Status
"location_status_valid" = "Gültiger Bereich";
"location_status_warning" = "Warnbereich";
"location_status_invalid" = "Ungültige Position";
"location_status_unknown" = "Unbekannter Status";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Ungültig: Null-Koordinaten (0,0)";
"coordinates_invalid_nan" = "Ungültig: Nicht-numerische Koordinaten";
"coordinates_out_of_range" = "Ungültig: Koordinaten außerhalb des gültigen Bereichs";
"coordinates_far_from_user" = "Warnung: Position ist weit von Ihrem aktuellen Standort entfernt";
"coordinates_ocean" = "Warnung: Position könnte im Ozean oder unbewohnten Gebiet liegen";

// MARK: - Batch Address Input
"batch_add_addresses" = "Adressen stapelweise hinzufügen";
"batch_address_input_placeholder" = "Bitte geben Sie Adressen ein oder fügen Sie sie ein, eine Adresse pro Zeile. Maximal 35 Adressen.";
"free_address_limit" = "Adresslimit der kostenlosen Version";
"address_count_limit" = "Adressenanzahl-Limit";
"free_version_max_addresses" = "Die kostenlose Version erlaubt maximal %d Adressen.";
"current_addresses_remaining" = "Sie haben derzeit %d Adressen, können nur noch %d Adressen hinzufügen.";
"current_route_address_limit" = "Die aktuelle Route hat %d Adressen, Sie können nur noch %d Adressen hinzufügen, insgesamt maximal %d Adressen.";
"selected_addresses_can_import" = "Sie haben %d Adressen ausgewählt, diese können importiert werden.";
"selected_addresses_exceeds" = "Sie haben %d Adressen ausgewählt, %d über dem hinzufügbaren Limit.";
"selected_addresses_all_importable" = "Sie haben %d Adressen ausgewählt, alle können importiert werden.";
"upgrade_for_unlimited_addresses" = "Upgrade zur Premium-Version für unbegrenzte Adressen!";
"import_first_n_addresses" = "Nur die ersten %d importieren";
"import_all_addresses" = "Alle Adressen importieren";
"import_selected_addresses" = "Ausgewählte Adressen importieren";
"no_importable_addresses" = "Keine importierbaren Adressen, bitte überprüfen Sie das Adresslimit";
"please_enter_valid_address" = "Bitte geben Sie mindestens eine gültige Adresse ein";

// MARK: - File Import
"import_success" = "Erfolgreich %d Adressen importiert, alle mit gültigen Koordinaten.";
"import_success_with_warnings" = "Erfolgreich %d Adressen importiert, davon %d Adressen mit normalen Koordinaten, %d Adressen mit Warnungen.\n\nAdressen mit Warnungen sind markiert und können nach dem Import manuell behoben werden.";

// MARK: - Web Download
"web_download" = "Web-Download";
"supported_formats" = "Unterstützte Formate";
"supported_format_csv" = "• CSV-Dateien: Adressspalte sollte vollständige Adressen enthalten";
"supported_format_json" = "• JSON-Daten: Array mit Adressfeldern";
"supported_format_text" = "• Klartext: Eine Adresse pro Zeile";
"download_history" = "Download-Verlauf";
"upgrade_to_premium" = "Auf Premium upgraden";
"input_address_data_url" = "Adressdaten-URL eingeben";
"import_result" = "Importergebnis";
"import_addresses" = "Adressen importieren";
"downloading" = "Wird heruntergeladen...";
"processing_data" = "Daten werden verarbeitet...";
"google_drive_download_failed" = "Google Drive Download fehlgeschlagen";
"second_attempt_invalid_data" = "Zweiter Download-Versuch gab ungültige Daten zurück";
"cannot_parse_json" = "JSON-Daten können nicht geparst werden, bitte überprüfen Sie das Dateiformat";
"cannot_parse_json_with_error" = "JSON-Daten können nicht geparst werden: %@";
"cannot_get_address_coordinates" = "Kann Adresskoordinaten nicht abrufen";
"cannot_read_file" = "Datei kann nicht gelesen werden: %@";
"success" = "Erfolg";
"warning" = "Warnung";
"failed" = "Lieferung fehlgeschlagen";
"no_matching_addresses" = "Keine passenden Adressen gefunden";
"no_valid_addresses" = "Keine gültigen Adressen gefunden";
"confirm" = "Bestätigen";
"processing_addresses" = "Adressen werden verarbeitet...";
"supports_file_types" = "Unterstützt CSV-, TXT- und JSON-Dateien";
"tap_to_select_file" = "Zum Auswählen der Datei tippen";
"import_addresses" = "Adressen importieren";
"company_name_optional" = "Firmenname (Optional)";
"input_company_name" = "Firmennamen eingeben (optional)";
"imported_addresses_count" = "%d Adressen importiert";
"select_all" = "Alle auswählen";
"excel_format_not_supported" = "Excel-Format nicht unterstützt";
"no_matching_addresses" = "Keine passenden Adressen gefunden";

// MARK: - Import Limits
"import_failed" = "Import fehlgeschlagen";
"no_importable_addresses" = "Keine importierbaren Adressen, bitte überprüfen Sie das Adresslimit";
"free_version_address_limit" = "Die kostenlose Version erlaubt maximal %d Adressen.";
"current_address_count" = "Sie haben derzeit %d Adressen, können nur noch %d Adressen hinzufügen.";
"can_import_selected" = "Sie haben %d Adressen ausgewählt, diese können importiert werden.";
"selected_exceeds_limit" = "Sie haben %d Adressen ausgewählt, %d über dem hinzufügbaren Limit.";
"upgrade_to_premium_unlimited" = "Upgrade zur Premium-Version für unbegrenzte Adressen!";
"route_address_limit" = "Die aktuelle Route hat %d Adressen, Sie können nur noch %d Adressen hinzufügen, insgesamt maximal %d Adressen.";
"free_version_limit" = "Adresslimit der kostenlosen Version";
"address_count_limit" = "Adressenanzahl-Limit";
"import_selected_addresses" = "Ausgewählte Adressen importieren";
"import_first_n" = "Nur die ersten %d importieren";
"import_all_n" = "Alle %d importieren";
"cannot_import" = "Import nicht möglich";
"select_at_least_one" = "Bitte wählen Sie mindestens eine Adresse aus";

// MARK: - Import Results
"no_valid_addresses_found" = "Keine gültigen Adressen gefunden";
"import_success_all_valid" = "Erfolgreich %d Adressen importiert, alle Adresskoordinaten sind normal.";
"import_success_some_warnings" = "Erfolgreich %d Adressen importiert, davon %d Adressen mit normalen Koordinaten, %d Adressen ohne verfügbare Koordinaten.";

// MARK: - Warnings
"invalid_csv_row" = "Ungültige CSV-Zeile";
"distance_warning" = "Mehr als 200 km vom aktuellen Standort entfernt";
"not_in_australia" = "Koordinaten nicht im australischen Bereich";
"cannot_get_coordinates" = "Adresskoordinaten können nicht abgerufen werden";
"empty_address" = "Leere Adresse";
"invalid_address_data" = "Ungültige Adressdaten";

// MARK: - Saved Groups
"saved_groups" = "Gespeicherte Gruppen";
"no_saved_groups" = "Keine gespeicherten Gruppen";
"select_points_create_groups" = "Lieferpunkte auswählen und erstellen Sie Gruppen für einfache Verwaltung";
"group_name" = "Gruppenname";
"group_details" = "Gruppendetails";
"navigate_to_these_points" = "Navigation zu diesen Punkten";
"confirm_remove_address" = "Sind Sie sicher, dass Sie die Adresse \"%@\" aus der Gruppe entfernen möchten?";
"confirm_remove_this_address" = "Sind Sie sicher, dass Sie diese Adresse aus der Gruppe entfernen möchten?";
"addresses_count" = "%d Adressen";
"no_saved_routes" = "Keine gespeicherten Routen";
"no_saved_routes_description" = "Sie haben noch keine Routen gespeichert";
"all_routes" = "Alle Routen";
"address_count_format_simple" = "%d Adressen";
"delete_all_routes" = "Alle Routen löschen";
"navigate_to_all_points" = "Zu allen Punkten navigieren";
"confirm_navigate_to_route" = "Sind Sie sicher, dass Sie zu allen Punkten in der Route \"%@\" navigieren möchten?";
"temp_navigation_group" = "Temporäre Navigationsgruppe";

// MARK: - Route Management
"route_management" = "Routenverwaltung";
"route_info" = "Routeninformationen";
"route_name" = "Routenname";
"route_addresses" = "Routenadressen";
"no_addresses_in_route" = "Diese Route hat keine Adressen";
"must_keep_one_route" = "Mindestens eine Route muss beibehalten werden";
"confirm_delete_route" = "Sind Sie sicher, dass Sie die Route \"%@\" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"confirm_delete_all_routes" = "Löschen aller Routen bestätigen";
"confirm_delete_all_routes_message" = "Sind Sie sicher, dass Sie alle Routen löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"delete_all" = "Alle löschen";

// MARK: - Navigation Buttons
"navigate" = "Navigieren";

// MARK: - GroupDetailView
"points_count_format" = "%d Punkte";

// MARK: - DeliveryPointDetailView
"address_information" = "Adressinformationen";
"group_belonging" = "Zugehörige Gruppe";
"view_map" = "Karte anzeigen";
"delivery_status" = "Lieferstatus";
"notes" = "Notizen";
"delete_delivery_point" = "Lieferpunkt löschen";
"delivery_point_details" = "Lieferpunkt-Details";
"confirm_deletion" = "Löschen bestätigen";
"delete_delivery_point_confirmation" = "Sind Sie sicher, dass Sie diesen Lieferpunkt löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";

// MARK: - Delivery Photos
"delivery_photos" = "Lieferfotos";
"view_delivery_photos" = "Lieferfotos anzeigen";
"no_photos_taken" = "Noch keine Fotos aufgenommen";
"take_photos" = "Fotos aufnehmen";
"loading_photos" = "Fotos werden geladen...";
"photo_not_found" = "Foto nicht gefunden";
"photo_deleted" = "Foto wurde gelöscht";
"door_number_photo" = "Foto der Hausnummer";
"package_label_photo" = "Foto des Paketetiketts";
"placement_photo" = "Ablagefoto";
"share_photos" = "Foto teilen";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Fotobestätigung";
"door_number_photo_title" = "Straßennummer/Hausnummer-Foto";
"package_label_photo_title" = "Paketetikett-Foto";
"placement_photo_title" = "Platzierungsort-Foto";
"door_number_photo_desc" = "Bitte machen Sie ein klares Foto der Hausnummer, stellen Sie sicher, dass Zahlen/Buchstaben sichtbar sind";
"package_label_photo_desc" = "Bitte fotografieren Sie das Paketetikett, stellen Sie sicher, dass die Empfängerinformationen klar sichtbar sind";
"placement_photo_desc" = "Bitte fotografieren Sie die endgültige Platzierung des Pakets";
"swipe_to_switch" = "Wischen zum Wechseln des Fototyps";
"complete_photos" = "Fotoaufnahme abschließen";
"saving_photos" = "Fotos werden gespeichert...";
"photo_save_success" = "Foto erfolgreich gespeichert";
"photo_save_failure" = "Foto speichern fehlgeschlagen";
"retake_photo" = "Erneut aufnehmen";
"no_photos_found" = "Keine Fotos gefunden";
"photos_deleted_or_not_taken" = "möglicherweise Foto wurde gelöscht oder noch nicht aufgenommen";
"share_photo" = "Foto teilen";
"photo_capture_preview" = "Vorschaumodus - Kamera-Simulation";
"photo_capture_close" = "Schließen";
"camera_start_failed" = "Kamera-Start fehlgeschlagen";
"camera_start_failed_retry" = "Kamera kann nicht gestartet werden, bitte erneut versuchen";
"camera_init_failed" = "Kamera-Initialisierung fehlgeschlagen";
"camera_access_failed" = "Kamera kann nicht zugegriffen werden";
"photo_processing_failed" = "Foto aufnehmen fehlgeschlagen";
"photo_processing_failed_retry" = "Fotobearbeitung kann nicht abgeschlossen werden, bitte erneut versuchen";
"photo_capture_progress" = "Fortschritt: %d/%d";
"photo_captured_continue" = "Aufnahme abgeschlossen, weiter mit %@";
"loading_photos" = "Fotos werden geladen...";
"cancel" = "Abbrechen";

// MARK: - Delivery Status
"pending" = "Ausstehende Lieferung";
"in_progress" = "In Lieferung";
"completed" = "Abgeschlossen";
"failed" = "Lieferung fehlgeschlagen";
"update_status" = "Status aktualisieren";
"select_delivery_status" = "Lieferstatus auswählen";
"select_failure_reason" = "Grund für Fehlschlag auswählen";
"delivered" = "Zugestellt";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Kunde nicht zu Hause";
"failure_reason_wrong_address" = "Falsche Adresse";
"failure_reason_no_access" = "Standort nicht zugänglich";
"failure_reason_rejected" = "Paket wurde abgelehnt";
"failure_reason_other" = "Andere Gründe";
"enter_custom_reason" = "Spezifischen Grund eingeben";
"custom_reason_placeholder" = "Bitte beschreiben Sie den spezifischen Grund...";
"custom_reason_required" = "Bitte geben Sie den spezifischen Grund ein";
"failure_reason_required" = "Bitte wählen Sie den Grund für den Fehlschlag";

// MARK: - Address Validation
"address_validation_failed" = "Adressvalidierung fehlgeschlagen";


"0" = "14天免費試用，隨時可取消";
