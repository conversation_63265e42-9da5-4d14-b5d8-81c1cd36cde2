/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Spracheinstellungen";
"system_language" = "Systemsprache";
"system_language_section" = "Systemeinstellungen";
"languages" = "Sprachen";
"language_info_title" = "Über Spracheinstellungen";
"language_info_description" = "Nach Änderung der Spracheinstellung wird die App Text in der ausgewählten Sprache anzeigen. Einige Inhalte erfordern möglicherweise einen Neustart der App, um die neue Spracheinstellung vollständig zu übernehmen.";
"restart_required" = "Neustart erforderlich";
"restart_app_message" = "Um die Sprachänderungen vollständig zu übernehmen, starten Sie bitte die App neu.";
"restart_now" = "Jetzt neu starten";
"restart_later" = "Später neu starten";
"close" = "Schließen";
"cancel" = "Abbrechen";
"save" = "Speichern";
"edit" = "Bearbeiten";
"delete" = "Löschen";
"done" = "Fertig";
"next" = "Weiter";
"back" = "Zurück";
"confirm" = "Bestätigen";
"error" = "Fehler";
"success" = "Erfolg";
"warning" = "Warnung";
"unknown_error" = "Unbekannter Fehler";
"loading" = "Wird geladen...";
"search" = "Suchen";
"settings" = "Einstellungen";
"help" = "Hilfe";
"about" = "Über";
"menu" = "Menü";
"understand" = "Ich verstehe";
"navigation" = "Navigation";
"start_navigation" = "Navigation starten";
"subscription" = "Abonnement";
"upgrade_to_pro" = "Auf Pro upgraden";
"upgrade_description" = "Ein-Klick-Navigationsgruppierung, 60x schneller als manuelle Bedienung";
"restore_purchases" = "Käufe wiederherstellen";
"learn_more" = "Mehr erfahren";
"upgrade_your_plan" = "Upgrade Ihres Plans";
"one_click_navigation_description" = "Ein-Klick-Navigationsgruppierung, spart Zeit und Kraftstoff";
"current_plan" = "Aktueller Plan";
"upgrade" = "Upgraden";
"maybe_later" = "Vielleicht später";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Kostenlos";
"pro_tier_price" = "$9.99/Monat";
"expert_tier_price" = "$59.99/Jahr";
"free_tier_description" = "Perfekt für Einzelpersonen und kleine Unternehmen mit wenigen Stopps";
"pro_tier_description" = "Für Benutzer, die Ein-Klick-Navigationsgruppierung und unbegrenzte Adressen benötigen";
"expert_tier_description" = "Wie Pro, aber sparen Sie 50% mit dem Jahresplan";
"route_optimization" = "Routenoptimierung";
"unlimited_routes" = "Unbegrenzte Routen";
"unlimited_optimizations" = "Unbegrenzte Optimierungen";
"max_15_addresses" = "Max. 15 Adressen pro Route";
"save_fuel_30" = "Bis zu 30% Kraftstoff sparen";
"unlimited_addresses" = "✨ Unbegrenzte Adressen ✨";
"one_click_navigation" = "⚡ Ein-Klick-Navigationsgruppierung - 60x schneller ⚡";
"package_finder" = "Paket-Finder";
"annual_savings" = "Jahresplan-Ersparnis";
"switched_to_free" = "Zum kostenlosen Plan gewechselt";
"switched_to_subscription" = "Zum Abonnementplan gewechselt";
"unlimited_stops" = "Unbegrenzte Haltestellen";
"plan_as_many_stops_as_needed" = "Fügen Sie beliebig viele Lieferpunkte ohne Einschränkungen hinzu";
"expires_in_days" = "Läuft in  Tagen ab";
"trial_expires_in_days" = "Testversion läuft in  Tagen ab";
"expired" = "Abgelaufen";
"subscription_expires_on" = "Abonnement läuft am %@ ab";
"subscription_active_until" = "Abonnement aktiv bis %@";
"enter_or_search_address" = "Adresse eingeben oder suchen";
"search_results_count" = "Suchergebnisse: %d";
"no_matching_addresses" = "Keine passenden Adressen gefunden";
"search_address_failed" = "Adressensuche fehlgeschlagen: %@";
"address_search_no_response" = "Keine Antwort bei der Adressensuche";
"cannot_get_address_coordinates" = "Kann Adresskoordinaten nicht abrufen";

"cannot_get_coordinates_retry" = "Kann Adresskoordinaten nicht abrufen, bitte manuell eingeben oder erneut versuchen";

"image_address_recognition" = "Bilderkennung von Adressen";
"select_images" = "Bilder auswählen";
"select_multiple_images" = "Mehrere Bilder auswählen";
"processing_images" = "Bilder verarbeiten";
"processing_image_progress" = "Bildverarbeitungsfortschritt";
"recognizing_text" = "Text erkennen";
"geocoding_addresses" = "Adressen geocodieren";
"recognition_complete" = "Erkennung abgeschlossen";
"no_text_recognized" = "Kein Text erkannt";
"no_addresses_found" = "Keine Adressen gefunden";
"image_recognition_failed" = "Bilderkennung fehlgeschlagen";
"image_recognition_error" = "Bilderkennungsfehler";
"text_recognition_failed" = "Texterkennung fehlgeschlagen";
"address_parsing_failed" = "Adressanalyse fehlgeschlagen";
"select_addresses_to_add" = "Adressen zum Hinzufügen auswählen";
"recognized_addresses" = "Erkannte Adressen";
"address_coordinates" = "Adresskoordinaten";
"toggle_address_selection" = "Adressauswahl umschalten";
"remove_address" = "Adresse entfernen";
"confirm_selected_addresses" = "Ausgewählte Adressen bestätigen";
"no_addresses_selected" = "Keine Adressen ausgewählt";
"image_processing_cancelled" = "Bildverarbeitung abgebrochen";
"unsupported_image_format" = "Nicht unterstütztes Bildformat";
"image_too_large" = "Bild zu groß";
"image_recognition_permission_required" = "Bilderkennungsberechtigung erforderlich";
"ocr_language_detection" = "OCR-Spracherkennung";
"improve_image_quality" = "Bildqualität verbessern";
"address_validation_in_progress" = "Adressvalidierung läuft";
"batch_address_import" = "Batch-Adressimport";
"validated_addresses_count" = "Anzahl validierter Adressen";
"addresses_with_issues" = "Adressen mit Problemen";
"select_all" = "Alle auswählen";
"import_selected" = "Ausgewählte importieren";
"validating_addresses" = "Adressen validieren";
"empty_address" = "Leere Adresse";
"invalid_coordinates" = "Ungültige Koordinaten";
"coordinate_warning" = "Koordinatenwarnung";
"address_validation_issue" = "Adressvalidierungsproblem";
"cannot_get_coordinates" = "Adresskoordinaten können nicht abgerufen werden";
"no_importable_addresses" = "Keine importierbaren Adressen, bitte überprüfen Sie das Adresslimit";
"free_version_max_addresses" = "Die kostenlose Version erlaubt maximal %d Adressen.";
"valid" = "Gültig";
"with_issues" = "Mit Problemen";
"low_confidence_address" = "Adresse mit geringer Vertrauenswürdigkeit";
"address_validation_failed" = "Adressvalidierung fehlgeschlagen";
"current_addresses_remaining" = "Sie haben derzeit %d Adressen, können nur noch %d Adressen hinzufügen.";
"can_import_selected" = "Sie haben %d Adressen ausgewählt, diese können importiert werden.";
"selected_exceeds_limit" = "Sie haben %d Adressen ausgewählt, %d über dem hinzufügbaren Limit.";
"selected_addresses_all_importable" = "Sie haben %d Adressen ausgewählt, alle können importiert werden.";
"upgrade_for_unlimited_addresses" = "Upgrade zur Premium-Version für unbegrenzte Adressen!";
"current_route_address_limit" = "Die aktuelle Route hat %d Adressen, Sie können nur noch %d Adressen hinzufügen, insgesamt maximal %d Adressen.";
"import_all_addresses" = "Alle Adressen importieren";
"import_first_n" = "Nur die ersten %d importieren";
"import_selected_addresses" = "Ausgewählte Adressen importieren";
"upgrade_to_premium" = "Auf Premium upgraden";
"batch_add_addresses" = "Adressen stapelweise hinzufügen";
"batch_address_input_placeholder" = "Bitte geben Sie Adressen ein oder fügen Sie sie ein, eine Adresse pro Zeile. Maximal 35 Adressen.";
"search_address" = "Adresse suchen";
"no_saved_addresses" = "Keine gespeicherten Adressen";
"no_saved_addresses_description" = "Sie haben noch keine Adressen gespeichert, oder es gibt keine Adressen, die Ihren Filterkriterien entsprechen";
"select_address_book" = "Adressbuch auswählen";
"routes" = "Routen";
"address_book" = "Adressbuch";
"saved_routes" = "Gespeicherte Routen";
"manage_your_routes" = "Verwalten Sie Ihre Routenplanung";
"manage_your_addresses" = "Verwalten Sie Ihre häufig verwendeten Adressen";
"preferences" = "Präferenzen";
"set_custom_start_point" = "Benutzerdefinierten Startpunkt festlegen";
"current_start_point" = "Aktueller Startpunkt: %@";
"support" = "Support";
"contact_us" = "Kontakt";
"contact_us_description" = "Haben Sie Fragen oder Vorschläge? Kontaktieren Sie uns!";
"help_center" = "Hilfecenter";
"quick_actions" = "Schnellaktionen";
"main_features" = "Hauptfunktionen";
"support_help" = "Support & Hilfe";
"customize_app_settings" = "App-Einstellungen anpassen";
"unlock_all_features" = "Alle Funktionen freischalten";
"get_help_support" = "Hilfe und Support erhalten";
"app_info_version" = "App-Informationen und Version";
"dev_tools" = "Entwicklertools";
"debug_testing_tools" = "Debug- und Testtools";
"version" = "Version";
"addresses" = "Adressen";
"limited_to_20_addresses" = "Begrenzt auf 20 Adressen";
"all_premium_features" = "Alle Premium-Funktionen";
"open_subscription_view" = "Abonnement-Ansicht öffnen";
"open_subscription_view_description" = "Zwischenschicht überspringen, direkt SubscriptionView anzeigen";
"restore_purchases_failed" = "Wiederherstellung der Käufe fehlgeschlagen: %@";
"rate_us" = "Bewerten Sie uns";
"rate_us_description" = "Ihr Feedback ist wichtig für uns und hilft uns, die App zu verbessern!";
"share_app" = "App teilen";
"share_app_text" = "Probieren Sie NaviBatch, eine tolle Routenplanungs-App!";
"about_app" = "Über die App";
"developer_tools" = "Entwicklertools";
"coordinate_debug_tool" = "Koordinaten-Debug-Tool";
"batch_fix_addresses" = "Adressen stapelweise korrigieren";
"clear_database" = "Datenbank löschen";
"clear_database_confirmation" = "Dadurch werden alle Daten gelöscht, einschließlich Routen, Adressen und Gruppen. Diese Aktion kann nicht rückgängig gemacht werden. Sind Sie sicher, dass Sie fortfahren möchten?";
"confirm_clear" = "Bestätigen";
"version_info" = "Version %@ (%@)";
"current_system_language" = "Aktuelle Systemsprache";
"reset_to_system_language" = "Auf Systemsprache zurücksetzen";
"language" = "Sprache";
"address" = "Adresse";
"coordinates" = "Koordinaten";
"distance_from_current_location" = "Entfernung vom aktuellen Standort";
"address_info" = "Adressinformationen";
"update_coordinates" = "Koordinaten aktualisieren";
"fix_address" = "Adresse korrigieren";
"prompt" = "Aufforderung";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Bitte ändern Sie die Adresse, bevor Sie die Koordinaten aktualisieren";
"coordinates_update_success" = "Koordinaten erfolgreich aktualisiert";
"coordinates_update_failure" = "Aktualisierung der Koordinaten fehlgeschlagen";
"save_failure" = "Speichern fehlgeschlagen: %@";
"no_saved_addresses_title" = "Keine gespeicherten Adressen";
"no_saved_addresses_message" = "Sie haben noch keine Adressen gespeichert";
"add_new_address" = "Neue Adresse hinzufügen";
"address_title" = "Adresse";
"add" = "Hinzufügen";
"refresh" = "Aktualisieren";
"notes" = "Notizen";
"address_details" = "Adressdetails";
"favorite" = "Favorit";
"edit_address" = "Adresse bearbeiten";
"confirm_delete" = "Löschen bestätigen";
"delete_address_confirmation" = "Sind Sie sicher, dass Sie diese Adresse löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"address_marker" = "Adresse";
"address_label" = "Adresse:";
"notes_label" = "Notizen:";
"created_at_label" = "Erstellt am:";
"open_in_maps" = "In Karten öffnen";
"copy_address" = "Adresse kopieren";
"address_details_title" = "Adressdetails";
"start_end_point" = "Start/Endpunkt";
"start_point" = "Startpunkt";
"end_point" = "Endpunkt";
"route_info" = "Routeninformationen";
"address_count" = "Anzahl der Adressen";
"address_count_format" = "%d Adressen";
"points_count_format" = "%d Punkte";
"additional_points_format" = "+%d Punkte";
"export_route" = "Route exportieren";
"navigate" = "Navigieren";
"address_list" = "Adressliste";
"no_addresses" = "Keine Adressen";
"no_addresses_message" = "Diese Route hat noch keine Adressen";
"address_point_start" = "Startpunkt";
"address_point_stop" = "Haltepunkt";
"address_point_end" = "Endpunkt";
"route_name" = "Routenname";
"new_route" = "Neue Route";
"saved_route" = "Gespeicherte Route";
"plan_route" = "Route planen";
"clear_all" = "Alles löschen";
"restore" = "Reihenfolge wiederherstellen";
"avoid" = "Vermeiden:";
"toll_roads" = "Mautstraßen";
"highways" = "Autobahnen";
"processing_addresses" = "Adressen werden verarbeitet...";
"same_start_end_point" = "Sie haben dieselbe Adresse sowohl als Start- als auch als Endpunkt festgelegt";
"add_start_point" = "Startpunkt hinzufügen";
"swipe_left_to_delete" = "Lange drücken zum Löschen";
"add_end_point" = "Endpunkt hinzufügen";
"enter_and_select_address" = "Adresse eingeben und auswählen";
"current_search_text" = "Aktueller Suchtext: %@";
"add_address" = "Adresse hinzufügen";
"selected_coordinates" = "Ausgewählte Koordinaten";
"company_name_optional" = "Firmenname (Optional)";
"url_optional" = "URL (Optional)";
"favorite_address" = "Favoritadresse";
"set_as_start_and_end" = "Als Start- und Endpunkt festlegen";
"batch_paste" = "Stapeleinfügung";
"file_import" = "Datei importieren";
"web_download" = "Web-Download";
"saving" = "Wird gespeichert...";
"delivery_point_management" = "Lieferpunktverwaltung";
"information_category" = "Informationskategorie";
"package_info" = "Paketinformationen";
"vehicle_position" = "Fahrzeugposition";
"delivery_info" = "Lieferinformationen";
"take_photo_record" = "Foto aufnehmen";
"update_status" = "Status aktualisieren";
"edit_address_button" = "Adresse bearbeiten";
"access_instructions" = "Zugangsanweisungen";
"add_access_instructions" = "Zugangsanweisungen hinzufügen...";
"package_count" = "Paketanzahl";
"packages" = "Pakete";
"package_unit" = "Paketeinheit";
"package_size" = "Paketgröße";
"package_type" = "Pakettyp";
"mark_as_important" = "Als wichtig markieren";
"priority_delivery" = "Prioritätslieferung";
"priority_level" = "Prioritätsstufe";
"priority_1" = "Hohe Priorität";
"priority_2" = "Mittlere Priorität";
"priority_3" = "Niedrige Priorität";
"no_priority" = "Keine Priorität";
"no_priority_short" = "Normal";
"set_priority" = "Priorität festlegen";
"select_package_position" = "Paketposition im Fahrzeug auswählen";
"vehicle_area" = "Fahrzeugbereich";
"left_right_position" = "Links/Rechts Position";
"vehicle_position_front" = "Vorne";
"vehicle_position_middle" = "Mitte";
"vehicle_position_back" = "Hinten";
"vehicle_position_left" = "Links";
"vehicle_position_right" = "Rechts";
"vehicle_position_floor" = "Unten";
"vehicle_position_shelf" = "Oben";
"height_position" = "Höhenposition";
"vehicle_position_none" = "Nicht angegeben";
"delivery_type" = "Liefertyp";
"delivery_status" = "Lieferstatus";
"order_info" = "Bestellinformationen";
"order_information" = "Bestellinformationen";
"order_number" = "Bestellnummer";
"enter_order_number" = "Bestellnummer eingeben";
"tracking_number" = "Sendungsnummer";
"enter_tracking_number" = "Sendungsnummer eingeben";
"tracking_info" = "Sendungsverfolgung";
"tracking_information" = "Sendungsverfolgungsinformationen";
"time_info" = "Zeitinformationen";
"time_information" = "Zeitinformationen";
"estimated_arrival_time" = "Voraussichtliche Ankunftszeit";
"anytime" = "Jederzeit";
"stop_time" = "Stoppzeit";
"minutes_format" = "%d Minuten";
"photo_record" = "Fotodokumentation";
"door_number_photo" = "Foto der Hausnummer";
"package_label_photo" = "Foto des Paketetiketts";
"placement_photo" = "Ablagefoto";
"door_number_desc" = "Bitte machen Sie ein klares Foto der Hausnummer oder Straßennummer, stellen Sie sicher, dass Zahlen/Buchstaben sichtbar sind";
"package_label_desc" = "Bitte fotografieren Sie das Paketetikett, stellen Sie sicher, dass die Empfängerinformationen klar sichtbar sind";
"placement_desc" = "Bitte fotografieren Sie die endgültige Platzierung des Pakets";
"photo_captured" = "Foto aufgenommen";
"photo_captured_options" = "Foto aufgenommen, möchten Sie mit dem nächsten Foto fortfahren oder das aktuelle Foto abschließen?";
"continue_to_next_photo" = "Weiter zum nächsten Foto - %@";
"retake" = "Erneut aufnehmen";
"tap_to_capture" = "Zum Aufnehmen tippen";
"flash_auto" = "Automatischer Blitz";
"flash_on" = "Blitz ein";
"flash_off" = "Blitz aus";
"photo_record_completed" = "Fotodokumentation abgeschlossen";
"photo_confirmation" = "Fotobestätigung";
"ok" = "OK";
"complete_photo_capture" = "Fotoaufnahme abschließen";
"photo_instructions" = "Tippen Sie auf jede Fotokarte zum Aufnehmen. Alle Fotos müssen abgeschlossen werden.";
"photo_options" = "Fotooptionen";
"view_photo" = "Foto anzeigen";
"retake_photo" = "Erneut aufnehmen";
"saving_photos" = "Fotos werden gespeichert...";
"completed" = "Abgeschlossen";
"not_taken" = "Nicht aufgenommen";
"route_options" = "Routenoptionen";
"avoid_tolls" = "Mautstraßen vermeiden";
"avoid_highways" = "Autobahnen vermeiden";
"optimize_route" = "Route optimieren";
"optimizing" = "Optimiere...";
"optimization_complete" = "Optimierung abgeschlossen";
"route_optimization_results" = "Ergebnisse";
"route_planning_options" = "Routenplanungsoptionen";
"before_optimization" = "Vor Optimierung";
"after_optimization" = "Nach Optimierung";
"auto_group" = "Auto-Gruppierung";
"optimized_route_order" = "Reihenfolge";
"apply" = "Anwenden";
"kilometers" = "Kilometer";
"street_number_issue_warning" = "Hausnummer-Problem-Warnung";
"address_validation_critical" = "Adressvalidierung kritisch";
"street_number_difference_high_risk" = "Hausnummer-Unterschied hohes Risiko";
"delete_address" = "Adresse löschen";
"street" = "Straße";
"city" = "Stadt";
"state" = "Bundesland";
"country" = "Land";
"postal_code" = "Postleitzahl";
"phone" = "Telefon";
"email" = "E-Mail";
"website" = "Webseite";
"company" = "Firma";
"latitude" = "Breitengrad";
"longitude" = "Längengrad";
"geocoding_error" = "Geokodierungsfehler";
"address_validation" = "Adressvalidierung";
"invalid_addresses" = "Ungültige Adressen";
"fix_addresses" = "Adressen korrigieren";
"route" = "Route";
"select_address_point" = "Adresspunkt auswählen";
"select_delivery_points" = "Lieferpunkte auswählen";
"create_delivery_route" = "Lieferroute erstellen";
"view_saved_routes" = "Gespeicherte Routen anzeigen";
"create_route" = "Route erstellen";
"edit_route" = "Route bearbeiten";
"delete_route" = "Route löschen";
"route_details" = "Routendetails";
"selected_addresses" = "%d Adressen ausgewählt";
"reached_limit" = "Limit erreicht";
"can_select_more" = "Noch %d auswählbar";
"navigate_button" = "Navigation";
"create_group" = "Gruppe erstellen";
"waypoints" = "Wegpunkte";
"total_distance" = "Gesamtentfernung";
"estimated_time" = "Geschätzte Zeit";
"route_summary" = "Routenzusammenfassung";
"route_saved" = "Route gespeichert";
"route_optimized" = "Route optimiert";
"optimizing_route" = "Route wird optimiert...";
"completed_percent" = "%d%% abgeschlossen";
"processing_points" = "Verarbeitung: %d/%d";
"estimated_remaining_time" = "Geschätzte verbleibende Zeit: %@";
"delivery" = "Lieferung";
"delivery_confirmation" = "Lieferbestätigung";
"take_photo" = "Foto aufnehmen";
"signature" = "Unterschrift";
"delivery_notes" = "Lieferhinweise";
"delivered" = "Zugestellt";
"not_delivered" = "Nicht zugestellt";
"delivery_time" = "Lieferzeit";
"delivery_date" = "Lieferdatum";
"package_details" = "Paketdetails";
"package_id" = "Paket-ID";
"package_weight" = "Paketgewicht";
"package_dimensions" = "Paketabmessungen";
"recipient_name" = "Name des Empfängers";
"recipient_phone" = "Telefon des Empfängers";
"groups" = "Gruppen";
"saved_groups" = "Gespeicherte Gruppen";
"edit_group" = "Gruppe bearbeiten";
"delete_group" = "Gruppe löschen";
"group_name" = "Gruppenname";
"group_details" = "Gruppendetails";
"auto_grouping" = "Automatische Gruppierung";
"group_by" = "Gruppieren nach";
"add_to_group" = "Zur Gruppe hinzufügen";
"remove_from_group" = "Aus Gruppe entfernen";
"group_created" = "Gruppe erstellt";
"default_group_name_format" = "Gruppe %d";
"auto_grouping_completed" = "Automatische Gruppierung abgeschlossen";
"auto_grouping_in_progress" = "Automatische Gruppierung läuft...";
"create_group_every_14_addresses" = "Für jeweils 14 Adressen eine Gruppe erstellen";
"create_delivery_group" = "Liefergruppe erstellen";
"enter_group_name" = "Gruppennamen eingeben";
"selected_delivery_points" = "Ausgewählte Lieferpunkte";
"drag_to_adjust_order" = "Ziehen zum Anpassen der Reihenfolge";
"free_plan" = "Kostenlose Version";
"pro_plan" = "Pro-Version";
"expert_plan" = "Experten-Version";
"monthly" = "Monatlich";
"yearly" = "Jährlich";
"subscribe" = "Abonnieren";
"manage_subscription" = "Abonnement verwalten";
"subscription_benefits" = "Abonnementvorteile";
"free_trial" = "Kostenlose Testversion";
"price_per_month" = "%@ pro Monat";
"price_per_year" = "%@ pro Jahr";
"save_percent" = "%@% sparen";
"subscription_terms" = "Abonnementbedingungen";
"privacy_policy" = "Datenschutzrichtlinie";
"terms_of_service" = "Nutzungsbedingungen";
"feature_comparison" = "Funktionsvergleich";
"addresses_per_route" = "Adressen pro Route";
"max_20_addresses" = "20 Adressen";
"fuel_savings" = "Kraftstoffeinsparung";
"up_to_30_percent" = "Bis zu 30%";
"choose_subscription_plan" = "Abonnementplan wählen";
"monthly_plan" = "Monatsplan";
"yearly_plan" = "Jahresplan";
"/month_suffix" = "/Monat";
"/year_suffix" = "/Jahr";
"save_30_percent" = "30% sparen";
"free_trial_7_days_cancel_anytime" = "Inklusive 14-tägiger kostenloser Testversion, jederzeit kündbar";
"subscription_auto_renew_notice" = "Das Abonnement verlängert sich automatisch, es sei denn, es wird mindestens 24 Stunden vor Ende der aktuellen Periode gekündigt.";
"and" = "und";
"subscription_exclusive" = "Nur Premium";
"free_version_optimization_limit" = "Optimierungslimit der kostenlosen Version";
"free_version_supports_max_addresses" = "Die kostenlose Version unterstützt bis zu %d Adressen.";
"current_route_contains_addresses" = "Die aktuelle Route enthält %d Adressen und überschreitet das Limit der kostenlosen Version.";
"upgrade_to_pro_unlimited_addresses" = "Upgrade auf Pro für unbegrenzte Adressen und Ein-Klick-Navigationsgruppierung!";
"continue_optimization" = "Optimierung fortsetzen";
"upgrade_unlock_one_click_navigation" = "Upgrade für Ein-Klick-Navigation - 14x schneller";
"learn_one_click_navigation_grouping" = "Mehr über Ein-Klick-Navigationsgruppierung erfahren";
"toggle_subscription_status" = "Abonnementstatus umschalten";
"toggle_subscription_description" = "Zwischen kostenloser und Pro-Version wechseln (nur für Entwicklungstests)";
"product_info_unavailable" = "Produktinformationen nicht verfügbar, bitte später erneut versuchen";
"purchase_failed" = "Kauf fehlgeschlagen: %@";
"upgrade_to_pro_version" = "Auf Pro upgraden";
"unlock_all_premium_features" = "Alle Premium-Funktionen freischalten";
"first_7_days_free_cancel_anytime" = "Erste 14 Tage kostenlos, jederzeit kündbar.";
"payment_terms_notice" = "Die Zahlung wird bei Kaufbestätigung von Ihrem Apple ID-Konto abgebucht. Das Abonnement verlängert sich automatisch, es sei denn, es wird mindestens 24 Stunden vor Ende der aktuellen Periode gekündigt.";
"terms_of_use" = "Nutzungsbedingungen";
"product_load_failed_check_connection" = "Produktinformationen konnten nicht geladen werden. Stellen Sie sicher, dass Ihr Gerät mit dem Internet verbunden und im App Store angemeldet ist";
"product_load_failed" = "Produktladen fehlgeschlagen: %@";
"verify_receipt" = "Beleg überprüfen";
"one_click_navigation_short" = "Ein-Klick-Navigation";
"save_30_percent_fuel" = "30% Kraftstoff sparen";
"monthly_short" = "Monatlich";
"yearly_short" = "Jährlich";
"upgrade_now" = "Jetzt upgraden";
"test_environment_pro_activated" = "Testumgebung: Pro-Version aktiviert";
"payment_terms_notice_detailed" = "Die Zahlung wird bei Kaufbestätigung von Ihrem Apple ID-Konto abgebucht. Das Abonnement verlängert sich automatisch, es sei denn, es wird mindestens 24 Stunden vor Ende der aktuellen Periode gekündigt. Sie können Ihre Abonnements in den App Store-Einstellungen verwalten und kündigen.";
"step_screenshot" = "Schritt %d Screenshot";
"previous_step" = "Zurück";
"next_step" = "Weiter";
"each_address_takes_3_5_seconds" = "Jede Adresse dauert 3-5 Sekunden";
"need_repeat_14_times" = "Muss 14 Mal wiederholt werden";
"navigation_order_often_confused" = "Navigationsreihenfolge oft verwirrend";
"error_prone_need_redo" = "Fehleranfällig, muss wiederholt werden";
"address_order_reversed_manual_adjust" = "Adressreihenfolge umgekehrt, manuelle Anpassung erforderlich";
"one_click_add_all" = "Alle mit einem Klick hinzufügen";
"smart_grouping_auto_sorting" = "Intelligente Gruppierung und automatische Sortierung";
"maintain_correct_visit_order" = "Korrekte Besuchsreihenfolge beibehalten";
"zero_errors_zero_repetition" = "Null Fehler, null Wiederholung";
"import" = "Importieren";
"export" = "Exportieren";
"import_addresses" = "Adressen importieren";
"export_addresses" = "Adressen exportieren";
"import_from_file" = "Aus Datei importieren";
"export_to_file" = "In Datei exportieren";
"file_format" = "Dateiformat";
"csv_format" = "CSV-Format";
"excel_format" = "Excel-Format";
"json_format" = "JSON-Format";
"import_success" = "Erfolgreich %d Adressen importiert, alle mit gültigen Koordinaten.";
"export_success" = "Export erfolgreich";
"import_error" = "Importfehler";
"export_error" = "Exportfehler";
"navigation_app" = "Navigations-App";
"apple_maps" = "Apple Karten";
"app_preferences" = "App-Einstellungen";
"distance_unit" = "Entfernungseinheit";
"current_language" = "Aktuelle Sprache";
"info" = "Informationen";
"contact_us_header" = "Kontaktieren Sie uns";
"contact_us_subheader" = "Wir sind hier, um zu helfen";
"contact_options" = "Kontaktoptionen";
"email_us" = "E-Mail senden";
"contact_form" = "Kontaktformular";
"contact_and_support" = "Kontakt und Support";
"common_questions" = "Häufige Fragen";
"how_to_use" = "Anleitung";
"subscription_faq" = "Abonnement FAQ";
"navigation_help" = "Navigationshilfe";
"troubleshooting" = "Fehlerbehebung";
"help_howto_content" = "Anleitungsinhalt";
"help_subscription_content" = "Abonnement-Hilfeinhalt";
"help_navigation_content" = "Navigations-Hilfeinhalt";
"help_troubleshooting_content" = "Fehlerbehebungs-Hilfeinhalt";
"actions" = "Aktionen";
"legal" = "Rechtliches";
"show_look_around" = "Look Around anzeigen";
"hide_look_around" = "Look Around ausblenden";
"map" = "Karte";
"map_type" = "Kartentyp";
"standard" = "Standard";
"satellite" = "Satellit";
"hybrid" = "Hybrid";
"show_traffic" = "Verkehr anzeigen";
"current_location" = "Aktueller Standort";
"directions" = "Wegbeschreibung";
"distance_to" = "Entfernung zu";
"eta" = "Voraussichtliche Ankunftszeit";
"look_around" = "Umschauen";
"locating_to_glen_waverley" = "Standortbestimmung nach Glen Waverley";
"network_error" = "Netzwerkfehler";
"location_error" = "Standortfehler";
"permission_denied" = "Berechtigung verweigert";
"location_permission_required" = "Standortberechtigung erforderlich";
"camera_permission_required" = "Kamerazugriff erforderlich";
"photo_library_permission_required" = "Fotobibliotheksberechtigung erforderlich";
"please_try_again" = "Bitte versuchen Sie es erneut";
"something_went_wrong" = "Etwas ist schief gelaufen";
"invalid_input" = "Ungültige Eingabe";
"required_field" = "Pflichtfeld";
"no_internet_connection" = "Keine Internetverbindung";
"server_error" = "Serverfehler";
"timeout_error" = "Zeitüberschreitung";
"data_not_found" = "Daten nicht gefunden";
"selection_limit_reached" = "Auswahlgrenze erreicht";
"selection_limit_description" = "Sie können maximal %d Adressen auswählen, Sie haben %d ausgewählt";
"location_status_valid" = "Gültiger Bereich";
"address_validation_unknown" = "Adressvalidierung unbekannt";
"address_validation_valid" = "Adresse gültig";
"address_validation_invalid" = "Adresse ungültig";
"address_validation_warning" = "Adressvalidierungswarnung";
"address_validation_mismatch" = "Adresse stimmt nicht überein";
"device_not_support_scanning" = "Gerät unterstützt kein Scannen";
"requires_ios16_a12_chip" = "Erfordert iOS 16 und A12-Chip oder neuer";
"debug_info" = "Debug-Informationen";
"address_confirmation" = "Adressbestätigung";
"continue_scanning" = "Scannen fortsetzen";
"confirm_add" = "Hinzufügen bestätigen";
"cannot_get_coordinates_scan_retry" = "Koordinaten können nicht abgerufen werden, erneut scannen oder wiederholen";
"unknown_country" = "Unbekanntes Land";
"unknown_city" = "Unbekannte Stadt";
"please_enter_valid_address" = "Bitte geben Sie mindestens eine gültige Adresse ein";
"please_select_valid_address" = "Bitte wählen Sie eine gültige Adresse";
"add_address_failed" = "Adresse hinzufügen fehlgeschlagen";
"location_permission_required_for_current_location" = "Standortberechtigung für aktuellen Standort erforderlich";
"cannot_get_current_location_check_settings" = "Aktueller Standort kann nicht abgerufen werden, Einstellungen prüfen";
"cannot_get_current_location_address" = "Aktuelle Standortadresse kann nicht abgerufen werden";
"get_current_location_failed" = "Aktueller Standort abrufen fehlgeschlagen";
"location_status_warning" = "Warnbereich";
"location_status_invalid" = "Ungültige Position";
"location_status_unknown" = "Unbekannter Status";
"coordinates_origin_point" = "Ungültig: Null-Koordinaten (0,0)";
"coordinates_invalid_nan" = "Ungültig: Nicht-numerische Koordinaten";
"coordinates_out_of_range" = "Ungültig: Koordinaten außerhalb des gültigen Bereichs";
"coordinates_far_from_user" = "Warnung: Position ist weit von Ihrem aktuellen Standort entfernt";
"coordinates_ocean" = "Warnung: Position könnte im Ozean oder unbewohnten Gebiet liegen";
"free_address_limit" = "Adresslimit der kostenlosen Version";
"address_count_limit" = "Adressenanzahl-Limit";
"selected_addresses_can_import" = "Sie haben %d Adressen ausgewählt, diese können importiert werden.";
"selected_addresses_exceeds" = "Sie haben %d Adressen ausgewählt, %d über dem hinzufügbaren Limit.";
"import_success_with_warnings" = "Erfolgreich %d Adressen importiert, davon %d Adressen mit normalen Koordinaten, %d Adressen mit Warnungen.\n\nAdressen mit Warnungen sind markiert und können nach dem Import manuell behoben werden.";
"supported_formats" = "Unterstützte Formate";
"supported_format_csv" = "• CSV-Dateien: Adressspalte sollte vollständige Adressen enthalten";
"supported_format_json" = "• JSON-Daten: Array mit Adressfeldern";
"supported_format_text" = "• Klartext: Eine Adresse pro Zeile";
"download_history" = "Download-Verlauf";
"input_address_data_url" = "Adressdaten-URL eingeben";
"import_result" = "Importergebnis";
"downloading" = "Wird heruntergeladen...";
"processing_data" = "Daten werden verarbeitet...";
"google_drive_download_failed" = "Google Drive Download fehlgeschlagen";
"second_attempt_invalid_data" = "Zweiter Download-Versuch gab ungültige Daten zurück";
"cannot_parse_json" = "JSON-Daten können nicht geparst werden, bitte überprüfen Sie das Dateiformat";
"cannot_parse_json_with_error" = "JSON-Daten können nicht geparst werden: %@";
"cannot_read_file" = "Datei kann nicht gelesen werden: %@";
"failed" = "Lieferung fehlgeschlagen";
"no_valid_addresses" = "Keine gültigen Adressen gefunden";
"supports_file_types" = "Unterstützt CSV-, TXT- und JSON-Dateien";
"tap_to_select_file" = "Zum Auswählen der Datei tippen";
"input_company_name" = "Firmennamen eingeben (optional)";
"imported_addresses_count" = "%d Adressen importiert";
"excel_format_not_supported" = "Excel-Format nicht unterstützt";
"import_failed" = "Import fehlgeschlagen";
"free_version_address_limit" = "Die kostenlose Version erlaubt maximal %d Adressen.";
"current_address_count" = "Sie haben derzeit %d Adressen, können nur noch %d Adressen hinzufügen.";
"upgrade_to_premium_unlimited" = "Upgrade zur Premium-Version für unbegrenzte Adressen!";
"route_address_limit" = "Die aktuelle Route hat %d Adressen, Sie können nur noch %d Adressen hinzufügen, insgesamt maximal %d Adressen.";
"free_version_limit" = "Adresslimit der kostenlosen Version";
"import_all_n" = "Alle %d importieren";
"cannot_import" = "Import nicht möglich";
"select_at_least_one" = "Bitte wählen Sie mindestens eine Adresse aus";
"no_valid_addresses_found" = "Keine gültigen Adressen gefunden";
"import_success_all_valid" = "Erfolgreich %d Adressen importiert, alle Adresskoordinaten sind normal.";
"import_success_some_warnings" = "Erfolgreich %d Adressen importiert, davon %d Adressen mit normalen Koordinaten, %d Adressen ohne verfügbare Koordinaten.";
"company_format" = "Firmenformat";
"added_from_web_download" = "Aus Web-Download hinzugefügt";
"invalid_csv_row" = "Ungültige CSV-Zeile";
"distance_warning" = "Mehr als 200 km vom aktuellen Standort entfernt";
"not_in_australia" = "Koordinaten nicht im australischen Bereich";
"invalid_address_data" = "Ungültige Adressdaten";
"distance_warning_confirm" = "Entfernungswarnung bestätigen";
"coordinates_missing" = "Koordinaten fehlen";
"low_accuracy_address" = "Adresse mit geringer Genauigkeit";
"address_partial_match" = "Adresse teilweise übereinstimmend";
"address_outside_region" = "Adresse außerhalb der Region";
"api_limit_reached" = "API-Limit erreicht";
"address_not_exist_or_incorrect_format" = "Adresse existiert nicht oder falsches Format";
"please_check_address_spelling" = "Bitte Adressschreibweise prüfen";
"try_smaller_street_number" = "Kleinere Hausnummer versuchen";
"use_full_street_type_name" = "Vollständigen Straßentyp-Namen verwenden";
"try_add_more_address_details" = "Mehr Adressdetails hinzufügen";
"cannot_find_address" = "Adresse kann nicht gefunden werden";
"please_check_spelling_or_add_details" = "Bitte Schreibweise prüfen oder Details hinzufügen";
"cannot_find_address_check_spelling" = "Adresse nicht gefunden, Schreibweise prüfen";
"address_not_set" = "Adresse nicht gesetzt";
"address_format_incomplete" = "Adressformat unvollständig";
"location_service_denied" = "Standortdienst verweigert";
"no_saved_groups" = "Keine gespeicherten Gruppen";
"select_points_create_groups" = "Lieferpunkte auswählen und erstellen Sie Gruppen für einfache Verwaltung";
"navigate_to_these_points" = "Navigation zu diesen Punkten";
"confirm_remove_address" = "Sind Sie sicher, dass Sie die Adresse \"%@\" aus der Gruppe entfernen möchten?";
"confirm_remove_this_address" = "Sind Sie sicher, dass Sie diese Adresse aus der Gruppe entfernen möchten?";
"addresses_count" = "%d Adressen";
"no_saved_routes" = "Keine gespeicherten Routen";
"no_saved_routes_description" = "Sie haben noch keine Routen gespeichert";
"all_routes" = "Alle Routen";
"address_count_format_simple" = "%d Adressen";
"delete_all_routes" = "Alle Routen löschen";
"navigate_to_all_points" = "Zu allen Punkten navigieren";
"confirm_navigate_to_route" = "Sind Sie sicher, dass Sie zu allen Punkten in der Route \"%@\" navigieren möchten?";
"temp_navigation_group" = "Temporäre Navigationsgruppe";
"route_management" = "Routenverwaltung";
"route_addresses" = "Routenadressen";
"no_addresses_in_route" = "Diese Route hat keine Adressen";
"must_keep_one_route" = "Mindestens eine Route muss beibehalten werden";
"confirm_delete_route" = "Sind Sie sicher, dass Sie die Route \"%@\" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"confirm_delete_all_routes" = "Löschen aller Routen bestätigen";
"confirm_delete_all_routes_message" = "Sind Sie sicher, dass Sie alle Routen löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"delete_all" = "Alle löschen";
"address_information" = "Adressinformationen";
"group_belonging" = "Zugehörige Gruppe";
"view_map" = "Karte anzeigen";
"delete_delivery_point" = "Lieferpunkt löschen";
"delivery_point_details" = "Lieferpunkt-Details";
"confirm_deletion" = "Löschen bestätigen";
"delete_delivery_point_confirmation" = "Sind Sie sicher, dass Sie diesen Lieferpunkt löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.";
"delivery_photos" = "Lieferfotos";
"view_delivery_photos" = "Lieferfotos anzeigen";
"no_photos_taken" = "Noch keine Fotos aufgenommen";
"take_photos" = "Fotos aufnehmen";
"loading_photos" = "Fotos werden geladen...";
"photo_not_found" = "Foto nicht gefunden";
"photo_deleted" = "Foto wurde gelöscht";
"share_photos" = "Foto teilen";
"photo_capture_title" = "Fotobestätigung";
"door_number_photo_title" = "Straßennummer/Hausnummer-Foto";
"package_label_photo_title" = "Paketetikett-Foto";
"placement_photo_title" = "Platzierungsort-Foto";
"door_number_photo_desc" = "Bitte machen Sie ein klares Foto der Hausnummer, stellen Sie sicher, dass Zahlen/Buchstaben sichtbar sind";
"package_label_photo_desc" = "Bitte fotografieren Sie das Paketetikett, stellen Sie sicher, dass die Empfängerinformationen klar sichtbar sind";
"placement_photo_desc" = "Bitte fotografieren Sie die endgültige Platzierung des Pakets";
"swipe_to_switch" = "Wischen zum Wechseln des Fototyps";
"photos_will_be_saved_to" = "Fotos werden gespeichert in";
"complete_photos" = "Fotoaufnahme abschließen";
"photo_save_success" = "Foto erfolgreich gespeichert";
"photo_save_failure" = "Foto speichern fehlgeschlagen";
"no_photos_found" = "Keine Fotos gefunden";
"photos_deleted_or_not_taken" = "möglicherweise Foto wurde gelöscht oder noch nicht aufgenommen";
"share_photo" = "Foto teilen";
"photo_capture_preview" = "Vorschaumodus - Kamera-Simulation";
"photo_capture_close" = "Schließen";
"camera_start_failed" = "Kamera-Start fehlgeschlagen";
"camera_start_failed_retry" = "Kamera kann nicht gestartet werden, bitte erneut versuchen";
"camera_init_failed" = "Kamera-Initialisierung fehlgeschlagen";
"camera_access_failed" = "Kamera kann nicht zugegriffen werden";
"photo_processing_failed" = "Foto aufnehmen fehlgeschlagen";
"photo_processing_failed_retry" = "Fotobearbeitung kann nicht abgeschlossen werden, bitte erneut versuchen";
"photo_capture_progress" = "Fortschritt: %d/%d";
"photo_captured_continue" = "Aufnahme abgeschlossen, weiter mit %@";
"pending" = "Ausstehende Lieferung";
"in_progress" = "In Lieferung";
"select_delivery_status" = "Lieferstatus auswählen";
"select_failure_reason" = "Grund für Fehlschlag auswählen";
"delivery_status_pending" = "Ausstehende Lieferung";
"delivery_status_in_progress" = "In Lieferung";
"delivery_status_completed" = "Lieferung abgeschlossen";
"delivery_status_failed" = "Lieferung fehlgeschlagen";
"failure_reason_not_at_home" = "Kunde nicht zu Hause";
"failure_reason_wrong_address" = "Falsche Adresse";
"failure_reason_no_access" = "Standort nicht zugänglich";
"failure_reason_rejected" = "Paket wurde abgelehnt";
"failure_reason_other" = "Andere Gründe";
"enter_custom_reason" = "Spezifischen Grund eingeben";
"custom_reason_placeholder" = "Bitte beschreiben Sie den spezifischen Grund...";
"custom_reason_required" = "Bitte geben Sie den spezifischen Grund ein";
"failure_reason_required" = "Bitte wählen Sie den Grund für den Fehlschlag";
"delivery_type_delivery" = "Lieferung";
"delivery_type_pickup" = "Abholung";
"delivery_order_first" = "Erste";
"delivery_order_auto" = "Automatisch";
"delivery_order_last" = "Letzte";
"package_size_small" = "Klein";
"package_size_medium" = "Mittel";
"package_size_large" = "Groß";
"package_type_box" = "Karton";
"package_type_bag" = "Tasche";
"package_type_letter" = "Brief";
"one_click_navigation_grouping" = "Ein-Klick-Navigation-Gruppierung";
"speed_60x_faster" = "60x schneller";
"goodbye_manual_address_adding" = "Auf Wiedersehen manuelles Hinzufügen von Adressen";
"watch_detailed_demo" = "Detaillierte Demo ansehen";
"upgrade_to_pro_now" = "Jetzt auf Pro upgraden";
"free_trial_7_days" = "7 Tage kostenlose Testversion";
"traditional_vs_navibatch_pro" = "Traditionell vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "Wischen für vollständigen Vergleich";
"traditional_method" = "Traditionelle Methode";
"drivers_get_lost_affect_efficiency" = "Fahrer verirren sich, beeinträchtigt Effizienz";
"repetitive_operations_waste_time" = "Wiederholende Vorgänge verschwenden Zeit";
"total_time_60_seconds" = "Gesamtzeit: 60 Sekunden";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Routen optimieren, Entfernung reduzieren";
"improve_delivery_efficiency_accuracy" = "Liefereffizienz und -genauigkeit verbessern";
"speed_boost_60x" = "60x Geschwindigkeitsschub";
"total_time_1_second" = "Gesamtzeit: 1 Sekunde";
"time_comparison" = "Zeitvergleich";
"traditional_method_problems" = "Probleme der traditionellen Methode";
"each_address_3_5_seconds_14_total_60" = "Jede Adresse 3-5 Sekunden, 14 Adressen = 60 Sekunden";
"repetitive_operations_cause_fatigue" = "Wiederholende Vorgänge verursachen Ermüdung";
"address_order_reversed_last_becomes_first" = "Adressreihenfolge umgekehrt, letzte wird erste";
"need_manual_reverse_adding_takes_longer" = "Manuelle Umkehrung nötig, Hinzufügen dauert länger";
"navibatch_advantages" = "NaviBatch Vorteile";
"add_14_addresses_1_second_60x_faster" = "14 Adressen in 1 Sekunde hinzufügen, 60x schneller";
"auto_maintain_correct_order_no_adjustment" = "Automatisch korrekte Reihenfolge beibehalten, keine Anpassung";
"zero_error_rate_no_repetition" = "Null Fehlerrate, keine Wiederholung";
"save_59_seconds" = "59 Sekunden sparen";
"speed_boost_60x_simple" = "60x Geschwindigkeitsschub";
"seconds_format" = "%d Sekunden";
"actual_benefits_one_click_navigation" = "Tatsächliche Vorteile der Ein-Klick-Navigation";
"daily_savings" = "Tägliche Einsparungen";
"daily_savings_value" = "59 Sekunden täglich";
"daily_savings_description" = "Zeitersparnis beim Hinzufügen von Adressen";
"monthly_savings" = "Monatliche Einsparungen";
"monthly_savings_value" = "30 Minuten monatlich";
"monthly_savings_description" = "Zusätzliche Zeit für mehr Lieferungen";
"fuel_savings_value" = "30% Kraftstoffeinsparung";
"fuel_savings_description" = "Optimierte Routen reduzieren Entfernung";
"income_increase" = "Einkommenssteigerung";
"income_increase_value" = "20% mehr Lieferungen";
"income_increase_description" = "Mehr Lieferungen in derselben Zeit";
"trial" = "Testversion";
"days_left" = "Tage verbleibend";
"free_plan_description" = "Kostenloser Plan aktiv";
"pro_plan_active" = "Pro-Plan aktiv";
"expert_plan_active" = "Expert-Plan aktiv";
"trial_active" = "Testversion aktiv";
"trial_expires_on" = "Testversion läuft am %@ ab";
"address_validation_mode" = "Adressvalidierungsmodus";
"validation_description" = "Validierungsbeschreibung";
"current_settings" = "Aktuelle Einstellungen";
"validation_mode_format" = "Validierungsmodus: %@";
"threshold_score_format" = "Schwellenwert: %.1f";
"validation_example" = "Validierungsbeispiel";
"original_address_example" = "Originaladresse: %@";
"reverse_address_example" = "Umgekehrte Adresse: %@";
"house_number_difference" = "Hausnummer-Unterschied: %d";
"result_label" = "Ergebnis: %@";
"may_pass_warning" = "Kann mit Warnung bestehen";
"will_not_pass" = "Wird nicht bestehen";
"real_case_example" = "Reales Fallbeispiel";
"real_case_description" = "Beschreibung des realen Falls";
"address_validation_settings" = "Adressvalidierungseinstellungen";
"clear" = "Löschen";
"view_details" = "Details anzeigen";
"create_test_data" = "Testdaten erstellen";
"manual_snapshot" = "Manueller Snapshot";
"start_location_updates" = "Standort-Updates starten";
"stop_location_updates" = "Standort-Updates stoppen";
"user_location_marker_test" = "Benutzerstandort-Marker-Test";
"location_animation_control" = "Standort-Animations-Steuerung";
"current_location_format" = "Aktueller Standort: %@";
"waiting_for_location" = "Warte auf Standort...";
"diagnostic_tools" = "Diagnose-Tools";
"storekit_diagnostics" = "StoreKit-Diagnose";
"subscription_function_test" = "Abonnement-Funktionstest";
"localization_test" = "Lokalisierungstest";
"address_validation_demo" = "Adressvalidierungs-Demo";
"localization_tools" = "Lokalisierungs-Tools";
"coordinate_debug_tools" = "Koordinaten-Debug-Tools";
"smart_abbreviation_expansion_test" = "Intelligenter Abkürzungserweiterungs-Test";
"subscription_restore_diagnostics" = "Abonnement-Wiederherstellungs-Diagnose";
"batch_address_import_test" = "Batch-Adressimport-Test";
"test_import_1000_addresses_memory" = "Test Import 1000 Adressen und Speicher";
"map_rendering_test" = "Karten-Rendering-Test";
"test_map_display_markers_memory" = "Test Kartenanzeige Marker und Speicher";
"select_test_language" = "Testsprache auswählen";
"discover_60x_speed_boost" = "60x Geschwindigkeitsschub entdecken";
"see_60x_speed_demo" = "60x Geschwindigkeits-Demo ansehen";
"free_vs_pro_comparison" = "Kostenlos vs Pro Vergleich";
"our_free_beats_competitors_paid" = "Unsere kostenlose Version schlägt kostenpflichtige Konkurrenz";
"features" = "Funktionen";
"up_to_20" = "Bis zu 20";
"unlimited" = "Unbegrenzt";
"smart_optimization" = "Intelligente Optimierung";
"up_to_20_percent" = "Bis zu 20%";
"free_tier_grouping_limit" = "Bis zu 10 Adressen";
"pro_tier_unlimited_grouping" = "Unbegrenzte Gruppierung";
"free_tier_navigation_limit" = "1 Gruppe (bis zu 10 Adressen)";
"pro_tier_unlimited_navigation" = "Mehrere Gruppen (unbegrenzt)";
"file_not_found" = "Datei nicht gefunden";
"sample_file_not_available" = "Beispieldatei nicht verfügbar";
"file_copy_failed" = "Datei kopieren fehlgeschlagen";

// PDF Processing
"ai_processing_pdf_text" = "PDF-Text wird verarbeitet";
"ai_processing_pdf_native" = "PDF-Dokument wird verarbeitet";
"processing_pdf_document" = "PDF-Dokument wird verarbeitet";
"processing_pdf_text" = "PDF-Text wird verarbeitet";
"select_pdf_files" = "PDF-Dateien auswählen";
"tap_to_select_from_files" = "Tippen Sie, um aus Dateien auszuwählen";
"loading_pdf_files" = "PDF-Dateien werden geladen...";
"pdf_selection_failed" = "PDF-Dateiauswahl fehlgeschlagen";
"no_pdf_pages_extracted" = "Keine Seiten aus PDF extrahiert";
"extracting_pdf_pages" = "PDF-Seiten werden extrahiert";

// 地图模式选择器
"map_mode_selector_title" = "Kartenmodus";
"map_mode_standard" = "Standard";
"map_mode_satellite" = "Satellit";
"map_mode_traffic" = "Verkehr";
"map_mode_hybrid" = "Hybrid";