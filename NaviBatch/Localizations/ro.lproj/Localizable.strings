/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Setări Limbă";
"system_language" = "Limba Sistemului";
"system_language_section" = "Setări Sistem";
"languages" = "Limbă";
"language_info_title" = "Despre Setările de Limbă";
"language_info_description" = "După modificarea setărilor de limbă, aplicația va afișa textul în limba selectată. Unele conținuturi pot necesita repornirea aplicației pentru a aplica complet noile setări de limbă.";
"restart_required" = "Repornirea Aplicației Este Necesară";
"restart_app_message" = "Pentru a aplica complet modificările de limbă, reporniți aplicația.";
"restart_now" = "Repornește Acum";
"restart_later" = "Repornește Mai Târziu";
"close" = "Închide";
"cancel" = "Anulează";
"save" = "Salvează";
"edit" = "Editează";
"delete" = "Șterge";
"done" = "Finalizat";
"next" = "Următorul";
"back" = "Înapoi";
"confirm" = "Confirmă";
"error" = "Eroare";
"success" = "Succes";
"warning" = "Avertisment";
"unknown_error" = "Eroare necunoscută";
"loading" = "Se încarcă...";
"search" = "Căutare";
"settings" = "Setări";
"help" = "Ajutor";
"about" = "Despre";
"menu" = "Meniu";
"understand" = "Înțeleg";
"navigation" = "Navigare";
"start_navigation" = "Începe Navigarea";
"subscription" = "Abonament";
"upgrade_to_pro" = "Actualizează la Pro";
"upgrade_description" = "Gruparea navigației cu un clic, de 60 de ori mai rapidă decât operarea manuală";
"restore_purchases" = "Restaurează Achizițiile";
"learn_more" = "Află Mai Multe";
"upgrade_your_plan" = "Actualizează-ți Planul";
"one_click_navigation_description" = "Gruparea adreselor de navigare cu un clic, economisește timp și combustibil";
"current_plan" = "Planul Actual";
"upgrade" = "Actualizează";
"maybe_later" = "Poate Mai Târziu";
"free_tier_name" = "Începător";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratuit";
"pro_tier_price" = "$9.99/lună";
"expert_tier_price" = "$59.99/an";
"free_tier_description" = "Potrivit pentru persoane fizice și întreprinderi mici, nu necesită multe opriri";
"pro_tier_description" = "Potrivit pentru utilizatorii care au nevoie de gruparea navigației cu un clic și adrese nelimitate";
"expert_tier_description" = "La fel ca Pro, dar economisește 31% prin planul anual";
"route_optimization" = "Optimizarea Rutei";
"unlimited_routes" = "Rute Nelimitate";
"unlimited_optimizations" = "Optimizări Nelimitate";
"max_15_addresses" = "Maxim 15 adrese pe rută";
"save_fuel_30" = "Economisește până la 30% combustibil";
"unlimited_addresses" = "✨ Adrese Nelimitate ✨";
"one_click_navigation" = "⚡ Gruparea Navigației cu Un Clic - De 60x Mai Rapid ⚡";
"package_finder" = "Localizator de Pachete";
"annual_savings" = "Economii Plan Anual";
"switched_to_free" = "Trecut la Planul Gratuit";
"switched_to_subscription" = "Trecut la Planul de Abonament";
"unlimited_stops" = "Opriri Nelimitate";
"plan_as_many_stops_as_needed" = "Adaugă orice număr de puncte de livrare fără limitări";
"expires_in_days" = "Expiră în %d zile";
"trial_expires_in_days" = "Perioada de încercare expiră în %d zile";
"expired" = "Expirat";
"subscription_expires_on" = "Abonamentul expiră pe %@";
"subscription_active_until" = "Abonament activ până la %@";
"enter_or_search_address" = "Introduceți sau căutați adresa";
"search_results_count" = "Rezultate căutare: %d";
"no_matching_addresses" = "Nu s-au găsit adrese corespunzătoare";
"search_address_failed" = "Căutarea adresei a eșuat: %@";
"address_search_no_response" = "Căutarea adresei nu răspunde";
"cannot_get_address_coordinates" = "Nu se pot obține coordonatele adresei";

"cannot_get_coordinates_retry" = "Nu se pot obține coordonatele adresei, introduceți manual sau încercați din nou";

"image_address_recognition" = "Recunoașterea adreselor din imagine";
"select_images" = "Selectează imagini";
"select_multiple_images" = "Selecția multiplă este suportată";
"processing_images" = "Se procesează imaginile...";
"processing_image_progress" = "Se procesează imaginea %d din %d";
"recognizing_text" = "Se recunoaște textul...";
"geocoding_addresses" = "Se obțin coordonatele adreselor...";
"recognition_complete" = "Recunoașterea completă";
"no_text_recognized" = "Nu s-a recunoscut niciun text";
"no_addresses_found" = "Nu s-au găsit adrese valide";
"image_recognition_failed" = "Recunoașterea imaginii a eșuat";
"image_recognition_error" = "Eroare recunoaștere imagine: %@";
"text_recognition_failed" = "Recunoașterea textului a eșuat";
"address_parsing_failed" = "Analiza adresei a eșuat";
"select_addresses_to_add" = "Selectează adresele de adăugat";
"recognized_addresses" = "Adrese recunoscute";
"address_coordinates" = "Coordonate adresă";
"toggle_address_selection" = "Comută selecția adresei";
"remove_address" = "Elimină adresa";
"confirm_selected_addresses" = "Confirmă adresele selectate";
"no_addresses_selected" = "Nu sunt adrese selectate";
"image_processing_cancelled" = "Procesarea imaginii anulată";
"unsupported_image_format" = "Format de imagine nesuportat";
"image_too_large" = "Fișierul imagine prea mare";
"image_recognition_permission_required" = "Este necesară permisiunea de acces la galeria foto";
"ocr_language_detection" = "Detectarea automată a limbii";
"improve_image_quality" = "Asigură-te că imaginea este clară și textul vizibil";
"address_validation_in_progress" = "Validarea adreselor în curs...";
"batch_address_import" = "Import adrese în lot";
"validated_addresses_count" = "%d adrese validate";
"addresses_with_issues" = "%d adrese cu probleme";
"select_all" = "Selectează Tot";
"import_selected" = "Importă selectate";
"validating_addresses" = "Se validează adresele...";
"empty_address" = "Adresă goală";
"invalid_coordinates" = "Coordonate invalide";
"coordinate_warning" = "Avertisment coordonate";
"address_validation_issue" = "Problemă validare adresă";
"cannot_get_coordinates" = "Nu se pot obține coordonatele adresei";
"no_importable_addresses" = "Nu există adrese de importat, verificați limitele adreselor";
"free_version_max_addresses" = "Versiunea gratuită permite maximum %d adrese.";
"valid" = "Valid";
"with_issues" = "Cu probleme";
"low_confidence_address" = "Validare adresă cu încredere scăzută";
"address_validation_failed" = "Validarea adresei a eșuat";
"current_addresses_remaining" = "În prezent aveți %d adrese, puteți adăuga doar încă %d adrese.";
"can_import_selected" = "Ați selectat %d adrese, puteți importa aceste adrese.";
"selected_exceeds_limit" = "Ați selectat %d adrese, depășind numărul permis cu %d.";
"selected_addresses_all_importable" = "Ați selectat %d adrese, toate pot fi importate.";
"upgrade_for_unlimited_addresses" = "Actualizați la premium pentru adrese nelimitate!";
"current_route_address_limit" = "Ruta actuală are %d adrese, puteți adăuga doar încă %d adrese, total maxim %d adrese.";
"import_all_addresses" = "Importă Toate Adresele";
"import_first_n" = "Importă doar primele %d";
"import_selected_addresses" = "Importă adresele selectate";
"upgrade_to_premium" = "Actualizează la Premium";
"batch_add_addresses" = "Adaugă Adrese în Lot";
"batch_address_input_placeholder" = "Introduceți sau lipiți adrese, o adresă pe linie. Maxim 35 de adrese.";
"search_address" = "Căutare Adresă";
"no_saved_addresses" = "Nicio Adresă Salvată";
"no_saved_addresses_description" = "Nu ați salvat încă nicio adresă sau nu există adrese care să îndeplinească criteriile de filtrare";
"select_address_book" = "Selectați Agenda de Adrese";
"routes" = "Rută";
"address_book" = "Agenda de Adrese";
"saved_routes" = "Rute Salvate";
"manage_your_routes" = "Gestionați planificarea rutelor";
"manage_your_addresses" = "Gestionați adresele folosite frecvent";
"preferences" = "Preferințe";
"set_custom_start_point" = "Setați Punctul de Pornire Personalizat";
"current_start_point" = "Punctul de pornire actual: %@";
"support" = "Suport";
"contact_us" = "Contactați-ne";
"contact_us_description" = "Aveți întrebări sau sugestii? Nu ezitați să ne contactați!";
"help_center" = "Centrul de Ajutor";
"quick_actions" = "Acțiuni rapide";
"main_features" = "Funcții principale";
"support_help" = "Suport și ajutor";
"customize_app_settings" = "Personalizează setările aplicației";
"unlock_all_features" = "Deblochează toate funcțiile";
"get_help_support" = "Obține ajutor și suport";
"app_info_version" = "Informații aplicație și versiune";
"dev_tools" = "Instrumente dezvoltator";
"debug_testing_tools" = "Instrumente debug și testare";
"version" = "Versiune";
"addresses" = "Adresă";
"limited_to_20_addresses" = "Limitat la 20 adrese";
"all_premium_features" = "Toate funcțiile premium";
"open_subscription_view" = "Deschide Direct Vizualizarea Abonamentului";
"open_subscription_view_description" = "Omite stratul intermediar, afișează direct SubscriptionView";
"restore_purchases_failed" = "Restaurarea achizițiilor a eșuat: %@";
"rate_us" = "Evaluați-ne";
"rate_us_description" = "Feedback-ul dvs. este important pentru noi și ne ajută să îmbunătățim aplicația!";
"share_app" = "Distribuie Aplicația";
"share_app_text" = "Încercați NaviBatch, o aplicație uimitoare de planificare a rutelor!";
"about_app" = "Despre Aplicație";
"developer_tools" = "Instrumente pentru Dezvoltatori";
"coordinate_debug_tool" = "Instrument de Debug Coordonate";
"batch_fix_addresses" = "Corectare Adrese în Lot";
"clear_database" = "Șterge Baza de Date";
"clear_database_confirmation" = "Aceasta va șterge toate datele, inclusiv rutele, adresele și grupurile. Această acțiune nu poate fi anulată. Sunteți sigur că doriți să continuați?";
"confirm_clear" = "Confirmă Ștergerea";
"version_info" = "Versiunea %@ (%@)";
"current_system_language" = "Limba Actuală a Sistemului";
"reset_to_system_language" = "Resetează la Limba Sistemului";
"language" = "Limbă";
"address" = "Adresă";
"coordinates" = "Coordonate";
"distance_from_current_location" = "Distanța de la Locația Actuală";
"address_info" = "Informații Adresă";
"update_coordinates" = "Actualizează Coordonatele";
"fix_address" = "Corectează Adresa";
"prompt" = "Prompt";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Vă rugăm să modificați adresa înainte de a actualiza coordonatele";
"coordinates_update_success" = "Coordonatele au fost actualizate cu succes";
"coordinates_update_failure" = "Actualizarea coordonatelor a eșuat";
"save_failure" = "Salvarea a eșuat: %@";
"no_saved_addresses_title" = "Nicio Adresă Salvată";
"no_saved_addresses_message" = "Nu ați salvat încă nicio adresă";
"add_new_address" = "Adaugă Adresă Nouă";
"address_title" = "Adresă";
"add" = "Adaugă";
"refresh" = "Reîmprospătează";
"notes" = "Note";
"address_details" = "Detalii Adresă";
"favorite" = "Favorit";
"edit_address" = "Editează Adresa";
"confirm_delete" = "Confirmă Ștergerea";
"delete_address_confirmation" = "Sunteți sigur că doriți să ștergeți această adresă? Această operațiune nu poate fi anulată.";
"address_marker" = "Adresă";
"address_label" = "Adresă:";
"notes_label" = "Note:";
"created_at_label" = "Ora creării:";
"open_in_maps" = "Deschide în Hartă";
"copy_address" = "Copiază Adresa";
"address_details_title" = "Detalii Adresă";
"start_end_point" = "Punct de Început/Sfârșit";
"start_point" = "Punct de Pornire";
"end_point" = "Punct Final";
"route_info" = "Informații Rută";
"address_count" = "Numărul de Adrese";
"address_count_format" = "%d adrese";
"points_count_format" = "%d puncte";
"additional_points_format" = "+%d puncte";
"export_route" = "Exportă Ruta";
"navigate" = "Navighează";
"address_list" = "Lista de Adrese";
"no_addresses" = "Nicio Adresă";
"no_addresses_message" = "Această rută nu are încă adrese";
"address_point_start" = "Punct de Pornire";
"address_point_stop" = "Punct de Oprire";
"address_point_end" = "Punct Final";
"route_name" = "Numele Rutei";
"new_route" = "Rută Nouă";
"saved_route" = "Rută Salvată";
"plan_route" = "Planifică Ruta";
"clear_all" = "Șterge Tot";
"avoid" = "Evită:";
"toll_roads" = "Drumuri cu Taxă";
"highways" = "Autostrăzi";
"processing_addresses" = "Procesarea adreselor...";
"same_start_end_point" = "Ați setat aceeași adresă ca punct de pornire și de sosire";
"add_start_point" = "Adaugă Punct de Pornire";
"swipe_left_to_delete" = "← Glisați la stânga pentru a șterge";
"add_end_point" = "Adaugă Punct Final";
"enter_and_select_address" = "Introduceți și selectați adresa";
"current_search_text" = "Textul de căutare actual: %@";
"add_address" = "Adaugă Adresă";
"selected_coordinates" = "Coordonate Selectate";
"company_name_optional" = "Numele Companiei (Opțional)";
"url_optional" = "URL (Opțional)";
"favorite_address" = "Adresă Favorită";
"set_as_start_and_end" = "Setează ca Punct de Pornire și de Sosire";
"batch_paste" = "Lipire în Lot";
"file_import" = "Import Fișier";
"web_download" = "Descărcare Web";
"saving" = "Se salvează...";
"delivery_point_management" = "Gestionarea Punctelor de Livrare";
"information_category" = "Categoria Informațiilor";
"package_info" = "Informații Pachet";
"vehicle_position" = "Poziția Vehiculului";
"delivery_info" = "Informații Livrare";
"take_photo_record" = "Fă Înregistrare Foto";
"update_status" = "Actualizează Statusul";
"edit_address_button" = "Editează Adresa";
"access_instructions" = "Instrucțiuni de Acces";
"add_access_instructions" = "Adaugă instrucțiuni de acces...";
"package_count" = "Numărul de Pachete";
"packages" = "pachete";
"package_unit" = "buc";
"package_size" = "Mărimea Pachetului";
"package_type" = "Tipul Pachetului";
"mark_as_important" = "Marchează ca Important";
"priority_delivery" = "Livrare prioritară";
"priority_level" = "Nivel prioritate";
"priority_1" = "Prioritate 1";
"priority_2" = "Prioritate 2";
"priority_3" = "Prioritate 3";
"no_priority" = "Fără prioritate";
"no_priority_short" = "Fără";
"set_priority" = "Setează prioritatea";
"select_package_position" = "Selectați Poziția Pachetului în Vehicul";
"vehicle_area" = "Zona Vehiculului";
"left_right_position" = "Poziția Stânga/Dreapta";
"vehicle_position_front" = "Față";
"vehicle_position_middle" = "Mijloc";
"vehicle_position_back" = "Spate";
"vehicle_position_left" = "Stânga";
"vehicle_position_right" = "Dreapta";
"vehicle_position_floor" = "Partea de Jos";
"vehicle_position_shelf" = "Partea de Sus";
"height_position" = "Poziția pe Înălțime";
"vehicle_position_none" = "Nicio poziție selectată";
"delivery_type" = "Tipul de Livrare";
"delivery_status" = "Statusul Livrării";
"order_info" = "Informații Comandă";
"order_information" = "Informații Comandă";
"order_number" = "Numărul Comenzii";
"enter_order_number" = "Introduceți numărul comenzii";
"tracking_number" = "Numărul de Urmărire";
"enter_tracking_number" = "Introduceți numărul de urmărire";
"tracking_info" = "Informații urmărire";
"tracking_information" = "Informații urmărire";
"time_info" = "Informații Timp";
"time_information" = "Informații Timp";
"estimated_arrival_time" = "Timpul Estimat de Sosire";
"anytime" = "Oricând";
"stop_time" = "Timpul de Oprire";
"minutes_format" = "%d minute";
"photo_record" = "Înregistrare Foto";
"door_number_photo" = "Foto Numărul Ușii";
"package_label_photo" = "Foto Eticheta Pachetului";
"placement_photo" = "Foto Plasare";
"door_number_desc" = "Faceți o fotografie clară a numărului casei sau străzii, asigurați-vă că cifrele/literele sunt vizibile";
"package_label_desc" = "Faceți o fotografie a etichetei pachetului, asigurați-vă că informațiile destinatarului sunt clar vizibile";
"placement_desc" = "Faceți o fotografie a locației finale de plasare a pachetului";
"photo_captured" = "Fotografia a fost făcută";
"photo_captured_options" = "Fotografia a fost făcută, doriți să continuați cu următoarea fotografie sau să finalizați fotografia actuală?";
"continue_to_next_photo" = "Continuă cu următoarea - %@";
"retake" = "Refă Fotografia";
"tap_to_capture" = "Atingeți pentru a Fotografia";
"flash_auto" = "Flash Automat";
"flash_on" = "Activează Flash-ul";
"flash_off" = "Dezactivează Flash-ul";
"photo_record_completed" = "Înregistrarea Foto Finalizată";
"photo_confirmation" = "Confirmarea Fotografiei";
"ok" = "OK";
"complete_photo_capture" = "Finalizează Fotografiile";
"photo_instructions" = "Atingeți fiecare card de fotografie pentru a fotografia. Toate fotografiile trebuie finalizate.";
"photo_options" = "Opțiuni Fotografie";
"view_photo" = "Vezi Fotografia";
"retake_photo" = "Refă Fotografia";
"saving_photos" = "Se salvează fotografiile...";
"completed" = "Finalizat";
"not_taken" = "Nu a Fost Făcută";
"route_options" = "Opțiuni Rută";
"avoid_tolls" = "Evită Drumurile cu Taxă";
"avoid_highways" = "Evită Autostrăzile";
"optimize_route" = "Optimizează Ruta";
"optimizing" = "Se optimizează...";
"optimization_complete" = "Optimizarea Finalizată";
"route_optimization_results" = "Rezultate optimizare rută";
"route_planning_options" = "Opțiuni planificare rută";
"before_optimization" = "Înainte de optimizare";
"after_optimization" = "După optimizare";
"auto_group" = "Grupare automată";
"optimized_route_order" = "Ordinea rutei optimizate";
"apply" = "Aplică";
"kilometers" = "kilometri";
"street_number_issue_warning" = "⚠️ Diferență mare la numărul străzii! Aceasta poate cauza livrarea la adresa greșită și amenzi. Verifică adresa imediat";
"address_validation_critical" = "Problemă critică validare adresă";
"street_number_difference_high_risk" = "Diferență mare număr stradă, risc ridicat";
"delete_address" = "删除地址";
"street" = "Stradă";
"city" = "Oraș";
"state" = "Stat/Provincie";
"country" = "Țară";
"postal_code" = "Cod Poștal";
"phone" = "Telefon";
"email" = "E-mail";
"website" = "Site Web";
"company" = "Companie";
"latitude" = "Latitudine";
"longitude" = "Longitudine";
"geocoding_error" = "Eroare de Geocodificare";
"address_validation" = "Validarea Adresei";
"invalid_addresses" = "Adresă Invalidă";
"fix_addresses" = "Corectează Adresa";
"route" = "Rută";
"select_address_point" = "Selectați Punctul de Adresă";
"select_delivery_points" = "Selectați Punctele de Livrare";
"create_delivery_route" = "Creați Ruta de Livrare";
"view_saved_routes" = "Vezi Rutele Salvate";
"create_route" = "Creați Ruta";
"edit_route" = "Editează Ruta";
"delete_route" = "Șterge Ruta";
"route_details" = "Detalii Rută";
"selected_addresses" = "%d adrese selectate";
"reached_limit" = "Limita Atinsă";
"can_select_more" = "Mai puteți selecta %d";
"navigate_button" = "Navigare";
"create_group" = "Creați Grup";
"waypoints" = "Puncte de Trecere";
"total_distance" = "Distanța Totală";
"estimated_time" = "Timpul Estimat";
"route_summary" = "Rezumatul Rutei";
"route_saved" = "Rută Salvată";
"route_optimized" = "Rută Optimizată";
"optimizing_route" = "Se optimizează ruta...";
"completed_percent" = "Finalizat %d%%";
"processing_points" = "Se procesează: %d/%d";
"estimated_remaining_time" = "Timpul Estimat Rămas: %@";
"delivery" = "Livrare";
"delivery_confirmation" = "Confirmarea Livrării";
"take_photo" = "Fă Fotografie";
"signature" = "Semnătură";
"delivery_notes" = "Note Livrare";
"delivered" = "Livrat";
"not_delivered" = "Nelivrat";
"delivery_time" = "Timpul Livrării";
"delivery_date" = "Data Livrării";
"package_details" = "Detalii Pachet";
"package_id" = "ID Pachet";
"package_weight" = "Greutatea Pachetului";
"package_dimensions" = "Dimensiunile Pachetului";
"recipient_name" = "Numele Destinatarului";
"recipient_phone" = "Telefonul Destinatarului";
"groups" = "Grup";
"saved_groups" = "Grupuri Salvate";
"edit_group" = "Editează Grupul";
"delete_group" = "Șterge Grupul";
"group_name" = "Numele Grupului";
"group_details" = "Detalii Grup";
"auto_grouping" = "Grupare Automată";
"group_by" = "Grupează După";
"add_to_group" = "Adaugă la Grup";
"remove_from_group" = "Elimină din Grup";
"group_created" = "Grup Creat";
"default_group_name_format" = "Grup%d";
"auto_grouping_completed" = "Gruparea Automată Finalizată";
"auto_grouping_in_progress" = "Gruparea Automată în Curs...";
"create_group_every_14_addresses" = "Creați un grup pentru fiecare 14 adrese";
"create_delivery_group" = "Creați Grup de Livrare";
"enter_group_name" = "Introduceți numele grupului";
"selected_delivery_points" = "Puncte de livrare selectate";
"drag_to_adjust_order" = "Trageți pentru a ajusta ordinea";
"free_plan" = "Versiunea Gratuită";
"pro_plan" = "Versiunea Pro";
"expert_plan" = "Versiunea Expert";
"monthly" = "Plan Lunar";
"yearly" = "Plan Anual";
"subscribe" = "Abonament";
"manage_subscription" = "Gestionează Abonamentul";
"subscription_benefits" = "Beneficiile Abonamentului";
"free_trial" = "Încercare Gratuită";
"price_per_month" = "%@ pe lună";
"price_per_year" = "%@ pe an";
"save_percent" = "Economisește %@%";
"subscription_terms" = "Termenii Abonamentului";
"privacy_policy" = "Politica de Confidențialitate";
"terms_of_service" = "Termenii de Serviciu";
"feature_comparison" = "Comparație funcții";
"addresses_per_route" = "Adrese pe rută";
"max_20_addresses" = "Maximum 20 adrese";
"fuel_savings" = "Economii combustibil";
"up_to_30_percent" = "Până la 30%";
"choose_subscription_plan" = "Alege planul de abonament";
"monthly_plan" = "Plan lunar";
"yearly_plan" = "Plan anual";
"/month_suffix" = "/lună";
"/year_suffix" = "/an";
"save_30_percent" = "Economisește 30%";
"free_trial_7_days_cancel_anytime" = "7 zile încercare gratuită, anulează oricând";
"subscription_auto_renew_notice" = "Abonamentul se reînnoiește automat";
"and" = "și";
"subscription_exclusive" = "Exclusiv pentru abonați";
"free_version_optimization_limit" = "Versiunea gratuită cu optimizare limitată";
"free_version_supports_max_addresses" = "Versiunea gratuită suportă adrese maxime";
"current_route_contains_addresses" = "Ruta curentă conține adrese";
"upgrade_to_pro_unlimited_addresses" = "Actualizează la Pro pentru adrese nelimitate";
"continue_optimization" = "Continuă optimizarea";
"upgrade_unlock_one_click_navigation" = "Actualizează pentru a debloca navigarea cu un clic";
"learn_one_click_navigation_grouping" = "Învață despre navigarea și gruparea cu un clic";
"toggle_subscription_status" = "Comută statusul abonamentului";
"toggle_subscription_description" = "Comută între abonament și gratuit pentru testare";
"product_info_unavailable" = "Informații produs indisponibile";
"purchase_failed" = "Achiziția a eșuat: %@";
"upgrade_to_pro_version" = "Actualizează la versiunea Pro";
"unlock_all_premium_features" = "Deblochează toate funcțiile premium";
"first_7_days_free_cancel_anytime" = "Primele 7 zile gratuite, anulează oricând";
"payment_terms_notice" = "Plata va fi percepută din contul Apple ID după confirmarea achiziției. Abonamentul se reînnoiește automat, cu excepția cazului în care este anulat cu cel puțin 24 de ore înainte de sfârșitul perioadei curente.";
"terms_of_use" = "Termeni de utilizare";
"product_load_failed_check_connection" = "Încărcarea informațiilor produsului a eșuat, asigură-te că dispozitivul este conectat la internet și App Store";
"product_load_failed" = "Încărcarea produsului a eșuat: %@";
"verify_receipt" = "Verifică chitanța";
"one_click_navigation_short" = "Navigare cu un clic";
"save_30_percent_fuel" = "Economisește 30% combustibil";
"monthly_short" = "Lunar";
"yearly_short" = "Anual";
"upgrade_now" = "Actualizează acum";
"test_environment_pro_activated" = "Mediu de testare: Pro activat";
"payment_terms_notice_detailed" = "Plata va fi percepută din contul Apple ID după confirmarea achiziției. Abonamentul se reînnoiește automat, cu excepția cazului în care este anulat cu cel puțin 24 de ore înainte de sfârșitul perioadei curente. Abonamentele pot fi gestionate și anulate în setările App Store.";
"step_screenshot" = "Captură ecran pasul %d";
"previous_step" = "Anterior";
"next_step" = "Următorul";
"each_address_takes_3_5_seconds" = "Fiecare adresă durează 3-5 secunde pentru adăugare";
"need_repeat_14_times" = "Trebuie să repeți aceeași operație de 14 ori";
"navigation_order_often_confused" = "Ordinea navigației adesea confuză";
"error_prone_need_redo" = "Predispus la erori, trebuie să refaci operațiile";
"address_order_reversed_manual_adjust" = "Ordinea adreselor inversată, ajustare manuală necesară";
"one_click_add_all" = "Un clic, adaugă tot";
"smart_grouping_auto_sorting" = "Grupare inteligentă, sortare automată";
"maintain_correct_visit_order" = "Menține ordinea corectă de vizitare";
"zero_errors_zero_repetition" = "Zero erori, zero repetare";
"import" = "Import";
"export" = "Export";
"import_addresses" = "Importă Adrese";
"export_addresses" = "Exportă Adrese";
"import_from_file" = "Importă din Fișier";
"export_to_file" = "Exportă în Fișier";
"file_format" = "Formatul Fișierului";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Au fost importate cu succes %d adrese, toate cu coordonate valide.";
"export_success" = "Export Reușit";
"import_error" = "Eroare Import";
"export_error" = "Eroare Export";
"navigation_app" = "Aplicație de navigare";
"apple_maps" = "Apple Maps";
"app_preferences" = "Preferințe aplicație";
"distance_unit" = "Unitate distanță";
"current_language" = "Limba curentă";
"info" = "Informații";
"contact_us_header" = "Contactează-ne";
"contact_us_subheader" = "Ai întrebări sau sugestii? Suntem bucuroși să ajutăm!";
"contact_options" = "Opțiuni contact";
"email_us" = "Trimite-ne email";
"contact_form" = "Formular contact";
"contact_and_support" = "Contact și suport";
"common_questions" = "Întrebări frecvente";
"how_to_use" = "Cum să folosești";
"subscription_faq" = "FAQ abonament";
"navigation_help" = "Ajutor navigare";
"troubleshooting" = "Depanare";
"help_howto_content" = "NaviBatch este o aplicație puternică de planificare rute care te ajută să optimizezi rutele de livrare, economisind timp și combustibil. Poți adăuga multiple adrese, optimiza automat ordinea rutei și naviga către Apple Maps cu un clic.";
"help_subscription_content" = "NaviBatch oferă versiuni gratuite și pro. Versiunea gratuită suportă până la 20 adrese, în timp ce versiunea pro oferă adrese nelimitate și funcții de navigare în grup cu un clic.";
"help_navigation_content" = "NaviBatch folosește Apple Maps pentru navigare. Poți naviga către fiecare adresă individual sau folosi funcția de grupare pentru a naviga către multiple adrese deodată.";
"help_troubleshooting_content" = "Dacă întâmpini probleme, mai întâi asigură-te că dispozitivul tău are conexiune la rețea și permisiunile de locație au fost acordate. Dacă problemele persistă, contactează echipa noastră de suport.";
"actions" = "Acțiuni";
"legal" = "Legal";
"show_look_around" = "Vezi Imaginea Străzii";
"hide_look_around" = "Ascunde Imaginea Străzii";
"map" = "Hartă";
"map_type" = "Tipul Hărții";
"standard" = "Standard";
"satellite" = "Satelit";
"hybrid" = "Hibrid";
"show_traffic" = "Afișează Traficul";
"current_location" = "Locația Actuală";
"directions" = "Indicații Rută";
"distance_to" = "Distanță";
"eta" = "Timpul Estimat de Sosire";
"look_around" = "Privește în Jur";
"locating_to_glen_waverley" = "Localizare la Glen Waverley";
"network_error" = "Eroare de Rețea";
"location_error" = "Eroare de Locație";
"permission_denied" = "Permisiunea Refuzată";
"location_permission_required" = "Permisiunea de Locație Necesară";
"camera_permission_required" = "Permisiunea Camerei Necesară";
"photo_library_permission_required" = "Permisiunea Bibliotecii de Fotografii Necesară";
"please_try_again" = "Încercați Din Nou";
"something_went_wrong" = "A Apărut o Eroare";
"invalid_input" = "Intrare Invalidă";
"required_field" = "Câmp Obligatoriu";
"no_internet_connection" = "Fără Conexiune la Internet";
"server_error" = "Eroare Server";
"timeout_error" = "Timpul Cererii Expirat";
"data_not_found" = "Datele Nu Au Fost Găsite";
"selection_limit_reached" = "Limita de Selecție Atinsă";
"selection_limit_description" = "Puteți selecta maximum %d adrese, ați selectat %d";
"location_status_valid" = "Interval Valid";
"address_validation_unknown" = "Nevalidat";
"address_validation_valid" = "Valid";
"address_validation_invalid" = "Invalid";
"address_validation_warning" = "Avertisment";
"address_validation_mismatch" = "Nu se potrivește";
"device_not_support_scanning" = "Dispozitivul nu suportă scanarea nativă";
"requires_ios16_a12_chip" = "Necesită iOS 16+ și chip A12 sau mai nou";
"debug_info" = "Informații debug:";
"address_confirmation" = "Confirmare adresă";
"continue_scanning" = "Continuă scanarea";
"confirm_add" = "Confirmă adăugarea";
"cannot_get_coordinates_scan_retry" = "Nu se pot obține coordonatele adresei, introdu manual sau scanează din nou";
"unknown_country" = "Țară necunoscută";
"unknown_city" = "Oraș necunoscut";
"please_enter_valid_address" = "Introduceți cel puțin o adresă validă";
"please_select_valid_address" = "Selectează o adresă validă";
"add_address_failed" = "Adăugarea adresei a eșuat";
"location_permission_required_for_current_location" = "Permisiunea de locație este necesară pentru obținerea locației curente";
"cannot_get_current_location_check_settings" = "Nu se poate obține locația curentă, verifică setările";
"cannot_get_current_location_address" = "Nu se poate obține adresa locației curente";
"get_current_location_failed" = "Obținerea locației curente a eșuat";
"location_status_warning" = "Interval de Avertisment";
"location_status_invalid" = "Locație Invalidă";
"location_status_unknown" = "Status Necunoscut";
"coordinates_origin_point" = "Invalid: Coordonate zero (0,0)";
"coordinates_invalid_nan" = "Invalid: Coordonate non-numerice";
"coordinates_out_of_range" = "Invalid: Coordonate în afara intervalului valid";
"coordinates_far_from_user" = "Avertisment: Locația este departe de poziția dvs. actuală";
"coordinates_ocean" = "Avertisment: Locația poate fi în ocean sau zonă nelocuită";
"free_address_limit" = "Limita Adreselor Versiunii Gratuite";
"address_count_limit" = "Limita Numărului de Adrese";
"selected_addresses_can_import" = "Ați selectat %d adrese, puteți importa aceste adrese.";
"selected_addresses_exceeds" = "Ați selectat %d adrese, depășind numărul permis cu %d.";
"import_success_with_warnings" = "A importat cu succes %d adrese, %d adrese cu coordonate normale, %d adrese cu avertismente.\n\nAdresele cu avertismente au fost marcate și pot fi reparate manual după import.";
"supported_formats" = "Formate Suportate";
"supported_format_csv" = "• Fișiere CSV: Coloana de adrese trebuie să conțină adrese complete";
"supported_format_json" = "• Date JSON: Array care conține câmpuri de adrese";
"supported_format_text" = "• Text simplu: O adresă pe linie";
"download_history" = "Istoricul Descărcărilor";
"input_address_data_url" = "Introduceți URL-ul datelor de adrese";
"import_result" = "Rezultatul Importului";
"downloading" = "Se descarcă...";
"processing_data" = "Se procesează datele...";
"google_drive_download_failed" = "Descărcarea Google Drive a eșuat";
"second_attempt_invalid_data" = "A doua încercare de descărcare a returnat date invalide";
"cannot_parse_json" = "Nu se pot analiza datele JSON, verificați formatul fișierului";
"cannot_parse_json_with_error" = "Nu se pot analiza datele JSON: %@";
"cannot_read_file" = "Nu se poate citi fișierul: %@";
"failed" = "Livrarea a Eșuat";
"no_valid_addresses" = "Nu s-au găsit adrese valide";
"supports_file_types" = "Suportă fișiere CSV, TXT și JSON";
"tap_to_select_file" = "Atingeți pentru a selecta fișierul";
"input_company_name" = "Introduceți numele companiei (opțional)";
"imported_addresses_count" = "Au fost importate %d adrese";
"excel_format_not_supported" = "Formatul Excel nu este suportat";
"import_failed" = "Importul a Eșuat";
"free_version_address_limit" = "Versiunea gratuită permite maximum %d adrese.";
"current_address_count" = "În prezent aveți %d adrese, puteți adăuga doar încă %d adrese.";
"upgrade_to_premium_unlimited" = "Actualizați la premium pentru adrese nelimitate!";
"route_address_limit" = "Ruta actuală are %d adrese, puteți adăuga doar încă %d adrese, total maxim %d adrese.";
"free_version_limit" = "Limita Adreselor Versiunii Gratuite";
"import_all_n" = "Importă toate %d";
"cannot_import" = "Nu se poate importa";
"select_at_least_one" = "Selectați cel puțin o adresă";
"no_valid_addresses_found" = "Nu s-au găsit adrese valide";
"import_success_all_valid" = "Au fost importate cu succes %d adrese, toate coordonatele adreselor sunt normale.";
"import_success_some_warnings" = "Au fost importate cu succes %d adrese, %d adrese cu coordonate normale, %d adrese nu pot obține coordonate.";
"company_format" = "Format companie";
"added_from_web_download" = "Adăugat din descărcarea web";
"invalid_csv_row" = "Linie CSV invalidă";
"distance_warning" = "Distanța depășește 200km de la locația actuală";
"not_in_australia" = "Coordonatele nu sunt în intervalul Australiei";
"invalid_address_data" = "Date de adresă invalide";
"distance_warning_confirm" = "Distanța de la locația curentă depășește 200 km, continui?";
"coordinates_missing" = "Coordonate lipsă";
"low_accuracy_address" = "Adresă cu acuratețe scăzută";
"address_partial_match" = "Potrivire parțială adresă";
"address_outside_region" = "Adresă în afara regiunii";
"api_limit_reached" = "Limita API atinsă";
"address_not_exist_or_incorrect_format" = "Adresa nu există sau format incorect";
"please_check_address_spelling" = "Verifică ortografia adresei";
"try_smaller_street_number" = "Încearcă un număr de stradă mai mic";
"use_full_street_type_name" = "Folosește numele complet al tipului de stradă";
"try_add_more_address_details" = "Încearcă să adaugi mai multe detalii adresei";
"cannot_find_address" = "Nu se poate găsi adresa";
"please_check_spelling_or_add_details" = "Verifică ortografia sau adaugă detalii";
"cannot_find_address_check_spelling" = "Nu se poate găsi adresa, verifică ortografia";
"address_not_set" = "Adresa nu este setată";
"address_format_incomplete" = "Format adresă incomplet";
"location_service_denied" = "Serviciul de locație refuzat";
"no_saved_groups" = "Nu există grupuri salvate";
"select_points_create_groups" = "Selectați puncte de livrare și creați grupuri pentru gestionare ușoară";
"navigate_to_these_points" = "Navigați la aceste puncte";
"confirm_remove_address" = "Sunteți sigur că doriți să eliminați adresa \"%@\" din grup?";
"confirm_remove_this_address" = "Sunteți sigur că doriți să eliminați această adresă din grup?";
"addresses_count" = "%d adrese";
"no_saved_routes" = "Nu există rute salvate";
"no_saved_routes_description" = "Nu ați salvat încă nicio rută";
"all_routes" = "Toate Rutele";
"address_count_format_simple" = "%d adrese";
"delete_all_routes" = "Șterge Toate Rutele";
"navigate_to_all_points" = "Navigați la toate punctele";
"confirm_navigate_to_route" = "Sunteți sigur că doriți să navigați la toate punctele din ruta \"%@\"?";
"temp_navigation_group" = "Grup de Navigare Temporar";
"route_management" = "Gestionarea Rutelor";
"route_addresses" = "Adresele Rutei";
"no_addresses_in_route" = "Această rută nu are adrese";
"must_keep_one_route" = "Trebuie să păstrați cel puțin o rută";
"confirm_delete_route" = "Sunteți sigur că doriți să ștergeți ruta \"%@\"? Această operațiune nu poate fi anulată.";
"confirm_delete_all_routes" = "Confirmați Ștergerea Tuturor Rutelor";
"confirm_delete_all_routes_message" = "Sunteți sigur că doriți să ștergeți toate rutele? Această operațiune nu poate fi anulată.";
"delete_all" = "Șterge Tot";
"address_information" = "Informații Adresă";
"group_belonging" = "Grupul de Apartenență";
"view_map" = "Vezi Harta";
"delete_delivery_point" = "Șterge Punctul de Livrare";
"delivery_point_details" = "Detalii Punct de Livrare";
"confirm_deletion" = "Confirmă Ștergerea";
"delete_delivery_point_confirmation" = "Sunteți sigur că doriți să ștergeți acest punct de livrare? Această operațiune nu poate fi anulată.";
"delivery_photos" = "Fotografii de Livrare";
"view_delivery_photos" = "Vezi Fotografiile de Livrare";
"no_photos_taken" = "Nu s-au făcut încă fotografii";
"take_photos" = "Fă Fotografii";
"loading_photos" = "Se încarcă fotografiile...";
"photo_not_found" = "Fotografia nu a fost găsită";
"photo_deleted" = "Fotografia a fost ștearsă";
"share_photos" = "Distribuie Fotografia";
"photo_capture_title" = "Confirmarea Fotografiei";
"door_number_photo_title" = "Fotografia Numărului de Drum/Casă";
"package_label_photo_title" = "Fotografia Etichetei Pachetului";
"placement_photo_title" = "Fotografia Locației de Plasare";
"door_number_photo_desc" = "Faceți o fotografie clară a numărului casei, asigurați-vă că cifrele/literele sunt vizibile";
"package_label_photo_desc" = "Faceți o fotografie a etichetei pachetului, asigurați-vă că informațiile destinatarului sunt clar vizibile";
"placement_photo_desc" = "Faceți o fotografie a locației finale de plasare a pachetului";
"swipe_to_switch" = "Glisați pentru a comuta tipul de fotografie";
"photos_will_be_saved_to" = "Fotografiile vor fi salvate în";
"complete_photos" = "Finalizează Fotografiile";
"photo_save_success" = "Fotografia a fost salvată cu succes";
"photo_save_failure" = "Salvarea fotografiei a eșuat";
"no_photos_found" = "Fotografia nu a fost găsită";
"photos_deleted_or_not_taken" = "Fotografia poate fi ștearsă sau nu a fost încă făcută";
"share_photo" = "Distribuie Fotografia";
"photo_capture_preview" = "Modul de previzualizare - simulare cameră";
"photo_capture_close" = "Închide";
"camera_start_failed" = "Pornirea camerei a eșuat";
"camera_start_failed_retry" = "Nu se poate porni camera, încercați din nou";
"camera_init_failed" = "Inițializarea camerei a eșuat";
"camera_access_failed" = "Nu se poate accesa camera";
"photo_processing_failed" = "Fotografierea a eșuat";
"photo_processing_failed_retry" = "Nu se poate finaliza procesarea fotografiei, încercați din nou";
"photo_capture_progress" = "Progres: %d/%d";
"photo_captured_continue" = "Fotografia finalizată, continuați cu %@";
"pending" = "În Așteptarea Livrării";
"in_progress" = "În Curs de Livrare";
"select_delivery_status" = "Selectați Statusul Livrării";
"select_failure_reason" = "Selectați Motivul Eșecului";
"delivery_status_pending" = "În așteptare";
"delivery_status_in_progress" = "În curs";
"delivery_status_completed" = "Finalizat";
"delivery_status_failed" = "Eșuat";
"failure_reason_not_at_home" = "Clientul nu este acasă";
"failure_reason_wrong_address" = "Adresă greșită";
"failure_reason_no_access" = "Nu se poate accesa locația";
"failure_reason_rejected" = "Pachetul a fost refuzat";
"failure_reason_other" = "Alt motiv";
"enter_custom_reason" = "Introduceți motivul specific";
"custom_reason_placeholder" = "Descrieți motivul specific...";
"custom_reason_required" = "Introduceți motivul specific";
"failure_reason_required" = "Selectați motivul eșecului";
"delivery_type_delivery" = "Livrare";
"delivery_type_pickup" = "Ridicare";
"delivery_order_first" = "Primul";
"delivery_order_auto" = "Automat";
"delivery_order_last" = "Ultimul";
"package_size_small" = "Mic";
"package_size_medium" = "Mediu";
"package_size_large" = "Mare";
"package_type_box" = "Cutie";
"package_type_bag" = "Geantă";
"package_type_letter" = "Scrisoare";
"one_click_navigation_grouping" = "Navigare și grupare cu un clic";
"speed_60x_faster" = "60x mai rapid";
"goodbye_manual_address_adding" = "La revedere adăugare manuală adrese";
"watch_detailed_demo" = "Urmărește demo detaliat";
"upgrade_to_pro_now" = "Actualizează la Pro acum";
"free_trial_7_days" = "7 zile încercare gratuită";
"traditional_vs_navibatch_pro" = "Metoda tradițională vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "Glisează pentru a vedea comparația completă";
"traditional_method" = "Metoda tradițională";
"drivers_get_lost_affect_efficiency" = "Șoferii se rătăcesc, afectează eficiența";
"repetitive_operations_waste_time" = "Operațiile repetitive pierd timp";
"total_time_60_seconds" = "Timp total: 60 secunde";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Optimizează rutele, reduce distanța";
"improve_delivery_efficiency_accuracy" = "Îmbunătățește eficiența și acuratețea livrării";
"speed_boost_60x" = "Creștere viteză 60x";
"total_time_1_second" = "Timp total: 1 secundă";
"time_comparison" = "Comparație timp";
"traditional_method_problems" = "Probleme metoda tradițională";
"each_address_3_5_seconds_14_total_60" = "Fiecare adresă 3-5 secunde, 14 adrese total 60 secunde";
"repetitive_operations_cause_fatigue" = "Operațiile repetitive cauzează oboseală";
"address_order_reversed_last_becomes_first" = "Ordinea adreselor inversată, ultimul devine primul";
"need_manual_reverse_adding_takes_longer" = "Necesită inversare manuală, adăugarea durează mai mult";
"navibatch_advantages" = "Avantajele NaviBatch";
"add_14_addresses_1_second_60x_faster" = "Adaugă 14 adrese în 1 secundă, 60x mai rapid";
"auto_maintain_correct_order_no_adjustment" = "Menține automat ordinea corectă, fără ajustare";
"zero_error_rate_no_repetition" = "Rată zero erori, fără repetare";
"save_59_seconds" = "Economisește 59 secunde";
"speed_boost_60x_simple" = "Creștere viteză 60x";
"seconds_format" = "%d secunde";
"actual_benefits_one_click_navigation" = "Beneficii reale navigare cu un clic";
"daily_savings" = "Economii zilnice";
"daily_savings_value" = "59 secunde";
"daily_savings_description" = "Economisește 59 secunde pentru fiecare 14 adrese";
"monthly_savings" = "Economii lunare";
"monthly_savings_value" = "30 minute";
"monthly_savings_description" = "Bazat pe 30 rute pe lună";
"fuel_savings_value" = "30%";
"fuel_savings_description" = "Optimizarea rutelor reduce consumul de combustibil";
"income_increase" = "Creșterea veniturilor";
"income_increase_value" = "15%";
"income_increase_description" = "Mai multe livrări pe zi = mai multe venituri";
"trial" = "Încercare";
"days_left" = "zile rămase";
"free_plan_description" = "Plan gratuit - Până la 20 adrese";
"pro_plan_active" = "Plan Pro activ";
"expert_plan_active" = "Plan Expert activ";
"trial_active" = "Încercare activă";
"trial_expires_on" = "Încercarea expiră pe %@";
"address_validation_mode" = "Mod validare adrese";
"validation_description" = "Controlează strictețea validării adreselor";
"current_settings" = "Setări curente";
"validation_mode_format" = "Mod: %@";
"threshold_score_format" = "Prag: %.1f";
"validation_example" = "Exemplu validare";
"original_address_example" = "Adresa originală: 123 Main St";
"reverse_address_example" = "Adresa inversă: 125 Main St";
"house_number_difference" = "Diferența numărului casei: 2";
"result_label" = "Rezultat:";
"may_pass_warning" = "Poate trece (avertisment)";
"will_not_pass" = "Nu va trece";
"real_case_example" = "Exemplu caz real";
"real_case_description" = "Bazat pe date reale de validare adrese";
"address_validation_settings" = "Setări validare adrese";
"clear" = "Șterge";
"view_details" = "Vezi detalii";
"create_test_data" = "Creează date test";
"manual_snapshot" = "Captură manuală";
"start_location_updates" = "Pornește actualizări locație";
"stop_location_updates" = "Oprește actualizări locație";
"user_location_marker_test" = "Test marcator locație utilizator";
"location_animation_control" = "Control animație locație";
"current_location_format" = "Locația curentă: %.6f, %.6f";
"waiting_for_location" = "Se așteaptă locația...";
"diagnostic_tools" = "Instrumente diagnostic";
"storekit_diagnostics" = "Diagnostice StoreKit";
"subscription_function_test" = "Test funcție abonament";
"localization_test" = "Test localizare";
"address_validation_demo" = "Demo validare adrese";
"localization_tools" = "Instrumente localizare";
"coordinate_debug_tools" = "Instrumente debug coordonate";
"smart_abbreviation_expansion_test" = "Test extindere inteligentă abrevieri";
"subscription_restore_diagnostics" = "Diagnostice restaurare abonament";
"batch_address_import_test" = "Test import adrese în lot";
"test_import_1000_addresses_memory" = "Test: importă 1000 adrese (memorie)";
"map_rendering_test" = "Test randare hartă";
"test_map_display_markers_memory" = "Test: afișează marcatori pe hartă (memorie)";
"select_test_language" = "Selectează limba test";
"discover_60x_speed_boost" = "Descoperă creșterea vitezei 60x";
"see_60x_speed_demo" = "Vezi demo viteză 60x";
"free_vs_pro_comparison" = "Comparație gratuit vs Pro";
"our_free_beats_competitors_paid" = "Planul nostru gratuit întrece planurile plătite ale concurenților";
"features" = "Caracteristici";
"up_to_20" = "Până la 20";
"unlimited" = "Nelimitat";
"smart_optimization" = "Optimizare inteligentă";
"up_to_20_percent" = "Până la 20%";
"file_not_found" = "Fișier negăsit";
"sample_file_not_available" = "Fișierul exemplu nu este disponibil";
"file_copy_failed" = "Copierea fișierului a eșuat";
