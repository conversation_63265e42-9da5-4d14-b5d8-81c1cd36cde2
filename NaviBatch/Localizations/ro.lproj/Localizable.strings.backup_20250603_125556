/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Setări Limbă";
"system_language" = "Limba Sistemului";
"system_language_section" = "Setări Sistem";
"languages" = "Limbă";
"language_info_title" = "Despre Setările de Limbă";
"language_info_description" = "După modificarea setărilor de limbă, aplicația va afișa textul în limba selectată. Unele conținuturi pot necesita repornirea aplicației pentru a aplica complet noile setări de limbă.";
"restart_required" = "Repornirea Aplicației Este Necesară";
"restart_app_message" = "Pentru a aplica complet modificările de limbă, reporniți aplicația.";
"restart_now" = "Repornește Acum";
"restart_later" = "Repornește Mai Târziu";

// MARK: - Common UI Elements
"close" = "Închide";
"cancel" = "Anulează";
"save" = "Salvează";
"edit" = "Editează";
"delete" = "Șterge";
"done" = "Finalizat";
"next" = "Următorul";
"back" = "Înapoi";
"confirm" = "Confirmă";
"error" = "Eroare";
"success" = "Succes";
"warning" = "Avertisment";
"loading" = "Se încarcă...";
"search" = "Căutare";
"settings" = "Setări";
"help" = "Ajutor";
"about" = "Despre";
"menu" = "Meniu";
"understand" = "Înțeleg";

// MARK: - Navigation
"navigation" = "Navigare";
"start_navigation" = "Începe Navigarea";

// MARK: - Subscription
"subscription" = "Abonament";
"upgrade_to_pro" = "Actualizează la Pro";
"upgrade_description" = "Gruparea navigației cu un clic, de 60 de ori mai rapidă decât operarea manuală";
"restore_purchases" = "Restaurează Achizițiile";
"learn_more" = "Află Mai Multe";
"upgrade_your_plan" = "Actualizează-ți Planul";
"one_click_navigation_description" = "Gruparea adreselor de navigare cu un clic, economisește timp și combustibil";
"current_plan" = "Planul Actual";
"upgrade" = "Actualizează";
"maybe_later" = "Poate Mai Târziu";
"free_tier_name" = "Începător";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratuit";
"pro_tier_price" = "$29.99/lună";
"expert_tier_price" = "$249.99/an";
"free_tier_description" = "Potrivit pentru persoane fizice și întreprinderi mici, nu necesită multe opriri";
"pro_tier_description" = "Potrivit pentru utilizatorii care au nevoie de gruparea navigației cu un clic și adrese nelimitate";
"expert_tier_description" = "La fel ca Pro, dar economisește 31% prin planul anual";
"route_optimization" = "Optimizarea Rutei";
"unlimited_routes" = "Rute Nelimitate";
"unlimited_optimizations" = "Optimizări Nelimitate";
"max_15_addresses" = "Maxim 15 adrese pe rută";
"save_fuel_30" = "Economisește până la 30% combustibil";
"unlimited_addresses" = "✨ Adrese Nelimitate ✨";
"one_click_navigation" = "⚡ Gruparea Navigației cu Un Clic - De 60x Mai Rapid ⚡";
"package_finder" = "Localizator de Pachete";
"annual_savings" = "Economii Plan Anual";
"switched_to_free" = "Trecut la Planul Gratuit";
"switched_to_subscription" = "Trecut la Planul de Abonament";
"unlimited_stops" = "Opriri Nelimitate";
"plan_as_many_stops_as_needed" = "Adaugă orice număr de puncte de livrare fără limitări";

// MARK: - Address Input
"enter_or_search_address" = "Introduceți sau căutați adresa";
"search_results_count" = "Rezultate căutare: %d";
"no_matching_addresses" = "Nu s-au găsit adrese corespunzătoare";
"search_address_failed" = "Căutarea adresei a eșuat: %@";
"address_search_no_response" = "Căutarea adresei nu răspunde";
"cannot_get_address_coordinates" = "Nu se pot obține coordonatele adresei";
"speech_recognizer_unavailable" = "Recunoașterea vocală indisponibilă";
"microphone_permission_denied" = "Permisiunea microfonului nu a fost acordată";
"speech_recognition_permission_denied" = "Permisiunea de recunoaștere vocală nu a fost acordată";
"listening" = "Ascultă...";
"recording_failed" = "Începerea înregistrării a eșuat: %@";
"cannot_get_coordinates_retry" = "Nu se pot obține coordonatele adresei, introduceți manual sau încercați din nou";
"cannot_create_recognition_request" = "Nu se poate crea cererea de recunoaștere";

// MARK: - Saved Address Picker
"search_address" = "Căutare Adresă";
"no_saved_addresses" = "Nicio Adresă Salvată";
"no_saved_addresses_description" = "Nu ați salvat încă nicio adresă sau nu există adrese care să îndeplinească criteriile de filtrare";
"select_address_book" = "Selectați Agenda de Adrese";

// MARK: - Menu
"menu" = "Meniu";
"routes" = "Rută";
"address_book" = "Agenda de Adrese";
"saved_routes" = "Rute Salvate";
"manage_your_routes" = "Gestionați planificarea rutelor";
"manage_your_addresses" = "Gestionați adresele folosite frecvent";
"settings" = "Setări";
"preferences" = "Preferințe";
"set_custom_start_point" = "Setați Punctul de Pornire Personalizat";
"current_start_point" = "Punctul de pornire actual: %@";
"support" = "Suport";
"contact_us" = "Contactați-ne";
"contact_us_description" = "Aveți întrebări sau sugestii? Nu ezitați să ne contactați!";
"help_center" = "Centrul de Ajutor";
"subscription" = "Abonament";
"upgrade_to_pro" = "Actualizează la Pro";
"upgrade_description" = "Gruparea navigației cu un clic, de 60 de ori mai rapidă decât operarea manuală";
"open_subscription_view" = "Deschide Direct Vizualizarea Abonamentului";
"open_subscription_view_description" = "Omite stratul intermediar, afișează direct SubscriptionView";
"restore_purchases_failed" = "Restaurarea achizițiilor a eșuat: %@";
"about" = "Despre";
"rate_us" = "Evaluați-ne";
"rate_us_description" = "Feedback-ul dvs. este important pentru noi și ne ajută să îmbunătățim aplicația!";
"share_app" = "Distribuie Aplicația";
"share_app_text" = "Încercați NaviBatch, o aplicație uimitoare de planificare a rutelor!";
"about_app" = "Despre Aplicație";
"developer_tools" = "Instrumente pentru Dezvoltatori";
"coordinate_debug_tool" = "Instrument de Debug Coordonate";
"batch_fix_addresses" = "Corectare Adrese în Lot";
"clear_database" = "Șterge Baza de Date";
"clear_database_confirmation" = "Aceasta va șterge toate datele, inclusiv rutele, adresele și grupurile. Această acțiune nu poate fi anulată. Sunteți sigur că doriți să continuați?";
"confirm_clear" = "Confirmă Ștergerea";
"version_info" = "Versiunea %@ (%@)";
"current_system_language" = "Limba Actuală a Sistemului";
"reset_to_system_language" = "Resetează la Limba Sistemului";
"language" = "Limbă";
"language_settings" = "Setări Limbă";

// MARK: - Address Edit
"address" = "Adresă";
"coordinates" = "Coordonate";
"distance_from_current_location" = "Distanța de la Locația Actuală";
"address_info" = "Informații Adresă";
"update_coordinates" = "Actualizează Coordonatele";
"fix_address" = "Corectează Adresa";
"prompt" = "Prompt";
"confirm" = "Confirmă";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Vă rugăm să modificați adresa înainte de a actualiza coordonatele";
"coordinates_update_success" = "Coordonatele au fost actualizate cu succes";
"coordinates_update_failure" = "Actualizarea coordonatelor a eșuat";
"save_failure" = "Salvarea a eșuat: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Nicio Adresă Salvată";
"no_saved_addresses_message" = "Nu ați salvat încă nicio adresă";
"add_new_address" = "Adaugă Adresă Nouă";
"address_title" = "Adresă";
"add" = "Adaugă";
"refresh" = "Reîmprospătează";
"notes" = "Note";
"address_details" = "Detalii Adresă";
"favorite" = "Favorit";
"edit_address" = "Editează Adresa";
"cancel" = "Anulează";
"save" = "Salvează";
"confirm_delete" = "Confirmă Ștergerea";
"delete" = "Șterge";
"delete_address_confirmation" = "Sunteți sigur că doriți să ștergeți această adresă? Această operațiune nu poate fi anulată.";
"edit" = "Editează";
"address_marker" = "Adresă";
"address_label" = "Adresă:";
"notes_label" = "Note:";
"created_at_label" = "Ora creării:";
"open_in_maps" = "Deschide în Hartă";
"copy_address" = "Copiază Adresa";
"address_details_title" = "Detalii Adresă";

// MARK: - Route Detail
"start_end_point" = "Punct de Început/Sfârșit";
"start_point" = "Punct de Pornire";
"end_point" = "Punct Final";
"route_info" = "Informații Rută";
"address_count" = "Numărul de Adrese";
"address_count_format" = "%d adrese";
"points_count_format" = "%d puncte";
"additional_points_format" = "+%d puncte";
"export_route" = "Exportă Ruta";
"navigate" = "Navighează";
"address_list" = "Lista de Adrese";
"no_addresses" = "Nicio Adresă";
"no_addresses_message" = "Această rută nu are încă adrese";

// MARK: - Route Bottom Sheet
"address_point_start" = "Punct de Pornire";
"address_point_stop" = "Punct de Oprire";
"address_point_end" = "Punct Final";
"route_name" = "Numele Rutei";
"save" = "Salvează";
"new_route" = "Rută Nouă";
"saved_route" = "Rută Salvată";
"edit" = "Editează";
"loading" = "Se încarcă...";
"plan_route" = "Planifică Ruta";
"clear_all" = "Șterge Tot";
"avoid" = "Evită:";
"toll_roads" = "Drumuri cu Taxă";
"highways" = "Autostrăzi";
"processing_addresses" = "Procesarea adreselor...";
"same_start_end_point" = "Ați setat aceeași adresă ca punct de pornire și de sosire";
"add_start_point" = "Adaugă Punct de Pornire";
"swipe_left_to_delete" = "← Glisați la stânga pentru a șterge";
"delete" = "Șterge";
"add_new_address" = "Adaugă Adresă Nouă";
"add_end_point" = "Adaugă Punct Final";

// MARK: - Simple Address Sheet
"address_title" = "Adresă";
"enter_and_select_address" = "Introduceți și selectați adresa";
"current_search_text" = "Textul de căutare actual: %@";
"search_results_count" = "Rezultate căutare: %d";
"no_matching_addresses" = "Nu s-au găsit adrese corespunzătoare";
"add_address" = "Adaugă Adresă";
"edit_address" = "Editează Adresa";
"selected_coordinates" = "Coordonate Selectate";
"company_name_optional" = "Numele Companiei (Opțional)";
"url_optional" = "URL (Opțional)";
"favorite_address" = "Adresă Favorită";
"set_as_start_and_end" = "Setează ca Punct de Pornire și de Sosire";
"address_book" = "Agenda de Adrese";
"batch_paste" = "Lipire în Lot";
"file_import" = "Import Fișier";
"web_download" = "Descărcare Web";
"cancel" = "Anulează";
"saving" = "Se salvează...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Gestionarea Punctelor de Livrare";
"information_category" = "Categoria Informațiilor";
"address_info" = "Informații Adresă";
"package_info" = "Informații Pachet";
"vehicle_position" = "Poziția Vehiculului";
"delivery_info" = "Informații Livrare";
"navigation" = "Navigare";
"take_photo_record" = "Fă Înregistrare Foto";
"update_status" = "Actualizează Statusul";
"done" = "Finalizat";
"edit_address_button" = "Editează Adresa";
"coordinates" = "Coordonate";
"access_instructions" = "Instrucțiuni de Acces";
"add_access_instructions" = "Adaugă instrucțiuni de acces...";
"package_count" = "Numărul de Pachete";
"package_size" = "Mărimea Pachetului";
"package_type" = "Tipul Pachetului";
"mark_as_important" = "Marchează ca Important";
"select_package_position" = "Selectați Poziția Pachetului în Vehicul";
"vehicle_area" = "Zona Vehiculului";
"left_right_position" = "Poziția Stânga/Dreapta";
"vehicle_position_front" = "Față";
"vehicle_position_middle" = "Mijloc";
"vehicle_position_back" = "Spate";
"vehicle_position_left" = "Stânga";
"vehicle_position_right" = "Dreapta";
"vehicle_position_floor" = "Partea de Jos";
"vehicle_position_shelf" = "Partea de Sus";
"height_position" = "Poziția pe Înălțime";
"delivery_type" = "Tipul de Livrare";
"delivery_status" = "Statusul Livrării";
"order_info" = "Informații Comandă";
"order_information" = "Informații Comandă";
"order_number" = "Numărul Comenzii";
"enter_order_number" = "Introduceți numărul comenzii";
"tracking_number" = "Numărul de Urmărire";
"enter_tracking_number" = "Introduceți numărul de urmărire";
"time_info" = "Informații Timp";
"time_information" = "Informații Timp";
"estimated_arrival_time" = "Timpul Estimat de Sosire";
"anytime" = "Oricând";
"stop_time" = "Timpul de Oprire";
"minutes_format" = "%d minute";
"photo_record" = "Înregistrare Foto";
"door_number_photo" = "Foto Numărul Ușii";
"package_label_photo" = "Foto Eticheta Pachetului";
"placement_photo" = "Foto Plasare";
"door_number_desc" = "Faceți o fotografie clară a numărului casei sau străzii, asigurați-vă că cifrele/literele sunt vizibile";
"package_label_desc" = "Faceți o fotografie a etichetei pachetului, asigurați-vă că informațiile destinatarului sunt clar vizibile";
"placement_desc" = "Faceți o fotografie a locației finale de plasare a pachetului";
"photo_captured" = "Fotografia a fost făcută";
"photo_captured_options" = "Fotografia a fost făcută, doriți să continuați cu următoarea fotografie sau să finalizați fotografia actuală?";
"continue_to_next_photo" = "Continuă cu următoarea - %@";
"retake" = "Refă Fotografia";
"tap_to_capture" = "Atingeți pentru a Fotografia";
"flash_auto" = "Flash Automat";
"flash_on" = "Activează Flash-ul";
"flash_off" = "Dezactivează Flash-ul";
"photo_record_completed" = "Înregistrarea Foto Finalizată";
"photo_confirmation" = "Confirmarea Fotografiei";
"error" = "Eroare";
"ok" = "OK";
"complete_photo_capture" = "Finalizează Fotografiile";
"tap_to_capture" = "Atingeți pentru a Fotografia";
"photo_instructions" = "Atingeți fiecare card de fotografie pentru a fotografia. Toate fotografiile trebuie finalizate.";
"photo_options" = "Opțiuni Fotografie";
"view_photo" = "Vezi Fotografia";
"retake_photo" = "Refă Fotografia";
"saving_photos" = "Se salvează fotografiile...";
"completed" = "Finalizat";
"not_taken" = "Nu a Fost Făcută";
"route_options" = "Opțiuni Rută";
"avoid_tolls" = "Evită Drumurile cu Taxă";
"avoid_highways" = "Evită Autostrăzile";
"optimize_route" = "Optimizează Ruta";
"optimizing" = "Se optimizează...";
"optimization_complete" = "Optimizarea Finalizată";

// MARK: - Addresses
"addresses" = "Adresă";
"add_address" = "Adaugă Adresă";
"edit_address" = "Editează Adresa";
"delete_address" = "删除地址";
"address_details" = "Detalii Adresă";
"street" = "Stradă";
"city" = "Oraș";
"state" = "Stat/Provincie";
"country" = "Țară";
"postal_code" = "Cod Poștal";
"phone" = "Telefon";
"email" = "E-mail";
"website" = "Site Web";
"company" = "Companie";
"notes" = "Note";
"coordinates" = "Coordonate";
"latitude" = "Latitudine";
"longitude" = "Longitudine";
"geocoding_error" = "Eroare de Geocodificare";
"address_validation" = "Validarea Adresei";
"invalid_addresses" = "Adresă Invalidă";
"fix_addresses" = "Corectează Adresa";

// MARK: - Routes
"route" = "Rută";
"routes" = "Rută";
"select_address_point" = "Selectați Punctul de Adresă";
"select_delivery_points" = "Selectați Punctele de Livrare";
"create_delivery_route" = "Creați Ruta de Livrare";
"view_saved_routes" = "Vezi Rutele Salvate";
"create_route" = "Creați Ruta";
"edit_route" = "Editează Ruta";
"delete_route" = "Șterge Ruta";
"route_name" = "Numele Rutei";
"route_details" = "Detalii Rută";
"selected_addresses" = "%d adrese selectate";
"reached_limit" = "Limita Atinsă";
"can_select_more" = "Mai puteți selecta %d";
"navigate_button" = "Navigare";
"create_group" = "Creați Grup";
"start_point" = "Punct de Pornire";
"end_point" = "Punct Final";
"waypoints" = "Puncte de Trecere";
"total_distance" = "Distanța Totală";
"estimated_time" = "Timpul Estimat";
"route_summary" = "Rezumatul Rutei";
"route_options" = "Opțiuni Rută";
"route_saved" = "Rută Salvată";
"route_optimized" = "Rută Optimizată";
"optimizing_route" = "Se optimizează ruta...";
"completed_percent" = "Finalizat %d%%";
"processing_points" = "Se procesează: %d/%d";
"estimated_remaining_time" = "Timpul Estimat Rămas: %@";

// MARK: - Delivery
"delivery" = "Livrare";
"delivery_confirmation" = "Confirmarea Livrării";
"take_photo" = "Fă Fotografie";
"signature" = "Semnătură";
"delivery_notes" = "Note Livrare";
"delivery_status" = "Statusul Livrării";
"delivered" = "Livrat";
"not_delivered" = "Nelivrat";
"delivery_time" = "Timpul Livrării";
"delivery_date" = "Data Livrării";
"package_details" = "Detalii Pachet";
"package_id" = "ID Pachet";
"package_weight" = "Greutatea Pachetului";
"package_dimensions" = "Dimensiunile Pachetului";
"recipient_name" = "Numele Destinatarului";
"recipient_phone" = "Telefonul Destinatarului";

// MARK: - Groups
"groups" = "Grup";
"saved_groups" = "Grupuri Salvate";
"create_group" = "Creați Grup";
"edit_group" = "Editează Grupul";
"delete_group" = "Șterge Grupul";
"group_name" = "Numele Grupului";
"group_details" = "Detalii Grup";
"auto_grouping" = "Grupare Automată";
"group_by" = "Grupează După";
"add_to_group" = "Adaugă la Grup";
"remove_from_group" = "Elimină din Grup";
"group_created" = "Grup Creat";
"default_group_name_format" = "Grup%d";
"auto_grouping_completed" = "Gruparea Automată Finalizată";
"auto_grouping_in_progress" = "Gruparea Automată în Curs...";
"create_group_every_14_addresses" = "Creați un grup pentru fiecare 14 adrese";
"create_delivery_group" = "Creați Grup de Livrare";
"enter_group_name" = "Introduceți numele grupului";
"selected_delivery_points" = "Puncte de livrare selectate";
"drag_to_adjust_order" = "Trageți pentru a ajusta ordinea";

// MARK: - Subscription
"subscription" = "Abonament";
"free_plan" = "Versiunea Gratuită";
"pro_plan" = "Versiunea Pro";
"expert_plan" = "Versiunea Expert";
"monthly" = "Plan Lunar";
"yearly" = "Plan Anual";
"subscribe" = "Abonament";
"upgrade" = "Actualizează";
"upgrade_to_pro" = "Actualizează la Pro";
"manage_subscription" = "Gestionează Abonamentul";
"restore_purchases" = "Restaurează Achizițiile";
"subscription_benefits" = "Beneficiile Abonamentului";
"free_trial" = "Încercare Gratuită";
"price_per_month" = "%@ pe lună";
"price_per_year" = "%@ pe an";
"save_percent" = "Economisește %@%";
"current_plan" = "Planul Actual";
"subscription_terms" = "Termenii Abonamentului";
"privacy_policy" = "Politica de Confidențialitate";
"terms_of_service" = "Termenii de Serviciu";

// MARK: - Import/Export
"import" = "Import";
"export" = "Export";
"import_addresses" = "Importă Adrese";
"export_addresses" = "Exportă Adrese";
"import_from_file" = "Importă din Fișier";
"export_to_file" = "Exportă în Fișier";
"file_format" = "Formatul Fișierului";
"csv_format" = "Format CSV";
"excel_format" = "Format Excel";
"json_format" = "Format JSON";
"import_success" = "Au fost importate cu succes %d adrese, toate cu coordonate valide.";
"export_success" = "Export Reușit";
"import_error" = "Eroare Import";
"export_error" = "Eroare Export";

// MARK: - Navigation
"navigate" = "Navighează";

// MARK: - Look Around
"show_look_around" = "Vezi Imaginea Străzii";
"hide_look_around" = "Ascunde Imaginea Străzii";

// MARK: - Map
"map" = "Hartă";
"map_type" = "Tipul Hărții";
"standard" = "Standard";
"satellite" = "Satelit";
"hybrid" = "Hibrid";
"show_traffic" = "Afișează Traficul";
"current_location" = "Locația Actuală";
"directions" = "Indicații Rută";
"distance_to" = "Distanță";
"eta" = "Timpul Estimat de Sosire";
"look_around" = "Privește în Jur";
"locating_to_glen_waverley" = "Localizare la Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Eroare de Rețea";
"location_error" = "Eroare de Locație";
"permission_denied" = "Permisiunea Refuzată";
"location_permission_required" = "Permisiunea de Locație Necesară";
"camera_permission_required" = "Permisiunea Camerei Necesară";
"photo_library_permission_required" = "Permisiunea Bibliotecii de Fotografii Necesară";
"please_try_again" = "Încercați Din Nou";
"something_went_wrong" = "A Apărut o Eroare";
"invalid_input" = "Intrare Invalidă";
"required_field" = "Câmp Obligatoriu";
"no_internet_connection" = "Fără Conexiune la Internet";
"server_error" = "Eroare Server";
"timeout_error" = "Timpul Cererii Expirat";
"data_not_found" = "Datele Nu Au Fost Găsite";
"selection_limit_reached" = "Limita de Selecție Atinsă";
"selection_limit_description" = "Puteți selecta maximum %d adrese, ați selectat %d";

// MARK: - Location Validation Status
"location_status_valid" = "Interval Valid";
"location_status_warning" = "Interval de Avertisment";
"location_status_invalid" = "Locație Invalidă";
"location_status_unknown" = "Status Necunoscut";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Invalid: Coordonate zero (0,0)";
"coordinates_invalid_nan" = "Invalid: Coordonate non-numerice";
"coordinates_out_of_range" = "Invalid: Coordonate în afara intervalului valid";
"coordinates_far_from_user" = "Avertisment: Locația este departe de poziția dvs. actuală";
"coordinates_ocean" = "Avertisment: Locația poate fi în ocean sau zonă nelocuită";

// MARK: - Batch Address Input
"batch_add_addresses" = "Adaugă Adrese în Lot";
"batch_address_input_placeholder" = "Introduceți sau lipiți adrese, o adresă pe linie. Maxim 35 de adrese.";
"free_address_limit" = "Limita Adreselor Versiunii Gratuite";
"address_count_limit" = "Limita Numărului de Adrese";
"free_version_max_addresses" = "Versiunea gratuită permite maximum %d adrese.";
"current_addresses_remaining" = "În prezent aveți %d adrese, puteți adăuga doar încă %d adrese.";
"current_route_address_limit" = "Ruta actuală are %d adrese, puteți adăuga doar încă %d adrese, total maxim %d adrese.";
"selected_addresses_can_import" = "Ați selectat %d adrese, puteți importa aceste adrese.";
"selected_addresses_exceeds" = "Ați selectat %d adrese, depășind numărul permis cu %d.";
"selected_addresses_all_importable" = "Ați selectat %d adrese, toate pot fi importate.";
"upgrade_for_unlimited_addresses" = "Actualizați la premium pentru adrese nelimitate!";
"import_first_n_addresses" = "Importă doar primele %d";
"import_all_addresses" = "Importă Toate Adresele";
"import_selected_addresses" = "Importă adresele selectate";
"no_importable_addresses" = "Nu există adrese de importat, verificați limitele adreselor";
"please_enter_valid_address" = "Introduceți cel puțin o adresă validă";

// MARK: - File Import
"import_success" = "Au fost importate cu succes %d adrese, toate cu coordonate valide.";
"import_success_with_warnings" = "A importat cu succes %d adrese, %d adrese cu coordonate normale, %d adrese cu avertismente.\n\nAdresele cu avertismente au fost marcate și pot fi reparate manual după import.";

// MARK: - Web Download
"web_download" = "Descărcare Web";
"supported_formats" = "Formate Suportate";
"supported_format_csv" = "• Fișiere CSV: Coloana de adrese trebuie să conțină adrese complete";
"supported_format_json" = "• Date JSON: Array care conține câmpuri de adrese";
"supported_format_text" = "• Text simplu: O adresă pe linie";
"download_history" = "Istoricul Descărcărilor";
"upgrade_to_premium" = "Actualizează la Premium";
"input_address_data_url" = "Introduceți URL-ul datelor de adrese";
"import_result" = "Rezultatul Importului";
"import_addresses" = "Importă Adrese";
"downloading" = "Se descarcă...";
"processing_data" = "Se procesează datele...";
"google_drive_download_failed" = "Descărcarea Google Drive a eșuat";
"second_attempt_invalid_data" = "A doua încercare de descărcare a returnat date invalide";
"cannot_parse_json" = "Nu se pot analiza datele JSON, verificați formatul fișierului";
"cannot_parse_json_with_error" = "Nu se pot analiza datele JSON: %@";
"cannot_get_address_coordinates" = "Nu se pot obține coordonatele adresei";
"cannot_read_file" = "Nu se poate citi fișierul: %@";
"success" = "Succes";
"warning" = "Avertisment";
"failed" = "Livrarea a Eșuat";
"no_matching_addresses" = "Nu s-au găsit adrese corespunzătoare";
"no_valid_addresses" = "Nu s-au găsit adrese valide";
"confirm" = "Confirmă";
"processing_addresses" = "Procesarea adreselor...";
"supports_file_types" = "Suportă fișiere CSV, TXT și JSON";
"tap_to_select_file" = "Atingeți pentru a selecta fișierul";
"import_addresses" = "Importă Adrese";
"company_name_optional" = "Numele Companiei (Opțional)";
"input_company_name" = "Introduceți numele companiei (opțional)";
"imported_addresses_count" = "Au fost importate %d adrese";
"select_all" = "Selectează Tot";
"excel_format_not_supported" = "Formatul Excel nu este suportat";
"no_matching_addresses" = "Nu s-au găsit adrese corespunzătoare";

// MARK: - Import Limits
"import_failed" = "Importul a Eșuat";
"no_importable_addresses" = "Nu există adrese de importat, verificați limitele adreselor";
"free_version_address_limit" = "Versiunea gratuită permite maximum %d adrese.";
"current_address_count" = "În prezent aveți %d adrese, puteți adăuga doar încă %d adrese.";
"can_import_selected" = "Ați selectat %d adrese, puteți importa aceste adrese.";
"selected_exceeds_limit" = "Ați selectat %d adrese, depășind numărul permis cu %d.";
"upgrade_to_premium_unlimited" = "Actualizați la premium pentru adrese nelimitate!";
"route_address_limit" = "Ruta actuală are %d adrese, puteți adăuga doar încă %d adrese, total maxim %d adrese.";
"free_version_limit" = "Limita Adreselor Versiunii Gratuite";
"address_count_limit" = "Limita Numărului de Adrese";
"import_selected_addresses" = "Importă adresele selectate";
"import_first_n" = "Importă doar primele %d";
"import_all_n" = "Importă toate %d";
"cannot_import" = "Nu se poate importa";
"select_at_least_one" = "Selectați cel puțin o adresă";

// MARK: - Import Results
"no_valid_addresses_found" = "Nu s-au găsit adrese valide";
"import_success_all_valid" = "Au fost importate cu succes %d adrese, toate coordonatele adreselor sunt normale.";
"import_success_some_warnings" = "Au fost importate cu succes %d adrese, %d adrese cu coordonate normale, %d adrese nu pot obține coordonate.";

// MARK: - Warnings
"invalid_csv_row" = "Linie CSV invalidă";
"distance_warning" = "Distanța depășește 200km de la locația actuală";
"not_in_australia" = "Coordonatele nu sunt în intervalul Australiei";
"cannot_get_coordinates" = "Nu se pot obține coordonatele adresei";
"empty_address" = "Adresă goală";
"invalid_address_data" = "Date de adresă invalide";

// MARK: - Saved Groups
"saved_groups" = "Grupuri Salvate";
"no_saved_groups" = "Nu există grupuri salvate";
"select_points_create_groups" = "Selectați puncte de livrare și creați grupuri pentru gestionare ușoară";
"group_name" = "Numele Grupului";
"group_details" = "Detalii Grup";
"navigate_to_these_points" = "Navigați la aceste puncte";
"confirm_remove_address" = "Sunteți sigur că doriți să eliminați adresa \"%@\" din grup?";
"confirm_remove_this_address" = "Sunteți sigur că doriți să eliminați această adresă din grup?";
"addresses_count" = "%d adrese";
"no_saved_routes" = "Nu există rute salvate";
"no_saved_routes_description" = "Nu ați salvat încă nicio rută";
"all_routes" = "Toate Rutele";
"address_count_format_simple" = "%d adrese";
"delete_all_routes" = "Șterge Toate Rutele";
"navigate_to_all_points" = "Navigați la toate punctele";
"confirm_navigate_to_route" = "Sunteți sigur că doriți să navigați la toate punctele din ruta \"%@\"?";
"temp_navigation_group" = "Grup de Navigare Temporar";

// MARK: - Route Management
"route_management" = "Gestionarea Rutelor";
"route_info" = "Informații Rută";
"route_name" = "Numele Rutei";
"route_addresses" = "Adresele Rutei";
"no_addresses_in_route" = "Această rută nu are adrese";
"must_keep_one_route" = "Trebuie să păstrați cel puțin o rută";
"confirm_delete_route" = "Sunteți sigur că doriți să ștergeți ruta \"%@\"? Această operațiune nu poate fi anulată.";
"confirm_delete_all_routes" = "Confirmați Ștergerea Tuturor Rutelor";
"confirm_delete_all_routes_message" = "Sunteți sigur că doriți să ștergeți toate rutele? Această operațiune nu poate fi anulată.";
"delete_all" = "Șterge Tot";

// MARK: - Navigation Buttons
"navigate" = "Navighează";

// MARK: - GroupDetailView
"points_count_format" = "%d puncte";

// MARK: - DeliveryPointDetailView
"address_information" = "Informații Adresă";
"group_belonging" = "Grupul de Apartenență";
"view_map" = "Vezi Harta";
"delivery_status" = "Statusul Livrării";
"notes" = "Note";
"delete_delivery_point" = "Șterge Punctul de Livrare";
"delivery_point_details" = "Detalii Punct de Livrare";
"confirm_deletion" = "Confirmă Ștergerea";
"delete_delivery_point_confirmation" = "Sunteți sigur că doriți să ștergeți acest punct de livrare? Această operațiune nu poate fi anulată.";

// MARK: - Delivery Photos
"delivery_photos" = "Fotografii de Livrare";
"view_delivery_photos" = "Vezi Fotografiile de Livrare";
"no_photos_taken" = "Nu s-au făcut încă fotografii";
"take_photos" = "Fă Fotografii";
"loading_photos" = "Se încarcă fotografiile...";
"photo_not_found" = "Fotografia nu a fost găsită";
"photo_deleted" = "Fotografia a fost ștearsă";
"door_number_photo" = "Foto Numărul Ușii";
"package_label_photo" = "Foto Eticheta Pachetului";
"placement_photo" = "Foto Plasare";
"share_photos" = "Distribuie Fotografia";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Confirmarea Fotografiei";
"door_number_photo_title" = "Fotografia Numărului de Drum/Casă";
"package_label_photo_title" = "Fotografia Etichetei Pachetului";
"placement_photo_title" = "Fotografia Locației de Plasare";
"door_number_photo_desc" = "Faceți o fotografie clară a numărului casei, asigurați-vă că cifrele/literele sunt vizibile";
"package_label_photo_desc" = "Faceți o fotografie a etichetei pachetului, asigurați-vă că informațiile destinatarului sunt clar vizibile";
"placement_photo_desc" = "Faceți o fotografie a locației finale de plasare a pachetului";
"swipe_to_switch" = "Glisați pentru a comuta tipul de fotografie";
"complete_photos" = "Finalizează Fotografiile";
"saving_photos" = "Se salvează fotografiile...";
"photo_save_success" = "Fotografia a fost salvată cu succes";
"photo_save_failure" = "Salvarea fotografiei a eșuat";
"retake_photo" = "Refă Fotografia";
"no_photos_found" = "Fotografia nu a fost găsită";
"photos_deleted_or_not_taken" = "Fotografia poate fi ștearsă sau nu a fost încă făcută";
"share_photo" = "Distribuie Fotografia";
"photo_capture_preview" = "Modul de previzualizare - simulare cameră";
"photo_capture_close" = "Închide";
"camera_start_failed" = "Pornirea camerei a eșuat";
"camera_start_failed_retry" = "Nu se poate porni camera, încercați din nou";
"camera_init_failed" = "Inițializarea camerei a eșuat";
"camera_access_failed" = "Nu se poate accesa camera";
"photo_processing_failed" = "Fotografierea a eșuat";
"photo_processing_failed_retry" = "Nu se poate finaliza procesarea fotografiei, încercați din nou";
"photo_capture_progress" = "Progres: %d/%d";
"photo_captured_continue" = "Fotografia finalizată, continuați cu %@";
"loading_photos" = "Se încarcă fotografiile...";
"cancel" = "Anulează";

// MARK: - Delivery Status
"pending" = "În Așteptarea Livrării";
"in_progress" = "În Curs de Livrare";
"completed" = "Finalizat";
"failed" = "Livrarea a Eșuat";
"update_status" = "Actualizează Statusul";
"select_delivery_status" = "Selectați Statusul Livrării";
"select_failure_reason" = "Selectați Motivul Eșecului";
"delivered" = "Livrat";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Clientul nu este acasă";
"failure_reason_wrong_address" = "Adresă greșită";
"failure_reason_no_access" = "Nu se poate accesa locația";
"failure_reason_rejected" = "Pachetul a fost refuzat";
"failure_reason_other" = "Alt motiv";
"enter_custom_reason" = "Introduceți motivul specific";
"custom_reason_placeholder" = "Descrieți motivul specific...";
"custom_reason_required" = "Introduceți motivul specific";
"failure_reason_required" = "Selectați motivul eșecului";

// MARK: - Address Validation
"address_validation_failed" = "Validarea adresei a eșuat";

