/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Configurações de Idioma";
"system_language" = "Idioma do Sistema";
"system_language_section" = "Configurações do Sistema";
"languages" = "Idioma";
"language_info_title" = "Sobre Configurações de Idioma";
"language_info_description" = "Após alterar as configurações de idioma, o aplicativo exibirá texto no idioma selecionado. Alguns conteúdos podem exigir reinicialização do aplicativo para aplicar completamente as novas configurações de idioma.";
"restart_required" = "Reinicialização do Aplicativo Necessária";
"restart_app_message" = "Para aplicar completamente as alterações de idioma, reinicie o aplicativo.";
"restart_now" = "Reiniciar Agora";
"restart_later" = "Reiniciar Mais Tarde";

// MARK: - Common UI Elements
"close" = "Fechar";
"cancel" = "Cancelar";
"save" = "Salvar";
"edit" = "Editar";
"delete" = "Excluir";
"done" = "Concluído";
"next" = "Próximo";
"back" = "Voltar";
"confirm" = "Confirmar";
"error" = "Erro";
"success" = "Sucesso";
"warning" = "Aviso";
"loading" = "Carregando...";
"search" = "Pesquisar";
"settings" = "Configurações";
"help" = "Ajuda";
"about" = "Sobre";
"menu" = "Menu";
"understand" = "Eu Entendo";

// MARK: - Navigation
"navigation" = "Navegação";
"start_navigation" = "Iniciar Navegação";

// MARK: - Subscription
"subscription" = "Assinatura";
"upgrade_to_pro" = "Atualizar para Pro";
"upgrade_description" = "Agrupamento de navegação com um clique, 60x mais rápido que operação manual";
"restore_purchases" = "Restaurar Compras";
"learn_more" = "Saiba Mais";
"upgrade_your_plan" = "Atualize Seu Plano";
"one_click_navigation_description" = "Agrupamento de endereços de navegação com um clique, economiza tempo e combustível";
"current_plan" = "Plano Atual";
"upgrade" = "Atualizar";
"maybe_later" = "Talvez Mais Tarde";
"free_tier_name" = "Iniciante";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Grátis";
"pro_tier_price" = "$29.99/mês";
"expert_tier_price" = "$249.99/ano";
"free_tier_description" = "Adequado para indivíduos e pequenas empresas, não requer muitas paradas";
"pro_tier_description" = "Adequado para usuários que precisam de agrupamento de navegação com um clique e endereços ilimitados";
"expert_tier_description" = "Igual ao Pro, mas economiza 31% com plano anual";
"route_optimization" = "Otimização de Rota";
"unlimited_routes" = "Rotas Ilimitadas";
"unlimited_optimizations" = "Otimizações Ilimitadas";
"max_15_addresses" = "Máximo de 15 endereços por rota";
"save_fuel_30" = "Economiza até 30% de combustível";
"unlimited_addresses" = "✨ Endereços Ilimitados ✨";
"one_click_navigation" = "⚡ Agrupamento de Navegação com Um Clique - 60x Mais Rápido ⚡";
"package_finder" = "Localizador de Pacotes";
"annual_savings" = "Economia do Plano Anual";
"switched_to_free" = "Mudou para Plano Gratuito";
"switched_to_subscription" = "Mudou para Plano de Assinatura";
"unlimited_stops" = "Paradas Ilimitadas";
"plan_as_many_stops_as_needed" = "Adicione qualquer número de pontos de entrega sem limitações";

// MARK: - Address Input
"enter_or_search_address" = "Digite ou pesquise endereço";
"search_results_count" = "Resultados da pesquisa: %d";
"no_matching_addresses" = "Nenhum endereço correspondente encontrado";
"search_address_failed" = "Falha na pesquisa de endereço: %@";
"address_search_no_response" = "Pesquisa de endereço sem resposta";
"cannot_get_address_coordinates" = "Não é possível obter coordenadas do endereço";
"speech_recognizer_unavailable" = "Reconhecimento de voz indisponível";
"microphone_permission_denied" = "Permissão de microfone não concedida";
"speech_recognition_permission_denied" = "Permissão de reconhecimento de voz não concedida";
"listening" = "Ouvindo...";
"recording_failed" = "Falha ao iniciar gravação: %@";
"cannot_get_coordinates_retry" = "Não é possível obter coordenadas do endereço, digite manualmente ou tente novamente";
"cannot_create_recognition_request" = "Não é possível criar solicitação de reconhecimento";

// MARK: - Saved Address Picker
"search_address" = "Pesquisar Endereço";
"no_saved_addresses" = "Nenhum Endereço Salvo";
"no_saved_addresses_description" = "Você ainda não salvou nenhum endereço ou não há endereços que atendam aos critérios de filtro";
"select_address_book" = "Selecionar Catálogo de Endereços";

// MARK: - Menu
"menu" = "Menu";
"routes" = "Rota";
"address_book" = "Catálogo de Endereços";
"saved_routes" = "Rotas Salvas";
"manage_your_routes" = "Gerencie seu planejamento de rotas";
"manage_your_addresses" = "Gerencie seus endereços usados frequentemente";
"settings" = "Configurações";
"preferences" = "Preferências";
"set_custom_start_point" = "Definir Ponto de Partida Personalizado";
"current_start_point" = "Ponto de partida atual: %@";
"support" = "Suporte";
"contact_us" = "Entre em Contato";
"contact_us_description" = "Tem dúvidas ou sugestões? Sinta-se à vontade para entrar em contato!";
"help_center" = "Central de Ajuda";
"subscription" = "Assinatura";
"upgrade_to_pro" = "Atualizar para Pro";
"upgrade_description" = "Agrupamento de navegação com um clique, 60x mais rápido que operação manual";
"open_subscription_view" = "Abrir Visualização de Assinatura Diretamente";
"open_subscription_view_description" = "Pular camada intermediária, mostrar SubscriptionView diretamente";
"restore_purchases_failed" = "Falha ao restaurar compras: %@";
"about" = "Sobre";
"rate_us" = "Avalie-nos";
"rate_us_description" = "Seu feedback é importante para nós e nos ajuda a melhorar o aplicativo!";
"share_app" = "Compartilhar App";
"share_app_text" = "Experimente o NaviBatch, um aplicativo incrível de planejamento de rotas!";
"about_app" = "Sobre o App";
"developer_tools" = "Ferramentas do Desenvolvedor";
"coordinate_debug_tool" = "Ferramenta de Debug de Coordenadas";
"batch_fix_addresses" = "Corrigir Endereços em Lote";
"clear_database" = "Limpar Banco de Dados";
"clear_database_confirmation" = "Isso excluirá todos os dados, incluindo rotas, endereços e grupos. Esta ação não pode ser desfeita. Tem certeza de que deseja continuar?";
"confirm_clear" = "Confirmar Limpeza";
"version_info" = "Versão %@ (%@)";
"current_system_language" = "Idioma Atual do Sistema";
"reset_to_system_language" = "Redefinir para Idioma do Sistema";
"language" = "Idioma";
"language_settings" = "Configurações de Idioma";

// MARK: - Address Edit
"address" = "Endereço";
"coordinates" = "Coordenadas";
"distance_from_current_location" = "Distância da Localização Atual";
"address_info" = "Informações do Endereço";
"update_coordinates" = "Atualizar Coordenadas";
"fix_address" = "Corrigir Endereço";
"prompt" = "Prompt";
"confirm" = "Confirmar";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Modifique o endereço antes de atualizar as coordenadas";
"coordinates_update_success" = "Coordenadas atualizadas com sucesso";
"coordinates_update_failure" = "Falha ao atualizar coordenadas";
"save_failure" = "Falha ao salvar: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Nenhum Endereço Salvo";
"no_saved_addresses_message" = "Você ainda não salvou nenhum endereço";
"add_new_address" = "Adicionar Novo Endereço";
"address_title" = "Endereço";
"add" = "Adicionar";
"refresh" = "Atualizar";
"notes" = "Notas";
"address_details" = "Detalhes do Endereço";
"favorite" = "Favorito";
"edit_address" = "Editar Endereço";
"cancel" = "Cancelar";
"save" = "Salvar";
"confirm_delete" = "Confirmar Exclusão";
"delete" = "Excluir";
"delete_address_confirmation" = "Tem certeza de que deseja excluir este endereço? Esta operação não pode ser desfeita.";
"edit" = "Editar";
"address_marker" = "Endereço";
"address_label" = "Endereço:";
"notes_label" = "Notas:";
"created_at_label" = "Hora de criação:";
"open_in_maps" = "Abrir no Mapa";
"copy_address" = "Copiar Endereço";
"address_details_title" = "Detalhes do Endereço";

// MARK: - Route Detail
"start_end_point" = "Ponto de Início/Fim";
"start_point" = "Ponto de Partida";
"end_point" = "Ponto Final";
"route_info" = "Informações da Rota";
"address_count" = "Contagem de Endereços";
"address_count_format" = "%d endereços";
"points_count_format" = "%d pontos";
"additional_points_format" = "+%d pontos";
"export_route" = "Exportar Rota";
"navigate" = "Navegar";
"address_list" = "Lista de Endereços";
"no_addresses" = "Nenhum Endereço";
"no_addresses_message" = "Esta rota ainda não tem endereços";

// MARK: - Route Bottom Sheet
"address_point_start" = "Ponto de Partida";
"address_point_stop" = "Ponto de Parada";
"address_point_end" = "Ponto Final";
"route_name" = "Nome da Rota";
"save" = "Salvar";
"new_route" = "Nova Rota";
"saved_route" = "Rota Salva";
"edit" = "Editar";
"loading" = "Carregando...";
"plan_route" = "Planejar Rota";
"clear_all" = "Limpar Tudo";
"avoid" = "Evitar:";
"toll_roads" = "Estradas com Pedágio";
"highways" = "Rodovias";
"processing_addresses" = "Processando endereços...";
"same_start_end_point" = "Você definiu o mesmo endereço como ponto de partida e chegada";
"add_start_point" = "Adicionar Ponto de Partida";
"swipe_left_to_delete" = "← Deslize para a esquerda para excluir";
"delete" = "Excluir";
"add_new_address" = "Adicionar Novo Endereço";
"add_end_point" = "Adicionar Ponto Final";

// MARK: - Simple Address Sheet
"address_title" = "Endereço";
"enter_and_select_address" = "Digite e selecione o endereço";
"current_search_text" = "Texto de pesquisa atual: %@";
"search_results_count" = "Resultados da pesquisa: %d";
"no_matching_addresses" = "Nenhum endereço correspondente encontrado";
"add_address" = "Adicionar Endereço";
"edit_address" = "Editar Endereço";
"selected_coordinates" = "Coordenadas Selecionadas";
"company_name_optional" = "Nome da Empresa (Opcional)";
"url_optional" = "URL (Opcional)";
"favorite_address" = "Endereço Favorito";
"set_as_start_and_end" = "Definir como Ponto de Partida e Chegada";
"address_book" = "Catálogo de Endereços";
"batch_paste" = "Colar em Lote";
"file_import" = "Importar Arquivo";
"web_download" = "Download da Web";
"cancel" = "Cancelar";
"saving" = "Salvando...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Gerenciamento de Pontos de Entrega";
"information_category" = "Categoria de Informações";
"address_info" = "Informações do Endereço";
"package_info" = "Informações do Pacote";
"vehicle_position" = "Posição do Veículo";
"delivery_info" = "Informações de Entrega";
"navigation" = "Navegação";
"take_photo_record" = "Fazer Registro Fotográfico";
"update_status" = "Atualizar Status";
"done" = "Concluído";
"edit_address_button" = "Editar Endereço";
"coordinates" = "Coordenadas";
"access_instructions" = "Instruções de Acesso";
"add_access_instructions" = "Adicionar instruções de acesso...";
"package_count" = "Contagem de Pacotes";
"package_size" = "Tamanho do Pacote";
"package_type" = "Tipo de Pacote";
"mark_as_important" = "Marcar como Importante";
"select_package_position" = "Selecionar Posição do Pacote no Veículo";
"vehicle_area" = "Área do Veículo";
"left_right_position" = "Posição Esquerda/Direita";
"vehicle_position_front" = "Frente";
"vehicle_position_middle" = "Meio";
"vehicle_position_back" = "Traseira";
"vehicle_position_left" = "Esquerda";
"vehicle_position_right" = "Direita";
"vehicle_position_floor" = "Parte Inferior";
"vehicle_position_shelf" = "Parte Superior";
"height_position" = "Posição de Altura";
"delivery_type" = "Tipo de Entrega";
"delivery_status" = "Status de Entrega";
"order_info" = "Informações do Pedido";
"order_information" = "Informações do Pedido";
"order_number" = "Número do Pedido";
"enter_order_number" = "Digite o número do pedido";
"tracking_number" = "Número de Rastreamento";
"enter_tracking_number" = "Digite o número de rastreamento";
"time_info" = "Informações de Tempo";
"time_information" = "Informações de Tempo";
"estimated_arrival_time" = "Tempo Estimado de Chegada";
"anytime" = "A Qualquer Hora";
"stop_time" = "Tempo de Parada";
"minutes_format" = "%d minutos";
"photo_record" = "Registro Fotográfico";
"door_number_photo" = "Foto do Número da Porta";
"package_label_photo" = "Foto da Etiqueta do Pacote";
"placement_photo" = "Foto da Colocação";
"door_number_desc" = "Tire uma foto clara do número da casa ou da rua, certifique-se de que os números/letras estejam visíveis";
"package_label_desc" = "Tire uma foto da etiqueta do pacote, certifique-se de que as informações do destinatário estejam claramente visíveis";
"placement_desc" = "Tire uma foto da localização final de colocação do pacote";
"photo_captured" = "Foto tirada";
"photo_captured_options" = "Foto tirada, você quer continuar com a próxima foto ou concluir a foto atual?";
"continue_to_next_photo" = "Continuar com a próxima - %@";
"retake" = "Tirar Novamente";
"tap_to_capture" = "Toque para Tirar Foto";
"flash_auto" = "Flash Automático";
"flash_on" = "Ligar Flash";
"flash_off" = "Desligar Flash";
"photo_record_completed" = "Registro Fotográfico Concluído";
"photo_confirmation" = "Confirmação de Foto";
"error" = "Erro";
"ok" = "OK";
"complete_photo_capture" = "Concluir Fotos";
"tap_to_capture" = "Toque para Tirar Foto";
"photo_instructions" = "Toque em cada cartão de foto para tirar. Todas as fotos devem ser concluídas.";
"photo_options" = "Opções de Foto";
"view_photo" = "Ver Foto";
"retake_photo" = "Tirar Novamente";
"saving_photos" = "Salvando fotos...";
"completed" = "Concluído";
"not_taken" = "Não Tirada";
"route_options" = "Opções de Rota";
"avoid_tolls" = "Evitar Estradas com Pedágio";
"avoid_highways" = "Evitar Rodovias";
"optimize_route" = "Otimizar Rota";
"optimizing" = "Otimizando...";
"optimization_complete" = "Otimização Concluída";
"route_optimization_results" = "Resultados da otimização de rota";
"route_planning_options" = "Opções de planejamento de rota";
"before_optimization" = "Antes da otimização";
"after_optimization" = "Após a otimização";
"auto_group" = "Agrupamento automático";
"optimized_route_order" = "Ordem de rota otimizada";
"apply" = "Aplicar";
"kilometers" = "quilômetros";

// MARK: - Addresses
"addresses" = "Endereço";
"add_address" = "Adicionar Endereço";
"edit_address" = "Editar Endereço";
"delete_address" = "删除地址";
"address_details" = "Detalhes do Endereço";
"street" = "Rua";
"city" = "Cidade";
"state" = "Estado/Província";
"country" = "País";
"postal_code" = "Código Postal";
"phone" = "Telefone";
"email" = "E-mail";
"website" = "Site";
"company" = "Empresa";
"notes" = "Notas";
"coordinates" = "Coordenadas";
"latitude" = "Latitude";
"longitude" = "Longitude";
"geocoding_error" = "Erro de Geocodificação";
"address_validation" = "Validação de Endereço";
"invalid_addresses" = "Endereço Inválido";
"fix_addresses" = "Corrigir Endereço";

// MARK: - Routes
"route" = "Rota";
"routes" = "Rota";
"select_address_point" = "Selecionar Ponto de Endereço";
"select_delivery_points" = "Selecionar Pontos de Entrega";
"create_delivery_route" = "Criar Rota de Entrega";
"view_saved_routes" = "Ver Rotas Salvas";
"create_route" = "Criar Rota";
"edit_route" = "Editar Rota";
"delete_route" = "Excluir Rota";
"route_name" = "Nome da Rota";
"route_details" = "Detalhes da Rota";
"selected_addresses" = "%d endereços selecionados";
"reached_limit" = "Limite Atingido";
"can_select_more" = "Ainda pode selecionar %d";
"navigate_button" = "Navegação";
"create_group" = "Criar Grupo";
"start_point" = "Ponto de Partida";
"end_point" = "Ponto Final";
"waypoints" = "Pontos de Passagem";
"total_distance" = "Distância Total";
"estimated_time" = "Tempo Estimado";
"route_summary" = "Resumo da Rota";
"route_options" = "Opções de Rota";
"route_saved" = "Rota Salva";
"route_optimized" = "Rota Otimizada";
"optimizing_route" = "Otimizando Rota...";
"completed_percent" = "Concluído %d%%";
"processing_points" = "Processando: %d/%d";
"estimated_remaining_time" = "Tempo Estimado Restante: %@";

// MARK: - Delivery
"delivery" = "Entrega";
"delivery_confirmation" = "Confirmação de Entrega";
"take_photo" = "Tirar Foto";
"signature" = "Assinatura";
"delivery_notes" = "Notas de Entrega";
"delivery_status" = "Status de Entrega";
"delivered" = "Entregue";
"not_delivered" = "Não Entregue";
"delivery_time" = "Hora de Entrega";
"delivery_date" = "Data de Entrega";
"package_details" = "Detalhes do Pacote";
"package_id" = "ID do Pacote";
"package_weight" = "Peso do Pacote";
"package_dimensions" = "Dimensões do Pacote";
"recipient_name" = "Nome do Destinatário";
"recipient_phone" = "Telefone do Destinatário";

// MARK: - Groups
"groups" = "Grupo";
"saved_groups" = "Grupos Salvos";
"create_group" = "Criar Grupo";
"edit_group" = "Editar Grupo";
"delete_group" = "Excluir Grupo";
"group_name" = "Nome do Grupo";
"group_details" = "Detalhes do Grupo";
"auto_grouping" = "Agrupamento Automático";
"group_by" = "Agrupar Por";
"add_to_group" = "Adicionar ao Grupo";
"remove_from_group" = "Remover do Grupo";
"group_created" = "Grupo Criado";
"default_group_name_format" = "Grupo%d";
"auto_grouping_completed" = "Agrupamento Automático Concluído";
"auto_grouping_in_progress" = "Agrupamento Automático em Andamento...";
"create_group_every_14_addresses" = "Criar um grupo para cada 14 endereços";
"create_delivery_group" = "Criar Grupo de Entrega";
"enter_group_name" = "Digite o nome do grupo";
"selected_delivery_points" = "Pontos de entrega selecionados";
"drag_to_adjust_order" = "Arraste para ajustar a ordem";

// MARK: - Subscription
"subscription" = "Assinatura";
"free_plan" = "Versão Gratuita";
"pro_plan" = "Versão Pro";
"expert_plan" = "Versão Expert";
"monthly" = "Plano Mensal";
"yearly" = "Plano Anual";
"subscribe" = "Assinatura";
"upgrade" = "Atualizar";
"upgrade_to_pro" = "Atualizar para Pro";
"manage_subscription" = "Gerenciar Assinatura";
"restore_purchases" = "Restaurar Compras";
"subscription_benefits" = "Benefícios da Assinatura";
"free_trial" = "Teste Gratuito";
"price_per_month" = "%@ por mês";
"price_per_year" = "%@ por ano";
"save_percent" = "Economize %@%";
"current_plan" = "Plano Atual";
"subscription_terms" = "Termos de Assinatura";
"privacy_policy" = "Política de Privacidade";
"terms_of_service" = "Termos de Serviço";

// MARK: - Import/Export
"import" = "Importar";
"export" = "Exportar";
"import_addresses" = "Importar Endereços";
"export_addresses" = "Exportar Endereços";
"import_from_file" = "Importar de Arquivo";
"export_to_file" = "Exportar para Arquivo";
"file_format" = "Formato de Arquivo";
"csv_format" = "Formato CSV";
"excel_format" = "Formato Excel";
"json_format" = "Formato JSON";
"import_success" = "Importados com sucesso %d endereços, todos com coordenadas válidas.";
"export_success" = "Exportação Bem-sucedida";
"import_error" = "Erro de Importação";
"export_error" = "Erro de Exportação";

// MARK: - Navigation
"navigate" = "Navegar";

// MARK: - Look Around
"show_look_around" = "Ver Vista da Rua";
"hide_look_around" = "Ocultar Vista da Rua";

// MARK: - Map
"map" = "Mapa";
"map_type" = "Tipo de Mapa";
"standard" = "Padrão";
"satellite" = "Satélite";
"hybrid" = "Híbrido";
"show_traffic" = "Mostrar Trânsito";
"current_location" = "Localização Atual";
"directions" = "Direções da Rota";
"distance_to" = "Distância";
"eta" = "Tempo Estimado de Chegada";
"look_around" = "Olhar ao Redor";
"locating_to_glen_waverley" = "Localizando para Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Erro de Rede";
"location_error" = "Erro de Localização";
"permission_denied" = "Permissão Negada";
"location_permission_required" = "Permissão de Localização Necessária";
"camera_permission_required" = "Permissão de Câmera Necessária";
"photo_library_permission_required" = "Permissão de Biblioteca de Fotos Necessária";
"please_try_again" = "Tente Novamente";
"something_went_wrong" = "Ocorreu um Erro";
"invalid_input" = "Entrada Inválida";
"required_field" = "Campo Obrigatório";
"no_internet_connection" = "Sem Conexão com a Internet";
"server_error" = "Erro do Servidor";
"timeout_error" = "Tempo Limite da Solicitação Excedido";
"data_not_found" = "Dados Não Encontrados";
"selection_limit_reached" = "Limite de Seleção Atingido";
"selection_limit_description" = "Você pode selecionar no máximo %d endereços, você selecionou %d";

// MARK: - Location Validation Status
"location_status_valid" = "Faixa Válida";
"location_status_warning" = "Faixa de Aviso";
"location_status_invalid" = "Localização Inválida";
"location_status_unknown" = "Status Desconhecido";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Inválido: Coordenadas zero (0,0)";
"coordinates_invalid_nan" = "Inválido: Coordenadas não numéricas";
"coordinates_out_of_range" = "Inválido: Coordenadas fora da faixa válida";
"coordinates_far_from_user" = "警告：位置距离您当前位置较远";
"coordinates_ocean" = "警告：位置可能在海洋或无人区";

// MARK: - Batch Address Input
"batch_add_addresses" = "Adicionar Endereços em Lote";
"batch_address_input_placeholder" = "请输入或粘贴地址，每行一个地址。最多35个地址。";
"free_address_limit" = "Limite de Endereços da Versão Gratuita";
"address_count_limit" = "Limite de Quantidade de Endereços";
"free_version_max_addresses" = "A versão gratuita permite no máximo %d endereços.";
"current_addresses_remaining" = "Atualmente tem %d endereços, só pode adicionar mais %d endereços.";
"current_route_address_limit" = "A rota atual tem %d endereços, só pode adicionar mais %d endereços, máximo total de %d endereços.";
"selected_addresses_can_import" = "Você selecionou %d endereços, pode importar esses endereços.";
"selected_addresses_exceeds" = "Você selecionou %d endereços, excedendo o número permitido em %d.";
"selected_addresses_all_importable" = "Você selecionou %d endereços, todos podem ser importados.";
"upgrade_for_unlimited_addresses" = "Atualize para premium para endereços ilimitados!";
"import_first_n_addresses" = "Importar apenas os primeiros %d";
"import_all_addresses" = "Importar Todos os Endereços";
"import_selected_addresses" = "Importar endereços selecionados";
"no_importable_addresses" = "Nenhum endereço para importar, verifique os limites de endereço";
"please_enter_valid_address" = "Digite pelo menos um endereço válido";

// MARK: - File Import
"import_success" = "Importados com sucesso %d endereços, todos com coordenadas válidas.";
"import_success_with_warnings" = "Importou com sucesso %d endereços, %d endereços com coordenadas normais, %d endereços com avisos.\n\nEndereços com avisos foram marcados e podem ser corrigidos manualmente após a importação.";

// MARK: - Web Download
"web_download" = "Download da Web";
"supported_formats" = "Formatos Suportados";
"supported_format_csv" = "• Arquivos CSV: A coluna de endereços deve conter endereços completos";
"supported_format_json" = "• Dados JSON: Array contendo campos de endereço";
"supported_format_text" = "• Texto simples: Um endereço por linha";
"download_history" = "Histórico de Downloads";
"upgrade_to_premium" = "Atualizar para Premium";
"input_address_data_url" = "Digite URL dos dados de endereço";
"import_result" = "Resultado da Importação";
"import_addresses" = "Importar Endereços";
"downloading" = "Baixando...";
"processing_data" = "Processando dados...";
"google_drive_download_failed" = "Falha no download do Google Drive";
"second_attempt_invalid_data" = "Segunda tentativa de download retornou dados inválidos";
"cannot_parse_json" = "Não é possível analisar dados JSON, verifique o formato do arquivo";
"cannot_parse_json_with_error" = "Não é possível analisar dados JSON: %@";
"cannot_get_address_coordinates" = "Não é possível obter coordenadas do endereço";
"cannot_read_file" = "Não é possível ler arquivo: %@";
"success" = "Sucesso";
"warning" = "Aviso";
"failed" = "Entrega Falhada";
"no_matching_addresses" = "Nenhum endereço correspondente encontrado";
"no_valid_addresses" = "Nenhum endereço válido encontrado";
"confirm" = "Confirmar";
"processing_addresses" = "Processando endereços...";
"supports_file_types" = "Suporta arquivos CSV, TXT e JSON";
"tap_to_select_file" = "Toque para selecionar arquivo";
"import_addresses" = "Importar Endereços";
"company_name_optional" = "Nome da Empresa (Opcional)";
"input_company_name" = "Digite nome da empresa (opcional)";
"imported_addresses_count" = "Importou %d endereços";
"select_all" = "Selecionar Tudo";
"excel_format_not_supported" = "Formato Excel não suportado";
"no_matching_addresses" = "Nenhum endereço correspondente encontrado";

// MARK: - Import Limits
"import_failed" = "Importação Falhada";
"no_importable_addresses" = "Nenhum endereço para importar, verifique os limites de endereço";
"free_version_address_limit" = "A versão gratuita permite no máximo %d endereços.";
"current_address_count" = "Atualmente tem %d endereços, só pode adicionar mais %d endereços.";
"can_import_selected" = "Você selecionou %d endereços, pode importar esses endereços.";
"selected_exceeds_limit" = "Você selecionou %d endereços, excedendo o número permitido em %d.";
"upgrade_to_premium_unlimited" = "Atualize para premium para endereços ilimitados!";
"route_address_limit" = "A rota atual tem %d endereços, só pode adicionar mais %d endereços, máximo total de %d endereços.";
"free_version_limit" = "Limite de Endereços da Versão Gratuita";
"address_count_limit" = "Limite de Quantidade de Endereços";
"import_selected_addresses" = "Importar endereços selecionados";
"import_first_n" = "Importar apenas os primeiros %d";
"import_all_n" = "Importar todos os %d";
"cannot_import" = "Não é possível importar";
"select_at_least_one" = "Selecione pelo menos um endereço";

// MARK: - Import Results
"no_valid_addresses_found" = "Nenhum endereço válido encontrado";
"import_success_all_valid" = "Importou com sucesso %d endereços, todas as coordenadas de endereço são normais.";
"import_success_some_warnings" = "Importou com sucesso %d endereços, %d endereços com coordenadas normais, %d endereços não conseguem obter coordenadas.";

// MARK: - Warnings
"invalid_csv_row" = "Linha CSV inválida";
"distance_warning" = "Distância excede 200km da localização atual";
"not_in_australia" = "Coordenadas não estão dentro do alcance da Austrália";
"cannot_get_coordinates" = "Não é possível obter coordenadas do endereço";
"empty_address" = "Endereço vazio";
"invalid_address_data" = "Dados de endereço inválidos";

// MARK: - Saved Groups
"saved_groups" = "Grupos Salvos";
"no_saved_groups" = "Nenhum grupo salvo";
"select_points_create_groups" = "Selecione pontos de entrega e crie grupos para fácil gerenciamento";
"group_name" = "Nome do Grupo";
"group_details" = "Detalhes do Grupo";
"navigate_to_these_points" = "Navegar para esses pontos";
"confirm_remove_address" = "Tem certeza de que deseja remover o endereço \"%@\" do grupo?";
"confirm_remove_this_address" = "Tem certeza de que deseja remover este endereço do grupo?";
"addresses_count" = "%d endereços";
"no_saved_routes" = "Nenhuma rota salva";
"no_saved_routes_description" = "Você ainda não salvou nenhuma rota";
"all_routes" = "Todas as Rotas";
"address_count_format_simple" = "%d endereços";
"delete_all_routes" = "Excluir Todas as Rotas";
"navigate_to_all_points" = "Navegar para todos os pontos";
"confirm_navigate_to_route" = "Tem certeza de que deseja navegar para todos os pontos na rota \"%@\"?";
"temp_navigation_group" = "Grupo de Navegação Temporário";

// MARK: - Route Management
"route_management" = "Gerenciamento de Rotas";
"route_info" = "Informações da Rota";
"route_name" = "Nome da Rota";
"route_addresses" = "Endereços da Rota";
"no_addresses_in_route" = "Esta rota não tem endereços";
"must_keep_one_route" = "Deve manter pelo menos uma rota";
"confirm_delete_route" = "Tem certeza de que deseja excluir a rota \"%@\"? Esta operação não pode ser desfeita.";
"confirm_delete_all_routes" = "Confirmar Exclusão de Todas as Rotas";
"confirm_delete_all_routes_message" = "Tem certeza de que deseja excluir todas as rotas? Esta operação não pode ser desfeita.";
"delete_all" = "Excluir Tudo";

// MARK: - Navigation Buttons
"navigate" = "Navegar";

// MARK: - GroupDetailView
"points_count_format" = "%d pontos";

// MARK: - DeliveryPointDetailView
"address_information" = "Informações do Endereço";
"group_belonging" = "Grupo de Pertencimento";
"view_map" = "Ver Mapa";
"delivery_status" = "Status de Entrega";
"notes" = "Notas";
"delete_delivery_point" = "Excluir Ponto de Entrega";
"delivery_point_details" = "Detalhes do Ponto de Entrega";
"confirm_deletion" = "Confirmar Exclusão";
"delete_delivery_point_confirmation" = "Tem certeza de que deseja excluir este ponto de entrega? Esta operação não pode ser desfeita.";

// MARK: - Delivery Photos
"delivery_photos" = "Fotos de Entrega";
"view_delivery_photos" = "Ver Fotos de Entrega";
"no_photos_taken" = "Ainda não tirou fotos";
"take_photos" = "Tirar Fotos";
"loading_photos" = "Carregando fotos...";
"photo_not_found" = "Foto não encontrada";
"photo_deleted" = "Foto foi excluída";
"door_number_photo" = "Foto do Número da Porta";
"package_label_photo" = "Foto da Etiqueta do Pacote";
"placement_photo" = "Foto da Colocação";
"share_photos" = "Compartilhar Foto";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Confirmação de Foto";
"door_number_photo_title" = "Foto do Número da Estrada/Casa";
"package_label_photo_title" = "Foto da Etiqueta do Pacote";
"placement_photo_title" = "Foto da Localização de Colocação";
"door_number_photo_desc" = "Tire uma foto clara do número da casa, certifique-se de que os números/letras estejam visíveis";
"package_label_photo_desc" = "Tire uma foto da etiqueta do pacote, certifique-se de que as informações do destinatário estejam claramente visíveis";
"placement_photo_desc" = "Tire uma foto da localização final de colocação do pacote";
"swipe_to_switch" = "Deslize para alternar tipo de foto";
"complete_photos" = "Concluir Fotos";
"saving_photos" = "Salvando fotos...";
"photo_save_success" = "Foto salva com sucesso";
"photo_save_failure" = "Falha ao salvar foto";
"retake_photo" = "Tirar Novamente";
"no_photos_found" = "Foto não encontrada";
"photos_deleted_or_not_taken" = "A foto pode ter sido excluída ou ainda não foi tirada";
"share_photo" = "Compartilhar Foto";
"photo_capture_preview" = "Modo de visualização - simulação de câmera";
"photo_capture_close" = "Fechar";
"camera_start_failed" = "Falha ao iniciar câmera";
"camera_start_failed_retry" = "Não é possível iniciar a câmera, tente novamente";
"camera_init_failed" = "Falha na inicialização da câmera";
"camera_access_failed" = "Não é possível acessar a câmera";
"photo_processing_failed" = "Falha ao tirar foto";
"photo_processing_failed_retry" = "Não é possível concluir o processamento da foto, tente novamente";
"photo_capture_progress" = "Progresso: %d/%d";
"photo_captured_continue" = "Foto concluída, continue com %@";
"loading_photos" = "Carregando fotos...";
"cancel" = "Cancelar";

// MARK: - Delivery Status
"pending" = "Aguardando Entrega";
"in_progress" = "Em Entrega";
"completed" = "Concluído";
"failed" = "Entrega Falhada";
"update_status" = "Atualizar Status";
"select_delivery_status" = "Selecionar Status de Entrega";
"select_failure_reason" = "Selecionar Motivo da Falha";
"delivered" = "Entregue";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Cliente não está em casa";
"failure_reason_wrong_address" = "Endereço incorreto";
"failure_reason_no_access" = "Não é possível acessar a localização";
"failure_reason_rejected" = "Pacote foi rejeitado";
"failure_reason_other" = "Outro motivo";
"enter_custom_reason" = "Digite motivo específico";
"custom_reason_placeholder" = "Descreva o motivo específico...";
"custom_reason_required" = "Digite motivo específico";
"failure_reason_required" = "Selecione motivo da falha";

// MARK: - Address Validation
"address_validation_failed" = "Falha na validação do endereço";

