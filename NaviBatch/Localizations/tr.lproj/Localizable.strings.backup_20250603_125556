/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Dil Ayarları";
"system_language" = "Sistem Dili";
"system_language_section" = "Sistem Ayarları";
"languages" = "Diller";
"language_info_title" = "Dil Ayarları Hakkında";
"language_info_description" = "Dil ayarlarını değiştirdikten sonra, uygulama seçilen dilde metin gösterecektir. Bazı içerikler, yeni dil ayarlarını tam olarak uygulamak için uygulamanın yeniden başlatılmasını gerektirebilir.";
"restart_required" = "Yeniden Başlatma Gerekli";
"restart_app_message" = "Dil değişikliklerini tam olarak uygulamak için lütfen uygulamayı yeniden başlatın.";
"restart_now" = "Şimdi Yeniden Başlat";
"restart_later" = "Daha Sonra Yeniden Başlat";

// MARK: - Common UI Elements
"close" = "Kapat";
"cancel" = "İptal";
"save" = "Kaydet";
"edit" = "Düzenle";
"delete" = "Sil";
"done" = "Tamam";
"next" = "İleri";
"back" = "Geri";
"confirm" = "Onayla";
"error" = "Hata";
"success" = "Başarılı";
"warning" = "Uyarı";
"loading" = "Yükleniyor...";
"search" = "Ara";
"settings" = "Ayarlar";
"help" = "Yardım";
"about" = "Hakkında";
"menu" = "Menü";
"understand" = "Anlıyorum";

// MARK: - Navigation
"navigation" = "Navigasyon";
"start_navigation" = "Navigasyonu Başlat";

// MARK: - Subscription
"subscription" = "Abonelik";
"upgrade_to_pro" = "Pro Sürümüne Yükselt";
"upgrade_description" = "Tek tıklamayla navigasyon gruplama, manuel işlemden 60 kat daha hızlı";
"restore_purchases" = "Satın Alımları Geri Yükle";
"learn_more" = "Daha Fazla Bilgi";
"upgrade_your_plan" = "Planınızı Yükseltin";
"one_click_navigation_description" = "Adresleri tek tıklamayla gruplandırarak zaman ve yakıt tasarrufu sağlayın";
"current_plan" = "Mevcut Plan";
"upgrade" = "Yükselt";
"maybe_later" = "Belki Daha Sonra";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Ücretsiz";
"pro_tier_price" = "$29.99/ay";
"expert_tier_price" = "$249.99/yıl";
"free_tier_description" = "Çok sayıda durak noktasına ihtiyaç duymayan bireyler ve küçük işletmeler için uygundur";
"pro_tier_description" = "Tek tıklamayla navigasyon gruplamaya ve sınırsız adrese ihtiyaç duyan kullanıcılar için uygundur";
"expert_tier_description" = "Pro ile aynı, ancak yıllık plan üzerinden %31 tasarruf";
"route_optimization" = "Rota Optimizasyonu";
"unlimited_routes" = "Sınırsız Rotalar";
"unlimited_optimizations" = "Sınırsız Optimizasyonlar";
"max_15_addresses" = "Rota başına maksimum 15 adres";
"save_fuel_30" = "%30'a kadar yakıt tasarrufu";
"unlimited_addresses" = "✨ Sınırsız Adresler ✨";
"one_click_navigation" = "⚡ Tek Tıklamayla Navigasyon Gruplama - 60x Daha Hızlı ⚡";
"package_finder" = "Paket Konum Bulucu";
"annual_savings" = "Yıllık Tasarruf";
"switched_to_free" = "Ücretsiz Plana Geçildi";
"switched_to_subscription" = "Abonelik Planına Geçildi";
"unlimited_stops" = "Sınırsız Duraklar";
"plan_as_many_stops_as_needed" = "İstediğiniz kadar teslimat noktası ekleyin, sınırsız";

// MARK: - Address Input
"enter_or_search_address" = "Adres girin veya arayın";
"search_results_count" = "Arama sonuçları: %d";
"no_matching_addresses" = "Eşleşen adres bulunamadı";
"search_address_failed" = "Adres araması başarısız oldu: %@";
"address_search_no_response" = "Adres aramasından yanıt yok";
"cannot_get_address_coordinates" = "Adres koordinatları alınamıyor";
"speech_recognizer_unavailable" = "Konuşma tanıma kullanılamıyor";
"microphone_permission_denied" = "Mikrofon izni reddedildi";
"speech_recognition_permission_denied" = "Konuşma tanıma izni reddedildi";
"listening" = "Dinleniyor...";
"recording_failed" = "Kayıt başlatılamadı: %@";
"cannot_get_coordinates_retry" = "Adres koordinatları alınamıyor, lütfen manuel olarak girin veya tekrar deneyin";
"cannot_create_recognition_request" = "Tanıma isteği oluşturulamıyor";

// MARK: - Saved Address Picker
"search_address" = "Adres Ara";
"no_saved_addresses" = "Kaydedilmiş adres yok";
"no_saved_addresses_description" = "Henüz herhangi bir adres kaydetmediniz veya filtreleme kriterlerine uyan adres yok";
"select_address_book" = "Adres Defteri Seç";

// MARK: - Menu
"menu" = "Menü";
"routes" = "Rotalar";
"address_book" = "Adres Defteri";
"saved_routes" = "Kaydedilmiş Rotalar";
"manage_your_routes" = "Rota planlamanızı yönetin";
"manage_your_addresses" = "Sık kullandığınız adresleri yönetin";
"settings" = "Ayarlar";
"preferences" = "Tercihler";
"set_custom_start_point" = "Özel Başlangıç Noktası Ayarla";
"current_start_point" = "Mevcut başlangıç noktası: %@";
"support" = "Destek";
"contact_us" = "Bize Ulaşın";
"contact_us_description" = "Sorularınız veya önerileriniz mi var? Bizimle iletişime geçmekten çekinmeyin!";
"help_center" = "Yardım Merkezi";
"subscription" = "Abonelik";
"upgrade_to_pro" = "Pro Sürümüne Yükselt";
"upgrade_description" = "Tek tıklamayla navigasyon gruplama, manuel işlemden 60 kat daha hızlı";
"open_subscription_view" = "Abonelik Görünümünü Doğrudan Aç";
"open_subscription_view_description" = "Ara katmanı atlayarak doğrudan Abonelik Görünümünü göster";
"restore_purchases_failed" = "Satın alımları geri yükleme başarısız oldu: %@";
"about" = "Hakkında";
"rate_us" = "Bizi Değerlendirin";
"rate_us_description" = "Geri bildiriminiz bizim için önemlidir ve uygulamayı geliştirmemize yardımcı olur!";
"share_app" = "Uygulamayı Paylaş";
"share_app_text" = "NaviBatch'i deneyin, harika bir rota planlama uygulaması!";
"about_app" = "Uygulama Hakkında";
"developer_tools" = "Geliştirici Araçları";
"coordinate_debug_tool" = "Koordinat Hata Ayıklama Aracı";
"batch_fix_addresses" = "Toplu Adres Düzeltme";
"clear_database" = "Veritabanını Temizle";
"clear_database_confirmation" = "Bu işlem, rotalar, adresler ve gruplar dahil olmak üzere tüm verileri silecektir. Bu işlem geri alınamaz. Devam etmek istediğinizden emin misiniz?";
"confirm_clear" = "Temizlemeyi Onayla";
"version_info" = "Sürüm %@ (%@)";
"current_system_language" = "Mevcut Sistem Dili";
"reset_to_system_language" = "Sistem Diline Sıfırla";
"language" = "Dil";
"language_settings" = "Dil Ayarları";

// MARK: - Address Edit
"address" = "Adres";
"coordinates" = "Koordinatlar";
"distance_from_current_location" = "Mevcut Konumdan Uzaklık";
"address_info" = "Adres Bilgisi";
"update_coordinates" = "Koordinatları Güncelle";
"fix_address" = "Adresi Düzelt";
"prompt" = "İstem";
"confirm" = "Onayla";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Koordinatları güncellemeden önce lütfen adresi değiştirin";
"coordinates_update_success" = "Koordinatlar başarıyla güncellendi";
"coordinates_update_failure" = "Koordinatlar güncellenemedi";
"save_failure" = "Kaydetme başarısız oldu: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Kaydedilmiş Adres Yok";
"no_saved_addresses_message" = "Henüz herhangi bir adres kaydetmediniz";
"add_new_address" = "Yeni Adres Ekle";
"address_title" = "Adres";
"add" = "Ekle";
"refresh" = "Yenile";
"notes" = "Notlar";
"address_details" = "Adres Detayları";
"favorite" = "Favori";
"edit_address" = "Adresi Düzenle";
"cancel" = "İptal";
"save" = "Kaydet";
"confirm_delete" = "Silmeyi Onayla";
"delete" = "Sil";
"delete_address_confirmation" = "Bu adresi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"edit" = "Düzenle";
"address_marker" = "Adres";
"address_label" = "Adres:";
"notes_label" = "Notlar:";
"created_at_label" = "Oluşturulma:";
"open_in_maps" = "Haritalarda Aç";
"copy_address" = "Adresi Kopyala";
"address_details_title" = "Adres Detayları";

// MARK: - Route Detail
"start_end_point" = "Başlangıç/Bitiş Noktası";
"start_point" = "Başlangıç Noktası";
"end_point" = "Bitiş Noktası";
"route_info" = "Rota Bilgisi";
"address_count" = "Adres Sayısı";
"address_count_format" = "%d adres";
"points_count_format" = "%d nokta";
"additional_points_format" = "+%d nokta";
"export_route" = "Rotayı Dışa Aktar";
"navigate" = "Navigasyon";
"address_list" = "Adres Listesi";
"no_addresses" = "Adres Yok";
"no_addresses_message" = "Bu rotada henüz adres yok";

// MARK: - Route Bottom Sheet
"address_point_start" = "Başlangıç Noktası";
"address_point_stop" = "Durak Noktası";
"address_point_end" = "Bitiş Noktası";
"route_name" = "Rota Adı";
"save" = "Kaydet";
"new_route" = "Yeni Rota";
"saved_route" = "Kaydedilmiş Rota";
"edit" = "Düzenle";
"loading" = "Yükleniyor...";
"plan_route" = "Rota Planla";
"clear_all" = "Tümünü Temizle";
"avoid" = "Kaçınılacaklar:";
"toll_roads" = "Ücretli Yollar";
"highways" = "Otoyollar";
"processing_addresses" = "Adresler işleniyor...";
"same_start_end_point" = "Aynı adresi hem başlangıç hem de bitiş noktası olarak ayarladınız";
"add_start_point" = "Başlangıç Noktası Ekle";
"swipe_left_to_delete" = "← Silmek için sola kaydırın";
"delete" = "Sil";
"add_new_address" = "Yeni Adres Ekle";
"add_end_point" = "Bitiş Noktası Ekle";

// MARK: - Simple Address Sheet
"address_title" = "Adres";
"enter_and_select_address" = "Adres girin ve seçin";
"current_search_text" = "Mevcut arama metni: %@";
"search_results_count" = "Arama sonuçları: %d";
"no_matching_addresses" = "Eşleşen adres bulunamadı";
"add_address" = "Adres Ekle";
"edit_address" = "Adresi Düzenle";
"selected_coordinates" = "Seçilen Koordinatlar";
"company_name_optional" = "Şirket Adı (İsteğe bağlı)";
"url_optional" = "URL (İsteğe bağlı)";
"favorite_address" = "Favori Adres";
"set_as_start_and_end" = "Başlangıç ve Bitiş Noktası Olarak Ayarla";
"address_book" = "Adres Defteri";
"batch_paste" = "Toplu Yapıştır";
"file_import" = "Dosyadan İçe Aktar";
"web_download" = "Web'den İndir";
"cancel" = "İptal";
"saving" = "Kaydediliyor...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Teslimat Noktası Yönetimi";
"information_category" = "Bilgi Kategorisi";
"address_info" = "Adres Bilgisi";
"package_info" = "Paket Bilgisi";
"vehicle_position" = "Araç Pozisyonu";
"delivery_info" = "Teslimat Bilgisi";
"navigation" = "Navigasyon";
"take_photo_record" = "Fotoğraf Çek";
"update_status" = "Durumu Güncelle";
"done" = "Tamam";
"edit_address_button" = "Adresi Düzenle";
"coordinates" = "Koordinatlar";
"access_instructions" = "Erişim Talimatları";
"add_access_instructions" = "Erişim talimatları ekle...";
"package_count" = "Paket Sayısı";
"package_size" = "Paket Boyutu";
"package_type" = "Paket Tipi";
"mark_as_important" = "Önemli Olarak İşaretle";
"select_package_position" = "Araçtaki Paket Pozisyonunu Seçin";
"vehicle_area" = "Araç Bölgesi";
"left_right_position" = "Sol/Sağ Pozisyon";
"vehicle_position_front" = "Ön";
"vehicle_position_middle" = "Orta";
"vehicle_position_back" = "Arka";
"vehicle_position_left" = "Sol";
"vehicle_position_right" = "Sağ";
"vehicle_position_floor" = "Alt";
"vehicle_position_shelf" = "Üst";
"height_position" = "Yükseklik Pozisyonu";
"delivery_type" = "Teslimat Tipi";
"delivery_status" = "Teslimat Durumu";
"order_info" = "Sipariş Bilgisi";
"order_information" = "Sipariş Bilgisi";
"order_number" = "Sipariş Numarası";
"enter_order_number" = "Sipariş numarası girin";
"tracking_number" = "Takip Numarası";
"enter_tracking_number" = "Takip numarası girin";
"time_info" = "Zaman Bilgisi";
"time_information" = "Zaman Bilgisi";
"estimated_arrival_time" = "Tahmini Varış Zamanı";
"anytime" = "Herhangi Bir Zamanda";
"stop_time" = "Durma Süresi";
"minutes_format" = "%d dakika";
"photo_record" = "Fotoğraf Kaydı";
"door_number_photo" = "Kapı Numarası Fotoğrafı";
"package_label_photo" = "Paket Etiketi Fotoğrafı";
"placement_photo" = "Yerleştirme Fotoğrafı";
"door_number_desc" = "Lütfen kapı numarasını veya sokak numarasını net bir şekilde çekin, rakam/harflerin görünür olduğundan emin olun";
"package_label_desc" = "Lütfen paket etiketini çekin, alıcı bilgilerinin net görünür olduğundan emin olun";
"placement_desc" = "Lütfen paketin son yerleştirildiği konumu çekin";
"photo_captured" = "Fotoğraf çekildi";
"photo_captured_options" = "Fotoğraf çekildi, bir sonraki fotoğrafı çekmek mi istiyorsunuz yoksa mevcut fotoğrafı tamamlamak mı?";
"continue_to_next_photo" = "Sonrakini çek - %@";
"retake" = "Tekrar çek";
"tap_to_capture" = "Çekmek için dokunun";
"flash_auto" = "Otomatik flaş";
"flash_on" = "Flaşı aç";
"flash_off" = "Flaşı kapat";
"photo_record_completed" = "Fotoğraf Kaydı Tamamlandı";
"photo_confirmation" = "Fotoğraf onayı";
"error" = "Hata";
"ok" = "Tamam";
"complete_photo_capture" = "Fotoğraf çekmeyi tamamla";
"tap_to_capture" = "Çekmek için dokunun";
"photo_instructions" = "Her fotoğraf kartına dokunarak çekim yapın. Tüm fotoğraflar tamamlanmalıdır.";
"photo_options" = "Fotoğraf seçenekleri";
"view_photo" = "Fotoğrafı görüntüle";
"retake_photo" = "Tekrar çek";
"saving_photos" = "Fotoğraflar kaydediliyor...";
"completed" = "Tamamlandı";
"not_taken" = "Çekilmedi";
"route_options" = "Rota Seçenekleri";
"avoid_tolls" = "Ücretli Yollardan Kaçın";
"avoid_highways" = "Otoyollardan Kaçın";
"optimize_route" = "Rotayı Optimize Et";
"optimizing" = "Optimize Ediliyor...";
"optimization_complete" = "Optimizasyon Tamamlandı";

// MARK: - Addresses
"addresses" = "Adresler";
"add_address" = "Adres Ekle";
"edit_address" = "Adresi Düzenle";
"delete_address" = "Adresi Sil";
"address_details" = "Adres Detayları";
"street" = "Sokak";
"city" = "Şehir";
"state" = "İl/İlçe";
"country" = "Ülke";
"postal_code" = "Posta Kodu";
"phone" = "Telefon";
"email" = "E-posta";
"website" = "Web Sitesi";
"company" = "Şirket";
"notes" = "Notlar";
"coordinates" = "Koordinatlar";
"latitude" = "Enlem";
"longitude" = "Boylam";
"geocoding_error" = "Coğrafi Kodlama Hatası";
"address_validation" = "Adres Doğrulama";
"invalid_addresses" = "Geçersiz Adresler";
"fix_addresses" = "Adresleri Düzelt";

// MARK: - Routes
"route" = "Rota";
"routes" = "Rotalar";
"select_address_point" = "Adres noktası seç";
"select_delivery_points" = "Teslimat noktası seç";
"create_delivery_route" = "Teslimat rotası oluştur";
"view_saved_routes" = "Kaydedilmiş rotaları görüntüle";
"create_route" = "Rota Oluştur";
"edit_route" = "Rota Düzenle";
"delete_route" = "Rota Sil";
"route_name" = "Rota Adı";
"route_details" = "Rota Detayları";
"selected_addresses" = "%d adres seçildi";
"reached_limit" = "Üst sınıra ulaşıldı";
"can_select_more" = "%d daha seçilebilir";
"navigate_button" = "Navigasyon";
"create_group" = "Grup Oluştur";
"start_point" = "Başlangıç Noktası";
"end_point" = "Bitiş Noktası";
"waypoints" = "Yol İşaretleri";
"total_distance" = "Toplam Mesafe";
"estimated_time" = "Tahmini Süre";
"route_summary" = "Rota Özeti";
"route_options" = "Rota Seçenekleri";
"route_saved" = "Rota Kaydedildi";
"route_optimized" = "Rota Optimize Edildi";
"optimizing_route" = "Rota Optimize Ediliyor...";
"completed_percent" = "Tamamlanan %d%%";
"processing_points" = "İşleniyor: %d/%d";
"estimated_remaining_time" = "Tahmini Kalan Süre: %@";

// MARK: - Delivery
"delivery" = "Teslimat";
"delivery_confirmation" = "Teslimat Onayı";
"take_photo" = "Fotoğraf Çek";
"signature" = "İmza";
"delivery_notes" = "Teslimat Notları";
"delivery_status" = "Teslimat Durumu";
"delivered" = "Teslimat Edildi";
"not_delivered" = "Teslimat Edilmedi";
"delivery_time" = "Teslimat Zamanı";
"delivery_date" = "Teslimat Tarihi";
"package_details" = "Paket Detayları";
"package_id" = "Paket ID";
"package_weight" = "Paket Ağırlığı";
"package_dimensions" = "Paket Boyutları";
"recipient_name" = "Alıcı Adı";
"recipient_phone" = "Alıcı Telefonu";

// MARK: - Groups
"groups" = "Gruplar";
"saved_groups" = "Kaydedilmiş gruplar";
"create_group" = "Grup Oluştur";
"edit_group" = "Grup Düzenle";
"delete_group" = "Grup Sil";
"group_name" = "Grup Adı";
"group_details" = "Grup Detayları";
"auto_grouping" = "Otomatik Gruplama";
"group_by" = "Gruplandırma Kriterleri";
"add_to_group" = "Grup Ekle";
"remove_from_group" = "Grupdan Çıkar";
"group_created" = "Grup Oluşturuldu";
"default_group_name_format" = "Grup%d";
"auto_grouping_completed" = "Otomatik Gruplama Tamamlandı";
"auto_grouping_in_progress" = "Otomatik Gruplama Devam Ediyor...";
"create_group_every_14_addresses" = "Her 14 adres için bir grup oluşturun";
"create_delivery_group" = "Teslimat grubu oluştur";
"enter_group_name" = "Grup adı girin";
"selected_delivery_points" = "Seçilen teslimat noktaları";
"drag_to_adjust_order" = "Sıralamayı ayarlamak için sürükleyin";

// MARK: - Subscription
"subscription" = "Abonelik";
"free_plan" = "Ücretsiz Plan";
"pro_plan" = "Pro Plan";
"expert_plan" = "Uzman Plan";
"monthly" = "Aylık Plan";
"yearly" = "Yıllık Plan";
"subscribe" = "Abone Ol";
"upgrade" = "Yükselt";
"upgrade_to_pro" = "Pro Sürümüne Yükselt";
"manage_subscription" = "Abonelik İşlemleri";
"restore_purchases" = "Satın Alımları Geri Yükle";
"subscription_benefits" = "Abonelik Faydaları";
"free_trial" = "Ücretsiz Deneme";
"price_per_month" = "Aylık %@";
"price_per_year" = "Yıllık %@";
"save_percent" = "Ekonomize %@%";
"current_plan" = "Mevcut Plan";
"subscription_terms" = "Abonelik Şartları";
"privacy_policy" = "Gizlilik Politikası";
"terms_of_service" = "Hizmet Şartları";

// MARK: - Import/Export
"import" = "İçe Aktar";
"export" = "Dışa Aktar";
"import_addresses" = "Adresleri İçe Aktar";
"export_addresses" = "Adresleri Dışa Aktar";
"import_from_file" = "Dosyadan İçe Aktar";
"export_to_file" = "Dosyaya Dışa Aktar";
"file_format" = "Dosya Formatı";
"csv_format" = "CSV Formatı";
"excel_format" = "Excel Formatı";
"json_format" = "JSON Formatı";
"import_success" = "%d adres başarıyla içe aktarıldı, hepsi geçerli koordinatlara sahip.";
"export_success" = "Dışa Aktarma Başarılı";
"import_error" = "İçe Aktarma Hatası";
"export_error" = "Dışa Aktarma Hatası";

// MARK: - Navigation
"navigate" = "Navigasyon";

// MARK: - Look Around
"show_look_around" = "Sokak görünümünü göster";
"hide_look_around" = "Sokak görünümünü gizle";

// MARK: - Map
"map" = "Harita";
"map_type" = "Harita Türü";
"standard" = "Standart";
"satellite" = "Uydu";
"hybrid" = "Hibrit";
"show_traffic" = "Trafik Durumunu Göster";
"current_location" = "Mevcut Konum";
"directions" = "Yön İşaretleri";
"distance_to" = "Mesafe";
"eta" = "Tahmini Varış Zamanı";
"look_around" = "Etrafını İncele";
"locating_to_glen_waverley" = "Glen Waverley'e Konumlandırılıyor";

// MARK: - Errors and Warnings
"network_error" = "Ağ Hatası";
"location_error" = "Konum Hatası";
"permission_denied" = "İzin Reddedildi";
"location_permission_required" = "Konum İzni Gerekli";
"camera_permission_required" = "Kamera İzni Gerekli";
"photo_library_permission_required" = "Fotoğraf Kitabı İzni Gerekli";
"please_try_again" = "Lütfen Tekrar Deneyin";
"something_went_wrong" = "Bir şeyler ters gitti";
"invalid_input" = "Geçersiz Giriş";
"required_field" = "Gerekli Alan";
"no_internet_connection" = "İnternet Bağlantısı Yok";
"server_error" = "Sunucu Hatası";
"timeout_error" = "İstek Zaman Aşımı";
"data_not_found" = "Veri Bulunamadı";
"selection_limit_reached" = "Seçim Limiti Aşıldı";
"selection_limit_description" = "En fazla %d adres seçebilirsiniz, %d seçtiniz";

// MARK: - Location Validation Status
"location_status_valid" = "Geçerli aralık";
"location_status_warning" = "Uyarı aralığı";
"location_status_invalid" = "Geçersiz konum";
"location_status_unknown" = "Bilinmeyen durum";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Geçersiz: sıfır koordinat (0,0)";
"coordinates_invalid_nan" = "Geçersiz: sayısal olmayan koordinat";
"coordinates_out_of_range" = "Geçersiz: koordinat geçerli aralığın dışında";
"coordinates_far_from_user" = "Uyarı: konum mevcut konumunuzdan uzak";
"coordinates_ocean" = "Uyarı: konum okyanus veya ıssız bölgede olabilir";

// MARK: - Batch Address Input
"batch_add_addresses" = "Toplu adres ekleme";
"batch_address_input_placeholder" = "Lütfen adresleri girin veya yapıştırın, her satırda bir adres. En fazla 35 adres.";
"free_address_limit" = "Ücretsiz sürüm adres sınırı";
"address_count_limit" = "Adres sayısı sınırı";
"free_version_max_addresses" = "Ücretsiz sürüm en fazla %d adrese izin verir.";
"current_addresses_remaining" = "Şu anda %d adres var, sadece %d adres daha ekleyebilirsiniz.";
"current_route_address_limit" = "Mevcut rotada %d adres var, sadece %d adres daha ekleyebilirsiniz, toplam en fazla %d adres.";
"selected_addresses_can_import" = "%d adres seçtiniz, bu adresleri içe aktarabilirsiniz.";
"selected_addresses_exceeds" = "%d adres seçtiniz, eklenebilir sayıyı %d adet aştınız.";
"selected_addresses_all_importable" = "%d adres seçtiniz, hepsini içe aktarabilirsiniz.";
"upgrade_for_unlimited_addresses" = "Premium sürüme yükselterek sınırsız adres kullanın!";
"import_first_n_addresses" = "Sadece ilk %d'ü içe aktar";
"import_all_addresses" = "Tüm adresleri içe aktar";
"import_selected_addresses" = "Seçilen adresleri içe aktar";
"no_importable_addresses" = "İçe aktarılabilir adres yok, adres sınırlarını kontrol edin";
"please_enter_valid_address" = "Lütfen en az bir geçerli adres girin";

// MARK: - File Import
"import_success" = "%d adres başarıyla içe aktarıldı, hepsi geçerli koordinatlara sahip.";
"import_success_with_warnings" = "%d adres başarıyla içe aktarıldı, %d adresin koordinatları normal, %d adresde uyarı var.

Uyarılı adresler işaretlendi, içe aktarma sonrası manuel olarak düzeltilebilir.";

// MARK: - Web Download
"web_download" = "Web'den İndir";
"supported_formats" = "Desteklenen formatlar";
"supported_format_csv" = "• CSV dosyası: Adres sütunu tam adres içermelidir";
"supported_format_json" = "• JSON verisi: Adres alanı içeren dizi";
"supported_format_text" = "• Düz metin: Her satırda bir adres";
"download_history" = "İndirme geçmişi";
"upgrade_to_premium" = "Premium sürüme yükselt";
"input_address_data_url" = "Adres verisi URL'si girin";
"import_result" = "İçe aktarma sonucu";
"import_addresses" = "Adresleri İçe Aktar";
"downloading" = "İndiriliyor...";
"processing_data" = "Veri işleniyor...";
"google_drive_download_failed" = "Google Drive indirme başarısız";
"second_attempt_invalid_data" = "İkinci indirme denemesi geçersiz veri döndürdü";
"cannot_parse_json" = "JSON verisi ayrıştırılamıyor, dosya formatını kontrol edin";
"cannot_parse_json_with_error" = "JSON verisi ayrıştırılamıyor: %@";
"cannot_get_address_coordinates" = "Adres koordinatları alınamıyor";
"cannot_read_file" = "Dosya okunamıyor: %@";
"success" = "Başarılı";
"warning" = "Uyarı";
"failed" = "Teslimat başarısız";
"no_matching_addresses" = "Eşleşen adres bulunamadı";
"no_valid_addresses" = "Geçerli adres bulunamadı";
"confirm" = "Onayla";
"processing_addresses" = "Adresler işleniyor...";
"supports_file_types" = "CSV, TXT ve JSON dosyalarını destekler";
"tap_to_select_file" = "Dosya seçmek için dokunun";
"import_addresses" = "Adresleri İçe Aktar";
"company_name_optional" = "Şirket Adı (İsteğe bağlı)";
"input_company_name" = "Şirket adı girin (isteğe bağlı)";
"imported_addresses_count" = "%d adres içe aktarıldı";
"select_all" = "Tümünü seç";
"excel_format_not_supported" = "Excel formatı desteklenmiyor";
"no_matching_addresses" = "Eşleşen adres bulunamadı";

// MARK: - Import Limits
"import_failed" = "İçe aktarma başarısız";
"no_importable_addresses" = "İçe aktarılabilir adres yok, adres sınırlarını kontrol edin";
"free_version_address_limit" = "Ücretsiz sürüm en fazla %d adrese izin verir.";
"current_address_count" = "Şu anda %d adres var, sadece %d adres daha ekleyebilirsiniz.";
"can_import_selected" = "%d adres seçtiniz, bu adresleri içe aktarabilirsiniz.";
"selected_exceeds_limit" = "%d adres seçtiniz, eklenebilir sayıyı %d adet aştınız.";
"upgrade_to_premium_unlimited" = "Premium sürüme yükselterek sınırsız adres kullanın!";
"route_address_limit" = "Mevcut rotada %d adres var, sadece %d adres daha ekleyebilirsiniz, toplam en fazla %d adres.";
"free_version_limit" = "Ücretsiz sürüm adres sınırı";
"address_count_limit" = "Adres sayısı sınırı";
"import_selected_addresses" = "Seçilen adresleri içe aktar";
"import_first_n" = "Sadece ilk %d'ü içe aktar";
"import_all_n" = "Tümünü içe aktar %d";
"cannot_import" = "İçe aktarılamıyor";
"select_at_least_one" = "Lütfen en az bir adres seçin";

// MARK: - Import Results
"no_valid_addresses_found" = "Geçerli adres bulunamadı";
"import_success_all_valid" = "%d adres başarıyla içe aktarıldı, tüm adres koordinatları normal.";
"import_success_some_warnings" = "%d adres başarıyla içe aktarıldı, %d adresin koordinatları normal, %d adresin koordinatları alınamadı.";

// MARK: - Warnings
"invalid_csv_row" = "Geçersiz CSV satırı";
"distance_warning" = "Mevcut konumdan 200 km'den fazla uzaklık";
"not_in_australia" = "Koordinatlar Avustralya sınırları içinde değil";
"cannot_get_coordinates" = "Adres koordinatları alınamıyor";
"empty_address" = "Boş adres";
"invalid_address_data" = "Geçersiz adres verisi";

// MARK: - Saved Groups
"saved_groups" = "Kaydedilmiş gruplar";
"no_saved_groups" = "Kaydedilmiş grup yok";
"select_points_create_groups" = "Teslimat noktalarını seçin ve yönetimi kolaylaştırmak için grup oluşturun";
"group_name" = "Grup Adı";
"group_details" = "Grup Detayları";
"navigate_to_these_points" = "Bu noktalara git";
"confirm_remove_address" = "\"%@\" adresini gruptan kaldırmak istediğinizden emin misiniz?";
"confirm_remove_this_address" = "Bu adresi gruptan kaldırmak istediğinizden emin misiniz?";
"addresses_count" = "%d adres";
"no_saved_routes" = "Kaydedilmiş rota yok";
"no_saved_routes_description" = "Henüz herhangi bir rota kaydetmediniz";
"all_routes" = "Tüm rotalar";
"address_count_format_simple" = "%d adres";
"delete_all_routes" = "Tüm rotaları sil";
"navigate_to_all_points" = "Tüm noktalara git";
"confirm_navigate_to_route" = "\"%@\" rotasındaki tüm noktalara gitmek istediğinizden emin misiniz?";
"temp_navigation_group" = "Geçici navigasyon grubu";

// MARK: - Route Management
"route_management" = "Rota yönetimi";
"route_info" = "Rota Bilgisi";
"route_name" = "Rota Adı";
"route_addresses" = "Rota adresleri";
"no_addresses_in_route" = "Bu rotada adres yok";
"must_keep_one_route" = "En az bir rota tutulmalıdır";
"confirm_delete_route" = "\"%@\" rotasını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"confirm_delete_all_routes" = "Tüm rotaları silmeyi onayla";
"confirm_delete_all_routes_message" = "Tüm rotaları silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"delete_all" = "Tümünü sil";

// MARK: - Navigation Buttons
"navigate" = "Navigasyon";

// MARK: - GroupDetailView
"points_count_format" = "%d nokta";

// MARK: - DeliveryPointDetailView
"address_information" = "Adres bilgisi";
"group_belonging" = "Ait olduğu grup";
"view_map" = "Haritayı görüntüle";
"delivery_status" = "Teslimat Durumu";
"notes" = "Notlar";
"delete_delivery_point" = "Teslimat noktasını sil";
"delivery_point_details" = "Teslimat noktası detayları";
"confirm_deletion" = "Silmeyi onayla";
"delete_delivery_point_confirmation" = "Bu teslimat noktasını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";

// MARK: - Delivery Photos
"delivery_photos" = "Teslimat fotoğrafları";
"view_delivery_photos" = "Teslimat fotoğraflarını görüntüle";
"no_photos_taken" = "Henüz fotoğraf çekilmedi";
"take_photos" = "Fotoğraf çek";
"loading_photos" = "Fotoğraflar yükleniyor...";
"photo_not_found" = "Fotoğraf bulunamadı";
"photo_deleted" = "Fotoğraf silindi";
"door_number_photo" = "Kapı Numarası Fotoğrafı";
"package_label_photo" = "Paket Etiketi Fotoğrafı";
"placement_photo" = "Yerleştirme Fotoğrafı";
"share_photos" = "Fotoğrafı paylaş";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Fotoğraf onayı";
"door_number_photo_title" = "Yol numarası/kapı numarası fotoğrafı";
"package_label_photo_title" = "Paket etiketi fotoğrafı";
"placement_photo_title" = "Yerleştirme konumu fotoğrafı";
"door_number_photo_desc" = "Lütfen kapı numarasını net çekin, rakam/harflerin görünür olduğundan emin olun";
"package_label_photo_desc" = "Lütfen paket etiketini çekin, alıcı bilgilerinin net görünür olduğundan emin olun";
"placement_photo_desc" = "Lütfen paketin son yerleştirildiği konumu çekin";
"swipe_to_switch" = "Fotoğraf türünü değiştirmek için kaydırın";
"complete_photos" = "Fotoğraf çekmeyi tamamla";
"saving_photos" = "Fotoğraflar kaydediliyor...";
"photo_save_success" = "Fotoğraf başarıyla kaydedildi";
"photo_save_failure" = "Fotoğraf kaydetme başarısız";
"retake_photo" = "Tekrar çek";
"no_photos_found" = "Fotoğraf bulunamadı";
"photos_deleted_or_not_taken" = "Fotoğraf silinmiş veya henüz çekilmemiş olabilir";
"share_photo" = "Fotoğrafı paylaş";
"photo_capture_preview" = "Önizleme modu - kamera simülasyonu";
"photo_capture_close" = "Kapat";
"camera_start_failed" = "Kamera başlatma başarısız";
"camera_start_failed_retry" = "Kamera başlatılamıyor, lütfen tekrar deneyin";
"camera_init_failed" = "Kamera başlatma başarısız";
"camera_access_failed" = "Kameraya erişilemiyor";
"photo_processing_failed" = "Fotoğraf çekme başarısız";
"photo_processing_failed_retry" = "Fotoğraf işleme tamamlanamıyor, lütfen tekrar deneyin";
"photo_capture_progress" = "İlerleme: %d/%d";
"photo_captured_continue" = "Çekim tamamlandı, %@ çekmeye devam et";
"loading_photos" = "Fotoğraflar yükleniyor...";
"cancel" = "İptal";

// MARK: - Delivery Status
"pending" = "Teslimat bekliyor";
"in_progress" = "Teslimat yapılıyor";
"completed" = "Tamamlandı";
"failed" = "Teslimat başarısız";
"update_status" = "Durumu Güncelle";
"select_delivery_status" = "Teslimat durumu seç";
"select_failure_reason" = "Başarısızlık nedenini seç";
"delivered" = "Teslimat Edildi";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Müşteri evde değil";
"failure_reason_wrong_address" = "Yanlış adres";
"failure_reason_no_access" = "Konuma erişilemiyor";
"failure_reason_rejected" = "Paket reddedildi";
"failure_reason_other" = "Diğer nedenler";
"enter_custom_reason" = "Belirli nedeni girin";
"custom_reason_placeholder" = "Lütfen belirli nedeni açıklayın...";
"custom_reason_required" = "Lütfen belirli nedeni girin";
"failure_reason_required" = "Lütfen başarısızlık nedenini seçin";

// MARK: - Address Validation
"address_validation_failed" = "Adres doğrulama başarısız";

