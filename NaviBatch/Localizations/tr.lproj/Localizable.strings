/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-06-03.
  Auto-synced with English base.

*/
"language_settings" = "Dil Ayarları";
"system_language" = "Sistem Dili";
"system_language_section" = "Sistem Ayarları";
"languages" = "Diller";
"language_info_title" = "Dil Ayarları Hakkında";
"language_info_description" = "Dil ayarlarını değiştirdikten sonra, uygulama seçilen dilde metin gösterecektir. Bazı içerikler, yeni dil ayarlarını tam olarak uygulamak için uygulamanın yeniden başlatılmasını gerektirebilir.";
"restart_required" = "Yeniden Başlatma Gerekli";
"restart_app_message" = "Dil değişikliklerini tam olarak uygulamak için lütfen uygulamayı yeniden başlatın.";
"restart_now" = "Şimdi Yeniden Başlat";
"restart_later" = "Daha Sonra Yeniden Başlat";
"close" = "Kapat";
"cancel" = "İptal";
"save" = "Kaydet";
"edit" = "Düzenle";
"delete" = "Sil";
"done" = "Tamam";
"next" = "İleri";
"back" = "Geri";
"confirm" = "Onayla";
"error" = "Hata";
"success" = "Başarılı";
"warning" = "Uyarı";
"unknown_error" = "Bilinmeyen hata";
"loading" = "Yükleniyor...";
"search" = "Ara";
"settings" = "Ayarlar";
"help" = "Yardım";
"about" = "Hakkında";
"menu" = "Menü";
"understand" = "Anlıyorum";
"navigation" = "Navigasyon";
"start_navigation" = "Navigasyonu Başlat";
"subscription" = "Abonelik";
"upgrade_to_pro" = "Pro Sürümüne Yükselt";
"upgrade_description" = "Tek tıklamayla navigasyon gruplama, manuel işlemden 60 kat daha hızlı";
"restore_purchases" = "Satın Alımları Geri Yükle";
"learn_more" = "Daha Fazla Bilgi";
"upgrade_your_plan" = "Planınızı Yükseltin";
"one_click_navigation_description" = "Adresleri tek tıklamayla gruplandırarak zaman ve yakıt tasarrufu sağlayın";
"current_plan" = "Mevcut Plan";
"upgrade" = "Yükselt";
"maybe_later" = "Belki Daha Sonra";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Ücretsiz";
"pro_tier_price" = "$9.99/ay";
"expert_tier_price" = "$59.99/yıl";
"free_tier_description" = "Çok sayıda durak noktasına ihtiyaç duymayan bireyler ve küçük işletmeler için uygundur";
"pro_tier_description" = "Tek tıklamayla navigasyon gruplamaya ve sınırsız adrese ihtiyaç duyan kullanıcılar için uygundur";
"expert_tier_description" = "Pro ile aynı, ancak yıllık plan üzerinden %83 tasarruf";
"route_optimization" = "Rota Optimizasyonu";
"unlimited_routes" = "Sınırsız Rotalar";
"unlimited_optimizations" = "Sınırsız Optimizasyonlar";
"max_15_addresses" = "Rota başına maksimum 15 adres";
"save_fuel_30" = "%30'a kadar yakıt tasarrufu";
"unlimited_addresses" = "✨ Sınırsız Adresler ✨";
"one_click_navigation" = "⚡ Tek Tıklamayla Navigasyon Gruplama - 60x Daha Hızlı ⚡";
"package_finder" = "Paket Konum Bulucu";
"annual_savings" = "Yıllık Tasarruf";
"switched_to_free" = "Ücretsiz Plana Geçildi";
"switched_to_subscription" = "Abonelik Planına Geçildi";
"unlimited_stops" = "Sınırsız Duraklar";
"plan_as_many_stops_as_needed" = "İstediğiniz kadar teslimat noktası ekleyin, sınırsız";
"expires_in_days" = "%d gün içinde sona eriyor";
"trial_expires_in_days" = "Deneme süresi %d gün içinde sona eriyor";
"expired" = "Süresi dolmuş";
"subscription_expires_on" = "Abonelik %@ tarihinde sona eriyor";
"subscription_active_until" = "Abonelik %@ tarihine kadar aktif";
"enter_or_search_address" = "Adres girin veya arayın";
"search_results_count" = "Arama sonuçları: %d";
"no_matching_addresses" = "Eşleşen adres bulunamadı";
"search_address_failed" = "Adres araması başarısız oldu: %@";
"address_search_no_response" = "Adres aramasından yanıt yok";
"cannot_get_address_coordinates" = "Adres koordinatları alınamıyor";

"cannot_get_coordinates_retry" = "Adres koordinatları alınamıyor, lütfen manuel olarak girin veya tekrar deneyin";

"image_address_recognition" = "Görüntüden adres tanıma";
"select_images" = "Görüntüleri seç";
"select_multiple_images" = "Çoklu seçim destekleniyor";
"processing_images" = "Görüntüler işleniyor...";
"processing_image_progress" = "%d/%d görüntü işleniyor";
"recognizing_text" = "Metin tanınıyor...";
"geocoding_addresses" = "Adres koordinatları alınıyor...";
"recognition_complete" = "Tanıma tamamlandı";
"no_text_recognized" = "Hiçbir metin tanınmadı";
"no_addresses_found" = "Geçerli adres bulunamadı";
"image_recognition_failed" = "Görüntü tanıma başarısız";
"image_recognition_error" = "Görüntü tanıma hatası: %@";
"text_recognition_failed" = "Metin tanıma başarısız";
"address_parsing_failed" = "Adres ayrıştırma başarısız";
"select_addresses_to_add" = "Eklenecek adresleri seç";
"recognized_addresses" = "Tanınan adresler";
"address_coordinates" = "Adres koordinatları";
"toggle_address_selection" = "Adres seçimini değiştir";
"remove_address" = "Adresi kaldır";
"confirm_selected_addresses" = "Seçilen adresleri onayla";
"no_addresses_selected" = "Hiçbir adres seçilmedi";
"image_processing_cancelled" = "Görüntü işleme iptal edildi";
"unsupported_image_format" = "Desteklenmeyen görüntü formatı";
"image_too_large" = "Görüntü dosyası çok büyük";
"image_recognition_permission_required" = "Fotoğraf galerisi erişim izni gerekli";
"ocr_language_detection" = "Otomatik dil algılama";
"improve_image_quality" = "Görüntünün net olduğundan ve metnin görünür olduğundan emin olun";
"address_validation_in_progress" = "Adres doğrulama devam ediyor...";
"batch_address_import" = "Toplu adres içe aktarma";
"validated_addresses_count" = "%d adres doğrulandı";
"addresses_with_issues" = "%d adres sorunlu";
"select_all" = "Tümünü seç";
"import_selected" = "Seçilenleri içe aktar";
"validating_addresses" = "Adresler doğrulanıyor...";
"empty_address" = "Boş adres";
"invalid_coordinates" = "Geçersiz koordinatlar";
"coordinate_warning" = "Koordinat uyarısı";
"address_validation_issue" = "Adres doğrulama sorunu";
"cannot_get_coordinates" = "Adres koordinatları alınamıyor";
"no_importable_addresses" = "İçe aktarılabilir adres yok, adres sınırlarını kontrol edin";
"free_version_max_addresses" = "Ücretsiz sürüm en fazla %d adrese izin verir.";
"valid" = "Geçerli";
"with_issues" = "Sorunlu";
"low_confidence_address" = "Düşük güvenilirlik adres doğrulaması";
"address_validation_failed" = "Adres doğrulama başarısız";
"current_addresses_remaining" = "Şu anda %d adres var, sadece %d adres daha ekleyebilirsiniz.";
"can_import_selected" = "%d adres seçtiniz, bu adresleri içe aktarabilirsiniz.";
"selected_exceeds_limit" = "%d adres seçtiniz, eklenebilir sayıyı %d adet aştınız.";
"selected_addresses_all_importable" = "%d adres seçtiniz, hepsini içe aktarabilirsiniz.";
"upgrade_for_unlimited_addresses" = "Premium sürüme yükselterek sınırsız adres kullanın!";
"current_route_address_limit" = "Mevcut rotada %d adres var, sadece %d adres daha ekleyebilirsiniz, toplam en fazla %d adres.";
"import_all_addresses" = "Tüm adresleri içe aktar";
"import_first_n" = "Sadece ilk %d'ü içe aktar";
"import_selected_addresses" = "Seçilen adresleri içe aktar";
"upgrade_to_premium" = "Premium sürüme yükselt";
"batch_add_addresses" = "Toplu adres ekleme";
"batch_address_input_placeholder" = "Lütfen adresleri girin veya yapıştırın, her satırda bir adres. En fazla 35 adres.";
"search_address" = "Adres Ara";
"no_saved_addresses" = "Kaydedilmiş adres yok";
"no_saved_addresses_description" = "Henüz herhangi bir adres kaydetmediniz veya filtreleme kriterlerine uyan adres yok";
"select_address_book" = "Adres Defteri Seç";
"routes" = "Rotalar";
"address_book" = "Adres Defteri";
"saved_routes" = "Kaydedilmiş Rotalar";
"manage_your_routes" = "Rota planlamanızı yönetin";
"manage_your_addresses" = "Sık kullandığınız adresleri yönetin";
"preferences" = "Tercihler";
"set_custom_start_point" = "Özel Başlangıç Noktası Ayarla";
"current_start_point" = "Mevcut başlangıç noktası: %@";
"support" = "Destek";
"contact_us" = "Bize Ulaşın";
"contact_us_description" = "Sorularınız veya önerileriniz mi var? Bizimle iletişime geçmekten çekinmeyin!";
"help_center" = "Yardım Merkezi";
"quick_actions" = "Hızlı işlemler";
"main_features" = "Ana özellikler";
"support_help" = "Destek ve yardım";
"customize_app_settings" = "Uygulama ayarlarını özelleştir";
"unlock_all_features" = "Tüm özellikleri aç";
"get_help_support" = "Yardım ve destek al";
"app_info_version" = "Uygulama bilgisi ve sürüm";
"dev_tools" = "Geliştirici araçları";
"debug_testing_tools" = "Hata ayıklama ve test araçları";
"version" = "Sürüm";
"addresses" = "Adresler";
"limited_to_20_addresses" = "20 adresle sınırlı";
"all_premium_features" = "Tüm premium özellikler";
"open_subscription_view" = "Abonelik Görünümünü Doğrudan Aç";
"open_subscription_view_description" = "Ara katmanı atlayarak doğrudan Abonelik Görünümünü göster";
"restore_purchases_failed" = "Satın alımları geri yükleme başarısız oldu: %@";
"rate_us" = "Bizi Değerlendirin";
"rate_us_description" = "Geri bildiriminiz bizim için önemlidir ve uygulamayı geliştirmemize yardımcı olur!";
"share_app" = "Uygulamayı Paylaş";
"share_app_text" = "NaviBatch'i deneyin, harika bir rota planlama uygulaması!";
"about_app" = "Uygulama Hakkında";
"developer_tools" = "Geliştirici Araçları";
"coordinate_debug_tool" = "Koordinat Hata Ayıklama Aracı";
"batch_fix_addresses" = "Toplu Adres Düzeltme";
"clear_database" = "Veritabanını Temizle";
"clear_database_confirmation" = "Bu işlem, rotalar, adresler ve gruplar dahil olmak üzere tüm verileri silecektir. Bu işlem geri alınamaz. Devam etmek istediğinizden emin misiniz?";
"confirm_clear" = "Onayla";
"version_info" = "Sürüm %@ (%@)";
"current_system_language" = "Mevcut Sistem Dili";
"reset_to_system_language" = "Sistem Diline Sıfırla";
"language" = "Dil";
"address" = "Adres";
"coordinates" = "Koordinatlar";
"distance_from_current_location" = "Mevcut Konumdan Uzaklık";
"address_info" = "Adres Bilgisi";
"update_coordinates" = "Koordinatları Güncelle";
"fix_address" = "Adresi Düzelt";
"prompt" = "İstem";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Koordinatları güncellemeden önce lütfen adresi değiştirin";
"coordinates_update_success" = "Koordinatlar başarıyla güncellendi";
"coordinates_update_failure" = "Koordinatlar güncellenemedi";
"save_failure" = "Kaydetme başarısız oldu: %@";
"no_saved_addresses_title" = "Kaydedilmiş Adres Yok";
"no_saved_addresses_message" = "Henüz herhangi bir adres kaydetmediniz";
"add_new_address" = "Yeni Adres Ekle";
"address_title" = "Adres";
"add" = "Ekle";
"refresh" = "Yenile";
"notes" = "Notlar";
"address_details" = "Adres Detayları";
"favorite" = "Favori";
"edit_address" = "Adresi Düzenle";
"confirm_delete" = "Silmeyi Onayla";
"delete_address_confirmation" = "Bu adresi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"address_marker" = "Adres";
"address_label" = "Adres:";
"notes_label" = "Notlar:";
"created_at_label" = "Oluşturulma:";
"open_in_maps" = "Haritalarda Aç";
"copy_address" = "Adresi Kopyala";
"address_details_title" = "Adres Detayları";
"start_end_point" = "Başlangıç/Bitiş Noktası";
"start_point" = "Başlangıç Noktası";
"end_point" = "Bitiş Noktası";
"route_info" = "Rota Bilgisi";
"address_count" = "Adres Sayısı";
"address_count_format" = "%d adres";
"points_count_format" = "%d nokta";
"additional_points_format" = "+%d nokta";
"export_route" = "Rotayı Dışa Aktar";
"navigate" = "Navigasyon";
"address_list" = "Adres Listesi";
"no_addresses" = "Adres Yok";
"no_addresses_message" = "Bu rotada henüz adres yok";
"address_point_start" = "Başlangıç Noktası";
"address_point_stop" = "Durak Noktası";
"address_point_end" = "Bitiş Noktası";
"route_name" = "Rota Adı";
"new_route" = "Yeni Rota";
"saved_route" = "Kaydedilmiş Rota";
"plan_route" = "Rota Planla";
"clear_all" = "Tümünü Temizle";
"avoid" = "Kaçınılacaklar:";
"toll_roads" = "Ücretli Yollar";
"highways" = "Otoyollar";
"processing_addresses" = "Adresler işleniyor...";
"same_start_end_point" = "Aynı adresi hem başlangıç hem de bitiş noktası olarak ayarladınız";
"add_start_point" = "Başlangıç Noktası Ekle";
"swipe_left_to_delete" = "← Silmek için sola kaydırın";
"add_end_point" = "Bitiş Noktası Ekle";
"enter_and_select_address" = "Adres girin ve seçin";
"current_search_text" = "Mevcut arama metni: %@";
"add_address" = "Adres Ekle";
"selected_coordinates" = "Seçilen Koordinatlar";
"company_name_optional" = "Şirket Adı (İsteğe bağlı)";
"url_optional" = "URL (İsteğe bağlı)";
"favorite_address" = "Favori Adres";
"set_as_start_and_end" = "Başlangıç ve Bitiş Noktası Olarak Ayarla";
"batch_paste" = "Toplu Yapıştır";
"file_import" = "Dosyadan İçe Aktar";
"web_download" = "Web'den İndir";
"saving" = "Kaydediliyor...";
"delivery_point_management" = "Teslimat Noktası Yönetimi";
"information_category" = "Bilgi Kategorisi";
"package_info" = "Paket Bilgisi";
"vehicle_position" = "Araç Pozisyonu";
"delivery_info" = "Teslimat Bilgisi";
"take_photo_record" = "Fotoğraf Çek";
"update_status" = "Durumu Güncelle";
"edit_address_button" = "Adresi Düzenle";
"access_instructions" = "Erişim Talimatları";
"add_access_instructions" = "Erişim talimatları ekle...";
"package_count" = "Paket Sayısı";
"packages" = "paketler";
"package_unit" = "adet";
"package_size" = "Paket Boyutu";
"package_type" = "Paket Tipi";
"mark_as_important" = "Önemli Olarak İşaretle";
"priority_delivery" = "Öncelikli teslimat";
"priority_level" = "Öncelik seviyesi";
"priority_1" = "Öncelik 1";
"priority_2" = "Öncelik 2";
"priority_3" = "Öncelik 3";
"no_priority" = "Öncelik yok";
"no_priority_short" = "Yok";
"set_priority" = "Öncelik belirle";
"select_package_position" = "Araçtaki Paket Pozisyonunu Seçin";
"vehicle_area" = "Araç Bölgesi";
"left_right_position" = "Sol/Sağ Pozisyon";
"vehicle_position_front" = "Ön";
"vehicle_position_middle" = "Orta";
"vehicle_position_back" = "Arka";
"vehicle_position_left" = "Sol";
"vehicle_position_right" = "Sağ";
"vehicle_position_floor" = "Alt";
"vehicle_position_shelf" = "Üst";
"height_position" = "Yükseklik Pozisyonu";
"vehicle_position_none" = "Hiçbir pozisyon seçilmedi";
"delivery_type" = "Teslimat Tipi";
"delivery_status" = "Teslimat Durumu";
"order_info" = "Sipariş Bilgisi";
"order_information" = "Sipariş Bilgisi";
"order_number" = "Sipariş Numarası";
"enter_order_number" = "Sipariş numarası girin";
"tracking_number" = "Takip Numarası";
"enter_tracking_number" = "Takip numarası girin";
"tracking_info" = "Takip bilgisi";
"tracking_information" = "Takip bilgileri";
"time_info" = "Zaman Bilgisi";
"time_information" = "Zaman Bilgisi";
"estimated_arrival_time" = "Tahmini Varış Zamanı";
"anytime" = "Herhangi Bir Zamanda";
"stop_time" = "Durma Süresi";
"minutes_format" = "%d dakika";
"photo_record" = "Fotoğraf Kaydı";
"door_number_photo" = "Kapı Numarası Fotoğrafı";
"package_label_photo" = "Paket Etiketi Fotoğrafı";
"placement_photo" = "Yerleştirme Fotoğrafı";
"door_number_desc" = "Lütfen kapı numarasını veya sokak numarasını net bir şekilde çekin, rakam/harflerin görünür olduğundan emin olun";
"package_label_desc" = "Lütfen paket etiketini çekin, alıcı bilgilerinin net görünür olduğundan emin olun";
"placement_desc" = "Lütfen paketin son yerleştirildiği konumu çekin";
"photo_captured" = "Fotoğraf çekildi";
"photo_captured_options" = "Fotoğraf çekildi, bir sonraki fotoğrafı çekmek mi istiyorsunuz yoksa mevcut fotoğrafı tamamlamak mı?";
"continue_to_next_photo" = "Sonrakini çek - %@";
"retake" = "Tekrar çek";
"tap_to_capture" = "Çekmek için dokunun";
"flash_auto" = "Otomatik flaş";
"flash_on" = "Flaşı aç";
"flash_off" = "Flaşı kapat";
"photo_record_completed" = "Fotoğraf Kaydı Tamamlandı";
"photo_confirmation" = "Fotoğraf onayı";
"ok" = "Tamam";
"complete_photo_capture" = "Fotoğraf çekmeyi tamamla";
"photo_instructions" = "Her fotoğraf kartına dokunarak çekim yapın. Tüm fotoğraflar tamamlanmalıdır.";
"photo_options" = "Fotoğraf seçenekleri";
"view_photo" = "Fotoğrafı görüntüle";
"retake_photo" = "Tekrar çek";
"saving_photos" = "Fotoğraflar kaydediliyor...";
"completed" = "Tamamlandı";
"not_taken" = "Çekilmedi";
"route_options" = "Rota Seçenekleri";
"avoid_tolls" = "Ücretli Yollardan Kaçın";
"avoid_highways" = "Otoyollardan Kaçın";
"optimize_route" = "Rotayı Optimize Et";
"optimizing" = "Optimize Ediliyor...";
"optimization_complete" = "Optimizasyon Tamamlandı";
"route_optimization_results" = "Rota optimizasyonu sonuçları";
"route_planning_options" = "Rota planlama seçenekleri";
"before_optimization" = "Optimizasyon öncesi";
"after_optimization" = "Optimizasyon sonrası";
"auto_group" = "Otomatik gruplama";
"optimized_route_order" = "Optimize edilmiş rota sırası";
"apply" = "Uygula";
"kilometers" = "kilometre";
"street_number_issue_warning" = "⚠️ Sokak numarasında büyük fark! Bu yanlış adrese teslimat ve cezalara neden olabilir. Adresi hemen kontrol edin";
"address_validation_critical" = "Kritik adres doğrulama sorunu";
"street_number_difference_high_risk" = "Büyük sokak numarası farkı, yüksek risk";
"delete_address" = "Adresi Sil";
"street" = "Sokak";
"city" = "Şehir";
"state" = "İl/İlçe";
"country" = "Ülke";
"postal_code" = "Posta Kodu";
"phone" = "Telefon";
"email" = "E-posta";
"website" = "Web Sitesi";
"company" = "Şirket";
"latitude" = "Enlem";
"longitude" = "Boylam";
"geocoding_error" = "Coğrafi Kodlama Hatası";
"address_validation" = "Adres Doğrulama";
"invalid_addresses" = "Geçersiz Adresler";
"fix_addresses" = "Adresleri Düzelt";
"route" = "Rota";
"select_address_point" = "Adres noktası seç";
"select_delivery_points" = "Teslimat noktası seç";
"create_delivery_route" = "Teslimat rotası oluştur";
"view_saved_routes" = "Kaydedilmiş rotaları görüntüle";
"create_route" = "Rota Oluştur";
"edit_route" = "Rota Düzenle";
"delete_route" = "Rota Sil";
"route_details" = "Rota Detayları";
"selected_addresses" = "%d adres seçildi";
"reached_limit" = "Üst sınıra ulaşıldı";
"can_select_more" = "%d daha seçilebilir";
"navigate_button" = "Navigasyon";
"create_group" = "Grup Oluştur";
"waypoints" = "Yol İşaretleri";
"total_distance" = "Toplam Mesafe";
"estimated_time" = "Tahmini Süre";
"route_summary" = "Rota Özeti";
"route_saved" = "Rota Kaydedildi";
"route_optimized" = "Rota Optimize Edildi";
"optimizing_route" = "Rota Optimize Ediliyor...";
"completed_percent" = "Tamamlanan %d%%";
"processing_points" = "İşleniyor: %d/%d";
"estimated_remaining_time" = "Tahmini Kalan Süre: %@";
"delivery" = "Teslimat";
"delivery_confirmation" = "Teslimat Onayı";
"take_photo" = "Fotoğraf Çek";
"signature" = "İmza";
"delivery_notes" = "Teslimat Notları";
"delivered" = "Teslimat Edildi";
"not_delivered" = "Teslimat Edilmedi";
"delivery_time" = "Teslimat Zamanı";
"delivery_date" = "Teslimat Tarihi";
"package_details" = "Paket Detayları";
"package_id" = "Paket ID";
"package_weight" = "Paket Ağırlığı";
"package_dimensions" = "Paket Boyutları";
"recipient_name" = "Alıcı Adı";
"recipient_phone" = "Alıcı Telefonu";
"groups" = "Gruplar";
"saved_groups" = "Kaydedilmiş gruplar";
"edit_group" = "Grup Düzenle";
"delete_group" = "Grup Sil";
"group_name" = "Grup Adı";
"group_details" = "Grup Detayları";
"auto_grouping" = "Otomatik Gruplama";
"group_by" = "Gruplandırma Kriterleri";
"add_to_group" = "Grup Ekle";
"remove_from_group" = "Grupdan Çıkar";
"group_created" = "Grup Oluşturuldu";
"default_group_name_format" = "Grup%d";
"auto_grouping_completed" = "Otomatik Gruplama Tamamlandı";
"auto_grouping_in_progress" = "Otomatik Gruplama Devam Ediyor...";
"create_group_every_14_addresses" = "Her 14 adres için bir grup oluşturun";
"create_delivery_group" = "Teslimat grubu oluştur";
"enter_group_name" = "Grup adı girin";
"selected_delivery_points" = "Seçilen teslimat noktaları";
"drag_to_adjust_order" = "Sıralamayı ayarlamak için sürükleyin";
"free_plan" = "Ücretsiz Plan";
"pro_plan" = "Pro Plan";
"expert_plan" = "Uzman Plan";
"monthly" = "Aylık Plan";
"yearly" = "Yıllık Plan";
"subscribe" = "Abone Ol";
"manage_subscription" = "Abonelik İşlemleri";
"subscription_benefits" = "Abonelik Faydaları";
"free_trial" = "Ücretsiz Deneme";
"price_per_month" = "Aylık %@";
"price_per_year" = "Yıllık %@";
"save_percent" = "Ekonomize %@%";
"subscription_terms" = "Abonelik Şartları";
"privacy_policy" = "Gizlilik Politikası";
"terms_of_service" = "Hizmet Şartları";
"feature_comparison" = "Özellik karşılaştırması";
"addresses_per_route" = "Rota başına adres";
"max_20_addresses" = "Maksimum 20 adres";
"fuel_savings" = "Yakıt tasarrufu";
"up_to_30_percent" = "%30'a kadar";
"choose_subscription_plan" = "Abonelik planı seç";
"monthly_plan" = "Aylık plan";
"yearly_plan" = "Yıllık plan";
"/month_suffix" = "/ay";
"/year_suffix" = "/yıl";
"save_30_percent" = "%30 tasarruf et";
"free_trial_7_days_cancel_anytime" = "7 gün ücretsiz deneme, istediğiniz zaman iptal edin";
"subscription_auto_renew_notice" = "Abonelik otomatik olarak yenilenir";
"and" = "ve";
"subscription_exclusive" = "Sadece aboneler için";
"free_version_optimization_limit" = "Ücretsiz sürüm sınırlı optimizasyon";
"free_version_supports_max_addresses" = "Ücretsiz sürüm maksimum adres destekler";
"current_route_contains_addresses" = "Mevcut rota adres içeriyor";
"upgrade_to_pro_unlimited_addresses" = "Sınırsız adres için Pro'ya yükseltin";
"continue_optimization" = "Optimizasyona devam et";
"upgrade_unlock_one_click_navigation" = "Tek tıkla navigasyonu açmak için yükseltin";
"learn_one_click_navigation_grouping" = "Tek tıkla navigasyon ve gruplama hakkında bilgi edinin";
"toggle_subscription_status" = "Abonelik durumunu değiştir";
"toggle_subscription_description" = "Test amaçlı abonelik ve ücretsiz arasında geçiş yap";
"product_info_unavailable" = "Ürün bilgisi mevcut değil";
"purchase_failed" = "Satın alma başarısız: %@";
"upgrade_to_pro_version" = "Pro sürümüne yükselt";
"unlock_all_premium_features" = "Tüm premium özellikleri aç";
"first_7_days_free_cancel_anytime" = "İlk 7 gün ücretsiz, istediğiniz zaman iptal edin";
"payment_terms_notice" = "Satın alma onaylandıktan sonra Apple ID hesabınızdan ödeme alınacaktır. Abonelik, mevcut dönemin bitiminden en az 24 saat önce iptal edilmedikçe otomatik olarak yenilenir.";
"terms_of_use" = "Kullanım şartları";
"product_load_failed_check_connection" = "Ürün bilgisi yüklenemedi, cihazın internete ve App Store'a bağlı olduğundan emin olun";
"product_load_failed" = "Ürün yüklenemedi: %@";
"verify_receipt" = "Makbuzu doğrula";
"one_click_navigation_short" = "Tek tıkla navigasyon";
"save_30_percent_fuel" = "%30 yakıt tasarrufu";
"monthly_short" = "Aylık";
"yearly_short" = "Yıllık";
"upgrade_now" = "Şimdi yükselt";
"test_environment_pro_activated" = "Test ortamı: Pro etkinleştirildi";
"payment_terms_notice_detailed" = "Satın alma onaylandıktan sonra Apple ID hesabınızdan ödeme alınacaktır. Abonelik, mevcut dönemin bitiminden en az 24 saat önce iptal edilmedikçe otomatik olarak yenilenir. Abonelikler App Store ayarlarından yönetilebilir ve iptal edilebilir.";
"step_screenshot" = "Adım %d ekran görüntüsü";
"previous_step" = "Önceki";
"next_step" = "Sonraki";
"each_address_takes_3_5_seconds" = "Her adres ekleme için 3-5 saniye alır";
"need_repeat_14_times" = "Aynı işlemi 14 kez tekrarlamak gerekir";
"navigation_order_often_confused" = "Navigasyon sırası sık sık karışır";
"error_prone_need_redo" = "Hata yapmaya meyilli, işlemleri tekrar yapmak gerekir";
"address_order_reversed_manual_adjust" = "Adres sırası ters, manuel ayarlama gerekir";
"one_click_add_all" = "Tek tıkla hepsini ekle";
"smart_grouping_auto_sorting" = "Akıllı gruplama, otomatik sıralama";
"maintain_correct_visit_order" = "Doğru ziyaret sırasını koru";
"zero_errors_zero_repetition" = "Sıfır hata, sıfır tekrar";
"import" = "İçe Aktar";
"export" = "Dışa Aktar";
"import_addresses" = "Adresleri İçe Aktar";
"export_addresses" = "Adresleri Dışa Aktar";
"import_from_file" = "Dosyadan İçe Aktar";
"export_to_file" = "Dosyaya Dışa Aktar";
"file_format" = "Dosya Formatı";
"csv_format" = "CSV Formatı";
"excel_format" = "Excel Formatı";
"json_format" = "JSON Formatı";
"import_success" = "%d adres başarıyla içe aktarıldı, hepsi geçerli koordinatlara sahip.";
"export_success" = "Dışa Aktarma Başarılı";
"import_error" = "İçe Aktarma Hatası";
"export_error" = "Dışa Aktarma Hatası";
"navigation_app" = "Navigasyon uygulaması";
"apple_maps" = "Apple Haritalar";
"app_preferences" = "Uygulama tercihleri";
"distance_unit" = "Mesafe birimi";
"current_language" = "Mevcut dil";
"info" = "Bilgi";
"contact_us_header" = "Bizimle İletişime Geçin";
"contact_us_subheader" = "Sorularınız veya önerileriniz mi var? Yardım etmekten memnuniyet duyarız!";
"contact_options" = "İletişim seçenekleri";
"email_us" = "Bize e-posta gönderin";
"contact_form" = "İletişim formu";
"contact_and_support" = "İletişim ve destek";
"common_questions" = "Sık sorulan sorular";
"how_to_use" = "Nasıl kullanılır";
"subscription_faq" = "Abonelik SSS";
"navigation_help" = "Navigasyon yardımı";
"troubleshooting" = "Sorun giderme";
"help_howto_content" = "NaviBatch, teslimat rotalarını optimize etmenize yardımcı olan güçlü bir rota planlama uygulamasıdır, zaman ve yakıt tasarrufu sağlar. Birden fazla adres ekleyebilir, rota sırasını otomatik olarak optimize edebilir ve tek tıkla Apple Maps'e geçebilirsiniz.";
"help_subscription_content" = "NaviBatch ücretsiz ve pro sürümler sunar. Ücretsiz sürüm 20 adrese kadar desteklerken, pro sürüm sınırsız adres ve tek tıkla grup navigasyon özellikleri sağlar.";
"help_navigation_content" = "NaviBatch navigasyon için Apple Maps kullanır. Her adrese ayrı ayrı navigasyon yapabilir veya birden fazla adrese aynı anda navigasyon için gruplama özelliğini kullanabilirsiniz.";
"help_troubleshooting_content" = "Sorun yaşıyorsanız, önce cihazınızın ağ bağlantısına sahip olduğundan ve konum izinlerinin verildiğinden emin olun. Sorunlar devam ederse, destek ekibimizle iletişime geçin.";
"actions" = "İşlemler";
"legal" = "Yasal";
"show_look_around" = "Sokak görünümünü göster";
"hide_look_around" = "Sokak görünümünü gizle";
"map" = "Harita";
"map_type" = "Harita Türü";
"standard" = "Standart";
"satellite" = "Uydu";
"hybrid" = "Hibrit";
"show_traffic" = "Trafik Durumunu Göster";
"current_location" = "Mevcut Konum";
"directions" = "Yön İşaretleri";
"distance_to" = "Mesafe";
"eta" = "Tahmini Varış Zamanı";
"look_around" = "Etrafını İncele";
"locating_to_glen_waverley" = "Glen Waverley'e Konumlandırılıyor";
"network_error" = "Ağ Hatası";
"location_error" = "Konum Hatası";
"permission_denied" = "İzin Reddedildi";
"location_permission_required" = "Konum İzni Gerekli";
"camera_permission_required" = "Kamera İzni Gerekli";
"photo_library_permission_required" = "Fotoğraf Kitabı İzni Gerekli";
"please_try_again" = "Lütfen Tekrar Deneyin";
"something_went_wrong" = "Bir şeyler ters gitti";
"invalid_input" = "Geçersiz Giriş";
"required_field" = "Gerekli Alan";
"no_internet_connection" = "İnternet Bağlantısı Yok";
"server_error" = "Sunucu Hatası";
"timeout_error" = "İstek Zaman Aşımı";
"data_not_found" = "Veri Bulunamadı";
"selection_limit_reached" = "Seçim Limiti Aşıldı";
"selection_limit_description" = "En fazla %d adres seçebilirsiniz, %d seçtiniz";
"location_status_valid" = "Geçerli aralık";
"address_validation_unknown" = "Doğrulanmamış";
"address_validation_valid" = "Geçerli";
"address_validation_invalid" = "Geçersiz";
"address_validation_warning" = "Uyarı";
"address_validation_mismatch" = "Eşleşmiyor";
"device_not_support_scanning" = "Cihaz yerel taramayı desteklemiyor";
"requires_ios16_a12_chip" = "iOS 16+ ve A12 çip veya daha yenisi gerekir";
"debug_info" = "Hata ayıklama bilgisi:";
"address_confirmation" = "Adres onayı";
"continue_scanning" = "Taramaya devam et";
"confirm_add" = "Eklemeyi onayla";
"cannot_get_coordinates_scan_retry" = "Adres koordinatları alınamıyor, manuel girin veya tekrar tarayın";
"unknown_country" = "Bilinmeyen ülke";
"unknown_city" = "Bilinmeyen şehir";
"please_enter_valid_address" = "Lütfen en az bir geçerli adres girin";
"please_select_valid_address" = "Lütfen geçerli bir adres seçin";
"add_address_failed" = "Adres ekleme başarısız";
"location_permission_required_for_current_location" = "Mevcut konumu almak için konum izni gerekli";
"cannot_get_current_location_check_settings" = "Mevcut konum alınamıyor, ayarları kontrol edin";
"cannot_get_current_location_address" = "Mevcut konum adresi alınamıyor";
"get_current_location_failed" = "Mevcut konum alma başarısız";
"location_status_warning" = "Uyarı aralığı";
"location_status_invalid" = "Geçersiz konum";
"location_status_unknown" = "Bilinmeyen durum";
"coordinates_origin_point" = "Geçersiz: sıfır koordinat (0,0)";
"coordinates_invalid_nan" = "Geçersiz: sayısal olmayan koordinat";
"coordinates_out_of_range" = "Geçersiz: koordinat geçerli aralığın dışında";
"coordinates_far_from_user" = "Uyarı: konum mevcut konumunuzdan uzak";
"coordinates_ocean" = "Uyarı: konum okyanus veya ıssız bölgede olabilir";
"free_address_limit" = "Ücretsiz sürüm adres sınırı";
"address_count_limit" = "Adres sayısı sınırı";
"selected_addresses_can_import" = "%d adres seçtiniz, bu adresleri içe aktarabilirsiniz.";
"selected_addresses_exceeds" = "%d adres seçtiniz, eklenebilir sayıyı %d adet aştınız.";
"supported_formats" = "Desteklenen formatlar";
"supported_format_csv" = "• CSV dosyası: Adres sütunu tam adres içermelidir";
"supported_format_json" = "• JSON verisi: Adres alanı içeren dizi";
"supported_format_text" = "• Düz metin: Her satırda bir adres";
"download_history" = "İndirme geçmişi";
"input_address_data_url" = "Adres verisi URL'si girin";
"import_result" = "İçe aktarma sonucu";
"downloading" = "İndiriliyor...";
"processing_data" = "Veri işleniyor...";
"google_drive_download_failed" = "Google Drive indirme başarısız";
"second_attempt_invalid_data" = "İkinci indirme denemesi geçersiz veri döndürdü";
"cannot_parse_json" = "JSON verisi ayrıştırılamıyor, dosya formatını kontrol edin";
"cannot_parse_json_with_error" = "JSON verisi ayrıştırılamıyor: %@";
"cannot_read_file" = "Dosya okunamıyor: %@";
"failed" = "Teslimat başarısız";
"no_valid_addresses" = "Geçerli adres bulunamadı";
"supports_file_types" = "CSV, TXT ve JSON dosyalarını destekler";
"tap_to_select_file" = "Dosya seçmek için dokunun";
"input_company_name" = "Şirket adı girin (isteğe bağlı)";
"imported_addresses_count" = "%d adres içe aktarıldı";
"excel_format_not_supported" = "Excel formatı desteklenmiyor";
"import_failed" = "İçe aktarma başarısız";
"free_version_address_limit" = "Ücretsiz sürüm en fazla %d adrese izin verir.";
"current_address_count" = "Şu anda %d adres var, sadece %d adres daha ekleyebilirsiniz.";
"upgrade_to_premium_unlimited" = "Premium sürüme yükselterek sınırsız adres kullanın!";
"route_address_limit" = "Mevcut rotada %d adres var, sadece %d adres daha ekleyebilirsiniz, toplam en fazla %d adres.";
"free_version_limit" = "Ücretsiz sürüm adres sınırı";
"import_all_n" = "Tümünü içe aktar %d";
"cannot_import" = "İçe aktarılamıyor";
"select_at_least_one" = "Lütfen en az bir adres seçin";
"no_valid_addresses_found" = "Geçerli adres bulunamadı";
"import_success_all_valid" = "%d adres başarıyla içe aktarıldı, tüm adres koordinatları normal.";
"import_success_some_warnings" = "%d adres başarıyla içe aktarıldı, %d adresin koordinatları normal, %d adresin koordinatları alınamadı.";
"company_format" = "Şirket formatı";
"added_from_web_download" = "Web indirmesinden eklendi";
"invalid_csv_row" = "Geçersiz CSV satırı";
"distance_warning" = "Mevcut konumdan 200 km'den fazla uzaklık";
"not_in_australia" = "Koordinatlar Avustralya sınırları içinde değil";
"invalid_address_data" = "Geçersiz adres verisi";
"distance_warning_confirm" = "Mevcut konumdan mesafe 200 km'yi aşıyor, devam et?";
"coordinates_missing" = "Koordinatlar eksik";
"low_accuracy_address" = "Düşük doğruluk adresi";
"address_partial_match" = "Adres kısmi eşleşme";
"address_outside_region" = "Adres bölge dışında";
"api_limit_reached" = "API limiti aşıldı";
"address_not_exist_or_incorrect_format" = "Adres mevcut değil veya yanlış format";
"please_check_address_spelling" = "Adres yazımını kontrol edin";
"try_smaller_street_number" = "Daha küçük sokak numarası deneyin";
"use_full_street_type_name" = "Tam sokak türü adını kullanın";
"try_add_more_address_details" = "Daha fazla adres detayı eklemeyi deneyin";
"cannot_find_address" = "Adres bulunamıyor";
"please_check_spelling_or_add_details" = "Yazımı kontrol edin veya detay ekleyin";
"cannot_find_address_check_spelling" = "Adres bulunamıyor, yazımı kontrol edin";
"address_not_set" = "Adres ayarlanmamış";
"address_format_incomplete" = "Adres formatı eksik";
"location_service_denied" = "Konum servisi reddedildi";
"no_saved_groups" = "Kaydedilmiş grup yok";
"select_points_create_groups" = "Teslimat noktalarını seçin ve yönetimi kolaylaştırmak için grup oluşturun";
"navigate_to_these_points" = "Bu noktalara git";
"confirm_remove_address" = "\"%@\" adresini gruptan kaldırmak istediğinizden emin misiniz?";
"confirm_remove_this_address" = "Bu adresi gruptan kaldırmak istediğinizden emin misiniz?";
"addresses_count" = "%d adres";
"no_saved_routes" = "Kaydedilmiş rota yok";
"no_saved_routes_description" = "Henüz herhangi bir rota kaydetmediniz";
"all_routes" = "Tüm rotalar";
"address_count_format_simple" = "%d adres";
"delete_all_routes" = "Tüm rotaları sil";
"navigate_to_all_points" = "Tüm noktalara git";
"confirm_navigate_to_route" = "\"%@\" rotasındaki tüm noktalara gitmek istediğinizden emin misiniz?";
"temp_navigation_group" = "Geçici navigasyon grubu";
"route_management" = "Rota yönetimi";
"route_addresses" = "Rota adresleri";
"no_addresses_in_route" = "Bu rotada adres yok";
"must_keep_one_route" = "En az bir rota tutulmalıdır";
"confirm_delete_route" = "\"%@\" rotasını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"confirm_delete_all_routes" = "Tüm rotaları silmeyi onayla";
"confirm_delete_all_routes_message" = "Tüm rotaları silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"delete_all" = "Tümünü sil";
"address_information" = "Adres bilgisi";
"group_belonging" = "Ait olduğu grup";
"view_map" = "Haritayı görüntüle";
"delete_delivery_point" = "Teslimat noktasını sil";
"delivery_point_details" = "Teslimat noktası detayları";
"confirm_deletion" = "Silmeyi onayla";
"delete_delivery_point_confirmation" = "Bu teslimat noktasını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.";
"delivery_photos" = "Teslimat fotoğrafları";
"view_delivery_photos" = "Teslimat fotoğraflarını görüntüle";
"no_photos_taken" = "Henüz fotoğraf çekilmedi";
"take_photos" = "Fotoğraf çek";
"loading_photos" = "Fotoğraflar yükleniyor...";
"photo_not_found" = "Fotoğraf bulunamadı";
"photo_deleted" = "Fotoğraf silindi";
"share_photos" = "Fotoğrafı paylaş";
"photo_capture_title" = "Fotoğraf onayı";
"door_number_photo_title" = "Yol numarası/kapı numarası fotoğrafı";
"package_label_photo_title" = "Paket etiketi fotoğrafı";
"placement_photo_title" = "Yerleştirme konumu fotoğrafı";
"door_number_photo_desc" = "Lütfen kapı numarasını net çekin, rakam/harflerin görünür olduğundan emin olun";
"package_label_photo_desc" = "Lütfen paket etiketini çekin, alıcı bilgilerinin net görünür olduğundan emin olun";
"placement_photo_desc" = "Lütfen paketin son yerleştirildiği konumu çekin";
"swipe_to_switch" = "Fotoğraf türünü değiştirmek için kaydırın";
"photos_will_be_saved_to" = "Fotoğraflar şuraya kaydedilecek";
"complete_photos" = "Fotoğraf çekmeyi tamamla";
"photo_save_success" = "Fotoğraf başarıyla kaydedildi";
"photo_save_failure" = "Fotoğraf kaydetme başarısız";
"no_photos_found" = "Fotoğraf bulunamadı";
"photos_deleted_or_not_taken" = "Fotoğraf silinmiş veya henüz çekilmemiş olabilir";
"share_photo" = "Fotoğrafı paylaş";
"photo_capture_preview" = "Önizleme modu - kamera simülasyonu";
"photo_capture_close" = "Kapat";
"camera_start_failed" = "Kamera başlatma başarısız";
"camera_start_failed_retry" = "Kamera başlatılamıyor, lütfen tekrar deneyin";
"camera_init_failed" = "Kamera başlatma başarısız";
"camera_access_failed" = "Kameraya erişilemiyor";
"photo_processing_failed" = "Fotoğraf çekme başarısız";
"photo_processing_failed_retry" = "Fotoğraf işleme tamamlanamıyor, lütfen tekrar deneyin";
"photo_capture_progress" = "İlerleme: %d/%d";
"photo_captured_continue" = "Çekim tamamlandı, %@ çekmeye devam et";
"pending" = "Teslimat bekliyor";
"in_progress" = "Teslimat yapılıyor";
"select_delivery_status" = "Teslimat durumu seç";
"select_failure_reason" = "Başarısızlık nedenini seç";
"delivery_status_pending" = "Beklemede";
"delivery_status_in_progress" = "Devam ediyor";
"delivery_status_completed" = "Tamamlandı";
"delivery_status_failed" = "Başarısız";
"failure_reason_not_at_home" = "Müşteri evde değil";
"failure_reason_wrong_address" = "Yanlış adres";
"failure_reason_no_access" = "Konuma erişilemiyor";
"failure_reason_rejected" = "Paket reddedildi";
"failure_reason_other" = "Diğer nedenler";
"enter_custom_reason" = "Belirli nedeni girin";
"custom_reason_placeholder" = "Lütfen belirli nedeni açıklayın...";
"custom_reason_required" = "Lütfen belirli nedeni girin";
"failure_reason_required" = "Lütfen başarısızlık nedenini seçin";
"delivery_type_delivery" = "Teslimat";
"delivery_type_pickup" = "Teslim alma";
"delivery_order_first" = "İlk";
"delivery_order_auto" = "Otomatik";
"delivery_order_last" = "Son";
"package_size_small" = "Küçük";
"package_size_medium" = "Orta";
"package_size_large" = "Büyük";
"package_type_box" = "Kutu";
"package_type_bag" = "Çanta";
"package_type_letter" = "Mektup";
"one_click_navigation_grouping" = "Tek tıkla navigasyon ve gruplama";
"speed_60x_faster" = "60 kat daha hızlı";
"goodbye_manual_address_adding" = "Manuel adres eklemeye veda";
"watch_detailed_demo" = "Detaylı demoyu izle";
"upgrade_to_pro_now" = "Şimdi Pro'ya yükselt";
"free_trial_7_days" = "7 gün ücretsiz deneme";
"traditional_vs_navibatch_pro" = "Geleneksel yöntem vs NaviBatch Pro";
"swipe_to_view_full_comparison" = "Tam karşılaştırmayı görmek için kaydırın";
"traditional_method" = "Geleneksel yöntem";
"drivers_get_lost_affect_efficiency" = "Sürücüler kaybolur, verimliliği etkiler";
"repetitive_operations_waste_time" = "Tekrarlayan işlemler zaman kaybı";
"total_time_60_seconds" = "Toplam süre: 60 saniye";
"navibatch_pro" = "NaviBatch Pro";
"optimize_routes_reduce_distance" = "Rotaları optimize et, mesafeyi azalt";
"improve_delivery_efficiency_accuracy" = "Teslimat verimliliği ve doğruluğunu artır";
"speed_boost_60x" = "60 kat hız artışı";
"total_time_1_second" = "Toplam süre: 1 saniye";
"time_comparison" = "Zaman karşılaştırması";
"traditional_method_problems" = "Geleneksel yöntem sorunları";
"each_address_3_5_seconds_14_total_60" = "Her adres 3-5 saniye, 14 adres toplam 60 saniye";
"repetitive_operations_cause_fatigue" = "Tekrarlayan işlemler yorgunluğa neden olur";
"address_order_reversed_last_becomes_first" = "Adres sırası ters, son ilk olur";
"need_manual_reverse_adding_takes_longer" = "Manuel tersine çevirme gerekir, ekleme daha uzun sürer";
"navibatch_advantages" = "NaviBatch avantajları";
"add_14_addresses_1_second_60x_faster" = "14 adresi 1 saniyede ekle, 60 kat daha hızlı";
"auto_maintain_correct_order_no_adjustment" = "Doğru sırayı otomatik koru, ayarlama yok";
"zero_error_rate_no_repetition" = "Sıfır hata oranı, tekrar yok";
"save_59_seconds" = "59 saniye tasarruf";
"speed_boost_60x_simple" = "60 kat hız artışı";
"seconds_format" = "%d saniye";
"actual_benefits_one_click_navigation" = "Tek tıkla navigasyonun gerçek faydaları";
"daily_savings" = "Günlük tasarruf";
"daily_savings_value" = "59 saniye";
"daily_savings_description" = "Her 14 adres için 59 saniye tasarruf";
"monthly_savings" = "Aylık tasarruf";
"monthly_savings_value" = "30 dakika";
"monthly_savings_description" = "Ayda 30 rota temelinde";
"fuel_savings_value" = "%30";
"fuel_savings_description" = "Rota optimizasyonu yakıt kullanımını azaltır";
"income_increase" = "Gelir artışı";
"income_increase_value" = "%15";
"income_increase_description" = "Günde daha fazla teslimat = daha fazla gelir";
"trial" = "Deneme";
"days_left" = "gün kaldı";
"free_plan_description" = "Ücretsiz plan - 20 adrese kadar";
"pro_plan_active" = "Pro plan aktif";
"expert_plan_active" = "Expert plan aktif";
"trial_active" = "Deneme aktif";
"trial_expires_on" = "Deneme %@ tarihinde sona eriyor";
"address_validation_mode" = "Adres doğrulama modu";
"validation_description" = "Adres doğrulama katılığını kontrol eder";
"current_settings" = "Mevcut ayarlar";
"validation_mode_format" = "Mod: %@";
"threshold_score_format" = "Eşik: %.1f";
"validation_example" = "Doğrulama örneği";
"original_address_example" = "Orijinal adres: 123 Main St";
"reverse_address_example" = "Ters adres: 125 Main St";
"house_number_difference" = "Ev numarası farkı: 2";
"result_label" = "Sonuç:";
"may_pass_warning" = "Geçebilir (uyarı)";
"will_not_pass" = "Geçmeyecek";
"real_case_example" = "Gerçek vaka örneği";
"real_case_description" = "Gerçek adres doğrulama verilerine dayalı";
"address_validation_settings" = "Adres doğrulama ayarları";
"clear" = "Temizle";
"view_details" = "Detayları görüntüle";
"create_test_data" = "Test verisi oluştur";
"manual_snapshot" = "Manuel anlık görüntü";
"start_location_updates" = "Konum güncellemelerini başlat";
"stop_location_updates" = "Konum güncellemelerini durdur";
"user_location_marker_test" = "Kullanıcı konum işaretçisi testi";
"location_animation_control" = "Konum animasyon kontrolü";
"current_location_format" = "Mevcut konum: %.6f, %.6f";
"waiting_for_location" = "Konum bekleniyor...";
"diagnostic_tools" = "Tanı araçları";
"storekit_diagnostics" = "StoreKit tanıları";
"subscription_function_test" = "Abonelik fonksiyon testi";
"localization_test" = "Yerelleştirme testi";
"address_validation_demo" = "Adres doğrulama demosu";
"localization_tools" = "Yerelleştirme araçları";
"coordinate_debug_tools" = "Koordinat hata ayıklama araçları";
"smart_abbreviation_expansion_test" = "Akıllı kısaltma genişletme testi";
"subscription_restore_diagnostics" = "Abonelik geri yükleme tanıları";
"batch_address_import_test" = "Toplu adres içe aktarma testi";
"test_import_1000_addresses_memory" = "Test: 1000 adres içe aktarma (bellek)";
"map_rendering_test" = "Harita render testi";
"test_map_display_markers_memory" = "Test: harita işaretçi gösterimi (bellek)";
"select_test_language" = "Test dili seç";
"discover_60x_speed_boost" = "60 kat hız artışını keşfedin";
"see_60x_speed_demo" = "60 kat hız demosunu görün";
"free_vs_pro_comparison" = "Ücretsiz vs Pro karşılaştırması";
"our_free_beats_competitors_paid" = "Ücretsiz planımız rakiplerin ücretli planlarını geçer";
"features" = "Özellikler";
"up_to_20" = "20'ye kadar";
"unlimited" = "Sınırsız";
"smart_optimization" = "Akıllı optimizasyon";
"up_to_20_percent" = "%20'ye kadar";
"file_not_found" = "Dosya bulunamadı";
"sample_file_not_available" = "Örnek dosya mevcut değil";
"file_copy_failed" = "Dosya kopyalama başarısız";
