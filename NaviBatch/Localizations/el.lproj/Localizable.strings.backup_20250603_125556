/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Ρυθμίσεις γλώσσας";
"system_language" = "Γλώσσα συστήματος";
"system_language_section" = "Ρυθμίσεις συστήματος";
"languages" = "Γλώσσες";
"language_info_title" = "Σχετικά με τις ρυθμίσεις γλώσσας";
"language_info_description" = "Μετά την αλλαγή των ρυθμίσεων γλώσσας, η εφαρμογή θα εμφανίζει κείμενο στην επιλεγμένη γλώσσα. Ορισμένο περιεχόμενο ενδέχεται να απαιτεί επανεκκίνηση της εφαρμογής για να εφαρμοστούν πλήρως οι νέες ρυθμίσεις γλώσσας.";
"restart_required" = "Απαιτείται επανεκκίνηση";
"restart_app_message" = "Για να εφαρμοστούν πλήρως οι αλλαγές γλώσσας, επανεκκινήστε την εφαρμογή.";
"restart_now" = "Επανεκκίνηση τώρα";
"restart_later" = "Επανεκκίνηση αργότερα";

// MARK: - Common UI Elements
"close" = "Κλείσιμο";
"cancel" = "Ακύρωση";
"save" = "Αποθήκευση";
"edit" = "Επεξεργασία";
"delete" = "Διαγραφή";
"done" = "Τέλος";
"next" = "Επόμενο";
"back" = "Πίσω";
"confirm" = "Επιβεβαίωση";
"error" = "Σφάλμα";
"success" = "Επιτυχία";
"warning" = "Προειδοποίηση";
"loading" = "Φόρτωση...";
"search" = "Αναζήτηση";
"settings" = "Ρυθμίσεις";
"help" = "Βοήθεια";
"about" = "Σχετικά";
"menu" = "Μενού";
"understand" = "Κατανοώ";

// MARK: - Navigation
"navigation" = "Πλοήγηση";
"start_navigation" = "Έναρξη πλοήγησης";

// MARK: - Subscription
"subscription" = "Συνδρομή";
"upgrade_to_pro" = "Αναβάθμιση σε Pro";
"upgrade_description" = "Ομαδοποίηση πλοήγησης με ένα κλικ, 60 φορές ταχύτερα από τη χειροκίνητη λειτουργία";
"restore_purchases" = "Επαναφορά αγορών";
"learn_more" = "Μάθετε περισσότερα";
"upgrade_your_plan" = "Αναβαθμίστε το πλάνο σας";
"one_click_navigation_description" = "Ομαδοποίηση διευθύνσεων με ένα κλικ για εξοικονόμηση χρόνου και καυσίμων";
"current_plan" = "Τρέχον πρόγραμμα";
"upgrade" = "Αναβάθμιση";
"maybe_later" = "Ίσως αργότερα";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Δωρεάν";
"pro_tier_price" = "$29.99/μήνα";
"expert_tier_price" = "$249.99/έτος";
"free_tier_description" = "Κατάλληλο για ιδιώτες και μικρές επιχειρήσεις που δεν χρειάζονται πολλά σημεία στάσης";
"pro_tier_description" = "Κατάλληλο για χρήστες που χρειάζονται ομαδοποίηση πλοήγησης με ένα κλικ και απεριόριστες διευθύνσεις";
"expert_tier_description" = "Ίδιο με το Pro, αλλά με 31% εξοικονόμηση μέσω του ετήσιου προγράμματος";
"route_optimization" = "Βελτιστοποίηση διαδρομής";
"unlimited_routes" = "Απεριόριστες διαδρομές";
"unlimited_optimizations" = "Απεριόριστες βελτιστοποιήσεις";
"max_15_addresses" = "Έως 15 διευθύνσεις ανά διαδρομή";
"save_fuel_30" = "Εξοικονομήστε έως και 30% καύσιμα";
"unlimited_addresses" = "✨ Απεριόριστες διευθύνσεις ✨";
"one_click_navigation" = "⚡ Ομαδοποίηση πλοήγησης με ένα κλικ - 60x ταχύτερα ⚡";
"package_finder" = "Εύρεση θέσης πακέτου";
"annual_savings" = "Ετήσια εξοικονόμηση";
"switched_to_free" = "Μετάβαση στο δωρεάν πλάνο";
"switched_to_subscription" = "Μετάβαση στο πλάνο συνδρομής";
"unlimited_stops" = "Απεριόριστα σημεία στάσης";
"plan_as_many_stops_as_needed" = "Προσθέστε όσα σημεία παράδοσης χρειάζεστε χωρίς περιορισμούς";

// MARK: - Address Input
"enter_or_search_address" = "Εισαγωγή ή αναζήτηση διεύθυνσης";
"search_results_count" = "Αποτελέσματα αναζήτησης: %d";
"no_matching_addresses" = "Δεν βρέθηκαν αντίστοιχες διευθύνσεις";
"search_address_failed" = "Η αναζήτηση διεύθυνσης απέτυχε: %@";
"address_search_no_response" = "Καμία απόκριση από την αναζήτηση διεύθυνσης";
"cannot_get_address_coordinates" = "Δεν είναι δυνατή η λήψη των συντεταγμένων διεύθυνσης";
"speech_recognizer_unavailable" = "Η αναγνώριση ομιλίας δεν είναι διαθέσιμη";
"microphone_permission_denied" = "Η άδεια μικροφώνου απορρίφθηκε";
"speech_recognition_permission_denied" = "Η άδεια αναγνώρισης ομιλίας απορρίφθηκε";
"listening" = "Ακρόαση...";
"recording_failed" = "Αποτυχία έναρξης εγγραφής: %@";
"cannot_get_coordinates_retry" = "Δεν είναι δυνατή η λήψη συντεταγμένων διεύθυνσης, παρακαλώ εισάγετε χειροκίνητα ή προσπαθήστε ξανά";
"cannot_create_recognition_request" = "Δεν είναι δυνατή η δημιουργία αιτήματος αναγνώρισης";

// MARK: - Saved Address Picker
"search_address" = "Αναζήτηση διεύθυνσης";
"no_saved_addresses" = "Δεν υπάρχουν αποθηκευμένες διευθύνσεις";
"no_saved_addresses_description" = "Δεν έχετε αποθηκεύσει ακόμα διευθύνσεις ή δεν υπάρχουν διευθύνσεις που να ταιριάζουν με τα κριτήρια φίλτρου";
"select_address_book" = "Επιλογή βιβλίου διευθύνσεων";

// MARK: - Menu
"menu" = "Μενού";
"routes" = "Διαδρομές";
"address_book" = "Βιβλίο διευθύνσεων";
"saved_routes" = "Αποθηκευμένες διαδρομές";
"manage_your_routes" = "Διαχείριση του σχεδιασμού διαδρομής σας";
"manage_your_addresses" = "Διαχείριση των συχνά χρησιμοποιούμενων διευθύνσεών σας";
"settings" = "Ρυθμίσεις";
"preferences" = "Προτιμήσεις";
"set_custom_start_point" = "Ορισμός προσαρμοσμένου σημείου εκκίνησης";
"current_start_point" = "Τρέχον σημείο εκκίνησης: %@";
"support" = "Υποστήριξη";
"contact_us" = "Επικοινωνήστε μαζί μας";
"contact_us_description" = "Έχετε ερωτήσεις ή προτάσεις; Μη διστάσετε να επικοινωνήσετε μαζί μας!";
"help_center" = "Κέντρο βοήθειας";
"subscription" = "Συνδρομή";
"upgrade_to_pro" = "Αναβάθμιση σε Pro";
"upgrade_description" = "Ομαδοποίηση πλοήγησης με ένα κλικ, 60 φορές ταχύτερα από τη χειροκίνητη λειτουργία";
"open_subscription_view" = "Άμεσο άνοιγμα προβολής συνδρομής";
"open_subscription_view_description" = "Παράκαμψη του ενδιάμεσου επιπέδου και άμεση εμφάνιση προβολής συνδρομής";
"restore_purchases_failed" = "Αποτυχία επαναφοράς αγορών: %@";
"about" = "Σχετικά";
"rate_us" = "Αξιολογήστε μας";
"rate_us_description" = "Τα σχόλιά σας είναι σημαντικά για μας και μας βοηθούν να βελτιώσουμε την εφαρμογή!";
"share_app" = "Κοινοποίηση εφαρμογής";
"share_app_text" = "Δοκιμάστε το NaviBatch, μια εκπληκτική εφαρμογή σχεδιασμού διαδρομών!";
"about_app" = "Σχετικά με την εφαρμογή";
"developer_tools" = "Εργαλεία προγραμματιστή";
"coordinate_debug_tool" = "Εργαλείο αποσφαλμάτωσης συντεταγμένων";
"batch_fix_addresses" = "Μαζική επιδιόρθωση διευθύνσεων";
"clear_database" = "Εκκαθάριση βάσης δεδομένων";
"clear_database_confirmation" = "Αυτό θα διαγράψει όλα τα δεδομένα, συμπεριλαμβανομένων των διαδρομών, διευθύνσεων και ομάδων. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί. Είστε βέβαιοι ότι θέλετε να συνεχίσετε;";
"confirm_clear" = "Επιβεβαίωση εκκαθάρισης";
"version_info" = "Έκδοση %@ (%@)";
"current_system_language" = "Τρέχουσα γλώσσα συστήματος";
"reset_to_system_language" = "Επαναφορά στη γλώσσα συστήματος";
"language" = "Γλώσσα";
"language_settings" = "Ρυθμίσεις γλώσσας";

// MARK: - Address Edit
"address" = "Διεύθυνση";
"coordinates" = "Συντεταγμένες";
"distance_from_current_location" = "Απόσταση από την τρέχουσα τοποθεσία";
"address_info" = "Πληροφορίες διεύθυνσης";
"update_coordinates" = "Ενημέρωση συντεταγμένων";
"fix_address" = "Διόρθωση διεύθυνσης";
"prompt" = "Προτροπή";
"confirm" = "Επιβεβαίωση";
"kilometers_format" = "%.1f χλμ";
"meters_format" = "%.0f μ";
"modify_address_first" = "Παρακαλώ τροποποιήστε τη διεύθυνση πριν την ενημέρωση των συντεταγμένων";
"coordinates_update_success" = "Οι συντεταγμένες ενημερώθηκαν με επιτυχία";
"coordinates_update_failure" = "Αποτυχία ενημέρωσης συντεταγμένων";
"save_failure" = "Αποτυχία αποθήκευσης: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "Δεν υπάρχουν αποθηκευμένες διευθύνσεις";
"no_saved_addresses_message" = "Δεν έχετε αποθηκεύσει ακόμα διευθύνσεις";
"add_new_address" = "Προσθήκη νέας διεύθυνσης";
"address_title" = "Διεύθυνση";
"add" = "Προσθήκη";
"refresh" = "Ανανέωση";
"notes" = "Σημειώσεις";
"address_details" = "Λεπτομέρειες διεύθυνσης";
"favorite" = "Αγαπημένο";
"edit_address" = "Επεξεργασία διεύθυνσης";
"cancel" = "Ακύρωση";
"save" = "Αποθήκευση";
"confirm_delete" = "Επιβεβαίωση διαγραφής";
"delete" = "Διαγραφή";
"delete_address_confirmation" = "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτή τη διεύθυνση; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.";
"edit" = "Επεξεργασία";
"address_marker" = "Διεύθυνση";
"address_label" = "Διεύθυνση:";
"notes_label" = "Σημειώσεις:";
"created_at_label" = "Δημιουργήθηκε στις:";
"open_in_maps" = "Άνοιγμα στους Χάρτες";
"copy_address" = "Αντιγραφή διεύθυνσης";
"address_details_title" = "Λεπτομέρειες διεύθυνσης";

// MARK: - Route Detail
"start_end_point" = "Σημείο εκκίνησης/τερματισμού";
"start_point" = "Σημείο εκκίνησης";
"end_point" = "Σημείο τερματισμού";
"route_info" = "Πληροφορίες διαδρομής";
"address_count" = "Αριθμός διευθύνσεων";
"address_count_format" = "%d διευθύνσεις";
"points_count_format" = "%d σημεία";
"additional_points_format" = "+%d σημεία";
"export_route" = "Εξαγωγή διαδρομής";
"navigate" = "Πλοήγηση";
"address_list" = "Λίστα διευθύνσεων";
"no_addresses" = "Δεν υπάρχουν διευθύνσεις";
"no_addresses_message" = "Αυτή η διαδρομή δεν έχει ακόμη διευθύνσεις";

// MARK: - Route Bottom Sheet
"address_point_start" = "Σημείο εκκίνησης";
"address_point_stop" = "Σημείο στάσης";
"address_point_end" = "Σημείο τερματισμού";
"route_name" = "Όνομα διαδρομής";
"save" = "Αποθήκευση";
"new_route" = "Νέα διαδρομή";
"saved_route" = "Αποθηκευμένη διαδρομή";
"edit" = "Επεξεργασία";
"loading" = "Φόρτωση...";
"plan_route" = "Σχεδιασμός διαδρομής";
"clear_all" = "Εκκαθάριση όλων";
"avoid" = "Αποφυγή:";
"toll_roads" = "Διόδια";
"highways" = "Αυτοκινητόδρομοι";
"processing_addresses" = "Επεξεργασία διευθύνσεων...";
"same_start_end_point" = "Έχετε ορίσει την ίδια διεύθυνση ως σημείο εκκίνησης και τερματισμού";
"add_start_point" = "Προσθήκη σημείου εκκίνησης";
"swipe_left_to_delete" = "← Σύρετε αριστερά για διαγραφή";
"delete" = "Διαγραφή";
"add_new_address" = "Προσθήκη νέας διεύθυνσης";
"add_end_point" = "Προσθήκη σημείου τερματισμού";

// MARK: - Simple Address Sheet
"address_title" = "Διεύθυνση";
"enter_and_select_address" = "Εισάγετε και επιλέξτε διεύθυνση";
"current_search_text" = "Τρέχον κείμενο αναζήτησης: %@";
"search_results_count" = "Αποτελέσματα αναζήτησης: %d";
"no_matching_addresses" = "Δεν βρέθηκαν αντίστοιχες διευθύνσεις";
"add_address" = "Προσθήκη διεύθυνσης";
"edit_address" = "Επεξεργασία διεύθυνσης";
"selected_coordinates" = "Επιλεγμένες συντεταγμένες";
"company_name_optional" = "Όνομα εταιρείας (Προαιρετικό)";
"url_optional" = "URL (Προαιρετικό)";
"favorite_address" = "Αγαπημένη διεύθυνση";
"set_as_start_and_end" = "Ορισμός ως σημείο εκκίνησης και τερματισμού";
"address_book" = "Βιβλίο διευθύνσεων";
"batch_paste" = "Μαζική επικόλληση";
"file_import" = "Εισαγωγή αρχείου";
"web_download" = "Λήψη από διαδίκτυο";
"cancel" = "Ακύρωση";
"saving" = "Αποθήκευση...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Διαχείριση σημείων παράδοσης";
"information_category" = "Κατηγορία πληροφοριών";
"address_info" = "Πληροφορίες διεύθυνσης";
"package_info" = "Πληροφορίες πακέτου";
"vehicle_position" = "Θέση οχήματος";
"delivery_info" = "Πληροφορίες παράδοσης";
"navigation" = "Πλοήγηση";
"take_photo_record" = "Λήψη φωτογραφίας";
"update_status" = "Ενημέρωση κατάστασης";
"done" = "Τέλος";
"edit_address_button" = "Επεξεργασία διεύθυνσης";
"coordinates" = "Συντεταγμένες";
"access_instructions" = "Οδηγίες πρόσβασης";
"add_access_instructions" = "Προσθήκη οδηγιών πρόσβασης...";
"package_count" = "Αριθμός πακέτων";
"package_size" = "Μέγεθος πακέτου";
"package_type" = "Τύπος πακέτου";
"mark_as_important" = "Επισήμανση ως σημαντικό";
"select_package_position" = "Επιλέξτε θέση πακέτου στο όχημα";
"vehicle_area" = "Περιοχή οχήματος";
"left_right_position" = "Θέση αριστερά/δεξιά";
"vehicle_position_front" = "Μπροστά";
"vehicle_position_middle" = "Μέση";
"vehicle_position_back" = "Πίσω";
"vehicle_position_left" = "Αριστερά";
"vehicle_position_right" = "Δεξιά";
"vehicle_position_floor" = "Κάτω";
"vehicle_position_shelf" = "Πάνω";
"height_position" = "Θέση ύψους";
"delivery_type" = "Τύπος παράδοσης";
"delivery_status" = "Κατάσταση παράδοσης";
"order_info" = "Πληροφορίες παραγγελίας";
"order_information" = "Πληροφορίες παραγγελίας";
"order_number" = "Αριθμός παραγγελίας";
"enter_order_number" = "Εισάγετε αριθμό παραγγελίας";
"tracking_number" = "Αριθμός παρακολούθησης";
"enter_tracking_number" = "Εισάγετε αριθμό παρακολούθησης";
"time_info" = "Πληροφορίες χρόνου";
"time_information" = "Πληροφορίες χρόνου";
"estimated_arrival_time" = "Εκτιμώμενος χρόνος άφιξης";
"anytime" = "Οποιαδήποτε στιγμή";
"stop_time" = "Χρόνος στάσης";
"minutes_format" = "%d λεπτά";
"photo_record" = "Καταγραφή φωτογραφιών";
"door_number_photo" = "Φωτογραφία αριθμού πόρτας";
"package_label_photo" = "Φωτογραφία ετικέτας πακέτου";
"placement_photo" = "Φωτογραφία τοποθέτησης";
"door_number_desc" = "Παρακαλώ τραβήξτε μια καθαρή φωτογραφία του αριθμού πόρτας ή δρόμου, βεβαιωθείτε ότι οι αριθμοί/γράμματα είναι ορατά";
"package_label_desc" = "Παρακαλώ φωτογραφίστε την ετικέτα του πακέτου, βεβαιωθείτε ότι οι πληροφορίες παραλήπτη είναι καθαρά ορατές";
"placement_desc" = "Παρακαλώ φωτογραφίστε την τελική θέση τοποθέτησης του πακέτου";
"photo_captured" = "Η φωτογραφία τραβήχτηκε";
"photo_captured_options" = "Η φωτογραφία τραβήχτηκε, θέλετε να συνεχίσετε με την επόμενη φωτογραφία ή να ολοκληρώσετε την τρέχουσα;";
"continue_to_next_photo" = "Συνέχεια στην επόμενη φωτογραφία - %@";
"retake" = "Επανάληψη λήψης";
"tap_to_capture" = "Πατήστε για λήψη";
"flash_auto" = "Αυτόματο φλας";
"flash_on" = "Ενεργοποίηση φλας";
"flash_off" = "Απενεργοποίηση φλας";
"photo_record_completed" = "Η καταγραφή φωτογραφιών ολοκληρώθηκε";
"photo_confirmation" = "Επιβεβαίωση φωτογραφίας";
"error" = "Σφάλμα";
"ok" = "Εντάξει";
"complete_photo_capture" = "Ολοκλήρωση λήψης φωτογραφιών";
"tap_to_capture" = "Πατήστε για λήψη";
"photo_instructions" = "Πατήστε κάθε κάρτα φωτογραφίας για λήψη. Όλες οι φωτογραφίες πρέπει να ολοκληρωθούν.";
"photo_options" = "Επιλογές φωτογραφίας";
"view_photo" = "Προβολή φωτογραφίας";
"retake_photo" = "Επανάληψη λήψης";
"saving_photos" = "Αποθήκευση φωτογραφιών...";
"completed" = "Ολοκληρώθηκε";
"not_taken" = "Δεν λήφθηκε";
"route_options" = "Επιλογές διαδρομής";
"avoid_tolls" = "Αποφυγή διοδίων";
"avoid_highways" = "Αποφυγή αυτοκινητοδρόμων";
"optimize_route" = "Βελτιστοποίηση διαδρομής";
"optimizing" = "Βελτιστοποίηση...";
"optimization_complete" = "Η βελτιστοποίηση ολοκληρώθηκε";

// MARK: - Addresses
"addresses" = "Διευθύνσεις";
"add_address" = "Προσθήκη διεύθυνσης";
"edit_address" = "Επεξεργασία διεύθυνσης";
"delete_address" = "Διαγραφή διεύθυνσης";
"address_details" = "Λεπτομέρειες διεύθυνσης";
"street" = "Οδός";
"city" = "Πόλη";
"state" = "Νομός/Περιφέρεια";
"country" = "Χώρα";
"postal_code" = "Ταχυδρομικός κώδικας";
"phone" = "Τηλέφωνο";
"email" = "Ηλεκτρονικό ταχυνόμενο";
"website" = "Ιστότοπος";
"company" = "Εταιρεία";
"notes" = "Σημειώσεις";
"coordinates" = "Συντεταγμένες";
"latitude" = "Γεωγραφικό πλάτος";
"longitude" = "Γεωγραφικό μήκος";
"geocoding_error" = "Σφάλμα γεωκωδικοποίησης";
"address_validation" = "Επαλήθευση διεύθυνσης";
"invalid_addresses" = "Άκυρες διευθύνσεις";
"fix_addresses" = "Διόρθωση διευθύνσεων";

// MARK: - Routes
"route" = "Διαδρομή";
"routes" = "Διαδρομές";
"select_address_point" = "Επιλογή σημείου διεύθυνσης";
"select_delivery_points" = "Επιλογή σημείων παράδοσης";
"create_delivery_route" = "Δημιουργία διαδρομής παράδοσης";
"view_saved_routes" = "Προβολή αποθηκευμένων διαδρομών";
"create_route" = "Δημιουργία διαδρομής";
"edit_route" = "Επεξεργασία διαδρομής";
"delete_route" = "Διαγραφή διαδρομής";
"route_name" = "Όνομα διαδρομής";
"route_details" = "Λεπτομέρειες διαδρομής";
"selected_addresses" = "Επιλέχθηκαν %d διευθύνσεις";
"reached_limit" = "Φτάσατε το όριο";
"can_select_more" = "Μπορείτε να επιλέξετε %d ακόμη";
"navigate_button" = "Πλοήγηση";
"create_group" = "Δημιουργία ομάδας";
"start_point" = "Σημείο εκκίνησης";
"end_point" = "Σημείο τερματισμού";
"waypoints" = "Σημεία διαδρομής";
"total_distance" = "Συνολική απόσταση";
"estimated_time" = "Εκτιμώμενος χρόνος";
"route_summary" = "Συνοπτική περιγραφή διαδρομής";
"route_options" = "Επιλογές διαδρομής";
"route_saved" = "Διαδρομή αποθηκευμένη";
"route_optimized" = "Διαδρομή βελτιστοποιημένη";
"optimizing_route" = "Βελτιστοποίηση διαδρομής...";
"completed_percent" = "Ολοκληρώθηκε %d%%";
"processing_points" = "Επεξεργασία: %d/%d";
"estimated_remaining_time" = "Εκτιμώμενος χρόνος παραμονής: %@";

// MARK: - Delivery
"delivery" = "Παράδοση";
"delivery_confirmation" = "Επιβεβαίωση παράδοσης";
"take_photo" = "Λήψη φωτογραφίας";
"signature" = "Υπογραφή";
"delivery_notes" = "Σημειώσεις παράδοσης";
"delivery_status" = "Κατάσταση παράδοσης";
"delivered" = "Παράδοση ολοκληρώθηκε";
"not_delivered" = "Παράδοση δεν ολοκληρώθηκε";
"delivery_time" = "Χρόνος παράδοσης";
"delivery_date" = "Ημερομηνία παράδοσης";
"package_details" = "Λεπτομέρειες πακέτου";
"package_id" = "Αναγνωριστικό πακέτου";
"package_weight" = "Βάρος πακέτου";
"package_dimensions" = "Διαστάσεις πακέτου";
"recipient_name" = "Όνομα παραλήπτη";
"recipient_phone" = "Τηλέφωνο παραλήπτη";

// MARK: - Groups
"groups" = "Ομάδες";
"saved_groups" = "Αποθηκευμένες ομάδες";
"create_group" = "Δημιουργία ομάδας";
"edit_group" = "Επεξεργασία ομάδας";
"delete_group" = "Διαγραφή ομάδας";
"group_name" = "Όνομα ομάδας";
"group_details" = "Λεπτομέρειες ομάδας";
"auto_grouping" = "Αυτόματη ομαδοποίηση";
"group_by" = "Ομαδοποίηση βάσει";
"add_to_group" = "Προσθήκη στην ομάδα";
"remove_from_group" = "Αφαίρεση από την ομάδα";
"group_created" = "Δημιουργήθηκε ομάδα";
"default_group_name_format" = "Ομάδα %d";
"auto_grouping_completed" = "Αυτόματη ομαδοποίηση ολοκληρώθηκε";
"auto_grouping_in_progress" = "Αυτόματη ομαδοποίηση σε εξέλιξη...";
"create_group_every_14_addresses" = "Δημιουργία ομάδας για κάθε 14 διευθύνσεις";
"create_delivery_group" = "Δημιουργία ομάδας παράδοσης";
"enter_group_name" = "Εισάγετε όνομα ομάδας";
"selected_delivery_points" = "Επιλεγμένα σημεία παράδοσης";
"drag_to_adjust_order" = "Σύρετε για προσαρμογή σειράς";

// MARK: - Subscription
"subscription" = "Συνδρομή";
"free_plan" = "Δωρεάν πρόγραμμα";
"pro_plan" = "Προγραμματισμένο πρόγραμμα";
"expert_plan" = "Προγραμματισμένο πρόγραμμα";
"monthly" = "Μηνιαίο πρόγραμμα";
"yearly" = "Ετήσιο πρόγραμμα";
"subscribe" = "Συνδρομή";
"upgrade" = "Αναβάθμιση";
"upgrade_to_pro" = "Αναβάθμιση σε Pro";
"manage_subscription" = "Διαχείριση συνδρομής";
"restore_purchases" = "Επαναφορά αγορών";
"subscription_benefits" = "Πλεονεκτήματα συνδρομής";
"free_trial" = "Δωρεάν δοκιμή";
"price_per_month" = "Μηνιαίο %@";
"price_per_year" = "Ετήσιο %@";
"save_percent" = "Αποθήκευση %@%";
"current_plan" = "Τρέχον πρόγραμμα";
"subscription_terms" = "Όροι συνδρομής";
"privacy_policy" = "Πολιτική ιδιωτικότητας";
"terms_of_service" = "Όροι υπηρεσίας";

// MARK: - Import/Export
"import" = "Εισαγωγή";
"export" = "Εξαγωγή";
"import_addresses" = "Εισαγωγή διευθύνσεων";
"export_addresses" = "Εξαγωγή διευθύνσεων";
"import_from_file" = "Εισαγωγή από αρχείο";
"export_to_file" = "Εξαγωγή σε αρχείο";
"file_format" = "Μορφή αρχείου";
"csv_format" = "Μορφή CSV";
"excel_format" = "Μορφή Excel";
"json_format" = "Μορφή JSON";
"import_success" = "Εισήχθησαν επιτυχώς %d διευθύνσεις, όλες με έγκυρες συντεταγμένες.";
"export_success" = "Εξαγωγή επιτυχής";
"import_error" = "Σφάλμα εισαγωγής";
"export_error" = "Σφάλμα εξαγωγής";

// MARK: - Navigation
"navigate" = "Πλοήγηση";

// MARK: - Look Around
"show_look_around" = "Προβολή Look Around";
"hide_look_around" = "Απόκρυψη Look Around";

// MARK: - Map
"map" = "Χάρτης";
"map_type" = "Τύπος χάρτη";
"standard" = "Κανονικός";
"satellite" = "Γεωστατικός";
"hybrid" = "Μικτός";
"show_traffic" = "Εμφάνιση κυκλοφοριακής κατάστασης";
"current_location" = "Τρέχουσα θέση";
"directions" = "Οδηγίες πλοήγησης";
"distance_to" = "Απόσταση";
"eta" = "Εκτιμώμενος χρόνος άφιξης";
"look_around" = "Περιήγηση";
"locating_to_glen_waverley" = "Γεώγραφηση σε Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Σφάλμα δικτύου";
"location_error" = "Σφάλμα τοποθεσίας";
"permission_denied" = "Άδεια απαγορεύτηκε";
"location_permission_required" = "Απαιτείται άδεια τοποθεσίας";
"camera_permission_required" = "Απαιτείται άδεια κάμερας";
"photo_library_permission_required" = "Απαιτείται άδεια βιβλιοθήκης φωτογραφιών";
"please_try_again" = "Παρακαλώ προσπαθήστε ξανά";
"something_went_wrong" = "Παρουσιάστηκε σφάλμα";
"invalid_input" = "Άκυρο είσοδο";
"required_field" = "Απαιτούμενο πεδίο";
"no_internet_connection" = "Χωρίς σύνδεση δικτύου";
"server_error" = "Σφάλμα διακομιστή";
"timeout_error" = "Σφάλμα προθεσμίας";
"data_not_found" = "Δε βρέθηκαν δεδομένα";
"selection_limit_reached" = "Όριο επιλογής φτάθηκε";
"selection_limit_description" = "Μπορείτε να επιλέξετε έως και %d διευθύνσεις, έχετε επιλέξει %d";

// MARK: - Location Validation Status
"location_status_valid" = "Έγκυρο εύρος";
"location_status_warning" = "Εύρος προειδοποίησης";
"location_status_invalid" = "Άκυρη θέση";
"location_status_unknown" = "Άγνωστη κατάσταση";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Άκυρο: Μηδενικές συντεταγμένες (0,0)";
"coordinates_invalid_nan" = "Άκυρο: Μη αριθμητικές συντεταγμένες";
"coordinates_out_of_range" = "Άκυρο: Συντεταγμένες εκτός έγκυρου εύρους";
"coordinates_far_from_user" = "Προειδοποίηση: Η θέση είναι μακριά από την τρέχουσα θέση σας";
"coordinates_ocean" = "Προειδοποίηση: Η θέση μπορεί να βρίσκεται στον ωκεανό ή σε ακατοίκητη περιοχή";

// MARK: - Batch Address Input
"batch_add_addresses" = "Μαζική προσθήκη διευθύνσεων";
"batch_address_input_placeholder" = "Παρακαλώ εισάγετε ή επικολλήστε διευθύνσεις, μία διεύθυνση ανά γραμμή. Έως 35 διευθύνσεις.";
"free_address_limit" = "Όριο διευθύνσεων δωρεάν έκδοσης";
"address_count_limit" = "Όριο αριθμού διευθύνσεων";
"free_version_max_addresses" = "Η δωρεάν έκδοση επιτρέπει έως %d διευθύνσεις.";
"current_addresses_remaining" = "Έχετε τώρα %d διευθύνσεις, μπορείτε να προσθέσετε μόνο %d ακόμη.";
"current_route_address_limit" = "Η τρέχουσα διαδρομή έχει %d διευθύνσεις, μπορείτε να προσθέσετε μόνο %d ακόμη, συνολικά έως %d διευθύνσεις.";
"selected_addresses_can_import" = "Επιλέξατε %d διευθύνσεις, μπορούν να εισαχθούν.";
"selected_addresses_exceeds" = "Επιλέξατε %d διευθύνσεις, υπερβαίνει το όριο κατά %d.";
"selected_addresses_all_importable" = "Επιλέξατε %d διευθύνσεις, μπορούν όλες να εισαχθούν.";
"upgrade_for_unlimited_addresses" = "Αναβαθμίστε στην premium έκδοση για απεριόριστες διευθύνσεις!";
"import_first_n_addresses" = "Εισαγωγή μόνο των πρώτων %d";
"import_all_addresses" = "Εισαγωγή όλων των διευθύνσεων";
"import_selected_addresses" = "Εισαγωγή επιλεγμένων διευθύνσεων";
"no_importable_addresses" = "Δεν υπάρχουν διευθύνσεις για εισαγωγή, ελέγξτε το όριο διευθύνσεων";
"please_enter_valid_address" = "Παρακαλώ εισάγετε τουλάχιστον μία έγκυρη διεύθυνση";

// MARK: - File Import
"import_success" = "Εισήχθησαν επιτυχώς %d διευθύνσεις, όλες με έγκυρες συντεταγμένες.";
"import_success_with_warnings" = "Επιτυχής εισαγωγή %d διευθύνσεων, από τις οποίες %d έχουν κανονικές συντεταγμένες, %d έχουν προειδοποιήσεις.\n\nΟι διευθύνσεις με προειδοποιήσεις έχουν επισημανθεί και μπορούν να διορθωθούν χειροκίνητα μετά την εισαγωγή.";

// MARK: - Web Download
"web_download" = "Λήψη από διαδίκτυο";
"supported_formats" = "Υποστηριζόμενες μορφές";
"supported_format_csv" = "• Αρχεία CSV: Η στήλη διευθύνσεων πρέπει να περιέχει πλήρεις διευθύνσεις";
"supported_format_json" = "• Δεδομένα JSON: Πίνακας που περιέχει πεδία διευθύνσεων";
"supported_format_text" = "• Απλό κείμενο: Μία διεύθυνση ανά γραμμή";
"download_history" = "Ιστορικό λήψεων";
"upgrade_to_premium" = "Αναβάθμιση σε premium";
"input_address_data_url" = "Εισάγετε URL δεδομένων διευθύνσεων";
"import_result" = "Αποτέλεσμα εισαγωγής";
"import_addresses" = "Εισαγωγή διευθύνσεων";
"downloading" = "Λήψη...";
"processing_data" = "Επεξεργασία δεδομένων...";
"google_drive_download_failed" = "Αποτυχία λήψης από Google Drive";
"second_attempt_invalid_data" = "Η δεύτερη προσπάθεια λήψης επέστρεψε άκυρα δεδομένα";
"cannot_parse_json" = "Δεν είναι δυνατή η ανάλυση δεδομένων JSON, ελέγξτε τη μορφή αρχείου";
"cannot_parse_json_with_error" = "Δεν είναι δυνατή η ανάλυση δεδομένων JSON: %@";
"cannot_get_address_coordinates" = "Δεν είναι δυνατή η λήψη των συντεταγμένων διεύθυνσης";
"cannot_read_file" = "Δεν είναι δυνατή η ανάγνωση αρχείου: %@";
"success" = "Επιτυχία";
"warning" = "Προειδοποίηση";
"failed" = "Αποτυχία παράδοσης";
"no_matching_addresses" = "Δεν βρέθηκαν αντίστοιχες διευθύνσεις";
"no_valid_addresses" = "Δεν βρέθηκαν έγκυρες διευθύνσεις";
"confirm" = "Επιβεβαίωση";
"processing_addresses" = "Επεξεργασία διευθύνσεων...";
"supports_file_types" = "Υποστηρίζει αρχεία CSV, TXT και JSON";
"tap_to_select_file" = "Πατήστε για επιλογή αρχείου";
"import_addresses" = "Εισαγωγή διευθύνσεων";
"company_name_optional" = "Όνομα εταιρείας (Προαιρετικό)";
"input_company_name" = "Εισάγετε όνομα εταιρείας (προαιρετικό)";
"imported_addresses_count" = "Εισήχθησαν %d διευθύνσεις";
"select_all" = "Επιλογή όλων";
"excel_format_not_supported" = "Δεν υποστηρίζεται μορφή Excel";
"no_matching_addresses" = "Δεν βρέθηκαν αντίστοιχες διευθύνσεις";

// MARK: - Import Limits
"import_failed" = "Αποτυχία εισαγωγής";
"no_importable_addresses" = "Δεν υπάρχουν διευθύνσεις για εισαγωγή, ελέγξτε το όριο διευθύνσεων";
"free_version_address_limit" = "Η δωρεάν έκδοση επιτρέπει έως %d διευθύνσεις.";
"current_address_count" = "Έχετε τώρα %d διευθύνσεις, μπορείτε να προσθέσετε μόνο %d ακόμη.";
"can_import_selected" = "Επιλέξατε %d διευθύνσεις, μπορούν να εισαχθούν.";
"selected_exceeds_limit" = "Επιλέξατε %d διευθύνσεις, υπερβαίνει το όριο κατά %d.";
"upgrade_to_premium_unlimited" = "Αναβαθμίστε στην premium έκδοση για απεριόριστες διευθύνσεις!";
"route_address_limit" = "Η τρέχουσα διαδρομή έχει %d διευθύνσεις, μπορείτε να προσθέσετε μόνο %d ακόμη, συνολικά έως %d διευθύνσεις.";
"free_version_limit" = "Όριο διευθύνσεων δωρεάν έκδοσης";
"address_count_limit" = "Όριο αριθμού διευθύνσεων";
"import_selected_addresses" = "Εισαγωγή επιλεγμένων διευθύνσεων";
"import_first_n" = "Εισαγωγή μόνο των πρώτων %d";
"import_all_n" = "Εισαγωγή όλων των %d";
"cannot_import" = "Δεν είναι δυνατή η εισαγωγή";
"select_at_least_one" = "Παρακαλώ επιλέξτε τουλάχιστον μία διεύθυνση";

// MARK: - Import Results
"no_valid_addresses_found" = "Δεν βρέθηκαν έγκυρες διευθύνσεις";
"import_success_all_valid" = "Επιτυχής εισαγωγή %d διευθύνσεων, όλες οι συντεταγμένες διευθύνσεων είναι κανονικές.";
"import_success_some_warnings" = "Επιτυχής εισαγωγή %d διευθύνσεων, από τις οποίες %d έχουν κανονικές συντεταγμένες, %d δεν μπόρεσαν να λάβουν συντεταγμένες.";

// MARK: - Warnings
"invalid_csv_row" = "Άκυρη γραμμή CSV";
"distance_warning" = "Απόσταση πάνω από 200 χιλιόμετρα από την τρέχουσα θέση";
"not_in_australia" = "Οι συντεταγμένες δεν βρίσκονται εντός της Αυστραλίας";
"cannot_get_coordinates" = "Δεν είναι δυνατή η λήψη συντεταγμένων διεύθυνσης";
"empty_address" = "Κενή διεύθυνση";
"invalid_address_data" = "Άκυρα δεδομένα διεύθυνσης";

// MARK: - Saved Groups
"saved_groups" = "Αποθηκευμένες ομάδες";
"no_saved_groups" = "Δεν υπάρχουν αποθηκευμένες ομάδες";
"select_points_create_groups" = "Επιλέξτε σημεία παράδοσης και δημιουργήστε ομάδες για εύκολη διαχείριση";
"group_name" = "Όνομα ομάδας";
"group_details" = "Λεπτομέρειες ομάδας";
"navigate_to_these_points" = "Πλοήγηση σε αυτά τα σημεία";
"confirm_remove_address" = "Είστε βέβαιοι ότι θέλετε να αφαιρέσετε τη διεύθυνση \"%@\" από την ομάδα;";
"confirm_remove_this_address" = "Είστε βέβαιοι ότι θέλετε να αφαιρέσετε αυτή τη διεύθυνση από την ομάδα;";
"addresses_count" = "%d διευθύνσεις";
"no_saved_routes" = "Δεν υπάρχουν αποθηκευμένες διαδρομές";
"no_saved_routes_description" = "Δεν έχετε αποθηκεύσει ακόμη καμία διαδρομή";
"all_routes" = "Όλες οι διαδρομές";
"address_count_format_simple" = "%d διευθύνσεις";
"delete_all_routes" = "Διαγραφή όλων των διαδρομών";
"navigate_to_all_points" = "Πλοήγηση σε όλα τα σημεία";
"confirm_navigate_to_route" = "Είστε βέβαιοι ότι θέλετε να πλοηγηθείτε στη διαδρομή \"%@\" σε όλα τα σημεία;";
"temp_navigation_group" = "Προσωρινή ομάδα πλοήγησης";

// MARK: - Route Management
"route_management" = "Διαχείριση διαδρομών";
"route_info" = "Πληροφορίες διαδρομής";
"route_name" = "Όνομα διαδρομής";
"route_addresses" = "Διευθύνσεις διαδρομής";
"no_addresses_in_route" = "Αυτή η διαδρομή δεν έχει διευθύνσεις";
"must_keep_one_route" = "Πρέπει να διατηρηθεί τουλάχιστον μία διαδρομή";
"confirm_delete_route" = "Είστε βέβαιοι ότι θέλετε να διαγράψετε τη διαδρομή \"%@\"; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.";
"confirm_delete_all_routes" = "Επιβεβαίωση διαγραφής όλων των διαδρομών";
"confirm_delete_all_routes_message" = "Είστε βέβαιοι ότι θέλετε να διαγράψετε όλες τις διαδρομές; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.";
"delete_all" = "Διαγραφή όλων";

// MARK: - Navigation Buttons
"navigate" = "Πλοήγηση";

// MARK: - GroupDetailView
"points_count_format" = "%d σημεία";

// MARK: - DeliveryPointDetailView
"address_information" = "Πληροφορίες διεύθυνσης";
"group_belonging" = "Ομάδα που ανήκει";
"view_map" = "Προβολή χάρτη";
"delivery_status" = "Κατάσταση παράδοσης";
"notes" = "Σημειώσεις";
"delete_delivery_point" = "Διαγραφή σημείου παράδοσης";
"delivery_point_details" = "Λεπτομέρειες σημείου παράδοσης";
"confirm_deletion" = "Επιβεβαίωση διαγραφής";
"delete_delivery_point_confirmation" = "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτό το σημείο παράδοσης; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.";

// MARK: - Delivery Photos
"delivery_photos" = "Φωτογραφίες παράδοσης";
"view_delivery_photos" = "Προβολή φωτογραφιών παράδοσης";
"no_photos_taken" = "Δεν έχουν τραβηχτεί ακόμη φωτογραφίες";
"take_photos" = "Λήψη φωτογραφιών";
"loading_photos" = "Φόρτωση φωτογραφιών...";
"photo_not_found" = "Η φωτογραφία δεν βρέθηκε";
"photo_deleted" = "Η φωτογραφία έχει διαγραφεί";
"door_number_photo" = "Φωτογραφία αριθμού πόρτας";
"package_label_photo" = "Φωτογραφία ετικέτας πακέτου";
"placement_photo" = "Φωτογραφία τοποθέτησης";
"share_photos" = "Κοινοποίηση φωτογραφίας";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Επιβεβαίωση φωτογραφίας";
"door_number_photo_title" = "Φωτογραφία αριθμού δρόμου/πόρτας";
"package_label_photo_title" = "Φωτογραφία ετικέτας πακέτου";
"placement_photo_title" = "Φωτογραφία θέσης τοποθέτησης";
"door_number_photo_desc" = "Παρακαλώ τραβήξτε καθαρή φωτογραφία του αριθμού πόρτας, βεβαιωθείτε ότι οι αριθμοί/γράμματα είναι ορατά";
"package_label_photo_desc" = "Παρακαλώ φωτογραφίστε την ετικέτα του πακέτου, βεβαιωθείτε ότι οι πληροφορίες παραλήπτη είναι καθαρά ορατές";
"placement_photo_desc" = "Παρακαλώ φωτογραφίστε την τελική θέση τοποθέτησης του πακέτου";
"swipe_to_switch" = "Σύρετε για εναλλαγή τύπου φωτογραφίας";
"complete_photos" = "Ολοκλήρωση λήψης φωτογραφιών";
"saving_photos" = "Αποθήκευση φωτογραφιών...";
"photo_save_success" = "Η φωτογραφία αποθηκεύτηκε επιτυχώς";
"photo_save_failure" = "Αποτυχία αποθήκευσης φωτογραφίας";
"retake_photo" = "Επανάληψη λήψης";
"no_photos_found" = "Δεν βρέθηκαν φωτογραφίες";
"photos_deleted_or_not_taken" = "Οι φωτογραφίες μπορεί να έχουν διαγραφεί ή να μην έχουν τραβηχτεί ακόμη";
"share_photo" = "Κοινοποίηση φωτογραφίας";
"photo_capture_preview" = "Λειτουργία προεπισκόπησης - Προσομοίωση κάμερας";
"photo_capture_close" = "Κλείσιμο";
"camera_start_failed" = "Αποτυχία εκκίνησης κάμερας";
"camera_start_failed_retry" = "Δεν είναι δυνατή η εκκίνηση της κάμερας, παρακαλώ προσπαθήστε ξανά";
"camera_init_failed" = "Αποτυχία αρχικοποίησης κάμερας";
"camera_access_failed" = "Δεν είναι δυνατή η πρόσβαση στην κάμερα";
"photo_processing_failed" = "Αποτυχία λήψης φωτογραφίας";
"photo_processing_failed_retry" = "Δεν είναι δυνατή η ολοκλήρωση επεξεργασίας φωτογραφίας, παρακαλώ προσπαθήστε ξανά";
"photo_capture_progress" = "Πρόοδος: %d/%d";
"photo_captured_continue" = "Η λήψη ολοκληρώθηκε, συνέχεια με %@";
"loading_photos" = "Φόρτωση φωτογραφιών...";
"cancel" = "Ακύρωση";

// MARK: - Delivery Status
"pending" = "Εκκρεμής παράδοση";
"in_progress" = "Σε παράδοση";
"completed" = "Ολοκληρώθηκε";
"failed" = "Αποτυχία παράδοσης";
"update_status" = "Ενημέρωση κατάστασης";
"select_delivery_status" = "Επιλογή κατάστασης παράδοσης";
"select_failure_reason" = "Επιλογή αιτίας αποτυχίας";
"delivered" = "Παράδοση ολοκληρώθηκε";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Ο πελάτης δεν είναι στο σπίτι";
"failure_reason_wrong_address" = "Λάθος διεύθυνση";
"failure_reason_no_access" = "Δεν είναι δυνατή η πρόσβαση στη θέση";
"failure_reason_rejected" = "Το πακέτο απορρίφθηκε";
"failure_reason_other" = "Άλλοι λόγοι";
"enter_custom_reason" = "Εισάγετε συγκεκριμένο λόγο";
"custom_reason_placeholder" = "Παρακαλώ περιγράψτε τον συγκεκριμένο λόγο...";
"custom_reason_required" = "Παρακαλώ εισάγετε συγκεκριμένο λόγο";
"failure_reason_required" = "Παρακαλώ επιλέξτε αιτία αποτυχίας";

// MARK: - Address Validation
"address_validation_failed" = "Αποτυχία επαλήθευσης διεύθυνσης";

