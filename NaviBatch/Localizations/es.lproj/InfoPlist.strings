/*
  InfoPlist.strings
  NaviBatch

  Created on 2023-11-16.

*/

// 权限描述
"NSLocationWhenInUseUsageDescription" = "NaviBatch needs to use location services to get your current location";
"NSLocationAlwaysAndWhenInUseUsageDescription" = "NaviBatch needs your location to show your position on the map and provide navigation even when the app is in the background";
"NSCameraUsageDescription" = "Navi<PERSON><PERSON> needs to use the camera to scan addresses and take delivery photos";
"NSPhotoLibraryUsageDescription" = "NaviBatch needs to access the photo library to save delivery photos";
"NSPhotoLibraryAddUsageDescription" = "Navi<PERSON><PERSON> needs to access the photo library to save delivery photos";
"NSDocumentsFolderUsageDescription" = "Navi<PERSON><PERSON> needs to access the documents folder to import and export address files";
