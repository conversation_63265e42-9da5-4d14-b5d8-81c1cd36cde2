/*
  Localizable.strings
  NaviBatch

  Created on 2023-11-16.
  Updated on 2025-05-25.

*/

// MARK: - Language Settings
"language_settings" = "Configuración de idioma";
"system_language" = "Idioma del sistema";
"system_language_section" = "Configuración del sistema";
"languages" = "Idiomas";
"language_info_title" = "Acerca de la configuración de idioma";
"language_info_description" = "Después de cambiar la configuración de idioma, la aplicación mostrará el texto en el idioma seleccionado. Es posible que algunos contenidos requieran reiniciar la aplicación para aplicar completamente la nueva configuración de idioma.";
"restart_required" = "Reinicio requerido";
"restart_app_message" = "Para aplicar completamente los cambios de idioma, por favor reinicie la aplicación.";
"restart_now" = "Reiniciar ahora";
"restart_later" = "Reiniciar más tarde";

// MARK: - Common UI Elements
"close" = "Cerrar";
"cancel" = "Cancelar";
"save" = "Guardar";
"edit" = "Editar";
"delete" = "Eliminar";
"done" = "Hecho";
"next" = "Siguiente";
"back" = "Atrás";
"confirm" = "Confirmar";
"error" = "Error";
"success" = "Éxito";
"warning" = "Advertencia";
"loading" = "Cargando...";
"search" = "Buscar";
"settings" = "Configuración";
"help" = "Ayuda";
"about" = "Acerca de";
"menu" = "Menú";
"understand" = "Entiendo";

// MARK: - Navigation
"navigation" = "Navegación";
"start_navigation" = "Iniciar navegación";

// MARK: - Subscription
"subscription" = "Suscripción";
"upgrade_to_pro" = "Actualizar a Pro";
"upgrade_description" = "Agrupación de navegación con un clic, 60 veces más rápido que la operación manual";
"restore_purchases" = "Restaurar compras";
"learn_more" = "Más información";
"upgrade_your_plan" = "Actualice su plan";
"one_click_navigation_description" = "Agrupación de navegación con un clic, ahorra tiempo y combustible";
"current_plan" = "Plan actual";
"upgrade" = "Actualizar";
"maybe_later" = "Quizás más tarde";
"free_tier_name" = "Starter";
"pro_tier_name" = "Pro";
"expert_tier_name" = "Expert";
"free_tier_price" = "Gratis";
"pro_tier_price" = "$29.99/mes";
"expert_tier_price" = "$249.99/año";
"free_tier_description" = "Perfecto para individuos y pequeñas empresas con pocas paradas";
"pro_tier_description" = "Para usuarios que necesitan agrupación de navegación con un clic y direcciones ilimitadas";
"expert_tier_description" = "Como Pro, pero ahorra un 31% con el plan anual";
"route_optimization" = "Optimización de rutas";
"unlimited_routes" = "Rutas ilimitadas";
"unlimited_optimizations" = "Optimizaciones ilimitadas";
"max_15_addresses" = "Máximo 15 direcciones por ruta";
"save_fuel_30" = "Ahorra hasta un 30% de combustible";
"unlimited_addresses" = "✨ Direcciones ilimitadas ✨";
"one_click_navigation" = "⚡ Agrupación de navegación con un clic - 60 veces más rápido ⚡";
"package_finder" = "Localizador de paquetes";
"annual_savings" = "Ahorro anual";
"switched_to_free" = "Cambiado al plan gratuito";
"switched_to_subscription" = "Cambiado al plan de suscripción";
"unlimited_stops" = "Paradas ilimitadas";
"plan_as_many_stops_as_needed" = "Agregue tantos puntos de entrega como necesite sin restricciones";

// MARK: - Address Input
"enter_or_search_address" = "Introduce o busca una dirección";
"search_results_count" = "Resultados de búsqueda: %d";
"no_matching_addresses" = "No se encontraron direcciones coincidentes";
"search_address_failed" = "Error en la búsqueda de direcciones: %@";
"address_search_no_response" = "No hay respuesta en la búsqueda de direcciones";
"cannot_get_address_coordinates" = "No se pueden obtener las coordenadas de la dirección";
"speech_recognizer_unavailable" = "Reconocimiento de voz no disponible";
"microphone_permission_denied" = "Permiso de micrófono denegado";
"speech_recognition_permission_denied" = "Permiso de reconocimiento de voz denegado";
"listening" = "Escuchando...";
"recording_failed" = "Error al iniciar la grabación: %@";
"cannot_get_coordinates_retry" = "No se pueden obtener las coordenadas de la dirección, por favor ingrese manualmente o vuelva a intentarlo";
"cannot_create_recognition_request" = "No se puede crear la solicitud de reconocimiento";

// MARK: - Saved Address Picker
"search_address" = "Buscar dirección";
"no_saved_addresses" = "No hay direcciones guardadas";
"no_saved_addresses_description" = "Aún no has guardado ninguna dirección, o no hay direcciones que coincidan con tus criterios de filtrado";
"select_address_book" = "Seleccionar libreta de direcciones";

// MARK: - Menu
"menu" = "Menú";
"routes" = "Rutas";
"address_book" = "Libreta de direcciones";
"saved_routes" = "Rutas guardadas";
"manage_your_routes" = "Gestiona tu planificación de rutas";
"manage_your_addresses" = "Gestiona tus direcciones de uso frecuente";
"settings" = "Configuración";
"preferences" = "Preferencias";
"set_custom_start_point" = "Establecer punto de inicio personalizado";
"current_start_point" = "Punto de inicio actual: %@";
"support" = "Soporte";
"contact_us" = "Contáctanos";
"contact_us_description" = "¿Tienes preguntas o sugerencias? ¡No dudes en contactarnos!";
"help_center" = "Centro de ayuda";
"subscription" = "Suscripción";
"upgrade_to_pro" = "Actualizar a Pro";
"upgrade_description" = "Agrupación de navegación con un clic, 60 veces más rápido que la operación manual";
"open_subscription_view" = "Abrir vista de suscripción";
"open_subscription_view_description" = "Saltar la capa intermedia, mostrar directamente SubscriptionView";
"restore_purchases_failed" = "Error al restaurar las compras: %@";
"about" = "Acerca de";
"rate_us" = "Califícanos";
"rate_us_description" = "¡Tu opinión es importante para nosotros y nos ayuda a mejorar la aplicación!";
"share_app" = "Compartir aplicación";
"share_app_text" = "¡Prueba NaviBatch, una increíble aplicación de planificación de rutas!";
"about_app" = "Acerca de la aplicación";
"developer_tools" = "Herramientas de desarrollador";
"coordinate_debug_tool" = "Herramienta de depuración de coordenadas";
"batch_fix_addresses" = "Corregir direcciones por lotes";
"clear_database" = "Borrar base de datos";
"clear_database_confirmation" = "Esto eliminará todos los datos, incluidas las rutas, direcciones y grupos. Esta acción no se puede deshacer. ¿Está seguro de que desea continuar?";
"confirm_clear" = "Confirmar borrado";
"version_info" = "Versión %@ (%@)";
"current_system_language" = "Idioma actual del sistema";
"reset_to_system_language" = "Restablecer al idioma del sistema";
"language" = "Idioma";
"language_settings" = "Configuración de idioma";

// MARK: - Address Edit
"address" = "Dirección";
"coordinates" = "Coordenadas";
"distance_from_current_location" = "Distancia desde la ubicación actual";
"address_info" = "Información de dirección";
"update_coordinates" = "Actualizar coordenadas";
"fix_address" = "Corregir dirección";
"prompt" = "Prompt";
"confirm" = "Confirmar";
"kilometers_format" = "%.1f km";
"meters_format" = "%.0f m";
"modify_address_first" = "Por favor modifique la dirección antes de actualizar las coordenadas";
"coordinates_update_success" = "Coordenadas actualizadas con éxito";
"coordinates_update_failure" = "Error al actualizar las coordenadas";
"save_failure" = "Error al guardar: %@";

// MARK: - Saved Addresses
"no_saved_addresses_title" = "No hay direcciones guardadas";
"no_saved_addresses_message" = "Aún no has guardado ninguna dirección";
"add_new_address" = "Agregar nueva dirección";
"address_title" = "Dirección";
"add" = "Agregar";
"refresh" = "Actualizar";
"notes" = "Notas";
"address_details" = "Detalles de la dirección";
"favorite" = "Favorito";
"edit_address" = "Editar dirección";
"cancel" = "Cancelar";
"save" = "Guardar";
"confirm_delete" = "Confirmar eliminación";
"delete" = "Eliminar";
"delete_address_confirmation" = "¿Está seguro de que desea eliminar esta dirección? Esta acción no se puede deshacer.";
"edit" = "Editar";
"address_marker" = "Dirección";
"address_label" = "Dirección:";
"notes_label" = "Notas:";
"created_at_label" = "Creado el:";
"open_in_maps" = "Abrir en Mapas";
"copy_address" = "Copiar dirección";
"address_details_title" = "Detalles de la dirección";

// MARK: - Route Detail
"start_end_point" = "Punto de inicio/fin";
"start_point" = "Punto de inicio";
"end_point" = "Punto final";
"route_info" = "Información de ruta";
"address_count" = "Número de direcciones";
"address_count_format" = "%d direcciones";
"points_count_format" = "%d puntos";
"additional_points_format" = "+%d puntos";
"export_route" = "Exportar ruta";
"navigate" = "Navegar";
"address_list" = "Lista de direcciones";
"no_addresses" = "No hay direcciones";
"no_addresses_message" = "Esta ruta aún no tiene direcciones";

// MARK: - Route Bottom Sheet
"address_point_start" = "Punto de inicio";
"address_point_stop" = "Punto de parada";
"address_point_end" = "Punto final";
"route_name" = "Nombre de la ruta";
"save" = "Guardar";
"new_route" = "Nueva ruta";
"saved_route" = "Ruta guardada";
"edit" = "Editar";
"loading" = "Cargando...";
"plan_route" = "Planificar ruta";
"clear_all" = "Borrar todo";
"avoid" = "Evitar:";
"toll_roads" = "Peajes";
"highways" = "Autopistas";
"processing_addresses" = "Procesando direcciones...";
"same_start_end_point" = "Ha establecido la misma dirección como punto de inicio y fin";
"add_start_point" = "Agregar punto de inicio";
"swipe_left_to_delete" = "← Desliza a la izquierda para eliminar";
"delete" = "Eliminar";
"add_new_address" = "Agregar nueva dirección";
"add_end_point" = "Agregar punto final";

// MARK: - Simple Address Sheet
"address_title" = "Dirección";
"enter_and_select_address" = "Ingresa y selecciona una dirección";
"current_search_text" = "Texto de búsqueda actual: %@";
"search_results_count" = "Resultados de búsqueda: %d";
"no_matching_addresses" = "No se encontraron direcciones coincidentes";
"add_address" = "Agregar dirección";
"edit_address" = "Editar dirección";
"selected_coordinates" = "Coordenadas seleccionadas";
"company_name_optional" = "Nombre de la empresa (Opcional)";
"url_optional" = "URL (Opcional)";
"favorite_address" = "Dirección favorita";
"set_as_start_and_end" = "Establecer como punto de inicio y fin";
"address_book" = "Libreta de direcciones";
"batch_paste" = "Pegar por lotes";
"file_import" = "Importar archivo";
"web_download" = "Descarga web";
"cancel" = "Cancelar";
"saving" = "Guardando...";

// MARK: - Delivery Point Manager
"delivery_point_management" = "Gestión de puntos de entrega";
"information_category" = "Categoría de información";
"address_info" = "Información de dirección";
"package_info" = "Información del paquete";
"vehicle_position" = "Posición del vehículo";
"delivery_info" = "Información de entrega";
"navigation" = "Navegación";
"take_photo_record" = "Tomar foto";
"update_status" = "Actualizar estado";
"done" = "Hecho";
"edit_address_button" = "Editar dirección";
"coordinates" = "Coordenadas";
"access_instructions" = "Instrucciones de acceso";
"add_access_instructions" = "Agregar instrucciones de acceso...";
"package_count" = "Cantidad de paquetes";
"package_size" = "Tamaño del paquete";
"package_type" = "Tipo de paquete";
"mark_as_important" = "Marcar como importante";
"select_package_position" = "Seleccionar posición del paquete en el vehículo";
"vehicle_area" = "Área del vehículo";
"left_right_position" = "Posición izquierda/derecha";
"vehicle_position_front" = "Frontal";
"vehicle_position_middle" = "Medio";
"vehicle_position_back" = "Trasero";
"vehicle_position_left" = "Izquierda";
"vehicle_position_right" = "Derecha";
"vehicle_position_floor" = "Inferior";
"vehicle_position_shelf" = "Superior";
"height_position" = "Posición de altura";
"delivery_type" = "Tipo de entrega";
"delivery_status" = "Estado de entrega";
"order_info" = "Información del pedido";
"order_information" = "Información del pedido";
"order_number" = "Número de pedido";
"enter_order_number" = "Ingrese el número de pedido";
"tracking_number" = "Número de seguimiento";
"enter_tracking_number" = "Ingrese el número de seguimiento";
"time_info" = "Información de tiempo";
"time_information" = "Información de tiempo";
"estimated_arrival_time" = "Hora estimada de llegada";
"anytime" = "Cualquier momento";
"stop_time" = "Tiempo de parada";
"minutes_format" = "%d minutos";
"photo_record" = "Registro fotográfico";
"door_number_photo" = "Foto del número de puerta";
"package_label_photo" = "Foto de la etiqueta del paquete";
"placement_photo" = "Foto de colocación";
"door_number_desc" = "Tome una foto clara del número de puerta o calle, asegúrese de que los números/letras sean visibles";
"package_label_desc" = "Tome una foto de la etiqueta del paquete, asegúrese de que la información del destinatario sea claramente visible";
"placement_desc" = "Tome una foto de donde se colocó finalmente el paquete";
"photo_captured" = "Foto tomada";
"photo_captured_options" = "Foto tomada, ¿desea continuar con la siguiente foto o completar la actual?";
"continue_to_next_photo" = "Continuar con la siguiente foto - %@";
"retake" = "Volver a tomar";
"tap_to_capture" = "Toque para capturar";
"flash_auto" = "Flash automático";
"flash_on" = "Flash encendido";
"flash_off" = "Flash apagado";
"photo_record_completed" = "Registro fotográfico completado";
"photo_confirmation" = "Confirmación de foto";
"error" = "Error";
"ok" = "OK";
"complete_photo_capture" = "Completar fotos";
"tap_to_capture" = "Toque para capturar";
"photo_instructions" = "Toque cada tarjeta de foto para capturar. Todas las fotos deben completarse.";
"photo_options" = "Opciones de foto";
"view_photo" = "Ver foto";
"retake_photo" = "Volver a tomar";
"saving_photos" = "Guardando fotos...";
"completed" = "Completado";
"not_taken" = "No tomado";
"route_options" = "Opciones de ruta";
"avoid_tolls" = "Evitar peajes";
"avoid_highways" = "Evitar autopistas";
"optimize_route" = "Optimizar ruta";
"optimizing" = "Optimizando...";
"optimization_complete" = "Optimización completada";
"route_optimization_results" = "Resultados de optimización de ruta";
"route_planning_options" = "Opciones de planificación de ruta";
"before_optimization" = "Antes de la optimización";
"after_optimization" = "Después de la optimización";
"auto_group" = "Agrupación automática";
"optimized_route_order" = "Orden de ruta optimizada";
"apply" = "Aplicar";
"kilometers" = "kilómetros";

// MARK: - Addresses
"addresses" = "Direcciones";
"add_address" = "Agregar dirección";
"edit_address" = "Editar dirección";
"delete_address" = "Eliminar dirección";
"address_details" = "Detalles de la dirección";
"street" = "Calle";
"city" = "Ciudad";
"state" = "Estado/Provincia";
"country" = "País";
"postal_code" = "Código postal";
"phone" = "Teléfono";
"email" = "Email";
"website" = "Sitio web";
"company" = "Empresa";
"notes" = "Notas";
"coordinates" = "Coordenadas";
"latitude" = "Latitud";
"longitude" = "Longitud";
"geocoding_error" = "Error de geocodificación";
"address_validation" = "Validación de dirección";
"invalid_addresses" = "Direcciones inválidas";
"fix_addresses" = "Corregir direcciones";

// MARK: - Routes
"route" = "Ruta";
"routes" = "Rutas";
"select_address_point" = "Seleccionar punto de dirección";
"select_delivery_points" = "Seleccionar puntos de entrega";
"create_delivery_route" = "Crear ruta de entrega";
"view_saved_routes" = "Ver rutas guardadas";
"create_route" = "Crear ruta";
"edit_route" = "Editar ruta";
"delete_route" = "Eliminar ruta";
"route_name" = "Nombre de la ruta";
"route_details" = "Detalles de la ruta";
"selected_addresses" = "Seleccionadas %d direcciones";
"reached_limit" = "Límite alcanzado";
"can_select_more" = "Puede seleccionar %d más";
"navigate_button" = "Navegar";
"create_group" = "Crear grupo";
"start_point" = "Punto de inicio";
"end_point" = "Punto final";
"waypoints" = "Puntos intermedios";
"total_distance" = "Distancia total";
"estimated_time" = "Tiempo estimado";
"route_summary" = "Resumen de la ruta";
"route_options" = "Opciones de ruta";
"route_saved" = "Ruta guardada";
"route_optimized" = "Ruta optimizada";
"optimizing_route" = "Optimizando ruta...";
"completed_percent" = "%d%% completado";
"processing_points" = "Procesando: %d/%d";
"estimated_remaining_time" = "Tiempo restante estimado: %@";

// MARK: - Delivery
"delivery" = "Entrega";
"delivery_confirmation" = "Confirmación de entrega";
"take_photo" = "Tomar foto";
"signature" = "Firma";
"delivery_notes" = "Notas de entrega";
"delivery_status" = "Estado de entrega";
"delivered" = "Entregado";
"not_delivered" = "No entregado";
"delivery_time" = "Hora de entrega";
"delivery_date" = "Fecha de entrega";
"package_details" = "Detalles del paquete";
"package_id" = "ID del paquete";
"package_weight" = "Peso del paquete";
"package_dimensions" = "Dimensiones del paquete";
"recipient_name" = "Nombre del destinatario";
"recipient_phone" = "Teléfono del destinatario";

// MARK: - Groups
"groups" = "Grupos";
"saved_groups" = "Grupos guardados";
"create_group" = "Crear grupo";
"edit_group" = "Editar grupo";
"delete_group" = "Eliminar grupo";
"group_name" = "Nombre del grupo";
"group_details" = "Detalles del grupo";
"auto_grouping" = "Agrupación automática";
"group_by" = "Agrupar por";
"add_to_group" = "Agregar al grupo";
"remove_from_group" = "Eliminar del grupo";
"group_created" = "Grupo creado";
"default_group_name_format" = "Grupo %d";
"auto_grouping_completed" = "Agrupación automática completada";
"auto_grouping_in_progress" = "Agrupación automática en progreso...";
"create_group_every_14_addresses" = "Crear un grupo por cada 14 direcciones";
"create_delivery_group" = "Crear grupo de entrega";
"enter_group_name" = "Ingrese nombre del grupo";
"selected_delivery_points" = "Puntos de entrega seleccionados";
"drag_to_adjust_order" = "Arrastre para ajustar el orden";

// MARK: - Subscription
"subscription" = "Suscripción";
"free_plan" = "Plan gratuito";
"pro_plan" = "Plan Pro";
"expert_plan" = "Plan Expert";
"monthly" = "Mensual";
"yearly" = "Anual";
"subscribe" = "Suscribirse";
"upgrade" = "Actualizar";
"upgrade_to_pro" = "Actualizar a Pro";
"manage_subscription" = "Gestionar suscripción";
"restore_purchases" = "Restaurar compras";
"subscription_benefits" = "Beneficios de la suscripción";
"free_trial" = "Prueba gratuita";
"price_per_month" = "%@ por mes";
"price_per_year" = "%@ por año";
"save_percent" = "Ahorra %@%";
"current_plan" = "Plan actual";
"subscription_terms" = "Términos de suscripción";
"privacy_policy" = "Política de privacidad";
"terms_of_service" = "Términos de servicio";

// MARK: - Import/Export
"import" = "Importar";
"export" = "Exportar";
"import_addresses" = "Importar direcciones";
"export_addresses" = "Exportar direcciones";
"import_from_file" = "Importar desde archivo";
"export_to_file" = "Exportar a archivo";
"file_format" = "Formato de archivo";
"csv_format" = "Formato CSV";
"excel_format" = "Formato Excel";
"json_format" = "Formato JSON";
"import_success" = "Se importaron exitosamente %d direcciones, todas con coordenadas válidas.";
"export_success" = "Exportación exitosa";
"import_error" = "Error de importación";
"export_error" = "Error de exportación";

// MARK: - Navigation
"navigate" = "Navegar";

// MARK: - Look Around
"show_look_around" = "Mostrar Look Around";
"hide_look_around" = "Ocultar Look Around";

// MARK: - Map
"map" = "Mapa";
"map_type" = "Tipo de mapa";
"standard" = "Estándar";
"satellite" = "Satélite";
"hybrid" = "Híbrido";
"show_traffic" = "Mostrar tráfico";
"current_location" = "Ubicación actual";
"directions" = "Direcciones";
"distance_to" = "Distancia a";
"eta" = "Hora estimada de llegada";
"look_around" = "Ver alrededor";
"locating_to_glen_waverley" = "Localizando a Glen Waverley";

// MARK: - Errors and Warnings
"network_error" = "Error de red";
"location_error" = "Error de ubicación";
"permission_denied" = "Permiso denegado";
"location_permission_required" = "Se requiere permiso de ubicación";
"camera_permission_required" = "Se requiere permiso de cámara";
"photo_library_permission_required" = "Se requiere permiso de biblioteca de fotos";
"please_try_again" = "Por favor intente de nuevo";
"something_went_wrong" = "Algo salió mal";
"invalid_input" = "Entrada inválida";
"required_field" = "Campo requerido";
"no_internet_connection" = "Sin conexión a internet";
"server_error" = "Error del servidor";
"timeout_error" = "Tiempo de espera agotado";
"data_not_found" = "Datos no encontrados";
"selection_limit_reached" = "Límite de selección alcanzado";
"selection_limit_description" = "Puede seleccionar un máximo de %d direcciones, ha seleccionado %d";

// MARK: - Location Validation Status
"location_status_valid" = "Rango válido";
"location_status_warning" = "Rango de advertencia";
"location_status_invalid" = "Ubicación inválida";
"location_status_unknown" = "Estado desconocido";

// MARK: - Global Coordinate Validation
"coordinates_origin_point" = "Inválido: Coordenadas cero (0,0)";
"coordinates_invalid_nan" = "Inválido: Coordenadas no numéricas";
"coordinates_out_of_range" = "Inválido: Coordenadas fuera del rango válido";
"coordinates_far_from_user" = "Advertencia: La ubicación está lejos de su posición actual";
"coordinates_ocean" = "Advertencia: La ubicación puede estar en el océano o área deshabitada";

// MARK: - Batch Address Input
"batch_add_addresses" = "Agregar direcciones por lotes";
"batch_address_input_placeholder" = "Ingrese o pegue direcciones, una dirección por línea. Máximo 35 direcciones.";
"free_address_limit" = "Límite de direcciones de la versión gratuita";
"address_count_limit" = "Límite de cantidad de direcciones";
"free_version_max_addresses" = "La versión gratuita permite máximo %d direcciones.";
"current_addresses_remaining" = "Actualmente tiene %d direcciones, solo puede agregar %d más.";
"current_route_address_limit" = "La ruta actual tiene %d direcciones, solo puede agregar %d más, máximo total de %d direcciones.";
"selected_addresses_can_import" = "Ha seleccionado %d direcciones, pueden ser importadas.";
"selected_addresses_exceeds" = "Ha seleccionado %d direcciones, excede el límite por %d.";
"selected_addresses_all_importable" = "Ha seleccionado %d direcciones, todas pueden ser importadas.";
"upgrade_for_unlimited_addresses" = "¡Actualice a la versión premium para direcciones ilimitadas!";
"import_first_n_addresses" = "Importar solo los primeros %d";
"import_all_addresses" = "Importar todas las direcciones";
"import_selected_addresses" = "Importar direcciones seleccionadas";
"no_importable_addresses" = "No hay direcciones para importar, verifique el límite de direcciones";
"please_enter_valid_address" = "Por favor ingrese al menos una dirección válida";

// MARK: - File Import
"import_success" = "Se importaron exitosamente %d direcciones, todas con coordenadas válidas.";
"import_success_with_warnings" = "Importadas exitosamente %d direcciones, de las cuales %d tienen coordenadas normales, %d tienen advertencias.\n\nLas direcciones con advertencias están marcadas y pueden corregirse manualmente después de la importación.";

// MARK: - Web Download
"web_download" = "Descarga web";
"supported_formats" = "Formatos compatibles";
"supported_format_csv" = "• Archivos CSV: La columna de direcciones debe contener direcciones completas";
"supported_format_json" = "• Datos JSON: Array que contiene campos de direcciones";
"supported_format_text" = "• Texto plano: Una dirección por línea";
"download_history" = "Historial de descargas";
"upgrade_to_premium" = "Actualizar a premium";
"input_address_data_url" = "Ingrese URL de datos de direcciones";
"import_result" = "Resultado de importación";
"import_addresses" = "Importar direcciones";
"downloading" = "Descargando...";
"processing_data" = "Procesando datos...";
"google_drive_download_failed" = "Error al descargar de Google Drive";
"second_attempt_invalid_data" = "El segundo intento de descarga devolvió datos inválidos";
"cannot_parse_json" = "No se pueden analizar los datos JSON, verifique el formato del archivo";
"cannot_parse_json_with_error" = "No se pueden analizar los datos JSON: %@";
"cannot_get_address_coordinates" = "No se pueden obtener las coordenadas de la dirección";
"cannot_read_file" = "No se puede leer el archivo: %@";
"success" = "Éxito";
"warning" = "Advertencia";
"failed" = "Entrega fallida";
"no_matching_addresses" = "No se encontraron direcciones coincidentes";
"no_valid_addresses" = "No se encontraron direcciones válidas";
"confirm" = "Confirmar";
"processing_addresses" = "Procesando direcciones...";
"supports_file_types" = "Compatible con archivos CSV, TXT y JSON";
"tap_to_select_file" = "Toque para seleccionar archivo";
"import_addresses" = "Importar direcciones";
"company_name_optional" = "Nombre de la empresa (Opcional)";
"input_company_name" = "Ingrese nombre de la empresa (opcional)";
"imported_addresses_count" = "Importadas %d direcciones";
"select_all" = "Seleccionar todo";
"excel_format_not_supported" = "Formato Excel no compatible";
"no_matching_addresses" = "No se encontraron direcciones coincidentes";

// MARK: - Import Limits
"import_failed" = "Error de importación";
"no_importable_addresses" = "No hay direcciones para importar, verifique el límite de direcciones";
"free_version_address_limit" = "La versión gratuita permite máximo %d direcciones.";
"current_address_count" = "Actualmente tiene %d direcciones, solo puede agregar %d más.";
"can_import_selected" = "Ha seleccionado %d direcciones, pueden ser importadas.";
"selected_exceeds_limit" = "Ha seleccionado %d direcciones, excede el límite por %d.";
"upgrade_to_premium_unlimited" = "¡Actualice a la versión premium para direcciones ilimitadas!";
"route_address_limit" = "La ruta actual tiene %d direcciones, solo puede agregar %d más, máximo total de %d direcciones.";
"free_version_limit" = "Límite de direcciones de la versión gratuita";
"address_count_limit" = "Límite de cantidad de direcciones";
"import_selected_addresses" = "Importar direcciones seleccionadas";
"import_first_n" = "Importar solo los primeros %d";
"import_all_n" = "Importar todas las %d";
"cannot_import" = "No se puede importar";
"select_at_least_one" = "Por favor seleccione al menos una dirección";

// MARK: - Import Results
"no_valid_addresses_found" = "No se encontraron direcciones válidas";
"import_success_all_valid" = "Importadas exitosamente %d direcciones, todas las coordenadas de direcciones son normales.";
"import_success_some_warnings" = "Importadas exitosamente %d direcciones, de las cuales %d tienen coordenadas normales, %d no pudieron obtener coordenadas.";

// MARK: - Warnings
"invalid_csv_row" = "Fila CSV inválida";
"distance_warning" = "Distancia desde la ubicación actual excede 200 km";
"not_in_australia" = "Las coordenadas no están dentro de Australia";
"cannot_get_coordinates" = "No se pueden obtener las coordenadas de la dirección";
"empty_address" = "Dirección vacía";
"invalid_address_data" = "Datos de dirección inválidos";

// MARK: - Saved Groups
"saved_groups" = "Grupos guardados";
"no_saved_groups" = "No hay grupos guardados";
"select_points_create_groups" = "Seleccione puntos de entrega y cree grupos para facilitar la gestión";
"group_name" = "Nombre del grupo";
"group_details" = "Detalles del grupo";
"navigate_to_these_points" = "Navegar a estos puntos";
"confirm_remove_address" = "¿Está seguro de que desea eliminar la dirección \"%@\" del grupo?";
"confirm_remove_this_address" = "¿Está seguro de que desea eliminar esta dirección del grupo?";
"addresses_count" = "%d direcciones";
"no_saved_routes" = "No hay rutas guardadas";
"no_saved_routes_description" = "Aún no ha guardado ninguna ruta";
"all_routes" = "Todas las rutas";
"address_count_format_simple" = "%d direcciones";
"delete_all_routes" = "Eliminar todas las rutas";
"navigate_to_all_points" = "Navegar a todos los puntos";
"confirm_navigate_to_route" = "¿Está seguro de que desea navegar a todos los puntos en la ruta \"%@\"?";
"temp_navigation_group" = "Grupo de navegación temporal";

// MARK: - Route Management
"route_management" = "Gestión de rutas";
"route_info" = "Información de ruta";
"route_name" = "Nombre de la ruta";
"route_addresses" = "Direcciones de ruta";
"no_addresses_in_route" = "Esta ruta no tiene direcciones";
"must_keep_one_route" = "Debe mantener al menos una ruta";
"confirm_delete_route" = "¿Está seguro de que desea eliminar la ruta \"%@\"? Esta acción no se puede deshacer.";
"confirm_delete_all_routes" = "Confirmar eliminación de todas las rutas";
"confirm_delete_all_routes_message" = "¿Está seguro de que desea eliminar todas las rutas? Esta acción no se puede deshacer.";
"delete_all" = "Eliminar todo";

// MARK: - Navigation Buttons
"navigate" = "Navegar";

// MARK: - GroupDetailView
"points_count_format" = "%d puntos";

// MARK: - DeliveryPointDetailView
"address_information" = "Información de dirección";
"group_belonging" = "Grupo al que pertenece";
"view_map" = "Ver mapa";
"delivery_status" = "Estado de entrega";
"notes" = "Notas";
"delete_delivery_point" = "Eliminar punto de entrega";
"delivery_point_details" = "Detalles del punto de entrega";
"confirm_deletion" = "Confirmar eliminación";
"delete_delivery_point_confirmation" = "¿Está seguro de que desea eliminar este punto de entrega? Esta acción no se puede deshacer.";

// MARK: - Delivery Photos
"delivery_photos" = "Fotos de entrega";
"view_delivery_photos" = "Ver fotos de entrega";
"no_photos_taken" = "Aún no se han tomado fotos";
"take_photos" = "Tomar fotos";
"loading_photos" = "Cargando fotos...";
"photo_not_found" = "Foto no encontrada";
"photo_deleted" = "La foto ha sido eliminada";
"door_number_photo" = "Foto del número de puerta";
"package_label_photo" = "Foto de la etiqueta del paquete";
"placement_photo" = "Foto de colocación";
"share_photos" = "Compartir foto";

// MARK: - Photo Capture and Viewer
"photo_capture_title" = "Confirmación de foto";
"door_number_photo_title" = "Foto de número de calle/puerta";
"package_label_photo_title" = "Foto de etiqueta del paquete";
"placement_photo_title" = "Foto de ubicación de colocación";
"door_number_photo_desc" = "Tome una foto clara del número de puerta, asegúrese de que los números/letras sean visibles";
"package_label_photo_desc" = "Tome una foto de la etiqueta del paquete, asegúrese de que la información del destinatario sea claramente visible";
"placement_photo_desc" = "Tome una foto de donde se colocó finalmente el paquete";
"swipe_to_switch" = "Deslice para cambiar el tipo de foto";
"complete_photos" = "Completar fotos";
"saving_photos" = "Guardando fotos...";
"photo_save_success" = "Foto guardada exitosamente";
"photo_save_failure" = "Error al guardar la foto";
"retake_photo" = "Volver a tomar";
"no_photos_found" = "No se encontraron fotos";
"photos_deleted_or_not_taken" = "Las fotos pueden haber sido eliminadas o aún no se han tomado";
"share_photo" = "Compartir foto";
"photo_capture_preview" = "Modo de vista previa - Simulación de cámara";
"photo_capture_close" = "Cerrar";
"camera_start_failed" = "Error al iniciar la cámara";
"camera_start_failed_retry" = "No se puede iniciar la cámara, por favor intente de nuevo";
"camera_init_failed" = "Error de inicialización de la cámara";
"camera_access_failed" = "No se puede acceder a la cámara";
"photo_processing_failed" = "Error al tomar la foto";
"photo_processing_failed_retry" = "No se puede completar el procesamiento de la foto, por favor intente de nuevo";
"photo_capture_progress" = "Progreso: %d/%d";
"photo_captured_continue" = "Captura completada, continuando con %@";
"loading_photos" = "Cargando fotos...";
"cancel" = "Cancelar";

// MARK: - Delivery Status
"pending" = "Pendiente de entrega";
"in_progress" = "En entrega";
"completed" = "Completado";
"failed" = "Entrega fallida";
"update_status" = "Actualizar estado";
"select_delivery_status" = "Seleccionar estado de entrega";
"select_failure_reason" = "Seleccionar razón del fallo";
"delivered" = "Entregado";

// MARK: - Failure Reasons
"failure_reason_not_at_home" = "Cliente no está en casa";
"failure_reason_wrong_address" = "Dirección incorrecta";
"failure_reason_no_access" = "No se puede acceder a la ubicación";
"failure_reason_rejected" = "Paquete rechazado";
"failure_reason_other" = "Otras razones";
"enter_custom_reason" = "Ingrese razón específica";
"custom_reason_placeholder" = "Por favor describa la razón específica...";
"custom_reason_required" = "Por favor ingrese la razón específica";
"failure_reason_required" = "Por favor seleccione la razón del fallo";

// MARK: - Address Validation
"address_validation_failed" = "Error de validación de dirección";


"0" = "14天免費試用，隨時可取消";
