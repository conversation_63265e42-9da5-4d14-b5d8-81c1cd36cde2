#!/bin/bash

# 🔥 Firebase AI 集成测试脚本
# 验证Firebase AI集成是否完成

echo "🔥 Firebase AI 集成测试"
echo "======================"
echo ""

# 检查项目目录
if [ ! -f "NaviBatchApp.swift" ]; then
    echo "❌ 错误：请在NaviBatch项目根目录运行此脚本"
    exit 1
fi

echo "✅ 项目目录检查通过"
echo ""

# 检查Firebase项目配置
echo "📋 检查Firebase项目配置..."
if [ -f ".firebaserc" ]; then
    PROJECT_ID=$(grep -o '"navibatch-ai-2024"' .firebaserc)
    if [ ! -z "$PROJECT_ID" ]; then
        echo "✅ Firebase项目ID: navibatch-ai-2024"
    else
        echo "❌ Firebase项目ID配置错误"
        exit 1
    fi
else
    echo "❌ 缺少.firebaserc文件"
    exit 1
fi

# 检查GoogleService-Info.plist
echo "📋 检查GoogleService-Info.plist..."
if [ -f "GoogleService-Info.plist" ]; then
    if grep -q "navibatch-ai-2024" GoogleService-Info.plist; then
        echo "✅ GoogleService-Info.plist 配置正确"
        
        # 提取关键信息
        API_KEY=$(grep -A1 "API_KEY" GoogleService-Info.plist | grep -o "AIzaSy[^<]*")
        BUNDLE_ID=$(grep -A1 "BUNDLE_ID" GoogleService-Info.plist | grep -o "com.navibatch.app")
        
        if [ ! -z "$API_KEY" ] && [ ! -z "$BUNDLE_ID" ]; then
            echo "   📱 Bundle ID: $BUNDLE_ID"
            echo "   🔑 API Key: ${API_KEY:0:20}..."
        fi
    else
        echo "❌ GoogleService-Info.plist 项目ID不匹配"
        exit 1
    fi
else
    echo "❌ 缺少GoogleService-Info.plist文件"
    exit 1
fi

# 检查Firebase代码启用状态
echo "📋 检查Firebase代码状态..."

# 检查FirebaseAIService.swift
if grep -q "import FirebaseCore" Services/FirebaseAIService.swift && grep -q "import FirebaseAI" Services/FirebaseAIService.swift; then
    echo "✅ FirebaseAIService.swift - import语句已启用"
else
    echo "❌ FirebaseAIService.swift - import语句未启用"
    exit 1
fi

if grep -q "generativeModel" Services/FirebaseAIService.swift && grep -q "ModelContent" Services/FirebaseAIService.swift; then
    echo "✅ FirebaseAIService.swift - Firebase AI实现已启用"
else
    echo "❌ FirebaseAIService.swift - Firebase AI实现未启用"
    exit 1
fi

# 检查FirebaseConfig.swift
if grep -q "import FirebaseCore" Services/FirebaseConfig.swift; then
    echo "✅ FirebaseConfig.swift - import语句已启用"
else
    echo "❌ FirebaseConfig.swift - import语句未启用"
    exit 1
fi

if grep -q "FirebaseApp.configure()" Services/FirebaseConfig.swift; then
    echo "✅ FirebaseConfig.swift - 初始化代码已启用"
else
    echo "❌ FirebaseConfig.swift - 初始化代码未启用"
    exit 1
fi

echo ""
echo "🎉 Firebase AI 集成检查完成！"
echo ""
echo "📊 集成状态总结："
echo "✅ Firebase项目: navibatch-ai-2024"
echo "✅ iOS应用配置: com.navibatch.app"
echo "✅ 配置文件: GoogleService-Info.plist"
echo "✅ SDK导入: FirebaseCore + FirebaseAI"
echo "✅ 代码启用: 所有Firebase代码已激活"
echo ""
echo "🚀 下一步："
echo "1. 在Xcode中编译项目"
echo "2. 运行应用并测试AI识别功能"
echo "3. 当OpenRouter失败时，点击'尝试Firebase AI'"
echo "4. 检查控制台日志确认Firebase AI正常工作"
echo ""
echo "🎯 预期效果："
echo "- 免费的AI识别服务"
echo "- 更快的响应速度"
echo "- 智能备用策略: OpenRouter → Firebase AI → OCR"
echo ""
echo "🔥 Firebase AI集成已完成！准备测试！"
