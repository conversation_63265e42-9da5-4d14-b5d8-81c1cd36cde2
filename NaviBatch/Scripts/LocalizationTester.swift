import Foundation

/// 本地化测试工具，用于测试不同语言环境下的本地化问题
class LocalizationTester {

    // MARK: - 配置

    // 本地化文件目录
    private static let localizationsDirectory: String = {
        // 尝试多种可能的路径
        let possiblePaths = [
            "Localizations",
            Bundle.main.bundlePath + "/Localizations",
            Bundle.main.resourcePath! + "/Localizations"
        ]

        // 检查哪个路径存在
        for path in possiblePaths {
            if FileManager.default.fileExists(atPath: path) {
                print("找到本地化目录: \(path)")
                return path
            }
        }

        // 尝试查找语言文件夹
        if let enPath = Bundle.main.path(forResource: "en", ofType: "lproj") {
            let parentPath = (enPath as NSString).deletingLastPathComponent
            print("使用语言文件夹的父目录: \(parentPath)")
            return parentPath
        }

        print("警告: 无法找到本地化目录，使用默认路径")
        return "Localizations"
    }()

    // 基准语言（用于比较其他语言文件）
    private static let baseLanguage = "zh-Hans"

    // 测试语言列表
    private static let testLanguages = [
        "zh-Hans", // 简体中文
        "ja",      // 日语
        "ar",      // 阿拉伯语（从右到左）
        "de",      // 德语（长单词）
        "es"       // 西班牙语
    ]

    // 测试场景
    enum TestScenario: String {
        case longText = "长文本测试"
        case rtl = "从右到左语言测试"
        case formatting = "格式化字符串测试"
        case specialChars = "特殊字符测试"
    }

    // 测试结果
    struct TestResult {
        var language: String
        var longTextIssues: [(key: String, baseLength: Int, langLength: Int, ratio: Double)]
        var formatIssues: [(key: String, baseValue: String, langValue: String, baseCount: Int, langCount: Int)]
        var specialCharIssues: [(key: String, value: String, char: String)]
        var isRTL: Bool
        var keyCount: Int
    }

    // MARK: - 公共方法

    /// 运行本地化测试，返回测试结果
    /// - Returns: 测试结果报告字符串
    static func run() -> String {
        var report = ""
        var results: [TestResult] = []

        // 读取基准语言文件
        let baseLanguagePath = "\(localizationsDirectory)/\(baseLanguage).lproj/Localizable.strings"
        let baseValues = extractKeyValues(from: baseLanguagePath)

        if baseValues.isEmpty {
            return "基准语言文件为空或无法解析"
        }

        report += "基准语言 (\(baseLanguage)) 包含 \(baseValues.count) 个本地化键\n\n"

        // 测试每种语言
        for language in testLanguages {
            let languagePath = "\(localizationsDirectory)/\(language).lproj/Localizable.strings"
            let languageValues = extractKeyValues(from: languagePath)

            if languageValues.isEmpty {
                report += "语言 \(language) 文件为空或无法解析\n"
                continue
            }

            var result = TestResult(
                language: language,
                longTextIssues: [],
                formatIssues: [],
                specialCharIssues: [],
                isRTL: language == "ar" || language == "he",
                keyCount: languageValues.count
            )

            // 测试长文本
            for (key, baseValue) in baseValues {
                if let langValue = languageValues[key] {
                    let baseLength = baseValue.count
                    let langLength = langValue.count

                    let ratio = Double(langLength) / Double(baseLength)
                    if ratio > 1.5 && baseLength > 10 {
                        result.longTextIssues.append((key, baseLength, langLength, ratio))
                    }
                }
            }

            // 测试格式化字符串
            let formatSpecifiers = ["%@", "%d", "%.1f", "%.0f", "%f"]

            for (key, baseValue) in baseValues {
                if let langValue = languageValues[key] {
                    let containsFormat = formatSpecifiers.contains { baseValue.contains($0) }

                    if containsFormat {
                        var baseFormatCount = 0
                        var langFormatCount = 0

                        for specifier in formatSpecifiers {
                            baseFormatCount += baseValue.components(separatedBy: specifier).count - 1
                            langFormatCount += langValue.components(separatedBy: specifier).count - 1
                        }

                        if baseFormatCount != langFormatCount {
                            result.formatIssues.append((key, baseValue, langValue, baseFormatCount, langFormatCount))
                        }
                    }
                }
            }

            // 测试特殊字符
            let specialChars = ["\\", "\n", "\t", "\r", "\""]

            for (key, value) in languageValues {
                for char in specialChars {
                    if value.contains(char) {
                        result.specialCharIssues.append((key, value, char))
                        break
                    }
                }
            }

            results.append(result)
        }

        // 生成报告
        report += generateReport(results: results)

        return report
    }

    // MARK: - 辅助函数

    /// 从 .strings 文件中提取键值对
    private static func extractKeyValues(from filePath: String) -> [String: String] {
        // 尝试多种编码
        let encodings: [String.Encoding] = [.utf8, .utf16, .utf16BigEndian, .utf16LittleEndian]

        var fileContent: String? = nil

        // 尝试不同的编码读取文件
        for encoding in encodings {
            if let content = try? String(contentsOfFile: filePath, encoding: encoding) {
                fileContent = content
                print("成功以 \(encoding) 编码读取文件: \(filePath)")
                break
            }
        }

        // 如果所有编码都失败，尝试使用 Data 读取
        if fileContent == nil {
            if let data = FileManager.default.contents(atPath: filePath),
               let content = String(data: data, encoding: .utf8) {
                fileContent = content
                print("成功使用 Data 方法读取文件: \(filePath)")
            }
        }

        // 如果仍然无法读取，返回空字典
        guard let content = fileContent else {
            print("无法读取文件: \(filePath)")

            // 检查文件是否存在
            if FileManager.default.fileExists(atPath: filePath) {
                print("文件存在，但无法读取内容")

                // 尝试读取文件属性
                if let attributes = try? FileManager.default.attributesOfItem(atPath: filePath) {
                    let fileSize = attributes[.size] as? UInt64 ?? 0
                    print("文件大小: \(fileSize) 字节")
                }
            } else {
                print("文件不存在: \(filePath)")
            }

            return [:]
        }

        var keyValues: [String: String] = [:]
        let lines = content.components(separatedBy: .newlines)

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            // 跳过空行、注释行和 MARK 行
            if trimmedLine.isEmpty ||
               trimmedLine.hasPrefix("//") ||
               trimmedLine.hasPrefix("/*") ||
               trimmedLine.hasPrefix("*/") {
                continue
            }

            // 提取键值对（格式: "key" = "value";）
            if let range = trimmedLine.range(of: "\".*?\"\\s*=\\s*\".*?\";", options: .regularExpression) {
                let keyValueString = String(trimmedLine[range])
                let components = keyValueString.components(separatedBy: "=")

                if components.count == 2 {
                    var key = components[0].trimmingCharacters(in: .whitespaces)
                    key = key.replacingOccurrences(of: "\"", with: "")

                    var value = components[1].trimmingCharacters(in: .whitespaces)
                    value = value.replacingOccurrences(of: "\";", with: "")
                    value = value.replacingOccurrences(of: "\"", with: "")

                    keyValues[key] = value
                }
            }
        }

        return keyValues
    }

    /// 生成测试报告
    private static func generateReport(results: [TestResult]) -> String {
        var report = ""

        for result in results {
            report += "\n========== 测试语言: \(result.language) ==========\n"
            report += "包含 \(result.keyCount) 个本地化键\n"

            // 长文本测试报告
            report += "\n=== 长文本测试 (\(result.language)) ===\n"

            if result.longTextIssues.isEmpty {
                report += "未发现明显的长文本问题\n"
            } else {
                for issue in result.longTextIssues {
                    report += "键: \(issue.key)\n"
                    report += "  基准语言 (\(issue.baseLength) 字符): [基准文本]\n"
                    report += "  测试语言 (\(issue.langLength) 字符): [测试文本]\n"
                    report += "  长度比例: \(issue.ratio)\n\n"
                }

                report += "发现 \(result.longTextIssues.count) 个可能需要注意的长文本\n"
            }

            // 从右到左语言测试报告
            report += "\n=== 从右到左语言测试 (\(result.language)) ===\n"

            if result.isRTL {
                report += "注意事项:\n"
                report += "1. 确保布局正确从右到左显示\n"
                report += "2. 检查文本对齐是否正确（右对齐）\n"
                report += "3. 验证导航方向是否正确\n"
                report += "4. 检查图标和控件位置是否适当调整\n\n"
                report += "建议使用本地化测试工具进行视觉检查\n"
            } else {
                report += "此语言不是从右到左语言，跳过测试\n"
            }

            // 格式化字符串测试报告
            report += "\n=== 格式化字符串测试 (\(result.language)) ===\n"

            if result.formatIssues.isEmpty {
                report += "未发现格式化字符串问题\n"
            } else {
                for issue in result.formatIssues {
                    report += "警告: 格式说明符数量不匹配\n"
                    report += "键: \(issue.key)\n"
                    report += "  基准语言: \(issue.baseValue) (\(issue.baseCount) 个格式说明符)\n"
                    report += "  测试语言: \(issue.langValue) (\(issue.langCount) 个格式说明符)\n\n"
                }
            }

            let formatCount = results.flatMap { $0.formatIssues }.count
            report += "发现 \(formatCount) 个包含格式说明符的字符串\n"
            report += "建议使用本地化测试工具测试格式化字符串的显示效果\n"

            // 特殊字符测试报告
            report += "\n=== 特殊字符测试 (\(result.language)) ===\n"

            if result.specialCharIssues.isEmpty {
                report += "未发现特殊字符问题\n"
            } else {
                for issue in result.specialCharIssues {
                    report += "键: \(issue.key)\n"
                    report += "  值: \(issue.value)\n"
                    report += "  包含特殊字符: \(issue.char)\n\n"
                }

                report += "发现 \(result.specialCharIssues.count) 个包含特殊字符的字符串\n"
            }

            report += "\n========== \(result.language) 测试完成 ==========\n\n"
        }

        report += "所有语言测试完成！\n"
        report += "建议使用本地化测试工具进行视觉检查，确保界面在不同语言环境下正常显示。"

        return report
    }
}
