import Foundation

/// 本地化文件更新工具，用于从基准语言文件复制缺失的键到其他语言文件
class LocalizationUpdater {

    // MARK: - 配置

    // 本地化文件目录
    private static let localizationsDirectory: String = {
        // 尝试多种可能的路径
        let possiblePaths = [
            "Localizations",
            Bundle.main.bundlePath + "/Localizations",
            Bundle.main.resourcePath! + "/Localizations"
        ]

        // 检查哪个路径存在
        for path in possiblePaths {
            if FileManager.default.fileExists(atPath: path) {
                print("找到本地化目录: \(path)")
                return path
            }
        }

        // 尝试查找语言文件夹
        if let enPath = Bundle.main.path(forResource: "en", ofType: "lproj") {
            let parentPath = (enPath as NSString).deletingLastPathComponent
            print("使用语言文件夹的父目录: \(parentPath)")
            return parentPath
        }

        print("警告: 无法找到本地化目录，使用默认路径")
        return "Localizations"
    }()

    // 基准语言（用于复制缺失的键）
    private static let baseLanguage = "zh-Hans"

    // 要更新的语言列表（不包括基准语言）
    private static let languagesToUpdate = [
        "de", "he", "el", "zh-Hant", "es", "it", "ms", "ko",
        "hu", "tr", "pl", "ru", "fr", "id", "nl", "th", "pt", "ro"
    ]

    // MARK: - 公共方法

    /// 运行本地化文件更新，返回更新结果
    /// - Returns: 更新结果报告字符串
    static func run() -> String {
        var report = ""

        // 读取基准语言文件
        let baseLanguagePath = "\(localizationsDirectory)/\(baseLanguage).lproj/Localizable.strings"
        let baseKeyValues = extractKeyValues(from: baseLanguagePath)

        if baseKeyValues.isEmpty {
            return "基准语言文件为空或无法解析"
        }

        report += "基准语言 (\(baseLanguage)) 包含 \(baseKeyValues.count) 个本地化键\n\n"

        // 更新所有语言文件
        for languageCode in languagesToUpdate {
            let result = updateLanguageFile(languageCode: languageCode, baseKeyValues: baseKeyValues)
            report += result + "\n"
        }

        report += "\n完成！所有语言文件已更新。"

        return report
    }

    // MARK: - 辅助函数

    /// 从 .strings 文件中提取键值对
    private static func extractKeyValues(from filePath: String) -> [String: String] {
        // 尝试多种编码
        let encodings: [String.Encoding] = [.utf8, .utf16, .utf16BigEndian, .utf16LittleEndian]

        var fileContent: String? = nil

        // 尝试不同的编码读取文件
        for encoding in encodings {
            if let content = try? String(contentsOfFile: filePath, encoding: encoding) {
                fileContent = content
                print("成功以 \(encoding) 编码读取文件: \(filePath)")
                break
            }
        }

        // 如果所有编码都失败，尝试使用 Data 读取
        if fileContent == nil {
            if let data = FileManager.default.contents(atPath: filePath),
               let content = String(data: data, encoding: .utf8) {
                fileContent = content
                print("成功使用 Data 方法读取文件: \(filePath)")
            }
        }

        // 如果仍然无法读取，返回空字典
        guard let content = fileContent else {
            print("无法读取文件: \(filePath)")

            // 检查文件是否存在
            if FileManager.default.fileExists(atPath: filePath) {
                print("文件存在，但无法读取内容")

                // 尝试读取文件属性
                if let attributes = try? FileManager.default.attributesOfItem(atPath: filePath) {
                    let fileSize = attributes[.size] as? UInt64 ?? 0
                    print("文件大小: \(fileSize) 字节")
                }
            } else {
                print("文件不存在: \(filePath)")
            }

            return [:]
        }

        var keyValues: [String: String] = [:]
        let lines = content.components(separatedBy: .newlines)

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            // 跳过空行、注释行和 MARK 行
            if trimmedLine.isEmpty ||
               trimmedLine.hasPrefix("//") ||
               trimmedLine.hasPrefix("/*") ||
               trimmedLine.hasPrefix("*/") {
                continue
            }

            // 提取键值对（格式: "key" = "value";）
            if let range = trimmedLine.range(of: "\".*?\"\\s*=\\s*\".*?\";", options: .regularExpression) {
                let keyValueString = String(trimmedLine[range])
                let components = keyValueString.components(separatedBy: "=")

                if components.count == 2 {
                    var key = components[0].trimmingCharacters(in: .whitespaces)
                    key = key.replacingOccurrences(of: "\"", with: "")

                    var value = components[1].trimmingCharacters(in: .whitespaces)
                    value = value.replacingOccurrences(of: "\";", with: "")
                    value = value.replacingOccurrences(of: "\"", with: "")

                    keyValues[key] = value
                }
            }
        }

        return keyValues
    }

    /// 获取文件中的注释和结构
    private static func extractFileStructure(from filePath: String) -> [String] {
        // 尝试多种编码
        let encodings: [String.Encoding] = [.utf8, .utf16, .utf16BigEndian, .utf16LittleEndian]

        var fileContent: String? = nil

        // 尝试不同的编码读取文件
        for encoding in encodings {
            if let content = try? String(contentsOfFile: filePath, encoding: encoding) {
                fileContent = content
                print("成功以 \(encoding) 编码读取文件结构: \(filePath)")
                break
            }
        }

        // 如果所有编码都失败，尝试使用 Data 读取
        if fileContent == nil {
            if let data = FileManager.default.contents(atPath: filePath),
               let content = String(data: data, encoding: .utf8) {
                fileContent = content
                print("成功使用 Data 方法读取文件结构: \(filePath)")
            }
        }

        // 如果仍然无法读取，返回空数组
        guard let content = fileContent else {
            print("无法读取文件结构: \(filePath)")
            return []
        }

        var structure: [String] = []
        let lines = content.components(separatedBy: .newlines)

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            // 保留空行、注释行和 MARK 行
            if trimmedLine.isEmpty ||
               trimmedLine.hasPrefix("//") ||
               trimmedLine.hasPrefix("/*") ||
               trimmedLine.hasPrefix("*/") {
                structure.append(line)
            }
        }

        return structure
    }

    /// 更新语言文件
    private static func updateLanguageFile(languageCode: String, baseKeyValues: [String: String]) -> String {
        let languagePath = "\(localizationsDirectory)/\(languageCode).lproj/Localizable.strings"

        // 读取当前语言文件的键值对
        let currentKeyValues = extractKeyValues(from: languagePath)

        if currentKeyValues.isEmpty {
            return "语言 \(languageCode) 文件为空或无法解析"
        }

        // 读取文件结构（注释等）
        let fileStructure = extractFileStructure(from: languagePath)

        // 创建新的文件内容
        var newContent = fileStructure.joined(separator: "\n")
        newContent += "\n\n"

        // 当前部分
        var currentSection = ""

        // 添加所有键值对，包括缺失的键（使用基准语言的值）
        for (key, baseValue) in baseKeyValues.sorted(by: { $0.key < $1.key }) {
            // 检查键是否属于新的部分
            if key.contains("_") {
                let possibleSection = key.components(separatedBy: "_")[0]
                if possibleSection != currentSection {
                    currentSection = possibleSection
                    newContent += "\n// MARK: - \(currentSection.capitalized)\n"
                }
            }

            // 使用当前语言的值（如果存在），否则使用基准语言的值
            let value = currentKeyValues[key] ?? baseValue
            newContent += "\"\(key)\" = \"\(value)\";\n"
        }

        // 计算添加的键数量
        let addedKeysCount = baseKeyValues.keys.filter { !currentKeyValues.keys.contains($0) }.count

        // 写入新文件
        do {
            try newContent.write(toFile: languagePath, atomically: true, encoding: .utf8)
            return "已更新 \(languageCode) 语言文件，添加了 \(addedKeysCount) 个新键"
        } catch {
            return "写入文件失败: \(error)"
        }
    }
}
