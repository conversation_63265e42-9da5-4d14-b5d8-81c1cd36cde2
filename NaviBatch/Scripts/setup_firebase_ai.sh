#!/bin/bash

# 🔥 Firebase AI 快速设置脚本
# 用于完成Firebase AI SDK集成的最后步骤

echo "🔥 Firebase AI SDK 设置脚本"
echo "=========================="
echo ""

# 检查是否在正确的目录
if [ ! -f "NaviBatchApp.swift" ]; then
    echo "❌ 错误：请在NaviBatch项目根目录运行此脚本"
    exit 1
fi

echo "✅ 项目目录检查通过"
echo ""

# 检查Firebase项目配置
if [ -f ".firebaserc" ]; then
    PROJECT_ID=$(grep -o '"navibatch-ai-2024"' .firebaserc)
    if [ ! -z "$PROJECT_ID" ]; then
        echo "✅ Firebase项目配置正确：navibatch-ai-2024"
    else
        echo "⚠️  Firebase项目ID需要更新"
    fi
else
    echo "❌ 缺少.firebaserc文件"
fi

echo ""

# 检查GoogleService-Info.plist
if [ -f "GoogleService-Info.plist" ]; then
    if grep -q "YOUR_API_KEY_HERE" GoogleService-Info.plist; then
        echo "⚠️  GoogleService-Info.plist 仍然是模板，需要从Firebase Console下载真实配置"
        echo "   📱 请访问：https://console.firebase.google.com/project/navibatch-ai-2024/settings/general"
        echo "   📱 添加iOS应用，Bundle ID: com.navibatch.app"
    else
        echo "✅ GoogleService-Info.plist 看起来已配置"
    fi
else
    echo "❌ 缺少GoogleService-Info.plist文件"
fi

echo ""

# 检查Firebase AI Service代码
if grep -q "// TODO: 手动安装Firebase AI SDK后取消注释" Services/FirebaseAIService.swift; then
    echo "⚠️  FirebaseAIService.swift 中的Firebase代码仍被注释"
    echo "   📝 需要在Xcode中添加Firebase AI SDK后取消注释"
else
    echo "✅ FirebaseAIService.swift 代码已启用"
fi

echo ""

# 显示下一步操作
echo "🚀 下一步操作："
echo "1. 在Xcode中添加Firebase AI SDK包"
echo "   URL: https://github.com/firebase/firebase-ios-sdk"
echo "   选择: FirebaseCore, FirebaseAI"
echo ""
echo "2. 从Firebase Console下载真实的GoogleService-Info.plist"
echo "   链接: https://console.firebase.google.com/project/navibatch-ai-2024/overview"
echo ""
echo "3. 取消注释Firebase相关代码"
echo ""
echo "4. 编译并测试应用"
echo ""

echo "🎉 Firebase AI集成即将完成！"
