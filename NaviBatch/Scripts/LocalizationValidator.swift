import Foundation

/// 本地化验证工具，用于验证所有语言文件包含相同的键
class LocalizationValidator {

    // MARK: - 配置

    // 本地化文件目录
    private static let localizationsDirectory: String = {
        // 尝试多种可能的路径
        let possiblePaths = [
            "Localizations",
            Bundle.main.bundlePath + "/Localizations",
            Bundle.main.resourcePath! + "/Localizations"
        ]

        // 检查哪个路径存在
        for path in possiblePaths {
            if FileManager.default.fileExists(atPath: path) {
                print("找到本地化目录: \(path)")
                return path
            }
        }

        // 尝试查找语言文件夹
        if let enPath = Bundle.main.path(forResource: "en", ofType: "lproj") {
            let parentPath = (enPath as NSString).deletingLastPathComponent
            print("使用语言文件夹的父目录: \(parentPath)")
            return parentPath
        }

        print("警告: 无法找到本地化目录，使用默认路径")
        return "Localizations"
    }()

    // 基准语言（用于比较其他语言文件）
    private static let baseLanguage = "zh-Hans"

    // MARK: - 公共方法

    /// 运行本地化验证，返回验证结果
    /// - Returns: 验证结果报告字符串
    static func run() -> String {
        var report = ""

        // 获取所有语言目录
        let languageDirectories = getLanguageDirectories()

        if languageDirectories.isEmpty {
            return "未找到任何语言目录"
        }

        // 查找基准语言目录
        guard let baseLanguageDir = languageDirectories.first(where: { $0.0 == baseLanguage }) else {
            return "未找到基准语言 (\(baseLanguage)) 目录"
        }

        // 查找简体中文目录
        let zhHansCode = "zh-Hans"
        if !languageDirectories.contains(where: { $0.0 == zhHansCode }) {
            print("未找到简体中文目录，将使用基准语言进行未翻译检测")
        }

        // 提取基准语言的键值对
        let baseStringsPath = "\(baseLanguageDir.1)/Localizable.strings"
        let baseKeyValues = extractKeyValues(from: baseStringsPath)

        if baseKeyValues.isEmpty {
            return "基准语言文件为空或无法解析"
        }

        // 提取简体中文的键值对（用于检测未翻译的字符串）
        var zhHansKeyValues: [String: String] = [:]
        if let zhHansDir = languageDirectories.first(where: { $0.0 == zhHansCode }) {
            let zhHansPath = "\(zhHansDir.1)/Localizable.strings"
            zhHansKeyValues = extractKeyValues(from: zhHansPath)
        }

        report += "基准语言 (\(baseLanguage)) 包含 \(baseKeyValues.count) 个本地化键\n\n"

        // 验证其他语言文件
        var allValid = true

        for (langCode, langDir) in languageDirectories where langCode != baseLanguage {
            let stringsPath = "\(langDir)/Localizable.strings"
            let langKeyValues = extractKeyValues(from: stringsPath)

            let keys = Array(langKeyValues.keys)
            let missingKeys = baseKeyValues.keys.filter { !keys.contains($0) }
            let extraKeys = keys.filter { !baseKeyValues.keys.contains($0) }

            report += "语言: \(langCode)\n"
            report += "  总键数: \(keys.count)\n"

            if !missingKeys.isEmpty {
                report += "  ⚠️ 缺少 \(missingKeys.count) 个键:\n"
                for key in missingKeys.sorted().prefix(10) {
                    report += "    - \"\(key)\"\n"
                }
                if missingKeys.count > 10 {
                    report += "    - ... 以及 \(missingKeys.count - 10) 个其他键\n"
                }
                allValid = false
            } else {
                report += "  ✅ 包含所有基准语言键\n"
            }

            if !extraKeys.isEmpty {
                report += "  ℹ️ 包含 \(extraKeys.count) 个额外键:\n"
                for key in extraKeys.sorted().prefix(5) {
                    report += "    - \"\(key)\"\n"
                }
                if extraKeys.count > 5 {
                    report += "    - ... 以及 \(extraKeys.count - 5) 个其他键\n"
                }
            }

            // 检测未翻译的字符串（与简体中文相同的字符串）
            if langCode != zhHansCode && !zhHansKeyValues.isEmpty {
                var untranslatedCount = 0
                var untranslatedKeys: [String] = []

                for (key, langValue) in langKeyValues {
                    if let zhValue = zhHansKeyValues[key], langValue == zhValue {
                        untranslatedCount += 1
                        if untranslatedKeys.count < 10 {
                            untranslatedKeys.append(key)
                        }
                    }
                }

                if untranslatedCount > 0 {
                    let percentage = Int(Double(untranslatedCount) / Double(langKeyValues.count) * 100)
                    report += "  ℹ️ 发现 \(untranslatedCount) 个疑似未翻译的键 (\(percentage)%):\n"
                    for key in untranslatedKeys.sorted() {
                        report += "    - \"\(key)\" = \"\(langKeyValues[key] ?? "")\"\n"
                    }
                    if untranslatedCount > 10 {
                        report += "    - ... 以及 \(untranslatedCount - 10) 个其他键\n"
                    }
                }
            }

            report += "\n"
        }

        if allValid {
            report += "✅ 所有语言文件都包含基准语言的所有键\n"
        } else {
            report += "⚠️ 一些语言文件缺少键，请更新这些文件\n"
            report += "提示: 可以使用「本地化文件更新」工具自动添加缺失的键\n"
        }

        // 检查空值
        report += "\n检查空值...\n"

        for (langCode, langDir) in languageDirectories {
            let stringsPath = "\(langDir)/Localizable.strings"
            let emptyValues = checkEmptyValues(in: stringsPath)

            if !emptyValues.isEmpty {
                report += "语言: \(langCode)\n"
                report += "  ⚠️ 发现 \(emptyValues.count) 个空值:\n"
                for key in emptyValues.sorted().prefix(10) {
                    report += "    - \"\(key)\"\n"
                }
                if emptyValues.count > 10 {
                    report += "    - ... 以及 \(emptyValues.count - 10) 个其他键\n"
                }
                report += "\n"
            }
        }

        return report
    }

    // MARK: - 辅助函数

    /// 从 .strings 文件中提取键值对
    private static func extractKeyValues(from filePath: String) -> [String: String] {
        // 尝试多种编码
        let encodings: [String.Encoding] = [.utf8, .utf16, .utf16BigEndian, .utf16LittleEndian]

        var fileContent: String? = nil

        // 尝试不同的编码读取文件
        for encoding in encodings {
            if let content = try? String(contentsOfFile: filePath, encoding: encoding) {
                fileContent = content
                print("成功以 \(encoding) 编码读取文件: \(filePath)")
                break
            }
        }

        // 如果所有编码都失败，尝试使用 Data 读取
        if fileContent == nil {
            if let data = FileManager.default.contents(atPath: filePath),
               let content = String(data: data, encoding: .utf8) {
                fileContent = content
                print("成功使用 Data 方法读取文件: \(filePath)")
            }
        }

        // 如果仍然无法读取，返回空字典
        guard let content = fileContent else {
            print("无法读取文件: \(filePath)")

            // 检查文件是否存在
            if FileManager.default.fileExists(atPath: filePath) {
                print("文件存在，但无法读取内容")

                // 尝试读取文件属性
                if let attributes = try? FileManager.default.attributesOfItem(atPath: filePath) {
                    let fileSize = attributes[.size] as? UInt64 ?? 0
                    print("文件大小: \(fileSize) 字节")
                }
            } else {
                print("文件不存在: \(filePath)")
            }

            return [:]
        }

        var keyValues: [String: String] = [:]
        let lines = content.components(separatedBy: .newlines)

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            // 跳过空行、注释行和 MARK 行
            if trimmedLine.isEmpty ||
               trimmedLine.hasPrefix("//") ||
               trimmedLine.hasPrefix("/*") ||
               trimmedLine.hasPrefix("*/") {
                continue
            }

            // 提取键值对（格式: "key" = "value";）
            if let range = trimmedLine.range(of: "\".*?\"\\s*=\\s*\".*?\";", options: .regularExpression) {
                let keyValueString = String(trimmedLine[range])
                let components = keyValueString.components(separatedBy: "=")

                if components.count == 2 {
                    var key = components[0].trimmingCharacters(in: .whitespaces)
                    key = key.replacingOccurrences(of: "\"", with: "")

                    var value = components[1].trimmingCharacters(in: .whitespaces)
                    value = value.replacingOccurrences(of: "\";", with: "")
                    value = value.replacingOccurrences(of: "\"", with: "")

                    keyValues[key] = value
                }
            }
        }

        return keyValues
    }

    /// 从 .strings 文件中提取键
    private static func extractKeys(from filePath: String) -> [String] {
        return Array(extractKeyValues(from: filePath).keys)
    }

    /// 获取所有语言目录
    private static func getLanguageDirectories() -> [(String, String)] {
        let fileManager = FileManager.default
        var languageDirs: [(String, String)] = []

        guard let contents = try? fileManager.contentsOfDirectory(atPath: localizationsDirectory) else {
            print("无法访问本地化目录: \(localizationsDirectory)")
            return []
        }

        for item in contents {
            if item.hasSuffix(".lproj") {
                let languageCode = item.replacingOccurrences(of: ".lproj", with: "")
                let dirPath = "\(localizationsDirectory)/\(item)"
                languageDirs.append((languageCode, dirPath))
            }
        }

        return languageDirs
    }

    /// 检查文件中的空值
    private static func checkEmptyValues(in filePath: String) -> [String] {
        guard let content = try? String(contentsOfFile: filePath, encoding: .utf8) else {
            print("无法读取文件: \(filePath)")
            return []
        }

        let lines = content.components(separatedBy: .newlines)
        var emptyValues: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            // 跳过空行、注释行和 MARK 行
            if trimmedLine.isEmpty ||
               trimmedLine.hasPrefix("//") ||
               trimmedLine.hasPrefix("/*") ||
               trimmedLine.hasPrefix("*/") {
                continue
            }

            // 检查是否有空值（格式: "key" = "";）
            if trimmedLine.contains(" = \"\";") {
                if let range = trimmedLine.range(of: "\".*?\"", options: .regularExpression) {
                    let key = String(trimmedLine[range])
                        .replacingOccurrences(of: "\"", with: "")
                    emptyValues.append(key)
                }
            }
        }

        return emptyValues
    }
}
