import Foundation

/// 本地化检查工具，用于检测代码中的硬编码文本
class LocalizationChecker {

    // MARK: - 配置

    // 要检查的目录
    private static let directoriesToCheck = ["Views"]

    // 要忽略的文件
    private static let filesToIgnore = [
        "LocalizationManager.swift",
        "AppDelegate.swift",
        "SceneDelegate.swift"
    ]

    // 要忽略的字符串模式（正则表达式）
    private static let patternsToIgnore = [
        #"^[0-9.]+$"#,                  // 纯数字
        #"^[A-Za-z0-9_]+\.[A-Za-z0-9_]+$"#, // 文件名或资源名
        #"^[A-Za-z0-9_]+$"#,            // 变量名或常量名
        #"\\.self"#,                    // .self 引用
        #"^[a-z0-9_]+(\.[a-z0-9_]+)+$"#, // 点表示法（如 system.name）
        #"^#[A-Fa-f0-9]{6}$"#,          // 颜色代码
        #"^[A-Za-z0-9_]+\\\("#          // 函数调用
    ]

    // 要检查的 UI 元素
    private static let uiElementsToCheck = [
        "Text\\(\"([^\"]+)\"\\)",
        "Label\\(\"([^\"]+)\"",
        "Button\\(\"([^\"]+)\"",
        "TextField\\(\"([^\"]+)\"",
        "NavigationTitle\\(\"([^\"]+)\"\\)",
        "title: \"([^\"]+)\"",
        "message: \"([^\"]+)\"",
        "placeholder: \"([^\"]+)\"",
        "navigationTitle\\(\"([^\"]+)\"\\)"
    ]

    // 存储结果
    private static var hardcodedStrings: [String: [(Int, String)]] = [:]

    // MARK: - 公共方法

    /// 运行本地化检查，返回检查结果
    /// - Returns: 检查结果字符串
    static func run() -> String {
        // 清空之前的结果
        hardcodedStrings = [:]

        // 获取所有要检查的文件
        var allFiles: [String] = []
        for directory in directoriesToCheck {
            allFiles.append(contentsOf: getSwiftFiles(in: directory))
        }

        // 检查所有文件
        for file in allFiles {
            checkFile(file)
        }

        // 生成结果报告
        return generateReport()
    }

    // MARK: - 辅助函数

    /// 获取目录中的所有 Swift 文件
    private static func getSwiftFiles(in directory: String) -> [String] {
        let fileManager = FileManager.default
        var files: [String] = []

        guard let enumerator = fileManager.enumerator(atPath: directory) else {
            print("无法访问目录: \(directory)")
            return []
        }

        while let file = enumerator.nextObject() as? String {
            if file.hasSuffix(".swift") && !filesToIgnore.contains(where: { file.contains($0) }) {
                files.append("\(directory)/\(file)")
            }
        }

        return files
    }

    /// 检查字符串是否应该被忽略
    private static func shouldIgnore(_ string: String) -> Bool {
        for pattern in patternsToIgnore {
            if string.range(of: pattern, options: .regularExpression) != nil {
                return true
            }
        }
        return false
    }

    /// 检查字符串是否已本地化
    private static func isLocalized(_ line: String) -> Bool {
        return line.contains(".localized") ||
               line.contains("NSLocalizedString") ||
               line.contains("Localizable.strings")
    }

    /// 检查文件中的硬编码字符串
    private static func checkFile(_ filePath: String) {
        guard let content = try? String(contentsOfFile: filePath, encoding: .utf8) else {
            print("无法读取文件: \(filePath)")
            return
        }

        let lines = content.components(separatedBy: .newlines)
        var results: [(Int, String)] = []

        for (index, line) in lines.enumerated() {
            // 跳过注释行
            if line.trimmingCharacters(in: .whitespaces).hasPrefix("//") {
                continue
            }

            // 跳过已本地化的行
            if isLocalized(line) {
                continue
            }

            // 检查 UI 元素中的硬编码字符串
            for pattern in uiElementsToCheck {
                if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
                    let nsRange = NSRange(line.startIndex..<line.endIndex, in: line)
                    let matches = regex.matches(in: line, options: [], range: nsRange)

                    for match in matches {
                        if match.numberOfRanges > 1, let range = Range(match.range(at: 1), in: line) {
                            let string = String(line[range])
                            if !shouldIgnore(string) && string.count > 1 {
                                results.append((index + 1, string))
                            }
                        }
                    }
                }
            }
        }

        if !results.isEmpty {
            hardcodedStrings[filePath] = results
        }
    }

    /// 生成检查报告
    private static func generateReport() -> String {
        var report = ""

        if hardcodedStrings.isEmpty {
            report += "✅ 没有发现硬编码字符串！"
        } else {
            report += "⚠️ 发现 \(hardcodedStrings.count) 个文件中有硬编码字符串：\n\n"

            for (file, strings) in hardcodedStrings.sorted(by: { $0.key < $1.key }) {
                let fileName = file.components(separatedBy: "/").last ?? file
                report += "📄 \(fileName):\n"

                for (lineNumber, string) in strings {
                    report += "   第 \(lineNumber) 行: \"\(string)\"\n"
                    report += "   建议: 将 \"\(string)\" 替换为 \"\(string.lowercased().replacingOccurrences(of: " ", with: "_")).localized\"\n\n"
                }
                report += "---\n"
            }

            report += "\n总计: \(hardcodedStrings.values.flatMap { $0 }.count) 个硬编码字符串"
        }

        return report
    }
}
