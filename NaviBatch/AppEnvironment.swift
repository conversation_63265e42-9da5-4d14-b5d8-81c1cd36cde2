import Foundation
import SwiftUI

/// 应用环境配置，用于区分不同的运行环境
struct AppEnvironment {
    /// 判断当前是否在预览环境中运行
    static var isPreview: Bool {
        #if DEBUG
        // 检测是否在SwiftUI预览中运行
        return ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"
        #else
        return false
        #endif
    }

    /// 判断当前是否在调试环境中运行
    static var isDebug: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }

    /// 获取应用版本
    static var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }

    /// 获取构建版本
    static var buildVersion: String {
        return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    }

    /// 获取完整的版本信息
    static var versionInfo: String {
        return "版本 \(appVersion) (\(buildVersion))"
    }

    /// 获取英文版本信息（用于About页面）
    static var versionInfoEnglish: String {
        return "Version \(appVersion) (\(buildVersion))"
    }
}