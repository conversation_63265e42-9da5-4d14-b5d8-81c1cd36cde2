import SwiftUI
import MapKit
import SwiftData
import Foundation
import CoreLocation

// 导入日志工具
import os.log

// 距离计算状态
enum DistanceCalculationStatus {
    case notCalculated  // 未计算
    case calculating    // 计算中
    case completed      // 完成（真实距离）
    case fallback       // 降级（直线距离）
}

// MKPolyline扩展，用于获取坐标数组
extension MKPolyline {
    var coordinates: [CLLocationCoordinate2D] {
        var coords = [CLLocationCoordinate2D](repeating: kCLLocationCoordinate2DInvalid, count: pointCount)
        getCoordinates(&coords, range: NSRange(location: 0, length: pointCount))
        return coords
    }
}

@MainActor
class RouteViewModel: NSObject, ObservableObject, CLLocationManagerDelegate {
    /// 共享实例 - 单例模式
    static let shared = RouteViewModel()



    // 路线管理器
    private var routeManager: RouteManager?

    // MARK: - 状态变量
    @Published var cameraPosition: MapCameraPosition = .region(MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: -37.8794, longitude: 145.1498),
        span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
    ))
    @Published var mapType: MKMapType = .standard
    @Published var deliveryPoints: [DeliveryPoint] = []
    @Published var driverLocation: CLLocationCoordinate2D? = CLLocationCoordinate2D(latitude: -37.8794, longitude: 145.1498)    // MARK: - 多选模式相关
    @Published var isMultiSelectMode = false   // 是否处于多选模式
    @Published var selectedPoints: [UUID] = [] // 当前选中的点的ID数组，保存UUID而非整个对象
    @Published var selectedPointsOrder: [UUID] = [] // 存储选中点的顺序
    @Published var maxSelectionCount = 10     // 最大可选择数量

    // MARK: - 路线动画相关
    @Published var routePulsePosition: CLLocationCoordinate2D? = nil // 脉动点当前位置
    @Published var isRouteAnimationActive = false // 路线动画是否激活
    @Published var selectedPointsRoute: MKRoute? = nil // 选中点的实际路线
    @Published var routeConnections: [RouteConnection] = [] // 路线连接线段

    // MARK: - 地址点显示控制
    @Published var showCompletedDeliveries = false // 默认不显示已完成派送的地址
    @Published var showingSavedGroups: Bool = false
    @Published var showingGroupNameInput: Bool = false
    @Published var groupNameInput: String = ""
    @Published var isShowingUserLocation: Bool = false // 是否显示用户位置标记
    @Published var isLoading: Bool = false { // 添加加载状态标志
        willSet {
            // 当加载状态变化时，记录日志
            if newValue != isLoading {
                if newValue {
                    logInfo("开始加载操作")
                } else {
                    logInfo("加载操作完成")
                }
            }
        }
    }

    // 防止重复加载的标志
    private var isSetupInProgress: Bool = false



    @Published var isOptimizingRoute: Bool = false // 添加路线优化中状态标志
    @Published var showSelectionLimitAlert: Bool = false // 添加选择上限提示标志
    @Published var selectedMapPoint: DeliveryPoint? = nil // 地图中选中的点

    // 当前路线相关
    @Published var currentRoute: Route? = nil {
        didSet {
            if let route = currentRoute {
                logInfo("RouteViewModel - 当前路线已更新: '\(route.name)', ID: \(route.id.uuidString), 点数: \(route.points.count)")

                // 当路线变更时，自动更新配送点，但避免重复加载
                if !isSetupInProgress {
                    Task {
                        await setupDeliveryPoints()
                    }
                } else {
                    logInfo("RouteViewModel - 跳过自动更新配送点，因为已有加载操作正在进行")
                }
                // 发送通知以便其他组件可以响应路线变化
                NotificationCenter.default.post(name: Notification.Name("CurrentRouteChanged"), object: route.id.uuidString)
            } else {
                logInfo("RouteViewModel - 当前路线已设置为nil")
            }
        }
    }
    @Published var showRouteSheet: Bool = false
    @Published var isRouteNewlyCreated: Bool = false // 跟踪路线是新创建的还是从数据库加载的

    // 添加路线数据变化监听器
    private var routeObserver: NSObjectProtocol? = nil

    // 路线优化进度跟踪属性
    @Published var optimizationProgress: Double = 0.0 // 优化进度（0-1）
    @Published var currentProcessingPoint: Int = 0 // 当前处理的点
    @Published var totalPoints: Int = 0 // 总点数
    @Published var estimatedRemainingTime: String? = nil // 预计剩余时间
    @Published var optimizingRotationDegrees: Double = 0 // 旋转动画角度
    @Published var justCompletedOptimization: Bool = false // 优化刚刚完成的标志

    // 路线优化阶段枚举
    enum OptimizationPhase {
        case initializing // 初始化数据
        case calculatingDistances // 计算距离矩阵
        case runningAlgorithm // 运行算法
        case finalizing // 完成优化

        var description: String {
            switch self {
            case .initializing:
                return "初始化数据"
            case .calculatingDistances:
                return "计算距离矩阵"
            case .runningAlgorithm:
                return "运行优化算法"
            case .finalizing:
                return "完成优化"
            }
        }
    }

    // 当前优化阶段
    @Published var currentOptimizationPhase: OptimizationPhase = .initializing

    // 自定义起点和终点
    @Published var customStartAddress: String? = nil // 自定义起始地址
    @Published var customStartCoordinate: CLLocationCoordinate2D? = nil // 自定义起始坐标

    // 路线优化相关信息
    @Published var routeOptimizationInfo: RouteOptimizationInfo? = nil // 路线优化信息
    // 临时存储优化前的配送点顺序，用于取消操作
    @Published var originalPointsOrder: [UUID] = []
    // 临时存储优化后的配送点顺序，用于确认操作
    @Published var optimizedPointsOrder: [UUID] = []
    // 优化卡片折叠/展开状态
    @Published var isOptimizationCardExpanded: Bool = false
    @Published var showRouteInfo: Bool = false

    // 最大可选点数常量（Apple Maps最多支持14个导航点，考虑到出发地点，所以限制为14个）
    private let maxSelectablePoints = 14

    // 计算剩余可选数量
    var remainingSelectableCount: Int {
        return max(0, maxSelectablePoints - selectedPoints.count)
    }

    // 日志辅助函数
    private func logInfo(_ message: String) {
        print("[INFO] \(message)")
    }

    private func logError(_ message: String) {
        print("[ERROR] \(message)")
    }

    // MARK: - 依赖属性
    var modelContext: ModelContext?
    private var locationManager: CLLocationManager?
    private var locationDelegate: LocationManagerDelegate? // 添加强引用属性

    // 订阅管理器
    private var subscriptionManager = SubscriptionManager.shared

    // 订阅相关状态
    @Published var showSubscriptionPrompt: Bool = false

    // MARK: - 地图显示控制
    @Published var showAllPointsOnMap = false  // 是否显示地图上的所有点，默认为false

    // MARK: - 初始化
    override init() {
        // 设置默认的地图位置 - 这可以在super.init()之前做，因为它不使用self
        cameraPosition = .region(MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631), // 默认位置
            span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        ))

        // 先调用父类初始化
        super.init()

        // 下面的操作都在super.init()之后执行
        logInfo("RouteViewModel - 初始化开始")

        // 设置位置管理器
        setupLocationManager()

        // 提示：需要创建路线
        promptForRouteCreationIfNeeded()

        // 打印调试信息
        logInfo("\n\n🚀 RouteViewModel 已初始化 🚀\n")

        // 移除硬编码模拟位置设置 - 现在使用iOS模拟器的自定义位置功能

        // 初始化RouteManager
        initializeRouteManager()

        logInfo("RouteViewModel - 初始化完成")

        // 注意：这里不主动调用检查路线方法，因为此时 modelContext 可能还没有设置
    }

    // 设置位置管理器
    private func setupLocationManager() {
        logInfo("RouteViewModel - 开始设置位置管理器")

        // 初始化位置管理器
        locationManager = CLLocationManager()
        guard let locationManager = locationManager else {
            logError("RouteViewModel - 位置管理器初始化失败")
            return
        }

        locationManager.delegate = self

        // 优化位置更新配置以提高能效
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters
        locationManager.distanceFilter = 10.0 // 移动10米才更新
        locationManager.activityType = .otherNavigation

        // 在iOS 11+ 上进一步减少位置更新
        if #available(iOS 11.0, *) {
            locationManager.showsBackgroundLocationIndicator = false
        }

        // 请求位置权限
        locationManager.requestWhenInUseAuthorization()

        // 如果权限已获得，开始更新位置
        if #available(iOS 14.0, *) {
            // iOS 14及以上使用实例属性
            if locationManager.authorizationStatus == .authorizedWhenInUse ||
               locationManager.authorizationStatus == .authorizedAlways {
                locationManager.startUpdatingLocation()
                logInfo("RouteViewModel - 位置管理器已开始更新位置")
            } else {
                logInfo("RouteViewModel - 位置权限未获得，等待用户授权")
            }
        } else {
            // iOS 14以下使用类方法（被弃用但仍需支持旧版iOS）
            if CLLocationManager.authorizationStatus() == .authorizedWhenInUse ||
               CLLocationManager.authorizationStatus() == .authorizedAlways {
                locationManager.startUpdatingLocation()
                logInfo("RouteViewModel - 位置管理器已开始更新位置")
            } else {
                logInfo("RouteViewModel - 位置权限未获得，等待用户授权")
            }
        }

        logInfo("RouteViewModel - 位置管理器设置完成")
    }

    // 初始化RouteManager
    private func initializeRouteManager() {
        Task { @MainActor in
            // 使用持久化存储容器
            let container = getPersistentContainer()
            routeManager = RouteManager(modelContext: container.mainContext)


        }
    }



    // 设置 ModelContext 并确保使用持久化存储
    func setModelContext(_ context: ModelContext?) {
        // 检查是否在预览环境中运行
        let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

        // 检查传入的 context 是否使用持久化存储
        if let context = context, let url = context.container.configurations.first?.url {
            if url.path == "/dev/null" && !isPreview {
                // 只在非预览环境中尝试切换到持久化存储
                logInfo("RouteViewModel - 警告：传入的 ModelContext 使用内存数据库，尝试切换到持久化存储")

                // 尝试获取持久化存储容器
                let persistentContainer = getPersistentContainer()
                if let persistentUrl = persistentContainer.configurations.first?.url, persistentUrl.path != "/dev/null" {
                    logInfo("RouteViewModel - 成功切换到持久化存储，路径: \(persistentUrl.path)")
                    self.modelContext = persistentContainer.mainContext

                    // 更新RouteManager的ModelContext
                    if routeManager == nil {
                        routeManager = RouteManager(modelContext: persistentContainer.mainContext)
                    } else {
                        // 创建新的RouteManager实例
                        routeManager = RouteManager(modelContext: persistentContainer.mainContext)
                    }

                } else {
                    logInfo("RouteViewModel - 无法获取持久化存储，使用传入的 ModelContext")
                    self.modelContext = context
                }
            } else {
                if isPreview {
                    logInfo("RouteViewModel - 预览环境中使用传入的 ModelContext，路径: \(url.path)")
                } else {
                    logInfo("RouteViewModel - 使用传入的持久化 ModelContext，路径: \(url.path)")
                }
                self.modelContext = context
            }
        } else {
            logInfo("RouteViewModel - 传入的 ModelContext 为 nil 或无法获取路径")
            self.modelContext = context
        }

        // 初始化
        initializeAfterModelContextSet()
    }

    // 添加一个方法，可以在 modelContext 设置后立即调用
    func initializeAfterModelContextSet() {
        guard let modelContext = modelContext else {
            logError("RouteViewModel - initializeAfterModelContextSet: ModelContext 尚未设置")
            return
        }

        logInfo("RouteViewModel - ModelContext 已设置，路径: \(modelContext.container.configurations.first?.url.path ?? "未知")")

        // 确保RouteManager使用相同的ModelContext
        if routeManager == nil {
            routeManager = RouteManager(modelContext: modelContext)
        } else if routeManager?.modelContext !== modelContext {
            // 如果ModelContext不同，创建新的RouteManager
            routeManager = RouteManager(modelContext: modelContext)
        }

        // 设置路线数据变化监听器
        setupRouteChangeObserver()

        // 主动检查路线
        Task {
            await checkAndCreateDefaultRoute()
        }
    }

    // 设置路线数据变化监听器
    private func setupRouteChangeObserver() {
        // 移除旧的观察者（如果存在）
        if let observer = routeObserver {
            NotificationCenter.default.removeObserver(observer)
            routeObserver = nil
        }

        // 添加新的观察者
        routeObserver = NotificationCenter.default.addObserver(
            forName: Notification.Name("RouteDataChanged"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }

            // 在主线程上执行所有操作，避免Sendable问题
            Task { @MainActor in
                // 如果当前没有选中的路线，不需要刷新
                if self.currentRoute == nil {
                    self.logInfo("RouteViewModel - 收到路线数据变化通知，但当前没有选中的路线")
                    return
                }

                // 如果已经在加载中，避免重复刷新
                if self.isSetupInProgress {
                    self.logInfo("RouteViewModel - 收到路线数据变化通知，但已有加载操作正在进行，跳过刷新")
                    return
                }

                // 获取当前路线ID
                if let route = self.currentRoute {
                    self.logInfo("RouteViewModel - 收到路线数据变化通知，刷新当前路线: \(route.id.uuidString)")
                } else {
                    self.logInfo("RouteViewModel - 收到路线数据变化通知，但currentRoute为nil")
                }

                // 刷新当前路线数据
                await self.refreshCurrentRoute()
            }
        }

        logInfo("RouteViewModel - 已设置路线数据变化监听器")
    }

    // 清理资源
    deinit {
        // 移除观察者
        if let observer = routeObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        // 在deinit中不能直接调用actor隔离的方法，使用print代替
        print("[INFO] RouteViewModel - deinit")
    }

    // 设置模拟位置 - 已移除硬编码，现在依赖iOS模拟器的自定义位置
    func setupMockLocation() {
        logInfo("RouteViewModel - 模拟器环境检测，但不再强制设置位置")

        // 只设置模拟器标志，不强制位置
        UserDefaults.standard.set(true, forKey: "IsRunningInSimulator")
        // 移除强制使用默认位置的标志
        UserDefaults.standard.removeObject(forKey: "ForceUseDefaultLocation")

        logInfo("RouteViewModel - 将使用iOS模拟器的自定义位置设置")
    }

    // 使用默认位置（当真实定位失败时）
    func useFallbackLocation() {
        logInfo("RouteViewModel - 使用默认位置（真实定位失败）")

        // 直接使用LocationManager的默认位置功能
        LocationManager.shared.useDefaultLocation()

        // 如果LocationManager已经有位置，直接使用
        if let userLocation = LocationManager.shared.userLocation {
            driverLocation = userLocation
            logInfo("RouteViewModel - 已从LocationManager获取默认位置: \(userLocation.latitude), \(userLocation.longitude)")
            return
        }

        // 如果LocationManager还没有位置，使用备用方法
        Task {
            // 使用指定的默认地址 - 确保格式规范，包含所有必要信息
            let defaultAddress = "1 Kerferd Road, Glen Waverley, VIC 3150, Australia"
            logInfo("RouteViewModel - 尝试地理编码默认地址: \(defaultAddress)")

            // 创建地理编码器并设置区域提示
            let geocoder = CLGeocoder()

            do {
                // 直接使用完整地址进行地理编码
                let placemarks = try await geocoder.geocodeAddressString(defaultAddress)

                // 详细记录所有返回的地标信息
                for (index, placemark) in placemarks.enumerated() {
                    if let location = placemark.location?.coordinate {
                        let country = placemark.country ?? "未知国家"
                        let locality = placemark.locality ?? "未知城市"
                        let thoroughfare = placemark.thoroughfare ?? "未知街道"
                        let subThoroughfare = placemark.subThoroughfare ?? "未知门牌"

                        logInfo("地理编码结果[\(index)]: \(defaultAddress) -> " +
                                "(\(location.latitude), \(location.longitude)), " +
                                "国家: \(country), 城市: \(locality), " +
                                "街道: \(thoroughfare), 门牌: \(subThoroughfare)")
                    }
                }

                // 优先选择澳大利亚的结果
                if let australiaPlacemark = placemarks.first(where: { $0.country == "Australia" }),
                   let location = australiaPlacemark.location?.coordinate {

                    await MainActor.run {
                        driverLocation = location
                        logInfo("RouteViewModel - 已设置默认位置坐标(从地址): \(location.latitude), \(location.longitude)")
                    }

                    // 将结果添加到缓存
                    geocodingCache[defaultAddress] = location
                    return
                }

                // 如果没有澳大利亚的结果，使用第一个结果
                if let firstPlacemark = placemarks.first,
                   let location = firstPlacemark.location?.coordinate {

                    await MainActor.run {
                        driverLocation = location
                        logInfo("RouteViewModel - 已设置默认位置坐标(从第一个结果): \(location.latitude), \(location.longitude)")
                    }

                    // 将结果添加到缓存
                    geocodingCache[defaultAddress] = location
                    return
                }

                // 如果没有任何结果，使用备用坐标
                logError("RouteViewModel - 地理编码没有返回结果，使用备用坐标")
                useFallbackCoordinate()

            } catch {
                logError("RouteViewModel - 地理编码失败: \(error.localizedDescription)，使用备用坐标")
                useFallbackCoordinate()
            }
        }
    }

    // 使用备用坐标（当前区域默认位置）
    private func useFallbackCoordinate() {
        Task {
            await MainActor.run {
                // 使用当前区域的默认坐标
                let defaultCoordinate = LocationManager.shared.getDefaultCoordinateForCurrentRegion()
                driverLocation = defaultCoordinate
                logInfo("RouteViewModel - 使用当前区域默认备用坐标: \(defaultCoordinate.latitude), \(defaultCoordinate.longitude)")

                // 确保位置已更新
                objectWillChange.send()
            }
        }
    }

    // MARK: - 自定义起始和终点地址方法

    /// 设置自定义起始地址
    /// - Parameters:
    ///   - address: 地址字符串
    ///   - coordinate: 地址对应的坐标
    func setCustomStartAddress(_ address: String, coordinate: CLLocationCoordinate2D) {
        customStartAddress = address
        customStartCoordinate = coordinate
        logInfo("已设置自定义起始地址: \(address), 坐标: (\(coordinate.latitude), \(coordinate.longitude))")

        // 🎯 自动将地图相机移动到起点位置
        withAnimation(.easeInOut(duration: 0.8)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
            ))
        }
        logInfo("地图已移动到起点位置: (\(coordinate.latitude), \(coordinate.longitude))")
    }

    /// 清除自定义起始地址
    func clearCustomStartAddress() {
        customStartAddress = nil
        customStartCoordinate = nil
        logInfo("已清除自定义起始地址")
    }



    // 更新用户位置
    func updateDriverLocation(_ location: CLLocationCoordinate2D) {
        // 记录位置信息（仅用于调试）
        logLocationInfo(location)

        // 直接使用提供的位置
        driverLocation = location
    }

    // 记录位置信息（仅用于调试）
    func logLocationInfo(_ coordinate: CLLocationCoordinate2D) {
        // 记录位置信息
        logInfo("RouteViewModel - 当前位置坐标: (\(coordinate.latitude), \(coordinate.longitude))")
    }

    // MARK: - 路线管理
    // 检查并创建默认路线
    func checkAndCreateDefaultRoute() async {
        logInfo("RouteViewModel - checkAndCreateDefaultRoute: 开始检查路线")

        // 如果已经有当前路线，则不覆盖它
        if let existingRoute = currentRoute {
            logInfo("RouteViewModel - checkAndCreateDefaultRoute: 已有当前路线，保持不变: '\(existingRoute.name)', ID=\(existingRoute.id.uuidString)")
            return
        }

        guard let modelContext = modelContext else {
            logError("RouteViewModel - 无法检查路线：没有ModelContext")
            logError("RouteViewModel - checkAndCreateDefaultRoute - 没有ModelContext，无法检查或创建路线")
            return
        }

        logInfo("RouteViewModel - ModelContext可用，开始检查现有路线")

        // 打印数据库路径，帮助调试
        if let url = modelContext.container.configurations.first?.url {
            logInfo("RouteViewModel - 数据库路径: \(url.path)")
        } else {
            logInfo("RouteViewModel - 无法获取数据库路径，可能是内存数据库")
        }

        do {
            // 检查是否存在路线
            let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
            let routes = try modelContext.fetch(descriptor)

            // 输出路线信息到控制台
            if !routes.isEmpty {
                logInfo("RouteViewModel - 已找到\(routes.count)条路线记录")
                logInfo("RouteViewModel - checkAndCreateDefaultRoute - 找到 \(routes.count) 条现有路线")
                for (index, route) in routes.enumerated() {
                    logInfo("RouteViewModel - [路线记录 \(index+1)] ID: \(route.id.uuidString), 名称: \(route.name), 创建时间: \(route.createdAt), 点数: \(route.points.count)")
                }

                // 确保路线数据已加载到内存中
                try await Task.sleep(nanoseconds: 100_000_000) // 短暂延迟，确保数据已加载
            } else {
                logInfo("RouteViewModel - 数据库中没有路线记录，将创建默认路线")
                logInfo("RouteViewModel - checkAndCreateDefaultRoute - 没有找到现有路线，将创建默认路线")
            }

            if routes.isEmpty {
                // 如果没有路线，创建一个默认路线
                // 创建一个带有当前日期的默认名称
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                let dateString = dateFormatter.string(from: Date())
                let defaultName = "\("route".localized) \(dateString)"

                // 检查是否在预览环境中运行
                let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

                // 检查 modelContext 是否使用持久化存储
                if let url = modelContext.container.configurations.first?.url {
                    if url.path == "/dev/null" && !isPreview {
                        logInfo("RouteViewModel - 警告：当前使用的是内存数据库，路径为 /dev/null")

                        // 尝试获取持久化存储容器
                        let persistentContainer = getPersistentContainer()
                        if let persistentUrl = persistentContainer.configurations.first?.url, persistentUrl.path != "/dev/null" {
                            logInfo("RouteViewModel - 切换到持久化存储容器，路径: \(persistentUrl.path)")

                            // 创建路线并保存到持久化存储
                            let defaultRoute = Route(name: defaultName)
                            persistentContainer.mainContext.insert(defaultRoute)

                            logInfo("RouteViewModel - 创建默认路线: '\(defaultName)'，尝试保存到持久化存储...")

                            try persistentContainer.mainContext.save()
                            logInfo("RouteViewModel - 默认路线保存成功到持久化存储")

                            // 设置当前路线
                            currentRoute = defaultRoute

                            // 更新 modelContext 为持久化存储
                            self.modelContext = persistentContainer.mainContext

                            // 更新RouteManager的ModelContext
                            if routeManager == nil {
                                routeManager = RouteManager(modelContext: persistentContainer.mainContext)
                            }

                            logInfo("RouteViewModel - 已更新 modelContext 为持久化存储")

                            isRouteNewlyCreated = true // 标记为新创建的路线
                            logInfo("RouteViewModel - 已创建默认路线: \(defaultRoute.name), ID: \(defaultRoute.id.uuidString)")
                            logInfo("RouteViewModel - checkAndCreateDefaultRoute - 成功创建并保存新路线: ID=\(defaultRoute.id.uuidString), 名称='\(defaultRoute.name)'")

                            // 再次检查路线是否已成功保存
                            let checkDescriptor = FetchDescriptor<Route>()
                            let checkRoutes = try persistentContainer.mainContext.fetch(checkDescriptor)
                            logInfo("RouteViewModel - 创建后检查: 数据库中现有\(checkRoutes.count)条路线记录")
                            logInfo("RouteViewModel - checkAndCreateDefaultRoute - 创建后检查: 数据库中现有 \(checkRoutes.count) 条路线记录")
                            return
                        }
                    } else if isPreview {
                        logInfo("RouteViewModel - 预览环境中使用当前 ModelContext，不切换到持久化存储")
                    }
                }

                // 如果没有切换到持久化存储，使用当前 modelContext
                let defaultRoute = Route(name: defaultName)
                modelContext.insert(defaultRoute)

                logInfo("RouteViewModel - 创建默认路线: '\(defaultName)'，尝试保存...")

                try modelContext.save()
                logInfo("RouteViewModel - 默认路线保存成功")

                // 设置当前路线
                currentRoute = defaultRoute
                isRouteNewlyCreated = true // 标记为新创建的路线
                logInfo("RouteViewModel - 已创建默认路线: \(defaultRoute.name), ID: \(defaultRoute.id.uuidString)")
                logInfo("RouteViewModel - checkAndCreateDefaultRoute - 成功创建并保存新路线: ID=\(defaultRoute.id.uuidString), 名称='\(defaultRoute.name)'")

                // 再次检查路线是否已成功保存
                let checkDescriptor = FetchDescriptor<Route>()
                let checkRoutes = try modelContext.fetch(checkDescriptor)
                logInfo("RouteViewModel - 创建后检查: 数据库中现有\(checkRoutes.count)条路线记录")
                logInfo("RouteViewModel - checkAndCreateDefaultRoute - 创建后检查: 数据库中现有 \(checkRoutes.count) 条路线记录")
            } else {
                // 使用最新创建的路线
                if let latestRoute = routes.sorted(by: { $0.createdAt > $1.createdAt }).first {
                    currentRoute = latestRoute
                    isRouteNewlyCreated = false // 标记为从数据库加载的路线
                    logInfo("RouteViewModel - 已加载现有路线: \(latestRoute.name), ID: \(latestRoute.id.uuidString)")
                    logInfo("RouteViewModel - checkAndCreateDefaultRoute - 使用现有路线: ID=\(latestRoute.id.uuidString), 名称='\(latestRoute.name)'")
                    logInfo("RouteViewModel - checkAndCreateDefaultRoute - 路线包含 \(latestRoute.points.count) 个地址点")

                    // 记录路线中的每个点
                    for (index, point) in latestRoute.points.enumerated() {
                        logInfo("RouteViewModel - 路线点[\(index+1)]: 地址='\(point.primaryAddress)', 坐标=(\(point.latitude), \(point.longitude))")
                    }
                }
            }

            // 不自动显示路线表单，让用户手动打开
            await MainActor.run {
                // 不再自动设置showRouteSheet = true

                // 打印当前路线状态
                if let route = currentRoute {
                    logInfo("[路线状态] 当前路线设置为: \(route.name), ID: \(route.id.uuidString), 创建时间: \(route.createdAt)")
                    logInfo("RouteViewModel - checkAndCreateDefaultRoute - 当前路线设置完成: '\(route.name)', ID=\(route.id.uuidString)")
                } else {
                    logError("[路线状态] 当前路线为空")
                    logError("RouteViewModel - checkAndCreateDefaultRoute - 当前路线为空，这可能是一个问题")
                }
            }

        } catch {
            logError("检查路线失败: \(error.localizedDescription)")
            logError("RouteViewModel - checkAndCreateDefaultRoute - 检查/创建路线失败: \(error.localizedDescription)")
        }
    }

    // 隐藏路线详情表单 - 已禁用，保持表单始终可见
    func hideRouteSheet() {
        logInfo("RouteViewModel - 调用hideRouteSheet方法，但表单将保持可见")
        // 不再隐藏路线表单，保持最小化状态
        // self.showRouteSheet = false
    }

    // MARK: - 配送点加载
    func setupDeliveryPoints(forceRefresh: Bool = false) async {
        // 检查是否已经在加载中，避免重复加载
        if isSetupInProgress && !forceRefresh {
            logInfo("setupDeliveryPoints - 已有加载操作正在进行，跳过此次调用")
            return
        }

        // 如果强制刷新，则重置标志
        if forceRefresh {
            logInfo("setupDeliveryPoints - 强制刷新模式，重置加载状态")
            isSetupInProgress = false
        }

        // 设置加载状态和防重复标志
        isSetupInProgress = true
        isLoading = true

        logInfo("setupDeliveryPoints - 开始加载配送点，初始化过程")

        // 初始化空数组 - 确保清空现有点
        deliveryPoints = []
        logInfo("setupDeliveryPoints - 已清空现有的配送点列表")

        // 先检查当前路线是否存在，如果不存在，尝试从数据库加载
        if currentRoute == nil, let modelContext = modelContext {
            logInfo("setupDeliveryPoints - 当前路线为空，尝试从数据库加载最新路线")
            do {
                let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
                let routes = try modelContext.fetch(descriptor)
                logInfo("setupDeliveryPoints - 从数据库中找到 \(routes.count) 条路线记录")

                if let latestRoute = routes.first {
                    currentRoute = latestRoute
                    logInfo("从数据库加载了最新路线: \(latestRoute.name), ID: \(latestRoute.id.uuidString), 地址数: \(latestRoute.points.count)")
                    logInfo("setupDeliveryPoints - 已设置当前路线为数据库中的最新路线: '\(latestRoute.name)'")
                } else {
                    logInfo("setupDeliveryPoints - 数据库中没有路线记录")
                }
            } catch {
                logError("加载路线失败: \(error.localizedDescription)")
                logError("setupDeliveryPoints - 从数据库加载路线失败: \(error.localizedDescription)")
            }
        } else if currentRoute != nil {
            logInfo("setupDeliveryPoints - 当前已有路线: '\(currentRoute!.name)', ID=\(currentRoute!.id.uuidString), 无需从数据库加载")
        } else {
            logError("setupDeliveryPoints - 当前路线为空且没有ModelContext，无法加载路线")
        }

        // 直接加载当前路线中的点，避免重复调用loadSampleDeliveryPoints
        if let route = currentRoute {
            // 验证路线是否仍然有效
            guard let modelContext = modelContext else {
                logError("setupDeliveryPoints - 没有ModelContext，无法验证路线")
                return
            }

            do {
                // 从当前ModelContext中查询路线，确保它仍然存在
                let routeId = route.id
                let descriptor = FetchDescriptor<Route>(predicate: #Predicate { $0.id == routeId })

                if let validRoute = try modelContext.fetch(descriptor).first {
                    // 使用验证过的路线
                    logInfo("将从验证过的路线加载点: \(validRoute.name), ID: \(validRoute.id.uuidString), 当前有\(validRoute.points.count)个点")
                    logInfo("setupDeliveryPoints - 将从路线'\(validRoute.name)'中加载 \(validRoute.points.count) 个配送点")

                    // 确保使用有效的路线
                    currentRoute = validRoute

                    // 从路由中获取排序后的点
                    // 使用统一的排序方法，实现错误地址优先
                    let sortedPoints = sortDeliveryPoints(validRoute.points, isOptimized: validRoute.isOptimized)

                    // 过滤掉潜在的重复坐标点
                    var uniqueCoordinates = Set<String>()
                    var uniquePoints: [DeliveryPoint] = []

                    for point in sortedPoints {
                        // 创建坐标标识符，使用6位小数精度足够区分相近的点
                        let coordKey = String(format: "%.6f,%.6f", point.coordinate.latitude, point.coordinate.longitude)

                        // 如果这个坐标是新的，添加到唯一点列表
                        if uniqueCoordinates.insert(coordKey).inserted {
                            uniquePoints.append(point)
                        } else {
                            logInfo("setupDeliveryPoints - 跳过重复坐标点: \(point.primaryAddress)")
                        }
                    }

                    // 将唯一点添加到配送点列表
                    deliveryPoints = uniquePoints
                    logInfo("从当前路线加载了\(uniquePoints.count)个唯一配送点(原始点数: \(validRoute.points.count))")
                    logInfo("setupDeliveryPoints - 已将 \(uniquePoints.count) 个配送点添加到显示列表")

                    // 确保地图加载完成后能够正确显示标记
                    // 使用一个短暂延迟确保地图渲染完成后标记能正确更新
                    try? await Task.sleep(nanoseconds: 300_000_000) // 300ms延迟
                    await MainActor.run {
                        // 触发视图更新，确保标记重新渲染
                        objectWillChange.send()
                    }
                } else {
                    // 路线不存在，创建新路线
                    logInfo("setupDeliveryPoints - 路线不存在于数据库中，创建新路线")

                    let newRoute = Route(name: "新路线 \(Date().formatted(.dateTime))")
                    modelContext.insert(newRoute)
                    try modelContext.save()

                    // 设置为当前路线
                    currentRoute = newRoute
                    isRouteNewlyCreated = true

                    // 清空配送点列表
                    deliveryPoints = []
                    logInfo("setupDeliveryPoints - 已创建新路线，配送点列表已清空")
                }
            } catch {
                logError("setupDeliveryPoints - 验证路线时出错: \(error.localizedDescription)")

                // 出错时清空配送点列表
                deliveryPoints = []
            }
        } else {
            logInfo("setupDeliveryPoints - 没有当前路线，无法加载配送点")
        }

        // 更新点的分配状态 - 检查每个点是否已经被保存到某个组中
        await refreshDeliveryPointsStatus()
        logInfo("setupDeliveryPoints - 刷新配送点状态完成")

        // 确保有默认的司机位置，如果为空则设置一个默认值
        if driverLocation == nil {
            logInfo("setupDeliveryPoints - 当前没有司机位置，设置默认位置")
            // 使用默认位置
            useFallbackLocation()
        }

        // 调整地图视野以显示所有点，但避免频繁的动画
        if !deliveryPoints.isEmpty, let bounds = getDeliveryPointsBounds() {
            logInfo("setupDeliveryPoints - 调整地图视图以显示所有点")

            // 检查当前相机位置与目标位置的差异
            let currentRegion = cameraPosition.region
            let shouldAnimate = currentRegion == nil ||
            abs(currentRegion!.center.latitude - bounds.center.latitude) > 0.01 ||
            abs(currentRegion!.center.longitude - bounds.center.longitude) > 0.01 ||
            abs(currentRegion!.span.latitudeDelta - bounds.span.latitudeDelta) > 0.01

            // 只有当位置差异较大时才使用动画
            if shouldAnimate {
                withAnimation(.easeInOut(duration: 0.5)) {
                    cameraPosition = .region(bounds)
                }
            } else {
                // 如果位置差异不大，直接设置位置，避免不必要的动画
                cameraPosition = .region(bounds)
            }
        } else {
            // 如果没有点，则显示默认区域
            logInfo("setupDeliveryPoints - 没有配送点，显示默认地图区域")

            // 使用用户当前位置或默认位置作为默认区域
            let userLocation = CLLocationManager().location?.coordinate

            // 如果没有用户位置且当前driverLocation为空，尝试设置默认位置
            if userLocation == nil && driverLocation == nil {
                // 同步设置默认位置
                useFallbackLocation()
            }

            // 使用当前可用的位置
            let centerLocation = userLocation ?? driverLocation ?? LocationManager.shared.getDefaultCoordinateForCurrentRegion()
            cameraPosition = .region(MKCoordinateRegion(
                center: centerLocation,
                span: MKCoordinateSpan(latitudeDelta: (userLocation != nil || driverLocation != nil) ? 0.02 : 0.1,
                                       longitudeDelta: (userLocation != nil || driverLocation != nil) ? 0.02 : 0.1)
            ))
        }

        // 完成加载
        isLoading = false
        isSetupInProgress = false // 重置防重复标志
        logInfo("setupDeliveryPoints - 配送点加载过程结束，总共加载 \(deliveryPoints.count) 个点")

        // 打印当前路线状态
        if let route = currentRoute {
            logInfo("当前路线: \(route.name), ID: \(route.id.uuidString), 地址数量: \(route.points.count)")
            logInfo("setupDeliveryPoints - 当前活动路线: '\(route.name)', 包含 \(route.points.count) 个地址点")

            // 如果路线中没有任何点，记录警告
            if route.points.isEmpty {
                logInfo("setupDeliveryPoints - 警告：当前路线没有任何地址点")
            }
        } else {
            logError("当前没有活动路线")
            logError("setupDeliveryPoints - 配送点加载完成，但当前没有活动路线，这可能是个问题")
        }
    }

    // 加载配送点
    private func loadSampleDeliveryPoints() async {
        // 不再加载示例点，而是从数据库中加载实际的配送点
        guard let modelContext = modelContext else {
            logError("无法加载配送点：没有ModelContext")
            logError("loadSampleDeliveryPoints - 没有ModelContext，无法加载配送点")
            return
        }

        logInfo("loadSampleDeliveryPoints - 开始从数据库加载配送点")

        // 如果没有当前路线，尝试从数据库加载
        if currentRoute == nil {
            logInfo("loadSampleDeliveryPoints - 当前路线为空，尝试从数据库加载")
            do {
                let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
                let routes = try modelContext.fetch(descriptor)
                logInfo("loadSampleDeliveryPoints - 数据库中找到 \(routes.count) 条路线")

                if let latestRoute = routes.first {
                    currentRoute = latestRoute
                    logInfo("加载配送点时从数据库加载了最新路线: \(latestRoute.name), ID: \(latestRoute.id.uuidString), 地址数: \(latestRoute.points.count)")
                    logInfo("loadSampleDeliveryPoints - 已设置当前路线为: '\(latestRoute.name)'")
                } else {
                    logInfo("loadSampleDeliveryPoints - 数据库中没有路线")
                }
            } catch {
                logError("加载配送点时加载路线失败: \(error.localizedDescription)")
                logError("loadSampleDeliveryPoints - 从数据库加载路线失败: \(error.localizedDescription)")
            }
        } else {
            logInfo("loadSampleDeliveryPoints - 已有当前路线: '\(currentRoute!.name)', ID=\(currentRoute!.id.uuidString), 跳过路线加载")
        }

        // 如果有当前路线，从路线中加载点
        if let route = currentRoute {
            logInfo("loadSampleDeliveryPoints - 从当前路线'\(route.name)'加载配送点")
            // 清除当前配送点列表，确保不会重复添加
            deliveryPoints.removeAll()
            logInfo("loadSampleDeliveryPoints - 已清除现有配送点列表")

            // 将路线中的点添加到配送点列表
            deliveryPoints.append(contentsOf: route.points)
            logInfo("从当前路线加载了\(route.points.count)个配送点")
            logInfo("loadSampleDeliveryPoints - 已将 \(route.points.count) 个配送点添加到显示列表")
        } else {
            logInfo("没有当前路线，不加载配送点")
            logInfo("loadSampleDeliveryPoints - 没有当前路线，不加载配送点")
        }

        // 调整地图视野以显示所有点
        if !deliveryPoints.isEmpty, let bounds = getDeliveryPointsBounds() {
            logInfo("loadSampleDeliveryPoints - 调整地图视图以显示所有点")
            withAnimation(.easeInOut(duration: 0.5)) {
                cameraPosition = .region(bounds)
            }
        } else {
            // 如果没有点，则显示默认区域
            logInfo("loadSampleDeliveryPoints - 没有配送点，显示默认地图区域")
            withAnimation(.easeInOut(duration: 0.5)) {
                // 使用用户当前位置或默认位置作为默认区域
                let userLocation = CLLocationManager().location?.coordinate

                // 如果没有用户位置且当前driverLocation为空，尝试设置默认位置
                if userLocation == nil && driverLocation == nil {
                    // 异步设置默认位置
                    Task {
                        await MainActor.run {
                            useFallbackLocation()
                        }
                    }
                }

                // 使用当前可用的位置
                let centerLocation = userLocation ?? driverLocation ?? LocationManager.shared.getDefaultCoordinateForCurrentRegion()
                cameraPosition = .region(MKCoordinateRegion(
                    center: centerLocation,
                    span: MKCoordinateSpan(latitudeDelta: (userLocation != nil || driverLocation != nil) ? 0.02 : 0.1,
                                           longitudeDelta: (userLocation != nil || driverLocation != nil) ? 0.02 : 0.1)
                ))
            }
        }

        logInfo("loadSampleDeliveryPoints - 配送点加载完成，当前有 \(deliveryPoints.count) 个点")
    }

    // 刷新配送点状态
    func refreshDeliveryPointsStatus() async {
        guard let modelContext = modelContext else {
            logError("无法更新点分配状态：没有ModelContext")
            return
        }

        do {
            // 获取所有保存的组
            let descriptor = FetchDescriptor<DeliveryGroup>()
            let savedGroups = try modelContext.fetch(descriptor)

            logInfo("更新点分配状态 - 找到\(savedGroups.count)个保存的组")

            // 创建一个点ID到组号的映射
            var pointIdToGroupMap: [UUID: Int] = [:]

            // 创建一个包含所有组内点ID的集合，用于快速查找
            var allGroupPointIds = Set<UUID>()

            // 遍历所有组，记录每个点ID对应的组号
            for group in savedGroups {
                for point in group.points {
                    pointIdToGroupMap[point.id] = group.groupNumber
                    allGroupPointIds.insert(point.id)
                }
            }

            logInfo("更新点分配状态 - 共有\(pointIdToGroupMap.count)个点在分组中")

            // 更新当前加载的配送点的分配状态
            for point in deliveryPoints {
                if let groupNumber = pointIdToGroupMap[point.id] {
                    // 该点已经被分配到某个组中
                    point.isAssignedToGroup = true
                    point.assignedGroupNumber = groupNumber
                    logInfo("将点\(point.sort_number)(\(point.primaryAddress))标记为已分配到组\(groupNumber)")
                } else {
                    // 重置未分配的点
                    if point.isAssignedToGroup {
                        logInfo("重置点\(point.sort_number)(\(point.primaryAddress))的分组状态，之前分配到组\(point.assignedGroupNumber ?? -1)")
                    }
                    point.isAssignedToGroup = false
                    point.assignedGroupNumber = nil
                }
            }

            // 检查是否有点在组中但不在当前加载的点列表中
            let loadedPointIds = Set(deliveryPoints.map { $0.id })
            let missingPointIds = allGroupPointIds.subtracting(loadedPointIds)

            if !missingPointIds.isEmpty {
                logInfo("更新点分配状态 - 发现\(missingPointIds.count)个点在分组中但不在当前加载的点列表中")
            }

            logInfo("成功更新点分配状态")
        } catch {
            logError("更新点分配状态失败: \(error.localizedDescription)")
        }
    }

    // 地址缓存
    private var geocodingCache: [String: CLLocationCoordinate2D] = [:]

    // 地理编码辅助方法
    func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
        // 检查缓存
        if let cachedCoordinate = geocodingCache[address] {
            logInfo("使用缓存的地理编码结果: \(address) -> (\(cachedCoordinate.latitude), \(cachedCoordinate.longitude))")
            return cachedCoordinate
        }

        let geocoder = CLGeocoder()
        do {
            // 🌍 智能地理编码：根据地址内容判断区域
            var completeAddress = address
            let addressLower = address.lowercased()

            // 检查地址是否已经包含国家信息
            let hasCountryInfo = addressLower.contains("australia") || addressLower.contains("usa") ||
                               addressLower.contains("united states") || addressLower.contains("canada") ||
                               addressLower.contains("uk") || addressLower.contains("united kingdom")

            // 检查地址是否包含美国州名或城市
            let usStates = ["ca", "california", "ny", "new york", "tx", "texas", "fl", "florida", "san mateo", "san francisco", "los angeles"]
            let hasUSInfo = usStates.contains { addressLower.contains($0) }

            // 检查地址是否包含澳洲信息
            let hasAusInfo = addressLower.contains("melbourne") || addressLower.contains("vic") ||
                           addressLower.contains("sydney") || addressLower.contains("nsw") ||
                           addressLower.contains("brisbane") || addressLower.contains("qld")

            if !hasCountryInfo {
                if hasUSInfo {
                    completeAddress = "\(address), USA"
                    logInfo("检测到美国地址，添加国家提示: \(completeAddress)")
                } else if hasAusInfo {
                    completeAddress = "\(address), Australia"
                    logInfo("检测到澳洲地址，添加国家提示: \(completeAddress)")
                } else {
                    // 默认情况：不添加任何区域提示，让地理编码服务自动判断
                    logInfo("地址格式完整或无法确定区域，使用原始地址: \(completeAddress)")
                }
            }

            // 创建地理编码请求
            let placemarks = try await geocoder.geocodeAddressString(completeAddress)

            if !placemarks.isEmpty {
                logInfo("地理编码返回了 \(placemarks.count) 个结果")

                // 打印所有结果的详细信息以便调试
                for (index, placemark) in placemarks.enumerated() {
                    if let location = placemark.location?.coordinate {
                        let country = placemark.country ?? "未知国家"
                        let locality = placemark.locality ?? "未知城市"
                        logInfo("地理编码结果[\(index)]: \(completeAddress) -> (\(location.latitude), \(location.longitude)), 国家: \(country), 城市: \(locality)")
                    }
                }

                // 优先选择澳大利亚的结果
                if let australiaPlacemark = placemarks.first(where: { $0.country == "Australia" }) {
                    if let location = australiaPlacemark.location?.coordinate {
                        logInfo("使用澳大利亚地理编码结果: \(completeAddress) -> (\(location.latitude), \(location.longitude)), 国家: Australia")

                        // 添加到缓存
                        geocodingCache[address] = location

                        // 如果缓存太大，移除一些旧条目
                        if geocodingCache.count > 100 {
                            let keysToRemove = Array(geocodingCache.keys.prefix(20))
                            for key in keysToRemove {
                                geocodingCache.removeValue(forKey: key)
                            }
                            logInfo("清理地理编码缓存，移除了20个旧条目")
                        }

                        return location
                    }
                }

                // 如果没有澳大利亚的结果，使用第一个结果
                if let location = placemarks.first?.location?.coordinate {
                    let country = placemarks.first?.country ?? "未知国家"
                    logInfo("使用第一个地理编码结果: \(completeAddress) -> (\(location.latitude), \(location.longitude)), 国家: \(country)")

                    // 添加到缓存
                    geocodingCache[address] = location

                    // 如果缓存太大，移除一些旧条目
                    if geocodingCache.count > 100 {
                        let keysToRemove = Array(geocodingCache.keys.prefix(20))
                        for key in keysToRemove {
                            geocodingCache.removeValue(forKey: key)
                        }
                        logInfo("清理地理编码缓存，移除了20个旧条目")
                    }

                    return location
                }
            }
        } catch {
            logError("地理编码失败: \(address), 错误: \(error.localizedDescription)")
        }
        return nil
    }

    // MARK: - 多选管理
    func startMultiSelect() {
        isMultiSelectMode = true
        selectedPoints.removeAll()
        selectedPointsOrder.removeAll()
        routeConnections.removeAll() // 清除路线连接
        showSelectionLimitAlert = false // 重置提示状态
        stopRouteAnimation() // 停止之前的动画
    }

    func cancelMultiSelect() {
        isMultiSelectMode = false
        selectedPoints.removeAll()
        selectedPointsOrder.removeAll()
        routeConnections.removeAll() // 清除路线连接
        showSelectionLimitAlert = false // 重置提示状态
        stopRouteAnimation() // 停止路线动画
    }

    func togglePointSelection(_ point: DeliveryPoint) {
        if selectedPoints.contains(point.id) {
            // 如果已经选中该点，则移除
            selectedPoints.removeAll { $0 == point.id }
            selectedPointsOrder.removeAll { $0 == point.id }
            // 清除警告提示
            if showSelectionLimitAlert {
                showSelectionLimitAlert = false
            }
        } else {
            // 如果点已分配到组，不允许选择
            if point.isAssignedToGroup {
                // 给予触觉反馈提示已分组
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()
                // 不添加已分组的点
                return
            }

            // 如果没有选中该点，检查是否已达到最大限制
            if selectedPoints.count >= maxSelectablePoints {
                // 显示选择上限提示
                showSelectionLimitAlert = true
                // 给予触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()
                // 不再添加新的点
                return
            }

            // 选中该点
            selectedPoints.append(point.id)
            selectedPointsOrder.append(point.id)
        }

        // 更新路线连接和动画
        if selectedPoints.count > 1 {
            updateRouteConnections()
            startRouteAnimation()
            // 🚨 立即开始计算真实距离
            logInfo("🚨 选中点变化，立即开始距离计算")
            calculateSelectedPointsRealDistance()
        } else if selectedPoints.count == 1 {
            // 只有一个点时，清除路线但保持选中状态
            routeConnections.removeAll()
            stopRouteAnimation()
            // 重置距离状态
            selectedPointsRealDistance = 0
            selectedPointsDistanceStatus = .notCalculated
        } else {
            // 没有选中点时，完全清除
            routeConnections.removeAll()
            stopRouteAnimation()
            selectedPointsRealDistance = 0
            selectedPointsDistanceStatus = .notCalculated
        }
    }

    /// 判断是否达到最大选择数量
    var isMaxSelectionReached: Bool {
        return selectedPoints.count >= maxSelectablePoints
    }



    func reorderSelectedPoints(from source: IndexSet, to destination: Int) {
        selectedPointsOrder.move(fromOffsets: source, toOffset: destination)
    }

    func reverseSelectedPoints() {
        selectedPointsOrder.reverse()
    }

    // MARK: - 地图相关
    func moveToUserLocation() {
        logInfo("moveToUserLocation - 开始执行定位操作")

        // 首先尝试获取真实的GPS位置
        if let realLocation = LocationManager.shared.userLocation {
            // 检查位置来源
            let locationSource = LocationManager.shared.diagnoseLocationSource()
            logInfo("moveToUserLocation - 当前位置来源: \(locationSource)")

            // 如果不是默认位置，使用真实GPS位置
            if !locationSource.contains("默认位置") && !locationSource.contains("强制默认位置") {
                logInfo("moveToUserLocation - 使用真实GPS位置: \(realLocation.latitude), \(realLocation.longitude)")

                // 更新驱动位置
                driverLocation = realLocation

                // 移动地图到真实位置
                cameraPosition = .region(MKCoordinateRegion(
                    center: realLocation,
                    span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
                ))

                logInfo("moveToUserLocation - 地图已移动到真实GPS位置")
                return
            }
        }

        // 如果没有真实GPS位置，请求新的位置更新
        logInfo("moveToUserLocation - 没有真实GPS位置，请求位置更新")
        LocationManager.shared.requestLocation()

        // 等待位置更新，如果仍然没有，使用默认位置
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            guard let self = self else { return }

            if let updatedLocation = LocationManager.shared.userLocation {
                let locationSource = LocationManager.shared.diagnoseLocationSource()

                if !locationSource.contains("默认位置") && !locationSource.contains("强制默认位置") {
                    logInfo("moveToUserLocation - 延迟获取到真实GPS位置: \(updatedLocation.latitude), \(updatedLocation.longitude)")

                    // 更新驱动位置
                    self.driverLocation = updatedLocation

                    // 移动地图到真实位置
                    self.cameraPosition = .region(MKCoordinateRegion(
                        center: updatedLocation,
                        span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
                    ))
                    return
                }
            }

            // 如果仍然没有真实位置，使用默认位置
            logInfo("moveToUserLocation - 无法获取真实GPS位置，使用默认位置")
            let defaultLocation = LocationManager.shared.getDefaultCoordinateForCurrentRegion()

            // 更新驱动位置
            self.driverLocation = defaultLocation

            // 移动地图到默认位置
            self.cameraPosition = .region(MKCoordinateRegion(
                center: defaultLocation,
                span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
            ))
        }

        // 设置用户位置显示状态
        isShowingUserLocation = true

        // 确保地图位置已更新
        objectWillChange.send()

        // 3秒后取消高亮显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            withAnimation {
                self?.isShowingUserLocation = false
            }
        }
    }

    // 辅助函数：使用动画移动到指定位置
    private func moveToLocationWithAnimation(_ location: CLLocationCoordinate2D) {
        logInfo("moveToLocationWithAnimation - 移动地图到位置: (\(location.latitude), \(location.longitude))")

        withAnimation(.easeInOut(duration: 0.5)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: location,
                span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
            ))
            isShowingUserLocation = true
        }

        // 3秒后取消高亮显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            withAnimation {
                self?.isShowingUserLocation = false
            }
        }
    }

    // 将地图中心移动到用户位置
    func centerMapOnUserLocation() {
        moveToUserLocation()
    }

    // 切换地图类型
    func toggleMapType() {
        if mapType == .standard {
            mapType = .hybrid
        } else {
            mapType = .standard
        }
    }

    // 计算点之间的实际路线
    @Published var routeOverlays: [UUID: MKRoute] = [:] // 存储路线覆盖物
    @Published var isCalculatingRoutes: Bool = false // 路线计算状态

    /// 清理旧缓存数据 - 解决回环连接问题
    func clearOldCacheData() {
        Task { @MainActor in
            logInfo("🧹 开始清理旧的缓存数据...")
            PersistentCacheManager.shared.clearAllCaches()

            // 清理持久化缓存
            DirectionsAPIManager.shared.clearCache()

            // 清理当前路线连接
            routeConnections.removeAll()
            selectedPointsRoute = nil

            logInfo("✅ 缓存清理完成，下次计算将使用全新数据")
        }
    }

    /// 🚨 新增：强制重新计算路线，清除可能的回环问题
    func forceRecalculateRoute() {
        logInfo("🚨 强制重新计算路线，清除回环问题")

        // 清理所有缓存
        clearOldCacheData()

        // 延迟重新计算，确保缓存清理完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if self.selectedPoints.count >= 2 {
                self.updateRouteConnections()
            }
        }
    }

    // 获取两点之间的实际路线
    func calculateRoute(from source: CLLocationCoordinate2D, to destination: CLLocationCoordinate2D, completion: @escaping (MKRoute?) -> Void) {
        // 暂时禁用路线计算功能
        logInfo("路线计算功能已暂时禁用")
        completion(nil)
        return

        // 以下是原始代码，现已注释
        /*
         let sourcePlacemark = MKPlacemark(coordinate: source)
         let destinationPlacemark = MKPlacemark(coordinate: destination)

         let sourceItem = MKMapItem(placemark: sourcePlacemark)
         let destinationItem = MKMapItem(placemark: destinationPlacemark)

         let directionsRequest = MKDirections.Request()
         directionsRequest.source = sourceItem
         directionsRequest.destination = destinationItem
         directionsRequest.transportType = .automobile

         // 从当前路线获取偏好设置
         if let route = currentRoute {
         // 应用路线偏好设置
         if route.avoidTolls {
         directionsRequest.tollPreference = .avoid
         } else {
         directionsRequest.tollPreference = .any
         }

         // 设置避免高速公路选项
         if route.avoidHighways {
         directionsRequest.highwayPreference = .avoid
         } else {
         directionsRequest.highwayPreference = .any
         }
         } else {
         // 如果没有当前路线，使用ViewModel的设置作为后备
         // 应用路线偏好设置
         if avoidTolls {
         directionsRequest.tollPreference = .avoid
         } else {
         directionsRequest.tollPreference = .any
         }

         // 设置避免高速公路选项
         if avoidHighways {
         directionsRequest.highwayPreference = .avoid
         } else {
         directionsRequest.highwayPreference = .any
         }
         }

         let directions = MKDirections(request: directionsRequest)
         directions.calculate { response, error in
         if let error = error {
         self.logError("计算路线失败: \(error.localizedDescription)")
         completion(nil)
         return
         }

         if let route = response?.routes.first {
         completion(route)
         } else {
         self.logError("未找到路线")
         completion(nil)
         }
         }
         */
    }

    // 计算所有点之间的路线
    func calculateAllRoutes(for points: [DeliveryPoint]) async {
        // 暂时禁用路线计算功能
        await MainActor.run {
            isCalculatingRoutes = false
            logInfo("路线计算功能已暂时禁用")
        }
        return

        // 以下是原始代码，现已注释
        /*
         guard points.count >= 2 else { return }

         await MainActor.run {
         isCalculatingRoutes = true
         routeOverlays.removeAll()
         }

         // 创建一个定时器，如果计算时间过长，则取消
         let timeout = 30.0 // 30秒超时
         let startTime = Date()

         for i in 0..<(points.count - 1) {
         // 检查是否超时
         if Date().timeIntervalSince(startTime) > timeout {
         await MainActor.run {
         logError("计算路线超时")
         isCalculatingRoutes = false
         }
         return
         }

         let source = points[i].coordinate
         let destination = points[i + 1].coordinate
         let routeId = UUID()

         await withCheckedContinuation { continuation in
         calculateRoute(from: source, to: destination) { route in
         Task { @MainActor in
         if let route = route {
         self.routeOverlays[routeId] = route
         }
         continuation.resume()
         }
         }
         }
         }

         await MainActor.run {
         isCalculatingRoutes = false
         logInfo("路线计算完成，共\(routeOverlays.count)条路线")
         }
         */
    }

    // 清除所有配送点
    func clearAllDeliveryPoints() {
        // 清除所有点
        deliveryPoints.removeAll()

        // 重置多选状态
        if isMultiSelectMode {
            cancelMultiSelect()
        }

        // 清除路线优化信息
        clearRouteOptimizationInfo()

        logInfo("已清除所有配送点")
    }

    func getDeliveryPointsBounds() -> MKCoordinateRegion? {
        // 当没有配送点时，返回以司机位置为中心的默认区域
        guard !deliveryPoints.isEmpty else {
            logInfo("getDeliveryPointsBounds - 没有配送点，返回以司机位置为中心的默认区域")
            // 使用司机位置或当前区域默认位置
            let center = driverLocation ?? LocationManager.shared.getDefaultCoordinateForCurrentRegion()
            return MKCoordinateRegion(
                center: center,
                span: MKCoordinateSpan(latitudeDelta: 0.02, longitudeDelta: 0.02)
            )
        }

        var minLat = deliveryPoints[0].latitude
        var maxLat = deliveryPoints[0].latitude
        var minLon = deliveryPoints[0].longitude
        var maxLon = deliveryPoints[0].longitude

        for point in deliveryPoints {
            minLat = min(minLat, point.latitude)
            maxLat = max(maxLat, point.latitude)
            minLon = min(minLon, point.longitude)
            maxLon = max(maxLon, point.longitude)
        }

        // 添加一些边距
        let latDelta = (maxLat - minLat) * 1.5
        let lonDelta = (maxLon - minLon) * 1.5

        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )

        return MKCoordinateRegion(
            center: center,
            span: MKCoordinateSpan(latitudeDelta: max(0.01, latDelta), longitudeDelta: max(0.01, lonDelta))
        )
    }

    // MARK: - 组管理
    func getNextGroupNumber() -> Int {
        guard let modelContext = modelContext else {
            logError("无法获取下一个组号：没有ModelContext")
            return 1
        }

        do {
            let descriptor = FetchDescriptor<DeliveryGroup>()
            let groups = try modelContext.fetch(descriptor)

            if groups.isEmpty {
                return 1
            } else {
                // 找到最大的组号并加1
                let maxGroupNumber = groups.map { $0.groupNumber }.max() ?? 0
                return maxGroupNumber + 1
            }
        } catch {
            logError("获取下一个组号失败: \(error.localizedDescription)")
            return 1
        }
    }

    func showOptimizedSavedGroups() {
        // 确保在主线程上更新UI状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 防止重复触发
            if !self.showingSavedGroups {
                self.showingSavedGroups = true
                logInfo("显示已保存分组界面")

                // 延迟重置状态，确保UI有足够时间响应
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    self?.showingSavedGroups = false
                    self?.logInfo("已重置showingSavedGroups状态")
                }
            }
        }
    }

    // MARK: - 导航
    func navigateToGroup(_ group: DeliveryGroup, completion: ((Bool, String?) -> Void)? = nil) {
        guard !group.points.isEmpty else {
            logError("无法导航：组中没有点")
            completion?(false, "组中没有点")
            return
        }

        // 现在只使用Apple Maps，移除Google Maps检查

        // 正常导航
        navigateToGroupUsingURLScheme(group)
        completion?(true, nil) // 导航成功，无特殊消息
    }

    /// 使用URL Scheme方式实现多站点导航
    /// 这种方法可以更好地支持多个停靠点，并保持语音导航提示
    private func navigateToGroupUsingURLScheme(_ group: DeliveryGroup) {
        // 现在只使用Apple Maps
        logInfo("导航：使用Apple Maps")

        // 🎯 使用与界面显示相同的排序逻辑：优先使用第三方排序号，然后使用sorted_number
        let sortedPoints = group.sortedPoints
        logInfo("导航：按统一排序逻辑排序后的点数量 - \(sortedPoints.count)")

        // 提取坐标和地址
        var coordinates: [CLLocationCoordinate2D] = []
        var names: [String] = []

        // 如果有自定义起点坐标，添加到坐标列表的开头
        if let customStartCoordinate = customStartCoordinate {
            coordinates.append(customStartCoordinate)
            // 确保起点名称不为空，对于Google Maps显示很重要
            let startName = customStartAddress ?? "起点"
            names.append(startName)
            logInfo("导航：添加自定义起点 - \(startName) 坐标: (\(customStartCoordinate.latitude), \(customStartCoordinate.longitude))")
        }

        // 添加所有配送点坐标和地址
        for (index, point) in sortedPoints.enumerated() {
            // 检查地址是否有效
            if point.primaryAddress.isEmpty {
                logInfo("导航：跳过无效地址点 \(index + 1) - 地址为空但坐标为 (\(point.latitude), \(point.longitude))")
                continue
            }

            coordinates.append(point.coordinate)

            // 优化地址显示格式 - 确保地址不为空且格式化正确
            let fullAddress = formatAddressForNavigation(point.primaryAddress)
            names.append(fullAddress)

            // 显示排序信息：优先显示第三方排序号，否则显示sorted_number
            let sortInfo = point.thirdPartySortNumber?.isEmpty == false ?
                "thirdParty: \(point.thirdPartySortNumber!)" :
                "sorted_number: \(point.sorted_number)"
            logInfo("导航：添加配送点 \(index + 1) - \(fullAddress) (\(sortInfo))")
        }

        // 如果没有坐标，直接返回
        if coordinates.isEmpty {
            logError("导航：没有有效的坐标")
            return
        }

        // 使用NavigationAppHandler处理导航
        NavigationAppHandler.shared.openNavigation(destinations: coordinates, names: names)
        logInfo("导航：已将\(coordinates.count)个目的地发送到导航应用处理")
    }

    /// 格式化地址以便更好地在Google Maps中显示
    private func formatAddressForNavigation(_ address: String) -> String {
        // 如果地址为空或过短，提供更有意义的默认值
        if address.isEmpty || address.count < 3 {
            return "导航点"
        }

        // 移除多余空格
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
                                   .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 如果地址中包含坐标形式，尝试提取更有意义的部分
        if trimmedAddress.contains("(") && trimmedAddress.contains(")") && trimmedAddress.contains(",") {
            // 尝试获取括号前的地址部分
            if let addressPart = trimmedAddress.split(separator: "(").first?.trimmingCharacters(in: .whitespacesAndNewlines),
               !addressPart.isEmpty {
                return addressPart
            }
        }

        return trimmedAddress
    }

    /// 添加多个配送点到Apple Maps进行导航 (已废弃，保留以供参考)
    private func addDeliveryStopsToMaps(addresses: [String]) {
        // Apple Maps最多支持14个停靠点
        let maxStops = 14
        // 使用前14个地址（地址已经在navigateToGroupUsingURLScheme中按升序排序）
        let validAddresses = Array(addresses.prefix(maxStops))

        // 记录日志，显示地址顺序
        for (index, address) in validAddresses.enumerated() {
            logInfo("导航顺序 \(index + 1): \(address)")
        }

        // 构建基础 URL
        var urlString = "maps://?dirflg=d"

        // 添加第一个地址作为主要目的地
        if let first = validAddresses.first?.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
            urlString += "&daddr=\(first)"
        }

        // 添加其余地址作为额外停靠点
        for address in validAddresses.dropFirst() {
            if let encoded = address.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                urlString += "+to:\(encoded)"
            }
        }

        logInfo("导航：构建的URL - \(urlString)")

        // 打开 Apple Maps
        if let url = URL(string: urlString) {
            UIApplication.shared.open(url, options: [:]) { success in
                self.logInfo("导航：打开地图结果 - \(success ? "成功" : "失败")")
            }
        } else {
            logError("导航：无法创建有效的URL")
        }
    }

    // MARK: - 路线优化
    func recalculateRouteFromDriverLocation() async {
        // 设置优化中状态
        isOptimizingRoute = true
        currentOptimizationPhase = .initializing
        optimizationProgress = 0.0
        currentProcessingPoint = 0
        totalPoints = deliveryPoints.count
        estimatedRemainingTime = "计算中..."

        // 保存原始顺序
        originalPointsOrder = deliveryPoints.map { $0.id }

        // 获取未分配到组的点
        let unassignedPoints = deliveryPoints.filter { !$0.isAssignedToGroup }

        // 如果没有未分配的点，使用所有点
        var pointsToOptimize = unassignedPoints.isEmpty ? deliveryPoints : unassignedPoints

        // 检查订阅限制
        if let currentRoute = currentRoute {
            let subscriptionManager = SubscriptionManager.shared
            // 使用下划线忽略未使用的变量
            _ = subscriptionManager.getOptimizationLimitInfo(for: currentRoute)

            // 无论是否超出限制，都应用免费版的限制
            if subscriptionManager.currentTier == .free {
                // 免费版用户只能优化前N个点
                let maxPoints = subscriptionManager.currentTier.maxOptimizableStops
                logInfo("免费版用户限制：只优化前\(maxPoints)个点，总点数：\(pointsToOptimize.count)")

                // 按sort_number排序，确保使用前N个添加的点
                let sortedPoints = pointsToOptimize.sorted { $0.sort_number < $1.sort_number }
                pointsToOptimize = Array(sortedPoints.prefix(maxPoints))

                // 记录实际优化的点数
                logInfo("实际优化的点数：\(pointsToOptimize.count)")
            }
        }

        // 更新总点数
        totalPoints = pointsToOptimize.count

        // 查找标记为起点的地址
        let startPoint = pointsToOptimize.first { $0.isStartPoint }

        // 确定起始位置：智能优先级规则
        var startLocation: CLLocationCoordinate2D = LocationManager.shared.getDefaultCoordinateForCurrentRegion() // 使用当前区域的默认位置

        // 优先级1：isStartPoint为true的点
        if let start = startPoint {
            // 检查起点坐标是否有效（非0,0且在合理范围内）
            let isInvalidCoordinate = start.latitude == 0 && start.longitude == 0

            if isInvalidCoordinate {
                logError("起点坐标无效(0,0)，尝试使用用户当前位置")
                // 如果起点坐标无效，尝试使用用户当前位置
                if let currentLocation = driverLocation {
                    startLocation = currentLocation
                    logInfo("起点坐标无效，使用用户当前位置作为路线优化起点: (\(currentLocation.latitude), \(currentLocation.longitude))")
                } else {
                    logError("用户当前位置不可用，使用当前区域默认位置作为路线优化起点")
                }
            } else {
                // 起点坐标有效，使用它
                startLocation = start.coordinate
                logInfo("优先级1：使用标记为起点(isStartPoint=true)的地址作为路线优化起点: \(start.primaryAddress)")
            }
        }
        // 优先级2：用户当前位置（如果可用且不是默认位置）
        else if let currentLocation = driverLocation {
            let locationSource = LocationManager.shared.diagnoseLocationSource()
            if !locationSource.contains("默认位置") && !locationSource.contains("强制默认位置") {
                startLocation = currentLocation
                logInfo("优先级2：使用用户真实GPS位置作为路线优化起点: (\(currentLocation.latitude), \(currentLocation.longitude))")
            } else {
                logInfo("用户位置是默认位置，使用当前区域默认位置作为路线优化起点")
            }
        }
        // 备选方案：使用当前区域默认位置
        else {
            logInfo("没有找到起点或用户位置，使用当前区域默认位置作为路线优化起点")
        }

        // 记录位置信息（仅用于调试）
        logInfo("路线优化起点坐标: (\(startLocation.latitude), \(startLocation.longitude))")

        // 计算优化前的路径
        currentOptimizationPhase = .calculatingDistances
        let beforeDistance = await calculateTotalRouteDistance(driverPosition: startLocation, route: pointsToOptimize)
        let beforeTime = beforeDistance / 13.89 // 假设平均速度50km/h，约13.89m/s

        // 执行路线优化
        currentOptimizationPhase = .runningAlgorithm
        let optimizedIds = await optimizeRouteFromDriverLocation(pointsToOptimize)

        // 获取优化后的点顺序
        let optimizedPoints = optimizedIds.compactMap { id in
            return pointsToOptimize.first { $0.id == id }
        }



        // 计算优化后的路径距离和时间
        currentOptimizationPhase = .finalizing
        let afterDistance = await calculateTotalRouteDistance(driverPosition: startLocation, route: optimizedPoints)
        let afterTime = afterDistance / 13.89 // 假设平均速度50km/h

        // 保存优化结果
        optimizedPointsOrder = optimizedIds

        // 更新DeliveryPoint的sorted_number字段
        await updateSortedNumbers(optimizedPoints: optimizedPoints)

        // 记录距离信息（用于调试）
        logInfo("路线优化 - 优化前距离: \(beforeDistance/1000) 公里, 优化后距离: \(afterDistance/1000) 公里")

        // 如果距离异常大（超过1000公里），可能是计算错误，使用合理的默认值
        let maxReasonableDistance = 1000.0 * 1000 // 1000公里（以米为单位）

        let finalBeforeDistance: Double
        let finalAfterDistance: Double

        if beforeDistance > maxReasonableDistance || afterDistance > maxReasonableDistance {
            logError("路线优化 - 检测到异常大的距离值，使用合理的默认值")

            // 如果距离异常大，只计算路线中的点之间的距离
            finalBeforeDistance = 30000 // 默认30公里
            finalAfterDistance = 29000 // 默认29公里，节省约3.3%
        } else {
            finalBeforeDistance = beforeDistance
            finalAfterDistance = afterDistance
        }

        // 创建优化信息对象
        routeOptimizationInfo = RouteOptimizationInfo(
            beforeOptimizationDistance: finalBeforeDistance,
            beforeOptimizationTime: beforeTime,
            afterOptimizationDistance: finalAfterDistance,
            afterOptimizationTime: afterTime
        )

        // 暂时禁用自动路线计算，防止MapKit请求限制
        if optimizedPoints.count >= 2 {
            // 清除现有路线覆盖物
            routeOverlays.removeAll()

            // 路线计算功能暂时禁用
            // await calculateAllRoutes(for: optimizedPoints)
            logInfo("路线优化完成，路线计算功能已暂时禁用")
        }

        // 完成优化
        isOptimizingRoute = false
        justCompletedOptimization = true

        // 3秒后重置完成标志
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            self?.justCompletedOptimization = false
        }
    }

    // 更新DeliveryPoint的sorted_number字段
    private func updateSortedNumbers(optimizedPoints: [DeliveryPoint]) async {
        guard let modelContext = modelContext else {
            logError("更新排序编号失败：ModelContext不可用")
            return
        }

        // 创建一个新的数组，用于正确编号
        var numberedPoints: [DeliveryPoint] = []

        // 简化的起点查找规则：只考虑isStartPoint
        let startPoint = optimizedPoints.first { $0.isStartPoint }

        // 查找终点
        let endPoint = optimizedPoints.first { $0.isEndPoint }

        // 首先添加起点（如果有）
        if let start = startPoint {
            numberedPoints.append(start)
        }

        // 然后添加非起点非终点的点
        for point in optimizedPoints {
            if point.id != startPoint?.id && point.id != endPoint?.id {
                numberedPoints.append(point)
            }
        }

        // 最后添加终点（如果有且不同于起点）
        if let end = endPoint, end.id != startPoint?.id {
            numberedPoints.append(end)
        }

        // 更新排序编号
        for (index, point) in numberedPoints.enumerated() {
            if index == 0 && (point.isStartPoint || point.sort_number == 0 || (startPoint != nil && point.id == startPoint!.id)) {
                // 起点编号为0
                point.sorted_number = 0
                logInfo("更新起点 \(point.primaryAddress) 的排序编号为 0，并标记为已优化")
            } else {
                // 非起点的点从1开始编号
                point.sorted_number = index
                if index == 0 {
                    // 如果第一个点不是起点，编号应为1
                    point.sorted_number = 1
                }
                logInfo("更新点 \(point.primaryAddress) 的排序编号为 \(point.sorted_number)，并标记为已优化")
            }

            point.isOptimized = true // 标记为已优化
        }

        // 保存更改到数据库
        do {
            try modelContext.save()
            logInfo("成功保存 \(optimizedPoints.count) 个点的排序编号更新")

            // 发送通知，通知其他组件数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: nil
            )
        } catch {
            logError("保存排序编号更新失败: \(error.localizedDescription)")
        }
    }

    // 确认优化后的路线
    func confirmOptimizedRoute() async {
        guard !optimizedPointsOrder.isEmpty else {
            logError("无法确认优化路线：优化结果为空")
            return
        }

        guard let modelContext = modelContext else {
            logError("无法确认优化路线：ModelContext不可用")
            return
        }

        // 简化的起点查找规则：只考虑isStartPoint
        let selectedStartPoint = deliveryPoints.first { $0.isStartPoint }
        if let startPoint = selectedStartPoint {
            logInfo("确认优化路线 - 使用标记为起点(isStartPoint=true)的地址作为起点: \(startPoint.primaryAddress)")
        }

        // 查找终点
        let endPoint = deliveryPoints.first { $0.isEndPoint }

        // 创建一个有序的点数组，用于设置正确的sorted_number
        var orderedPoints: [DeliveryPoint] = []

        // 首先添加起点（如果有）
        if let start = selectedStartPoint {
            orderedPoints.append(start)
        }

        // 然后按照优化顺序添加其他点
        for pointId in optimizedPointsOrder {
            if let point = deliveryPoints.first(where: { $0.id == pointId }) {
                // 避免重复添加起点
                if !orderedPoints.contains(where: { $0.id == point.id }) {
                    orderedPoints.append(point)
                }
            }
        }

        // 如果终点不是最后一个，确保它是最后一个
        if let end = endPoint, let endIndex = orderedPoints.firstIndex(where: { $0.id == end.id }) {
            if endIndex != orderedPoints.count - 1 {
                orderedPoints.remove(at: endIndex)
                orderedPoints.append(end)
            }
        }

        // 🚨 保护排序字段：检查是否有第三方排序号
        let hasThirdPartySort = orderedPoints.contains { point in
            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                return true
            }
            return false
        }

        if hasThirdPartySort {
            logInfo("🚨 检测到第三方排序号，保护所有排序字段不被修改")
            // 🚨 有第三方排序号时，不修改任何排序字段
            for point in orderedPoints {
                // 只标记为已优化
                point.isOptimized = true
                logInfo("🚨 保护排序字段: \(point.primaryAddress) - sorted_number=\(point.sorted_number), thirdPartySort=\(point.thirdPartySortNumber ?? "nil") (未修改)")
            }
        } else {
            logInfo("🚨 无第三方排序号，但仍保护排序字段不被修改")
            // 🚨 即使没有第三方排序号，也不修改排序字段
            for point in orderedPoints {
                // 只标记为已优化
                point.isOptimized = true
                logInfo("🚨 保护排序字段: \(point.primaryAddress) - sorted_number=\(point.sorted_number) (未修改)")
            }
        }

        // 更新路线的优化状态
        if let route = currentRoute {
            route.isOptimized = true
            logInfo("将路线 \(route.name) 标记为已优化")
        }

        // 保存更改到数据库
        do {
            try modelContext.save()
            logInfo("成功保存 \(orderedPoints.count) 个点的排序编号更新")

            // 发送通知，通知其他组件数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: nil
            )
        } catch {
            logError("保存排序编号更新失败: \(error.localizedDescription)")
        }

        // 获取优化后的点顺序
        let optimizedPoints = optimizedPointsOrder.compactMap { pointId in
            return deliveryPoints.first { $0.id == pointId }
        }

        // 暂时禁用自动路线计算，防止MapKit请求限制
        if optimizedPoints.count >= 2 {
            // 清除现有路线覆盖物
            routeOverlays.removeAll()

            // 路线计算功能暂时禁用
            // await calculateAllRoutes(for: optimizedPoints)
            logInfo("优化路线确认完成，路线计算功能已暂时禁用")
        }

        // 清除优化信息
        clearRouteOptimizationInfo()

        // 刷新配送点状态
        await refreshDeliveryPointsStatus()
    }

    // 取消优化后的路线
    func cancelOptimizedRoute() {
        // 清除优化信息
        clearRouteOptimizationInfo()
    }

    // 清除路线优化信息
    func clearRouteOptimizationInfo() {
        routeOptimizationInfo = nil
        optimizedPointsOrder = []
        originalPointsOrder = []
    }

    // 检查路线中是否存在无效地址
    func hasInvalidAddresses() -> Bool {
        // 检查是否有地址点
        guard !deliveryPoints.isEmpty else {
            return false // 没有地址点，不算无效
        }

        // 移除日志输出，因为这个方法在SwiftUI中被频繁调用
        // 如果需要调试，可以调用hasInvalidAddressesWithLogging()方法

        // 检查每个地址点是否有效
        var hasInvalid = false
        for point in deliveryPoints {
            if hasAddressValidationIssue(point) {
                hasInvalid = true
                break // 找到一个无效地址就可以返回了
            }
        }

        return hasInvalid
    }

    // 带日志的版本，用于调试时调用
    func hasInvalidAddressesWithLogging() -> Bool {
        // 检查是否有地址点
        guard !deliveryPoints.isEmpty else {
            return false // 没有地址点，不算无效
        }

        // 记录当前验证模式（只在开始时记录一次）
        let validationService = ReverseGeocodingValidationService.shared
        let threshold = validationService.validationMode.threshold
        logInfo("RouteViewModel - 开始地址验证检查，当前验证模式: \(validationService.validationMode.description), 阈值: \(threshold)")

        // 检查每个地址点是否有效
        var hasInvalid = false
        for point in deliveryPoints {
            if hasAddressValidationIssue(point) {
                // 🔍 调试：记录有问题的地址详情
                logInfo("RouteViewModel - 发现有问题的地址: \(point.primaryAddress)")
                logInfo("  - 地址验证分数: \(point.addressValidationScore)")
                logInfo("  - 地理编码警告: \(point.geocodingWarning ?? "无")")
                logInfo("  - 地址验证问题: \(point.addressValidationIssues ?? "无")")
                logInfo("  - 位置验证状态: \(point.locationValidationStatus)")
                logInfo("  - 坐标: (\(point.latitude), \(point.longitude))")
                hasInvalid = true
            }
        }

        if hasInvalid {
            logInfo("RouteViewModel - 总共发现 \(getInvalidAddressIndices().count) 个有问题的地址")
        } else {
            logInfo("RouteViewModel - 所有地址验证正常")
        }

        return hasInvalid
    }

    // 检查单个地址点是否有验证问题
    private func hasAddressValidationIssue(_ point: DeliveryPoint) -> Bool {
        // 🎯 排除起点的验证检查（sort_number = 0 或 isStartPoint = true）
        if point.isStartPoint || point.sort_number == 0 {
            return false
        }

        // 检查坐标是否为0,0（无效坐标）
        if point.latitude == 0 && point.longitude == 0 {
            return true
        }

        // 检查坐标是否在有效范围内
        if point.latitude < -90 || point.latitude > 90 ||
            point.longitude < -180 || point.longitude > 180 {
            return true
        }

        // 检查是否有地理编码警告
        if let warning = point.geocodingWarning, !warning.isEmpty {
            logInfo("  - 发现地理编码警告: \(warning)")
            return true
        }

        // 🚨 根据验证模式检查地址验证分数
        let validationService = ReverseGeocodingValidationService.shared
        let threshold = validationService.validationMode.threshold
        // 移除重复的验证模式日志，这个信息在hasInvalidAddresses方法开始时已经记录过

        // 🎯 临时修复：为验证分数为0的手动添加地址设置默认分数
        if point.addressValidationScore == 0.0 {
            logInfo("  - 检测到验证分数为0的地址，设置为默认分数100")
            point.addressValidationScore = 100.0
            point.addressValidationIssues = nil
            point.geocodingWarning = nil

            // 保存到数据库
            if let modelContext = self.modelContext {
                try? modelContext.save()
            }
        }

        // 🌍 全球化修复：如果地址只是因为距离远而被标记为低分，但地址本身有效，则提高分数
        if point.addressValidationScore == 50.0 &&
           point.geocodingWarning?.contains("far from your current position") == true {
            logInfo("  - 检测到因距离远而被低评分的有效地址，提高验证分数到85")
            point.addressValidationScore = 85.0 // 设置为85分，高于80分阈值但表明有距离警告

            // 保存到数据库
            if let modelContext = self.modelContext {
                try? modelContext.save()
            }
        }

        if point.addressValidationScore < threshold {
            logInfo("  - 地址验证分数不达标: \(point.addressValidationScore) < \(threshold)")
            return true
        }

        // 检查是否有地址验证问题描述
        if let issues = point.addressValidationIssues, !issues.isEmpty {
            logInfo("  - 发现地址验证问题: \(issues)")
            return true
        }

        // 检查位置验证状态 - 只有明确的invalid状态才算有问题
        let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
        if validationStatus == .invalid {
            return true
        }

        return false
    }

    // 获取无效地址的索引列表
    func getInvalidAddressIndices() -> [Int] {
        var invalidIndices: [Int] = []

        for (index, point) in deliveryPoints.enumerated() {
            if hasAddressValidationIssue(point) {
                invalidIndices.append(index)
            }
        }

        return invalidIndices
    }

    // 重新验证所有地址
    func revalidateAllAddresses() {
        logInfo("RouteViewModel - 开始重新验证所有地址")

        for point in deliveryPoints {
            // 重新验证坐标
            _ = point.validateCoordinates()

            // 基于用户位置重新验证
            if let userLocation = LocationManager.shared.userLocation {
                point.validateLocationBasedOnUserPosition(userLocation)
            }
        }

        // 保存更改
        if let modelContext = modelContext {
            do {
                try modelContext.save()
                logInfo("RouteViewModel - 地址重新验证完成，已保存到数据库")
            } catch {
                logError("RouteViewModel - 保存地址验证结果失败: \(error.localizedDescription)")
            }
        }

        // 触发UI更新
        objectWillChange.send()
    }

    // 检查地址修正后是否可以启用优化
    func checkOptimizationAvailabilityAfterAddressUpdate() {
        // 立即检查一次（使用带日志的版本，因为这是调试用的方法）
        let hasInvalid = self.hasInvalidAddressesWithLogging()
        let hasEnoughPoints = self.hasEnoughPointsForOptimization()

        logInfo("RouteViewModel - 地址更新后立即检查优化可用性: 有无效地址=\(hasInvalid), 有足够点数=\(hasEnoughPoints)")

        // 立即触发UI更新
        self.objectWillChange.send()

        // 再延迟检查一次，确保数据完全更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            let hasInvalidDelayed = self.hasInvalidAddressesWithLogging()
            let hasEnoughPointsDelayed = self.hasEnoughPointsForOptimization()

            logInfo("RouteViewModel - 地址更新后延迟检查优化可用性: 有无效地址=\(hasInvalidDelayed), 有足够点数=\(hasEnoughPointsDelayed)")

            // 再次触发UI更新
            self.objectWillChange.send()
        }
    }

    // 清除所有路线数据（仅用于测试）
    func clearAllRoutes() async {
        guard let modelContext = modelContext else {
            logError("无法清除路线：没有ModelContext")
            return
        }

        do {
            // 获取所有路线
            let descriptor = FetchDescriptor<Route>()
            let routes = try modelContext.fetch(descriptor)

            // 删除所有路线
            for route in routes {
                modelContext.delete(route)
            }

            // 保存更改
            try modelContext.save()

            // 重置当前路线
            currentRoute = nil
            isRouteNewlyCreated = false

            logInfo("已清除所有\(routes.count)条路线记录")
        } catch {
            logError("清除路线失败: \(error.localizedDescription)")
        }
    }

    // 🎯 检测当前路线中的主要第三方应用类型
    func getDominantThirdPartyApp() -> DeliveryAppType? {
        guard let route = currentRoute else { return nil }

        // 获取所有非起点、非终点的配送点
        let regularPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
        guard !regularPoints.isEmpty else { return nil }

        // 统计各个第三方应用的数量（排除manual类型）
        var appTypeCounts: [DeliveryAppType: Int] = [:]
        for point in regularPoints {
            let appType = point.sourceApp
            // 只统计非manual类型且有第三方排序号的点
            if appType != .manual && point.thirdPartySortNumber != nil && !point.thirdPartySortNumber!.isEmpty {
                appTypeCounts[appType, default: 0] += 1
            }
        }

        // 如果没有第三方应用，返回nil
        guard !appTypeCounts.isEmpty else { return nil }

        // 找出数量最多的第三方应用类型
        let sortedTypes = appTypeCounts.sorted { $0.value > $1.value }
        let dominantApp = sortedTypes.first?.key
        let dominantCount = sortedTypes.first?.value ?? 0
        let totalThirdPartyCount = appTypeCounts.values.reduce(0, +)

        // 如果主导应用占比超过50%，返回该应用；否则返回nil（显示通用"第三方"）
        if Double(dominantCount) / Double(totalThirdPartyCount) > 0.5 {
            return dominantApp
        } else {
            return nil
        }
    }

    // 计算路线的总距离 - 使用真实道路距离
    private func calculateTotalRouteDistance(driverPosition: CLLocationCoordinate2D, route: [DeliveryPoint]) async -> Double {
        if route.isEmpty {
            return 0
        }

        if route.count == 1 {
            return 0 // 只有一个点时，距离为0
        }

        // 记录位置信息（仅用于调试）
        logLocationInfo(driverPosition)

        // 更新进度信息
        totalPoints = route.count
        currentProcessingPoint = 0

        // 使用真实道路距离计算
        return await withCheckedContinuation { continuation in
            let calculator = LocalDistanceCalculator.shared
            calculator.calculateTotalRealDistance(points: route) { [weak self] result in
                guard let self = self else {
                    continuation.resume(returning: 0)
                    return
                }

                switch result {
                case .success(let distance):
                    self.logInfo("总路线距离（真实道路）: \(distance/1000) 公里")

                    // 更新进度为完成
                    DispatchQueue.main.async {
                        self.currentProcessingPoint = self.totalPoints
                        self.optimizationProgress = 1.0
                    }

                    continuation.resume(returning: distance)

                case .failure(let error):
                    self.logInfo("真实距离计算失败: \(error.localizedDescription)")

                    // 不使用直线距离，返回0表示计算失败
                    DispatchQueue.main.async {
                        self.currentProcessingPoint = self.totalPoints
                        self.optimizationProgress = 1.0
                    }

                    continuation.resume(returning: 0)
                }
            }
        }
    }

    // 已移除直线距离计算方法
    // 现在只使用真实道路距离

    // MARK: - 选中点路线计算

    /// 获取选中点的路线坐标数组（按选择顺序）
    func getSelectedPointsRoute() -> [CLLocationCoordinate2D] {
        let selectedDeliveryPoints = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }
        return selectedDeliveryPoints.map { $0.coordinate }
    }

    // 缓存的选中点距离
    @Published var selectedPointsRealDistance: Double = 0
    @Published var selectedPointsDistanceStatus: DistanceCalculationStatus = .notCalculated

    // 已移除直线距离计算方法
    // 现在只使用真实道路距离计算

    /// 异步计算选中点的真实道路距离（使用新的缓存系统）
    func calculateSelectedPointsRealDistance() {
        let coordinates = getSelectedPointsRoute()

        logInfo("🚨 开始计算选中点距离 - 坐标点数: \(coordinates.count)")

        // 打印详细的坐标信息用于调试
        for (index, coord) in coordinates.enumerated() {
            logInfo("🚨 坐标 \(index + 1): (\(coord.latitude), \(coord.longitude))")
        }

        guard coordinates.count > 1 else {
            logInfo("🚨 选中点数不足2个，重置距离状态")
            selectedPointsRealDistance = 0
            selectedPointsDistanceStatus = .completed
            return
        }

        // 立即设置为计算中状态
        selectedPointsDistanceStatus = .calculating
        logInfo("🚨 距离计算状态设置为计算中")

        // 🎯 使用新的RouteSequenceManager进行计算
        Task { @MainActor in
            RouteSequenceManager.shared.calculateRouteSequence(coordinates: coordinates) { [weak self] result in
                Task { @MainActor in
                    guard let self = self else { return }

                    switch result {
                    case .success(let (distance, time)):
                        self.selectedPointsRealDistance = distance
                        self.selectedPointsDistanceStatus = .completed
                        self.logInfo("🚨 选中点真实距离计算完成: \(distance/1000) 公里, 时间: \(time/60) 分钟")

                    case .failure(let error):
                        self.logInfo("🚨 选中点真实距离计算失败: \(error.localizedDescription)")
                        // 🚨 重试距离计算
                        self.retryDistanceCalculation(coordinates: coordinates, retryCount: 1)
                    }
                }
            }
        }
    }

    /// 🚨 重试距离计算（使用新的缓存系统）
    private func retryDistanceCalculation(coordinates: [CLLocationCoordinate2D], retryCount: Int) {
        guard retryCount <= 2 else {
            logInfo("🚨 距离计算重试次数已达上限，标记为失败")
            selectedPointsRealDistance = 0
            selectedPointsDistanceStatus = .fallback
            return
        }

        logInfo("🚨 开始第 \(retryCount) 次距离计算重试")

        // 🎯 使用新的RouteSequenceManager进行重试，添加延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + Double(retryCount)) {
            Task { @MainActor in
                RouteSequenceManager.shared.calculateRouteSequence(coordinates: coordinates) { [weak self] result in
                    Task { @MainActor in
                        guard let self = self else { return }

                        switch result {
                        case .success(let (distance, time)):
                            self.selectedPointsRealDistance = distance
                            self.selectedPointsDistanceStatus = .completed
                            self.logInfo("🚨 距离计算重试成功: \(distance/1000) 公里, 时间: \(time/60) 分钟")

                        case .failure(let error):
                            self.logInfo("🚨 距离计算重试失败: \(error.localizedDescription)")
                            self.retryDistanceCalculation(coordinates: coordinates, retryCount: retryCount + 1)
                        }
                    }
                }
            }
        }
    }

    /// 格式化选中点路线距离显示
    func getFormattedSelectedPointsDistance() -> String {
        switch selectedPointsDistanceStatus {
        case .notCalculated:
            return "计算中..."
        case .calculating:
            return "计算中..."
        case .completed:
            return DistanceFormatter.shared.formatDistance(selectedPointsRealDistance)
        case .fallback:
            return "距离计算失败"
        }
    }

    // MARK: - 路线动画控制

    /// 开始路线脉动动画 - 临时禁用用于调试
    func startRouteAnimation() {
        // 🚨 临时禁用脉动动画用于调试直线连接问题
        logInfo("🚨 脉动动画已临时禁用用于调试")
        stopRouteAnimation()
        return

        // 注释掉的代码，避免编译警告
        /*
        // 先停止任何现有动画，避免多个动画同时运行
        stopRouteAnimation()

        // 获取选中点的坐标（按选中顺序）
        let selectedLocations = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }

        guard selectedLocations.count > 1 else {
            logInfo("🚨 选中点数不足，无法开始路线动画")
            return
        }

        logInfo("🚨 开始路线脉动动画 - 选中点数: \(selectedLocations.count)")
        isRouteAnimationActive = true

        // 🚨 立即开始动画，不延迟
        logInfo("🚨 立即启动脉动动画")
        animateAlongSelectedPoints(points: selectedLocations)
        */
    }

    /// 停止路线脉动动画
    func stopRouteAnimation() {
        logInfo("🚨 停止路线脉动动画")
        isRouteAnimationActive = false
        routePulsePosition = nil
    }

    /// 按选中点顺序动画移动脉动点 - 简化逻辑避免多重动画
    private func animateAlongSelectedPoints(points: [DeliveryPoint]) {
        guard isRouteAnimationActive && points.count > 1 else {
            logInfo("🚨 动画条件不满足，停止动画")
            return
        }

        logInfo("🚨 开始脉动动画 - 沿 \(points.count) 个选中点移动")

        // 🚨 立即设置第一个点的位置，确保动画可见
        routePulsePosition = points[0].coordinate
        logInfo("🚨 脉动点初始位置设置为: \(points[0].primaryAddress)")

        // 简化策略：直接在选中点之间移动，避免复杂的路线坐标动画
        animateToNextSelectedPoint(currentIndex: 0, points: points)
    }

    /// 循环动画方法 - 在选中点之间移动脉动点（到达终点后从起点重新开始）
    private func animateToNextSelectedPoint(currentIndex: Int, points: [DeliveryPoint]) {
        guard isRouteAnimationActive else {
            logInfo("🚨 动画已停止，退出动画循环")
            return
        }

        // 🚨 修复：到达终点后从起点重新开始，而不是停止
        let actualIndex = currentIndex % points.count
        let currentPoint = points[actualIndex]

        // 🚨 使用动画设置脉动点位置到当前点
        withAnimation(.easeInOut(duration: 0.5)) {
            routePulsePosition = currentPoint.coordinate
        }

        logInfo("🚨 脉动点移动到第 \(actualIndex + 1) 个点: \(currentPoint.primaryAddress)")

        // 0.6秒后移动到下一个点（稍微快一点，让动画更流畅）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            // 🚨 关键修复：到达最后一个点后，在起点重新开始，不绘制返回线
            let nextIndex = (actualIndex + 1) % points.count

            // 如果即将回到起点，稍微延迟一下让用户看清楚到达了终点
            if nextIndex == 0 {
                self.logInfo("🚨 脉动点到达终点，1秒后从起点重新开始")
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    // 直接跳转到起点，不使用动画，避免绘制返回线
                    self.routePulsePosition = points[0].coordinate
                    self.logInfo("🚨 脉动点重置到起点: \(points[0].primaryAddress)")

                    // 然后继续正常动画
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.animateToNextSelectedPoint(currentIndex: 1, points: points)
                    }
                }
            } else {
                self.animateToNextSelectedPoint(currentIndex: currentIndex + 1, points: points)
            }
        }
    }

    // 移除复杂的路线坐标动画方法，简化为只在选中点之间移动

    /// 比较两个坐标是否相等（考虑浮点数精度）
    private func coordinatesAreEqual(_ coord1: CLLocationCoordinate2D, _ coord2: CLLocationCoordinate2D) -> Bool {
        let tolerance: Double = 0.000001 // 约1米的精度
        return abs(coord1.latitude - coord2.latitude) < tolerance &&
               abs(coord1.longitude - coord2.longitude) < tolerance
    }



    // 从指定起点开始重新计算最优路线
    func optimizeRouteFromDriverLocation(_ points: [DeliveryPoint]) async -> [UUID] {
        logInfo("开始路线优化，共\(points.count)个点")

        // 查找标记为起点的地址
        let startPoint = points.first { $0.isStartPoint }

        // 确定起始位置：简化的优先级规则
        var startLocation: CLLocationCoordinate2D = CLLocationCoordinate2D(latitude: -37.8794, longitude: 145.1498) // 默认位置

        // 优先级1：isStartPoint为true的点
        if let start = startPoint {
            // 检查起点坐标是否有效（非0,0且在合理范围内）
            let isInvalidCoordinate = start.latitude == 0 && start.longitude == 0

            if isInvalidCoordinate {
                logError("起点坐标无效(0,0)，尝试使用用户当前位置")
                // 如果起点坐标无效，尝试使用用户当前位置
                if let currentLocation = driverLocation {
                    startLocation = currentLocation
                    logInfo("起点坐标无效，使用用户当前位置作为路线优化起点: (\(currentLocation.latitude), \(currentLocation.longitude))")
                } else {
                    logError("用户当前位置不可用，使用Glen Waverley默认位置作为路线优化起点")
                }
            } else {
                // 起点坐标有效，使用它
                startLocation = start.coordinate
                logInfo("优先级1：使用标记为起点(isStartPoint=true)的地址作为路线优化起点: \(start.primaryAddress)")
            }
        }
        // 优先级2：用户当前位置（如果可用）
        else if let currentLocation = driverLocation {
            startLocation = currentLocation
            logInfo("优先级2：使用用户当前位置作为路线优化起点: (\(currentLocation.latitude), \(currentLocation.longitude))")
        }
        // 备选方案：使用默认位置
        else {
            logInfo("没有找到起点或用户位置，使用Glen Waverley默认位置作为路线优化起点")
        }

        // 记录位置信息（仅用于调试）
        logInfo("路线优化起点坐标: (\(startLocation.latitude), \(startLocation.longitude))")

        // 记录源坐标和第一个目标坐标的信息，用于调试
        if let firstPoint = points.first(where: { !$0.isStartPoint }) {
            logInfo("路线优化: 源: (\(startLocation.latitude), \(startLocation.longitude)), 目标: (\(firstPoint.latitude), \(firstPoint.longitude))")
        }

        // 🎯 使用单纯的贪心算法优化路线
        let optimizedPoints = await optimizeWithGreedy(
            points: points,
            startLocation: startLocation
        )

        // 提取优化后的点ID
        let optimizedIds = optimizedPoints.map { $0.id }

        logInfo("贪心算法路线优化完成，从\(points.count)个点优化为\(optimizedIds.count)个点")

        return optimizedIds
    }

    // 🎯 使用单纯的贪心算法优化路线 - 简化版本
    private func optimizeWithGreedy(points: [DeliveryPoint], startLocation: CLLocationCoordinate2D) async -> [DeliveryPoint] {
        logInfo("开始贪心算法优化，共\(points.count)个点")

        // 如果点数少于2，无需优化
        if points.count < 2 {
            return points
        }

        // 创建结果数组和剩余点集合
        var result: [DeliveryPoint] = []
        var remainingPoints = points

        // 🎯 第一步：处理起点
        if let startPoint = remainingPoints.first(where: { $0.isStartPoint }) {
            result.append(startPoint)
            remainingPoints.removeAll { $0.id == startPoint.id }
            logInfo("贪心算法 - 起点: \(startPoint.primaryAddress)")
        }

        // 当前位置：如果有起点使用起点坐标，否则使用传入的起始位置
        var currentLocation = result.isEmpty ? startLocation : result.last!.coordinate

        // 🎯 第二步：贪心选择最近的点
        while !remainingPoints.isEmpty {
            // 更新进度
            optimizationProgress = Double(result.count) / Double(points.count)
            currentProcessingPoint = result.count + 1

            // 找到距离当前位置最近的点
            var nearestPoint: DeliveryPoint?
            var shortestDistance = Double.infinity

            for point in remainingPoints {
                // 使用简单的坐标差值作为距离估算（用于路线优化）
                let latDiff = abs(currentLocation.latitude - point.coordinate.latitude)
                let lonDiff = abs(currentLocation.longitude - point.coordinate.longitude)
                let distance = latDiff + lonDiff // 曼哈顿距离作为快速估算

                if distance < shortestDistance {
                    shortestDistance = distance
                    nearestPoint = point
                }
            }

            // 添加最近的点到路线中
            if let nearest = nearestPoint {
                result.append(nearest)
                remainingPoints.removeAll { $0.id == nearest.id }
                currentLocation = nearest.coordinate

                logInfo("贪心算法 - 选择点: \(nearest.primaryAddress)")
            }

            // 动态延迟：根据点数量调整
            if result.count % 20 == 0 {
                // 每20个点暂停一次，延迟时间根据总点数动态调整
                let delayNanoseconds = points.count > 100 ? 5_000_000 : 2_000_000 // 5ms或2ms
                try? await Task.sleep(nanoseconds: UInt64(delayNanoseconds))
            }
        }

        // 完成进度
        optimizationProgress = 1.0
        logInfo("贪心算法完成，优化后路线有\(result.count)个点")

        return result
    }



    // 获取待派送的点数量
    func getPendingDeliveryCount() -> Int {
        // 过滤：1. 未分组的点 2. 未完成派送的点 3. 不是起点和终点
        let pendingPoints = deliveryPoints.filter {
            !$0.isAssignedToGroup &&
            !$0.isStartPoint &&
            !$0.isEndPoint &&
            $0.status != DeliveryStatus.completed.rawValue
        }

        // 仅在必要时记录详细信息
        #if DEBUG
        // 使用节流控制日志频率
        let currentTime = Date().timeIntervalSince1970
        if currentTime - lastPendingCountLogTime > 5.0 { // 每5秒最多记录一次
            lastPendingCountLogTime = currentTime
            logInfo("getPendingDeliveryCount - 总点数: \(deliveryPoints.count), 未分组未完成非起终点数: \(pendingPoints.count)")

            // 记录前5个点的状态作为示例，避免过多日志
            for (index, point) in deliveryPoints.prefix(5).enumerated() {
                logInfo("点\(index+1): \(point.primaryAddress) - 已分组: \(point.isAssignedToGroup), 起点: \(point.isStartPoint), 终点: \(point.isEndPoint), 状态: \(point.status)")
            }
            if deliveryPoints.count > 5 {
                logInfo("... 还有\(deliveryPoints.count - 5)个点 ...")
            }
        }
        #endif

        return pendingPoints.count
    }

    // 获取已分组的点数量（不包括起点和终点）
    func getGroupedDeliveryCount() -> Int {
        let groupedPoints = deliveryPoints.filter {
            $0.isAssignedToGroup &&
            !$0.isStartPoint &&
            !$0.isEndPoint
        }
        return groupedPoints.count
    }

    // 获取总的配送点数量（不包括起点和终点）
    func getTotalDeliveryCount() -> Int {
        let totalPoints = deliveryPoints.filter {
            !$0.isStartPoint &&
            !$0.isEndPoint
        }
        return totalPoints.count
    }

    // 时间戳属性，用于控制日志输出频率
    private var lastPendingCountLogTime: TimeInterval = 0

    // MARK: - 订阅相关方法

    // 检查是否可以添加更多停靠点
    func canAddMoreStops() -> Bool {
        guard let currentRoute = currentRoute else { return true }
        return subscriptionManager.canAddMoreStops(to: currentRoute)
    }

    // 获取剩余可添加的停靠点数量
    func remainingStopsAllowed() -> Int {
        guard let currentRoute = currentRoute else { return 0 }
        return subscriptionManager.remainingStopsAllowed(for: currentRoute)
    }

    // 检查是否可以使用自动分组功能
    func canUseAutoGrouping() -> Bool {
        return subscriptionManager.canUseAutoGrouping()
    }

    // 检查是否可以访问顶部按钮功能
    func canAccessTopButtons() -> Bool {
        return subscriptionManager.canAccessTopButtons()
    }

    // MARK: - 自动分组功能

    /// 自动将未分组的地址按每14个一组进行分组
    /// - Returns: 创建的分组数量
    @MainActor
    func autoGroupDeliveryPoints() async -> Int {
        guard let modelContext = modelContext else {
            logError("无法自动分组：没有ModelContext")
            return 0
        }

        guard let currentRoute = currentRoute else {
            logError("无法自动分组：没有当前路线")
            return 0
        }

        // 获取当前路线中所有未分组的地址点
        // 过滤条件：1. 未分组 2. 不是起点和终点 3. 未完成派送
        let ungroupedPoints = deliveryPoints.filter {
            !$0.isAssignedToGroup &&
            !$0.isStartPoint &&
            !$0.isEndPoint &&
            $0.status != DeliveryStatus.completed.rawValue
        }

        if ungroupedPoints.isEmpty {
            logInfo("没有未分组的地址点，无需创建分组")
            return 0
        }

        // 检查免费版限制
        let subscriptionManager = SubscriptionManager.shared
        let remainingSlots = subscriptionManager.remainingGroupAddressSlots(for: currentRoute)

        // 🎯 使用统一排序逻辑，确保按正确顺序分组
        let sortedPoints = sortDeliveryPoints(ungroupedPoints, isOptimized: currentRoute.isOptimized)

        // 对于免费版用户，限制可分组的地址数量
        let pointsToGroup: [DeliveryPoint]
        if subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial {
            pointsToGroup = Array(sortedPoints.prefix(remainingSlots))
            if pointsToGroup.count < sortedPoints.count {
                logInfo("免费版限制：只能将前\(pointsToGroup.count)个地址加入组，剩余\(sortedPoints.count - pointsToGroup.count)个地址需要升级后才能分组")
            }
        } else {
            pointsToGroup = sortedPoints
        }

        if pointsToGroup.isEmpty {
            logInfo("没有可分组的地址点（可能受免费版限制）")
            return 0
        }

        // 每组最多包含的地址数量
        let pointsPerGroup = 14

        // 计算需要创建的分组数量
        let groupCount = Int(ceil(Double(pointsToGroup.count) / Double(pointsPerGroup)))

        // 获取下一个组号
        let nextGroupNumber = getNextGroupNumber()

        // 创建分组
        var createdGroups = 0

        for i in 0..<groupCount {
            // 计算当前分组的起始和结束索引
            let startIndex = i * pointsPerGroup
            let endIndex = min(startIndex + pointsPerGroup, pointsToGroup.count)

            // 获取当前分组的地址点
            let groupPoints = Array(pointsToGroup[startIndex..<endIndex])

            // 创建分组名称 - 使用本地化格式，与手动创建分组保持一致
            let groupName = String(format: "default_group_name_format".localized, nextGroupNumber + i)

            // 更新点的状态 - 保持原始的sort_number，只更新分组状态
            for point in groupPoints {
                point.isAssignedToGroup = true
                point.assignedGroupNumber = nextGroupNumber + i
            }

            // 创建新的配送组
            let newGroup = DeliveryGroup(
                name: groupName,
                points: groupPoints,
                groupNumber: nextGroupNumber + i
            )

            // 保存到数据库
            modelContext.insert(newGroup)

            createdGroups += 1

            // 记录详细日志，包括分组范围和排序信息
            let startNum = startIndex + 1
            let endNum = endIndex
            let sortedNumberRange = groupPoints.isEmpty ? "无" : "\(groupPoints.first?.sorted_number ?? 0)-\(groupPoints.last?.sorted_number ?? 0)"
            logInfo("已创建配送分组：\(groupName)，包含\(groupPoints.count)个配送点，范围：\(startNum)-\(endNum)，优化序号范围：\(sortedNumberRange)") // 内部调试日志，无需本地化
        }

        // 保存更改
        try? modelContext.save()

        // 刷新配送点状态
        await refreshDeliveryPointsStatus()

        return createdGroups
    }

    // 检查是否需要显示升级提示
    func shouldShowUpgradePrompt() -> Bool {
        guard let currentRoute = currentRoute else { return false }
        return subscriptionManager.shouldShowUpgradePrompt(for: currentRoute)
    }

    // 添加停靠点到路线中，并检查订阅限制
    func addStopToRoute(address: String, coordinate: CLLocationCoordinate2D) -> Bool {
        guard let currentRoute = currentRoute else {
            logError("无法添加停靠点：当前没有活动路线")
            return false
        }

        // 检查是否超过订阅限制
        if !canAddMoreStops() {
            // 显示订阅提示
            showSubscriptionPrompt = true
            logInfo("无法添加更多停靠点：已达到订阅计划限制")
            return false
        }

        // 🎯 修复：标准化地址后创建新的配送点，正确设置地址字段
        let standardizedAddress = AddressStandardizer.standardizeAddress(address)
        let newPoint = DeliveryPoint(
            sort_number: currentRoute.points.count + 1,
            streetName: standardizedAddress, // 设置为完整地址，让primaryAddress能正确显示
            coordinate: coordinate
        )

        // 添加到当前路线，使用辅助方法
        currentRoute.addPoint(newPoint)

        // 保存到数据库
        guard let modelContext = modelContext else {
            logError("无法保存停靠点：没有ModelContext")
            return false
        }

        modelContext.insert(newPoint)

        do {
            try modelContext.save()
            logInfo("已添加新停靠点到路线: \(address)")

            // 刷新配送点列表
            deliveryPoints.append(newPoint)

            // 如果接近限制，提示用户升级
            if shouldShowUpgradePrompt() {
                showSubscriptionPrompt = true
            }

            return true
        } catch {
            logError("添加停靠点失败: \(error.localizedDescription)")
            return false
        }
    }

    // MARK: - 地址坐标修复功能
    // 已移除批量更新地址坐标和检查警告的方法，改为在添加地址时自动处理

    // MARK: - 路线名称管理

    // 更新路线名称并保存到数据库
    func updateRouteName(_ route: Route, newName: String) async -> Bool {
        guard let modelContext = modelContext else {
            logError("RouteViewModel - 无法更新路线名称：没有ModelContext")
            return false
        }

        let oldName = route.name
        logInfo("RouteViewModel - 开始更新路线名称: 从 '\(oldName)' 更新为 '\(newName)'")

        // 更新路线名称
        route.name = newName

        do {
            // 保存到数据库
            try modelContext.save()
            logInfo("RouteViewModel - 成功保存路线名称更新: '\(oldName)' -> '\(newName)', 路线ID: \(route.id.uuidString)")

            // 验证更新是否成功
            let descriptor = FetchDescriptor<Route>()
            let routes = try modelContext.fetch(descriptor)
            if let updatedRoute = routes.first(where: { $0.id == route.id }) {
                logInfo("RouteViewModel - 验证路线名称更新: 数据库中的名称现在是 '\(updatedRoute.name)'")

                if updatedRoute.name == newName {
                    logInfo("RouteViewModel - 路线名称更新验证成功")
                    return true
                } else {
                    logError("RouteViewModel - 路线名称更新验证失败: 期望的名称是 '\(newName)', 实际的名称是 '\(updatedRoute.name)'")
                    return false
                }
            } else {
                logError("RouteViewModel - 路线名称更新后无法找到路线: ID = \(route.id.uuidString)")
                return false
            }
        } catch {
            logError("RouteViewModel - 保存路线名称更新失败: \(error.localizedDescription)")
            return false
        }
    }

    // 验证路线名称是否正确保存
    func verifyRouteName(_ route: Route) async -> Bool {
        guard let modelContext = modelContext else {
            logError("RouteViewModel - 无法验证路线名称：没有ModelContext")
            return false
        }

        do {
            let descriptor = FetchDescriptor<Route>()
            let routes = try modelContext.fetch(descriptor)
            if let fetchedRoute = routes.first(where: { $0.id == route.id }) {
                logInfo("RouteViewModel - 验证路线名称: 内存中的名称是 '\(route.name)', 数据库中的名称是 '\(fetchedRoute.name)'")

                return fetchedRoute.name == route.name
            } else {
                logError("RouteViewModel - 验证路线名称失败: 无法找到路线 ID = \(route.id.uuidString)")
                return false
            }
        } catch {
            logError("RouteViewModel - 验证路线名称失败: \(error.localizedDescription)")
            return false
        }
    }

    // MARK: - 路线点操作

    /// 清除当前路线的所有点和关联的分组
    func clearAllRoutePoints() {
        guard let route = currentRoute else {
            logError("clearAllRoutePoints - 当前没有活动路线")
            return
        }

        // 防止重复调用
        if isSetupInProgress {
            logInfo("clearAllRoutePoints - 已有操作正在进行，跳过此次调用")
            return
        }

        Task {
            // 设置防重复标志
            await MainActor.run {
                isSetupInProgress = true
            }

            logInfo("开始清除路线的所有点和关联分组 - 路线ID: \(route.id)")

            // 修改数据模型前，确保有ModelContext
            guard let modelContext = self.modelContext else {
                logError("clearAllRoutePoints - ModelContext不可用")
                await MainActor.run {
                    isSetupInProgress = false
                }
                return
            }

            // 创建删除操作
            do {
                // 直接获取路线的所有点
                let points = route.points
                logInfo("找到\(points.count)个点需要删除")

                // 1. 首先收集所有相关的delivery groups
                var groupsToDelete = Set<DeliveryGroup>()

                // 方法1：通过点的分组信息查找
                for point in points {
                    if point.isAssignedToGroup, let groupNumber = point.assignedGroupNumber {
                        // 查找对应的delivery group
                        let groupDescriptor = FetchDescriptor<DeliveryGroup>(
                            predicate: #Predicate<DeliveryGroup> { group in
                                group.groupNumber == groupNumber
                            }
                        )

                        if let groups = try? modelContext.fetch(groupDescriptor) {
                            for group in groups {
                                // 检查这个group是否包含当前route的points
                                let hasRoutePoints = group.points.contains { groupPoint in
                                    points.contains { routePoint in
                                        routePoint.id == groupPoint.id
                                    }
                                }

                                if hasRoutePoints {
                                    groupsToDelete.insert(group)
                                }
                            }
                        }
                    }
                }

                // 方法2：查找所有delivery groups，检查是否包含当前route的points
                let allGroupsDescriptor = FetchDescriptor<DeliveryGroup>()
                if let allGroups = try? modelContext.fetch(allGroupsDescriptor) {
                    let routePointIds = Set(points.map { $0.id })

                    for group in allGroups {
                        let groupPointIds = Set(group.points.map { $0.id })
                        // 如果group的任何点都在当前route中，就删除这个group
                        if !groupPointIds.intersection(routePointIds).isEmpty {
                            groupsToDelete.insert(group)
                        }
                    }
                }

                logInfo("找到\(groupsToDelete.count)个相关分组需要删除")

                // 2. 删除相关的delivery groups
                for group in groupsToDelete {
                    logInfo("删除分组: \(group.name) (组号: \(group.groupNumber))")
                    modelContext.delete(group)
                }

                // 3. 删除所有点
                for point in points {
                    modelContext.delete(point)
                }

                // 保存更改
                try modelContext.save()

                // 额外的安全措施：清理所有空的delivery groups
                let emptyGroupsDescriptor = FetchDescriptor<DeliveryGroup>()
                if let allGroups = try? modelContext.fetch(emptyGroupsDescriptor) {
                    let emptyGroups = allGroups.filter { $0.points.isEmpty }
                    for emptyGroup in emptyGroups {
                        logInfo("清理空分组: \(emptyGroup.name) (组号: \(emptyGroup.groupNumber))")
                        modelContext.delete(emptyGroup)
                    }
                    if !emptyGroups.isEmpty {
                        try? modelContext.save()
                        logInfo("额外清理了\(emptyGroups.count)个空分组")
                    }
                }

                // 直接更新内存状态，避免调用refreshCurrentRoute()
                await MainActor.run {
                    // 清空配送点列表
                    self.deliveryPoints = []

                    // 重置路线优化状态
                    route.isOptimized = false

                    logInfo("成功清除路线的所有点和\(groupsToDelete.count)个关联分组 - 路线ID: \(route.id)")

                    // 触发UI更新
                    self.objectWillChange.send()

                    // 重置防重复标志
                    self.isSetupInProgress = false
                }
            } catch {
                logError("清除路线点和分组失败: \(error.localizedDescription)")
                await MainActor.run {
                    isSetupInProgress = false
                }
            }
        }
    }

    /// 刷新当前路线数据
    private func refreshCurrentRoute() async {
        // 检查是否已经在加载中，避免重复加载
        if isSetupInProgress {
            logInfo("refreshCurrentRoute - 已有加载操作正在进行，跳过此次调用")
            return
        }

        // 设置防重复标志
        isSetupInProgress = true

        guard let route = currentRoute, let modelContext = self.modelContext else {
            logError("refreshCurrentRoute - 无法刷新路线：当前路线或ModelContext为空")
            isSetupInProgress = false // 重置标志
            return
        }

        do {
            // 先提取路线ID到本地变量
            let routeId = route.id

            logInfo("refreshCurrentRoute - 开始刷新路线数据，路线ID: \(routeId.uuidString)")

            // 重新获取路线对象以刷新其点数据
            let descriptor = FetchDescriptor<Route>(predicate: #Predicate { r in
                r.id == routeId
            })

            if let updatedRoute = try modelContext.fetch(descriptor).first {
                // 记录更新前后的点数变化
                let oldPointCount = route.points.count
                let newPointCount = updatedRoute.points.count

                await MainActor.run {
                    // 更新当前路线
                    self.currentRoute = updatedRoute

                    // 刷新配送点列表
                    self.deliveryPoints = updatedRoute.points.sorted { $0.sort_number < $1.sort_number }

                    // 只有当点数发生变化时才发送通知，减少不必要的UI刷新
                    if oldPointCount != newPointCount {
                        // 发送通知，通知其他组件路线数据已更新
                        NotificationCenter.default.post(
                            name: Notification.Name("RouteDataRefreshed"),
                            object: updatedRoute.id.uuidString
                        )
                    }
                }

                logInfo("refreshCurrentRoute - 刷新路线数据完成 - 路线ID: \(routeId.uuidString), 路线名称: \(updatedRoute.name)")
                logInfo("refreshCurrentRoute - 点数变化: \(oldPointCount) -> \(newPointCount)")

                // 如果点数发生变化，记录详细日志
                if oldPointCount != newPointCount {
                    logInfo("refreshCurrentRoute - 路线点数发生变化，触发UI更新")

                    // 确保UI更新
                    await MainActor.run {
                        self.objectWillChange.send()
                    }
                }
            } else {
                logError("refreshCurrentRoute - 无法找到路线，ID: \(routeId.uuidString)")

                // 如果找不到路线，可能是已被删除，尝试加载最新路线
                let allRoutesDescriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
                let allRoutes = try modelContext.fetch(allRoutesDescriptor)

                if let latestRoute = allRoutes.first {
                    logInfo("refreshCurrentRoute - 找不到当前路线，切换到最新路线: \(latestRoute.name)")

                    await MainActor.run {
                        self.currentRoute = latestRoute
                        self.deliveryPoints = latestRoute.points.sorted { $0.sort_number < $1.sort_number }
                    }
                } else {
                    logError("refreshCurrentRoute - 数据库中没有路线记录")
                }
            }
        } catch {
            logError("refreshCurrentRoute - 刷新路线数据失败: \(error.localizedDescription)")
        }

        // 重置防重复标志
        isSetupInProgress = false
    }

    // MARK: - CLLocationManagerDelegate

    // 添加位置更新阈值和上次更新位置的属性
    private var lastSignificantLocation: CLLocation?
    private let locationUpdateThreshold: Double = 5.0 // 5米阈值
    private let updateIntervalThreshold: TimeInterval = 1.0 // 1秒时间阈值
    private var lastUpdateTimestamp: Date?

    nonisolated func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }

        // 使用 Task 在主线程上调用 @MainActor 方法
        Task { @MainActor in
            // 检查是否满足更新条件
            let shouldUpdate = checkShouldUpdateLocation(location)

            if shouldUpdate {
                logInfo("位置已更新: \(location.coordinate.latitude), \(location.coordinate.longitude)")

                // 更新司机位置
                driverLocation = location.coordinate

                // 如果这是第一次获取位置，将地图视图移动到当前位置
                if let region = cameraPosition.region,
                   region.center.latitude == 0 &&
                    region.center.longitude == 0 {
                    cameraPosition = .region(MKCoordinateRegion(
                        center: location.coordinate,
                        span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
                    ))
                }

                // 更新最后一次有效位置和时间戳
                lastSignificantLocation = location
                lastUpdateTimestamp = Date()
            }
        }
    }

    // 检查是否应该更新位置
    @MainActor
    private func checkShouldUpdateLocation(_ newLocation: CLLocation) -> Bool {
        let currentTime = Date()

        // 第一次更新或初始状态，始终更新
        if lastSignificantLocation == nil || lastUpdateTimestamp == nil {
            return true
        }

        // 检查自上次更新以来的时间
        if let lastTime = lastUpdateTimestamp {
            let timeDifference = currentTime.timeIntervalSince(lastTime)
            // 如果时间间隔太短，不更新
            if timeDifference < updateIntervalThreshold {
                return false
            }
        }

        // 检查移动距离
        if let lastLocation = lastSignificantLocation {
            let distanceMoved = newLocation.distance(from: lastLocation)
            // 如果移动距离小于阈值，不更新
            if distanceMoved < locationUpdateThreshold {
                return false
            }
        }

        return true
    }

    nonisolated func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        // 使用 Task 在主线程上调用 @MainActor 方法
        Task { @MainActor in
            logError("位置更新失败: \(error.localizedDescription)")

            // 如果位置服务被拒绝或禁用，使用默认位置
            if let clError = error as? CLError {
                switch clError.code {
                case .denied, .locationUnknown:
                    logInfo("位置服务被拒绝或不可用，使用默认位置")
                    useFallbackLocation()
                default:
                    break
                }
            }
        }
    }

    nonisolated func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        // 使用 Task 在主线程上调用 @MainActor 方法
        Task { @MainActor in
            switch manager.authorizationStatus {
            case .authorizedWhenInUse, .authorizedAlways:
                logInfo("用户已授权位置权限")
                manager.startUpdatingLocation()
            case .denied, .restricted:
                logInfo("位置权限被拒绝或受限制")
                useFallbackLocation()
            case .notDetermined:
                logInfo("位置权限未确定")
            @unknown default:
                logError("未知的位置权限状态")
            }
        }
    }

    // MARK: - 配送点状态管理

    /// 从分组中移除已完成的配送点
    /// - Parameter point: 已完成状态的配送点
    func removeCompletedPointFromGroup(_ point: DeliveryPoint) async {
        guard let modelContext = modelContext else {
            logError("无法从分组移除已完成配送点：没有ModelContext")
            return
        }

        // 确保点状态为已完成且已分配到组
        guard point.status == DeliveryStatus.completed.rawValue && point.isAssignedToGroup else {
            logInfo("removeCompletedPointFromGroup - 点不是已完成状态或未分配到组，无需从分组移除: \(point.primaryAddress), 状态: \(point.status), 已分配到组: \(point.isAssignedToGroup), 组号: \(point.assignedGroupNumber ?? -1)")
            return
        }

        // 记录原始分组号以便日志记录
        let originalGroupNumber = point.assignedGroupNumber ?? -1
        logInfo("removeCompletedPointFromGroup - 准备从组\(originalGroupNumber)中移除已完成的配送点: \(point.primaryAddress)")

        do {
            // 确保 assignedGroupNumber 不为 nil
            guard let groupNumber = point.assignedGroupNumber else {
                logError("removeCompletedPointFromGroup - 点的 assignedGroupNumber 为 nil，无法查找分组")
                return
            }

            // 查找点所属的分组 - 修复谓词表达式
            let descriptor = FetchDescriptor<DeliveryGroup>(predicate: #Predicate { group in
                group.groupNumber == groupNumber
            })

            logInfo("removeCompletedPointFromGroup - 查询组号\(originalGroupNumber)的分组")
            let groups = try modelContext.fetch(descriptor)
            logInfo("removeCompletedPointFromGroup - 查询结果: 找到\(groups.count)个匹配的分组")

            if let group = groups.first {
                let originalPointCount = group.points.count
                logInfo("removeCompletedPointFromGroup - 找到配送点所属分组: \(group.name), 组内共有\(originalPointCount)个点，准备移除点: \(point.primaryAddress), ID: \(point.id)")

                // 从分组中移除点
                group.points.removeAll { $0.id == point.id }
                let newPointCount = group.points.count

                // 检查点是否成功从组中移除
                if originalPointCount == newPointCount {
                    logError("removeCompletedPointFromGroup - 从分组\(group.name)移除点\(point.primaryAddress)失败，点未从数组中移除")
                } else {
                    logInfo("removeCompletedPointFromGroup - 成功从分组\(group.name)的points数组中移除点，原有\(originalPointCount)个点，现有\(newPointCount)个点")
                }

                // 重置点的分组状态属性
                point.isAssignedToGroup = false
                point.assignedGroupNumber = nil
                logInfo("removeCompletedPointFromGroup - 已重置点\(point.primaryAddress)的分组状态: isAssignedToGroup=false, assignedGroupNumber=nil")

                // 保存更改
                try modelContext.save()
                logInfo("removeCompletedPointFromGroup - 已保存更改到数据库")

                // 再次确认点状态已正确更新
                if point.isAssignedToGroup || point.assignedGroupNumber != nil {
                    logError("removeCompletedPointFromGroup - 数据库保存后点状态检查失败: isAssignedToGroup=\(point.isAssignedToGroup), assignedGroupNumber=\(point.assignedGroupNumber ?? -1)")
                } else {
                    logInfo("removeCompletedPointFromGroup - 数据库保存后点状态检查成功: isAssignedToGroup=\(point.isAssignedToGroup), assignedGroupNumber=\(point.assignedGroupNumber ?? -1)")
                }

                logInfo("removeCompletedPointFromGroup - 已成功从分组 \(group.name) 中移除已完成的配送点: \(point.primaryAddress)")

                // 发送通知以更新UI
                NotificationCenter.default.post(
                    name: Notification.Name("RouteDataChanged"),
                    object: nil
                )
                logInfo("removeCompletedPointFromGroup - 已发送 RouteDataChanged 通知")
            } else {
                logError("removeCompletedPointFromGroup - 未找到配送点所属的分组，groupNumber=\(originalGroupNumber)")

                // 虽然没找到分组，但仍重置点的分组状态属性
                point.isAssignedToGroup = false
                point.assignedGroupNumber = nil
                logInfo("removeCompletedPointFromGroup - 虽未找到分组，但已重置点\(point.primaryAddress)的分组状态: isAssignedToGroup=false, assignedGroupNumber=nil")

                // 保存更改
                try modelContext.save()
                logInfo("removeCompletedPointFromGroup - 已保存点状态更改到数据库")

                // 发送通知以更新UI
                NotificationCenter.default.post(
                    name: Notification.Name("RouteDataChanged"),
                    object: nil
                )
                logInfo("removeCompletedPointFromGroup - 已发送 RouteDataChanged 通知")
            }
        } catch {
            logError("removeCompletedPointFromGroup - 从分组移除已完成配送点失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 路线创建和管理

    /// 提示用户创建路线（如需要）- 此方法已被checkAndCreateDefaultRoute替代，保留以向后兼容
    private func promptForRouteCreationIfNeeded() {
        logInfo("RouteViewModel - promptForRouteCreationIfNeeded 方法已被调用，但功能已被替代")

        // 调用新方法以保持功能一致性
        Task {
            await checkAndCreateDefaultRoute()
        }
    }

    // MARK: - 地址排序方法

    /// 统一的地址排序方法
    /// 🎯 优先使用第三方排序号，然后AI扫描地址保持原始顺序，手动地址实现错误地址优先
    private func sortDeliveryPoints(_ points: [DeliveryPoint], isOptimized: Bool) -> [DeliveryPoint] {
        return points.sorted { point1, point2 in
            // 1. 起点总是最前面
            let point1IsStart = point1.isStartPoint || point1.sort_number == 0
            let point2IsStart = point2.isStartPoint || point2.sort_number == 0

            if point1IsStart && !point2IsStart { return true }
            if !point1IsStart && point2IsStart { return false }

            // 2. 🎯 新逻辑：优先使用第三方排序号
            let point1ThirdParty = point1.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            let point2ThirdParty = point2.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

            let point1HasThirdParty = !point1ThirdParty.isEmpty
            let point2HasThirdParty = !point2ThirdParty.isEmpty

            // 如果都有第三方排序号，按第三方排序号排序
            if point1HasThirdParty && point2HasThirdParty {
                let num1 = extractNumber(from: point1ThirdParty)
                let num2 = extractNumber(from: point2ThirdParty)
                logInfo("RouteViewModel - 第三方排序比较: \(point1ThirdParty)(\(num1)) vs \(point2ThirdParty)(\(num2))")
                return num1 < num2
            }

            // 有第三方排序号的优先排在前面
            if point1HasThirdParty && !point2HasThirdParty {
                logInfo("RouteViewModel - \(point1ThirdParty) 有第三方排序号，优先排序")
                return true
            }
            if !point1HasThirdParty && point2HasThirdParty {
                logInfo("RouteViewModel - \(point2ThirdParty) 有第三方排序号，优先排序")
                return false
            }

            // 3. 都没有第三方排序号时，使用原有逻辑
            let point1IsAIScanned = point1.sourceApp != .manual
            let point2IsAIScanned = point2.sourceApp != .manual

            // 如果都是AI扫描的地址，直接按sort_number排序，不考虑错误状态
            if point1IsAIScanned && point2IsAIScanned {
                if isOptimized {
                    return point1.sorted_number < point2.sorted_number
                } else {
                    return point1.sort_number < point2.sort_number
                }
            }

            // 如果都是手动输入的地址，使用错误地址优先逻辑
            if !point1IsAIScanned && !point2IsAIScanned {
                let point1HasError = hasAddressError(point1)
                let point2HasError = hasAddressError(point2)

                if point1HasError && !point2HasError { return true }
                if !point1HasError && point2HasError { return false }
            }

            // 4. 根据路线是否优化选择排序字段
            if isOptimized {
                return point1.sorted_number < point2.sorted_number
            } else {
                return point1.sort_number < point2.sort_number
            }
        }
    }

    /// 提取第三方排序号中的数字部分
    private func extractNumber(from string: String) -> Int {
        let numbers = string.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return Int(numbers) ?? Int.max
    }

    /// 检查地址是否有错误
    private func hasAddressError(_ point: DeliveryPoint) -> Bool {
        // 检查地理编码警告
        if let warning = point.geocodingWarning, !warning.isEmpty {
            return true
        }

        // 检查位置验证状态 - 只有明确的invalid状态才算有问题
        let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
        return validationStatus == .invalid
    }

    // MARK: - 路线连接功能（简化版本）
    func updateRouteConnections() {
        logInfo("🔍 updateRouteConnections - 当前选中点数: \(selectedPoints.count)")

        // 清除现有路线
        routeConnections.removeAll()
        selectedPointsRoute = nil

        // 如果选中的地点少于2个，不绘制路线
        guard selectedPoints.count >= 2 else {
            logInfo("🔍 选中点数不足2个，不绘制路线")
            return
        }

        // 🚨 简化路线计算逻辑
        calculateSimpleRoute()
    }

    // MARK: - 简化的路线计算方法
    private func calculateSimpleRoute() {
        // 获取选中点的坐标（按选中顺序）
        let coordinates = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }?.coordinate
        }

        guard coordinates.count >= 2 else {
            logInfo("🚨 坐标数不足，无法计算路线")
            return
        }

        logInfo("✅ 开始计算简化路线，坐标数: \(coordinates.count)")

        // 使用DirectionsAPIManager计算路线
        DirectionsAPIManager.shared.calculateMultipleRoutes(waypoints: coordinates) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }

                switch result {
                case .success(let routes):
                    self.logInfo("✅ 路线计算成功，获得\(routes.count)条路线")
                    self.createSimpleRouteConnections(from: routes)

                case .failure(let error):
                    self.logInfo("🚨 路线计算失败: \(error.localizedDescription)")
                    // 简单重试一次
                    self.retrySimpleRoute(coordinates: coordinates)
                }
            }
        }
    }

    // 创建简化的路线连接
    private func createSimpleRouteConnections(from routes: [MKRoute]) {
        routeConnections.removeAll()

        guard !routes.isEmpty else {
            logInfo("🚨 没有有效路线，无法创建连接")
            return
        }

        // 合并所有路线的坐标点
        var allCoordinates: [CLLocationCoordinate2D] = []

        for route in routes {
            let polyline = route.polyline
            let pointCount = polyline.pointCount
            let routeCoordinates = UnsafeMutablePointer<CLLocationCoordinate2D>.allocate(capacity: pointCount)
            polyline.getCoordinates(routeCoordinates, range: NSRange(location: 0, length: pointCount))

            // 添加坐标点（避免重复）
            for i in 0..<pointCount {
                let coord = routeCoordinates[i]
                if allCoordinates.isEmpty ||
                   (allCoordinates.last!.latitude != coord.latitude ||
                    allCoordinates.last!.longitude != coord.longitude) {
                    allCoordinates.append(coord)
                }
            }

            routeCoordinates.deallocate()
        }

        // 创建路线连接
        if allCoordinates.count >= 2 {
            let connection = RouteConnection(
                startCoordinate: allCoordinates.first!,
                endCoordinate: allCoordinates.last!,
                routeCoordinates: allCoordinates
            )

            routeConnections.append(connection)
            logInfo("✅ 路线连接创建成功，坐标点数: \(allCoordinates.count)")
        }
    }

    // 简单重试机制
    private func retrySimpleRoute(coordinates: [CLLocationCoordinate2D]) {
        logInfo("🔄 重试路线计算...")

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.calculateSimpleRoute()
        }
    }

    // 🚨 强制使用实际路线：完全禁止直线连接
    private func calculateRealRouteOnly() {
        let selectedLocations = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }

        guard selectedLocations.count >= 2 else {
            logInfo("选中位置数量不足，无法计算路线")
            return
        }

        logInfo("🚨 强制实际路线计算 - 选中位置数: \(selectedLocations.count)")

        // 记录选中点的顺序
        for (index, location) in selectedLocations.enumerated() {
            logInfo("路线点 \(index + 1): \(location.primaryAddress)")
        }

        // 🚨 严格要求：立即开始实际路线计算，不使用任何直线连接
        logInfo("🚨 开始强制实际路线计算，禁止直线连接")
        startMandatoryRealRouteCalculation(locations: selectedLocations)
    }

    // 🚨 强制实际路线计算 - 无条件执行，不允许降级
    private func startMandatoryRealRouteCalculation(locations: [DeliveryPoint]) {
        logInfo("🚨 开始强制实际路线计算 - 无条件执行")

        // 立即开始计算，不延迟
        calculateMandatoryRealRoute(locations: locations)
    }

    // 判断是否应该计算真实路线
    private func shouldCalculateRealRoute(for locations: [DeliveryPoint]) -> Bool {
        // 检查API状态
        let apiManager = DirectionsAPIManager.shared
        let status = apiManager.getRequestStatus()

        // 如果API使用量过高，跳过
        if status.current >= status.max - 10 {
            logInfo("API使用量过高，跳过真实路线计算")
            return false
        }

        // 如果点数太多，只计算前几段
        if locations.count > 6 {
            logInfo("点数较多(\(locations.count))，将使用采样计算")
        }

        return true
    }

    // 🚨 强制计算实际路线 - 不允许降级到直线连接
    private func calculateMandatoryRealRoute(locations: [DeliveryPoint]) {
        let coordinates = locations.map { $0.coordinate }

        logInfo("🚨 强制实际路线计算 - 包含所有 \(coordinates.count) 个选中点")

        // 🚨 无论点数多少，都必须计算实际路线，不设置点数限制
        if coordinates.count > 8 {
            // 超过8个点时，使用更智能的分段策略
            calculateAdvancedSegmentedRoute(coordinates: coordinates)
        } else if coordinates.count > 4 {
            // 4-8个点时，使用标准分段策略
            calculateMandatorySegmentedRoute(coordinates: coordinates)
        } else {
            // 点数较少，直接计算完整路线
            DirectionsAPIManager.shared.calculateMultipleRoutes(waypoints: coordinates) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }

                    switch result {
                    case .success(let routes):
                        self.logInfo("🚨 强制实际路线计算成功，获得\(routes.count)条路线")
                        self.updateToRealRoute(routes: routes, originalCoordinates: coordinates)

                    case .failure(let error):
                        self.logInfo("🚨 强制实际路线计算失败: \(error.localizedDescription)")
                        // 🚨 严格禁止：不允许降级到直线连接，重试计算
                        self.retryMandatoryRealRoute(coordinates: coordinates, retryCount: 1)
                    }
                }
            }
        }
    }

    // 🚨 强制分段计算路线，不允许降级到直线连接
    private func calculateMandatorySegmentedRoute(coordinates: [CLLocationCoordinate2D]) {
        logInfo("🚨 强制分段计算路线 - 总点数: \(coordinates.count)")

        // 将路线分成多个段，每段最多4个点（包含重叠点）
        var segments: [[CLLocationCoordinate2D]] = []
        let maxPointsPerSegment = 4

        for i in stride(from: 0, to: coordinates.count - 1, by: maxPointsPerSegment - 1) {
            let endIndex = min(i + maxPointsPerSegment - 1, coordinates.count - 1)
            let segment = Array(coordinates[i...endIndex])
            segments.append(segment)

            logInfo("🚨 创建强制路线段: 从点 \(i+1) 到点 \(endIndex+1), 包含 \(segment.count) 个点")
        }

        logInfo("🚨 总共创建了 \(segments.count) 个强制路线段")

        // 计算每个段的路线
        calculateMandatorySegmentRoutes(segments: segments, allCoordinates: coordinates)
    }

    // 🚨 计算强制分段路线
    private func calculateMandatorySegmentRoutes(segments: [[CLLocationCoordinate2D]], allCoordinates: [CLLocationCoordinate2D]) {
        // 🚨 关键修复：使用字典保存路线段，确保按原始顺序拼接
        var segmentRoutes: [Int: [MKRoute]] = [:]
        let group = DispatchGroup()

        for (index, segment) in segments.enumerated() {
            guard segment.count >= 2 else { continue }

            group.enter()
            DirectionsAPIManager.shared.calculateMultipleRoutes(waypoints: segment) { result in
                defer { group.leave() }

                switch result {
                case .success(let routes):
                    DispatchQueue.main.async {
                        // 🚨 关键修复：按段索引保存路线，而不是直接添加到数组
                        segmentRoutes[index] = routes
                        self.logInfo("🚨 强制路线段 \(index+1) 计算成功，获得 \(routes.count) 条路线")
                    }
                case .failure(let error):
                    self.logInfo("🚨 强制路线段 \(index+1) 计算失败: \(error.localizedDescription)")
                }
            }
        }

        group.notify(queue: .main) {
            // 🚨 关键修复：按原始段顺序重新组装路线
            var orderedRoutes: [MKRoute] = []
            for i in 0..<segments.count {
                if let routes = segmentRoutes[i] {
                    orderedRoutes.append(contentsOf: routes)
                    self.logInfo("🚨 添加路线段 \(i+1) 的 \(routes.count) 条路线到最终路线")
                }
            }

            if !orderedRoutes.isEmpty {
                self.logInfo("🚨 所有强制路线段计算完成，按顺序组装了 \(orderedRoutes.count) 条路线")
                self.updateToRealRoute(routes: orderedRoutes, originalCoordinates: allCoordinates)
            } else {
                self.logInfo("🚨 所有强制路线段计算失败，重试强制计算")
                self.retryMandatoryRealRoute(coordinates: allCoordinates, retryCount: 1)
            }
        }
    }

    // 🚨 高级分段路线计算 - 处理超过8个点的情况
    private func calculateAdvancedSegmentedRoute(coordinates: [CLLocationCoordinate2D]) {
        logInfo("🚨 高级分段路线计算 - 总点数: \(coordinates.count)")

        // 将路线分成更小的段，每段最多3个点（包含重叠点）
        var segments: [[CLLocationCoordinate2D]] = []
        let maxPointsPerSegment = 3

        for i in stride(from: 0, to: coordinates.count - 1, by: maxPointsPerSegment - 1) {
            let endIndex = min(i + maxPointsPerSegment - 1, coordinates.count - 1)
            let segment = Array(coordinates[i...endIndex])
            segments.append(segment)

            logInfo("🚨 创建高级路线段: 从点 \(i+1) 到点 \(endIndex+1), 包含 \(segment.count) 个点")
        }

        logInfo("🚨 总共创建了 \(segments.count) 个高级路线段")

        // 计算每个段的路线
        calculateAdvancedSegmentRoutes(segments: segments, allCoordinates: coordinates)
    }

    // 🚨 计算高级分段路线
    private func calculateAdvancedSegmentRoutes(segments: [[CLLocationCoordinate2D]], allCoordinates: [CLLocationCoordinate2D]) {
        // 🚨 关键修复：使用字典保存路线段，确保按原始顺序拼接
        var segmentRoutes: [Int: [MKRoute]] = [:]
        let group = DispatchGroup()
        var completedSegments = 0

        for (index, segment) in segments.enumerated() {
            guard segment.count >= 2 else { continue }

            group.enter()

            // 添加延迟以避免API限制
            let delay = Double(index) * 0.2
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                DirectionsAPIManager.shared.calculateMultipleRoutes(waypoints: segment) { result in
                    defer { group.leave() }

                    switch result {
                    case .success(let routes):
                        DispatchQueue.main.async {
                            // 🚨 关键修复：按段索引保存路线，而不是直接添加到数组
                            segmentRoutes[index] = routes
                            completedSegments += 1
                            self.logInfo("🚨 高级路线段 \(index+1) 计算成功，获得 \(routes.count) 条路线 (完成: \(completedSegments)/\(segments.count))")
                        }
                    case .failure(let error):
                        self.logInfo("🚨 高级路线段 \(index+1) 计算失败: \(error.localizedDescription)")
                    }
                }
            }
        }

        group.notify(queue: .main) {
            // 🚨 关键修复：按原始段顺序重新组装路线
            var orderedRoutes: [MKRoute] = []
            for i in 0..<segments.count {
                if let routes = segmentRoutes[i] {
                    orderedRoutes.append(contentsOf: routes)
                    self.logInfo("🚨 添加高级路线段 \(i+1) 的 \(routes.count) 条路线到最终路线")
                }
            }

            if !orderedRoutes.isEmpty {
                self.logInfo("🚨 所有高级路线段计算完成，按顺序组装了 \(orderedRoutes.count) 条路线")
                self.updateToRealRoute(routes: orderedRoutes, originalCoordinates: allCoordinates)
            } else {
                self.logInfo("🚨 所有高级路线段计算失败，重试强制计算")
                self.retryMandatoryRealRoute(coordinates: allCoordinates, retryCount: 1)
            }
        }
    }

    // 🚨 强制重试实际路线计算
    private func retryMandatoryRealRoute(coordinates: [CLLocationCoordinate2D], retryCount: Int) {
        guard retryCount <= 3 else {
            logInfo("🚨 强制路线计算重试次数已达上限，清除路线连接并显示错误信息")
            // 🚨 关键修复：确保清除所有路线连接
            routeConnections.removeAll()
            selectedPointsRoute = nil
            showRouteCalculationError()
            return
        }

        logInfo("🚨 开始第 \(retryCount) 次强制路线重试")

        // 延迟重试，避免API限制
        DispatchQueue.main.asyncAfter(deadline: .now() + Double(retryCount)) {
            DirectionsAPIManager.shared.calculateMultipleRoutes(waypoints: coordinates) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }

                    switch result {
                    case .success(let routes):
                        self.logInfo("🚨 强制路线重试成功，获得\(routes.count)条路线")
                        self.updateToRealRoute(routes: routes, originalCoordinates: coordinates)

                    case .failure(let error):
                        self.logInfo("🚨 强制路线重试失败: \(error.localizedDescription)")
                        self.retryMandatoryRealRoute(coordinates: coordinates, retryCount: retryCount + 1)
                    }
                }
            }
        }
    }

    // 🚨 显示路线计算错误信息
    private func showRouteCalculationError() {
        // 🚨 关键修复：清除所有路线连接，防止显示直线
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 清除路线连接，防止显示直线
            self.routeConnections.removeAll()
            self.selectedPointsRoute = nil

            // 显示用户友好的错误信息
            NotificationCenter.default.post(
                name: Notification.Name("MandatoryRouteCalculationFailed"),
                object: nil,
                userInfo: [
                    "message": "无法计算实际路线，请检查网络连接或稍后重试。"
                ]
            )

            self.logInfo("🚨 路线计算失败，已清除所有路线连接")
        }
    }

    // 更新为真实路线，确保包含所有原始选中点
    private func updateToRealRoute(routes: [MKRoute], originalCoordinates: [CLLocationCoordinate2D]) {
        var allRoutePoints: [CLLocationCoordinate2D] = []

        for (index, route) in routes.enumerated() {
            let coordinates = route.polyline.coordinates

            // 如果不是第一段，跳过第一个点以避免重复
            let startIndex = (index == 0) ? 0 : 1
            if startIndex < coordinates.count {
                allRoutePoints.append(contentsOf: coordinates[startIndex...])
            }
        }

        if !allRoutePoints.isEmpty {
            logInfo("更新为真实路线，总坐标点数: \(allRoutePoints.count)")

            // 🚨 关键修复：验证路线点数量，防止意外的直线连接
            if allRoutePoints.count < 3 && originalCoordinates.count > 2 {
                logInfo("🚨 警告：路线点数量过少(\(allRoutePoints.count))，可能不是真实路线，重新计算")
                retryMandatoryRealRoute(coordinates: originalCoordinates, retryCount: 1)
                return
            }

            // 验证所有原始选中点是否都在路线附近
            let missingPoints = findMissingPoints(originalCoordinates: originalCoordinates, routePoints: allRoutePoints)

            if !missingPoints.isEmpty {
                logInfo("发现 \(missingPoints.count) 个选中点可能被跳过，使用混合路线")
                // 创建混合路线，确保包含所有选中点
                let hybridRoute = createHybridRoute(realRoutePoints: allRoutePoints, originalPoints: originalCoordinates)

                // 🚨 验证混合路线是否有效
                if hybridRoute.count >= 3 || originalCoordinates.count == 2 {
                    // 使用平滑动画过渡到混合路线
                    withAnimation(.easeInOut(duration: 0.4)) {
                        createRouteConnections(from: hybridRoute)
                    }
                } else {
                    logInfo("🚨 混合路线无效，重新计算")
                    retryMandatoryRealRoute(coordinates: originalCoordinates, retryCount: 1)
                }
            } else {
                // 所有点都包含在真实路线中，使用平滑动画过渡
                withAnimation(.easeInOut(duration: 0.4)) {
                    createRouteConnections(from: allRoutePoints)
                }
            }

            logInfo("路线更新完成，确保包含所有 \(originalCoordinates.count) 个选中点")
        } else {
            logInfo("🚨 真实路线为空，清除路线连接而不是使用直线连接")
            // 🚨 关键修复：清除路线连接，不允许回退到直线连接
            routeConnections.removeAll()
            selectedPointsRoute = nil
            showRouteCalculationError()
        }
    }

    // 查找可能被跳过的选中点
    private func findMissingPoints(originalCoordinates: [CLLocationCoordinate2D], routePoints: [CLLocationCoordinate2D]) -> [CLLocationCoordinate2D] {
        let threshold = 100.0 // 100米阈值
        var missingPoints: [CLLocationCoordinate2D] = []

        for originalPoint in originalCoordinates {
            let isNearRoute = routePoints.contains { routePoint in
                let distance = CLLocation(latitude: originalPoint.latitude, longitude: originalPoint.longitude)
                    .distance(from: CLLocation(latitude: routePoint.latitude, longitude: routePoint.longitude))
                return distance <= threshold
            }

            if !isNearRoute {
                missingPoints.append(originalPoint)
            }
        }

        return missingPoints
    }

    // 🚨 创建混合路线，确保包含所有选中点，但不使用直线连接
    private func createHybridRoute(realRoutePoints: [CLLocationCoordinate2D], originalPoints: [CLLocationCoordinate2D]) -> [CLLocationCoordinate2D] {
        logInfo("🚨 创建混合路线 - 使用实际路线点，严格禁止直线连接")

        // 🚨 严格要求：即使有缺失点，也使用实际路线点，不降级到直线
        // 如果实际路线点不为空且数量合理，优先使用实际路线
        if !realRoutePoints.isEmpty {
            // 🚨 关键修复：验证实际路线点数量，防止意外的直线连接
            if realRoutePoints.count < 3 && originalPoints.count > 2 {
                logInfo("🚨 实际路线点数量过少(\(realRoutePoints.count))，可能不是真实路线")
                return [] // 返回空数组，让上层处理
            }
            return realRoutePoints
        } else {
            // 如果实际路线为空，返回空数组而不是重新计算
            logInfo("🚨 实际路线为空，返回空数组")
            return []
        }
    }

    private func shouldUseSmartRouteCalculation() -> Bool {
        // 检查API请求状态
        let status = DirectionsAPIManager.shared.getRequestStatus()

        // 如果接近API限制，使用智能策略
        if status.current >= status.max - 10 {
            return true
        }

        // 如果选中点数过多（超过8个），使用智能策略
        if selectedPoints.count > 8 {
            return true
        }

        return false
    }

    private func calculateSmartRoute() {
        let selectedLocations = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }

        guard selectedLocations.count >= 2 else {
            logInfo("选中位置数量不足，无法计算路线")
            return
        }

        logInfo("智能路线计算 - 选中位置数: \(selectedLocations.count)")

        // 对于多点路线，使用采样策略减少API调用
        if selectedLocations.count > 4 {
            calculateSampledRoute(locations: selectedLocations)
        } else {
            // 少于4个点，正常计算
            calculateActualRoute()
        }
    }

    private func calculateSampledRoute(locations: [DeliveryPoint]) {
        logInfo("使用采样路线计算 - 总点数: \(locations.count)")

        // 采样策略：只计算关键点之间的路线
        var keyPoints: [DeliveryPoint] = []

        // 始终包含起点和终点
        keyPoints.append(locations.first!)

        // 如果有中间点，采样一些关键点
        if locations.count > 2 {
            let middlePoints = Array(locations[1..<locations.count-1])
            let sampleSize = min(3, middlePoints.count) // 最多采样3个中间点

            if sampleSize < middlePoints.count {
                // 均匀采样
                let step = middlePoints.count / sampleSize
                for i in 0..<sampleSize {
                    let index = i * step
                    if index < middlePoints.count {
                        keyPoints.append(middlePoints[index])
                    }
                }
            } else {
                keyPoints.append(contentsOf: middlePoints)
            }
        }

        keyPoints.append(locations.last!)

        logInfo("采样后的关键点数: \(keyPoints.count)")

        // 计算关键点之间的路线
        let waypoints = keyPoints.map { MKMapItem(placemark: MKPlacemark(coordinate: $0.coordinate)) }

        if waypoints.count > 2 {
            calculateMultiWaypointRoute(waypoints: waypoints)
        } else {
            // 只有两个点，直接计算
            let source = waypoints[0].placemark.coordinate
            let destination = waypoints[1].placemark.coordinate

            DirectionsAPIManager.shared.calculateRoute(from: source, to: destination) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }

                    switch result {
                    case .success(let route):
                        self.selectedPointsRoute = route
                        self.createRouteConnections(from: route)
                    case .failure(let error):
                        // 🚨 严格禁止：不允许降级到直线连接，重试计算
                        self.logInfo("🚨 两点路线计算失败，重试: \(error.localizedDescription)")
                        self.retryMandatoryRealRoute(coordinates: [source, destination], retryCount: 1)
                    }
                }
            }
        }
    }

    private func calculateActualRoute() {
        let selectedLocations = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }

        guard selectedLocations.count >= 2 else {
            logInfo("选中位置数量不足，无法计算路线")
            return
        }

        logInfo("计算实际路线 - 选中位置数: \(selectedLocations.count)")

        // 记录选中点的顺序
        for (index, location) in selectedLocations.enumerated() {
            logInfo("路线点 \(index + 1): \(location.primaryAddress)")
        }

        let waypoints = selectedLocations.map { MKMapItem(placemark: MKPlacemark(coordinate: $0.coordinate)) }

        let request = MKDirections.Request()
        request.source = waypoints.first
        request.destination = waypoints.last

        // 如果有中间点，需要分段计算
        if waypoints.count > 2 {
            logInfo("多点路线计算 - 总点数: \(waypoints.count)")
            calculateMultiWaypointRoute(waypoints: waypoints)
        } else {
            // 只有起点和终点
            logInfo("两点路线计算")

            let source = waypoints[0].placemark.coordinate
            let destination = waypoints[1].placemark.coordinate

            DirectionsAPIManager.shared.calculateRoute(from: source, to: destination) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }

                    switch result {
                    case .success(let route):
                        self.logInfo("两点路线计算成功，距离: \(route.distance/1000) 公里")
                        self.selectedPointsRoute = route
                        self.createRouteConnections(from: route)

                    case .failure(let error):
                        self.logInfo("🚨 两点路线计算失败: \(error.localizedDescription)")
                        // 🚨 严格禁止：不允许降级到直线连接，重试计算
                        self.retryMandatoryRealRoute(coordinates: [source, destination], retryCount: 1)
                    }
                }
            }
        }
    }

    private func calculateMultiWaypointRoute(waypoints: [MKMapItem]) {
        logInfo("开始计算多点路线，总共\(waypoints.count)个点")

        // 检查API请求状态
        let apiManager = DirectionsAPIManager.shared
        let status = apiManager.getRequestStatus()

        if status.current >= status.max - 5 { // 留5个请求的缓冲
            logInfo("🚨 API请求接近限制 (\(status.current)/\(status.max))，等待后重试")
            // 🚨 严格禁止：不使用直线连接，等待后重试
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.calculateMultiWaypointRoute(waypoints: waypoints)
            }
            return
        }

        // 转换为坐标数组
        let coordinates = waypoints.map { $0.placemark.coordinate }

        // 使用API管理器进行批量计算
        apiManager.calculateMultipleRoutes(waypoints: coordinates) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }

                switch result {
                case .success(let routes):
                    self.logInfo("批量路线计算成功，获得\(routes.count)条路线")
                    self.processCalculatedRoutes(routes)

                case .failure(let error):
                    self.logInfo("🚨 批量路线计算失败: \(error.localizedDescription)")
                    // 🚨 严格禁止：不允许降级到直线连接，重试计算
                    let coordinates = waypoints.map { $0.placemark.coordinate }
                    self.retryMandatoryRealRoute(coordinates: coordinates, retryCount: 1)
                }
            }
        }
    }

    private func processCalculatedRoutes(_ routes: [MKRoute]) {
        logInfo("🔍 processCalculatedRoutes - 开始处理计算的路线")
        logInfo("🔍 收到路线段数: \(routes.count)")
        logInfo("🔍 期望路线段数: \(selectedPoints.count - 1)")

        // 获取选中的 DeliveryPoint 对象
        let selectedDeliveryPoints = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }

        // 🚨 关键验证：检查路线段数是否完整
        let expectedSegments = selectedDeliveryPoints.count - 1
        if routes.count < expectedSegments {
            logInfo("🚨 路线段不完整 (\(routes.count)/\(expectedSegments))，重新计算")
            let coordinates = selectedDeliveryPoints.map { $0.coordinate }
            retryMandatoryRealRoute(coordinates: coordinates, retryCount: 1)
            return
        }

        var allRoutePoints: [CLLocationCoordinate2D] = []

        for (index, route) in routes.enumerated() {
            let coordinates = route.polyline.coordinates
            logInfo("🔍 路线段 \(index+1): 距离 \(String(format: "%.1f", route.distance/1000))公里, 坐标点数 \(coordinates.count)")

            // 如果不是第一段，跳过第一个点以避免重复
            let startIndex = (index == 0) ? 0 : 1
            if startIndex < coordinates.count {
                allRoutePoints.append(contentsOf: coordinates[startIndex...])
                logInfo("🔍 添加路线段 \(index+1)，实际添加坐标点数: \(coordinates.count - startIndex)")
            } else {
                logInfo("🚨 路线段 \(index+1) 坐标点数不足，跳过")
            }
        }

        logInfo("🔍 合并后总坐标点数: \(allRoutePoints.count)")

        // 🚨 验证合并后的路线是否有效
        if allRoutePoints.count < 3 && selectedDeliveryPoints.count > 2 {
            logInfo("🚨 合并后路线坐标点过少，可能不是真实路线，重新计算")
            let coordinates = selectedDeliveryPoints.map { $0.coordinate }
            retryMandatoryRealRoute(coordinates: coordinates, retryCount: 1)
            return
        }

        if !allRoutePoints.isEmpty {
            logInfo("✅ 创建路线连接，总坐标点数: \(allRoutePoints.count)")
            createRouteConnections(from: allRoutePoints)
        } else {
            logInfo("🚨 没有有效的路线点，重新计算")
            let coordinates = selectedDeliveryPoints.map { $0.coordinate }
            retryMandatoryRealRoute(coordinates: coordinates, retryCount: 1)
        }
    }

    // 🚨 已移除 createStraightLineConnections 方法
    // 严格禁止使用直线连接作为降级方案

    private func createRouteConnections(from route: MKRoute) {
        let coordinates = route.polyline.coordinates
        logInfo("🔍 从MKRoute创建路线连接")
        logInfo("🔍 MKRoute.distance: \(String(format: "%.1f", route.distance/1000))公里")
        logInfo("🔍 MKRoute.polyline.pointCount: \(route.polyline.pointCount)")
        logInfo("🔍 提取的坐标点数: \(coordinates.count)")

        // 🔍 详细检查前几个坐标点
        if coordinates.count > 0 {
            logInfo("🔍 MKRoute第1个坐标: (\(String(format: "%.6f", coordinates[0].latitude)), \(String(format: "%.6f", coordinates[0].longitude)))")
            if coordinates.count > 1 {
                logInfo("🔍 MKRoute第2个坐标: (\(String(format: "%.6f", coordinates[1].latitude)), \(String(format: "%.6f", coordinates[1].longitude)))")
            }
            if coordinates.count > 2 {
                logInfo("🔍 MKRoute第3个坐标: (\(String(format: "%.6f", coordinates[2].latitude)), \(String(format: "%.6f", coordinates[2].longitude)))")
            }
            if coordinates.count > 3 {
                logInfo("🔍 MKRoute最后一个坐标: (\(String(format: "%.6f", coordinates.last!.latitude)), \(String(format: "%.6f", coordinates.last!.longitude)))")
            }
        }

        createRouteConnections(from: coordinates)
    }

    private func createRouteConnections(from coordinates: [CLLocationCoordinate2D]) {
        logInfo("🔍 创建路线连接 - 输入坐标点数: \(coordinates.count), 选中点数: \(selectedPoints.count)")

        // 🔍 详细日志：显示前几个和后几个坐标点
        if coordinates.count > 0 {
            logInfo("🔍 起始坐标: (\(String(format: "%.6f", coordinates.first!.latitude)), \(String(format: "%.6f", coordinates.first!.longitude)))")
            if coordinates.count > 1 {
                logInfo("🔍 结束坐标: (\(String(format: "%.6f", coordinates.last!.latitude)), \(String(format: "%.6f", coordinates.last!.longitude)))")
            }
            if coordinates.count > 2 {
                logInfo("🔍 中间坐标点数: \(coordinates.count - 2)")
            }
        }

        routeConnections.removeAll()

        // 🚨 严格验证：防止在多选模式下创建直线连接
        if selectedPoints.count > 2 && coordinates.count < 3 {
            logInfo("🚨 多选模式下路线计算失败，禁止创建直线连接")
            return
        }

        // 🚨 严格验证：对于两个选中点的情况，确保有足够的路线坐标点
        if selectedPoints.count == 2 && coordinates.count < 2 {
            logInfo("🚨 两点模式下坐标点数量不足，跳过路线创建")
            return
        }

        // 🚨 严格验证：如果只有两个坐标点但选中了多个点，这是直线连接，应该禁止
        if coordinates.count == 2 && selectedPoints.count > 2 {
            logInfo("🚨 检测到潜在的直线连接，禁止创建")
            return
        }

        // 🚨 临时禁用严格验证，用于调试
        let validationResult = isValidRouteCoordinates(coordinates)
        logInfo("🔍 路线坐标验证结果: \(validationResult)")
        if !validationResult {
            logInfo("🚨 路线坐标验证失败，但继续创建以便调试")
            // 临时注释掉return，继续创建路线以便调试
            // return
        }

        // 🚨 关键修复：移除重复访问的坐标点，避免往返重复
        var cleanedCoordinates: [CLLocationCoordinate2D] = []
        var visitedCoordinates: Set<String> = []

        logInfo("🔍 开始清理重复坐标，原始坐标数: \(coordinates.count)")

        for (index, coordinate) in coordinates.enumerated() {
            // 创建坐标的唯一标识符（保留6位小数精度）
            let coordKey = String(format: "%.6f,%.6f", coordinate.latitude, coordinate.longitude)

            // 第一个坐标（起点）总是添加
            if index == 0 {
                cleanedCoordinates.append(coordinate)
                visitedCoordinates.insert(coordKey)
                logInfo("🔍 添加起点: \(coordKey)")
            }
            // 其他坐标只有在未访问过时才添加
            else if !visitedCoordinates.contains(coordKey) {
                cleanedCoordinates.append(coordinate)
                visitedCoordinates.insert(coordKey)
                logInfo("🔍 添加新坐标: \(coordKey)")
            } else {
                logInfo("🚨 跳过重复坐标: \(coordKey) (已在索引 \(index) 处访问过)")
            }
        }

        logInfo("🔍 坐标清理完成，清理前: \(coordinates.count)，清理后: \(cleanedCoordinates.count)")

        // 🚨 强化回环检测：检查末尾多个点是否接近起点
        if cleanedCoordinates.count > 2 {
            let firstPoint = cleanedCoordinates.first!

            // 获取选中点的坐标，用于验证是否应该有回环
            let selectedLocations = selectedPointsOrder.compactMap { pointId in
                deliveryPoints.first { $0.id == pointId }
            }

            // 检查用户选择的起点和终点是否相同
            let userStartPoint = selectedLocations.first
            let userEndPoint = selectedLocations.last
            let userWantsLoop = userStartPoint?.id == userEndPoint?.id

            if !userWantsLoop {
                // 🚨 强化逻辑：检查末尾最多5个点，移除所有接近起点的点
                var pointsToRemove = 0
                let maxPointsToCheck = min(5, cleanedCoordinates.count - 1)

                for i in 1...maxPointsToCheck {
                    let checkIndex = cleanedCoordinates.count - i
                    let checkPoint = cleanedCoordinates[checkIndex]

                    let distance = CLLocation(latitude: firstPoint.latitude, longitude: firstPoint.longitude)
                        .distance(from: CLLocation(latitude: checkPoint.latitude, longitude: checkPoint.longitude))

                    // 如果距离起点很近（小于100米），标记为需要移除
                    if distance < 100 {
                        pointsToRemove = i
                        logInfo("🚨 检测到异常回环点 \(checkIndex)，距离起点: \(String(format: "%.1f", distance))米")
                    } else {
                        // 一旦找到距离起点较远的点，停止检查
                        break
                    }
                }

                if pointsToRemove > 0 {
                    // 移除末尾的异常回环点
                    cleanedCoordinates = Array(cleanedCoordinates.dropLast(pointsToRemove))
                    logInfo("🚨 移除 \(pointsToRemove) 个异常回环点，最终坐标点数: \(cleanedCoordinates.count)")
                }
            }
        }

        // 获取选中点的坐标，用于检测异常的回环连接
        let selectedLocations = selectedPointsOrder.compactMap { pointId in
            deliveryPoints.first { $0.id == pointId }
        }

        let _ = selectedLocations.first?.coordinate
        let _ = selectedLocations.last?.coordinate

        logInfo("🔍 路线连接创建 - 选中点数: \(selectedLocations.count), 清理后坐标数: \(cleanedCoordinates.count)")
        if let first = selectedLocations.first {
            logInfo("🔍 用户选择的第一个点: \(first.primaryAddress)")
        }
        if let last = selectedLocations.last {
            logInfo("🔍 用户选择的最后一个点: \(last.primaryAddress)")
        }
        if let firstCoord = cleanedCoordinates.first {
            logInfo("🔍 路线起点坐标: (\(String(format: "%.6f", firstCoord.latitude)), \(String(format: "%.6f", firstCoord.longitude)))")
        }
        if let lastCoord = cleanedCoordinates.last {
            logInfo("🔍 路线终点坐标: (\(String(format: "%.6f", lastCoord.latitude)), \(String(format: "%.6f", lastCoord.longitude)))")
        }

        // 🚨 关键修复：创建单个完整的路线连接，而不是分割成多个小段
        guard cleanedCoordinates.count >= 2 else {
            logInfo("🚨 清理后坐标点数量不足，无法创建路线连接")
            return
        }

        let startCoord = cleanedCoordinates.first!
        let endCoord = cleanedCoordinates.last!

        logInfo("🔍 创建完整路线连接:")
        logInfo("🔍 起点: (\(String(format: "%.6f", startCoord.latitude)), \(String(format: "%.6f", startCoord.longitude)))")
        logInfo("🔍 终点: (\(String(format: "%.6f", endCoord.latitude)), \(String(format: "%.6f", endCoord.longitude)))")
        logInfo("🔍 路线坐标点数: \(cleanedCoordinates.count)")

        // 🚨 使用新的初始化方法，传递完整的路线坐标
        let connection = RouteConnection(
            startCoordinate: startCoord,
            endCoordinate: endCoord,
            routeCoordinates: cleanedCoordinates
        )

        routeConnections.append(connection)

        logInfo("✅ 完整路线连接创建成功 - 坐标点数: \(connection.routeCoordinates.count), 是否为实际路线: \(connection.isRealRoute)")
        logInfo("✅ 路线总距离: \(String(format: "%.1f", connection.totalDistance/1000))公里")

        // 🔍 详细调试：输出前几个和后几个坐标点
        if connection.routeCoordinates.count > 0 {
            logInfo("🔍 路线第1个坐标: (\(String(format: "%.6f", connection.routeCoordinates[0].latitude)), \(String(format: "%.6f", connection.routeCoordinates[0].longitude)))")
            if connection.routeCoordinates.count > 1 {
                logInfo("🔍 路线第2个坐标: (\(String(format: "%.6f", connection.routeCoordinates[1].latitude)), \(String(format: "%.6f", connection.routeCoordinates[1].longitude)))")
            }
            if connection.routeCoordinates.count > 2 {
                logInfo("🔍 路线第3个坐标: (\(String(format: "%.6f", connection.routeCoordinates[2].latitude)), \(String(format: "%.6f", connection.routeCoordinates[2].longitude)))")
            }
            if connection.routeCoordinates.count > 3 {
                logInfo("🔍 路线最后一个坐标: (\(String(format: "%.6f", connection.routeCoordinates.last!.latitude)), \(String(format: "%.6f", connection.routeCoordinates.last!.longitude)))")
            }
        }

        logInfo("路线连接创建完成 - 连接线段数: \(routeConnections.count)")
    }

    // 🚨 验证路线坐标的合理性，防止直线连接
    private func isValidRouteCoordinates(_ coordinates: [CLLocationCoordinate2D]) -> Bool {
        guard coordinates.count >= 2 else {
            logInfo("🚨 路线验证：坐标点数量不足")
            return false
        }

        // 检查是否有异常长距离的连接段
        var totalDistance: CLLocationDistance = 0
        var maxSegmentDistance: CLLocationDistance = 0
        var longSegmentCount = 0

        for i in 0..<(coordinates.count - 1) {
            let startCoord = coordinates[i]
            let endCoord = coordinates[i + 1]

            let segmentDistance = CLLocation(latitude: startCoord.latitude, longitude: startCoord.longitude)
                .distance(from: CLLocation(latitude: endCoord.latitude, longitude: endCoord.longitude))

            totalDistance += segmentDistance
            maxSegmentDistance = max(maxSegmentDistance, segmentDistance)

            // 统计超过3公里的连接段数量
            if segmentDistance > 3000 {
                longSegmentCount += 1
            }
        }

        let averageSegmentDistance = totalDistance / Double(coordinates.count - 1)

        logInfo("🔍 路线验证 - 总距离: \(String(format: "%.1f", totalDistance/1000))km, 最大段距离: \(String(format: "%.1f", maxSegmentDistance/1000))km, 平均段距离: \(String(format: "%.1f", averageSegmentDistance))米, 长段数量: \(longSegmentCount)")

        // 🚨 放宽验证条件，避免误判真实路线
        // 1. 最大单段距离不能超过10公里（防止明显的直线连接）
        if maxSegmentDistance > 10000 {
            logInfo("🚨 路线验证失败：存在超长连接段(\(String(format: "%.1f", maxSegmentDistance/1000))km)")
            return false
        }

        // 2. 如果只有2个坐标点但选中了多个点，这明显是直线连接
        if coordinates.count == 2 && selectedPoints.count > 2 {
            logInfo("🚨 路线验证失败：多个选中点但只有2个坐标点，明显是直线连接")
            return false
        }

        // 3. 如果坐标点数量太少，可能是直线连接
        if coordinates.count < 3 && selectedPoints.count > 2 {
            logInfo("🚨 路线验证失败：多个选中点但坐标点数量过少(\(coordinates.count)个)")
            return false
        }

        logInfo("✅ 路线验证通过")
        return true
    }
}
