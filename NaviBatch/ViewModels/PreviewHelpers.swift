import SwiftUI
import MapKit
import SwiftData
import Foundation
import CoreLocation

/// 预览辅助工具类
/// 包含用于预览的简化版视图模型和数据
@MainActor
class PreviewHelpers {
    /// 创建用于预览的RouteViewModel
    static func createPreviewRouteViewModel() -> RouteViewModel {
        // 创建一个空的视图模型
        // 由于这个方法在@MainActor类中，所以可以安全地创建和修改RouteViewModel
        return RouteViewModel.shared
    }
}

/// 预览扩展
@MainActor
extension View {
    /// 添加预览所需的环境对象
    func withPreviewEnvironment() -> some View {
        self
            .environmentObject(PreviewHelpers.createPreviewRouteViewModel())
    }
}
