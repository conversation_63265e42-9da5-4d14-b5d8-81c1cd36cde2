name: Auto Version Update

on:
  # 每天检查一次 App Store 版本
  schedule:
    - cron: '0 9 * * *'  # 每天上午9点 UTC

  # 手动触发
  workflow_dispatch:
    inputs:
      version:
        description: '手动指定版本号 (留空则自动检测)'
        required: false
        type: string
      force_update:
        description: '是否强制更新'
        required: false
        type: boolean
        default: false
      update_notes:
        description: '更新说明 (用逗号分隔)'
        required: false
        type: string

jobs:
  update-version:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install dependencies
      run: |
        cd CloudflareWorkers/navibatch-config
        npm install

    - name: Check App Store version
      id: check-version
      run: |
        cd CloudflareWorkers/navibatch-config

        # 获取 App Store 版本
        APP_STORE_VERSION=$(curl -s "https://itunes.apple.com/lookup?id=6746371287&country=us" | jq -r '.results[0].version')
        echo "app_store_version=$APP_STORE_VERSION" >> $GITHUB_OUTPUT

        # 获取当前 Worker 配置版本
        CURRENT_VERSION=$(grep -o 'fallbackVersion: "[^"]*"' src/index.js | sed 's/fallbackVersion: "\(.*\)"/\1/')
        echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

        # 比较版本
        if [ "$APP_STORE_VERSION" != "$CURRENT_VERSION" ]; then
          echo "needs_update=true" >> $GITHUB_OUTPUT
          echo "🔄 发现版本差异: $CURRENT_VERSION -> $APP_STORE_VERSION"
        else
          echo "needs_update=false" >> $GITHUB_OUTPUT
          echo "✅ 版本已是最新: $CURRENT_VERSION"
        fi

    - name: Update Worker configuration
      if: steps.check-version.outputs.needs_update == 'true' || github.event.inputs.version != ''
      run: |
        cd CloudflareWorkers/navibatch-config

        # 确定要使用的版本
        if [ -n "${{ github.event.inputs.version }}" ]; then
          NEW_VERSION="${{ github.event.inputs.version }}"
          echo "🎯 使用手动指定版本: $NEW_VERSION"
        else
          NEW_VERSION="${{ steps.check-version.outputs.app_store_version }}"
          echo "🤖 使用 App Store 版本: $NEW_VERSION"
        fi

        # 更新版本号
        sed -i "s/fallbackVersion: \"[^\"]*\"/fallbackVersion: \"$NEW_VERSION\"/" src/index.js

        # 更新强制更新标志
        if [ "${{ github.event.inputs.force_update }}" = "true" ]; then
          sed -i "s/forceUpdate: [^,]*/forceUpdate: true/" src/index.js
        fi

        # 更新时间戳
        TODAY=$(date +%Y-%m-%d)
        sed -i "s/releaseDate: \"[^\"]*\"/releaseDate: \"$TODAY\"/" src/index.js

        # 如果有更新说明，更新它们
        if [ -n "${{ github.event.inputs.update_notes }}" ]; then
          echo "📝 更新说明: ${{ github.event.inputs.update_notes }}"
          # 这里可以添加更新说明的逻辑
        fi

        echo "✅ Worker 配置已更新"

    - name: Deploy to Cloudflare Workers
      if: steps.check-version.outputs.needs_update == 'true' || github.event.inputs.version != ''
      run: |
        cd CloudflareWorkers/navibatch-config
        echo "${{ secrets.CLOUDFLARE_API_TOKEN }}" | wrangler auth login --api-token
        wrangler deploy --env production

    - name: Create commit
      if: steps.check-version.outputs.needs_update == 'true' || github.event.inputs.version != ''
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add CloudflareWorkers/navibatch-config/src/index.js

        if [ -n "${{ github.event.inputs.version }}" ]; then
          git commit -m "🚀 手动更新版本到 ${{ github.event.inputs.version }}"
        else
          git commit -m "🤖 自动更新版本到 ${{ steps.check-version.outputs.app_store_version }}"
        fi

    - name: Push changes
      if: steps.check-version.outputs.needs_update == 'true' || github.event.inputs.version != ''
      uses: ad-m/github-push-action@master
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}

    - name: Send notification
      if: steps.check-version.outputs.needs_update == 'true' || github.event.inputs.version != ''
      run: |
        echo "🎉 版本更新完成！"
        echo "📱 新版本已部署到 Cloudflare Workers"
        echo "🔗 Worker URL: https://navibatch-config.jasonkwok2018.workers.dev"
