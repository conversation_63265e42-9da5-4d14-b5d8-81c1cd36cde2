{"name": "navibatch-config-worker", "version": "1.0.0", "description": "NaviBatch 配置服务 Cloudflare Worker", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --env production", "deploy-dev": "wrangler deploy --env development", "tail": "wrangler tail --env production", "update-version": "node update-version.js", "auto-update": "node update-version.js auto --deploy", "check-version": "node update-version.js check", "quick-update": "./quick-update.sh", "sync-appstore": "./quick-update.sh 1.0.7 \"🎉 与 App Store 同步\" \"📱 最新版本功能\"", "manage": "node manage-updates.js", "manage-list": "node manage-updates.js list", "manage-add": "node manage-updates.js add", "manage-edit": "node manage-updates.js edit", "manage-apply": "node manage-updates.js apply", "admin": "open admin.html", "test": "curl -H \"X-App-Version: 1.0.6\" -H \"X-Debug-Mode: true\" \"https://navibatch-config.jasonkwok2018.workers.dev/config\"", "test-local": "curl -H \"X-App-Version: 1.0.6\" -H \"X-Debug-Mode: true\" \"http://localhost:8787/config\"", "test-update": "curl -H \"X-App-Version: 1.0.6\" \"https://navibatch-config.jasonkwok2018.workers.dev/config\" | jq '.updateAvailable'"}, "keywords": ["cloudflare", "workers", "navibatch", "config", "version-management"], "author": "NaviBatch Team", "license": "MIT", "devDependencies": {"wrangler": "^4.22.0"}}