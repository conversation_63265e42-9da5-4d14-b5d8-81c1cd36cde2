# NaviBatch配置服务

使用Cloudflare Workers提供动态配置API，解决硬编码API密钥和模型配置的问题。

## 🎯 解决的问题

- ❌ **之前**: API密钥和模型配置硬编码在应用中
- ✅ **现在**: 配置存储在云端，可以随时修改而无需更新应用

## 🚀 部署步骤

### 1. 安装Wrangler CLI
```bash
npm install -g wrangler
```

### 2. 登录Cloudflare
```bash
wrangler login
```

### 3. 部署服务
```bash
cd NaviBatch/CloudflareWorkers/navibatch-config
./deploy.sh
```

### 4. 更新iOS应用配置
部署成功后，复制Worker URL并更新`ConfigService.swift`中的`configURL`：

```swift
private let configURL = "https://navibatch-config.your-subdomain.workers.dev/config"
```

## 📋 API端点

| 端点 | 描述 | 缓存时间 |
|------|------|----------|
| `GET /health` | 健康检查 | 无缓存 |
| `GET /config` | 获取完整配置 | 5分钟 |
| `GET /config/ai` | 获取AI配置 | 5分钟 |
| `GET /config/features` | 获取功能配置 | 10分钟 |

## 🔧 配置管理

### 修改配置
1. 编辑`src/index.js`中的`APP_CONFIG`对象
2. 运行`./deploy.sh`重新部署
3. 应用会在下次启动或定期刷新时获取新配置

### 配置结构
```javascript
const APP_CONFIG = {
  ai: {
    openRouter: {
      apiKey: "your-api-key",
      baseURL: "https://openrouter.ai/api/v1/chat/completions",
      gemmaModels: [
        "google/gemma-3-27b-it:free",
        "meta-llama/llama-3.2-3b-instruct:free"
      ],
      timeout: 30000,
      maxRetries: 3
    }
  },
  features: {
    lookAround: {
      enabled: true,
      cacheEnabled: true,
      maxCacheSize: 50
    },
    routeOptimization: {
      enabled: true,
      maxPoints: 100
    }
  }
}
```

## 📱 iOS集成

### ConfigService使用
```swift
// 获取配置服务实例
let configService = ConfigService.shared

// 获取API密钥
let apiKey = configService.apiKey

// 获取模型列表
let models = configService.gemmaModels

// 检查功能是否启用
let isEnabled = configService.isFeatureEnabled("lookAround")
```

### 自动刷新
- 应用启动时自动加载配置
- 每30分钟自动刷新配置
- 支持手动刷新：`configService.refreshConfig()`

## 💰 成本分析

### Cloudflare Workers免费额度
- ✅ **请求数**: 100,000次/天
- ✅ **CPU时间**: 10ms/请求
- ✅ **内存**: 128MB

### 预估使用量
- **配置请求**: ~1000次/天（每个用户每30分钟1次）
- **成本**: 完全免费

## 🔒 安全考虑

### API密钥保护
- API密钥存储在Cloudflare Workers中，不暴露给客户端
- 支持CORS，但可以限制来源域名
- 可以添加访问频率限制

### 访问控制
```javascript
// 可选：添加简单的访问控制
const ALLOWED_ORIGINS = [
  'https://navibatch.com',
  'https://www.navibatch.com'
];
```

## 🎛️ 高级功能

### 版本控制
- 支持基于应用版本返回不同配置
- 可以实现渐进式功能发布

### A/B测试
- 可以基于用户ID返回不同配置
- 支持功能开关和实验

### 监控
- Cloudflare提供详细的分析数据
- 可以监控API调用频率和错误率

## 🔄 更新流程

1. **修改配置**: 编辑`src/index.js`
2. **部署**: 运行`./deploy.sh`
3. **验证**: 访问`/health`端点确认部署成功
4. **生效**: 应用会在下次刷新时获取新配置

## 🆘 故障排除

### 常见问题

**Q: 应用获取不到配置怎么办？**
A: 检查Worker URL是否正确，确认网络连接正常

**Q: 配置更新后应用没有生效？**
A: 等待缓存过期（最多30分钟）或重启应用

**Q: 部署失败怎么办？**
A: 检查wrangler是否已登录，确认账户权限正常

### 调试命令
```bash
# 查看部署日志
wrangler tail

# 本地测试
wrangler dev

# 查看配置
curl https://your-worker-url.workers.dev/config
```
