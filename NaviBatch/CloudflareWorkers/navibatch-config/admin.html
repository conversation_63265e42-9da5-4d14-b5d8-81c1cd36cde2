<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NaviBatch 版本管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
            color: #1d1d1f;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            color: #007AFF;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .card h2 {
            color: #1d1d1f;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007AFF;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #0056CC;
        }
        
        .btn-secondary {
            background: #8E8E93;
        }
        
        .btn-secondary:hover {
            background: #6D6D70;
        }
        
        .btn-danger {
            background: #FF3B30;
        }
        
        .btn-danger:hover {
            background: #D70015;
        }
        
        .version-list {
            display: grid;
            gap: 20px;
        }
        
        .version-item {
            border: 2px solid #e5e5e7;
            border-radius: 12px;
            padding: 20px;
            transition: border-color 0.3s;
        }
        
        .version-item:hover {
            border-color: #007AFF;
        }
        
        .version-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .version-number {
            font-size: 1.3em;
            font-weight: 700;
            color: #007AFF;
        }
        
        .version-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .version-subtitle {
            color: #666;
            margin-bottom: 10px;
        }
        
        .version-notes {
            margin-bottom: 15px;
        }
        
        .version-notes ul {
            list-style: none;
            padding-left: 0;
        }
        
        .version-notes li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .version-notes li:last-child {
            border-bottom: none;
        }
        
        .version-meta {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .force-update {
            background: #FF3B30;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #D1F2EB;
            color: #00875A;
            border: 1px solid #00875A;
        }
        
        .status.error {
            background: #FFEBEE;
            color: #D32F2F;
            border: 1px solid #D32F2F;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 NaviBatch 版本管理</h1>
            <p>管理应用版本更新内容和配置</p>
        </div>
        
        <div id="status" class="status hidden"></div>
        
        <!-- 版本列表 -->
        <div class="card">
            <h2>📋 版本配置列表</h2>
            <div id="versionList" class="version-list">
                <!-- 版本列表将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 添加/编辑版本 -->
        <div class="card">
            <h2 id="formTitle">➕ 添加新版本</h2>
            <form id="versionForm">
                <div class="form-group">
                    <label for="version">版本号</label>
                    <input type="text" id="version" placeholder="例如: 1.0.8" required>
                </div>
                
                <div class="form-group">
                    <label for="title">更新标题</label>
                    <input type="text" id="title" placeholder="例如: 智能优化升级" required>
                </div>
                
                <div class="form-group">
                    <label for="subtitle">更新副标题</label>
                    <input type="text" id="subtitle" placeholder="例如: 更快更准的路线规划体验">
                </div>
                
                <div class="form-group">
                    <label for="notes">更新说明 (每行一条)</label>
                    <textarea id="notes" placeholder="🚀 新功能1&#10;🐛 修复问题2&#10;✨ 体验优化3"></textarea>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="forceUpdate"> 强制更新
                    </label>
                </div>
                
                <div class="form-group">
                    <label for="releaseDate">发布日期</label>
                    <input type="date" id="releaseDate" required>
                </div>
                
                <button type="submit" class="btn">保存配置</button>
                <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                <button type="button" class="btn btn-secondary" onclick="applyAndDeploy()" id="deployBtn" style="display:none;">应用并部署</button>
            </form>
        </div>
    </div>
    
    <script>
        let versionConfig = {};
        let currentEditingVersion = null;
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadVersionConfig();
            setDefaultDate();
        });
        
        // 设置默认日期为今天
        function setDefaultDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('releaseDate').value = today;
        }
        
        // 加载版本配置
        function loadVersionConfig() {
            // 这里应该从服务器加载配置，现在使用示例数据
            versionConfig = {
                "versionUpdates": {
                    "1.0.7": {
                        "title": "车道级·真境时代",
                        "subtitle": "AI 空间建模 刷新导航视界",
                        "notes": [
                            "🎉 与 App Store 同步",
                            "📱 最新版本 1.0.7",
                            "✨ 版本更新功能完善"
                        ],
                        "forceUpdate": false,
                        "releaseDate": "2025-06-27"
                    }
                }
            };
            
            renderVersionList();
        }
        
        // 渲染版本列表
        function renderVersionList() {
            const container = document.getElementById('versionList');
            container.innerHTML = '';
            
            Object.entries(versionConfig.versionUpdates).forEach(([version, info]) => {
                const versionItem = document.createElement('div');
                versionItem.className = 'version-item';
                versionItem.innerHTML = `
                    <div class="version-header">
                        <span class="version-number">v${version}</span>
                        ${info.forceUpdate ? '<span class="force-update">强制更新</span>' : ''}
                    </div>
                    <div class="version-title">${info.title}</div>
                    <div class="version-subtitle">${info.subtitle}</div>
                    <div class="version-meta">
                        <span>📅 ${info.releaseDate}</span>
                    </div>
                    <div class="version-notes">
                        <ul>
                            ${info.notes.map(note => `<li>${note}</li>`).join('')}
                        </ul>
                    </div>
                    <div>
                        <button class="btn btn-secondary" onclick="editVersion('${version}')">编辑</button>
                        <button class="btn btn-danger" onclick="deleteVersion('${version}')">删除</button>
                        <button class="btn" onclick="applyVersion('${version}')">应用此版本</button>
                    </div>
                `;
                container.appendChild(versionItem);
            });
        }
        
        // 表单提交
        document.getElementById('versionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveVersion();
        });
        
        // 保存版本配置
        function saveVersion() {
            const version = document.getElementById('version').value;
            const title = document.getElementById('title').value;
            const subtitle = document.getElementById('subtitle').value;
            const notes = document.getElementById('notes').value.split('\n').filter(note => note.trim());
            const forceUpdate = document.getElementById('forceUpdate').checked;
            const releaseDate = document.getElementById('releaseDate').value;
            
            if (!versionConfig.versionUpdates) {
                versionConfig.versionUpdates = {};
            }
            
            versionConfig.versionUpdates[version] = {
                title,
                subtitle,
                notes,
                forceUpdate,
                releaseDate
            };
            
            showStatus('版本配置已保存', 'success');
            renderVersionList();
            resetForm();
        }
        
        // 编辑版本
        function editVersion(version) {
            const info = versionConfig.versionUpdates[version];
            if (!info) return;
            
            currentEditingVersion = version;
            document.getElementById('formTitle').textContent = `✏️ 编辑版本 ${version}`;
            document.getElementById('version').value = version;
            document.getElementById('version').disabled = true;
            document.getElementById('title').value = info.title;
            document.getElementById('subtitle').value = info.subtitle;
            document.getElementById('notes').value = info.notes.join('\n');
            document.getElementById('forceUpdate').checked = info.forceUpdate;
            document.getElementById('releaseDate').value = info.releaseDate;
            document.getElementById('deployBtn').style.display = 'inline-block';
            
            // 滚动到表单
            document.getElementById('versionForm').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 删除版本
        function deleteVersion(version) {
            if (confirm(`确定要删除版本 ${version} 的配置吗？`)) {
                delete versionConfig.versionUpdates[version];
                showStatus(`版本 ${version} 已删除`, 'success');
                renderVersionList();
            }
        }
        
        // 应用版本
        function applyVersion(version) {
            if (confirm(`确定要应用版本 ${version} 的配置吗？这将更新 Worker 配置。`)) {
                // 这里应该调用 API 应用配置
                showStatus(`版本 ${version} 配置已应用`, 'success');
            }
        }
        
        // 应用并部署
        function applyAndDeploy() {
            const version = document.getElementById('version').value;
            if (confirm(`确定要应用版本 ${version} 并部署到 Cloudflare Workers 吗？`)) {
                // 这里应该调用 API 应用配置并部署
                showStatus(`版本 ${version} 已应用并部署`, 'success');
            }
        }
        
        // 重置表单
        function resetForm() {
            document.getElementById('versionForm').reset();
            document.getElementById('formTitle').textContent = '➕ 添加新版本';
            document.getElementById('version').disabled = false;
            document.getElementById('deployBtn').style.display = 'none';
            currentEditingVersion = null;
            setDefaultDate();
        }
        
        // 显示状态消息
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.classList.remove('hidden');
            
            setTimeout(() => {
                status.classList.add('hidden');
            }, 3000);
        }
    </script>
</body>
</html>
