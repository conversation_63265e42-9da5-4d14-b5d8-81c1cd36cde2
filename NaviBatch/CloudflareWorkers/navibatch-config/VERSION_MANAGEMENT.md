# 🤖 NaviBatch 版本管理自动化

现在您有**4种方式**管理版本更新，从完全手动到完全自动化！

## 🎯 **方案对比**

| 方案 | 自动化程度 | 适用场景 | 优势 |
|------|------------|----------|------|
| **手动更新** | ⭐ | 完全控制 | 简单直接 |
| **自动检测** | ⭐⭐⭐ | 大部分情况 | 无需手动维护 |
| **CLI 工具** | ⭐⭐⭐⭐ | 开发流程 | 灵活便捷 |
| **GitHub Actions** | ⭐⭐⭐⭐⭐ | 完全自动化 | 零人工干预 |

---

## 🔧 **方案1: 手动更新 (传统方式)**

### 步骤：
1. 编辑 `src/index.js` 中的 `fallbackVersion`
2. 运行 `wrangler deploy --env production`

```javascript
// 在 src/index.js 中修改
fallbackVersion: "1.0.5",  // 手动更新这里
```

---

## 🤖 **方案2: 自动检测 (推荐)**

Worker 现在会自动从 App Store 获取最新版本！

### 特性：
- ✅ **自动获取** App Store 最新版本
- ✅ **智能缓存** 避免频繁请求
- ✅ **降级处理** 失败时使用手动配置
- ✅ **零维护** 部署后自动工作

### 配置：
```javascript
autoVersionCheck: {
  enabled: true,              // 启用自动检测
  appStoreAppId: "6738046894", // App Store ID
  checkInterval: 3600,        // 检查间隔(秒)
  lastCheck: null,            // 最后检查时间
  cachedVersion: null         // 缓存版本
}
```

---

## ⚡ **方案3: CLI 工具 (开发者友好)**

使用我们的版本管理工具：

### 安装依赖：
```bash
cd CloudflareWorkers/navibatch-config
npm install
```

### 使用命令：

```bash
# 🤖 自动从 App Store 获取版本并部署
npm run auto-update

# 🎯 手动指定版本
npm run update-version manual 1.0.5 --deploy "🚀 新功能" "🐛 修复问题"

# 📋 检查当前状态
npm run check-version

# 🚀 仅部署当前配置
npm run deploy

# 🧪 测试配置
npm run test
```

### 详细用法：
```bash
# 自动检测版本（不部署）
node update-version.js auto

# 自动检测版本并部署
node update-version.js auto --deploy

# 手动指定版本
node update-version.js manual 1.0.5 --deploy

# 带更新说明
node update-version.js manual 1.0.5 --deploy "🚀 新功能" "🐛 修复问题"
```

---

## 🔄 **方案4: GitHub Actions (完全自动化)**

### 设置步骤：

1. **添加 Secrets**：
   在 GitHub 仓库设置中添加：
   ```
   CLOUDFLARE_API_TOKEN = your_cloudflare_api_token
   ```

2. **自动触发**：
   - 每天自动检查 App Store 版本
   - 发现新版本时自动更新并部署
   - 自动提交代码变更

3. **手动触发**：
   在 GitHub Actions 页面可以手动运行，支持：
   - 指定版本号
   - 设置强制更新
   - 添加更新说明

### 工作流程：
```
App Store 发布新版本
        ↓
GitHub Actions 检测到变化
        ↓
自动更新 Worker 配置
        ↓
部署到 Cloudflare
        ↓
提交代码变更
        ↓
发送通知
```

---

## 🎯 **推荐使用方式**

### 🥇 **日常开发**: 方案2 (自动检测)
- 部署一次，自动工作
- 无需人工干预
- 适合大部分场景

### 🥈 **版本发布**: 方案3 (CLI工具)
```bash
# 发布新版本时
npm run update-version manual 1.0.5 --deploy \
  "🚀 全新功能" \
  "🎯 性能优化" \
  "🐛 修复问题"
```

### 🥉 **完全自动化**: 方案4 (GitHub Actions)
- 适合成熟的 CI/CD 流程
- 零人工干预
- 自动化程度最高

---

## 🧪 **测试验证**

### 测试自动检测：
```bash
# 测试 Worker 是否正常工作
curl -H "X-Debug-Mode: true" \
  "https://navibatch-config.jasonkwok2018.workers.dev/config"
```

### 在应用中测试：
1. 打开 NaviBatch 应用
2. 进入开发者工具 → 版本更新测试
3. 点击"调试模式检查更新"

---

## 🔍 **故障排除**

### 自动检测失败：
- 检查 App Store ID 是否正确
- 网络连接是否正常
- 会自动降级到手动配置版本

### CLI 工具问题：
```bash
# 检查 Node.js 版本
node --version  # 需要 >= 14

# 重新安装依赖
npm install

# 检查权限
chmod +x update-version.js
```

### GitHub Actions 问题：
- 检查 CLOUDFLARE_API_TOKEN 是否设置
- 查看 Actions 运行日志
- 确认仓库权限设置

---

## 🎉 **总结**

现在您有了完整的版本管理解决方案：

- ✅ **自动检测** - Worker 自动获取 App Store 版本
- ✅ **CLI 工具** - 灵活的命令行管理
- ✅ **GitHub Actions** - 完全自动化流程
- ✅ **手动备份** - 传统方式仍然可用

**推荐配置**：启用自动检测 + 使用 CLI 工具进行版本发布！🚀
