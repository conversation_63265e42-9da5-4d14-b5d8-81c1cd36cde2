#!/usr/bin/env node

/**
 * NaviBatch 版本更新管理工具
 * 自动更新 Cloudflare Worker 中的版本信息
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const CONFIG = {
  workerFile: './src/index.js',
  appStoreAppId: '6746371287', // 正确的 NaviBatch App Store ID
  packageJsonPath: '../../package.json' // 如果有的话
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 从 App Store 获取最新版本
async function getAppStoreVersion() {
  try {
    log('🔍 从 App Store 获取最新版本信息...', 'blue');

    const response = await fetch(`https://itunes.apple.com/lookup?id=${CONFIG.appStoreAppId}&country=us`);
    const data = await response.json();

    if (data.results && data.results.length > 0) {
      const version = data.results[0].version;
      log(`✅ App Store 最新版本: ${version}`, 'green');
      return version;
    }

    throw new Error('未找到应用信息');
  } catch (error) {
    log(`❌ 获取 App Store 版本失败: ${error.message}`, 'red');
    return null;
  }
}

// 更新 Worker 文件中的版本信息
function updateWorkerVersion(newVersion, updateNotes = []) {
  try {
    log('📝 更新 Worker 配置文件...', 'blue');

    let content = fs.readFileSync(CONFIG.workerFile, 'utf8');

    // 更新 fallbackVersion
    content = content.replace(
      /fallbackVersion:\s*"[^"]*"/,
      `fallbackVersion: "${newVersion}"`
    );

    // 如果提供了更新说明，也更新它
    if (updateNotes.length > 0) {
      const notesString = updateNotes.map(note => `      "${note}"`).join(',\n');
      content = content.replace(
        /updateNotes:\s*\[[^\]]*\]/s,
        `updateNotes: [\n${notesString}\n    ]`
      );
    }

    // 更新时间戳
    const now = new Date().toISOString();
    content = content.replace(
      /releaseDate:\s*"[^"]*"/,
      `releaseDate: "${now.split('T')[0]}"`
    );

    fs.writeFileSync(CONFIG.workerFile, content);
    log(`✅ Worker 配置已更新到版本 ${newVersion}`, 'green');

    return true;
  } catch (error) {
    log(`❌ 更新 Worker 配置失败: ${error.message}`, 'red');
    return false;
  }
}

// 部署到 Cloudflare
function deployWorker() {
  try {
    log('🚀 部署到 Cloudflare Workers...', 'blue');

    execSync('wrangler deploy --env production', {
      stdio: 'inherit',
      cwd: __dirname
    });

    log('✅ 部署成功！', 'green');
    return true;
  } catch (error) {
    log(`❌ 部署失败: ${error.message}`, 'red');
    return false;
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  log('🎯 NaviBatch 版本更新管理工具', 'cyan');
  log('================================', 'cyan');

  switch (command) {
    case 'auto':
      // 自动从 App Store 获取并更新
      const appStoreVersion = await getAppStoreVersion();
      if (appStoreVersion) {
        if (updateWorkerVersion(appStoreVersion)) {
          if (args.includes('--deploy')) {
            deployWorker();
          } else {
            log('💡 使用 --deploy 参数自动部署', 'yellow');
          }
        }
      }
      break;

    case 'manual':
      // 手动指定版本
      const version = args[1];
      if (!version) {
        log('❌ 请指定版本号: npm run update-version manual 1.0.5', 'red');
        return;
      }

      const notes = args.slice(2).filter(arg => !arg.startsWith('--'));
      if (updateWorkerVersion(version, notes)) {
        if (args.includes('--deploy')) {
          deployWorker();
        }
      }
      break;

    case 'deploy':
      // 仅部署
      deployWorker();
      break;

    case 'check':
      // 检查当前状态
      await getAppStoreVersion();
      log('📋 当前 Worker 配置:', 'blue');

      const content = fs.readFileSync(CONFIG.workerFile, 'utf8');
      const versionMatch = content.match(/fallbackVersion:\s*"([^"]*)"/);
      if (versionMatch) {
        log(`   配置版本: ${versionMatch[1]}`, 'yellow');
      }
      break;

    default:
      log('📖 使用说明:', 'yellow');
      log('');
      log('  npm run update-version auto [--deploy]     # 从 App Store 自动获取版本', 'white');
      log('  npm run update-version manual 1.0.5 [--deploy] [notes...]  # 手动指定版本', 'white');
      log('  npm run update-version deploy             # 仅部署当前配置', 'white');
      log('  npm run update-version check              # 检查当前状态', 'white');
      log('');
      log('示例:', 'yellow');
      log('  npm run update-version auto --deploy', 'cyan');
      log('  npm run update-version manual 1.0.5 --deploy "🚀 新功能" "🐛 修复问题"', 'cyan');
      break;
  }
}

// 运行
main().catch(error => {
  log(`❌ 执行失败: ${error.message}`, 'red');
  process.exit(1);
});
