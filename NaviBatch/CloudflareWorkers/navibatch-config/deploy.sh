#!/bin/bash

# NaviBatch配置服务部署脚本
# 使用Cloudflare Workers部署动态配置API

echo "🚀 开始部署NaviBatch配置服务到Cloudflare Workers..."

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
    echo "❌ 错误: 未找到wrangler CLI"
    echo "请先安装: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "🔐 请先登录Cloudflare:"
    wrangler login
fi

# 部署到生产环境
echo "📦 部署到生产环境..."
wrangler deploy --env production

if [ $? -eq 0 ]; then
    echo "✅ 部署成功！"
    echo ""
    echo "🌐 配置服务已部署到:"
    echo "   https://navibatch-config.jasonkwok2018.workers.dev"
    echo ""
    echo "📋 可用的API端点:"
    echo "   GET /health          - 健康检查"
    echo "   GET /config          - 获取完整配置"
    echo "   GET /config/ai       - 获取AI配置"
    echo "   GET /config/features - 获取功能配置"
    echo ""
    echo "🧪 测试版本更新功能:"
    echo "   curl -H \"X-App-Version: 1.0.3\" -H \"X-Debug-Mode: true\" \"https://navibatch-config.jasonkwok2018.workers.dev/config\""
    echo ""
    echo "🔧 下一步:"
    echo "1. 在iOS应用中测试版本更新功能"
    echo "2. 进入开发者工具 → 版本更新测试"
    echo "3. 点击\"调试模式检查更新\"或\"模拟版本更新提示\""
    echo ""
    echo "💡 提示: 修改版本信息只需编辑src/index.js中的versionUpdate配置，然后重新部署即可"
else
    echo "❌ 部署失败"
    exit 1
fi
