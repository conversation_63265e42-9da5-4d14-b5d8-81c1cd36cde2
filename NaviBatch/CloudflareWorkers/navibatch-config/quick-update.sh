#!/bin/bash

# NaviBatch 快速版本更新脚本
# 用法: ./quick-update.sh 1.0.7 "更新说明1" "更新说明2"

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查参数
if [ $# -lt 1 ]; then
    print_message $RED "❌ 错误: 请提供版本号"
    echo ""
    print_message $YELLOW "用法:"
    print_message $CYAN "  ./quick-update.sh 1.0.7"
    print_message $CYAN "  ./quick-update.sh 1.0.7 \"🚀 新功能\" \"🐛 修复问题\""
    echo ""
    print_message $YELLOW "当前配置版本:"
    grep -o 'fallbackVersion: "[^"]*"' src/index.js | sed 's/fallbackVersion: "\(.*\)"/  \1/'
    exit 1
fi

NEW_VERSION=$1
shift  # 移除第一个参数，剩下的都是更新说明

print_message $CYAN "🎯 NaviBatch 快速版本更新"
print_message $CYAN "=========================="
echo ""

# 显示当前版本
CURRENT_VERSION=$(grep -o 'fallbackVersion: "[^"]*"' src/index.js | sed 's/fallbackVersion: "\(.*\)"/\1/')
print_message $BLUE "📋 当前版本: $CURRENT_VERSION"
print_message $BLUE "🎯 目标版本: $NEW_VERSION"
echo ""

# 确认更新
read -p "$(echo -e ${YELLOW}确认更新到版本 $NEW_VERSION? [y/N]: ${NC})" -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_message $YELLOW "❌ 取消更新"
    exit 0
fi

# 1. 更新版本号
print_message $BLUE "📝 更新版本号..."
sed -i.bak "s/fallbackVersion: \"[^\"]*\"/fallbackVersion: \"$NEW_VERSION\"/" src/index.js

# 2. 更新时间戳
TODAY=$(date +%Y-%m-%d)
sed -i.bak "s/releaseDate: \"[^\"]*\"/releaseDate: \"$TODAY\"/" src/index.js

# 3. 更新说明（如果提供）
if [ $# -gt 0 ]; then
    print_message $BLUE "📝 更新说明..."
    
    # 构建更新说明数组
    NOTES_ARRAY=""
    for note in "$@"; do
        if [ -n "$NOTES_ARRAY" ]; then
            NOTES_ARRAY="$NOTES_ARRAY,\n      \"$note\""
        else
            NOTES_ARRAY="      \"$note\""
        fi
    done
    
    # 使用 perl 进行多行替换
    perl -i.bak -pe 'BEGIN{undef $/;} s/updateNotes: \[[^\]]*\]/updateNotes: [\n'"$NOTES_ARRAY"'\n    ]/smg' src/index.js
    
    print_message $GREEN "✅ 更新说明已添加:"
    for note in "$@"; do
        print_message $GREEN "   • $note"
    done
fi

# 4. 部署到 Cloudflare
print_message $BLUE "🚀 部署到 Cloudflare Workers..."
if wrangler deploy --env production; then
    print_message $GREEN "✅ 部署成功！"
else
    print_message $RED "❌ 部署失败"
    # 恢复备份
    mv src/index.js.bak src/index.js
    exit 1
fi

# 5. 清理备份文件
rm -f src/index.js.bak

# 6. 测试部署
print_message $BLUE "🧪 测试部署..."
sleep 2  # 等待部署生效

# 测试版本检查
TEST_VERSION="1.0.6"  # 使用一个较低的版本进行测试
RESPONSE=$(curl -s -H "X-App-Version: $TEST_VERSION" "https://navibatch-config.jasonkwok2018.workers.dev/config")

if echo "$RESPONSE" | grep -q "\"latestVersion\":\"$NEW_VERSION\""; then
    print_message $GREEN "✅ 版本检查测试通过！"
    print_message $GREEN "📱 最新版本: $NEW_VERSION"
else
    print_message $YELLOW "⚠️  版本检查测试未通过，但部署可能仍然成功"
fi

echo ""
print_message $CYAN "🎉 版本更新完成！"
print_message $CYAN "==================="
print_message $GREEN "📱 新版本: $NEW_VERSION"
print_message $GREEN "🌐 Worker URL: https://navibatch-config.jasonkwok2018.workers.dev"
print_message $GREEN "📅 更新日期: $TODAY"
echo ""
print_message $YELLOW "🧪 在应用中测试:"
print_message $YELLOW "1. 打开 NaviBatch 应用"
print_message $YELLOW "2. 进入开发者工具 → 版本更新测试"
print_message $YELLOW "3. 点击\"调试模式检查更新\""
echo ""
print_message $BLUE "💡 提示: 如果需要强制更新，请手动编辑 src/index.js 中的 forceUpdate 字段"
