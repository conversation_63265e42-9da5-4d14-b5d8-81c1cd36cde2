#!/bin/bash

# 🚀 NaviBatch Config Worker 紧急部署脚本
# 用于修复版本检测问题

echo "🚀 开始部署 NaviBatch Config Worker..."
echo "📝 修复内容:"
echo "  - 更新 fallbackVersion 到 1.0.9"
echo "  - 添加详细的版本检测调试日志"
echo "  - 优化 App Store API 调用"
echo ""

# 检查是否在正确的目录
if [ ! -f "wrangler.toml" ]; then
    echo "❌ 错误: 请在 navibatch-config 目录下运行此脚本"
    exit 1
fi

# 检查 wrangler 是否安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ 错误: wrangler 未安装"
    echo "💡 请运行: npm install -g wrangler"
    exit 1
fi

# 显示当前配置
echo "📋 当前配置:"
echo "  - fallbackVersion: $(grep -o 'fallbackVersion: "[^"]*"' src/index.js | sed 's/fallbackVersion: "\(.*\)"/\1/')"
echo "  - autoVersionCheck.enabled: $(grep -A 5 'autoVersionCheck:' src/index.js | grep 'enabled:' | sed 's/.*enabled: \(.*\),/\1/')"
echo ""

# 部署到生产环境
echo "🚀 部署到生产环境..."
wrangler deploy --env production

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 部署成功!"
    echo ""
    echo "🔍 测试步骤:"
    echo "1. 在手机上打开 NaviBatch 1.0.8"
    echo "2. 应该会看到更新提示"
    echo "3. 如果没有，请检查 Cloudflare Worker 日志"
    echo ""
    echo "🌐 Worker URL: https://navibatch-config.jasonkwok2018.workers.dev"
    echo "🔍 健康检查: https://navibatch-config.jasonkwok2018.workers.dev/health"
    echo "📊 配置测试: https://navibatch-config.jasonkwok2018.workers.dev/config"
    echo ""
    echo "📱 如果仍然没有更新提示，可以尝试:"
    echo "  - 完全关闭并重新打开 App"
    echo "  - 等待 5-10 分钟让缓存过期"
    echo "  - 检查网络连接"
else
    echo ""
    echo "❌ 部署失败!"
    echo "💡 请检查 wrangler 配置和网络连接"
    exit 1
fi
