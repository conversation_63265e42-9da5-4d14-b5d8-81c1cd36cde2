/**
 * NaviBatch配置服务 - Cloudflare Worker
 * 提供动态配置API，避免硬编码API密钥和模型配置
 */

// 配置数据 - 可以随时修改而无需更新App
const APP_CONFIG = {
  // AI服务配置
  ai: {
    // OpenRouter API配置
    openRouter: {
      apiKey: "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",
      baseURL: "https://openrouter.ai/api/v1/chat/completions",

      // Gemma模型配置（按优先级排序）
      gemmaModels: [
        "google/gemma-3-27b-it:free",           // 首选：27B参数最强
        "google/gemma-3-12b-it:free"            // 备用：12B参数强大
      ],

      // 请求配置
      timeout: 30000,
      maxRetries: 3,

      // 模型特定配置
      modelConfigs: {
        "google/gemma-3-27b-it:free": {
          maxTokens: 60000,
          temperature: 0.1,
          topP: 0.9
        },
        "google/gemma-3-12b-it:free": {
          maxTokens: 60000,
          temperature: 0.1,
          topP: 0.9
        }
      }
    }
  },

  // 应用功能配置
  features: {
    // 实景图功能
    lookAround: {
      enabled: true,
      cacheEnabled: true,
      maxCacheSize: 50
    },

    // 路线优化
    routeOptimization: {
      enabled: true,
      maxPoints: 100,
      algorithms: ["genetic", "nearest_neighbor"]
    },

    // 订阅功能
    subscription: {
      enabled: true,
      trialDays: 60,
      features: {
        unlimitedAddresses: true,
        advancedOptimization: true,
        exportFeatures: true
      }
    }
  },

  // 版本和兼容性
  version: "1.0.3",
  minAppVersion: "1.0.0",
  lastUpdated: new Date().toISOString(),

  // 🚀 版本更新配置
  versionUpdate: {
    // 自动版本检测配置
    autoVersionCheck: {
      enabled: true,
      appStoreAppId: "6746371287", // NaviBatch App Store ID (正确的ID)
      checkInterval: 3600, // 每小时检查一次
      lastCheck: null,
      cachedVersion: null
    },

    // 手动版本配置（当自动检测失败时使用）
    fallbackVersion: "1.0.9",
    forceUpdate: false,
    updateTitle: "NaviBatch 1.0.9",
    updateSubtitle: "视频处理优化与界面改进",
    updateNotes: [
      "🎯 优化视频处理帧阈值，减少快速滑动时的内容遗漏",
      "🔧 移除无用的图片排序提示文本",
      "✨ 改进用户界面体验",
      "🐛 修复已知问题"
    ],
    appStoreURL: "https://apps.apple.com/us/app/navibatch-route-planner/id6746371287",
    releaseDate: "2025-07-01"
  },

  // 🚀 更新说明（历史版本）
  updateNotes: {
    "1.0.4": [
      "🚀 全新版本更新提示功能",
      "🎯 智能版本检查机制",
      "✨ 优化用户体验",
      "🔧 修复已知问题"
    ],
    "1.0.3": [
      "🔑 更新API密钥",
      "🇭🇰 添加DeepSeek后备模型（香港友好）",
      "🚦 优化频率限制处理",
      "⚙️ 改进模型配置参数"
    ]
  }
};

// CORS头配置
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-App-Version',
  'Access-Control-Max-Age': '86400',
};

// 主要处理函数
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    // 处理CORS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: CORS_HEADERS
      });
    }

    try {
      // 路由处理
      switch (path) {
        case '/':
        case '/health':
          return handleHealthCheck();

        case '/config':
          return handleGetConfig(request);

        case '/config/ai':
          return handleGetAIConfig(request);

        case '/config/features':
          return handleGetFeaturesConfig(request);

        default:
          return new Response('Not Found', {
            status: 404,
            headers: CORS_HEADERS
          });
      }
    } catch (error) {
      console.error('Worker error:', error);
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...CORS_HEADERS
        }
      });
    }
  }
};

// 健康检查
function handleHealthCheck() {
  return new Response(JSON.stringify({
    status: 'healthy',
    service: 'NaviBatch Config Service',
    version: APP_CONFIG.version,
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      ...CORS_HEADERS
    }
  });
}

// 获取完整配置
async function handleGetConfig(request) {
  const appVersion = request.headers.get('X-App-Version') || '1.0.0';
  const debugMode = request.headers.get('X-Debug-Mode') === 'true';

  // 获取最新版本（自动检测或手动配置）
  const latestVersion = await getLatestVersion();

  // 版本比较逻辑
  const needsUpdate = compareVersions(appVersion, latestVersion) < 0;
  const shouldShowUpdate = needsUpdate || debugMode;

  // 🔍 详细的版本比较日志
  console.log('🔍 版本比较详情:');
  console.log('  📱 客户端版本:', appVersion);
  console.log('  🆕 最新版本:', latestVersion);
  console.log('  📊 比较结果:', compareVersions(appVersion, latestVersion));
  console.log('  🎯 需要更新:', needsUpdate);
  console.log('  🐛 调试模式:', debugMode);
  console.log('  ✅ 显示更新:', shouldShowUpdate);

  // 基于App版本返回不同配置
  const config = {
    ...APP_CONFIG,
    // 版本更新信息（仅在需要时返回）
    ...(shouldShowUpdate && {
      updateAvailable: {
        hasUpdate: true,
        currentVersion: appVersion,
        latestVersion: latestVersion,
        forceUpdate: APP_CONFIG.versionUpdate.forceUpdate,
        updateInfo: {
          ...APP_CONFIG.versionUpdate,
          latestVersion: latestVersion
        },
        debugMode: debugMode
      }
    }),
    // 元数据
    _metadata: {
      requestTime: new Date().toISOString(),
      appVersion: appVersion,
      needsUpdate: needsUpdate,
      debugMode: debugMode
    }
  };

  return new Response(JSON.stringify(config), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=300', // 缓存5分钟
      ...CORS_HEADERS
    }
  });
}

// 🤖 自动获取最新版本
async function getLatestVersion() {
  const config = APP_CONFIG.versionUpdate;

  console.log('🔍 开始版本检测流程...');
  console.log('📋 自动检测启用状态:', config.autoVersionCheck.enabled);
  console.log('🆔 App Store ID:', config.autoVersionCheck.appStoreAppId);

  // 如果禁用自动检测，直接返回手动配置的版本
  if (!config.autoVersionCheck.enabled) {
    console.log('⚠️ 自动检测已禁用，使用手动配置版本:', config.fallbackVersion);
    return config.fallbackVersion;
  }

  try {
    // 检查缓存是否有效
    const now = Date.now();
    const lastCheck = config.autoVersionCheck.lastCheck;
    const checkInterval = config.autoVersionCheck.checkInterval * 1000; // 转换为毫秒

    console.log('⏰ 缓存检查 - 当前时间:', new Date(now).toISOString());
    console.log('⏰ 上次检查时间:', lastCheck ? new Date(lastCheck).toISOString() : '从未检查');
    console.log('⏰ 检查间隔:', checkInterval / 1000, '秒');

    if (lastCheck && (now - lastCheck) < checkInterval && config.autoVersionCheck.cachedVersion) {
      console.log('🔄 使用缓存的版本信息:', config.autoVersionCheck.cachedVersion);
      return config.autoVersionCheck.cachedVersion;
    }

    console.log('🌐 开始从 App Store 获取最新版本...');
    // 从 App Store 获取最新版本信息
    const appStoreVersion = await fetchAppStoreVersion(config.autoVersionCheck.appStoreAppId);

    if (appStoreVersion) {
      // 更新缓存
      config.autoVersionCheck.cachedVersion = appStoreVersion;
      config.autoVersionCheck.lastCheck = now;

      console.log('✅ 从 App Store 获取到最新版本:', appStoreVersion);
      console.log('💾 已更新缓存');
      return appStoreVersion;
    } else {
      console.log('❌ App Store 返回空版本信息');
    }
  } catch (error) {
    console.error('❌ 自动版本检测失败:', error.message);
    console.error('🔍 错误详情:', error.stack);
  }

  // 如果自动检测失败，使用手动配置的版本
  console.log('🔄 自动检测失败，使用手动配置的版本:', config.fallbackVersion);
  return config.fallbackVersion;
}

// 从 App Store 获取版本信息
async function fetchAppStoreVersion(appId) {
  try {
    const url = `https://itunes.apple.com/lookup?id=${appId}&country=us`;
    console.log('🌐 请求 App Store API:', url);

    const response = await fetch(url);
    console.log('📡 App Store API 响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`App Store API 请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📦 App Store API 响应数据:', JSON.stringify(data, null, 2));

    if (data.results && data.results.length > 0) {
      const appInfo = data.results[0];
      console.log('📱 应用信息:', {
        bundleId: appInfo.bundleId,
        version: appInfo.version,
        trackName: appInfo.trackName,
        releaseDate: appInfo.releaseDate
      });
      return appInfo.version;
    }

    throw new Error('App Store 中未找到应用信息');
  } catch (error) {
    console.error('❌ 获取 App Store 版本失败:', error.message);
    console.error('🔍 错误详情:', error.stack);
    throw error;
  }
}

// 版本比较函数
function compareVersions(version1, version2) {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
    const v1part = v1parts[i] || 0;
    const v2part = v2parts[i] || 0;

    if (v1part < v2part) return -1;
    if (v1part > v2part) return 1;
  }

  return 0;
}

// 获取AI配置
function handleGetAIConfig(request) {
  return new Response(JSON.stringify({
    ai: APP_CONFIG.ai,
    version: APP_CONFIG.version,
    lastUpdated: APP_CONFIG.lastUpdated
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=300',
      ...CORS_HEADERS
    }
  });
}

// 获取功能配置
function handleGetFeaturesConfig(request) {
  return new Response(JSON.stringify({
    features: APP_CONFIG.features,
    version: APP_CONFIG.version,
    lastUpdated: APP_CONFIG.lastUpdated
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=600', // 功能配置缓存10分钟
      ...CORS_HEADERS
    }
  });
}
