name = "navibatch-config"
main = "src/index.js"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]

# 路由配置 - 你需要替换为你的域名
# routes = [
#   { pattern = "config.navibatch.com/*", zone_name = "navibatch.com" }
# ]

# 或者使用workers.dev子域名（免费）
# 部署后会得到类似：navibatch-config.your-subdomain.workers.dev

# 环境变量（可选）
[vars]
ENVIRONMENT = "production"
SERVICE_NAME = "NaviBatch Config Service"

# 开发环境配置
[env.development]
name = "navibatch-config-dev"

[env.development.vars]
ENVIRONMENT = "development"

# 生产环境配置
[env.production]
name = "navibatch-config"

[env.production.vars]
ENVIRONMENT = "production"
