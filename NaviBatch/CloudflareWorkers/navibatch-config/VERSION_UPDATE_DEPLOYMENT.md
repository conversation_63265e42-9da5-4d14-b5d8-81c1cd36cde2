# NaviBatch 版本更新功能 - Cloudflare Worker 部署指南

## 🎉 部署成功！

版本更新功能已成功部署到 Cloudflare Workers：

**Worker URL**: https://navibatch-config.jasonkwok2018.workers.dev

## 🧪 测试部署

### 1. 测试版本检查 API

```bash
# 测试调试模式（强制返回更新信息）
curl -H "X-App-Version: 1.0.3" -H "X-Debug-Mode: true" \
  "https://navibatch-config.jasonkwok2018.workers.dev/config"

# 测试正常模式（仅当有更新时返回）
curl -H "X-App-Version: 1.0.3" \
  "https://navibatch-config.jasonkwok2018.workers.dev/config"

# 测试最新版本（不应返回更新信息）
curl -H "X-App-Version: 1.0.4" \
  "https://navibatch-config.jasonkwok2018.workers.dev/config"
```

### 2. 验证返回的更新信息

成功的响应应包含 `updateAvailable` 字段：

```json
{
  "updateAvailable": {
    "hasUpdate": true,
    "currentVersion": "1.0.3",
    "latestVersion": "1.0.4",
    "forceUpdate": false,
    "updateInfo": {
      "updateTitle": "车道级·真境时代",
      "updateSubtitle": "AI 空间建模 刷新导航视界",
      "updateNotes": [
        "🚀 全新版本更新提示功能",
        "🎯 智能版本检查机制",
        "✨ 优化用户体验",
        "🔧 修复已知问题"
      ],
      "appStoreURL": "https://apps.apple.com/app/navibatch/id6738046894"
    },
    "debugMode": true
  }
}
```

## 📱 在应用中测试

### 1. 开发者工具测试
1. 打开 NaviBatch 应用
2. 进入设置 → 开发者工具
3. 选择"版本更新测试"
4. 尝试以下测试：
   - **模拟版本更新提示**: 直接显示更新弹窗
   - **调试模式检查更新**: 从服务器获取更新信息
   - **正常模式检查更新**: 测试正常流程

### 2. 自动触发测试
- 应用启动时会自动检查更新
- 每30分钟会刷新配置
- 如果检测到版本差异，会自动显示更新提示

## 🔧 配置管理

### 修改版本信息

编辑 `src/index.js` 中的 `versionUpdate` 配置：

```javascript
versionUpdate: {
  latestVersion: "1.0.5",        // 修改最新版本号
  forceUpdate: false,            // 是否强制更新
  updateTitle: "新版本标题",      // 更新标题
  updateSubtitle: "更新副标题",   // 更新副标题
  updateNotes: [                 // 更新说明
    "🚀 新功能1",
    "🐛 修复问题2",
    "✨ 优化体验3"
  ],
  appStoreURL: "https://apps.apple.com/app/navibatch/id6738046894",
  releaseDate: "2024-12-27"
}
```

### 重新部署

```bash
# 在 navibatch-config 目录下执行
wrangler deploy --env production

# 或使用部署脚本
./deploy.sh
```

## 🎯 版本控制策略

### 版本号格式
- 使用语义化版本：`主版本.次版本.修订版本`
- 例如：`1.0.3` → `1.0.4` → `1.1.0` → `2.0.0`

### 更新类型
1. **普通更新** (`forceUpdate: false`)
   - 用户可以选择稍后提醒或跳过
   - 适用于功能更新、优化改进

2. **强制更新** (`forceUpdate: true`)
   - 用户只能选择立即更新
   - 适用于安全修复、重要bug修复

### 发布流程
1. 更新应用版本号
2. 修改 Worker 配置中的 `latestVersion`
3. 更新 `updateNotes` 说明
4. 部署 Worker
5. 发布应用到 App Store

## 🔍 监控和调试

### 查看 Worker 日志
```bash
wrangler tail --env production
```

### 调试版本检查
- 使用 `X-Debug-Mode: true` 头部强制返回更新信息
- 检查 `_metadata` 字段了解版本比较结果

### 常见问题
1. **更新提示不显示**: 检查版本号比较逻辑
2. **调试模式无效**: 确认请求头设置正确
3. **配置加载失败**: 检查网络连接和 Worker 状态

## 📈 使用统计

Worker 会在 `_metadata` 中记录：
- 请求时间
- 应用版本
- 是否需要更新
- 是否为调试模式

可以通过 Cloudflare Analytics 查看使用情况。

## 🚀 下一步

1. **测试完整流程**: 从版本检查到用户更新
2. **监控用户反馈**: 观察更新提示的用户接受度
3. **优化提示频率**: 根据用户行为调整提醒策略
4. **扩展功能**: 考虑添加 A/B 测试、渐进式发布等

---

## 🎊 总结

版本更新功能现已完全部署并可用：

- ✅ **Cloudflare Worker 部署成功**
- ✅ **版本检查 API 正常工作**
- ✅ **调试模式可用于测试**
- ✅ **应用集成完成**
- ✅ **本地测试工具就绪**

现在您可以完整体验版本更新功能了！🚀
