#!/usr/bin/env node

/**
 * NaviBatch 更新内容管理工具
 * 管理版本更新的标题、说明、强制更新等配置
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置文件路径
const CONFIG_FILE = './version-config.json';
const WORKER_FILE = './src/index.js';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 加载配置文件
function loadConfig() {
  try {
    const content = fs.readFileSync(CONFIG_FILE, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    log(`❌ 加载配置文件失败: ${error.message}`, 'red');
    return null;
  }
}

// 保存配置文件
function saveConfig(config) {
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    log('✅ 配置文件已保存', 'green');
    return true;
  } catch (error) {
    log(`❌ 保存配置文件失败: ${error.message}`, 'red');
    return false;
  }
}

// 更新 Worker 文件
function updateWorkerFile(version, updateInfo) {
  try {
    let content = fs.readFileSync(WORKER_FILE, 'utf8');
    
    // 更新版本号
    content = content.replace(
      /fallbackVersion:\s*"[^"]*"/,
      `fallbackVersion: "${version}"`
    );
    
    // 更新标题
    content = content.replace(
      /updateTitle:\s*"[^"]*"/,
      `updateTitle: "${updateInfo.title}"`
    );
    
    // 更新副标题
    content = content.replace(
      /updateSubtitle:\s*"[^"]*"/,
      `updateSubtitle: "${updateInfo.subtitle}"`
    );
    
    // 更新说明
    const notesString = updateInfo.notes.map(note => `      "${note}"`).join(',\n');
    content = content.replace(
      /updateNotes:\s*\[[^\]]*\]/s,
      `updateNotes: [\n${notesString}\n    ]`
    );
    
    // 更新强制更新标志
    content = content.replace(
      /forceUpdate:\s*[^,]*/,
      `forceUpdate: ${updateInfo.forceUpdate}`
    );
    
    // 更新时间戳
    content = content.replace(
      /releaseDate:\s*"[^"]*"/,
      `releaseDate: "${updateInfo.releaseDate}"`
    );
    
    fs.writeFileSync(WORKER_FILE, content);
    log('✅ Worker 文件已更新', 'green');
    return true;
  } catch (error) {
    log(`❌ 更新 Worker 文件失败: ${error.message}`, 'red');
    return false;
  }
}

// 部署到 Cloudflare
function deployWorker() {
  try {
    log('🚀 部署到 Cloudflare Workers...', 'blue');
    execSync('wrangler deploy --env production', { stdio: 'inherit' });
    log('✅ 部署成功！', 'green');
    return true;
  } catch (error) {
    log(`❌ 部署失败: ${error.message}`, 'red');
    return false;
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  log('📝 NaviBatch 更新内容管理工具', 'cyan');
  log('================================', 'cyan');
  
  const config = loadConfig();
  if (!config) return;
  
  switch (command) {
    case 'list':
      // 列出所有版本配置
      log('📋 已配置的版本更新:', 'blue');
      Object.entries(config.versionUpdates).forEach(([version, info]) => {
        log(`\n🔸 版本 ${version}:`, 'yellow');
        log(`   标题: ${info.title}`, 'white');
        log(`   副标题: ${info.subtitle}`, 'white');
        log(`   强制更新: ${info.forceUpdate ? '是' : '否'}`, 'white');
        log(`   发布日期: ${info.releaseDate}`, 'white');
        log(`   更新说明:`, 'white');
        info.notes.forEach(note => log(`     • ${note}`, 'white'));
      });
      break;
      
    case 'add':
      // 添加新版本配置
      const version = args[1];
      if (!version) {
        log('❌ 请指定版本号: npm run manage add 1.0.8', 'red');
        return;
      }
      
      // 交互式添加
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const ask = (question) => new Promise(resolve => rl.question(question, resolve));
      
      try {
        log(`\n📝 为版本 ${version} 添加更新信息:`, 'blue');
        
        const title = await ask('标题: ');
        const subtitle = await ask('副标题: ');
        const forceUpdate = (await ask('强制更新? (y/N): ')).toLowerCase() === 'y';
        
        log('\n📝 更新说明 (每行一条，空行结束):');
        const notes = [];
        let note;
        while ((note = await ask('• ')) !== '') {
          notes.push(note);
        }
        
        const updateInfo = {
          title: title || config.defaultConfig.title,
          subtitle: subtitle || config.defaultConfig.subtitle,
          notes: notes.length > 0 ? notes : config.defaultConfig.notes,
          forceUpdate: forceUpdate,
          releaseDate: new Date().toISOString().split('T')[0]
        };
        
        config.versionUpdates[version] = updateInfo;
        
        if (saveConfig(config)) {
          log(`\n✅ 版本 ${version} 配置已添加`, 'green');
          
          const deploy = (await ask('\n是否立即应用并部署? (y/N): ')).toLowerCase() === 'y';
          if (deploy) {
            if (updateWorkerFile(version, updateInfo)) {
              deployWorker();
            }
          }
        }
      } finally {
        rl.close();
      }
      break;
      
    case 'apply':
      // 应用指定版本的配置
      const targetVersion = args[1];
      if (!targetVersion) {
        log('❌ 请指定版本号: npm run manage apply 1.0.8', 'red');
        return;
      }
      
      const updateInfo = config.versionUpdates[targetVersion];
      if (!updateInfo) {
        log(`❌ 未找到版本 ${targetVersion} 的配置`, 'red');
        return;
      }
      
      log(`📝 应用版本 ${targetVersion} 的配置...`, 'blue');
      if (updateWorkerFile(targetVersion, updateInfo)) {
        if (args.includes('--deploy')) {
          deployWorker();
        } else {
          log('💡 使用 --deploy 参数自动部署', 'yellow');
        }
      }
      break;
      
    case 'edit':
      // 编辑指定版本的配置
      const editVersion = args[1];
      if (!editVersion) {
        log('❌ 请指定版本号: npm run manage edit 1.0.8', 'red');
        return;
      }
      
      if (!config.versionUpdates[editVersion]) {
        log(`❌ 未找到版本 ${editVersion} 的配置`, 'red');
        return;
      }
      
      log(`📝 编辑版本 ${editVersion} 的配置`, 'blue');
      log('💡 提示: 直接回车保持原值不变', 'yellow');
      
      const currentInfo = config.versionUpdates[editVersion];
      const readline2 = require('readline');
      const rl2 = readline2.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const ask2 = (question) => new Promise(resolve => rl2.question(question, resolve));
      
      try {
        const newTitle = await ask2(`标题 [${currentInfo.title}]: `);
        const newSubtitle = await ask2(`副标题 [${currentInfo.subtitle}]: `);
        const newForceUpdate = await ask2(`强制更新 [${currentInfo.forceUpdate ? 'y' : 'n'}]: `);
        
        log('\n📝 更新说明 (当前):');
        currentInfo.notes.forEach((note, i) => log(`  ${i + 1}. ${note}`));
        
        const editNotes = (await ask2('\n是否编辑更新说明? (y/N): ')).toLowerCase() === 'y';
        let newNotes = currentInfo.notes;
        
        if (editNotes) {
          log('📝 输入新的更新说明 (每行一条，空行结束):');
          newNotes = [];
          let note;
          while ((note = await ask2('• ')) !== '') {
            newNotes.push(note);
          }
          if (newNotes.length === 0) newNotes = currentInfo.notes;
        }
        
        // 更新配置
        config.versionUpdates[editVersion] = {
          title: newTitle || currentInfo.title,
          subtitle: newSubtitle || currentInfo.subtitle,
          notes: newNotes,
          forceUpdate: newForceUpdate === 'y' ? true : (newForceUpdate === 'n' ? false : currentInfo.forceUpdate),
          releaseDate: currentInfo.releaseDate
        };
        
        if (saveConfig(config)) {
          log(`\n✅ 版本 ${editVersion} 配置已更新`, 'green');
        }
      } finally {
        rl2.close();
      }
      break;
      
    case 'remove':
      // 删除版本配置
      const removeVersion = args[1];
      if (!removeVersion) {
        log('❌ 请指定版本号: npm run manage remove 1.0.8', 'red');
        return;
      }
      
      if (config.versionUpdates[removeVersion]) {
        delete config.versionUpdates[removeVersion];
        if (saveConfig(config)) {
          log(`✅ 版本 ${removeVersion} 配置已删除`, 'green');
        }
      } else {
        log(`❌ 未找到版本 ${removeVersion} 的配置`, 'red');
      }
      break;
      
    default:
      log('📖 使用说明:', 'yellow');
      log('');
      log('  npm run manage list                    # 列出所有版本配置', 'white');
      log('  npm run manage add 1.0.8              # 添加新版本配置', 'white');
      log('  npm run manage edit 1.0.8             # 编辑版本配置', 'white');
      log('  npm run manage apply 1.0.8 [--deploy] # 应用版本配置', 'white');
      log('  npm run manage remove 1.0.8           # 删除版本配置', 'white');
      log('');
      log('示例:', 'yellow');
      log('  npm run manage add 1.0.8              # 交互式添加版本配置', 'cyan');
      log('  npm run manage apply 1.0.8 --deploy   # 应用配置并部署', 'cyan');
      break;
  }
}

// 运行
main().catch(error => {
  log(`❌ 执行失败: ${error.message}`, 'red');
  process.exit(1);
});
