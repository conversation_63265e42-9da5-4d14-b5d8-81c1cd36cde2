//
//  TestGellertBlvd.swift
//  NaviBatch
//
//  Created by Augment Agent on 2024-12-26.
//  测试928 Gellert Blvd地址问题的修复
//

import Foundation
import CoreLocation
import MapKit

class TestGellertBlvd {
    
    /// 测试928 Gellert Blvd地址问题
    static func testGellertBlvdFix() async {
        print("🧪 测试928 Gellert Blvd地址修复")
        print(String(repeating: "=", count: 60))
        
        let problematicAddress = "928 Gellert Blvd, Daly City, CA, 94015"
        
        print("📍 问题地址: \(problematicAddress)")
        print("🎯 期望结果: 能够找到正确的坐标")
        print("")
        
        // 测试修复后的UniversalAddressProcessor
        print("🔬 测试修复后的UniversalAddressProcessor:")
        let result = await UniversalAddressProcessor.shared.processGlobalAddress(problematicAddress)
        
        switch result {
        case .success(let originalAddress, let formattedAddress, let coordinate, let placemark, let strategy, let confidence):
            print("✅ 地址处理成功!")
            print("   策略: \(strategy)")
            print("   置信度: \(confidence)")
            print("   原始地址: \(originalAddress)")
            print("   格式化地址: \(formattedAddress)")
            print("   坐标: (\(coordinate.latitude), \(coordinate.longitude))")
            print("")
            
            print("🔍 详细信息:")
            print("   门牌号: \(placemark.subThoroughfare ?? "无")")
            print("   街道: \(placemark.thoroughfare ?? "无")")
            print("   城市: \(placemark.locality ?? "无")")
            print("   州: \(placemark.administrativeArea ?? "无")")
            print("   邮编: \(placemark.postalCode ?? "无")")
            print("   国家: \(placemark.country ?? "无")")
            print("")
            
            // 验证结果是否正确
            if let thoroughfare = placemark.thoroughfare,
               let locality = placemark.locality {
                
                let isCorrectStreet = thoroughfare.lowercased().contains("gellert")
                let isCorrectCity = locality.lowercased().contains("daly city")
                
                if isCorrectStreet && isCorrectCity {
                    print("🎉 修复成功! 找到了正确的地址")
                    print("   ✅ 街道匹配: \(thoroughfare)")
                    print("   ✅ 城市匹配: \(locality)")
                } else {
                    print("⚠️ 结果可能不正确:")
                    print("   街道匹配: \(isCorrectStreet) (\(thoroughfare))")
                    print("   城市匹配: \(isCorrectCity) (\(locality))")
                }
            }
            
        case .failed(let address, let reason):
            print("❌ 地址处理仍然失败:")
            print("   地址: \(address)")
            print("   原因: \(reason)")
            print("")
            print("🔧 需要进一步调试...")
        }
        
        print("")
        print(String(repeating: "=", count: 60))
        print("🧪 测试完成")
    }
    
    /// 比较修复前后的差异
    static func compareBeforeAfterFix() async {
        print("🧪 比较修复前后的差异")
        print(String(repeating: "=", count: 60))
        
        let testAddresses = [
            "928 Gellert Blvd, Daly City, CA, 94015",
            "500 King Dr, Daly City, CA, 94015",
            "123 Main St, San Francisco, CA, 94102"
        ]
        
        for (index, address) in testAddresses.enumerated() {
            print("📍 测试地址 \(index + 1): \(address)")
            
            let result = await UniversalAddressProcessor.shared.processGlobalAddress(address)
            
            switch result {
            case .success(_, _, let coordinate, let placemark, let strategy, let confidence):
                print("   ✅ 成功 - 策略: \(strategy)")
                print("   📍 坐标: (\(coordinate.latitude), \(coordinate.longitude))")
                print("   🎯 置信度: \(confidence)")
                
                if let thoroughfare = placemark.thoroughfare,
                   let locality = placemark.locality {
                    print("   🏠 \(thoroughfare), \(locality)")
                }
                
            case .failed(_, let reason):
                print("   ❌ 失败: \(reason)")
            }
            
            print("")
        }
        
        print(String(repeating: "=", count: 60))
        print("🧪 比较测试完成")
    }
}
