//
//  AddressStandardizationTest.swift
//  NaviBatch
//
//  Created by Augment Agent on 2024-12-26.
//  测试Apple Maps地址标准化行为
//

import Foundation
import CoreLocation
import MapKit

class AddressStandardizationTest {

    /// 测试928 Gellert Blvd修复
    static func testGellertBlvdFix() async {
        print("🧪 测试928 Gellert Blvd地址修复")
        print(String(repeating: "=", count: 60))

        let problematicAddress = "928 Gellert Blvd, Daly City, CA, 94015"

        print("📍 问题地址: \(problematicAddress)")
        print("🎯 期望结果: 能够找到正确的坐标")
        print("")

        // 测试修复后的UniversalAddressProcessor
        print("🔬 测试修复后的UniversalAddressProcessor:")
        let result = await UniversalAddressProcessor.shared.processGlobalAddress(problematicAddress)

        switch result {
        case .success(let originalAddress, let formattedAddress, let coordinate, let placemark, let strategy, let confidence):
            print("✅ 地址处理成功!")
            print("   策略: \(strategy)")
            print("   置信度: \(confidence)")
            print("   原始地址: \(originalAddress)")
            print("   格式化地址: \(formattedAddress)")
            print("   坐标: (\(coordinate.latitude), \(coordinate.longitude))")
            print("")

            print("🔍 详细信息:")
            print("   门牌号: \(placemark.subThoroughfare ?? "无")")
            print("   街道: \(placemark.thoroughfare ?? "无")")
            print("   城市: \(placemark.locality ?? "无")")
            print("   州: \(placemark.administrativeArea ?? "无")")
            print("   邮编: \(placemark.postalCode ?? "无")")
            print("   国家: \(placemark.country ?? "无")")
            print("")

            // 验证结果是否正确
            if let thoroughfare = placemark.thoroughfare,
               let locality = placemark.locality {

                let isCorrectStreet = thoroughfare.lowercased().contains("gellert")
                let isCorrectCity = locality.lowercased().contains("daly city")

                if isCorrectStreet && isCorrectCity {
                    print("🎉 修复成功! 找到了正确的地址")
                    print("   ✅ 街道匹配: \(thoroughfare)")
                    print("   ✅ 城市匹配: \(locality)")
                } else {
                    print("⚠️ 结果可能不正确:")
                    print("   街道匹配: \(isCorrectStreet) (\(thoroughfare))")
                    print("   城市匹配: \(isCorrectCity) (\(locality))")
                }
            }

        case .failed(let address, let reason):
            print("❌ 地址处理仍然失败:")
            print("   地址: \(address)")
            print("   原因: \(reason)")
            print("")
            print("🔧 需要进一步调试...")
        }

        print("")
        print(String(repeating: "=", count: 60))
        print("🧪 测试完成")
    }

    /// 测试Apple Maps地址标准化
    static func testAppleMapsStandardization() async {
        print("🧪 开始测试Apple Maps地址标准化")
        print(String(repeating: "=", count: 60))

        let testAddress = "500 King Drive, Daly City, CA, United States"

        print("📍 测试地址: \(testAddress)")
        print("")

        // 使用UniversalAddressProcessor处理地址
        let result = await UniversalAddressProcessor.shared.processGlobalAddress(testAddress)

        switch result {
        case .success(let originalAddress, let formattedAddress, let coordinate, let placemark, let strategy, let confidence):
            print("✅ 地址处理成功")
            print("   策略: \(strategy)")
            print("   置信度: \(confidence)")
            print("   原始地址: \(originalAddress)")
            print("   格式化地址: \(formattedAddress)")
            print("   坐标: (\(coordinate.latitude), \(coordinate.longitude))")
            print("")

            print("🔍 Placemark详细信息:")
            print("   subThoroughfare: '\(placemark.subThoroughfare ?? "nil")'")
            print("   thoroughfare: '\(placemark.thoroughfare ?? "nil")'")
            print("   locality: '\(placemark.locality ?? "nil")'")
            print("   subAdministrativeArea: '\(placemark.subAdministrativeArea ?? "nil")'")
            print("   administrativeArea: '\(placemark.administrativeArea ?? "nil")'")
            print("   postalCode: '\(placemark.postalCode ?? "nil")'")
            print("   country: '\(placemark.country ?? "nil")'")
            print("   isoCountryCode: '\(placemark.isoCountryCode ?? "nil")'")
            print("")

            // 测试结构化地址填充
            print("🏗️ 测试结构化地址填充:")
            let testPoint = DeliveryPoint(
                sort_number: 1,
                streetName: "测试地址",
                latitude: coordinate.latitude,
                longitude: coordinate.longitude
            )

            print("📋 填充前的字段:")
            printDeliveryPointFields(testPoint)

            testPoint.populateStructuredAddress(from: placemark)

            print("📋 填充后的字段:")
            printDeliveryPointFields(testPoint)

            // 验证Drive是否被标准化为Dr
            if let thoroughfare = placemark.thoroughfare {
                if thoroughfare.contains("Dr") && !thoroughfare.contains("Drive") {
                    print("✅ 确认: Apple Maps将'Drive'标准化为'Dr'")
                    print("   标准化后的街道名: \(thoroughfare)")
                } else if thoroughfare.contains("Drive") {
                    print("⚠️ 注意: 街道名仍包含完整的'Drive'")
                    print("   街道名: \(thoroughfare)")
                } else {
                    print("ℹ️ 街道名: \(thoroughfare)")
                }
            }

        case .failed(let address, let reason):
            print("❌ 地址处理失败")
            print("   地址: \(address)")
            print("   原因: \(reason)")
        }

        print("")
        print(String(repeating: "=", count: 60))
        print("🧪 测试完成")
    }

    /// 打印DeliveryPoint的结构化字段
    private static func printDeliveryPointFields(_ point: DeliveryPoint) {
        print("   streetNumber: '\(point.streetNumber ?? "nil")'")
        print("   streetName: '\(point.streetName ?? "nil")'")
        print("   suburb: '\(point.suburb ?? "nil")'")
        print("   city: '\(point.city ?? "nil")'")
        print("   state: '\(point.state ?? "nil")'")
        print("   postalCode: '\(point.postalCode ?? "nil")'")
        print("   country: '\(point.country ?? "nil")'")
        print("   countryCode: '\(point.countryCode ?? "nil")'")
        print("   unitNumber: '\(point.unitNumber ?? "nil")'")
    }

    /// 测试多个地址的标准化行为
    static func testMultipleAddresses() async {
        print("🧪 测试多个地址的标准化行为")
        print(String(repeating: "=", count: 60))

        let testAddresses = [
            "500 King Drive, Daly City, CA, United States",
            "123 Main Street, San Francisco, CA, United States",
            "456 Oak Avenue, Los Angeles, CA, United States",
            "789 Pine Road, Seattle, WA, United States"
        ]

        for (index, address) in testAddresses.enumerated() {
            print("📍 测试地址 \(index + 1): \(address)")

            let result = await UniversalAddressProcessor.shared.processGlobalAddress(address)

            switch result {
            case .success(_, _, _, let placemark, _, _):
                if let thoroughfare = placemark.thoroughfare {
                    print("   街道名: \(thoroughfare)")

                    // 检查标准化
                    let standardizations = [
                        ("Drive", "Dr"),
                        ("Street", "St"),
                        ("Avenue", "Ave"),
                        ("Road", "Rd"),
                        ("Boulevard", "Blvd"),
                        ("Circle", "Cir"),
                        ("Court", "Ct")
                    ]

                    for (full, abbrev) in standardizations {
                        if address.contains(full) && thoroughfare.contains(abbrev) && !thoroughfare.contains(full) {
                            print("   ✅ 标准化: '\(full)' → '\(abbrev)'")
                        }
                    }
                } else {
                    print("   ⚠️ 无街道名信息")
                }

            case .failed(_, let reason):
                print("   ❌ 失败: \(reason)")
            }

            print("")
        }

        print(String(repeating: "=", count: 60))
        print("🧪 多地址测试完成")
    }

    /// 测试Apple Maps界面 vs API的差异
    static func testAppleMapsInterfaceVsAPI() async {
        print("🧪 测试Apple Maps界面 vs API差异")
        print(String(repeating: "=", count: 60))

        let problematicAddress = "928 Gellert Blvd, Daly City, CA, 94015"

        print("📍 问题地址: \(problematicAddress)")
        print("")

        // 测试1: 直接使用CLGeocoder
        print("🔬 测试1: 直接CLGeocoder")
        await testDirectCLGeocoder(problematicAddress)
        print("")

        // 测试2: 使用MKLocalSearch
        print("🔬 测试2: MKLocalSearch")
        await testMKLocalSearch(problematicAddress)
        print("")

        // 测试3: 使用我们的UniversalAddressProcessor
        print("🔬 测试3: UniversalAddressProcessor")
        await testUniversalProcessor(problematicAddress)
        print("")

        print(String(repeating: "=", count: 60))
        print("🧪 Apple Maps差异测试完成")
    }

    /// 测试直接CLGeocoder
    private static func testDirectCLGeocoder(_ address: String) async {
        let geocoder = CLGeocoder()

        do {
            let placemarks = try await geocoder.geocodeAddressString(
                address,
                in: nil,
                preferredLocale: Locale(identifier: "en_US")
            )

            if let placemark = placemarks.first {
                print("✅ CLGeocoder成功:")
                print("   门牌号: \(placemark.subThoroughfare ?? "无")")
                print("   街道: \(placemark.thoroughfare ?? "无")")
                print("   城市: \(placemark.locality ?? "无")")
                print("   州: \(placemark.administrativeArea ?? "无")")
                print("   坐标: (\(placemark.location?.coordinate.latitude ?? 0), \(placemark.location?.coordinate.longitude ?? 0))")
            } else {
                print("❌ CLGeocoder无结果")
            }
        } catch {
            print("❌ CLGeocoder失败: \(error.localizedDescription)")
        }
    }

    /// 测试MKLocalSearch
    private static func testMKLocalSearch(_ address: String) async {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = address
        request.resultTypes = [MKLocalSearch.Request.ResultType.address]

        let search = MKLocalSearch(request: request)

        do {
            let response = try await search.start()

            if let mapItem = response.mapItems.first {
                let placemark = mapItem.placemark
                print("✅ MKLocalSearch成功:")
                print("   门牌号: \(placemark.subThoroughfare ?? "无")")
                print("   街道: \(placemark.thoroughfare ?? "无")")
                print("   城市: \(placemark.locality ?? "无")")
                print("   州: \(placemark.administrativeArea ?? "无")")
                print("   坐标: (\(placemark.coordinate.latitude), \(placemark.coordinate.longitude))")
            } else {
                print("❌ MKLocalSearch无结果")
            }
        } catch {
            print("❌ MKLocalSearch失败: \(error.localizedDescription)")
        }
    }

    /// 测试UniversalAddressProcessor
    private static func testUniversalProcessor(_ address: String) async {
        let result = await UniversalAddressProcessor.shared.processGlobalAddress(address)

        switch result {
        case .success(let originalAddress, let formattedAddress, let coordinate, let placemark, let strategy, let confidence):
            print("✅ UniversalProcessor成功:")
            print("   策略: \(strategy)")
            print("   置信度: \(confidence)")
            print("   原始地址: \(originalAddress)")
            print("   格式化地址: \(formattedAddress)")
            print("   门牌号: \(placemark.subThoroughfare ?? "无")")
            print("   街道: \(placemark.thoroughfare ?? "无")")
            print("   城市: \(placemark.locality ?? "无")")
            print("   州: \(placemark.administrativeArea ?? "无")")
            print("   坐标: (\(coordinate.latitude), \(coordinate.longitude))")

        case .failed(let address, let reason):
            print("❌ UniversalProcessor失败:")
            print("   地址: \(address)")
            print("   原因: \(reason)")
        }
    }
}
