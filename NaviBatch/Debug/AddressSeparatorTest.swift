import Foundation

/// 地址分隔符格式化测试
/// 测试SpeedX专用的州简称格式优化：City,CA 而不是 City, CA
class AddressSeparatorTest {
    
    /// 测试地址分隔符标准化
    static func testAddressSeparatorStandardization() {
        print("🧪 开始测试地址分隔符标准化")
        print(String(repeating: "=", count: 60))
        
        let testCases = [
            // SpeedX常见地址格式
            ("455 eastmoor ave 315, Daly City, CA", "455 eastmoor ave 315, Daly City,CA"),
            ("20 Louvaine Pl, Daly City, CA", "20 Louvaine Pl, Daly City,CA"),
            ("272 88th st, San Francisco, CA", "272 88th st, San Francisco,CA"),
            ("1260 Edgeworth ave, Pacifica, CA", "1260 Edgeworth ave, Pacifica,CA"),
            
            // 其他州的测试
            ("123 Main St, New York, NY", "123 Main St, New York,NY"),
            ("456 Oak Ave, Austin, TX", "456 Oak Ave, Austin,TX"),
            ("789 Pine Dr, Miami, FL", "789 Pine Dr, Miami,FL"),
            
            // 多个逗号的情况
            ("123 Main St, Apt 2, Los Angeles, CA", "123 Main St, Apt 2, Los Angeles,CA"),
            ("456 Oak Ave, Suite 100, Seattle, WA", "456 Oak Ave, Suite 100, Seattle,WA"),
            
            // 已经是正确格式的
            ("123 Main St, Boston,MA", "123 Main St, Boston,MA"),
            ("456 Oak Ave, Chicago,IL", "456 Oak Ave, Chicago,IL"),
            
            // 多余空格的情况
            ("123 Main St,   Denver,   CO", "123 Main St, Denver,CO"),
            ("456 Oak Ave  ,  Portland  ,  OR", "456 Oak Ave, Portland,OR"),
            
            // 非美国地址（应该保持原格式）
            ("123 Main St, Toronto, ON", "123 Main St, Toronto, ON"),
            ("456 Oak Ave, Sydney, NSW", "456 Oak Ave, Sydney, NSW"),
        ]
        
        for (index, (input, expected)) in testCases.enumerated() {
            let result = standardizeAddressSeparators(input)
            let success = result == expected
            
            print("📍 测试 \(index + 1): \(success ? "✅" : "❌")")
            print("   输入: '\(input)'")
            print("   期望: '\(expected)'")
            print("   结果: '\(result)'")
            
            if !success {
                print("   ⚠️  格式化结果与期望不符")
            }
            print("")
        }
        
        print("🎯 测试完成")
    }
    
    /// 模拟FirebaseAIService中的standardizeAddressSeparators方法
    private static func standardizeAddressSeparators(_ address: String) -> String {
        var standardized = address
        
        // 🎯 SpeedX专用优化：州简称前不加空格 "City,CA" 而不是 "City, CA"
        // 检测美国州简称模式并去掉前面的空格
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]
        
        // 对于美国州简称，使用无空格格式：City,CA
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }
        
        // 标准化其他空格和逗号
        standardized = standardized
            .replacingOccurrences(of: "\\s*,\\s*", with: ", ", options: .regularExpression)
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 再次应用州简称规则（因为上面的标准化可能又加了空格）
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }
        
        return standardized
    }
    
    /// 测试特定的SpeedX地址案例
    static func testSpeedXSpecificCases() {
        print("🚀 开始测试SpeedX特定地址案例")
        print(String(repeating: "=", count: 60))
        
        let speedxCases = [
            // 从用户日志中的实际案例
            "455 eastmoor ave 315, Daly City, CA",
            "20 Louvaine Pl, Daly City, CA", 
            "20 Louvaine PI, Daly City, CA",
            "1260 Edgeworth ave Daly City 307, Daly City, CA",
            "272 88th st, San Francisco, CA"
        ]
        
        for (index, address) in speedxCases.enumerated() {
            let result = standardizeAddressSeparators(address)
            print("📍 SpeedX案例 \(index + 1):")
            print("   原始: '\(address)'")
            print("   优化: '\(result)'")
            
            // 验证CA前面没有空格
            let hasCorrectFormat = result.contains(",CA") && !result.contains(", CA")
            print("   格式: \(hasCorrectFormat ? "✅ 正确 (,CA)" : "❌ 错误 (, CA)")")
            print("")
        }
        
        print("🎯 SpeedX测试完成")
    }
}

// 运行测试的扩展
extension AddressSeparatorTest {
    /// 运行所有测试
    static func runAllTests() {
        print("🧪 地址分隔符格式化测试套件")
        print(String(repeating: "=", count: 80))
        print("")
        
        testAddressSeparatorStandardization()
        print("")
        testSpeedXSpecificCases()
        
        print(String(repeating: "=", count: 80))
        print("✅ 所有测试完成")
    }
}
