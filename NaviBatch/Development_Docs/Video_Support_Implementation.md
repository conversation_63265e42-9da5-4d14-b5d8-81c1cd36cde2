# 视频支持功能实现文档

## 概述
为NaviBatch扫描器添加视频支持功能，允许用户上传配送应用的视频文件，系统会自动提取关键帧进行地址识别。

## 实现日期
2025-06-23

## 技术实现

### 1. 核心修改

#### 1.1 导入新框架
```swift
import AVFoundation
import AVKit
```

#### 1.2 添加Movie传输类型
```swift
struct Movie: Transferable {
    let url: URL
    
    static var transferRepresentation: some TransferRepresentation {
        FileRepresentation(contentType: .movie) { movie in
            SentTransferredFile(movie.url)
        } importing: { received in
            let copy = URL.documentsDirectory.appending(path: "movie.mov")
            if FileManager.default.fileExists(atPath: copy.path()) {
                try FileManager.default.removeItem(at: copy)
            }
            try FileManager.default.copyItem(at: received.file, to: copy)
            return Self.init(url: copy)
        }
    }
}
```

#### 1.3 更新PhotosPicker支持
```swift
// 原来只支持图片
PhotosPicker(selection: $selectedItems, matching: .images, photoLibrary: .shared())

// 现在支持图片和视频
PhotosPicker(selection: $selectedItems, matching: .any(of: [.images, .videos]), photoLibrary: .shared())
```

### 2. 视频帧提取功能

#### 2.1 新增状态变量
```swift
@State private var selectedVideos: [AVAsset] = []
@State private var videoFrames: [UIImage] = []
```

#### 2.2 视频帧提取方法
```swift
private func extractFramesFromVideo(_ asset: AVAsset) async -> [UIImage] {
    let generator = AVAssetImageGenerator(asset: asset)
    generator.appliesPreferredTrackTransform = true
    generator.requestedTimeToleranceBefore = .zero
    generator.requestedTimeToleranceAfter = .zero
    
    // 提取3个关键帧：开始(10%)、中间(50%)、结束(90%)
    let times = [
        CMTime(seconds: durationSeconds * 0.1, preferredTimescale: 600),
        CMTime(seconds: durationSeconds * 0.5, preferredTimescale: 600),
        CMTime(seconds: durationSeconds * 0.9, preferredTimescale: 600)
    ]
    
    // 提取帧并转换为UIImage
    for time in times {
        let cgImage = try await generator.image(at: time).image
        let uiImage = UIImage(cgImage: cgImage)
        frames.append(uiImage)
    }
    
    return frames
}
```

### 3. 媒体文件处理流程

#### 3.1 统一媒体加载方法
```swift
private func loadMediaItems(from items: [PhotosPickerItem]) async {
    for item in items {
        if item.supportedContentTypes.contains(.movie) || 
           item.supportedContentTypes.contains(.video) ||
           item.supportedContentTypes.contains(.quickTimeMovie) {
            // 处理视频文件
            if let movie = try await item.loadTransferable(type: Movie.self) {
                let asset = AVAsset(url: movie.url)
                let frames = await extractFramesFromVideo(asset)
                loadedImages.append(contentsOf: frames)
            }
        } else {
            // 处理图片文件
            if let data = try await item.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                loadedImages.append(image)
            }
        }
    }
}
```

### 4. 用户界面更新

#### 4.1 更新选择器提示文本
- 原来：`"select_delivery_screenshots"` (选择派送应用截图)
- 现在：`"select_delivery_media"` (选择派送应用截图/视频)

#### 4.2 更新支持格式说明
- 原来：`"amazon_flex_imile_etc"` (纯图片 • Amazon Flex • iMile)
- 现在：`"amazon_flex_imile_video_support"` (纯图片 • Amazon Flex • iMile（截图和视频）)

### 5. 本地化字符串更新

#### 5.1 英文版本 (en.lproj/Localizable.strings)
```
"select_delivery_media" = "Select Delivery App Screenshots/Videos";
"amazon_flex_imile_video_support" = "Just Photo • Amazon Flex • iMile (Screenshots & Videos)";
"loading_media" = "Loading media files...";
"processing_media_progress" = "Processing media %d/%d";
"no_media_loaded" = "No media files loaded";
```

#### 5.2 中文版本 (zh-Hans.lproj/Localizable.strings)
```
"select_delivery_media" = "选择派送应用截图/视频";
"amazon_flex_imile_video_support" = "纯图片 • Amazon Flex • iMile（截图和视频）";
"loading_media" = "正在加载媒体文件...";
"processing_media_progress" = "正在处理第 %d 个媒体文件，共 %d 个";
"no_media_loaded" = "未加载到媒体文件";
```

## 功能特点

### 1. 智能帧提取
- 自动从视频中提取3个关键帧（10%、50%、90%位置）
- 保持原有的图片处理流程不变
- 提取的帧与普通图片一样进行AI识别

### 2. 用户体验优化
- 统一的媒体选择界面
- 清晰的进度提示
- 支持混合选择（图片+视频）

### 3. 技术优势
- 复用现有的图片识别管道
- 最小化代码修改
- 保持向后兼容性

## 测试建议

1. **功能测试**
   - 测试纯图片选择
   - 测试纯视频选择
   - 测试图片+视频混合选择

2. **性能测试**
   - 测试大视频文件的处理时间
   - 测试多个视频文件的并发处理
   - 测试内存使用情况

3. **用户体验测试**
   - 测试进度提示的准确性
   - 测试错误处理机制
   - 测试本地化文本显示

## 后续优化建议

1. **帧选择优化**
   - 允许用户手动选择视频中的特定时间点
   - 基于运动检测选择最佳帧
   - 支持更多帧数提取选项

2. **性能优化**
   - 视频预处理和压缩
   - 异步帧提取
   - 缓存机制

3. **用户界面增强**
   - 视频预览功能
   - 帧选择界面
   - 处理进度的详细显示

## 🎯 Gemma 3 视频支持发现

### 新发现 (2025-06-23)
根据最新研究，**Gemma 3 原生支持视频帧处理**：

1. **视频帧序列支持**：
   - 可以接受带时间戳的视频帧序列
   - 格式：`"Frame 00.00: <image>, Frame 00.05: <image>..."`
   - 比单独处理帧更智能

2. **优化方向**：
   - 考虑修改GemmaVisionService直接支持视频帧序列
   - 利用时间戳信息提高识别准确性
   - 减少多次API调用，提高效率

3. **技术优势**：
   - 更好的上下文理解（帧之间的关联）
   - 可以识别视频中的动态变化
   - 支持时间戳定位功能

### 实现建议
```swift
// 未来可以考虑的视频帧序列处理
func processVideoFrameSequence(_ frames: [(UIImage, TimeInterval)]) async throws -> GemmaAddressResult {
    let framePrompts = frames.enumerated().map { index, (image, timestamp) in
        "Frame \(String(format: "%.2f", timestamp)): <image>"
    }
    // 发送带时间戳的帧序列给Gemma 3
}
```

## 🔧 问题修复记录

### PhotosPicker Delegate 错误修复 (2025-06-23)

#### 问题描述
用户遇到错误：`PHPickerViewControllerDelegate_Private doesn't respond to _pickerDidPerformConfirmationAction`

#### 修复措施

1. **PhotosPicker配置优化**：
```swift
// 修复前
PhotosPicker(selection: $selectedItems, matching: .any(of: [.images, .videos]), photoLibrary: .shared())

// 修复后
if #available(iOS 16.0, *) {
    PhotosPicker(
        selection: $selectedItems,
        maxSelectionCount: 50,
        matching: .any(of: [.images, .videos])
    )
} else {
    PhotosPicker(
        selection: $selectedItems,
        maxSelectionCount: 50,
        matching: .images
    )
}
```

2. **视频检测方式改进**：
```swift
// 更安全的视频检测
let isVideo = item.supportedContentTypes.contains { contentType in
    contentType.conforms(to: .movie) ||
    contentType.conforms(to: .video) ||
    contentType.identifier.contains("video") ||
    contentType.identifier.contains("movie")
}
```

3. **图片加载多重备用方案**：
```swift
// 方式1：直接加载UIImage
if let image = try? await item.loadTransferable(type: UIImage.self) {
    loadedImages.append(image)
}
// 方式2：加载Data后转换
else if let data = try? await item.loadTransferable(type: Data.self),
        let image = UIImage(data: data) {
    loadedImages.append(image)
}
```

4. **Movie类型修复**：
```swift
// 使用临时目录避免权限问题
let tempDir = FileManager.default.temporaryDirectory
let copy = tempDir.appendingPathComponent("movie_\(UUID().uuidString).mov")
```

5. **增强调试信息**：
```swift
print("📊 媒体加载完成统计:")
print("   - 选择的项目数: \(items.count)")
print("   - 加载的图片数: \(loadedImages.count)")
print("   - 加载的视频数: \(loadedVideos.count)")
```

#### 修复效果
- ✅ 解决PhotosPicker delegate错误
- ✅ 提高媒体文件加载成功率
- ✅ 增强iOS版本兼容性
- ✅ 改善错误诊断能力

## 编译状态
✅ 编译成功 (2025-06-23 01:02:35)
✅ Gemma 3 视频支持确认 (2025-06-23 01:15:00)
✅ PhotosPicker错误修复 (2025-06-23 01:30:00)
