# 第三方排序保存按钮功能

## 🎯 功能概述

根据用户需求，在路线优化结果界面的第三方排序按钮旁边添加了"Save"按钮，当用户点击Save按钮时，将当前第三方排序的顺序保存到`sorted_number`字段中。

## ✅ 完成的修改

### 1. 本地化字符串添加

**文件**: `NaviBatch/Localizations/en.lproj/Localizable.strings`
```
"save_third_party_sort" = "Save";
```

**文件**: `NaviBatch/Localizations/zh-Hans.lproj/Localizable.strings`
```
"save_third_party_sort" = "保存";
```

**文件**: `NaviBatch/Localizations/zh-Hant.lproj/Localizable.strings`
```
"save_third_party_sort" = "保存";
```

**文件**: `NaviBatch/Localizations/zh-CN.lproj/Localizable.strings`
```
"save_third_party_sort" = "保存";
```

### 2. 路线优化结果界面UI增强

**文件**: `NaviBatch/Views/Components/OptimizationResultSheet.swift`

#### 2.1 Save按钮UI添加
在`optimizedRouteListCard`的HStack中，在第三方排序按钮和取消优化按钮之间添加了Save按钮：

```swift
// Save按钮 - 只在第三方排序模式下显示
if isThirdPartySorted {
    Button {
        saveThirdPartySorting()
    } label: {
        HStack(spacing: 4) {
            Image(systemName: "checkmark")
                .font(.system(size: 14))
            Text("save_third_party_sort".localized)
                .font(.subheadline)
        }
        .foregroundColor(.green)
    }
    .buttonStyle(.plain)
}
```

#### 2.2 Save功能实现
添加了 `saveThirdPartySorting()` 方法：

```swift
// 保存第三方排序到sorted_number
private func saveThirdPartySorting() {
    logInfo("OptimizationResultSheet - saveThirdPartySorting: 开始保存第三方排序")

    guard let currentRoute = viewModel.currentRoute else {
        logError("OptimizationResultSheet - saveThirdPartySorting: 当前路线为空")
        return
    }

    // 获取所有配送点（非起点）
    let deliveryPoints = currentRoute.points.filter({ !$0.isStartPoint && $0.sort_number != 0 })

    var savedCount = 0

    // 遍历每个配送点，将thirdPartySortNumber复制到sorted_number
    for point in deliveryPoints {
        if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
            let oldSortedNumber = point.sorted_number

            // 提取第三方排序号中的数字部分
            let gofoNumber = extractNumber(from: thirdPartySort)

            // 将第三方排序号复制到sorted_number
            point.sorted_number = gofoNumber
            point.isOptimized = true // 标记为已优化

            savedCount += 1
        }
    }

    // 标记路线为已优化
    currentRoute.isOptimized = true

    // 保存更改到数据库
    do {
        try modelContext.save()

        // 更新ViewModel中的数据
        viewModel.deliveryPoints = currentRoute.points.sorted { point1, point2 in
            // 起点排在最前面
            if (point1.isStartPoint || point1.sort_number == 0) && !(point2.isStartPoint || point2.sort_number == 0) {
                return true
            } else if !(point1.isStartPoint || point1.sort_number == 0) && (point2.isStartPoint || point2.sort_number == 0) {
                return false
            }

            // 其他点按sorted_number排序
            return point1.sorted_number < point2.sorted_number
        }
        viewModel.objectWillChange.send()

        // 发送通知，通知其他组件路线数据已更新
        NotificationCenter.default.post(
            name: Notification.Name("RouteDataChanged"),
            object: currentRoute.id.uuidString
        )

        // 退出第三方排序预览模式，显示保存后的结果
        displayedPoints = optimizedPoints
        isThirdPartySorted = false

    } catch {
        logError("OptimizationResultSheet - saveThirdPartySorting: 保存失败 - \(error.localizedDescription)")
    }
}
```

## 🎨 UI 设计特点

### 按钮样式
- **Save按钮**: 使用绿色 (`.green`) 表示保存操作
- **图标**: `checkmark` 系统图标，表示确认/保存
- **显示条件**: 只在 `isThirdPartySorted` 为 `true` 时显示
- **位置**: 在第三方排序按钮和取消优化按钮之间

### 按钮布局
```
[优化后路线顺序]     [第三方] [保存] [还原]
```

## 🔧 技术实现

### 数据流程
1. **用户操作流程**:
   - 用户点击"第三方排序"按钮 → 进入第三方排序预览模式
   - Save按钮出现
   - 用户点击"Save"按钮 → 调用 `saveThirdPartySorting()` 方法

2. **保存操作**:
   - 遍历所有配送点
   - 将每个点的 `thirdPartySortNumber` 复制到 `sorted_number`
   - 标记点为已优化 (`isOptimized = true`)
   - 标记路线为已优化
   - 保存到数据库
   - 更新ViewModel
   - 发送通知
   - 退出第三方排序预览模式

### 核心逻辑
- **字段复制**: `thirdPartySortNumber` → `sorted_number`
- **数字提取**: 使用 `extractNumber(from:)` 方法提取第三方排序号中的数字部分
- **状态管理**: 保存后自动退出第三方排序预览模式
- **数据同步**: 更新ViewModel并发送通知确保界面同步

## 📱 用户体验

### 操作流程
1. 用户进行路线优化
2. 在优化结果界面点击"第三方排序"按钮
3. 界面显示第三方排序结果，同时出现"Save"按钮
4. 用户可以：
   - **点击Save** - 保存第三方排序到sorted_number，退出预览模式
   - **再次点击第三方排序** - 切换回AI优化结果，Save按钮消失
   - **点击还原** - 恢复原始顺序

### 视觉反馈
- Save按钮使用绿色突出显示保存操作
- 只在第三方排序模式下显示，避免界面混乱
- 保存后自动退出预览模式，显示保存后的结果

## 🌍 多语言支持

已添加中英文本地化支持：
- 英文: "Save"
- 简体中文: "保存"
- 繁体中文: "保存"

## 🔧 问题修复

### 问题描述
初始实现中，Save按钮的逻辑有问题：
- 原逻辑：直接将GoFo号码设置为`sorted_number`
- 问题：这会导致`sorted_number`不连续（如：111, 112, 113, 115...）

### 修复方案
修改`saveThirdPartySorting()`方法，采用与`syncThirdPartyToSortedNumber()`相同的逻辑：

1. **收集第三方排序点**：获取所有有`thirdPartySortNumber`的点
2. **按GoFo号码排序**：按第三方排序号从小到大排序
3. **分配连续编号**：按排序后的顺序分配连续的`sorted_number`（1, 2, 3, 4...）

### 修复后的逻辑
```swift
// 🎯 第三步：按排序后的顺序分配连续的sorted_number（1, 2, 3...）
for (index, item) in pointsWithThirdParty.enumerated() {
    let point = item.point
    let gofoNumber = item.gofoNumber
    let oldSortedNumber = point.sorted_number
    let newSortedNumber = index + 1 // 连续编号：1, 2, 3...

    point.sorted_number = newSortedNumber
    point.isOptimized = true
    syncCount += 1
}
```

## 🎉 总结

此次功能增强完全满足了用户的需求：
1. ✅ **Save按钮位置** - 在第三方排序按钮旁边，位置合理
2. ✅ **功能实现** - 按第三方排序顺序分配连续的sorted_number
3. ✅ **用户体验** - 只在第三方排序模式下显示，操作直观
4. ✅ **数据同步** - 保存后正确更新所有相关数据
5. ✅ **多语言支持** - 支持中英文界面
6. ✅ **状态管理** - 保存后自动退出预览模式
7. ✅ **逻辑修复** - 确保sorted_number连续性，与现有同步逻辑一致

用户现在可以方便地预览第三方排序结果，并在满意时点击Save按钮将第三方排序顺序保存为连续的sorted_number（1, 2, 3...），大大提升了使用的灵活性和便利性。
