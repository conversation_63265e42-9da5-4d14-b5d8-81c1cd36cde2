# 停靠点优先验证策略

## 📋 核心思路

基于用户提供的智能判断逻辑，实现停靠点优先的数据验证策略，确保数据的准确性和完整性。

## 🎯 验证优先级

### 1. 停靠点优先原则
- **停靠点号码是唯一标识**：一个停靠点只能对应一个地址
- **停靠点冲突必须解决**：不同地址不能有相同停靠点号码
- **地址可以重复**：同一收件人可能有多个包裹（不同停靠点）

### 2. 判断流程

```
停靠点存在？
    ↓ 是
停靠点有完整地址？
    ↓ 是  
停靠点重复？
    ↓ 是
地址相同？
    ↓ 否
地址完整？
    ↓ 否
忽略（截图不全）
```

## 🔧 技术实现

### 1. 核心验证方法

```swift
private func resolveStopNumberConflicts() {
    // 1. 按停靠点号码分组
    var stopNumberGroups: [String: [AddressInfo]] = [:]
    
    // 2. 检测冲突
    for (stopNumber, addressGroup) in stopNumberGroups {
        if addressGroup.count > 1 {
            // 应用智能冲突解决策略
            let addressesToKeep = resolveStopNumberConflict(
                stopNumber: stopNumber, 
                addresses: addressGroup
            )
        }
    }
}
```

### 2. 智能冲突解决策略

```swift
private func resolveStopNumberConflict(
    stopNumber: String, 
    addresses: [AddressInfo]
) -> Set<Int> {
    
    // 1. 分析地址完整性
    for address in addresses {
        let isComplete = isAddressComplete(address)
    }
    
    // 2. 按地址分组
    var addressGroups: [String: [AddressInfo]] = [:]
    
    // 3. 应用判断策略
    for (normalizedAddress, sameAddressGroup) in addressGroups {
        if sameAddressGroup.count > 1 {
            // 相同地址+相同停靠点：保留最完整的
            let bestAddress = selectBestAddress(from: sameAddressGroup)
        } else {
            // 不同地址+相同停靠点：检查完整性
            if addressInfo.isComplete {
                // 保留完整地址
            } else {
                // 忽略不完整地址（可能截图不全）
            }
        }
    }
}
```

### 3. 地址完整性判断

```swift
private func isAddressComplete(_ address: String, trackingNumber: String) -> Bool {
    let hasStreetNumber = address.contains(regex: #"\d+"#)
    let hasStreetName = address.count > 10
    let hasCity = address.contains(",")
    let hasTrackingNumber = !trackingNumber.isEmpty
    let hasTruncationMarkers = address.contains("...")
    
    return hasStreetNumber && hasStreetName && hasCity && 
           hasTrackingNumber && !hasTruncationMarkers
}
```

## 📊 实际应用场景

### 场景1：相同地址+相同停靠点（重复数据）
```
输入：
- 123 Main St + 停靠点1 + 追踪号A (完整)
- 123 Main St + 停靠点1 + 追踪号A (完整)

处理：保留第一个，忽略重复
结果：✅ 保留 123 Main St + 停靠点1
```

### 场景2：不同地址+相同停靠点（冲突）
```
输入：
- 123 Main St + 停靠点1 + 追踪号A (完整)
- 456 Oak Ave + 停靠点1 + 追踪号B (完整)

处理：停靠点冲突，保留第一个完整地址
结果：✅ 保留 123 Main St + 停靠点1
      ❌ 忽略 456 Oak Ave + 停靠点1
```

### 场景3：不完整地址+停靠点冲突
```
输入：
- 123 Main St, Daly City, CA + 停靠点1 + 追踪号A (完整)
- 456 Oak... + 停靠点1 (不完整，截图不全)

处理：保留完整地址，忽略不完整地址
结果：✅ 保留 123 Main St + 停靠点1
      ❌ 忽略不完整地址（截图不全）
```

### 场景4：相同地址+不同停靠点（合理）
```
输入：
- 123 Main St + 停靠点1 + 追踪号A
- 123 Main St + 停靠点2 + 追踪号B

处理：同一收件人多个包裹，都保留
结果：✅ 保留 123 Main St + 停靠点1
      ✅ 保留 123 Main St + 停靠点2
```

## 🎯 优势分析

### 1. 智能判断
- **停靠点优先**：确保停靠点号码的唯一性
- **完整性检查**：自动识别截图不全的数据
- **质量优先**：保留最完整、最可靠的数据

### 2. 实用性强
- **符合物流实际**：一个停靠点对应一个地址
- **处理常见问题**：截图不全、数据重复
- **支持多包裹**：同一收件人的多个包裹

### 3. 性能优化
- **分组处理**：按停靠点分组，提高效率
- **智能过滤**：早期过滤无效数据
- **批量操作**：减少重复计算

## 📝 日志示例

```
🎯 开始停靠点冲突解决（停靠点优先策略）
🚨 发现停靠点1冲突：3个地址
🔍 解决停靠点1的冲突（3个地址）
   📍 地址0: 123 Main St, Daly City, CA... 完整性:✅ 置信度:85%
   📍 地址1: 123 Main St, Daly City, CA... 完整性:✅ 置信度:80%
   📍 地址2: 456 Oak... 完整性:❌ 置信度:60%
   🔄 相同地址重复：123 main st daly city ca (2个)
   ✅ 保留最佳地址：索引0 (完整性:✅)
   ❌ 忽略不完整地址：456 oak...
✅ 停靠点冲突解决完成：移除了2个冲突地址
📊 停靠点冲突统计：发现1个冲突，解决2个问题
```

## 🔍 质量保证

### 1. 数据完整性
- 确保每个停靠点只有一个地址
- 保留最完整、最可靠的数据
- 自动过滤截图不全的数据

### 2. 逻辑正确性
- 支持同一收件人多个包裹
- 正确处理地址重复情况
- 智能判断数据质量

### 3. 用户体验
- 详细的处理日志
- 清晰的决策逻辑
- 可追溯的处理过程

这个停靠点优先策略确保了数据验证的智能化和实用性，完美解决了SpeedX派送地址识别中的各种冲突和重复问题。
