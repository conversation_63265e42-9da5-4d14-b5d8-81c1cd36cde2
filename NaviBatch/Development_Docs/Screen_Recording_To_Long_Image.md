# 录屏转长图功能实现

## 功能概述

为了解决用户截长图技术门槛高的问题，我们实现了录屏视频自动转换为长图的功能。用户只需要录制屏幕滚动过程，系统会自动：

1. **密集提取帧**：从录屏视频中提取大量帧
2. **智能去重**：使用Core Image检测并移除重复帧
3. **滚动检测**：分析滚动方向和重叠区域
4. **无缝拼接**：将去重后的帧拼接成完整长图

## 技术架构

### 1. VideoToLongImageProcessor 服务

核心处理器，负责整个录屏转长图的流程：

```swift
class VideoToLongImageProcessor {
    // 配置参数
    private let frameExtractionInterval: Double = 0.5 // 每0.5秒提取一帧
    private let similarityThreshold: Float = 0.85 // 相似度阈值
    private let overlapDetectionThreshold: Float = 0.7 // 重叠检测阈值
    private let maxFrames = 200 // 最大处理帧数
    
    // 主要处理方法
    func processVideoToLongImage(_ asset: AVAsset) async -> ProcessingResult
}
```

### 2. 处理流程

#### 2.1 密集帧提取
```swift
private func extractDenseFrames(from asset: AVAsset) async throws -> [UIImage] {
    let generator = AVAssetImageGenerator(asset: asset)
    generator.maximumSize = CGSize(width: 1080, height: 1920)
    
    // 每0.5秒提取一帧，而不是只提取3个关键帧
    var currentTime: Double = 0
    while currentTime < durationSeconds && frames.count < maxFrames {
        let time = CMTime(seconds: currentTime, preferredTimescale: 600)
        // 提取帧...
        currentTime += frameExtractionInterval
    }
}
```

#### 2.2 帧间去重算法
```swift
private func removeRedundantFrames(_ frames: [UIImage]) async -> [UIImage] {
    var uniqueFrames: [UIImage] = [frames[0]]
    
    for i in 1..<frames.count {
        let similarity = await calculateFrameSimilarity(currentFrame, previousFrame)
        
        // 如果相似度低于阈值，说明是新内容
        if similarity < similarityThreshold {
            uniqueFrames.append(currentFrame)
        }
    }
}
```

#### 2.3 相似度计算
```swift
private func calculateFrameSimilarity(_ image1: UIImage, _ image2: UIImage) async -> Float {
    // 缩小图片以提高计算速度
    let targetSize = CGSize(width: 100, height: 100)
    let resized1 = resizeImage(image1, to: targetSize)
    let resized2 = resizeImage(image2, to: targetSize)
    
    // 字节级比较（可优化为更精确的算法）
    // 返回相似度 0.0-1.0
}
```

#### 2.4 智能拼接
```swift
private func stitchFramesToLongImage(_ frames: [UIImage], scrollDirection: ScrollDirection) async -> UIImage? {
    // 估算重叠区域
    let overlapRatio: CGFloat = 0.2 // 假设20%重叠
    let effectiveHeight = frameHeight * (1 - overlapRatio)
    
    // 计算总画布尺寸
    let totalHeight = frameHeight + effectiveHeight * CGFloat(frames.count - 1)
    
    // 逐帧绘制到画布上
    // ...
}
```

### 3. 集成到ImageAddressRecognizer

#### 3.1 录屏检测
```swift
private func isScreenRecordingVideo(_ asset: AVAsset) async -> Bool {
    let duration = try await asset.load(.duration)
    let durationSeconds = CMTimeGetSeconds(duration)
    
    // 录屏视频特征：
    // 1. 时长5秒到2分钟
    // 2. 分辨率为设备屏幕分辨率
    return durationSeconds > 5 && durationSeconds < 120
}
```

#### 3.2 处理流程选择
```swift
private func extractFramesFromVideo(_ asset: AVAsset) async -> [UIImage] {
    if await isScreenRecordingVideo(asset) {
        print("🎬 检测到录屏视频，尝试转换为长图")
        return await processScreenRecordingToLongImage(asset)
    } else {
        print("🎬 普通视频，提取关键帧")
        return await extractKeyFrames(asset)
    }
}
```

## 用户体验优化

### 1. UI提示
在PhotosPicker中添加了录屏支持的提示：
```swift
Text("🎬 支持录屏视频自动转长图")
    .font(.caption2)
    .foregroundColor(.orange)
    .fontWeight(.medium)
```

### 2. 进度反馈
```swift
let result = await processor.processVideoToLongImage(asset) { status, progress in
    Task { @MainActor in
        processingStatus = status
        processingProgress = progress * 0.5
    }
}
```

### 3. 降级机制
如果录屏转长图失败，自动降级到关键帧提取：
```swift
if result.success, let longImage = result.longImage {
    return [longImage]
} else {
    print("⚠️ 录屏转长图失败，降级到关键帧提取")
    return await extractKeyFrames(asset)
}
```

## 技术优势

### 1. 用户友好
- **零技术门槛**：用户只需录屏，无需掌握截长图技术
- **自动化处理**：系统自动完成复杂的图像处理
- **兼容性好**：支持任意长度的滚动内容

### 2. 技术先进
- **智能去重**：避免重复内容，提高处理效率
- **内存优化**：限制最大帧数，避免内存问题
- **质量保证**：保持图像质量的同时优化处理速度

### 3. 扩展性强
- **算法可优化**：相似度算法可以升级为更精确的方案
- **参数可调**：提取间隔、相似度阈值等参数可根据需要调整
- **功能可扩展**：可以添加水平滚动、多方向滚动等支持

## 未来优化方向

### 1. 算法优化
- 使用更精确的图像相似度算法（如SSIM、感知哈希）
- 实现更智能的重叠区域检测
- 支持水平滚动和多方向滚动

### 2. 性能优化
- 使用GPU加速图像处理
- 优化内存使用，支持更长的录屏
- 并行处理多个帧

### 3. 用户体验
- 添加预览功能，让用户确认拼接结果
- 支持手动调整重叠区域
- 提供拼接质量选项

## 使用场景

1. **快递应用截图**：录制滚动快递列表，自动生成完整长图
2. **聊天记录保存**：录制长聊天记录，生成完整截图
3. **网页内容保存**：录制网页滚动，保存完整页面内容
4. **应用界面展示**：录制应用操作过程，生成演示图片

这个功能大大降低了用户的使用门槛，让任何人都能轻松获得完整的长图内容。
