import SwiftUI
import UIKit

// MARK: - AI集成演示视图
struct AIIntegrationDemo: View {
    @State private var showingImagePicker = false
    @State private var demoResults: [String] = []
    @State private var isProcessing = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(spacing: 12) {
                    Text("🤖 Gemma AI 地址识别演示")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("展示AI智能识别配送地址的强大功能")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()

                // 功能特点
                VStack(alignment: .leading, spacing: 12) {
                    FeatureRow(
                        icon: "brain.head.profile",
                        title: "AI智能识别",
                        description: "使用Gemma 3-27B模型，准确率高达95%",
                        color: .purple
                    )

                    FeatureRow(
                        icon: "arrow.triangle.2.circlepath",
                        title: "智能降级",
                        description: "AI失败时自动切换到传统OCR",
                        color: .blue
                    )

                    FeatureRow(
                        icon: "globe",
                        title: "多语言支持",
                        description: "支持中文、英文等多种语言地址",
                        color: .green
                    )

                    FeatureRow(
                        icon: "speedometer",
                        title: "快速处理",
                        description: "平均处理时间2-5秒",
                        color: .orange
                    )
                }
                .padding()

                // 演示按钮
                VStack(spacing: 16) {
                    Button(action: {
                        showingImagePicker = true
                    }) {
                        HStack {
                            Image(systemName: "photo.on.rectangle.angled")
                            Text("开始AI识别演示")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                    .disabled(isProcessing)

                    Button(action: {
                        runMockDemo()
                    }) {
                        HStack {
                            Image(systemName: "play.circle")
                            Text("运行模拟演示")
                        }
                        .font(.headline)
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(12)
                    }
                    .disabled(isProcessing)
                }
                .padding()

                // 演示结果
                if !demoResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("演示结果:")
                            .font(.headline)

                        ForEach(demoResults, id: \.self) { result in
                            Text("✅ \(result)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.leading)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .padding()
                }

                Spacer()

                // 技术信息
                VStack(spacing: 4) {
                    Text("技术栈")
                        .font(.caption)
                        .fontWeight(.semibold)

                    Text("Gemma 3-27B • OpenRouter API • SwiftUI")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .navigationTitle("AI演示")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingImagePicker) {
            ImageAddressRecognizer(
                onAddressesConfirmed: { addresses, appType in
                    demoResults = addresses.map { $0.0 }
                    showingImagePicker = false
                },
                onDismiss: {
                    showingImagePicker = false
                }
            )
            .presentationDetents([.medium, .large], selection: .constant(.large))
            .presentationDragIndicator(.visible)
            .presentationBackgroundInteraction(.enabled(upThrough: .medium))
        }
    }

    private func runMockDemo() {
        isProcessing = true
        demoResults = []

        // 模拟AI处理过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            demoResults.append("正在使用Gemma 3-27B模型分析...")
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            demoResults.append("识别到地址: 123 Collins Street, Melbourne VIC 3000")
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            demoResults.append("识别到地址: Unit 5/456 Swanston Street, Carlton VIC 3053")
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
            demoResults.append("AI识别完成，准确率: 95%")
            isProcessing = false
        }
    }
}

// MARK: - 功能特点行组件
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 预览
#Preview {
    AIIntegrationDemo()
}
