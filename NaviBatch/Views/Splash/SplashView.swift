import SwiftUI

/// NaviBatch启动闪屏页面
/// 显示品牌logo和加载动画，营造专业的启动体验
struct SplashView: View {
    @State private var logoScale: CGFloat = 0.5
    @State private var logoOpacity: Double = 0.0
    @State private var titleOpacity: Double = 0.0
    @State private var subtitleOpacity: Double = 0.0
    @State private var progressOpacity: Double = 0.0
    @State private var animationProgress: CGFloat = 0.0
    
    let onComplete: () -> Void
    
    var body: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.1),
                    Color.blue.opacity(0.05),
                    Color.clear
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 32) {
                Spacer()
                
                // Logo区域
                VStack(spacing: 24) {
                    // 主Logo图标
                    ZStack {
                        // 背景圆圈动画
                        Circle()
                            .stroke(Color.blue.opacity(0.2), lineWidth: 2)
                            .frame(width: 120, height: 120)
                            .scaleEffect(logoScale * 1.2)
                        
                        Circle()
                            .stroke(Color.blue.opacity(0.1), lineWidth: 1)
                            .frame(width: 140, height: 140)
                            .scaleEffect(logoScale * 1.4)
                        
                        // 中心图标
                        Image(systemName: "location.north.circle.fill")
                            .font(.system(size: 60))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [Color.blue, Color.blue.opacity(0.8)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .scaleEffect(logoScale)
                            .opacity(logoOpacity)
                    }
                    
                    // 应用名称
                    Text("NaviBatch")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .opacity(titleOpacity)
                    
                    // 副标题
                    Text("app_tagline".localized)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.secondary)
                        .opacity(subtitleOpacity)
                }
                
                Spacer()
                
                // 加载进度区域
                VStack(spacing: 16) {
                    // 进度条
                    ZStack(alignment: .leading) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 8)
                        
                        RoundedRectangle(cornerRadius: 4)
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue, Color.blue.opacity(0.8)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: max(0, animationProgress * UIScreen.main.bounds.width * 0.6), height: 8)
                    }
                    .frame(width: UIScreen.main.bounds.width * 0.6)
                    .opacity(progressOpacity)
                    
                    // 加载文本
                    Text("initializing".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                        .opacity(progressOpacity)
                }
                .padding(.bottom, 60)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            startAnimations()
        }
    }
    
    private func startAnimations() {
        // 第一阶段：Logo出现
        withAnimation(.easeOut(duration: 0.8)) {
            logoScale = 1.0
            logoOpacity = 1.0
        }
        
        // 第二阶段：标题出现
        withAnimation(.easeOut(duration: 0.6).delay(0.3)) {
            titleOpacity = 1.0
        }
        
        // 第三阶段：副标题出现
        withAnimation(.easeOut(duration: 0.6).delay(0.6)) {
            subtitleOpacity = 1.0
        }
        
        // 第四阶段：进度条出现
        withAnimation(.easeOut(duration: 0.4).delay(0.9)) {
            progressOpacity = 1.0
        }
        
        // 第五阶段：进度条动画
        withAnimation(.easeInOut(duration: 1.5).delay(1.2)) {
            animationProgress = 1.0
        }
        
        // 完成后跳转
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            withAnimation(.easeInOut(duration: 0.5)) {
                onComplete()
            }
        }
    }
}

#Preview("SplashView") {
    SplashView {
        print("Splash completed")
    }
}
