import SwiftUI

/// NaviBatch欢迎页面
/// 介绍应用核心价值和主要功能，引导用户开始使用
struct WelcomeView: View {
    @State private var currentPage = 0
    @State private var showFeatures = false
    let onComplete: () -> Void
    
    private let features = [
        WelcomeFeature(
            icon: "camera.fill",
            title: "smart_scanning".localized,
            description: "smart_scanning_description".localized,
            color: .blue
        ),
        WelcomeFeature(
            icon: "map.fill",
            title: "batch_navigation".localized,
            description: "batch_navigation_description".localized,
            color: .green
        ),
        WelcomeFeature(
            icon: "clock.fill",
            title: "time_saving".localized,
            description: "time_saving_description".localized,
            color: .orange
        ),
        WelcomeFeature(
            icon: "star.fill",
            title: "professional_tools".localized,
            description: "professional_tools_description".localized,
            color: .purple
        )
    ]
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemBackground),
                    Color.blue.opacity(0.05)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部标题区域
                headerSection
                
                // 功能展示区域
                featuresSection
                
                // 底部按钮区域
                bottomSection
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8).delay(0.3)) {
                showFeatures = true
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 24) {
            // 主标题
            VStack(spacing: 12) {
                Text("welcome_to".localized)
                    .font(.system(size: 28, weight: .light))
                    .foregroundColor(.secondary)

                Text("NaviBatch")
                    .font(.system(size: 42, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.blue, Color.blue.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Text("delivery_driver_smart_assistant".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 核心价值展示
            HStack(spacing: 20) {
                ValueCard(
                    number: "35",
                    unit: "addresses_batch_processing".localized,
                    description: "batch_processing".localized
                )

                ValueCard(
                    number: "2+",
                    unit: "hours_daily_savings".localized,
                    description: "daily_savings".localized
                )

                ValueCard(
                    number: "300%",
                    unit: "efficiency_improvement".localized,
                    description: "improvement_rate".localized
                )
            }
        }
        .padding(.top, 60)
        .padding(.horizontal, 24)
    }
    
    private var featuresSection: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(Array(features.enumerated()), id: \.offset) { index, feature in
                    FeatureCard(feature: feature)
                        .opacity(showFeatures ? 1 : 0)
                        .offset(y: showFeatures ? 0 : 30)
                        .animation(
                            .easeOut(duration: 0.6).delay(Double(index) * 0.1),
                            value: showFeatures
                        )
                }
            }
            .padding(.horizontal, 24)
        }
        .padding(.vertical, 32)
    }
    
    private var bottomSection: some View {
        VStack(spacing: 16) {
            // 权限说明
            VStack(spacing: 8) {
                Text("for_best_experience_permissions".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                HStack(spacing: 20) {
                    PermissionItem(icon: "location.fill", text: "location_permission".localized)
                    PermissionItem(icon: "camera.fill", text: "camera_permission".localized)
                    PermissionItem(icon: "photo.fill", text: "photos_permission".localized)
                }
            }
            .padding(.horizontal, 24)
            
            // 开始使用按钮
            Button(action: onComplete) {
                HStack(spacing: 12) {
                    Text("start_using_navibatch".localized)
                        .font(.system(size: 18, weight: .semibold))

                    Image(systemName: "arrow.right.circle.fill")
                        .font(.system(size: 20))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.blue.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(16)
                .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .padding(.horizontal, 24)
        }
        .padding(.bottom, 40)
    }
}

// MARK: - 支持组件

struct WelcomeFeature {
    let icon: String
    let title: String
    let description: String
    let color: Color
}

struct ValueCard: View {
    let number: String
    let unit: String
    let description: String
    
    var body: some View {
        VStack(spacing: 8) {
            VStack(spacing: 2) {
                Text(number)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.blue)
                
                Text(unit)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.blue.opacity(0.8))
            }
            
            Text(description)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct FeatureCard: View {
    let feature: WelcomeFeature
    
    var body: some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(feature.color.opacity(0.1))
                    .frame(width: 50, height: 50)
                
                Image(systemName: feature.icon)
                    .font(.system(size: 24))
                    .foregroundColor(feature.color)
            }
            
            VStack(spacing: 6) {
                Text(feature.title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text(feature.description)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(20)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct PermissionItem: View {
    let icon: String
    let text: String
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(.blue)
            
            Text(text)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.secondary)
        }
    }
}

#Preview("WelcomeView") {
    WelcomeView {
        print("Welcome completed")
    }
}
