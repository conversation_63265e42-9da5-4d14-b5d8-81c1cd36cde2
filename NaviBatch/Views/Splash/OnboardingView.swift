import SwiftUI

/// NaviBatch功能引导页面
/// 分步骤展示应用的核心功能和使用方法
struct OnboardingView: View {
    @State private var currentStep = 0
    @State private var showContent = false
    let onComplete: () -> Void
    
    private let steps = [
        OnboardingStep(
            icon: "doc.text.viewfinder",
            title: "photo_scan_addresses".localized,
            description: "photo_scan_description".localized,
            instruction: "photo_scan_instruction".localized,
            color: .blue
        ),
        OnboardingStep(
            icon: "plus.circle.fill",
            title: "batch_add_addresses".localized,
            description: "batch_add_description".localized,
            instruction: "batch_add_instruction".localized,
            color: .green
        ),
        OnboardingStep(
            icon: "arrow.triangle.swap",
            title: "smart_route_optimization".localized,
            description: "route_optimization_description".localized,
            instruction: "route_optimization_instruction".localized,
            color: .orange
        ),
        OnboardingStep(
            icon: "map.fill",
            title: "one_click_batch_navigation".localized,
            description: "batch_navigation_description".localized,
            instruction: "batch_navigation_instruction".localized,
            color: .purple
        )
    ]
    
    var body: some View {
        ZStack {
            // 背景
            Color(.systemBackground)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部进度指示器
                progressIndicator
                
                // 内容区域
                contentArea
                
                // 底部导航按钮
                navigationButtons
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6)) {
                showContent = true
            }
        }
    }
    
    private var progressIndicator: some View {
        VStack(spacing: 16) {
            // 步骤指示器
            HStack(spacing: 8) {
                ForEach(0..<steps.count, id: \.self) { index in
                    Circle()
                        .fill(index <= currentStep ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 10, height: 10)
                        .scaleEffect(index == currentStep ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.3), value: currentStep)
                }
            }
            
            // 步骤文本
            Text("step_progress_format".localized(with: currentStep + 1, steps.count))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
        }
        .padding(.top, 60)
        .padding(.bottom, 20)
    }
    
    private var contentArea: some View {
        TabView(selection: $currentStep) {
            ForEach(Array(steps.enumerated()), id: \.offset) { index, step in
                StepContentView(step: step, isActive: index == currentStep)
                    .tag(index)
            }
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        .id(currentStep) // 强制TabView重新渲染，避免缓存问题
        .opacity(showContent ? 1 : 0)
        .offset(y: showContent ? 0 : 20)
        .animation(.easeOut(duration: 0.6), value: showContent)
    }
    
    private var navigationButtons: some View {
        VStack(spacing: 16) {
            // 主要操作按钮
            HStack(spacing: 16) {
                // 上一步按钮
                if currentStep > 0 {
                    Button(action: previousStep) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                            Text("previous_step".localized)
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
                
                // 下一步/完成按钮
                Button(action: nextStep) {
                    HStack(spacing: 8) {
                        Text(currentStep < steps.count - 1 ? "next_step".localized : "start_using".localized)

                        Image(systemName: currentStep < steps.count - 1 ? "chevron.right" : "checkmark.circle.fill")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(
                        LinearGradient(
                            colors: [Color.blue, Color.blue.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                }
            }
            
            // 跳过按钮
            Button(action: {
                #if DEBUG
                print("OnboardingView - 用户点击了跳过按钮，调用onComplete")
                #endif
                onComplete()
            }) {
                Text("skip_guide".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 40)
    }
    
    private func previousStep() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentStep = max(0, currentStep - 1)
        }
    }
    
    private func nextStep() {
        if currentStep < steps.count - 1 {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentStep += 1
            }
        } else {
            #if DEBUG
            print("OnboardingView - 用户完成了所有引导步骤，调用onComplete")
            #endif
            onComplete()
        }
    }
}

// MARK: - 支持组件

struct OnboardingStep {
    let icon: String
    let title: String
    let description: String
    let instruction: String
    let color: Color
}

struct StepContentView: View {
    let step: OnboardingStep
    let isActive: Bool
    
    @State private var animateIcon = false
    
    var body: some View {
        VStack(spacing: 32) {
            // 图标区域
            ZStack {
                // 背景圆圈
                Circle()
                    .fill(step.color.opacity(0.1))
                    .frame(width: 120, height: 120)
                    .scaleEffect(animateIcon ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animateIcon)

                Circle()
                    .stroke(step.color.opacity(0.2), lineWidth: 2)
                    .frame(width: 140, height: 140)

                // 主图标 - 强制居中对齐
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Image(systemName: step.icon)
                            .font(.system(size: 50, weight: .medium, design: .default))
                            .foregroundColor(step.color)
                            .scaleEffect(animateIcon ? 1.05 : 1.0)
                            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animateIcon)
                        Spacer()
                    }
                    Spacer()
                }
                .frame(width: 120, height: 120)
                .clipped()
            }
            
            // 文本内容
            VStack(spacing: 16) {
                Text(step.title)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                
                Text(step.description)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                
                // 操作说明
                VStack(spacing: 8) {
                    Text("operation_tips".localized)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(step.color)

                    Text(step.instruction)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(step.color.opacity(0.05))
                        .cornerRadius(12)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 32)
        .onAppear {
            if isActive {
                animateIcon = true
            }
        }
        .onChange(of: isActive) { _, newValue in
            animateIcon = newValue
        }
    }
}

#Preview {
    OnboardingView {
        print("Onboarding completed")
    }
}
