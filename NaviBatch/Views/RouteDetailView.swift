import SwiftUI
import SwiftData
import MapKit

struct RouteDetailView: View {
    let route: Route
    @State private var mapRegion: MKCoordinateRegion

    init(route: Route) {
        self.route = route

        // 初始化地图区域
        if let firstPoint = route.points.first {
            self._mapRegion = State(initialValue: MKCoordinateRegion(
                center: firstPoint.coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            ))
        } else {
            // 默认区域（如果没有点）
            self._mapRegion = State(initialValue: MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642),
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            ))
        }
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 地图预览
                if !route.points.isEmpty {
                    Map(initialPosition: .region(mapRegion)) {
                        // 路线详情视图中不显示路线连线，只显示地址点
                        // 根据用户需求，在没有优化路线之前不显示连接线

                        // 显示所有点
                        ForEach(route.points) { point in
                            if point.isStartPoint && point.isEndPoint {
                                // 起点和终点相同的特殊标记
                                Marker("start_end_point".localized, coordinate: point.coordinate)
                                    .tint(.orange) // 使用橙色表示起终点组合
                            } else if point.isStartPoint {
                                // 起点标记
                                Marker("start_point".localized, coordinate: point.coordinate)
                                    .tint(.blue) // 起点使用蓝色
                            } else if point.isEndPoint {
                                // 终点标记
                                Marker("end_point".localized, coordinate: point.coordinate)
                                    .tint(.green) // 终点使用绿色
                            } else {
                                // 中间停靠点
                                Marker("\(point.sorted_number)", coordinate: point.coordinate)
                                    .tint(Color(hex: "B36AE2")) // 途经点使用紫色
                            }
                        }
                    }
                    .frame(height: 250)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .padding(.horizontal)
                }

                // 路线信息
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("route_info".localized)
                            .font(.headline)

                        Spacer()

                        Text(formattedDate(route.createdAt))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Divider()

                    HStack {
                        Label("address_count".localized, systemImage: "mappin.and.ellipse")
                        Spacer()
                        Text("\(route.points.count)")
                            .fontWeight(.medium)
                    }
                    .padding(.vertical, 4)

                    if let startPoint = route.points.first(where: { $0.isStartPoint }) {
                        HStack {
                            Label("start_point".localized, systemImage: "house.fill")
                            Spacer()
                            Text(startPoint.primaryAddress)
                                .lineLimit(1)
                                .truncationMode(.middle)
                                .font(.subheadline)
                        }
                        .padding(.vertical, 4)
                    }

                    if let endPoint = route.points.first(where: { $0.isEndPoint }) {
                        HStack {
                            Label("end_point".localized, systemImage: "flag.fill")
                            Spacer()
                            Text(endPoint.primaryAddress)
                                .lineLimit(1)
                                .truncationMode(.middle)
                                .font(.subheadline)
                        }
                        .padding(.vertical, 4)
                    }

                    // 路线操作按钮
                    HStack(spacing: 12) {
                        Button(action: {
                            // 导出路线
                        }) {
                            Label("export_route".localized, systemImage: "square.and.arrow.up")
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 8)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                        }

                        Button(action: {
                            // 导航到路线上的所有点
                        }) {
                            Label("navigate".localized, systemImage: "location.fill")
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 8)
                                .background(Color.green.opacity(0.1))
                                .foregroundColor(.green)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.top, 8)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .padding(.horizontal)

                // 地址列表
                VStack(alignment: .leading, spacing: 8) {
                    Text("address_list".localized)
                        .font(.headline)
                        .padding(.horizontal)

                    if route.points.isEmpty {
                        ContentUnavailableView {
                            Label("no_addresses".localized, systemImage: "mappin.slash")
                        } description: {
                            Text("no_addresses_message".localized)
                        }
                        .frame(height: 200)
                    } else {
                        ForEach(route.points.sorted(by: { $0.sorted_number < $1.sorted_number })) { point in
                            HStack(spacing: 12) {
                                Text("\(point.sorted_number)")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .frame(width: 24, height: 24)
                                    .background(Circle().fill(point.deliveryStatus.color))

                                VStack(alignment: .leading, spacing: 2) {
                                    Text(point.primaryAddress)
                                        .font(.subheadline)
                                        .lineLimit(1)

                                    if let notes = point.notes, !notes.isEmpty {
                                        Text(notes)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .lineLimit(1)
                                    }
                                }

                                Spacer()

                                Image(systemName: point.deliveryStatus.iconName)
                                    .foregroundColor(point.deliveryStatus.color)
                            }
                            .padding(.vertical, 8)
                            .padding(.horizontal)
                            .background(Color(.systemBackground))
                            .cornerRadius(8)
                            .padding(.horizontal)
                        }
                    }
                }
            }
            .padding(.vertical)
        }
        .navigationTitle(route.localizedName)
        .onAppear {
            updateMapRegion()
        }
    }

    private func updateMapRegion() {
        guard !route.points.isEmpty else { return }

        // 计算所有点的边界
        var minLat = route.points[0].latitude
        var maxLat = route.points[0].latitude
        var minLon = route.points[0].longitude
        var maxLon = route.points[0].longitude

        for point in route.points {
            minLat = min(minLat, point.latitude)
            maxLat = max(maxLat, point.latitude)
            minLon = min(minLon, point.longitude)
            maxLon = max(maxLon, point.longitude)
        }

        // 添加一些边距
        let latDelta = max(0.02, maxLat - minLat) * 1.2
        let lonDelta = max(0.02, maxLon - minLon) * 1.2

        // 计算中心点
        let centerLat = (minLat + maxLat) / 2
        let centerLon = (minLon + maxLon) / 2

        // 更新地图区域
        mapRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: centerLat, longitude: centerLon),
            span: MKCoordinateSpan(latitudeDelta: latDelta, longitudeDelta: lonDelta)
        )
    }

    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

struct RouteDetailPreview: PreviewProvider {
    static var previews: some View {
        // 创建示例路线
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try! ModelContainer(for: Route.self, DeliveryPoint.self, DeliveryGroup.self, configurations: config)

        let route = Route(name: "路线 2025-04-21")

        // 添加示例点 - 仅用于预览
        let points = [
            DeliveryPoint(sort_number: 0, streetName: "Address 1", coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642), isStartPoint: true),
            DeliveryPoint(sort_number: 1, streetName: "Address 2", coordinate: CLLocationCoordinate2D(latitude: -37.8798, longitude: 145.1642), isEndPoint: true)
        ]

        for point in points {
            container.mainContext.insert(point)
            route.addPoint(point)
        }

        container.mainContext.insert(route)
        try? container.mainContext.save()

        return NavigationStack {
            RouteDetailView(route: route)
        }
        .modelContainer(container)
    }
}
