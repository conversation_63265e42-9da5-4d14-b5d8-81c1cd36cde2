import SwiftUI

/// 分组详情功能演示界面
/// 展示新增的第三方标签、Dark Mode 优化和路线优化功能
struct GroupDetailDemoView: View {
    @State private var selectedDemo = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 功能选择器
                Picker("演示功能", selection: $selectedDemo) {
                    Text("第三方标签").tag(0)
                    Text("Dark Mode").tag(1)
                    Text("路线优化").tag(2)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // 演示内容
                TabView(selection: $selectedDemo) {
                    // 第三方标签演示
                    thirdPartyTagsDemo
                        .tag(0)
                    
                    // Dark Mode 演示
                    darkModeDemo
                        .tag(1)
                    
                    // 路线优化演示
                    routeOptimizationDemo
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("分组详情新功能演示")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 第三方标签演示
    private var thirdPartyTagsDemo: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("🏷️ 第三方排序标签")
                    .font(.title2.bold())
                    .foregroundColor(.adaptivePrimaryText)
                    .padding()
                
                Text("现在分组详情界面会显示第三方配送应用的排序标签，就像 bottom sheet 一样！")
                    .font(.subheadline)
                    .foregroundColor(.adaptiveSecondaryText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // 模拟地址行
                VStack(spacing: 12) {
                    // GoFo 标签示例
                    mockAddressRow(
                        number: "1",
                        tag: "GoFo: 4",
                        tagColor: .adaptiveGoFo,
                        address: "3420 Tupelo Drive",
                        subtitle: "Stockton, CA 95209",
                        status: .completed
                    )
                    
                    // Amazon 标签示例
                    mockAddressRow(
                        number: "2",
                        tag: "Amazon: 2",
                        tagColor: .adaptiveAmazonFlex,
                        address: "10727 Trevor Drive",
                        subtitle: "Stockton, CA 95209",
                        status: .normal
                    )
                    
                    // iMile 标签示例
                    mockAddressRow(
                        number: "3",
                        tag: "iMile: 7",
                        tagColor: .adaptivePrimaryIcon,
                        address: "4265 Maddie Circle",
                        subtitle: "Stockton, CA 95209",
                        status: .warning
                    )
                    
                    // 无标签示例
                    mockAddressRow(
                        number: "4",
                        tag: nil,
                        tagColor: .clear,
                        address: "4167 Maddie Circle",
                        subtitle: "Stockton, CA 95209",
                        status: .normal
                    )
                }
                .padding(.horizontal)
                
                // 说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("✨ 新功能特点：")
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)
                    
                    Text("• 自动显示第三方排序标签")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 不同应用使用不同颜色")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 只有包含排序号的地址才显示标签")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 与 bottom sheet 样式保持一致")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding(.vertical)
        }
        .background(Color.adaptiveBackground)
    }
    
    // MARK: - Dark Mode 演示
    private var darkModeDemo: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("🌙 Dark Mode 优化")
                    .font(.title2.bold())
                    .foregroundColor(.adaptivePrimaryText)
                    .padding()
                
                Text("所有界面元素都针对 Dark Mode 进行了优化，确保司机在夜间使用时清晰可见。")
                    .font(.subheadline)
                    .foregroundColor(.adaptiveSecondaryText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // 颜色对比演示
                VStack(spacing: 16) {
                    HStack(spacing: 16) {
                        colorSample("主要文字", color: .adaptivePrimaryText)
                        colorSample("次要文字", color: .adaptiveSecondaryText)
                    }
                    
                    HStack(spacing: 16) {
                        colorSample("成功状态", color: .adaptiveSuccess)
                        colorSample("警告状态", color: .adaptiveWarning)
                    }
                    
                    HStack(spacing: 16) {
                        colorSample("错误状态", color: .adaptiveError)
                        colorSample("主要图标", color: .adaptivePrimaryIcon)
                    }
                }
                .padding(.horizontal)
                
                // 按钮演示
                VStack(spacing: 12) {
                    Button("优化组内路线") {}
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.adaptiveWarning)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    
                    Button("导航到这些点") {}
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.adaptivePrimaryIcon)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding(.horizontal)
                
                // 说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("🎨 优化内容：")
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)
                    
                    Text("• 提升文字对比度，确保可读性")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 优化按钮颜色，更加醒目")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 状态颜色更加明亮清晰")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 适合司机夜间使用环境")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding(.vertical)
        }
        .background(Color.adaptiveBackground)
    }
    
    // MARK: - 路线优化演示
    private var routeOptimizationDemo: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("🛣️ 组内路线优化")
                    .font(.title2.bold())
                    .foregroundColor(.adaptivePrimaryText)
                    .padding()
                
                Text("司机现在可以针对单个分组进行路线优化，提升配送效率！")
                    .font(.subheadline)
                    .foregroundColor(.adaptiveSecondaryText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // 优化前后对比
                HStack(spacing: 16) {
                    // 优化前
                    VStack(spacing: 8) {
                        Text("优化前")
                            .font(.headline)
                            .foregroundColor(.adaptiveError)
                        
                        VStack(spacing: 4) {
                            optimizationStep("1", "3420 Tupelo Dr", isOptimal: false)
                            optimizationStep("2", "10948 Dutch Tulip Dr", isOptimal: false)
                            optimizationStep("3", "4265 Maddie Circle", isOptimal: false)
                            optimizationStep("4", "10727 Trevor Dr", isOptimal: false)
                        }
                        
                        Text("总距离: 15.2 km")
                            .font(.caption)
                            .foregroundColor(.adaptiveSecondaryText)
                    }
                    .padding()
                    .background(Color.adaptiveError.opacity(0.1))
                    .cornerRadius(12)
                    
                    // 优化后
                    VStack(spacing: 8) {
                        Text("优化后")
                            .font(.headline)
                            .foregroundColor(.adaptiveSuccess)
                        
                        VStack(spacing: 4) {
                            optimizationStep("1", "3420 Tupelo Dr", isOptimal: true)
                            optimizationStep("2", "4265 Maddie Circle", isOptimal: true)
                            optimizationStep("3", "10727 Trevor Dr", isOptimal: true)
                            optimizationStep("4", "10948 Dutch Tulip Dr", isOptimal: true)
                        }
                        
                        Text("总距离: 12.8 km")
                            .font(.caption)
                            .foregroundColor(.adaptiveSecondaryText)
                    }
                    .padding()
                    .background(Color.adaptiveSuccess.opacity(0.1))
                    .cornerRadius(12)
                }
                .padding(.horizontal)
                
                // 节省信息
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.adaptiveSuccess)
                    Text("节省 2.4 km (15.8%)")
                        .font(.headline)
                        .foregroundColor(.adaptiveSuccess)
                }
                .padding()
                .background(Color.adaptiveSuccess.opacity(0.1))
                .cornerRadius(10)
                .padding(.horizontal)
                
                // 说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("⚡ 功能特点：")
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)
                    
                    Text("• 一键优化单个分组的路线")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 自动计算最短路径")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 保持手动调整的灵活性")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 节省时间和燃油成本")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding(.vertical)
        }
        .background(Color.adaptiveBackground)
    }
    
    // MARK: - 辅助视图
    
    private func mockAddressRow(
        number: String,
        tag: String?,
        tagColor: Color,
        address: String,
        subtitle: String,
        status: AddressStatus
    ) -> some View {
        HStack(spacing: 12) {
            // 序号圆圈
            Text(number)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(status.color)
                .cornerRadius(6)
            
            // 地址信息
            VStack(alignment: .leading, spacing: 4) {
                // 第三方标签
                if let tag = tag {
                    Text(tag)
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(tagColor)
                        .cornerRadius(6)
                }
                
                Text(address)
                    .font(.subheadline)
                    .foregroundColor(.adaptivePrimaryText)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.adaptiveSecondaryText)
            }
            
            Spacer()
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 4)
    }
    
    private func colorSample(_ title: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 40, height: 40)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.adaptiveSecondaryText)
        }
    }
    
    private func optimizationStep(_ number: String, _ address: String, isOptimal: Bool) -> some View {
        HStack(spacing: 8) {
            Text(number)
                .font(.caption.bold())
                .foregroundColor(.white)
                .frame(width: 20, height: 20)
                .background(isOptimal ? Color.adaptiveSuccess : Color.adaptiveError)
                .cornerRadius(4)
            
            Text(address)
                .font(.caption)
                .foregroundColor(.adaptiveSecondaryText)
                .lineLimit(1)
            
            Spacer()
        }
    }
    
    enum AddressStatus {
        case normal, completed, warning, error
        
        var color: Color {
            switch self {
            case .normal: return .adaptivePrimaryIcon
            case .completed: return .adaptiveSuccess
            case .warning: return .adaptiveWarning
            case .error: return .adaptiveError
            }
        }
    }
}

#Preview("Group Detail Demo - Light") {
    GroupDetailDemoView()
        .preferredColorScheme(.light)
}

#Preview("Group Detail Demo - Dark") {
    GroupDetailDemoView()
        .preferredColorScheme(.dark)
}
