import SwiftUI
import SwiftData
import MapKit
import os.log
// 导入共享组件

struct AddressBookView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var addresses: [SavedAddress] = []
    @State private var isEditing = false
    @State private var editingAddress: SavedAddress? = nil
    @State private var editedName = ""
    @State private var editedNotes = ""
    // 移除地址类型状态变量
    @State private var editedPhoneNumber = ""
    @State private var editedCompanyName = ""
    @State private var editedUrl = ""
    @State private var showDeleteAlert = false
    @State private var addressToDelete: SavedAddress? = nil
    @State private var isLoading = true
    @State private var showingAddAddressSheet = false
    // 移除地址类型过滤器状态变量
    @State private var startAddressId: UUID? = nil
    @State private var endAddressId: UUID? = nil

    var body: some View {
        List {
            if isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                        .padding()
                    Spacer()
                }
                .listRowBackground(Color.clear)
            } else if addresses.isEmpty {
                ContentUnavailableView {
                    Label("没有保存的地址", systemImage: "bookmark")
                } description: {
                    Text("您还没有保存任何地址")
                } actions: {
                    Button(action: { showingAddAddressSheet = true }) {
                        Text("添加新地址")
                    }
                    .buttonStyle(.bordered)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .listRowBackground(Color.clear)
            } else {
                // 移除地址类型过滤器

                // 地址列表
                Section {
                    ForEach(filteredAddresses) { address in
                        AddressBookRow(
                            address: address,
                            isStart: address.id == startAddressId,
                            isEnd: address.id == endAddressId,
                            onSetStart: { setAsStart(address) },
                            onSetEnd: { setAsEnd(address) }
                        )
                        .onLongPressGesture {
                            // 长按删除功能
                            print("🗑️ 长按删除地址被触发: \(address.address)")
                            // 添加触觉反馈
                            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                            impactFeedback.impactOccurred()
                            addressToDelete = address
                            showDeleteAlert = true
                        }
                        .contextMenu {
                            // 保留上下文菜单作为备选删除方式
                            Button(role: .destructive) {
                                print("🗑️ 上下文菜单删除地址被触发: \(address.address)")
                                addressToDelete = address
                                showDeleteAlert = true
                            } label: {
                                Label("删除", systemImage: "trash")
                            }
                        }
                    }
                }
            }
        }
        .onAppear {
            // 显示加载状态
            isLoading = true
            logInfo("AddressBookView - 页面出现")

            // 直接刷新地址列表
            refreshAddresses()
        }
        .navigationTitle("地址簿")
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用程序返回前台时刷新地址列表
            logInfo("AddressBookView - 应用程序返回前台，刷新地址列表")
            refreshAddresses()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("AddressAddedNotification"))) { notification in
            // 当收到地址添加通知时刷新地址列表
            if let addressId = notification.object as? String {
                logInfo("AddressBookView - 收到地址添加通知: \(addressId)")
            } else {
                logInfo("AddressBookView - 收到地址添加通知")
            }

            // 给更多时间让数据库操作完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                refreshAddresses()

                // 再次尝试刷新，以防第一次刷新没有成功
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    refreshAddresses()
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RefreshAddressesNotification"))) { _ in
            // 当收到刷新地址列表通知时刷新地址列表
            logInfo("AddressBookView - 收到刷新地址列表通知")

            // 给一点时间让数据库操作完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                refreshAddresses()
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddAddressSheet = true }) {
                    Label("添加", systemImage: "plus")
                }
            }

            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: refreshAddresses) {
                    Label("刷新", systemImage: "arrow.clockwise")
                }
            }
        }
        .sheet(isPresented: $showingAddAddressSheet) {
            AddSavedAddressSheet(onAddressAdded: { address in
                // 发送通知以刷新地址列表
                NotificationCenter.default.post(
                    name: Notification.Name("AddressAddedNotification"),
                    object: address.id.uuidString
                )
            })
        }
        .sheet(isPresented: $isEditing) {
            if let address = editingAddress {
                NavigationView {
                    Form {
                        Section(header: Text("地址信息")) {
                            // 不再需要名称字段

                            // 移除地址类型选择器

                            // 电话号码
                            TextField("电话号码", text: $editedPhoneNumber)
                                .keyboardType(.phonePad)
                                .onAppear {
                                    editedPhoneNumber = address.phoneNumber ?? ""
                                }

                            // 公司名称
                            TextField("公司名称", text: $editedCompanyName)
                                .autocorrectionDisabled()
                                .onAppear {
                                    editedCompanyName = address.companyName ?? ""
                                }

                            // URL
                            TextField("网址", text: $editedUrl)
                                .keyboardType(.URL)
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                                .onAppear {
                                    editedUrl = address.url ?? ""
                                }

                            // 备注
                            VStack(alignment: .leading) {
                                Text("备注")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                TextEditor(text: $editedNotes)
                                    .frame(minHeight: 100)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                                    )
                                    .onAppear {
                                        editedNotes = address.notes ?? ""
                                    }
                            }
                        }

                        Section {
                            Button(action: {
                                updateAddress(address)
                                isEditing = false
                            }) {
                                Text("保存更改")
                                    .frame(maxWidth: .infinity)
                                    .foregroundColor(.white)
                            }
                            .listRowBackground(Color.blue)

                            Button(action: {
                                isEditing = false
                            }) {
                                Text("取消")
                                    .frame(maxWidth: .infinity)
                            }
                            .listRowBackground(Color.gray.opacity(0.2))
                        }
                    }
                    .navigationTitle("编辑地址")
                    .navigationBarTitleDisplayMode(.inline)
                }
            }
        }
        .alert("确认删除", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                if let address = addressToDelete {
                    deleteAddress(address)
                }
            }
        } message: {
            Text("确定要删除这个地址吗？此操作无法撤销。")
        }
    }

    // 过滤后的地址列表 - 不再需要地址类型过滤
    private var filteredAddresses: [SavedAddress] {
        return addresses
    }

    // 更新地址
    private func updateAddress(_ address: SavedAddress) {
        let addressId = address.id.uuidString

        logInfo("AddressBookView - 开始更新地址: ID: \(addressId)")

        do {
            // 直接更新环境中的 modelContext 中的地址
            address.notes = editedNotes.isEmpty ? nil : editedNotes
            // 移除地址类型相关代码
            address.phoneNumber = editedPhoneNumber.isEmpty ? nil : editedPhoneNumber
            address.companyName = editedCompanyName.isEmpty ? nil : editedCompanyName
            address.url = editedUrl.isEmpty ? nil : editedUrl
            try modelContext.save()

            logInfo("AddressBookView - 成功更新地址: ID: \(addressId)")

            if !editedCompanyName.isEmpty {
                logInfo("AddressBookView - 更新了公司名称: \(editedCompanyName)")
            }

            if !editedUrl.isEmpty {
                logInfo("AddressBookView - 更新了URL: \(editedUrl)")
            }

            // 刷新地址列表
            refreshAddresses()
        } catch {
            logError("AddressBookView - 更新地址失败: \(error.localizedDescription)")
        }
    }

    // 删除地址
    private func deleteAddress(_ address: SavedAddress) {
        let addressId = address.id.uuidString

        logInfo("AddressBookView - 开始删除地址: ID: \(addressId)")

        do {
            // 直接从环境中的 modelContext 删除地址
            modelContext.delete(address)
            try modelContext.save()

            logInfo("AddressBookView - 成功删除地址: ID: \(addressId)")

            // 从当前列表中删除地址
            DispatchQueue.main.async {
                self.addresses.removeAll { $0.id.uuidString == addressId }
            }

            // 刷新地址列表
            refreshAddresses()
        } catch {
            logError("AddressBookView - 删除地址失败: \(error.localizedDescription)")
        }
    }

    // 刷新地址列表
    private func refreshAddresses() {
        isLoading = true

        logInfo("AddressBookView - 开始刷新地址数据")

        // 尝试使用环境中的 modelContext 和 getPersistentContainer() 函数获取的容器
        do {
            // 首先使用环境中的 modelContext
            let descriptor = FetchDescriptor<SavedAddress>()
            var addresses = try modelContext.fetch(descriptor)

            logInfo("AddressBookView - 从 modelContext 中查询到 \(addresses.count) 个地址")

            // 如果环境中的 modelContext 没有返回地址，尝试使用 getPersistentContainer() 函数获取的容器
            if addresses.isEmpty {
                logInfo("AddressBookView - 环境中的 modelContext 没有返回地址，尝试使用 getPersistentContainer() 函数")

                let container = getPersistentContainer()
                let containerAddresses = try container.mainContext.fetch(descriptor)

                if !containerAddresses.isEmpty {
                    logInfo("AddressBookView - 从 getPersistentContainer() 中查询到 \(containerAddresses.count) 个地址")
                    addresses = containerAddresses
                }
            }

            // 打印地址信息以便调试
            for (index, address) in addresses.enumerated() {
                logInfo("[地址 \(index+1)] " +
                       "\n地址: \(address.address)" +
                       "\n坐标: \(address.coordinate.latitude), \(address.coordinate.longitude)" +
                       "\nID: \(address.id.uuidString)")
            }

            // 更新地址列表
            DispatchQueue.main.async {
                self.addresses = addresses
                logInfo("AddressBookView - 地址列表已更新，共 \(addresses.count) 个地址")
                self.isLoading = false
            }
        } catch {
            logError("AddressBookView - 刷新查询地址失败: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
            }
        }
    }

    private func setAsStart(_ address: SavedAddress) {
        startAddressId = address.id
        print("设为起点: \(address.address)")
    }

    private func setAsEnd(_ address: SavedAddress) {
        endAddressId = address.id
        print("设为终点: \(address.address)")
    }

    private func deleteAddress(at offsets: IndexSet) {
        for index in offsets {
            let address = filteredAddresses[index]
            deleteAddress(address)
        }
    }
}

// 地址行视图
struct AddressBookRow: View {
    let address: SavedAddress
    let isStart: Bool
    let isEnd: Bool
    let onSetStart: () -> Void
    let onSetEnd: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top) {
                Image(systemName: isStart ? "play.circle.fill" : (isEnd ? "flag.circle.fill" : "mappin.circle.fill"))
                    .font(.title3)
                    .foregroundColor(isStart ? .blue : (isEnd ? .green : .red))
                    .frame(width: 30, height: 30)
                    .padding(.top, 4)

                VStack(alignment: .leading, spacing: 4) {
                    if address.isFavorite {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                    }
                    Text(address.address)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                        .fixedSize(horizontal: false, vertical: true)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 8) {
                        if let phoneNumber = address.phoneNumber, !phoneNumber.isEmpty {
                            HStack {
                                Image(systemName: "phone.fill")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(phoneNumber)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        if let companyName = address.companyName, !companyName.isEmpty {
                            HStack {
                                Image(systemName: "building.2.fill")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                Text(companyName)
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    .padding(.vertical, 2)
                                    .padding(.horizontal, 4)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                    }

                    if let url = address.url, !url.isEmpty {
                        HStack {
                            Image(systemName: "link")
                                .font(.caption)
                                .foregroundColor(.green)
                            Text(url)
                                .font(.caption)
                                .foregroundColor(.green)
                                .lineLimit(1)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            if let notes = address.notes, !notes.isEmpty {
                Text(notes)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            HStack {
                Spacer()
                Button(action: onSetStart) {
                    Text(isStart ? "已设为起点" : "设为起点")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(isStart ? Color.blue : Color.blue.opacity(0.1))
                        .cornerRadius(6)
                }
                Button(action: onSetEnd) {
                    Text(isEnd ? "已设为终点" : "设为终点")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(isEnd ? Color.green : Color.green.opacity(0.1))
                        .cornerRadius(6)
                }
            }
            .padding(.top, 4)
        }
        .padding(.vertical, 8)
    }
}

// 使用共享组件中的 FilterChip

#Preview("AddressBookView") {
    // 使用持久化存储
    let schema = Schema([SavedAddress.self])
    let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
    let container = try! ModelContainer(for: schema, configurations: [modelConfiguration])

    // 创建示例地址 - 仅用于预览
    let homeAddress = SavedAddress(
        address: "123 Main Street, Melbourne VIC 3000, Australia",
        coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631),
        phoneNumber: "0412345678",
        companyName: "家庭地址"
    )

    let workAddress = SavedAddress(
        address: "456 Business Road, Melbourne VIC 3000, Australia",
        coordinate: CLLocationCoordinate2D(latitude: -37.8156, longitude: 144.9631),
        companyName: "ABC公司",
        url: "https://example.com/addresses"
    )

    container.mainContext.insert(homeAddress)
    container.mainContext.insert(workAddress)
    try! container.mainContext.save()

    // 打印数据库路径
    if let url = container.configurations.first?.url {
        print("AddressBookView Preview - 数据库路径: \(url.path)")
    }

    return NavigationStack {
        AddressBookView()
            .modelContainer(container)
    }
}
