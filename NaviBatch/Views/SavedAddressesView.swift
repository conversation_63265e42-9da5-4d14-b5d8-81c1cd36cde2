import SwiftUI
import SwiftData
import os.log
import MapKit
import UIKit

struct SavedAddressesView: View {
    // 使用环境中的 ModelContext
    @Environment(\.modelContext) private var modelContext
    @State private var addresses: [SavedAddress] = []
    @State private var isEditing = false
    @State private var editingAddress: SavedAddress? = nil
    // 移除名称状态变量
    @State private var editedNotes = ""
    @State private var showDeleteAlert = false
    @State private var addressToDelete: SavedAddress? = nil
    @State private var isLoading = true
    @State private var showingAddAddressSheet = false

    var body: some View {
        List {
            if isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                        .padding()
                    Spacer()
                }
                .listRowBackground(Color.clear)
            } else if addresses.isEmpty {
                ContentUnavailableView {
                    Label("no_saved_addresses_title".localized, systemImage: "bookmark")
                } description: {
                    Text("no_saved_addresses_message".localized)
                } actions: {
                    Button(action: { showingAddAddressSheet = true }) {
                        Text("add_new_address".localized)
                    }
                    .buttonStyle(.bordered)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .listRowBackground(Color.clear)
            } else {
                ForEach(addresses) { address in
                    NavigationLink(destination: AddressDetailView(address: address)) {
                        AddressRow(address: address, onEdit: {
                            editingAddress = address
                            editedNotes = address.notes ?? ""
                            isEditing = true
                        }, onDelete: {
                            addressToDelete = address
                            showDeleteAlert = true
                        })
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .onAppear {
            // 显示加载状态
            isLoading = true

            logInfo("SavedAddressesView - 页面出现")

            // 直接刷新地址列表
            refreshAddresses()
        }
        .navigationTitle("address_title".localized)
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用程序返回前台时刷新地址列表
            logInfo("SavedAddressesView - 应用程序返回前台，刷新地址列表")
            refreshAddresses()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("AddressAddedNotification"))) { notification in
            // 当收到地址添加通知时刷新地址列表
            if let addressId = notification.object as? String {
                logInfo("SavedAddressesView - 收到地址添加通知: \(addressId)")
            } else {
                logInfo("SavedAddressesView - 收到地址添加通知")
            }

            // 给一点时间让数据库操作完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                refreshAddresses()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RefreshAddressesNotification"))) { _ in
            // 当收到刷新地址列表通知时刷新地址列表
            logInfo("SavedAddressesView - 收到刷新地址列表通知")

            // 给一点时间让数据库操作完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                refreshAddresses()
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddAddressSheet = true }) {
                    Label("add".localized, systemImage: "plus")
                }
            }

            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: refreshAddresses) {
                    Label("refresh".localized, systemImage: "arrow.clockwise")
                }
            }
        }
        .sheet(isPresented: $isEditing) {
            if let address = editingAddress {
                NavigationStack {
                    Form {
                        Section(header: Text("address_info".localized)) {
                            // 移除地址名称字段
                            TextField("notes".localized, text: $editedNotes)
                        }

                        Section(header: Text("address_details".localized)) {
                            Text(address.address)
                                .foregroundColor(.secondary)
                        }

                        Section {
                            Toggle("favorite".localized, isOn: Binding(
                                get: { address.isFavorite },
                                set: { address.isFavorite = $0 }
                            ))
                        }
                    }
                    .navigationTitle("edit_address".localized)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .cancellationAction) {
                            Button("cancel".localized) {
                                isEditing = false
                            }
                        }
                        ToolbarItem(placement: .confirmationAction) {
                            Button("save".localized) {
                                saveAddressChanges()
                            }
                        }
                    }
                }
                .presentationDetents([.medium])
            }
        }
        .sheet(isPresented: $showingAddAddressSheet) {
            AddSavedAddressSheet(onAddressAdded: { newAddress in
                logInfo("新地址已添加: \(newAddress.address), ID: \(newAddress.id.uuidString)")

                // 刷新地址列表
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    refreshAddresses()
                }
            })
        }
        .alert("confirm_delete".localized, isPresented: $showDeleteAlert) {
            Button("cancel".localized, role: .cancel) {}
            Button("delete".localized, role: .destructive) {
                if let address = addressToDelete {
                    deleteAddress(address)
                }
            }
        } message: {
            Text("delete_address_confirmation".localized)
        }
    }

    // 删除了不再需要的方法

    private func saveAddressChanges() {
        if let address = editingAddress {
            let addressId = address.id.uuidString

            logInfo("SavedAddressesView - 开始更新地址: ID: \(addressId)")

            do {
                // 直接更新地址
                address.notes = editedNotes.isEmpty ? nil : editedNotes

                try modelContext.save()
                logInfo("SavedAddressesView - 成功更新地址: ID: \(addressId)")

                // 刷新地址列表
                refreshAddresses()
            } catch {
                logError("SavedAddressesView - 更新地址失败: \(error.localizedDescription)")
            }
        }
        isEditing = false
    }

    private func deleteAddress(_ address: SavedAddress) {
        let addressId = address.id.uuidString

        logInfo("SavedAddressesView - 开始删除地址: ID: \(addressId)")

        do {
            // 直接从 modelContext 删除地址
            modelContext.delete(address)
            try modelContext.save()

            logInfo("SavedAddressesView - 成功删除地址: ID: \(addressId)")

            // 从当前列表中删除地址
            DispatchQueue.main.async {
                self.addresses.removeAll { $0.id.uuidString == addressId }
            }

            // 刷新地址列表
            refreshAddresses()
        } catch {
            logError("SavedAddressesView - 删除地址失败: \(error.localizedDescription)")
        }
    }

    private func refreshAddresses() {
        isLoading = true

        logInfo("SavedAddressesView - 开始刷新地址数据")

        do {
            let descriptor = FetchDescriptor<SavedAddress>()

            // 直接从环境的 modelContext 中查询地址
            let addresses = try modelContext.fetch(descriptor)

            logInfo("SavedAddressesView - 从 modelContext 中查询到 \(addresses.count) 个地址")

            // 打印地址信息以便调试
            for (index, address) in addresses.enumerated() {
                logInfo("[地址 \(index+1)] " +
                       "\n地址: \(address.address)" +
                       "\n坐标: \(address.coordinate.latitude), \(address.coordinate.longitude)" +
                       "\nID: \(address.id.uuidString)")
            }

            // 更新地址列表
            DispatchQueue.main.async {
                self.addresses = addresses
                logInfo("SavedAddressesView - 地址列表已更新，共 \(addresses.count) 个地址")
                self.isLoading = false
            }
        } catch {
            logError("SavedAddressesView - 刷新查询地址失败: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
            }
        }
    }
}

struct AddressRow: View {
    let address: SavedAddress
    let onEdit: () -> Void
    let onDelete: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        if address.isFavorite {
                            Image(systemName: "star.fill")
                                .foregroundColor(.yellow)
                                .font(.caption)
                        }
                    }

                    Text(address.address)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .fixedSize(horizontal: false, vertical: true)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                Image(systemName: "mappin.circle.fill")
                    .font(.title3)
                    .foregroundColor(.red)
                    .padding(.top, 4)
            }

            if let notes = address.notes, !notes.isEmpty {
                Text(notes)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            HStack {
                Button(action: onEdit) {
                    Label("edit".localized, systemImage: "pencil")
                        .font(.caption)
                }
                .buttonStyle(.bordered)
                .controlSize(.small)

                Spacer()

                Button(action: onDelete) {
                    Label("delete".localized, systemImage: "trash")
                        .font(.caption)
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                .tint(.red)
            }
        }
        .padding(.vertical, 8)
    }
}

struct AddressDetailView: View {
    let address: SavedAddress

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 地图预览
                Map(initialPosition: .region(MKCoordinateRegion(
                    center: address.coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                ))) {
                    Marker("address_marker".localized, coordinate: address.coordinate)
                }
                .frame(height: 200)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .padding(.horizontal)

                // 地址信息
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("address_info".localized)
                            .font(.headline)

                        Spacer()

                        if address.isFavorite {
                            Label("favorite".localized, systemImage: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                        }
                    }

                    Divider()

                    // 移除名称字段

                    HStack(alignment: .top) {
                        Text("address_label".localized)
                            .foregroundColor(.secondary)
                        Text(address.address)
                            .fontWeight(.medium)
                            .fixedSize(horizontal: false, vertical: true)
                            .multilineTextAlignment(.leading)
                    }
                    .padding(.vertical, 4)

                    if let notes = address.notes, !notes.isEmpty {
                        HStack(alignment: .top) {
                            Text("notes_label".localized)
                                .foregroundColor(.secondary)
                            Text(notes)
                                .fontWeight(.medium)
                        }
                        .padding(.vertical, 4)
                    }

                    HStack {
                        Text("created_at_label".localized)
                            .foregroundColor(.secondary)
                        Text(formattedDate(address.createdAt))
                            .fontWeight(.medium)
                    }
                    .padding(.vertical, 4)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .padding(.horizontal)

                // 操作按钮
                VStack(spacing: 12) {
                    Button(action: {
                        openInMaps()
                    }) {
                        Label("open_in_maps".localized, systemImage: "map")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }

                    Button(action: {
                        copyToClipboard()
                    }) {
                        Label("copy_address".localized, systemImage: "doc.on.doc")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(10)
                    }
                }
                .padding()
            }
            .padding(.vertical)
        }
        .navigationTitle("address_details_title".localized)
    }

    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }

    private func openInMaps() {
        // 使用NavigationAppHandler打开导航，确保遵循用户选择的导航应用偏好
        print("[DEBUG] SavedAddressesView - 打开导航到: \(address.address)")
        NavigationAppHandler.shared.openNavigation(to: address.coordinate, name: address.address)
    }

    private func copyToClipboard() {
        UIPasteboard.general.string = address.address
    }
}

// 删除了SearchCompleterDelegate类

#Preview("SavedAddressesView") {
    NavigationStack {
        SavedAddressesView()
    }
    .onAppear {
        // 在预览中初始化数据
        Task {
            // 等待数据库初始化
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            await DatabaseDebugger.printAllAddresses()
        }
    }
}
