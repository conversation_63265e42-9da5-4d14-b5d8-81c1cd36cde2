import SwiftUI

/// 路线界面 Dark Mode 优化对比预览
/// 展示优化前后的视觉效果差异
struct RouteDarkModePreview: View {
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签切换器
                Picker("版本", selection: $selectedTab) {
                    Text("优化前").tag(0)
                    Text("优化后").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    // 优化前版本
                    oldVersionView
                        .tag(0)
                    
                    // 优化后版本
                    newVersionView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("路线界面 Dark Mode 对比")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 优化前版本
    private var oldVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化前 - 存在的问题")
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding()
                
                // 路线头部区域 - 优化前
                VStack(spacing: 12) {
                    HStack {
                        // GoFo 标签 - 优化前（刺眼的黄色）
                        Text("GoFo")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.yellow)
                            .cornerRadius(6)
                        
                        Spacer()
                        
                        // 全部清除按钮
                        Text("全部清除")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.red)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.red.opacity(0.1))
                            .cornerRadius(6)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    
                    // 地址列表项 - 优化前
                    ForEach(1...3, id: \.self) { index in
                        HStack(spacing: 12) {
                            // 编号圆圈
                            ZStack {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue)
                                    .frame(width: 36, height: 36)
                                
                                Text("\(index)")
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            
                            // 地址信息
                            VStack(alignment: .leading, spacing: 4) {
                                Text("GoFo: \(index)")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.yellow)
                                    .cornerRadius(6)
                                
                                Text("\(3420 + index * 100) Tupelo Drive, 95209")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(.primary)
                                    .lineLimit(2)
                            }
                            
                            Spacer()
                            
                            // 操作按钮 - 优化前
                            HStack(spacing: 12) {
                                // GO 按钮
                                Button(action: {}) {
                                    Image(systemName: "location.fill")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 36, height: 36)
                                        .background(Color.blue)
                                        .cornerRadius(8)
                                }
                                
                                // Deliver 按钮
                                Button(action: {}) {
                                    Image(systemName: "checkmark.circle.fill")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 36, height: 36)
                                        .background(Color.green)
                                        .cornerRadius(8)
                                }
                                
                                // More 按钮
                                Button(action: {}) {
                                    Image(systemName: "ellipsis")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.gray)
                                        .frame(width: 36, height: 36)
                                        .background(Color(.systemGray5))
                                        .cornerRadius(8)
                                }
                            }
                        }
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(radius: 1)
                    }
                }
                .padding()
                
                // 问题说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("❌ 存在的问题：")
                        .font(.subheadline.bold())
                        .foregroundColor(.red)
                    
                    Text("• GoFo 黄色标签在 Dark Mode 下过于刺眼")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 蓝色和绿色按钮对比度不足")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• More 按钮在暗色背景下不够明显")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 地址文字可读性差")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 优化后版本
    private var newVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化后 - 改进效果")
                    .font(.headline)
                    .foregroundColor(.green)
                    .padding()
                
                // 路线头部区域 - 优化后
                VStack(spacing: 12) {
                    HStack {
                        // GoFo 标签 - 优化后（自适应颜色）
                        Text("GoFo")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.adaptiveGoFo)
                            .cornerRadius(6)
                        
                        Spacer()
                        
                        // 全部清除按钮
                        Text("全部清除")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.adaptiveError)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.adaptiveError.opacity(0.1))
                            .cornerRadius(6)
                    }
                    .padding()
                    .adaptiveCardStyle()
                    
                    // 地址列表项 - 优化后
                    ForEach(1...3, id: \.self) { index in
                        HStack(spacing: 12) {
                            // 编号圆圈
                            ZStack {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.adaptivePrimaryIcon)
                                    .frame(width: 36, height: 36)
                                
                                Text("\(index)")
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            
                            // 地址信息
                            VStack(alignment: .leading, spacing: 4) {
                                Text("GoFo: \(index)")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.adaptiveGoFo)
                                    .cornerRadius(6)
                                
                                Text("\(3420 + index * 100) Tupelo Drive, 95209")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(.adaptivePrimaryText)
                                    .lineLimit(2)
                            }
                            
                            Spacer()
                            
                            // 操作按钮 - 优化后
                            HStack(spacing: 12) {
                                // GO 按钮
                                Button(action: {}) {
                                    Image(systemName: "location.fill")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 36, height: 36)
                                        .background(Color.adaptivePrimaryIcon)
                                        .cornerRadius(8)
                                }
                                
                                // Deliver 按钮
                                Button(action: {}) {
                                    Image(systemName: "checkmark.circle.fill")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 36, height: 36)
                                        .background(Color.adaptiveSuccess)
                                        .cornerRadius(8)
                                }
                                
                                // More 按钮
                                Button(action: {}) {
                                    Image(systemName: "ellipsis")
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.adaptiveSecondaryText)
                                        .frame(width: 36, height: 36)
                                        .background(Color.adaptiveSecondaryButton)
                                        .cornerRadius(8)
                                }
                            }
                        }
                        .padding()
                        .adaptiveCardStyle()
                    }
                }
                .padding()
                
                // 改进说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("✅ 改进效果：")
                        .font(.subheadline.bold())
                        .foregroundColor(.green)
                    
                    Text("• GoFo 标签使用深金色，Dark Mode 下更协调")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 按钮使用自适应颜色，对比度更好")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• More 按钮在暗色背景下更明显")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 地址文字使用自适应颜色，可读性提升")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 专为司机夜间驾驶优化")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveSuccess.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
        }
        .background(Color.adaptiveBackground)
    }
}

#Preview("Route Dark Mode - Light") {
    RouteDarkModePreview()
        .preferredColorScheme(.light)
}

#Preview("Route Dark Mode - Dark") {
    RouteDarkModePreview()
        .preferredColorScheme(.dark)
}
