import SwiftUI

struct PainPointSolutionView: View {
    var body: some View {
        VStack(spacing: 0) {
            // 标题
            Text("traditional_vs_navibatch_pro".localized)
                .font(.headline)
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color(UIColor.secondarySystemBackground))

            // 对比内容
            HStack(spacing: 8) {
                // 传统方式
                VStack(alignment: .leading, spacing: 12) {
                    Label("traditional_method".localized, systemImage: "exclamationmark.triangle.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.orange)

                    VStack(alignment: .leading, spacing: 8) {
                        painPointRow(icon: "clock.fill", text: "each_address_takes_3_5_seconds".localized)
                        painPointRow(icon: "repeat", text: "need_repeat_14_times".localized)
                        painPointRow(icon: "arrow.up.arrow.down", text: "navigation_order_often_confused".localized)
                        painPointRow(icon: "hand.tap", text: "error_prone_need_redo".localized)
                        painPointRow(icon: "arrow.uturn.backward", text: "address_order_reversed_manual_adjust".localized)
                    }

                    Spacer()

                    Text("total_time_60_seconds".localized)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.red)
                        .padding(.vertical, 8)
                }
                .padding()
                .background(Color.orange.opacity(0.05))



                // NaviBatch方式
                VStack(alignment: .leading, spacing: 12) {
                    Label("NaviBatch Pro", systemImage: "checkmark.seal.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.green)

                    VStack(alignment: .leading, spacing: 8) {
                        solutionRow(icon: "bolt.fill", text: "one_click_add_all".localized, highlight: true)
                        solutionRow(icon: "arrow.triangle.swap", text: "smart_grouping_auto_sorting".localized)
                        solutionRow(icon: "arrow.forward", text: "maintain_correct_visit_order".localized)
                        solutionRow(icon: "hand.thumbsup.fill", text: "zero_errors_zero_repetition".localized)
                        solutionRow(icon: "bolt.fill", text: "speed_boost_60x".localized, highlight: true)
                    }

                    Spacer()

                    Text("total_time_1_second".localized)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.green)
                        .padding(.vertical, 8)
                }
                .padding()
                .background(Color.green.opacity(0.05))
            }
            .frame(height: 250) // 增加高度以容纳新增的行
        }
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5)
        .padding(.horizontal)
    }

    private func painPointRow(icon: String, text: String) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.orange.opacity(0.8))
                .frame(width: 20)

            Text(text)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }

    private func solutionRow(icon: String, text: String, highlight: Bool = false) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.green.opacity(0.8))
                .frame(width: 20)

            Text(text)
                .font(.subheadline)
                .foregroundColor(highlight ? .green : .secondary)
                .fontWeight(highlight ? .bold : .regular)
        }
    }
}

struct PainPointSolutionView_Previews: PreviewProvider {
    static var previews: some View {
        PainPointSolutionView()
            .previewLayout(.sizeThatFits)
            .padding()
    }
}
