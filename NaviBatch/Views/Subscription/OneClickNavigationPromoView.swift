import SwiftUI
import StoreKit
import UIKit

struct OneClickNavigationPromoView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isAnimating = false
    @State private var showDetailedDemo = false
    @State private var showSubscriptionView = false
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 关闭按钮
                HStack {
                    Spacer()
                    Button(action: {
                        #if DEBUG
                        print("[DEBUG] OneClickNavigationPromoView - 关闭按钮被点击")
                        #endif
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                    .padding([.top, .trailing], 16)
                }

                // 1. 顶部标题区 - 突出核心价值主张
                VStack(spacing: 8) {
                    Text("one_click_navigation_grouping".localized)
                        .font(.system(size: 28, weight: .bold))

                    HStack(spacing: 4) {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.yellow)
                            .opacity(isAnimating ? 1 : 0.7)
                            .scaleEffect(isAnimating ? 1.2 : 1)
                            .animation(
                                Animation.easeInOut(duration: 0.6)
                                    .repeatForever(autoreverses: true),
                                value: isAnimating
                            )

                        Text("speed_60x_faster".localized)
                            .font(.system(size: 24, weight: .heavy))
                            .foregroundColor(.blue)

                        Image(systemName: "bolt.fill")
                            .foregroundColor(.yellow)
                            .opacity(isAnimating ? 1 : 0.7)
                            .scaleEffect(isAnimating ? 1.2 : 1)
                            .animation(
                                Animation.easeInOut(duration: 0.6)
                                    .repeatForever(autoreverses: true)
                                    .delay(0.3),
                                value: isAnimating
                            )
                    }

                    Text("goodbye_manual_address_adding".localized)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.top, 4)
                }
                .padding(.top, 8)

                // 2. 痛点与解决方案对比 - 支持滚动
                ImprovedPainPointSolutionView()

                // 3. 动态演示对比
                TimeComparisonView()

                // 4. 量化价值卡片
                QuantifiedValueCard()

                // 5. 详细演示按钮
                Button(action: {
                    #if DEBUG
                    print("[DEBUG] OneClickNavigationPromoView - 观看详细演示按钮被点击")
                    #endif
                    showDetailedDemo = true
                }) {
                    HStack {
                        Image(systemName: "play.fill")
                        Text("watch_detailed_demo".localized)
                    }
                    .font(.headline)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue, lineWidth: 1)
                    )
                }
                .padding(.horizontal)

                // 6. 订阅按钮
                VStack(spacing: 8) {
                    Button(action: {
                        #if DEBUG
                        print("[DEBUG] 主视图中的订阅按钮被点击 - 打开订阅视图")
                        #endif

                        // 提供触觉反馈
                        let generator = UIImpactFeedbackGenerator(style: .medium)
                        generator.impactOccurred()

                        // 显示订阅视图
                        showSubscriptionView = true
                        #if DEBUG
                        print("[DEBUG] OneClickNavigationPromoView - 设置 showSubscriptionView = true")
                        #endif
                    }) {
                        HStack {
                            Image(systemName: "crown.fill")
                            Text("upgrade_to_pro_now".localized)
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Text("free_trial_7_days".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
            }
            .padding(.bottom, 32)
        }
        .onAppear {
            isAnimating = true
            #if DEBUG
            print("[DEBUG] OneClickNavigationPromoView 已出现")
            #endif
        }
        .sheet(isPresented: $showDetailedDemo) {
            DetailedDemoView()
        }
        .sheet(isPresented: $showSubscriptionView) {
            // 使用统一订阅视图 - 现代风格，适合sheet展示
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: false, // 避免循环引用
                presentationMode: .sheet
            ))
            .presentationDetents([.large], selection: .constant(.large))
            .presentationDragIndicator(.hidden)
        }
    }
}

// 订阅按钮组件
struct SubscriptionButton: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var storeKitManager = StoreKitManager.shared
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var showSubscriptionView = false

    var body: some View {
        VStack(spacing: 8) {
            Button(action: {
                #if DEBUG
                print("[DEBUG] 订阅按钮被点击 - 打开订阅视图")
                #endif

                // 提供触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                // 确保产品已加载
                Task {
                    do {
                        isLoading = true
                        try await storeKitManager.loadProducts()

                        // 检查是否有产品
                        if storeKitManager.products.isEmpty {
                            #if DEBUG
                            print("[DEBUG] 订阅按钮 - 没有找到产品，尝试使用测试产品")

                            // 在开发环境中，我们总是将其视为测试环境
                            print("[DEBUG] 订阅按钮 - 在开发环境中，使用测试产品")
                            // 创建测试产品
                            storeKitManager.createTestProducts()
                            #endif
                        }

                        await MainActor.run {
                            isLoading = false
                            // 显示订阅视图
                            showSubscriptionView = true
                            #if DEBUG
                            print("[DEBUG] 订阅视图显示标志已设置为 true")
                            #endif
                        }
                    } catch {
                        await MainActor.run {
                            isLoading = false
                            #if DEBUG
                            print("[ERROR] 加载产品失败: \(error.localizedDescription)")

                            // 在开发环境中，我们仍然显示订阅视图
                            print("[DEBUG] 订阅按钮 - 在开发环境中，尽管加载失败，仍然显示订阅视图")
                            // 创建测试产品
                            storeKitManager.createTestProducts()
                            // 显示订阅视图
                            showSubscriptionView = true
                            #else
                            errorMessage = "加载产品失败: \(error.localizedDescription)"
                            #endif
                        }
                    }
                }
            }) {
                HStack {
                    Image(systemName: "crown.fill")
                    Text("upgrade_to_pro_now".localized)
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle()) // 确保按钮样式不会干扰点击事件
            .sheet(isPresented: $showSubscriptionView) {
                // 使用统一订阅视图 - 现代风格，适合sheet展示
                UnifiedSubscriptionView(config: SubscriptionViewConfig(
                    style: .modern,
                    showTitle: true,
                    showCloseButton: true,
                    showFeatureComparison: true,
                    showOneClickPromo: false, // 避免循环引用
                    presentationMode: .sheet
                ))
                .presentationDetents([.large], selection: .constant(.large))
                .presentationDragIndicator(.visible)
            }

            Text("free_trial_7_days".localized)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal)
        .padding(.top, 8)
        .onAppear {
            print("[DEBUG] 订阅按钮视图已出现")

            // 加载产品
            Task {
                do {
                    try await storeKitManager.loadProducts()
                } catch {
                    print("[ERROR] 订阅按钮 - 加载产品失败: \(error.localizedDescription)")
                    errorMessage = "加载产品失败: \(error.localizedDescription)"
                }
            }
        }
        .alert(isPresented: .init(
            get: { errorMessage != nil },
            set: { if !$0 { errorMessage = nil } }
        )) {
            Alert(
                title: Text("error".localized),
                message: Text(errorMessage ?? "unknown_error".localized),
                dismissButton: .default(Text("confirm".localized))
            )
        }
        .overlay {
            if isLoading {
                ZStack {
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()

                    ProgressView()
                        .scaleEffect(1.5)
                        .tint(.white)
                }
            }
        }
    }
}

// MARK: - 改进的痛点解决方案对比视图
struct ImprovedPainPointSolutionView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("traditional_vs_navibatch_pro".localized)
                .font(.headline)
                .padding(.horizontal)

            // 使用ScrollView支持横向滚动查看完整内容
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // 传统方式
                    VStack(alignment: .leading, spacing: 12) {
                        Label("traditional_method".localized, systemImage: "exclamationmark.triangle.fill")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.orange)

                        VStack(alignment: .leading, spacing: 8) {
                            painPointRow(icon: "clock.fill", text: "each_address_takes_3_5_seconds".localized)
                            painPointRow(icon: "repeat", text: "need_repeat_14_times".localized)
                            painPointRow(icon: "arrow.up.arrow.down", text: "navigation_order_often_confused".localized)
                            painPointRow(icon: "hand.tap", text: "error_prone_need_redo".localized)
                            painPointRow(icon: "arrow.uturn.backward", text: "address_order_reversed_manual_adjust".localized)
                            painPointRow(icon: "exclamationmark.circle", text: "drivers_get_lost_affect_efficiency".localized)
                            painPointRow(icon: "clock.arrow.circlepath", text: "repetitive_operations_waste_time".localized)
                        }

                        Spacer()

                        Text("total_time_60_seconds".localized)
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.red)
                            .padding(.vertical, 8)
                    }
                    .frame(width: 280) // 固定宽度确保内容完整显示
                    .padding()
                    .background(Color.orange.opacity(0.05))
                    .cornerRadius(12)

                    // NaviBatch方式
                    VStack(alignment: .leading, spacing: 12) {
                        Label("navibatch_pro".localized, systemImage: "checkmark.seal.fill")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.green)

                        VStack(alignment: .leading, spacing: 8) {
                            solutionRow(icon: "bolt.fill", text: "one_click_add_all".localized, highlight: true)
                            solutionRow(icon: "arrow.triangle.swap", text: "smart_grouping_auto_sorting".localized)
                            solutionRow(icon: "arrow.forward", text: "maintain_correct_visit_order".localized)
                            solutionRow(icon: "hand.thumbsup.fill", text: "zero_errors_zero_repetition".localized)
                            solutionRow(icon: "map.fill", text: "optimize_routes_reduce_distance".localized)
                            solutionRow(icon: "speedometer", text: "improve_delivery_efficiency_accuracy".localized)
                            solutionRow(icon: "bolt.fill", text: "speed_boost_60x".localized, highlight: true)
                        }

                        Spacer()

                        Text("total_time_1_second".localized)
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.green)
                            .padding(.vertical, 8)
                    }
                    .frame(width: 280) // 固定宽度确保内容完整显示
                    .padding()
                    .background(Color.green.opacity(0.05))
                    .cornerRadius(12)
                }
                .padding(.horizontal)
            }

            // 滚动提示
            HStack {
                Spacer()
                Text("swipe_to_view_full_comparison".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            .padding(.top, 4)
        }
        .background(Color(UIColor.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 10)
        .padding(.horizontal)
    }

    private func painPointRow(icon: String, text: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.orange.opacity(0.8))
                .frame(width: 20)
                .padding(.top, 2)

            Text(text)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    private func solutionRow(icon: String, text: String, highlight: Bool = false) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.green.opacity(0.8))
                .frame(width: 20)
                .padding(.top, 2)

            Text(text)
                .font(.subheadline)
                .foregroundColor(highlight ? .green : .secondary)
                .fontWeight(highlight ? .bold : .regular)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

struct OneClickNavigationPromoView_Previews: PreviewProvider {
    static var previews: some View {
        OneClickNavigationPromoView()
    }
}
