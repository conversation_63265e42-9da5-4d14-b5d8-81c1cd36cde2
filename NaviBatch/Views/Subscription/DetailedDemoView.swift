import SwiftUI

struct DetailedDemoView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var currentStep = 0

    let steps = [
        (title: "选择您的路线", description: "在NaviBatch中选择已优化的配送路线", image: "step1"),
        (title: "点击一键导航", description: "在路线详情页面点击\"一键导航分组\"按钮", image: "step2"),
        (title: "自动添加地址", description: "系统自动将所有地址按顺序添加到导航应用", image: "step3"),
        (title: "开始导航", description: "无需任何额外操作，立即开始按优化顺序导航", image: "step4")
    ]

    var body: some View {
        VStack(spacing: 0) {
            // 顶部标题栏
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.primary)
                        .padding()
                }

                Spacer()

                Text("功能演示")
                    .font(.headline)

                Spacer()

                // 平衡布局的空按钮
                Button(action: {}) {
                    Image(systemName: "xmark")
                        .foregroundColor(.clear)
                        .padding()
                }
            }
            .background(Color(UIColor.secondarySystemBackground))

            // 步骤内容
            TabView(selection: $currentStep) {
                ForEach(0..<steps.count, id: \.self) { index in
                    VStack(spacing: 20) {
                        // 这里应该是实际的截图图片
                        // 由于无法提供实际图片，使用占位符
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(height: 300)
                                .cornerRadius(12)

                            VStack {
                                Image(systemName: ["map", "hand.tap", "arrow.up.doc.fill", "location.fill"][index])
                                    .font(.system(size: 60))
                                    .foregroundColor(.blue)

                                Text("step_screenshot".localized(with: index + 1))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.top, 8)
                            }
                        }

                        VStack(spacing: 8) {
                            Text(steps[index].title)
                                .font(.title3)
                                .fontWeight(.semibold)

                            Text(steps[index].description)
                                .font(.body)
                                .multilineTextAlignment(.center)
                                .foregroundColor(.secondary)
                                .padding(.horizontal)
                        }
                    }
                    .padding()
                    .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .always))

            // 底部导航按钮
            HStack(spacing: 20) {
                Button(action: {
                    withAnimation {
                        currentStep = max(0, currentStep - 1)
                    }
                }) {
                    HStack {
                        Image(systemName: "chevron.left")
                        Text("previous_step".localized)
                    }
                    .foregroundColor(currentStep > 0 ? .blue : .gray)
                    .padding()
                }
                .disabled(currentStep == 0)

                Spacer()

                Button(action: {
                    withAnimation {
                        if currentStep < steps.count - 1 {
                            currentStep += 1
                        } else {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                }) {
                    HStack {
                        Text(currentStep < steps.count - 1 ? "next_step".localized : "done".localized)
                        Image(systemName: currentStep < steps.count - 1 ? "chevron.right" : "checkmark")
                    }
                    .foregroundColor(.blue)
                    .padding()
                }
            }
            .padding(.horizontal)
            .background(Color(UIColor.secondarySystemBackground))
        }
    }
}

struct DetailedDemoView_Previews: PreviewProvider {
    static var previews: some View {
        DetailedDemoView()
    }
}
