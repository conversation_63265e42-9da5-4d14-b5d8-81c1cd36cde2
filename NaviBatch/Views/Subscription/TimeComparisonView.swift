import SwiftUI

struct TimeComparisonView: View {
    @State private var isAnimating = false
    @State private var traditionalProgress: CGFloat = 0
    @State private var oneClickProgress: CGFloat = 0

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                Text("time_comparison".localized)
                    .font(.headline)
                    .padding(.top, 4)

                // 传统方式进度条
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "person.fill")
                            .foregroundColor(.orange)

                        Text("traditional_method".localized)
                            .font(.subheadline)

                        Spacer()

                        Text("seconds_format".localized(with: Int(traditionalProgress * 60)))
                            .font(.system(size: 15, weight: .medium))
                            .foregroundColor(.orange)
                            .animation(.linear(duration: 0.1), value: traditionalProgress)
                    }

                    ZStack(alignment: .leading) {
                        // 背景条
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 10)
                            .cornerRadius(5)

                        // 进度条
                        Rectangle()
                            .fill(Color.orange)
                            .frame(width: UIScreen.main.bounds.width * 0.8 * traditionalProgress, height: 10)
                            .cornerRadius(5)
                    }

                    // 地址添加指示器
                    if isAnimating {
                        HStack {
                            ForEach(1...14, id: \.self) { i in
                                Circle()
                                    .fill(traditionalProgress * 14 >= Double(i) ? Color.orange : Color.gray.opacity(0.3))
                                    .frame(width: 8, height: 8)
                                    .animation(.easeIn(duration: 0.2), value: traditionalProgress)
                            }
                        }
                        .padding(.top, 4)
                    }

                    // 传统方式的问题说明
                    VStack(alignment: .leading, spacing: 4) {
                        Text("traditional_method_problems".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 8)

                        Text("each_address_3_5_seconds_14_total_60".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("repetitive_operations_cause_fatigue".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("address_order_reversed_last_becomes_first".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("need_manual_reverse_adding_takes_longer".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color.orange.opacity(0.05))
                .cornerRadius(10)

                // NaviBatch方式进度条
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.blue)

                        Text("one_click_navigation_grouping".localized)
                            .font(.subheadline)

                        Spacer()

                        Text("seconds_format".localized(with: Int(oneClickProgress * 1)))
                            .font(.system(size: 15, weight: .medium))
                            .foregroundColor(.blue)
                            .animation(.linear(duration: 0.1), value: oneClickProgress)
                    }

                    ZStack(alignment: .leading) {
                        // 背景条
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 10)
                            .cornerRadius(5)

                        // 进度条
                        Rectangle()
                            .fill(Color.blue)
                            .frame(width: UIScreen.main.bounds.width * 0.8 * oneClickProgress, height: 10)
                            .cornerRadius(5)
                    }

                    // 地址添加指示器
                    if isAnimating {
                        HStack {
                            ForEach(1...14, id: \.self) { _ in
                                Circle()
                                    .fill(oneClickProgress >= 1.0 ? Color.blue : Color.gray.opacity(0.3))
                                    .frame(width: 8, height: 8)
                            }
                        }
                        .padding(.top, 4)
                    }

                    // NaviBatch优势说明
                    VStack(alignment: .leading, spacing: 4) {
                        Text("navibatch_advantages".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 8)

                        Text("add_14_addresses_1_second_60x_faster".localized)
                            .font(.caption)
                            .foregroundColor(.green)
                            .bold()

                        Text("auto_maintain_correct_order_no_adjustment".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("zero_error_rate_no_repetition".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color.blue.opacity(0.05))
                .cornerRadius(10)

                // 节省时间标签
                if isAnimating && traditionalProgress > 0.5 {
                    HStack {
                        Spacer()

                        VStack(spacing: 4) {
                            Text("save_59_seconds".localized)
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.white)

                            Text("speed_boost_60x_simple".localized)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white.opacity(0.9))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.green, Color.blue]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(20)
                        .shadow(color: Color.blue.opacity(0.3), radius: 5)
                        .transition(.scale.combined(with: .opacity))
                    }
                    .padding(.top, 8)
                }
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.05), radius: 10)
            .padding(.horizontal)
        }
        .onAppear {
            isAnimating = true

            // 传统方式进度动画 - 缓慢增长到100%
            withAnimation(.linear(duration: 6.0)) {
                traditionalProgress = 1.0
            }

            // 一键导航进度动画 - 快速增长到100%
            withAnimation(.linear(duration: 0.1)) {
                oneClickProgress = 1.0
            }
        }
    }
}

struct TimeComparisonView_Previews: PreviewProvider {
    static var previews: some View {
        TimeComparisonView()
            .previewLayout(.sizeThatFits)
            .padding()
    }
}
