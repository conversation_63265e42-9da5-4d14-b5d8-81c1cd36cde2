import SwiftUI

/// 订阅成功视图
/// 显示订阅成功后的庆祝界面和功能介绍
struct SubscriptionSuccessView: View {
    let subscriptionTier: SubscriptionTier
    let onDismiss: () -> Void
    
    @State private var showConfetti = false
    @State private var animateFeatures = false
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    subscriptionTier == .expert ? Color.purple.opacity(0.1) : Color.blue.opacity(0.1),
                    Color.clear
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 32) {
                    // 顶部间距
                    Spacer(minLength: 40)
                    
                    // 成功图标和标题
                    VStack(spacing: 20) {
                        // 庆祝图标
                        ZStack {
                            Circle()
                                .fill(subscriptionTier == .expert ? Color.purple.opacity(0.2) : Color.blue.opacity(0.2))
                                .frame(width: 120, height: 120)
                            
                            Image(systemName: "crown.fill")
                                .font(.system(size: 50))
                                .foregroundColor(subscriptionTier == .expert ? .purple : .blue)
                                .scaleEffect(showConfetti ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: showConfetti)
                        }
                        
                        // 成功标题
                        Text("subscription_success_title".localized)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.primary)
                        
                        // 订阅详情
                        Text(getSuccessMessage())
                            .font(.body)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                    }
                    
                    // 解锁功能列表
                    VStack(alignment: .leading, spacing: 16) {
                        Text("subscription_success_features".localized)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .opacity(animateFeatures ? 1.0 : 0.0)
                            .animation(.easeInOut(duration: 0.8).delay(0.5), value: animateFeatures)
                        
                        // 功能列表
                        VStack(alignment: .leading, spacing: 12) {
                            SubscriptionFeatureRow(
                                icon: "infinity",
                                title: "unlimited_addresses".localized,
                                description: "plan_as_many_stops_as_needed".localized,
                                delay: 0.7
                            )

                            SubscriptionFeatureRow(
                                icon: "bolt.fill",
                                title: "one_click_navigation".localized,
                                description: "speed_60x_faster".localized,
                                delay: 0.9
                            )

                            SubscriptionFeatureRow(
                                icon: "map.fill",
                                title: "route_optimization".localized,
                                description: "save_fuel_30".localized,
                                delay: 1.1
                            )

                            if subscriptionTier == .expert {
                                SubscriptionFeatureRow(
                                    icon: "percent",
                                    title: "annual_savings".localized,
                                    description: "save_30_percent".localized,
                                    delay: 1.3
                                )
                            }
                        }
                        .opacity(animateFeatures ? 1.0 : 0.0)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(16)
                    .padding(.horizontal)
                    
                    // 行动按钮
                    VStack(spacing: 16) {
                        Button(action: onDismiss) {
                            Text("subscription_success_action".localized)
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(subscriptionTier == .expert ? Color.purple : Color.blue)
                                .cornerRadius(12)
                        }
                        .padding(.horizontal)
                        
                        Button(action: onDismiss) {
                            Text("close".localized)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer(minLength: 40)
                }
            }
        }
        .onAppear {
            // 启动动画
            withAnimation(.easeInOut(duration: 0.6)) {
                showConfetti = true
            }
            
            withAnimation(.easeInOut(duration: 0.8).delay(0.3)) {
                animateFeatures = true
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func getSuccessMessage() -> String {
        switch subscriptionTier {
        case .pro:
            // 这里需要判断是月度还是年度，暂时使用月度
            return "subscription_success_pro_monthly".localized
        case .expert:
            return "subscription_success_expert".localized
        case .free:
            return "subscription_success_pro_monthly".localized
        }
    }
}

/// 功能行组件
struct SubscriptionFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    let delay: Double

    @State private var isVisible = false

    var body: some View {
        HStack(spacing: 16) {
            // 图标
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)

            // 文本内容
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .opacity(isVisible ? 1.0 : 0.0)
        .offset(x: isVisible ? 0 : 50)
        .animation(.easeInOut(duration: 0.6).delay(delay), value: isVisible)
        .onAppear {
            isVisible = true
        }
    }
}

#Preview("SubscriptionSuccessView") {
    SubscriptionSuccessView(subscriptionTier: .pro) {
        print("Dismissed")
    }
}
