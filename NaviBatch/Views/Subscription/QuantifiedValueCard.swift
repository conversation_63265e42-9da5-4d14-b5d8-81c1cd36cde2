import SwiftUI

struct QuantifiedValueCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("actual_benefits_one_click_navigation".localized)
                .font(.headline)
                .padding(.horizontal)

            VStack(spacing: 0) {
                // 每日收益
                valueRow(
                    title: "daily_savings".localized,
                    value: "daily_savings_value".localized,
                    description: "daily_savings_description".localized,
                    icon: "clock.fill",
                    color: .blue
                )

                Divider()
                    .padding(.leading, 56)
                    .opacity(0.5)

                // 每月收益
                valueRow(
                    title: "monthly_savings".localized,
                    value: "monthly_savings_value".localized,
                    description: "monthly_savings_description".localized,
                    icon: "calendar",
                    color: .purple
                )

                Divider()
                    .padding(.leading, 56)
                    .opacity(0.5)

                // 燃油节省
                valueRow(
                    title: "fuel_savings".localized,
                    value: "fuel_savings_value".localized,
                    description: "fuel_savings_description".localized,
                    icon: "leaf.fill",
                    color: .green
                )

                Divider()
                    .padding(.leading, 56)
                    .opacity(0.5)

                // 收入提升
                valueRow(
                    title: "income_increase".localized,
                    value: "income_increase_value".localized,
                    description: "income_increase_description".localized,
                    icon: "dollarsign.circle.fill",
                    color: .orange
                )
            }
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
            .padding(.horizontal)
        }
    }

    private func valueRow(title: String, value: String, description: String, icon: String, color: Color) -> some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(color)
                .frame(width: 40, height: 40)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                HStack(alignment: .firstTextBaseline, spacing: 6) {
                    Text(value)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(color)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()
        }
        .padding()
    }
}

struct QuantifiedValueCard_Previews: PreviewProvider {
    static var previews: some View {
        QuantifiedValueCard()
            .previewLayout(.sizeThatFits)
            .padding()
    }
}
