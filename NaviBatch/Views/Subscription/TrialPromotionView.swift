import SwiftUI

/// 60天试用推广页面 - 仿YouTube Premium风格
struct TrialPromotionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showingSubscriptionSheet = false
    @State private var showingTermsDetails = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 简洁的黑白主题背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.black,  // 纯黑色
                        Color(red: 0.1, green: 0.1, blue: 0.1),  // 深灰色
                        Color(red: 0.95, green: 0.95, blue: 0.95) // 浅灰色
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 顶部导航栏
                    HStack {
                        // App Logo和名称
                        HStack(spacing: 8) {
                            Text("NaviBatch Pro")
                                .font(.title2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                        }
                        
                        Spacer()
                        
                        // 关闭按钮 - 调整位置避免被状态栏遮挡
                        Button(action: {
                            // 用户明确关闭推广页面
                            TrialPromotionManager.shared.userDismissedPromotion()
                            dismiss()
                        }) {
                            Text("✕")
                                .font(.title2)
                                .foregroundColor(.white.opacity(0.8))
                                .padding(8)
                                .background(Color.black.opacity(0.3))
                                .clipShape(Circle())
                        }
                        .padding(.trailing, 8) // 增加右边距，避免被状态栏遮挡
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, max(geometry.safeAreaInsets.top + 10, 50))
                    
                    Spacer()
                    
                    // 主要内容区域 - 优化响应式布局
                    VStack(spacing: min(24, geometry.size.height * 0.03)) {
                        // 主标题 - 根据屏幕尺寸调整字体大小
                        VStack(spacing: 12) {
                            Text("trial_promotion_title_1".localized)
                                .font(.system(size: min(32, geometry.size.width * 0.08), weight: .bold))
                                .foregroundColor(.white)

                            Text("trial_promotion_title_2".localized)
                                .font(.system(size: min(32, geometry.size.width * 0.08), weight: .bold))
                                .foregroundColor(.white)

                            Text("trial_promotion_title_3".localized)
                                .font(.system(size: min(32, geometry.size.width * 0.08), weight: .bold))
                                .foregroundColor(.white)
                        }
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.horizontal, 20)

                        // 副标题描述
                        VStack(spacing: 8) {
                            Text("trial_promotion_subtitle_1".localized)
                                .font(.system(size: 16))
                                .foregroundColor(.white.opacity(0.9))

                            Text("trial_promotion_subtitle_2".localized)
                                .font(.system(size: 16))
                                .foregroundColor(.white.opacity(0.9))
                        }
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.horizontal, 20)
                        
                        // 特性列表 - 优化间距
                        VStack(spacing: min(16, geometry.size.height * 0.02)) {
                            TrialFeatureRow(
                                title: "trial_promotion_feature_unlimited_addresses".localized,
                                description: "trial_promotion_feature_unlimited_addresses_desc".localized
                            )

                            TrialFeatureRow(
                                title: "trial_promotion_feature_60x_speed".localized,
                                description: "trial_promotion_feature_60x_speed_desc".localized
                            )

                            TrialFeatureRow(
                                title: "trial_promotion_feature_smart_optimization".localized,
                                description: "trial_promotion_feature_smart_optimization_desc".localized
                            )

                            TrialFeatureRow(
                                title: "trial_promotion_feature_unlimited_groups".localized,
                                description: "trial_promotion_feature_unlimited_groups_desc".localized
                            )
                        }
                        .padding(.horizontal, 20)
                    }
                    
                    Spacer()
                    
                    // 底部按钮区域 - 优化间距和安全区域
                    VStack(spacing: 16) {
                        // 主要CTA按钮
                        Button(action: {
                            showingSubscriptionSheet = true
                        }) {
                            HStack {
                                Text("trial_promotion_start_trial".localized)
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(Color.black)
                            .cornerRadius(28)
                        }
                        .padding(.horizontal, 20)

                        // 稍后再说按钮
                        Button(action: {
                            // 用户选择稍后再说
                            TrialPromotionManager.shared.userDismissedPromotion()
                            dismiss()
                        }) {
                            Text("maybe_later".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.horizontal, 20)

                        // 免责声明
                        VStack(spacing: 6) {
                            Text("trial_promotion_terms".localized)
                                .font(.system(size: 12))
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                                .lineLimit(nil)

                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showingTermsDetails.toggle()
                                }
                            }) {
                                Text(showingTermsDetails ? "▲" : "▼")
                                    .font(.system(size: 12))
                                    .foregroundColor(.white.opacity(0.8))
                            }

                            // 条款详情
                            if showingTermsDetails {
                                VStack(spacing: 8) {
                                    Text("trial_promotion_terms_details".localized)
                                        .font(.system(size: 11))
                                        .foregroundColor(.white.opacity(0.7))
                                        .multilineTextAlignment(.center)
                                        .lineLimit(nil)
                                        .padding(.top, 8)

                                    HStack(spacing: 16) {
                                        Button("privacy_policy".localized) {
                                            // 打开隐私政策
                                            if let url = URL(string: "https://navibatch.com/privacy") {
                                                UIApplication.shared.open(url)
                                            }
                                        }
                                        .font(.system(size: 11))
                                        .foregroundColor(.blue.opacity(0.8))

                                        Button("terms_of_service".localized) {
                                            // 打开服务条款
                                            if let url = URL(string: "https://navibatch.com/terms") {
                                                UIApplication.shared.open(url)
                                            }
                                        }
                                        .font(.system(size: 11))
                                        .foregroundColor(.blue.opacity(0.8))
                                    }
                                }
                                .transition(.opacity.combined(with: .move(edge: .top)))
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.bottom, max(geometry.safeAreaInsets.bottom, 20))
                }
            }
        }
        .ignoresSafeArea()
        .sheet(isPresented: $showingSubscriptionSheet) {
            SubscriptionView()
        }
    }
    
    private func startFreeTrial() {
        // 启动免费试用
        subscriptionManager.startFreeTrial()
        // 通知管理器用户开始了试用
        TrialPromotionManager.shared.userStartedTrial()
        dismiss()
    }
}

/// 试用推广特性行组件 - 优化响应式设计
struct TrialFeatureRow: View {
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // 文字内容 - 优化排版
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(2)

                Text(description)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.95))
                    .lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
            }

            Spacer(minLength: 0)
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    TrialPromotionView()
}
