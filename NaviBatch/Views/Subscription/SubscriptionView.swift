import SwiftUI
import StoreKit
import UIKit

// MARK: - 订阅视图配置
struct SubscriptionViewConfig {
    var style: SubscriptionViewStyle = .modern
    var showTitle: Bool = true
    var showCloseButton: Bool = true
    var showFeatureComparison: Bool = true

    var showOneClickPromo: Bool = true
    var presentationMode: PresentationMode = .sheet

    enum SubscriptionViewStyle {
        case classic
        case modern
        case compact
    }

    enum PresentationMode {
        case sheet
        case fullScreen
        case overlay
    }
}

// MARK: - 统一订阅视图
struct UnifiedSubscriptionView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.customDismiss) private var customDismiss
    @Environment(\.colorScheme) private var colorScheme
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared
    @ObservedObject private var storeKitManager = StoreKitManager.shared

    let config: SubscriptionViewConfig

    @State private var selectedTier: SubscriptionTier = .pro
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var showOneClickPromo = false
    @State private var showTimeComparison = false
    @State private var showSuccessView = false


    // 用于调试
    private let viewID = UUID().uuidString

    init(config: SubscriptionViewConfig = SubscriptionViewConfig()) {
        self.config = config
    }

    var body: some View {
        Group {
            switch config.style {
            case .modern:
                modernStyleView
            case .classic:
                modernStyleView // 使用现代风格替代
            case .compact:
                modernStyleView // 使用现代风格替代
            }
        }
        .onAppear {
            setupView()
        }
        .sheet(isPresented: $showOneClickPromo) {
            if config.showOneClickPromo {
                OneClickNavigationPromoView()
                    .presentationDetents([.medium, .large])
                    .presentationDragIndicator(.hidden)
            }
        }
        .sheet(isPresented: $showTimeComparison) {
            NavigationStack {
                TimeComparisonView()
                    .navigationTitle("time_comparison".localized)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .cancellationAction) {
                            Button("close".localized) {
                                showTimeComparison = false
                            }
                        }
                    }
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.hidden)
        }
        .fullScreenCover(isPresented: $showSuccessView) {
            SubscriptionSuccessView(subscriptionTier: selectedTier) {
                showSuccessView = false
                handleDismiss()
            }
        }

    }

    // MARK: - 现代风格视图
    private var modernStyleView: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变 - 适配暗黑模式
                LinearGradient(
                    gradient: Gradient(colors: colorScheme == .dark ? [
                        Color(red: 0.02, green: 0.02, blue: 0.02),
                        Color(red: 0.08, green: 0.08, blue: 0.08)
                    ] : [
                        Color(red: 0.06, green: 0.17, blue: 0.36),
                        Color(red: 0.12, green: 0.06, blue: 0.36)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                // 主内容
                VStack(spacing: 0) {
                    // 顶部按钮栏
                    if config.showCloseButton {
                        topButtonBar
                    }

                    // 滚动内容
                    ScrollView {
                        VStack(spacing: 16) {
                            if config.showTitle {
                                modernHeaderSection
                            }

                            if config.showFeatureComparison {
                                modernFeatureSection(geometry: geometry)
                            }

                            modernPricingSection
                            modernPurchaseSection

                            if config.showOneClickPromo {
                                modernLearnMoreSection
                            }

                            modernLegalSection
                        }
                    }
                }

                // 加载遮罩
                if isLoading {
                    loadingOverlay
                }
            }
        }
    }

    // MARK: - 经典风格视图 (已注释，使用现代风格替代)
    /*
    private var classicStyleView: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    if config.showTitle {
                        classicHeaderSection
                    }

                    if config.showFeatureComparison {
                        classicFeatureComparisonSection
                    }

                    classicSubscriptionPlansSection
                    classicPurchaseButtonSection
                    classicRestorePurchaseButton

                    if config.showOneClickPromo {
                        classicLearnMoreButton
                    }



                    classicTermsAndPrivacySection
                }
                .padding()
            }
            .navigationTitle(config.showTitle ? "upgrade_your_plan".localized : "")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                if config.showCloseButton {
                    ToolbarItem(placement: .cancellationAction) {
                        Button("close".localized) {
                            handleDismiss()
                        }
                    }
                }
            }
            .overlay {
                if storeKitManager.isLoading || isLoading {
                    loadingOverlay
                }
            }
            .alert(isPresented: .init(
                get: { storeKitManager.errorMessage != nil || errorMessage != nil },
                set: { if !$0 { storeKitManager.errorMessage = nil; errorMessage = nil } }
            )) {
                Alert(
                    title: Text("error".localized),
                    message: Text(storeKitManager.errorMessage ?? errorMessage ?? "未知错误"),
                    dismissButton: .default(Text("confirm".localized))
                )
            }
        }
    }
    */

    // MARK: - 紧凑风格视图 (已注释，使用现代风格替代)
    /*
    private var compactStyleView: some View {
        VStack(spacing: 16) {
            if config.showCloseButton {
                HStack {
                    Spacer()
                    Button(action: handleDismiss) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                }
                .padding(.horizontal)
            }

            VStack(spacing: 12) {
                Text("upgrade_to_pro_version".localized)
                    .font(.title2)
                    .fontWeight(.bold)

                Text("unlock_all_premium_features".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                // 简化的功能列表
                VStack(alignment: .leading, spacing: 8) {
                    compactFeatureRow(icon: "infinity", text: "unlimited_addresses".localized)
                    compactFeatureRow(icon: "location.fill", text: "one_click_navigation_short".localized)
                    compactFeatureRow(icon: "fuelpump.fill", text: "save_30_percent_fuel".localized)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 价格选择
                HStack(spacing: 12) {
                    compactPriceButton(tier: .pro, title: "monthly_short".localized, price: "pro_tier_price".localized)
                    compactPriceButton(tier: .expert, title: "yearly_short".localized, price: "expert_tier_price".localized, badge: "save_30_percent".localized)
                }

                // 购买按钮
                Button(action: purchaseSelectedPlan) {
                    Text(purchaseButtonText)
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(selectedTier == .pro ? Color.blue : Color.purple)
                        .cornerRadius(12)
                }
                .disabled(isLoading)

                // OneClickPromo入口
                if config.showOneClickPromo {
                    Button(action: {
                        showOneClickPromo = true
                    }) {
                        HStack {
                            Image(systemName: "bolt.fill")
                                .foregroundColor(.orange)
                                .font(.caption)

                            Text("see_60x_speed_demo".localized)
                                .font(.caption)
                                .foregroundColor(.blue)

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.caption2)
                                .foregroundColor(.gray)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                Text("free_trial_7_days_cancel_anytime".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 10)
    }
    */
}

struct SubscriptionView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.subscriptionViewOptions) private var options
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared
    @ObservedObject private var storeKitManager = StoreKitManager.shared

    @State private var selectedTier: SubscriptionTier = .pro
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var showOneClickPromo = false
    @State private var showSuccessView = false


    // 用于调试
    private let viewID = UUID().uuidString

    // 动态生成购买按钮文本
    private var purchaseButtonText: String {
        guard let product = storeKitManager.product(for: selectedTier) else {
            return "upgrade".localized
        }

        // 检查用户是否已经在试用期中
        let isCurrentlyInTrial = subscriptionManager.isInFreeTrial

        // 检查产品是否有试用期配置
        if let subscription = product.subscription,
           let introOffer = subscription.introductoryOffer {

            // 如果是免费试用期且用户当前不在试用期
            if introOffer.paymentMode == .freeTrial && !isCurrentlyInTrial {
                let period = introOffer.period

                // 根据试用期长度生成文本
                if period.unit == .day && period.value == 60 {
                    return "Start 60-day free trial"
                } else if period.unit == .month && period.value == 2 {
                    return "Start 60-day free trial"
                } else if period.unit == .week && period.value == 1 {
                    return "Start 7-day free trial"
                } else if period.unit == .day {
                    return "Start \(period.value)-day free trial"
                } else if period.unit == .month {
                    let days = period.value * 30 // 近似计算
                    return "Start \(days)-day free trial"
                } else {
                    return "Start free trial"
                }
            }
        }

        // 如果用户已在试用期或没有试用期，显示普通升级文本
        if isCurrentlyInTrial {
            return selectedTier == .pro ? "Switch to Monthly" : "Switch to Annual"
        } else {
            return "upgrade".localized
        }
    }

    // 动态生成试用期信息文本
    private var trialInfoText: String {
        guard let product = storeKitManager.product(for: selectedTier) else {
            return "free_trial_7_days_cancel_anytime".localized
        }

        // 检查产品是否有试用期配置
        if let subscription = product.subscription,
           let introOffer = subscription.introductoryOffer,
           introOffer.paymentMode == .freeTrial {

            let period = introOffer.period

            // 根据试用期长度生成信息文本
            if period.unit == .day && period.value == 60 {
                return "Free for 60 days, then cancel anytime"
            } else if period.unit == .month && period.value == 2 {
                return "Free for 60 days, then cancel anytime"
            } else if period.unit == .week && period.value == 1 {
                return "free_trial_7_days_cancel_anytime".localized
            } else if period.unit == .day {
                return "Free for \(period.value) days, then cancel anytime"
            } else if period.unit == .month {
                let days = period.value * 30 // 近似计算
                return "Free for \(days) days, then cancel anytime"
            } else {
                return "Free trial, cancel anytime"
            }
        }

        // 如果没有试用期，显示默认文本
        return "Cancel anytime"
    }

    var body: some View {
        // 重定向到统一订阅视图以确保一致性
        UnifiedSubscriptionView(config: SubscriptionViewConfig(
            style: .modern,
            showTitle: options.showTitle,
            showCloseButton: options.showCloseButton,
            showFeatureComparison: true,
            showOneClickPromo: true,
            presentationMode: .sheet
        ))
    }

    // 保留原始实现作为备份（已注释）
    /*
    var originalBody: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部标题
                    if options.showTitle {
                        headerSection
                    }

                    // 功能对比
                    featureComparisonSection

                    // 订阅计划选择
                    subscriptionPlansSection

                    // 购买按钮
                    purchaseButtonSection

                    // 恢复购买按钮
                    restorePurchaseButton

                    // 了解更多按钮
                    learnMoreButton



                    // 条款和隐私政策
                    termsAndPrivacySection
                }
                .padding()
            }
            .navigationTitle(options.showTitle ? "upgrade_your_plan".localized : "")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                if options.showCloseButton {
                    ToolbarItem(placement: .cancellationAction) {
                        Button("close".localized) {
                            print("[DEBUG] SubscriptionView[\(viewID)] - 关闭按钮被点击")
                            dismiss()
                        }
                    }
                }
            }
            .overlay {
                if storeKitManager.isLoading || isLoading {
                    loadingOverlay
                }
            }
            .alert(isPresented: .init(
                get: { storeKitManager.errorMessage != nil || errorMessage != nil },
                set: { if !$0 { storeKitManager.errorMessage = nil; errorMessage = nil } }
            )) {
                Alert(
                    title: Text("error".localized),
                    message: Text(storeKitManager.errorMessage ?? errorMessage ?? "unknown_error".localized),
                    dismissButton: .default(Text("confirm".localized))
                )
            }
            .sheet(isPresented: $showOneClickPromo) {
                OneClickNavigationPromoView()
            }
            .onAppear {
                #if DEBUG
                print("[DEBUG] SubscriptionView[\(viewID)] - 视图出现")
                #endif

                // 提供触觉反馈，确认视图已显示
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                // 检查 StoreKit 测试配置
                #if DEBUG
                print("[DEBUG] SubscriptionView[\(viewID)] - 检查 StoreKit 测试配置")
                storeKitManager.printStoreKitTestConfiguration()
                #endif

                // 加载产品
                Task {
                    do {
                        #if DEBUG
                        print("[DEBUG] SubscriptionView[\(viewID)] - 开始加载产品")
                        #endif
                        isLoading = true

                        // 打印产品 ID
                        #if DEBUG
                        let productIDs = SubscriptionTier.allCases
                            .map { $0.productID }
                            .filter { !$0.isEmpty }
                        print("[DEBUG] SubscriptionView[\(viewID)] - 请求产品 ID: \(productIDs)")
                        #endif

                        try await storeKitManager.loadProducts(forceReload: true) // 强制重新加载产品

                        // 在MainActor上下文中更新UI
                        isLoading = false
                        #if DEBUG
                        print("[DEBUG] SubscriptionView[\(viewID)] - 产品加载成功: \(storeKitManager.products.count) 个产品")
                        #endif

                        // 打印产品信息以便调试
                        #if DEBUG
                        for (index, product) in storeKitManager.products.enumerated() {
                            print("[DEBUG] SubscriptionView[\(viewID)] - 产品 \(index+1): ID=\(product.id), 价格=\(product.displayPrice)")
                        }
                        #endif

                        // 如果没有产品，显示错误信息
                        if storeKitManager.products.isEmpty {
                            #if DEBUG
                            print("[DEBUG] SubscriptionView[\(viewID)] - 没有找到产品，尝试使用测试产品")
                            #endif

                            // 检查是否在测试环境中运行
                            // 在开发过程中，我们总是将其视为测试环境，以便能够使用模拟产品
                            #if DEBUG
                            // 在DEBUG模式下，检查是否有特定环境变量来控制测试环境
                            let forceTestEnvironment = ProcessInfo.processInfo.environment["FORCE_TEST_ENVIRONMENT"]
                            let isTestEnvironment = forceTestEnvironment == "false" ? false : true
                            #else
                            let isTestEnvironment = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" ||
                                                   ProcessInfo.processInfo.environment["STORE_KIT_TEST_MODE"] != nil
                            #endif

                            if isTestEnvironment {
                                #if DEBUG
                                print("[DEBUG] SubscriptionView[\(viewID)] - 在测试环境中运行，创建测试产品")
                                // 创建测试产品 - 使用静态方法调用避免@ObservedObject问题
                                StoreKitManager.shared.createTestProducts()
                                #endif
                                // 默认选择月度计划
                                selectedTier = .pro

                                // 移除自动切换到Pro的逻辑 - 用户可以通过开发者工具手动切换
                                #if DEBUG
                                print("[DEBUG] SubscriptionView[\(viewID)] - 测试环境已激活，当前订阅状态: \(SubscriptionManager.shared.currentTier.rawValue)")
                                print("[DEBUG] SubscriptionView[\(viewID)] - 如需测试Pro功能，请使用开发者工具手动切换")
                                #endif

                                // 移除Toast通知，避免干扰订阅界面显示
                                // NotificationCenter.default.post(
                                //     name: Notification.Name("ShowToast"),
                                //     object: "测试环境已激活 - 请使用开发者工具切换订阅状态"
                                // )
                            } else {
                                // 这个分支现在可以在DEBUG模式下执行，如果设置了FORCE_TEST_ENVIRONMENT=false
                                #if DEBUG
                                print("[DEBUG] SubscriptionView[\(viewID)] - 不在测试环境中，显示错误信息")
                                #endif
                                errorMessage = "product_load_failed_check_connection".localized
                            }
                        } else {
                            // 默认选择月度计划
                            selectedTier = .pro
                        }
                    } catch {
                        await MainActor.run {
                            isLoading = false
                            errorMessage = "product_load_failed".localized(with: error.localizedDescription)
                            print("[ERROR] SubscriptionView[\(viewID)] - 加载产品失败: \(error.localizedDescription)")
                        }
                    }
                }
            }
        }
    }
    */

    // MARK: - 子视图

    // 顶部标题
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("pro_tier_name".localized)
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.primary)

            Text("upgrade_description".localized)
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .padding(.top, 8)
    }

    // 功能对比
    private var featureComparisonSection: some View {
        VStack(spacing: 16) {
            // 标题
            Text("feature_comparison".localized)
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 功能列表
            featureRow(title: "route_optimization".localized, free: true, pro: true)
            featureRow(title: "unlimited_routes".localized, free: true, pro: true)
            featureRow(title: "addresses_per_route".localized, freeValue: "max_20_addresses".localized, proValue: "unlimited_addresses".localized)
            featureRow(title: "one_click_navigation".localized, free: false, pro: true)
            featureRow(title: "package_finder".localized, free: false, pro: true)
            featureRow(title: "fuel_savings".localized, freeValue: "up_to_30_percent".localized, proValue: "save_fuel_30".localized)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
    }

    // 功能行
    private func featureRow(title: String, free: Bool, pro: Bool) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            // 免费版
            Image(systemName: free ? "checkmark.circle.fill" : "xmark.circle")
                .foregroundColor(free ? .green : .gray)
                .frame(width: 80)

            // Pro版
            Image(systemName: pro ? "checkmark.circle.fill" : "xmark.circle")
                .foregroundColor(pro ? .green : .gray)
                .frame(width: 80)
        }
    }

    // 功能行（带值）
    private func featureRow(title: String, freeValue: String, proValue: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            // 免费版
            Text(freeValue)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80)

            // Pro版
            Text(proValue)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80)
        }
    }

    // 订阅计划选择
    private var subscriptionPlansSection: some View {
        VStack(spacing: 16) {
            // 标题
            Text("choose_subscription_plan".localized)
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 月度计划
            subscriptionPlanCard(tier: .pro)

            // 年度计划
            subscriptionPlanCard(tier: .expert)
        }
    }

    // 订阅计划卡片
    private func subscriptionPlanCard(tier: SubscriptionTier) -> some View {
        let isSelected = selectedTier == tier
        let product = storeKitManager.product(for: tier)

        return Button(action: {
            withAnimation {
                selectedTier = tier
            }
        }) {
            HStack {
                // 选择指示器
                ZStack {
                    Circle()
                        .stroke(isSelected ? tier == .pro ? Color.blue : Color.purple : Color.gray, lineWidth: 2)
                        .frame(width: 24, height: 24)

                    if isSelected {
                        Circle()
                            .fill(tier == .pro ? Color.blue : Color.purple)
                            .frame(width: 16, height: 16)
                    }
                }

                // 计划信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(tier == .pro ? "monthly_plan".localized : "yearly_plan".localized)
                        .font(.headline)
                        .foregroundColor(.primary)

                    if let product = product {
                        Text(product.displayPrice + (tier == .pro ? "/month_suffix".localized : "/year_suffix".localized))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text(tier.price)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // 节省标签
                if tier == .expert {
                    Text("save_30_percent".localized)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.purple)
                        .cornerRadius(12)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? (tier == .pro ? Color.blue : Color.purple) : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 购买按钮
    private var purchaseButtonSection: some View {
        VStack(spacing: 8) {
            // 购买按钮
            Button(action: {
                purchaseSelectedPlan()
            }) {
                Text(purchaseButtonText)
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(selectedTier == .pro ? Color.blue : Color.purple)
                    .cornerRadius(12)
            }
            .disabled(storeKitManager.isLoading || isLoading)

            // 试用信息
            Text(trialInfoText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.top, 8)
    }

    // 恢复购买按钮
    private var restorePurchaseButton: some View {
        Button(action: {
            // 恢复购买
            Task {
                do {
                    try await storeKitManager.restorePurchases()
                } catch {
                    print("[ERROR] SubscriptionView - 恢复购买失败: \(error.localizedDescription)")
                    errorMessage = "restore_purchases_failed".localized(with: error.localizedDescription)
                }
            }
        }) {
            Text("restore_purchases".localized)
                .font(.subheadline)
                .foregroundColor(.blue)
        }
        .padding(.top, 8)
        .disabled(storeKitManager.isLoading || isLoading)
    }

    // 了解更多按钮
    private var learnMoreButton: some View {
        Button(action: {
            showOneClickPromo = true
        }) {
            Text("learn_more".localized)
                .font(.subheadline)
                .foregroundColor(.blue)
        }
        .padding(.top, 4)
    }



    // 条款和隐私政策
    private var termsAndPrivacySection: some View {
        VStack(spacing: 12) {
            Text("subscription_auto_renew_notice".localized)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            // 更明显的链接布局
            VStack(spacing: 8) {
                Button(action: {
                    // 打开服务条款
                    if let url = URL(string: "https://www.navibatch.com/terms.html") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "doc.text")
                            .font(.caption)
                        Text("terms_of_service".localized)
                            .font(.caption)
                        Image(systemName: "arrow.up.right.square")
                            .font(.caption2)
                    }
                    .foregroundColor(.blue)
                }

                Button(action: {
                    // 打开隐私政策
                    if let url = URL(string: "https://www.navibatch.com/privacy.html") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "hand.raised")
                            .font(.caption)
                        Text("privacy_policy".localized)
                            .font(.caption)
                        Image(systemName: "arrow.up.right.square")
                            .font(.caption2)
                    }
                    .foregroundColor(.blue)
                }
            }
        }
        .padding(.top, 16)
    }

    // 加载遮罩 - 适配暗黑模式
    private var loadingOverlay: some View {
        ZStack {
            Color.black.opacity(colorScheme == .dark ? 0.6 : 0.4)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)

                Text("loading".localized)
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6).opacity(0.8))
                    .blur(radius: 0.5)
            )
        }
    }

    // MARK: - 辅助方法

    // 购买选中的计划
    private func purchaseSelectedPlan() {
        guard let product = storeKitManager.product(for: selectedTier) else {
            errorMessage = "product_info_unavailable".localized
            return
        }

        isLoading = true

        Task {
            do {
                let success = try await storeKitManager.purchase(product)

                await MainActor.run {
                    isLoading = false

                    if success {
                        // 购买成功，显示成功页面
                        showSuccessView = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    #if DEBUG
                    print("[ERROR] SubscriptionView - 购买失败: \(error.localizedDescription)")
                    #endif
                    errorMessage = "purchase_failed".localized(with: error.localizedDescription)
                }
            }
        }
    }
}

// MARK: - UnifiedSubscriptionView 实现方法
extension UnifiedSubscriptionView {

    // MARK: - 通用方法
    private func setupView() {
        #if DEBUG
        print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 视图出现")
        #endif

        // 提供触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 加载产品
        Task {
            await loadProducts()
        }
    }

    private func handleDismiss() {
        #if DEBUG
        print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 处理关闭")
        #endif

        // 根据展示模式选择合适的关闭方式
        switch config.presentationMode {
        case .overlay:
            // 覆盖层模式，优先使用customDismiss
            customDismiss()
        case .sheet, .fullScreen:
            // Sheet或全屏模式，使用SwiftUI的dismiss
            dismiss()
        }
    }

    private func loadProducts() async {
        do {
            #if DEBUG
            print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 开始加载产品")
            #endif
            isLoading = true

            try await storeKitManager.loadProducts(forceReload: true)

            await MainActor.run {
                isLoading = false
                #if DEBUG
                print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 产品加载成功: \(storeKitManager.products.count) 个产品")
                #endif

                if storeKitManager.products.isEmpty {
                    #if DEBUG
                    print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 在测试环境中创建测试产品")
                    // 使用静态方法调用避免@ObservedObject问题
                    StoreKitManager.shared.createTestProducts()
                    print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 测试产品已创建，当前订阅状态: \(SubscriptionManager.shared.currentTier.rawValue)")
                    print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 如需测试Pro功能，请使用开发者工具手动切换")
                    #else
                    errorMessage = "product_load_failed_check_connection".localized
                    #endif
                }

                selectedTier = .pro
            }
        } catch {
            await MainActor.run {
                isLoading = false
                errorMessage = "product_load_failed".localized(with: error.localizedDescription)
                print("[ERROR] UnifiedSubscriptionView[\(viewID)] - 加载产品失败: \(error.localizedDescription)")
            }
        }
    }

    private func purchaseSelectedPlan() {
        guard let product = storeKitManager.product(for: selectedTier) else {
            #if DEBUG
            errorMessage = "产品信息不可用。产品数量: \(storeKitManager.products.count)。请尝试重新加载产品或等待明天价格生效。"
            #else
            errorMessage = "product_info_unavailable".localized
            #endif
            return
        }

        isLoading = true

        Task {
            do {
                let success = try await storeKitManager.purchase(product)

                await MainActor.run {
                    isLoading = false

                    if success {
                        // 购买成功，显示成功页面
                        showSuccessView = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    #if DEBUG
                    print("[ERROR] UnifiedSubscriptionView - 购买失败: \(error.localizedDescription)")
                    #endif
                    errorMessage = "purchase_failed".localized(with: error.localizedDescription)
                }
            }
        }
    }

    // MARK: - 现代风格组件
    private var topButtonBar: some View {
        HStack {
            HStack(spacing: 8) {
                // 恢复购买按钮
                Button(action: {
                    Task {
                        isLoading = true
                        do {
                            try await storeKitManager.restorePurchases()
                        } catch {
                            NotificationCenter.default.post(
                                name: Notification.Name("ShowToast"),
                                object: "restore_purchases_failed".localized(with: error.localizedDescription)
                            )
                        }
                        isLoading = false
                    }
                }) {
                    Text("restore_purchases".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.9))
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color.white.opacity(0.15))
                        .cornerRadius(16)
                }

                #if false // 临时隐藏调试按钮用于截图推广
                // 重新加载产品按钮（仅调试模式）
                Button(action: {
                    Task {
                        await loadProducts()
                    }
                }) {
                    Text("重新加载产品")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.9))
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color.blue.opacity(0.7))
                        .cornerRadius(16)
                }

                // 验证收据按钮（仅调试模式）
                Button(action: {
                    SubscriptionManager.shared.validateSubscriptionReceipt()
                }) {
                    Text("verify_receipt".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.9))
                        .padding(.vertical, 6)
                        .padding(.horizontal, 12)
                        .background(Color.orange.opacity(0.7))
                        .cornerRadius(16)
                }
                #endif
            }

            Spacer()

            // 关闭按钮
            Button(action: handleDismiss) {
                ZStack {
                    Circle()
                        .fill(Color.black.opacity(0.3))
                        .frame(width: 32, height: 32)

                    Image(systemName: "xmark")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 10)
        .padding(.bottom, 5)
    }

    private var modernHeaderSection: some View {
        VStack(spacing: 8) {
            Text("upgrade_to_pro_version".localized)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            Text("unlock_all_premium_features".localized)
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(.top, 8)
    }

    private func modernFeatureSection(geometry: GeometryProxy) -> some View {
        VStack(spacing: 12) {
            // 标题和说明
            VStack(spacing: 4) {
                Text("free_vs_pro_comparison".localized)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)

                Text("our_free_beats_competitors_paid".localized)
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .padding(.bottom, 8)

            // 表头
            HStack {
                Text("features".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
                    .frame(maxWidth: .infinity, alignment: .leading)

                Text("free_plan".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.green)
                    .frame(width: 70)

                Text("pro_plan".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.yellow)
                    .frame(width: 70)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(Color.white.opacity(0.1))
            .cornerRadius(8)

            // 功能对比
            VStack(spacing: 0) {
                modernComparisonRow(title: "route_optimization".localized, free: true, pro: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "unlimited_routes".localized, free: true, pro: true, freeHighlight: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "addresses_per_route".localized, freeValue: "up_to_20".localized, proValue: "unlimited".localized, freeHighlight: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "smart_optimization".localized, free: true, pro: true, freeHighlight: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "auto_grouping".localized, freeValue: "free_tier_grouping_limit".localized, proValue: "pro_tier_unlimited_grouping".localized, freeHighlight: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "one_click_navigation".localized, freeValue: "free_tier_navigation_limit".localized, proValue: "pro_tier_unlimited_navigation".localized, badge: "60x", freeHighlight: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "package_finder".localized, free: true, pro: true, freeHighlight: true)
                Divider().background(Color.white.opacity(0.1))
                modernComparisonRow(title: "save_fuel_30".localized, freeValue: "up_to_20_percent".localized, proValue: "up_to_30_percent".localized)
            }
            .background(Color.white.opacity(0.05))
            .cornerRadius(10)
        }
        .padding(.horizontal, 16)
    }

    private func modernComparisonRow(title: String, free: Bool? = nil, pro: Bool? = nil, freeValue: String? = nil, proValue: String? = nil, badge: String? = nil, freeHighlight: Bool = false) -> some View {
        HStack(alignment: .center, spacing: 12) {
            // 功能名称
            HStack {
                Text(title)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.white)

                if let badge = badge {
                    Text(badge)
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 1)
                        .background(Color.blue)
                        .cornerRadius(4)
                }

                Spacer()
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Free版本
            HStack {
                if let freeValue = freeValue {
                    Text(freeValue)
                        .font(.system(size: 11, weight: freeHighlight ? .bold : .medium))
                        .foregroundColor(freeHighlight ? .green : .white.opacity(0.9))
                } else if let free = free {
                    Image(systemName: free ? "checkmark.circle.fill" : "xmark.circle")
                        .font(.system(size: 14))
                        .foregroundColor(free ? (freeHighlight ? .green : .white.opacity(0.9)) : .gray)
                }
            }
            .frame(width: 70)

            // Pro版本
            HStack {
                if let proValue = proValue {
                    Text(proValue)
                        .font(.system(size: 11, weight: .bold))
                        .foregroundColor(.yellow)
                } else if let pro = pro {
                    Image(systemName: pro ? "checkmark.circle.fill" : "xmark.circle")
                        .font(.system(size: 14))
                        .foregroundColor(pro ? .yellow : .gray)
                }
            }
            .frame(width: 70)
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 16)
        .background(freeHighlight ? Color.green.opacity(0.1) : Color.clear)
    }

    private func modernFeatureRow(title: String, badge: String? = nil) -> some View {
        HStack(alignment: .center, spacing: 12) {
            VStack(alignment: .leading, spacing: 3) {
                Text(title)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }
            Spacer()

            if let badge = badge {
                Text(badge)
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 5)
                    .padding(.vertical, 2)
                    .background(Color.blue)
                    .cornerRadius(8)
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 14)
        .background(Color.white.opacity(0.15))
    }

    private var modernPricingSection: some View {
        VStack(spacing: 16) {
            // 简洁的价格对比 - 按照参考图片设计
            HStack(spacing: 12) {
                // 月度方案
                Button(action: { selectedTier = .pro }) {
                    VStack(spacing: 8) {
                        Text("1 month")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)

                        Text("$9.99/mo")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.primary)

                        Text("Billed monthly")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, minHeight: 90)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(selectedTier == .pro ? Color.orange : Color.gray.opacity(0.3), lineWidth: selectedTier == .pro ? 2 : 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())

                // 年度方案 - 带Save徽章
                Button(action: { selectedTier = .expert }) {
                    ZStack {
                        // 价格卡片 - 突出显示实际计费金额
                        VStack(spacing: 8) {
                            Text("12 months")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.primary)

                            Text("$59.99/year")
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.primary)

                            Text("$4.99/mo equivalent")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity, minHeight: 90)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemBackground))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(selectedTier == .expert ? Color.orange : Color.gray.opacity(0.3), lineWidth: selectedTier == .expert ? 2 : 1)
                                )
                        )

                        // Save 徽章 - 浮在卡片上方
                        VStack {
                            Text("Save \(calculateSavingsPercentage())%")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 4)
                                .background(Color.orange)
                                .cornerRadius(16)
                            Spacer()
                        }
                        .offset(y: -8)
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 16)
    }



    private var modernPurchaseSection: some View {
        VStack(spacing: 12) {
            // 简洁的购买按钮 - 根据选中产品的试用期信息动态显示
            Button(action: purchaseSelectedPlan) {
                Text(purchaseButtonText)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.orange)
                    )
            }
            .disabled(isLoading)

            Text(trialInfoText)
                .font(.system(size: 14))
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }

    // 计算节省百分比
    private func calculateSavingsPercentage() -> Int {
        let monthlyPrice: Double = 9.99
        let annualPrice: Double = 59.99
        let annualMonthlyEquivalent = annualPrice / 12.0
        let savings = (monthlyPrice - annualMonthlyEquivalent) / monthlyPrice
        return Int(round(savings * 100))
    }

    // 动态生成购买按钮文本
    private var purchaseButtonText: String {
        guard let product = storeKitManager.product(for: selectedTier) else {
            #if DEBUG
            print("[DEBUG] UnifiedSubscriptionView - 没有找到产品: \(selectedTier.productID)")
            print("  可用产品: \(storeKitManager.products.map { $0.id })")
            print("  在测试环境中，假设有60天试用期")
            #endif

            // 在测试环境中，如果没有找到产品，假设有60天试用期
            #if DEBUG
            let isCurrentlyInTrial = subscriptionManager.isInFreeTrial
            if !isCurrentlyInTrial {
                return "Start 60-day free trial"
            } else {
                return selectedTier == .pro ? "Switch to Monthly" : "Switch to Annual"
            }
            #else
            return "upgrade".localized
            #endif
        }

        // 检查用户是否已经在试用期中
        let isCurrentlyInTrial = subscriptionManager.isInFreeTrial

        #if DEBUG
        print("[DEBUG] UnifiedSubscriptionView - 购买按钮文本检查:")
        print("  产品ID: \(product.id)")
        print("  用户当前试用期状态: \(isCurrentlyInTrial)")
        print("  产品有试用期配置: \(product.subscription?.introductoryOffer != nil)")
        if let subscription = product.subscription {
            print("  订阅组ID: \(subscription.subscriptionGroupID)")
            if let introOffer = subscription.introductoryOffer {
                print("  试用期类型: \(introOffer.paymentMode)")
                print("  试用期长度: \(introOffer.period)")
                print("  试用期价格: \(introOffer.displayPrice)")
            } else {
                print("  ⚠️ 没有找到试用期配置")
            }
        } else {
            print("  ⚠️ 产品不是订阅类型")
        }
        #endif

        // 检查产品是否有试用期配置
        if let subscription = product.subscription,
           let introOffer = subscription.introductoryOffer {

            #if DEBUG
            print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 检查试用期信息:")
            print("  产品ID: \(product.id)")
            print("  试用期类型: \(introOffer.paymentMode)")
            print("  试用期长度: \(introOffer.period)")
            print("  试用期价格: \(introOffer.displayPrice)")
            print("  用户当前是否在试用期: \(isCurrentlyInTrial)")
            #endif

            // 如果是免费试用期且用户当前不在试用期
            if introOffer.paymentMode == .freeTrial && !isCurrentlyInTrial {
                let period = introOffer.period

                // 根据试用期长度生成文本
                if period.unit == .day && period.value == 60 {
                    return "Start 60-day free trial"
                } else if period.unit == .month && period.value == 2 {
                    return "Start 60-day free trial"
                } else if period.unit == .week && period.value == 1 {
                    return "Start 7-day free trial"
                } else if period.unit == .day {
                    return "Start \(period.value)-day free trial"
                } else if period.unit == .month {
                    let days = period.value * 30 // 近似计算
                    return "Start \(days)-day free trial"
                } else {
                    return "Start free trial"
                }
            }
        }

        // 如果用户已在试用期或没有试用期，显示普通升级文本
        if isCurrentlyInTrial {
            return selectedTier == .pro ? "Switch to Monthly" : "Switch to Annual"
        } else {
            return "upgrade".localized
        }
    }

    // 动态生成试用期信息文本
    private var trialInfoText: String {
        guard let product = storeKitManager.product(for: selectedTier) else {
            #if DEBUG
            // 在测试环境中，假设有60天试用期
            return "Free for 60 days, then cancel anytime"
            #else
            return "free_trial_7_days_cancel_anytime".localized
            #endif
        }

        // 检查产品是否有试用期配置
        if let subscription = product.subscription,
           let introOffer = subscription.introductoryOffer,
           introOffer.paymentMode == .freeTrial {

            let period = introOffer.period

            // 根据试用期长度生成信息文本
            if period.unit == .day && period.value == 60 {
                return "Free for 60 days, then cancel anytime"
            } else if period.unit == .month && period.value == 2 {
                return "Free for 60 days, then cancel anytime"
            } else if period.unit == .week && period.value == 1 {
                return "free_trial_7_days_cancel_anytime".localized
            } else if period.unit == .day {
                return "Free for \(period.value) days, then cancel anytime"
            } else if period.unit == .month {
                let days = period.value * 30 // 近似计算
                return "Free for \(days) days, then cancel anytime"
            } else {
                return "Free trial, cancel anytime"
            }
        }

        // 如果没有试用期，显示默认文本
        return "Cancel anytime"
    }

    private var modernLearnMoreSection: some View {
        VStack(spacing: 8) {
            Button(action: {
                showOneClickPromo = true
            }) {
                HStack {
                    Image(systemName: "bolt.fill")
                        .foregroundColor(.yellow)

                    Text("discover_60x_speed_boost".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.white.opacity(0.1))
                .cornerRadius(10)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }

    private var modernLegalSection: some View {
        VStack(spacing: 10) {
            Text("payment_terms_notice".localized)
                .font(.system(size: 10))
                .foregroundColor(.white.opacity(0.6))
                .multilineTextAlignment(.center)
                .padding(.bottom, 4)

            HStack(spacing: 20) {
                Button(action: {
                    if let url = URL(string: "https://www.navibatch.com/privacy.html") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Text("privacy_policy".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.blue.opacity(0.8))
                        .underline()
                }

                Button(action: {
                    if let url = URL(string: "https://www.navibatch.com/terms.html") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Text("terms_of_use".localized)
                        .font(.system(size: 12))
                        .foregroundColor(.blue.opacity(0.8))
                        .underline()
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 12)
        .padding(.bottom, 16)
    }

    // MARK: - 经典风格组件 (已注释，使用现代风格替代)
    /*
    private var classicHeaderSection: some View {
        VStack(spacing: 8) {
            Text("pro_tier_name".localized)
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.primary)

            Text("upgrade_description".localized)
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .padding(.top, 8)
    }

    private var classicFeatureComparisonSection: some View {
        VStack(spacing: 16) {
            Text("feature_comparison".localized)
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            classicFeatureRow(title: "route_optimization".localized, free: true, pro: true)
            classicFeatureRow(title: "unlimited_routes".localized, free: true, pro: true)
            classicFeatureRow(title: "addresses_per_route".localized, freeValue: "max_20_addresses".localized, proValue: "unlimited_addresses".localized)
            classicFeatureRow(title: "one_click_navigation".localized, free: false, pro: true)
            classicFeatureRow(title: "package_finder".localized, free: false, pro: true)
            classicFeatureRow(title: "fuel_savings".localized, freeValue: "up_to_30_percent".localized, proValue: "save_fuel_30".localized)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
    }

    private func classicFeatureRow(title: String, free: Bool, pro: Bool) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            Image(systemName: free ? "checkmark.circle.fill" : "xmark.circle")
                .foregroundColor(free ? .green : .gray)
                .frame(width: 80)

            Image(systemName: pro ? "checkmark.circle.fill" : "xmark.circle")
                .foregroundColor(pro ? .green : .gray)
                .frame(width: 80)
        }
    }

    private func classicFeatureRow(title: String, freeValue: String, proValue: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            Text(freeValue)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80)

            Text(proValue)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80)
        }
    }

    private var classicSubscriptionPlansSection: some View {
        VStack(spacing: 16) {
            Text("choose_subscription_plan".localized)
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            classicSubscriptionPlanCard(tier: .pro)
            classicSubscriptionPlanCard(tier: .expert)
        }
    }

    private func classicSubscriptionPlanCard(tier: SubscriptionTier) -> some View {
        let isSelected = selectedTier == tier
        let product = storeKitManager.product(for: tier)

        return Button(action: {
            withAnimation {
                selectedTier = tier
            }
        }) {
            HStack {
                ZStack {
                    Circle()
                        .stroke(isSelected ? (tier == .pro ? Color.blue : Color.purple) : Color.gray, lineWidth: 2)
                        .frame(width: 24, height: 24)

                    if isSelected {
                        Circle()
                            .fill(tier == .pro ? Color.blue : Color.purple)
                            .frame(width: 16, height: 16)
                    }
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(tier == .pro ? "monthly_plan".localized : "yearly_plan".localized)
                        .font(.headline)
                        .foregroundColor(.primary)

                    if let product = product {
                        Text(product.displayPrice + (tier == .pro ? "/month_suffix".localized : "/year_suffix".localized))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text(tier.price)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                if tier == .expert {
                    Text("save_30_percent".localized)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.purple)
                        .cornerRadius(12)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? (tier == .pro ? Color.blue : Color.purple) : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var classicPurchaseButtonSection: some View {
        VStack(spacing: 8) {
            Button(action: purchaseSelectedPlan) {
                Text(purchaseButtonText)
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(selectedTier == .pro ? Color.blue : Color.purple)
                    .cornerRadius(12)
            }
            .disabled(storeKitManager.isLoading || isLoading)

            Text(trialInfoText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.top, 8)
    }

    private var classicRestorePurchaseButton: some View {
        Button(action: {
            Task {
                do {
                    try await storeKitManager.restorePurchases()
                } catch {
                    print("[ERROR] UnifiedSubscriptionView - 恢复购买失败: \(error.localizedDescription)")
                    errorMessage = "restore_purchases_failed".localized(with: error.localizedDescription)
                }
            }
        }) {
            Text("restore_purchases".localized)
                .font(.subheadline)
                .foregroundColor(.blue)
        }
        .padding(.top, 8)
        .disabled(storeKitManager.isLoading || isLoading)
    }

    private var classicLearnMoreButton: some View {
        Button(action: {
            showOneClickPromo = true
        }) {
            Text("learn_more".localized)
                .font(.subheadline)
                .foregroundColor(.blue)
        }
        .padding(.top, 4)
    }



    private var classicTermsAndPrivacySection: some View {
        VStack(spacing: 8) {
            Text("subscription_auto_renew_notice".localized)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            HStack(spacing: 4) {
                Button(action: {
                    if let url = URL(string: "https://www.navibatch.com/terms.html") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Text("terms_of_service".localized)
                        .font(.caption)
                        .foregroundColor(.blue)
                }

                Text("and".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Button(action: {
                    if let url = URL(string: "https://www.navibatch.com/privacy.html") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Text("privacy_policy".localized)
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(.top, 16)
    }
    */

    // MARK: - 紧凑风格组件 (已注释，使用现代风格替代)
    /*
    private func compactFeatureRow(icon: String, text: String) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)

            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()
        }
    }

    private func compactPriceButton(tier: SubscriptionTier, title: String, price: String, badge: String? = nil) -> some View {
        Button(action: {
            selectedTier = tier
        }) {
            VStack(spacing: 4) {
                if let badge = badge {
                    Text(badge)
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.purple)
                        .cornerRadius(8)
                }

                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(price)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(selectedTier == tier ? Color.blue.opacity(0.1) : Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(selectedTier == tier ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    */

    // MARK: - 试用期相关方法
    #if DEBUG
    private func resetTrialStatusInSandbox() async {
        print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 重置沙盒试用期状态")

        // 在沙盒环境中，我们可以通过重置订阅状态来模拟重置试用期
        await subscriptionManager.resetSubscriptionForTesting()

        // 重新加载产品以获取最新的试用期信息
        await loadProducts()

        print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 沙盒试用期状态已重置")
    }

    private func checkTrialEligibility() async {
        print("[DEBUG] UnifiedSubscriptionView[\(viewID)] - 检查试用期资格")

        for tier in SubscriptionTier.allCases {
            guard let product = storeKitManager.product(for: tier) else { continue }

            if let subscription = product.subscription,
               let introOffer = subscription.introductoryOffer {
                print("[DEBUG] 产品 \(product.id):")
                print("  - 试用期类型: \(introOffer.paymentMode)")
                print("  - 试用期长度: \(introOffer.period)")
                print("  - 试用期价格: \(introOffer.displayPrice)")
            } else {
                print("[DEBUG] 产品 \(product.id): 无试用期配置")
            }
        }

        print("[DEBUG] 用户当前试用期状态: \(subscriptionManager.isInFreeTrial ? "试用中" : "非试用")")
        print("[DEBUG] 用户当前订阅状态: \(subscriptionManager.currentTier.rawValue)")
    }
    #endif

    // MARK: - 通用组件
    private var loadingOverlay: some View {
        ZStack {
            Color.black.opacity(colorScheme == .dark ? 0.6 : 0.4)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)

                Text("loading".localized)
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6).opacity(0.8))
                    .blur(radius: 0.5)
            )
        }
    }
}

// MARK: - 使用示例
/*
 统一订阅视图使用示例（现代风格）：

 1. 全屏覆盖层模式：
 UnifiedSubscriptionView(config: SubscriptionViewConfig(
     style: .modern,
     showTitle: true,
     showCloseButton: true,
     showFeatureComparison: true,
     showOneClickPromo: true,
     presentationMode: .overlay
 ))

 2. Sheet展示模式：
 UnifiedSubscriptionView(config: SubscriptionViewConfig(
     style: .modern,
     showTitle: true,
     showCloseButton: true,
     showFeatureComparison: true,
     showOneClickPromo: true,
     presentationMode: .sheet
 ))

 3. 在MenuView中使用（覆盖层模式）：
 .overlay {
     if showSubscriptionView {
         ZStack {
             Color.black.opacity(0.5).ignoresSafeArea()
             UnifiedSubscriptionView(config: SubscriptionViewConfig(style: .modern))
                 .environment(\.customDismiss, { showSubscriptionView = false })
         }
     }
 }

 4. 在其他视图中使用（sheet模式）：
 .sheet(isPresented: $showSubscriptionView) {
     UnifiedSubscriptionView(config: SubscriptionViewConfig(style: .modern))
 }
 */

// MARK: - 预览
#Preview("统一订阅视图 - 现代风格") {
    UnifiedSubscriptionView(config: SubscriptionViewConfig(style: .modern))
}

#Preview("简洁价格对比") {
    VStack(spacing: 16) {
        Text("Price Comparison Preview")
            .font(.title2)
            .padding()

        Text("See main app for full pricing UI")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}

#Preview("原始订阅视图") {
    // 重定向到统一订阅视图以确保一致性
    UnifiedSubscriptionView(config: SubscriptionViewConfig(style: .modern))
}
