import SwiftUI

// 演示已优化路线点（紫色）的新矩形+三角形设计
struct OptimizedPinDemo: View {
    var body: some View {
        VStack(spacing: 30) {
            Text("已优化路线点（紫色）设计演示")
                .font(.title2)
                .padding()
            
            Text("所有已优化的路线点现在都使用统一的矩形+三角形设计")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // 优化前后对比
            VStack(spacing: 20) {
                Text("路线优化状态对比")
                    .font(.headline)
                
                HStack(spacing: 50) {
                    VStack {
                        OptimizedPinExample(number: 1, isOptimized: false)
                        Text("未优化")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                    
                    Image(systemName: "arrow.right")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    VStack {
                        OptimizedPinExample(number: 1, isOptimized: true)
                        Text("已优化")
                            .font(.caption)
                            .foregroundColor(Color(hex: "B36AE2"))
                    }
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            // 不同数字的紫色pin展示
            VStack(spacing: 20) {
                Text("已优化路线点示例")
                    .font(.headline)
                
                HStack(spacing: 30) {
                    VStack {
                        OptimizedPinExample(number: 2, isOptimized: true)
                        Text("第2站")
                            .font(.caption)
                    }
                    
                    VStack {
                        OptimizedPinExample(number: 15, isOptimized: true)
                        Text("第15站")
                            .font(.caption)
                    }
                    
                    VStack {
                        OptimizedPinExample(number: 99, isOptimized: true)
                        Text("第99站")
                            .font(.caption)
                    }
                }
            }
            
            // 特点说明
            VStack(alignment: .leading, spacing: 8) {
                Text("紫色Pin特点：")
                    .font(.headline)
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("使用统一的矩形+三角形设计")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("紫色边框和数字，清晰标识已优化状态")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("数字在矩形中完全居中")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("高性能SF Symbols渲染")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("与其他pin保持一致的视觉风格")
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            Text("颜色代码: #B36AE2")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
}

// 优化状态Pin示例
struct OptimizedPinExample: View {
    let number: Int
    let isOptimized: Bool
    let size: CGFloat = 24
    
    private var pinColor: Color {
        isOptimized ? Color(hex: "B36AE2") : Color.blue
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 矩形部分 - 包含数字内容
            ZStack {
                // 主体矩形
                Image(systemName: "rectangle")
                    .font(.system(size: size, weight: .medium))
                    .foregroundStyle(pinColor)

                // 数字 - 在矩形中完全居中
                Text("\(number)")
                    .font(.system(size: size * 0.5, weight: .bold))
                    .foregroundColor(pinColor)
            }
            
            // 向下的实心三角形指针
            Image(systemName: "arrowtriangle.down.fill")
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundStyle(pinColor)
                .offset(y: -2)
        }
    }
}

// Color扩展已在 Extensions/Color+Hex.swift 中定义，无需重复定义

#Preview("OptimizedPinDemo") {
    OptimizedPinDemo()
}
