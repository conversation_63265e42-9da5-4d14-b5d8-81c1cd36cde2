//
//  AddressMigrationView.swift
//  NaviBatch
//
//  Created by System on 2025-01-27.
//  Copyright © 2025 NaviBatch. All rights reserved.
//

import SwiftUI
import SwiftData

/// 🔄 地址迁移界面
struct AddressMigrationView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var migrationManager: AddressMigrationManager
    @State private var needMigrationCount: Int = 0
    @State private var showConfirmation: Bool = false
    
    init(modelContext: ModelContext) {
        self._migrationManager = StateObject(wrappedValue: AddressMigrationManager(modelContext: modelContext))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                
                // 📊 迁移状态卡片
                migrationStatusCard
                
                // 🔍 检查按钮
                checkMigrationButton
                
                // 🚀 迁移按钮
                migrationButton
                
                // 📋 说明信息
                migrationExplanation
                
                Spacer()
            }
            .padding()
            .navigationTitle("地址迁移")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                checkMigrationNeeded()
            }
        }
    }
    
    // MARK: - 迁移状态卡片
    private var migrationStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "location.circle.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("地址格式状态")
                        .font(.headline)
                    
                    if needMigrationCount > 0 {
                        Text("\(needMigrationCount) 个地址需要迁移到新格式")
                            .font(.subheadline)
                            .foregroundColor(.orange)
                    } else {
                        Text("所有地址都已使用新格式")
                            .font(.subheadline)
                            .foregroundColor(.green)
                    }
                }
                
                Spacer()
            }
            
            // 迁移进度
            if migrationManager.isMigrating {
                VStack(spacing: 8) {
                    ProgressView(value: migrationManager.migrationProgress)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text(migrationManager.migrationStatus)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 检查迁移按钮
    private var checkMigrationButton: some View {
        Button(action: checkMigrationNeeded) {
            HStack {
                Image(systemName: "magnifyingglass")
                Text("检查迁移状态")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .cornerRadius(10)
        }
        .disabled(migrationManager.isMigrating)
    }
    
    // MARK: - 迁移按钮
    private var migrationButton: some View {
        Button(action: { showConfirmation = true }) {
            HStack {
                Image(systemName: migrationManager.isMigrating ? "arrow.clockwise" : "arrow.up.circle.fill")
                    .rotationEffect(.degrees(migrationManager.isMigrating ? 360 : 0))
                    .animation(migrationManager.isMigrating ? .linear(duration: 1).repeatForever(autoreverses: false) : .default, value: migrationManager.isMigrating)
                
                Text(migrationManager.isMigrating ? "迁移中..." : "开始迁移")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(needMigrationCount > 0 ? Color.orange : Color.gray.opacity(0.3))
            .foregroundColor(.white)
            .cornerRadius(10)
        }
        .disabled(needMigrationCount == 0 || migrationManager.isMigrating)
        .alert("确认迁移", isPresented: $showConfirmation) {
            Button("取消", role: .cancel) { }
            Button("开始迁移", role: .destructive) {
                Task {
                    await migrationManager.startMigration()
                    checkMigrationNeeded()
                }
            }
        } message: {
            Text("将迁移 \(needMigrationCount) 个地址到新的结构化格式。这个过程可能需要几分钟时间。")
        }
    }
    
    // MARK: - 说明信息
    private var migrationExplanation: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("关于地址迁移")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                explanationItem(
                    icon: "1.circle.fill",
                    title: "新格式优势",
                    description: "结构化地址提供更好的灵活性和国际化支持"
                )
                
                explanationItem(
                    icon: "2.circle.fill",
                    title: "迁移过程",
                    description: "系统会重新验证每个地址并提取详细的地址组件"
                )
                
                explanationItem(
                    icon: "3.circle.fill",
                    title: "安全保障",
                    description: "原始地址信息会保留，确保数据不会丢失"
                )
                
                explanationItem(
                    icon: "4.circle.fill",
                    title: "自动迁移",
                    description: "新添加的地址会自动使用结构化格式"
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    private func explanationItem(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.system(size: 16, weight: .medium))
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 方法
    private func checkMigrationNeeded() {
        needMigrationCount = migrationManager.checkMigrationNeeded()
    }
}

#Preview {
    AddressMigrationView(modelContext: ModelContext(try! ModelContainer(for: DeliveryPoint.self)))
}
