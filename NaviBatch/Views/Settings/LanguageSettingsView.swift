import SwiftUI

// MARK: - 地址验证设置界面
struct AddressValidationSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedMode: AddressValidationMode

    init() {
        // 初始化选中的验证模式
        let initialMode = ReverseGeocodingValidationService.shared.validationMode
        self._selectedMode = State(initialValue: initialMode)
    }

    var body: some View {
        NavigationStack {
            List {
                // 验证模式选择
                Section {
                    ForEach([AddressValidationMode.standard, .strict, .perfect], id: \.self) { mode in
                        Button(action: {
                            selectedMode = mode
                        }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(mode.description)
                                        .foregroundColor(.primary)
                                        .font(.body)

                                    Text(mode.detailDescription)
                                        .foregroundColor(.secondary)
                                        .font(.caption)
                                }

                                Spacer()

                                if selectedMode == mode {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                            }
                        }
                    }
                } header: {
                    Text("address_validation_mode".localized)
                } footer: {
                    Text("validation_description".localized)
                        .font(.caption)
                }

                // 当前设置说明
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("current_settings".localized)
                            .font(.headline)

                        Text("validation_mode_format".localized(with: selectedMode.description))
                            .font(.body)

                        Text("threshold_score_format".localized(with: Int(selectedMode.threshold)))
                            .font(.body)

                        Text(selectedMode.riskDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                }

                // 示例说明
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("validation_example".localized)
                            .font(.headline)

                        Text("original_address_example".localized)
                            .font(.caption)
                            .foregroundColor(.primary)

                        Text("reverse_address_example".localized)
                            .font(.caption)
                            .foregroundColor(.primary)

                        Text("house_number_difference".localized)
                            .font(.caption)
                            .foregroundColor(.red)

                        HStack {
                            Text("result_label".localized)
                                .font(.caption)
                                .bold()

                            switch selectedMode {
                            case .standard:
                                Text("may_pass_warning".localized)
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            case .strict:
                                Text("will_not_pass".localized)
                                    .font(.caption)
                                    .foregroundColor(.red)
                            case .perfect:
                                Text("will_not_pass".localized)
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                } header: {
                    Text("real_case_example".localized)
                } footer: {
                    Text("real_case_description".localized)
                        .font(.caption)
                }
            }
            .navigationTitle("address_validation_settings".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        // 保存验证模式设置（会自动保存到UserDefaults）
                        ReverseGeocodingValidationService.shared.validationMode = selectedMode

                        // 关闭视图
                        dismiss()
                    }
                }
            }
        }
    }
}

struct LanguageSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var localizationManager = LocalizationManager.shared
    @State private var selectedLanguage: LocalizationManager.Language

    init() {
        // 初始化选中的语言
        let initialLanguage = LocalizationManager.shared.selectedLanguage
        self._selectedLanguage = State(initialValue: initialLanguage)
    }

    var body: some View {
        NavigationStack {
            List {
                // 系统语言选项
                Section {
                    Button(action: {
                        selectedLanguage = .system
                    }) {
                        HStack {
                            Text(LocalizationManager.Language.system.displayName)
                                .foregroundColor(.primary)

                            Spacer()

                            if selectedLanguage == .system {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                } header: {
                    Text("system_language_section".localized)
                }

                // 所有支持的语言
                Section {
                    ForEach(LocalizationManager.Language.allCases.filter { $0 != .system }) { language in
                        Button(action: {
                            selectedLanguage = language
                        }) {
                            HStack {
                                Text(language.displayName)
                                    .foregroundColor(.primary)

                                Spacer()

                                if selectedLanguage == language {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                            }
                        }
                    }
                } header: {
                    Text("languages".localized)
                }

                // 语言信息
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("language_info_title".localized)
                            .font(.headline)

                        Text("language_info_description".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("language_settings".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        // 保存语言设置
                        localizationManager.selectedLanguage = selectedLanguage

                        // 关闭视图
                        dismiss()
                    }
                }
            }
        }
        .localizedLayout()
    }
}

// 预览
#Preview("地址验证设置") {
    AddressValidationSettingsView()
}

#Preview("语言设置") {
    LanguageSettingsView()
}
