import SwiftUI

// SF Symbols Pin选项演示 - 不同的高性能实现方案
struct SFSymbolsPinOptions: View {
    let size: CGFloat = 24
    let color: Color = .blue
    
    var body: some View {
        VStack(spacing: 20) {
            Text("SF Symbols Pin选项对比")
                .font(.title2)
                .padding()
            
            HStack(spacing: 30) {
                VStack {
                    // 选项1：矩形+三角形组合 - 当前使用
                    VStack(spacing: 0) {
                        Image(systemName: "rectangle")
                            .font(.system(size: size, weight: .medium))
                            .foregroundStyle(color)
                        Image(systemName: "arrowtriangle.down.fill")
                            .font(.system(size: size * 0.4, weight: .medium))
                            .foregroundStyle(color)
                            .offset(y: -2)
                    }
                    Text("rectangle + 三角形")
                        .font(.caption)
                }

                VStack {
                    // 选项2：经典mappin
                    Image(systemName: "mappin")
                        .font(.system(size: size, weight: .medium))
                        .foregroundStyle(color)
                    Text("mappin")
                        .font(.caption)
                }

                VStack {
                    // 选项3：mappin.circle - 圆形版本
                    Image(systemName: "mappin.circle")
                        .font(.system(size: size, weight: .medium))
                        .foregroundStyle(color)
                    Text("mappin.circle")
                        .font(.caption)
                }
            }
            
            HStack(spacing: 30) {
                VStack {
                    // 选项4：空心效果 - 白色填充+彩色边框
                    ZStack {
                        Image(systemName: "mappin")
                            .font(.system(size: size, weight: .bold))
                            .foregroundStyle(color)
                        Image(systemName: "mappin.fill")
                            .font(.system(size: size - 2, weight: .medium))
                            .foregroundStyle(.white)
                    }
                    Text("空心效果")
                        .font(.caption)
                }
                
                VStack {
                    // 选项5：双层效果 - 阴影+主体
                    ZStack {
                        Image(systemName: "mappin.fill")
                            .font(.system(size: size + 2, weight: .medium))
                            .foregroundStyle(color.opacity(0.3))
                            .offset(x: 1, y: 1)
                        Image(systemName: "mappin.fill")
                            .font(.system(size: size, weight: .medium))
                            .foregroundStyle(color)
                    }
                    Text("阴影效果")
                        .font(.caption)
                }
                
                VStack {
                    // 选项6：渐变效果
                    Image(systemName: "mappin.fill")
                        .font(.system(size: size, weight: .medium))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [color, color.opacity(0.6)],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                    Text("渐变效果")
                        .font(.caption)
                }
            }
            
            Text("性能优势：")
                .font(.headline)
                .padding(.top)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("渲染性能提升 80-90%")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("内存占用减少 70%")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("GPU硬件加速")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("自动适配不同设备")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("代码简化 60%")
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
        .padding()
    }
}

#Preview("SFSymbolsPinOptions") {
    SFSymbolsPinOptions()
}
