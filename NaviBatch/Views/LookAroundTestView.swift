import SwiftUI
import CoreLocation

// 测试实景图功能的演示视图
struct LookAroundTestView: View {
    @State private var selectedLocation = TestLocation.glenWaverley
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("实景图功能测试")
                    .font(.title2)
                    .padding()
                
                Text("选择测试地址")
                    .font(.headline)
                
                // 地址选择器
                Picker("测试地址", selection: $selectedLocation) {
                    ForEach(TestLocation.allCases, id: \.self) { location in
                        Text(location.name).tag(location)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                
                // 显示选中地址的详细信息
                VStack(alignment: .leading, spacing: 8) {
                    Text("地址详情")
                        .font(.headline)
                    
                    Text("名称: \(selectedLocation.name)")
                    Text("地址: \(selectedLocation.address)")
                    Text("坐标: \(selectedLocation.coordinate.latitude), \(selectedLocation.coordinate.longitude)")
                    Text("预期结果: \(selectedLocation.expectedResult)")
                        .foregroundColor(selectedLocation.hasLookAround ? .green : .orange)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 测试卡片
                MapMarkerCalloutView(
                    point: selectedLocation.deliveryPoint,
                    isSubscriber: true
                )
                .frame(width: 350)
                .padding()
                
                // 说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("测试说明：")
                        .font(.headline)
                    
                    Text("• 点击'查看实景'按钮测试实景图功能")
                    Text("• 如果地址有实景图，会显示街景")
                    Text("• 如果没有实景图，会显示提示信息")
                    Text("• 观察控制台日志了解加载过程")
                }
                .font(.caption)
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
                
                Spacer()
            }
        }
        .navigationTitle("实景图测试")
    }
}

// 测试地址枚举
enum TestLocation: CaseIterable {
    case glenWaverley
    case melbourneCBD
    case invalidLocation
    
    var name: String {
        switch self {
        case .glenWaverley:
            return "Glen Waverley"
        case .melbourneCBD:
            return "Melbourne CBD"
        case .invalidLocation:
            return "无效地址"
        }
    }
    
    var address: String {
        switch self {
        case .glenWaverley:
            return "67 Callaghan Avenue, Glen Waverley, VIC, 3150, AU"
        case .melbourneCBD:
            return "Federation Square, Melbourne, VIC, 3000, AU"
        case .invalidLocation:
            return "Invalid Address, Nowhere, XX, 0000, XX"
        }
    }
    
    var coordinate: CLLocationCoordinate2D {
        switch self {
        case .glenWaverley:
            return CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642)
        case .melbourneCBD:
            return CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)
        case .invalidLocation:
            return CLLocationCoordinate2D(latitude: 0.0, longitude: 0.0)
        }
    }
    
    var hasLookAround: Bool {
        switch self {
        case .glenWaverley, .melbourneCBD:
            return true
        case .invalidLocation:
            return false
        }
    }
    
    var expectedResult: String {
        switch self {
        case .glenWaverley:
            return "应该显示住宅区街景"
        case .melbourneCBD:
            return "应该显示城市街景"
        case .invalidLocation:
            return "应该显示'无实景图数据'提示"
        }
    }
    
    var deliveryPoint: DeliveryPoint {
        let point = DeliveryPoint(
            sort_number: 1,
            streetName: address,
            coordinate: coordinate
        )
        point.packageCount = 1
        return point
    }
}

#Preview("LookAroundTestView") {
    LookAroundTestView()
}
