import SwiftUI

/// 扫描器界面 Dark Mode 优化对比预览
/// 展示优化前后的视觉效果差异
struct ScannerDarkModePreview: View {
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签切换器
                Picker("版本", selection: $selectedTab) {
                    Text("优化前").tag(0)
                    Text("优化后").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    // 优化前版本
                    oldVersionView
                        .tag(0)
                    
                    // 优化后版本
                    newVersionView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("扫描器 Dark Mode 对比")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 优化前版本
    private var oldVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化前 - 存在的问题")
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding()
                
                // 国家选择区域 - 优化前
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.blue.opacity(0.15))
                                .frame(width: 32, height: 32)
                            
                            Image(systemName: "location.fill")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.blue)
                        }
                        
                        Text("美国快递")
                            .font(.subheadline.weight(.semibold))
                            .foregroundColor(.orange)
                        
                        Spacer()
                        
                        Image(systemName: "pencil")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.blue)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    
                    // 应用类型按钮组 - 优化前
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(["Just Photo", "Amazon Flex", "iMile", "GoFo"], id: \.self) { appName in
                                Button(action: {}) {
                                    Text(appName)
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(appName == "Just Photo" ? .white : .orange)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 12)
                                        .background(appName == "Just Photo" ? Color.orange : Color.orange.opacity(0.1))
                                        .cornerRadius(16)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 16)
                                                .stroke(Color.orange, lineWidth: appName == "Just Photo" ? 2 : 1)
                                        )
                                }
                            }
                        }
                        .padding(.horizontal, 1)
                    }
                    
                    // 选择照片区域 - 优化前
                    VStack(spacing: 12) {
                        Image(systemName: "photo.on.rectangle.angled")
                            .font(.largeTitle)
                            .foregroundColor(.blue)
                        
                        Text("选择照片")
                            .font(.headline)
                            .foregroundColor(.blue)
                        
                        Text("点击从照片库选择")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 120)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    )
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(16)
                .shadow(radius: 2)
                
                // 问题说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("❌ 存在的问题：")
                        .font(.subheadline.bold())
                        .foregroundColor(.red)
                    
                    Text("• 橙色背景在深色主题下过于突兀")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 蓝色元素对比度不足")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 按钮边框在暗色背景下不够明显")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 整体视觉层次不清晰")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 优化后版本
    private var newVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化后 - 改进效果")
                    .font(.headline)
                    .foregroundColor(.green)
                    .padding()
                
                // 国家选择区域 - 优化后
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.adaptivePrimaryIcon.opacity(0.15))
                                .frame(width: 32, height: 32)
                            
                            Image(systemName: "location.fill")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.adaptivePrimaryIcon)
                        }
                        
                        Text("美国快递")
                            .font(.subheadline.weight(.semibold))
                            .foregroundColor(.adaptivePrimaryText)
                        
                        Spacer()
                        
                        Image(systemName: "pencil")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.adaptivePrimaryIcon)
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
                    
                    // 应用类型按钮组 - 优化后
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(["Just Photo", "Amazon Flex", "iMile", "GoFo"], id: \.self) { appName in
                                Button(action: {}) {
                                    Text(appName)
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(appName == "Just Photo" ? .white : .adaptivePrimaryText)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 12)
                                        .background(appName == "Just Photo" ? Color.orange : Color.adaptiveSecondaryButton)
                                        .cornerRadius(16)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 16)
                                                .stroke(appName == "Just Photo" ? Color.orange : Color.adaptiveBorder, 
                                                       lineWidth: appName == "Just Photo" ? 2 : 1)
                                        )
                                }
                            }
                        }
                        .padding(.horizontal, 1)
                    }
                    
                    // 选择照片区域 - 优化后
                    VStack(spacing: 12) {
                        Image(systemName: "photo.on.rectangle.angled")
                            .font(.largeTitle)
                            .foregroundColor(.adaptivePrimaryIcon)
                        
                        Text("选择照片")
                            .font(.headline)
                            .foregroundColor(.adaptivePrimaryIcon)
                        
                        Text("点击从照片库选择")
                            .font(.caption)
                            .foregroundColor(.adaptiveSecondaryText)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 120)
                    .background(Color.adaptivePrimaryIcon.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.adaptivePrimaryIcon, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    )
                }
                .padding()
                .adaptiveCardStyle()
                
                // 改进说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("✅ 改进效果：")
                        .font(.subheadline.bold())
                        .foregroundColor(.green)
                    
                    Text("• 使用自适应颜色，Dark Mode 下更协调")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 增强了按钮和边框的对比度")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 文字可读性显著提升")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 整体视觉层次更加清晰")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 专为司机夜间使用优化")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveSuccess.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
        }
        .background(Color.adaptiveBackground)
    }
}

#Preview("Scanner Dark Mode - Light") {
    ScannerDarkModePreview()
        .preferredColorScheme(.light)
}

#Preview("Scanner Dark Mode - Dark") {
    ScannerDarkModePreview()
        .preferredColorScheme(.dark)
}
