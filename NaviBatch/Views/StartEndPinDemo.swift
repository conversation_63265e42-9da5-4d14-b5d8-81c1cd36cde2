import SwiftUI

// 演示起点和终点map pin的正确显示
struct StartEndPinDemo: View {
    var body: some View {
        VStack(spacing: 30) {
            Text("起点和终点Pin演示")
                .font(.title2)
                .padding()
            
            Text("起点和终点直接使用symbol图标，不需要矩形框")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // Pin类型展示
            VStack(spacing: 30) {
                // 起点Pin
                VStack(spacing: 12) {
                    Text("起点 (Start)")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    MarkerView(
                        number: 0, // start点的number通常是0，但不会显示
                        packageCount: 1,
                        color: .blue,
                        isAssignedToGroup: false,
                        groupNumber: nil,
                        shouldFade: false,
                        pointType: .start,
                        customText: nil,
                        hasCoordinateWarning: false // 🚨 演示用，无警告
                    )
                    
                    VStack(spacing: 4) {
                        Text("• 直接使用house.fill图标")
                            .font(.caption)
                        Text("• 蓝色图标，无矩形框")
                            .font(.caption)
                        Text("• 简洁的symbol设计")
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
                
                // 终点Pin
                VStack(spacing: 12) {
                    Text("终点 (End)")
                        .font(.headline)
                        .foregroundColor(.green)
                    
                    MarkerView(
                        number: -1, // end点的number通常是-1，但不会显示
                        packageCount: 1,
                        color: .green,
                        isAssignedToGroup: false,
                        groupNumber: nil,
                        shouldFade: false,
                        pointType: .end,
                        customText: nil,
                        hasCoordinateWarning: false // 🚨 演示用，无警告
                    )
                    
                    VStack(spacing: 4) {
                        Text("• 直接使用flag.fill图标")
                            .font(.caption)
                        Text("• 绿色图标，无矩形框")
                            .font(.caption)
                        Text("• 简洁的symbol设计")
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(12)
                
                // 普通配送点Pin
                VStack(spacing: 12) {
                    Text("配送点 (Waypoint)")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    HStack(spacing: 20) {
                        VStack(spacing: 8) {
                            MarkerView(
                                number: 1,
                                packageCount: 1,
                                color: .blue,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                pointType: .waypoint,
                                customText: nil,
                                hasCoordinateWarning: false // 🚨 演示用，无警告
                            )
                            Text("未优化")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        
                        VStack(spacing: 8) {
                            MarkerView(
                                number: 1,
                                packageCount: 1,
                                color: Color(hex: "B36AE2"),
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                pointType: .waypoint,
                                customText: nil,
                                hasCoordinateWarning: false // 🚨 演示用，无警告
                            )
                            Text("已优化")
                                .font(.caption)
                                .foregroundColor(Color(hex: "B36AE2"))
                        }
                    }
                    
                    VStack(spacing: 4) {
                        Text("• 显示数字编号")
                            .font(.caption)
                        Text("• 蓝色（未优化）或紫色（已优化）")
                            .font(.caption)
                        Text("• 根据优化状态显示不同编号")
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
            
            // 修复说明
            VStack(alignment: .leading, spacing: 8) {
                Text("修复内容：")
                    .font(.headline)
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("起点直接使用house.fill symbol，无矩形框")
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("终点直接使用flag.fill symbol，无矩形框")
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("简洁的symbol设计，更加直观")
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("配送点继续使用矩形+三角形设计")
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            Spacer()
        }
        .padding()
    }
}

// Color扩展已在 Extensions/Color+Hex.swift 中定义

#Preview("StartEndPinDemo") {
    StartEndPinDemo()
}
