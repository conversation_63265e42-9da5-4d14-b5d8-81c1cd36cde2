import SwiftUI
import SwiftData
import MapKit

struct DebugDataView: View {
    @Query private var routes: [Route]
    @Query private var points: [DeliveryPoint]
    @Query private var groups: [DeliveryGroup]
    @State private var exportText: String = ""
    @State private var showExportText: Bool = false
    @Environment(\.modelContext) private var modelContext

    var body: some View {
        NavigationView {
            VStack {
                Button("导出所有数据") {
                    exportAllData()
                }
                .padding()
                <PERSON><PERSON>("生成测试数据") {
                    generateTestData()
                }
                .padding(.bottom)
                <PERSON><PERSON>("在页面显示全部数据") {
                    exportText = getAllDataText()
                    showExportText = true
                }
                .padding(.bottom)
                debugListView
                .navigationTitle("SwiftData DebugView")
                if showExportText {
                    ScrollView {
                        Text(exportText)
                            .font(.system(size: 13, design: .monospaced))
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                    .padding()
                }
            }
        }
    }

    private var debugListView: some View {
        List {
            Section(header: Text("所有路线")) {
                ForEach(routes) { route in
                    VStack(alignment: .leading, spacing: 4) {
                        Text("名称: \(route.localizedName)").font(.headline)
                        Text("ID: \(route.id.uuidString)").font(.caption)
                        Text("创建时间: \(route.createdAt.formatted())").font(.caption2)
                        Text("点数: \(route.points.count)").font(.caption2)
                        if !route.points.isEmpty {
                            ForEach(route.points) { point in
                                Text("  - 点编号: \(point.sort_number), 排序号: \(point.sorted_number), 地址: \(point.primaryAddress)")
                                    .font(.caption2)
                            }
                        }
                    }
                    .padding(.vertical, 4)
                }
            }
            Section(header: Text("所有配送点")) {
                ForEach(points) { point in
                    VStack(alignment: .leading, spacing: 2) {
                        let isStart = point.isStartPoint ? "是" : "否"
                        let isEnd = point.isEndPoint ? "是" : "否"
                        let routeName = point.route?.name ?? "无"
                        Text("地址: \(point.primaryAddress)").font(.subheadline)
                        Text("ID: \(point.id.uuidString)").font(.caption2)
                        Text("编号: \(point.sort_number), 排序号: \(point.sorted_number)").font(.caption2)
                        Text("起点: \(isStart) 终点: \(isEnd)").font(.caption2)
                        Text("坐标: (\(point.coordinate.latitude), \(point.coordinate.longitude))").font(.caption2)
                        Text("所属路线: \(routeName)").font(.caption2)
                    }
                    .padding(.vertical, 2)
                }
            }
            Section(header: Text("所有分组")) {
                ForEach(groups as [DeliveryGroup], id: \.id) { group in
                    VStack(alignment: .leading, spacing: 2) {
                        Text("分组: \(group.name)").font(.subheadline)
                        Text("组号: \(group.groupNumber)").font(.caption2)
                        Text("ID: \(group.id.uuidString)").font(.caption2)
                        Text("点数: \(group.points.count)").font(.caption2)
                        if !group.points.isEmpty {
                            ForEach(group.points as [DeliveryPoint], id: \.id) { point in
                                Text("  - 点编号: \(point.sort_number), 排序号: \(point.sorted_number), 地址: \(point.primaryAddress)")
                                    .font(.caption2)
                            }
                        }
                    }
                    .padding(.vertical, 2)
                }
            }
        }
    }

    private func exportAllData() {
        var export = "\n===== SwiftData 全部数据导出 =====\n"
        export += "\n【所有路线】\n"
        for route in routes {
            export += "路线: \(route.name), ID: \(route.id.uuidString), 创建时间: \(route.createdAt.formatted()), 点数: \(route.points.count)\n"
            for point in route.points {
                export += "  - 点编号: \(point.sort_number), 排序号: \(point.sorted_number), 地址: \(point.primaryAddress)\n"
            }
        }
        export += "\n【所有配送点】\n"
        for point in points {
            let isStart = point.isStartPoint ? "是" : "否"
            let isEnd = point.isEndPoint ? "是" : "否"
            let routeName = point.route?.name ?? "无"
            export += "配送点: \(point.primaryAddress), 编号: \(point.sort_number), 排序号: \(point.sorted_number), 起点: \(isStart), 终点: \(isEnd), 坐标: (\(point.coordinate.latitude), \(point.coordinate.longitude)), 所属路线: \(routeName)\n"
        }
        export += "\n【所有分组】\n"
        for group in groups {
            export += "分组: \(group.name), 组号: \(group.groupNumber), ID: \(group.id.uuidString), 点数: \(group.points.count)\n"
            for point in group.points {
                export += "  - 点编号: \(point.sort_number), 排序号: \(point.sorted_number), 地址: \(point.primaryAddress)\n"
            }
        }
        export += "===== 导出结束 =====\n"
        print(export)
    }

    private func generateTestData() {
        let context = modelContext
        // 创建测试路线
        let route = Route(name: "测试路线")
        context.insert(route)

        // 创建测试配送点
        let addresses = [
            "1 Kinnoull Grove, Glen Waverley, VIC, Australia",
            "2 Montclair Av, Glen Waverley, VIC, Australia",
            "1 Coleman Pde, Glen Waverley, VIC, Australia"
        ]
        var points: [DeliveryPoint] = []
        for (i, addr) in addresses.enumerated() {
            let coordinate = CLLocationCoordinate2D(latitude: -37.88 + Double(i)*0.001, longitude: 145.16 + Double(i)*0.001)
            let point = DeliveryPoint(
                sort_number: i,
                streetName: addr,
                coordinate: coordinate,
                isStartPoint: i == 0,
                isEndPoint: i == addresses.count-1
            )
            context.insert(point)
            route.addPoint(point)
            points.append(point)
        }

        // 创建测试分组
        let group = DeliveryGroup(name: "测试分组", points: points, groupNumber: 1)
        context.insert(group)

        // 保存
        do {
            try context.save()
        } catch {
            print("保存测试数据失败: \(error)")
        }
    }

    private func getAllDataText() -> String {
        var export = "\n===== SwiftData 全部数据导出 =====\n"
        export += "\n【所有路线】\n"
        for route in routes {
            export += "路线: \(route.name), ID: \(route.id.uuidString), 创建时间: \(route.createdAt.formatted()), 点数: \(route.points.count)\n"
            for point in route.points {
                export += "  - 点编号: \(point.sort_number), 排序号: \(point.sorted_number), 地址: \(point.primaryAddress)\n"
            }
        }
        export += "\n【所有配送点】\n"
        for point in points {
            let isStart = point.isStartPoint ? "是" : "否"
            let isEnd = point.isEndPoint ? "是" : "否"
            let routeName = point.route?.name ?? "无"
            export += "配送点: \(point.primaryAddress), 编号: \(point.sort_number), 排序号: \(point.sorted_number), 起点: \(isStart), 终点: \(isEnd), 坐标: (\(point.coordinate.latitude), \(point.coordinate.longitude)), 所属路线: \(routeName)\n"
        }
        export += "\n【所有分组】\n"
        for group in groups {
            export += "分组: \(group.name), 组号: \(group.groupNumber), ID: \(group.id.uuidString), 点数: \(group.points.count)\n"
            for point in group.points {
                export += "  - 点编号: \(point.sort_number), 排序号: \(point.sorted_number), 地址: \(point.primaryAddress)\n"
            }
        }
        export += "===== 导出结束 =====\n"
        return export
    }
}

#Preview("DebugDataView") {
    DebugDataView()
}