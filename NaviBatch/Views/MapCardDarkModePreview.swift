import SwiftUI
import CoreLocation

/// 地图地址卡片 Dark Mode 优化对比预览
/// 展示地址卡片在 Dark Mode 下的优化效果
struct MapCardDarkModePreview: View {
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签切换器
                Picker("版本", selection: $selectedTab) {
                    Text("优化前").tag(0)
                    Text("优化后").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    // 优化前版本
                    oldVersionView
                        .tag(0)
                    
                    // 优化后版本
                    newVersionView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("地址卡片 Dark Mode 对比")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 优化前版本
    private var oldVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化前 - 存在的问题")
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding()
                
                // 地址卡片 - 优化前
                VStack(spacing: 0) {
                    // 地址信息部分
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(spacing: 8) {
                            // 编号圆圈 - 优化前（固定紫色）
                            Text("3")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .frame(width: 36, height: 36)
                                .background(Color(hex: "B36AE2"))
                                .cornerRadius(6)
                            
                            // 地址信息
                            VStack(alignment: .leading, spacing: 2) {
                                // GoFo 标签 - 优化前（刺眼的蓝色）
                                HStack(spacing: 4) {
                                    Image(systemName: "number.circle.fill")
                                        .font(.caption)
                                    Text("GoFo: 4")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                }
                                .foregroundColor(.blue)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(4)
                                
                                // 地址文字 - 优化前（可读性差）
                                Text("4265 Maddie Circle")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                
                                Text("95209")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    
                    // 底部按钮区域 - 优化前
                    HStack(spacing: 0) {
                        // 导航按钮
                        Button(action: {}) {
                            HStack {
                                Image(systemName: "location.fill")
                                Text("导航")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .foregroundColor(.white)
                        }
                        
                        // 查看实景图按钮
                        Button(action: {}) {
                            HStack {
                                Image(systemName: "binoculars.fill")
                                Text("查看实景图")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .foregroundColor(.white)
                        }
                    }
                }
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                .padding(.horizontal)
                
                // 问题说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("❌ 存在的问题：")
                        .font(.subheadline.bold())
                        .foregroundColor(.red)
                    
                    Text("• GoFo 标签在 Dark Mode 下不够醒目")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 蓝色按钮对比度不足")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 地址文字在暗色背景下可读性差")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 卡片背景与环境不协调")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 优化后版本
    private var newVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化后 - 改进效果")
                    .font(.headline)
                    .foregroundColor(.green)
                    .padding()
                
                // 地址卡片 - 优化后
                VStack(spacing: 0) {
                    // 地址信息部分
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(spacing: 8) {
                            // 编号圆圈 - 优化后（自适应颜色）
                            Text("3")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .frame(width: 36, height: 36)
                                .background(Color.adaptivePrimaryIcon)
                                .cornerRadius(6)
                            
                            // 地址信息
                            VStack(alignment: .leading, spacing: 2) {
                                // GoFo 标签 - 优化后（自适应颜色）
                                Text("GoFo: 4")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.adaptiveGoFo)
                                    .cornerRadius(6)
                                
                                // 地址文字 - 优化后（自适应颜色）
                                Text("4265 Maddie Circle")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.adaptivePrimaryText)
                                
                                Text("95209")
                                    .font(.subheadline)
                                    .foregroundColor(.adaptiveSecondaryText)
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    
                    // 底部按钮区域 - 优化后
                    HStack(spacing: 0) {
                        // 导航按钮
                        Button(action: {}) {
                            HStack {
                                Image(systemName: "location.fill")
                                Text("导航")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.adaptivePrimaryIcon)
                            .foregroundColor(.white)
                        }
                        
                        // 查看实景图按钮
                        Button(action: {}) {
                            HStack {
                                Image(systemName: "binoculars.fill")
                                Text("查看实景图")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.adaptivePrimaryIcon)
                            .foregroundColor(.white)
                        }
                    }
                }
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                .padding(.horizontal)
                
                // 改进说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("✅ 改进效果：")
                        .font(.subheadline.bold())
                        .foregroundColor(.green)
                    
                    Text("• GoFo 标签使用深金色，Dark Mode 下更协调")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 按钮使用自适应蓝色，对比度更好")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 地址文字使用自适应颜色，可读性提升")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 卡片背景自适应，与环境协调")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 编号圆圈根据状态显示不同颜色")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveSuccess.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 不同状态示例
                VStack(alignment: .leading, spacing: 12) {
                    Text("不同状态示例：")
                        .font(.subheadline.bold())
                        .foregroundColor(.adaptivePrimaryText)
                    
                    // 已完成状态
                    statusExampleCard(
                        number: "1",
                        appTag: "GoFo: 1",
                        address: "123 Main Street",
                        subtitle: "已完成",
                        numberColor: .adaptiveSuccess,
                        tagColor: .adaptiveGoFo
                    )
                    
                    // 失败状态
                    statusExampleCard(
                        number: "2",
                        appTag: "Amazon: 2",
                        address: "456 Oak Avenue",
                        subtitle: "派送失败",
                        numberColor: .adaptiveError,
                        tagColor: .adaptiveAmazonFlex
                    )
                    
                    // 警告状态
                    statusExampleCard(
                        number: "3",
                        appTag: "iMile: 3",
                        address: "789 Pine Road",
                        subtitle: "地址需确认",
                        numberColor: .adaptiveWarning,
                        tagColor: .adaptivePrimaryIcon
                    )
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding()
        }
        .background(Color.adaptiveBackground)
    }
    
    // MARK: - 辅助方法
    private func statusExampleCard(
        number: String,
        appTag: String,
        address: String,
        subtitle: String,
        numberColor: Color,
        tagColor: Color
    ) -> some View {
        HStack(spacing: 8) {
            // 编号圆圈
            Text(number)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(numberColor)
                .cornerRadius(6)
            
            // 地址信息
            VStack(alignment: .leading, spacing: 2) {
                Text(appTag)
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(tagColor)
                    .cornerRadius(4)
                
                Text(address)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(.adaptivePrimaryText)
                
                Text(subtitle)
                    .font(.system(size: 10))
                    .foregroundColor(.adaptiveSecondaryText)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

#Preview("Map Card Dark Mode - Light") {
    MapCardDarkModePreview()
        .preferredColorScheme(.light)
}

#Preview("Map Card Dark Mode - Dark") {
    MapCardDarkModePreview()
        .preferredColorScheme(.dark)
}
