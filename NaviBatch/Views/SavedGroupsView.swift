import SwiftUI
import SwiftData
import os.log
import Foundation

struct SavedGroupsView: View {
    // 使用共享容器的ModelContext
    @State private var container = getPersistentContainer()
    @State private var modelContext: ModelContext
    @State private var routes: [Route] = []
    @State private var isEditing = false
    @State private var editingRoute: Route? = nil
    @State private var editedName = ""
    @State private var routeToDelete: Route? = nil
    @State private var showDeleteAlert = false
    @State private var isLoading = true
    @State private var showAllRoutes = false
    @State private var allRoutes: [Route] = []
    @State private var showDeleteAllAlert = false
    @State private var showManageRoutesSheet = false
    @Environment(\.dismiss) private var dismiss

    @State private var selectedRouteForNavigation: Route? = nil

    // 初始化方法
    init() {
        // 获取共享容器
        let sharedContainer = getPersistentContainer()
        _container = State(initialValue: sharedContainer)
        _modelContext = State(initialValue: sharedContainer.mainContext)
    }

    var body: some View {
        mainContentView
            .sheet(isPresented: $isEditing) {
            if let route = editingRoute {
                NavigationStack {
                    Form {
                        Section(header: Text("route_info".localized)) {
                            TextField("route_name".localized, text: $editedName)
                        }

                        Section(header: Text("route_addresses".localized)) {
                            if route.points.isEmpty {
                                Text("no_addresses_in_route".localized)
                                    .foregroundColor(.secondary)
                            } else {
                                ForEach(route.points) { point in
                                    HStack {
                                        Text("\(point.sort_number)")
                                            .font(.caption)
                                            .foregroundColor(.white)
                                            .frame(width: 24, height: 24)
                                            .background(Circle().fill(Color.blue))

                                        Text(point.primaryAddress)
                                            .lineLimit(1)
                                    }
                                }
                            }
                        }
                    }
                    .navigationTitle("编辑路线")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .cancellationAction) {
                            Button("取消") {
                                isEditing = false
                            }
                        }
                        ToolbarItem(placement: .confirmationAction) {
                            Button("保存") {
                                saveRouteChanges()
                            }
                        }
                    }
                }
                .presentationDetents([.medium])
            }
        }
        .alert("confirm_delete".localized, isPresented: $showDeleteAlert) {
            Button("cancel".localized, role: .cancel) {}
            Button("delete".localized, role: .destructive) {
                if let route = routeToDelete, routes.count > 1 {
                    deleteRoute(route)
                } else {
                    // 如果尝试删除最后一条路线，显示提示
                    showDeleteAlert = false
                }
            }
        } message: {
            if routes.count <= 1 {
                Text("must_keep_one_route".localized)
            } else {
                Text(String(format: "confirm_delete_route".localized, routeToDelete?.name ?? ""))
            }
        }
        .alert("confirm_delete_all_routes".localized, isPresented: $showDeleteAllAlert) {
            Button("cancel".localized, role: .cancel) {}
            Button("delete_all".localized, role: .destructive) {
                deleteAllRoutes()
            }
        } message: {
            Text("confirm_delete_all_routes_message".localized)
        }
        .sheet(isPresented: $showManageRoutesSheet) {
            NavigationStack {
                List {
                    Section(header: Text("all_routes".localized)) {
                        ForEach(allRoutes) { route in
                            HStack {
                                VStack(alignment: .leading) {
                                    Text(route.localizedName)
                                        .font(.headline)
                                    Text("ID: \(route.id.uuidString.prefix(8))")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(String(format: "address_count_format_simple".localized, route.points.count))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                Button(action: {
                                    routeToDelete = route
                                    showDeleteAlert = true
                                    showManageRoutesSheet = false
                                }) {
                                    Image(systemName: "trash")
                                        .foregroundColor(.red)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }

                    Section {
                        Button(action: {
                            showDeleteAllAlert = true
                        }) {
                            HStack {
                                Spacer()
                                Text("delete_all_routes".localized)
                                    .foregroundColor(.red)
                                    .bold()
                                Spacer()
                            }
                        }
                    }
                }
                .navigationTitle("manage_routes".localized)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .cancellationAction) {
                        Button("关闭") {
                            showManageRoutesSheet = false
                        }
                    }
                    ToolbarItem(placement: .confirmationAction) {
                        Button("刷新") {
                            loadAllRoutes()
                        }
                    }
                }
                .onAppear {
                    loadAllRoutes()
                }
            }
            .presentationDetents([.medium, .large])
        }
    }

    // Extracted main content and primary modifiers
    private var mainContentView: some View {
        routesListContent
        .task {
            // 显示加载状态
            isLoading = true

            // 确保使用共享容器
            let sharedContainer = getPersistentContainer()
            container = sharedContainer
            modelContext = sharedContainer.mainContext

            // 同步路线计数器与实际路线数量
            await Route.syncRouteCount()

            // 检查当前路线状态
            do {
                let descriptor = FetchDescriptor<Route>()
                let fetchedRoutes = try modelContext.fetch(descriptor)
                routes = fetchedRoutes

                // 如果没有路线，创建一个新路线
                if fetchedRoutes.isEmpty {
                    createNewRoute()
                }
            } catch {
                logError("SavedGroupsView - 查询路线失败: \(error.localizedDescription)")
            }

            // 加载所有路线以便管理
            loadAllRoutes()

            isLoading = false
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RouteDeleted"))) { _ in
            // 当路线被删除时刷新列表
            refreshRoutes()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RouteUpdated"))) { _ in
            // 当路线被更新时刷新列表
            refreshRoutes()
        }
        .navigationTitle("saved_routes".localized)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            // 右侧工具栏 - 只保留添加新路线按钮
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: createNewRoute) {
                    Image(systemName: "plus")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)
                }
            }
        }
    }

    // Extracted List content to a computed property
    private var routesListContent: some View {
        VStack(spacing: 0) {
            // 内容区域 - 直接开始显示内容，不需要额外的标题
            if isLoading {
                // 加载状态
                VStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(1.2)
                        .padding()
                    Text("loading".localized)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else if routes.isEmpty {
                // 空状态
                ContentUnavailableView {
                    Label("no_saved_routes".localized, systemImage: "map")
                } description: {
                    Text("no_saved_routes_description".localized)
                } actions: {
                    Button(action: createNewRoute) {
                        Text("create_new_route".localized)
                    }
                    .buttonStyle(.borderedProminent)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 路线列表 - 使用List实现向左滑动删除功能
                List {
                    ForEach(routes) { route in
                        RouteRow(route: route)
                            .contentShape(Rectangle()) // 确保整个区域可点击
                            .onTapGesture {
                                // 获取共享视图模型
                                let viewModel = getSharedRouteViewModel()

                                // 设置当前路线并确保不会被覆盖
                                Task {
                                    do {
                                        // 重要：先确保路线ID有效，并从当前ModelContext中获取路线
                                        let routeId = route.id
                                        let descriptor = FetchDescriptor<Route>(predicate: #Predicate { $0.id == routeId })

                                        // 尝试从当前ModelContext获取路线
                                        if let validRoute = try modelContext.fetch(descriptor).first {
                                            print("SavedGroupsView - 已验证路线存在: '\(validRoute.name)', ID=\(validRoute.id.uuidString)")

                                            // 设置当前路线
                                            viewModel.modelContext = modelContext
                                            viewModel.currentRoute = validRoute
                                            viewModel.isRouteNewlyCreated = false

                                            // 强制刷新配送点
                                            await viewModel.setupDeliveryPoints()

                                            // 确保显示路线表单，因为用户选择了路线
                                            viewModel.showRouteSheet = true

                                            // 发送通知
                                            DispatchQueue.main.async {
                                                // 标记从SavedGroupsView导航，确保底部表单显示
                                                UserDefaults.standard.set(true, forKey: "NavigatingFromSavedGroups")

                                                // 发送路线变更通知
                                                NotificationCenter.default.post(name: Notification.Name("SelectedRouteChanged"), object: validRoute.id.uuidString)

                                                // 记录日志
                                                print("SavedGroupsView - 已选择路线: '\(validRoute.name)', ID=\(validRoute.id.uuidString)")

                                                // 发送特殊通知，确保导航到RouteView
                                                NotificationCenter.default.post(name: Notification.Name("NavigateToRouteView"), object: nil)

                                                // 发送一个额外的通知，用于关闭菜单视图
                                                NotificationCenter.default.post(name: Notification.Name("CloseMenuAndShowRoute"), object: nil)

                                                // 使用dismiss()关闭当前视图
                                                dismiss()

                                                // 添加额外的日志，帮助调试
                                                print("SavedGroupsView - 已发送导航通知并关闭当前视图")
                                            }
                                        } else {
                                            print("[ERROR] SavedGroupsView - 无法在当前ModelContext中找到路线: ID=\(routeId.uuidString)")

                                            // 尝试创建新路线作为备选方案
                                            let newRoute = Route(name: "新路线 \(Date().formatted(.dateTime))")
                                            modelContext.insert(newRoute)
                                            try modelContext.save()

                                            print("[INFO] SavedGroupsView - 已创建新路线作为备选: '\(newRoute.name)', ID=\(newRoute.id.uuidString)")

                                            // 设置为当前路线
                                            viewModel.modelContext = modelContext
                                            viewModel.currentRoute = newRoute
                                            viewModel.isRouteNewlyCreated = true

                                            // 强制刷新配送点
                                            await viewModel.setupDeliveryPoints()

                                            // 关闭当前视图
                                            DispatchQueue.main.async {
                                                dismiss()
                                            }
                                        }
                                    } catch {
                                        print("[ERROR] SavedGroupsView - 验证路线时出错: \(error.localizedDescription)")

                                        // 出错时也关闭当前视图
                                        DispatchQueue.main.async {
                                            dismiss()
                                        }
                                    }
                                }
                            }
                            .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                if routes.count > 1 { // 只有当路线数量大于1时才允许删除
                                    Button(role: .destructive) {
                                        routeToDelete = route
                                        showDeleteAlert = true
                                    } label: {
                                        Label("删除", systemImage: "trash")
                                    }
                                }
                            }
                            .listRowBackground(Color(.systemBackground))
                            .listRowSeparator(.hidden)
                            .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                    }
                }
                .listStyle(PlainListStyle())
                .background(Color(.systemGroupedBackground))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))

        // 移除浮动的新建按钮
    }

    // MARK: - 辅助函数

    private func refreshRoutes() {
        isLoading = true

        // 使用任务异步执行同步路线计数器
        Task { @MainActor in
            // 同步路线计数器与实际路线数量
            await Route.syncRouteCount()

            // 直接查询路线数据
            do {
                let descriptor = FetchDescriptor<Route>()
                let fetchedRoutes = try modelContext.fetch(descriptor)
                self.routes = fetchedRoutes

                // 如果没有路线，创建一个新路线
                if fetchedRoutes.isEmpty {
                    createNewRoute()
                }
            } catch {
                logError("SavedGroupsView - 查询路线失败: \(error.localizedDescription)")
            }

            // 完成刷新
            self.isLoading = false
        }
    }

    private func createNewRoute() {
        let newRoute = Route(name: Route.defaultName())
        modelContext.insert(newRoute)

        do {
            try modelContext.save()
            refreshRoutes()
        } catch {
            logError("SavedGroupsView - 创建路线失败: \(error.localizedDescription)")
        }
    }

    private func saveRouteChanges() {
        if let route = editingRoute {
            // 保存旧名称用于日志记录
            let _ = route.name
            route.name = editedName

            do {
                try modelContext.save()
                refreshRoutes()
            } catch {
                logError("SavedGroupsView - 更新路线名称失败: \(error.localizedDescription)")
            }
        }
        isEditing = false
    }

    private func deleteRoute(_ route: Route) {
        do {
            // 确保不是最后一条路线
            let fetchRequest = FetchDescriptor<Route>()
            let allRoutes = try modelContext.fetch(fetchRequest)

            if allRoutes.count <= 1 {
                // 不应该发生，因为UI已经阻止了删除最后一条路线的操作
                print("错误：不能删除最后一条路线")
                return
            }

            // 删除路线
            modelContext.delete(route)

            // 保存更改
            try modelContext.save()

            // 刷新路线列表
            refreshRoutes()

            // 获取更新后的路线列表
            let updatedRoutes = try modelContext.fetch(fetchRequest)

            // 如果还有路线，切换到第一条路线
            if !updatedRoutes.isEmpty {
                Task { @MainActor in
                    let viewModel = getSharedRouteViewModel()
                    viewModel.currentRoute = updatedRoutes[0]
                    viewModel.isRouteNewlyCreated = false
                    await viewModel.setupDeliveryPoints()
                }
            }

        } catch {
            print("删除路线时出错：\(error)")
        }
    }

    /// 打印数据库内容
    private func printDatabase() {
        Task { @MainActor in
            // 使用数据库调试器打印所有内容
            await DatabaseDebugger.printAllRoutes()
        }
    }

    /// 加载所有路线
    private func loadAllRoutes() {
        do {
            let descriptor = FetchDescriptor<Route>()
            let fetchedRoutes = try modelContext.fetch(descriptor)
            DispatchQueue.main.async { [self] in
                self.allRoutes = fetchedRoutes
            }
        } catch {
            logError("SavedGroupsView - 加载所有路线失败: \(error.localizedDescription)")
            DispatchQueue.main.async { [self] in
                self.allRoutes = []
            }
        }
    }

    /// 删除所有路线
    private func deleteAllRoutes() {
        do {
            let descriptor = FetchDescriptor<Route>()
            let fetchedRoutes = try modelContext.fetch(descriptor)

            for route in fetchedRoutes {
                modelContext.delete(route)
            }

            try modelContext.save()

            // 刷新路线数据
            DispatchQueue.main.async { [self] in
                self.routes = []
                self.allRoutes = []
                self.showManageRoutesSheet = false
            }

            refreshRoutes()
        } catch {
            logError("SavedGroupsView - 删除所有路线失败: \(error.localizedDescription)")
        }
    }

    @MainActor
    private func setCurrentRouteAndDismiss(_ route: Route) async {
        // 获取共享RouteViewModel实例
        let viewModel = getSharedRouteViewModel()

        // 设置当前路线
        viewModel.modelContext = modelContext
        viewModel.currentRoute = route
        viewModel.isRouteNewlyCreated = false

        // 刷新视图模型数据
        await viewModel.setupDeliveryPoints()
        viewModel.showRouteSheet = true
        viewModel.objectWillChange.send()

        // 1. 标记从SavedGroupsView导航
        UserDefaults.standard.set(true, forKey: "NavigatingFromSavedGroups")

        // 2. 发送通知
        NotificationCenter.default.post(name: Notification.Name("SelectedRouteChanged"), object: route.id.uuidString)

        // 3. 关闭当前视图栈
        dismiss()

        // 4. 添加特殊的导航标记通知
        NotificationCenter.default.post(name: Notification.Name("NavigateToRouteView"), object: nil)

        // 5. 发送一个额外的通知，用于关闭菜单视图
        NotificationCenter.default.post(name: Notification.Name("CloseMenuAndShowRoute"), object: nil)

        // 5. 添加日志
        print("SavedGroupsView - setCurrentRouteAndDismiss - 已发送关闭菜单和显示路线通知")
    }

    @MainActor
    private func getSharedRouteViewModel() -> RouteViewModel {
        return RouteViewModel.shared
    }
}

// 路线行组件
fileprivate struct RouteRow: View {
    let route: Route
    @StateObject private var viewModel = RouteViewModel.shared
    @State private var showingNavigationConfirm = false

    // 格式化日期 - 统一使用一种格式
    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    // 获取路线中的有效地址数量
    private var validAddressCount: Int {
        return route.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
    }

    // 导航到路线中的所有点
    private func navigateToRoute() {
        print("[DEBUG] SavedGroupsView - 开始执行Apple Maps导航操作")

        // 创建一个临时分组，包含路线中的所有点
        let tempGroup = DeliveryGroup(
            name: "temp_navigation_group".localized,
            points: route.points,
            groupNumber: 0
        )

        // 调用导航方法
        viewModel.navigateToGroup(tempGroup) { success, message in
            print("[DEBUG] SavedGroupsView - 导航回调: success=\(success), message=\(message ?? "无")")
            // 移除Google Maps网页版相关逻辑，现在只使用Apple Maps
        }
    }

    var body: some View {
        HStack(spacing: 16) {
            // 左侧图标
            ZStack {
                Circle()
                    .fill(Color.blue.opacity(0.1))
                    .frame(width: 44, height: 44)

                Image(systemName: "map")
                    .font(.system(size: 18))
                    .foregroundColor(.blue)
            }
            .padding(.leading, 4)

            // 路线信息
            VStack(alignment: .leading, spacing: 4) {
                Text(route.localizedName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                    .lineLimit(1)

                HStack(spacing: 8) {
                    // 日期
                    Image(systemName: "calendar")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)

                    Text(formattedDate(route.createdAt))
                        .font(.system(size: 13))
                        .foregroundColor(.secondary)


                    // 地址数量
                    Image(systemName: "mappin")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)

                    Text(String(format: "addresses_count".localized, validAddressCount))
                        .font(.system(size: 13))
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // 导航按钮
            if !route.points.isEmpty {
                Button(action: {
                    showingNavigationConfirm = true
                }) {
                    Image(systemName: "location.fill")
                        .font(.system(size: 18))
                        .foregroundColor(.blue)
                        .padding(8)
                        .background(Circle().fill(Color.blue.opacity(0.1)))
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.trailing, 4)
                .alert("navigate_to_all_points".localized, isPresented: $showingNavigationConfirm) {
                    Button("cancel".localized, role: .cancel) {}
                    Button("navigate".localized) {
                        navigateToRoute()
                    }
                } message: {
                    Text(String(format: "confirm_navigate_to_route".localized, route.name))
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct SavedGroupsPreview: PreviewProvider {
    static var previews: some View {
        // 使用应用程序的共享ModelContainer
        let _ = getPersistentContainer()  // 调用函数以确保容器初始化

        return NavigationStack {
            SavedGroupsView()
        }
    }
}
