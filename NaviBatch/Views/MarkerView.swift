import SwiftUI

// 高性能SF Symbols地图Pin标记 - 矩形+三角形组合（已移至MarkerView中直接实现）
// 保留此结构体以备将来使用或其他组件调用
struct CompactPinMarker: View {
    let size: CGFloat
    let color: Color
    let isSelected: Bool

    var body: some View {
        VStack(spacing: 0) {
            ZStack {
                // 外圈 - 选中时显示
                if isSelected {
                    Image(systemName: "rectangle")
                        .font(.system(size: size + 4, weight: .medium))
                        .foregroundStyle(color.opacity(0.3))
                }

                // 主体矩形 - 空心效果
                Image(systemName: "rectangle")
                    .font(.system(size: size, weight: .medium))
                    .foregroundStyle(color)
            }

            // 向下的实心三角形指针
            Image(systemName: "arrowtriangle.down.fill")
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundStyle(color)
                .offset(y: -2) // 稍微向上偏移，让三角形与矩形更好地连接
        }
        .background(Color.clear)
    }
}

// 双色分割旗帜形状 - 用于表示同时是起点和终点的地址
struct SplitBubbleMarkerShape: View {
    let size: CGFloat
    let leftColor: Color  // 左侧颜色（起点）
    let rightColor: Color // 右侧颜色（终点）

    var body: some View {
        ZStack {
            // 旗帜形状背景
            Path { path in
                let radius = size / 2
                let center = CGPoint(x: radius, y: radius)

                // 绘制圆形部分
                path.addArc(center: center, radius: radius, startAngle: .zero, endAngle: .degrees(360), clockwise: false)

                // 绘制底部指针
                let pointerHeight = size * 0.4
                let pointerWidth = size * 0.3

                path.move(to: CGPoint(x: center.x - pointerWidth/2, y: size))
                path.addLine(to: CGPoint(x: center.x, y: size + pointerHeight))
                path.addLine(to: CGPoint(x: center.x + pointerWidth/2, y: size))
                path.closeSubpath()
            }
            .fill(Color.white)
            .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 0.5)

            // 左半部分边框（起点）
            Circle()
                .trim(from: 0.25, to: 0.75) // 左半圆
                .stroke(leftColor, lineWidth: 1.5)
                .frame(width: size, height: size)
                .rotationEffect(.degrees(180))

            // 右半部分边框（终点）
            Circle()
                .trim(from: 0.25, to: 0.75) // 右半圆
                .stroke(rightColor, lineWidth: 1.5)
                .frame(width: size, height: size)

            // 底部指针边框 - 渐变色
            Path { path in
                let radius = size / 2
                let center = CGPoint(x: radius, y: radius)
                let pointerHeight = size * 0.4
                let pointerWidth = size * 0.3

                path.move(to: CGPoint(x: center.x - pointerWidth/2, y: size))
                path.addLine(to: CGPoint(x: center.x, y: size + pointerHeight))
                path.addLine(to: CGPoint(x: center.x + pointerWidth/2, y: size))
            }
            .stroke(
                LinearGradient(
                    gradient: Gradient(colors: [leftColor, rightColor]),
                    startPoint: .leading,
                    endPoint: .trailing
                ),
                lineWidth: 1.5
            )

            // 中间分割线
            Rectangle()
                .fill(Color.gray.opacity(0.4))
                .frame(width: 1, height: size * 0.7)
        }
        .frame(width: size, height: size * 1.4)
        .background(Color.clear)
    }
}

// 点类型枚举 - 用于区分不同类型的地址点
enum PointType {
    case start    // 起点
    case waypoint // 途经点
    case end      // 终点
    case startEnd // 同时是起点和终点
}

// 标记渲染模式 - 用于选择不同的渲染性能策略
enum MarkerRenderMode {
    case rectangle  // 矩形+三角形模式（当前，兼容性好）
    case drop       // Drop符号模式（高性能，减少90%渲染消耗）
    case numbersRectangle // rectangle.fill图标模式（清晰简洁，可显示任意数字）
}

// 🚀 高性能Drop标记视图 - 使用单个SF Symbol大幅提升渲染性能
struct DropMarkerView: View {
    let number: Int
    let packageCount: Int
    let color: Color
    let isAssignedToGroup: Bool
    let groupNumber: Int?
    let shouldFade: Bool
    var pointType: PointType? = nil
    var isCompleted: Bool = false
    var isFailed: Bool = false
    var customText: String? = nil
    var hasCoordinateWarning: Bool = false

    // 使用缓存计算属性减少渲染期间的计算
    private var opacity: Double {
        shouldFade ? 0.5 : 1.0
    }

    // Drop标记尺寸 - 增大尺寸确保数字清晰可见
    private var markerSize: CGFloat {
        return 36  // 🔍 从28增加到36，增大28%
    }

    // 字体大小 - 根据标记大小自适应
    private var fontSize: CGFloat {
        return markerSize * 0.35 // 🎯 调整为0.35，确保数字完整显示在水滴内
    }

    // 标记颜色 - 根据状态确定
    private var markerColor: Color {
        if isCompleted {
            return Color.green
        } else if isFailed {
            return Color.red
        } else if hasCoordinateWarning {
            return Color.orange
        } else {
            return color
        }
    }

    // 显示的文本内容
    private var displayText: String {
        if isCompleted {
            return "✓"
        } else if isFailed {
            return "✗"
        } else if isAssignedToGroup, let groupNumber = groupNumber {
            return "G\(groupNumber)"
        } else if let customText = customText {
            return customText
        } else if number >= 0 {
            return "\(number)"
        } else {
            return "\(abs(number))"
        }
    }

    var body: some View {
        // 检查是否是start或end点，如果是则使用专用符号
        if let pointType = pointType, (pointType == .start || pointType == .end) {
            Group {
                if pointType == .start {
                    Image(systemName: "house.fill")
                        .font(.system(size: markerSize, weight: .medium))
                        .foregroundColor(markerColor)
                } else if pointType == .end {
                    Image(systemName: "flag.fill")
                        .font(.system(size: markerSize, weight: .medium))
                        .foregroundColor(markerColor)
                }
            }
            .opacity(opacity)
            .background(Color.clear)
            .drawingGroup(opaque: false) // 🚀 GPU硬件加速
            .id("drop-symbol-\(pointType)-\(number)")
        } else {
            // 其他点使用高性能drop设计 - 180度翻转，尖端朝下
            ZStack {
                // Drop轮廓符号 - 边框统一灰色，不填充
                Image(systemName: "drop")
                    .font(.system(size: markerSize, weight: .medium))
                    .foregroundStyle(.gray) // 🎯 边框统一灰色，更柔和
                    .rotationEffect(.degrees(180)) // 🔄 180度翻转，尖端朝下

                // 文本内容 - 分组标记使用黑色，黑色背景使用白色字体，其他保持逻辑颜色
                Text(displayText)
                    .font(.system(size: fontSize, weight: .bold))
                    .foregroundColor(isAssignedToGroup ? .black : (markerColor == .black ? .white : markerColor)) // 🎯 黑色背景使用白色字体
                    .offset(y: -markerSize * 0.08) // 🔍 调整偏移量适配更大的标记
            }
            .opacity(opacity)
            .background(Color.clear)
            .drawingGroup(opaque: false) // 🚀 GPU硬件加速
            .id("drop-\(number)-\(isCompleted)-\(isFailed)")
        }
    }
}

// 🎯 Rectangle.Fill标记视图 - 使用清晰的rectangle.fill图标，可显示任意数字
struct NumbersRectangleMarkerView: View {
    let number: Int
    let packageCount: Int
    let color: Color
    let isAssignedToGroup: Bool
    let groupNumber: Int?
    let shouldFade: Bool
    var pointType: PointType? = nil
    var isCompleted: Bool = false
    var isFailed: Bool = false
    var customText: String? = nil
    var hasCoordinateWarning: Bool = false

    // 使用缓存计算属性减少渲染期间的计算
    private var opacity: Double {
        shouldFade ? 0.5 : 1.0
    }

    // 标记尺寸
    private var markerSize: CGFloat {
        return 32
    }

    // 字体大小
    private var fontSize: CGFloat {
        return markerSize * 0.4
    }

    // 显示文本
    private var displayText: String {
        if isCompleted {
            return "✓"
        } else if isFailed {
            return "✗"
        } else if isAssignedToGroup, let groupNumber = groupNumber {
            return "G\(groupNumber)"
        } else if let customText = customText {
            return customText
        } else if number >= 0 {
            return "\(number)"
        } else {
            return "?"
        }
    }

    // 标记颜色
    private var markerColor: Color {
        if let pointType = pointType {
            switch pointType {
            case .start:
                return .blue
            case .end:
                return .green
            case .startEnd:
                return .orange
            case .waypoint:
                return isAssignedToGroup ? .yellow : color
            }
        }
        return color
    }

    var body: some View {
        ZStack {
            // 背景矩形 - 根据是否为第三方号码选择不同图标
            Image(systemName: isThirdPartyNumber ? "square.badge.plus" : "rectangle.fill")
                .font(.system(size: markerSize, weight: .medium))
                .foregroundStyle(markerColor)

            // 数字文本 - 可以显示任意数字或文本
            Text(displayText)
                .font(.system(size: fontSize, weight: .bold))
                .foregroundColor(markerColor == .black ? .white : .white)
        }
        .opacity(opacity)
        .background(Color.clear)
        .drawingGroup(opaque: false)
        .id("rectangle-\(number)-\(isCompleted)-\(isFailed)")
    }

    // 判断是否为第三方号码
    private var isThirdPartyNumber: Bool {
        return customText != nil && !customText!.isEmpty
    }
}

// 🎯 智能标记视图组件 - 根据性能配置自动选择最优渲染模式
struct MarkerView: View {
    let number: Int
    let packageCount: Int
    let color: Color
    let isAssignedToGroup: Bool
    let groupNumber: Int?
    let shouldFade: Bool
    var pointType: PointType? = nil // 点类型参数，默认为nil
    var isCompleted: Bool = false // 新增：标记是否已完成配送
    var isFailed: Bool = false // 新增：标记是否配送失败
    var customText: String? = nil // 新增：自定义显示文本（用于第三方排序号）
    var hasCoordinateWarning: Bool = false // 新增：标记是否有坐标警告

    // 渲染模式 - 可以手动指定，否则使用全局配置
    var renderMode: MarkerRenderMode? = nil

    // 使用缓存计算属性减少渲染期间的计算
    private var opacity: Double {
        shouldFade ? 0.5 : 1.0
    }

    // Pin标记尺寸 - 统一更大的尺寸，确保所有标记点大小一致
    private var markerSize: CGFloat {
        return 28 // 所有标记点使用统一的更大尺寸
    }

    // Pin标记字体大小 - 适配统一的更大pin尺寸
    private var fontSize: CGFloat {
        if number < 10 {
            return 14 // 一位数使用较大字体
        } else if number < 100 {
            return 13 // 两位数稍小字体
        } else if number < 1000 {
            return 11 // 三位数较小字体，确保能完整显示
        } else {
            return 10 // 四位数及以上使用最小字体
        }
    }

    // 根据点类型获取颜色
    private var markerColor: Color {
        // 如果是已完成状态，始终显示为绿色
        if isCompleted {
            return Color.green // 已完成的点显示为绿色
        }

        // 如果是失败状态，始终显示为红色
        if isFailed {
            return Color.red // 失败的点显示为红色
        }

        // 🚨 如果有坐标警告，显示为橙色（优先级高于分组状态）
        if hasCoordinateWarning {
            return Color.orange // 有坐标问题的点显示为橙色
        }

        // 如果已分配到组，显示为灰色
        if isAssignedToGroup {
            return Color.gray // 已分配到组的点显示为灰色
        }

        // 如果指定了点类型，根据类型返回颜色
        if let pointType = pointType {
            switch pointType {
            case .start:
                return Color.blue // 起点使用蓝色 #007AFF
            case .waypoint:
                return color // 途经点使用传入的颜色（蓝色=未优化，紫色=已优化）
            case .end:
                return Color.green // 终点使用绿色 #34C759
            case .startEnd:
                return Color.orange // 起终点组合使用橙色作为备用（实际上会使用双色标记）
            }
        }

        // 如果没有指定点类型，使用传入的颜色
        return color
    }

    // 起点颜色
    private let startColor = Color.blue // #007AFF

    // 终点颜色
    private let endColor = Color.green // #34C759

    // 当前使用的渲染模式
    private var currentRenderMode: MarkerRenderMode {
        return renderMode ?? LoggerConfig.shared.markerRenderMode
    }

    var body: some View {
        // 🚀 根据渲染模式选择最优的渲染方式
        switch currentRenderMode {
        case .drop:
            // 高性能Drop模式 - 减少90%渲染消耗
            DropMarkerView(
                number: number,
                packageCount: packageCount,
                color: color,
                isAssignedToGroup: isAssignedToGroup,
                groupNumber: groupNumber,
                shouldFade: shouldFade,
                pointType: pointType,
                isCompleted: isCompleted,
                isFailed: isFailed,
                customText: customText,
                hasCoordinateWarning: hasCoordinateWarning
            )

        case .rectangle:
            // 传统矩形模式 - 兼容性好
            rectangleMarkerView

        case .numbersRectangle:
            // Rectangle.Fill模式 - 清晰简洁，可显示任意数字
            NumbersRectangleMarkerView(
                number: number,
                packageCount: packageCount,
                color: color,
                isAssignedToGroup: isAssignedToGroup,
                groupNumber: groupNumber,
                shouldFade: shouldFade,
                pointType: pointType,
                isCompleted: isCompleted,
                isFailed: isFailed,
                customText: customText,
                hasCoordinateWarning: hasCoordinateWarning
            )
        }
    }

    // 传统矩形+三角形标记视图
    private var rectangleMarkerView: some View {
        Group {
            // 检查是否是start或end点，如果是则直接显示symbol
            if let pointType = pointType, (pointType == .start || pointType == .end) {
                // Start和End点直接使用symbol，不需要矩形框
                Group {
                    if pointType == .start {
                        Image(systemName: "house.fill")
                            .font(.system(size: markerSize, weight: .medium))
                            .foregroundColor(markerColor)
                    } else if pointType == .end {
                        Image(systemName: "flag.fill")
                            .font(.system(size: markerSize, weight: .medium))
                            .foregroundColor(markerColor)
                    }
                }
                .opacity(opacity)
                .background(Color.clear)
                .drawingGroup(opaque: false)
                .id("symbol-\(pointType)-\(number)")
            } else {
                // 其他点使用矩形+三角形设计
                VStack(spacing: 0) {
                    // 矩形部分 - 包含数字内容
                    ZStack {
                        // 主体形状 - 所有编号统一使用矩形，确保大小一致
                        Image(systemName: "rectangle")
                            .font(.system(size: markerSize, weight: .medium))
                            .foregroundStyle(markerColor)

                        // 内容显示 - 在矩形中完全居中
                        if isCompleted {
                            // 已完成显示小对勾
                            Image(systemName: "checkmark")
                                .font(.system(size: fontSize, weight: .bold))
                                .foregroundColor(markerColor)
                        } else if isFailed {
                            // 失败显示小叉号
                            Image(systemName: "xmark")
                                .font(.system(size: fontSize, weight: .bold))
                                .foregroundColor(markerColor)
                        } else if isAssignedToGroup, let groupNumber = groupNumber {
                            // 分组显示G+组号
                            Text("G\(groupNumber)")
                                .font(.system(size: fontSize, weight: .bold))
                                .foregroundColor(.black) // 🎯 分组标记使用黑色字体
                        } else if let customText = customText {
                            // 显示自定义文本（如第三方排序号）
                            Text(customText)
                                .font(.system(size: fontSize, weight: .bold))
                                .foregroundColor(markerColor)
                        } else if number >= 0 {
                            // 显示数字编号
                            Text("\(number)")
                                .font(.system(size: fontSize, weight: .bold))
                                .foregroundColor(markerColor == .black ? .white : markerColor)
                                .fixedSize()
                        } else if number < 0 {
                            // 处理负数情况
                            Text("\(abs(number))")
                                .font(.system(size: fontSize, weight: .bold))
                                .foregroundColor(markerColor)
                                .fixedSize()
                        }
                    }

                    // 向下的实心三角形指针
                    Image(systemName: "arrowtriangle.down.fill")
                        .font(.system(size: markerSize * 0.4, weight: .medium))
                        .foregroundStyle(markerColor)
                        .offset(y: -2)
                }
                .opacity(opacity)
                .background(Color.clear)
                .drawingGroup(opaque: false)
                .id("rect-\(number)-\(isCompleted)-\(isFailed)")
            }
        }
    }
}

// 聚类标记视图 - 用于显示多个点的聚合
struct ClusterMarkerView: View {
    let count: Int
    let color: Color

    // 根据聚类大小动态计算标记大小
    private var markerSize: CGFloat {
        if count < 10 {
            return 40
        } else if count < 50 {
            return 50
        } else {
            return 60
        }
    }

    // 根据聚类大小动态计算字体大小
    private var fontSize: CGFloat {
        if count < 10 {
            return 16
        } else if count < 50 {
            return 18
        } else {
            return 20
        }
    }

    var body: some View {
        ZStack {
            // 外圈
            Circle()
                .fill(color.opacity(0.3))
                .frame(width: markerSize + 10, height: markerSize + 10)

            // 内圈
            Circle()
                .fill(color)
                .frame(width: markerSize, height: markerSize)
                .shadow(color: Color.black.opacity(0.3), radius: 3, x: 0, y: 2)

            // 数字
            Text("\(count)")
                .font(.system(size: fontSize, weight: .bold))
                .foregroundColor(.white)
                .shadow(color: Color.black.opacity(0.7), radius: 1, x: 0, y: 0.5)
        }
        .background(Color.clear)
        .drawingGroup(opaque: false)
    }
}
