import SwiftUI

/// 路线优化演示界面
/// 展示优化前后的对比效果和用户反馈
struct RouteOptimizationDemoView: View {
    @State private var isOptimizing = false
    @State private var showResult = false
    @State private var optimizationStep = 0
    @State private var savedDistance = 2.4
    @State private var savedPercentage = 15.8
    
    // 模拟地址数据
    @State private var addresses = [
        ("1", "3420 Tupelo Drive", false),
        ("2", "10948 Dutch Tulip Drive", false),
        ("3", "4265 Maddie Circle", false),
        ("4", "10727 Trevor Drive", false)
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("🛣️ 组内路线优化演示")
                    .font(.title2.bold())
                    .foregroundColor(.adaptivePrimaryText)
                    .padding()
                
                // 地址列表
                VStack(spacing: 12) {
                    ForEach(Array(addresses.enumerated()), id: \.offset) { index, address in
                        addressRow(
                            number: address.0,
                            address: address.1,
                            isOptimized: address.2,
                            isHighlighted: isOptimizing && optimizationStep == index
                        )
                    }
                }
                .padding(.horizontal)
                
                // 距离信息
                HStack {
                    VStack {
                        Text("当前距离")
                            .font(.caption)
                            .foregroundColor(.adaptiveSecondaryText)
                        Text("15.2 km")
                            .font(.headline)
                            .foregroundColor(.adaptivePrimaryText)
                    }
                    
                    Spacer()
                    
                    if showResult {
                        VStack {
                            Text("节省距离")
                                .font(.caption)
                                .foregroundColor(.adaptiveSecondaryText)
                            Text("\(savedDistance, specifier: "%.1f") km")
                                .font(.headline)
                                .foregroundColor(.adaptiveSuccess)
                        }
                        
                        Spacer()
                        
                        VStack {
                            Text("节省比例")
                                .font(.caption)
                                .foregroundColor(.adaptiveSecondaryText)
                            Text("\(savedPercentage, specifier: "%.1f")%")
                                .font(.headline)
                                .foregroundColor(.adaptiveSuccess)
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 12)
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 优化按钮
                Button(action: {
                    startOptimization()
                }) {
                    HStack {
                        if isOptimizing {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                            Text("正在优化...")
                        } else {
                            Image(systemName: "arrow.triangle.swap")
                            Text("优化组内路线")
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(isOptimizing ? Color.adaptiveSecondaryIcon : Color.adaptiveWarning)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isOptimizing)
                .padding(.horizontal)
                
                if showResult {
                    // 优化结果
                    VStack(spacing: 8) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.adaptiveSuccess)
                            Text("路线优化成功！")
                                .font(.headline)
                                .foregroundColor(.adaptiveSuccess)
                        }
                        
                        Text("节省了 \(savedDistance, specifier: "%.1f") 公里的距离（\(savedPercentage, specifier: "%.1f")%）")
                            .font(.subheadline)
                            .foregroundColor(.adaptiveSecondaryText)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .background(Color.adaptiveSuccess.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                
                // 功能说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("💡 用户反馈改进：")
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)
                    
                    Text("• 按钮显示加载状态和进度")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 优化完成后显示具体节省数据")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 地址行显示优化状态指示器")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 提供触觉反馈增强体验")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
                
                Spacer()
            }
            .background(Color.adaptiveBackground)
            .navigationTitle("路线优化演示")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("重置") {
                        resetDemo()
                    }
                    .foregroundColor(.adaptivePrimaryIcon)
                }
            }
        }
    }
    
    // MARK: - 地址行视图
    private func addressRow(number: String, address: String, isOptimized: Bool, isHighlighted: Bool) -> some View {
        HStack(spacing: 12) {
            // 序号圆圈
            Text(number)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(isOptimized ? Color.adaptiveSuccess : Color.adaptivePrimaryIcon)
                .cornerRadius(6)
                .scaleEffect(isHighlighted ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 0.3), value: isHighlighted)
            
            // 地址信息
            VStack(alignment: .leading, spacing: 2) {
                Text(address)
                    .font(.subheadline)
                    .foregroundColor(.adaptivePrimaryText)
                
                Text("Stockton, CA 95209")
                    .font(.caption)
                    .foregroundColor(.adaptiveSecondaryText)
            }
            
            Spacer()
            
            // 优化状态指示器
            if isOptimized {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.adaptiveSuccess)
                    .font(.system(size: 16))
                    .scaleEffect(0.8)
                    .opacity(0.0)
                    .scaleEffect(1.0)
                    .opacity(1.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.6), value: isOptimized)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(isHighlighted ? Color.adaptiveWarning.opacity(0.1) : Color.adaptiveCardBackground)
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(isHighlighted ? Color.adaptiveWarning : Color.clear, lineWidth: 2)
        )
        .animation(.easeInOut(duration: 0.3), value: isHighlighted)
    }
    
    // MARK: - 优化动画
    private func startOptimization() {
        isOptimizing = true
        showResult = false
        optimizationStep = 0
        
        // 触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
        
        // 模拟优化过程
        Timer.scheduledTimer(withTimeInterval: 0.8, repeats: true) { timer in
            if optimizationStep < addresses.count {
                optimizationStep += 1
            } else {
                timer.invalidate()
                completeOptimization()
            }
        }
    }
    
    private func completeOptimization() {
        // 更新地址顺序（模拟优化结果）
        addresses = [
            ("1", "3420 Tupelo Drive", true),
            ("2", "4265 Maddie Circle", true),
            ("3", "10727 Trevor Drive", true),
            ("4", "10948 Dutch Tulip Drive", true)
        ]
        
        isOptimizing = false
        showResult = true
        
        // 成功触觉反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
    }
    
    private func resetDemo() {
        isOptimizing = false
        showResult = false
        optimizationStep = 0
        
        // 重置地址顺序
        addresses = [
            ("1", "3420 Tupelo Drive", false),
            ("2", "10948 Dutch Tulip Drive", false),
            ("3", "4265 Maddie Circle", false),
            ("4", "10727 Trevor Drive", false)
        ]
    }
}

#Preview("Route Optimization Demo - Light") {
    RouteOptimizationDemoView()
        .preferredColorScheme(.light)
}

#Preview("Route Optimization Demo - Dark") {
    RouteOptimizationDemoView()
        .preferredColorScheme(.dark)
}
