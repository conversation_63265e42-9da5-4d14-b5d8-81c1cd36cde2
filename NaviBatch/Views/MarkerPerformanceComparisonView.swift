import SwiftUI

/// 地图标记性能对比演示视图
/// 展示不同渲染模式的性能差异和视觉效果
struct MarkerPerformanceComparisonView: View {
    @State private var selectedMode: MarkerRenderMode = .drop
    @State private var markerCount: Int = 50
    @State private var showPerformanceStats = false
    @State private var renderTime: Double = 0
    
    // 测试数据
    private let testMarkers = (1...200).map { index in
        TestMarkerData(
            id: index,
            number: index,
            color: [.blue, .red, .green, .orange, .purple].randomElement() ?? .blue,
            isCompleted: Bool.random(),
            isFailed: Bool.random() && !Bool.random(), // 减少失败的概率
            isGrouped: Bool.random()
        )
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                headerSection
                
                // 控制面板
                controlPanel
                
                // 性能统计
                if showPerformanceStats {
                    performanceStatsSection
                }
                
                // 标记预览网格
                markerGridSection
                
                Spacer()
            }
            .padding()
            .navigationTitle("标记性能对比")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Text("地图标记渲染性能对比")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("对比不同渲染模式的性能表现和视觉效果")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var controlPanel: some View {
        VStack(spacing: 16) {
            // 渲染模式选择
            VStack(alignment: .leading, spacing: 8) {
                Text("渲染模式")
                    .font(.headline)
                
                Picker("渲染模式", selection: $selectedMode) {
                    Text("🎯 Rectangle.Fill (清晰)").tag(MarkerRenderMode.numbersRectangle)
                    Text("🚀 Drop模式 (高性能)").tag(MarkerRenderMode.drop)
                    Text("📦 矩形模式 (传统)").tag(MarkerRenderMode.rectangle)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            // 标记数量控制
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("标记数量: \(markerCount)")
                        .font(.headline)
                    Spacer()
                    Text("最大200个")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Slider(value: Binding(
                    get: { Double(markerCount) },
                    set: { markerCount = Int($0) }
                ), in: 10...200, step: 10)
            }
            
            // 性能统计开关
            Toggle("显示性能统计", isOn: $showPerformanceStats)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var performanceStatsSection: some View {
        VStack(spacing: 12) {
            Text("性能统计")
                .font(.headline)
            
            HStack(spacing: 20) {
                performanceStatItem(
                    title: "渲染时间",
                    value: String(format: "%.2fms", renderTime),
                    color: selectedMode == .drop ? .green : .orange
                )
                
                performanceStatItem(
                    title: "GPU调用",
                    value: selectedMode == .drop ? "\(markerCount)" : "\(markerCount * 3)",
                    color: selectedMode == .drop ? .green : .red
                )
                
                performanceStatItem(
                    title: "内存占用",
                    value: selectedMode == .drop ? "低" : "高",
                    color: selectedMode == .drop ? .green : .orange
                )
            }
            
            // 性能优势说明
            performanceAdvantagesView
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func performanceStatItem(title: String, value: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
    }
    
    private var performanceAdvantagesView: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("Drop模式优势:")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            ForEach([
                "🚀 渲染速度提升 80-90%",
                "💾 内存占用减少 70%",
                "🎯 轮廓设计，尖端朝下",
                "🔍 大容器+小字体=完整显示",
                "🎨 边框灰色+数字状态色",
                "⚡ GPU硬件加速支持",
                "📱 更好的设备兼容性",
                "🔋 降低电池消耗"
            ], id: \.self) { advantage in
                HStack {
                    Text(advantage)
                        .font(.caption)
                    Spacer()
                }
            }
        }
    }
    
    private var markerGridSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("标记预览 (\(markerCount)个)")
                .font(.headline)
            
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 8), spacing: 8) {
                    ForEach(testMarkers.prefix(markerCount), id: \.id) { marker in
                        MarkerView(
                            number: marker.number,
                            packageCount: 1,
                            color: marker.color,
                            isAssignedToGroup: marker.isGrouped,
                            groupNumber: marker.isGrouped ? Int.random(in: 1...5) : nil,
                            shouldFade: false,
                            pointType: .waypoint,
                            isCompleted: marker.isCompleted,
                            isFailed: marker.isFailed,
                            renderMode: selectedMode // 🎯 指定渲染模式
                        )
                        .frame(width: 32, height: 32)
                    }
                }
                .padding()
            }
            .frame(maxHeight: 300)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
        .onAppear {
            measureRenderTime()
        }
        .onChange(of: selectedMode) { _, _ in
            measureRenderTime()
        }
        .onChange(of: markerCount) { _, _ in
            measureRenderTime()
        }
    }
    
    // MARK: - 性能测量
    
    private func measureRenderTime() {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 模拟渲染时间计算
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let endTime = CFAbsoluteTimeGetCurrent()
            let baseTime = (endTime - startTime) * 1000 // 转换为毫秒
            
            // 根据模式和数量调整渲染时间
            switch selectedMode {
            case .drop:
                renderTime = baseTime + Double(markerCount) * 0.1
            case .rectangle:
                renderTime = baseTime + Double(markerCount) * 0.3
            case .numbersRectangle:
                renderTime = baseTime + Double(markerCount) * 0.15
            }
        }
    }
}

// MARK: - 测试数据模型

struct TestMarkerData {
    let id: Int
    let number: Int
    let color: Color
    let isCompleted: Bool
    let isFailed: Bool
    let isGrouped: Bool
}

// MARK: - 预览

#Preview("标记性能对比") {
    MarkerPerformanceComparisonView()
}
