import SwiftUI
import MapKit

// 点位置的MapView实景图预览组件
struct MapViewPreview: View {
    let coordinate: CLLocationCoordinate2D
    @State private var lookAroundScene: MKLookAroundScene?

    var body: some View {
        VStack {
            if let scene = lookAroundScene {
                if #available(iOS 16.0, *) {
                    LookAroundPreviewView(scene: scene)
                        .frame(height: 200)
                        .overlay(alignment: .bottomTrailing) {
                            Text("© Apple地图")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Color.black.opacity(0.5))
                                .cornerRadius(4)
                                .padding(4)
                        }
                } else {
                    // 在不支持 MKLookAroundView 的系统版本上显示替代视图
                    VStack {
                        Text("实景图在 iOS 16 及以上版本可用")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding()
                    }
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(alignment: .bottomTrailing) {
                        Text("© Apple地图")
                            .font(.caption2)
                            .foregroundColor(.white)
                            .padding(4)
                            .background(Color.black.opacity(0.5))
                            .cornerRadius(4)
                            .padding(4)
                    }
                }
            } else {
                // 实景图加载中或不可用时显示标准地图
                Map {
                    Marker("", coordinate: coordinate)
                }
                .mapStyle(.standard(elevation: .realistic))
                .overlay {
                    if lookAroundScene == nil {
                        ProgressView()
                            .scaleEffect(1.2)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(Color.black.opacity(0.1))
                    }
                }
            }
        }
        .onAppear {
            Logger.info("开始加载实景图，坐标: \(coordinate.latitude), \(coordinate.longitude)", type: .info)
            loadLookAroundScene()
        }
    }

    private func loadLookAroundScene() {
        let lookAroundRequest = MKLookAroundSceneRequest(coordinate: coordinate)
        lookAroundRequest.getSceneWithCompletionHandler { scene, error in
            DispatchQueue.main.async {
                if let scene = scene {
                    self.lookAroundScene = scene
                    Logger.info("实景图加载成功，坐标: \(self.coordinate.latitude), \(self.coordinate.longitude)", type: .data)
                } else if let error = error {
                    Logger.error("实景图加载失败: \(error.localizedDescription), 坐标: \(self.coordinate.latitude), \(self.coordinate.longitude)", type: .data)
                } else {
                    Logger.warning("该位置不存在实景图数据，坐标: \(self.coordinate.latitude), \(self.coordinate.longitude)", type: .validation)
                }
            }
        }
    }
}
