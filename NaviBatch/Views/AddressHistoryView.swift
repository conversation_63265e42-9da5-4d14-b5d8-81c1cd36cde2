import SwiftUI
import SwiftData
import CoreLocation
import MapKit

/// 历史地址管理界面
/// 显示用户地址数据库中的所有历史地址，支持查看、删除、修复功能
struct AddressHistoryView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @Query(sort: [SortDescriptor(\ValidatedAddress.lastUsedAt, order: .reverse)])
    private var allValidatedAddresses: [ValidatedAddress]

    @State private var searchText = ""
    @State private var isLoading = false
    @State private var showDeleteAlert = false
    @State private var addressToDelete: ValidatedAddress?
    @State private var addressToEdit: ValidatedAddress?
    @State private var showingClearAllAlert = false

    // 过滤后的地址列表
    private var filteredAddresses: [ValidatedAddress] {
        if searchText.isEmpty {
            return allValidatedAddresses
        } else {
            return allValidatedAddresses.filter { address in
                address.originalAddress.localizedCaseInsensitiveContains(searchText) ||
                address.normalizedAddress.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar

                // 🎯 优化：简化统计信息，移除边框，一体化设计
                statisticsSection

                // 地址列表
                addressList
            }
            .navigationTitle("address_history".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("back".localized) {
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("clear_all".localized) {
                        showingClearAllAlert = true
                    }
                    .foregroundColor(.red)
                }
            }
        }
            .alert("confirm_delete".localized, isPresented: $showDeleteAlert) {
                Button("cancel".localized, role: .cancel) { }
                Button("delete".localized, role: .destructive) {
                    if let address = addressToDelete {
                        deleteAddress(address)
                    }
                }
            } message: {
                Text("delete_address_confirmation".localized)
            }
            .alert("clear_database".localized, isPresented: $showingClearAllAlert) {
                Button("cancel".localized, role: .cancel) { }
                Button("confirm_clear".localized, role: .destructive) {
                    clearAllAddresses()
                }
            } message: {
                Text("clear_database_confirmation".localized)
            }
            .sheet(item: $addressToEdit, onDismiss: {
                print("🔧 Edit sheet closed")
            }) { address in
                AddressHistoryEditView(address: address) {
                    // SwiftData会自动刷新，无需手动刷新
                }
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
            }
    }



    // MARK: - 搜索栏
    private var searchBar: some View {
        HStack(spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16, weight: .medium))

                TextField("search_address".localized, text: $searchText)
                    .font(.system(size: 16))

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 16))
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }

    // MARK: - 🎯 优化：简化统计信息，一体化设计
    private var statisticsSection: some View {
        HStack(spacing: 40) {
            // 总地址数
            VStack(spacing: 4) {
                Text("\(filteredAddresses.count)")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Text("total_addresses".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }

            // 常用地址数
            VStack(spacing: 4) {
                Text("\(filteredAddresses.filter { $0.usageCount > 1 }.count)")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.blue)

                Text("frequently_used".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 16)
        .frame(maxWidth: .infinity)
    }

    // MARK: - 地址列表
    private var addressList: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                if filteredAddresses.isEmpty {
                    emptyStateView
                        .frame(minHeight: 300)
                } else {
                    ForEach(filteredAddresses, id: \.id) { address in
                        AddressHistoryRow(
                            address: address,
                            onEdit: {
                                print("🔧 Edit button clicked, address: \(address.originalAddress)")
                                addressToEdit = address
                                print("🔧 addressToEdit set: \(address.originalAddress)")
                            },
                            onDelete: {
                                addressToDelete = address
                                showDeleteAlert = true
                            }
                        )
                    }
                }
            }
            .padding(.top, 8)
        }
    }

    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            // 图标
            ZStack {
                Circle()
                    .fill(Color(.systemGray6))
                    .frame(width: 80, height: 80)

                Image(systemName: "location.slash")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.secondary)
            }

            VStack(spacing: 8) {
                Text("no_saved_addresses_title".localized)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("no_saved_addresses_message".localized)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
        }
        .padding(.vertical, 40)
        .frame(maxWidth: .infinity)
    }

    // MARK: - 私有方法

    private func deleteAddress(_ address: ValidatedAddress) {
        do {
            modelContext.delete(address)
            try modelContext.save()
            print("🗑️ Deleted address: \(address.originalAddress)")
        } catch {
            print("❌ Failed to delete address: \(error)")
        }
    }

    private func clearAllAddresses() {
        // 🧹 只清空司机的地址库（ValidatedAddress），不影响其他数据
        do {
            // 获取所有ValidatedAddress记录
            let addressesToDelete = allValidatedAddresses
            let count = addressesToDelete.count

            // 逐个删除所有记录
            for address in addressesToDelete {
                modelContext.delete(address)
            }

            // 保存更改
            try modelContext.save()

            print("🧹 已清空司机地址库，删除了 \(count) 条记录")

        } catch {
            print("❌ 清空地址库失败: \(error.localizedDescription)")
        }
    }
}

/// 历史地址行视图
struct AddressHistoryRow: View {
    let address: ValidatedAddress
    let onEdit: () -> Void
    let onDelete: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 主要内容
            HStack(alignment: .top, spacing: 12) {
                // 状态指示器
                VStack(spacing: 4) {
                    Circle()
                        .fill(coordinateStatusColor)
                        .frame(width: 12, height: 12)
                        .shadow(color: coordinateStatusColor.opacity(0.3), radius: 2, x: 0, y: 1)

                    Text(String(format: "%.1f", address.confidence))
                        .font(.system(size: 10, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary)
                }
                .padding(.top, 2)

                // 地址信息
                VStack(alignment: .leading, spacing: 6) {
                    Text(address.originalAddress)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(2)
                        .fixedSize(horizontal: false, vertical: true)

                    if !address.normalizedAddress.isEmpty && address.normalizedAddress != address.originalAddress {
                        Text(address.normalizedAddress)
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }

                    // 统计信息标签
                    HStack(spacing: 8) {
                        // 使用次数
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(.blue)
                            Text("\(address.usageCount)")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.blue)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(Color.blue.opacity(0.1))
                        )

                        // 最后使用时间
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(.orange)
                            Text(formatDate(address.lastUsedAt))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.orange)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(Color.orange.opacity(0.1))
                        )

                        Spacer()

                        // 🎯 优化：移除manual和screenshot标签，只显示其他来源
                        if let sourceEnum = AddressSource(rawValue: address.source),
                           sourceEnum != .manual && sourceEnum != .screenshot {
                            Text(sourceEnum.rawValue)
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(Color(.systemGray5))
                                )
                        }
                    }
                }

                // 编辑按钮
                Button(action: onEdit) {
                    Image(systemName: "pencil.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.blue)
                        .background(
                            Circle()
                                .fill(Color(.systemBackground))
                                .frame(width: 26, height: 26)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.tertiarySystemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .padding(.horizontal, 16)
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onLongPressGesture {
            onDelete()
        }
        .contextMenu {
            Button("edit_address".localized) {
                onEdit()
            }

            Button("copy_address".localized) {
                UIPasteboard.general.string = address.originalAddress
            }

            Divider()

            Button("delete_address".localized, role: .destructive) {
                onDelete()
            }
        }
    }

    private var coordinateStatusColor: Color {
        if address.confidence >= 0.8 {
            return .green
        } else if address.confidence >= 0.5 {
            return .orange
        } else {
            return .red
        }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

#Preview {
    AddressHistoryView()
        .modelContainer(for: [ValidatedAddress.self], inMemory: true)
}
