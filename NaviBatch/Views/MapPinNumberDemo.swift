import SwiftUI
import CoreLocation

// 演示map pin编号根据优化状态动态显示的功能
struct MapPinNumberDemo: View {
    @State private var samplePoints: [DeliveryPoint] = []
    @State private var showOptimized = false
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Map Pin编号动态显示演示")
                .font(.title2)
                .padding()
            
            Text("根据路线优化状态，pin显示不同的编号")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // 切换优化状态按钮
            Button(action: {
                withAnimation {
                    showOptimized.toggle()
                    updatePointsOptimizationStatus()
                }
            }) {
                HStack {
                    Image(systemName: showOptimized ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(showOptimized ? Color(hex: "B36AE2") : .gray)
                    Text(showOptimized ? "路线已优化" : "路线未优化")
                        .foregroundColor(showOptimized ? Color(hex: "B36AE2") : .primary)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
            
            // 显示编号逻辑说明
            VStack(alignment: .leading, spacing: 12) {
                Text("编号显示逻辑：")
                    .font(.headline)
                
                HStack {
                    Image(systemName: "circle.fill")
                        .foregroundColor(.blue)
                        .font(.caption)
                    Text("未优化时：显示 sort_number（原始顺序）")
                        .font(.subheadline)
                }
                
                HStack {
                    Image(systemName: "circle.fill")
                        .foregroundColor(Color(hex: "B36AE2"))
                        .font(.caption)
                    Text("已优化时：显示 sorted_number（优化后顺序）")
                        .font(.subheadline)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            // 示例点列表
            VStack(alignment: .leading, spacing: 16) {
                Text("示例配送点：")
                    .font(.headline)
                
                ForEach(samplePoints, id: \.id) { point in
                    HStack(spacing: 16) {
                        // Map Pin预览
                        VStack(spacing: 0) {
                            // 矩形部分 - 包含数字内容
                            ZStack {
                                // 主体矩形
                                Image(systemName: "rectangle")
                                    .font(.system(size: 24, weight: .medium))
                                    .foregroundStyle(point.isOptimized ? Color(hex: "B36AE2") : Color.blue)

                                // 数字 - 如果是第三方快递且有排序号，显示第三方排序号
                                Text(getDisplayText(for: point))
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(point.isOptimized ? Color(hex: "B36AE2") : Color.blue)
                            }
                            
                            // 向下的实心三角形指针
                            Image(systemName: "arrowtriangle.down.fill")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundStyle(point.isOptimized ? Color(hex: "B36AE2") : Color.blue)
                                .offset(y: -2)
                        }
                        
                        // 点信息
                        VStack(alignment: .leading, spacing: 4) {
                            Text(point.primaryAddress)
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            HStack(spacing: 16) {
                                Text("sort_number: \(point.sort_number)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Text("sorted_number: \(point.sorted_number)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Text("显示编号: \(point.displayNumber)")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(point.isOptimized ? Color(hex: "B36AE2") : .blue)
                        }
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
            }
            
            Spacer()
        }
        .padding()
        .onAppear {
            setupSamplePoints()
        }
    }
    
    // 获取显示文本
    private func getDisplayText(for point: DeliveryPoint) -> String {
        if point.isThirdPartyWithSort, let thirdPartySort = point.thirdPartySortNumber {
            return thirdPartySort
        } else {
            return "\(point.displayNumber)"
        }
    }

    private func setupSamplePoints() {
        samplePoints = [
            createSamplePoint(sortNumber: 1, sortedNumber: 3, address: "123 Main Street"),
            createSamplePoint(sortNumber: 2, sortedNumber: 1, address: "456 Oak Avenue"),
            createSamplePoint(sortNumber: 3, sortedNumber: 2, address: "789 Pine Road"),
            createSamplePoint(sortNumber: 4, sortedNumber: 4, address: "321 Elm Street")
        ]
    }
    
    private func createSamplePoint(sortNumber: Int, sortedNumber: Int, address: String) -> DeliveryPoint {
        let point = DeliveryPoint(
            sort_number: sortNumber,
            streetName: address,
            coordinate: CLLocationCoordinate2D(latitude: -37.8770, longitude: 145.1610)
        )
        point.sorted_number = sortedNumber
        point.isOptimized = false
        return point
    }
    
    private func updatePointsOptimizationStatus() {
        for point in samplePoints {
            point.isOptimized = showOptimized
        }
    }
}

// Color扩展已在 Extensions/Color+Hex.swift 中定义

#Preview("MapPinNumberDemo") {
    MapPinNumberDemo()
}
