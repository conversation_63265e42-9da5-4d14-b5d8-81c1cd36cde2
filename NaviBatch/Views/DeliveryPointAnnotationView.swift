import SwiftUI

// 配送点标记视图 - 性能优化版本
struct DeliveryPointAnnotationView: View {
    let point: DeliveryPoint
    let isMultiSelectMode: Bool
    let isSelected: Bool
    let onTap: () -> Void
    var isMaxSelectionReached: Bool = false  // 添加最大选择限制属性
    var showStatus: Bool = true              // 是否显示状态指示器

    // 缓存颜色计算结果，避免重复计算
    private var pointColor: Color {
        getPointColor()
    }

    // 缓存是否应该淡化显示，避免重复计算
    private var shouldFadeOut: Bool {
        isMultiSelectMode && isMaxSelectionReached && !isSelected
    }

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // 主体标记 - 如果是第三方快递且有排序号，使用第三方排序号
                let customText = point.isThirdPartyWithSort ? point.thirdPartySortNumber : nil

                MarkerView(
                    number: point.displayNumber, // 🎯 使用displayNumber，优先显示第三方号码
                    packageCount: point.packageCount,
                    color: pointColor,
                    isAssignedToGroup: point.isAssignedToGroup,
                    groupNumber: point.assignedGroupNumber,
                    shouldFade: shouldFadeOut,
                    pointType: getPointType(), // 添加点类型
                    isCompleted: point.deliveryStatus == .completed,
                    isFailed: point.deliveryStatus == .failed,
                    customText: customText,
                    hasCoordinateWarning: point.geocodingWarning != nil // 🚨 传递坐标警告状态
                )

                // 地址文本标签 - 仅在多选模式下且点被选中时显示
                if isMultiSelectMode && isSelected {
                    HStack(alignment: .center) {
                        // 仅显示简短地址
                        let shortAddress = point.primaryAddress.components(separatedBy: ",").first ?? point.primaryAddress
                        Text(shortAddress)
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(.white)
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                            .shadow(color: Color.black.opacity(0.5), radius: 1, x: 0, y: 0)
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(
                        Capsule()
                            .fill(Color.red)
                            .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)
                    )
                    .offset(y: 5) // 放在标记正下方
                }
            }
            .background(Color.clear) // 确保背景完全透明
        }
        .buttonStyle(PlainButtonStyle())
        // 当达到选择上限且点未被选中时禁用按钮
        .disabled(shouldFadeOut)
        // 添加视觉反馈：已达上限但未选中的点显示半透明效果
        .saturation(shouldFadeOut ? 0.5 : 1.0)
        // 使用drawingGroup()提高渲染性能
        .drawingGroup(opaque: false) // 修改为非不透明，允许透明
        .background(Color.clear) // 额外确保无黑色背景
    }

    // 获取点类型
    private func getPointType() -> PointType {
        if point.isStartPoint && point.isEndPoint {
            // 同时是起点和终点
            return .startEnd
        } else if point.isStartPoint {
            return .start
        } else if point.isEndPoint {
            return .end
        } else {
            return .waypoint
        }
    }

    private func getPointColor() -> Color {
        // 多选模式下，使用橙色作为选中颜色，提高可见性
        if isMultiSelectMode && isSelected {
            return Color.orange
        }

        // 如果显示状态且不在多选模式下，使用状态颜色
        if showStatus && !isMultiSelectMode && !isSelected {
            return point.deliveryStatus.color
        }

        if point.isAssignedToGroup {
            return Color.gray.opacity(0.8)   // 已分配到组的点显示为灰色
        } else if isSelected {
            return Color.blue   // 当前选中的点显示为蓝色
        } else {
            // 根据点类型返回颜色
            if point.isStartPoint {
                return Color.blue // 起点使用蓝色
            } else if point.isEndPoint {
                return Color.green // 终点使用绿色
            } else if point.isOptimized {
                return Color(hex: "B36AE2") // 已优化的途经点使用紫色
            } else {
                return Color.blue // 未优化的途经点使用蓝色
            }
        }
    }
}
