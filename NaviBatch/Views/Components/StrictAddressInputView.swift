import SwiftUI
import CoreLocation
import Combine

// 严格地址输入组件 - 阻止Apple Maps自动修正
struct StrictAddressInputView: View {
    @Binding var address: String
    let placeholder: String
    let onAddressValidated: ((AddressValidationResult) -> Void)?
    
    @State private var isValidating = false
    @State private var validationResult: AddressValidationResult?
    @State private var validationTimer: Timer?
    @State private var cancellables = Set<AnyCancellable>()
    
    private let geocodingService = GeocodingService.shared
    
    init(
        address: Binding<String>,
        placeholder: String = "输入完整地址",
        onAddressValidated: ((AddressValidationResult) -> Void)? = nil
    ) {
        self._address = address
        self.placeholder = placeholder
        self.onAddressValidated = onAddressValidated
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 地址输入框
            HStack {
                TextField(placeholder, text: $address)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.words)
                    .onChange(of: address) { _, newValue in
                        scheduleValidation(for: newValue)
                    }
                
                // 验证状态指示器
                if isValidating {
                    ProgressView()
                        .scaleEffect(0.8)
                } else if let result = validationResult {
                    validationStatusIcon(for: result)
                }
            }
            
            // 格式提示
            formatHintView
            
            // 验证结果提示
            if let result = validationResult {
                validationResultView(result)
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func scheduleValidation(for address: String) {
        // 取消之前的定时器
        validationTimer?.invalidate()
        
        // 清除之前的结果
        validationResult = nil
        
        // 如果地址为空，不进行验证
        guard !address.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        // 设置新的定时器，延迟1秒后验证
        validationTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { _ in
            validateAddress(address)
        }
    }
    
    private func validateAddress(_ address: String) {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedAddress.isEmpty else { return }
        
        isValidating = true
        
        // 使用严格验证
        geocodingService.strictValidateAddress(trimmedAddress)
            .receive(on: DispatchQueue.main)
            .sink { result in
                isValidating = false
                validationResult = result
                onAddressValidated?(result)
            }
            .store(in: &cancellables)
    }
    
    private var formatHintView: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("地址格式要求：")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                    .font(.caption)
                
                Text("门牌号 街道名, 城市, 州 邮编")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Image(systemName: "checkmark.circle")
                    .foregroundColor(.green)
                    .font(.caption)
                
                Text("例如：123 Collins Street, Melbourne, VIC 3000")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 4)
    }
    
    private func validationStatusIcon(for result: AddressValidationResult) -> some View {
        Image(systemName: iconName(for: result))
            .foregroundColor(iconColor(for: result))
            .font(.title3)
    }
    
    private func validationResultView(_ result: AddressValidationResult) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 主要状态
            HStack {
                Image(systemName: iconName(for: result))
                    .foregroundColor(iconColor(for: result))
                    .font(.subheadline)
                
                Text(statusMessage(for: result))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(iconColor(for: result))
            }
            
            // 警告信息
            if let warningMessage = result.warningMessage {
                HStack(alignment: .top) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    
                    Text(warningMessage)
                        .font(.caption)
                        .foregroundColor(.orange)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
            
            // 建议
            if !result.suggestions.isEmpty {
                VStack(alignment: .leading, spacing: 2) {
                    ForEach(result.suggestions, id: \.self) { suggestion in
                        HStack(alignment: .top) {
                            Image(systemName: "lightbulb.fill")
                                .foregroundColor(.yellow)
                                .font(.caption)
                            
                            Text(suggestion)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                    }
                }
            }
            
            // 成功时显示找到的地址
            if result.confidence == .high && !result.geocodedAddress.isEmpty {
                HStack(alignment: .top) {
                    Image(systemName: "location.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("验证成功，找到地址：")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(result.geocodedAddress)
                            .font(.caption)
                            .foregroundColor(.green)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }
            }
        }
        .padding(.horizontal, 4)
    }
    
    private func iconName(for result: AddressValidationResult) -> String {
        if result.coordinate.latitude != 0 && result.coordinate.longitude != 0 {
            return "checkmark.circle.fill"
        } else {
            switch result.confidence {
            case .high:
                return "checkmark.circle.fill"
            case .medium:
                return "exclamationmark.triangle.fill"
            case .low, .veryLow:
                return "xmark.circle.fill"
            }
        }
    }
    
    private func iconColor(for result: AddressValidationResult) -> Color {
        if result.coordinate.latitude != 0 && result.coordinate.longitude != 0 {
            return .green
        } else {
            switch result.confidence {
            case .high:
                return .green
            case .medium:
                return .orange
            case .low, .veryLow:
                return .red
            }
        }
    }
    
    private func statusMessage(for result: AddressValidationResult) -> String {
        if result.coordinate.latitude != 0 && result.coordinate.longitude != 0 {
            return "地址验证成功"
        } else {
            switch result.confidence {
            case .high:
                return "地址格式正确"
            case .medium:
                return "地址需要完善"
            case .low:
                return "地址格式有误"
            case .veryLow:
                return "地址无效"
            }
        }
    }
}

// MARK: - 预览
struct StrictAddressInputView_Previews: PreviewProvider {
    @State static var address1 = "123 Collins Street, Melbourne, VIC 3000"
    @State static var address2 = "84 Kin, Gen Waverley"
    @State static var address3 = ""
    
    static var previews: some View {
        VStack(spacing: 30) {
            VStack(alignment: .leading) {
                Text("完整正确的地址")
                    .font(.headline)
                StrictAddressInputView(
                    address: $address1,
                    placeholder: "输入完整地址"
                ) { result in
                    print("地址1验证结果: \(result)")
                }
            }
            
            VStack(alignment: .leading) {
                Text("不完整的地址")
                    .font(.headline)
                StrictAddressInputView(
                    address: $address2,
                    placeholder: "输入完整地址"
                ) { result in
                    print("地址2验证结果: \(result)")
                }
            }
            
            VStack(alignment: .leading) {
                Text("空地址")
                    .font(.headline)
                StrictAddressInputView(
                    address: $address3,
                    placeholder: "输入完整地址"
                ) { result in
                    print("地址3验证结果: \(result)")
                }
            }
            
            Spacer()
        }
        .padding()
    }
}
