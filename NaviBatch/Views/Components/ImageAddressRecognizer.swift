import SwiftUI
import Vision
import CoreLocation
import PhotosUI
import Foundation
import AVFoundation
import AVKit
import PDFKit
import UniformTypeIdentifiers

// Movie类型用于视频传输 - 修复版本
struct Movie: Transferable {
    let url: URL

    static var transferRepresentation: some TransferRepresentation {
        FileRepresentation(contentType: .movie) { movie in
            SentTransferredFile(movie.url)
        } importing: { received in
            // 使用临时目录而不是documents目录
            let tempDir = FileManager.default.temporaryDirectory
            let copy = tempDir.appendingPathComponent("movie_\(UUID().uuidString).mov")

            // 确保目标文件不存在
            if FileManager.default.fileExists(atPath: copy.path) {
                try? FileManager.default.removeItem(at: copy)
            }

            try FileManager.default.copyItem(at: received.file, to: copy)
            return Self.init(url: copy)
        }
    }
}

// PDF类型用于PDF文档传输
struct PDFDocument: Transferable {
    let url: URL

    static var transferRepresentation: some TransferRepresentation {
        FileRepresentation(contentType: .pdf) { pdf in
            SentTransferredFile(pdf.url)
        } importing: { received in
            // 使用临时目录
            let tempDir = FileManager.default.temporaryDirectory
            let copy = tempDir.appendingPathComponent("document_\(UUID().uuidString).pdf")

            // 确保目标文件不存在
            if FileManager.default.fileExists(atPath: copy.path) {
                try? FileManager.default.removeItem(at: copy)
            }

            try FileManager.default.copyItem(at: received.file, to: copy)
            return Self.init(url: copy)
        }
    }
}

// MARK: - 错误处理数据结构
struct ProcessingError: Identifiable {
    let id = UUID()
    let type: ErrorType
    let title: String
    let message: String
    let suggestions: [ErrorSuggestion]

    enum ErrorType {
        case networkError
        case aiModelUnavailable
        case imageProcessingFailed
        case rateLimitExceeded
        case unknown
    }

    struct ErrorSuggestion {
        let title: String
        let action: () -> Void
        let style: SuggestionStyle

        enum SuggestionStyle {
            case primary
            case secondary
            case destructive
        }
    }
}

/// 图片地址识别组件
/// 支持从图片中识别地址并批量添加
struct ImageAddressRecognizer: View {
    // 🚀 优化：减少重复日志的静态变量
    #if DEBUG
    private static var loggedCleanups: Set<String> = []
    #endif

    // 回调函数，用于返回识别到的地址、坐标和用户选择的应用类型
    var onAddressesConfirmed: ([(String, CLLocationCoordinate2D)], DeliveryAppType) -> Void

    // 关闭回调函数
    var onDismiss: () -> Void

    // 图片、视频和PDF选择器状态
    @State private var selectedItems: [PhotosPickerItem] = []
    @State private var selectedVideoItems: [PhotosPickerItem] = [] // 🎬 视频选择器专用
    @State private var selectedImages: [UIImage] = []
    @State private var selectedVideos: [AVAsset] = []
    @State private var selectedPDFs: [PDFKit.PDFDocument] = []
    @State private var videoFrames: [UIImage] = [] // 从视频提取的帧
    @State private var pdfPages: [UIImage] = [] // 从PDF提取的页面
    @State private var imageSourceTypes: [Bool] = [] // 标记每个图片是否来自PDF (true=PDF, false=普通图片)
    @State private var originalFrameIndices: [Int] = [] // 🎯 新增：保存原始帧编号

    // 🎯 新增：图片交互状态
    @State private var showingImageViewer = false
    @State private var selectedImageIndex = 0
    @State private var imagesReadyForProcessing = false

    // 🔄 拖拽排序状态
    @State private var draggedImageIndex: Int? = nil

    // 🎯 订阅管理（使用现有系统）
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showingSubscriptionView = false
    @State private var showingNetworkErrorView = false
    @State private var networkError: Error?

    // DocumentPicker状态
    @State private var isShowingDocumentPicker = false
    @State private var selectedDocumentURLs: [URL] = []

    // 📄 智能PDF处理状态
    @State private var showingSmartPDFProcessor = false

    // 识别状态
    @State private var isProcessing: Bool = false
    @State private var recognizedAddresses: [(String, CLLocationCoordinate2D, Bool, Bool, Double?)] = [] // 地址, 坐标, 是否选中, 是否有效坐标, 置信度
    @State private var processingProgress: Double = 0
    @State private var currentProcessingImage: Int = 0
    @State private var processingStatus: String = ""
    @State private var errorMessage: String? = nil

    // 🔒 防重复处理的强锁机制
    @State private var processingTaskID: UUID? = nil
    @State private var showingErrorSheet = false
    @State private var currentError: ProcessingError?

    // 🚫 全局执行锁 - 防止SwiftUI重复触发
    @State private var isExecuting: Bool = false

    // 🚫 静态防重复机制 - 使用时间戳
    @State private var lastClickTime: Date = Date.distantPast

    // ⏱️ 计时相关状态
    @State private var analysisStartTime: Date?
    @State private var analysisEndTime: Date?

    // 移除复杂的SpeedX序列检查弹窗 - 保持在route bottom sheet中提示
    @State private var showingNetworkErrorRecovery = false
    @State private var networkFailedAddresses: [String] = []

    // 🆕 总数输入相关状态
    @State private var userInputTotalCount: Int? = nil
    @State private var showTotalCountInput = false
    @State private var totalCountInputText = ""
    @State private var detectedTotalCount: Int? = nil  // 从图片识别的总数

    // AI相关状态
    @State private var isUsingAI: Bool = true
    @State private var aiModelUsed: String = ""
    @State private var aiConfidence: Double = 0.0
    @State private var recognitionMethod: String = ""
    @State private var ocrText: String? = nil

    // AI配置和服务
    @StateObject private var aiConfig = AIConfiguration.shared
    private let hybridService = HybridAddressRecognitionService()
    private let firebaseAIService = FirebaseAIService.shared
    private let ocrService = OCRService()

    // 🤖 智能地址修复系统
    @StateObject private var smartRetryService = SmartAddressRetryService.shared

    // 应用类型选择
    @State private var selectedAppType: DeliveryAppType = .justPhoto
    @State private var showingAppSelector = false

    // 地址验证选项 - 默认包含所有识别的地址，因为地址来源于截图，司机可自行纠正
    private let includeUnverifiedAddresses = true // 始终包含验证失败的地址

    // 🚀 批量保存优化：收集验证成功的地址，最后一次性保存到数据库
    @State private var batchAddressesToSave: [(String, CLLocationCoordinate2D, AddressSource, Float)] = []

    // 🎯 地址数量限制检查相关状态
    @State private var showLimitExceededSheet = false
    @State private var limitExceededInfo: (currentCount: Int, remainingSlots: Int, maxAllowed: Int, selectedCount: Int, selectedAddresses: [(String, CLLocationCoordinate2D)])? = nil

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 顶部内容区域
                    topContentView
                        .padding(.top, 16) // 增加顶部间距，避免与navigation title重叠

                    // 应用类型选择器
                    appTypeSelectorView

                    // 图片选择器区域
                    imagePickerView

                    // 移除复杂的补充模式指示器 - 保持简洁的用户体验

                    // 处理状态区域
                    processingStatusView

                    // 识别结果列表
                    if !recognizedAddresses.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("recognized_addresses".localized)
                                    .font(.headline)

                                Spacer()

                                // 🚀 清空按钮 - 支持多图片叠加识别
                                Button(action: {
                                    recognizedAddresses = []
                                }) {
                                    HStack(spacing: 4) {
                                        Image(systemName: "trash")
                                            .font(.caption)
                                        Text("clear_all".localized)
                                            .font(.caption)
                                    }
                                    .foregroundColor(.red)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.red.opacity(0.1))
                                    .cornerRadius(6)
                                }
                                .buttonStyle(PlainButtonStyle())

                                // 动态计数显示
                                Text(getAddressCountDisplay())
                                    .font(.caption)
                                    .foregroundColor(.adaptiveSecondaryText)
                            }
                            .padding(.horizontal)

                            LazyVStack(spacing: 8) {
                                ForEach(0..<recognizedAddresses.count, id: \.self) { index in
                                    let (address, coordinate, isSelected, hasValidCoordinate, confidence) = recognizedAddresses[index]

                                    HStack(alignment: .top, spacing: 12) {
                                        VStack(alignment: .leading, spacing: 4) {
                                            // 🎯 第三方排序标签显示
                                            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                                            if selectedAppType != .manual && selectedAppType != .justPhoto &&
                                               !separatedInfo.thirdPartySortNumber.isEmpty &&
                                               separatedInfo.thirdPartySortNumber != "missing" {
                                                HStack(spacing: 4) {
                                                    let companyColor = selectedAppType.primaryColor
                                                    Text("third_party_sort_label".localized(with: selectedAppType.displayName, separatedInfo.thirdPartySortNumber))
                                                        .font(.system(size: 12, weight: .bold))
                                                        .foregroundColor(.white)
                                                        .padding(.horizontal, 6)
                                                        .padding(.vertical, 2)
                                                        .background(companyColor)
                                                        .cornerRadius(4)
                                                    Spacer()
                                                }
                                            }

                                            HStack(spacing: 8) {
                                                Text(cleanAddressForDisplay(address))
                                                    .font(.subheadline)
                                                    .lineLimit(nil)
                                                    .multilineTextAlignment(.leading)
                                                    .fixedSize(horizontal: false, vertical: true)

                                                // 状态指示器
                                                HStack(spacing: 4) {
                                                    if !hasValidCoordinate {
                                                        Image(systemName: "location.slash")
                                                            .foregroundColor(.orange)
                                                            .font(.caption)
                                                    }

                                                    // 置信度指示器
                                                    if let conf = confidence, conf < 1.0 {
                                                        Text("\(Int(conf * 100))%")
                                                            .font(.caption2)
                                                            .foregroundColor(.blue)
                                                            .padding(.horizontal, 4)
                                                            .padding(.vertical, 1)
                                                            .background(Color.blue.opacity(0.1))
                                                            .cornerRadius(4)
                                                    }
                                                }
                                            }

                                            HStack(spacing: 4) {
                                                Text("\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude))")
                                                    .font(.caption)
                                                    .foregroundColor(hasValidCoordinate ? .secondary : .orange)

                                                if !hasValidCoordinate {
                                                    Text("(approximate_location".localized + ")")
                                                        .font(.caption2)
                                                        .foregroundColor(.orange)
                                                }
                                            }
                                        }
                                        .frame(maxWidth: .infinity, alignment: .leading)

                                        HStack(spacing: 8) {
                                            Button {
                                                toggleAddressSelection(at: index)
                                            } label: {
                                                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                                    .foregroundColor(isSelected ? .blue : .gray)
                                                    .font(.title3)
                                            }

                                            Button {
                                                removeAddress(at: index)
                                            } label: {
                                                Image(systemName: "trash")
                                                    .foregroundColor(.red)
                                                    .font(.title3)
                                            }
                                        }
                                    }
                                    .padding(.vertical, 8)
                                    .padding(.horizontal, 16)
                                    .background(Color(.systemBackground))
                                    .cornerRadius(8)
                                    .shadow(radius: 1)
                                }
                            }
                            .padding(.horizontal)
                        }
                        .padding(.bottom, 100) // 为底部确认按钮留出空间
                    }

                    Spacer(minLength: 20)
                }
                .padding(.bottom, 20) // 为ScrollView底部添加间距
            }
            .navigationTitle("scanner".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 右侧：关闭按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        onDismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 20))
                    }
                }
            }
            // 📄 智能PDF处理器 Sheet - 暂时隐藏
            /*
            .sheet(isPresented: $showingSmartPDFProcessor) {
                SmartPDFProcessingView(
                    appType: selectedAppType,
                    onComplete: { addresses in
                        // 将智能PDF处理的结果转换为当前格式
                        let convertedAddresses = addresses.map { address in
                            let coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0) // 临时坐标，后续会地理编码
                            return (address, coordinate, true, false, 0.9) // 地址, 坐标, 选中, 有效坐标, 置信度
                        }

                        // 添加到现有的识别结果中
                        recognizedAddresses.append(contentsOf: convertedAddresses)

                        // 关闭智能PDF处理器
                        showingSmartPDFProcessor = false

                        Logger.info("🎯 智能PDF处理完成，添加了\(addresses.count)个地址")
                    }
                )
            }
            */
            .overlay(
                Group {
                    if !recognizedAddresses.isEmpty && !isProcessing {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                // 🎯 优化确认按钮的视觉设计，使其更加突出和明显
                                Button(action: {
                                    confirmSelectedAddresses()
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "checkmark.circle.fill")
                                            .font(.system(size: 16, weight: .semibold))
                                        Text("confirm".localized)
                                            .font(.system(size: 18, weight: .semibold))
                                    }
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 24)
                                    .padding(.vertical, 14)
                                    .background(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .cornerRadius(12)
                                    .shadow(color: Color.blue.opacity(0.3), radius: 8, x: 0, y: 4)
                                    .scaleEffect(recognizedAddresses.filter { $0.2 }.isEmpty ? 0.95 : 1.0)
                                    .animation(.easeInOut(duration: 0.2), value: recognizedAddresses.filter { $0.2 }.isEmpty)
                                }
                                .disabled(recognizedAddresses.filter { $0.2 }.isEmpty)
                                .opacity(recognizedAddresses.filter { $0.2 }.isEmpty ? 0.6 : 1.0)
                                .padding(.trailing, 20)
                                .padding(.bottom, 20)
                            }
                        }
                    }
                }
            )
            .alert("error".localized, isPresented: .init(get: { errorMessage != nil }, set: { if !$0 { errorMessage = nil } })) {
                Button("ok".localized) { }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
            .sheet(isPresented: $showingErrorSheet) {
                if let error = currentError {
                    ErrorHandlingSheet(error: error) {
                        showingErrorSheet = false
                        currentError = nil
                    }
                }
            }
            // 移除复杂的SpeedX序列检查弹窗 - 保持简洁的用户体验
            // 🆕 总数输入弹窗
            .alert("输入总数", isPresented: $showTotalCountInput) {
                TextField("请输入总包裹数", text: $totalCountInputText)
                    .keyboardType(.numberPad)
                Button("确认") {
                    if let count = Int(totalCountInputText), count > 0 {
                        userInputTotalCount = count
                        Logger.aiInfo("📊 用户输入总数: \(count)")
                        // 继续处理
                        Task {
                            await processImages()
                        }
                    }
                }
                Button("跳过") {
                    userInputTotalCount = nil
                    Logger.aiInfo("📊 用户跳过总数输入")
                    // 继续处理
                    Task {
                        await processImages()
                    }
                }
            } message: {
                Text("为了确保数据完整性，请输入预期的总包裹数量。如果不确定，可以选择跳过。")
            }
            .sheet(isPresented: $showingNetworkErrorRecovery) {
                NetworkErrorRecoveryView(
                    failedAddresses: networkFailedAddresses,
                    onRetry: {
                        showingNetworkErrorRecovery = false
                        // 🎯 不自动重新处理，让用户手动点击按钮
                        Logger.aiInfo("🔄 网络错误恢复：等待用户手动重新开始")
                    },
                    onDismiss: {
                        showingNetworkErrorRecovery = false
                    }
                )
            }

            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RetryAIProcessing"))) { _ in
                retryProcessing()
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("SwitchToOCRMode"))) { _ in
                switchToOCRMode()
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ReduceImageCount"))) { _ in
                reduceImageCount()
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("TryFirebaseAI"))) { _ in
                tryFirebaseAI()
            }
            .onAppear {
                print("🎯 DEBUG: ImageAddressRecognizer初始化，selectedAppType = \(selectedAppType.rawValue) (\(selectedAppType.displayName))")
            }
            // 🎯 地址数量限制超出提示表单
            .sheet(isPresented: Binding(
            get: { showLimitExceededSheet },
            set: { showLimitExceededSheet = $0 }
        )) {
                if let info = limitExceededInfo {
                    AddressLimitExceededSheet(
                        currentCount: info.currentCount,
                        remainingSlots: info.remainingSlots,
                        maxAllowed: info.maxAllowed,
                        selectedCount: info.selectedCount,
                        isFreeUser: SubscriptionManager.shared.currentTier == .free,
                        selectedAddresses: info.selectedAddresses.map { $0.0 }, // 只传递地址字符串
                        onImportLimited: { limitedAddresses in
                            // 只导入符合限制数量的地址
                            let limitedAddressesWithCoords = Array(info.selectedAddresses.prefix(20))
                            Logger.aiInfo("ImageAddressRecognizer - 导入地址数量限制为20个，实际导入\(limitedAddressesWithCoords.count)个")

                            if !limitedAddressesWithCoords.isEmpty {
                                // 🎯 新逻辑：自动处理重复，不阻止用户操作
                                let processedAddresses = autoFixDuplicateThirdPartySortNumbers(limitedAddressesWithCoords)
                                let finalSelectedAddresses = processedAddresses

                                // 🎯 处理地址信息，提取sort number和清理地址
                                let finalProcessedAddresses = finalSelectedAddresses.map { (addressString, coordinate) in
                                    // 使用AddressStandardizer清理地址
                                    let cleanedAddress = AddressStandardizer.cleanDuplicateInformation(addressString)

                                    // 🎯 检查地址是否已经包含APP标签，避免重复添加
                                    let alreadyHasAppTag = cleanedAddress.contains("|APP:")
                                    let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto) && !alreadyHasAppTag
                                    let finalAddress = shouldAddAppTag ? "\(cleanedAddress)|APP:\(selectedAppType.rawValue)" : cleanedAddress

                                    return (finalAddress, coordinate)
                                }

                                // 调用原始回调，传递用户选择的应用类型
                                onAddressesConfirmed(finalProcessedAddresses, selectedAppType)

                                // 检查问题地址
                                Task {
                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                                    await checkAndShowProblemAddresses()
                                }
                            }
                        },
                        onUpgrade: {
                            // 关闭当前sheet并显示订阅界面
                            showLimitExceededSheet = false
                            showingSubscriptionView = true
                        },
                        onCancel: {
                            // 关闭sheet并清理状态
                            showLimitExceededSheet = false
                            limitExceededInfo = nil
                            Logger.aiInfo("ImageAddressRecognizer - 用户取消地址限制对话框")
                        }
                    )
                }
            }
        }
    }

    // MARK: - 国家检测支持方法

    /// 获取检测到的国家
    private func getDetectedCountry() -> DeliveryCountry? {
        // 从设备地区设置检测国家
        let deviceCountry = Locale.current.region?.identifier ?? "US"
        return DeliveryCountry(rawValue: deviceCountry) ?? .unitedStates
    }

    /// 获取当前选中的国家（用于与CountryDetectionView同步）
    @State private var selectedCountry: DeliveryCountry? = nil

    /// 根据国家获取对应的快递应用列表
    private func getAppsForCountry(_ country: DeliveryCountry) -> [DeliveryAppType] {
        switch country {
        case .unitedStates:
            return [.justPhoto, .speedx, .gofo, .uniuni, .amazonFlex, .imile, .ldsEpod, .piggy, .ywe]
        case .australia:
            return [.justPhoto, .imile]
        // 🚫 暂时注释掉其他国家的应用列表，目前只支持美国和澳洲
        // case .canada:
        //     return [.justPhoto, .imile] // 可以根据需要扩展
        // case .unitedKingdom:
        //     return [.justPhoto] // 可以根据需要扩展
        // default:
        //     return [.justPhoto] // 其他国家默认只显示通用选项
        }
    }

    // MARK: - 私有方法

    // MARK: - 图片加载和处理

    // 处理DocumentPicker选择的PDF文件
    private func handleDocumentPickerResult(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            Task {
                await loadPDFDocuments(from: urls)
            }
        case .failure(let error):
            print("❌ PDF文件选择失败: \(error)")
            errorMessage = "pdf_selection_failed".localized + ": \(error.localizedDescription)"
        }
    }

    // 加载选中的PDF文档
    private func loadPDFDocuments(from urls: [URL]) async {
        await MainActor.run {
            selectedDocumentURLs = urls
            isProcessing = true
            processingProgress = 0
            currentProcessingImage = 0
            processingStatus = "loading_pdf_files".localized
            errorMessage = nil
            imageSourceTypes = [] // 重置图片来源标记
        }

        var loadedPDFs: [PDFKit.PDFDocument] = []
        var extractedPages: [UIImage] = []

        for (index, url) in urls.enumerated() {
            // 确保有访问权限
            let accessing = url.startAccessingSecurityScopedResource()
            defer {
                if accessing {
                    url.stopAccessingSecurityScopedResource()
                }
            }

            if let pdf = PDFKit.PDFDocument(url: url) {
                loadedPDFs.append(pdf)

                // 🚀 第一优先级：尝试Firebase AI原生PDF处理
                await MainActor.run {
                    processingStatus = "processing_pdf_document".localized + ": \(url.lastPathComponent)"
                }

                if let pdfData = try? Data(contentsOf: url) {
                    do {
                        let nativeResult = try await HybridAIService.shared.extractAddressesFromPDFNatively(pdfData, appType: selectedAppType)

                        if nativeResult.success && !nativeResult.addresses.isEmpty {
                            print("✅ PDF原生AI识别成功: \(nativeResult.addresses.count)个地址")

                            // 直接处理AI识别结果
                            await MainActor.run {
                                let newAddresses = nativeResult.addresses.map { address in
                                    let coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0) // 临时坐标，后续会地理编码
                                    return (address, coordinate, true, false, nativeResult.confidence) // 地址, 坐标, 选中, 有效坐标, 置信度
                                }
                                recognizedAddresses.append(contentsOf: newAddresses)
                                print("📄 PDF原生AI处理完成: \(url.lastPathComponent)，识别了\(newAddresses.count)个地址")
                            }

                            // 跳过其他处理方式，直接继续下一个文件
                            continue
                        } else {
                            print("⚠️ PDF原生AI识别失败，降级到文本处理")
                        }
                    } catch {
                        print("⚠️ PDF原生AI处理失败: \(error.localizedDescription)，降级到文本处理")
                    }
                }

                // 🤖 第二优先级：尝试AI文本处理
                await MainActor.run {
                    processingStatus = "processing_pdf_text".localized + ": \(url.lastPathComponent)"
                }

                let pdfText = await extractTextFromPDF(pdf)

                if !pdfText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    print("📄 PDF文本提取成功，尝试AI处理: \(url.lastPathComponent)")

                    // 使用AI处理PDF文本
                    do {
                        let aiResult = try await HybridAIService.shared.extractAddressesFromPDFText(pdfText, appType: selectedAppType)

                        if aiResult.success && !aiResult.addresses.isEmpty {
                            print("✅ PDF AI文本识别成功: \(aiResult.addresses.count)个地址")

                            // 直接处理AI识别结果
                            await MainActor.run {
                                let newAddresses = aiResult.addresses.map { address in
                                    let coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0) // 临时坐标，后续会地理编码
                                    return (address, coordinate, true, false, aiResult.confidence) // 地址, 坐标, 选中, 有效坐标, 置信度
                                }
                                recognizedAddresses.append(contentsOf: newAddresses)
                                print("📄 PDF AI处理完成: \(url.lastPathComponent)，识别了\(newAddresses.count)个地址")
                            }

                            // 跳过图像处理，直接继续下一个文件
                            continue
                        } else {
                            print("⚠️ PDF AI文本识别失败，降级到图像处理")
                        }
                    } catch {
                        print("⚠️ PDF AI文本处理失败: \(error.localizedDescription)，降级到图像处理")
                    }
                } else {
                    print("⚠️ PDF文本提取为空，使用图像处理")
                }

                // 降级到图像处理
                await MainActor.run {
                    processingStatus = "extracting_pdf_pages".localized + ": \(url.lastPathComponent)"
                }

                let pages = await extractPagesFromPDF(pdf)
                extractedPages.append(contentsOf: pages)
                print("📄 PDF图像处理完成: \(url.lastPathComponent)，提取了\(pages.count)页")
            } else {
                print("❌ 无法打开PDF文件: \(url.lastPathComponent)")
            }

            await MainActor.run {
                processingProgress = Double(index + 1) / Double(urls.count) * 0.3
            }
        }

        await MainActor.run {
            selectedPDFs.append(contentsOf: loadedPDFs)
            selectedImages.append(contentsOf: extractedPages)
            pdfPages.append(contentsOf: extractedPages)

            // 为所有PDF页面添加来源标记
            for _ in extractedPages {
                imageSourceTypes.append(true) // true表示来自PDF
            }

            print("📊 PDF加载完成统计:")
            print("   - 选择的PDF文件数: \(urls.count)")
            print("   - 成功加载的PDF数: \(loadedPDFs.count)")
            print("   - 提取的页面数: \(extractedPages.count)")
        }

        if !extractedPages.isEmpty {
            // 🎯 不自动开始处理，等待用户手动点击
            await MainActor.run {
                imagesReadyForProcessing = true
                isProcessing = false
            }
            print("✅ PDF处理完成，等待用户手动开始分析")
        } else {
            await MainActor.run {
                isProcessing = false
                errorMessage = "no_pdf_pages_extracted".localized
                print("❌ 没有成功提取任何PDF页面")
            }
        }
    }

    // 加载选中的图片、视频和PDF
    private func loadMediaItems(from items: [PhotosPickerItem]) async {
        selectedImages = []
        selectedVideos = []
        selectedPDFs = []
        videoFrames = []
        pdfPages = []
        imageSourceTypes = [] // 重置图片来源标记

        // 清空之前的识别结果
        recognizedAddresses = []

        isProcessing = true
        processingProgress = 0
        currentProcessingImage = 0
        processingStatus = "loading_media".localized
        errorMessage = nil

        var loadedImages: [UIImage] = []
        var loadedVideos: [AVAsset] = []
        var loadedPDFs: [PDFKit.PDFDocument] = []
        var tempImageSourceTypes: [Bool] = [] // 临时存储图片来源

        for (index, item) in items.enumerated() {
            do {
                // 🔧 检测媒体类型
                let isVideo = item.supportedContentTypes.contains { contentType in
                    contentType.conforms(to: .movie) ||
                    contentType.conforms(to: .video) ||
                    contentType.identifier.contains("video") ||
                    contentType.identifier.contains("movie")
                }

                let isPDF = item.supportedContentTypes.contains { contentType in
                    contentType.conforms(to: .pdf) ||
                    contentType.identifier.contains("pdf")
                }

                if isPDF {
                    // 📄 处理PDF文档
                    if let pdfDoc = try await item.loadTransferable(type: PDFDocument.self) {
                        if let pdf = PDFKit.PDFDocument(url: pdfDoc.url) {
                            loadedPDFs.append(pdf)

                            // 从PDF提取页面图片
                            let pages = await extractPagesFromPDF(pdf)
                            loadedImages.append(contentsOf: pages)
                            // 标记这些页面为PDF图像
                            for _ in pages {
                                tempImageSourceTypes.append(true) // true表示来自PDF
                            }
                            print("📄 PDF处理完成，提取了\(pages.count)页")
                        }
                    }
                } else if isVideo {
                    // 🎬 处理视频
                    if let movie = try await item.loadTransferable(type: Movie.self) {
                        let asset = AVAsset(url: movie.url)
                        loadedVideos.append(asset)

                        // 从视频提取关键帧
                        let frames = await extractFramesFromVideo(asset)
                        loadedImages.append(contentsOf: frames)
                        // 标记这些帧为普通图像（来自视频）
                        for _ in frames {
                            tempImageSourceTypes.append(false) // false表示不是PDF
                        }
                        print("🎬 视频处理完成，提取了\(frames.count)帧")
                    }
                } else {
                    // 🖼️ 处理图片 - 尝试多种方式加载
                    var imageLoaded = false

                    // 方式1：尝试加载为Data然后转换为UIImage
                    if let data = try? await item.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        loadedImages.append(image)
                        tempImageSourceTypes.append(false) // false表示普通图片
                        imageLoaded = true
                        print("🖼️ 图片加载成功（方式1）")
                    }
                    // 方式2：尝试加载为Data然后转换
                    else if let data = try? await item.loadTransferable(type: Data.self),
                            let image = UIImage(data: data) {
                        loadedImages.append(image)
                        tempImageSourceTypes.append(false) // false表示普通图片
                        imageLoaded = true
                        print("🖼️ 图片加载成功（方式2）")
                    }

                    if !imageLoaded {
                        print("⚠️ 无法加载媒体项目，跳过")
                    }
                }
            } catch {
                print("❌ 加载媒体项目失败: \(error)")
            }

            await MainActor.run {
                processingProgress = Double(index + 1) / Double(items.count) * 0.3
            }
        }

        await MainActor.run {
            selectedImages = loadedImages
            selectedVideos = loadedVideos
            selectedPDFs = loadedPDFs
            imageSourceTypes = tempImageSourceTypes // 设置图片来源标记

            // 计算从不同来源提取的图片
            let videoFrameCount = loadedVideos.count * 3 // 假设每个视频提取3帧
            let pdfPageCount = loadedImages.count - (selectedImages.count - videoFrameCount)

            videoFrames = Array(loadedImages.suffix(videoFrameCount))
            pdfPages = Array(loadedImages.suffix(pdfPageCount))
        }

        await MainActor.run {
            print("📊 媒体加载完成统计:")
            print("   - 选择的项目数: \(items.count)")
            print("   - 加载的图片数: \(loadedImages.count)")
            print("   - 加载的视频数: \(loadedVideos.count)")
            print("   - 加载的PDF数: \(loadedPDFs.count)")
            print("   - 视频帧数: \(videoFrames.count)")
            print("   - PDF页数: \(pdfPages.count)")
        }

        // 🎯 修改：不自动开始处理，只标记准备就绪
        await MainActor.run {
            if !loadedImages.isEmpty {
                imagesReadyForProcessing = true
                isProcessing = false
                print("✅ 图片加载完成，等待用户手动开始分析")
            } else {
                isProcessing = false
                errorMessage = "no_media_loaded".localized
                print("❌ 没有成功加载任何媒体文件")
            }
        }
    }

    // 🎬 加载视频文件（Pro功能专用）
    private func loadVideoItems(from items: [PhotosPickerItem]) async {
        guard subscriptionManager.currentTier.allowsVideoProcessing else {
            print("⚠️ 免费用户尝试加载视频")
            return
        }

        // 清空之前的视频数据
        selectedVideos = []
        videoFrames = []

        await MainActor.run {
            isProcessing = true
            processingProgress = 0
            currentProcessingImage = 0
            processingStatus = "loading_videos".localized
            errorMessage = nil
        }

        var loadedVideos: [AVAsset] = []
        var extractedFrames: [UIImage] = []

        for item in items {
            if let movie = try? await item.loadTransferable(type: Movie.self) {
                let asset = AVAsset(url: movie.url)
                loadedVideos.append(asset)

                // 🎬 使用录屏转长图功能
                let frames = await extractFramesFromVideo(asset)
                extractedFrames.append(contentsOf: frames)
                print("🎬 视频处理完成，生成了\(frames.count)张图片")
            }
        }

        await MainActor.run {
            selectedVideos = loadedVideos
            videoFrames = extractedFrames
            selectedImages.append(contentsOf: extractedFrames)

            // 标记这些图片来自视频
            for _ in extractedFrames {
                imageSourceTypes.append(false) // false表示不是PDF
            }

            // 🎯 标记准备就绪，不自动开始处理
            if !extractedFrames.isEmpty {
                imagesReadyForProcessing = true
                isProcessing = false
            }

            print("🎬 视频加载完成: \(loadedVideos.count)个视频，生成\(extractedFrames.count)张图片")
        }

        // 🎯 修改：不自动开始处理，只标记准备就绪
        if !extractedFrames.isEmpty {
            // 已经在上面设置了 imagesReadyForProcessing = true
            print("✅ 视频处理完成，等待用户手动开始分析")
        } else {
            await MainActor.run {
                isProcessing = false
                errorMessage = "no_videos_processed".localized
                print("❌ 没有成功处理任何视频文件")
            }
        }
    }

    // 从视频中提取帧并进行智能处理
    private func extractFramesFromVideo(_ asset: AVAsset) async -> [UIImage] {
        // 🎯 统一处理：所有视频都使用VideoToLongImageProcessor进行智能帧提取和去重
        print("🎬 开始视频帧提取和智能处理")
        return await processVideoWithIntelligentFrameExtraction(asset)
    }

    // 🎯 统一的视频智能处理方法
    private func processVideoWithIntelligentFrameExtraction(_ asset: AVAsset) async -> [UIImage] {
        let processor = VideoToLongImageProcessor()

        let result = await processor.processVideoToLongImage(asset, appType: selectedAppType) { status, progress in
            Task { @MainActor in
                processingStatus = status
                processingProgress = progress * 0.5 // 视频处理占总进度的50%
            }
        }

        if result.success {
            // 🎯 直接返回处理后的帧，已经过30帧阈值去重
            print("✅ 视频智能处理成功:")
            print("   📊 原始帧数: \(result.originalFrameCount)")
            print("   🔄 重复帧数: \(result.duplicateFrameCount)")
            print("   ✅ 保留帧数: \(result.uniqueFrameCount)")
            print("   💾 最终图片数: \(result.savedFrameURLs.count)")

            // 从保存的URL加载图片
            var finalImages: [UIImage] = []
            for url in result.savedFrameURLs {
                if let image = UIImage(contentsOfFile: url.path) {
                    finalImages.append(image)
                }
            }

            print("🎬 视频处理完成，生成了\(finalImages.count)张图片")
            return finalImages

        } else {
            print("❌ 视频智能处理失败: \(result.errorMessage ?? "未知错误")")
            print("🔄 降级到简单帧提取")
            return await extractKeyFrames(asset)
        }
    }

    // 检测是否为录屏视频
    private func isScreenRecordingVideo(_ asset: AVAsset) async -> Bool {
        do {
            let duration = try await asset.load(.duration)
            let durationSeconds = CMTimeGetSeconds(duration)

            // 🎯 更严格的录屏视频检测逻辑
            // 录屏视频特征：
            // 1. 时长通常在10秒到90秒之间（滚动操作）
            // 2. 需要检查分辨率是否为手机屏幕分辨率
            // 3. 帧率通常较低（录屏时系统优化）

            guard durationSeconds > 10 && durationSeconds < 90 else {
                print("🎬 视频时长\(String(format: "%.1f", durationSeconds))秒，不符合录屏特征")
                return false
            }

            // 检查视频轨道信息
            let tracks = try await asset.loadTracks(withMediaType: .video)
            guard let videoTrack = tracks.first else {
                print("⚠️ 无法获取视频轨道信息")
                return false
            }

            let naturalSize = try await videoTrack.load(.naturalSize)
            let frameRate = try await videoTrack.load(.nominalFrameRate)

            // 常见手机屏幕分辨率检查
            let commonScreenSizes: [CGSize] = [
                CGSize(width: 1170, height: 2532), // iPhone 13 Pro
                CGSize(width: 1080, height: 2340), // iPhone 12 mini
                CGSize(width: 1284, height: 2778), // iPhone 13 Pro Max
                CGSize(width: 828, height: 1792),  // iPhone 11
                CGSize(width: 1125, height: 2436), // iPhone X/XS
                CGSize(width: 750, height: 1334),  // iPhone 8
                CGSize(width: 1080, height: 1920), // 常见Android
                CGSize(width: 1440, height: 2560), // 高端Android
            ]

            let isScreenSize = commonScreenSizes.contains { screenSize in
                (abs(naturalSize.width - screenSize.width) < 50 && abs(naturalSize.height - screenSize.height) < 50) ||
                (abs(naturalSize.width - screenSize.height) < 50 && abs(naturalSize.height - screenSize.width) < 50) // 横竖屏
            }

            // 录屏视频通常帧率较低（15-30fps）
            let isLowFrameRate = frameRate <= 30

            let isScreenRecording = isScreenSize && isLowFrameRate

            print("🎬 视频分析: \(String(format: "%.0fx%.0f", naturalSize.width, naturalSize.height)), \(String(format: "%.1f", frameRate))fps, \(String(format: "%.1f", durationSeconds))s")
            print("🎬 录屏检测: 屏幕尺寸=\(isScreenSize), 低帧率=\(isLowFrameRate) → \(isScreenRecording ? "录屏视频" : "普通视频")")

            return isScreenRecording

        } catch {
            print("⚠️ 无法获取视频信息: \(error)")
        }

        return false
    }

    // 处理录屏视频 - 直接提取帧，跳过长图转换
    private func processScreenRecordingToLongImage(_ asset: AVAsset) async -> [UIImage] {
        let processor = VideoToLongImageProcessor()

        let result = await processor.processVideoToLongImage(asset, appType: selectedAppType) { status, progress in
            Task { @MainActor in
                processingStatus = status
                processingProgress = progress * 0.5 // 视频处理占总进度的50%
            }
        }

        if result.success {
            // 🎯 新策略：直接返回去重后的帧，不再转换为长图
            print("✅ 视频帧处理成功:")
            print("   📊 原始帧数: \(result.originalFrameCount)")
            print("   🔄 重复帧数: \(result.duplicateFrameCount)")
            print("   ✅ 保留帧数: \(result.uniqueFrameCount)")
            print("   💾 保存帧数: \(result.savedFrameURLs.count)")

            // 打印保存的帧文件路径，方便用户检查
            if !result.savedFrameURLs.isEmpty {
                print("📁 保存的帧文件（按原始编号排序）:")
                let sortedFrames = result.uniqueFrames.sorted { $0.originalIndex < $1.originalIndex }
                for (index, numberedFrame) in sortedFrames.enumerated() {
                    if index < result.savedFrameURLs.count {
                        let url = result.savedFrameURLs[index]
                        print("   原始帧#\(numberedFrame.originalIndex): \(url.lastPathComponent) (时间戳: \(String(format: "%.1f", numberedFrame.timestamp))s)")
                    }
                }
                print("📂 文件夹路径: \(result.savedFrameURLs.first?.deletingLastPathComponent().path ?? "")")
                print("🔢 显示顺序: \(sortedFrames.map { $0.originalIndex })")
            }

            // 🎯 提取图片并按原始编号排序，同时保存原始编号信息
            let sortedFrames = result.uniqueFrames.sorted { $0.originalIndex < $1.originalIndex }

            // 🎯 保存原始帧编号到状态变量
            await MainActor.run {
                originalFrameIndices = sortedFrames.map { $0.originalIndex }
            }

            return sortedFrames.map { $0.image }
        } else {
            print("⚠️ 视频帧处理失败: \(result.errorMessage ?? "未知错误")，降级到关键帧提取")
            return await extractKeyFrames(asset)
        }
    }

    // 提取关键帧（密集提取版本）
    private func extractKeyFrames(_ asset: AVAsset) async -> [UIImage] {
        let generator = AVAssetImageGenerator(asset: asset)
        generator.appliesPreferredTrackTransform = true
        generator.requestedTimeToleranceBefore = .zero
        generator.requestedTimeToleranceAfter = .zero

        // 🎯 高分辨率优化：确保文字和数字清晰识别
        generator.maximumSize = CGSize(width: 1500, height: 2000)
        generator.apertureMode = .cleanAperture
        generator.videoComposition = nil

        var frames: [UIImage] = []

        do {
            let duration = try await asset.load(.duration)
            let durationSeconds = CMTimeGetSeconds(duration)

            print("🎬 普通视频密集帧提取: 总时长 \(String(format: "%.1f", durationSeconds))秒")

            // 🎯 新逻辑：密集提取而不是只有3个关键帧
            let frameInterval: Double = 2.0  // 每2秒提取一帧

            var currentTime: Double = 0.5  // 从0.5秒开始，避免黑屏
            var frameCount = 0

            while currentTime < durationSeconds {
                let time = CMTime(seconds: currentTime, preferredTimescale: 600)

                do {
                    let cgImage = try await generator.image(at: time).image
                    let uiImage = UIImage(cgImage: cgImage)
                    frames.append(uiImage)
                    frameCount += 1
                    print("📸 提取帧#\(frameCount) at \(String(format: "%.1f", currentTime))s")
                } catch {
                    print("⚠️ 提取帧失败 at \(String(format: "%.1f", currentTime))s: \(error)")
                }

                currentTime += frameInterval
            }

            // 🎯 确保至少有几个关键帧（如果密集提取失败）
            if frames.isEmpty {
                print("⚠️ 密集提取失败，降级到关键帧提取")
                let keyTimes = [
                    CMTime(seconds: durationSeconds * 0.1, preferredTimescale: 600), // 10%
                    CMTime(seconds: durationSeconds * 0.3, preferredTimescale: 600), // 30%
                    CMTime(seconds: durationSeconds * 0.5, preferredTimescale: 600), // 50%
                    CMTime(seconds: durationSeconds * 0.7, preferredTimescale: 600), // 70%
                    CMTime(seconds: durationSeconds * 0.9, preferredTimescale: 600)  // 90%
                ]

                for time in keyTimes {
                    do {
                        let cgImage = try await generator.image(at: time).image
                        let uiImage = UIImage(cgImage: cgImage)
                        frames.append(uiImage)
                    } catch {
                        print("⚠️ 关键帧提取失败 at \(time): \(error)")
                    }
                }
            }

            print("🎬 普通视频帧提取完成: 共提取\(frames.count)帧")

        } catch {
            print("❌ 获取视频时长失败: \(error)")
        }

        return frames
    }

    // 从PDF中提取页面图片
    private func extractPagesFromPDF(_ pdf: PDFKit.PDFDocument) async -> [UIImage] {
        var pages: [UIImage] = []
        let pageCount = pdf.pageCount

        print("📄 开始处理PDF，共\(pageCount)页")

        // 限制最大页数，避免处理过大的PDF
        let maxPages = min(pageCount, 20) // 最多处理20页

        for pageIndex in 0..<maxPages {
            if let page = pdf.page(at: pageIndex) {
                // 智能调整渲染尺寸，避免图片过大
                let pageRect = page.bounds(for: .mediaBox)
                let maxDimension: CGFloat = 6144 // 大幅增加最大尺寸以支持超长PDF（160个地址单页）
                let scale: CGFloat

                if max(pageRect.width, pageRect.height) > maxDimension {
                    scale = maxDimension / max(pageRect.width, pageRect.height)
                } else {
                    scale = 2.0 // 默认2倍分辨率
                }

                let scaledSize = CGSize(
                    width: pageRect.width * scale,
                    height: pageRect.height * scale
                )

                print("📄 页面\(pageIndex + 1)尺寸: \(pageRect.width)x\(pageRect.height) -> \(scaledSize.width)x\(scaledSize.height)")

                // 渲染PDF页面为图片
                let renderer = UIGraphicsImageRenderer(size: scaledSize)
                let image = renderer.image { context in
                    // 设置白色背景
                    UIColor.white.set()
                    context.fill(CGRect(origin: .zero, size: scaledSize))

                    // 缩放上下文
                    context.cgContext.scaleBy(x: scale, y: scale)

                    // 渲染PDF页面
                    page.draw(with: .mediaBox, to: context.cgContext)
                }

                // 进一步压缩图片以避免AI服务限制
                if let compressedImage = compressImageForAI(image) {
                    pages.append(compressedImage)
                    print("📄 已处理第\(pageIndex + 1)页，压缩后尺寸: \(compressedImage.size)")
                } else {
                    pages.append(image)
                    print("📄 已处理第\(pageIndex + 1)页（未压缩）")
                }
            }
        }

        if pageCount > maxPages {
            print("⚠️ PDF页数过多(\(pageCount)页)，只处理前\(maxPages)页")
        }

        return pages
    }

    // 压缩图片以适应AI服务限制
    private func compressImageForAI(_ image: UIImage) -> UIImage? {
        let maxFileSize: Int = 4 * 1024 * 1024 // 4MB限制
        let maxDimension: CGFloat = 6144 // 大幅增加最大尺寸以支持超长PDF（160个地址单页）

        var currentImage = image

        // 首先调整尺寸
        if max(image.size.width, image.size.height) > maxDimension {
            let scale = maxDimension / max(image.size.width, image.size.height)
            let newSize = CGSize(
                width: image.size.width * scale,
                height: image.size.height * scale
            )

            let renderer = UIGraphicsImageRenderer(size: newSize)
            currentImage = renderer.image { _ in
                image.draw(in: CGRect(origin: .zero, size: newSize))
            }
        }

        // 然后调整压缩质量
        var compressionQuality: CGFloat = 0.8
        var imageData = currentImage.jpegData(compressionQuality: compressionQuality)

        while let data = imageData, data.count > maxFileSize && compressionQuality > 0.1 {
            compressionQuality -= 0.1
            imageData = currentImage.jpegData(compressionQuality: compressionQuality)
        }

        if let finalData = imageData, let finalImage = UIImage(data: finalData) {
            print("📄 图片压缩完成: \(finalData.count / 1024)KB, 质量: \(Int(compressionQuality * 100))%")
            return finalImage
        }

        return currentImage
    }

    // 从PDF中提取文本内容（用于AI处理）
    private func extractTextFromPDF(_ pdf: PDFKit.PDFDocument) async -> String {
        var extractedText = ""
        let pageCount = pdf.pageCount

        print("📄 开始从PDF提取文本，共\(pageCount)页")

        // 限制最大页数，避免处理过大的PDF
        let maxPages = min(pageCount, 20) // 最多处理20页

        for pageIndex in 0..<maxPages {
            if let page = pdf.page(at: pageIndex) {
                if let pageText = page.string {
                    // 清理和验证文本内容
                    let cleanedText = pageText.trimmingCharacters(in: .whitespacesAndNewlines)
                    if !cleanedText.isEmpty {
                        extractedText += cleanedText + "\n\n"
                        print("📄 已提取第\(pageIndex + 1)页文本，长度: \(cleanedText.count)字符")
                    }
                }
            }
        }

        if pageCount > maxPages {
            print("⚠️ PDF页数过多(\(pageCount)页)，只处理前\(maxPages)页文本")
        }

        // 检查文本质量
        let finalText = extractedText.trimmingCharacters(in: .whitespacesAndNewlines)
        print("📄 PDF文本提取完成，总长度: \(finalText.count)字符")

        // 如果文本太短，可能是扫描PDF
        if finalText.count < 50 {
            print("⚠️ PDF文本内容过少，可能是扫描生成的图像PDF")
        }

        return finalText
    }

    // 🚀 使用Firebase AI原生PDF处理
    private func processPDFNativelyWithAI(_ pdfData: Data, fileName: String) async {
        print("📄 开始Firebase AI原生PDF处理: \(fileName)")

        await MainActor.run {
            processingStatus = "processing_pdf_document".localized + ": \(fileName)"
        }

        do {
            let result = try await HybridAIService.shared.extractAddressesFromPDFNatively(pdfData, appType: selectedAppType)

            if result.success && !result.addresses.isEmpty {
                print("✅ PDF原生AI识别成功: \(result.addresses.count)个地址")

                await MainActor.run {
                    let newAddresses = result.addresses.map { address in
                        let coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0) // 临时坐标，后续会地理编码
                        return (address, coordinate, true, false, result.confidence) // 地址, 坐标, 选中, 有效坐标, 置信度
                    }
                    recognizedAddresses.append(contentsOf: newAddresses)
                    print("📄 PDF原生AI处理完成: \(fileName)，识别了\(newAddresses.count)个地址")
                }
            } else {
                print("⚠️ PDF原生AI识别失败，结果为空")
                await MainActor.run {
                    errorMessage = "pdf_native_ai_failed".localized + ": \(fileName)"
                }
            }
        } catch {
            print("❌ PDF原生智能处理失败: \(error.localizedDescription)")
            await MainActor.run {
                errorMessage = "pdf_native_processing_error".localized + ": \(error.localizedDescription)"
            }
        }
    }

    // 删除图片
    private func removeImage(at index: Int) {
        guard index < selectedImages.count else { return }
        selectedImages.remove(at: index)

        // 🎯 同步删除原始帧编号
        if index < originalFrameIndices.count {
            let removedFrameIndex = originalFrameIndices.remove(at: index)
            print("🗑️ 删除图片及其原始帧编号: 帧#\(removedFrameIndex)")
        }

        // 🎯 同步删除图片来源类型标记
        if index < imageSourceTypes.count {
            imageSourceTypes.remove(at: index)
        }

        // 如果删除后没有图片了，清空识别结果
        if selectedImages.isEmpty {
            recognizedAddresses = []
            originalFrameIndices = [] // 🎯 清空原始帧编号
        }
    }

    // 处理图片 - 使用混合识别服务（智能频率控制）
    private func processImages() async {
        guard !selectedImages.isEmpty else {
            isProcessing = false
            return
        }

        // 移除复杂的总数输入检查 - 保持简洁的用户体验

        // 🔒 强化防重复机制：检查是否已经在处理中
        let (shouldProceed, currentTaskID) = await MainActor.run {
            // 如果已经在处理中，跳过重复调用
            if isProcessing && processingTaskID != nil {
                Logger.aiWarning("⚠️ 图片处理已在进行中，跳过重复调用 (当前任务: \(processingTaskID?.uuidString ?? "nil"))")
                return (false, processingTaskID)
            }

            // 如果有临时任务ID（从按钮设置），使用它；否则创建新的
            let finalTaskID = processingTaskID ?? UUID()

            // 设置处理状态
            isProcessing = true
            processingTaskID = finalTaskID
            processingProgress = 0.0
            currentProcessingImage = 0
            processingStatus = "analyzing_images".localized
            // 🔒 清空之前的识别结果，避免累积重复
            recognizedAddresses = []

            Logger.aiInfo("🔒 开始图片处理，任务ID: \(finalTaskID.uuidString)")
            return (true, finalTaskID)
        }

        guard shouldProceed else { return }

        // 给用户一个明确的开始提示
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒，让用户看到开始状态

        // 🚀 SpeedX风格优化：大批量处理预优化
        if selectedImages.count > 50 {
            Logger.aiInfo("🚀 检测到大批量处理(\(selectedImages.count)张图片)，启用SpeedX优化")
            await optimizeForLargeBatch()
        }

        // 🚀 智能批量处理：优化大批量图片处理
        let batchSize = determineBatchSize(imageCount: selectedImages.count)

        // 🎯 用于跟踪全局序号的变量（暂时保留，后续通过reassignSortNumbers统一处理）
        _ = 1 // 移除未使用的变量

        for batchIndex in stride(from: 0, to: selectedImages.count, by: batchSize) {
            let endIndex = min(batchIndex + batchSize, selectedImages.count)
            let batch = Array(selectedImages[batchIndex..<endIndex])

            await MainActor.run {
                let currentBatch = batchIndex / batchSize + 1
                let totalBatches = (selectedImages.count + batchSize - 1) / batchSize
                processingStatus = String(format: "processing_batch_progress".localized, currentBatch, totalBatches)
                processingProgress = Double(batchIndex) / Double(selectedImages.count)
                currentProcessingImage = batchIndex + 1
            }

            // 并行处理当前批次
            await processBatchImages(batch, startIndex: batchIndex)

            // 🚀 智能批次间延迟：根据批次大小和API使用情况动态调整
            if endIndex < selectedImages.count {
                await MainActor.run {
                    processingStatus = "waiting_between_batches".localized
                }

                // 🎯 动态延迟：小批次短延迟，大批次长延迟
                let delaySeconds = calculateOptimalBatchDelay(batchSize: batchSize, remainingImages: selectedImages.count - endIndex)
                Logger.aiInfo("⏱️ 批次间智能延迟: \(delaySeconds)秒")
                try? await Task.sleep(nanoseconds: UInt64(delaySeconds * 1_000_000_000))
            }
        }

        await MainActor.run {
            processingProgress = 1.0
            isProcessing = false
            processingTaskID = nil  // 🔒 清除任务ID
            processingStatus = "processing_complete".localized
            currentProcessingImage = selectedImages.count

            // ⏱️ 记录分析结束时间并计算耗时
            if let startTime = analysisStartTime {
                analysisEndTime = Date()
                let duration = analysisEndTime!.timeIntervalSince(startTime)
                Logger.aiInfo("⏱️ 图片分析处理完成，总耗时: \(String(format: "%.2f", duration))秒")
                Logger.aiInfo("⏱️ 处理了\(selectedImages.count)张图片，平均每张: \(String(format: "%.2f", duration / Double(selectedImages.count)))秒")

                // 🚀 性能优化效果评估
                let expectedTimeWithoutOptimization = Double(selectedImages.count) * 10.38 // 优化前的平均时间
                let timeSaved = expectedTimeWithoutOptimization - duration
                let improvementPercentage = (timeSaved / expectedTimeWithoutOptimization) * 100

                if timeSaved > 0 {
                    Logger.aiInfo("🚀 性能优化效果: 节省 \(String(format: "%.2f", timeSaved))秒 (提升\(String(format: "%.1f", improvementPercentage))%)")
                }

                // 🎯 详细的性能统计
                print("📊 图片分析性能统计:")
                print("   📸 图片数量: \(selectedImages.count)")
                print("   ⏱️ 总耗时: \(String(format: "%.2f", duration))秒")
                print("   📈 平均每张: \(String(format: "%.2f", duration / Double(selectedImages.count)))秒")
                print("   🎯 识别地址数: \(recognizedAddresses.count)")
                if !recognizedAddresses.isEmpty {
                    print("   📍 平均每个地址: \(String(format: "%.2f", duration / Double(recognizedAddresses.count)))秒")
                }
            }

            Logger.aiInfo("🔒 图片处理完成，任务ID: \(currentTaskID?.uuidString ?? "unknown")")

            // 🎯 重新分配连续的序号，解决多张图片序号重复问题
            reassignSortNumbers()

            // 🚨 检查第三方排序号重复
            checkForDuplicateThirdPartySortNumbers()

            // 🚨 SpeedX专用：检查地址重复问题
            if selectedAppType == .speedx {
                checkForDuplicateAddresses()
                // 移除复杂的序号连续性检查 - 保持在route bottom sheet中提示
            }

            // 🎯 混合识别完成，不需要检查网络错误
            // AI识别的地址使用临时坐标是正常的，不是错误
        }

        // 🤖 检查是否有问题地址需要AI修复
        await checkAndTriggerSmartRetry()

        // 移除复杂的补充截图模式处理 - 保持简洁的用户体验
    }

    // MARK: - 图片处理优化

    // 🚀 智能批量大小确定 - 支持所有快递优化，支持超大批量
    private func determineBatchSize(imageCount: Int) -> Int {
        // 🎯 主要快递统一优化：固定5张图片同步处理，提升并发效果
        if selectedAppType == .gofo || selectedAppType == .amazonFlex ||
           selectedAppType == .imile || selectedAppType == .ldsEpod ||
           selectedAppType == .piggy || selectedAppType == .uniuni ||
           selectedAppType == .ywe {
            return 5 // 主要快递固定5张图片批处理，统一并发性能
        }

        // SpeedX和其他应用的标准批量策略
        if imageCount <= 10 {
            return 1 // 少量图片，逐个处理
        } else if imageCount <= 30 {
            return 3 // 中等数量，小批次处理
        } else if imageCount <= 60 {
            return 5 // 大量图片，较大批次处理
        } else if imageCount <= 120 {
            return 8 // 超大批量，如SpeedX的160个地址场景
        } else {
            return 10 // 极大批量，最大并行度
        }
    }

    // 🚀 批量处理图片 - SpeedX优化版本，支持大批量处理
    private func processBatchImages(_ batch: [UIImage], startIndex: Int) async {
        Logger.aiInfo("🚀 开始处理批次，起始索引: \(startIndex), 图片数量: \(batch.count)")

        await MainActor.run {
            processingStatus = "analyzing_image_content".localized
            processingProgress = Double(startIndex) / Double(selectedImages.count)
        }

        // 🏠 预热地址库缓存，提高后续处理速度
        await UserAddressDatabase.shared.updateDatabaseStats()

        // 🚦 检查是否需要暂停处理
        if await GlobalGeocodingRateLimiter.shared.shouldPauseBatchProcessing() {
            Logger.aiWarning("🚦 API使用率过高，暂停批量处理30秒")
            try? await Task.sleep(nanoseconds: 30_000_000_000)
        }

        // 🚀 主要快递并发优化：5张图片同步处理
        if (selectedAppType == .gofo || selectedAppType == .amazonFlex ||
            selectedAppType == .imile || selectedAppType == .ldsEpod ||
            selectedAppType == .piggy || selectedAppType == .uniuni ||
            selectedAppType == .ywe) && batch.count <= 5 {
            Logger.aiInfo("🚀 \(selectedAppType.displayName)超级并发模式：同时处理\(batch.count)张图片（激进压缩+5张并发）")

            await withTaskGroup(of: Void.self) { group in
                for (index, image) in batch.enumerated() {
                    let globalIndex = startIndex + index

                    group.addTask {
                        // 更新进度提示
                        await MainActor.run {
                            self.currentProcessingImage = globalIndex + 1
                            self.processingProgress = Double(globalIndex) / Double(self.selectedImages.count)
                            self.processingStatus = String(format: "processing_image_progress".localized, globalIndex + 1, self.selectedImages.count)
                        }

                        // 检查这个图片是否来自PDF
                        let isPDFImage = await MainActor.run {
                            globalIndex < self.imageSourceTypes.count ? self.imageSourceTypes[globalIndex] : false
                        }

                        // 🚀 图片预处理优化：压缩图片以提升AI处理速度
                        let optimizedImage = await MainActor.run { self.optimizeImageForAI(image) }

                        // 🎯 并发处理图片，使用线程安全的方式更更新数组
                        await self.processImageWithOCRPlusAI(optimizedImage, imageIndex: globalIndex, isPDFImage: isPDFImage)

                        let totalImages = await MainActor.run { self.selectedImages.count }
                        Logger.aiInfo("✅ 图片 \(globalIndex + 1)/\(totalImages) 处理完成")
                    }
                }
            }
        } else {
            // 🔧 其他应用或大批次：串行处理避免竞态条件
            for (index, image) in batch.enumerated() {
                let globalIndex = startIndex + index

                // 更新进度提示
                await MainActor.run {
                    self.currentProcessingImage = globalIndex + 1
                    self.processingProgress = Double(globalIndex) / Double(self.selectedImages.count)
                    self.processingStatus = String(format: "processing_image_progress".localized, globalIndex + 1, self.selectedImages.count)
                }

                // 检查这个图片是否来自PDF
                let isPDFImage = await MainActor.run {
                    globalIndex < self.imageSourceTypes.count ? self.imageSourceTypes[globalIndex] : false
                }

                // 🚀 图片预处理优化：压缩图片以提升AI处理速度
                let optimizedImage = optimizeImageForAI(image)

                // 🎯 串行处理每张图片，避免并发修改recognizedAddresses数组
                await self.processImageWithOCRPlusAI(optimizedImage, imageIndex: globalIndex, isPDFImage: isPDFImage)

                Logger.aiInfo("✅ 图片 \(globalIndex + 1)/\(self.selectedImages.count) 处理完成")
            }
        }

        // 🧹 批次处理完成后，触发内存清理
        await MainActor.run {
            let processedCount = startIndex + batch.count
            processingProgress = 0.3 + (Double(processedCount) / Double(selectedImages.count)) * 0.7
            currentProcessingImage = processedCount

            // 强制垃圾回收，释放已处理图片的内存
            autoreleasepool {
                // 内存清理在autoreleasepool中自动进行
            }
        }

        Logger.aiInfo("✅ 批次处理完成，起始索引: \(startIndex)")
    }

    // 🎯 新增：使用OCR + AI组合处理图片 - 优化版本：OCR先出结果
    private func processImageWithOCRPlusAI(_ image: UIImage, imageIndex: Int, isPDFImage: Bool = false) async {
        await MainActor.run {
            isUsingAI = true
            processingStatus = "extracting_ocr_text".localized
        }

        do {
            // 🎯 检查用户的识别模式设置（在外层作用域声明）
            let userMode = aiConfig.getOptimalRecognitionMode()

            // 🚀 智能检测超长图片 - 根据用户设置决定是否跳过OCR
            if shouldSplitImage(image) {
                Logger.aiInfo("🔄 检测到超长图片 (\(image.size.width)x\(image.size.height))")

                // 🎯 统一AI Only模式：所有主要快递都跳过OCR，直接使用智能切割+AI
                if userMode == .aiOnly || selectedAppType.shouldUseAIOnly {
                    let reason: String
                    switch selectedAppType {
                    case .speedx:
                        reason = "SpeedX：跳过OCR，直接使用纯AI智能切割以确保停靠点号码准确"
                    case .gofo:
                        reason = "GoFo：跳过OCR，直接使用纯AI智能切割以避免地图OCR问题"
                    case .amazonFlex:
                        reason = "Amazon Flex：跳过OCR，直接使用纯AI智能切割以确保时间段识别"
                    case .imile:
                        reason = "iMile：跳过OCR，直接使用纯AI智能切割以支持多地区格式"
                    case .ldsEpod, .piggy, .uniuni, .ywe:
                        reason = "\(selectedAppType.displayName)：跳过OCR，直接使用纯AI智能切割以确保追踪号准确"
                    default:
                        reason = "用户选择AI Only模式：跳过OCR，直接使用纯AI智能切割"
                    }

                    Logger.aiInfo("🚀 \(reason)")

                    // 🎯 使用纯AI分割方法，不使用OCR
                    await processImageWithFirebaseAISplitting(image, imageIndex: imageIndex)
                    return
                }

                Logger.aiInfo("🔄 其他模式：优先尝试整图OCR")

                // 🎯 优化：先尝试整图OCR，成功则无需分割
                do {
                    Logger.aiInfo("🔍 尝试整图OCR识别...")

                    // 🔧 对于超大图片，可能需要适当压缩以避免内存问题
                    let processedImage: UIImage
                    let imageSize = image.size.width * image.size.height
                    if imageSize > 50_000_000 { // 超过5000万像素
                        Logger.aiInfo("🔧 图片过大(\(Int(imageSize/1_000_000))M像素)，适当压缩以优化OCR")
                        processedImage = image.resized(to: CGSize(width: image.size.width * 0.8, height: image.size.height * 0.8)) ?? image
                    } else {
                        processedImage = image
                    }

                    // 🔍 使用标准OCR处理
                    let ocrResponse = try await ocrService.recognizeText(from: processedImage)

                    // 检查OCR是否识别到足够的文本
                    if ocrResponse.results.count > 0 && ocrResponse.confidence > 0.1 {
                        Logger.aiInfo("✅ 整图OCR成功: \(ocrResponse.results.count)个文本块, 置信度: \(Int(ocrResponse.confidence * 100))%")

                        await MainActor.run {
                            processingStatus = "intelligent_text_analysis".localized
                            ocrText = ocrResponse.fullText
                        }

                        // 使用AI处理完整的OCR文本
                        let formattedText = ocrService.formatTextForAI(ocrResponse)
                        let aiResult = try await firebaseAIService.extractAddressesFromOCRText(formattedText, appType: selectedAppType)

                        await MainActor.run {
                            aiModelUsed = "OCR + \(aiResult.modelUsed)"
                            aiConfidence = (Double(ocrResponse.confidence) + aiResult.confidence) / 2.0
                            recognitionMethod = "Whole Image OCR + AI"
                            processingStatus = "address_verification".localized
                        }

                        Logger.aiInfo("✅ 整图OCR+AI处理完成: \(aiResult.addresses.count)个地址")

                        // 保存识别结果
                        await saveOCRPlusAIResults(aiResult.addresses, imageIndex: imageIndex)
                        return

                    } else {
                        Logger.aiInfo("⚠️ 整图OCR文本不足，降级到分割处理")
                    }

                } catch {
                    Logger.aiInfo("⚠️ 整图OCR失败，降级到分割处理: \(error)")
                }

                // 降级到分割处理
                Logger.aiInfo("🔄 使用分割方案处理超长图片")

                // 🎯 根据用户模式选择分割方法
                if userMode == .aiOnly {
                    Logger.aiInfo("🚀 AI Only模式：使用纯AI分割处理")
                    await processImageWithFirebaseAISplitting(image, imageIndex: imageIndex)
                } else {
                    Logger.aiInfo("🔄 使用OCR+AI分割处理")
                    await processImageWithOCRPlusAISplitting(image, imageIndex: imageIndex, isPDFImage: isPDFImage)
                }
                return
            }

            // 🎯 统一AI Only模式：所有主要快递都跳过OCR，直接使用AI识别
            if userMode == .aiOnly || selectedAppType.shouldUseAIOnly {
                let reason: String
                switch selectedAppType {
                case .speedx:
                    reason = "SpeedX：跳过OCR，直接使用纯AI识别以确保停靠点号码准确"
                case .gofo:
                    reason = "GoFo：跳过OCR，直接使用纯AI识别以避免地图OCR问题"
                case .amazonFlex:
                    reason = "Amazon Flex：跳过OCR，直接使用纯AI识别以确保时间段识别"
                case .imile:
                    reason = "iMile：跳过OCR，直接使用纯AI识别以支持多地区格式"
                case .ldsEpod, .piggy, .uniuni, .ywe:
                    reason = "\(selectedAppType.displayName)：跳过OCR，直接使用纯AI识别以确保追踪号准确"
                default:
                    reason = "用户选择AI Only模式：跳过OCR，直接使用AI识别以确保准确性"
                }

                Logger.aiInfo("🚀 \(reason)")

                // 直接使用AI处理图片
                await MainActor.run {
                    processingStatus = "intelligent_analysis".localized
                    recognitionMethod = "Direct AI Recognition"
                }

                let aiResult = try await firebaseAIService.extractAddressesFromImage(image, appType: selectedAppType, isPDFImage: isPDFImage)

                await MainActor.run {
                    aiModelUsed = aiResult.modelUsed
                    aiConfidence = aiResult.confidence
                    processingStatus = "address_verification".localized
                }

                Logger.aiInfo("✅ 直接AI识别完成: \(aiResult.addresses.count)个地址")

                // 保存识别结果
                await saveOCRPlusAIResults(aiResult.addresses, imageIndex: imageIndex)
                return
            }

            // 第一步：使用OCR提取文本
            Logger.aiInfo("🔍 开始OCR文本提取")
            let ocrResponse = try await ocrService.recognizeText(from: image)

            await MainActor.run {
                processingStatus = "analyzing_image_content".localized
                ocrText = ocrResponse.fullText
            }

            // 📝 记录OCR提取的文本信息
            Logger.aiInfo("📝 OCR文本长度: \(ocrResponse.fullText.count)字符")
            Logger.aiInfo("📝 OCR文本预览: \(String(ocrResponse.fullText.prefix(200)))...")

            // 第二步：将OCR文本传给AI进行结构化处理
            Logger.aiInfo("🤖 开始AI语义分析，OCR置信度: \(Int(ocrResponse.confidence * 100))%")
            let formattedText = ocrService.formatTextForAI(ocrResponse)

            // 使用Firebase AI处理OCR文本
            let aiResult = try await firebaseAIService.extractAddressesFromOCRText(formattedText, appType: selectedAppType)

            await MainActor.run {
                aiModelUsed = "OCR + \(aiResult.modelUsed)"
                aiConfidence = (Double(ocrResponse.confidence) + aiResult.confidence) / 2.0  // 综合置信度
                recognitionMethod = "OCR + AI Analysis"
                processingStatus = "address_verification".localized
            }

            Logger.aiInfo("✅ OCR+AI处理完成: \(aiResult.addresses.count)个地址, 综合置信度: \(Int(aiConfidence * 100))%")

            // 保存识别结果
            await saveOCRPlusAIResults(aiResult.addresses, imageIndex: imageIndex)

        } catch {
            Logger.aiError("❌ OCR+AI处理失败: \(error)")

            // 🔧 检查是否是可以自动处理的错误（如图片过大）
            let shouldShowError = !isAutoRecoverableError(error)

            if shouldShowError {
                await MainActor.run {
                    errorMessage = "OCR+AI处理失败: \(error.localizedDescription)"
                }
            } else {
                Logger.aiInfo("🔄 检测到可自动恢复的错误，不显示用户提示，直接降级处理")
            }

            // 降级到传统混合服务
            Logger.aiInfo("🔄 降级到传统混合服务处理")
            await processImageWithHybridService(image, imageIndex: imageIndex, isPDFImage: isPDFImage)
        }
    }

    // 检查是否是可以自动恢复的错误
    internal func isAutoRecoverableError(_ error: Error) -> Bool {
        // 检查是否是GemmaError中的可自动恢复错误
        if let gemmaError = error as? GemmaError {
            switch gemmaError {
            case .imageTooLarge:
                return true  // 图片过大可以自动降级到混合服务
            case .networkError:
                return false // 网络错误需要用户知道
            case .rateLimitExceeded:
                return false // 频率限制需要用户知道
            case .allGemmaModelsFailed:
                return false // 所有模型失败需要用户知道
            case .geographicRestriction:
                return false // 地理限制需要用户知道
            case .imageProcessingFailed:
                return false // 图片处理失败需要用户知道
            case .apiError(_):
                return false // API错误需要用户知道
            case .jsonParsingFailed:
                return false // JSON解析失败需要用户知道
            case .duplicateProcessing:
                return false // 重复处理需要用户知道
            }
        }

        // 检查是否是Firebase AI的图片过大错误
        if let nsError = error as NSError? {
            // Firebase AI错误0通常是图片过大
            if nsError.domain.contains("Firebase") && nsError.code == 0 {
                return true
            }
            // POSIX错误40是"Message too long"，通常是图片过大
            if nsError.domain == NSPOSIXErrorDomain && nsError.code == 40 {
                return true
            }
        }

        return false
    }

    // 使用混合服务处理图片
    private func processImageWithHybridService(_ image: UIImage, imageIndex: Int, isPDFImage: Bool = false) async {
        await MainActor.run {
            isUsingAI = true
        }

        do {
            // 🚀 智能检测超长图片并自动分割
            if shouldSplitImage(image) {
                Logger.aiInfo("🔄 检测到超长图片 (\(image.size.width)x\(image.size.height))，启动智能分割处理")
                await processImageWithSmartSplitting(image, imageIndex: imageIndex, isPDFImage: isPDFImage)
                return
            }

            // 自动选择最佳识别模式，用户无需选择
            let optimalMode = aiConfig.getOptimalRecognitionMode()
            let mode: HybridAddressRecognitionService.RecognitionMode = {
                switch optimalMode {
                case .ocrFirst:
                    return .ocrFirst
                case .aiFirst:
                    return .aiFirst
                case .ocrOnly:
                    return .ocrOnly
                case .aiOnly:
                    return .aiOnly
                }
            }()

            // 使用混合服务识别
            let result = try await hybridService.recognizeAddresses(
                from: image,
                mode: mode,
                appType: selectedAppType,  // 🎯 传递用户选择的应用类型
                isPDFImage: isPDFImage     // 🎯 传递PDF图像标识
            ) { status in
                Task { @MainActor in
                    processingStatus = status
                }
            }

            await MainActor.run {
                aiModelUsed = result.modelUsed ?? "Unknown"
                aiConfidence = result.confidence
                recognitionMethod = result.methodUsed
                ocrText = result.ocrText
                processingStatus = "geocoding_addresses".localized

                // 🎯 智能识别不再检测应用类型，完全由用户手动选择
                Logger.aiInfo("🎯 使用用户手动选择的应用类型: \(selectedAppType.displayName)")

                Logger.aiInfo("🎯 识别完成: \(result.methodUsed), 置信度: \(Int(result.confidence * 100))%")
                if let ocrText = result.ocrText {
                    Logger.aiDebug("📝 OCR文字: \(ocrText.prefix(100))...")
                }
            }

            // 🎯 AI识别完成后直接保存，然后批量进行坐标验证
            // AI的作用只是OCR识别地址、订单号、排序号，不负责坐标验证
            var addressesToVerify: [(String, Int)] = []

            for address in result.addresses {
                // 🎯 使用DeliveryPointManager分离地址信息，检查是否有实际地址
                let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                let rawAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查分离后的地址是否为空或无效
                if rawAddress.isEmpty || rawAddress.count < 3 {
                    Logger.aiWarning("跳过无效地址（无实际地址内容）: '\(address)' -> 分离后地址: '\(rawAddress)'")
                    continue
                }

                // 🎯 对于SpeedX，应用Apple Maps格式化
                let actualAddress = formatAddressForSpeedX(rawAddress)

                // 记录格式化过程（仅在有变化时）
                if selectedAppType == .speedx && actualAddress != rawAddress {
                    Logger.aiInfo("🍎 SpeedX混合服务地址格式化: '\(rawAddress)' -> '\(actualAddress)'")
                }

                // 🎯 AI识别的地址先保存到数组，并记录正确的索引
                let addressIndex = await MainActor.run { () -> Int in
                    // 🎯 在地址中添加用户选择的应用类型标签
                    let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto)
                    let finalAddress = shouldAddAppTag ? "\(address)|APP:\(selectedAppType.rawValue)" : address



                    // 先添加临时坐标，然后批量验证
                    let tempCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                    recognizedAddresses.append((finalAddress, tempCoordinate, true, false, 0.8))

                    // 返回刚添加的地址的索引
                    return recognizedAddresses.count - 1
                }

                // 记录需要验证的地址和正确的索引（使用格式化后的地址）
                addressesToVerify.append((actualAddress, addressIndex))

                Logger.aiInfo("✅ AI识别地址已保存: \(actualAddress), 索引: \(addressIndex)")
            }

            // 🚦 批量串行验证坐标，避免并发请求超限
            if !addressesToVerify.isEmpty {
                Logger.aiInfo("🚦 开始串行验证 \(addressesToVerify.count) 个地址的坐标")
                await batchVerifyAddressCoordinates(addressesToVerify)
            }

        } catch {
            await MainActor.run {
                handleProcessingError(error)
                Logger.aiError("混合识别失败: \(error)")
            }
        }
    }



    // 🎯 简化的地理编码方法 - 保持快递地址原样
    private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
        Logger.aiInfo("🌍 ImageAddressRecognizer - 简单地理编码快递地址: \(address)")

        // 🧹 清理地址：移除元数据信息（|SORT:|TRACK:|CUSTOMER:等）
        let cleanAddress = cleanAddressForGeocoding(address)
        Logger.aiInfo("🧹 地址清理后: \(cleanAddress)")

        // 🚦 等待速率限制
        await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

        // 🎯 使用清理后的地址进行地理编码
        let geocoder = CLGeocoder()
        do {
            let placemarks = try await geocoder.geocodeAddressString(cleanAddress)
            if let location = placemarks.first?.location {
                Logger.aiInfo("🌍 ImageAddressRecognizer - Apple Maps找到地址: \(address) -> (\(location.coordinate.latitude), \(location.coordinate.longitude))")
                return location.coordinate
            } else {
                Logger.aiInfo("🌍 ImageAddressRecognizer - Apple Maps未找到地址: \(address)")
                return nil
            }
        } catch {
            let nsError = error as NSError
            if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                Logger.aiError("🚨 ImageAddressRecognizer - Apple Maps API限制触发 - \(error.localizedDescription)")
                // 🎯 智能解析等待时间
                let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
            } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                Logger.aiWarning("🚫 ImageAddressRecognizer - Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)")
                // 🎯 智能解析等待时间
                let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
            } else {
                Logger.aiInfo("🌍 ImageAddressRecognizer - Apple Maps地理编码失败: \(address) - \(error)")
            }
            return nil
        }
    }

    // 🧹 清理地址用于地理编码 - 移除元数据信息
    private func cleanAddressForGeocoding(_ address: String) -> String {
        var cleanedAddress = address

        // 移除所有管道符号（|）后的元数据信息
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )

        // 清理多余的空格
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        )

        return cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // MARK: - 地址管理

    // 🎯 重新分配连续的序号，解决多张图片序号重复问题
    private func reassignSortNumbers() {
        var updatedAddresses: [(String, CLLocationCoordinate2D, Bool, Bool, Double?)] = []
        var currentSortNumber = 1

        for (address, coordinate, isSelected, hasValidCoordinate, confidence) in recognizedAddresses {
            // 提取地址中的SORT信息
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)



            // 重新构建地址，使用连续的序号
            var newAddress = separatedInfo.address

            // 🎯 添加连续的内部序号作为SORT标签（用于内部排序）
            newAddress += SortNumberConstants.internalSortTag(currentSortNumber)

            // 🎯 保留原始第三方排序号（如果存在）
            if !separatedInfo.thirdPartySortNumber.isEmpty {
                newAddress += SortNumberConstants.thirdPartySortTag(separatedInfo.thirdPartySortNumber)
                Logger.aiInfo("🎯 保留第三方排序号: \(separatedInfo.thirdPartySortNumber), 内部序号: \(currentSortNumber)")
            } else {
                Logger.aiInfo("🎯 分配内部连续序号: \(selectedAppType.rawValue) -> \(currentSortNumber)")
            }

            // 保留其他信息
            if !separatedInfo.tracking.isEmpty {
                newAddress += SortNumberConstants.trackingTag(separatedInfo.tracking)
            }
            if !separatedInfo.customer.isEmpty {
                newAddress += SortNumberConstants.customerTag(separatedInfo.customer)
            }
            if !separatedInfo.deliveryTime.isEmpty {
                newAddress += SortNumberConstants.timeTag(separatedInfo.deliveryTime)
            }
            if !separatedInfo.appType.isEmpty {
                newAddress += SortNumberConstants.appTag(separatedInfo.appType)
            }

            updatedAddresses.append((newAddress, coordinate, isSelected, hasValidCoordinate, confidence))
            currentSortNumber += 1
        }

        recognizedAddresses = updatedAddresses
        Logger.aiInfo("🎯 重新分配序号完成，共\(recognizedAddresses.count)个地址，序号1-\(currentSortNumber-1)")
    }

    // 清理地址用于显示 - 隐藏第三方sort number等内部信息
    private func cleanAddressForDisplay(_ address: String) -> String {
        var cleanedAddress = address

        // 移除第三方sort number模式（如 ISORT:8, D90, D91等）
        // 移除以字母开头后跟冒号和数字的模式
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\b[A-Z]+:\\d+\\b",
            with: "",
            options: .regularExpression
        )

        // 移除单独的字母+数字组合（如 D90, D91, D146等）
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\b[A-Z]\\d+\\b",
            with: "",
            options: .regularExpression
        )

        // 移除多余的 "|" 符号和相关标记（SORT、TRACK、CUSTOMER、TIME等）
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )

        // 移除重复的地区名称（如连续出现的 SAN MATEO）
        cleanedAddress = removeDuplicateRegions(cleanedAddress)

        // 移除多余的空格和逗号
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\s*,\\s*,",
            with: ",",
            options: .regularExpression
        )

        // 🎯 SpeedX专用：应用地址分隔符格式优化
        cleanedAddress = applySpeedXAddressSeparatorFormat(cleanedAddress)

        // 移除开头和结尾的逗号、空格和 "|"
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        cleanedAddress = cleanedAddress.trimmingCharacters(in: CharacterSet(charactersIn: ",|"))
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        // 🚀 优化：减少重复的清理日志，只在DEBUG模式下输出
        #if DEBUG
        if cleanedAddress != address {
            // 使用静态缓存避免重复日志
            let logKey = "\(address)->\(cleanedAddress)"
            if !Self.loggedCleanups.contains(logKey) {
                Self.loggedCleanups.insert(logKey)
                print("🧹 地址显示清理: '\(address)' -> '\(cleanedAddress)'")

                // 限制缓存大小，避免内存泄漏
                if Self.loggedCleanups.count > 100 {
                    Self.loggedCleanups.removeAll()
                }
            }
        }
        #endif

        return cleanedAddress.isEmpty ? address : cleanedAddress
    }

    /// 🎯 SpeedX专用：应用地址分隔符格式优化
    private func applySpeedXAddressSeparatorFormat(_ address: String) -> String {
        var formatted = address

        // 美国州简称列表
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]

        // 对于美国州简称，使用无空格格式：City,CA
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            formatted = formatted.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }

        return formatted
    }

    // 移除重复的地区名称
    private func removeDuplicateRegions(_ address: String) -> String {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        var uniqueComponents: [String] = []
        var seenComponents = Set<String>()

        for component in components {
            let normalizedComponent = component.uppercased()
            if !seenComponents.contains(normalizedComponent) && !component.isEmpty {
                seenComponents.insert(normalizedComponent)
                uniqueComponents.append(component)
            }
        }

        return uniqueComponents.joined(separator: ", ")
    }

    // 切换地址选择状态
    private func toggleAddressSelection(at index: Int) {
        guard index < recognizedAddresses.count else { return }
        recognizedAddresses[index].2.toggle()
    }

    // 删除地址
    private func removeAddress(at index: Int) {
        guard index < recognizedAddresses.count else { return }
        recognizedAddresses.remove(at: index)
    }

    // 确认选中的地址
    private func confirmSelectedAddresses() {
        // ⏱️ 打印最终的计时统计
        printFinalTimingStats()

        let selectedAddresses = recognizedAddresses.filter { $0.2 }.map { ($0.0, $0.1) }

        if !selectedAddresses.isEmpty {
            // 🎯 优先检查：非Pro用户且选择地址超过20个时的处理
            let subscriptionManager = SubscriptionManager.shared
            let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial

            if isFreeUser && selectedAddresses.count > 20 {
                // 非Pro用户且选择地址超过20个，弹出选择对话框
                Logger.aiInfo("ImageAddressRecognizer - 非Pro用户选择了\(selectedAddresses.count)个地址，超过20个限制，弹出选择对话框")

                // 🎯 修复空白页问题：使用RouteBottomSheet验证过的延迟模式
                limitExceededInfo = (
                    currentCount: 0, // 这里不关心当前路线的地址数
                    remainingSlots: 20, // 免费用户限制为20个
                    maxAllowed: 20,
                    selectedCount: selectedAddresses.count,
                    selectedAddresses: selectedAddresses
                )

                // 直接设置显示sheet，自定义Binding已经解决了SwiftUI的渲染问题
                Logger.aiInfo("ImageAddressRecognizer - 在主线程设置showLimitExceededSheet=true，limitExceededInfo已设置")
                self.showLimitExceededSheet = true
                return
            }
            // 🎯 新逻辑：自动处理重复，不阻止用户操作
            let processedAddresses = autoFixDuplicateThirdPartySortNumbers(selectedAddresses)
            let finalSelectedAddresses = processedAddresses

            print("🎯 DEBUG: ImageAddressRecognizer - 确认导入\(finalSelectedAddresses.count)个地址")
            print("🎯 DEBUG: 当前selectedAppType = \(selectedAppType.rawValue) (\(selectedAppType.displayName))")

            // 🎯 处理地址信息，提取sort number和清理地址
            let finalProcessedAddresses = finalSelectedAddresses.map { (addressString, coordinate) in
                // 使用AddressStandardizer清理地址
                let cleanedAddress = AddressStandardizer.cleanDuplicateInformation(addressString)

                // 🎯 检查地址是否已经包含APP标签，避免重复添加
                let alreadyHasAppTag = cleanedAddress.contains("|APP:")
                let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto) && !alreadyHasAppTag
                let finalAddress = shouldAddAppTag ? "\(cleanedAddress)|APP:\(selectedAppType.rawValue)" : cleanedAddress

                print("🎯 DEBUG: ImageAddressRecognizer - 地址处理流程:")
                print("🎯 DEBUG:   原始: \(addressString)")
                print("🎯 DEBUG:   清理后: \(cleanedAddress)")
                print("🎯 DEBUG:   已有APP标签: \(alreadyHasAppTag)")
                print("🎯 DEBUG:   用户选择的应用类型: \(selectedAppType.rawValue) (\(selectedAppType.displayName))")
                print("🎯 DEBUG:   应用标签: \(shouldAddAppTag ? "添加|APP:\(selectedAppType.rawValue)" : alreadyHasAppTag ? "已存在，跳过" : "不添加")")
                print("🎯 DEBUG:   最终: \(finalAddress)")

                return (finalAddress, coordinate)
            }

            Logger.aiInfo("📤 确认导入\(finalSelectedAddresses.count)个地址，应用类型: \(selectedAppType.displayName)")
            Logger.aiInfo("🧹 地址已通过自动重复处理和AddressStandardizer清理")

            // 🚨 在确认地址后检查问题地址并提示用户
            Task {
                // 先调用原始回调，传递用户选择的应用类型
                onAddressesConfirmed(finalProcessedAddresses, selectedAppType)

                // 延迟一下让地址处理完成
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

                // 检查是否有问题地址需要提示用户
                await checkAndShowProblemAddresses()
            }
        }
    }

    // 🚨 检查并显示问题地址给用户
    private func checkAndShowProblemAddresses() async {
        await MainActor.run {
            ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "image_address_recognition".localized)
        }
    }

    // 📊 获取地址计数显示文本
    private func getAddressCountDisplay() -> String {
        let currentCount = recognizedAddresses.count

        // 如果用户输入了总数，显示 "当前/总数"
        if let totalCount = userInputTotalCount {
            return "\(currentCount)/\(totalCount)"
        }

        // 如果从图片识别到了总数，显示 "当前/识别总数"
        if let detectedTotal = detectedTotalCount {
            return "\(currentCount)/\(detectedTotal)"
        }

        // 否则只显示当前数量
        return "\(currentCount)"
    }

    // 🚀 图片AI处理优化：智能压缩图片以提升处理速度
    private func optimizeImageForAI(_ image: UIImage) -> UIImage {
        let startTime = Date()

        // 计算原始图片大小
        let originalSize = image.size
        let originalArea = originalSize.width * originalSize.height

        // 🎯 智能尺寸限制：根据应用类型和图片内容密度调整
        let maxDimension: CGFloat
        let maxArea: CGFloat

        // 🚀 主要快递统一激进压缩：界面文字清晰，可以大幅压缩提升速度
        if selectedAppType == .gofo || selectedAppType == .amazonFlex ||
           selectedAppType == .imile || selectedAppType == .ldsEpod ||
           selectedAppType == .piggy || selectedAppType == .uniuni ||
           selectedAppType == .ywe {
            if originalArea > 4_000_000 { // 超大图片 (2000x2000)
                maxDimension = 600  // 主要快递激进压缩
                maxArea = 360_000   // 600x600
            } else if originalArea > 1_000_000 { // 大图片 (1000x1000)
                maxDimension = 450  // 主要快递激进压缩
                maxArea = 202_500   // 450x450
            } else {
                maxDimension = 350  // 主要快递小图片也大幅压缩
                maxArea = 122_500   // 350x350
            }
        } else {
            // 其他应用使用标准压缩
            if originalArea > 4_000_000 { // 超大图片 (2000x2000)
                maxDimension = 1200
                maxArea = 1_440_000 // 1200x1200
            } else if originalArea > 1_000_000 { // 大图片 (1000x1000)
                maxDimension = 1024
                maxArea = 1_048_576 // 1024x1024
            } else {
                // 小图片保持原样，避免过度压缩影响识别精度
                Logger.aiInfo("🖼️ 图片尺寸适中，无需压缩: \(Int(originalSize.width))x\(Int(originalSize.height))")
                return image
            }
        }

        // 检查是否需要压缩
        if originalSize.width <= maxDimension && originalSize.height <= maxDimension && originalArea <= maxArea {
            if selectedAppType == .gofo || selectedAppType == .amazonFlex ||
               selectedAppType == .imile || selectedAppType == .ldsEpod ||
               selectedAppType == .piggy || selectedAppType == .uniuni ||
               selectedAppType == .ywe {
                Logger.aiInfo("🚀 \(selectedAppType.displayName)图片尺寸在激进限制范围内，无需压缩: \(Int(originalSize.width))x\(Int(originalSize.height))")
            } else {
                Logger.aiInfo("🖼️ 图片尺寸在限制范围内，无需压缩: \(Int(originalSize.width))x\(Int(originalSize.height))")
            }
            return image
        }

        // 计算压缩比例，保持宽高比
        let widthRatio = maxDimension / originalSize.width
        let heightRatio = maxDimension / originalSize.height
        let ratio = min(widthRatio, heightRatio, 1.0)

        let newSize = CGSize(
            width: originalSize.width * ratio,
            height: originalSize.height * ratio
        )

        // 执行高质量压缩
        let renderer = UIGraphicsImageRenderer(size: newSize)
        let compressedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }

        let processingTime = Date().timeIntervalSince(startTime)
        let compressionRatio = (newSize.width * newSize.height) / originalArea

        if selectedAppType == .gofo || selectedAppType == .amazonFlex ||
           selectedAppType == .imile || selectedAppType == .ldsEpod ||
           selectedAppType == .piggy || selectedAppType == .uniuni ||
           selectedAppType == .ywe {
            Logger.aiInfo("🚀 \(selectedAppType.displayName)激进图片优化完成:")
        } else {
            Logger.aiInfo("🚀 图片AI优化完成:")
        }
        Logger.aiInfo("   📏 原始尺寸: \(Int(originalSize.width))x\(Int(originalSize.height))")
        Logger.aiInfo("   📐 压缩尺寸: \(Int(newSize.width))x\(Int(newSize.height))")
        Logger.aiInfo("   📊 压缩比例: \(String(format: "%.1f", compressionRatio * 100))%")
        Logger.aiInfo("   ⏱️ 处理耗时: \(String(format: "%.3f", processingTime))秒")

        return compressedImage
    }

    // 🚀 智能批次延迟计算：根据处理情况和应用类型动态调整延迟时间
    private func calculateOptimalBatchDelay(batchSize: Int, remainingImages: Int) -> Double {
        // 🎯 GoFo专用优化：5张图片并发处理，需要适当延迟避免API限制
        if selectedAppType == .gofo {
            let gofoBaseDelay: Double = 1.5 // GoFo并发处理：适中延迟避免API限制

            // GoFo剩余图片调整：更激进的加速策略
            let gofoRemainingFactor: Double
            if remainingImages > 50 {
                gofoRemainingFactor = 0.5 // 大量剩余，最小延迟
            } else if remainingImages > 20 {
                gofoRemainingFactor = 0.7 // 中等剩余，减少延迟
            } else {
                gofoRemainingFactor = 1.0 // 少量剩余，保持延迟
            }

            let gofoFinalDelay = gofoBaseDelay * gofoRemainingFactor
            return max(0.2, min(gofoFinalDelay, 1.5)) // GoFo限制在0.2-1.5秒之间
        }

        // 其他应用的标准延迟配置
        let baseDelay: Double
        if batchSize <= 3 {
            baseDelay = 1.0 // 小批次，短延迟
        } else if batchSize <= 8 {
            baseDelay = 1.5 // 中批次，中等延迟
        } else {
            baseDelay = 2.0 // 大批次，长延迟
        }

        // 剩余图片调整：剩余越多，延迟越短（加快整体进度）
        let remainingFactor: Double
        if remainingImages > 50 {
            remainingFactor = 0.7 // 大量剩余，减少延迟
        } else if remainingImages > 20 {
            remainingFactor = 0.85 // 中等剩余，略减延迟
        } else {
            remainingFactor = 1.0 // 少量剩余，保持延迟
        }

        let finalDelay = baseDelay * remainingFactor
        return max(0.5, min(finalDelay, 3.0)) // 限制在0.5-3秒之间
    }

    // ⏱️ 打印最终计时统计
    private func printFinalTimingStats() {
        guard let startTime = analysisStartTime else {
            Logger.aiWarning("⏱️ 无法计算耗时：缺少开始时间")
            return
        }

        let endTime = analysisEndTime ?? Date()
        let totalDuration = endTime.timeIntervalSince(startTime)
        let selectedCount = recognizedAddresses.filter { $0.2 }.count

        print("🎯 ===== 图片分析完整计时报告 =====")
        print("📸 处理图片数量: \(selectedImages.count)")
        print("🔍 识别地址总数: \(recognizedAddresses.count)")
        print("✅ 用户选择地址数: \(selectedCount)")
        print("⏱️ 从Start到Confirm总耗时: \(String(format: "%.2f", totalDuration))秒")
        print("📊 平均每张图片: \(String(format: "%.2f", totalDuration / Double(max(selectedImages.count, 1))))秒")
        if recognizedAddresses.count > 0 {
            print("📍 平均每个地址: \(String(format: "%.2f", totalDuration / Double(recognizedAddresses.count)))秒")
        }
        print("🎯 ================================")

        Logger.aiInfo("🎯 用户确认地址，完整流程耗时: \(String(format: "%.2f", totalDuration))秒")
    }

    // 🚨 验证第三方排序号是否重复（已弃用 - 现在使用自动修复）
    // 注释：此方法已被autoFixDuplicateThirdPartySortNumbers替代，不再阻止用户操作
    /*
    private func validateThirdPartySortNumbers(_ addresses: [(String, CLLocationCoordinate2D)]) -> String? {
        var thirdPartySortNumbers: [String] = []
        var duplicates: [String] = []

        for (address, _) in addresses {
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)

            // 只检查有第三方排序号且不是missing的地址
            if !separatedInfo.thirdPartySortNumber.isEmpty && separatedInfo.thirdPartySortNumber != "missing" {
                let sortNumber = separatedInfo.thirdPartySortNumber

                if thirdPartySortNumbers.contains(sortNumber) {
                    if !duplicates.contains(sortNumber) {
                        duplicates.append(sortNumber)
                    }
                } else {
                    thirdPartySortNumbers.append(sortNumber)
                }
            }
        }

        if !duplicates.isEmpty {
            let duplicateList = duplicates.joined(separator: ", ")
            Logger.aiError("🚨 检测到第三方排序号重复: \(duplicateList)")
            return "检测到重复的第三方排序号: \(duplicateList)\n\n这表明AI识别有误，请重新识别图片或手动修正。"
        }

        return nil
    }
    */

    // 🎯 自动修复重复的地址+第三方排序号组合（简化逻辑：地址+第三方sort完全一致就是绝对重复，保一删一）
    private func autoFixDuplicateThirdPartySortNumbers(_ addresses: [(String, CLLocationCoordinate2D)]) -> [(String, CLLocationCoordinate2D)] {
        var seenAddressSortCombos: Set<String> = []
        var processedAddresses: [(String, CLLocationCoordinate2D)] = []
        var duplicateCount = 0

        for (address, coordinate) in addresses {
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
            let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            let thirdPartySortNumber = separatedInfo.thirdPartySortNumber

            // 🎯 构建简化的组合键：地址+第三方排序号
            let simplifiedComboKey = "\(cleanAddress)|\(thirdPartySortNumber)"

            if seenAddressSortCombos.contains(simplifiedComboKey) {
                // 🎯 发现绝对重复（地址+第三方sort完全一致），直接跳过（保一删一）
                duplicateCount += 1
                Logger.aiWarning("🔧 确认阶段去重：跳过绝对重复（地址+第三方sort）: \(cleanAddress) + \(thirdPartySortNumber)")
            } else {
                // 第一次出现，保留并记录
                seenAddressSortCombos.insert(simplifiedComboKey)
                processedAddresses.append((address, coordinate))
            }
        }

        if duplicateCount > 0 {
            Logger.aiInfo("✅ 确认阶段去重：删除了 \(duplicateCount) 个绝对重复（地址+第三方sort），保留第一个出现的地址")
        }

        return processedAddresses
    }

    // 🔧 创建标记为missing的地址字符串
    private func createAddressWithMissingThirdPartySort(_ separatedInfo: (address: String, tracking: String, sortNumber: String, thirdPartySortNumber: String, customer: String, deliveryTime: String, appType: String)) -> String {
        var newAddress = separatedInfo.address

        // 保持内部序号
        if !separatedInfo.sortNumber.isEmpty, let sortNum = Int(separatedInfo.sortNumber) {
            newAddress += SortNumberConstants.internalSortTag(sortNum)
        }

        // 标注第三方排序号为missing
        newAddress += SortNumberConstants.thirdPartySortTag("missing")

        // 保留追踪号
        if !separatedInfo.tracking.isEmpty {
            newAddress += SortNumberConstants.trackingTag(separatedInfo.tracking)
        }

        return newAddress
    }

    // 🚨 检查识别结果中的重复，并标注为missing（简化逻辑：地址+第三方sort完全一致就是绝对重复）
    private func checkForDuplicateThirdPartySortNumbers() {
        // 1. 检查第三方排序号重复（保留原有逻辑用于调试）
        checkThirdPartySortNumberDuplicates()

        // 2. 检查地址+第三方sort组合重复（新的简化逻辑）
        checkAddressThirdPartySortComboDuplicates()
    }

    // 🎯 检查第三方排序号重复（提供详细日志）
    private func checkThirdPartySortNumberDuplicates() {
        var thirdPartySortNumbers: [String: (Int, String)] = [:] // 排序号 -> (索引, 地址)
        var duplicateFound = false

        for (index, (address, _, _, _, _)) in recognizedAddresses.enumerated() {
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)

            if !separatedInfo.thirdPartySortNumber.isEmpty && separatedInfo.thirdPartySortNumber != "missing" {
                let sortNumber = separatedInfo.thirdPartySortNumber

                if let (existingIndex, existingAddress) = thirdPartySortNumbers[sortNumber] {
                    // 🎯 详细日志：显示重复的排序号和涉及的地址
                    Logger.aiError("🚨 检测到重复的第三方排序号: \(sortNumber)")
                    Logger.aiError("   📍 第一个地址 (索引\(existingIndex)): \(existingAddress)")
                    Logger.aiError("   📍 重复地址 (索引\(index)): \(separatedInfo.address)")
                    duplicateFound = true
                } else {
                    thirdPartySortNumbers[sortNumber] = (index, separatedInfo.address)
                }
            }
        }

        if duplicateFound {
            Logger.aiError("🚨 AI识别结果包含重复的第三方排序号，建议重新识别")
        } else {
            Logger.aiInfo("✅ 第三方排序号验证通过，无重复")
        }
    }

    // 🎯 检查地址+第三方sort组合重复（简化逻辑）
    private func checkAddressThirdPartySortComboDuplicates() {
        var addressSortCombos: [String: Int] = [:]
        var duplicateIndices: Set<Int> = []

        for (index, (address, _, _, _, _)) in recognizedAddresses.enumerated() {
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)

            let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            let thirdPartySortNumber = separatedInfo.thirdPartySortNumber

            // 创建地址+第三方sort的组合键
            let comboKey = "\(cleanAddress)|\(thirdPartySortNumber)"

            if !cleanAddress.isEmpty && !thirdPartySortNumber.isEmpty && thirdPartySortNumber != "missing" {
                if let existingIndex = addressSortCombos[comboKey] {
                    Logger.aiError("🚨 发现绝对重复（地址+第三方sort）:")
                    Logger.aiError("   📍 地址: \(cleanAddress)")
                    Logger.aiError("   🔢 第三方sort: \(thirdPartySortNumber)")
                    Logger.aiError("   🔢 保留索引: \(existingIndex)，删除重复索引: \(index)")
                    // 🎯 只标记当前重复的地址索引，保留第一个出现的
                    duplicateIndices.insert(index)
                } else {
                    addressSortCombos[comboKey] = index
                }
            }
        }

        // 🎯 删除重复的地址+第三方sort组合（保一删一）
        if !duplicateIndices.isEmpty {
            // 按索引从大到小排序，避免删除时索引变化
            let sortedIndices = duplicateIndices.sorted(by: >)
            for index in sortedIndices {
                if index < recognizedAddresses.count {
                    recognizedAddresses.remove(at: index)
                }
            }
            Logger.aiWarning("🗑️ 已删除\(duplicateIndices.count)个绝对重复（地址+第三方sort），保留第一个出现的地址")
        } else {
            Logger.aiInfo("✅ 地址+第三方sort组合验证通过，无重复")
        }
    }

    // 🏷️ 将重复的地址+订单号组合标注为missing
    private func markDuplicateThirdPartySortAsMissing(_ duplicateIndices: Set<Int>) {
        for index in duplicateIndices {
            guard index < recognizedAddresses.count else { continue }

            let (address, coordinate, isSelected, hasValidCoordinate, confidence) = recognizedAddresses[index]
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)

            // 重新构建地址，移除重复的第三方排序号，标注为missing
            var newAddress = separatedInfo.address
            newAddress += SortNumberConstants.internalSortTag(index + 1) // 保持内部序号

            // 🏷️ 标注第三方排序号为missing（因为地址+订单号重复）
            newAddress += SortNumberConstants.thirdPartySortTag("missing")

            // 保留其他信息
            if !separatedInfo.tracking.isEmpty {
                newAddress += SortNumberConstants.trackingTag(separatedInfo.tracking)
            }
            if !separatedInfo.customer.isEmpty {
                newAddress += SortNumberConstants.customerTag(separatedInfo.customer)
            }
            if !separatedInfo.deliveryTime.isEmpty {
                newAddress += SortNumberConstants.timeTag(separatedInfo.deliveryTime)
            }
            if !separatedInfo.appType.isEmpty {
                newAddress += SortNumberConstants.appTag(separatedInfo.appType)
            }

            // 更新地址
            recognizedAddresses[index] = (newAddress, coordinate, isSelected, hasValidCoordinate, confidence)

            Logger.aiInfo("🏷️ 地址索引\(index)的第三方排序号已标注为missing: \(separatedInfo.address)")
        }
    }

    // 🚦 批量智能验证地址坐标 - SpeedX优化版本
    private func batchVerifyAddressCoordinates(_ addressesToVerify: [(String, Int)]) async {
        Logger.aiInfo("🚦 开始批量智能验证 \(addressesToVerify.count) 个地址坐标")

        // 🏠 Step 1: 批量检查地址库，提高缓存命中率
        let addresses = addressesToVerify.map { $0.0 }
        let cachedResults = await UserAddressDatabase.shared.batchGetValidatedAddresses(for: addresses)
        Logger.aiInfo("🏠 地址库批量查询完成: \(cachedResults.count)/\(addresses.count) 命中缓存")

        // 🚦 Step 2: 智能分批处理，避免API频率限制
        let batchSize = determineBatchSizeForVerification(totalCount: addressesToVerify.count)
        Logger.aiInfo("🚦 使用智能批次大小: \(batchSize)")

        for batchIndex in stride(from: 0, to: addressesToVerify.count, by: batchSize) {
            let endIndex = min(batchIndex + batchSize, addressesToVerify.count)
            let batch = Array(addressesToVerify[batchIndex..<endIndex])

            Logger.aiInfo("🔍 处理批次 \(batchIndex/batchSize + 1)/\(Int(ceil(Double(addressesToVerify.count)/Double(batchSize)))): \(batch.count)个地址")

            // 🔧 修复并发问题：串行处理坐标验证避免数组索引冲突
            // 坐标验证涉及修改recognizedAddresses数组，必须串行处理
            for (address, arrayIndex) in batch {
                await self.verifyAndUpdateAddressCoordinate(address: address, index: arrayIndex, cachedResults: cachedResults)
            }

            // 🚦 智能批次间延迟，根据API使用率动态调整
            if endIndex < addressesToVerify.count {
                let smartDelay = await GlobalGeocodingRateLimiter.shared.getSmartBatchDelay()
                try? await Task.sleep(nanoseconds: UInt64(smartDelay * 1_000_000_000))
                Logger.aiInfo("🚦 智能延迟 \(smartDelay)秒后继续下一批次")
            }
        }

        // 🚀 批量保存所有验证成功的地址到数据库（性能优化）
        if !batchAddressesToSave.isEmpty {
            Logger.aiInfo("🚀 开始批量保存 \(batchAddressesToSave.count) 个验证地址到数据库")
            await UserAddressDatabase.shared.batchSaveValidatedAddresses(batchAddressesToSave)

            // 清空批量保存数组
            await MainActor.run {
                batchAddressesToSave.removeAll()
            }
            Logger.aiInfo("✅ 批量保存完成")
        }

        Logger.aiInfo("✅ 批量坐标验证完成")
    }

    // 🎯 智能确定验证批次大小 - 性能优化版本
    private func determineBatchSizeForVerification(totalCount: Int) -> Int {
        if totalCount <= 20 {
            return 8 // 优化：小批量提高并行度
        } else if totalCount <= 50 {
            return 15 // 优化：中批量大幅提高并行度
        } else if totalCount <= 100 {
            return 25 // 优化：大批量高并行度
        } else if totalCount <= 200 {
            return 35 // 优化：超大批量，如SpeedX的160个地址
        } else {
            return 40 // 优化：极大批量，接近Apple Maps限制
        }
    }

    // 🚀 SpeedX风格的大批量处理优化
    private func optimizeForLargeBatch() async {
        // 🏠 预加载地址库统计信息
        await UserAddressDatabase.shared.updateDatabaseStats()

        // 🚦 重置速率限制器统计
        await GlobalGeocodingRateLimiter.shared.resetStats()

        // 🧹 触发内存优化
        await MainActor.run {
            autoreleasepool {
                // 清理可能的内存碎片
            }
        }

        Logger.aiInfo("🚀 大批量处理优化完成")
    }

    // 🎯 智能并发数计算：根据片段数量和应用类型动态调整
    private func calculateOptimalConcurrency(segmentCount: Int) -> Int {
        // 基础并发数配置
        let baseConcurrency: Int

        if segmentCount <= 10 {
            baseConcurrency = 3 // 小批量：保守并发
        } else if segmentCount <= 30 {
            baseConcurrency = 5 // 中等批量：标准并发
        } else if segmentCount <= 60 {
            baseConcurrency = 6 // 大批量：提升并发
        } else {
            baseConcurrency = 8 // 超大批量：最大并发
        }

        // 🎯 应用特殊优化：由于跳过OCR，可以更激进的并发
        let finalConcurrency: Int = switch selectedAppType {
        case .gofo:
            // GoFo最激进：AI Only + 优化压缩 = 最高并发
            min(baseConcurrency + 4, 12)
        case .speedx:
            // SpeedX激进：AI Only模式
            min(baseConcurrency + 2, 10)
        default:
            // 其他应用标准并发
            baseConcurrency
        }

        Logger.aiInfo("🎯 智能并发配置: \(segmentCount)个片段 → \(finalConcurrency)并发 (应用: \(selectedAppType.displayName))")

        return finalConcurrency
    }

    // 🚀 立即验证地址坐标并更新UI - SpeedX优化版本，支持缓存
    private func verifyAndUpdateAddressCoordinate(address: String, index: Int, cachedResults: [String: ValidatedAddressResult] = [:]) async {
        Logger.aiInfo("🔍 开始验证地址坐标: \(address)")

        // 清理地址（移除APP标签等）
        let cleanAddress = AddressStandardizer.cleanDuplicateInformation(address)

        // 🏠 首先检查缓存结果
        if let cachedResult = cachedResults[cleanAddress] {
            Logger.aiInfo("🏠 使用地址库缓存结果: \(cleanAddress) -> (\(cachedResult.coordinate.latitude), \(cachedResult.coordinate.longitude))")

            await MainActor.run {
                guard index < recognizedAddresses.count else {
                    Logger.aiWarning("⚠️ 地址索引无效，跳过坐标更新: \(index)")
                    return
                }

                let currentAddress = recognizedAddresses[index]
                recognizedAddresses[index] = (
                    currentAddress.0, // 保持原始地址字符串
                    cachedResult.coordinate,  // 使用缓存坐标
                    currentAddress.2, // 保持其他属性
                    true,             // 标记为有效坐标
                    currentAddress.4
                )
            }
            return
        }

        // 🎯 优化：AI识别的地址已经通过geocoding验证，使用更宽松的验证策略
        // 参考确认按钮后的处理逻辑，避免过度严格的验证导致正确地址被误判
        let coordinate = await geocodeAddressForVerification(cleanAddress)

        await MainActor.run {
            // 检查索引是否仍然有效
            guard index < recognizedAddresses.count else {
                Logger.aiWarning("⚠️ 地址索引无效，跳过坐标更新: \(index)")
                return
            }

            if let validCoordinate = coordinate {
                // 更新坐标
                let currentAddress = recognizedAddresses[index]
                recognizedAddresses[index] = (
                    currentAddress.0, // 保持原始地址字符串
                    validCoordinate,  // 更新坐标
                    currentAddress.2, // 保持其他属性
                    true,             // 标记为有效坐标
                    currentAddress.4
                )

                Logger.aiInfo("✅ 地址坐标验证成功: \(cleanAddress) -> (\(validCoordinate.latitude), \(validCoordinate.longitude))")

                // 🚀 收集验证成功的地址用于批量保存（性能优化）
                batchAddressesToSave.append((cleanAddress, validCoordinate, .screenshot, 0.85))
                Logger.aiInfo("📝 已收集验证地址用于批量保存: \(cleanAddress)")
            } else {
                Logger.aiWarning("❌ 地址坐标验证失败: \(cleanAddress)")
                // 保持原有的0,0坐标，用户可以手动处理
            }
        }
    }

    // 🎯 简化的地址验证方法 - 保持快递地址原样
    private func geocodeAddressForVerification(_ address: String) async -> CLLocationCoordinate2D? {
        Logger.aiInfo("🌍 ImageAddressRecognizer - 简单验证快递地址: \(address)")

        // 🧹 清理地址：移除元数据信息（|SORT:|TRACK:|CUSTOMER:等）
        let cleanAddress = cleanAddressForGeocoding(address)
        Logger.aiInfo("🧹 地址清理后: \(cleanAddress)")

        // 🚦 等待速率限制
        await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

        // 🎯 使用清理后的地址进行地理编码
        let geocoder = CLGeocoder()
        do {
            let placemarks = try await geocoder.geocodeAddressString(cleanAddress)
            if let location = placemarks.first?.location {
                Logger.aiInfo("🌍 ImageAddressRecognizer - Apple Maps找到地址: \(address) -> (\(location.coordinate.latitude), \(location.coordinate.longitude))")
                return location.coordinate
            } else {
                Logger.aiInfo("🌍 ImageAddressRecognizer - Apple Maps未找到地址，保持原样: \(address)")
                return nil
            }
        } catch {
            let nsError = error as NSError
            if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                Logger.aiError("🚨 ImageAddressRecognizer - Apple Maps API限制触发 - \(error.localizedDescription)")
                // 🎯 智能解析等待时间
                let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
            } else if nsError.domain == "kCLErrorDomain" && nsError.code == 2 {
                Logger.aiWarning("🚫 ImageAddressRecognizer - Apple Maps API限制 (kCLErrorDomain 2) - \(error.localizedDescription)")
                // 🎯 智能解析等待时间
                let waitTime = await GlobalGeocodingRateLimiter.shared.parseWaitTimeFromError(error)
                await GlobalGeocodingRateLimiter.shared.emergencyWait(customWaitTime: waitTime)
            } else {
                Logger.aiInfo("🌍 ImageAddressRecognizer - Apple Maps地理编码失败，保持原样: \(address) - \(error)")
            }
            return nil
        }
    }

    // 🌐 检查网络错误并收集失败的地址
    // 🎯 修复：AI识别的地址使用临时坐标，不应该被视为网络错误
    private func checkForNetworkErrors() {
        // 🎯 AI识别的地址都使用临时坐标 (0,0)，这是正常的，不是网络错误
        // 只有在实际进行地理编码验证时失败的地址才是网络错误
        // 由于我们已经移除了AI识别后的地理编码验证，这里暂时不检查网络错误

        Logger.info("🎯 AI识别完成，地址将在用户使用时进行坐标验证", type: .location)

        // 如果需要检查网络错误，应该在用户实际使用地址时进行
        // 例如：在导航时、编辑地址时、或用户手动触发验证时
    }

    // MARK: - 智能错误处理
    private func handleProcessingError(_ error: Error) {
        let processingError = createProcessingError(from: error)
        currentError = processingError
        showingErrorSheet = true
    }

    private func createProcessingError(from error: Error) -> ProcessingError {
        if let gemmaError = error as? GemmaError {
            switch gemmaError {
            case .geographicRestriction:
                return ProcessingError(
                    type: .aiModelUnavailable,
                    title: "hong_kong_service_restriction".localized,
                    message: "hong_kong_service_restriction_message".localized,
                    suggestions: [
                        ProcessingError.ErrorSuggestion(
                            title: "use_ocr_mode".localized,
                            action: {
                                NotificationCenter.default.post(name: Notification.Name("SwitchToOCRMode"), object: nil)
                            },
                            style: .primary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "learn_ocr_advantages".localized,
                            action: {
                                // 显示OCR模式的优势说明
                                NotificationCenter.default.post(name: Notification.Name("ShowOCRAdvantages"), object: nil)
                            },
                            style: .secondary
                        )
                    ]
                )
            case .allGemmaModelsFailed:
                return ProcessingError(
                    type: .aiModelUnavailable,
                    title: "advanced_recognition_service_restriction".localized,
                    message: "advanced_recognition_service_restriction_message".localized,
                    suggestions: [
                        ProcessingError.ErrorSuggestion(
                            title: "try_backup_service".localized,
                            action: {
                                // 尝试备用服务
                                NotificationCenter.default.post(name: Notification.Name("TryFirebaseAI"), object: nil)
                            },
                            style: .primary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "use_basic_mode".localized,
                            action: {
                                // 优先推荐基础模式
                                NotificationCenter.default.post(name: Notification.Name("SwitchToOCRMode"), object: nil)
                            },
                            style: .secondary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "等待2分钟后重试",
                            action: {
                                // 🇭🇰 香港地区需要更长等待时间
                                DispatchQueue.main.asyncAfter(deadline: .now() + 120.0) {
                                    NotificationCenter.default.post(name: Notification.Name("RetryProcessing"), object: nil)
                                }
                            },
                            style: .secondary
                        )
                    ]
                )
            case .networkError:
                // 显示专门的网络错误视图
                DispatchQueue.main.async {
                    self.networkError = error
                    self.showingNetworkErrorView = true
                }

                return ProcessingError(
                    type: .networkError,
                    title: "网络连接不稳定",
                    message: "AI服务需要稳定的网络连接。系统已自动重试3次，建议检查网络后重试，或使用离线OCR模式。",
                    suggestions: [
                        ProcessingError.ErrorSuggestion(
                            title: "查看详细信息",
                            action: {
                                self.networkError = error
                                self.showingNetworkErrorView = true
                            },
                            style: .primary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "使用离线OCR",
                            action: {
                                NotificationCenter.default.post(name: Notification.Name("SwitchToOCRMode"), object: nil)
                            },
                            style: .secondary
                        )
                    ]
                )
            case .rateLimitExceeded:
                return ProcessingError(
                    type: .rateLimitExceeded,
                    title: "API使用频率过高",
                    message: "OpenRouter API对免费用户有严格的频率限制。建议等待短时间后重试，或使用OCR模式。系统已优化为智能处理，成功时立即继续。",
                    suggestions: [
                        ProcessingError.ErrorSuggestion(
                            title: "使用OCR模式",
                            action: {
                                NotificationCenter.default.post(name: Notification.Name("SwitchToOCRMode"), object: nil)
                            },
                            style: .primary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "等待10秒后重试",
                            action: {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
                                    NotificationCenter.default.post(name: Notification.Name("RetryAIProcessing"), object: nil)
                                }
                            },
                            style: .secondary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "只处理1张图片",
                            action: {
                                // 关闭错误sheet，让用户重新选择图片
                                NotificationCenter.default.post(name: Notification.Name("ReduceImageCount"), object: nil)
                            },
                            style: .secondary
                        )
                    ]
                )
            case .imageTooLarge:
                return ProcessingError(
                    type: .imageProcessingFailed,
                    title: "图片过大",
                    message: "图片文件过大，超出了AI服务的处理限制。建议压缩图片或使用OCR模式处理。",
                    suggestions: [
                        ProcessingError.ErrorSuggestion(
                            title: "使用OCR模式",
                            action: {
                                NotificationCenter.default.post(name: Notification.Name("SwitchToOCRMode"), object: nil)
                            },
                            style: .primary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "压缩图片后重试",
                            action: {
                                // 提示用户压缩图片
                                NotificationCenter.default.post(name: Notification.Name("CompressImageAndRetry"), object: nil)
                            },
                            style: .secondary
                        ),
                        ProcessingError.ErrorSuggestion(
                            title: "选择其他图片",
                            action: {
                                // 关闭错误sheet，让用户重新选择图片
                                NotificationCenter.default.post(name: Notification.Name("SelectDifferentImage"), object: nil)
                            },
                            style: .secondary
                        )
                    ]
                )
            default:
                return ProcessingError(
                    type: .unknown,
                    title: "处理失败",
                    message: error.localizedDescription,
                    suggestions: [
                        ProcessingError.ErrorSuggestion(
                            title: "重试",
                            action: {
                                NotificationCenter.default.post(name: Notification.Name("RetryAIProcessing"), object: nil)
                            },
                            style: .primary
                        )
                    ]
                )
            }
        } else {
            return ProcessingError(
                type: .unknown,
                title: "processing_failed".localized,
                message: error.localizedDescription,
                suggestions: [
                    ProcessingError.ErrorSuggestion(
                        title: "retry".localized,
                        action: {
                            NotificationCenter.default.post(name: Notification.Name("RetryProcessing"), object: nil)
                        },
                        style: .primary
                    )
                ]
            )
        }
    }

    // MARK: - 错误处理操作
    private func retryProcessing() {
        showingErrorSheet = false
        currentError = nil

        // 🎯 不自动重新处理，让用户手动点击按钮
        Logger.aiInfo("🔄 错误重试：等待用户手动重新开始")
    }

    private func switchToOCRMode() {
        showingErrorSheet = false
        currentError = nil

        // 🎯 不自动重新处理，让用户手动点击按钮
        Logger.aiInfo("🔄 切换到OCR模式：等待用户手动重新开始")
    }

    private func reduceImageCount() {
        showingErrorSheet = false
        currentError = nil

        // 🚦 由于API频率限制严格，只保留第1张图片
        if selectedImages.count > 1 {
            selectedImages = Array(selectedImages.prefix(1))
            Logger.aiInfo("🚦 已减少图片数量至\(selectedImages.count)张，避免API频率限制")
        }

        // 🚀 支持多图片叠加识别：不清空已有地址，保留用户已识别的结果
        // recognizedAddresses = [] // 注释掉这行，保留已识别的地址
        isProcessing = false
    }

    private func tryFirebaseAI() {
        showingErrorSheet = false
        currentError = nil

        // 使用Firebase AI处理图片
        Task {
            await processImagesWithFirebaseAI()
        }
    }

    private func processImagesWithFirebaseAI() async {
        guard !selectedImages.isEmpty else {
            isProcessing = false
            return
        }

        await MainActor.run {
            isProcessing = true
            processingStatus = "processing_images".localized
            // 🚀 支持多图片叠加识别：不清空已有地址，而是继续添加
            // recognizedAddresses = [] // 注释掉这行，保留已识别的地址
        }

        for (index, image) in selectedImages.enumerated() {
            await MainActor.run {
                currentProcessingImage = index + 1
                processingProgress = Double(index) / Double(selectedImages.count)
                processingStatus = String(format: "processing_image_progress".localized, index + 1, selectedImages.count)
            }

            await processImageWithFirebaseAI(image, imageIndex: index)

            // 🚀 优化：Firebase AI处理后立即继续，无需延迟
        }

        await MainActor.run {
            processingProgress = 1.0
            isProcessing = false
            processingStatus = "processing_complete".localized

            // ⏱️ 记录Firebase AI处理结束时间
            if let startTime = analysisStartTime {
                analysisEndTime = Date()
                let duration = analysisEndTime!.timeIntervalSince(startTime)
                Logger.aiInfo("⏱️ Firebase AI处理完成，耗时: \(String(format: "%.2f", duration))秒")
            }

            // 🎯 重新分配连续的序号，解决多张图片序号重复问题
            reassignSortNumbers()

            // 🎯 AI识别完成，不需要检查网络错误
            // AI识别的地址使用临时坐标是正常的，不是错误
        }
    }

    private func processImageWithFirebaseAI(_ image: UIImage, imageIndex: Int) async {
        do {
            // 🚀 智能检测超长图片并自动分割
            if shouldSplitImage(image) {
                Logger.aiInfo("🔥 Firebase AI检测到超长图片，启动智能分割处理")
                await processImageWithFirebaseAISplitting(image, imageIndex: imageIndex)
                return
            }

            let result = try await firebaseAIService.extractAddressesFromImage(image, appType: selectedAppType)

            await MainActor.run {
                aiModelUsed = result.modelUsed
                aiConfidence = result.confidence
                recognitionMethod = "Advanced Recognition"
                processingStatus = "validating_addresses".localized

                // 🆕 保存检测到的总数
                if let detectedTotal = result.detectedTotalCount {
                    detectedTotalCount = detectedTotal
                    Logger.aiInfo("📊 从FirebaseAI检测到总数: \(detectedTotal)")
                }
            }

            // 🎯 Firebase AI识别完成后直接保存，然后批量进行坐标验证
            // AI的作用只是OCR识别地址、订单号、排序号，不负责坐标验证
            var addressesToVerify: [(String, Int)] = []

            for address in result.addresses {
                // 🎯 使用DeliveryPointManager分离地址信息，检查是否有实际地址
                let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                let rawAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查分离后的地址是否为空或无效
                if rawAddress.isEmpty || rawAddress.count < 3 {
                    Logger.aiWarning("跳过无效地址（无实际地址内容）: '\(address)' -> 分离后地址: '\(rawAddress)'")
                    continue
                }

                // 🎯 对于SpeedX，应用Apple Maps格式化
                let actualAddress = formatAddressForSpeedX(rawAddress)

                // 记录格式化过程（仅在有变化时）
                if selectedAppType == .speedx && actualAddress != rawAddress {
                    Logger.aiInfo("🍎 SpeedX Firebase AI地址格式化: '\(rawAddress)' -> '\(actualAddress)'")
                }

                // 🎯 Firebase AI识别的地址先保存，稍后批量验证
                let addressIndex = await MainActor.run { () -> Int in
                    // 🎯 在Firebase AI识别的地址中添加用户选择的应用类型标签
                    let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto)
                    let finalAddress = shouldAddAppTag ? "\(address)|APP:\(selectedAppType.rawValue)" : address



                    // 先添加临时坐标，然后批量验证
                    let tempCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                    recognizedAddresses.append((finalAddress, tempCoordinate, true, false, 0.8))

                    // 返回刚添加的地址的索引
                    return recognizedAddresses.count - 1
                }

                // 记录需要验证的地址和正确的索引（使用格式化后的地址）
                addressesToVerify.append((actualAddress, addressIndex))

                Logger.aiInfo("✅ Firebase AI识别地址已保存: \(actualAddress), 索引: \(addressIndex)")
            }

            // 🚦 批量串行验证坐标，避免并发请求超限
            if !addressesToVerify.isEmpty {
                Logger.aiInfo("🚦 开始串行验证 \(addressesToVerify.count) 个地址的坐标")

                await batchVerifyAddressCoordinates(addressesToVerify)
            }

        } catch {
            await MainActor.run {
                errorMessage = "Firebase AI处理失败: \(error.localizedDescription)"
                Logger.aiError("Firebase AI处理失败: \(error)")
            }
        }
    }

    private func processImagesWithOCRMode() async {
        guard !selectedImages.isEmpty else {
            isProcessing = false
            return
        }

        await MainActor.run {
            isProcessing = true
            processingStatus = "processing_images".localized
            // 🚀 支持多图片叠加识别：不清空已有地址，而是继续添加
            // recognizedAddresses = [] // 注释掉这行，保留已识别的地址
        }

        // 🚦 OCR模式也使用智能延迟，避免地理编码API限制
        for (index, image) in selectedImages.enumerated() {
            await MainActor.run {
                currentProcessingImage = index + 1
                processingProgress = Double(index) / Double(selectedImages.count)
                processingStatus = String(format: "processing_image_progress".localized, index + 1, selectedImages.count)
            }

            await processImageWithOCRMode(image, imageIndex: index)

            // 🚀 优化：OCR处理后立即继续，地理编码限制由GlobalGeocodingRateLimiter处理
        }

        await MainActor.run {
            processingProgress = 1.0
            isProcessing = false
            processingStatus = "ocr_processing_complete".localized

            // ⏱️ 记录OCR处理结束时间
            if let startTime = analysisStartTime {
                analysisEndTime = Date()
                let duration = analysisEndTime!.timeIntervalSince(startTime)
                Logger.aiInfo("⏱️ OCR处理完成，耗时: \(String(format: "%.2f", duration))秒")
            }

            // 🎯 重新分配连续的序号，解决多张图片序号重复问题
            reassignSortNumbers()

            // 🎯 OCR识别完成，不需要检查网络错误
            // AI识别的地址使用临时坐标是正常的，不是错误
        }
    }

    private func processImageWithOCRMode(_ image: UIImage, imageIndex: Int) async {
        do {
            // 强制使用OCR模式
            let result = try await hybridService.recognizeAddresses(
                from: image,
                mode: .ocrOnly,
                appType: selectedAppType
            ) { status in
                Task { @MainActor in
                    processingStatus = status
                }
            }

            await MainActor.run {
                recognitionMethod = "OCR Only"
                processingStatus = "validating_addresses".localized
            }

            // 🎯 OCR识别完成后直接保存，不进行地理编码验证
            // AI的作用只是OCR识别地址、订单号、排序号，不负责坐标验证
            for address in result.addresses {
                // 🎯 使用DeliveryPointManager分离地址信息，检查是否有实际地址
                let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                let actualAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查分离后的地址是否为空或无效
                if actualAddress.isEmpty || actualAddress.count < 3 {
                    Logger.aiWarning("跳过无效地址（无实际地址内容）: '\(address)' -> 分离后地址: '\(actualAddress)'")
                    continue
                }

                // 🎯 OCR识别的地址立即进行坐标验证
                let addressIndex = await MainActor.run { () -> Int in
                    // 🎯 在OCR识别的地址中添加用户选择的应用类型标签
                    let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto)
                    let finalAddress = shouldAddAppTag ? "\(address)|APP:\(selectedAppType.rawValue)" : address

                    // 先添加临时坐标，然后异步验证
                    let tempCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                    recognizedAddresses.append((finalAddress, tempCoordinate, true, false, 0.8))

                    // 返回刚添加的地址的索引
                    return recognizedAddresses.count - 1
                }

                Logger.aiInfo("✅ OCR识别地址已保存，开始坐标验证: \(address), 索引: \(addressIndex)")

                // 🚀 立即进行地址验证和坐标获取
                Task {
                    await verifyAndUpdateAddressCoordinate(address: address, index: addressIndex)
                }
            }

        } catch {
            await MainActor.run {
                errorMessage = "OCR处理失败: \(error.localizedDescription)"
                Logger.aiError("OCR处理失败: \(error)")
            }
        }
    }
}

// MARK: - 子视图组件
extension ImageAddressRecognizer {

    // 顶部内容区域（描述文字和图片预览）
    private var topContentView: some View {
        VStack(spacing: 8) {
            // 描述区域 - 当有图片时隐藏，避免UI叠加
            if selectedImages.isEmpty {
                Text("delivery_app_screenshot_description".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.top, 8)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }

            // 图片预览区域 - 当有图片时显示
            if !selectedImages.isEmpty {
                imagePreviewView
            }
        }
        .animation(.easeInOut(duration: 0.4), value: selectedImages.isEmpty)
        .sheet(isPresented: $showingImageViewer) {
            ImageViewerSheet(
                images: selectedImages,
                selectedIndex: $selectedImageIndex,
                onDelete: { index in
                    removeImage(at: index)
                    showingImageViewer = false
                },
                onReorder: { from, to in
                    moveImage(from: from, to: to)
                }
            )
        }
    }

    // 图片预览视图
    private var imagePreviewView: some View {
        VStack(spacing: 16) {
            // 🔢 快速排序按钮（仅在多图片时显示）
            if selectedImages.count > 1 {
                HStack {
                    Spacer()

                    // 快速排序按钮
                    Button {
                        reverseImageOrder()
                    } label: {
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.up.arrow.down")
                                .font(.caption)
                            Text(NSLocalizedString("reverse", comment: ""))
                                .font(.caption)
                        }
                        .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)
            }

            // 图片列表（支持拖拽排序）
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(0..<selectedImages.count, id: \.self) { index in
                        imageItemView(at: index)
                            .onDrag {
                                // 🎯 设置拖拽状态
                                draggedImageIndex = index
                                print("🎯 开始拖拽图片: \(index)")
                                return NSItemProvider(object: "\(index)" as NSString)
                            }
                            .onDrop(of: [.text], delegate: ImageDropDelegate(
                                destinationIndex: index,
                                images: $selectedImages,
                                imageSourceTypes: $imageSourceTypes,
                                moveAction: moveImage,
                                draggedIndex: $draggedImageIndex
                            ))
                            .scaleEffect(draggedImageIndex == index ? 0.95 : 1.0)
                            .opacity(draggedImageIndex == index ? 0.7 : 1.0)
                            .animation(.easeInOut(duration: 0.2), value: draggedImageIndex)
                    }
                }
                .padding(.horizontal)
            }
            .frame(height: 92)

            // 🎯 新增：开始分析按钮
            if imagesReadyForProcessing && !isProcessing {
                startAnalysisButton
            }
        }
        .transition(.opacity.combined(with: .move(edge: .top)))
    }

    // 单个图片项视图
    private func imageItemView(at index: Int) -> some View {
        ZStack {
            // 主图片
            Image(uiImage: selectedImages[index])
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 80, height: 80)
                .clipped()
                .cornerRadius(8)
                .onTapGesture {
                    // 🎯 新增：点击查看大图
                    selectedImageIndex = index
                    showingImageViewer = true
                }

            // 🔢 图片编号标记（左上角）- 与关闭图标样式一致
            VStack {
                HStack {
                    ZStack {
                        // 🎯 使用与关闭图标相同的样式
                        Circle()
                            .fill(Color.black.opacity(0.8))
                            .frame(width: 24, height: 24)

                        Circle()
                            .stroke(Color.white, lineWidth: 1)
                            .frame(width: 24, height: 24)

                        // 🎯 显示原始帧编号（如果有的话）
                        Text(getDisplayNumber(for: index))
                            .font(.system(size: 11, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .offset(x: -4, y: 4)

                    Spacer()
                }
                Spacer()
            }

            // 删除按钮（右上角）
            VStack {
                HStack {
                    Spacer()

                    Button {
                        removeImage(at: index)
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 20)) // 🎯 与编号图标保持一致的大小
                            .foregroundColor(.red)
                            .background(Color.white)
                            .clipShape(Circle())
                    }
                    .offset(x: 6, y: -6)
                }
                Spacer()
            }
        }
        .padding(.top, 6)
        .padding(.trailing, 6)
        .padding(.leading, 6)
    }

    // 🎯 获取显示编号的方法
    private func getDisplayNumber(for index: Int) -> String {
        // 如果有原始帧编号信息，使用原始编号；否则使用序号
        if index < originalFrameIndices.count {
            return "\(originalFrameIndices[index])"
        } else {
            return "\(index + 1)"
        }
    }

    // 图片选择器视图
    private var imagePickerView: some View {
        VStack(spacing: 12) {
            // 📷📹 照片和视频选择器（并排布局）
            HStack(spacing: 12) {
                // 📷 照片选择器（仅支持图片）
                Group {
                    if #available(iOS 16.0, *) {
                        PhotosPicker(
                            selection: $selectedItems,
                            maxSelectionCount: 50,
                            matching: .images,
                            preferredItemEncoding: .automatic
                        ) {
                            compactPickerContent
                        }
                    } else {
                        PhotosPicker(
                            selection: $selectedItems,
                            maxSelectionCount: 50,
                            matching: .images
                        ) {
                            compactPickerContent
                        }
                    }
                }
                .frame(maxWidth: .infinity)

                // 🎬 视频选择器（Pro功能）
                compactVideoPickerView
                    .frame(maxWidth: .infinity)
            }

            // 📄 智能PDF处理器（推荐）- 暂时隐藏
            /*
            Button(action: {
                showingSmartPDFProcessor = true
            }) {
                smartPDFProcessorContent
            }
            */

            // 传统PDF文件选择器 - 暂时屏蔽
            /*
            Button(action: {
                isShowingDocumentPicker = true
            }) {
                pdfPickerContent
            }
            .fileImporter(
                isPresented: $isShowingDocumentPicker,
                allowedContentTypes: [.pdf],
                allowsMultipleSelection: true
            ) { result in
                handleDocumentPickerResult(result)
            }
            */
        }
    }

    // 照片选择器内容视图
    private var pickerContent: some View {
        VStack(spacing: 12) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.largeTitle)
                .foregroundColor(.adaptivePrimaryIcon)
                .animation(.none, value: selectedImages.count)

            Text("select_photos".localized)
                .font(.headline)
                .foregroundColor(.adaptivePrimaryIcon)
                .multilineTextAlignment(.center)
                .animation(.none, value: selectedImages.count)

            Text("tap_to_select_from_photos".localized)
                .font(.caption)
                .foregroundColor(.adaptiveSecondaryText)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .background(Color.adaptivePrimaryIcon.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.adaptivePrimaryIcon, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
        .onChange(of: selectedItems) { _, items in
            // 🎯 重置状态
            imagesReadyForProcessing = false

            // 清空之前的识别结果
            recognizedAddresses = []

            Task {
                await loadMediaItems(from: items)
            }
        }
        .padding(.horizontal)
    }

    // 📷 紧凑版照片选择器内容
    private var compactPickerContent: some View {
        VStack(spacing: 8) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.title2)
                .foregroundColor(.adaptivePrimaryIcon)

            Text("select_photos".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.adaptivePrimaryIcon)
                .multilineTextAlignment(.center)

            Text("photo_recognition".localized)
                .font(.caption2)
                .foregroundColor(.adaptiveSecondaryText)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 100)
        .background(Color.adaptivePrimaryIcon.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.adaptivePrimaryIcon, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
        .onChange(of: selectedItems) { _, items in
            // 🎯 重置状态
            imagesReadyForProcessing = false

            // 清空之前的识别结果
            recognizedAddresses = []

            Task {
                await loadMediaItems(from: items)
            }
        }
    }

    // 🎬 视频选择器视图
    private var videoPickerView: some View {
        Group {
            if subscriptionManager.currentTier.allowsVideoProcessing {
                // Pro/Expert用户：显示可用的视频选择器
                Group {
                    if #available(iOS 16.0, *) {
                        PhotosPicker(
                            selection: $selectedVideoItems,
                            maxSelectionCount: 5,
                            matching: .videos,
                            preferredItemEncoding: .automatic
                        ) {
                            videoPickerContent
                        }
                    } else {
                        PhotosPicker(
                            selection: $selectedVideoItems,
                            maxSelectionCount: 5,
                            matching: .videos
                        ) {
                            videoPickerContent
                        }
                    }
                }
                .onChange(of: selectedVideoItems) { _, items in
                    // 🎯 重置状态
                    imagesReadyForProcessing = false
                    recognizedAddresses = []
                    Task {
                        await loadVideoItems(from: items)
                    }
                }
            } else {
                // 免费用户：显示升级提示
                Button(action: {
                    showingSubscriptionView = true
                }) {
                    videoPickerProContent
                }
            }
        }
        .sheet(isPresented: $showingNetworkErrorView) {
            if let error = networkError {
                AINetworkErrorView(
                    error: error,
                    onRetry: {
                        showingNetworkErrorView = false
                        retryProcessing()
                    },
                    onSwitchToOCR: {
                        showingNetworkErrorView = false
                        switchToOCRMode()
                    },
                    onDismiss: {
                        showingNetworkErrorView = false
                    }
                )
            }
        }
    }

    // 🎬 紧凑版视频选择器视图
    private var compactVideoPickerView: some View {
        Group {
            if subscriptionManager.currentTier.allowsVideoProcessing {
                // Pro用户：显示可用的视频选择器
                Group {
                    if #available(iOS 16.0, *) {
                        PhotosPicker(
                            selection: $selectedVideoItems,
                            maxSelectionCount: 5,
                            matching: .videos,
                            preferredItemEncoding: .automatic
                        ) {
                            compactVideoPickerContent
                        }
                    } else {
                        PhotosPicker(
                            selection: $selectedVideoItems,
                            maxSelectionCount: 5,
                            matching: .videos
                        ) {
                            compactVideoPickerContent
                        }
                    }
                }
                .onChange(of: selectedVideoItems) { _, items in
                    Task {
                        await loadVideoItems(from: items)
                    }
                }
            } else {
                // 免费用户：显示升级提示
                Button(action: {
                    showingSubscriptionView = true
                }) {
                    compactVideoPickerProContent
                }
            }
        }
        .sheet(isPresented: $showingSubscriptionView) {
            SubscriptionView()
        }
    }

    // 视频选择器内容（Pro用户可用）
    private var videoPickerContent: some View {
        VStack(spacing: 12) {
            Image(systemName: "video.badge.plus")
                .font(.largeTitle)
                .foregroundColor(.orange)
                .animation(.none, value: selectedVideos.count)

            Text("select_videos".localized)
                .font(.headline)
                .foregroundColor(.orange)
                .multilineTextAlignment(.center)
                .animation(.none, value: selectedVideos.count)

            VStack(spacing: 4) {
                Text("video_to_long_image".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                HStack(spacing: 4) {
                    Image(systemName: "crown.fill")
                        .font(.caption2)
                        .foregroundColor(.orange)

                    Text("pro_tier_name".localized)
                        .font(.caption2)
                        .foregroundColor(.orange)
                        .fontWeight(.bold)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(Color.orange.opacity(0.2))
                .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 130)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
        .padding(.horizontal)
    }

    // 视频选择器内容（免费用户看到的Pro提示）
    private var videoPickerProContent: some View {
        VStack(spacing: 12) {
            Image(systemName: "video.badge.plus")
                .font(.largeTitle)
                .foregroundColor(.gray)
                .animation(.none, value: selectedVideos.count)

            Text("select_videos".localized)
                .font(.headline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .animation(.none, value: selectedVideos.count)

            VStack(spacing: 4) {
                Text("video_to_long_image".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                HStack(spacing: 4) {
                    Image(systemName: "crown.fill")
                        .font(.caption2)
                        .foregroundColor(.orange)

                    Text("upgrade_unlock".localized)
                        .font(.caption2)
                        .foregroundColor(.orange)
                        .fontWeight(.bold)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(Color.orange.opacity(0.2))
                .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 130)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
        .padding(.horizontal)
    }

    // 🎬 紧凑版视频选择器内容（Pro用户可用）
    private var compactVideoPickerContent: some View {
        VStack(spacing: 8) {
            Image(systemName: "video.badge.plus")
                .font(.title2)
                .foregroundColor(.orange)

            Text("select_videos".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.orange)
                .multilineTextAlignment(.center)

            HStack(spacing: 4) {
                Image(systemName: "crown.fill")
                    .font(.caption2)
                    .foregroundColor(.orange)

                Text("video_to_long_image".localized)
                    .font(.caption2)
                    .foregroundColor(.orange)
                    .fontWeight(.bold)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 100)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
    }

    // 🎬 紧凑版视频选择器内容（免费用户看到的Pro提示）
    private var compactVideoPickerProContent: some View {
        VStack(spacing: 8) {
            Image(systemName: "video.badge.plus")
                .font(.title2)
                .foregroundColor(.gray)

            Text("select_videos".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)

            HStack(spacing: 4) {
                Image(systemName: "crown.fill")
                    .font(.caption2)
                    .foregroundColor(.orange)

                Text("upgrade_unlock".localized)
                    .font(.caption2)
                    .foregroundColor(.orange)
                    .fontWeight(.bold)
            }
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.orange.opacity(0.2))
            .cornerRadius(6)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 100)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
    }

    // 📄 智能PDF处理器内容视图
    private var smartPDFProcessorContent: some View {
        VStack(spacing: 8) {
            Image(systemName: "doc.text.fill")
                .font(.title2)
                .foregroundColor(.adaptivePrimaryIcon)

            Text("select_pdfs".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.adaptivePrimaryIcon)
                .multilineTextAlignment(.center)

            Text("pdf_recognition".localized)
                .font(.caption2)
                .foregroundColor(.adaptiveSecondaryText)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 100)
        .background(Color.adaptivePrimaryIcon.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.adaptivePrimaryIcon, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
    }

    // PDF选择器内容视图 - 暂时屏蔽
    /*
    private var pdfPickerContent: some View {
        VStack(spacing: 12) {
            Image(systemName: "doc.text.fill")
                .font(.largeTitle)
                .foregroundColor(.orange)
                .animation(.none, value: selectedImages.count + videoFrames.count + pdfPages.count)

            Text("传统PDF处理")
                .font(.headline)
                .foregroundColor(.orange)
                .multilineTextAlignment(.center)
                .animation(.none, value: selectedImages.count + videoFrames.count + pdfPages.count)

            Text("适用于小型PDF文件")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            supportedAppsView
        }
        .frame(maxWidth: .infinity)
        .frame(height: 140)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange, style: StrokeStyle(lineWidth: 2, dash: [5]))
        )
        .padding(.horizontal)
    }
    */

    // 应用类型选择器视图 - 智能国家检测
    private var appTypeSelectorView: some View {
        VStack(spacing: 16) {
            // 国家检测组件 - 带回调支持
            CountryDetectionViewWithCallback { country in
                selectedCountry = country
            }

            // 根据选中的国家显示对应的快递服务
            let currentCountry = selectedCountry ?? getDetectedCountry() ?? .unitedStates
            regionSelectorView(
                region: currentCountry.deliveryRegion,
                title: "", // 移除重复标题
                apps: getAppsForCountry(currentCountry),
                color: currentCountry.themeColor
            )
        }
        .padding(.horizontal)
    }

    // 地区选择器视图
    private func regionSelectorView(region: DeliveryRegion, title: String, apps: [DeliveryAppType], color: Color) -> some View {
        VStack(spacing: 8) {
            // 移除标题显示，因为上面的国家检测已经显示了

            // 水平滚动布局 - 节省垂直空间
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(apps, id: \.self) { appType in
                        Button(action: {
                            selectedAppType = appType
                            print("🎯 DEBUG: 用户选择 \(title) - \(appType.displayName)")
                            print("🎯 DEBUG: selectedAppType已更新为: \(selectedAppType.rawValue)")

                            // 🔍 打印AI提示词日志
                            printAIPromptForAppType(appType)
                        }) {
                            Text(appType.displayName)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(selectedAppType == appType ? .white : .adaptivePrimaryText)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12) // 增加上下 padding 从 8 到 12
                                .background(selectedAppType == appType ? color : Color.adaptiveSecondaryButton)
                                .cornerRadius(16)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(selectedAppType == appType ? color : Color.adaptiveBorder,
                                               lineWidth: selectedAppType == appType ? 2 : 1)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .frame(minHeight: 40) // 增加最小高度从 32 到 40
                    }
                }
                .padding(.horizontal)
            }
        }
    }



    // 支持的应用描述视图
    private var supportedAppsView: some View {
        VStack(spacing: 4) {
            Text("supported_delivery_apps".localized)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Text("amazon_flex_imile_pdf_support".localized)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.blue)
                .multilineTextAlignment(.center)
        }
        .animation(.none, value: selectedImages.count)
    }

    // 移除复杂的补充模式指示器视图 - 保持简洁的用户体验

    // 处理状态视图
    private var processingStatusView: some View {
        Group {
            if isProcessing {
                VStack(spacing: 16) {
                    // 识别状态指示器
                    HStack(spacing: 12) {
                        ProgressView()
                            .scaleEffect(0.8)

                        Text(processingStatus.isEmpty ? "analyzing_images".localized : processingStatus)
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Spacer()

                        // 隐藏技术细节以保护商业机密
                        // 状态指示器已隐藏
                    }

                    // 进度条
                    VStack(spacing: 8) {
                        ProgressView(value: processingProgress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle())

                        HStack {
                            Text(String(format: "processing_image_progress".localized, currentProcessingImage, selectedImages.count))
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            Text("\(Int(processingProgress * 100))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
                .frame(minHeight: 120)
            } else {
                // 占位空间，避免布局跳动
                Spacer()
                    .frame(height: 0)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isProcessing)
    }
}

// MARK: - 支持视图

// 错误处理Sheet
struct ErrorHandlingSheet: View {
    let error: ProcessingError
    let onDismiss: () -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 错误图标和标题
                VStack(spacing: 16) {
                    Image(systemName: iconForErrorType(error.type))
                        .font(.system(size: 48))
                        .foregroundColor(colorForErrorType(error.type))

                    Text(error.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)

                    Text(error.message)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                // 建议操作
                VStack(spacing: 12) {
                    ForEach(Array(error.suggestions.enumerated()), id: \.offset) { index, suggestion in
                        Button(action: {
                            suggestion.action()
                            onDismiss()
                        }) {
                            HStack {
                                Text(suggestion.title)
                                    .fontWeight(.medium)
                                Spacer()
                                Image(systemName: "arrow.right")
                                    .font(.caption)
                            }
                            .padding()
                            .background(backgroundColorForStyle(suggestion.style))
                            .foregroundColor(textColorForStyle(suggestion.style))
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .padding()
            .navigationTitle("处理错误")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        onDismiss()
                    }
                }
            }
        }
        .presentationDetents([.medium])
        .presentationDragIndicator(.visible)
    }

    private func iconForErrorType(_ type: ProcessingError.ErrorType) -> String {
        switch type {
        case .networkError:
            return "wifi.exclamationmark"
        case .aiModelUnavailable:
            return "exclamationmark.triangle.fill"
        case .imageProcessingFailed:
            return "photo.badge.exclamationmark"
        case .rateLimitExceeded:
            return "clock.badge.exclamationmark"
        case .unknown:
            return "exclamationmark.triangle"
        }
    }

    private func colorForErrorType(_ type: ProcessingError.ErrorType) -> Color {
        switch type {
        case .networkError:
            return .orange
        case .aiModelUnavailable:
            return .orange
        case .imageProcessingFailed:
            return .red
        case .rateLimitExceeded:
            return .yellow
        case .unknown:
            return .gray
        }
    }

    private func backgroundColorForStyle(_ style: ProcessingError.ErrorSuggestion.SuggestionStyle) -> Color {
        switch style {
        case .primary:
            return .blue
        case .secondary:
            return Color(.systemGray5)
        case .destructive:
            return .red
        }
    }

    private func textColorForStyle(_ style: ProcessingError.ErrorSuggestion.SuggestionStyle) -> Color {
        switch style {
        case .primary:
            return .white
        case .secondary:
            return .primary
        case .destructive:
            return .white
        }
    }
}

// 识别状态指示器
struct RecognitionStatusIndicator: View {
    let isUsingAdvanced: Bool
    let modelUsed: String
    let confidence: Double
    let recognitionMethod: String

    var body: some View {
        HStack(spacing: 6) {
            // 根据识别方法显示不同图标
            Image(systemName: getIconForMethod())
                .foregroundColor(getColorForMethod())
                .font(.caption)

            Text(getDisplayText())
                .font(.caption2)
                .foregroundColor(getColorForMethod())

            if confidence > 0 {
                Text("\(Int(confidence * 100))%")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    private func getIconForMethod() -> String {
        switch recognitionMethod {
        case "OCR + AI", "混合识别":
            return "eye.trianglebadge.exclamationmark"
        case "AI Vision", "高级识别":
            return "brain.head.profile"
        case "OCR Only", "基础识别":
            return "camera.viewfinder"
        case "AI Only", "智能识别":
            return "brain"
        default:
            return isUsingAdvanced ? "brain.head.profile" : "camera.viewfinder"
        }
    }

    private func getColorForMethod() -> Color {
        switch recognitionMethod {
        case "OCR + AI", "混合识别":
            return .blue
        case "AI Vision", "高级识别":
            return .green
        case "OCR Only", "基础识别":
            return .orange
        case "AI Only", "智能识别":
            return .purple
        default:
            return isUsingAdvanced ? .green : .orange
        }
    }

    private func getDisplayText() -> String {
        switch recognitionMethod {
        case "OCR + AI", "混合识别":
            return "混合"
        case "AI Vision", "高级识别":
            return "高级"
        case "OCR Only", "基础识别":
            return "OCR"
        case "AI Only", "智能识别":
            return "智能"
        default:
            return isUsingAdvanced ? "智能" : "OCR"
        }
    }
}

// 识别状态视图
struct RecognitionStatusView: View {
    let status: String
    let isAnimating: Bool
    let isUsingAdvanced: Bool
    let modelUsed: String
    let confidence: Double
    let recognitionMethod: String

    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 12) {
                if isAnimating {
                    ProgressView()
                        .scaleEffect(0.8)
                }

                Text(status)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                // 识别状态指示器
                RecognitionStatusIndicator(
                    isUsingAdvanced: isUsingAdvanced,
                    modelUsed: modelUsed,
                    confidence: confidence,
                    recognitionMethod: recognitionMethod
                )
            }

            // 模型信息（仅在使用高级识别时显示）
            if isUsingAdvanced && !modelUsed.isEmpty {
                HStack {
                    Text(String(format: "model_used".localized, modelUsed))
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }
        }
    }
}

#Preview("ImageAddressRecognizer") {
    ImageAddressRecognizer(
        onAddressesConfirmed: { addresses, appType in
            print("Selected \(addresses.count) addresses with app type: \(appType.displayName)")
        },
        onDismiss: {
            print("Dismissed")
        }
    )
}

#Preview("Error Handling") {
    ErrorHandlingSheet(
        error: ProcessingError(
            type: .aiModelUnavailable,
            title: "智能识别暂时不可用",
            message: "网络连接问题导致智能识别服务无法使用。您可以重试或切换到OCR模式继续处理图片。",
            suggestions: [
                ProcessingError.ErrorSuggestion(
                    title: "重试智能识别",
                    action: {
                        print("Retry Processing")
                        NotificationCenter.default.post(name: Notification.Name("RetryProcessing"), object: nil)
                    },
                    style: .primary
                ),
                ProcessingError.ErrorSuggestion(
                    title: "切换到OCR模式",
                    action: {
                        print("Switch to OCR")
                        NotificationCenter.default.post(name: Notification.Name("SwitchToOCRMode"), object: nil)
                    },
                    style: .secondary
                ),
                ProcessingError.ErrorSuggestion(
                    title: "检查网络连接",
                    action: {
                        print("Check network")
                        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(settingsUrl)
                        }
                    },
                    style: .secondary
                )
            ]
        ),
        onDismiss: { print("Dismissed") }
    )
}

// MARK: - 🔍 AI提示词调试功能扩展
extension ImageAddressRecognizer {

    /// 打印指定快递类型的AI提示词
    private func printAIPromptForAppType(_ appType: DeliveryAppType) {
        print("🔍 ==================== AI提示词调试 ====================")
        print("🔍 快递类型: \(appType.displayName) (\(appType.rawValue))")
        print("🔍 地区: \(appType.region.rawValue)")
        print("🔍 ========================================================")

        // 获取Firebase AI提示词
        let firebasePrompt = createFirebaseAIPromptForDebug(appType: appType)
        print("🔥 Firebase AI 提示词:")
        print("📄 \(firebasePrompt)")
        print("🔍 ========================================================")

        // 获取Gemma提示词
        let gemmaPrompt = createGemmaPromptForDebug(appType: appType)
        print("🤖 Gemma AI 提示词:")
        print("📄 \(gemmaPrompt)")
        print("🔍 ========================================================")
    }

    /// 创建Firebase AI提示词用于调试（复制自FirebaseAIService的逻辑）
    private func createFirebaseAIPromptForDebug(appType: DeliveryAppType, isPDFImage: Bool = false) -> String {
        let basePrompt: String
        switch appType {
        case .amazonFlex:
            basePrompt = createAmazonFlexPromptForDebug()
        case .imile:
            basePrompt = createiMilePromptForDebug()
        case .ldsEpod:
            basePrompt = createLDSEpodPromptForDebug()
        case .piggy:
            basePrompt = createPiggyPromptForDebug()
        case .uniuni:
            basePrompt = createUniUniPromptForDebug()
        case .gofo:
            basePrompt = createGoFoPromptForDebug()
        case .ywe:
            basePrompt = createYWEPromptForDebug()
        case .speedx:
            basePrompt = createSpeedXPromptForDebug()
        case .justPhoto, .manual:
            basePrompt = createGeneralAddressPromptForDebug()
        default:
            basePrompt = createGeneralAddressPromptForDebug()
        }

        let commonSuffix = """

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_only_if_found", "tracking_number": "tracking_if_found", "address": "formatted_address", "customer": "name_if_found", "delivery_time": "time_if_found"}]}

        Do not include any text before or after the JSON.
        """

        // GoFo特殊处理：不添加地区提示词，避免覆盖专用提示词的指令
        if appType == .gofo {
            return basePrompt + commonSuffix
        }
        return basePrompt + createRegionSpecificPromptForDebug(appType: appType) + commonSuffix
    }

    /// 创建Gemma提示词用于调试（复制自GemmaVisionService的逻辑）
    private func createGemmaPromptForDebug(appType: DeliveryAppType, isPDFImage: Bool = false) -> String {
        let basePrompt: String
        switch appType {
        case .amazonFlex:
            basePrompt = createAmazonFlexPromptForDebug()
        case .imile:
            basePrompt = createiMilePromptForDebug()
        case .ldsEpod:
            basePrompt = createLDSEpodPromptForDebug()
        case .piggy:
            basePrompt = createPiggyPromptForDebug()
        case .uniuni:
            basePrompt = createUniUniPromptForDebug()
        case .gofo:
            basePrompt = createGoFoPromptForDebug()
        case .ywe:
            basePrompt = createYWEPromptForDebug()
        case .speedx:
            basePrompt = createSpeedXPromptForDebug()
        case .justPhoto, .manual:
            basePrompt = createGeneralAddressPromptForDebug()
        default:
            basePrompt = createGeneralAddressPromptForDebug()
        }

        let commonSuffix = """

        You MUST respond with ONLY valid JSON in this exact format:
        {"success": true, "deliveries": [{"third_party_sort": "number_only_if_found", "tracking_number": "tracking_if_found", "address": "formatted_address", "customer": "name_if_found", "delivery_time": "time_if_found"}]}

        Do not include any text before or after the JSON.
        """

        // GoFo特殊处理：不添加地区提示词，避免覆盖专用提示词的指令
        if appType == .gofo {
            return basePrompt + commonSuffix
        }
        return basePrompt + createRegionSpecificPromptForDebug(appType: appType) + commonSuffix
    }

    // MARK: - 调试用提示词创建方法

    private func createAmazonFlexPromptForDebug() -> String {
        return """
        📦 Amazon Flex Delivery Recognition - Simplified Extraction:

        🎯 Core Information (Extract only these two items):
        1. Sort Number: Numbers from left timeline (8, 9, 10, 11, 12, 13, 14...)
        2. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "1721 MARINA CT APT D" → full: "1721 Marina Ct, Apt D, San Mateo, CA", address: "1721 Marina Ct, San Mateo, CA"

        🎯 Amazon Flex Interface Features:
        - Left timeline displays sort numbers
        - Blue circle icons
        - Tracking format: # B.L11.OV (ignore)
        - Delivery time information (ignore)
        - Address format: US addresses, may contain apartment numbers

        IMPORTANT:
        - Extract only sort numbers and addresses, ignore other information
        - Sort numbers come from left timeline numbers
        - Addresses must be in English format, no tracking numbers
        - If no apartment number, return only address field

        ⚡ Simplified recognition, focus on core information
        """
    }

    private func createiMilePromptForDebug() -> String {
        return """
        🚨 STOP! BEFORE DOING ANYTHING ELSE - READ THIS FIRST! 🚨

        ABSOLUTE RULE #1: NO TEST DATA ALLOWED
        If you see ANY of these words ANYWHERE in the image, immediately return:
        {"success": false, "deliveries": []}

        FORBIDDEN WORDS (ANY CAPITALIZATION):
        - amazon, Amazon Court, Fulham, Wellington, Taylors, ROWVILLE, rowville, Rowville
        - Margaret Johns, Sandra Tighe, Jenine Gray, Ann-Maree Mudie, Alison Rankcom
        - Any address with "3178" AND "Victoria" together
        - Any combination of the above

        DO NOT PROCEED IF YOU SEE ANY FORBIDDEN WORDS!

        ==========================================

        ONLY if the image contains NO forbidden words, then analyze this iMile delivery app screenshot.
        """
    }

    private func createLDSEpodPromptForDebug() -> String {
        return """
        📄 LDS EPOD Delivery Recognition - Smart Address Separation:

        🎯 Key Recognition:
        1. Sort Number: Sequential numbers (1, 2, 3, 4...)
        2. Complete Address: Including street, city, state, zipcode
        3. Order Number: CNUSUP + 11 digits format (e.g., CNUSUP00011738482)

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "500 King Dr, Apt 105" → full: "500 King Dr, Apt 105, Daly City, CA, 94015", street: "500 King Dr, Daly City, CA, 94015"

        ⚡ Simplified recognition, focus on core information
        """
    }

    private func createPiggyPromptForDebug() -> String {
        return """
        📦 PIGGY Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Sort Number: Sequential numbers (54, 55, 56, 57, 58, 59, 60, 61...) - numbers in red background
        2. Tracking Number: Two formats
           - PG + 11 digits (e.g., PG10005375906, PG10005376399, PG10005433664)
           - 14 pure digits (e.g., 20000060727717, 20000061579923, 20000060813327)
        3. Address: Smart apartment number separation
        4. Location Info: Mainly in Concord CA, zip codes 94520/94518

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "1530 Ellis Street Apt. 309" → full: "1530 Ellis Street Apt. 309, Concord, CA, 94520", street: "1530 Ellis Street, Concord, CA, 94520"

        CRITICAL: Look for the actual information in the image. In PIGGY screenshots, you will see:
        - Sequential numbers like: 54, 55, 56, 57, 58, 59, 60, 61, 62 (these are in red background)
        - Two tracking number formats as described above
        - Addresses like: "1530 Ellis Street Apt. 309", "1731 Ellis St Apt 54", "1441 Detroit Ave Apt 263"
        - Location: Concord CA with zip codes 94520, 94518
        """
    }

    private func createUniUniPromptForDebug() -> String {
        return """
        📦 UNIUNI Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Route Number: Three-digit numbers (149, 150, 151, 152, etc.) - large numbers on the left
        2. Tracking Number: UUS + 16 digits (e.g., UUS56D056436296171)
        3. Recipient: Complete English names (Faith quick, Allan Zehnder, Lelsie Drinkwater, Cynthia Baker)
        4. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        Apartment Number Keywords:
        - Apt, Apartment
        - Unit, Suite, Room, Rm, Ste
        - Standalone # numbers

        🌟 Interface Features:
        - Chinese interface (displays "路线号")
        - White background, clean card layout
        - Left side shows three-digit route numbers
        - Right side shows delivery details
        - Address format: Mainly California US addresses
        - Alert messages: "Suspected Apt/Suspecté Apt..."

        CRITICAL: Extract actually visible information from the image
        - Route numbers use three-digit format (149, 150, 151...)
        - Tracking number format: UUS + 16 digits
        - Addresses must be in English format, no tracking numbers
        - If no apartment number, return only address field
        """
    }

    private func createGoFoPromptForDebug() -> String {
        return """
        📦 GoFo Delivery Recognition - Extract ONLY what is actually shown in the image:

        🎯 Core Information:
        1. Sort Number: Extract the EXACT numeric sort number shown on the left side of each delivery item (e.g., 15, 16, 17, 18, 112, 113...)
        2. Address: Extract ONLY the address text shown in the image, do not add any extra information
        3. Tracking Number: GF + 12 digits (e.g., GF611244756320)
        4. Customer Name: Full name

        🚨 CRITICAL: Extract ONLY the address text actually displayed in the image
        - Do NOT add city, state, zipcode or any information not shown in the image
        - Do NOT "complete" or "standardize" address formats
        - Keep the address exactly as shown in the image
        - 🚫 NEVER add country suffixes - DO NOT include "USA", "US", "United States" in addresses

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: If image shows "10624 Pleasant Valley Circ,95209", return exactly "10624 Pleasant Valley Circ,95209", do NOT add city/state information

        🎯 GoFo Interface Features:
        - Map background interface with blue circle markers
        - Left-side numeric sort numbers (1, 2, 3...)
        - Address display format: "Street Address, Zipcode"
        - Tracking number format: GF + 12 digits
        - Customer name below address
        - May display Chinese interface text

        🚨 COMPLETE TASK VALIDATION:
        - ONLY extract deliveries that have: Address + Sort Number
        - If ANY of these 2 required elements is missing, SKIP that task entirely
        - Video recording may show same addresses multiple times due to scrolling
        - If you see the same address with same sort number, only include it ONCE
        - Each sort number must be unique across all deliveries

        🔍 DUPLICATE PREVENTION:
        - Check for duplicates: if same address + same sort number already exists, skip
        - No duplicate address + sort number combinations
        - If you find duplicates, remove them and keep only one instance

        ⚡ Simplified recognition, focus on core information
        """
    }

    private func createYWEPromptForDebug() -> String {
        return """
        📦 YWE Delivery Recognition - Smart Address Separation:

        🎯 Core Information:
        1. Sort Number: # + number (#1, #2, #3, #4...)
        2. Waybill Number: YWAUS + 15 digits (e.g., YWAUS010000147255)
        3. Recipient: Complete English name
        4. Address: Smart apartment number separation

        🏠 Smart Address Separation Rules:
        If address contains apartment/unit information, provide two versions:
        - full_address: Complete address (including apartment number)
        - address: Street address only (without apartment number)

        🔍 Apartment Number Keywords:
        - Apt, Apartment, Unit, Suite, Room, Rm, Ste
        - Standalone # numbers
        - Example: "14801 Ronald W Reagan Blvd Apt 9109" → full: "14801 Ronald W Reagan Blvd Apt 9109, Leander, TX, 78641", street: "14801 Ronald W Reagan Blvd, Leander, TX, 78641"

        ⚡ Address Format Examples:
        - Abbreviation preferred: "2200 Cabrillo Path, Leander, TX, 78641"
        - Alternative full form: "2200 Cabrillo Path, Leander, Texas, 78641"

        🎯 YWE Interface Features:
        - Chinese interface: 派送任务、收件人、地址、派送成功
        - White background list interface, card layout
        - Left side # sort number, right side green "派送成功" status
        - Yellow "派送图片待核验" warning indicator
        - Waybill format: YWAUS + 15 digits
        - Recipient: Complete English name
        - Address: US address format, may contain apartment numbers

        ⚡ Simplified recognition, focus on core information
        """
    }

    private func createSpeedXPromptForDebug() -> String {
        return """
        SpeedX Delivery Recognition:

        Extract delivery information from SpeedX app. Each delivery has:
        - Address (left side, 1-2 lines)
        - Customer name (right side, blue text)
        - Tracking number (bottom left, starts with SPXSF)
        - Stop number (bottom right, format: 停靠点: X)

        🚨 CRITICAL ADDRESS SEPARATION RULES - ABSOLUTELY MANDATORY:
        - 🚫 NEVER EVER include customer names in the "address" field
        - 🚫 NEVER EVER include recipient names in the "address" field
        - 🚫 NEVER EVER include personal names in the "address" field
        - ✅ Address field MUST ONLY contain: Street Number + Street Name + City + State
        - ✅ Customer names go ONLY in the separate "customer" field

        Key rules:
        1. Each delivery is separated by blue left border
        2. Extract ONLY the number from "停靠点: X" (e.g., from "停靠点: 18" return "18")
        3. Each stop number must be unique - no duplicates
        4. Match information within the same task block

        ⚡ Simplified recognition, focus on core information
        """
    }

    private func createGeneralAddressPromptForDebug() -> String {
        return """
        📸 General Image Recognition:
        - Auto-detect address format (US/Australia/Other)
        - Extract all visible address information
        - Identify sort numbers and tracking numbers
        - Adapt to various delivery app interfaces
        """
    }

    private func createRegionSpecificPromptForDebug(appType: DeliveryAppType) -> String {
        // 特殊处理iMile：根据用户选择的地区来决定提示词
        if appType == .imile {
            return createiMileRegionPromptForDebug()
        }

        switch appType.region {
        case .usa:
            return createUSADeliveryPromptForDebug(appType: appType)
        case .australia:
            return createAustraliaDeliveryPromptForDebug(appType: appType)
        case .universal:
            return createUniversalPromptForDebug(appType: appType)
        }
    }

    private func createiMileRegionPromptForDebug() -> String {
        return """

        iMile 地区特定提示:
        - 支持美国和澳洲地址格式
        - 追踪号码: iMile格式追踪号
        - 排序号码: 各种格式的排序号
        """
    }

    private func createUSADeliveryPromptForDebug(appType: DeliveryAppType) -> String {
        let baseUSAPrompt = """

        🇺🇸 美国地址识别规则:
        - 地址格式: "Number Street, City, State, Zipcode"
        - 示例: "1721 Marina Court, San Mateo, CA, 94403"
        - 重点识别美国州名缩写 (CA, NY, TX, FL等)
        - 美国邮编格式: 5位数字或5+4位格式
        - 🚫 禁止包含国家: 绝对不要添加"USA"、"US"、"United States"等国家标识
        """

        switch appType {
        case .amazonFlex:
            return baseUSAPrompt + """

            AMAZON FLEX 特征:
            - 排序号码: 简单数字 (2, 3, 4, 5, 6, 7, 8等)
            - 追踪号码: Amazon风格追踪号
            - 配送时间: "已预约 3:00 - 8:00 上午 今天" 或 "Scheduled 3:00 - 8:00 AM Today"
            - 重点提取配送时间段信息
            """
        case .gofo:
            // GoFo特殊处理：不添加美国地址格式规则，因为GoFo专用提示词已经明确要求只识别图片中显示的内容
            return ""
        default:
            return baseUSAPrompt
        }
    }

    private func createAustraliaDeliveryPromptForDebug(appType: DeliveryAppType) -> String {
        let baseAustraliaPrompt = """

        🇦🇺 澳洲地址识别规则:
        - 地址格式: "Number Street, Suburb, State, Postcode"
        - 示例: "123 Collins Street, Melbourne, VIC, 3000"
        - 重点识别澳洲州名缩写 (VIC, NSW, QLD, WA, SA, TAS, NT, ACT)
        - 澳洲邮编格式: 4位数字
        """

        switch appType {
        case .imile:
            return baseAustraliaPrompt + """

            IMILE 特征:
            - 追踪号码: iMile格式追踪号
            - 排序号码: 各种格式的排序号
            - 支持澳洲和美国地址格式
            """
        default:
            return baseAustraliaPrompt
        }
    }

    private func createUniversalPromptForDebug(appType: DeliveryAppType) -> String {
        switch appType {
        case .justPhoto:
            return """

            📸 通用图片识别:
            - 自动检测地址格式 (美国/澳洲/其他)
            - 提取所有可见的地址信息
            - 识别排序号码和追踪号码
            - 适应各种快递应用界面
            """
        default:
            return ""
        }
    }

    // MARK: - 🤖 智能地址修复系统集成

    /// 检查并提示用户处理问题地址
    private func checkAndTriggerSmartRetry() async {
        // 检查是否有收集的问题地址
        if smartRetryService.hasPendingProblems {
            let problemCount = ProblemAddressCollector.shared.problemAddresses.count
            print("🤖 SCANNER: 🎯 发现 \(problemCount) 个问题地址，提示用户手动处理")
            print("🤖 SCANNER: 🎯 用户可以通过编辑地址来修复问题")

            // 延迟一下让用户看到处理完成的状态
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟

            // 🎯 新逻辑：只提示用户，不自动修复
            await showProblemAddressAlert()
        }
    }

    /// 显示问题地址提示
    private func showProblemAddressAlert() async {
        await MainActor.run {
            let problemCount = ProblemAddressCollector.shared.problemAddresses.count
            let message = String(format: "found_addresses_need_confirmation".localized, problemCount)

            // 这里可以显示一个简单的提示，或者设置一个状态让UI显示
            print("🤖 SCANNER: 💡 提示用户: \(message)")

            // 可以通过设置一个Published属性来让UI显示提示
            // 例如：hasProblemsToShow = true
        }
    }

    // MARK: - 🚀 智能图片分割功能

    /// 检测是否需要分割图片
    private func shouldSplitImage(_ image: UIImage) -> Bool {
        let height = image.size.height
        let width = image.size.width

        // 检测超长图片：高度超过8000像素或高宽比超过15:1
        let isVeryTall = height > 8000
        let aspectRatio = height / width
        let hasHighAspectRatio = aspectRatio > 15.0

        Logger.aiInfo("📏 图片尺寸分析: \(width)x\(height), 高宽比: \(String(format: "%.1f", aspectRatio)):1")

        if isVeryTall || hasHighAspectRatio {
            Logger.aiInfo("🔄 触发智能分割条件: 高度>\(height > 8000 ? "8000" : "正常"), 高宽比>\(hasHighAspectRatio ? "15:1" : "正常")")
            return true
        }

        return false
    }

    /// 智能分割超长图片并处理
    private func processImageWithSmartSplitting(_ image: UIImage, imageIndex: Int, isPDFImage: Bool) async {
        Logger.aiInfo("🚀 开始智能分割处理超长图片")

        await MainActor.run {
            processingStatus = "splitting_image".localized
        }

        // 分割图片
        let segments = splitImageIntoSegments(image)
        Logger.aiInfo("✂️ 图片已分割为 \(segments.count) 个片段")

        var allAddresses: [String] = []
        var totalConfidence: Double = 0.0
        var successfulSegments = 0

        // 处理每个片段
        for (segmentIndex, segment) in segments.enumerated() {
            await MainActor.run {
                processingStatus = "\("processing_segment".localized) \(segmentIndex + 1)/\(segments.count)..."
            }

            Logger.aiInfo("🔍 处理分割片段 \(segmentIndex + 1)/\(segments.count), 尺寸: \(segment.size.width)x\(segment.size.height)")

            do {
                // 为每个片段选择最佳识别模式
                let optimalMode = aiConfig.getOptimalRecognitionMode()
                let mode: HybridAddressRecognitionService.RecognitionMode = {
                    switch optimalMode {
                    case .ocrFirst:
                        return .ocrFirst
                    case .aiFirst:
                        return .aiFirst
                    case .ocrOnly:
                        return .ocrOnly
                    case .aiOnly:
                        return .aiOnly
                    }
                }()

                // 处理分割片段
                let result = try await hybridService.recognizeAddresses(
                    from: segment,
                    mode: mode,
                    appType: selectedAppType,
                    isPDFImage: isPDFImage
                ) { status in
                    Task { @MainActor in
                        processingStatus = "\("processing_segment".localized) \(segmentIndex + 1)..."
                    }
                }

                // 收集结果
                allAddresses.append(contentsOf: result.addresses)
                totalConfidence += result.confidence
                successfulSegments += 1

                Logger.aiInfo("✅ 片段 \(segmentIndex + 1) 处理完成，识别到 \(result.addresses.count) 个地址")

                // 片段间延迟，避免API限制
                if segmentIndex < segments.count - 1 {
                    try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
                }

            } catch {
                Logger.aiError("❌ 片段 \(segmentIndex + 1) 处理失败: \(error)")
            }
        }

        // 合并和去重结果
        let uniqueAddresses = removeDuplicateAddresses(allAddresses)
        let averageConfidence = successfulSegments > 0 ? totalConfidence / Double(successfulSegments) : 0.0

        Logger.aiInfo("🎯 分割处理完成: 原始\(allAddresses.count)个地址 → 去重后\(uniqueAddresses.count)个地址")

        await MainActor.run {
            aiModelUsed = "Smart Split Processing"
            aiConfidence = averageConfidence
            recognitionMethod = "Smart Split Recognition"
            processingStatus = "merging_results".localized
        }

        // 保存合并后的结果
        await saveSegmentedResults(uniqueAddresses)
    }

    // 🎯 新增：OCR+AI智能分割处理
    private func processImageWithOCRPlusAISplitting(_ image: UIImage, imageIndex: Int, isPDFImage: Bool) async {
        Logger.aiInfo("🔍 OCR+AI开始智能分割处理超长图片")

        await MainActor.run {
            processingStatus = "intelligent_splitting".localized
        }

        // 分割图片
        let segments = splitImageIntoSegments(image)
        Logger.aiInfo("✂️ OCR+AI图片已分割为 \(segments.count) 个片段")

        var allAddresses: [String] = []
        var totalConfidence: Double = 0.0
        var successfulSegments = 0

        // 处理每个片段
        for (segmentIndex, segment) in segments.enumerated() {
            await MainActor.run {
                processingStatus = "\("analyzing_image_segment".localized) \(segmentIndex + 1)/\(segments.count)..."
            }

            Logger.aiInfo("🔍 OCR+AI处理分割片段 \(segmentIndex + 1)/\(segments.count)")

            do {
                // 第一步：OCR提取文本
                let ocrResponse = try await ocrService.recognizeText(from: segment)

                // 第二步：AI处理OCR文本
                let formattedText = ocrService.formatTextForAI(ocrResponse)
                let aiResult = try await firebaseAIService.extractAddressesFromOCRText(formattedText, appType: selectedAppType)

                // 收集结果
                allAddresses.append(contentsOf: aiResult.addresses)
                let segmentConfidence = (Double(ocrResponse.confidence) + aiResult.confidence) / 2.0
                totalConfidence += segmentConfidence
                successfulSegments += 1

                Logger.aiInfo("✅ OCR+AI片段 \(segmentIndex + 1) 处理完成，识别到 \(aiResult.addresses.count) 个地址")

                // 片段间延迟，避免API限制
                if segmentIndex < segments.count - 1 {
                    try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
                }

            } catch {
                Logger.aiError("❌ OCR+AI片段 \(segmentIndex + 1) 处理失败: \(error)")
            }
        }

        // 合并和去重结果
        let uniqueAddresses = removeDuplicateAddresses(allAddresses)
        let averageConfidence = successfulSegments > 0 ? totalConfidence / Double(successfulSegments) : 0.0

        Logger.aiInfo("🎯 OCR+AI分割处理完成: 原始\(allAddresses.count)个地址 → 去重后\(uniqueAddresses.count)个地址")

        await MainActor.run {
            aiModelUsed = "OCR + Smart Split"
            aiConfidence = averageConfidence
            recognitionMethod = "OCR + Smart Split Recognition"
            processingStatus = "merging_processing_results".localized
        }

        // 🎯 最终降级：如果OCR+AI完全失败，使用传统混合服务
        if uniqueAddresses.isEmpty {
            Logger.aiInfo("⚠️ OCR+AI分割处理未识别到任何地址，最终降级到传统混合服务")

            await MainActor.run {
                processingStatus = "switching_processing_method".localized
                recognitionMethod = "Fallback to Traditional AI"
            }

            // 使用传统混合服务处理原图
            await processImageWithHybridService(image, imageIndex: imageIndex, isPDFImage: isPDFImage)
            return
        }

        // 保存合并后的结果
        await saveSegmentedResults(uniqueAddresses)
    }



    // 🎯 保存OCR+AI识别结果
    private func saveOCRPlusAIResults(_ addresses: [String], imageIndex: Int) async {
        // 🔄 首先进行去重处理，确保没有重复地址
        let uniqueAddresses = await finalDeduplicateAddresses(addresses)
        Logger.aiInfo("🔄 OCR+AI去重: 原始\(addresses.count)个 → 去重后\(uniqueAddresses.count)个地址")

        var addressesToVerify: [(String, Int)] = []

        for (_, address) in uniqueAddresses.enumerated() {
            // 解析地址信息
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
            let rawAddress = separatedInfo.address

            // 🎯 对于SpeedX，应用Apple Maps格式化以移除ZIP码、国家后缀等
            let newAddress = formatAddressForSpeedX(rawAddress)

            // 记录格式化过程（仅在有变化时）
            if selectedAppType == .speedx && newAddress != rawAddress {
                Logger.aiInfo("🍎 SpeedX地址格式化: '\(rawAddress)' -> '\(newAddress)'")
            }

            // 先添加地址到数组，获取正确的索引
            let addressIndex = await MainActor.run { () -> Int in
                let currentSortNumber = recognizedAddresses.count + 1

                // 添加内部序号
                var finalAddress = newAddress + SortNumberConstants.internalSortTag(currentSortNumber)

                // 🎯 保留原始第三方排序号（如果存在）
                if !separatedInfo.thirdPartySortNumber.isEmpty {
                    finalAddress += SortNumberConstants.thirdPartySortTag(separatedInfo.thirdPartySortNumber)
                    Logger.aiInfo("🎯 saveOCRPlusAI保留第三方排序号: \(separatedInfo.thirdPartySortNumber), 内部序号: \(currentSortNumber)")
                }

                // 添加其他信息（使用统一的标签常量）
                if !separatedInfo.tracking.isEmpty {
                    finalAddress += SortNumberConstants.trackingTag(separatedInfo.tracking)
                }
                if !separatedInfo.customer.isEmpty {
                    finalAddress += SortNumberConstants.customerTag(separatedInfo.customer)
                }
                if !separatedInfo.deliveryTime.isEmpty {
                    finalAddress += SortNumberConstants.timeTag(separatedInfo.deliveryTime)
                }

                // 🎯 添加用户选择的应用类型标签（修复缺失的sourceAppRaw问题）
                let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto)
                if shouldAddAppTag {
                    finalAddress += SortNumberConstants.appTag(selectedAppType.rawValue)
                }

                // 添加到数组
                let tempCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                recognizedAddresses.append((finalAddress, tempCoordinate, true, false, 0.8))

                return recognizedAddresses.count - 1
            }

            addressesToVerify.append((newAddress, addressIndex))
        }

        // 批量验证坐标
        await batchVerifyAddressCoordinates(addressesToVerify)
    }

    // 🎯 SpeedX专用地址格式化方法
    private func formatAddressForSpeedX(_ address: String) -> String {
        // 只对SpeedX应用Apple Maps格式化
        guard selectedAppType == .speedx else {
            return address
        }

        // 应用Apple Maps格式化：移除ZIP码、国家后缀等
        return AppleMapsAddressFormatter.formatForAppleMaps(address)
    }

    // 🎯 从第三方排序号中提取数字部分
    private func extractNumberFromSortNumber(_ sortNumber: String) -> String {
        let numberString = sortNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return numberString
    }

    /// 将超长图片分割为多个重叠的片段
    private func splitImageIntoSegments(_ image: UIImage) -> [UIImage] {
        let originalHeight = image.size.height
        let originalWidth = image.size.width

        // 计算分割参数
        let segmentHeight: CGFloat = 4000  // 每个片段的高度
        let overlapHeight: CGFloat = 150   // 重叠区域高度，避免地址被截断（减少重叠）
        let effectiveHeight = segmentHeight - overlapHeight

        var segments: [UIImage] = []
        var currentY: CGFloat = 0

        Logger.aiInfo("✂️ 分割参数: 片段高度=\(segmentHeight), 重叠=\(overlapHeight), 有效高度=\(effectiveHeight)")

        while currentY < originalHeight {
            // 计算当前片段的实际高度
            let remainingHeight = originalHeight - currentY
            let actualSegmentHeight = min(segmentHeight, remainingHeight)

            // 创建分割区域
            let segmentRect = CGRect(
                x: 0,
                y: currentY,
                width: originalWidth,
                height: actualSegmentHeight
            )

            Logger.aiInfo("✂️ 创建片段: y=\(currentY), 高度=\(actualSegmentHeight)")

            // 分割图片
            if let segmentImage = cropImage(image, to: segmentRect) {
                segments.append(segmentImage)
            }

            // 移动到下一个片段位置
            currentY += effectiveHeight

            // 如果剩余高度小于有效高度，直接处理最后一段
            if remainingHeight <= segmentHeight {
                break
            }
        }

        Logger.aiInfo("✂️ 分割完成，共生成 \(segments.count) 个片段")
        return segments
    }

    /// 裁剪图片到指定区域
    private func cropImage(_ image: UIImage, to rect: CGRect) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        // 转换坐标系（UIKit坐标系转CGImage坐标系）
        let scale = image.scale
        let scaledRect = CGRect(
            x: rect.origin.x * scale,
            y: rect.origin.y * scale,
            width: rect.size.width * scale,
            height: rect.size.height * scale
        )

        guard let croppedCGImage = cgImage.cropping(to: scaledRect) else { return nil }

        return UIImage(cgImage: croppedCGImage, scale: scale, orientation: image.imageOrientation)
    }

    /// 去除重复地址（基于地址+第三方sort组合）
    private func removeDuplicateAddresses(_ addresses: [String]) -> [String] {
        var uniqueAddresses: [String] = []
        var seenAddressSortCombos: Set<String> = []

        for address in addresses {
            // 提取地址和第三方排序号
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
            let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            let thirdPartySortNumber = separatedInfo.thirdPartySortNumber

            // 跳过空地址 - SpeedX优化：降低最小长度要求
            if cleanAddress.isEmpty || cleanAddress.count < 2 {
                Logger.aiWarning("🚫 跳过过短地址: '\(cleanAddress)' (长度: \(cleanAddress.count))")
                continue
            }

            // 🎯 构建简化的组合键：地址+第三方排序号
            let simplifiedComboKey = "\(cleanAddress)|\(thirdPartySortNumber)"

            // 检查是否重复
            if !seenAddressSortCombos.contains(simplifiedComboKey) {
                seenAddressSortCombos.insert(simplifiedComboKey)
                uniqueAddresses.append(address)
                Logger.aiInfo("✅ 保留地址: \(cleanAddress) (第三方sort: \(thirdPartySortNumber.isEmpty ? "无" : thirdPartySortNumber))")
            } else {
                Logger.aiWarning("🔄 跳过绝对重复: \(cleanAddress) + \(thirdPartySortNumber)")
            }
        }

        Logger.aiInfo("📊 去重统计: 原始\(addresses.count)个 → 保留\(uniqueAddresses.count)个 (过滤\(addresses.count - uniqueAddresses.count)个)")
        return uniqueAddresses
    }

    /// 保存分割处理的结果（地址已经过去重处理）
    private func saveSegmentedResults(_ addresses: [String]) async {
        Logger.aiInfo("🔄 保存分割结果，地址数量: \(addresses.count)")

        var addressesToVerify: [(String, Int)] = []

        for address in addresses {
            // 检查地址有效性
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
            let actualAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

            if actualAddress.isEmpty || actualAddress.count < 3 {
                Logger.aiWarning("跳过无效地址: '\(address)'")
                continue
            }

            // 保存地址并记录正确的索引
            let addressIndex = await MainActor.run { () -> Int in
                let shouldAddAppTag = (selectedAppType != .manual && selectedAppType != .justPhoto)
                let finalAddress = shouldAddAppTag ? "\(address)|APP:\(selectedAppType.rawValue)" : address

                // 添加临时坐标
                let tempCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                recognizedAddresses.append((finalAddress, tempCoordinate, true, false, 0.8))

                // 返回刚添加的地址的索引
                return recognizedAddresses.count - 1
            }

            // 记录需要验证的地址和正确的索引
            addressesToVerify.append((address, addressIndex))

            Logger.aiInfo("✅ 分割结果地址已保存: \(address), 索引: \(addressIndex)")
        }

        // 批量验证坐标
        if !addressesToVerify.isEmpty {
            Logger.aiInfo("🚦 开始验证 \(addressesToVerify.count) 个分割结果地址的坐标")
            await batchVerifyAddressCoordinates(addressesToVerify)
        }

        await MainActor.run {
            processingStatus = "smart_split_complete".localized
        }
    }

    /// Firebase AI智能分割处理
    private func processImageWithFirebaseAISplitting(_ image: UIImage, imageIndex: Int) async {
        Logger.aiInfo("🔥 Firebase AI开始智能分割处理超长图片")

        await MainActor.run {
            processingStatus = "intelligent_splitting".localized
        }

        // 分割图片
        let segments = splitImageIntoSegments(image)
        Logger.aiInfo("✂️ Firebase AI图片已分割为 \(segments.count) 个片段")

        // 🚨 SpeedX专用：验证图片分割是否正确
        if selectedAppType == .speedx {
            Logger.aiError("🚨 SpeedX图片分割验证:")
            Logger.aiError("📊 原图尺寸: \(image.size.width)x\(image.size.height)")
            Logger.aiError("💡 建议：使用PDF合并工具将多页合并为单页长图，避免分割错误")
            Logger.aiError("💡 Mac命令：convert input.pdf -append output.png")
            for (index, segment) in segments.enumerated() {
                Logger.aiError("📊 片段\(index+1)尺寸: \(segment.size.width)x\(segment.size.height)")
            }
        }

        var allAddresses: [String] = []
        var totalConfidence: Double = 0.0
        var successfulSegments = 0
        var allDeliveries: [(segmentIndex: Int, addresses: [String])] = [] // 用于去重处理

        // 🚀 智能并发处理优化：根据片段数量动态调整并发数
        let concurrentBatchSize = calculateOptimalConcurrency(segmentCount: segments.count)
        let batches = segments.chunked(into: concurrentBatchSize)

        Logger.aiInfo("🚀 智能并发优化: \(segments.count)个片段分为\(batches.count)批，每批并发处理\(concurrentBatchSize)个")

        for (batchIndex, batch) in batches.enumerated() {
            await MainActor.run {
                processingStatus = "\("analyzing_image_segment".localized) \("batch".localized)\(batchIndex + 1)/\(batches.count)..."
            }

            Logger.aiInfo("🔥 Firebase AI处理批次 \(batchIndex + 1)/\(batches.count)，包含\(batch.count)个片段")

            // 🚀 并发处理当前批次的所有片段
            let currentAppType = selectedAppType // 在TaskGroup外部捕获值
            await withTaskGroup(of: (Int, [String], Double)?.self) { group in
                // 为批次中的每个片段创建并发任务
                for (segmentIndexInBatch, segment) in batch.enumerated() {
                    let globalSegmentIndex = batchIndex * concurrentBatchSize + segmentIndexInBatch

                    group.addTask {
                        do {
                            Logger.aiInfo("🔥 开始处理片段 \(globalSegmentIndex + 1)/\(segments.count)")
                            Logger.aiInfo("🖼️ 片段\(globalSegmentIndex + 1)尺寸: \(segment.size.width)x\(segment.size.height)")

                            // 使用Firebase AI处理分割片段（使用简化提示词）
                            let result = try await firebaseAIService.extractAddressesFromImage(segment, appType: currentAppType, isPDFImage: false, isSegmentedImage: true)

                            Logger.aiInfo("✅ 片段 \(globalSegmentIndex + 1) 处理完成，识别到 \(result.addresses.count) 个地址")

                            // 🔍 调试：显示识别到的第一个地址（用于验证是否处理了不同片段）
                            if let firstAddress = result.addresses.first {
                                let cleanAddr = firstAddress.components(separatedBy: "|").first ?? firstAddress
                                Logger.aiInfo("🔍 片段\(globalSegmentIndex + 1)首个地址: \(cleanAddr)")
                            }

                            // 🚨 SpeedX专用：详细记录每个片段的识别结果
                            if currentAppType == .speedx {
                                Logger.aiError("🚨 SpeedX片段\(globalSegmentIndex + 1)详细结果:")
                                for (index, address) in result.addresses.enumerated() {
                                    let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                                    Logger.aiError("🔍 片段\(globalSegmentIndex + 1)-地址\(index+1): 排序号[\(separatedInfo.thirdPartySortNumber)] - 地址[\(separatedInfo.address.prefix(30))...]")
                                }
                            }

                            return (globalSegmentIndex, result.addresses, result.confidence)
                        } catch {
                            Logger.aiError("❌ 片段 \(globalSegmentIndex + 1) 处理失败: \(error)")
                            return nil
                        }
                    }
                }

                // 收集批次结果
                for await result in group {
                    if let (segmentIndex, addresses, confidence) = result {
                        // 收集所有结果用于后续去重
                        allDeliveries.append((segmentIndex: segmentIndex, addresses: addresses))
                        totalConfidence += confidence
                        successfulSegments += 1

                        // 实时更新进度
                        await MainActor.run {
                            processingStatus = "\("analyzing_image_segment".localized) \(successfulSegments)/\(segments.count)..."
                        }
                    }
                }
            }

            // 批次间延迟，避免API限制
            if batchIndex < batches.count - 1 {
                Logger.aiInfo("⏱️ 批次间延迟2秒，避免API限制...")
                try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒延迟，忽略取消错误
            }
        }

        // 🔄 智能去重：处理重叠区域的重复识别
        await MainActor.run {
            processingStatus = "deduplicating_overlapping_areas".localized
        }

        // 🎯 按片段索引排序后收集地址，确保第三方排序号顺序正确
        let sortedDeliveries = allDeliveries.sorted { $0.segmentIndex < $1.segmentIndex }
        Logger.aiInfo("🔄 按片段顺序合并: \(sortedDeliveries.map { String($0.segmentIndex) }.joined(separator: ","))")

        for delivery in sortedDeliveries {
            allAddresses.append(contentsOf: delivery.addresses)
            Logger.aiDebug("📦 片段\(delivery.segmentIndex): 添加\(delivery.addresses.count)个地址")
        }

        // 智能去重：基于第三方排序号和地址相似度
        let uniqueAddresses = await smartDeduplicateAddresses(allAddresses)
        let averageConfidence = successfulSegments > 0 ? totalConfidence / Double(successfulSegments) : 0.0

        Logger.aiInfo("🔄 智能去重完成: 原始\(allAddresses.count)个地址 → 去重后\(uniqueAddresses.count)个地址")

        await MainActor.run {
            aiModelUsed = "Firebase AI Smart Split"
            aiConfidence = averageConfidence
            recognitionMethod = "Firebase AI Split Recognition"
            processingStatus = "merging_analysis_results".localized
        }

        // 🔄 最终去重处理，确保显示给用户的地址没有重复
        let finalUniqueAddresses = await finalDeduplicateAddresses(uniqueAddresses)
        Logger.aiInfo("🔄 Firebase AI最终去重: \(uniqueAddresses.count) → \(finalUniqueAddresses.count) 个地址")

        // 保存合并后的结果
        await saveSegmentedResults(finalUniqueAddresses)
    }

    /// 智能去重：基于地址+第三方sort组合（简化逻辑：地址+第三方sort完全一致就是绝对重复）
    private func smartDeduplicateAddresses(_ addresses: [String]) async -> [String] {
        var uniqueAddresses: [String] = []
        var seenAddressSortCombos: Set<String> = []

        Logger.aiInfo("🔄 开始智能去重处理（基于地址+第三方sort组合）...")

        for address in addresses {
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
            let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            let thirdPartySortNumber = separatedInfo.thirdPartySortNumber

            // 🎯 构建简化的组合键：地址+第三方排序号
            let simplifiedComboKey = "\(cleanAddress)|\(thirdPartySortNumber)"

            if seenAddressSortCombos.contains(simplifiedComboKey) {
                // 🎯 发现绝对重复（地址+第三方sort完全一致），直接跳过（保一删一）
                Logger.aiWarning("🔄 智能去重：跳过绝对重复（地址+第三方sort）: \(cleanAddress) + \(thirdPartySortNumber)")
            } else {
                // 第一次出现，保留并记录
                seenAddressSortCombos.insert(simplifiedComboKey)
                uniqueAddresses.append(address)
                Logger.aiInfo("✅ 智能去重：保留地址: \(cleanAddress) (第三方sort: \(thirdPartySortNumber.isEmpty ? "无" : thirdPartySortNumber))")
            }
        }

        Logger.aiInfo("🔄 智能去重统计: 原始\(addresses.count)个 → 唯一\(uniqueAddresses.count)个")
        Logger.aiInfo("📊 高级去重统计: 原始\(addresses.count)个 → 保留\(uniqueAddresses.count)个 (排序号重复+地址重复共过滤\(addresses.count - uniqueAddresses.count)个)")
        return uniqueAddresses
    }

    /// 最终去重处理：确保显示给用户的地址没有重复
    private func finalDeduplicateAddresses(_ addresses: [String]) async -> [String] {
        var uniqueAddresses: [String] = []
        var seenCleanAddresses: Set<String> = []
        var seenAddressSortCombos: Set<String> = []

        Logger.aiInfo("🔄 开始最终去重处理（基于地址+第三方sort组合）...")

        for address in addresses {
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
            let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            // trackingNumber 不再需要，因为简化的去重逻辑只使用地址+第三方sort

            // 跳过空地址
            if cleanAddress.isEmpty || cleanAddress.count < 3 {
                Logger.aiWarning("🚫 跳过无效地址: '\(cleanAddress)'")
                continue
            }

            var shouldAdd = true
            var skipReason = ""

            // 🎯 SpeedX简化重复检测：地址+第三方sort完全一致就是绝对重复（保一删一）
            let thirdPartySortNumber = separatedInfo.thirdPartySortNumber

            // 构建简化的组合键：地址+第三方排序号
            let simplifiedComboKey = "\(cleanAddress)|\(thirdPartySortNumber)"

            if seenAddressSortCombos.contains(simplifiedComboKey) {
                shouldAdd = false
                skipReason = "绝对重复（地址+第三方sort完全一致）: \(cleanAddress) + \(thirdPartySortNumber)"
            } else {
                seenAddressSortCombos.insert(simplifiedComboKey)
            }

            if shouldAdd {
                uniqueAddresses.append(address)
                seenCleanAddresses.insert(cleanAddress)
                Logger.aiInfo("✅ 保留地址: \(cleanAddress) (第三方sort: \(thirdPartySortNumber.isEmpty ? "无" : thirdPartySortNumber))")
            } else {
                Logger.aiWarning("🔄 跳过绝对重复: \(cleanAddress) (原因: \(skipReason))")
            }
        }

        Logger.aiInfo("📊 最终去重统计: 原始\(addresses.count)个 → 保留\(uniqueAddresses.count)个")
        return uniqueAddresses
    }

    /// 从地址字符串中提取第三方排序号
    private func extractThirdPartySortNumber(from address: String) -> String {
        if let range = address.range(of: "THIRD_PARTY_SORT:") {
            let afterSort = address[range.upperBound...]
            if let pipeRange = afterSort.range(of: "|") {
                return String(afterSort[..<pipeRange.lowerBound])
            } else {
                return String(afterSort)
            }
        }
        return ""
    }

    /// 从地址字符串中提取纯地址
    private func extractCleanAddress(from address: String) -> String {
        if let pipeRange = address.range(of: "|") {
            return String(address[..<pipeRange.lowerBound]).trimmingCharacters(in: .whitespaces)
        }
        return address.trimmingCharacters(in: .whitespaces)
    }

    // MARK: - 🎯 新增UI组件

    // 开始分析按钮 - 优化进度提示
    private var startAnalysisButton: some View {
        Button(action: {
            let now = Date()

            // 🚫 时间戳防重复 - 500ms内的点击被忽略
            if now.timeIntervalSince(lastClickTime) < 0.5 {
                Logger.aiWarning("🚫 重复点击被阻止，时间间隔: \(String(format: "%.3f", now.timeIntervalSince(lastClickTime)))s")
                return
            }
            lastClickTime = now

            // 🚫 全局执行锁 - 最早阶段防重复
            guard !isExecuting else {
                Logger.aiWarning("🚫 全局执行锁阻止重复执行")
                return
            }

            // 🚫 防重复点击检查
            guard !isProcessing && processingTaskID == nil else {
                Logger.aiWarning("🚫 按钮重复点击被阻止，当前状态: isProcessing=\(isProcessing), taskID=\(processingTaskID?.uuidString ?? "nil")")
                return
            }

            // 立即设置执行锁
            isExecuting = true

            // ⏱️ 记录分析开始时间
            analysisStartTime = now
            analysisEndTime = nil

            Logger.aiInfo("🔘 用户点击Start Analysis按钮 (时间戳: \(now.timeIntervalSince1970))")
            Logger.aiInfo("⏱️ 开始计时：图片分析处理开始")

            Task {
                defer {
                    // 确保执行完成后释放锁
                    Task { @MainActor in
                        isExecuting = false
                    }
                }

                Logger.aiInfo("🚀 开始执行processImages")
                await processImages()
                Logger.aiInfo("✅ processImages执行完成")
            }
        }) {
            HStack(spacing: 12) {
                // 根据处理状态显示不同图标
                if isProcessing {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Image(systemName: "brain.head.profile")
                        .font(.title2)
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text(isProcessing ? "processing".localized : "start_analysis".localized)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(isProcessing ? processingStatus : "analyze_selected_images".localized)
                        .font(.caption)
                        .opacity(0.8)
                        .lineLimit(1)
                }

                Spacer()

                if !isProcessing {
                    Image(systemName: "arrow.right.circle.fill")
                        .font(.title2)
                } else {
                    // 显示进度百分比
                    Text("\(Int(processingProgress * 100))%")
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        isProcessing ? Color.gray : Color.adaptivePrimaryIcon,
                        isProcessing ? Color.gray.opacity(0.8) : Color.adaptivePrimaryIcon.opacity(0.8)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
            .shadow(color: (isProcessing ? Color.gray : Color.adaptivePrimaryIcon).opacity(0.3), radius: 8, x: 0, y: 4)
            .animation(.easeInOut(duration: 0.3), value: isProcessing)
        }
        .disabled(isExecuting || isProcessing || processingTaskID != nil)
        .padding(.horizontal)
        .transition(.scale.combined(with: .opacity))
    }

    // 图片移动方法
    private func moveImage(from source: Int, to destination: Int) {
        guard source != destination,
              source >= 0, source < selectedImages.count,
              destination >= 0, destination < selectedImages.count else { return }

        let movedImage = selectedImages.remove(at: source)
        selectedImages.insert(movedImage, at: destination)

        // 同步移动图片来源类型标记
        if source < imageSourceTypes.count && destination < imageSourceTypes.count {
            let movedType = imageSourceTypes.remove(at: source)
            imageSourceTypes.insert(movedType, at: destination)
        }

        // 🎯 同步移动原始帧编号
        if source < originalFrameIndices.count && destination < originalFrameIndices.count {
            let movedIndex = originalFrameIndices.remove(at: source)
            originalFrameIndices.insert(movedIndex, at: destination)
            print("🔄 原始帧编号同步移动: 帧#\(movedIndex) 从位置\(source)移动到位置\(destination)")
        }

        print("🔄 图片顺序调整: 从位置\(source)移动到位置\(destination)")
    }

    // 🔄 反转图片顺序
    private func reverseImageOrder() {
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedImages.reverse()
            imageSourceTypes.reverse()
        }
        print("🔄 图片顺序已反转")
    }

    // 🚨 SpeedX专用：检查地址重复问题
    private func checkForDuplicateAddresses() {
        Logger.aiInfo("🔍 SpeedX重复地址检测开始")

        var duplicateGroups: [String: [Int]] = [:]
        var addressCounts: [String: Int] = [:]

        // 统计地址出现次数
        for (index, (address, _, _, _, _)) in recognizedAddresses.enumerated() {
            let cleanAddress = cleanAddressForComparison(address)

            if addressCounts[cleanAddress] == nil {
                addressCounts[cleanAddress] = 1
                duplicateGroups[cleanAddress] = [index]
            } else {
                addressCounts[cleanAddress]! += 1
                duplicateGroups[cleanAddress]!.append(index)
            }
        }

        // 找出重复的地址
        let duplicates = duplicateGroups.filter { $0.value.count > 1 }

        if !duplicates.isEmpty {
            Logger.aiError("🚨 SpeedX重复地址检测结果:")
            Logger.aiError("📊 总地址数: \(recognizedAddresses.count)")
            Logger.aiError("🔄 重复地址组数: \(duplicates.count)")

            var totalDuplicates = 0
            for (address, indices) in duplicates {
                totalDuplicates += indices.count - 1 // 减去原始地址
                Logger.aiError("🔄 重复地址: \(address)")
                Logger.aiError("   📍 出现位置: \(indices)")
                Logger.aiError("   🔢 重复次数: \(indices.count)")
            }

            Logger.aiError("📈 重复地址统计: \(totalDuplicates)个重复项")
            Logger.aiError("💡 建议: 检查图片是否有重叠内容或分割错误")

            // 可选：自动去重（保留第一个）
            if totalDuplicates > recognizedAddresses.count / 3 { // 如果重复超过1/3
                Logger.aiError("🚨 重复率过高(\(Int(Double(totalDuplicates)/Double(recognizedAddresses.count)*100))%)，建议检查图片处理逻辑")
            }
        } else {
            Logger.aiInfo("✅ SpeedX重复地址检测完成：未发现重复地址")
        }
    }

    // 移除复杂的SpeedX序号连续性检查 - 保持简洁的用户体验

    }

    // 🆕 数据完整性验证和自动补全 - 基于停靠点优先的智能判断
    private func performDataIntegrityValidation(
        stopNumbers: [Int],
        totalExpectedCount: Int?,
        missingNumbers: [Int],
        incompleteAddresses: [String]
    ) {
        Logger.aiInfo("🔍 开始数据完整性验证（停靠点优先策略）")

        // 移除复杂的数据验证功能 - 保持简洁的用户体验

        Logger.aiInfo("🔍 数据完整性验证完成")
    }

    // 移除复杂的停靠点冲突解决功能 - 保持简洁的用户体验

    // 移除复杂的冲突解决策略 - 保持简洁的用户体验

    // 移除复杂的地址完整性检查 - 保持简洁的用户体验

    // 移除复杂的地址选择策略 - 保持简洁的用户体验

    // 移除复杂的不完整地址判断 - 保持简洁的用户体验

    // 移除复杂的地址标准化功能 - 保持简洁的用户体验

    // 移除复杂的地址停靠点映射验证 - 保持简洁的用户体验

    // 移除复杂的缺失停靠点处理 - 保持简洁的用户体验

    // 移除复杂的占位符地址创建 - 保持简洁的用户体验

    // 移除复杂的最终验证功能 - 保持简洁的用户体验

    // 移除复杂的AI预测地址功能 - 保持简洁的用户体验

    // 移除复杂的补充截图模式 - 保持简洁的用户体验

    // 移除复杂的补充截图结果处理 - 保持简洁的用户体验

    // 🔢 从地址中提取停靠点号码
    private func extractStopNumberFromAddress(_ address: String) -> Int? {
        let components = address.components(separatedBy: "|")
        if components.count >= 2 {
            return Int(components[1])
        }
        return nil
    }

    // 清理地址用于比较（移除格式差异）
    private func cleanAddressForComparison(_ address: String) -> String {
        return address
            .components(separatedBy: "|").first ?? address // 移除管道符后的内容
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .lowercased()
            .replacingOccurrences(of: "  ", with: " ") // 移除多余空格
            .replacingOccurrences(of: " st", with: " street")
            .replacingOccurrences(of: " ave", with: " avenue")
            .replacingOccurrences(of: " rd", with: " road")
            .replacingOccurrences(of: " dr", with: " drive")
            .replacingOccurrences(of: " ct", with: " court")
            .replacingOccurrences(of: " ln", with: " lane")
            .replacingOccurrences(of: " blvd", with: " boulevard")
    }

// MARK: - 🎯 拖拽排序支持
struct ImageDropDelegate: DropDelegate {
    let destinationIndex: Int
    @Binding var images: [UIImage]
    @Binding var imageSourceTypes: [Bool]
    let moveAction: (Int, Int) -> Void
    @Binding var draggedIndex: Int?

    func performDrop(info: DropInfo) -> Bool {
        guard let draggedIndex = draggedIndex else { return false }

        if draggedIndex != destinationIndex {
            moveAction(draggedIndex, destinationIndex)
        }

        // 重置拖拽状态
        self.draggedIndex = nil
        return true
    }

    func dropEntered(info: DropInfo) {
        // 可以添加视觉反馈
        print("🎯 拖拽进入位置: \(destinationIndex)")
    }

    func dropExited(info: DropInfo) {
        // 可以添加视觉反馈
        print("🎯 拖拽离开位置: \(destinationIndex)")
    }

    func dropUpdated(info: DropInfo) -> DropProposal? {
        return DropProposal(operation: .move)
    }
}

// 移除复杂的SpeedX专用UI组件 - 保持简洁的用户体验



// MARK: - UIImage扩展
extension UIImage {
    func resized(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, self.scale)
        defer { UIGraphicsEndImageContext() }

        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}
