import SwiftUI
import SwiftData
import os.log
import UIKit

/// 路线优化结果表单
/// 显示路线优化前后的对比和优化后的路线顺序
struct OptimizationResultSheet: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext

    // 数据绑定
    @ObservedObject var viewModel: RouteViewModel
    var optimizedPoints: [DeliveryPoint]
    var originalTotalDistance: Double
    var optimizedTotalDistance: Double

    // 状态变量 - 移除不需要的状态

    // 回调函数
    var onApply: () -> Void

    // 日志
    private let logger = os.Logger(subsystem: "com.jasonkwok.NaviBatch", category: "OptimizationResultSheet")

    // 一键分组状态
    @State private var isAutoGrouping = false

    // 展开的地址项ID
    @State private var expandedPointId: UUID? = nil

    // 配送点管理界面状态
    @State private var showingDeliveryPointManagerSheet = false
    @State private var deliveryPointWrapper: DeliveryPointWrapper? = nil

    // 第三方排序状态
    @State private var isThirdPartySorted = false
    @State private var isSavedThirdPartySorted = false
    @State private var showSavedMessage = false
    @State private var displayedPoints: [DeliveryPoint] = []

    // 移除状态更新界面的本地状态，改为通过通知传递给RouteView处理

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 距离对比卡片 - 重新设计
                    distanceComparisonCard

                    // 一键分组按钮
                    autoGroupButton

                    // 优化后顺序列表 - 改进
                    optimizedRouteListCard
                }
                .padding(.horizontal)
                .padding(.bottom, 16)
                .overlay {
                    if isAutoGrouping {
                        ProgressView("creating_groups".localized)
                            .padding()
                            .background(Color(.systemBackground).opacity(0.9))
                            .cornerRadius(10)
                            .shadow(radius: 5)
                    }
                }
            }
            .navigationTitle("route_optimization_results".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }



                // 应用AI优化按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        applyOptimization()
                        onApply()
                        dismiss()
                    } label: {
                        Label("apply".localized, systemImage: "checkmark.circle.fill")
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            // 移除包裹设置表单，因为我们不需要编辑功能
        }
        .sheet(item: $deliveryPointWrapper, onDismiss: {
            // 重置deliveryPointWrapper
            logInfo("OptimizationResultSheet - DeliveryPointManagerSheet关闭，重置deliveryPointWrapper")
            deliveryPointWrapper = nil
        }) { wrapper in
            // 使用更简单的结构，直接使用包装器中的点
            ZStack {
                // 添加一个明确的背景色
                Color(.systemBackground)
                    .ignoresSafeArea()

                DeliveryPointManagerView(deliveryPoint: wrapper.point)
                    .onAppear {
                        // 记录日志，帮助调试
                        logInfo("OptimizationResultSheet - DeliveryPointManagerSheet出现，point=\(wrapper.point.primaryAddress)")
                    }
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
            .presentationCornerRadius(12)
            .presentationBackground(.regularMaterial)
        }
        // StatusUpdateSheet现在通过RouteView处理，移除本地sheet调用
        .onAppear {
            // 初始化显示的点列表
            displayedPoints = optimizedPoints
        }
    }

    // 应用优化结果 - 保存AI优化结果
    private func applyOptimization() {
        markPointsAsOptimized()
        logInfo("OptimizationResultSheet - 保存AI优化结果")
    }

    // 🎯 同步第三方排序到sorted_number
    private func syncThirdPartyToSortedNumber() {
        logInfo("🔄 OptimizationResultSheet - syncThirdPartyToSortedNumber: 函数被调用")

        guard let currentRoute = viewModel.currentRoute else {
            logError("❌ OptimizationResultSheet - syncThirdPartyToSortedNumber: 当前路线为空")
            return
        }

        logInfo("✅ OptimizationResultSheet - syncThirdPartyToSortedNumber: 当前路线: \(currentRoute.name)")
        logInfo("📊 OptimizationResultSheet - syncThirdPartyToSortedNumber: 路线总点数: \(currentRoute.points.count)")

        let deliveryPoints = currentRoute.points.filter({ !$0.isStartPoint && $0.sort_number != 0 })
        logInfo("🎯 OptimizationResultSheet - syncThirdPartyToSortedNumber: 配送点数量: \(deliveryPoints.count)")

        var syncCount = 0
        var skipCount = 0

        // 🎯 第一步：收集所有有第三方排序号的点
        var pointsWithThirdParty: [(point: DeliveryPoint, gofoNumber: Int)] = []

        for point in deliveryPoints {
            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                let gofoNumber = extractNumber(from: thirdPartySort)
                pointsWithThirdParty.append((point: point, gofoNumber: gofoNumber))
            }
        }

        // 🎯 第二步：按GoFo号码从小到大排序
        pointsWithThirdParty.sort { $0.gofoNumber < $1.gofoNumber }

        logInfo("🔢 OptimizationResultSheet - 按GoFo号码排序后的顺序:")
        for (index, item) in pointsWithThirdParty.enumerated() {
            logInfo("   排序 \(index + 1): GoFo \(item.gofoNumber) - \(item.point.primaryAddress)")
        }

        // 🎯 第三步：按排序后的顺序分配连续的sorted_number
        for (index, item) in pointsWithThirdParty.enumerated() {
            let point = item.point
            let gofoNumber = item.gofoNumber
            let oldSortedNumber = point.sorted_number
            let newSortedNumber = index + 1 // 连续编号：1, 2, 3...

            point.sorted_number = newSortedNumber
            point.isOptimized = true
            syncCount += 1

            logInfo("📍 OptimizationResultSheet - 同步点 \(index + 1)/\(pointsWithThirdParty.count)")
            logInfo("   地址: \(point.primaryAddress)")
            logInfo("   GoFo号码: \(gofoNumber)")
            logInfo("   ✅ sorted_number: \(oldSortedNumber) -> \(newSortedNumber)")
        }

        // 🎯 第四步：处理没有第三方排序号的点
        for point in deliveryPoints {
            if point.thirdPartySortNumber?.isEmpty != false {
                skipCount += 1
                logInfo("   ⚠️ 跳过: \(point.primaryAddress) - 无第三方排序号，保持sorted_number: \(point.sorted_number)")
            }
        }

        logInfo("📈 OptimizationResultSheet - syncThirdPartyToSortedNumber: 同步统计 - 成功: \(syncCount), 跳过: \(skipCount)")

        // 标记路线为已优化
        currentRoute.isOptimized = true
        logInfo("🏁 OptimizationResultSheet - syncThirdPartyToSortedNumber: 路线 \(currentRoute.name) 已标记为按第三方排序优化")

        // 保存更改 - 使用多重保存机制
        do {
            logInfo("💾 OptimizationResultSheet - syncThirdPartyToSortedNumber: 开始保存到数据库...")

            // 1. 强制刷新ModelContext
            modelContext.processPendingChanges()
            logInfo("🔄 OptimizationResultSheet - syncThirdPartyToSortedNumber: ModelContext已刷新")

            // 2. 保存到数据库
            try modelContext.save()
            logInfo("✅ OptimizationResultSheet - syncThirdPartyToSortedNumber: 第一次保存成功")

            // 3. 再次强制保存确保数据持久化
            try modelContext.save()
            logInfo("✅ OptimizationResultSheet - syncThirdPartyToSortedNumber: 第二次保存成功")

            // 4. 验证数据是否真的保存了
            let verifyPoints = currentRoute.points.filter({ !$0.isStartPoint && $0.sort_number != 0 })
            logInfo("🔍 OptimizationResultSheet - syncThirdPartyToSortedNumber: 验证保存结果...")
            for (index, point) in verifyPoints.prefix(5).enumerated() {
                logInfo("   验证点 \(index + 1): \(point.primaryAddress) - sorted_number: \(point.sorted_number)")
            }

        } catch {
            logError("❌ OptimizationResultSheet - syncThirdPartyToSortedNumber: 保存失败 - \(error.localizedDescription)")
            logError("❌ 错误详情: \(error)")
        }

        logInfo("🎉 OptimizationResultSheet - syncThirdPartyToSortedNumber: 函数执行完成")
    }

    // 标记点为已优化
    private func markPointsAsOptimized() {
        guard let currentRoute = viewModel.currentRoute else {
            logError("OptimizationResultSheet - markPointsAsOptimized: 当前路线为空")
            return
        }

        logInfo("OptimizationResultSheet - markPointsAsOptimized: 开始标记AI优化状态")

        // 标记所有点为已优化（AI优化）
        for point in currentRoute.points {
            point.isOptimized = true
            logInfo("OptimizationResultSheet - markPointsAsOptimized: 点 \(point.primaryAddress) 标记为AI优化")
        }

        // 标记路线为已优化
        currentRoute.isOptimized = true
        logInfo("OptimizationResultSheet - markPointsAsOptimized: 路线 \(currentRoute.name) 标记为AI优化")

        do {
            try modelContext.save()
            logInfo("OptimizationResultSheet - markPointsAsOptimized: 成功保存AI优化状态")

            // 更新ViewModel中的数据
            viewModel.deliveryPoints = currentRoute.points.sorted { point1, point2 in
                // 起点排在最前面
                if (point1.isStartPoint || point1.sort_number == 0) && !(point2.isStartPoint || point2.sort_number == 0) {
                    return true
                } else if !(point1.isStartPoint || point1.sort_number == 0) && (point2.isStartPoint || point2.sort_number == 0) {
                    return false
                }

                // 其他点按sorted_number排序
                return point1.sorted_number < point2.sorted_number
            }
            viewModel.objectWillChange.send()

            // 发送通知，通知其他组件路线数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: currentRoute.id.uuidString
            )

            // 执行回调并关闭界面
            onApply()
            dismiss()

        } catch {
            logError("OptimizationResultSheet - markPointsAsOptimized: 保存AI优化状态失败: \(error.localizedDescription)")
        }
    }

    // 切换第三方排序显示
    private func toggleThirdPartySorting() {
        logInfo("OptimizationResultSheet - toggleThirdPartySorting: 切换第三方排序状态")

        if isThirdPartySorted {
            // 如果当前是第三方排序，切换回AI优化结果
            displayedPoints = optimizedPoints
            isThirdPartySorted = false
            isSavedThirdPartySorted = false // 重置保存状态
            showSavedMessage = false
            logInfo("OptimizationResultSheet - toggleThirdPartySorting: 切换到AI优化结果")
        } else {
            // 切换到第三方排序
            let sortedByThirdParty = getSortedByThirdParty()
            displayedPoints = sortedByThirdParty
            isThirdPartySorted = true
            logInfo("OptimizationResultSheet - toggleThirdPartySorting: 切换到第三方排序")
        }
    }

    // 保存第三方排序到sorted_number
    private func saveThirdPartySorting() {
        logInfo("OptimizationResultSheet - saveThirdPartySorting: 开始保存第三方排序")

        guard let currentRoute = viewModel.currentRoute else {
            logError("OptimizationResultSheet - saveThirdPartySorting: 当前路线为空")
            return
        }

        logInfo("OptimizationResultSheet - saveThirdPartySorting: 当前路线: \(currentRoute.name)")

        // 🎯 修复：使用optimizedPoints作为数据源，确保包含第三方排序号
        let deliveryPoints = optimizedPoints.filter({ !$0.isStartPoint && $0.sort_number != 0 })
        logInfo("OptimizationResultSheet - saveThirdPartySorting: 配送点数量: \(deliveryPoints.count)")

        var syncCount = 0
        var skipCount = 0

        // 🎯 第一步：收集所有有第三方排序号的点
        var pointsWithThirdParty: [(point: DeliveryPoint, gofoNumber: Int)] = []

        for point in deliveryPoints {
            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                let gofoNumber = extractNumber(from: thirdPartySort)
                pointsWithThirdParty.append((point: point, gofoNumber: gofoNumber))
            }
        }

        logInfo("OptimizationResultSheet - saveThirdPartySorting: 有第三方排序号的点数量: \(pointsWithThirdParty.count)")

        // 🎯 第二步：按GoFo号码从小到大排序
        pointsWithThirdParty.sort { $0.gofoNumber < $1.gofoNumber }

        logInfo("🔢 OptimizationResultSheet - saveThirdPartySorting: 按GoFo号码排序后的顺序:")
        for (index, item) in pointsWithThirdParty.enumerated() {
            logInfo("   排序 \(index + 1): GoFo \(item.gofoNumber) - \(item.point.primaryAddress)")
        }

        // 🎯 第三步：按第三方排序顺序分配连续的sorted_number（保持系统逻辑正常）
        for (index, item) in pointsWithThirdParty.enumerated() {
            let point = item.point
            let gofoNumber = item.gofoNumber
            let oldSortedNumber = point.sorted_number
            let newSortedNumber = index + 1 // 连续编号：1, 2, 3, 4, 5...

            point.sorted_number = newSortedNumber // 使用连续编号保持系统逻辑
            point.isOptimized = true
            syncCount += 1

            logInfo("📍 OptimizationResultSheet - saveThirdPartySorting: 按第三方排序分配连续编号")
            logInfo("   地址: \(point.primaryAddress)")
            logInfo("   第三方号码: \(gofoNumber) (保存在thirdPartySortNumber)")
            logInfo("   ✅ sorted_number: \(oldSortedNumber) -> \(newSortedNumber) (连续编号)")
        }

        // 🎯 第四步：处理没有第三方排序号的点
        for point in deliveryPoints {
            if point.thirdPartySortNumber?.isEmpty != false {
                skipCount += 1
                logInfo("   ⚠️ 跳过: \(point.primaryAddress) - 无第三方排序号，保持sorted_number: \(point.sorted_number)")
            }
        }

        logInfo("📈 OptimizationResultSheet - saveThirdPartySorting: 同步统计 - 成功: \(syncCount), 跳过: \(skipCount)")

        // 🔍 验证同步后的sorted_number值（应该是连续的1, 2, 3...）
        logInfo("🔍 OptimizationResultSheet - saveThirdPartySorting: 验证同步后的sorted_number值:")
        for point in deliveryPoints.sorted(by: { $0.sorted_number < $1.sorted_number }) {
            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                let gofoNumber = extractNumber(from: thirdPartySort)
                logInfo("   ✅ 点: \(point.primaryAddress) - sorted_number: \(point.sorted_number) (连续), 第三方号码: \(gofoNumber) (显示用)")
            }
        }

        // 标记路线为已优化
        currentRoute.isOptimized = true
        logInfo("🏁 OptimizationResultSheet - saveThirdPartySorting: 路线 \(currentRoute.name) 已标记为按第三方排序优化")

        // 保存更改 - 使用多重保存机制
        do {
            logInfo("💾 OptimizationResultSheet - saveThirdPartySorting: 开始保存到数据库...")

            // 1. 强制刷新ModelContext
            modelContext.processPendingChanges()
            logInfo("🔄 OptimizationResultSheet - saveThirdPartySorting: ModelContext已刷新")

            // 2. 保存到数据库
            try modelContext.save()
            logInfo("✅ OptimizationResultSheet - saveThirdPartySorting: 成功保存第三方排序，同步了\(syncCount)个点，跳过了\(skipCount)个点")

            // 3. 更新ViewModel中的数据
            viewModel.deliveryPoints = currentRoute.points.sorted { point1, point2 in
                // 起点排在最前面
                if (point1.isStartPoint || point1.sort_number == 0) && !(point2.isStartPoint || point2.sort_number == 0) {
                    return true
                } else if !(point1.isStartPoint || point1.sort_number == 0) && (point2.isStartPoint || point2.sort_number == 0) {
                    return false
                }

                // 其他点按sorted_number排序
                return point1.sorted_number < point2.sorted_number
            }
            viewModel.objectWillChange.send()

            // 4. 发送通知，通知其他组件路线数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: currentRoute.id.uuidString
            )

            // 5. 重新构建displayedPoints，反映保存后的sorted_number顺序
            logInfo("🔄 OptimizationResultSheet - saveThirdPartySorting: 开始重新构建displayedPoints")

            // 🔍 先验证currentRoute.points中的数据
            let allRoutePoints = currentRoute.points.filter { !$0.isStartPoint && $0.sort_number != 0 }
            logInfo("🔍 OptimizationResultSheet - saveThirdPartySorting: currentRoute.points中的配送点数量: \(allRoutePoints.count)")
            for point in allRoutePoints.prefix(5) {
                logInfo("   点: \(point.primaryAddress) - sorted_number: \(point.sorted_number)")
            }

            let updatedPoints = currentRoute.points
                .filter { !$0.isStartPoint && $0.sort_number != 0 }
                .sorted { $0.sorted_number < $1.sorted_number }

            logInfo("🔍 OptimizationResultSheet - saveThirdPartySorting: 排序后的updatedPoints数量: \(updatedPoints.count)")
            for (index, point) in updatedPoints.prefix(5).enumerated() {
                logInfo("   排序后第\(index + 1)个点: \(point.primaryAddress) - sorted_number: \(point.sorted_number)")
            }

            displayedPoints = updatedPoints
            isThirdPartySorted = false
            isSavedThirdPartySorted = true // 标记为已保存第三方排序

            // 显示保存成功消息
            withAnimation(.easeInOut(duration: 0.3)) {
                showSavedMessage = true
            }

            // 3秒后自动隐藏保存消息
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showSavedMessage = false
                }
            }

            logInfo("🎉 OptimizationResultSheet - saveThirdPartySorting: 第三方排序保存完成，已退出预览模式")
            logInfo("🔄 OptimizationResultSheet - saveThirdPartySorting: 重新构建displayedPoints，保持显示第三方排序号")

            // 验证最终的displayedPoints
            let displayOrder = displayedPoints.prefix(10).map {
                let gofo = $0.thirdPartySortNumber ?? "无"
                return "sorted_number:\($0.sorted_number), GoFo:\(gofo), 地址:\($0.primaryAddress)"
            }
            logInfo("🔍 OptimizationResultSheet - saveThirdPartySorting: 最终displayedPoints前10个点:")
            for (index, info) in displayOrder.enumerated() {
                logInfo("   第\(index + 1)个: \(info)")
            }

        } catch {
            logError("❌ OptimizationResultSheet - saveThirdPartySorting: 保存失败 - \(error.localizedDescription)")
        }
    }

    // 获取按第三方排序的点列表（仅用于显示）
    private func getSortedByThirdParty() -> [DeliveryPoint] {
        // 获取所有非起点的点
        let nonStartPoints = optimizedPoints.filter { !$0.isStartPoint && $0.sort_number != 0 }

        // 按第三方排序号排序
        let sortedPoints = nonStartPoints.sorted { point1, point2 in
            let sort1 = point1.thirdPartySortNumber ?? ""
            let sort2 = point2.thirdPartySortNumber ?? ""

            // 如果都有第三方排序号，按数字排序
            if !sort1.isEmpty && !sort2.isEmpty {
                // 提取数字部分进行比较
                let num1 = extractNumber(from: sort1)
                let num2 = extractNumber(from: sort2)
                return num1 < num2
            }

            // 有第三方排序号的排在前面
            if !sort1.isEmpty && sort2.isEmpty { return true }
            if sort1.isEmpty && !sort2.isEmpty { return false }

            // 都没有第三方排序号时，按sort_number排序
            return point1.sort_number < point2.sort_number
        }

        return sortedPoints
    }

    // 🎯 将第三方排序顺序复制到sorted_number（不修改第三方排序数据）
    private func applyThirdPartySortingToSortedNumber() {
        guard let currentRoute = viewModel.currentRoute else {
            logError("OptimizationResultSheet - applyThirdPartySortingToSortedNumber: 当前路线为空")
            return
        }

        logInfo("OptimizationResultSheet - applyThirdPartySortingToSortedNumber: 开始将第三方排序顺序复制到sorted_number")

        // 🚨 保护排序字段：不修改任何排序字段
        logInfo("🚨 OptimizationResultSheet - 保护排序字段，不修改任何排序数据")
        for point in currentRoute.points.filter({ !$0.isStartPoint && $0.sort_number != 0 }) {
            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                // 🚨 不修改 sorted_number，保持数据完整性
                // 🚨 不修改 sort_number，保持原始顺序
                // 🚨 不修改 thirdPartySortNumber，保持第三方排序
                point.isOptimized = true // 只标记为已优化

                logInfo("🚨 OptimizationResultSheet - 保护排序字段: 点 \(point.primaryAddress) - thirdPartySort: \(thirdPartySort), sorted_number: \(point.sorted_number) (未修改)")
            } else {
                // 🚨 即使没有第三方排序号，也不修改排序字段
                point.isOptimized = true // 只标记为已优化
                logInfo("🚨 OptimizationResultSheet - 保护排序字段: 点 \(point.primaryAddress) - sorted_number: \(point.sorted_number) (未修改)")
            }
        }

        // 标记路线为已优化
        currentRoute.isOptimized = true
        logInfo("OptimizationResultSheet - applyThirdPartySortingToSortedNumber: 路线 \(currentRoute.name) 已标记为按第三方排序优化")

        // 保存更改
        do {
            try modelContext.save()
            logInfo("OptimizationResultSheet - applyThirdPartySortingToSortedNumber: 成功保存第三方排序顺序到sorted_number")
        } catch {
            logError("OptimizationResultSheet - applyThirdPartySortingToSortedNumber: 保存失败 - \(error.localizedDescription)")
        }
    }

    // 应用第三方排序到数据库（保留原函数，但现在不再使用）
    private func applyThirdPartySorting() {
        guard let currentRoute = viewModel.currentRoute else {
            logError("OptimizationResultSheet - applyThirdPartySorting: 当前路线为空")
            return
        }

        logInfo("OptimizationResultSheet - applyThirdPartySorting: 开始应用第三方排序")

        // 首先清除所有点的优化状态
        for point in currentRoute.points {
            point.isOptimized = false
        }

        // 获取所有非起点的点
        let nonStartPoints = currentRoute.points.filter { !$0.isStartPoint && $0.sort_number != 0 }

        // 按第三方排序号排序
        let sortedPoints = nonStartPoints.sorted { point1, point2 in
            let sort1 = point1.thirdPartySortNumber ?? ""
            let sort2 = point2.thirdPartySortNumber ?? ""

            // 如果都有第三方排序号，按数字排序
            if !sort1.isEmpty && !sort2.isEmpty {
                // 提取数字部分进行比较
                let num1 = extractNumber(from: sort1)
                let num2 = extractNumber(from: sort2)
                return num1 < num2
            }

            // 有第三方排序号的排在前面
            if !sort1.isEmpty && sort2.isEmpty { return true }
            if sort1.isEmpty && !sort2.isEmpty { return false }

            // 都没有第三方排序号时，按sort_number排序
            return point1.sort_number < point2.sort_number
        }

        // 将第三方排序重新分配连续的sorted_number（不改动sort_number）
        for (index, point) in sortedPoints.enumerated() {
            let oldSortedNumber = point.sorted_number

            // 🎯 所有点都重新分配连续的sorted_number：1, 2, 3, 4...
            point.sorted_number = index + 1
            point.isOptimized = true // 标记为已优化（按第三方排序）

            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                logInfo("OptimizationResultSheet - applyThirdPartySorting: 点 \(point.primaryAddress) - 第三方排序号: \(thirdPartySort) -> sorted_number: \(oldSortedNumber) -> \(point.sorted_number)")
            } else {
                logInfo("OptimizationResultSheet - applyThirdPartySorting: 点 \(point.primaryAddress) - 无第三方排序号，sorted_number: \(oldSortedNumber) -> \(point.sorted_number)")
            }
        }

        // 标记路线为已优化
        currentRoute.isOptimized = true
        logInfo("OptimizationResultSheet - applyThirdPartySorting: 路线 \(currentRoute.name) 已标记为按第三方排序优化")

        // 保存更改
        do {
            try modelContext.save()
            logInfo("OptimizationResultSheet - applyThirdPartySorting: 成功保存第三方排序后的路线")

            // 更新ViewModel中的数据
            viewModel.deliveryPoints = currentRoute.points.sorted { point1, point2 in
                // 起点排在最前面
                if (point1.isStartPoint || point1.sort_number == 0) && !(point2.isStartPoint || point2.sort_number == 0) {
                    return true
                } else if !(point1.isStartPoint || point1.sort_number == 0) && (point2.isStartPoint || point2.sort_number == 0) {
                    return false
                }

                // 其他点按sorted_number排序
                return point1.sorted_number < point2.sorted_number
            }
            viewModel.objectWillChange.send()

            // 发送通知，通知其他组件路线数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: currentRoute.id.uuidString
            )

            // 执行回调并关闭界面
            onApply()
            dismiss()

        } catch {
            logError("OptimizationResultSheet - applyThirdPartySorting: 保存第三方排序后的路线失败: \(error.localizedDescription)")
        }
    }

    // 提取数字的辅助函数
    private func extractNumber(from string: String) -> Int {
        let numbers = string.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return Int(numbers) ?? 0
    }

    // 取消优化（在当前界面中）
    private func cancelOptimization() {
        guard let currentRoute = viewModel.currentRoute else {
            logError("OptimizationResultSheet - cancelOptimization: 当前路线为空")
            return
        }

        logInfo("OptimizationResultSheet - cancelOptimization: 开始取消优化")

        // 将所有delivery point的sorted_number重置为sort_number
        for point in currentRoute.points {
            let oldSortedNumber = point.sorted_number
            point.sorted_number = point.sort_number
            point.isOptimized = false // 移除优化标记

            logInfo("OptimizationResultSheet - cancelOptimization: 点 \(point.primaryAddress) - sorted_number: \(oldSortedNumber) -> \(point.sorted_number)")
        }

        // 移除路线的优化状态
        currentRoute.isOptimized = false
        logInfo("OptimizationResultSheet - cancelOptimization: 路线 \(currentRoute.name) 已移除优化状态")

        // 保存更改
        do {
            try modelContext.save()
            logInfo("OptimizationResultSheet - cancelOptimization: 成功保存取消优化后的路线")

            // 更新ViewModel中的数据
            viewModel.deliveryPoints = currentRoute.points.sorted { $0.sort_number < $1.sort_number }
            viewModel.objectWillChange.send()

            // 发送通知，通知其他组件路线数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: currentRoute.id.uuidString
            )

            // 关闭界面
            dismiss()

        } catch {
            logError("OptimizationResultSheet - cancelOptimization: 保存取消优化后的路线失败: \(error.localizedDescription)")
        }
    }

    // 还原原始路线顺序（保留原有函数以防其他地方使用）
    private func restoreOriginalRoute() {
        guard let currentRoute = viewModel.currentRoute else {
            logError("OptimizationResultSheet - restoreOriginalRoute: 当前路线为空")
            return
        }

        logInfo("OptimizationResultSheet - restoreOriginalRoute: 开始恢复原始排序")

        // 将所有delivery point的sorted_number重置为sort_number
        for point in currentRoute.points {
            let oldSortedNumber = point.sorted_number
            point.sorted_number = point.sort_number
            point.isOptimized = false // 移除优化标记

            logInfo("OptimizationResultSheet - restoreOriginalRoute: 点 \(point.primaryAddress) - sorted_number: \(oldSortedNumber) -> \(point.sorted_number)")
        }

        // 移除路线的优化状态
        currentRoute.isOptimized = false
        logInfo("OptimizationResultSheet - restoreOriginalRoute: 路线 \(currentRoute.name) 已移除优化状态")

        // 保存更改
        do {
            try modelContext.save()
            logInfo("OptimizationResultSheet - restoreOriginalRoute: 成功保存恢复后的路线")

            // 更新ViewModel中的数据
            viewModel.deliveryPoints = currentRoute.points.sorted { $0.sort_number < $1.sort_number }
            viewModel.objectWillChange.send()

            // 发送通知，通知其他组件路线数据已更新
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: currentRoute.id.uuidString
            )

            // 关闭优化结果界面
            dismiss()

        } catch {
            logError("OptimizationResultSheet - restoreOriginalRoute: 保存恢复后的路线失败: \(error.localizedDescription)")
        }
    }

    // 算法信息卡片 - 已移除算法名称显示

    // 距离对比卡片 - 重新设计
    private var distanceComparisonCard: some View {
        HStack(spacing: 0) {
            // 优化前卡片
            VStack(spacing: 6) {
                Text("before_optimization".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Text(String(format: "%.1f", originalTotalDistance))
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.primary)

                Text("kilometers".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )

            // 中间节省百分比
            ZStack {
                Circle()
                    .fill(originalTotalDistance > optimizedTotalDistance ? Color.green : Color.orange)
                    .frame(width: 60, height: 60)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)

                VStack(spacing: 0) {
                    if originalTotalDistance > optimizedTotalDistance {
                        let savedPercent = (originalTotalDistance - optimizedTotalDistance) / originalTotalDistance * 100

                        Image(systemName: "arrow.down")
                            .font(.system(size: 12))
                            .foregroundColor(.white)

                        Text(String(format: "%.1f%%", savedPercent))
                            .font(.system(size: 14, weight: .bold))
                            .foregroundColor(.white)
                    } else {
                        Text("0%")
                            .font(.system(size: 14, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
            }
            .offset(x: 0, y: -10)
            .zIndex(1)

            // 优化后卡片
            VStack(spacing: 6) {
                HStack {
                    Image(systemName: "sparkles")
                    Text("after_optimization".localized)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                Text(String(format: "%.1f", optimizedTotalDistance))
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.green)

                Text("kilometers".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
            )
        }
        .padding(.vertical, 10)
    }

    // 优化后顺序列表卡片 - 改进
    private var optimizedRouteListCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题行，包含标题和操作按钮
            HStack {
                Text("optimized_route_order".localized)
                    .font(.headline)

                Spacer()

                // 第三方排序按钮
                Button {
                    toggleThirdPartySorting()
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.up.arrow.down")
                            .font(.system(size: 14))
                        Text("sort_by_third_party".localized)
                            .font(.subheadline)
                    }
                    .foregroundColor(isThirdPartySorted ? .orange : .blue)
                }
                .buttonStyle(.plain)

                // Save按钮 - 只在第三方排序模式下且未保存时显示
                if isThirdPartySorted && !isSavedThirdPartySorted {
                    Button {
                        saveThirdPartySorting()
                    } label: {
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.2.squarepath")
                                .font(.system(size: 14))
                            Text("save_third_party_sort".localized)
                                .font(.subheadline)
                        }
                        .foregroundColor(.green)
                    }
                    .buttonStyle(.plain)
                }

                // 保存成功提示
                if showSavedMessage {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 14))
                        Text("Saved")
                            .font(.subheadline)
                    }
                    .foregroundColor(.green)
                    .transition(.opacity)
                }

                // 取消优化按钮
                Button {
                    cancelOptimization()
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.counterclockwise")
                            .font(.system(size: 14))
                        Text("restore_route".localized)
                            .font(.subheadline)
                    }
                    .foregroundColor(.orange)
                }
                .buttonStyle(.plain)
            }
            .padding(.horizontal)

            VStack(spacing: 0) {
                ForEach(Array(displayedPoints.enumerated()), id: \.element.id) { index, point in
                    OptimizedRouteListItem(
                        point: point,
                        index: index,
                        optimizedPoints: displayedPoints,
                        isThirdPartySorted: isThirdPartySorted,
                        isSavedThirdPartySorted: isSavedThirdPartySorted,
                        isExpanded: expandedPointId == point.id,
                        onTap: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                if expandedPointId == point.id {
                                    expandedPointId = nil
                                } else {
                                    expandedPointId = point.id
                                }
                            }
                        },
                        onNavigate: {
                            // 导航功能
                            navigateToPoint(point)
                        },
                        onDelivery: {
                            // 标记派送完成
                            markAsDelivered(point)
                        },
                        onMore: {
                            // 进入管理页面
                            showManagementForPoint(point)
                        }
                    )

                    if index < optimizedPoints.count - 1 {
                        Divider()
                            .padding(.leading, 44)
                    }
                }
            }
            .background(Color(.secondarySystemGroupedBackground))
            .cornerRadius(12)
        }
    }

    // 记录日志的辅助函数
    private func logInfo(_ message: String) {
        logger.info("\(message)")
    }

    private func logError(_ message: String) {
        logger.error("\(message)")
    }

    // 计算显示编号的辅助函数
    private func getDisplayNumber(for point: DeliveryPoint, at index: Int, in points: [DeliveryPoint]) -> Int {
        // 1. 如果当前点是起点 (通过 isStartPoint 或 sort_number == 0 判断)，则显示为 0
        if point.isStartPoint || point.sort_number == 0 {
            return 0
        }

        // 2. 如果当前点不是起点，则计算它是第几个非起点
        // 遍历到当前点的所有点（包括当前点）
        var nonStartPointOrder = 0
        for i in 0...index {
            let currentIterationPoint = points[i]
            // 如果遍历到的点不是起点，则非起点计数器加1
            if !(currentIterationPoint.isStartPoint || currentIterationPoint.sort_number == 0) {
                nonStartPointOrder += 1
            }
        }
        return nonStartPointOrder
    }

    // 导航到指定地点
    private func navigateToPoint(_ point: DeliveryPoint) {
        logInfo("导航到地址: \(point.primaryAddress)")

        // 使用NavigationAppHandler直接导航到单个地点
        NavigationAppHandler.shared.openNavigation(
            to: point.coordinate,
            name: point.primaryAddress
        )

        dismiss()
    }

    // 显示状态更新界面
    private func markAsDelivered(_ point: DeliveryPoint) {
        logInfo("显示状态更新界面: \(point.primaryAddress)")

        // 确保其他表单都已关闭
        showingDeliveryPointManagerSheet = false
        deliveryPointWrapper = nil

        // 通过通知请求RouteView显示状态更新界面
        print("[INFO] OptimizationResultSheet - 发送状态更新请求: \(point.primaryAddress)")

        // 发送通知给RouteView（OptimizationResultSheet中没有展开状态，所以不需要保存）
        NotificationCenter.default.post(
            name: Notification.Name("ShowStatusUpdateSheet"),
            object: point,
            userInfo: ["expandedPointId": ""] // 空字符串表示没有展开状态需要恢复
        )
    }

    // 显示管理页面
    private func showManagementForPoint(_ point: DeliveryPoint) {
        logInfo("显示地址管理页面: \(point.primaryAddress)")

        // 创建DeliveryPointWrapper并显示管理页面
        deliveryPointWrapper = DeliveryPointWrapper(point: point)

        // 延迟显示管理表单，确保其他表单已关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            showingDeliveryPointManagerSheet = true
        }
    }

    // 更新配送状态并保存
    private func updateDeliveryStatusWithReason(_ point: DeliveryPoint, newStatus: DeliveryStatus, failureReason: DeliveryFailureReason? = nil, customReason: String? = nil) {
        logInfo("更新配送状态: \(point.primaryAddress) -> \(newStatus.localizedName)")

        // 更新状态和可能的失败原因
        point.updateStatus(newStatus, failureReason: failureReason, customReason: customReason)

        // 保存到数据库
        do {
            try modelContext.save()
            logInfo("成功更新配送状态")
        } catch {
            logError("更新配送状态失败: \(error.localizedDescription)")
        }
    }

    // 一键分组按钮
    private var autoGroupButton: some View {
        let subscriptionManager = SubscriptionManager.shared
        let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial
        let remainingSlots = isFreeUser ? subscriptionManager.remainingGroupAddressSlots(for: viewModel.currentRoute ?? Route(name: "")) : Int.max

        return Button(action: {
            performAutoGrouping()
        }) {
            VStack(spacing: 4) {
                HStack {
                    Image(systemName: "rectangle.stack.fill.badge.plus")
                        .font(.system(size: 18))
                    Text("auto_group".localized)
                        .font(.headline)
                }

                // 免费版显示限制信息
                if isFreeUser {
                    Text(String(format: "free_version_can_group_addresses".localized, remainingSlots))
                        .font(.caption)
                        .opacity(0.8)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.purple)
            .foregroundColor(.white)
            .cornerRadius(10)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 2)
        }
        .disabled(isAutoGrouping || (isFreeUser && remainingSlots == 0))
        .opacity(viewModel.canUseAutoGrouping() ? 1.0 : 0.5)
        .overlay(
            Group {
                if !viewModel.canUseAutoGrouping() {
                    HStack {
                        Image(systemName: "crown.fill")
                        Text("subscription_exclusive".localized)
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.yellow)
                    .foregroundColor(.black)
                    .cornerRadius(12)
                    .offset(x: 0, y: -20)
                } else if isFreeUser && remainingSlots == 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 12, weight: .semibold))
                        Text("reached_free_version_limit".localized)
                            .font(.system(size: 12, weight: .semibold))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.red)
                            .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                    )
                    .foregroundColor(.white)
                    .offset(x: 0, y: -35)
                    .scaleEffect(1.05)
                    .animation(.easeInOut(duration: 0.3).repeatForever(autoreverses: true), value: isFreeUser && remainingSlots == 0)
                }
            }
        )
    }

    // 检查是否已经按第三方排序进行了优化
    private func checkIfThirdPartySortingIsSynced() -> Bool {
        guard let currentRoute = viewModel.currentRoute else { return false }

        // 检查路线是否已优化且按第三方排序
        if !currentRoute.isOptimized {
            return false
        }

        let deliveryPoints = currentRoute.points.filter { !$0.isStartPoint && $0.sort_number != 0 }
        let pointsWithThirdParty = deliveryPoints.filter {
            $0.thirdPartySortNumber?.isEmpty == false
        }

        // 如果没有第三方排序点，返回false
        if pointsWithThirdParty.isEmpty {
            return false
        }

        // 检查是否按第三方排序号的顺序排列
        let sortedByThirdParty = pointsWithThirdParty.sorted { point1, point2 in
            let num1 = extractNumber(from: point1.thirdPartySortNumber ?? "")
            let num2 = extractNumber(from: point2.thirdPartySortNumber ?? "")
            return num1 < num2
        }

        // 检查sorted_number是否是连续的
        for (index, point) in sortedByThirdParty.enumerated() {
            if point.sorted_number != index + 1 {
                return false
            }
        }

        logInfo("OptimizationResultSheet - checkIfThirdPartySortingIsSynced: 已按第三方排序优化，sorted_number为连续编号")
        return true
    }

    // 执行自动分组
    private func performAutoGrouping() {
        // 检查订阅状态
        if !viewModel.canUseAutoGrouping() {
            // 显示订阅提示
            viewModel.showSubscriptionPrompt = true
            return
        }

        // 检查免费用户是否已达到分组限制
        let subscriptionManager = SubscriptionManager.shared
        if subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial {
            let groupedCount = subscriptionManager.getCurrentGroupedAddressCount(for: viewModel.currentRoute ?? Route(name: ""))
            if groupedCount >= subscriptionManager.currentTier.maxAutoGroupAddresses {
                // 免费用户已达到分组限制，显示升级提示
                logInfo("OptimizationResultSheet - 免费用户已达到分组限制(\(groupedCount)/\(subscriptionManager.currentTier.maxAutoGroupAddresses))，显示升级提示")
                viewModel.showSubscriptionPrompt = true
                return
            }
        }

        // 检查是否有未分组的地址
        let pendingCount = viewModel.getPendingDeliveryCount()
        if pendingCount == 0 {
            // 没有未分组的地址，不执行操作
            return
        }

        // 给予触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 显示进度指示器
        withAnimation {
            isAutoGrouping = true
        }

        // 执行自动分组
        Task {
            // 🎯 检查是否已经同步了第三方排序号
            let hasThirdPartySyncedSortedNumbers = checkIfThirdPartySortingIsSynced()

            if hasThirdPartySyncedSortedNumbers {
                // 如果已经同步了第三方排序号，直接执行分组，不要重置sorted_number
                logInfo("OptimizationResultSheet - performAutoGrouping: 检测到已同步第三方排序号，直接执行分组")
            } else {
                // 否则先应用当前显示的优化结果
                if isThirdPartySorted {
                    // 如果是第三方排序状态，应用第三方排序
                    applyThirdPartySorting()
                } else {
                    // 否则应用AI优化结果
                    markPointsAsOptimized()
                }
                onApply()
            }

            // 然后执行自动分组
            let createdGroups = await viewModel.autoGroupDeliveryPoints()

            // 更新UI
            await MainActor.run {
                withAnimation {
                    isAutoGrouping = false
                }

                // 显示成功消息
                if createdGroups > 0 {
                    logInfo("成功创建\(createdGroups)个分组")

                    // 检查是否是免费用户，如果是则显示升级提示
                    let subscriptionManager = SubscriptionManager.shared
                    if subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial {
                        let remainingSlots = subscriptionManager.remainingGroupAddressSlots(for: viewModel.currentRoute ?? Route(name: ""))
                        if remainingSlots == 0 {
                            // 免费版额度已用完，显示升级提示
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                viewModel.showSubscriptionPrompt = true
                            }
                        }
                    }
                } else {
                    logInfo("没有创建任何分组")
                }

                // 关闭表单
                dismiss()
            }
        }
    }
}

/// 优化路线列表项组件
/// 支持展开式操作按钮
struct OptimizedRouteListItem: View {
    let point: DeliveryPoint
    let index: Int
    let optimizedPoints: [DeliveryPoint]
    let isThirdPartySorted: Bool
    let isSavedThirdPartySorted: Bool
    let isExpanded: Bool
    let onTap: () -> Void
    let onNavigate: () -> Void
    let onDelivery: () -> Void
    let onMore: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // 主要内容区域
            HStack(spacing: 12) {
                // 序号或图标
                ZStack {
                    if point.isStartPoint {
                        // 起点使用绿色圆形图标
                        Circle()
                            .fill(Color.green)
                            .frame(width: 32, height: 32)
                            .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)

                        Image(systemName: "house.fill")
                            .foregroundColor(.white)
                    } else {
                        // 使用方形数字框，颜色根据优化状态变化
                        RoundedRectangle(cornerRadius: 6)
                            .fill(point.isOptimized ? Color.purple : Color.blue)
                            .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                            .frame(width: 30, height: 30)

                        // 显示序号：第三方排序时显示1-n，否则显示sorted_number
                        Text(getDisplayText(for: point, at: index, in: optimizedPoints, isThirdPartySorted: isThirdPartySorted, isSavedThirdPartySorted: isSavedThirdPartySorted))
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                            .minimumScaleFactor(0.7)
                    }
                }

                // 地址信息 - 只有这部分可以点击
                VStack(alignment: .leading, spacing: 2) {
                    Text(point.primaryAddress)
                        .font(.subheadline)
                        .lineLimit(1)
                        .foregroundColor(.primary)

                    HStack(spacing: 4) {
                        // 包裹信息
                        if point.packageCount > 1 {
                            Label(String(format: "package_count_format".localized, point.packageCount), systemImage: "cube.box")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        // 🎯 显示第三方排序标签
                        if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                            Text("\(point.sourceApp.displayName): \(thirdPartySortNumber)")
                                .font(.system(size: 11, weight: .bold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(point.sourceApp.primaryColor)
                                .cornerRadius(4)
                        }

                        // 🏢 Unit标识：如果有单位号，显示橙色Unit标签
                        if point.hasUnitNumber, let unitNumber = point.unitNumber, !unitNumber.isEmpty {
                            Text(unitNumber)
                                .font(.system(size: 11, weight: .bold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange)
                                .cornerRadius(4)
                        }
                    }

                    if let notes = point.notes, !notes.isEmpty {
                        Text(notes)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                .contentShape(Rectangle()) // 只有地址信息区域可以点击
                .onTapGesture {
                    onTap()
                }

                Spacer()
            }
            .padding(.vertical, 8)
            .padding(.horizontal)
            .background(point.isOptimized ? Color.purple.opacity(0.05) : Color.clear)

            // 展开的操作按钮区域
            if isExpanded {
                HStack(spacing: 12) {
                    // GO 按钮
                    Button(action: onNavigate) {
                        HStack(spacing: 4) {
                            Image(systemName: "location.fill")
                                .font(.system(size: 12, weight: .semibold))
                            Text("GO")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(width: 60, height: 32)
                        .background(Color.blue)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Deliver 按钮
                    Button(action: onDelivery) {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 12, weight: .semibold))
                            Text("Deliver")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(width: 80, height: 32)
                        .background(Color.green)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // More 按钮
                    Button(action: onMore) {
                        Image(systemName: "ellipsis")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.gray)
                            .frame(width: 32, height: 32)
                            .background(Color(.systemGray5))
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Spacer()
                }
                .padding(.top, 4) // 减少地址和按钮之间的间距
                .padding(.bottom, 8)
                .padding(.leading, 44) // 对齐到地址文本：编号区域(32) + 间距(12) = 44
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }

    // 获取显示文本的辅助函数
    // 🎯 根据当前状态显示不同的序号
    private func getDisplayText(for point: DeliveryPoint, at index: Int, in points: [DeliveryPoint], isThirdPartySorted: Bool, isSavedThirdPartySorted: Bool) -> String {
        if isThirdPartySorted || isSavedThirdPartySorted {
            // 第三方排序时或已保存第三方排序时，显示实际的第三方排序号（如15, 16, 17, 71, 72）
            if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                return thirdPartySortNumber
            } else {
                // 如果没有第三方排序号，显示重新排列后的序号
                return "\(getDisplayNumber(for: point, at: index, in: points))"
            }
        } else {
            // AI优化时，显示sorted_number
            return "\(point.sorted_number)"
        }
    }

    // 计算显示编号的辅助函数
    private func getDisplayNumber(for point: DeliveryPoint, at index: Int, in points: [DeliveryPoint]) -> Int {
        if point.isStartPoint || point.sort_number == 0 {
            return 0
        }

        var nonStartPointOrder = 0
        for i in 0...index {
            let currentIterationPoint = points[i]
            if !(currentIterationPoint.isStartPoint || currentIterationPoint.sort_number == 0) {
                nonStartPointOrder += 1
            }
        }
        return nonStartPointOrder
    }
}

