import SwiftUI
import MapKit
import CoreLocation
import os.log
import SwiftData

// 验证地址信息结构体
struct ValidatedAddressInfo {
    let address: String
    let coordinate: CLLocationCoordinate2D
    let warningMessage: String?
}

// 复选框样式
struct CheckboxToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        Button(action: {
            configuration.isOn.toggle()
        }) {
            HStack {
                Image(systemName: configuration.isOn ? "checkmark.square.fill" : "square")
                    .foregroundColor(configuration.isOn ? .blue : .gray)
                    .font(.system(size: 20))
                configuration.label
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 导入订阅管理器
// 移除NaviBatch导入，直接使用本地类型

// 警告项
fileprivate struct BatchAlertItem: Identifiable {
    let id = UUID()
    let message: String
}

// 地址限制超出提示表单
struct AddressLimitExceededSheet: View {
    let currentCount: Int
    let remainingSlots: Int
    let maxAllowed: Int
    let selectedCount: Int
    let isFreeUser: Bool
    let selectedAddresses: [String]
    let onImportLimited: ([String]) -> Void
    let onUpgrade: () -> Void
    let onCancel: () -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var isProcessing: Bool = false

    var body: some View {
        // 🎯 采用UnifiedSubscriptionView的成功模式：简单的Group + 内容
        Group {
            GeometryReader { geometry in
                ZStack {
                    // 背景
                    Color(.systemBackground)
                        .ignoresSafeArea()

                    // 主内容
                    VStack(spacing: 0) {
                        // 顶部栏
                        topButtonBar

                        // 滚动内容
                        ScrollView {
                            VStack(spacing: 24) {
                                // 图标
                                Image(systemName: "exclamationmark.triangle")
                                    .font(.system(size: 50))
                                    .foregroundColor(.orange)
                                    .padding(.top, 20)

                                // 信息显示
                                VStack(spacing: 12) {
                                    Text("免费版最多允许20个地址。")
                                        .font(.body)
                                        .multilineTextAlignment(.center)

                                    Text("当前已有\(currentCount)个地址，只能再添加\(remainingSlots)个地址。")
                                        .font(.body)
                                        .multilineTextAlignment(.center)

                                    Text("您选择了\(selectedCount)个地址，超出可添加数量\(selectedCount - remainingSlots)个。")
                                        .font(.body)
                                        .foregroundColor(.orange)
                                        .multilineTextAlignment(.center)

                                    Text("升级到高级版可享受无限地址！")
                                        .font(.body)
                                        .bold()
                                        .multilineTextAlignment(.center)
                                        .padding(.top, 8)
                                }
                                .padding(.horizontal, 24)

                                // 底部升级按钮
                                if isFreeUser {
                                    Button(action: {
                                        onUpgrade()
                                    }) {
                                        Text("升级到高级版")
                                            .foregroundColor(.white)
                                            .font(.body)
                                            .fontWeight(.medium)
                                            .frame(maxWidth: .infinity)
                                            .padding(.vertical, 14)
                                            .background(Color.green)
                                            .cornerRadius(12)
                                    }
                                    .padding(.horizontal, 24)
                                    .padding(.bottom, 24)
                                }
                            }
                        }
                    }
                }
            }
        }
        .onAppear {
            // 详细的调试信息，帮助追踪渲染过程
            print("[DEBUG] AddressLimitExceededSheet 已出现")
            print("[DEBUG] - currentCount: \(currentCount)")
            print("[DEBUG] - remainingSlots: \(remainingSlots)")
            print("[DEBUG] - selectedCount: \(selectedCount)")
            print("[DEBUG] - isFreeUser: \(isFreeUser)")
            print("[DEBUG] - selectedAddresses.count: \(selectedAddresses.count)")

            // 提供触觉反馈，确认sheet已显示
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.impactOccurred()
        }
    }

    // 顶部按钮栏 - 参考UnifiedSubscriptionView的模式
    private var topButtonBar: some View {
        HStack {
            Button("取消") {
                onCancel()
            }
            .foregroundColor(.blue)

            Spacer()

            // 右侧按钮 - 简化逻辑
            if isFreeUser && selectedCount > 20 {
                Button("仅导入前20个") {
                    guard !isProcessing else { return }
                    isProcessing = true
                    let limitedAddresses = Array(selectedAddresses.prefix(20))
                    onImportLimited(limitedAddresses)
                }
                .foregroundColor(.blue)
                .disabled(isProcessing)
            } else if remainingSlots <= 0 {
                Button("import_all_addresses".localized) {
                    guard !isProcessing else { return }
                    isProcessing = true
                    onImportLimited(selectedAddresses)
                }
                .foregroundColor(.blue)
                .disabled(isProcessing)
            } else if selectedCount > remainingSlots {
                Button(String(format: "import_first_n".localized, remainingSlots)) {
                    guard !isProcessing else { return }
                    isProcessing = true
                    let limitedAddresses = Array(selectedAddresses.prefix(remainingSlots))
                    onImportLimited(limitedAddresses)
                }
                .foregroundColor(.blue)
                .disabled(isProcessing)
            } else {
                Button(String(format: "import_selected_addresses".localized, selectedCount)) {
                    guard !isProcessing else { return }
                    isProcessing = true
                    onImportLimited(selectedAddresses)
                }
                .foregroundColor(.blue)
                .disabled(isProcessing)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

/// 批量地址输入表单
struct BatchAddressInputSheet: View {
    @Environment(\.dismiss) private var dismiss
    @State private var addressText = ""
    @State private var isProcessing = false
    @State private var isImporting = false
    @State private var errorMessage: String? = nil
    @State private var showLimitExceededSheet = false
    @State private var limitExceededInfo: (currentCount: Int, remainingSlots: Int, maxAllowed: Int, selectedCount: Int, selectedAddresses: [String])? = nil
    @State private var showSubscriptionView = false
    @State private var validatedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = [] // 地址, 坐标, 是否选中, 警告信息
    @State private var processingProgress: Double = 0
    @State private var showValidationResults = false

    // 🚦 速率限制状态管理
    @StateObject private var rateLimitManager = RateLimitStatusManager()

    // 日志辅助函数 - 使用统一的Logger系统
    private func logInfo(_ message: String, function: String = #function) {
        Logger.info("[BatchAddressInputSheet.\(function)] \(message)", type: .action)
    }

    private func logError(_ message: String, function: String = #function) {
        Logger.error("[BatchAddressInputSheet.\(function)] \(message)", type: .error)
    }

    // 获取当前路线
    private func getCurrentRoute() -> Route? {
        do {
            let container = getPersistentContainer()
            let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
            let routes = try container.mainContext.fetch(descriptor)
            return routes.first
        } catch {
            logError("获取当前路线失败: \(error.localizedDescription)")
            return nil
        }
    }

    // 占位符视图
    private var placeholderView: some View {
        Group {
            if addressText.isEmpty {
                VStack {
                    HStack {
                        Text("batch_address_input_placeholder".localized)
                            .foregroundColor(.secondary)
                            .font(.caption)
                            .padding()
                        Spacer()
                    }
                    Spacer()
                }
            }
        }
    }

    // 移除选项卡状态

    // 回调函数 - 传递地址和验证信息
    var onAddressesAdded: (([String]) -> Void)?
    var onValidatedAddressesAdded: (([ValidatedAddressInfo]) -> Void)?

    // MARK: - 视图组件

    // 地址输入视图
    private var addressInputView: some View {
        VStack(spacing: 0) {
            // 文本输入区域
            TextEditor(text: $addressText)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(8)
                .background(Color(.systemGray6).opacity(0.3))
                .cornerRadius(8)
                .overlay(
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Button(action: {
                                // 粘贴功能
                                if let pasteboardString = UIPasteboard.general.string {
                                    addressText = pasteboardString
                                }
                            }) {
                                Image(systemName: "doc.on.clipboard")
                                    .foregroundColor(.blue)
                                    .padding(8)
                                    .background(Color.white)
                                    .cornerRadius(8)
                                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                            }
                            .padding(8)
                        }
                    }
                )
                .overlay(placeholderView)
                .padding(.horizontal)
                .padding(.top, 8)

            // 搜索按钮
            Button(action: {
                processAddresses()
            }) {
                Text("search".localized)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(8)
            }
            .padding()
            .disabled(addressText.isEmpty || isProcessing)
            .opacity(addressText.isEmpty || isProcessing ? 0.6 : 1)
        }
    }

    // 验证结果视图
    private var validationResultsView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 结果统计
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(String(format: "validated_addresses_count".localized, validatedAddresses.count))
                        .font(.headline)

                    HStack(spacing: 12) {
                        // 有效地址数量
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text("\(validAddressCount) \("valid".localized)")
                                .font(.caption)
                                .foregroundColor(.green)
                        }

                        // 有问题地址数量
                        if problematicAddressCount > 0 {
                            HStack(spacing: 4) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                                Text("\(problematicAddressCount) \("with_issues".localized)")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }

                Spacer()

                // 全选/全不选切换按钮
                Toggle(isOn: Binding(
                    get: { validatedAddresses.allSatisfy { $0.2 } },
                    set: { selectAllValidatedAddresses($0) }
                )) {
                    Text("select_all".localized)
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                .toggleStyle(SwitchToggleStyle(tint: .blue))
                .labelsHidden()
                .fixedSize()
            }
            .padding(.horizontal)
            .padding(.vertical, 8)

            // 地址列表
            List {
                ForEach(0..<validatedAddresses.count, id: \.self) { index in
                    let (address, coordinate, isSelected, warning) = validatedAddresses[index]
                    let hasIssue = !warning.isEmpty || (coordinate.latitude == 0 && coordinate.longitude == 0)

                    HStack(spacing: 10) {
                        // 选择框
                        Toggle(isOn: Binding(
                            get: { isSelected },
                            set: { _ in toggleValidatedAddressSelection(at: index) }
                        )) {}
                        .toggleStyle(CheckboxToggleStyle())
                        .labelsHidden()

                        // 地址信息
                        VStack(alignment: .leading, spacing: 4) {
                            HStack(spacing: 6) {
                                Text(address)
                                    .lineLimit(1)
                                    .font(.subheadline)

                                // 验证状态图标
                                if hasIssue {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.orange)
                                        .font(.caption)
                                } else {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.caption)
                                }
                            }

                            // 坐标和警告信息
                            HStack(spacing: 6) {
                                Text(String(format: "(%.4f, %.4f)", coordinate.latitude, coordinate.longitude))
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                if !warning.isEmpty {
                                    Text(warning)
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .lineLimit(1)
                                } else if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                    Text("无法获取坐标")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .lineLimit(1)
                                }
                            }
                        }

                        Spacer()
                    }
                    .padding(.vertical, 2)
                }
            }
            .listStyle(PlainListStyle())
        }
    }

    // 计算选中的验证地址数量
    private var selectedValidatedAddressCount: Int {
        validatedAddresses.filter { $0.2 }.count
    }

    // 计算有问题的地址数量
    private var problematicAddressCount: Int {
        validatedAddresses.filter { !$0.3.isEmpty }.count
    }

    // 计算有效地址数量
    private var validAddressCount: Int {
        validatedAddresses.filter { $0.3.isEmpty }.count
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {

                if showValidationResults && !validatedAddresses.isEmpty {
                    // 验证结果视图
                    validationResultsView
                } else {
                    // 地址输入视图
                    addressInputView
                }
            }
            .navigationTitle("batch_add_addresses".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 左侧：取消按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        if showValidationResults {
                            // 返回输入界面
                            showValidationResults = false
                            validatedAddresses = []
                        } else {
                            dismiss()
                        }
                    }
                    .foregroundColor(.blue)
                }

                // 右侧：导入按钮（仅在验证结果界面显示）
                if showValidationResults {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("import".localized) {
                            // 添加防重复点击保护
                            guard !isImporting else { return }
                            isImporting = true

                            // 使用Task确保异步处理正确
                            Task {
                                // 添加小延迟，让UI更新有时间响应
                                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                                await MainActor.run {
                                    importSelectedValidatedAddresses()
                                    // 重置处理状态
                                    isImporting = false
                                }
                            }
                        }
                        .disabled(selectedValidatedAddressCount == 0 || isProcessing || isImporting)
                        .opacity(selectedValidatedAddressCount > 0 && !isProcessing && !isImporting ? 1.0 : 0.5)
                        .foregroundColor(.blue)
                    }
                }
            }
            .overlay {
                // 统一的智能加载界面
                SimpleLoadingView(
                    isVisible: isProcessing || rateLimitManager.showWarning,
                    message: "validating_addresses".localized,
                    subtitle: nil,
                    progress: processingProgress,
                    showPercentage: true,
                    isRateLimited: rateLimitManager.showWarning
                )
            }
            .alert(item: Binding<BatchAlertItem?>(
                get: { errorMessage.map { BatchAlertItem(message: $0) } },
                set: { errorMessage = $0?.message }
            )) { alert in
                Alert(
                    title: Text("error".localized),
                    message: Text(alert.message),
                    dismissButton: .default(Text("confirm".localized))
                )
            }
            .sheet(isPresented: $showLimitExceededSheet) {
                if let info = limitExceededInfo {
                    AddressLimitExceededSheet(
                        currentCount: info.currentCount,
                        remainingSlots: info.remainingSlots,
                        maxAllowed: info.maxAllowed,
                        selectedCount: info.selectedCount,
                        isFreeUser: SubscriptionManager.shared.currentTier == .free,
                        selectedAddresses: info.selectedAddresses,
                        onImportLimited: { limitedAddresses in
                            // 只导入符合限制数量的地址
                            logInfo("BatchAddressInputSheet - 导入地址数量限制为\(info.remainingSlots)个，实际导入\(limitedAddresses.count)个")
                            if !limitedAddresses.isEmpty {
                                self.onAddressesAdded?(limitedAddresses)
                            } else {
                                // 显示错误提示
                                errorMessage = "no_importable_addresses".localized
                                return
                            }
                            self.dismiss()
                        },
                        onUpgrade: {
                            // 关闭当前sheet并显示订阅界面
                            showLimitExceededSheet = false
                            showSubscriptionView = true
                        },
                        onCancel: {
                            // 关闭表单
                            showLimitExceededSheet = false
                        }
                    )
                    // 为限制超出提示表单添加Sheet样式 - 支持medium和large尺寸
                    .presentationDetents([.medium, .large])
                    .presentationDragIndicator(.visible)
                    .interactiveDismissDisabled(false) // 允许交互式关闭
                }
            }
            .sheet(isPresented: $showSubscriptionView) {
                // 显示订阅界面
                UnifiedSubscriptionView(config: SubscriptionViewConfig(
                    style: .modern,
                    showTitle: true,
                    showCloseButton: true,
                    showFeatureComparison: true,
                    showOneClickPromo: true,
                    presentationMode: .sheet
                ))
                .presentationDetents([.large])
                .presentationDragIndicator(.visible)
            }
        }
    }

    // 处理地址
    private func processAddresses() {
        // 分割文本为多行
        let addresses = addressText
            .components(separatedBy: .newlines)
            .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        if addresses.isEmpty {
            errorMessage = "please_enter_valid_address".localized
            return
        }

        logInfo("BatchAddressInputSheet - 开始验证 \(addresses.count) 个地址")

        isProcessing = true
        processingProgress = 0

        // 🚦 检查是否需要显示速率限制警告
        Task {
            let status = await GlobalGeocodingRateLimiter.shared.getStatus()
            if status.isNearLimit {
                await MainActor.run {
                    rateLimitManager.triggerRateLimitWarning()
                }
            }
        }

        // 使用统一验证服务验证地址
        Task {
            let validationResults = await UnifiedAddressValidationService.shared.validateAddresses(
                addresses,
                batchSize: 3 // 🚦 减少批次大小
            ) { progress in
                DispatchQueue.main.async {
                    self.processingProgress = progress
                }
            }

            // 转换验证结果为显示格式
            let processedAddresses = validationResults.map { result in
                // 判断地址是否有问题
                var hasIssue = false
                var warningMessage = ""

                // 检查验证警告
                if let warning = result.warningMessage, !warning.isEmpty {
                    hasIssue = true
                    warningMessage = warning
                }

                // 检查坐标有效性
                if result.coordinate.latitude == 0 && result.coordinate.longitude == 0 {
                    hasIssue = true
                    if warningMessage.isEmpty {
                        warningMessage = "cannot_get_coordinates".localized
                    }
                }

                // 检查置信度
                if result.confidence == .veryLow || result.confidence == .low {
                    hasIssue = true
                    if warningMessage.isEmpty {
                        warningMessage = "low_confidence_address".localized
                    }
                }

                // 检查是否有效
                if !result.isValid {
                    hasIssue = true
                    if warningMessage.isEmpty {
                        warningMessage = "address_validation_failed".localized
                    }
                }

                return (
                    result.originalAddress,
                    result.coordinate,
                    !hasIssue, // 只有没有问题的地址才默认选中
                    warningMessage
                )
            }

            await MainActor.run {
                self.validatedAddresses = processedAddresses
                self.isProcessing = false
                self.showValidationResults = true

                // 🚦 关闭速率限制警告
                self.rateLimitManager.dismissWarning()

                logInfo("BatchAddressInputSheet - 验证完成，共 \(processedAddresses.count) 个地址")
            }
        }
    }

    // 导入选中的验证地址
    private func importSelectedValidatedAddresses() {
        let selectedValidatedInfo = validatedAddresses
            .filter { $0.2 } // 只选择被选中的地址
            .map { ValidatedAddressInfo(address: $0.0, coordinate: $0.1, warningMessage: $0.3.isEmpty ? nil : $0.3) }

        let selectedAddresses = selectedValidatedInfo.map { $0.address }

        if selectedAddresses.isEmpty {
            errorMessage = "no_addresses_selected".localized
            return
        }

        // 检查订阅限制
        let subscriptionManager = SubscriptionManager.shared
        let currentRoute = getCurrentRoute()
        let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute

        logInfo("BatchAddressInputSheet - 准备导入 \(selectedAddresses.count) 个地址")

        if let route = currentRoute {
            let currentCount = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
            let remainingSlots = maxAllowed - currentCount

            if selectedAddresses.count > remainingSlots {
                // 保存超出限制的信息，并显示自定义表单
                limitExceededInfo = (
                    currentCount: currentCount,
                    remainingSlots: remainingSlots,
                    maxAllowed: maxAllowed,
                    selectedCount: selectedAddresses.count,
                    selectedAddresses: selectedAddresses
                )

                // 显示自定义表单
                showLimitExceededSheet = true
                return
            }
        }

        // 优先调用新的验证信息回调函数
        if let validatedCallback = onValidatedAddressesAdded {
            validatedCallback(selectedValidatedInfo)
        } else {
            // 兼容旧的回调函数
            onAddressesAdded?(selectedAddresses)
        }

        // 🚨 检查并提示问题地址
        Task {
            // 延迟一下让地址处理完成
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            await MainActor.run {
                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "批量地址输入")
            }
        }

        // 关闭表单
        dismiss()
    }

    // 选择/取消选择所有验证地址
    private func selectAllValidatedAddresses(_ selectAll: Bool) {
        for index in 0..<validatedAddresses.count {
            validatedAddresses[index].2 = selectAll
        }
    }

    // 切换验证地址选择状态
    private func toggleValidatedAddressSelection(at index: Int) {
        guard index < validatedAddresses.count else { return }
        validatedAddresses[index].2.toggle()
    }
}

// 移除选项卡按钮结构体

#Preview("BatchAddressInputSheet") {
    BatchAddressInputSheet(onAddressesAdded: { _ in })
}
