import SwiftUI
import PhotosUI

/// 配送照片查看组件
struct DeliveryPhotoViewer: View {
    // 环境变量
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // 配送点
    let deliveryPoint: DeliveryPoint

    // 初始照片类型（可选）
    var initialPhotoType: DeliveryPhotoType?

    // 状态变量
    @State private var selectedPhotoType: DeliveryPhotoType = .doorNumber
    @State private var photos: [DeliveryPhotoType: UIImage] = [:]
    @State private var isLoading = true
    @State private var errorMessage: String? = nil
    @State private var showingShareSheet = false
    @State private var imageToShare: UIImage?

    // 初始化方法
    init(deliveryPoint: DeliveryPoint, initialPhotoType: DeliveryPhotoType? = nil) {
        self.deliveryPoint = deliveryPoint
        self.initialPhotoType = initialPhotoType
        self._selectedPhotoType = State(initialValue: initialPhotoType ?? .doorNumber)
    }

    var body: some View {
        ZStack {
            // 背景色
            backgroundGradient
                .ignoresSafeArea()

            // 主要内容
            mainContent
        }
        .navigationTitle("delivery_photos".localized)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                shareButton
            }
        }
        .alert("error".localized, isPresented: .init(get: { errorMessage != nil }, set: { if !$0 { errorMessage = nil } })) {
            Button("ok".localized, role: .cancel) {
                errorMessage = nil
            }
        } message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            loadPhotos()
        }
        .sheet(isPresented: $showingShareSheet, onDismiss: {
            imageToShare = nil
        }) {
            if let image = imageToShare {
                ShareSheet(items: [image])
            }
        }
    }

    // 主要内容视图
    private var mainContent: some View {
        Group {
            if isLoading {
                loadingView
            } else if !photos.isEmpty {
                photoDisplayView
            } else {
                emptyStateView
            }
        }
    }

    // 加载中视图
    private var loadingView: some View {
        VStack {
            ProgressView()
                .scaleEffect(1.5)
            Text("loading_photos".localized)
                .font(.headline)
                .padding(.top, 16)
        }
    }

    // 照片显示视图
    private var photoDisplayView: some View {
        VStack {
            // 照片类型选择器
            Picker("照片类型", selection: $selectedPhotoType) {
                ForEach(DeliveryPhotoType.allCases) { type in
                    if photos[type] != nil {
                        Text(type.title)
                            .tag(type)
                    }
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal)
            .padding(.top, 8)

            // 照片显示区域
            photoImageView

            Spacer()
        }
    }

    // 当前选中的照片视图
    private var photoImageView: some View {
        Group {
            if let image = photos[selectedPhotoType] {
                ZoomableImageView(image: image)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .padding()
            } else {
                // 理论上不应该到达这里，因为我们已经过滤了只显示有照片的类型
                Color.clear
            }
        }
    }

    // 无照片状态视图
    private var emptyStateView: some View {
        VStack {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            Text("no_photos_found".localized)
                .font(.title)
                .padding(.top, 16)

            Text("photos_deleted_or_not_taken".localized)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }

    // 分享按钮
    private var shareButton: some View {
        Group {
            if !photos.isEmpty, let image = photos[selectedPhotoType] {
                Button(action: {
                    self.imageToShare = image
                    self.showingShareSheet = true
                }) {
                    Image(systemName: "square.and.arrow.up")
                }
            } else {
                EmptyView()
            }
        }
    }

    // 获取当前照片的标题
    private func getTitleForCurrentPhoto() -> String {
        switch selectedPhotoType {
        case .doorNumber:
            return "door_number_photo".localized
        case .packageLabel:
            return "package_label_photo".localized
        case .placement:
            return "placement_photo".localized
        }
    }

    // 背景渐变
    private var backgroundGradient: some View {
        Group {
            if colorScheme == .dark {
                LinearGradient(
                    gradient: Gradient(colors: [Color(hex: "1A1A1A"), Color(hex: "2A2A2A")]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            } else {
                LinearGradient(
                    gradient: Gradient(colors: [Color(hex: "F8F8F8"), Color(hex: "F0F0F0")]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            }
        }
    }

    // 加载照片
    private func loadPhotos() {
        Task {
            do {
                // 设置加载状态
                isLoading = true

                // 使用空字典初始化
                var loadedPhotos: [DeliveryPhotoType: UIImage] = [:]

                // 加载门牌号照片
                if let doorPath = deliveryPoint.doorPhotoPath {
                    if let doorImage = try await loadImage(from: doorPath) {
                        loadedPhotos[.doorNumber] = doorImage
                    }
                }

                // 加载包裹标签照片
                if let packagePath = deliveryPoint.packagePhotoPath {
                    if let packageImage = try await loadImage(from: packagePath) {
                        loadedPhotos[.packageLabel] = packageImage
                    }
                }

                // 加载放置位置照片
                if let placementPath = deliveryPoint.placementPhotoPath {
                    if let placementImage = try await loadImage(from: placementPath) {
                        loadedPhotos[.placement] = placementImage
                    }
                }

                // 更新状态
                await MainActor.run {
                    self.photos = loadedPhotos

                    // 如果有照片，选择第一张可用的照片
                    if let firstType = loadedPhotos.keys.sorted(by: { $0.rawValue < $1.rawValue }).first {
                        self.selectedPhotoType = firstType
                    }

                    // 完成加载
                    self.isLoading = false
                }
            } catch {
                print("加载照片错误: \(error.localizedDescription)")
                await MainActor.run {
                    self.errorMessage = "无法加载照片: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    // 从路径加载图片
    private func loadImage(from path: String) async throws -> UIImage? {
        return try await PhotoAlbumService.shared.loadPhotoFromPath(path)
    }
}

// 实现用于分享照片的UIActivityViewController包装器
struct ShareSheet: UIViewControllerRepresentable {
    var items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // 无需更新
    }
}

#Preview("DeliveryPhotoViewer") {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: DeliveryPoint.self, configurations: config)

    let samplePoint = DeliveryPoint(
        sort_number: 1,
        streetName: "42 Blackburn Road, Glen Waverley, VIC 3150",
        latitude: -37.8815,
        longitude: 145.1642
    )
    let _ = {
        samplePoint.doorPhotoPath = "sample_path"
        samplePoint.packagePhotoPath = "sample_path"
        samplePoint.placementPhotoPath = "sample_path"
        container.mainContext.insert(samplePoint)
    }()

    return NavigationStack {
        DeliveryPhotoViewer(deliveryPoint: samplePoint)
    }
    .modelContainer(container)
}