import SwiftUI
import MapKit

/// 智能地址补全视图 - 显示AI预测的地址供用户确认
struct SmartAddressCompletionView: View {
    let predictions: [AddressPatternAnalyzer.PredictedAddress]
    let onConfirmAddress: (Int, String) -> Void
    let onUseAppleMaps: (Int) -> Void
    let onSkip: (Int) -> Void
    let onCancel: () -> Void

    @State private var currentIndex = 0
    @State private var isShowingAppleMaps = false
    @State private var selectedStopNumber = 0

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.blue)
                    .font(.title2)

                Text("AI地址预测")
                    .font(.headline)
                    .foregroundColor(.primary)
            }
            .padding(.top)

            // 进度指示器
            if predictions.count > 1 {
                HStack {
                    Text("第 \(currentIndex + 1) 个，共 \(predictions.count) 个")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    // 进度条
                    ProgressView(value: Double(currentIndex + 1), total: Double(predictions.count))
                        .frame(width: 100)
                }
                .padding(.horizontal)
            }

            Divider()

            if currentIndex < predictions.count {
                let prediction = predictions[currentIndex]

                // 当前预测内容
                VStack(alignment: .leading, spacing: 16) {
                    // 停靠点信息
                    HStack {
                        Image(systemName: "mappin.circle.fill")
                            .foregroundColor(.red)
                            .font(.title3)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("停靠点 \(prediction.stopNumber)")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Text("置信度: \(Int(prediction.confidence * 100))%")
                                .font(.caption)
                                .foregroundColor(confidenceColor(prediction.confidence))
                        }

                        Spacer()
                    }

                    // 预测地址
                    VStack(alignment: .leading, spacing: 8) {
                        Text("AI预测地址:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)

                        Text(prediction.fullAddress)
                            .font(.body)
                            .foregroundColor(.primary)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                    }

                    // 模式信息
                    VStack(alignment: .leading, spacing: 4) {
                        Text("基于模式:")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)

                        Text("街道: \(prediction.pattern.streetName)")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("递增: \(prediction.pattern.increment) (每个停靠点)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 8)
                }
                .padding(.horizontal)

                // 操作按钮
                VStack(spacing: 12) {
                    // 主要操作 - 使用Apple Maps确认坐标
                    Button(action: {
                        selectedStopNumber = prediction.stopNumber
                        isShowingAppleMaps = true
                    }) {
                        HStack {
                            Image(systemName: "map.fill")
                            Text("使用Apple Maps确认坐标")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }

                    // 次要操作
                    HStack(spacing: 12) {
                        // 直接使用预测地址（不推荐，但允许）
                        Button(action: {
                            confirmCurrentAddressWithoutCoordinates()
                        }) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle")
                                Text("直接使用")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.orange.opacity(0.2))
                            .foregroundColor(.orange)
                            .cornerRadius(8)
                        }

                        // 跳过此项
                        Button(action: {
                            skipCurrentAddress()
                        }) {
                            HStack {
                                Image(systemName: "arrow.right.circle")
                                Text("跳过")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                        }
                    }

                    // 取消按钮
                    if currentIndex == 0 {
                        Button(action: onCancel) {
                            Text("取消")
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 10)
                                .background(Color.gray.opacity(0.2))
                                .foregroundColor(.primary)
                                .cornerRadius(8)
                        }
                    }
                }
                .padding(.horizontal)
            }

            Spacer()
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 10)
        .padding(.horizontal, 20)
        .sheet(isPresented: $isShowingAppleMaps) {
            AppleMapsSearchView(
                stopNumber: selectedStopNumber,
                predictedAddress: currentIndex < predictions.count ? predictions[currentIndex].fullAddress : "",
                onAddressSelected: { address in
                    onConfirmAddress(selectedStopNumber, address)
                    moveToNext()
                },
                onCancel: {
                    isShowingAppleMaps = false
                }
            )
        }
    }

    // MARK: - Private Methods

    private func confidenceColor(_ confidence: Double) -> Color {
        if confidence >= 0.8 {
            return .green
        } else if confidence >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }

    private func confirmCurrentAddressWithoutCoordinates() {
        let prediction = predictions[currentIndex]
        onConfirmAddress(prediction.stopNumber, prediction.fullAddress)
        moveToNext()
    }

    private func skipCurrentAddress() {
        let prediction = predictions[currentIndex]
        onSkip(prediction.stopNumber)
        moveToNext()
    }

    private func moveToNext() {
        if currentIndex < predictions.count - 1 {
            withAnimation {
                currentIndex += 1
            }
        } else {
            // 所有预测都处理完了
            onCancel()
        }
    }
}

/// Apple Maps搜索视图
struct AppleMapsSearchView: View {
    let stopNumber: Int
    let predictedAddress: String
    let onAddressSelected: (String) -> Void
    let onCancel: () -> Void

    @State private var searchText = ""
    @State private var searchResults: [MKMapItem] = []
    @State private var isSearching = false
    @State private var hasAutoSearched = false

    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // AI预测地址提示
                if !predictedAddress.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("AI预测地址:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)

                        Text(predictedAddress)
                            .font(.body)
                            .foregroundColor(.primary)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )

                        Text("请在下方搜索确认坐标，或修改地址后搜索")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                }

                // 搜索框
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)

                    TextField("搜索停靠点 \(stopNumber) 的地址...", text: $searchText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .onSubmit {
                            searchAddress()
                        }

                    if isSearching {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
                .padding(.horizontal)

                // 搜索结果
                List(searchResults, id: \.self) { mapItem in
                    VStack(alignment: .leading, spacing: 4) {
                        Text(mapItem.name ?? "未知地址")
                            .font(.headline)

                        if let address = mapItem.placemark.title {
                            Text(address)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectAddress(mapItem)
                    }
                }

                Spacer()
            }
            .navigationTitle("地址搜索")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消", action: onCancel),
                trailing: Button("搜索") {
                    searchAddress()
                }
                .disabled(searchText.isEmpty || isSearching)
            )
            .onAppear {
                // 自动填充AI预测的地址并搜索
                if !predictedAddress.isEmpty && !hasAutoSearched {
                    searchText = predictedAddress
                    hasAutoSearched = true
                    // 延迟一点时间自动搜索，让用户看到预测地址
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        searchAddress()
                    }
                }
            }
        }
    }

    private func searchAddress() {
        guard !searchText.isEmpty else { return }

        isSearching = true

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = searchText

        let search = MKLocalSearch(request: request)
        search.start { response, error in
            DispatchQueue.main.async {
                isSearching = false

                if let response = response {
                    searchResults = response.mapItems
                } else {
                    searchResults = []
                }
            }
        }
    }

    private func selectAddress(_ mapItem: MKMapItem) {
        let address = formatAddress(from: mapItem.placemark)
        onAddressSelected(address)
    }

    private func formatAddress(from placemark: CLPlacemark) -> String {
        var components: [String] = []

        if let streetNumber = placemark.subThoroughfare {
            components.append(streetNumber)
        }

        if let streetName = placemark.thoroughfare {
            components.append(streetName)
        }

        if let city = placemark.locality {
            components.append(city)
        }

        if let state = placemark.administrativeArea {
            components.append(state)
        }

        if let zipCode = placemark.postalCode {
            components.append(zipCode)
        }

        return components.joined(separator: ", ")
    }
}

#Preview("SmartAddressCompletionView") {
    let samplePredictions = [
        AddressPatternAnalyzer.PredictedAddress(
            stopNumber: 4,
            fullAddress: "399 Imperial Way Apt 240, Daly City, CA, 94015",
            confidence: 0.85,
            pattern: AddressPatternAnalyzer.AddressPattern(
                streetName: "Imperial Way",
                baseNumber: 397,
                increment: 2,
                isOddSequence: true,
                suffix: "Apt",
                city: "Daly City",
                state: "CA",
                zipCode: "94015",
                confidence: 0.85
            )
        )
    ]

    SmartAddressCompletionView(
        predictions: samplePredictions,
        onConfirmAddress: { _, _ in },
        onUseAppleMaps: { _ in },
        onSkip: { _ in },
        onCancel: { }
    )
}
