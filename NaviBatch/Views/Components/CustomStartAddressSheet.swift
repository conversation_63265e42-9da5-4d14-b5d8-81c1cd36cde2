import SwiftUI
import MapKit

struct CustomStartAddressSheet: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: RouteViewModel
    
    @State private var addressText: String = ""
    @State private var isProcessing: Bool = false
    @State private var errorMessage: String? = nil
    
    var body: some View {
        NavigationView {
            VStack {
                // 标题和说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("设置自定义起点")
                        .font(.headline)
                        .padding(.top, 8)
                    
                    Text("输入一个地址作为导航的起点。如果不设置，系统将使用您的当前位置作为起点。")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
                .padding(.bottom, 8)
                
                // 地址输入框
                VStack(alignment: .leading, spacing: 4) {
                    Text("地址")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    TextField("输入完整地址", text: $addressText)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                .padding(.horizontal)
                
                // 错误信息
                if let error = errorMessage {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(.horizontal)
                        .padding(.top, 4)
                }
                
                // 当前设置的起点信息
                if let currentAddress = viewModel.customStartAddress {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("当前设置的起点")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text(currentAddress)
                            .font(.body)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                    .padding(.horizontal)
                    .padding(.top, 16)
                }
                
                Spacer()
                
                // 按钮区域
                VStack(spacing: 12) {
                    // 设置按钮
                    Button(action: {
                        Task {
                            await setCustomStartAddress()
                        }
                    }) {
                        HStack {
                            Image(systemName: "location.fill")
                            Text("设置起点")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(addressText.isEmpty || isProcessing)
                    
                    // 清除按钮
                    Button(action: {
                        viewModel.clearCustomStartAddress()
                        addressText = ""
                        errorMessage = nil
                    }) {
                        HStack {
                            Image(systemName: "xmark.circle")
                            Text("清除起点")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red.opacity(0.8))
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(viewModel.customStartAddress == nil || isProcessing)
                }
                .padding()
            }
            .navigationBarTitle("自定义起点", displayMode: .inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    dismiss()
                }
            )
            .onAppear {
                // 如果已有自定义起点，显示在输入框中
                if let address = viewModel.customStartAddress {
                    addressText = address
                }
            }
        }
    }
    
    // 设置自定义起点
    private func setCustomStartAddress() async {
        guard !addressText.isEmpty else { return }
        
        // 设置处理中状态
        isProcessing = true
        errorMessage = nil
        
        // 地理编码地址
        if let coordinate = await viewModel.geocodeAddress(addressText) {
            // 设置自定义起点
            viewModel.setCustomStartAddress(addressText, coordinate: coordinate)
            
            // 完成处理
            isProcessing = false
            
            // 关闭表单
            dismiss()
        } else {
            // 地理编码失败
            errorMessage = "无法解析地址，请确保输入了完整的地址信息"
            isProcessing = false
        }
    }
}

#Preview("CustomStartAddressSheet") {
    CustomStartAddressSheet(viewModel: RouteViewModel.shared)
}
