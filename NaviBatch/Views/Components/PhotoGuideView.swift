import SwiftUI

/// 照片拍摄指南视图
/// 为用户提供拍摄特定类型照片的指导
struct PhotoGuideView: View {
    // 照片类型
    let photoType: DeliveryPhotoType
    
    // 是否显示详细指南
    @State private var showingDetailedGuide: Bool = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和图标
            HStack(spacing: 12) {
                // 图标
                Image(systemName: photoType.iconName)
                    .font(.system(size: 24))
                    .foregroundColor(.blue)
                    .frame(width: 40, height: 40)
                    .background(Color.blue.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 10))
                
                VStack(alignment: .leading, spacing: 4) {
                    // 标题
                    Text(photoType.title)
                        .font(.headline)
                    
                    // 简短描述
                    Text(photoType.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // 展开/收起按钮
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        showingDetailedGuide.toggle()
                    }
                }) {
                    Image(systemName: showingDetailedGuide ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                        .font(.system(size: 22))
                        .foregroundColor(.blue)
                }
            }
            
            // 详细指南（可展开/收起）
            if showingDetailedGuide {
                VStack(alignment: .leading, spacing: 12) {
                    Divider()
                    
                    // 示例图片
                    exampleImage
                        .frame(height: 120)
                        .cornerRadius(8)
                    
                    // 详细指南
                    detailedGuide
                }
                .padding(.top, 8)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // 示例图片
    private var exampleImage: some View {
        ZStack {
            // 使用占位图
            Rectangle()
                .fill(Color.gray.opacity(0.2))
            
            // 图标
            VStack(spacing: 8) {
                Image(systemName: photoType.iconName)
                    .font(.system(size: 30))
                    .foregroundColor(.blue)
                
                Text("示例图片")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // 详细指南
    private var detailedGuide: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("拍摄要求")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            // 根据照片类型显示不同的拍摄要求
            ForEach(guidePoints, id: \.self) { point in
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.green)
                        .padding(.top, 4)
                    
                    Text(point)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // 根据照片类型返回不同的指南要点
    private var guidePoints: [String] {
        switch photoType {
        case .doorNumber:
            return [
                "确保门牌号清晰可见",
                "尽量正面拍摄，避免倾斜",
                "保持适当距离，确保数字/字母清晰",
                "避免强光反射或阴影遮挡"
            ]
        case .packageLabel:
            return [
                "确保包裹标签上的编号或标识清晰可见",
                "避免手指遮挡重要信息",
                "保持适当距离，确保文字清晰",
                "如有条形码或追踪号，请确保完整显示"
            ]
        case .placement:
            return [
                "拍摄包裹放置位置",
                "确保能看到包裹放置的环境",
                "如放置在特殊位置，请拍摄清晰",
                "确保照片能证明包裹已妥善放置"
            ]
        }
    }
}

// 预览
#Preview("PhotoGuideView") {
    VStack(spacing: 20) {
        PhotoGuideView(photoType: .doorNumber)
        PhotoGuideView(photoType: .packageLabel)
        PhotoGuideView(photoType: .placement)
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
