import SwiftUI
import MapKit

/// 地图位置预览组件
struct MapLocationPreview: View {
    let coordinate: CLLocationCoordinate2D
    @State private var cameraPosition: MapCameraPosition
    @Environment(\.dismiss) private var dismiss

    init(coordinate: CLLocationCoordinate2D) {
        self.coordinate = coordinate
        self._cameraPosition = State(initialValue: .region(MKCoordinateRegion(
            center: coordinate,
            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
        )))
    }

    var body: some View {
        ZStack {
            Map(position: $cameraPosition) {
                Marker("位置", coordinate: coordinate)
                    .tint(.red)
            }
            .mapStyle(.standard)
            .ignoresSafeArea()

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                            .padding()
                    }
                }
                Spacer()
            }

            // 底部控制按钮
            VStack {
                Spacer()
                HStack {
                    // 放大按钮
                    Button(action: {
                        withAnimation {
                            zoomIn()
                        }
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()

                    Spacer()

                    // 缩小按钮
                    Button(action: {
                        withAnimation {
                            zoomOut()
                        }
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                .padding(.bottom)
            }
        }
    }

    // 放大地图
    private func zoomIn() {
        // 创建新的缩放区域
        let zoomRegion = MKCoordinateRegion(
            center: coordinate,
            span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
        )

        // 直接设置新的缩放区域
        withAnimation {
            cameraPosition = .region(zoomRegion)
        }
    }

    // 缩小地图
    private func zoomOut() {
        // 创建新的缩放区域
        let zoomRegion = MKCoordinateRegion(
            center: coordinate,
            span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        )

        // 直接设置新的缩放区域
        withAnimation {
            cameraPosition = .region(zoomRegion)
        }
    }
}

#Preview("MapLocationPreview") {
    MapLocationPreview(coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642))
}
