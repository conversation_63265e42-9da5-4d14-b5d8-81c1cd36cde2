import SwiftUI
import MapKit
import os.log

/// 路线地图视图
/// 显示路线上的所有配送点，并绘制路线
struct RouteMapView: View {
    // 路线
    let route: Route

    // 选中的配送点
    @Binding var selectedPoint: DeliveryPoint?

    // 地图类型
    @State private var mapType: MKMapType = .standard

    // 是否显示用户位置
    @State private var showUserLocation: Bool = true

    // 是否显示路线
    @State private var showRoute: Bool = true

    // 是否显示警告
    @State private var showWarnings: Bool = true

    // 路线覆盖物
    @State private var routeOverlay: MKRoute?

    var body: some View {
        ZStack {
            mapContent
            .mapStyle(.standard)
            .onAppear {
                updateCameraPosition()
                calculateRoute()
            }
            .onChange(of: route.points) { _, _ in
                updateCameraPosition()
                calculateRoute()
            }
            .onChange(of: selectedPoint) { _, _ in
                handleSelectedPointChange()
            }

            // 控制按钮
            VStack {
                HStack {
                    Spacer()
                    VStack(spacing: 8) {
                        // 地图类型切换按钮已移除

                        // 用户位置切换
                        Button {
                            showUserLocation.toggle()
                        } label: {
                            Image(systemName: showUserLocation ? "location.fill" : "location")
                                .padding(8)
                                .background(Color(.systemBackground))
                                .clipShape(Circle())
                                .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }

                        // 路线显示切换
                        Button {
                            showRoute.toggle()
                        } label: {
                            Image(systemName: showRoute ? "route" : "route.slash")
                                .padding(8)
                                .background(Color(.systemBackground))
                                .clipShape(Circle())
                                .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }

                        // 警告显示切换
                        Button {
                            showWarnings.toggle()
                        } label: {
                            Image(systemName: showWarnings ? "exclamationmark.triangle" : "exclamationmark.triangle.slash")
                                .padding(8)
                                .background(Color(.systemBackground))
                                .clipShape(Circle())
                                .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }

                        // 重置视图
                        Button {
                            updateCameraPosition()
                        } label: {
                            Image(systemName: "arrow.triangle.2.circlepath")
                                .padding(8)
                                .background(Color(.systemBackground))
                                .clipShape(Circle())
                                .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                    }
                    .padding(8)
                }
                Spacer()
            }

            // 详情视图
            if let point = selectedPoint {
                VStack {
                    Spacer()
                    DeliveryPointCalloutView(point: point)
                        .padding()
                }
            }
        }
    }

    // 地图内容 - 分离复杂表达式
    private var mapContent: some View {
        Map {
            // 用户位置
            if showUserLocation, let userLocation = LocationManager.shared.userLocation {
                Annotation("", coordinate: userLocation) {
                    UserLocationPulseView()
                }
            }

            // 配送点标记 - 暂时使用原生标记
            ForEach(route.points) { point in
                Marker(getDisplayText(for: point, in: route.points), coordinate: point.coordinate)
                    .tint(getPointColor(point))
            }

            // 🚨 路线覆盖物（优化显示效果）
            if let routeOverlay = routeOverlay, showRoute {
                MapPolyline(routeOverlay.polyline)
                    .stroke(
                        LinearGradient(
                            colors: [.blue, .cyan],
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 5, lineCap: .round, lineJoin: .round)
                    )
            }
        }
        .mapStyle(.standard)
        .onAppear {
            updateCameraPosition()
            calculateRoute()
        }
        .onChange(of: route.points) { _, _ in
            updateCameraPosition()
            calculateRoute()
        }
        .onChange(of: selectedPoint) { _, _ in
            handleSelectedPointChange()
        }
    }



    // 处理选中点变化
    private func handleSelectedPointChange() {
        // 简化实现，避免使用cameraPosition
    }

    // 更新地图位置
    private func updateCameraPosition() {
        // 简化实现，避免使用cameraPosition
    }

    // 计算路线 - 优化版本：使用后台线程
    private func calculateRoute() {
        guard route.points.count >= 2 else {
            routeOverlay = nil
            return
        }

        // 使用后台任务处理路线计算
        Task.detached(priority: .userInitiated) {
            await self.performRouteCalculationInBackground()
        }
    }

    // 在后台线程执行路线计算 - 修复：计算完整的多点路线
    private func performRouteCalculationInBackground() async {
        // 按排序号排序
        let sortedPoints = route.points.sorted { $0.sorted_number < $1.sorted_number }

        guard sortedPoints.count >= 2 else {
            await MainActor.run {
                self.routeOverlay = nil
            }
            return
        }

        print("🔍 RouteMapView - 开始计算完整路线，点数: \(sortedPoints.count)")

        // 🚨 修复：计算完整的多点路线，而不是只计算起点到终点
        let coordinates = sortedPoints.map { $0.coordinate }
        let calculatedRoute = await calculateMultiPointRoute(coordinates: coordinates)

        // 在主线程更新UI
        await MainActor.run {
            if let calculatedRoute = calculatedRoute {
                self.routeOverlay = calculatedRoute
                print("✅ RouteMapView - 完整路线计算成功，距离: \(String(format: "%.1f", calculatedRoute.distance/1000))公里")

                // 更新路线信息
                self.route.totalDistance = calculatedRoute.distance
                self.route.estimatedDuration = calculatedRoute.expectedTravelTime
            } else {
                self.routeOverlay = nil
                print("🚨 RouteMapView - 完整路线计算失败")
            }
        }
    }

    // 🚨 新增：计算多点路线的方法
    private func calculateMultiPointRoute(coordinates: [CLLocationCoordinate2D]) async -> MKRoute? {
        guard coordinates.count >= 2 else { return nil }

        print("🔍 RouteMapView - 计算多点路线，坐标数: \(coordinates.count)")

        // 如果只有两个点，直接计算
        if coordinates.count == 2 {
            return await calculateRouteAsync(
                from: coordinates[0],
                to: coordinates[1],
                avoidTolls: route.avoidTolls,
                avoidHighways: route.avoidHighways
            )
        }

        // 多个点时，计算所有段的路线并合并
        var allRouteCoordinates: [CLLocationCoordinate2D] = []
        var totalDistance: CLLocationDistance = 0
        var totalTime: TimeInterval = 0

        for i in 0..<(coordinates.count - 1) {
            let segmentRoute = await calculateRouteAsync(
                from: coordinates[i],
                to: coordinates[i + 1],
                avoidTolls: route.avoidTolls,
                avoidHighways: route.avoidHighways
            )

            if let segmentRoute = segmentRoute {
                // 提取路线坐标
                let polyline = segmentRoute.polyline
                let pointCount = polyline.pointCount
                let routeCoordinates = UnsafeMutablePointer<CLLocationCoordinate2D>.allocate(capacity: pointCount)
                polyline.getCoordinates(routeCoordinates, range: NSRange(location: 0, length: pointCount))

                // 添加到总路线（避免重复点）
                for j in 0..<pointCount {
                    if allRouteCoordinates.isEmpty ||
                       (allRouteCoordinates.last!.latitude != routeCoordinates[j].latitude ||
                        allRouteCoordinates.last!.longitude != routeCoordinates[j].longitude) {
                        allRouteCoordinates.append(routeCoordinates[j])
                    }
                }

                routeCoordinates.deallocate()
                totalDistance += segmentRoute.distance
                totalTime += segmentRoute.expectedTravelTime

                print("✅ RouteMapView - 路线段 \(i+1) 计算成功，距离: \(String(format: "%.1f", segmentRoute.distance/1000))公里")
            } else {
                print("🚨 RouteMapView - 路线段 \(i+1) 计算失败")
                return nil
            }
        }

        // 创建合并后的路线
        if !allRouteCoordinates.isEmpty {
            return createMKRoute(from: allRouteCoordinates, distance: totalDistance, expectedTravelTime: totalTime)
        }

        return nil
    }

    // 🚨 新增：从坐标数组创建MKRoute的辅助方法
    private func createMKRoute(from coordinates: [CLLocationCoordinate2D], distance: CLLocationDistance, expectedTravelTime: TimeInterval) -> MKRoute? {
        // 创建MKPolyline
        let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)

        // 由于MKRoute是只读的，我们需要使用一个包装类或者直接使用MKPolyline
        // 这里我们返回一个简单的MKRoute替代方案
        // 注意：这是一个简化的实现，实际的MKRoute有更多属性

        return MKRouteWrapper(polyline: polyline, distance: distance, expectedTravelTime: expectedTravelTime)
    }

    // 异步路线计算方法（保持原有的两点计算逻辑）
    private func calculateRouteAsync(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        avoidTolls: Bool,
        avoidHighways: Bool
    ) async -> MKRoute? {

        return await withCheckedContinuation { continuation in
            // 创建请求
            let request = MKDirections.Request()

            // 设置起点和终点
            request.source = MKMapItem(placemark: MKPlacemark(coordinate: source))
            request.destination = MKMapItem(placemark: MKPlacemark(coordinate: destination))

            // 设置交通方式
            request.transportType = .automobile

            // 应用路线偏好设置
            request.tollPreference = avoidTolls ? .avoid : .any
            request.highwayPreference = avoidHighways ? .avoid : .any

            // 计算路线
            let directions = MKDirections(request: request)
            directions.calculate { response, error in
                if let error = error {
                    print("[ERROR] 计算路线失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                    return
                }

                continuation.resume(returning: response?.routes.first)
            }
        }
    }

    // 获取显示文本
    // 🎯 优先显示第三方排序号，如果没有则显示sorted_number
    private func getDisplayText(for point: DeliveryPoint, in points: [DeliveryPoint]) -> String {
        // 如果有第三方排序号，优先显示第三方排序号
        if let thirdPartySort = point.thirdPartySortNumber,
           !thirdPartySort.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return thirdPartySort
        }
        // 否则显示sorted_number
        return "\(point.sorted_number)"
    }

    // 获取点的颜色
    private func getPointColor(_ point: DeliveryPoint) -> Color {
        // 首先检查配送状态
        if point.deliveryStatus == .completed {
            return .green // 已完成配送使用绿色
        } else if point.deliveryStatus == .failed {
            return .red // 配送失败使用红色
        } else if point.isStartPoint {
            return .blue // 起点使用蓝色
        } else if point.isEndPoint {
            return .green // 终点使用绿色
        } else if point.isOptimized {
            return Color(hex: "B36AE2") // 已优化的途经点使用紫色
        } else {
            return .blue // 未优化的途经点使用蓝色
        }
    }

    // 获取点的类型
    private func getPointType(_ point: DeliveryPoint) -> PointType {
        if point.isStartPoint && point.isEndPoint {
            return .startEnd
        } else if point.isStartPoint {
            return .start
        } else if point.isEndPoint {
            return .end
        } else {
            return .waypoint
        }
    }
}
