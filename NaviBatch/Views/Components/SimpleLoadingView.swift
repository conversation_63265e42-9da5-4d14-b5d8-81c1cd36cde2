import SwiftUI

/// 简洁的加载等待视图
/// 用于替换复杂的技术警告，提供友好的用户体验
struct SimpleLoadingView: View {
    let isVisible: Bool
    let message: String
    let subtitle: String?
    let progress: Double?
    let showPercentage: Bool
    let isRateLimited: Bool

    init(
        isVisible: Bool,
        message: String = "importing_addresses_please_wait".localized,
        subtitle: String? = "please_keep_app_open".localized,
        progress: Double? = nil,
        showPercentage: Bool = false,
        isRateLimited: Bool = false
    ) {
        self.isVisible = isVisible
        self.isRateLimited = isRateLimited

        // 智能消息选择
        if isRateLimited {
            self.message = "importing_addresses_please_wait".localized
            self.subtitle = "please_keep_app_open".localized
        } else {
            self.message = message
            self.subtitle = subtitle
        }

        self.progress = progress
        self.showPercentage = showPercentage && !isRateLimited
    }
    
    var body: some View {
        if isVisible {
            VStack(spacing: 16) {
                // 智能加载指示器
                if let progress = progress, !isRateLimited {
                    // 正常状态：显示进度条
                    ProgressView(value: progress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .scaleEffect(1.1)
                } else if let _ = progress, isRateLimited {
                    // 速率限制状态：显示圆形指示器，但保留进度信息在文字中
                    ProgressView()
                        .scaleEffect(1.2)
                        .progressViewStyle(CircularProgressViewStyle(tint: .orange))
                } else {
                    // 无进度状态：显示圆形加载指示器
                    ProgressView()
                        .scaleEffect(1.2)
                        .progressViewStyle(CircularProgressViewStyle(tint: isRateLimited ? .orange : .blue))
                }

                // 主要消息
                Text(message)
                    .font(.body)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                // 智能进度显示
                if let progress = progress {
                    if isRateLimited {
                        // 速率限制时：在副标题中显示进度
                        Text(String(format: "progress_with_rate_limit".localized, progress * 100))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    } else if showPercentage {
                        // 正常时：显示百分比
                        Text(String(format: "%.0f%%", progress * 100))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // 副标题（可选）
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(24)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 4)
            .padding(.horizontal, 32)
        }
    }
}

// MARK: - 预览

#Preview("SimpleLoadingView") {
    VStack {
        Spacer()
        
        SimpleLoadingView(
            isVisible: true,
            message: "正在导入地址，请稍候...",
            subtitle: "请保持应用开启"
        )
        
        Spacer()
    }
    .background(Color(.systemGroupedBackground))
}

#Preview("English") {
    VStack {
        Spacer()
        
        SimpleLoadingView(
            isVisible: true,
            message: "Importing addresses, please wait...",
            subtitle: "Please keep the app open"
        )
        
        Spacer()
    }
    .background(Color(.systemGroupedBackground))
}

#Preview("Processing") {
    VStack {
        Spacer()

        SimpleLoadingView(
            isVisible: true,
            message: "processing_addresses".localized,
            subtitle: nil
        )

        Spacer()
    }
    .background(Color(.systemGroupedBackground))
}

#Preview("With Progress") {
    VStack {
        Spacer()

        SimpleLoadingView(
            isVisible: true,
            message: "validating_addresses".localized,
            subtitle: nil,
            progress: 0.65,
            showPercentage: true
        )

        Spacer()
    }
    .background(Color(.systemGroupedBackground))
}

#Preview("Rate Limited with Progress") {
    VStack {
        Spacer()

        SimpleLoadingView(
            isVisible: true,
            message: "validating_addresses".localized,
            subtitle: nil,
            progress: 0.35,
            showPercentage: true,
            isRateLimited: true
        )

        Spacer()
    }
    .background(Color(.systemGroupedBackground))
}
