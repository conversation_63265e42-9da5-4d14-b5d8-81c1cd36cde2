import SwiftUI
import MapKit

/// 地图标记点击后显示的信息卡片视图
struct MapMarkerCalloutView: View {
    let point: DeliveryPoint
    let isSubscriber: Bool

    @State private var showLookAround: Bool = false
    @State private var lookAroundScene: MKLookAroundScene?
    @State private var isLoadingScene: Bool = false
    @State private var lookAroundOpacity: Double = 0 // 控制实景图透明度的动画状态
    @State private var currentCoordinateIndex: Int = 0 // 当前尝试的坐标索引
    @State private var coordinateVariations: [(latitude: Double, longitude: Double, description: String)] = []

    var body: some View {
        VStack(spacing: 0) {
            // 地址信息部分
            VStack(alignment: .leading, spacing: 8) {
                // 排序编号和地址标题 - 确保左对齐
                HStack(spacing: 8) {
                    // 排序编号 - Dark Mode 优化，使用自适应颜色
                    Text(getDisplayText())
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(width: 36, height: 36)
                        .background(adaptiveNumberBackground)
                        .cornerRadius(6)
                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)

                    // 地址标题 - 根据是否为第三方地址采用不同显示方式
                    VStack(alignment: .leading, spacing: 2) {
                        if point.sourceApp != .manual {
                            // 第三方地址：显示完整地址并支持自动分行（类似bottom sheet）
                            let addressParts = getFormattedAddressParts(from: point.primaryAddress)

                            // 第三方排序标签 - 显示在地址前方 - Dark Mode 优化
                            if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                                Text(getThirdPartySortLabel())
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(adaptiveAppTagBackground)
                                    .cornerRadius(6)
                                    .padding(.bottom, 2)
                            }

                            // 主要地址文本 - 支持自动分行 - Dark Mode 优化
                            Text(addressParts.main)
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.adaptivePrimaryText)
                                .lineLimit(nil)
                                .multilineTextAlignment(.leading)
                                .frame(maxWidth: .infinity, alignment: .leading)

                            // 副标题 (地址第二行) - 如果有 - Dark Mode 优化
                            if let subtitle = addressParts.subtitle, !subtitle.isEmpty {
                                Text(subtitle)
                                    .font(.subheadline)
                                    .foregroundColor(.adaptiveSecondaryText)
                                    .lineLimit(1)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                        } else {
                            // 手动输入地址：使用原有的智能分割方式
                            let addressParts = getSmartAddressParts()

                            // 第一行 - 主要地址部分 - Dark Mode 优化
                            Text(addressParts.firstLine)
                                .font(.headline)
                                .foregroundColor(.adaptivePrimaryText)
                                .lineLimit(1)

                            // 第二行 - 剩余地址部分（如果有）- Dark Mode 优化
                            if !addressParts.secondLine.isEmpty {
                                Text(addressParts.secondLine)
                                    .font(.subheadline)
                                    .foregroundColor(.adaptiveSecondaryText)
                                    .lineLimit(1)
                            }
                        }

                        // 如果同时是起点和终点，显示特殊标签
                        if point.isStartPoint && point.isEndPoint {
                            HStack(spacing: 4) {
                                Image(systemName: "arrow.triangle.2.circlepath")
                                    .font(.caption)
                                Text("起点/终点")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.orange)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(4)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 地址详情 - 只对手动输入的地址显示 - Dark Mode 优化
                if point.sourceApp == .manual, let addressDetails = getCleanedAddressDetails() {
                    Text(addressDetails)
                        .font(.subheadline)
                        .foregroundColor(.adaptiveSecondaryText)
                        .lineLimit(2)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // 包裹信息已移除

                // 分组信息（如果有）
                if point.isAssignedToGroup, let groupNumber = point.assignedGroupNumber {
                    HStack {
                        Image(systemName: "rectangle.stack.fill")
                            .foregroundColor(.black)
                        Text("group_number_format".localized(with: groupNumber))
                            .font(.subheadline)
                            .foregroundColor(.black)
                    }
                }


            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)

            // 实景图区域 - 移到外层以去除水平padding
            if true { // 移除订阅限制，所有用户都可以查看实景图
                VStack(spacing: 0) {
                    if showLookAround {
                        if isLoadingScene {
                            // 简单的加载状态指示器
                            VStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(1.2)
                                Text("加载中...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.top, 8)
                            }
                            .frame(height: 200)
                            .frame(maxWidth: .infinity)
                            .background(Color.gray.opacity(0.05))
                        } else if let scene = lookAroundScene {
                            // 实景视图 - 使用兼容的组件，去除圆角以与按钮融合
                            if #available(iOS 16.0, *) {
                                // 使用新的 LookAroundPreviewView 组件 (iOS 16+)
                                LookAroundPreviewView(scene: scene)
                                    .frame(height: 200)
                                    .onAppear {
                                        print("[DEBUG] MapMarkerCalloutView - LookAroundPreviewView出现，地址: \(point.primaryAddress)")
                                    }
                                    .onDisappear {
                                        print("[DEBUG] MapMarkerCalloutView - LookAroundPreviewView消失，地址: \(point.primaryAddress)")
                                    }
                            } else {
                                // 在不支持 LookAroundPreviewView 的系统版本上显示替代视图
                                VStack {
                                    Image(systemName: "binoculars")
                                        .font(.system(size: 40))
                                        .foregroundColor(.gray)
                                    Text("look_around_ios_16_required".localized)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .multilineTextAlignment(.center)
                                        .padding(.top, 8)
                                }
                                .frame(height: 200)
                                .frame(maxWidth: .infinity)
                                .background(Color.gray.opacity(0.1))
                            }
                        } else {
                            // 无实景图数据时的增强提示
                            VStack(spacing: 12) {
                                Image(systemName: "binoculars.slash")
                                    .font(.system(size: 36))
                                    .foregroundColor(.orange)

                                VStack(spacing: 6) {
                                    Text("该位置无实景图数据")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.primary)

                                    Text("地址坐标正确，可正常导航")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    Text("Apple Maps在此位置暂无街景数据")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                        .multilineTextAlignment(.center)
                                }

                                // 添加操作按钮
                                HStack(spacing: 12) {
                                    // 导航按钮
                                    Button(action: {
                                        openInMaps()
                                    }) {
                                        HStack(spacing: 4) {
                                            Image(systemName: "location.fill")
                                                .font(.caption)
                                            Text("导航")
                                                .font(.caption)
                                                .fontWeight(.medium)
                                        }
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.black)
                                        .cornerRadius(12)
                                    }

                                    // 尝试附近位置按钮
                                    Button(action: {
                                        tryNearbyLocation()
                                    }) {
                                        HStack(spacing: 4) {
                                            Image(systemName: "location.magnifyingglass")
                                                .font(.caption)
                                            Text("尝试附近")
                                                .font(.caption)
                                                .fontWeight(.medium)
                                        }
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.gray)
                                        .cornerRadius(12)
                                    }
                                }
                            }
                            .frame(height: 200)
                            .frame(maxWidth: .infinity)
                            .background(Color.gray.opacity(0.05))
                        }
                    }
                }
                .frame(height: showLookAround ? 200 : 0, alignment: .top)
                .clipped()
                .animation(.easeInOut(duration: 0.3), value: showLookAround)
            }

            // 底部按钮区域 - Dark Mode 优化
            HStack(spacing: 0) {
                // 导航按钮 - 使用主题色
                Button(action: {
                    openInMaps()
                }) {
                    HStack {
                        Image(systemName: "location.fill")
                        Text("navigate".localized)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.black)
                    .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)

                // 查看实景图按钮 - Dark Mode 优化
                Button(action: {
                    print("[DEBUG] MapMarkerCalloutView - Look Around按钮被点击，当前状态: showLookAround=\(showLookAround)")

                    withAnimation(.easeInOut(duration: 0.3)) {
                        if showLookAround {
                            print("[DEBUG] MapMarkerCalloutView - 隐藏实景图")
                            showLookAround = false
                            // 通知RouteView实景图已关闭
                            NotificationCenter.default.post(name: Notification.Name("LookAroundDeactivated"), object: nil)
                        } else {
                            print("[DEBUG] MapMarkerCalloutView - 显示实景图")
                            showLookAround = true
                            // 通知RouteView实景图已激活
                            NotificationCenter.default.post(name: Notification.Name("LookAroundActivated"), object: nil)

                            if lookAroundScene == nil {
                                print("[DEBUG] MapMarkerCalloutView - 开始加载实景图场景")
                                loadLookAroundScene()
                            } else {
                                print("[DEBUG] MapMarkerCalloutView - 实景图场景已存在，直接显示")
                            }
                        }
                    }
                }) {
                    HStack {
                        Image(systemName: showLookAround ? "eye.slash.fill" : "binoculars.fill")
                        Text(showLookAround ? "hide_look_around".localized : "show_look_around".localized)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(showLookAround ? Color.gray : Color.black)
                    .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
            }
        }
        .background(Color.adaptiveCardBackground)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
        .onAppear {
            print("[DEBUG] MapMarkerCalloutView - 卡片出现，地址: \(point.primaryAddress)")
            // 初始化坐标变化
            coordinateVariations = generateCoordinateVariations()
        }
        .onDisappear {
            print("[DEBUG] MapMarkerCalloutView - 卡片消失，地址: \(point.primaryAddress)")
            // 如果实景图正在显示，通知RouteView实景图已关闭
            if showLookAround {
                NotificationCenter.default.post(name: Notification.Name("LookAroundDeactivated"), object: nil)
            }
        }
    }

    // 获取显示文本 - 🎯 优先显示第三方号码，与地图标记保持一致
    private func getDisplayText() -> String {
        return "\(point.displayNumber)"
    }

    // MARK: - Dark Mode 自适应颜色

    /// 编号背景颜色 - 统一使用主题色
    private var adaptiveNumberBackground: Color {
        if point.deliveryStatus == .completed {
            return Color.adaptiveSuccess
        } else if point.deliveryStatus == .failed {
            return Color.adaptiveError
        } else if point.hasAnyValidationIssues {
            return Color.adaptiveWarning
        } else {
            // 统一使用黑色主题色
            return Color.black
        }
    }

    /// 应用标签背景颜色 - 统一使用主题色
    private var adaptiveAppTagBackground: Color {
        // 统一使用黑色主题色，保持专业外观
        return Color.black
    }

    // 加载实景图 - 简化版本，避免过度动画
    private func loadLookAroundScene() {
        // 如果已经有场景数据，直接返回
        if lookAroundScene != nil {
            print("[DEBUG] MapMarkerCalloutView - 实景图场景已存在，跳过加载")
            return
        }

        print("[DEBUG] MapMarkerCalloutView - 开始加载实景图，坐标: \(point.coordinate)")

        // 简单设置加载状态
        isLoadingScene = true

        // 创建请求
        let lookAroundRequest = MKLookAroundSceneRequest(coordinate: point.coordinate)

        // 执行请求
        lookAroundRequest.getSceneWithCompletionHandler { scene, error in
            print("[DEBUG] MapMarkerCalloutView - 实景图请求完成")

            // 确保在主线程更新UI
            DispatchQueue.main.async {
                // 立即关闭加载状态
                self.isLoadingScene = false

                // 处理结果
                if let scene = scene {
                    // 成功获取场景
                    print("[DEBUG] MapMarkerCalloutView - 实景图加载成功，设置场景")
                    print("[DEBUG] MapMarkerCalloutView - 场景详情: \(scene)")
                    print("[DEBUG] MapMarkerCalloutView - 地址: \(self.point.primaryAddress)")
                    print("[DEBUG] MapMarkerCalloutView - 坐标: \(self.point.coordinate)")

                    self.lookAroundScene = scene

                    // 延迟检查是否还存在
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        print("[DEBUG] MapMarkerCalloutView - 1秒后检查: showLookAround=\(self.showLookAround), scene存在=\(self.lookAroundScene != nil)")
                    }
                } else {
                    // 加载失败或无数据时，保持showLookAround为true，显示错误信息
                    if let error = error {
                        print("[DEBUG] MapMarkerCalloutView - 实景图加载失败: \(error.localizedDescription)")
                    } else {
                        print("[DEBUG] MapMarkerCalloutView - 该位置无实景图数据")
                    }
                    // 不重置showLookAround状态，让用户看到"无实景图数据"的提示
                    print("[DEBUG] MapMarkerCalloutView - 保持showLookAround=true以显示错误提示")
                }
            }
        }
    }

    // 智能分割地址为两行显示
    private func getSmartAddressParts() -> (firstLine: String, secondLine: String) {
        let fullAddress = point.primaryAddress.components(separatedBy: "|").first?.trimmingCharacters(in: .whitespaces) ?? point.primaryAddress
        let components = fullAddress.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

        guard components.count > 1 else {
            // 如果只有一个组件，检查长度决定是否分割
            let singleAddress = components.first ?? ""
            if singleAddress.count > 25 {
                // 尝试在空格处分割长地址
                let words = singleAddress.components(separatedBy: " ")
                let midPoint = words.count / 2
                let firstPart = words.prefix(midPoint).joined(separator: " ")
                let secondPart = words.suffix(from: midPoint).joined(separator: " ")
                return (firstPart, secondPart)
            } else {
                return (singleAddress, "")
            }
        }

        // 多个组件的情况，智能分配到两行
        if components.count == 2 {
            return (components[0], components[1])
        } else if components.count >= 3 {
            // 第一行：街道地址
            let firstLine = components[0]
            // 第二行：剩余部分
            let secondLine = components.dropFirst().joined(separator: ", ")
            return (firstLine, secondLine)
        }

        return (components.first ?? "", "")
    }

    // 获取清理后的地址详情
    private func getCleanedAddressDetails() -> String? {
        let addressComponents = point.primaryAddress.components(separatedBy: ",")
        guard addressComponents.count > 1 else { return nil }

        let addressDetails = addressComponents.dropFirst().joined(separator: ",").trimmingCharacters(in: .whitespaces)

        // 移除末尾的 | 符号和其后的内容
        let cleanedDetails = addressDetails.components(separatedBy: "|").first?.trimmingCharacters(in: .whitespaces)

        return cleanedDetails?.isEmpty == false ? cleanedDetails : nil
    }

    // 🎯 地址格式化：分割显示主标题和副标题（与bottom sheet保持一致）
    private func getFormattedAddressParts(from fullAddress: String) -> (main: String, subtitle: String?) {
        let trimmedAddress = fullAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果地址包含逗号，使用逗号分隔
        if trimmedAddress.contains(",") {
            let components = trimmedAddress.components(separatedBy: ",")
            if components.count > 1 {
                // 第一部分作为主要地址
                let mainPart = components[0].trimmingCharacters(in: .whitespacesAndNewlines)

                // 剩余部分作为副标题（地区、邮编和国家）
                let subtitlePart = components[1...].joined(separator: ", ").trimmingCharacters(in: .whitespacesAndNewlines)

                // 🧹 清理副标题：移除管道符号和国家信息
                let cleanedSubtitle = cleanAddressForBottomSheetDisplay(subtitlePart)

                return (mainPart, cleanedSubtitle.isEmpty ? nil : cleanedSubtitle)
            }
        }

        // 如果没有找到合适的分隔符，返回整个地址作为主要部分
        return (trimmedAddress, nil)
    }

    // 🧹 清理地址用于底部表单显示 - 移除管道符号和国家信息（与bottom sheet保持一致）
    private func cleanAddressForBottomSheetDisplay(_ address: String) -> String {
        var cleanedAddress = address

        // 1. 移除所有管道符号（|）后的元数据信息
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )

        // 2. 移除常见国家名称（高密度配送区域不需要显示国家）
        let commonCountries = [
            "United States", "USA", "US",
            "Australia", "AU", "AUS",
            "Canada", "CA", "CAN",
            "United Kingdom", "UK", "GB",
            "Hong Kong", "HK",
            "China", "CN", "CHN",
            "Taiwan", "TW", "TWN"
        ]

        for country in commonCountries {
            // 移除国家名称（考虑前后可能有逗号和空格）
            cleanedAddress = cleanedAddress.replacingOccurrences(
                of: ",\\s*\(country)\\s*$",
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
            cleanedAddress = cleanedAddress.replacingOccurrences(
                of: "^\\s*\(country)\\s*,",
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
            cleanedAddress = cleanedAddress.replacingOccurrences(
                of: "\\s+\(country)\\s*$",
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 3. 清理多余的逗号和空格
        cleanedAddress = cleanedAddress.replacingOccurrences(of: ",,+", with: ",")
        cleanedAddress = cleanedAddress.replacingOccurrences(of: "^,\\s*", with: "")
        cleanedAddress = cleanedAddress.replacingOccurrences(of: "\\s*,$", with: "")
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleanedAddress
    }

    // 在地图中打开导航
    private func openInMaps() {
        // 使用NavigationAppHandler打开导航，确保遵循用户选择的导航应用偏好
        print("[DEBUG] MapMarkerCalloutView - 打开导航到: \(point.primaryAddress)")
        NavigationAppHandler.shared.openNavigation(to: point.coordinate, name: point.primaryAddress)
    }

    // 获取第三方排序标签 - 与底部列表保持一致的格式
    private func getThirdPartySortLabel() -> String {
        guard let sortNumber = point.thirdPartySortNumber else { return "" }

        let appName = point.sourceApp.displayName

        // 使用与底部列表相同的本地化格式："%@: %@"
        return "third_party_sort_label".localized(with: appName, sortNumber)
    }

    // 生成坐标变化用于寻找实景图
    private func generateCoordinateVariations() -> [(latitude: Double, longitude: Double, description: String)] {
        let originalLat = point.coordinate.latitude
        let originalLng = point.coordinate.longitude

        // 计算度数偏移（大约1米 ≈ 0.00001度）
        let meterToDegree = 0.00001
        let offset = 20.0 * meterToDegree // 20米偏移

        return [
            (originalLat, originalLng, "原始位置"),
            (originalLat + offset, originalLng, "向北20米"),
            (originalLat - offset, originalLng, "向南20米"),
            (originalLat, originalLng + offset, "向东20米"),
            (originalLat, originalLng - offset, "向西20米"),
            (originalLat + offset/2, originalLng + offset/2, "向东北10米"),
            (originalLat - offset/2, originalLng + offset/2, "向东南10米"),
            (originalLat + offset/2, originalLng - offset/2, "向西北10米"),
            (originalLat - offset/2, originalLng - offset/2, "向西南10米"),
        ]
    }

    // 尝试附近位置的实景图
    private func tryNearbyLocation() {
        guard currentCoordinateIndex < coordinateVariations.count - 1 else {
            // 已经尝试了所有变化，重置到开始
            currentCoordinateIndex = 0
            print("[DEBUG] MapMarkerCalloutView - 已尝试所有附近位置，重置到原始位置")
            return
        }

        currentCoordinateIndex += 1
        let variation = coordinateVariations[currentCoordinateIndex]

        print("[DEBUG] MapMarkerCalloutView - 尝试附近位置: \(variation.description)")
        print("[DEBUG] MapMarkerCalloutView - 新坐标: (\(variation.latitude), \(variation.longitude))")

        // 重置状态
        lookAroundScene = nil
        isLoadingScene = true

        // 创建新的请求
        let coordinate = CLLocationCoordinate2D(latitude: variation.latitude, longitude: variation.longitude)
        let lookAroundRequest = MKLookAroundSceneRequest(coordinate: coordinate)

        // 执行请求
        lookAroundRequest.getSceneWithCompletionHandler { scene, error in
            DispatchQueue.main.async {
                self.isLoadingScene = false

                if let scene = scene {
                    print("[DEBUG] MapMarkerCalloutView - 附近位置实景图加载成功: \(variation.description)")
                    self.lookAroundScene = scene
                } else {
                    print("[DEBUG] MapMarkerCalloutView - 附近位置也无实景图: \(variation.description)")
                    // 如果这个位置也没有，可以继续尝试下一个
                    if self.currentCoordinateIndex < self.coordinateVariations.count - 1 {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            self.tryNearbyLocation()
                        }
                    }
                }
            }
        }
    }
}

#Preview("MapMarkerCalloutView") {
    let point = DeliveryPoint(
        sort_number: 1,
        streetName: "1 Kerferd Road, Glen Waverley, VIC 3150, Australia",
        coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642)
    )
    let _ = {
        point.packageCount = 3
        point.assignedGroupNumber = 2
    }()

    return MapMarkerCalloutView(point: point, isSubscriber: true)
        .frame(width: 300)
        .padding()
}
