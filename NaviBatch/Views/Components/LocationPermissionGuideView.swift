import SwiftUI
import CoreLocation

/// 位置权限引导界面
/// 帮助用户理解和设置位置权限
struct LocationPermissionGuideView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationManager = LocationManager.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部图标和标题
                    VStack(spacing: 16) {
                        Image(systemName: "location.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        Text("位置权限设置")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("NaviBatch是一个路线规划导航应用，需要您的位置信息来提供准确的导航服务")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    
                    // 当前状态卡片
                    VStack(alignment: .leading, spacing: 12) {
                        Text("当前状态")
                            .font(.headline)
                        
                        StatusRow(
                            icon: "location.circle",
                            title: "位置权限",
                            value: authorizationStatusText,
                            color: authorizationStatusColor
                        )
                        
                        StatusRow(
                            icon: "dot.radiowaves.left.and.right",
                            title: "位置来源",
                            value: locationManager.diagnoseLocationSource(),
                            color: locationSourceColor
                        )
                        
                        if let location = locationManager.userLocation {
                            StatusRow(
                                icon: "mappin.and.ellipse",
                                title: "当前位置",
                                value: String(format: "%.4f, %.4f", location.latitude, location.longitude),
                                color: .primary
                            )
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .padding(.horizontal)
                    
                    // 功能说明
                    VStack(alignment: .leading, spacing: 16) {
                        Text("为什么需要位置权限？")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        VStack(spacing: 12) {
                            LocationFeatureRow(
                                icon: "map",
                                title: "精确路线规划",
                                description: "基于您的实际位置计算最优配送路线"
                            )

                            LocationFeatureRow(
                                icon: "location.north",
                                title: "实时导航",
                                description: "提供从您当前位置到配送点的导航指引"
                            )

                            LocationFeatureRow(
                                icon: "clock",
                                title: "距离计算",
                                description: "显示配送点与您的距离，优化配送顺序"
                            )
                        }
                        .padding(.horizontal)
                    }
                    
                    // 操作按钮
                    VStack(spacing: 12) {
                        if locationManager.authorizationStatus == .notDetermined {
                            Button("授权位置权限") {
                                locationManager.requestLocationPermission()
                            }
                            .buttonStyle(.borderedProminent)
                            .controlSize(.large)
                        } else if locationManager.authorizationStatus == .denied || locationManager.authorizationStatus == .restricted {
                            VStack(spacing: 8) {
                                Text("位置权限已被拒绝")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                Button("打开系统设置") {
                                    openSystemSettings()
                                }
                                .buttonStyle(.borderedProminent)
                                .controlSize(.large)
                                
                                Text("在设置中找到NaviBatch，开启位置权限")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                        } else {
                            VStack(spacing: 8) {
                                if locationManager.diagnoseLocationSource().contains("默认") {
                                    Button("重新获取GPS位置") {
                                        locationManager.forceRefreshRealLocation()
                                        dismiss()
                                    }
                                    .buttonStyle(.borderedProminent)
                                    .controlSize(.large)
                                    
                                    Text("当前使用默认位置，点击重新获取真实GPS位置")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .multilineTextAlignment(.center)
                                } else {
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                        Text("位置权限已正确设置")
                                            .foregroundColor(.green)
                                    }
                                    .font(.subheadline)
                                }
                            }
                        }
                        
                        Button("稍后设置") {
                            dismiss()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.large)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 20)
                }
            }
            .navigationTitle("位置设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var authorizationStatusText: String {
        switch locationManager.authorizationStatus {
        case .notDetermined: return "未确定"
        case .denied: return "已拒绝"
        case .restricted: return "受限制"
        case .authorizedWhenInUse: return "使用时允许"
        case .authorizedAlways: return "始终允许"
        @unknown default: return "未知"
        }
    }
    
    private var authorizationStatusColor: Color {
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways: return .green
        case .denied, .restricted: return .red
        case .notDetermined: return .orange
        @unknown default: return .gray
        }
    }
    
    private var locationSourceColor: Color {
        let source = locationManager.diagnoseLocationSource()
        if source.contains("GPS") {
            return .green
        } else if source.contains("默认") {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - 方法
    
    private func openSystemSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

// MARK: - 辅助视图

struct StatusRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .foregroundColor(color)
                .fontWeight(.medium)
        }
    }
}

struct LocationFeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)
                .font(.system(size: 16))

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

#Preview("LocationPermissionGuideView") {
    LocationPermissionGuideView()
}
