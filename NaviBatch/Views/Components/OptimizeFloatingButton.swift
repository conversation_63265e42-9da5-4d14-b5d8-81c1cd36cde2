import SwiftUI

struct OptimizeFloatingButton: View {
    var action: () -> Void
    var isDisabled: Bool = false
    var hasInvalidAddresses: Bool = false
    var hasThirdPartyWithSort: Bool = false
    var isRouteOptimized: Bool = false

    var body: some View {
        Button(action: {
            if !isDisabled {
                action()
            }
        }) {
            ZStack {
                Circle()
                    .fill(buttonColor)
                    .frame(width: 56, height: 56)
                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)

                Image(systemName: hasInvalidAddresses ? "exclamationmark.triangle" : "arrow.triangle.2.circlepath")
                    .font(.system(size: 22, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .transition(.scale.combined(with: .opacity))
        .opacity(isDisabled ? 0.6 : 1.0)
    }

    // 根据状态返回不同的按钮颜色
    private var buttonColor: Color {
        if isDisabled {
            return Color.gray
        } else if hasInvalidAddresses {
            return Color.orange
        } else if isRouteOptimized {
            // 已优化路线使用紫色
            return Color(hex: "B36AE2")
        } else {
            // 未优化路线使用蓝色
            return Color.blue
        }
    }
}

#Preview("OptimizeFloatingButton") {
    ZStack {
        Color.gray.opacity(0.2).ignoresSafeArea()

        VStack {
            Spacer()
            HStack {
                Spacer()
                OptimizeFloatingButton(
                    action: {
                        print("优化按钮点击")
                    },
                    isDisabled: false,
                    hasInvalidAddresses: false,
                    hasThirdPartyWithSort: false,
                    isRouteOptimized: false
                )
                .padding(.trailing, 16)
                .padding(.bottom, 30)
            }
        }
    }
}
