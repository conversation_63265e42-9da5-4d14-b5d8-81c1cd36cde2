import SwiftUI
import SwiftData
import CoreLocation

/// 批量地址修正表单
/// 用于批量修正问题地址
struct BatchAddressFixSheet: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    let problematicPoints: [DeliveryPoint]
    @State private var isProcessing = false
    @State private var progress: Double = 0
    @State private var processedCount = 0
    @State private var successCount = 0
    @State private var failedCount = 0
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 获取用户位置
    @StateObject private var locationManager = LocationManager.shared

    var body: some View {
        NavigationView {
            VStack {
                if isProcessing {
                    // 处理中显示进度
                    SimpleLoadingView(
                        isVisible: true,
                        message: String(format: "fixing_addresses_progress".localized, processedCount, problematicPoints.count),
                        subtitle: "成功: \(successCount), 失败: \(failedCount)",
                        progress: progress,
                        showPercentage: true
                    )
                } else if processedCount > 0 && processedCount == problematicPoints.count {
                    // 处理完成显示结果
                    VStack(spacing: 20) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.green)
                            .padding()

                        Text("address_fix_complete".localized)
                            .font(.headline)

                        Text(String(format: "address_fix_result".localized, successCount, failedCount))
                            .multilineTextAlignment(.center)

                        Button("close".localized) {
                            dismiss()
                        }
                        .buttonStyle(.borderedProminent)
                        .padding(.top, 20)
                    }
                    .padding()
                } else {
                    // 显示问题地址列表
                    List {
                        Section(header: Text("问题地址")) {
                            ForEach(problematicPoints) { point in
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(point.primaryAddress)
                                        .font(.headline)

                                    if let warning = point.geocodingWarning, !warning.isEmpty {
                                        HStack {
                                            Image(systemName: "exclamationmark.triangle")
                                                .foregroundColor(.orange)
                                            Text(warning)
                                                .font(.caption)
                                                .foregroundColor(.orange)
                                        }
                                    }

                                    let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
                                    if validationStatus != .valid {
                                        HStack {
                                            Image(systemName: validationStatus.iconName)
                                                .foregroundColor(validationStatus.color)
                                            Text(validationStatus.localizedName)
                                                .font(.caption)
                                                .foregroundColor(validationStatus.color)
                                        }
                                    }
                                }
                                .padding(.vertical, 4)
                            }
                        }

                        Section {
                            Button("开始批量修正") {
                                startBatchFix()
                            }
                            .frame(maxWidth: .infinity, alignment: .center)
                            .disabled(isProcessing)
                        }
                    }
                }
            }
            .navigationTitle("批量修正地址")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("提示"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
    }

    // 开始批量修正
    private func startBatchFix() {
        isProcessing = true
        progress = 0
        processedCount = 0
        successCount = 0
        failedCount = 0

        Task {
            for (index, point) in problematicPoints.enumerated() {
                // 更新进度
                await MainActor.run {
                    progress = Double(index) / Double(problematicPoints.count)
                    processedCount = index
                }

                // 尝试修正地址
                var success = false

                // 使用GeocodingService重新获取坐标
                await withCheckedContinuation { continuation in
                    DeliveryPointManager.shared.updateDeliveryPointCoordinates(
                        deliveryPoint: point,
                        modelContext: modelContext
                    ) { result in
                        success = result

                        // 更新基于用户位置的验证
                        if let userLocation = locationManager.userLocation {
                            point.validateLocationBasedOnUserPosition(userLocation)
                        }

                        continuation.resume()
                    }
                }

                // 更新计数
                await MainActor.run {
                    if success {
                        successCount += 1
                    } else {
                        failedCount += 1
                    }
                }

                // 添加延迟以避免API限制
                try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
            }

            // 完成处理
            await MainActor.run {
                isProcessing = false
                progress = 1.0
                processedCount = problematicPoints.count

                // 保存更改
                do {
                    try modelContext.save()

                    // 通知RouteViewModel检查优化按钮可用性
                    RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()
                } catch {
                    alertMessage = "保存失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
}
