import SwiftUI

/// 配送状态显示组件
struct DeliveryStatusView: View {
    let status: DeliveryStatus
    var showText: Bool = true
    var compact: Bool = false
    
    var body: some View {
        HStack(spacing: compact ? 4 : 8) {
            // 状态图标
            Image(systemName: status.iconName)
                .font(.system(size: compact ? 12 : 14, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: compact ? 20 : 24, height: compact ? 20 : 24)
                .background(status.color)
                .clipShape(Circle())
            
            // 状态文本（可选）
            if showText {
                Text(status.localizedName)
                    .font(.system(size: compact ? 12 : 14, weight: .medium))
                    .foregroundColor(status.color)
            }
        }
        .padding(.vertical, compact ? 2 : 4)
        .padding(.horizontal, compact ? 6 : 8)
        .background(status.color.opacity(0.1))
        .cornerRadius(compact ? 12 : 16)
    }
}

/// 可交互的配送状态选择器
struct DeliveryStatusSelector: View {
    @Binding var selectedStatus: DeliveryStatus
    var onStatusChanged: ((DeliveryStatus) -> Void)? = nil
    
    var body: some View {
        Menu {
            ForEach([DeliveryStatus.pending, .inProgress, .completed, .failed], id: \.self) { status in
                Button(action: {
                    selectedStatus = status
                    onStatusChanged?(status)
                }) {
                    Label(status.localizedName, systemImage: status.iconName)
                }
            }
        } label: {
            DeliveryStatusView(status: selectedStatus)
                .padding(.vertical, 4)
        }
    }
}

#Preview("DeliveryStatusView") {
    VStack(spacing: 20) {
        Text("状态显示")
            .font(.headline)
        
        HStack(spacing: 12) {
            DeliveryStatusView(status: .pending)
            DeliveryStatusView(status: .inProgress)
            DeliveryStatusView(status: .completed)
            DeliveryStatusView(status: .failed)
        }
        
        Text("紧凑模式")
            .font(.headline)
        
        HStack(spacing: 12) {
            DeliveryStatusView(status: .pending, compact: true)
            DeliveryStatusView(status: .inProgress, compact: true)
            DeliveryStatusView(status: .completed, compact: true)
            DeliveryStatusView(status: .failed, compact: true)
        }
        
        Text("仅图标")
            .font(.headline)
        
        HStack(spacing: 12) {
            DeliveryStatusView(status: .pending, showText: false)
            DeliveryStatusView(status: .inProgress, showText: false)
            DeliveryStatusView(status: .completed, showText: false)
            DeliveryStatusView(status: .failed, showText: false)
        }
        
        Divider()
        
        Text("状态选择器")
            .font(.headline)
        
        StateWrapper()
    }
    .padding()
}

// 用于预览的状态包装器
private struct StateWrapper: View {
    @State private var status: DeliveryStatus = .pending
    
    var body: some View {
        VStack(spacing: 10) {
            DeliveryStatusSelector(selectedStatus: $status) { newStatus in
                print("状态已更改为: \(newStatus.localizedName)")
            }
            
            Text("当前选择: \(status.localizedName)")
                .font(.subheadline)
        }
    }
}
