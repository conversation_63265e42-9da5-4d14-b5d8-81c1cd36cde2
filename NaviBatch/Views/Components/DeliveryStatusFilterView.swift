import SwiftUI
// 导入共享组件

/// 配送状态筛选组件
struct DeliveryStatusFilterView: View {
    @Binding var selectedStatuses: Set<DeliveryStatus>
    let allStatuses: [DeliveryStatus] = [.pending, .inProgress, .completed, .failed]

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                // 全部选项
                FilterChip(
                    title: "all".localized,
                    color: .gray,
                    isSelected: selectedStatuses.count == allStatuses.count || selectedStatuses.isEmpty
                ) {
                    // 如果已经全选，则清空（显示全部）；如果未全选，则全选
                    if selectedStatuses.count == allStatuses.count {
                        selectedStatuses.removeAll()
                    } else {
                        selectedStatuses = Set(allStatuses)
                    }
                }

                // 各状态选项
                ForEach(allStatuses, id: \.self) { status in
                    FilterChip(
                        title: status.localizedName,
                        color: status.color,
                        isSelected: selectedStatuses.contains(status)
                    ) {
                        toggleStatus(status)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
        .background(Color(.systemBackground))
    }

    private func toggleStatus(_ status: DeliveryStatus) {
        if selectedStatuses.contains(status) {
            selectedStatuses.remove(status)
        } else {
            selectedStatuses.insert(status)
        }
    }
}

// 使用共享组件中的 FilterChip

private struct DeliveryStatusFilterPreviewWrapper: View {
    @State private var selectedStatuses: Set<DeliveryStatus> = [.pending, .inProgress]

    var body: some View {
        VStack(spacing: 20) {
            DeliveryStatusFilterView(selectedStatuses: $selectedStatuses)

            Text("selected_statuses".localized + ": \(selectedStatuses.map { $0.localizedName }.joined(separator: ", "))")
                .font(.caption)
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}

#Preview("DeliveryStatusFilterView") {
    DeliveryStatusFilterPreviewWrapper()
}
