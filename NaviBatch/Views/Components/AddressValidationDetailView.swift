import SwiftUI
import CoreLocation
import SwiftData

/// 在bottom sheet中显示地址验证详情的视图
struct AddressValidationDetailView: View {
    let deliveryPoint: DeliveryPoint

    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 验证状态概览
            AddressValidationSummaryView(deliveryPoint: deliveryPoint)

            // 如果有验证问题，显示详细信息
            if deliveryPoint.hasValidationIssues {
                DisclosureGroup(
                    isExpanded: $isExpanded,
                    content: {
                        AddressValidationDetailsView(deliveryPoint: deliveryPoint)
                    },
                    label: {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text("地址验证详情")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                )
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
    }
}

/// 地址验证状态概览
struct AddressValidationSummaryView: View {
    let deliveryPoint: DeliveryPoint

    var body: some View {
        HStack(spacing: 12) {
            // 总体验证分数
            VStack(spacing: 4) {
                Text("\(Int(deliveryPoint.addressValidationScore))")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(scoreColor)

                Text("验证分数")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(width: 60)

            Divider()

            // 各项验证状态
            VStack(alignment: .leading, spacing: 6) {
                ValidationStatusRow(
                    title: "门牌号",
                    status: AddressValidationStatus(rawValue: deliveryPoint.streetNumberValidation) ?? .unknown
                )

                ValidationStatusRow(
                    title: "街道名",
                    status: AddressValidationStatus(rawValue: deliveryPoint.streetNameValidation) ?? .unknown
                )

                ValidationStatusRow(
                    title: "邮编",
                    status: AddressValidationStatus(rawValue: deliveryPoint.postalCodeValidation) ?? .unknown
                )

                ValidationStatusRow(
                    title: "国家",
                    status: AddressValidationStatus(rawValue: deliveryPoint.countryValidation) ?? .unknown
                )
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(scoreColor.opacity(0.3), lineWidth: 1)
        )
    }

    private var scoreColor: Color {
        if deliveryPoint.addressValidationScore >= 80 {
            return .green
        } else if deliveryPoint.addressValidationScore >= 60 {
            return .orange
        } else {
            return .red
        }
    }
}

/// 单个验证状态行
struct ValidationStatusRow: View {
    let title: String
    let status: AddressValidationStatus

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: status.iconName)
                .foregroundColor(status.color)
                .font(.caption)
                .frame(width: 16)

            Text(title)
                .font(.caption)
                .foregroundColor(.primary)

            Spacer()

            Text(status.localizedName)
                .font(.caption)
                .foregroundColor(status.color)
        }
    }
}

/// 详细验证信息
struct AddressValidationDetailsView: View {
    let deliveryPoint: DeliveryPoint

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 原始地址 vs 反向地理编码地址
            if let reverseAddress = deliveryPoint.reverseGeocodedAddress {
                VStack(alignment: .leading, spacing: 8) {
                    Text("地址对比")
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("原始地址:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(deliveryPoint.primaryAddress)
                            .font(.caption)
                            .padding(8)
                            .background(Color(.systemGray6))
                            .cornerRadius(6)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text("反向编码地址:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(reverseAddress)
                            .font(.caption)
                            .padding(8)
                            .background(Color(.systemGray6))
                            .cornerRadius(6)
                    }
                }
            }

            // 验证问题列表
            if !deliveryPoint.validationIssuesList.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("验证问题")
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    ForEach(deliveryPoint.validationIssuesList, id: \.self) { issue in
                        HStack(spacing: 8) {
                            // 为门牌号差异过大的问题使用特殊图标
                            Image(systemName: isStreetNumberIssue(issue) ? "exclamationmark.octagon.fill" : "exclamationmark.circle.fill")
                                .foregroundColor(isStreetNumberIssue(issue) ? .red : .orange)
                                .font(.caption)

                            Text(issue)
                                .font(.caption)
                                .foregroundColor(isStreetNumberIssue(issue) ? .red : .primary)
                        }
                        .padding(.vertical, 4)
                        .padding(.horizontal, 8)
                        .background(isStreetNumberIssue(issue) ? Color.red.opacity(0.1) : Color.orange.opacity(0.1))
                        .cornerRadius(6)
                    }
                }
            }

            // 建议操作
            VStack(alignment: .leading, spacing: 8) {
                Text("建议")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                // 检查是否有门牌号差异过大的问题
                if hasStreetNumberIssue(deliveryPoint) {
                    RecommendationRow(
                        icon: "exclamationmark.octagon.fill",
                        color: .red,
                        text: "street_number_issue_warning".localized
                    )
                } else if deliveryPoint.addressValidationScore < 40 {
                    RecommendationRow(
                        icon: "exclamationmark.triangle.fill",
                        color: .red,
                        text: "地址与坐标严重不匹配，强烈建议手动核实地址以避免送错地址被罚款"
                    )
                } else if deliveryPoint.addressValidationScore < 60 {
                    RecommendationRow(
                        icon: "exclamationmark.triangle.fill",
                        color: .orange,
                        text: "地址验证分数较低，建议核实地址准确性"
                    )
                } else if deliveryPoint.addressValidationScore < 80 {
                    RecommendationRow(
                        icon: "info.circle.fill",
                        color: .blue,
                        text: "地址基本匹配，但存在小问题，可考虑核实"
                    )
                } else {
                    RecommendationRow(
                        icon: "checkmark.circle.fill",
                        color: .green,
                        text: "地址验证通过，可以安全配送"
                    )
                }
            }
        }
        .padding(.top, 8)
    }

    /// 检查是否有门牌号差异过大的问题
    private func hasStreetNumberIssue(_ deliveryPoint: DeliveryPoint) -> Bool {
        guard let issues = deliveryPoint.addressValidationIssues else { return false }
        return isStreetNumberIssue(issues)
    }

    /// 检查单个问题字符串是否为门牌号差异问题
    private func isStreetNumberIssue(_ issue: String) -> Bool {
        // 检查是否包含门牌号差异的关键词
        let streetNumberIssueKeywords = [
            "地址差异很大",
            "地址差异极大",
            "差11号", "差12号", "差13号", "差14号", "差15号",
            "差16号", "差17号", "差18号", "差19号", "差20号",
            "差21号", "差22号", "差23号", "差24号", "差25号",
            "差26号", "差27号", "差28号", "差29号", "差30号",
            "差31号", "差32号", "差33号", "差34号", "差35号",
            "差36号", "差37号", "差38号", "差39号", "差40号",
            "差41号", "差42号", "差43号", "差44号", "差45号",
            "差46号", "差47号", "差48号", "差49号", "差50号",
            "高风险", "极高风险", "必须修正地址"
        ]

        return streetNumberIssueKeywords.contains { keyword in
            issue.contains(keyword)
        }
    }
}

/// 建议行
struct RecommendationRow: View {
    let icon: String
    let color: Color
    let text: String

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
        }
        .padding(8)
        .background(color.opacity(0.1))
        .cornerRadius(6)
    }
}

/// 在RouteView的bottom sheet中显示的地址验证警告列表
struct AddressValidationWarningListView: View {
    let deliveryPoints: [DeliveryPoint]

    var problematicPoints: [DeliveryPoint] {
        deliveryPoints.filter { $0.hasValidationIssues }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("地址验证警告")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(problematicPoints.count) 个问题")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray5))
                    .cornerRadius(12)
            }

            if problematicPoints.isEmpty {
                // 无问题状态
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.green)

                    Text("所有地址验证通过")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                // 问题地址列表
                LazyVStack(spacing: 12) {
                    ForEach(problematicPoints, id: \.id) { point in
                        AddressValidationWarningCard(deliveryPoint: point)
                    }
                }
            }
        }
        .padding()
    }
}

/// 地址验证警告卡片
struct AddressValidationWarningCard: View {
    let deliveryPoint: DeliveryPoint

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 地址和分数
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(deliveryPoint.primaryAddress)
                        .font(.subheadline)
                        .lineLimit(2)

                    Text("验证分数: \(Int(deliveryPoint.addressValidationScore))分")
                        .font(.caption)
                        .foregroundColor(scoreColor)
                }

                Spacer()

                // 分数徽章
                Text("\(Int(deliveryPoint.addressValidationScore))")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(width: 32, height: 32)
                    .background(scoreColor)
                    .clipShape(Circle())
            }

            // 统一的地址验证问题提示
            if deliveryPoint.hasValidationIssues {
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.caption)

                    Text("address_validation_issue".localized)
                        .font(.caption)
                        .foregroundColor(.orange)
                        .lineLimit(1)
                }
            }
        }
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(scoreColor.opacity(0.3), lineWidth: 1)
        )
    }

    private var scoreColor: Color {
        // 如果有门牌号差异过大的问题，直接返回红色
        if hasStreetNumberIssue(deliveryPoint) {
            return .red
        }

        if deliveryPoint.addressValidationScore >= 80 {
            return .green
        } else if deliveryPoint.addressValidationScore >= 60 {
            return .orange
        } else {
            return .red
        }
    }

    /// 检查是否有门牌号差异过大的问题
    private func hasStreetNumberIssue(_ deliveryPoint: DeliveryPoint) -> Bool {
        guard let issues = deliveryPoint.addressValidationIssues else { return false }

        // 检查是否包含门牌号差异的关键词
        let streetNumberIssueKeywords = [
            "地址差异很大",
            "地址差异极大",
            "差11号", "差12号", "差13号", "差14号", "差15号",
            "差16号", "差17号", "差18号", "差19号", "差20号",
            "差21号", "差22号", "差23号", "差24号", "差25号",
            "差26号", "差27号", "差28号", "差29号", "差30号",
            "差31号", "差32号", "差33号", "差34号", "差35号",
            "差36号", "差37号", "差38号", "差39号", "差40号",
            "差41号", "差42号", "差43号", "差44号", "差45号",
            "差46号", "差47号", "差48号", "差49号", "差50号",
            "高风险", "极高风险", "必须修正地址"
        ]

        return streetNumberIssueKeywords.contains { keyword in
            issues.contains(keyword)
        }
    }
}
