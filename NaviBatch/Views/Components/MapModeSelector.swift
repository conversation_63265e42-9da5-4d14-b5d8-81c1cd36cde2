import SwiftUI
import MapKit

/// 地图模式选择器
/// 类似Apple Maps的多模式选择界面
struct MapModeSelector: View {
    @Binding var selectedMapMode: MapDisplayMode
    @Binding var isPresented: Bool

    let modes: [MapDisplayMode] = [.driving, .satellite]

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            ZStack {
                // 标题居中于整个sheet
                Text("map_mode_selector_title".localized)
                    .font(.headline)
                    .foregroundColor(.primary)

                // 关闭按钮在右上角
                HStack {
                    Spacer()

                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))

            // 模式选择 - 水平布局（只有两个选项）
            HStack(spacing: 16) {
                ForEach(modes, id: \.self) { mode in
                    MapModeCard(
                        mode: mode,
                        isSelected: selectedMapMode == mode,
                        action: {
                            selectedMapMode = mode
                            // 添加触觉反馈
                            let generator = UIImpactFeedbackGenerator(style: .medium)
                            generator.impactOccurred()
                        }
                    )
                }
            }
            .padding()

            Spacer()
        }
        .background(Color(.systemGroupedBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

/// 地图模式卡片
struct MapModeCard: View {
    let mode: MapDisplayMode
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // 预览图
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(mode.previewColor)
                        .frame(height: 100)

                    // 模式图标
                    Image(systemName: mode.iconName)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 3)
                )

                // 模式名称
                Text(mode.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

/// 地图显示模式枚举 - 基于真正的MapKit样式
enum MapDisplayMode: String, CaseIterable {
    case driving = "driving"
    case satellite = "satellite"

    var displayName: String {
        switch self {
        case .driving:
            return "map_mode_driving".localized
        case .satellite:
            return "map_mode_satellite".localized
        }
    }

    var iconName: String {
        switch self {
        case .driving:
            return "car.fill"
        case .satellite:
            return "globe.americas"
        }
    }

    var previewColor: Color {
        switch self {
        case .driving:
            return Color.blue.opacity(0.7)
        case .satellite:
            return Color.gray.opacity(0.7)
        }
    }

    /// 真正的MapKit地图样式
    var mapStyle: MapStyle {
        switch self {
        case .driving:
            // 驾驶模式：标准地图，现实高程，显示交通信息
            return .standard(elevation: .realistic, pointsOfInterest: .including([.gasStation, .restroom]))
        case .satellite:
            // 卫星模式：卫星图像，现实高程
            return .imagery(elevation: .realistic)
        }
    }
}

#Preview {
    @Previewable @State var selectedMode: MapDisplayMode = .driving
    @Previewable @State var isPresented = true

    return MapModeSelector(selectedMapMode: $selectedMode, isPresented: $isPresented)
        .frame(width: 350, height: 400)
}
