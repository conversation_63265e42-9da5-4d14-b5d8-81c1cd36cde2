//
//  OverlapSelectionMenuView.swift
//  NaviBatch
//
//  Created by AI Assistant on 2025-08-02.
//

import SwiftUI
import CoreLocation

// 🎯 重叠标记选择菜单视图
struct OverlapSelectionMenuView: View {
    let points: [DeliveryPoint]
    let onPointSelected: (DeliveryPoint) -> Void
    let onCancel: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标题
                HStack {
                    Text("选择配送点")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("相同位置有多个配送点")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                .padding(.bottom, 12)
                
                Divider()
                
                // 配送点列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(points, id: \.id) { point in
                            OverlapPointRowView(
                                point: point,
                                onTap: {
                                    onPointSelected(point)
                                }
                            )
                            
                            if point.id != points.last?.id {
                                Divider()
                                    .padding(.leading, 60)
                            }
                        }
                    }
                }
                .padding(.top, 8)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        onCancel()
                    }
                }
            }
        }
    }
}

// 🎯 重叠点行视图
struct OverlapPointRowView: View {
    let point: DeliveryPoint
    let onTap: () -> Void
    
    // 状态颜色
    private var statusColor: Color {
        switch point.deliveryStatus {
        case .completed:
            return .green
        case .failed:
            return .red
        case .inProgress:
            return .orange
        case .pending:
            if point.validationIssueDescription != nil {
                return .orange // 有坐标警告
            } else {
                return .blue
            }
        }
    }

    // 状态文本
    private var statusText: String {
        switch point.deliveryStatus {
        case .completed:
            return "已完成"
        case .failed:
            return "失败"
        case .inProgress:
            return "进行中"
        case .pending:
            if point.validationIssueDescription != nil {
                return "坐标警告"
            } else {
                return "待配送"
            }
        }
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 编号标记
                ZStack {
                    Circle()
                        .fill(statusColor)
                        .frame(width: 36, height: 36)
                    
                    Text("\(point.sorted_number)")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }
                
                // 地址信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(point.primaryAddress)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    HStack {
                        // 包裹数量
                        if point.packageCount > 1 {
                            Label("\(point.packageCount)个包裹", systemImage: "shippingbox")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        // 状态标签
                        Text(statusText)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(statusColor.opacity(0.1))
                            .foregroundColor(statusColor)
                            .cornerRadius(4)
                    }
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 预览
#Preview {
    OverlapSelectionMenuView(
        points: [
            // 这里需要创建测试数据，但由于DeliveryPoint是Core Data实体，
            // 在预览中创建会比较复杂，所以暂时留空
        ],
        onPointSelected: { _ in },
        onCancel: { }
    )
}
