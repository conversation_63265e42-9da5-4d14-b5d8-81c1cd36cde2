import SwiftUI
import MapKit
import SwiftData
import os.log
import CoreLocation

/// 添加配送点表单
/// 使用统一的SimpleAddressSheet组件实现地址输入功能
struct AddDeliveryPointSheet: View {
    @Environment(\.modelContext) private var modelContext
    @State private var packageCount: Int = 1
    @State private var notes: String = ""
    @State private var status: DeliveryStatus = .pending

    var addressPointType: AddressPointType? = .stop
    var onPointAdded: ((DeliveryPoint) -> Void)? = nil

    var body: some View {
        // 使用统一的SimpleAddressSheet组件
        SimpleAddressSheet(
            onAddressAdded: { savedAddress in
                // 当地址添加完成后的回调
                // 创建一个配送点并调用onPointAdded回调
                Task {
                    do {
                        // 🎯 修复：使用结构化地址创建新的配送点，正确设置地址字段
                        let standardizedAddress = StreetAbbreviationStandardizer.standardizeStreetAbbreviations(savedAddress.address)
                        let newPoint = DeliveryPoint(
                            sort_number: 0, // 临时编号，后续会更新
                            streetName: standardizedAddress, // 设置为标准化地址，让primaryAddress能正确显示
                            originalAddress: standardizedAddress, // 🎯 确保originalAddress也被设置，避免显示管道符号
                            coordinate: savedAddress.coordinate
                        )

                        // 设置配送信息
                        newPoint.packageCount = packageCount
                        newPoint.status = status.rawValue
                        newPoint.statusUpdateTime = Date()
                        if !notes.isEmpty {
                            newPoint.notes = notes
                        }

                        // 🚨 处理notes中的特殊信息
                        if let savedNotes = savedAddress.notes {
                            let noteLines = savedNotes.components(separatedBy: "\n")

                            for noteLine in noteLines {
                                // 处理验证警告信息
                                if noteLine.hasPrefix("VALIDATION_WARNING:") {
                                    let warningMessage = String(noteLine.dropFirst("VALIDATION_WARNING:".count))

                                    // 根据警告类型设置相应的字段
                                    if warningMessage.contains("缺少门牌号") || warningMessage.contains("missing") {
                                        newPoint.setGeocodingWarning(.partialMatch)
                                        newPoint.addressValidationIssues = warningMessage
                                        newPoint.addressValidationScore = 60.0
                                    } else if warningMessage.contains("无法获取坐标") || warningMessage.contains("cannot_get_coordinates") {
                                        newPoint.setGeocodingWarning(.coordinateMissing)
                                        newPoint.addressValidationIssues = warningMessage
                                        newPoint.addressValidationScore = 25.0
                                    } else if warningMessage.contains("置信度较低") || warningMessage.contains("低精度") {
                                        newPoint.setGeocodingWarning(.lowAccuracy)
                                        newPoint.addressValidationIssues = warningMessage
                                        newPoint.addressValidationScore = 50.0
                                    } else {
                                        // 通用验证问题
                                        newPoint.addressValidationIssues = warningMessage
                                        newPoint.addressValidationScore = 50.0
                                        newPoint.setGeocodingWarning(.lowAccuracy)
                                    }

                                    print("🚨 保存地址验证警告到DeliveryPoint: \(warningMessage)")
                                }

                                // 🎯 处理用户选择的应用类型信息
                                else if noteLine.hasPrefix("USER_SELECTED_APP_TYPE:") {
                                    let appTypeRaw = String(noteLine.dropFirst("USER_SELECTED_APP_TYPE:".count))
                                    if let appType = DeliveryAppType(rawValue: appTypeRaw) {
                                        newPoint.sourceAppRaw = appType.rawValue
                                        print("🎯 设置用户选择的应用类型: \(appType.displayName) -> \(savedAddress.address)")
                                        Logger.info("📱 从图片识别设置应用类型: \(appType.displayName) -> \(savedAddress.address)", type: .data)
                                    }
                                }
                            }
                        }

                        // 设置点类型
                        if let pointType = addressPointType {
                            newPoint.isStartPoint = (pointType == .start)
                            newPoint.isEndPoint = (pointType == .end)
                        }

                        // 保存到数据库
                        modelContext.insert(newPoint)
                        try modelContext.save()

                        // 记录日志
                        Logger.info("成功添加配送点: \(savedAddress.address)", type: .data)

                        // 回调通知
                        onPointAdded?(newPoint)
                    } catch {
                        Logger.error("添加配送点失败: \(error.localizedDescription)", type: .data)
                    }
                }
            },
            addressPointType: addressPointType,
            pointToEdit: nil
        )
    }


}

#Preview("AddDeliveryPointSheet") {
    // 使用持久化存储
    let schema = Schema([DeliveryPoint.self])
    let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
    let container = try! ModelContainer(for: schema, configurations: [config])

    // 打印数据库路径
    if let url = container.configurations.first?.url {
        print("AddDeliveryPointSheet Preview - 数据库路径: \(url.path)")
    }

    return AddDeliveryPointSheet()
        .modelContainer(container)
}
