import SwiftUI

/// 配送点行视图
/// 用于在列表中显示配送点信息
struct DeliveryPointRow: View {
    let point: DeliveryPoint
    var showEditButton: Bool = false
    var onEdit: (() -> Void)? = nil
    var onPhotoTap: (() -> Void)? = nil

    // 状态标签映射
    private let statusLabels: [DeliveryStatus: String] = [
        .pending: "pending".localized,
        .inProgress: "in_progress".localized,
        .completed: "completed".localized,
        .failed: "failed".localized
    ]

    var body: some View {
        HStack {
            // 编号 - 如果有地理编码警告，显示红色
            ZStack {
                Circle()
                    .fill(hasGeocodingIssue ? .white : statusColor)
                    .frame(width: 28, height: 28)
                    .overlay(
                        Circle()
                            .stroke(hasGeocodingIssue ? .red : .clear, lineWidth: 2)
                    )

                if hasGeocodingIssue {
                    // 显示警告图标
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 12))
                        .foregroundColor(.red)
                } else {
                    // 显示编号 - 如果是第三方快递且有排序号，显示第三方排序号
                    Text(displayText)
                        .font(.headline)
                        .foregroundColor(.white)
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                // 地址 - 优化单位信息显示
                HStack(spacing: 4) {
                    // 如果有单位号，优先显示并突出
                    if point.hasUnitNumber, let unitNumber = point.unitNumber {
                        Text(unitNumber)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 1)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(3)
                    }

                    // 显示街道地址（不包含单位号，因为已经单独显示）
                    Text(formatMainAddress(point.primaryAddress, hasUnit: point.hasUnitNumber))
                        .lineLimit(1)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // 警告信息（如果有）
                if let warning = point.geocodingWarning, !warning.isEmpty {
                    HStack {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.orange)
                        Text(warning)
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }

                // 位置验证状态（如果不是有效状态）
                let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
                if validationStatus != .valid {
                    HStack {
                        Image(systemName: validationStatus.iconName)
                            .foregroundColor(validationStatus.color)
                        Text(validationStatus.localizedName)
                            .font(.caption)
                            .foregroundColor(validationStatus.color)
                    }
                }

                // 与用户位置的距离（如果有）
                if let distance = point.distanceFromUserLocation {
                    Text(formatDistance(distance))
                        .font(.caption)
                        .foregroundColor(distance <= 20_000_000 ? .green : .orange)
                }
            }

            Spacer()

            // 照片状态图标和按钮
            if hasPhotos {
                Button(action: {
                    onPhotoTap?()
                }) {
                    Image(systemName: "photo.on.rectangle")
                        .foregroundColor(.blue)
                        .padding(.trailing, 4)
                }
                .buttonStyle(BorderlessButtonStyle())
            }

            // 状态图标
            Image(systemName: point.deliveryStatus.iconName)
                .foregroundColor(statusColor)

            // 编辑按钮（如果启用）
            if showEditButton && onEdit != nil {
                Button(action: {
                    onEdit?()
                }) {
                    Image(systemName: "pencil")
                        .foregroundColor(.blue)
                }
                .buttonStyle(BorderlessButtonStyle())
            }

            // 状态标签
            if let statusLabel = statusLabels[point.deliveryStatus] {
                Spacer()

                HStack(spacing: 5) {
                    Circle()
                        .fill(point.deliveryStatus.color)
                        .frame(width: 10, height: 10)
                    Text(statusLabel)
                        .font(.caption)
                        .foregroundColor(point.deliveryStatus.color)
                }
                .padding(.vertical, 4)
                .padding(.horizontal, 8)
                .background(point.deliveryStatus.color.opacity(0.1))
                .cornerRadius(10)
            }

            // 如果是失败状态，显示失败原因
            if point.deliveryStatus == .failed {
                Spacer()

                Text(point.displayFailureReason)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.vertical, 2)
                    .padding(.horizontal, 6)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding(.vertical, 4)
    }

    // 格式化距离
    private func formatDistance(_ distance: Double) -> String {
        return DistanceFormatter.shared.formatDistance(distance)
    }

    // 检查是否有照片
    private var hasPhotos: Bool {
        return (point.doorPhotoPath != nil) ||
               (point.packagePhotoPath != nil) ||
               (point.placementPhotoPath != nil)
    }

    // 判断是否有地理编码问题
    private var hasGeocodingIssue: Bool {
        // 检查是否有地理编码警告
        if let warning = point.geocodingWarning, !warning.isEmpty {
            return true
        }

        // 检查位置验证状态
        let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
        if validationStatus != .valid && validationStatus != .unknown {
            return true
        }

        // 检查坐标是否为0,0（无效坐标）
        if point.latitude == 0 && point.longitude == 0 {
            return true
        }

        // 检查坐标是否在有效范围内
        if point.latitude < -90 || point.latitude > 90 ||
           point.longitude < -180 || point.longitude > 180 {
            return true
        }

        return false
    }

    // 显示文本 - 如果是第三方快递且有排序号，显示第三方排序号
    private var displayText: String {
        if point.isThirdPartyWithSort, let thirdPartySort = point.thirdPartySortNumber {
            return thirdPartySort
        } else {
            return "\(point.sorted_number)"
        }
    }

    // 状态颜色
    private var statusColor: Color {
        // 如果有地理编码警告，优先显示警告颜色
        if let warning = point.geocodingWarning, !warning.isEmpty {
            return .orange
        }

        // 如果有位置验证状态，根据状态显示颜色
        let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
        if validationStatus != .valid {
            return validationStatus.color
        }

        // 否则根据配送状态显示颜色
        return point.deliveryStatus.color
    }

    /// 格式化主要地址显示，确保单位信息正确处理
    private func formatMainAddress(_ fullAddress: String, hasUnit: Bool) -> String {
        let components = fullAddress.components(separatedBy: ",")

        if hasUnit && !components.isEmpty {
            // 如果有单位号，第一个组件通常包含"单位号, 街道地址"
            // 我们需要移除单位号部分，只显示街道地址
            let firstComponent = components[0].trimmingCharacters(in: .whitespaces)

            // 查找第一个逗号后的内容作为街道地址
            if let commaIndex = firstComponent.firstIndex(of: ",") {
                let streetAddress = String(firstComponent[firstComponent.index(after: commaIndex)...])
                    .trimmingCharacters(in: .whitespaces)
                return streetAddress.isEmpty ? firstComponent : streetAddress
            }
        }

        // 默认返回第一个组件或完整地址
        return components.first?.trimmingCharacters(in: .whitespaces) ?? fullAddress
    }
}

// 完整示例用法：
/*
List {
    ForEach(deliveryPoints) { point in
        DeliveryPointRow(point: point, onPhotoTap: {
            // 显示照片查看器
            let photoViewer = NavigationStack {
                DeliveryPhotoViewer(deliveryPoint: point)
            }
            UIApplication.shared.windows.first?.rootViewController?.present(UIHostingController(rootView: photoViewer), animated: true)
        })
    }
}
*/
