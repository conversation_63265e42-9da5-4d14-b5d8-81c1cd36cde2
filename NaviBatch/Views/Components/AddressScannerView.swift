import SwiftUI
import AVFoundation
import Vision
import CoreLocation
import VisionKit

// MARK: - 扫描状态枚举
enum ScanningState: Equatable {
    case scanning
    case verifying(String)
    case verified(address: String, coordinate: CLLocationCoordinate2D, box: CGRect)
    case failed(String)
    case error(String)

    // 简化的Equatable实现
    static func == (lhs: ScanningState, rhs: ScanningState) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.scanning, .scanning):
            return true
        case (.verifying(let a), .verifying(let b)):
            return a == b
        case (.verified(let a, _, _), .verified(let b, _, _)):
            return a == b  // 简化比较，只比较地址文本
        case (.failed(let a), .failed(let b)):
            return a == b
        case (.error(let a), .error(let b)):
            return a == b
        default:
            return false
        }
    }
}

// MARK: - 🎯 简化版原生地址扫描器
@available(iOS 16.0, *)
struct SimpleAddressScannerSheet: View {
    @State private var scanningState: ScanningState = .scanning
    @State private var verifiedData: (address: String, coordinate: CLLocationCoordinate2D)? = nil
    @State private var showBottomSheet: Bool = false
    @State private var flashlightOn: Bool = false

    var onAddressConfirmed: ((String, CLLocationCoordinate2D) -> Void)
    @Environment(\.dismiss) private var dismiss

    init(onAddressConfirmed: @escaping (String, CLLocationCoordinate2D) -> Void) {
        self.onAddressConfirmed = onAddressConfirmed
    }

    var body: some View {
        ZStack {
            // 🎯 纯净的原生扫描器 - 全屏无遮挡
            if DataScannerViewController.isSupported && DataScannerViewController.isAvailable {
                NativeDataScannerView(scanningState: $scanningState)
                    .ignoresSafeArea(.all)
                    .onAppear {
                        print("✅ DataScannerViewController 支持状态:")
                        print("   - isSupported: \(DataScannerViewController.isSupported)")
                        print("   - isAvailable: \(DataScannerViewController.isAvailable)")
                    }
            } else {
                VStack {
                    Text("device_not_support_scanning".localized)
                        .foregroundColor(.white)
                        .font(.headline)
                    Text("requires_ios16_a12_chip".localized)
                        .foregroundColor(.white.opacity(0.8))
                        .font(.caption)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("debug_info".localized)
                            .foregroundColor(.yellow)
                            .font(.caption)
                        Text("isSupported: \(DataScannerViewController.isSupported)")
                            .foregroundColor(.white.opacity(0.7))
                            .font(.caption2)
                        Text("isAvailable: \(DataScannerViewController.isAvailable)")
                            .foregroundColor(.white.opacity(0.7))
                            .font(.caption2)
                    }
                    .padding(.top, 20)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            }

            // 简单的关闭按钮 - 放在左下角避免与Apple UI冲突
            VStack {
                Spacer()
                HStack {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding(.leading, 20)
                    .padding(.bottom, 50)

                    Spacer()
                }
            }

            // 底部确认Sheet - 简化版本
            if showBottomSheet, let data = verifiedData {
                confirmationSheet(for: data)
            }
        }
        .onChange(of: scanningState) { _, newState in
            handleStateChange(newState)
        }
        .navigationBarHidden(true)
        .statusBarHidden(true)
    }

    private func handleStateChange(_ newState: ScanningState) {
        switch newState {
        case .verified(let address, let coordinate, _):
            verifiedData = (address, coordinate)
            withAnimation(.easeInOut) {
                showBottomSheet = true
            }
        case .scanning, .failed, .error, .verifying:
            if showBottomSheet {
                withAnimation {
                    showBottomSheet = false
                }
            }
        }
    }

    @ViewBuilder
    private func confirmationSheet(for data: (address: String, coordinate: CLLocationCoordinate2D)) -> some View {
        VStack {
            Spacer()
            VStack(spacing: 20) {
                Text("address_confirmation".localized)
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(data.address)
                    .font(.body)
                    .multilineTextAlignment(.center)

                HStack(spacing: 20) {
                    Button("continue_scanning".localized) {
                        showBottomSheet = false
                        scanningState = .scanning
                    }
                    .buttonStyle(.bordered)

                    // 🎯 优化确认添加按钮的视觉设计
                    Button(action: {
                        onAddressConfirmed(data.address, data.coordinate)
                        dismiss()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 14, weight: .semibold))
                            Text("confirm_add".localized)
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(10)
                        .shadow(color: Color.blue.opacity(0.3), radius: 6, x: 0, y: 3)
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(20)
            .padding()
        }
        .transition(.move(edge: .bottom))
        .zIndex(3)
    }
}

// MARK: - 原生DataScanner包装器
@available(iOS 16.0, *)
struct NativeDataScannerView: UIViewControllerRepresentable {
    @Binding var scanningState: ScanningState

    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }

    func makeUIViewController(context: Context) -> DataScannerViewController {
        let recognizedDataTypes: Set<DataScannerViewController.RecognizedDataType> = [
            .text(languages: ["zh-Hans", "en-US", "en-AU", "en-GB"])  // 支持中英文混合识别
        ]

        let scanner = DataScannerViewController(
            recognizedDataTypes: recognizedDataTypes,
            qualityLevel: .balanced,  // 使用balanced获得更好的性能
            recognizesMultipleItems: true,
            isGuidanceEnabled: true,
            isHighlightingEnabled: true
        )

        scanner.delegate = context.coordinator
        return scanner
    }

    func updateUIViewController(_ uiViewController: DataScannerViewController, context: Context) {
        // 确保扫描器处于正确状态
        if !uiViewController.isScanning {
            do {
                try uiViewController.startScanning()
                print("✅ DataScanner 启动成功")
            } catch {
                print("❌ DataScanner 启动失败: \(error)")
            }
        }
    }

    class Coordinator: NSObject, DataScannerViewControllerDelegate {
        var parent: NativeDataScannerView
        private var isProcessing = false

        init(parent: NativeDataScannerView) {
            self.parent = parent
        }

        func dataScanner(_ dataScanner: DataScannerViewController, didTapOn item: RecognizedItem) {
            switch item {
            case .text(let text):
                let scannedText = text.transcript
                if isLikelyAddressText(scannedText) {
                    verifyAddress(text: scannedText)
                } else {
                    parent.scanningState = .failed(scannedText)
                }
            default:
                break
            }
        }

        func dataScanner(_ dataScanner: DataScannerViewController, didAdd addedItems: [RecognizedItem], allItems: [RecognizedItem]) {
            // 收集所有文本项
            var textItems: [String] = []

            for item in addedItems {
                if case .text(let text) = item {
                    let scannedText = text.transcript
                    textItems.append(scannedText)
                }
            }

            // 尝试找到最佳地址匹配
            for textItem in textItems {
                if isLikelyAddressText(textItem) {
                    verifyAddress(text: textItem)
                    break
                }
            }

            // 如果没有找到单独的地址，尝试组合相邻的文本
            if textItems.count > 1 {
                tryCombinatorialAddressMatching(textItems: textItems)
            }
        }

        private func tryCombinatorialAddressMatching(textItems: [String]) {
            // 尝试不同的文本组合来形成完整地址
            for i in 0..<textItems.count {
                for j in i+1..<min(i+3, textItems.count) { // 最多组合3行
                    let combinedText = Array(textItems[i...j]).joined(separator: " ")
                    if isLikelyAddressText(combinedText) && combinedText.count <= 150 {
                        verifyAddress(text: combinedText)
                        return
                    }
                }
            }
        }

        private func isLikelyAddressText(_ text: String) -> Bool {
            let cleanText = text.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

            // 调整长度限制，允许更长的地址
            guard cleanText.count >= 8 && cleanText.count <= 150 else { return false }

            let hasNumber = cleanText.rangeOfCharacter(from: .decimalDigits) != nil

            // 扩展澳洲街道类型关键词
            let streetKeywords = [
                "street", "st", "road", "rd", "avenue", "ave", "lane", "ln", "drive", "dr",
                "boulevard", "blvd", "place", "pl", "court", "ct", "way", "circle", "cir",
                "crescent", "cres", "close", "cl", "terrace", "tce", "parade", "pde",
                "highway", "hwy", "freeway", "fwy", "grove", "gr", "walk", "wlk",
                "square", "sq", "gardens", "gdns", "park", "pk", "rise", "esplanade", "esp"
            ]

            let hasStreetKeyword = streetKeywords.contains { keyword in
                cleanText.contains(" \(keyword) ") || cleanText.hasSuffix(" \(keyword)") || cleanText.hasPrefix("\(keyword) ")
            }

            // 澳洲邮编和州名检测
            let hasAustralianPostcode = cleanText.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil
            let australianStates = ["vic", "nsw", "qld", "wa", "sa", "tas", "act", "nt"]
            let hasAustralianState = australianStates.contains { state in
                cleanText.contains(" \(state) ") || cleanText.hasSuffix(" \(state)")
            }

            // 单元号检测 (如 U 1, Unit 1, Apt 1, 等)
            let hasUnitNumber = cleanText.range(of: "\\b(u|unit|apt|apartment|suite|ste)\\s*\\d+", options: .regularExpression) != nil

            let excludeKeywords = ["invoice", "statement", "abn", "acn", "phone", "email", "website", "www", "commission", "securities", "investments"]
            let hasExcludeKeyword = excludeKeywords.contains { keyword in
                cleanText.contains(keyword)
            }

            // 如果包含排除关键词，但没有明确的地址特征，则排除
            if hasExcludeKeyword && !hasStreetKeyword && !hasAustralianPostcode && !hasAustralianState {
                return false
            }

            // 排除纯数字或简单格式
            let isSimpleNumber = cleanText.range(of: "^[0-9/\\-\\s]+$", options: .regularExpression) != nil
            if isSimpleNumber {
                return false
            }

            // 地址必须包含数字，并且至少有一个地址特征
            return hasNumber && (hasStreetKeyword || hasAustralianPostcode || hasAustralianState || hasUnitNumber)
        }

        private func verifyAddress(text: String) {
            guard !isProcessing else { return }

            isProcessing = true

            // 智能提取地址部分
            let extractedAddress = extractAddressFromText(text)
            parent.scanningState = .verifying(extractedAddress)

            // 使用NaviBatch的统一地址验证服务
            Task {
                let validationResult = await UnifiedAddressValidationService.shared.validateAddress(extractedAddress)

                await MainActor.run {
                    self.isProcessing = false

                    if validationResult.isValid {
                        self.parent.scanningState = .verified(
                            address: validationResult.validatedAddress,
                            coordinate: validationResult.coordinate,
                            box: .zero
                        )
                    } else {
                        self.parent.scanningState = .failed(extractedAddress)
                    }
                }
            }
        }

        private func cleanAndFormatAddress(_ text: String) -> String {
            var cleaned = text.trimmingCharacters(in: .whitespacesAndNewlines)

            // 移除多余的空格
            cleaned = cleaned.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

            // 处理特殊字符和OCR错误
            cleaned = cleaned.replacingOccurrences(of: "[|\\\\]", with: "", options: .regularExpression)

            // 标准化单元号格式 - 处理更多变体
            cleaned = cleaned.replacingOccurrences(of: "\\b(U|UNIT)\\s*(\\d+)", with: "Unit $2", options: [.regularExpression, .caseInsensitive])
            cleaned = cleaned.replacingOccurrences(of: "\\b(APT|APARTMENT)\\s*(\\d+)", with: "Apartment $2", options: [.regularExpression, .caseInsensitive])
            cleaned = cleaned.replacingOccurrences(of: "\\b(STE|SUITE)\\s*(\\d+)", with: "Suite $2", options: [.regularExpression, .caseInsensitive])

            // 标准化街道类型 - 扩展映射
            let streetTypeMap = [
                " ST ": " Street ",
                " RD ": " Road ",
                " AVE ": " Avenue ",
                " DR ": " Drive ",
                " LN ": " Lane ",
                " CT ": " Court ",
                " PL ": " Place ",
                " CRES ": " Crescent ",
                " CL ": " Close ",
                " TCE ": " Terrace ",
                " PDE ": " Parade ",
                " HWY ": " Highway ",
                " FWY ": " Freeway ",
                " GR ": " Grove ",
                " WLK ": " Walk ",
                " SQ ": " Square ",
                " GDNS ": " Gardens ",
                " PK ": " Park ",
                " ESP ": " Esplanade "
            ]

            for (abbrev, full) in streetTypeMap {
                cleaned = cleaned.replacingOccurrences(of: abbrev, with: full, options: .caseInsensitive)
                // 也处理末尾的缩写
                if cleaned.hasSuffix(abbrev.trimmingCharacters(in: .whitespaces)) {
                    cleaned = cleaned.replacingOccurrences(of: abbrev.trimmingCharacters(in: .whitespaces) + "$", with: full.trimmingCharacters(in: .whitespaces), options: [.regularExpression, .caseInsensitive])
                }
            }

            // 确保以街道类型结尾的地址格式正确
            let streetTypes = ["Street", "Road", "Avenue", "Drive", "Lane", "Court", "Place", "Crescent", "Close", "Terrace", "Parade", "Highway", "Freeway", "Grove", "Walk", "Square", "Gardens", "Park", "Esplanade"]
            for streetType in streetTypes {
                if cleaned.hasSuffix(" \(streetType.uppercased())") {
                    cleaned = cleaned.replacingOccurrences(of: " \(streetType.uppercased())", with: " \(streetType)")
                }
            }

            // 处理常见的OCR错误 - 在门牌号中O通常应该是0
            cleaned = cleaned.replacingOccurrences(of: "\\b(\\d+)O(\\d*)\\b", with: "$10$2", options: .regularExpression)
            cleaned = cleaned.replacingOccurrences(of: "\\bO(\\d+)\\b", with: "0$1", options: .regularExpression)

            // 确保邮编格式正确 (4位数字)
            cleaned = cleaned.replacingOccurrences(of: "\\b(\\d{4})\\b", with: "$1", options: .regularExpression)

            return cleaned
        }

        /// 从扫描文本中智能提取地址部分
        /// 过滤掉公司名称、发票信息等无关内容，只保留真正的地址
        private func extractAddressFromText(_ text: String) -> String {
            let lines = text.components(separatedBy: .newlines)
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }

            var addressComponents: [String] = []
            var foundAddressStart = false

            for line in lines {
                let lineLower = line.lowercased()

                // 跳过明显的非地址行
                if shouldSkipLine(lineLower) {
                    continue
                }

                // 检查是否是地址的开始（单元号、门牌号等）
                if !foundAddressStart && isAddressStartLine(lineLower) {
                    foundAddressStart = true
                    addressComponents.append(line)
                    continue
                }

                // 如果已经找到地址开始，继续收集地址相关行
                if foundAddressStart {
                    if isAddressContinuationLine(lineLower) {
                        addressComponents.append(line)
                    } else {
                        // 遇到非地址行，停止收集
                        break
                    }
                } else {
                    // 还没找到地址开始，检查当前行是否是完整地址
                    if isCompleteAddressLine(lineLower) {
                        return formatAddressForNaviBatch(line)
                    }
                }
            }

            // 如果找到了地址组件，组合它们
            if !addressComponents.isEmpty {
                let combinedAddress = addressComponents.joined(separator: " ")
                return formatAddressForNaviBatch(combinedAddress)
            }

            // 如果没有找到明确的地址，返回清理后的原文本
            return formatAddressForNaviBatch(text)
        }

        /// 将地址格式化为NaviBatch期望的格式
        /// 从 "Unit 1 12 KERFERD Road GLEN WAVERLEY VIC 3150"
        /// 转换为 "1/12 KERFERD Road, GLEN WAVERLEY, VIC 3150"
        private func formatAddressForNaviBatch(_ address: String) -> String {
            let cleaned = cleanAndFormatAddress(address)

            // 首先处理单元号格式转换
            let convertedAddress = convertUnitNumberFormat(cleaned)

            // 使用正则表达式解析地址组件
            let addressPattern = "^(.+?)\\s+([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*)\\s+([A-Z]{2,3})\\s+(\\d{4})$"

            if let regex = try? NSRegularExpression(pattern: addressPattern, options: []),
               let match = regex.firstMatch(in: convertedAddress, options: [], range: NSRange(location: 0, length: convertedAddress.count)) {

                let streetAddress = String(convertedAddress[Range(match.range(at: 1), in: convertedAddress)!])
                let suburb = String(convertedAddress[Range(match.range(at: 2), in: convertedAddress)!])
                let state = String(convertedAddress[Range(match.range(at: 3), in: convertedAddress)!])
                let postcode = String(convertedAddress[Range(match.range(at: 4), in: convertedAddress)!])

                return "\(streetAddress), \(suburb), \(state) \(postcode)"
            }

            // 如果正则表达式匹配失败，尝试简单的空格分割
            return formatAddressWithSpaceSplit(convertedAddress)
        }

        /// 转换单元号格式
        /// Unit 1 12 KERFERD Road → 1/12 KERFERD Road
        /// U 1 12 KERFERD Road → 1/12 KERFERD Road
        /// Room 5 25 Smith Street → 5/25 Smith Street
        /// Rm 3 100 Main Road → 3/100 Main Road
        private func convertUnitNumberFormat(_ address: String) -> String {
            var converted = address

            // 匹配各种单元号格式：Unit/U/Room/Rm + 单元号 + 门牌号
            let unitPatterns = [
                // Unit 1 12 KERFERD Road → 1/12 KERFERD Road
                ("\\b(Unit|U)\\s+(\\d+)\\s+(\\d+)\\s+", "$2/$3 "),
                // Room 5 25 Smith Street → 5/25 Smith Street
                ("\\b(Room|Rm)\\s+(\\d+)\\s+(\\d+)\\s+", "$2/$3 "),
                // Apartment 2 15 Oak Ave → 2/15 Oak Ave
                ("\\b(Apartment|Apt)\\s+(\\d+)\\s+(\\d+)\\s+", "$2/$3 "),
                // Suite 10 200 Collins St → 10/200 Collins St
                ("\\b(Suite|Ste)\\s+(\\d+)\\s+(\\d+)\\s+", "$2/$3 ")
            ]

            for (pattern, replacement) in unitPatterns {
                converted = converted.replacingOccurrences(
                    of: pattern,
                    with: replacement,
                    options: [.regularExpression, .caseInsensitive]
                )
            }

            return converted
        }

        /// 使用空格分割的方式格式化地址
        private func formatAddressWithSpaceSplit(_ address: String) -> String {
            let words = address.components(separatedBy: " ").filter { !$0.isEmpty }

            var streetAddressParts: [String] = []
            var suburb: String = ""
            var state: String = ""
            var postcode: String = ""

            var i = 0

            // 收集街道地址部分（直到遇到城市名）
            while i < words.count {
                let word = words[i]

                // 检查是否是澳洲州名
                if ["VIC", "NSW", "QLD", "WA", "SA", "TAS", "ACT", "NT"].contains(word.uppercased()) {
                    state = word.uppercased()
                    i += 1
                    break
                }

                // 检查是否是邮编
                if word.range(of: "^\\d{4}$", options: .regularExpression) != nil {
                    postcode = word
                    i += 1
                    break
                }

                // 检查是否是常见城市名的开始
                if isCommonCityStart(word) {
                    // 收集城市名（可能是多个单词）
                    var cityParts: [String] = []
                    while i < words.count {
                        let cityWord = words[i]

                        // 如果遇到州名或邮编，停止收集城市名
                        if ["VIC", "NSW", "QLD", "WA", "SA", "TAS", "ACT", "NT"].contains(cityWord.uppercased()) ||
                           cityWord.range(of: "^\\d{4}$", options: .regularExpression) != nil {
                            break
                        }

                        cityParts.append(cityWord)
                        i += 1
                    }
                    suburb = cityParts.joined(separator: " ")
                    break
                }

                streetAddressParts.append(word)
                i += 1
            }

            // 继续处理剩余的州名和邮编
            while i < words.count {
                let word = words[i]

                if ["VIC", "NSW", "QLD", "WA", "SA", "TAS", "ACT", "NT"].contains(word.uppercased()) && state.isEmpty {
                    state = word.uppercased()
                } else if word.range(of: "^\\d{4}$", options: .regularExpression) != nil && postcode.isEmpty {
                    postcode = word
                }

                i += 1
            }

            // 组合最终地址
            let streetAddress = streetAddressParts.joined(separator: " ")

            if !suburb.isEmpty && !state.isEmpty && !postcode.isEmpty {
                return "\(streetAddress), \(suburb), \(state) \(postcode)"
            } else if !suburb.isEmpty && !state.isEmpty {
                return "\(streetAddress), \(suburb), \(state)"
            } else if !suburb.isEmpty {
                return "\(streetAddress), \(suburb)"
            } else {
                return streetAddress
            }
        }

        /// 检查是否是常见城市名的开始
        private func isCommonCityStart(_ word: String) -> Bool {
            let commonCityStarts = ["GLEN", "MOUNT", "SOUTH", "NORTH", "EAST", "WEST", "ST", "PORT", "MELBOURNE", "SYDNEY", "BRISBANE", "PERTH", "ADELAIDE", "HOBART", "DARWIN", "CANBERRA"]
            return commonCityStarts.contains(word.uppercased())
        }

        /// 判断是否应该跳过这一行（明显的非地址内容）
        private func shouldSkipLine(_ line: String) -> Bool {
            let skipKeywords = [
                "pty", "ltd", "limited", "company", "corp", "corporation",
                "invoice", "statement", "bill", "receipt", "tax",
                "abn", "acn", "phone", "tel", "fax", "email", "website",
                "www", "http", "commission", "securities", "investments",
                "issued", "date", "amount", "total", "gst", "inc"
            ]

            return skipKeywords.contains { keyword in
                line.contains(keyword)
            }
        }

        /// 判断是否是地址的开始行（单元号、门牌号）
        private func isAddressStartLine(_ line: String) -> Bool {
            // 单元号模式
            let unitPattern = "^(unit|u|apt|apartment|suite|ste|level|l|floor|f)\\s*\\d+"
            if line.range(of: unitPattern, options: .regularExpression) != nil {
                return true
            }

            // 门牌号开头的模式
            let houseNumberPattern = "^\\d+[a-z]?\\s+"
            if line.range(of: houseNumberPattern, options: .regularExpression) != nil {
                return true
            }

            return false
        }

        /// 判断是否是地址的延续行
        private func isAddressContinuationLine(_ line: String) -> Bool {
            // 街道名称
            let streetKeywords = ["street", "st", "road", "rd", "avenue", "ave", "lane", "ln", "drive", "dr", "court", "ct", "place", "pl", "crescent", "cres", "close", "cl", "terrace", "tce", "parade", "pde"]
            let hasStreetKeyword = streetKeywords.contains { keyword in
                line.contains(" \(keyword) ") || line.hasSuffix(" \(keyword)")
            }

            // 城市名称和邮编
            let hasPostcode = line.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil
            let australianStates = ["vic", "nsw", "qld", "wa", "sa", "tas", "act", "nt"]
            let hasState = australianStates.contains { state in
                line.contains(" \(state) ") || line.hasSuffix(" \(state)")
            }

            // 常见城市名称
            let commonCities = ["melbourne", "sydney", "brisbane", "perth", "adelaide", "hobart", "darwin", "canberra", "glen waverley", "wheelers hill", "mount waverley"]
            let hasCity = commonCities.contains { city in
                line.contains(city)
            }

            return hasStreetKeyword || hasPostcode || hasState || hasCity
        }

        /// 判断是否是完整的地址行
        private func isCompleteAddressLine(_ line: String) -> Bool {
            let hasNumber = line.rangeOfCharacter(from: .decimalDigits) != nil
            let hasStreetKeyword = isAddressContinuationLine(line)
            let hasPostcode = line.range(of: "\\b\\d{4}\\b", options: .regularExpression) != nil

            return hasNumber && hasStreetKeyword && hasPostcode && line.count >= 15 && line.count <= 150
        }

        private func extractMainAddress(_ address: String) -> String {
            var mainAddress = address

            // 移除单元号部分 (Unit 1, U 1, Apt 1, 等)
            mainAddress = mainAddress.replacingOccurrences(
                of: "^(Unit|U|Apt|Apartment|Suite|Ste)\\s*\\d+[A-Za-z]*[,/\\s]*",
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )

            // 移除楼层信息 (Level 1, L1, Floor 1, 等)
            mainAddress = mainAddress.replacingOccurrences(
                of: "^(Level|L|Floor|F)\\s*\\d+[A-Za-z]*[,/\\s]*",
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )

            return mainAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        }
    }
}

// MARK: - 兼容性包装器
/// 兼容原有的AddressScannerSheet接口
struct AddressScannerSheet: View {
    var onAddressConfirmed: ((String, CLLocationCoordinate2D) -> Void)
    var currentRoutePoints: [DeliveryPoint]? = nil

    init(onAddressConfirmed: @escaping (String, CLLocationCoordinate2D) -> Void, currentRoutePoints: [DeliveryPoint]? = nil) {
        self.onAddressConfirmed = onAddressConfirmed
        self.currentRoutePoints = currentRoutePoints
    }

    var body: some View {
        if #available(iOS 16.0, *) {
            SimpleAddressScannerSheet(onAddressConfirmed: onAddressConfirmed)
        } else {
            fallbackView
        }
    }

    private var fallbackView: some View {
        VStack {
            Text("地址扫描需要 iOS 16 或更高版本")
                .font(.headline)
                .padding()

            Text("请升级您的设备系统版本以使用此功能")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding()
        }
    }
}

// Preview - 禁用预览避免编译器超时
#if DEBUG
struct AddressScannerView_Previews: PreviewProvider {
    static var previews: some View {
        Text("Address Scanner - Preview Disabled")
            .foregroundColor(.secondary)
    }
}
#endif