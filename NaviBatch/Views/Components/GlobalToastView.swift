//
//  GlobalToastView.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-05-29.
//

import SwiftUI

struct GlobalToastView: View {
    @State private var toastMessage: String = ""
    @State private var showToast: Bool = false
    @State private var toastTimer: Timer?

    var body: some View {
        ZStack {
            // 透明背景，不影响其他视图
            Color.clear

            // Toast 消息
            if showToast {
                VStack {
                    Spacer()

                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(.white)

                            Text(toastMessage.components(separatedBy: "\n").first ?? toastMessage)
                                .foregroundColor(.white)
                                .font(.system(size: 14, weight: .medium))
                                .multilineTextAlignment(.leading)
                        }

                        // 如果有多行内容，显示详细信息
                        let lines = toastMessage.components(separatedBy: "\n")
                        if lines.count > 1 {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(Array(lines.dropFirst().enumerated()), id: \.offset) { _, line in
                                    if !line.isEmpty {
                                        Text(line)
                                            .foregroundColor(.white.opacity(0.9))
                                            .font(.system(size: 12, weight: .regular))
                                            .multilineTextAlignment(.leading)
                                    }
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.black.opacity(0.85))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .animation(.easeInOut(duration: 0.3), value: showToast)
                }
                .zIndex(9999)
            }
        }
        .onAppear {
            setupToastListener()
        }
        .onDisappear {
            toastTimer?.invalidate()
            NotificationCenter.default.removeObserver(self)
        }
    }

    private func setupToastListener() {
        NotificationCenter.default.addObserver(
            forName: Notification.Name("ShowToast"),
            object: nil,
            queue: .main
        ) { notification in
            if let message = notification.object as? String {
                showToastMessage(message)
            }
        }
    }

    private func showToastMessage(_ message: String) {
        // 取消之前的定时器
        toastTimer?.invalidate()

        // 设置新消息
        toastMessage = message

        // 显示Toast
        withAnimation(.easeInOut(duration: 0.3)) {
            showToast = true
        }

        // 3秒后自动隐藏
        toastTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                showToast = false
            }
        }
    }
}

#Preview("GlobalToastView") {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        GlobalToastView()
    }
}
