import SwiftUI

/// 地址处理速度优化等待视图 - 简洁友好版本
struct RateLimitWarningView: View {
    let isVisible: Bool
    let onDismiss: () -> Void
    let waitTime: Int
    let processedCount: Int
    let totalCount: Int

    @State private var timer: Timer?
    @State private var countdown: Int

    init(isVisible: Bool, onDismiss: @escaping () -> Void, waitTime: Int = 60, processedCount: Int = 0, totalCount: Int = 0) {
        self.isVisible = isVisible
        self.onDismiss = onDismiss
        self.waitTime = waitTime
        self.processedCount = processedCount
        self.totalCount = totalCount
        self._countdown = State(initialValue: waitTime)
    }

    var body: some View {
        if isVisible {
            VStack(spacing: 16) {
                // 图标
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 40))
                    .foregroundColor(.orange)

                // 标题
                Text("🚦 Apple Maps API速率优化")
                    .font(.headline)
                    .fontWeight(.semibold)

                // 说明
                Text("为保护Apple Maps服务，正在智能调节处理速度")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                // 进度信息（如果有）
                if totalCount > 0 {
                    VStack(spacing: 8) {
                        ProgressView(value: Double(processedCount), total: Double(totalCount))
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))

                        Text("已处理: \(processedCount)/\(totalCount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // 倒计时
                Text("恢复处理: \(countdown)秒")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
                    .monospacedDigit()

                // 提示
                Text("💡 建议分批处理大量地址，每批不超过10个")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(24)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(radius: 8)
            .onAppear {
                startCountdown()
            }
            .onDisappear {
                stopCountdown()
            }
        }
    }

    private func startCountdown() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if countdown > 0 {
                countdown -= 1
            } else {
                stopCountdown()
                onDismiss()
            }
        }
    }

    private func stopCountdown() {
        timer?.invalidate()
        timer = nil
    }
}

/// 速率限制状态管理器
@MainActor
class RateLimitStatusManager: ObservableObject {
    @Published var showWarning = false
    @Published var isRateLimited = false

    private var checkTimer: Timer?

    init() {
        startMonitoring()
    }

    deinit {
        checkTimer?.invalidate()
        checkTimer = nil
    }

    /// 开始监控速率限制状态
    func startMonitoring() {
        checkTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task {
                await self?.checkRateLimitStatus()
            }
        }
    }

    /// 停止监控
    func stopMonitoring() {
        checkTimer?.invalidate()
        checkTimer = nil
    }

    /// 检查当前速率限制状态
    private func checkRateLimitStatus() async {
        let status = await GlobalGeocodingRateLimiter.shared.getStatus()

        await MainActor.run {
            let wasRateLimited = isRateLimited
            isRateLimited = status.isNearLimit

            // 如果从正常状态变为接近限制，显示警告
            if !wasRateLimited && isRateLimited {
                showWarning = true
            }
            // 如果从限制状态恢复正常，自动关闭警告
            else if wasRateLimited && !isRateLimited {
                showWarning = false
            }
        }
    }

    /// 手动触发速率限制警告
    func triggerRateLimitWarning() {
        showWarning = true
        isRateLimited = true
    }

    /// 关闭警告
    func dismissWarning() {
        showWarning = false
    }
}

// MARK: - 预览

#Preview("RateLimitWarningView") {
    VStack {
        Spacer()

        RateLimitWarningView(
            isVisible: true,
            onDismiss: {}
        )

        Spacer()
    }
    .background(Color(.systemGroupedBackground))
}
