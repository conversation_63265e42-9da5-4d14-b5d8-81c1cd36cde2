import SwiftUI
import CoreLocation
import Combine

// 智能地址输入组件
struct SmartAddressInputView: View {
    @Binding var address: String
    let placeholder: String
    let onAddressValidated: ((AddressValidationResult) -> Void)?
    
    @State private var isValidating = false
    @State private var validationResult: AddressValidationResult?
    @State private var showConfirmationSheet = false
    @State private var validationTimer: Timer?
    @State private var cancellables = Set<AnyCancellable>()
    
    private let geocodingService = GeocodingService.shared
    
    init(
        address: Binding<String>,
        placeholder: String = "输入地址",
        onAddressValidated: ((AddressValidationResult) -> Void)? = nil
    ) {
        self._address = address
        self.placeholder = placeholder
        self.onAddressValidated = onAddressValidated
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 地址输入框
            HStack {
                TextField(placeholder, text: $address)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.words)
                    .onChange(of: address) { _, newValue in
                        scheduleValidation(for: newValue)
                    }
                
                // 验证状态指示器
                if isValidating {
                    ProgressView()
                        .scaleEffect(0.8)
                } else if let result = validationResult {
                    validationStatusIcon(for: result)
                }
            }
            
            // 验证结果提示
            if let result = validationResult {
                validationResultView(result)
            }
        }
        .sheet(isPresented: $showConfirmationSheet) {
            if let result = validationResult {
                AddressConfirmationSheet(validationResult: result) { useOriginal, finalAddress in
                    address = finalAddress
                    onAddressValidated?(result)
                }
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func scheduleValidation(for address: String) {
        // 取消之前的定时器
        validationTimer?.invalidate()
        
        // 清除之前的结果
        validationResult = nil
        
        // 如果地址为空，不进行验证
        guard !address.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        // 设置新的定时器，延迟1秒后验证
        validationTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { _ in
            validateAddress(address)
        }
    }
    
    private func validateAddress(_ address: String) {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedAddress.isEmpty else { return }
        
        isValidating = true
        
        geocodingService.validateAddress(trimmedAddress)
            .receive(on: DispatchQueue.main)
            .sink { result in
                isValidating = false
                validationResult = result
                
                // 如果地址被修正且置信度较低，自动显示确认界面
                if result.isModified && (result.confidence == .low || result.confidence == .veryLow) {
                    showConfirmationSheet = true
                } else {
                    // 自动接受高置信度的结果
                    onAddressValidated?(result)

                    // 🚨 检查并提示问题地址（如果验证失败）
                    if !result.isValid {
                        Task {
                            // 延迟一下让地址处理完成
                            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                            await MainActor.run {
                                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "智能地址输入")
                            }
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func validationStatusIcon(for result: AddressValidationResult) -> some View {
        Button {
            showConfirmationSheet = true
        } label: {
            Image(systemName: iconName(for: result.confidence))
                .foregroundColor(iconColor(for: result.confidence))
                .font(.title3)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func validationResultView(_ result: AddressValidationResult) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            if result.isModified {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    
                    Text("地址已自动修正")
                        .font(.caption)
                        .foregroundColor(.orange)
                    
                    Spacer()
                    
                    Button("查看详情") {
                        showConfirmationSheet = true
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                
                if !result.geocodedAddress.isEmpty {
                    Text("修正为: \(result.geocodedAddress)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            } else if result.confidence == .high {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    Text("地址验证通过")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
            
            // 显示警告信息
            if let warningMessage = result.warningMessage {
                Text(warningMessage)
                    .font(.caption)
                    .foregroundColor(.orange)
                    .lineLimit(3)
            }
        }
        .padding(.horizontal, 4)
    }
    
    private func iconName(for confidence: AddressConfidence) -> String {
        switch confidence {
        case .high:
            return "checkmark.circle.fill"
        case .medium:
            return "exclamationmark.triangle.fill"
        case .low:
            return "xmark.circle.fill"
        case .veryLow:
            return "questionmark.circle.fill"
        }
    }
    
    private func iconColor(for confidence: AddressConfidence) -> Color {
        switch confidence {
        case .high:
            return .green
        case .medium:
            return .orange
        case .low:
            return .red
        case .veryLow:
            return .gray
        }
    }
}

// MARK: - 预览
struct SmartAddressInputView_Previews: PreviewProvider {
    @State static var address = "84 Kin, Gen Waverley, VIC 3150"
    
    static var previews: some View {
        VStack(spacing: 20) {
            SmartAddressInputView(
                address: $address,
                placeholder: "输入地址"
            ) { result in
                print("地址验证结果: \(result)")
            }
            
            Spacer()
        }
        .padding()
    }
}
