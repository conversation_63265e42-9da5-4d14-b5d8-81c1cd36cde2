import SwiftUI
import UIKit

/// 应用版本更新提示视图 - NaviBatch 专用设计
struct AppUpdatePromptView: View {
    @ObservedObject private var updateService = AppUpdateService.shared
    @Environment(\.dismiss) private var dismiss

    let updateInfo: AppUpdateInfo

    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景不关闭（强制更新时）
                    if !updateInfo.forceUpdate {
                        updateService.remindLater()
                    }
                }

            // 主要内容
            VStack(spacing: 0) {
                // 顶部图标区域
                topIconSection

                // 内容区域
                contentSection

                // 按钮区域
                buttonSection
            }
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
            .padding(.horizontal, 40)
        }
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: updateService.showUpdatePrompt)
    }

    // MARK: - 顶部图标区域
    private var topIconSection: some View {
        VStack(spacing: 12) {
            // NaviBatch 应用图标
            if let appIcon = Bundle.main.icon {
                Image(uiImage: appIcon)
                    .resizable()
                    .frame(width: 80, height: 80)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
            } else {
                // 备用图标
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [Color.blue, Color.cyan],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "map.fill")
                            .font(.system(size: 40, weight: .medium))
                            .foregroundColor(.white)
                    )
                    .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
            }

            // 标题
            Text("app_update_title".localized)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            // 副标题
            Text("app_update_subtitle".localized)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 32)
        .padding(.horizontal, 24)
    }

    // MARK: - 内容区域
    private var contentSection: some View {
        VStack(spacing: 16) {
            // 版本信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("current_version".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(updateInfo.currentVersion)
                        .font(.headline)
                        .foregroundColor(.primary)
                }

                Spacer()

                Image(systemName: "arrow.right")
                    .font(.title3)
                    .foregroundColor(.blue)

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("latest_version".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(updateInfo.latestVersion)
                        .font(.headline)
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(Color(.systemGray6))
            .cornerRadius(12)

            // 更新内容
            VStack(alignment: .leading, spacing: 12) {
                Text("update_content".localized)
                    .font(.headline)
                    .foregroundColor(.primary)

                VStack(alignment: .leading, spacing: 8) {
                    HStack(alignment: .top, spacing: 8) {
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 4, height: 4)
                            .padding(.top, 8)

                        Text("app_update_notes".localized)
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .fixedSize(horizontal: false, vertical: true)

                        Spacer()
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 调试信息（仅调试模式显示）
            if updateInfo.debugMode == true {
                debugInfoSection
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
    }

    // MARK: - 调试信息区域
    private var debugInfoSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("🧪 调试信息")
                .font(.caption)
                .foregroundColor(.orange)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 4) {
                Text("• 这是测试模式的更新提示")
                Text("• 实际版本: \(AppEnvironment.appVersion)")
                Text("• 模拟最新版本: \(updateInfo.latestVersion)")
            }
            .font(.caption2)
            .foregroundColor(.secondary)
        }
        .padding(12)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }

    // MARK: - 按钮区域
    private var buttonSection: some View {
        VStack(spacing: 12) {
            // 立即更新按钮
            Button(action: {
                updateService.updateNow()
            }) {
                HStack {
                    Image(systemName: "arrow.down.circle.fill")
                        .font(.title3)
                    Text("update_now".localized + " (\(updateInfo.latestVersion))")
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    LinearGradient(
                        colors: [Color.blue, Color.blue.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())

            // 底部按钮组
            if !updateInfo.forceUpdate {
                HStack(spacing: 16) {
                    // 稍后提醒
                    Button("remind_later".localized) {
                        updateService.remindLater()
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)

                    // 跳过此版本
                    Button("skip_version".localized) {
                        updateService.skipThisVersion()
                    }
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color(.systemGray5))
                    .cornerRadius(10)
                }
            } else {
                // 强制更新提示
                Text("force_update_message".localized)
                    .font(.caption)
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)
                    .padding(.top, 8)
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 32)
    }
}

// MARK: - 预览
#Preview("正常更新") {
    AppUpdatePromptView(
        updateInfo: AppUpdateInfo(
            hasUpdate: true,
            currentVersion: "1.0.7",
            latestVersion: "1.0.8",
            forceUpdate: false,
            updateInfo: AppUpdateInfo.UpdateDetails(
                latestVersion: "1.0.8",
                forceUpdate: false,
                updateTitle: "NaviBatch",
                updateSubtitle: "Bug fixes and improvements",
                updateNotes: [
                    "Bug fixes and improvements"
                ],
                appStoreURL: "https://apps.apple.com/app/navibatch/id6738046894",
                releaseDate: "2024-12-27"
            ),
            debugMode: false
        )
    )
}

#Preview("强制更新") {
    AppUpdatePromptView(
        updateInfo: AppUpdateInfo(
            hasUpdate: true,
            currentVersion: "1.0.7",
            latestVersion: "1.0.8",
            forceUpdate: true,
            updateInfo: AppUpdateInfo.UpdateDetails(
                latestVersion: "1.0.8",
                forceUpdate: true,
                updateTitle: "NaviBatch",
                updateSubtitle: "Bug fixes and improvements",
                updateNotes: [
                    "Bug fixes and improvements"
                ],
                appStoreURL: "https://apps.apple.com/app/navibatch/id6738046894",
                releaseDate: "2024-12-27"
            ),
            debugMode: false
        )
    )
}

#Preview("调试模式") {
    AppUpdatePromptView(
        updateInfo: AppUpdateInfo(
            hasUpdate: true,
            currentVersion: "1.0.3",
            latestVersion: "1.0.4",
            forceUpdate: false,
            updateInfo: AppUpdateInfo.UpdateDetails(
                latestVersion: "1.0.4",
                forceUpdate: false,
                updateTitle: "NaviBatch",
                updateSubtitle: "Bug fixes and improvements",
                updateNotes: [
                    "Bug fixes and improvements"
                ],
                appStoreURL: "https://apps.apple.com/app/navibatch/id6738046894",
                releaseDate: "2024-12-27"
            ),
            debugMode: true
        )
    )
}

// MARK: - Bundle Extension for App Icon
extension Bundle {
    var icon: UIImage? {
        if let icons = infoDictionary?["CFBundleIcons"] as? [String: Any],
           let primaryIcon = icons["CFBundlePrimaryIcon"] as? [String: Any],
           let iconFiles = primaryIcon["CFBundleIconFiles"] as? [String],
           let lastIcon = iconFiles.last {
            return UIImage(named: lastIcon)
        }
        return nil
    }
}