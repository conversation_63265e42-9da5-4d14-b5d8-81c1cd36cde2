import SwiftUI
import MapKit
import SwiftData
import os.log
import CoreLocation
import AVFoundation
import Combine



/// 统一的地址添加表单
/// 这是应用中地址输入的主要组件，用于所有地址输入场景
struct SimpleAddressSheet: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext

    @State private var address: String = ""
    @State private var selectedCoordinate: CLLocationCoordinate2D? = nil
    @State private var isAddingAddress: Bool = false
    @State private var errorMessage: String? = nil
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var searchCompleter = MKLocalSearchCompleter()
    @State private var completerDelegate: SimpleAddressCompleterDelegate? = nil
    @State private var isFavorite: Bool = false
    @State private var isShowingScanner: Bool = false
    @State private var scannedCode: String = ""
    @State private var showingSavedAddresses: Bool = false
    @Query(sort: \SavedAddress.createdAt, order: .reverse) private var savedAddresses: [SavedAddress]
    @State private var showingImageRecognizer: Bool = false // 图片识别器状态
    @State private var showingBatchInput: Bool = false // 批量贴上状态
    @State private var showingFileImport: Bool = false // 文件导入状态
    @State private var showingWebDownload: Bool = false // 线上下载状态
    @State private var companyName: String = "" // 公司名称
    @State private var url: String = "" // URL
    @State private var navigationTitle: String = "address_title".localized // 导航标题
    @State private var pointToEditPublisher = PassthroughSubject<DeliveryPoint?, Never>() // 发布者，用于监听pointToEdit变化

    var onAddressAdded: ((SavedAddress) -> Void)? = nil
    var addressPointType: AddressPointType? = nil
    var saveToAddressBook: Bool = false
    @Binding var pointToEdit: DeliveryPoint?

    // 智能按钮标题计算属性
    private var smartButtonTitle: String {
        if address.isEmpty {
            return "close".localized
        } else {
            return "save".localized
        }
    }

    // 初始化方法，允许使用非绑定的pointToEdit
    init(onAddressAdded: ((SavedAddress) -> Void)? = nil,
         addressPointType: AddressPointType? = nil,
         saveToAddressBook: Bool = false,
         pointToEdit: DeliveryPoint? = nil) {
        self.onAddressAdded = onAddressAdded
        self.addressPointType = addressPointType
        self.saveToAddressBook = saveToAddressBook
        self._pointToEdit = .constant(pointToEdit)
        logInfo("SimpleAddressSheet - 使用非绑定初始化，pointToEdit=\(pointToEdit?.primaryAddress ?? "nil")")
    }

    // 初始化方法，使用绑定的pointToEdit
    init(onAddressAdded: ((SavedAddress) -> Void)? = nil,
         addressPointType: AddressPointType? = nil,
         saveToAddressBook: Bool = false,
         pointToEdit: Binding<DeliveryPoint?>) {
        self.onAddressAdded = onAddressAdded
        self.addressPointType = addressPointType
        self.saveToAddressBook = saveToAddressBook
        self._pointToEdit = pointToEdit
        logInfo("SimpleAddressSheet - 使用绑定初始化，pointToEdit=\(pointToEdit.wrappedValue?.primaryAddress ?? "nil")")
    }

    // 搜索栏组件 - 符合Apple Design Guidelines
    private var searchBar: some View {
        VStack(spacing: 8) {
            // 输入框 - 图标在内部，输入时消失
            HStack(spacing: 8) {
                // 搜索图标 - 始终显示，使用自适应颜色
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.adaptiveSecondaryIcon)  // 🎯 Dark Mode 优化：使用自适应图标颜色
                    .font(.system(size: 16))

                // 输入框 - 使用自适应文字颜色
                TextField("enter_and_select_address".localized, text: $address)
                    .autocorrectionDisabled()
                    .font(.system(size: 16))
                    .foregroundColor(.adaptivePrimaryText)  // 🎯 Dark Mode 优化：使用自适应文字颜色
                    .onChange(of: address) { _, newValue in
                        if !newValue.isEmpty {
                            // 🎯 编辑模式下，如果地址已有有效坐标，跳过搜索
                            if let editPoint = pointToEdit,
                               editPoint.latitude != 0 && editPoint.longitude != 0,
                               newValue == editPoint.primaryAddress {
                                logInfo("SimpleAddressSheet - 编辑模式下地址未变化且有有效坐标，跳过搜索: \(newValue)")
                                searchResults = []
                                return
                            }

                            // 🧹 清理地址元数据，只保留纯净地址用于搜索
                            let cleanAddress = cleanAddressForSearch(newValue)
                            logInfo("🧹 地址搜索清理: '\(newValue)' -> '\(cleanAddress)'")

                            // 🎯 检测是否是完整地址格式，如果是则直接搜索
                            if isCompleteAddress(cleanAddress) {
                                logInfo("SimpleAddressSheet - 检测到完整地址，直接搜索: \(cleanAddress)")
                                performDirectAddressSearch(cleanAddress)
                            } else {
                                // 使用语言环境感知搜索
                                performLocaleAwareSearch(cleanAddress)
                            }
                        } else {
                            searchResults = []
                        }
                    }

                // 右侧图标组 - 输入时隐藏，空白时显示
                if address.isEmpty {
                    HStack(spacing: 12) {
                        // 当前位置按钮
                        Button(action: {
                            useCurrentLocation()
                        }) {
                            Image(systemName: "mappin.and.ellipse")
                                .foregroundColor(.adaptiveSecondaryIcon)  // 🎯 Dark Mode 优化：使用自适应图标颜色
                                .font(.system(size: 16))
                        }

                        // 扫描按钮 - 使用扫描图标而非相机图标
                        Button(action: {
                            isShowingScanner = true
                        }) {
                            Image(systemName: "qrcode.viewfinder")
                                .foregroundColor(.adaptiveSecondaryIcon)  // 🎯 Dark Mode 优化：使用自适应图标颜色
                                .font(.system(size: 16))
                        }
                    }
                } else {
                    // 清除按钮 - 输入时显示
                    Button(action: {
                        address = ""
                        searchResults = []
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.adaptiveSecondaryIcon)  // 🎯 Dark Mode 优化：使用自适应图标颜色
                            .font(.system(size: 16))
                    }
                }
            }
            .padding(.horizontal, 12)  // 与下方按钮保持一致的padding
            .padding(.vertical, 10)
            .background(Color.adaptiveInputBackground)  // 🎯 Dark Mode 优化：使用自适应背景
            .cornerRadius(15)  // 与按钮保持一致的圆角
            .overlay(
                RoundedRectangle(cornerRadius: 15)
                    .stroke(Color.adaptiveBorder, lineWidth: 1.0)  // 🎯 Dark Mode 优化：使用自适应边框颜色
            )

            // 🧪 测试按钮 - 独立显示在输入框下方
            if !address.isEmpty {
                Button(action: {
                    print("🚨🚨🚨 测试按钮被点击了！🚨🚨🚨")
                    logInfo("🚨🚨🚨 测试按钮被点击了！🚨🚨🚨")
                    logInfo("🧪 测试按钮点击 - 开始测试地址搜索")
                    logInfo("🧪 测试地址: '\(address)'")
                    print("🧪 测试地址: '\(address)'")

                    let cleanAddress = cleanAddressForSearch(address)
                    logInfo("🧪 清理后地址: '\(cleanAddress)'")
                    print("🧪 清理后地址: '\(cleanAddress)'")

                    // 🧪 使用语言环境感知搜索服务进行测试
                    print("🧪 使用LocaleAwareAddressSearchService进行测试搜索")
                    logInfo("🧪 使用LocaleAwareAddressSearchService进行测试搜索")

                    LocaleAwareAddressSearchService.shared.performSearch(query: cleanAddress) { results in
                        DispatchQueue.main.async {
                            print("🧪 LocaleAware搜索完成，结果数量: \(results.count)")
                            logInfo("🧪 LocaleAware搜索完成，结果数量: \(results.count)")

                            for (index, result) in results.enumerated() {
                                print("🧪 LocaleAware结果[\(index)]: '\(result.title)' - '\(result.subtitle)'")
                                logInfo("🧪 LocaleAware结果[\(index)]: '\(result.title)' - '\(result.subtitle)'")
                            }

                            // 更新搜索结果到UI
                            self.searchResults = results
                            print("🧪 LocaleAware搜索结果已更新到UI")
                            logInfo("🧪 LocaleAware搜索结果已更新到UI")
                        }
                    }

                    logInfo("🧪 测试搜索已触发，请观察后续日志")
                    print("🧪 测试搜索已触发，请观察后续日志")
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "flask.fill")
                            .font(.system(size: 18, weight: .bold))
                        Text("🧪 测试地址搜索")
                            .font(.system(size: 16, weight: .bold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                    .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
                }
                .padding(.top, 12)
                .scaleEffect(1.0)  // 确保按钮可见
                .opacity(1.0)      // 确保按钮不透明
                .buttonStyle(PlainButtonStyle())  // 确保按钮样式正确
            }
        }
        .padding(.horizontal, 12)  // 与Current Location、Address Book按钮保持一致
        .padding(.vertical, 4)
    }

    // 搜索信息视图
    private var searchInfoView: some View {
        Group {
            if !address.isEmpty {
                HStack {
                    Text(String(format: "current_search_text".localized, address))
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    Spacer()
                    Text(String(format: "search_results_count".localized, searchResults.count))
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
            }
        }
    }

    // 搜索结果列表视图
    private var searchResultsView: some View {
        Group {
            if !searchResults.isEmpty {
                LazyVStack(spacing: 0) {
                    ForEach(searchResults, id: \.self) { result in
                        Button(action: {
                            selectSearchResult(result)
                        }) {
                            HStack(spacing: 12) {
                                // 地址图标
                                Image(systemName: "mappin.circle.fill")
                                    .foregroundColor(.adaptiveError)
                                    .font(.system(size: 22))

                                VStack(alignment: .leading, spacing: 4) {
                                    Text(result.title)
                                        .foregroundColor(.adaptivePrimaryText)
                                        .font(.system(size: 16, weight: .medium))
                                        .lineLimit(1)

                                    if !result.subtitle.isEmpty {
                                        Text(result.subtitle)
                                            .foregroundColor(.adaptiveSecondaryText)
                                            .font(.system(size: 14))
                                            .lineLimit(1)
                                    }
                                }

                                Spacer()
                            }
                            .padding(.vertical, 10)
                            .padding(.horizontal, 12)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .buttonStyle(PlainButtonStyle())

                        Divider()
                            .background(Color.adaptiveSeparator)
                            .padding(.leading, 46)
                    }
                }
                .background(Color.adaptiveBackground)
            } else if !address.isEmpty && !isAddingAddress {
                VStack {
                    Text("no_matching_addresses".localized)
                        .foregroundColor(.adaptiveSecondaryText)
                        .font(.system(size: 16))
                        .padding(.top, 20)
                }
            }
        }
    }

    // 获取导航标题
    private func getNavigationTitle() -> String {
        // 直接根据pointToEdit判断，不依赖address状态
        // 这样即使address尚未填充，也能正确判断操作类型
        if let point = pointToEdit {
            // 检查是否是临时点
            let isTempPoint = point.primaryAddress == "add_start_point".localized ||
                              point.primaryAddress == "add_new_address".localized ||
                              point.primaryAddress == "add_end_point".localized

            // 记录日志，帮助调试
            logInfo("SimpleAddressSheet - getNavigationTitle: 有编辑点, 临时点=\(isTempPoint), 点地址='\(point.primaryAddress)', 点ID=\(point.id.uuidString)")

            if isTempPoint {
                // 如果是临时点，根据类型显示不同的标题
                if point.isStartPoint {
                    logInfo("SimpleAddressSheet - 设置导航标题: 添加起点")
                    return "add_start_point".localized
                } else if point.isEndPoint {
                    logInfo("SimpleAddressSheet - 设置导航标题: 添加终点")
                    return "add_end_point".localized
                } else {
                    logInfo("SimpleAddressSheet - 设置导航标题: 添加地址")
                    return "add_address".localized
                }
            } else {
                // 如果不是临时点，则认为是编辑操作
                logInfo("SimpleAddressSheet - 设置导航标题: 编辑地址 '\(point.primaryAddress)'")
                return "edit_address".localized
            }
        } else if let type = addressPointType {
            // 如果pointToEdit为nil但有addressPointType，根据类型设置标题
            if type == .start {
                logInfo("SimpleAddressSheet - 设置导航标题: 添加起点 (根据addressPointType)")
                return "add_start_point".localized
            } else if type == .end {
                logInfo("SimpleAddressSheet - 设置导航标题: 添加终点 (根据addressPointType)")
                return "add_end_point".localized
            } else {
                logInfo("SimpleAddressSheet - 设置导航标题: 添加地址 (根据addressPointType)")
                return "add_address".localized
            }
        } else {
            // 如果pointToEdit和addressPointType都为nil，则认为是添加操作
            logInfo("SimpleAddressSheet - 设置导航标题: 添加地址 (pointToEdit和addressPointType都为nil)")
            return "add_address".localized
        }
    }

    // 已选择地址信息视图
    private var selectedAddressView: some View {
        Group {
            if let coordinate = selectedCoordinate, searchResults.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "location.circle.fill")
                            .foregroundColor(.adaptiveSuccess)
                            .font(.system(size: 16))

                        Text("selected_coordinates".localized)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.adaptivePrimaryText)
                    }
                    .padding(.horizontal, 12)
                    .padding(.top, 8)

                    Text("\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude))")
                        .font(.system(size: 14))
                        .foregroundColor(.adaptiveSecondaryText)
                        .padding(.horizontal, 12)

                    // 公司名称输入
                    HStack {
                        Image(systemName: "building.2.fill")
                            .foregroundColor(.adaptivePrimaryIcon)
                            .font(.system(size: 16))

                        TextField("company_name_optional".localized, text: $companyName)
                            .font(.system(size: 14))
                    }
                    .padding(.horizontal, 12)

                    // URL输入
                    HStack {
                        Image(systemName: "link")
                            .foregroundColor(.adaptiveSuccess)
                            .font(.system(size: 16))

                        TextField("url_optional".localized, text: $url)
                            .font(.system(size: 14))
                            .keyboardType(.URL)
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                    }
                    .padding(.horizontal, 12)

                    Toggle("favorite_address".localized, isOn: $isFavorite)
                        .toggleStyle(SwitchToggleStyle(tint: .adaptivePrimaryButton))
                        .font(.system(size: 14))
                        .padding(.horizontal, 12)

                    // 如果是添加起点或终点，显示设为起终点选项
                    if addressPointType == .start || addressPointType == .end {
                        Toggle("set_as_start_and_end".localized, isOn: Binding(
                            get: {
                                // 检查是否同时是起点和终点
                                return addressPointType == .start && pointToEdit?.isEndPoint == true ||
                                       addressPointType == .end && pointToEdit?.isStartPoint == true
                            },
                            set: { newValue in
                                if newValue {
                                    // 如果开启，将点设为起点和终点
                                    if addressPointType == .start {
                                        pointToEdit?.isEndPoint = true
                                    } else if addressPointType == .end {
                                        pointToEdit?.isStartPoint = true
                                    }
                                } else {
                                    // 如果关闭，根据当前类型保留一种属性
                                    if addressPointType == .start {
                                        pointToEdit?.isEndPoint = false
                                    } else if addressPointType == .end {
                                        pointToEdit?.isStartPoint = false
                                    }
                                }
                            }
                        ))
                        .toggleStyle(SwitchToggleStyle(tint: .adaptiveWarning))
                        .font(.system(size: 14))
                        .padding(.horizontal, 12)
                        .padding(.bottom, 8)
                    } else {
                        Spacer().frame(height: 8)
                    }
                }
                .adaptiveCardStyle()
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
            }
        }
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 搜索栏
                    searchBar
                        .padding(.bottom, 8)

                    // 功能按钮区域 - 移除Current Location，增加按钮高度
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            // 图片识别按钮 - 第一个位置
                            Button(action: {
                                showingImageRecognizer = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "photo")
                                        .font(.system(size: 16))
                                    Text("image_recognition".localized)
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .padding(.vertical, 10)
                                .padding(.horizontal, 12)
                                .adaptiveFunctionButtonStyle()
                            }

                            // 地址簿标签按钮 - 增加高度
                            Button(action: {
                                showingSavedAddresses = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "book")
                                        .font(.system(size: 16))
                                    Text("address_book".localized)
                                        .font(.system(size: 16, weight: .medium))
                                    if !savedAddresses.isEmpty {
                                        Text("\(savedAddresses.count)")
                                            .font(.system(size: 14, weight: .medium))
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(Color.black.opacity(0.1))
                                            .clipShape(Circle())
                                    }
                                }
                                .padding(.vertical, 10)  // 增加垂直padding，与输入框高度一致
                                .padding(.horizontal, 12)
                                .adaptiveFunctionButtonStyle()
                            }

                            // 批量贴上按钮 - 增加高度
                            Button(action: {
                                logInfo("SimpleAddressSheet - 点击批量贴上")
                                showingBatchInput = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "doc.on.clipboard")
                                        .font(.system(size: 16))
                                    Text("batch_paste".localized)
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .padding(.vertical, 10)  // 增加垂直padding
                                .padding(.horizontal, 12)
                                .adaptiveFunctionButtonStyle()
                            }

                            // 文件导入按钮 - 增加高度
                            Button(action: {
                                logInfo("SimpleAddressSheet - 点击文件导入")
                                showingFileImport = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "arrow.down.doc")
                                        .font(.system(size: 16))
                                    Text("file_import".localized)
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .padding(.vertical, 10)  // 增加垂直padding
                                .padding(.horizontal, 12)
                                .adaptiveFunctionButtonStyle()
                            }

                            // 线上下载按钮 - 增加高度
                            Button(action: {
                                logInfo("SimpleAddressSheet - 点击线上下载")
                                showingWebDownload = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "arrow.down.circle")
                                        .font(.system(size: 16))
                                    Text("web_download".localized)
                                        .font(.system(size: 16, weight: .medium))
                                }
                                .padding(.vertical, 10)  // 增加垂直padding
                                .padding(.horizontal, 12)
                                .adaptiveFunctionButtonStyle()
                            }

                            // 🎯 智能简化按钮（仅在编辑模式且检测到问题时显示）
                            if pointToEdit != nil && !address.isEmpty {
                                if AddressSimplifier.detectRecognitionIssues(address) != nil {
                                    Button(action: {
                                        logInfo("SimpleAddressSheet - 点击智能简化地址")
                                        let simplified = AddressSimplifier.smartSimplify(address)
                                        if simplified != address {
                                            address = simplified
                                            selectedCoordinate = nil
                                            searchResults = []
                                            // 触发新的搜索
                                            performLocaleAwareSearch(simplified)
                                            logInfo("SimpleAddressSheet - 地址已简化: \(address) -> \(simplified)")
                                        }
                                    }) {
                                        HStack(spacing: 4) {
                                            Image(systemName: "wand.and.stars")
                                                .font(.system(size: 16))
                                            Text("simplify_address".localized)
                                                .font(.system(size: 16, weight: .medium))
                                        }
                                        .padding(.vertical, 10)
                                        .padding(.horizontal, 12)
                                        .background(Color.adaptiveWarning.opacity(0.15))
                                        .foregroundColor(.adaptiveWarning)
                                        .cornerRadius(15)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 12)
                    }
                    .padding(.vertical, 8)

                    // 搜索信息
                    searchInfoView
                        .padding(.bottom, 4)

                    // 搜索结果列表
                    searchResultsView
                        .padding(.bottom, 4)

                    // 已选择地址信息
                    selectedAddressView
                        .padding(.bottom, 8)

                    // 底部安全区域
                    Color.clear
                        .frame(height: 20)
                }
            }
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 左侧：取消按钮（在所有模式都显示）
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                    .foregroundColor(.adaptivePrimaryText)  // 🎯 Dark Mode 优化：使用自适应文字颜色
                }

                // 右侧：智能按钮（根据状态显示Save或Close）
                ToolbarItem(placement: .navigationBarTrailing) {
                    if pointToEdit != nil {
                        // 编辑模式：保存按钮
                        Button("save".localized) {
                            logInfo("SimpleAddressSheet - 编辑模式保存按钮被点击")
                            if let onAddressAdded = onAddressAdded {
                                let addressToUse = address
                                let coordinateToUse = selectedCoordinate ?? CLLocationCoordinate2D(latitude: 0, longitude: 0)
                                let fakeSaved = SavedAddress(address: addressToUse, coordinate: coordinateToUse)
                                logInfo("SimpleAddressSheet - 编辑模式调用回调: \(addressToUse)")
                                onAddressAdded(fakeSaved)
                            }
                            dismiss()
                        }
                        .disabled(address.isEmpty || selectedCoordinate == nil)
                        .foregroundColor(address.isEmpty || selectedCoordinate == nil ? .adaptiveSecondaryText : .adaptivePrimaryText)
                    } else {
                        // 添加模式：智能按钮
                        Button(smartButtonTitle) {
                            logInfo("SimpleAddressSheet - 智能按钮被点击: \(smartButtonTitle)")
                            if address.isEmpty {
                                // 地址为空，直接关闭
                                logInfo("SimpleAddressSheet - 地址为空，直接关闭")
                                dismiss()
                            } else {
                                // 地址不为空，保存后关闭
                                logInfo("SimpleAddressSheet - 地址不为空，保存后关闭")
                                if let coordinate = selectedCoordinate {
                                    saveSelectedAddress(address: address, coordinate: coordinate)
                                } else {
                                    logWarning("SimpleAddressSheet - 保存失败：坐标为空")
                                    errorMessage = "请先选择一个地址"
                                }
                            }
                        }
                        .foregroundColor(.adaptivePrimaryText)
                    }
                }
            }
            .overlay {
                if isAddingAddress {
                    ProgressView("saving".localized)
                        .padding()
                        .background(Color.adaptiveCardBackground.opacity(0.95))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                }
            }
            .alert("error".localized, isPresented: Binding(
                get: { errorMessage != nil },
                set: { _ in errorMessage = nil }
            )) {
                Button("ok".localized) {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
            .onAppear {
                // 记录初始状态
                logInfo("SimpleAddressSheet - onAppear: pointToEdit=\(pointToEdit?.primaryAddress ?? "nil"), isStartPoint=\(pointToEdit?.isStartPoint ?? false), isEndPoint=\(pointToEdit?.isEndPoint ?? false)")

                // 立即设置导航标题
                self.navigationTitle = self.getNavigationTitle()
                logInfo("SimpleAddressSheet - onAppear时立即设置导航标题: \(self.navigationTitle)")

                // 立即触发发布者，发送当前pointToEdit值
                pointToEditPublisher.send(pointToEdit)
                logInfo("SimpleAddressSheet - 已触发pointToEdit发布者")

                setupSearchCompleter()
                if let editPoint = pointToEdit {
                    // 编辑模式，自动填充
                    address = editPoint.primaryAddress
                    safeSetCoordinate(CLLocationCoordinate2D(latitude: editPoint.latitude, longitude: editPoint.longitude))

                    logInfo("SimpleAddressSheet - 已填充编辑点数据: 地址='\(editPoint.primaryAddress)', 坐标=(\(editPoint.latitude), \(editPoint.longitude))")

                    // 如果是有问题的地址（坐标为0,0或有警告标记），显示提示
                    if editPoint.geocodingWarningType != .none {
                        errorMessage = String(format: "address_has_problem_fix".localized, editPoint.geocodingWarningType.message)
                        logInfo("SimpleAddressSheet - 检测到地址问题: \(editPoint.geocodingWarningType.message)")
                    }

                    // 🎯 检测地址识别问题并提供建议
                    if let issue = AddressSimplifier.detectRecognitionIssues(editPoint.primaryAddress) {
                        logInfo("SimpleAddressSheet - 检测到地址识别问题: \(issue)")
                    }
                }

//                // 自动执行测试（仅在调试模式下）
//                #if DEBUG
//                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
//                    logInfo("SimpleAddressSheet - 自动执行地理编码测试")
//                    testSpecificAddress()
//                }
//                #endif
            }
            .sheet(isPresented: $isShowingScanner) {
                // 传递当前路线点以检查地址是否已存在
                AddressScannerSheet(onAddressConfirmed: { address, coordinate in
                    logInfo("AddressScannerSheet 回调，地址: \(address), 坐标: \(coordinate)")

                    // 🧹 清理地址元数据，只显示纯净地址给用户
                    let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                    let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                    logInfo("🧹 地址清理: '\(address)' -> '\(cleanAddress)'")

                    self.address = cleanAddress  // 使用清理后的地址
                    self.safeSetCoordinate(coordinate)

                    // 自动触发搜索补全
                    if !cleanAddress.isEmpty {
                        self.performLocaleAwareSearch(cleanAddress)
                        logInfo("扫描地址后已触发语言环境感知搜索: \(cleanAddress)")
                    }

                    // 直接保存地址并关闭表单（使用原始地址保留元数据）
                    if let coordinate = self.selectedCoordinate {
                        self.saveSelectedAddress(address: address, coordinate: coordinate)  // 保存时使用原始地址
                    }
                }, currentRoutePoints: pointToEdit?.route?.points.filter { $0.id != pointToEdit?.id } ?? [])
            }
            .sheet(isPresented: $showingSavedAddresses) {
                // 地址簿选择器
                SavedAddressPickerView(onAddressSelected: { savedAddress in
                    // 当选择地址簿中的地址时
                    // 使用原始地址，确保格式一致
                    let formattedAddress = savedAddress.address

                    self.address = formattedAddress
                    self.safeSetCoordinate(savedAddress.coordinate)
                    self.searchResults = []

                    logInfo("SimpleAddressSheet - 选择了地址簿中的地址: \(savedAddress.address), 格式化为: \(formattedAddress)")

                    // 直接保存到路线
                    saveSelectedAddress(address: formattedAddress, coordinate: savedAddress.coordinate)

                    // 关闭选择器
                    showingSavedAddresses = false
                })
                .presentationDetents([.height(400), .medium, .large])
                .presentationDragIndicator(.visible)
                .presentationBackgroundInteraction(.enabled(upThrough: .height(400)))
            }

            .sheet(isPresented: $showingImageRecognizer) {
                // 图片地址识别器
                ImageAddressRecognizer(
                    onAddressesConfirmed: { addresses, selectedAppType in
                        logInfo("ImageAddressRecognizer 回调，收到 \(addresses.count) 个地址，应用类型: \(selectedAppType.displayName)")

                        // 如果有地址，批量处理所有地址
                        if !addresses.isEmpty {
                            if addresses.count == 1 {
                                // 单个地址，直接保存
                                let (addressText, coordinate) = addresses.first!

                                // 🧹 清理地址显示：移除元数据，只显示纯净地址
                                let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(addressText)
                                let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                                // 更新地址和坐标（显示清理后的地址）
                                self.address = cleanAddress
                                self.selectedCoordinate = coordinate
                                self.searchResults = []

                                logInfo("ImageAddressRecognizer - 选择了单个地址: \(cleanAddress)")
                                logInfo("🧹 地址清理: '\(addressText)' -> '\(cleanAddress)'")
                                self.saveSelectedAddress(address: addressText, coordinate: coordinate, appType: selectedAppType)
                            } else {
                                // 多个地址，批量处理
                                logInfo("ImageAddressRecognizer - 开始批量处理 \(addresses.count) 个地址")

                                // 🚀 显示批量处理状态，避免UI看起来hang
                                self.isAddingAddress = true

                                // 🧹 清理第一个地址用于UI显示
                                if let firstAddress = addresses.first {
                                    let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(firstAddress.0)
                                    let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)
                                    self.address = cleanAddress
                                    logInfo("🧹 批量处理UI显示: '\(firstAddress.0)' -> '\(cleanAddress)'")
                                }

                                self.processImportedAddresses(addresses, appType: selectedAppType)
                            }
                        }

                        // 关闭图片识别器
                        showingImageRecognizer = false
                    },
                    onDismiss: {
                        showingImageRecognizer = false
                    }
                )
                .presentationDetents([.medium, .large], selection: .constant(.large))
                .presentationDragIndicator(.visible)
                .presentationBackgroundInteraction(.enabled(upThrough: .medium))
            }
            .sheet(isPresented: $showingBatchInput) {
                // 批量地址输入
                BatchAddressInputSheet(
                    onAddressesAdded: { addresses in
                        logInfo("BatchAddressInputSheet 回调，收到 \(addresses.count) 个地址")
                        // 处理批量地址
                        processBatchAddresses(addresses)
                    },
                    onValidatedAddressesAdded: { validatedAddresses in
                        logInfo("BatchAddressInputSheet 验证回调，收到 \(validatedAddresses.count) 个验证地址")
                        // 处理验证后的批量地址
                        processValidatedBatchAddresses(validatedAddresses)
                    }
                )
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
                .presentationBackgroundInteraction(.enabled(upThrough: .medium))
            }
            .sheet(isPresented: $showingFileImport) {
                // 文件导入
                FileImportSheet { addresses in
                    logInfo("FileImportSheet 回调，收到 \(addresses.count) 个地址")

                    // 处理带验证信息的导入地址
                    processImportedAddressesWithValidation(addresses)
                }
                .presentationDetents([.medium, .large]) // 支持半屏和全屏展开
                .presentationDragIndicator(.visible)
                .interactiveDismissDisabled(true) // 禁用拖拽关闭，只能通过Cancel或Import按钮关闭
                .presentationBackgroundInteraction(.enabled(upThrough: .medium)) // 允许与背景交互
            }
            .sheet(isPresented: $showingWebDownload) {
                // 线上下载
                WebDownloadSheet { addresses in
                    logInfo("WebDownloadSheet 回调，收到 \(addresses.count) 个地址")

                    // 处理下载的地址
                    processImportedAddresses(addresses)
                }
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
                .interactiveDismissDisabled(false) // 允许交互式关闭
                .presentationBackgroundInteraction(.enabled(upThrough: .medium)) // 允许与背景交互
            }
            .onReceive(pointToEditPublisher) { point in
                // 当pointToEdit变化时，更新导航标题
                DispatchQueue.main.async {
                    self.navigationTitle = self.getNavigationTitle()
                    logInfo("SimpleAddressSheet - 通过发布者更新导航标题: \(self.navigationTitle), pointToEdit=\(point?.primaryAddress ?? "nil")")
                }
            }
        }
    }

    // 🎯 检测是否是完整地址格式
    private func isCompleteAddress(_ address: String) -> Bool {
        // 检测地址是否包含完整的地址组件
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

        // 🎯 优化：包含"USA"的地址不使用直接搜索，因为Apple Maps对此搜索效果不佳
        if address.uppercased().contains("USA") || address.uppercased().contains("UNITED STATES") {
            logInfo("SimpleAddressSheet - 地址包含USA，使用自动补全而非直接搜索: \(address)")
            return false
        }

        // 检查是否包含州/省的缩写（2个字母）+ 邮编格式
        if components.count >= 2 {
            let secondLastComponent = components[components.count - 2].trimmingCharacters(in: .whitespaces)
            // 检查州缩写格式：2个字母 + 可选的邮编
            if secondLastComponent.count == 2 && secondLastComponent.allSatisfy({ $0.isLetter }) {
                logInfo("SimpleAddressSheet - 检测到完整地址（包含州缩写）: \(address)")
                return true
            }

            // 检查是否有"州缩写 邮编"格式
            let stateZipPattern = secondLastComponent.split(separator: " ")
            if stateZipPattern.count == 2,
               stateZipPattern[0].count == 2 && stateZipPattern[0].allSatisfy({ $0.isLetter }),
               stateZipPattern[1].count == 5 && stateZipPattern[1].allSatisfy({ $0.isNumber }) {
                logInfo("SimpleAddressSheet - 检测到完整地址（州缩写+邮编）: \(address)")
                return true
            }
        }

        // 如果有3个或更多组件，且包含其他国家代码，认为是完整地址
        if components.count >= 3 {
            let lastComponent = components.last?.lowercased() ?? ""
            // 检查是否包含其他国家代码（排除USA相关）
            let countryIndicators = ["au", "australia", "ca", "canada", "gb", "uk", "united kingdom"]
            if countryIndicators.contains(lastComponent) {
                logInfo("SimpleAddressSheet - 检测到完整地址（包含国家）: \(address)")
                return true
            }
        }

        return false
    }

    // 🎯 直接搜索完整地址
    private func performDirectAddressSearch(_ address: String) {
        logInfo("SimpleAddressSheet - 开始直接搜索地址: \(address)")

        // 🎯 编辑模式下，如果地址未变化且有有效坐标，直接返回
        if let editPoint = pointToEdit,
           editPoint.latitude != 0 && editPoint.longitude != 0,
           address == editPoint.primaryAddress {
            logInfo("SimpleAddressSheet - 编辑模式下地址未变化且有有效坐标，跳过直接搜索: \(address)")
            searchResults = []
            return
        }

        // 清空之前的搜索结果
        searchResults = []

        // 🧹 确保地址已清理（防止重复清理）
        let cleanAddress = address.contains("|") ? cleanAddressForSearch(address) : address
        logInfo("SimpleAddressSheet - 直接搜索使用地址: \(cleanAddress)")

        // 使用MKLocalSearch直接搜索，强制英文结果
        let searchRequest = MKLocalSearch.Request()
        searchRequest.naturalLanguageQuery = cleanAddress

        // 🎯 不限制搜索区域，让Apple Maps自动识别全球地址
        // 设置全球搜索区域
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        searchRequest.region = globalRegion

        let search = MKLocalSearch(request: searchRequest)
        search.start { response, error in
            DispatchQueue.main.async {

                if let error = error {
                    logError("SimpleAddressSheet - 直接搜索失败: \(error.localizedDescription)")
                    // 降级到语言环境感知搜索
                    performLocaleAwareSearch(address)
                    return
                }

                guard let response = response, !response.mapItems.isEmpty else {
                    logInfo("SimpleAddressSheet - 直接搜索无结果，降级到自动补全")
                    // 降级到语言环境感知搜索
                    performLocaleAwareSearch(address)
                    return
                }

                // 🎯 改进搜索结果处理，确保地址匹配度和英文显示
                let mockCompletions = response.mapItems.prefix(5).compactMap { mapItem -> MKLocalSearchCompletion? in
                    let placemark = mapItem.placemark

                    // 构建英文地址字符串
                    var addressComponents: [String] = []

                    // 门牌号 + 街道
                    if let subThoroughfare = placemark.subThoroughfare,
                       let thoroughfare = placemark.thoroughfare {
                        addressComponents.append("\(subThoroughfare) \(thoroughfare)")
                    } else if let thoroughfare = placemark.thoroughfare {
                        addressComponents.append(thoroughfare)
                    } else if let name = mapItem.name {
                        addressComponents.append(name)
                    }

                    // 城市、州、国家
                    var locationComponents: [String] = []
                    if let locality = placemark.locality {
                        locationComponents.append(locality)
                    }
                    if let administrativeArea = placemark.administrativeArea {
                        locationComponents.append(administrativeArea)
                    }
                    if let postalCode = placemark.postalCode {
                        locationComponents.append(postalCode)
                    }
                    if let country = placemark.country {
                        locationComponents.append(country)
                    }

                    // 检查是否包含中文字符，如果有则跳过此结果
                    let fullLocationString = locationComponents.joined(separator: ", ")
                    if AddressStandardizer.containsChineseCharacters(fullLocationString) {
                        logInfo("SimpleAddressSheet - 跳过包含中文的搜索结果: \(fullLocationString)")
                        return nil
                    }

                    let completion = MockLocalSearchCompletion()
                    completion.title = addressComponents.first ?? address
                    completion.subtitle = fullLocationString

                    logInfo("SimpleAddressSheet - 处理搜索结果: \(completion.title), \(completion.subtitle)")
                    return completion
                }

                searchResults = Array(mockCompletions)
                logInfo("SimpleAddressSheet - 直接搜索成功，找到\(searchResults.count)个英文结果")

                // 如果没有英文结果，降级到自动补全
                if searchResults.isEmpty {
                    logInfo("SimpleAddressSheet - 没有英文搜索结果，降级到自动补全")
                    searchCompleter.queryFragment = address
                }
            }
        }
    }

    // 设置搜索自动完成 - 使用语言环境感知搜索
    private func setupSearchCompleter() {
        logInfo("SimpleAddressSheet - 设置语言环境感知搜索自动完成")

        // 注意：现在主要使用LocaleAwareAddressSearchService进行搜索
        // 保留原有的searchCompleter作为备用

        // 创建并保存代理实例（备用）
        completerDelegate = SimpleAddressCompleterDelegate(onUpdateResults: { results in
            logInfo("📥 SimpleAddressSheet - 收到代理回调结果")
            logInfo("📥 原始结果数量: \(results.count)")

            DispatchQueue.main.async {
                // 🎯 智能过滤：根据系统语言调整过滤策略
                let systemLanguage = Locale.current.language.languageCode?.identifier
                let isChineseSystem = systemLanguage == "zh"

                let filteredResults = results.filter { result in
                    let titleHasChinese = AddressStandardizer.containsChineseCharacters(result.title)
                    let subtitleHasChinese = AddressStandardizer.containsChineseCharacters(result.subtitle)

                    logInfo("📥 检查结果: '\(result.title)' - '\(result.subtitle)'")
                    logInfo("📥   标题包含中文: \(titleHasChinese), 副标题包含中文: \(subtitleHasChinese)")
                    logInfo("📥   系统语言: \(systemLanguage ?? "unknown"), 中文系统: \(isChineseSystem)")

                    // 如果是中文系统，接受副标题包含中文的结果（Apple Maps本地化）
                    if isChineseSystem {
                        // 中文系统：只过滤掉标题包含中文的结果
                        if titleHasChinese {
                            logInfo("📥 中文系统 - 过滤标题包含中文的搜索结果: \(result.title)")
                            return false
                        }
                        return true
                    } else {
                        // 英文系统：过滤掉任何包含中文的结果
                        if titleHasChinese || subtitleHasChinese {
                            logInfo("📥 英文系统 - 过滤包含中文的搜索结果: \(result.title) - \(result.subtitle)")
                            return false
                        }
                        return true
                    }
                }

                logInfo("📥 过滤后结果数量: \(filteredResults.count)")
                for (index, result) in filteredResults.enumerated() {
                    logInfo("📥 过滤后结果[\(index)]: '\(result.title)' - '\(result.subtitle)'")
                }

                self.searchResults = filteredResults
                logInfo("📥 SimpleAddressSheet - 搜索结果已更新到UI")
            }
        })

        // 设置代理
        searchCompleter.delegate = completerDelegate

        // 设置搜索类型
        searchCompleter.resultTypes = .address

        // 🎯 设置美国区域，提高搜索准确性
        let usRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 39.0, longitude: -98.0), // 美国中心
            span: MKCoordinateSpan(latitudeDelta: 50.0, longitudeDelta: 50.0) // 覆盖整个美国
        )
        searchCompleter.region = usRegion
        logInfo("SimpleAddressSheet - 设置美国搜索区域")
    }

    // 🧹 清理地址用于搜索（移除元数据标签）
    private func cleanAddressForSearch(_ address: String) -> String {
        // 使用DeliveryPointManager的分离方法提取纯净地址
        let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
        let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果清理后地址为空，返回原始地址
        if cleanAddress.isEmpty {
            logWarning("SimpleAddressSheet - 地址清理后为空，返回原始地址: \(address)")
            return address
        }

        return cleanAddress
    }

    // 🌍 使用语言环境感知的地址搜索
    private func performLocaleAwareSearch(_ query: String) {
        logInfo("🔍 SimpleAddressSheet - 开始语言环境感知地址搜索")
        logInfo("🔍 搜索查询: '\(query)'")
        logInfo("🔍 当前系统语言: \(Locale.current.identifier)")

        // 🎯 使用专门的语言环境感知搜索服务
        LocaleAwareAddressSearchService.shared.performSearch(query: query) { results in
            DispatchQueue.main.async {
                logInfo("🌍 语言环境感知搜索完成，结果数量: \(results.count)")
                for (index, result) in results.enumerated() {
                    logInfo("🌍 结果[\(index)]: '\(result.title)' - '\(result.subtitle)'")
                }

                self.searchResults = results
                logInfo("🌍 SimpleAddressSheet - 语言环境感知搜索结果已更新到UI")
            }
        }
    }



    // 创建模拟的MKLocalSearchCompletion - 过滤中文结果
    private func createMockCompletion(from mapItem: MKMapItem) -> MKLocalSearchCompletion? {
        let placemark = mapItem.placemark

        // 构建标题（通常是门牌号和街道名）
        var titleComponents: [String] = []

        if let subThoroughfare = placemark.subThoroughfare {
            titleComponents.append(subThoroughfare)
        }

        if let thoroughfare = placemark.thoroughfare {
            titleComponents.append(thoroughfare)
        }

        let title = titleComponents.isEmpty ? (mapItem.name ?? "Unknown") : titleComponents.joined(separator: " ")

        // 构建副标题（城市、州、国家）
        var subtitleComponents: [String] = []

        if let locality = placemark.locality {
            subtitleComponents.append(locality)
        }

        if let administrativeArea = placemark.administrativeArea {
            subtitleComponents.append(administrativeArea)
        }

        if let postalCode = placemark.postalCode {
            subtitleComponents.append(postalCode)
        }

        if let country = placemark.country, country != "United States" {
            subtitleComponents.append(country)
        }

        let subtitle = subtitleComponents.joined(separator: ", ")

        // 🚫 检查是否包含中文字符，如果包含则过滤掉
        let fullText = "\(title) \(subtitle)"
        let hasChinese = fullText.range(of: "\\p{Script=Han}", options: .regularExpression) != nil

        if hasChinese {
            return nil
        }

        // 创建模拟的completion对象
        let mockCompletion = MockLocalSearchCompletion()
        mockCompletion.title = title
        mockCompletion.subtitle = subtitle

        return mockCompletion
    }

    // 🛡️ 安全设置坐标，防止NaN值导致UI卡死
    private func safeSetCoordinate(_ coordinate: CLLocationCoordinate2D) {
        // 检查坐标是否包含NaN值
        if coordinate.latitude.isNaN || coordinate.longitude.isNaN {
            logError("SimpleAddressSheet - ⚠️ 检测到NaN坐标，拒绝设置: lat=\(coordinate.latitude), lon=\(coordinate.longitude)")
            // 设置为无效坐标而不是NaN
            self.selectedCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
            return
        }

        // 检查坐标是否在有效范围内
        if !CLLocationCoordinate2DIsValid(coordinate) {
            logError("SimpleAddressSheet - ⚠️ 检测到无效坐标，拒绝设置: lat=\(coordinate.latitude), lon=\(coordinate.longitude)")
            self.selectedCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
            return
        }

        // 坐标有效，安全设置
        self.selectedCoordinate = coordinate
        logInfo("SimpleAddressSheet - ✅ 安全设置坐标: lat=\(coordinate.latitude), lon=\(coordinate.longitude)")
    }





    // 选择搜索结果并直接保存 - 使用快速地址处理器
    private func selectSearchResult(_ result: MKLocalSearchCompletion) {
        logInfo("🌍 SimpleAddressSheet - 开始处理选中的搜索结果: \(result.title)")

        // 显示正在搜索
        isAddingAddress = true

        // 构建完整地址字符串
        let fullAddress = result.subtitle.isEmpty ? result.title : "\(result.title), \(result.subtitle)"

        // 使用快速地址处理器处理地址，带超时保护和错误处理
        Task {
            logInfo("🚀 SimpleAddressSheet - 开始地址处理流程: \(fullAddress)")

            // 添加超时保护 - 减少到8秒避免hang
            let timeoutTask = Task {
                try? await Task.sleep(nanoseconds: 8_000_000_000) // 8秒超时
                logWarning("⏰ SimpleAddressSheet - 地址处理超时，强制结束")
                await MainActor.run {
                    self.isAddingAddress = false
                    // 🚫 屏蔽超时错误提示，避免影响用户体验
                    // self.errorMessage = "address_processing_timeout".localized
                }
            }

            defer {
                timeoutTask.cancel()
            }

                // 🏠 首先检查地址库中是否已存在该地址
                logInfo("🏠 SimpleAddressSheet - 检查地址库: \(fullAddress)")
                let existingAddress = await UserAddressDatabase.shared.getValidatedAddress(for: fullAddress)

                if let existing = existingAddress {
                    logInfo("🏠 SimpleAddressSheet - ✅ 地址库命中: \(fullAddress) -> (\(existing.coordinate.latitude), \(existing.coordinate.longitude))")
                    logInfo("🏠 SimpleAddressSheet - 地址库信息: 使用次数=\(existing.usageCount), 置信度=\(existing.confidence), 来源=\(existing.source)")

                    await MainActor.run {
                        self.isAddingAddress = false
                        self.address = fullAddress
                        self.safeSetCoordinate(existing.coordinate)
                        self.searchResults = []

                        // 直接保存使用地址库数据
                        self.saveSelectedAddress(address: fullAddress, coordinate: existing.coordinate)
                        logInfo("🏠 SimpleAddressSheet - 使用地址库数据完成地址选择: \(fullAddress)")
                    }
                    return
                }

                logInfo("🏠 SimpleAddressSheet - 地址库未命中，使用快速地理编码: \(fullAddress)")

                // 🚀 使用超快速地址处理，避免hang问题
                logInfo("🚀 SimpleAddressSheet - 使用超快速地址处理")
                let quickResult = await processAddressUltraFast(fullAddress)
                logInfo("🚀 SimpleAddressSheet - 超快速地址处理完成")

                logInfo("🚀 SimpleAddressSheet - 准备更新UI")
                await MainActor.run {
                    logInfo("🚀 SimpleAddressSheet - 在主线程中处理结果")
                    switch quickResult {
                    case .success(_, let formattedAddress, let coordinate, let placemark):
                        logInfo("🚀 SimpleAddressSheet - 快速地址处理成功: \(formattedAddress)")

                        // 🏠 保存到地址库
                        Task {
                            logInfo("🏠 SimpleAddressSheet - 保存新地址到地址库: \(formattedAddress) -> (\(coordinate.latitude), \(coordinate.longitude))")
                            await UserAddressDatabase.shared.saveValidatedAddress(
                                formattedAddress,
                                coordinate: coordinate,
                                source: .manual,
                                confidence: 0.95 // 用户主动选择的地址，置信度很高
                            )
                            logInfo("🏠 SimpleAddressSheet - ✅ 地址库更新完成: \(formattedAddress)")
                        }

                        // 🏠 详细记录Apple Maps返回的Placemark信息
                        logInfo("🏠 SimpleAddressSheet - Apple Maps返回的Placemark详情:")
                        logInfo("  - 门牌号: '\(placemark.subThoroughfare ?? "无")'")
                        logInfo("  - 街道: '\(placemark.thoroughfare ?? "无")'")
                        logInfo("  - 区域: '\(placemark.locality ?? "无")'")
                        logInfo("  - 行政区: '\(placemark.administrativeArea ?? "无")'")
                        logInfo("  - 国家: '\(placemark.country ?? "无")'")
                        logInfo("  - 完整名称: '\(placemark.name ?? "无")'")
                        logInfo("  - 邮政编码: '\(placemark.postalCode ?? "无")'")
                        logInfo("  - 坐标: (\(coordinate.latitude), \(coordinate.longitude))")
                        logInfo("  - 原始输入: '\(fullAddress)'")
                        logInfo("  - 格式化输出: '\(formattedAddress)'")



                        // 更新选中的地址和坐标
                        logInfo("🚀 SimpleAddressSheet - 更新UI状态")
                        self.address = formattedAddress
                        self.safeSetCoordinate(coordinate)
                        self.searchResults = []
                        self.isAddingAddress = false

                        logInfo("🚀 SimpleAddressSheet - 地址选择完成: \(result.title) -> \(formattedAddress)")

                        // 直接保存格式化地址到当前路线（同时传递placemark以便正确处理结构化地址）
                        logInfo("🚀 SimpleAddressSheet - 调用 saveSelectedAddress")
                        self.saveSelectedAddress(address: formattedAddress, coordinate: coordinate, placemark: placemark)
                        logInfo("🚀 SimpleAddressSheet - saveSelectedAddress 调用完成")

                    case .failed(let reason):
                        // 快速处理失败，降级到传统搜索
                        logWarning("SimpleAddressSheet - 快速地址处理失败: \(reason)，降级到传统搜索")
                        self.address = fullAddress
                        self.isAddingAddress = false

                        // 🚫 屏蔽处理失败错误提示，避免影响用户体验
                        // self.errorMessage = String(format: "address_processing_failed_retry".localized, reason)

                        // 进行传统的MKLocalSearch回退搜索
                        logInfo("🚀 SimpleAddressSheet - 调用 fallbackToOriginalSearch")
                        fallbackToOriginalSearch(result)

                        // 🚨 检查并提示问题地址
                        Task {
                            // 延迟一下让地址处理完成
                            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                            await MainActor.run {
                                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "简单地址表单")
                            }
                        }
                    }

                    logInfo("🚀 SimpleAddressSheet - 主线程处理完成")
                }

            logInfo("🚀 SimpleAddressSheet - Task 完成")
        }
    }

    /// 降级到原有搜索逻辑
    private func fallbackToOriginalSearch(_ result: MKLocalSearchCompletion) {
        logInfo("🔄 SimpleAddressSheet - 降级到原有搜索逻辑")

        // 从搜索结果获取坐标
        let searchRequest = MKLocalSearch.Request(completion: result)
        let search = MKLocalSearch(request: searchRequest)

        search.start { response, error in
            // 切换到主线程更新UI
            DispatchQueue.main.async {
                if let error = error {
                    self.isAddingAddress = false
                    logError("SimpleAddressSheet - 地址搜索错误: \(error.localizedDescription)")
                    return
                }

                guard let response = response else {
                    self.isAddingAddress = false
                    logError("SimpleAddressSheet - 地址搜索无响应")
                    return
                }

                guard let mapItem = response.mapItems.first else {
                    self.isAddingAddress = false
                    logError("SimpleAddressSheet - 地址搜索无结果")
                    return
                }

                let coordinate = mapItem.placemark.coordinate
                let country = mapItem.placemark.country ?? "未知国家"
                logInfo("SimpleAddressSheet - 获取到地址坐标: \(coordinate.latitude), \(coordinate.longitude), 国家: \(country)")

                // 格式化地址，按照要求的格式：街道名称, 区域, 州 国家
                let streetName = result.title
                let locationDetails = result.subtitle
                let formattedAddress = "\(streetName), \(locationDetails)"

                // 🏠 保存到地址库（降级逻辑）
                Task {
                    logInfo("🏠 SimpleAddressSheet - 降级逻辑保存地址到地址库: \(formattedAddress) -> (\(coordinate.latitude), \(coordinate.longitude))")
                    await UserAddressDatabase.shared.saveValidatedAddress(
                        formattedAddress,
                        coordinate: coordinate,
                        source: .manual,
                        confidence: 0.85 // 降级逻辑的置信度稍低
                    )
                    logInfo("🏠 SimpleAddressSheet - ✅ 降级逻辑地址库更新完成: \(formattedAddress)")
                }

                // 更新选中的地址和坐标
                self.address = formattedAddress
                self.safeSetCoordinate(coordinate)
                self.searchResults = []

                logInfo("SimpleAddressSheet - 地址选择完成: \(result.title)")

                // 直接保存格式化地址到当前路线（同时传递placemark对象以便正确处理结构化地址）
                self.saveSelectedAddress(address: formattedAddress, coordinate: coordinate, placemark: mapItem.placemark)
            }
        }
    }

    // 保存选定的地址到路线并关闭 - 使用同步方法确保关闭前完成
    private func saveSelectedAddress(address: String, coordinate: CLLocationCoordinate2D, placemark: CLPlacemark? = nil, appType: DeliveryAppType = .manual) {
        isAddingAddress = true
        errorMessage = nil

        // 🎯 首先检查并修复缺少州信息的地址
        Task {
            let fixedAddress = await fixAddressStateIfNeeded(address)

            await MainActor.run {
                // 如果地址被修复，更新界面显示
                if fixedAddress != address {
                    self.address = fixedAddress
                    logInfo("🔧 SimpleAddressSheet - 地址已自动修复: \(address) -> \(fixedAddress)")
                }

                // Only save to address book if needed
                if saveToAddressBook {
                    saveAddressToAddressBook(address: fixedAddress, coordinate: coordinate)
                }

                logInfo("SimpleAddressSheet - Address selected, ready to pass via callback: \(fixedAddress)")

                // 继续使用修复后的地址进行后续处理
                self.continueWithSaveSelectedAddress(address: fixedAddress, coordinate: coordinate, placemark: placemark, appType: appType)
            }
        }
    }

    // 继续保存地址的逻辑（从原 saveSelectedAddress 方法分离出来）
    private func continueWithSaveSelectedAddress(address: String, coordinate: CLLocationCoordinate2D, placemark: CLPlacemark? = nil, appType: DeliveryAppType = .manual) {

        logInfo("SimpleAddressSheet - continueWithSaveSelectedAddress 开始: address=\(address), coordinate=(\(coordinate.latitude), \(coordinate.longitude))")

        // If callback exists, create temporary object to pass
        if let onAddressAdded = self.onAddressAdded {
            logInfo("SimpleAddressSheet - onAddressAdded 回调存在，准备创建临时地址对象")

            // 检查是否需要设置为起终点组合
            let isStartEndCombination = (addressPointType == .start && pointToEdit?.isEndPoint == true) ||
                                        (addressPointType == .end && pointToEdit?.isStartPoint == true)

            if isStartEndCombination {
                logInfo("SimpleAddressSheet - 检测到起终点组合设置")
            }

            // Create temporary object to pass
            let tempAddress = SavedAddress(
                address: address,
                coordinate: coordinate,
                companyName: companyName.isEmpty ? nil : companyName,
                url: url.isEmpty ? nil : url
            )

            // 保存placemark信息，以便在创建DeliveryPoint时填充结构化地址
            if let placemark = placemark {
                // 将placemark信息保存到notes字段，添加特殊标记
                tempAddress.notes = "PLACEMARK_INFO:true"
                // 使用关联值存储placemark对象
                AssociatedValues.shared.setPlacemark(for: tempAddress.id.uuidString, placemark: placemark)
                logInfo("SimpleAddressSheet - saveSelectedAddress: 已保存placemark信息到临时地址")
            }

            // 如果是起终点组合，添加特殊标记
            if isStartEndCombination {
                let currentNotes = tempAddress.notes ?? ""
                tempAddress.notes = currentNotes + (currentNotes.isEmpty ? "" : "\n") + "START_END_COMBINATION"
                logInfo("SimpleAddressSheet - 已添加起终点组合标记")
            }

            // 🎯 添加用户选择的应用类型信息
            if appType != .manual && appType != .justPhoto {
                let currentNotes = tempAddress.notes ?? ""
                let appTypeInfo = "USER_SELECTED_APP_TYPE:\(appType.rawValue)"
                tempAddress.notes = currentNotes + (currentNotes.isEmpty ? "" : "\n") + appTypeInfo
                logInfo("SimpleAddressSheet - 已添加用户选择的应用类型: \(appType.displayName)")
            }

            // 检查是否是批量地址
            let isBatchAddress = tempAddress.notes?.hasPrefix("BATCH_ADDRESSES:") ?? false

            // 如果不是批量地址，检查当前路线是否已达到地址上限
            if !isBatchAddress {
                logInfo("SimpleAddressSheet - 检查订阅限制（非批量地址）")

                // 获取当前路线 - 使用更可靠的方法获取当前路线
                let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
                if let routes = try? modelContext.fetch(descriptor),
                   let currentRoute = routes.first {

                    // 检查订阅限制
                    let subscriptionManager = SubscriptionManager.shared
                    let canAddMore = subscriptionManager.canAddMoreStops(to: currentRoute)
                    logInfo("SimpleAddressSheet - 订阅限制检查结果: canAddMore=\(canAddMore), 当前点数=\(currentRoute.points.count), 最大允许=\(subscriptionManager.currentTier.maxStopsPerRoute)")

                    if !canAddMore {
                        // 显示错误信息
                        logError("SimpleAddressSheet - 达到订阅限制，无法添加更多地址")
                        errorMessage = String(format: "free_version_max_addresses_single".localized, subscriptionManager.currentTier.maxStopsPerRoute)
                        isAddingAddress = false
                        return
                    }

                    // 记录当前路线信息，帮助调试
                    logInfo("SimpleAddressSheet - 当前路线: \(currentRoute.name), ID: \(currentRoute.id.uuidString), 点数: \(currentRoute.points.count)")
                } else {
                    logError("SimpleAddressSheet - 无法获取当前路线，这可能导致地址无法正确保存")
                }
            } else {
                logInfo("SimpleAddressSheet - 跳过订阅限制检查（批量地址）")
            }

            // Call callback
            logInfo("SimpleAddressSheet - 准备调用回调: address=\(address), coordinate=(\(coordinate.latitude), \(coordinate.longitude))")
            if !companyName.isEmpty {
                logInfo("SimpleAddressSheet - With company name: \(companyName)")
            }
            if !url.isEmpty {
                logInfo("SimpleAddressSheet - With URL: \(url)")
            }

            // 确保回调被调用
            logInfo("SimpleAddressSheet - 正在调用 onAddressAdded 回调...")
            onAddressAdded(tempAddress)
            logInfo("SimpleAddressSheet - ✅ onAddressAdded 回调执行成功")

            // Close form
            logInfo("SimpleAddressSheet - 准备关闭表单...")
            isAddingAddress = false
            dismiss()
            logInfo("SimpleAddressSheet - ✅ 表单已关闭")
        } else {
            logError("SimpleAddressSheet - ❌ 没有定义 onAddressAdded 回调函数，无法通知UI更新")
            // 即使没有回调，也要关闭表单
            isAddingAddress = false
            dismiss()
        }
    }

    // 将地址保存到地址簿中
    private func saveAddressToAddressBook(address: String, coordinate: CLLocationCoordinate2D) {
        let context = modelContext
        let lat = coordinate.latitude
        let lon = coordinate.longitude
        do {
            // 查重：查找是否已存在同样坐标的地址
            let descriptor = FetchDescriptor<SavedAddress>(
                predicate: #Predicate { $0.latitude == lat && $0.longitude == lon }
            )
            let existing = try context.fetch(descriptor)
            if let exist = existing.first {
                // 如果地址已存在但有新的公司名称或URL，则更新
                if (!companyName.isEmpty && exist.companyName != companyName) ||
                   (!url.isEmpty && exist.url != url) {

                    if !companyName.isEmpty {
                        exist.companyName = companyName
                        logInfo("SimpleAddressSheet - 更新已存在地址的公司名称: \(companyName)")
                    }

                    if !url.isEmpty {
                        exist.url = url
                        logInfo("SimpleAddressSheet - 更新已存在地址的URL: \(url)")
                    }

                    try context.save()
                    logInfo("SimpleAddressSheet - 已更新地址信息: \(exist.address), ID: \(exist.id.uuidString)")
                } else {
                    logInfo("SimpleAddressSheet - 地址已存在，不重复保存: \(exist.address), ID: \(exist.id.uuidString)")
                }
                return
            }
            // 🎯 标准化街道简称后插入新地址
            let standardizedAddress = StreetAbbreviationStandardizer.standardizeStreetAbbreviations(address)
            let newAddress = SavedAddress(
                address: standardizedAddress,
                coordinate: coordinate,
                companyName: companyName.isEmpty ? nil : companyName,
                url: url.isEmpty ? nil : url
            )
            newAddress.isFavorite = self.isFavorite
            context.insert(newAddress)
            try context.save()
            NotificationCenter.default.post(
                name: Notification.Name("AddressAddedNotification"),
                object: newAddress.id.uuidString
            )
            logInfo("SimpleAddressSheet - 成功添加地址到地址簿: \(address), ID: \(newAddress.id.uuidString)")
            if !companyName.isEmpty {
                logInfo("SimpleAddressSheet - 包含公司名称: \(companyName)")
            }
            if !url.isEmpty {
                logInfo("SimpleAddressSheet - 包含URL: \(url)")
            }
        } catch {
            logError("SimpleAddressSheet - 添加地址到地址簿失败: \(error.localizedDescription)")
        }
    }

    // 处理批量地址
    private func processBatchAddresses(_ addresses: [String]) {
        logInfo("SimpleAddressSheet - 开始处理批量地址: \(addresses.count) 个")

        // 如果没有地址，直接返回
        if addresses.isEmpty {
            logInfo("SimpleAddressSheet - 批量地址为空，不处理")
            return
        }

        // 如果只有一个地址，直接处理
        if addresses.count == 1 {
            let address = addresses[0]
            self.address = address

            // 尝试地理编码获取坐标
            Task {
                if let coordinate = await geocodeAddress(address) {
                    await MainActor.run {
                        self.selectedCoordinate = coordinate
                        logInfo("SimpleAddressSheet - 成功获取批量地址坐标，准备保存: \(address)")
                        saveSelectedAddress(address: address, coordinate: coordinate)
                    }
                } else {
                    await MainActor.run {
                        errorMessage = "无法获取地址坐标，请手动输入或重新尝试"
                        logError("SimpleAddressSheet - 无法获取批量地址的坐标: \(address)")
                    }
                }
            }
            return
        }

        // 如果有多个地址，则需要将地址传递给路线视图进行处理
        // 这里需要调用回调函数，将所有地址传递给路线视图
        if let onAddressAdded = onAddressAdded {
            // 创建一个临时对象来传递第一个地址，并在其中包含所有地址信息
            let firstAddress = addresses[0]

            // 尝试地理编码获取坐标
            Task {
                if let coordinate = await geocodeAddress(firstAddress) {
                    await MainActor.run {
                        // 获取当前路线
                        let descriptor = FetchDescriptor<Route>()
                        if let routes = try? modelContext.fetch(descriptor),
                           let currentRoute = routes.first {

                            // 检查订阅限制（不包括start和end点）
                            let subscriptionManager = SubscriptionManager.shared
                            let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute
                            let currentCount = currentRoute.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
                            let remainingSlots = maxAllowed - currentCount

                            // 如果地址数量超过剩余槽位，显示错误信息
                            if addresses.count > remainingSlots {
                                if subscriptionManager.currentTier == .free {
                                    // 免费用户显示升级提示
                                    errorMessage = String(format: "free_version_max_addresses_batch".localized, maxAllowed, currentCount, remainingSlots, addresses.count, addresses.count - remainingSlots)
                                } else {
                                    // 付费用户显示一般提示
                                    errorMessage = String(format: "paid_version_max_addresses_batch".localized, currentCount, remainingSlots, maxAllowed, addresses.count, addresses.count - remainingSlots)
                                }
                                return
                            }
                        }

                        // 创建一个临时对象
                        let tempAddress = SavedAddress(
                            address: firstAddress,
                            coordinate: coordinate
                        )

                        // 在用户信息中添加批量地址信息
                        tempAddress.notes = "BATCH_ADDRESSES:\(addresses.joined(separator: "\n"))"

                        // 调用回调
                        logInfo("SimpleAddressSheet - 调用回调传递批量地址: \(addresses.count) 个")
                        onAddressAdded(tempAddress)

                        // 关闭表单
                        dismiss()
                    }
                } else {
                    await MainActor.run {
                        errorMessage = "无法获取地址坐标，请手动输入或重新尝试"
                        logError("SimpleAddressSheet - 无法获取批量地址的坐标: \(firstAddress)")
                    }
                }
            }
        } else {
            logInfo("SimpleAddressSheet - 没有回调函数，无法通知UI更新")
        }
    }

    // 处理验证后的批量地址
    private func processValidatedBatchAddresses(_ validatedAddresses: [ValidatedAddressInfo]) {
        logInfo("SimpleAddressSheet - 开始处理验证后的批量地址: \(validatedAddresses.count) 个")

        if let onAddressAdded = onAddressAdded {
            Task {
                for validatedAddress in validatedAddresses {
                    let tempAddress = SavedAddress(
                        address: validatedAddress.address,
                        coordinate: validatedAddress.coordinate,
                        companyName: self.companyName.isEmpty ? nil : self.companyName,
                        url: self.url.isEmpty ? nil : self.url
                    )

                    // 保存验证警告信息到 notes 字段
                    if let warningMessage = validatedAddress.warningMessage, !warningMessage.isEmpty {
                        tempAddress.notes = "VALIDATION_WARNING:\(warningMessage)"
                        logInfo("SimpleAddressSheet - 保存批量地址验证警告: \(warningMessage)")
                    }

                    // 在主线程调用回调
                    await MainActor.run {
                        onAddressAdded(tempAddress)
                    }
                }

                // 关闭表单
                await MainActor.run {
                    dismiss()
                }

                // 🚨 检查并提示问题地址
                await MainActor.run {
                    ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "批量地址验证")
                }
            }
        } else {
            logInfo("SimpleAddressSheet - 没有回调函数，无法通知UI更新")
        }
    }

    // 处理导入或下载的地址
    private func processImportedAddresses(_ addresses: [(String, CLLocationCoordinate2D)], appType: DeliveryAppType = .manual) {
        logInfo("SimpleAddressSheet - 开始处理导入的地址: \(addresses.count) 个")

        if addresses.isEmpty {
            logInfo("SimpleAddressSheet - 导入的地址为空，不处理")
            return
        }

        Task {
            // 首先检查订阅限制
            await MainActor.run {
                // 获取当前路线
                let descriptor = FetchDescriptor<Route>()
                if let routes = try? modelContext.fetch(descriptor),
                   let currentRoute = routes.first {

                    // 检查订阅限制（不包括start和end点）
                    let subscriptionManager = SubscriptionManager.shared
                    let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute
                    let currentCount = currentRoute.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
                    let remainingSlots = maxAllowed - currentCount

                    // 如果地址数量超过剩余槽位，显示错误信息
                    if addresses.count > remainingSlots {
                        if subscriptionManager.currentTier == .free {
                            // 免费用户显示升级提示
                            errorMessage = String(format: "free_version_max_addresses_batch".localized, maxAllowed, currentCount, remainingSlots, addresses.count, addresses.count - remainingSlots)
                            logInfo("SimpleAddressSheet - 导入地址超过免费版限制: 当前\(currentCount)个，尝试导入\(addresses.count)个，剩余槽位\(remainingSlots)个")
                            return
                        } else {
                            // 付费用户显示一般提示
                            errorMessage = String(format: "paid_version_max_addresses_batch".localized, currentCount, remainingSlots, maxAllowed, addresses.count, addresses.count - remainingSlots)
                            logInfo("SimpleAddressSheet - 导入地址超过限制: 当前\(currentCount)个，尝试导入\(addresses.count)个，剩余槽位\(remainingSlots)个")
                            return
                        }
                    }

                    // 如果地址数量在限制范围内，记录日志
                    logInfo("SimpleAddressSheet - 导入地址在限制范围内: 当前\(currentCount)个，导入\(addresses.count)个，剩余槽位\(remainingSlots)个")
                }
            }

            // 🎯 修复：图片识别的地址已经通过geocoding验证，坐标是正确的，跳过重复验证
            logInfo("SimpleAddressSheet - 图片识别地址已验证，直接使用现有坐标")
            var validationResults: [AddressValidationResult] = []

            for (addressText, coordinate) in addresses {
                // 🚨 修复：直接使用已验证的坐标，不进行重复验证
                // 🎯 保持原始地址信息（包含所有元数据），不要清理
                let validationResult = AddressValidationResult(
                    originalAddress: addressText, // 保持完整的原始地址信息
                    geocodedAddress: addressText, // 保持完整的原始地址信息
                    coordinate: coordinate, // 使用已验证的正确坐标
                    isModified: false,
                    confidence: .high, // 图片识别已验证的地址设为高置信度
                    modificationType: .none,
                    suggestions: [],
                    warningMessage: nil // 已验证的地址无警告
                )
                validationResults.append(validationResult)
                logInfo("SimpleAddressSheet - 使用图片识别已验证地址（保持完整元数据）: \(addressText), 坐标: (\(coordinate.latitude), \(coordinate.longitude))")
            }

            var successfullyProcessedCount = 0
            var firstAddressProcessedForUI = false

            // 🚀 分批处理地址，避免UI阻塞
            let batchSize = 3 // 每批处理3个地址
            let totalBatches = Int(ceil(Double(validationResults.count) / Double(batchSize)))

            logInfo("SimpleAddressSheet - 开始分批处理: \(validationResults.count)个地址，分\(totalBatches)批，每批\(batchSize)个")

            for batchIndex in 0..<totalBatches {
                let startIndex = batchIndex * batchSize
                let endIndex = min(startIndex + batchSize, validationResults.count)
                let currentBatch = Array(validationResults[startIndex..<endIndex])

                logInfo("SimpleAddressSheet - 处理第\(batchIndex + 1)/\(totalBatches)批: \(currentBatch.count)个地址")

                for validationResult in currentBatch {
                    logInfo("SimpleAddressSheet - 正在处理验证后的地址: \(validationResult.originalAddress)")

                    // 如果 onAddressAdded 回调存在
                    if let callback = self.onAddressAdded {
                        // 🎯 修复：使用原始地址（包含所有元数据）而不是验证后的地址
                        let tempAddress = SavedAddress(
                            address: validationResult.originalAddress, // 使用包含元数据的原始地址
                            coordinate: validationResult.coordinate,
                            companyName: self.companyName.isEmpty ? nil : self.companyName,
                            url: self.url.isEmpty ? nil : self.url
                        )

                        // 保存验证警告信息到 notes 字段
                        var notesArray: [String] = []
                        if let warningMessage = validationResult.warningMessage, !warningMessage.isEmpty {
                            notesArray.append("VALIDATION_WARNING:\(warningMessage)")
                            logInfo("SimpleAddressSheet - 保存图片识别地址验证警告: \(warningMessage)")
                        }

                        // 🎯 添加用户选择的应用类型信息
                        if appType != .manual && appType != .justPhoto {
                            notesArray.append("USER_SELECTED_APP_TYPE:\(appType.rawValue)")
                            logInfo("SimpleAddressSheet - 批量处理中添加用户选择的应用类型: \(appType.displayName)")
                        }

                        if !notesArray.isEmpty {
                            tempAddress.notes = notesArray.joined(separator: "\n")
                        }

                        // 在主线程调用回调
                        await MainActor.run {
                            callback(tempAddress)
                            successfullyProcessedCount += 1

                            // 更新UI以显示第一个成功处理的地址（清理后的版本）
                            if !firstAddressProcessedForUI {
                                // 🧹 清理地址显示：移除元数据，只显示纯净地址
                                let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(validationResult.validatedAddress)
                                let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                                self.address = cleanAddress
                                self.selectedCoordinate = validationResult.coordinate
                                self.searchResults = [] // 清空搜索结果
                                firstAddressProcessedForUI = true

                                logInfo("🧹 批量处理UI更新: '\(validationResult.validatedAddress)' -> '\(cleanAddress)'")
                            }
                        }
                        logInfo("SimpleAddressSheet - 已为验证后的地址调用回调: \(validationResult.validatedAddress)")
                    } else {
                        logInfo("SimpleAddressSheet - No callback function (onAddressAdded) defined for \(validationResult.originalAddress).")
                    }
                }

                // 🚀 批次间延迟，让UI有机会更新，避免hang
                if batchIndex < totalBatches - 1 {
                    logInfo("SimpleAddressSheet - 批次间延迟0.3秒，避免UI阻塞...")
                    try? await Task.sleep(nanoseconds: 300_000_000) // 0.3秒延迟
                }
            }

            // 所有地址都尝试通过回调传递后
            await MainActor.run {
                logInfo("SimpleAddressSheet - 完成处理 \(addresses.count) 个导入地址，成功回调 \(successfullyProcessedCount) 个。")
                if successfullyProcessedCount > 0 {
                    // 可选：可以向用户显示一个摘要，例如 "成功添加 X 个地址"
                } else if !addresses.isEmpty {
                    // 可选：如果一个都没成功，可以显示提示
                }
                isAddingAddress = false // 重置任何加载状态
                dismiss() // 在所有地址都处理完毕后关闭 sheet

                // 🚨 检查并提示问题地址
                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "图片地址识别")
            }
        }
    }

    // 处理带验证信息的导入地址
    private func processImportedAddressesWithValidation(_ addresses: [(String, CLLocationCoordinate2D, String)]) {
        logInfo("SimpleAddressSheet - 开始处理带验证信息的导入地址: \(addresses.count) 个")

        if addresses.isEmpty {
            logInfo("SimpleAddressSheet - 导入的地址为空，不处理")
            return
        }

        Task {
            // 检查订阅限制
            let subscriptionManager = SubscriptionManager.shared
            let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute

            if maxAllowed != -1 { // -1 表示无限制
                // 获取当前路线的地址数量
                let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
                if let routes = try? modelContext.fetch(descriptor),
                   let currentRoute = routes.first {
                    let currentCount = currentRoute.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
                    let remainingSlots = max(0, maxAllowed - currentCount)

                    // 如果地址数量超过剩余槽位，显示错误信息
                    if addresses.count > remainingSlots {
                        await MainActor.run {
                            if subscriptionManager.currentTier == .free {
                                // 免费用户显示升级提示
                                errorMessage = String(format: "free_version_max_addresses_batch".localized, maxAllowed, currentCount, remainingSlots, addresses.count, addresses.count - remainingSlots)
                            } else {
                                // 付费用户显示一般提示
                                errorMessage = String(format: "paid_version_max_addresses_batch".localized, currentCount, remainingSlots, maxAllowed, addresses.count, addresses.count - remainingSlots)
                            }
                        }
                        return
                    }

                    // 如果地址数量在限制范围内，记录日志
                    logInfo("SimpleAddressSheet - 导入地址在限制范围内: 当前\(currentCount)个，导入\(addresses.count)个，剩余槽位\(remainingSlots)个")
                }
            }

            var successfullyProcessedCount = 0
            var firstAddressProcessedForUI = false

            for (addressText, coordinate, warning) in addresses {
                logInfo("SimpleAddressSheet - 正在处理导入的地址: \(addressText), 验证警告: \(warning)")

                // 如果 onAddressAdded 回调存在
                if let callback = self.onAddressAdded {
                    let tempAddress = SavedAddress(
                        address: addressText,
                        coordinate: coordinate,
                        companyName: self.companyName.isEmpty ? nil : self.companyName,
                        url: self.url.isEmpty ? nil : self.url
                    )

                    // 保存验证警告信息到 notes 字段
                    if !warning.isEmpty {
                        tempAddress.notes = "VALIDATION_WARNING:\(warning)"
                        logInfo("SimpleAddressSheet - 保存验证警告: \(warning)")
                    }

                    // 在主线程调用回调
                    await MainActor.run {
                        callback(tempAddress)
                        successfullyProcessedCount += 1

                        // 更新UI以显示第一个成功处理的地址
                        if !firstAddressProcessedForUI {
                            self.address = addressText
                            self.safeSetCoordinate(coordinate)
                            self.searchResults = [] // 清空搜索结果
                            firstAddressProcessedForUI = true
                        }
                    }
                    logInfo("SimpleAddressSheet - 已为导入的地址调用回调: \(addressText)")
                } else {
                    logInfo("SimpleAddressSheet - No callback function (onAddressAdded) defined for \(addressText).")
                }
            }

            // 所有地址都尝试通过回调传递后
            await MainActor.run {
                logInfo("SimpleAddressSheet - 完成处理 \(addresses.count) 个导入地址，成功回调 \(successfullyProcessedCount) 个。")
                isAddingAddress = false // 重置任何加载状态
                dismiss() // 在所有地址都处理完毕后关闭 sheet

                // 🚨 检查并提示问题地址
                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "地址导入验证")
            }
        }
    }

    // 处理扫描到的代码
    private func handleScannedCode(_ code: String) {
        logInfo("SimpleAddressSheet - 扫描到代码: \(code)")

        // 将扫描到的代码设置为地址
        self.address = code

        // 尝试地理编码获取坐标
        Task {
            // 首先检查订阅限制
            await MainActor.run {
                // 获取当前路线
                let descriptor = FetchDescriptor<Route>()
                if let routes = try? modelContext.fetch(descriptor),
                   let currentRoute = routes.first {

                    // 检查订阅限制
                    let subscriptionManager = SubscriptionManager.shared
                    if !subscriptionManager.canAddMoreStops(to: currentRoute) {
                        // 显示错误信息
                        errorMessage = String(format: "free_version_max_addresses_single".localized, subscriptionManager.currentTier.maxStopsPerRoute)
                        logInfo("SimpleAddressSheet - 扫描地址超过免费版限制: 当前\(currentRoute.points.count)个，最大允许\(subscriptionManager.currentTier.maxStopsPerRoute)个")
                        return
                    }
                }
            }

            if let coordinate = await geocodeAddress(code) {
                // 如果成功获取坐标，直接保存地址
                await MainActor.run {
                    self.safeSetCoordinate(coordinate)
                    logInfo("SimpleAddressSheet - 成功获取扫描地址坐标，准备保存: \(code)")

                    // 格式化扫描到的地址
                    let formattedAddress = code

                    // 保存到路线和地址簿
                    saveSelectedAddress(address: formattedAddress, coordinate: coordinate)
                }
            } else {
                // 如果无法获取坐标，显示错误信息
                await MainActor.run {
                    errorMessage = "cannot_get_coordinates_scan_retry".localized
                    logError("SimpleAddressSheet - 无法获取扫描地址的坐标: \(code)")
                }
            }
        }
    }

    // 地理编码辅助方法 - 使用与搜索界面相同的全球地址处理器
    private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
        logInfo("🌍 SimpleAddressSheet - 使用全球地址处理器处理AI扫描地址: \(address)")

        // 使用与搜索界面相同的全球地址处理器
        let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(address)

        switch globalResult {
        case .success(_, let formattedAddress, let coordinate, _, let strategy, let confidence):
            logInfo("🌍 SimpleAddressSheet - AI扫描地址处理成功: \(strategy) - \(formattedAddress) (置信度: \(confidence))")
            return coordinate

        case .failed(let address, let reason):
            logWarning("SimpleAddressSheet - AI扫描地址处理失败: \(reason)")

            // 回退到原始的CLGeocoder方法
            let geocoder = CLGeocoder()
            do {
                // 🎯 创建地理编码请求，强制使用英文locale
                let placemarks = try await geocoder.geocodeAddressString(
                    address,
                    in: nil,
                    preferredLocale: Locale(identifier: "en_US")
                )

            if !placemarks.isEmpty {
                logInfo("SimpleAddressSheet - 地理编码返回了 \(placemarks.count) 个结果")

                // 打印所有结果的详细信息以便调试
                for (index, placemark) in placemarks.enumerated() {
                    if let location = placemark.location?.coordinate {
                        let country = placemark.country ?? "unknown_country".localized
                        let locality = placemark.locality ?? "unknown_city".localized
                        logInfo("SimpleAddressSheet - 地理编码结果[\(index)]: \(address) -> (\(location.latitude), \(location.longitude)), 国家: \(country), 城市: \(locality)")
                    }
                }

                // 使用第一个结果并验证坐标有效性
                if let location = placemarks.first?.location?.coordinate {
                    let country = placemarks.first?.country ?? "unknown_country".localized
                    logInfo("SimpleAddressSheet - 地理编码结果: \(address) -> (\(location.latitude), \(location.longitude)), 国家: \(country)")

                    // 验证坐标有效性
                    let userLocation = LocationManager.shared.userLocation
                    let validationResult = DeliveryPoint.validateCoordinatesGlobally(
                        latitude: location.latitude,
                        longitude: location.longitude,
                        userLocation: userLocation
                    )

                    // 只有在坐标有效时才返回，否则返回nil让用户知道需要修正
                    if validationResult.isValid {
                        return location
                    } else {
                        logError("SimpleAddressSheet - 地理编码返回无效坐标: \(address) -> (\(location.latitude), \(location.longitude)), 原因: \(validationResult.warning ?? "坐标无效")")
                        return nil
                    }
                }
            }
            } catch {
                logError("SimpleAddressSheet - 回退地理编码失败: \(address) - \(error)")
            }

            return nil
        }
    }

    // 原来的保存地址方法 (保留作为备用)
    private func addSavedAddress() {
        guard !address.isEmpty else {
            errorMessage = "please_enter_valid_address".localized
            return
        }

        guard let coordinate = selectedCoordinate else {
            errorMessage = "please_select_valid_address".localized
            return
        }

        isAddingAddress = true
        errorMessage = nil

        Task {
            do {
                // 🎯 标准化街道简称后创建新的保存地址
                let standardizedAddress = StreetAbbreviationStandardizer.standardizeStreetAbbreviations(address)
                let newAddress = SavedAddress(
                    address: standardizedAddress,
                    coordinate: coordinate
                )

                // 设置收藏状态
                newAddress.isFavorite = isFavorite

                // 保存到数据库
                modelContext.insert(newAddress)
                try modelContext.save()

                // 记录日志
                logInfo("SimpleAddressSheet - 成功添加保存地址: \(address)")

                // 回调通知
                onAddressAdded?(newAddress)

                // 关闭表单
                await MainActor.run {
                    isAddingAddress = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isAddingAddress = false
                    errorMessage = String(format: "add_address_failed".localized, error.localizedDescription)
                    logError("SimpleAddressSheet - 添加地址失败: \(error.localizedDescription)")
                }
            }
        }
    }



    // 使用当前位置
    private func useCurrentLocation() {
        logInfo("SimpleAddressSheet - 🎯 点击使用当前位置按钮")

        // 🔧 修复：使用LocationManager单例检查权限，避免创建多个实例
        guard LocationManager.shared.hasLocationPermission() else {
            errorMessage = "location_permission_required_for_current_location".localized
            logError("SimpleAddressSheet - 位置权限未授权: \(LocationManager.shared.authorizationStatus.rawValue)")
            return
        }

        // 显示加载状态
        isAddingAddress = true
        errorMessage = nil

        // 🔧 修复：直接请求新的位置而不是使用可能过时的缓存位置
        Task {
            do {
                // 🎯 修复：强制请求新的GPS位置，避免使用可能错误的缓存位置
                logInfo("SimpleAddressSheet - 🛰️ 强制请求新的GPS位置更新")
                LocationManager.shared.requestLocation()

                // 等待GPS位置更新
                try await Task.sleep(nanoseconds: 3_000_000_000) // 3秒，给GPS更多时间

                // 首先检查LocationManager是否有真实的GPS位置
                let locationSource = LocationManager.shared.diagnoseLocationSource()
                logInfo("SimpleAddressSheet - 📍 当前位置来源: \(locationSource)")

                // 🚨 修复：如果仍然是默认位置，再次尝试获取真实位置
                if locationSource.contains("默认位置") || locationSource.contains("强制默认位置") {
                    logInfo("SimpleAddressSheet - ⚠️ 仍在使用默认位置，再次尝试获取真实GPS位置")

                    // 再次请求位置更新
                    LocationManager.shared.startUpdatingLocation()

                    // 再等待一段时间
                    try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

                    // 停止位置更新
                    LocationManager.shared.stopUpdatingLocation()
                }

                // 再次检查位置
                guard let userLocation = LocationManager.shared.userLocation else {
                    await MainActor.run {
                        self.errorMessage = "cannot_get_current_location_check_settings".localized
                        self.isAddingAddress = false
                    }
                    logError("SimpleAddressSheet - ❌ 无法获取用户位置")
                    return
                }

                // 🎯 修复：验证获取到的位置是否合理，避免使用错误的坐标
                let finalLocationSource = LocationManager.shared.diagnoseLocationSource()
                logInfo("SimpleAddressSheet - ✅ 获取到用户位置: (\(userLocation.latitude), \(userLocation.longitude))")
                logInfo("SimpleAddressSheet - 📊 最终位置来源: \(finalLocationSource)")

                // 🚨 安全检查：如果仍然是默认位置且坐标看起来不合理，提醒用户
                if finalLocationSource.contains("默认位置") {
                    logInfo("SimpleAddressSheet - ⚠️ 警告：仍在使用默认位置，可能不是真实GPS位置")
                }

                // 使用逆地理编码获取地址
                logInfo("SimpleAddressSheet - 🔄 开始反向地理编码当前位置")
                logInfo("SimpleAddressSheet - 📍 当前坐标: (\(userLocation.latitude), \(userLocation.longitude))")

                let geocoder = CLGeocoder()
                let location = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)

                // 🎯 反向地理编码，强制使用英文locale
                let placemarks = try await geocoder.reverseGeocodeLocation(
                    location,
                    preferredLocale: Locale(identifier: "en_US")
                )

                guard let placemark = placemarks.first else {
                    await MainActor.run {
                        self.errorMessage = "cannot_get_current_location_address".localized
                        self.isAddingAddress = false
                    }
                    logError("SimpleAddressSheet - ❌ 反向地理编码无结果: 坐标(\(userLocation.latitude), \(userLocation.longitude))")
                    return
                }

                // 记录详细的placemark信息
                logInfo("SimpleAddressSheet - 🏠 反向地理编码Placemark详情:")
                logInfo("SimpleAddressSheet -   门牌号: '\(placemark.subThoroughfare ?? "无")'")
                logInfo("SimpleAddressSheet -   街道: '\(placemark.thoroughfare ?? "无")'")
                logInfo("SimpleAddressSheet -   区域: '\(placemark.locality ?? "无")'")
                logInfo("SimpleAddressSheet -   行政区: '\(placemark.administrativeArea ?? "无")'")
                logInfo("SimpleAddressSheet -   邮编: '\(placemark.postalCode ?? "无")'")
                logInfo("SimpleAddressSheet -   国家: '\(placemark.country ?? "无")'")
                logInfo("SimpleAddressSheet -   名称: '\(placemark.name ?? "无")'")
                logInfo("SimpleAddressSheet -   时区: '\(placemark.timeZone?.identifier ?? "无")'")
                logInfo("SimpleAddressSheet -   坐标: (\(placemark.location?.coordinate.latitude ?? 0), \(placemark.location?.coordinate.longitude ?? 0))")

                // 🎯 修复：构建完整地址字符串，确保包含所有必要组件
                var addressComponents: [String] = []

                // 构建街道地址部分（门牌号 + 街道名）
                var streetAddress = ""
                if let subThoroughfare = placemark.subThoroughfare {
                    streetAddress = subThoroughfare
                }
                if let thoroughfare = placemark.thoroughfare {
                    if !streetAddress.isEmpty {
                        streetAddress += " \(thoroughfare)"
                    } else {
                        streetAddress = thoroughfare
                    }
                }

                // 只有当街道地址不为空时才添加
                if !streetAddress.isEmpty {
                    addressComponents.append(streetAddress)
                }

                // 添加城市/地区
                if let locality = placemark.locality {
                    addressComponents.append(locality)
                }

                // 添加州/省
                if let administrativeArea = placemark.administrativeArea {
                    addressComponents.append(administrativeArea)
                }

                // 添加邮编
                if let postalCode = placemark.postalCode {
                    addressComponents.append(postalCode)
                }

                // 添加国家（使用国家代码或国家名）
                if let countryCode = placemark.isoCountryCode {
                    addressComponents.append(countryCode)
                } else if let country = placemark.country {
                    addressComponents.append(country)
                }

                let formattedAddress = addressComponents.joined(separator: ", ")

                await MainActor.run {
                    logInfo("SimpleAddressSheet - 当前位置地址: \(formattedAddress)")

                    // 更新界面
                    self.address = formattedAddress
                    self.selectedCoordinate = userLocation
                    self.searchResults = []
                    self.isAddingAddress = false

                    // 🎯 修复：对于当前位置，创建特殊的SavedAddress对象，包含placemark信息
                    self.saveCurrentLocationAddress(
                        formattedAddress: formattedAddress,
                        coordinate: userLocation,
                        placemark: placemark
                    )
                }

            } catch {
                await MainActor.run {
                    self.errorMessage = String(format: "get_current_location_failed".localized, error.localizedDescription)
                    self.isAddingAddress = false
                }
                logError("SimpleAddressSheet - 获取当前位置失败: \(error.localizedDescription)")
            }
        }
    }

    // 🎯 新增：专门处理当前位置地址保存的方法
    private func saveCurrentLocationAddress(formattedAddress: String, coordinate: CLLocationCoordinate2D, placemark: CLPlacemark) {
        isAddingAddress = true
        errorMessage = nil

        // 🔍 详细日志：当前位置保存开始
        logInfo("🌍 SimpleAddressSheet - saveCurrentLocationAddress 开始")
        logInfo("📍 SimpleAddressSheet - 格式化地址: \(formattedAddress)")
        logInfo("📍 SimpleAddressSheet - 坐标: (\(coordinate.latitude), \(coordinate.longitude))")
        logInfo("📍 SimpleAddressSheet - Placemark详情:")
        logInfo("   - thoroughfare: \(placemark.thoroughfare ?? "nil")")
        logInfo("   - subThoroughfare: \(placemark.subThoroughfare ?? "nil")")
        logInfo("   - locality: \(placemark.locality ?? "nil")")
        logInfo("   - subLocality: \(placemark.subLocality ?? "nil")")
        logInfo("   - administrativeArea: \(placemark.administrativeArea ?? "nil")")
        logInfo("   - country: \(placemark.country ?? "nil")")

        // 保存到地址簿（如果需要）
        if saveToAddressBook {
            logInfo("💾 SimpleAddressSheet - 保存到地址簿")
            saveAddressToAddressBook(address: formattedAddress, coordinate: coordinate)
        }

        logInfo("🔄 SimpleAddressSheet - 当前位置地址选择，准备通过回调传递: \(formattedAddress)")

        // 🎯 关键修复：创建特殊的SavedAddress对象，使用特殊标记表示这是当前位置
        let savedAddress = SavedAddress(
            address: formattedAddress,
            coordinate: coordinate
        )

        // 使用notes字段传递placemark信息的标记
        savedAddress.notes = "CURRENT_LOCATION_WITH_PLACEMARK"
        logInfo("🏷️ SimpleAddressSheet - 设置特殊标记: CURRENT_LOCATION_WITH_PLACEMARK")

        // 通过回调传递地址，让接收方知道这是当前位置，需要特殊处理
        if let onAddressAdded = onAddressAdded {
            logInfo("📞 SimpleAddressSheet - 调用onAddressAdded回调，传递当前位置地址")
            onAddressAdded(savedAddress)
        } else {
            logError("❌ SimpleAddressSheet - onAddressAdded回调为nil！")
        }

        // 关闭界面
        logInfo("🚪 SimpleAddressSheet - 关闭界面")
        dismiss()
    }
}



// 搜索自动完成代理
class SimpleAddressCompleterDelegate: NSObject, MKLocalSearchCompleterDelegate {
    var onUpdateResults: ([MKLocalSearchCompletion]) -> Void

    init(onUpdateResults: @escaping ([MKLocalSearchCompletion]) -> Void) {
        self.onUpdateResults = onUpdateResults
        super.init()
    }

    func completerDidUpdateResults(_ completer: MKLocalSearchCompleter) {
        print("🚨🚨🚨 代理收到搜索结果！🚨🚨🚨")
        logInfo("🎯 SimpleAddressCompleterDelegate - 收到搜索结果")
        logInfo("🎯 结果数量: \(completer.results.count)")
        logInfo("🎯 查询片段: '\(completer.queryFragment)'")
        print("🎯 结果数量: \(completer.results.count)")
        print("🎯 查询片段: '\(completer.queryFragment)'")

        for (index, result) in completer.results.enumerated() {
            logInfo("🎯 结果[\(index)]: '\(result.title)' - '\(result.subtitle)'")
            print("🎯 结果[\(index)]: '\(result.title)' - '\(result.subtitle)'")
        }

        onUpdateResults(completer.results)
        logInfo("🎯 已调用结果回调")
        print("🎯 已调用结果回调")
    }

    func completer(_ completer: MKLocalSearchCompleter, didFailWithError error: Error) {
        let errorCode = (error as NSError).code
        let errorDomain = (error as NSError).domain
        print("🚨🚨🚨 代理收到搜索错误！🚨🚨🚨")
        logError("❌ SimpleAddressCompleterDelegate - 地址自动完成错误")
        logError("❌ 错误描述: \(error.localizedDescription)")
        logError("❌ 错误域: \(errorDomain)")
        logError("❌ 错误代码: \(errorCode)")
        logError("❌ 查询片段: '\(completer.queryFragment)'")
        print("❌ 错误描述: \(error.localizedDescription)")
        print("❌ 错误域: \(errorDomain)")
        print("❌ 错误代码: \(errorCode)")
        print("❌ 查询片段: '\(completer.queryFragment)'")
    }
}
// MARK: - SimpleAddressSheet 地址州修复扩展
extension SimpleAddressSheet {
    /// 检查并修复缺少州信息的地址
    /// - Parameter address: 原始地址
    /// - Returns: 修复后的地址（如果不需要修复则返回原地址）
    private func fixAddressStateIfNeeded(_ address: String) async -> String {
        // 使用 AddressStateFixService 检测并修复地址
        if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: address) {
            logInfo("🔧 SimpleAddressSheet - 地址州修复成功: \(address) -> \(fixedAddress)")
            return fixedAddress
        }

        // 如果不需要修复，返回原地址
        return address
    }

    // MARK: - 超快速地址处理

    /// 超快速地址处理 - 避免hang问题
    private func processAddressUltraFast(_ address: String) async -> LightweightGeocodingResult {
        logInfo("⚡ 开始超快速地址处理: \(address)")

        // 检查是否已经有坐标信息（从搜索结果中获得）
        if let existingCoordinate = self.selectedCoordinate {
            logInfo("⚡ 使用现有坐标信息，跳过地理编码")

            // 创建一个简单的placemark - 使用可用的初始化方法
            let placemark = MKPlacemark(coordinate: existingCoordinate)

            return .success(
                originalAddress: address,
                formattedAddress: address,
                coordinate: existingCoordinate,
                placemark: placemark
            )
        }

        // 如果没有坐标，进行最简单的地理编码（无速率限制等待）
        logInfo("⚡ 执行简单地理编码（无等待）")

        let geocoder = CLGeocoder()

        // 1秒超时
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 1_000_000_000)
            geocoder.cancelGeocode()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            let placemarks = try await geocoder.geocodeAddressString(
                address,
                in: nil,
                preferredLocale: Locale(identifier: "en_US")
            )

            guard let placemark = placemarks.first,
                  let location = placemark.location else {
                logWarning("⚡ 地理编码无结果，使用默认坐标")
                return .failed(reason: "无法获取地址坐标")
            }

            logInfo("⚡ 地理编码成功")
            return .success(
                originalAddress: address,
                formattedAddress: address,
                coordinate: location.coordinate,
                placemark: placemark
            )

        } catch {
            logWarning("⚡ 地理编码失败，使用默认处理: \(error.localizedDescription)")
            return .failed(reason: "地理编码失败")
        }
    }
}











// 预览
struct SimpleAddressSheetPreview: View {
    @State private var pointToEdit: DeliveryPoint? = nil

    var body: some View {
        // 使用持久化存储而非内存存储
        let schema = Schema([SavedAddress.self, Route.self, DeliveryPoint.self])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
        var container: ModelContainer

        do {
            let tempContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
            container = tempContainer
        } catch {
            print("[INFO] 预览创建容器失败，使用共享容器")
            container = getPersistentContainer()
        }

        return SimpleAddressSheet(
            onAddressAdded: { _ in },
            addressPointType: .stop,
            saveToAddressBook: false,
            pointToEdit: $pointToEdit
        )
        .modelContainer(container)
    }
}

#Preview("SimpleAddressSheet") {
    SimpleAddressSheetPreview()
}
