import SwiftUI
import CoreLocation
import os.log
import SwiftData
import UIKit
import UniformTypeIdentifiers
import Combine
import Foundation
import MapKit

// 定义Apple地理编码选项常量
private let CLGeocodeAppleOptionCountryKey = "country"
private let CLGeocodeAppleOptionStateKey = "state"
private let CLGeocodeAppleOptionPostalCodeKey = "postalCode"

// 导入订阅管理器
// 移除NaviBatch导入，直接使用本地类型

// 警告项
fileprivate struct WebAlertItem: Identifiable {
    let id = UUID()
    let title: String
    let message: String
    let primaryButton: Alert.Button

    init(title: String = "prompt".localized, message: String, primaryButton: Alert.Button = .default(Text("confirm".localized))) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
    }
}

/// 线上下载表单
/// 支持从URL下载地址数据
struct WebDownloadSheet: View {
    @Environment(\.dismiss) private var dismiss
    @State private var urlString: String = ""
    @State private var companyName: String = ""
    @State private var isDownloading: Bool = false
    @State private var isProcessing: Bool = false
    @State private var downloadedAddresses: [(String, CLLocationCoordinate2D, Bool)] = [] // 地址, 坐标, 是否选中
    @State private var processingProgress: Double = 0
    @State private var errorMessage: String? = nil
    @State private var showingHistory: Bool = false
    @State private var alertItem: WebAlertItem? = nil
    @State private var showSubscriptionView = false

    // 历史记录
    @State private var downloadHistory: [DownloadHistoryItem] = []

    // 历史列表视图
    private var historyListView: some View {
        List {
            ForEach(downloadHistory) { item in
                Button {
                    urlString = item.url
                    companyName = item.companyName
                } label: {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(item.url)
                                .font(.subheadline)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            HStack(spacing: 6) {
                                if !item.companyName.isEmpty {
                                    Text(item.companyName)
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                        .padding(.vertical, 2)
                                        .padding(.horizontal, 6)
                                        .background(Color.blue.opacity(0.1))
                                        .cornerRadius(4)
                                }

                                Text(item.formattedDate)
                                Text("•")
                                Text("\(item.count) 个地址")
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                        }

                        Spacer()

                        Image(systemName: "arrow.up.forward.app")
                            .foregroundColor(.blue)
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .frame(minHeight: 100) // 最小高度保证内容可见
    }

    // 回调函数
    var onAddressesConfirmed: ([(String, CLLocationCoordinate2D)]) -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {

                ZStack {
                    // 添加背景点击手势来关闭键盘
                    Color.clear
                        .contentShape(Rectangle())
                        .onTapGesture {
                            hideKeyboard()
                        }

                    VStack(spacing: 0) {
                    // URL输入区域
                    VStack(alignment: .leading, spacing: 12) { // 减少间距
                        // 移除了"从网络下载地址"标题，因为导航栏已经有"线上下载"标题
                        Spacer().frame(height: 8) // 保留一些顶部间距

                    // URL输入框
                    VStack(spacing: 8) { // 减少间距
                        HStack {
                            Image(systemName: "link")
                                .foregroundColor(.secondary)
                                .font(.system(size: 14)) // 减小图标大小

                            TextField("input_address_data_url".localized, text: $urlString)
                                .autocapitalization(.none)
                                .keyboardType(.URL)
                                .disableAutocorrection(true)
                                .font(.system(size: 14)) // 减小字体大小

                            if !urlString.isEmpty {
                                Button {
                                    urlString = ""
                                } label: {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.gray)
                                        .font(.system(size: 14)) // 减小图标大小
                                }
                            }
                        }
                        .padding(10) // 减少内边距
                        .background(Color(.systemGray6))
                        .cornerRadius(8) // 减小圆角

                        // 公司名称输入框
                        HStack {
                            Image(systemName: "building.2")
                                .foregroundColor(.secondary)
                                .font(.system(size: 14)) // 减小图标大小

                            TextField("company_name_optional".localized, text: $companyName)
                                .disableAutocorrection(true)
                                .font(.system(size: 14)) // 减小字体大小

                            if !companyName.isEmpty {
                                Button {
                                    companyName = ""
                                } label: {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.gray)
                                        .font(.system(size: 14)) // 减小图标大小
                                }
                            }
                        }
                        .padding(10) // 减少内边距
                        .background(Color(.systemGray6))
                        .cornerRadius(8) // 减小圆角
                    }
                    .padding(.horizontal)

                    // 支持的格式说明
                    VStack(alignment: .leading, spacing: 4) { // 减少间距
                        Text("supported_formats".localized)
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        VStack(alignment: .leading, spacing: 2) { // 减少间距
                            Text("supported_format_csv".localized)
                            Text("supported_format_json".localized)
                            Text("supported_format_text".localized)
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8) // 减少垂直间距

                // 下载历史区域
                VStack(spacing: 0) {
                    // 下载历史按钮
                    Button {
                        showingHistory.toggle()
                    } label: {
                        HStack {
                            Image(systemName: "clock.arrow.circlepath")
                            Text("download_history".localized)
                            Spacer()
                            Image(systemName: showingHistory ? "chevron.up" : "chevron.down")
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)

                    // 下载历史列表
                    if showingHistory {
                        GeometryReader { geometry in
                            historyListView
                                .frame(minHeight: min(200, geometry.size.height),
                                       maxHeight: downloadHistory.count > 2 ? geometry.size.height : nil)
                                .listStyle(PlainListStyle())
                                .background(Color(.systemGray6).opacity(0.5))
                                .cornerRadius(10)
                                .padding(.horizontal)
                                .padding(.top, 8)
                        }
                        .frame(maxHeight: downloadHistory.isEmpty ? 100 : (downloadHistory.count > 2 ? .infinity : 200))
                    }
                }

                // 导入按钮
                Button {
                    hideKeyboard() // 隐藏键盘
                    startDownload()
                } label: {
                    HStack {
                        Image(systemName: "arrow.down.circle")
                        Text("import_addresses".localized)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(urlString.isEmpty ? Color.blue.opacity(0.3) : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    .padding(.horizontal)
                    .padding(.top, 16)
                }
                .disabled(urlString.isEmpty || isDownloading)

                // 处理中状态
                if isDownloading || isProcessing {
                    VStack(spacing: 12) {
                        ProgressView(value: processingProgress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle())

                        Text(isDownloading ? "downloading".localized : "processing_data".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }

                // 下载的地址列表
                if !downloadedAddresses.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("下载的地址")
                                .font(.headline)

                            Spacer()

                            Text("\(downloadedAddresses.filter { $0.2 }.count)/\(downloadedAddresses.count) 已选择")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)

                        List {
                            ForEach(0..<downloadedAddresses.count, id: \.self) { index in
                                let (address, coordinate, isSelected) = downloadedAddresses[index]

                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(address)
                                            .font(.subheadline)
                                            .lineLimit(2)

                                        Text("\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude))")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }

                                    Spacer()

                                    // 选择按钮
                                    Button {
                                        toggleAddressSelection(at: index)
                                    } label: {
                                        Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                            .foregroundColor(isSelected ? .blue : .gray)
                                            .font(.title3)
                                    }
                                }
                                .padding(.vertical, 4)
                            }
                        }
                        .listStyle(PlainListStyle())
                    }
                }

                Spacer()

                // 🎯 优化已下载地址确认按钮的视觉设计
                if !downloadedAddresses.isEmpty {
                    Button {
                        hideKeyboard() // 隐藏键盘

                        // 添加防重复点击和状态控制
                        guard !isProcessing else { return }
                        isProcessing = true

                        // 使用Task确保异步处理正确
                        Task {
                            // 添加小延迟，让UI更新有时间响应
                            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                            await MainActor.run {
                                confirmSelectedAddresses()
                                // 处理完成后重置状态
                                isProcessing = false
                            }
                        }
                    } label: {
                        HStack(spacing: 10) {
                            // 根据处理状态显示不同图标
                            if isProcessing {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .tint(.white)
                                    .scaleEffect(0.9)
                            } else {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            Text(isProcessing ? "处理中..." : "确认导入所选地址")
                                .font(.system(size: 17, weight: .semibold))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .padding(.horizontal, 20)
                        .background(
                            Group {
                                if downloadedAddresses.filter({ $0.2 }).isEmpty || isProcessing {
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.gray.opacity(0.4))
                                } else {
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .shadow(color: Color.blue.opacity(0.3), radius: 8, x: 0, y: 4)
                                }
                            }
                        )
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .scaleEffect((downloadedAddresses.filter { $0.2 }.isEmpty || isProcessing) ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: downloadedAddresses.filter { $0.2 }.isEmpty || isProcessing)
                    }
                    .disabled(downloadedAddresses.filter { $0.2 }.isEmpty || isDownloading || isProcessing)
                    .padding(.bottom, 20)
                }
            }
            }
            }
            .navigationTitle("web_download".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 20))
                    }
                }
            }
            .alert(item: $alertItem) { alert in
                Alert(
                    title: Text(alert.title),
                    message: Text(alert.message),
                    dismissButton: alert.primaryButton
                )
            }
            .sheet(isPresented: $showSubscriptionView) {
                // 显示订阅界面
                UnifiedSubscriptionView(config: SubscriptionViewConfig(
                    style: .modern,
                    showTitle: true,
                    showCloseButton: true,
                    showFeatureComparison: true,
                    showOneClickPromo: true,
                    presentationMode: .sheet
                ))
                .presentationDetents([.large])
                .presentationDragIndicator(.hidden)
            }
        }
    }

    // 开始下载
    private func startDownload() {
        guard !urlString.isEmpty else { return }

        // 清理URL字符串，移除多余的斜杠
        let cleanedURLString = cleanupURL(urlString)

        guard let url = URL(string: cleanedURLString) else {
            showAlert(title: "URL错误", message: "无效的URL格式，请检查输入")
            logError("WebDownloadSheet - 无效的URL: \(urlString) -> \(cleanedURLString)")
            return
        }

        logInfo("WebDownloadSheet - 开始从URL下载: \(cleanedURLString)")
        isDownloading = true
        processingProgress = 0
        downloadedAddresses = []

        // 创建配置，增加超时时间
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60.0  // 增加到60秒
        config.timeoutIntervalForResource = 90.0 // 资源超时增加到90秒
        config.waitsForConnectivity = true       // 等待连接可用

        // 创建自定义会话
        let session = URLSession(configuration: config)

        // 创建请求对象以便添加更多配置
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.cachePolicy = .reloadIgnoringLocalCacheData // 忽略缓存
        request.timeoutInterval = 60.0 // 再次确认超时设置

        // 添加常见的请求头
        request.addValue("application/json,text/plain,text/html,*/*", forHTTPHeaderField: "Accept")
        request.addValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        request.addValue("zh-CN,zh;q=0.9,en;q=0.8", forHTTPHeaderField: "Accept-Language")
        request.addValue("NaviBatch/1.0", forHTTPHeaderField: "User-Agent")

        // 创建下载任务
        let task = session.dataTask(with: request) { data, response, error in

            // 处理错误情况
            if let error = error as NSError? {
                DispatchQueue.main.async {
                    // 根据错误类型提供更具体的错误信息
                    let errorMessage: String

                    switch error.code {
                    case NSURLErrorTimedOut:
                        errorMessage = "下载超时，请检查网络连接或稍后重试"
                    case NSURLErrorNotConnectedToInternet:
                        errorMessage = "无网络连接，请检查您的网络设置"
                    case NSURLErrorCannotFindHost, NSURLErrorCannotConnectToHost:
                        errorMessage = "无法连接到服务器，请检查URL是否正确"
                    case NSURLErrorSecureConnectionFailed, NSURLErrorServerCertificateHasBadDate,
                         NSURLErrorServerCertificateUntrusted, NSURLErrorServerCertificateHasUnknownRoot:
                        errorMessage = "安全连接失败，请检查URL或网络设置"
                    case -1017: // cannot parse response，通常是Google Drive的特殊响应
                        if self.isGoogleDriveURL(self.urlString) {
                            errorMessage = "无法解析Google Drive响应，文件可能需要确认下载或过大"
                        } else {
                            errorMessage = "无法解析服务器响应: \(error.localizedDescription)"
                        }
                    default:
                        errorMessage = "下载失败: \(error.localizedDescription)"
                    }

                    self.showAlert(title: "下载失败", message: errorMessage)
                    self.isDownloading = false
                    logError("WebDownloadSheet - 下载失败: \(error.localizedDescription), 错误代码: \(error.code)")

                    // 如果是超时错误，提供重试选项
                    if error.code == NSURLErrorTimedOut {
                        self.alertItem = WebAlertItem(
                            title: "下载超时",
                            message: "下载文件时连接超时。您想重试下载吗？",
                            primaryButton: .default(Text("重试")) {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    self.startDownload() // 重试下载
                                }
                            }
                        )
                    }

                    // 如果是Google Drive链接且出错，提供特殊提示
                    if self.isGoogleDriveURL(self.urlString) {
                        self.alertItem = WebAlertItem(
                            title: "Google Drive下载失败",
                            message: "从Google Drive下载文件失败。请确保：\n\n1. 文件已设置为'任何人都可以查看'\n2. 链接格式正确\n3. 文件大小不超过限制\n\n您可以尝试将文件下载到本地，然后使用文件导入功能。",
                            primaryButton: .default(Text("确定"))
                        )
                    }
                }
                return
            }

            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    self.showAlert(title: "响应错误", message: "服务器返回不是HTTP响应")
                    self.isDownloading = false
                    logError("WebDownloadSheet - 服务器返回不是HTTP响应")
                }
                return
            }

            logInfo("WebDownloadSheet - 收到HTTP响应: 状态码 \(httpResponse.statusCode), 内容类型: \(httpResponse.mimeType ?? "未知")")

            // 检查HTTP状态码
            guard (200...299).contains(httpResponse.statusCode) else {
                DispatchQueue.main.async {
                    var statusMessage: String
                    switch httpResponse.statusCode {
                    case 404:
                        statusMessage = "文件未找到 (404)，请检查URL是否正确"
                    case 403:
                        statusMessage = "访问被拒绝 (403)，没有权限访问此文件"
                    case 500, 502, 503, 504:
                        statusMessage = "服务器错误 (\(httpResponse.statusCode))，请稍后重试"
                    default:
                        statusMessage = "服务器返回错误: HTTP \(httpResponse.statusCode)"
                    }

                    // 针对Google Drive的特殊处理
                    if self.isGoogleDriveURL(self.urlString) {
                        if httpResponse.statusCode == 403 {
                            statusMessage = "无法访问Google Drive文件 (403)。请确保文件已设置为'任何人都可以查看'。"
                        } else if httpResponse.statusCode == 404 {
                            statusMessage = "Google Drive文件未找到 (404)。请检查链接是否正确或文件是否已被删除。"
                        } else if httpResponse.statusCode == 429 {
                            statusMessage = "Google Drive请求过多 (429)。请稍后重试。"
                        } else if httpResponse.statusCode >= 500 {
                            statusMessage = "Google Drive服务器错误 (\(httpResponse.statusCode))。请稍后重试。"
                        }

                        self.alertItem = WebAlertItem(
                            title: "Google Drive错误",
                            message: statusMessage + "\n\n您可以尝试将文件下载到本地，然后使用文件导入功能。",
                            primaryButton: .default(Text("确定"))
                        )
                    } else {
                        self.showAlert(title: "服务器错误", message: statusMessage)
                    }

                    self.isDownloading = false
                    logError("WebDownloadSheet - 服务器返回错误: HTTP \(httpResponse.statusCode)")
                }
                return
            }

            // 检查数据
            guard let data = data, !data.isEmpty else {
                DispatchQueue.main.async {
                    self.showAlert(title: "数据错误", message: "下载的数据为空")
                    self.isDownloading = false
                    logError("WebDownloadSheet - 下载的数据为空")
                }
                return
            }

            logInfo("WebDownloadSheet - 成功下载数据: \(data.count) 字节, 内容类型: \(httpResponse.mimeType ?? "未知")")

            // 检查是否是Google Drive的HTML响应（通常包含确认下载按钮）
            if self.isGoogleDriveURL(self.urlString) &&
               (httpResponse.mimeType?.contains("text/html") == true ||
                httpResponse.mimeType?.contains("application/xhtml") == true) {

                // 尝试从HTML中提取直接下载链接
                if let htmlString = String(data: data, encoding: .utf8),
                   let directLink = self.extractDownloadLinkFromGoogleDriveHTML(htmlString) {

                    logInfo("WebDownloadSheet - 从Google Drive HTML响应中提取到直接下载链接，重新尝试下载")

                    // 创建新的请求
                    let newRequest = URLRequest(url: URL(string: directLink)!)
                    let newTask = session.dataTask(with: newRequest) { newData, newResponse, newError in
                        if let newError = newError {
                            DispatchQueue.main.async {
                                self.showAlert(title: "Google Drive下载失败",
                                               message: "二次尝试下载失败: \(newError.localizedDescription)")
                                self.isDownloading = false
                                logError("WebDownloadSheet - 二次下载失败: \(newError.localizedDescription)")
                            }
                            return
                        }

                        guard let newData = newData, !newData.isEmpty,
                              let newHttpResponse = newResponse as? HTTPURLResponse,
                              (200...299).contains(newHttpResponse.statusCode) else {
                            DispatchQueue.main.async {
                                self.showAlert(title: "google_drive_download_failed".localized,
                                               message: "second_attempt_invalid_data".localized)
                                self.isDownloading = false
                                logError("WebDownloadSheet - 二次下载返回无效数据")
                            }
                            return
                        }

                        // 成功获取数据，处理
                        DispatchQueue.main.async {
                            self.isDownloading = false
                            self.isProcessing = true
                            self.processDownloadedData(newData, contentType: newHttpResponse.mimeType)
                        }
                    }

                    newTask.resume()
                    return
                } else {
                    // 无法从HTML中提取下载链接
                    DispatchQueue.main.async {
                        self.alertItem = WebAlertItem(
                            title: "Google Drive下载失败",
                            message: "Google Drive返回了确认页面，但无法自动处理。请尝试：\n\n1. 确保文件已设置为'任何人都可以查看'\n2. 将文件下载到本地，然后使用文件导入功能",
                            primaryButton: .default(Text("确定"))
                        )
                        self.isDownloading = false
                        logError("WebDownloadSheet - Google Drive返回HTML确认页面，无法自动处理")
                    }
                    return
                }
            }

            // 下载完成，开始处理数据
            DispatchQueue.main.async {
                self.isDownloading = false
                self.isProcessing = true
                self.processDownloadedData(data, contentType: httpResponse.mimeType)
            }
        }

        task.resume()
    }

    // 清理URL字符串
    private func cleanupURL(_ urlString: String) -> String {
        var cleanedURL = urlString.trimmingCharacters(in: .whitespacesAndNewlines)

        // 检查是否是Google Drive链接，如果是则转换
        if isGoogleDriveURL(cleanedURL) {
            if let directURL = convertGoogleDriveURL(cleanedURL) {
                logInfo("WebDownloadSheet - 转换Google Drive链接: \(cleanedURL) -> \(directURL)")
                return directURL
            }
        }

        // 修复常见URL问题

        // 1. 修复双斜杠问题 (除了协议部分)
        if let protocolRange = cleanedURL.range(of: "://") {
            let protocolPart = cleanedURL[..<protocolRange.upperBound]
            var pathPart = String(cleanedURL[protocolRange.upperBound...])

            // 替换路径中的多个连续斜杠为单个斜杠
            while pathPart.contains("//") {
                pathPart = pathPart.replacingOccurrences(of: "//", with: "/")
            }

            cleanedURL = String(protocolPart) + pathPart
        }

        // 2. 确保URL有协议前缀
        if !cleanedURL.hasPrefix("http://") && !cleanedURL.hasPrefix("https://") {
            cleanedURL = "https://" + cleanedURL
        }

        // 3. 编码URL中的特殊字符
        if let encodedURL = cleanedURL.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
            return encodedURL
        }

        return cleanedURL
    }

    // 检查是否是Google Drive URL
    private func isGoogleDriveURL(_ url: String) -> Bool {
        return url.contains("drive.google.com") ||
               url.contains("docs.google.com") ||
               url.contains("sheets.google.com")
    }

    // 转换Google Drive URL为直接下载链接
    private func convertGoogleDriveURL(_ url: String) -> String? {
        // 提取文件ID
        if let fileID = extractGoogleDriveFileID(from: url) {
            // 检查是否是Google文档或表格
            if url.contains("docs.google.com") || url.contains("spreadsheets/d/") {
                // 对于Google文档，使用导出格式
                if url.contains("document") {
                    return "https://docs.google.com/document/d/\(fileID)/export?format=txt"
                } else if url.contains("spreadsheets") {
                    return "https://docs.google.com/spreadsheets/d/\(fileID)/export?format=csv"
                }
            }

            // 对于普通文件，使用更可靠的下载链接格式
            // 添加confirm=t参数以绕过确认页面
            return "https://drive.google.com/uc?export=download&id=\(fileID)&confirm=t"
        }

        return nil
    }

    // 从Google Drive URL提取文件ID
    private func extractGoogleDriveFileID(from url: String) -> String? {
        logInfo("WebDownloadSheet - 尝试从URL提取Google Drive文件ID: \(url)")

        // 处理标准文件链接格式: https://drive.google.com/file/d/FILE_ID/view
        if url.contains("drive.google.com/file/d/") {
            let components = url.components(separatedBy: "/file/d/")
            if components.count > 1 {
                let afterFileID = components[1].components(separatedBy: "/")
                if afterFileID.count > 0 {
                    let fileID = afterFileID[0]
                    logInfo("WebDownloadSheet - 从标准文件链接提取到ID: \(fileID)")
                    return fileID
                }
            }
        }

        // 处理共享链接格式: https://drive.google.com/open?id=FILE_ID
        if url.contains("drive.google.com/open?id=") {
            let components = url.components(separatedBy: "open?id=")
            if components.count > 1 {
                let afterFileID = components[1].components(separatedBy: "&")
                if afterFileID.count > 0 {
                    let fileID = afterFileID[0]
                    logInfo("WebDownloadSheet - 从共享链接提取到ID: \(fileID)")
                    return fileID
                }
            }
        }

        // 处理Google Docs/Sheets链接
        if url.contains("docs.google.com") || url.contains("sheets.google.com") {
            if let range = url.range(of: "/d/([^/]+)", options: .regularExpression) {
                let match = url[range]
                let fileID = String(match.dropFirst(3)) // 去掉 "/d/"

                // 如果ID中包含额外参数，去除
                let cleanID = fileID.components(separatedBy: "/").first ?? fileID
                logInfo("WebDownloadSheet - 从Docs/Sheets链接提取到ID: \(cleanID)")
                return cleanID
            }
        }

        // 处理其他可能的格式
        let patterns = [
            "id=([^&]+)",
            "/d/([^/?]+)",
            "folders/([^?]+)"
        ]

        for pattern in patterns {
            if let range = url.range(of: pattern, options: .regularExpression) {
                let match = url[range]
                if let equalsIndex = match.firstIndex(of: "=") {
                    let fileID = String(match[match.index(after: equalsIndex)...])
                    logInfo("WebDownloadSheet - 使用正则表达式提取到ID: \(fileID)")
                    return fileID
                } else if let slashIndex = match.firstIndex(of: "/") {
                    let fileID = String(match[match.index(after: slashIndex)...])
                    logInfo("WebDownloadSheet - 使用正则表达式提取到ID: \(fileID)")
                    return fileID
                }
            }
        }

        logError("WebDownloadSheet - 无法从URL提取Google Drive文件ID: \(url)")
        return nil
    }

    // 显示警告
    private func showAlert(title: String, message: String) {
        alertItem = WebAlertItem(title: title, message: message)
        errorMessage = message
    }

    // 从Google Drive HTML响应中提取下载链接
    private func extractDownloadLinkFromGoogleDriveHTML(_ html: String) -> String? {
        logInfo("WebDownloadSheet - 尝试从Google Drive HTML响应中提取下载链接")

        // 方法1: 查找包含"export=download"的链接
        if let range = html.range(of: "href=\"([^\"]*export=download[^\"]*)\"", options: .regularExpression) {
            let match = html[range]
            let link = match.replacingOccurrences(of: "href=\"", with: "").replacingOccurrences(of: "\"", with: "")

            // 如果链接是相对路径，转换为绝对路径
            if link.hasPrefix("/") {
                return "https://drive.google.com\(link)"
            }

            logInfo("WebDownloadSheet - 从HTML中提取到下载链接(方法1): \(link)")
            return link
        }

        // 方法2: 查找包含"uc?export=download"的链接
        if let range = html.range(of: "href=\"([^\"]*uc\\?export=download[^\"]*)\"", options: .regularExpression) {
            let match = html[range]
            let link = match.replacingOccurrences(of: "href=\"", with: "").replacingOccurrences(of: "\"", with: "")

            // 如果链接是相对路径，转换为绝对路径
            if link.hasPrefix("/") {
                return "https://drive.google.com\(link)"
            }

            logInfo("WebDownloadSheet - 从HTML中提取到下载链接(方法2): \(link)")
            return link
        }

        // 方法3: 查找包含"confirm="的链接
        if let range = html.range(of: "href=\"([^\"]*confirm=[^\"]*)\"", options: .regularExpression) {
            let match = html[range]
            let link = match.replacingOccurrences(of: "href=\"", with: "").replacingOccurrences(of: "\"", with: "")

            // 如果链接是相对路径，转换为绝对路径
            if link.hasPrefix("/") {
                return "https://drive.google.com\(link)"
            }

            logInfo("WebDownloadSheet - 从HTML中提取到下载链接(方法3): \(link)")
            return link
        }

        // 方法4: 查找包含"id="的链接
        if let fileIDRange = html.range(of: "id=\"([^\"]+)\"", options: .regularExpression),
           let confirmRange = html.range(of: "confirm=\"([^\"]+)\"", options: .regularExpression) {

            let fileIDMatch = html[fileIDRange]
            let confirmMatch = html[confirmRange]

            let fileID = fileIDMatch.replacingOccurrences(of: "id=\"", with: "").replacingOccurrences(of: "\"", with: "")
            let confirm = confirmMatch.replacingOccurrences(of: "confirm=\"", with: "").replacingOccurrences(of: "\"", with: "")

            let link = "https://drive.google.com/uc?export=download&id=\(fileID)&confirm=\(confirm)"

            logInfo("WebDownloadSheet - 从HTML中提取到下载链接(方法4): \(link)")
            return link
        }

        logError("WebDownloadSheet - 无法从Google Drive HTML响应中提取下载链接")
        return nil
    }

    // 处理下载的数据
    private func processDownloadedData(_ data: Data, contentType: String?) {
        logInfo("WebDownloadSheet - processDownloadedData - 收到的 contentType: \(contentType ?? "nil")")
        // 根据内容类型处理数据
        if let contentType = contentType {
            if contentType.contains("text/csv") || contentType.contains("application/csv") {
                logInfo("WebDownloadSheet - processDownloadedData - 检测到 CSV (基于contentType)")
                processCSVData(data)
            } else if contentType.contains("application/json") {
                logInfo("WebDownloadSheet - processDownloadedData - 检测到 JSON (基于contentType)")
                processJSONData(data)
            } else if contentType.contains("text/plain") {
                logInfo("WebDownloadSheet - processDownloadedData - 检测到 Plain Text (基于contentType)")
                processTextData(data)
            } else if contentType.contains("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                      contentType.contains("application/vnd.ms-excel") {
                logInfo("WebDownloadSheet - processDownloadedData - 检测到 Excel (基于contentType)")
                processExcelData(data)
            } else {
                logInfo("WebDownloadSheet - processDownloadedData - contentType 未知，尝试自动检测")
                // 尝试自动检测格式
                autoDetectAndProcessData(data)
            }
        } else {
            logInfo("WebDownloadSheet - processDownloadedData - contentType 为 nil，尝试自动检测")
            // 尝试自动检测格式
            autoDetectAndProcessData(data)
        }
    }

    // 自动检测并处理数据
    private func autoDetectAndProcessData(_ data: Data) {
        logInfo("WebDownloadSheet - autoDetectAndProcessData - 开始尝试自动检测数据格式")

        // 尝试解析为JSON
        if let _ = try? JSONSerialization.jsonObject(with: data, options: []) {
            logInfo("WebDownloadSheet - autoDetectAndProcessData - 自动检测为JSON格式")
            processJSONData(data)
            return
        }


        // 尝试解析为文本
        if let text = String(data: data, encoding: .utf8) {
            // 检查是否为CSV (包含逗号分隔)
            if text.contains(",") {
                logInfo("WebDownloadSheet - autoDetectAndProcessData - 自动检测为CSV格式")
                processCSVData(data)
            } else {
                logInfo("WebDownloadSheet - autoDetectAndProcessData - 自动检测为纯文本格式")
                processTextData(data)
            }
            return
        }

        // 无法识别格式
        DispatchQueue.main.async {
            errorMessage = "无法识别数据格式"
            isProcessing = false
            logError("WebDownloadSheet - 无法识别数据格式")
        }
    }

    // 处理CSV数据
    private func processCSVData(_ data: Data) {
        guard let text = String(data: data, encoding: .utf8) else {
            DispatchQueue.main.async {
                errorMessage = "无法解析CSV数据"
                isProcessing = false
            }
            return
        }

        let rows = text.components(separatedBy: .newlines)

        // 移除空行
        let validRows = rows.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        // 处理每一行
        processAddressLines(validRows)

        // 添加到历史记录
        addToHistory(url: urlString, count: validRows.count)
    }

    // 处理JSON数据
    private func processJSONData(_ data: Data) {
        logInfo("WebDownloadSheet - processJSONData - 开始处理JSON数据")
        do {
            // 尝试解析为JSON数组
            if let jsonArray = try JSONSerialization.jsonObject(with: data, options: []) as? [[String: Any]] {
                logInfo("WebDownloadSheet - processJSONData - 解析为顶层数组 [[String: Any]]，数量: \(jsonArray.count)")
                // 从每个对象中提取地址
                var addresses: [String] = []

                for item in jsonArray {
                    // 首先检查是否有完整地址字段
                    if let address = item["address"] as? String, !address.isEmpty {
                        // 检查是否有suburb、state、postcode等字段，如果有则组合成完整地址
                        var fullAddress = address

                        if let suburb = item["suburb"] as? String, !suburb.isEmpty {
                            fullAddress += ", \(suburb)"
                        }

                        if let state = item["state"] as? String, !state.isEmpty {
                            fullAddress += ", \(state)"
                        }

                        if let postcode = item["postcode"] as? String, !postcode.isEmpty {
                            fullAddress += " \(postcode)"
                        } else if let postcode = item["postcode"] as? Int {
                            fullAddress += " \(postcode)"
                        }

                        if let country = item["country"] as? String, !country.isEmpty {
                            fullAddress += ", \(country)"
                        }

                        addresses.append(fullAddress)
                        logInfo("WebDownloadSheet - processJSONData - 组合地址: \(fullAddress)")
                    } else {
                        // 如果没有address字段，尝试其他常见字段
                        let possibleKeys = ["location", "place", "name", "title"]
                        for key in possibleKeys {
                            if let address = item[key] as? String, !address.isEmpty {
                                addresses.append(address)
                                logInfo("WebDownloadSheet - processJSONData - 使用替代字段 \(key): \(address)")
                                break
                            }
                        }
                    }
                }

                if addresses.isEmpty {
                    DispatchQueue.main.async {
                        errorMessage = "JSON数据中未找到地址字段"
                        isProcessing = false
                    }
                } else {
                    processAddressLines(addresses)

                    // 添加到历史记录
                    addToHistory(url: urlString, count: addresses.count)
                }
            } else {
                // 不是预期的 [[String: Any]] 格式，尝试解析为 { "addresses": [...] } 结构
                logInfo("WebDownloadSheet - processJSONData - 顶层不是数组，尝试解析为 [String: Any] 对象")
                if let jsonObject = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let addressArray = jsonObject["addresses"] as? [[String: Any]] {
                    logInfo("WebDownloadSheet - processJSONData - 成功解析为 {{ \"addresses\": [...] }} 结构，数量: \(addressArray.count)")

                    var extractedAddresses: [String] = []
                    for item in addressArray {
                        // 首先检查是否有完整地址字段
                        if let address = item["address"] as? String, !address.isEmpty {
                            // 检查是否有suburb、state、postcode等字段，如果有则组合成完整地址
                            var fullAddress = address

                            if let suburb = item["suburb"] as? String, !suburb.isEmpty {
                                fullAddress += ", \(suburb)"
                            }

                            if let state = item["state"] as? String, !state.isEmpty {
                                fullAddress += ", \(state)"
                            }

                            if let postcode = item["postcode"] as? String, !postcode.isEmpty {
                                fullAddress += " \(postcode)"
                            } else if let postcode = item["postcode"] as? Int {
                                fullAddress += " \(postcode)"
                            }

                            if let country = item["country"] as? String, !country.isEmpty {
                                fullAddress += ", \(country)"
                            }

                            extractedAddresses.append(fullAddress.trimmingCharacters(in: .whitespacesAndNewlines))
                            logInfo("WebDownloadSheet - processJSONData - 组合地址: \(fullAddress.trimmingCharacters(in: .whitespacesAndNewlines))")
                        } else {
                            // 如果没有明确的组合字段，再尝试通用key
                            let possibleKeys = ["location", "place", "name", "title"]
                            for key in possibleKeys {
                                if let address = item[key] as? String, !address.isEmpty {
                                    extractedAddresses.append(address.trimmingCharacters(in: .whitespacesAndNewlines))
                                    logInfo("WebDownloadSheet - processJSONData - 使用替代字段 \(key): \(address.trimmingCharacters(in: .whitespacesAndNewlines))")
                                    break
                                }
                            }
                        }
                    }

                    if extractedAddresses.isEmpty {
                        DispatchQueue.main.async {
                            errorMessage = "JSON数据中未找到有效的地址字段"
                            isProcessing = false
                        }
                    } else {
                        processAddressLines(extractedAddresses)

                        // 添加到历史记录
                        addToHistory(url: urlString, count: extractedAddresses.count)
                    }
                }
            }
        } catch {
            DispatchQueue.main.async {
                errorMessage = "JSON解析失败: \(error.localizedDescription)"
                isProcessing = false
            }
        }
    }

    // 处理文本数据
    private func processTextData(_ data: Data) {
        guard let text = String(data: data, encoding: .utf8) else {
            DispatchQueue.main.async {
                errorMessage = "无法解析文本数据"
                isProcessing = false
            }
            return
        }

        let lines = text.components(separatedBy: .newlines)

        // 移除空行
        let validLines = lines.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        // 处理每一行
        processAddressLines(validLines)

        // 添加到历史记录
        addToHistory(url: urlString, count: validLines.count)
    }

    // 处理Excel数据（已弃用）
    private func processExcelData(_ data: Data) {
        logInfo("WebDownloadSheet - 收到Excel格式数据，但该功能已弃用")

        DispatchQueue.main.async {
            self.errorMessage = "Excel格式导入功能已不再支持。请将文件另存为CSV或TXT格式后重试。"
            self.isProcessing = false
            self.logError("WebDownloadSheet - 不支持Excel格式导入")
        }
    }

    // 处理地址行
    private func processAddressLines(_ lines: [String]) {
        logInfo("WebDownloadSheet - processAddressLines - 开始处理 \(lines.count) 行地址")
        isProcessing = true
        processingProgress = 0
        // 创建一个队列来处理地址
        let processingQueue = DispatchQueue(label: "com.navibatch.addressProcessing", qos: .userInitiated)
        let totalLines = lines.count

        // 智能限流策略
        let requestLimit = 50 // 每分钟请求限制
        let safetyFactor = 0.8 // 安全系数，避免完全用尽配额
        let safeRequestLimit = Int(Double(requestLimit) * safetyFactor)

        // 根据地址总数设置适当的批处理大小和延迟
        let batchSize: Int
        let requestInterval: TimeInterval // 每个请求之间的间隔
        let batchDelay: TimeInterval // 批次之间的延迟

        if totalLines <= safeRequestLimit {
            // 地址数量在限制之内，可以快速处理
            batchSize = min(8, totalLines) // 适当的批量大小，但不超过总数
            requestInterval = 0.5 // 增加单个请求间隔确保稳定性
            batchDelay = 1.0 // 增加批次间延迟
            logInfo("WebDownloadSheet - 地址数量在限制内，使用快速处理模式")
        } else {
            // 地址数量接近或超过限制，需要控制速率
            batchSize = 5
            // 计算所需间隔，确保一分钟内不超过限制
            let totalMinutesNeeded = ceil(Double(totalLines) / Double(safeRequestLimit))
            requestInterval = 0.75 // 增加基础间隔
            batchDelay = max(3.0, (60.0 * totalMinutesNeeded / Double(totalLines / batchSize)) - Double(batchSize) * requestInterval)
            logInfo("WebDownloadSheet - 地址数量较多，使用限流处理模式，预计需要 \(totalMinutesNeeded) 分钟")
        }

        logInfo("WebDownloadSheet - 开始处理 \(totalLines) 个地址，使用分批处理（每批 \(batchSize) 个，批次延迟 \(batchDelay) 秒，请求间隔 \(requestInterval) 秒）")

        // 记录失败的地址
        var failedAddresses: [String] = []

        processingQueue.async {
            var processedCount = 0
            var successCount = 0

            // 分批处理地址
            for i in stride(from: 0, to: lines.count, by: batchSize) {
                let end = min(i + batchSize, lines.count)
                let batch = Array(lines[i..<end])

                // 处理当前批次
                for line in batch {
                    // 清理地址文本
                    let cleanAddress = line.trimmingCharacters(in: .whitespacesAndNewlines)

                    // 更新进度
                    processedCount += 1
                    DispatchQueue.main.async {
                        processingProgress = Double(processedCount) / Double(totalLines)
                    }

                    // 地理编码
                    let semaphore = DispatchSemaphore(value: 0)

                    geocodeAddress(cleanAddress) { coordinate, error in
                        if let coordinate = coordinate {
                            successCount += 1
                            DispatchQueue.main.async {
                                // 添加到下载结果列表，默认选中
                                downloadedAddresses.append((cleanAddress, coordinate, true))
                            }
                        } else if let errorCode = error {
                            // 记录失败的地址及错误代码
                            failedAddresses.append(cleanAddress)
                            logError("WebDownloadSheet - 地址地理编码失败: '\(cleanAddress)', 错误代码: \(errorCode)")
                        }
                        semaphore.signal()
                    }

                    // 等待地理编码完成，最多10秒
                    _ = semaphore.wait(timeout: .now() + 10)

                    // 每个地址请求之间添加合适延迟
                    Thread.sleep(forTimeInterval: requestInterval)
                }

                // 批次之间添加延迟以避免服务限制
                if i + batchSize < lines.count {
                    let currentBatch = i/batchSize + 1
                    let totalBatches = Int(ceil(Double(lines.count)/Double(batchSize)))
                    let remainingTime = (totalBatches - currentBatch) * (batchSize * Int(requestInterval) + Int(batchDelay))

                    logInfo("WebDownloadSheet - 完成批次 \(currentBatch)/\(totalBatches)，延迟 \(batchDelay) 秒，预计剩余时间: \(remainingTime) 秒")
                    Thread.sleep(forTimeInterval: batchDelay)
                }
            }

            // 完成所有处理
            DispatchQueue.main.async {
                processingProgress = 1.0
                isProcessing = false
                logInfo("WebDownloadSheet - 地址处理完成，共 \(totalLines) 个地址，成功 \(successCount) 个")

                // 打印所有失败的地址，方便在地图中查找
                if !failedAddresses.isEmpty {
                    logInfo("WebDownloadSheet - 以下 \(failedAddresses.count) 个地址地理编码失败：")
                    for (index, address) in failedAddresses.enumerated() {
                        logInfo("WebDownloadSheet - 失败地址[\(index+1)]: '\(address)'")
                    }
                }

                // 根据成功率提供不同的提示
                let successRate = Double(successCount) / Double(totalLines)
                if successRate < 0.5 && totalLines > 3 {
                    // 成功率低于50%，显示警告
                    alertItem = WebAlertItem(
                        title: "地理编码部分失败",
                        message: "成功处理 \(successCount)/\(totalLines) 个地址。\n\n可能原因：\n1. 部分地址格式不正确\n2. 可能不是该地区内的有效地址\n3. 苹果地理编码服务暂时不可用\n\n您可以尝试：\n- 确保地址包含准确的街道名称和号码\n- 在地址中添加区域名称和邮编\n- 稍后再试",
                        primaryButton: .default(Text("确定"))
                    )
                } else if successRate < 1.0 && totalLines > 3 {
                    // 部分失败，显示提示
                    alertItem = WebAlertItem(
                        title: "部分地址导入成功",
                        message: "成功处理 \(successCount)/\(totalLines) 个地址。您仍然可以使用已成功地理编码的地址。",
                        primaryButton: .default(Text("确定"))
                    )
                }
            }
        }
    }

    // 地理编码
    private func geocodeAddress(_ address: String, completion: @escaping (CLLocationCoordinate2D?, Int?) -> Void) {
        // 不创建未使用的变量

        // 添加更智能的区域提示，确保地址被解析到正确的地理区域
        var completeAddress = address

        // 处理地址前检查是否需要特殊修正
        completeAddress = correctProblematicAddress(completeAddress)

        // 分析地址信息，确定是否需要补充
        let containsVIC = address.contains("VIC") || address.contains("Victoria")
        let containsPostcode = address.contains("3150")
        let containsSuburb = address.lowercased().contains("glen waverley")
        let containsAustralia = address.lowercased().contains("australia")
        // 将未使用的变量赋值给_
        let _ = address.lowercased().contains("melbourne")

        // 根据地址信息智能补充
        if !containsAustralia {
            if !containsVIC && !containsPostcode {
                if containsSuburb {
                    completeAddress = "\(address), VIC 3150, Australia"
                } else {
                    completeAddress = "\(address), Glen Waverley, VIC 3150, Australia"
                }
            } else if !containsSuburb && (containsVIC || containsPostcode) {
                completeAddress = "\(address), Glen Waverley, Australia"
            } else {
                completeAddress = "\(address), Australia"
            }
            logInfo("WebDownloadSheet - 地址补充区域信息: '\(address)' -> '\(completeAddress)'")
        }

        // 执行地理编码，添加自动重试策略
        performGeocode(originalAddress: address, enhancedAddress: completeAddress, attempts: 0, maxAttempts: 3, completion: completion)
    }

    // 修正特定问题地址
    private func correctProblematicAddress(_ address: String) -> String {
        var correctedAddress = address

        // 处理特定问题地址 - 基于Six项目的实现

        // 处理Police Road的特殊情况
        if address.contains("Police Road, Glen Waverley") {
            // 确保地址格式正确
            if !address.contains("VIC") {
                correctedAddress = address.replacingOccurrences(
                    of: "Police Road, Glen Waverley",
                    with: "Police Road, Glen Waverley, VIC 3150"
                )
                logInfo("WebDownloadSheet - 修正Police Road地址: 添加VIC 3150")
            }
        }

        // 处理Golfers Drive的特殊情况
        if address.contains("Golfers Drive") || address.contains("Golfers Dr") {
            correctedAddress = address.replacingOccurrences(
                of: "Golfers Drive",
                with: "Golfers Avenue"
            ).replacingOccurrences(
                of: "Golfers Dr",
                with: "Golfers Avenue"
            )
            logInfo("WebDownloadSheet - 修正Golfers Drive -> Golfers Avenue")
        }

        // 处理Century Drive的特殊情况
        if address.contains("Century Drive") || address.contains("Century Dr") {
            correctedAddress = address.replacingOccurrences(
                of: "Century Drive",
                with: "Capital Avenue"
            ).replacingOccurrences(
                of: "Century Dr",
                with: "Capital Avenue"
            )
            logInfo("WebDownloadSheet - 修正Century Drive -> Capital Avenue")
        }

        // 可以添加更多特定地址修正规则...

        return correctedAddress
    }

    // 执行地理编码，包含重试逻辑
    private func performGeocode(originalAddress: String, enhancedAddress: String, attempts: Int, maxAttempts: Int, completion: @escaping (CLLocationCoordinate2D?, Int?) -> Void) {
        // 将geocoder变量放在实际使用的位置
        var currentAttempt = attempts

        // 确定本次使用的地址
        let addressToUse: String

        // 不同尝试使用不同策略
        switch currentAttempt {
        case 0:
            // 第一次尝试：使用增强的完整地址
            addressToUse = enhancedAddress
        case 1:
            // 第二次尝试：使用MapKit可能更熟悉的格式
            addressToUse = formatAddressForMapKit(originalAddress)
        default:
            // 最后尝试：构建备用地址
            addressToUse = buildFallbackAddress(originalAddress)
        }

        logInfo("WebDownloadSheet - 地理编码尝试\(currentAttempt+1)/\(maxAttempts): 使用地址 '\(addressToUse)'")

        // 创建区域限定区域 (MKCoordinateRegion)
        // 这有助于确保搜索在正确的地区
        let melbourneRegion = createMelbourneRegion()

        // 添加区域字典以提供更精确的搜索区域
        var hints: [String: Any]? = nil
        if addressToUse.contains("VIC") || addressToUse.contains("Victoria") || addressToUse.contains("3150") {
            hints = [
                CLGeocodeAppleOptionCountryKey: "AU",
                CLGeocodeAppleOptionStateKey: "VIC",
                CLGeocodeAppleOptionPostalCodeKey: "3150"
            ]
            logInfo("WebDownloadSheet - 添加区域提示: [国家:AU, 州:VIC, 邮编:3150]")
        }

        // 不再保存未使用的变量
        let _ = hints

        // 创建geocoder并立即使用
        let geocoder = CLGeocoder()
        geocoder.geocodeAddressString(addressToUse,
                                    in: melbourneRegion, // 使用墨尔本区域作为搜索限制
                                    preferredLocale: Locale(identifier: "en_AU"),
                                    completionHandler: { placemarks, error in
            if let error = error as NSError? {
                logError("WebDownloadSheet - 地理编码失败(尝试\(currentAttempt+1)/\(maxAttempts)): '\(addressToUse)', 错误: \(error.localizedDescription), 错误代码: \(error.code)")

                // 判断是否应该重试
                currentAttempt += 1
                if currentAttempt < maxAttempts {
                    // 递增重试间隔，避免过快重试
                    let delayInterval = 1.0 * Double(currentAttempt) // 增加每次重试的延迟
                    logInfo("WebDownloadSheet - 将在\(delayInterval)秒后进行第\(currentAttempt+1)次尝试")

                    DispatchQueue.global(qos: .userInitiated).asyncAfter(deadline: .now() + delayInterval) {
                        self.performGeocode(originalAddress: originalAddress, enhancedAddress: enhancedAddress, attempts: currentAttempt, maxAttempts: maxAttempts, completion: completion)
                    }
                    return
                }

                // 所有尝试失败
                completion(nil, error.code)
                return
            }

            if let location = placemarks?.first?.location?.coordinate {
                // 检查坐标是否有效，并且在墨尔本区域附近
                if self.isValidCoordinate(location) && self.isNearMelbourne(location) {
                    logInfo("WebDownloadSheet - 地理编码成功: '\(addressToUse)' -> (\(location.latitude), \(location.longitude))")
                    completion(location, nil)
                } else {
                    logError("WebDownloadSheet - 地理编码返回无效坐标 (\(location.latitude), \(location.longitude)) 对于地址: '\(addressToUse)'")

                    // 如果坐标有效但不在墨尔本附近，尝试下一种策略
                    if self.isValidCoordinate(location) && !self.isNearMelbourne(location) && currentAttempt < maxAttempts {
                        currentAttempt += 1
                        let delayInterval = 1.0 * Double(currentAttempt)
                        logInfo("WebDownloadSheet - 坐标不在墨尔本附近，\(delayInterval)秒后进行第\(currentAttempt+1)次尝试")

                        DispatchQueue.global(qos: .userInitiated).asyncAfter(deadline: .now() + delayInterval) {
                            self.performGeocode(originalAddress: originalAddress, enhancedAddress: enhancedAddress, attempts: currentAttempt, maxAttempts: maxAttempts, completion: completion)
                        }
                        return
                    }

                    completion(nil, -1) // 自定义错误码表示坐标无效或不在目标区域
                }
            } else {
                logError("WebDownloadSheet - 未找到地址的坐标: '\(addressToUse)'")

                // 尝试下一策略
                currentAttempt += 1
                if currentAttempt < maxAttempts {
                    let delayInterval = 1.0 * Double(currentAttempt)
                    logInfo("WebDownloadSheet - 将在\(delayInterval)秒后进行第\(currentAttempt+1)次尝试")

                    DispatchQueue.global(qos: .userInitiated).asyncAfter(deadline: .now() + delayInterval) {
                        self.performGeocode(originalAddress: originalAddress, enhancedAddress: enhancedAddress, attempts: currentAttempt, maxAttempts: maxAttempts, completion: completion)
                    }
                    return
                }

                completion(nil, -2) // 自定义错误码表示未找到坐标
            }
        })
    }

    // 为MapKit格式化地址
    private func formatAddressForMapKit(_ address: String) -> String {
        // 去除额外的逗号和空格
        var formattedAddress = address.replacingOccurrences(of: ", ,", with: ",")
            .replacingOccurrences(of: "  ", with: " ")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        // 添加标准化的区域信息
        if !formattedAddress.contains("VIC") && !formattedAddress.contains("Victoria") {
            formattedAddress += ", VIC"
        }

        if !formattedAddress.contains("3150") {
            formattedAddress += " 3150"
        }

        if !formattedAddress.lowercased().contains("australia") {
            formattedAddress += ", Australia"
        }

        return formattedAddress
    }

    // 创建墨尔本地区的搜索区域
    private func createMelbourneRegion() -> CLRegion? {
        // Glen Waverley大致坐标
        let glenWaverleyCenter = CLLocationCoordinate2D(latitude: -37.90219, longitude: 145.16034)

        // 创建一个覆盖墨尔本东南区域的搜索区域（半径约10公里）
        return CLCircularRegion(center: glenWaverleyCenter, radius: 10000, identifier: "Melbourne_SE")
    }

    // 检查坐标是否在墨尔本附近
    private func isNearMelbourne(_ coordinate: CLLocationCoordinate2D) -> Bool {
        // 获取用户位置
        let userLocation = LocationManager.shared.userLocation

        // 进行全球验证
        let validationResult = DeliveryPoint.validateCoordinatesGlobally(
            latitude: coordinate.latitude,
            longitude: coordinate.longitude,
            userLocation: userLocation
        )

        // 只有完全有效的坐标才被认为是"附近"
        return validationResult.isValid && validationResult.validationStatus == LocationValidationStatus.valid
    }

    // 构建备用地址尝试格式
    private func buildFallbackAddress(_ address: String) -> String {
        // 移除可能导致问题的特殊字符
        var cleanAddress = address.replacingOccurrences(of: "\\n", with: " ")
                                  .replacingOccurrences(of: ",", with: " ")
                                  .replacingOccurrences(of: "/", with: " ")

        // 规范化空格
        while cleanAddress.contains("  ") {
            cleanAddress = cleanAddress.replacingOccurrences(of: "  ", with: " ")
        }

        // 分析地址组件
        let components = cleanAddress.components(separatedBy: " ")
        var result = ""

        // 尝试提取地址数字和街道名称
        if let firstComponent = components.first, Int(firstComponent) != nil {
            // 如果第一部分是数字，可能是门牌号
            if components.count >= 2 {
                // 简化地址到"门牌号 + 街道名"
                let streetNameIndex = min(2, components.count - 1)
                var simplifiedAddress = components[0] + " " + components[1]
                if streetNameIndex > 1 {
                    simplifiedAddress += " " + components[streetNameIndex]
                }
                result = simplifiedAddress + ", Glen Waverley, VIC 3150, Australia"
            } else {
                result = cleanAddress + ", Glen Waverley, VIC 3150, Australia"
            }
        } else if cleanAddress.count > 0 {
            // 如果没有门牌号模式，尝试作为街道名称处理
            let streetPart = components.prefix(2).joined(separator: " ")
            result = streetPart + ", Glen Waverley, VIC 3150, Australia"
        } else {
            // 默认方案
            result = "Glen Waverley, VIC 3150, Australia"
        }

        logInfo("WebDownloadSheet - 构建备用地址: \(result)")
        return result
    }

    // 验证坐标有效性
    private func isValidCoordinate(_ coordinate: CLLocationCoordinate2D) -> Bool {
        let validationResult = DeliveryPoint.validateCoordinatesGlobally(
            latitude: coordinate.latitude,
            longitude: coordinate.longitude
        )
        return validationResult.isValid
    }

    // 切换地址选择状态
    private func toggleAddressSelection(at index: Int) {
        guard index < downloadedAddresses.count else { return }

        downloadedAddresses[index].2.toggle()
    }

    // 地址限制超出提示表单
    struct AddressLimitExceededSheet: View {
        let currentCount: Int
        let remainingSlots: Int
        let maxAllowed: Int
        let selectedCount: Int
        let isFreeUser: Bool
        let selectedAddresses: [(String, CLLocationCoordinate2D)]
        let onImportLimited: ([(String, CLLocationCoordinate2D)]) -> Void
        let onUpgrade: () -> Void
        let onCancel: () -> Void

        @Environment(\.dismiss) private var dismiss

        var body: some View {
            NavigationView {
                VStack(spacing: 20) {
                    // 图标
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                        .padding()

                    // 信息
                    VStack(alignment: .leading, spacing: 10) {
                        if isFreeUser {
                            Text(String(format: "free_version_max_addresses".localized, maxAllowed))
                                .font(.body)

                            Text(String(format: "current_addresses_remaining".localized, currentCount, remainingSlots))
                                .font(.body)

                            if remainingSlots <= 0 {
                                Text(String(format: "can_import_selected".localized, selectedCount))
                                    .font(.body)
                                    .foregroundColor(.blue)
                            } else if selectedCount > remainingSlots {
                                Text(String(format: "selected_exceeds_limit".localized, selectedCount, selectedCount - remainingSlots))
                                    .font(.body)
                                    .foregroundColor(.orange)
                            } else {
                                Text(String(format: "selected_addresses_all_importable".localized, selectedCount))
                                    .font(.body)
                                    .foregroundColor(.green)
                            }

                            Text("upgrade_for_unlimited_addresses".localized)
                                .font(.body)
                                .bold()
                                .padding(.top, 5)
                        } else {
                            Text(String(format: "current_route_address_limit".localized, currentCount, remainingSlots, maxAllowed))
                                .font(.body)

                            if remainingSlots <= 0 {
                                Text(String(format: "can_import_selected".localized, selectedCount))
                                    .font(.body)
                                    .foregroundColor(.blue)
                            } else if selectedCount > remainingSlots {
                                Text(String(format: "selected_exceeds_limit".localized, selectedCount, selectedCount - remainingSlots))
                                    .font(.body)
                                    .foregroundColor(.orange)
                            } else {
                                Text(String(format: "selected_addresses_all_importable".localized, selectedCount))
                                    .font(.body)
                                    .foregroundColor(.green)
                            }
                        }
                    }
                    .padding(.horizontal)

                    Spacer()
                }
                .padding()
                .toolbar {
                    // 左侧取消按钮
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("cancel".localized) {
                            onCancel()
                        }
                    }

                    // 右侧按钮
                    ToolbarItem(placement: .navigationBarTrailing) {
                        if remainingSlots <= 0 {
                            // 当剩余槽位为0时，但仍然允许导入已选择的地址
                            Button("import_selected_addresses".localized) {
                                // 直接导入所有选择的地址
                                onImportLimited(selectedAddresses)
                            }
                            .foregroundColor(.blue)
                        } else if selectedCount > remainingSlots {
                            Button(String(format: "import_first_n".localized, remainingSlots)) {
                                // 创建限制后的地址列表
                                let limitedAddresses = Array(selectedAddresses.prefix(remainingSlots))
                                onImportLimited(limitedAddresses)
                            }
                            .foregroundColor(.blue)
                        } else {
                            Button(String(format: "import_all_n".localized, selectedCount)) {
                                // 导入所有选择的地址
                                onImportLimited(selectedAddresses)
                            }
                            .foregroundColor(.blue)
                        }
                    }
                }
                .toolbar {
                    // 底部工具栏 - 仅显示升级按钮（如果是免费用户）
                    ToolbarItemGroup(placement: .bottomBar) {
                        if isFreeUser {
                            Spacer()
                            Button(action: {
                                onUpgrade()
                            }) {
                                Text("upgrade_to_premium".localized)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.green)
                                    .cornerRadius(8)
                            }
                            Spacer()
                        }
                    }
                }
            }
        }
    }

    // 确认选中的地址
    private func confirmSelectedAddresses() {
        let selectedAddresses = downloadedAddresses.filter { $0.2 }.map { ($0.0, $0.1) }

        if selectedAddresses.isEmpty {
            // 显示错误提示
            alertItem = WebAlertItem(
                title: "cannot_import".localized,
                message: "select_at_least_one".localized,
                primaryButton: .default(Text("confirm".localized))
            )
            // 重置处理状态
            isProcessing = false
            return
        }

        // 记录操作开始
        logInfo("WebDownloadSheet - 开始处理地址导入请求: \(selectedAddresses.count)个地址")

        // 检查订阅限制
        let subscriptionManager = SubscriptionManager.shared
        let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute

        logInfo("WebDownloadSheet - 当前订阅级别: \(subscriptionManager.currentTier.rawValue), 最大允许地址数: \(maxAllowed)")

        // 获取当前路线
        if let currentRoute = getCurrentRoute() {
            let currentCount = currentRoute.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
            let remainingSlots = maxAllowed - currentCount

            logInfo("WebDownloadSheet - 当前路线: \(currentRoute.name), 当前地址数: \(currentCount), 剩余槽位: \(remainingSlots)")
            logInfo("WebDownloadSheet - 选择的地址数: \(selectedAddresses.count), 是否超出限制: \(selectedAddresses.count > remainingSlots)")

            // 检查是否超出限制
            if remainingSlots <= 0 {
                logInfo("WebDownloadSheet - 剩余槽位为0，任何数量的地址都无法添加")
            }

            if selectedAddresses.count > remainingSlots {
                // 显示地址限制超出表单
                let sheet = AddressLimitExceededSheet(
                    currentCount: currentCount,
                    remainingSlots: remainingSlots,
                    maxAllowed: maxAllowed,
                    selectedCount: selectedAddresses.count,
                    isFreeUser: subscriptionManager.currentTier == .free,
                    selectedAddresses: selectedAddresses,
                    onImportLimited: { limitedAddresses in
                        // 调用回调函数
                        self.onAddressesConfirmed(limitedAddresses)
                        self.dismiss()
                    },
                    onUpgrade: {
                        // 关闭当前sheet并显示订阅界面
                        showSubscriptionView = true
                        self.dismiss()
                    },
                    onCancel: {
                        // 不执行任何操作，保持当前界面
                    }
                )

                // 使用UIKit展示表单，因为SwiftUI的sheet不能在函数中直接调用
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {

                    let hostingController = UIHostingController(rootView: sheet)
                    hostingController.modalPresentationStyle = .formSheet
                    hostingController.preferredContentSize = CGSize(width: 400, height: 500)
                    rootViewController.present(hostingController, animated: true)
                }

                return
            }
        }

        // 调用回调函数
        onAddressesConfirmed(selectedAddresses)
        dismiss()
    }

    // 获取当前路线
    private func getCurrentRoute() -> Route? {
        // 使用共享的持久化ModelContainer
        let container = getPersistentContainer()
        let context = container.mainContext

        do {
            let descriptor = FetchDescriptor<Route>()
            let routes = try context.fetch(descriptor)

            if routes.isEmpty {
                logInfo("WebDownloadSheet - 数据库中没有找到路线")
                return nil
            }

            logInfo("WebDownloadSheet - 成功获取路线: \(routes.first?.name ?? "未命名"), ID=\(routes.first?.id.uuidString ?? "无ID"), 点数: \(routes.first?.points.count ?? 0)")
            return routes.first
        } catch {
            logError("WebDownloadSheet - 获取当前路线失败: \(error.localizedDescription)")
            return nil
        }
    }

    // 添加到历史记录
    private func addToHistory(url: String, count: Int) {
        // 检查是否已存在
        if !downloadHistory.contains(where: { $0.url == url }) {
            let newItem = DownloadHistoryItem(url: url, date: Date(), count: count, companyName: companyName.trimmingCharacters(in: .whitespacesAndNewlines))
            downloadHistory.insert(newItem, at: 0)

            // 限制历史记录数量
            if downloadHistory.count > 5 {
                downloadHistory = Array(downloadHistory.prefix(5))
            }

            // 如果有公司名称，保存到地址簿
            if !companyName.isEmpty {
                saveCompanyURLToAddressBook(url: url, companyName: companyName)
            }
        }
    }

    // 保存公司URL到地址簿
    private func saveCompanyURLToAddressBook(url: String, companyName: String) {
        // 获取ModelContext
        guard let modelContext = try? ModelContainer(for: SavedAddress.self).mainContext else {
            logError("WebDownloadSheet - 无法获取ModelContext")
            return
        }

        // 创建一个虚拟地址，使用墨尔本的默认坐标
        let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)

        // 创建保存的地址
        let savedAddress = SavedAddress(
            address: String(format: "company_format".localized, companyName),
            coordinate: defaultCoordinate,
            notes: "added_from_web_download".localized,
            companyName: companyName,
            url: url
        )

        // 保存到数据库
        modelContext.insert(savedAddress)

        do {
            try modelContext.save()
            logInfo("WebDownloadSheet - 已保存公司URL到地址簿: \(companyName), \(url)")
        } catch {
            logError("WebDownloadSheet - 保存公司URL失败: \(error.localizedDescription)")
        }
    }

    // 日志辅助函数
    private func logInfo(_ message: String) {
        os_log("%{public}@", log: Logger.app, type: .info, message)
    }

    private func logError(_ message: String) {
        os_log("%{public}@", log: Logger.app, type: .error, message)
    }
}

// 下载历史项
struct DownloadHistoryItem: Identifiable {
    let id = UUID()
    let url: String
    let date: Date
    let count: Int
    let companyName: String

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    init(url: String, date: Date, count: Int, companyName: String = "") {
        self.url = url
        self.date = date
        self.count = count
        self.companyName = companyName
    }
}

#Preview("WebDownloadSheet") {
    WebDownloadSheet { addresses in
        print("Selected \(addresses.count) addresses")
    }
}

// 隐藏键盘的扩展
extension View {
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

