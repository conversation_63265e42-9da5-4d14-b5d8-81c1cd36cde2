import SwiftUI
import SwiftData
import MapKit
import os.log
// 导入共享组件

struct SavedAddressPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Query(sort: \SavedAddress.createdAt, order: .reverse) private var savedAddresses: [SavedAddress]
    @State private var searchText = ""
    // 移除地址类型过滤器

    var onAddressSelected: (SavedAddress) -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)

                    TextField("search_address".localized, text: $searchText)
                        .autocorrectionDisabled()

                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6).opacity(0.5))

                // 移除地址类型过滤器

                if filteredAddresses.isEmpty {
                    ContentUnavailableView {
                        Label("no_saved_addresses".localized, systemImage: "mappin.slash")
                    } description: {
                        Text("no_saved_addresses_description".localized)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // 地址列表
                    List {
                        ForEach(filteredAddresses) { address in
                            Button(action: {
                                onAddressSelected(address)
                                dismiss()
                            }) {
                                SavedAddressRow(address: address)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("select_address_book".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 20))
                    }
                }
            }
        }
    }

    // 过滤后的地址列表
    private var filteredAddresses: [SavedAddress] {
        var addresses = savedAddresses

        // 应用搜索文本过滤
        if !searchText.isEmpty {
            addresses = addresses.filter { address in
                // 搜索地址
                if address.address.localizedCaseInsensitiveContains(searchText) {
                    return true
                }

                // 搜索公司名称
                if let companyName = address.companyName,
                   companyName.localizedCaseInsensitiveContains(searchText) {
                    return true
                }

                // 搜索URL
                if let url = address.url,
                   url.localizedCaseInsensitiveContains(searchText) {
                    return true
                }

                // 搜索电话号码
                if let phoneNumber = address.phoneNumber,
                   phoneNumber.localizedCaseInsensitiveContains(searchText) {
                    return true
                }

                return false
            }
        }

        return addresses
    }
}

// 地址行视图
struct SavedAddressRow: View {
    let address: SavedAddress

    var body: some View {
        HStack(spacing: 12) {
            // 地址图标
            Image(systemName: "mappin.circle.fill")
                .font(.title3)
                .foregroundColor(.red)
                .frame(width: 36, height: 36)
                .background(Color.red.opacity(0.1))
                .clipShape(Circle())

            VStack(alignment: .leading, spacing: 4) {
                Text(address.address)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(2)

                HStack(spacing: 8) {
                    if let phoneNumber = address.phoneNumber, !phoneNumber.isEmpty {
                        Text(phoneNumber)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    if let companyName = address.companyName, !companyName.isEmpty {
                        Text(companyName)
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding(.vertical, 2)
                            .padding(.horizontal, 6)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(4)
                    }
                }

                if let url = address.url, !url.isEmpty {
                    Text(url)
                        .font(.caption)
                        .foregroundColor(.green)
                        .lineLimit(1)
                }
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// 使用共享组件中的 FilterChip

#Preview {
    NavigationStack {
        SavedAddressPickerView(onAddressSelected: { _ in })
            .modelContainer(getPersistentContainer())
    }
}
