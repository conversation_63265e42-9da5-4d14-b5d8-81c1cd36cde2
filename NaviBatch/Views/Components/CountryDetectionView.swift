//
//  CountryDetectionView.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/23.
//  国家检测和选择组件 - 完全本地化
//

import SwiftUI

// MARK: - 配送国家枚举
enum DeliveryCountry: String, CaseIterable, Identifiable {
    case unitedStates = "US"
    case australia = "AU"
    // 🚫 暂时注释掉其他国家，目前只支持美国和澳洲
    // case canada = "CA"
    // case unitedKingdom = "GB"
    // case germany = "DE"
    // case france = "FR"
    // case japan = "JP"
    // case china = "CN"

    var id: String { rawValue }

    // 常用地区 - 目前只显示美国和澳洲
    static let popular: [DeliveryCountry] = [
        .unitedStates, .australia
    ]

    // 本地化名称
    var localizedName: String {
        switch self {
        case .unitedStates:
            return "country_united_states".localized
        case .australia:
            return "country_australia".localized
        // 🚫 暂时注释掉其他国家的本地化名称
        // case .canada:
        //     return "country_canada".localized
        // case .unitedKingdom:
        //     return "country_united_kingdom".localized
        // case .germany:
        //     return "country_germany".localized
        // case .france:
        //     return "country_france".localized
        // case .japan:
        //     return "country_japan".localized
        // case .china:
        //     return "country_china".localized
        }
    }

    // SF Symbols图标
    var systemIcon: String {
        return "flag.fill"
    }

    // 图标颜色
    var iconColor: Color {
        switch self {
        case .unitedStates: return .blue
        case .australia: return .green
        // 🚫 暂时注释掉其他国家的图标颜色
        // case .canada: return .red
        // case .unitedKingdom: return .purple
        // case .germany: return .orange
        // case .france: return .blue
        // case .japan: return .red
        // case .china: return .red
        }
    }

    // 支持的快递服务数量（示例）
    var supportedServicesCount: Int {
        switch self {
        case .unitedStates: return 8
        case .australia: return 2
        // 🚫 暂时注释掉其他国家的服务数量
        // case .canada: return 3
        // case .unitedKingdom: return 4
        // case .germany: return 3
        // case .france: return 3
        // case .japan: return 2
        // case .china: return 5
        }
    }

    // 映射到现有的DeliveryRegion系统
    var deliveryRegion: DeliveryRegion {
        switch self {
        case .unitedStates:
            return .usa
        case .australia:
            return .australia
        }
    }

    // 主题颜色
    var themeColor: Color {
        switch self {
        case .unitedStates:
            return .orange
        case .australia:
            return .blue
        // 🚫 暂时注释掉其他国家的主题颜色
        // case .canada:
        //     return .red
        // case .unitedKingdom:
        //     return .purple
        // default:
        //     return .blue
        }
    }
}

// MARK: - 检测状态
enum CountryDetectionState {
    case detecting
    case detected(country: DeliveryCountry, confidence: Float)
    case userConfirmed(country: DeliveryCountry)
    case userOverride(country: DeliveryCountry)
}

// MARK: - 国家检测视图
struct CountryDetectionView: View {
    @State private var detectionState: CountryDetectionState = .detecting
    @State private var showCountrySelector = false

    var body: some View {
        VStack(spacing: 16) {
            switch detectionState {
            case .detecting:
                detectingView
            case .detected(let country, let confidence):
                detectedView(country: country, confidence: confidence)
            case .userConfirmed(let country), .userOverride(let country):
                confirmedView(country: country)
            }
        }
        .onAppear {
            detectUserCountry()
        }
        .sheet(isPresented: $showCountrySelector) {
            CountrySelector(
                selectedCountry: getCurrentCountry(),
                onCountrySelected: { country in
                    detectionState = .userOverride(country: country)
                    showCountrySelector = false
                }
            )
        }
    }

    // MARK: - 检测中视图
    private var detectingView: some View {
        HStack {
            ProgressView()
                .scaleEffect(0.8)
            Text("detecting_location".localized)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
    }

    // MARK: - 检测结果视图 - 更醒目的设计
    private func detectedView(country: DeliveryCountry, confidence: Float) -> some View {
        VStack(spacing: 16) {
            // 检测结果卡片
            VStack(spacing: 12) {
                // 检测结果标题 - 更醒目
                HStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.blue.opacity(0.15))
                            .frame(width: 40, height: 40)

                        Image(systemName: "location.fill")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.blue)
                    }

                    VStack(alignment: .leading, spacing: 2) {
                        Text(String(format: "detected_in_country".localized, country.localizedName))
                            .font(.headline.weight(.bold))
                            .foregroundColor(.primary)

                        Text(country.localizedName)
                            .font(.subheadline)
                            .foregroundColor(country.themeColor)
                    }

                    Spacer()

                    Button(action: { showCountrySelector = true }) {
                        Image(systemName: "pencil.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.blue)
                    }
                }

                // 置信度警告 - 更醒目
                if confidence < 0.8 {
                    HStack(spacing: 8) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.system(size: 16))
                        Text("low_confidence_detection".localized)
                            .font(.caption.weight(.medium))
                            .foregroundColor(.orange)
                        Spacer()
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.blue.opacity(0.3), lineWidth: 2)
            )

            // 确认按钮 - 更醒目的设计
            HStack(spacing: 16) {
                Button(action: {
                    detectionState = .userConfirmed(country: country)
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                        Text("confirm_country".localized)
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }

                Button(action: {
                    showCountrySelector = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "globe")
                        Text("change_country".localized)
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(Color(.systemGray5))
                    .foregroundColor(.primary)
                    .cornerRadius(12)
                }
            }
        }
    }

    // MARK: - 确认后视图 - 醒目的卡片设计
    private func confirmedView(country: DeliveryCountry) -> some View {
        VStack(spacing: 16) {
            // 主要信息卡片
            HStack(spacing: 16) {
                // 国家图标 - 更大更醒目
                ZStack {
                    Circle()
                        .fill(country.themeColor.opacity(0.15))
                        .frame(width: 56, height: 56)

                    Image(systemName: country.systemIcon)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(country.themeColor)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(String(format: "delivery_services_for_country".localized, country.localizedName))
                        .font(.title2.weight(.bold))
                        .foregroundColor(.primary)

                    Text(String(format: "supported_services_count".localized, country.supportedServicesCount))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 编辑按钮 - 更醒目的设计
                Button(action: { showCountrySelector = true }) {
                    Image(systemName: "pencil.circle.fill")
                        .font(.system(size: 28))
                        .foregroundColor(.blue)
                        .background(Color.white)
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: country.themeColor.opacity(0.2), radius: 8, x: 0, y: 4)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(country.themeColor.opacity(0.3), lineWidth: 2)
            )
        }
    }

    // MARK: - 私有方法
    private func detectUserCountry() {
        // 模拟检测过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let deviceCountry = Locale.current.region?.identifier ?? "US"
            let country = DeliveryCountry(rawValue: deviceCountry) ?? .unitedStates
            let confidence = calculateConfidence(deviceCountry)

            detectionState = .detected(country: country, confidence: confidence)
        }
    }

    private func calculateConfidence(_ countryCode: String) -> Float {
        // 简化的置信度计算
        var confidence: Float = 0.7

        // 设备语言匹配
        let languageCode: String?
        if #available(iOS 16, *) {
            languageCode = Locale.current.language.languageCode?.identifier
        } else {
            languageCode = Locale.current.languageCode
        }

        if let languageCode = languageCode {
            switch (countryCode, languageCode) {
            case ("US", "en"), ("AU", "en"), ("GB", "en"):
                confidence += 0.2
            case ("CN", "zh"), ("TW", "zh"):
                confidence += 0.2
            case ("JP", "ja"):
                confidence += 0.2
            case ("DE", "de"):
                confidence += 0.2
            case ("FR", "fr"):
                confidence += 0.2
            default:
                break
            }
        }

        return min(confidence, 1.0)
    }

    private func getCurrentCountry() -> DeliveryCountry {
        switch detectionState {
        case .detected(let country, _), .userConfirmed(let country), .userOverride(let country):
            return country
        default:
            return .unitedStates
        }
    }
}

// MARK: - 国家选择器
struct CountrySelector: View {
    let selectedCountry: DeliveryCountry
    let onCountrySelected: (DeliveryCountry) -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                Section("popular_regions".localized) {
                    ForEach(DeliveryCountry.popular) { country in
                        countryRow(country)
                    }
                }

                Section("all_regions".localized) {
                    ForEach(DeliveryCountry.allCases) { country in
                        countryRow(country)
                    }
                }
            }
            .navigationTitle("select_region".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
        }
    }

    private func countryRow(_ country: DeliveryCountry) -> some View {
        HStack {
            Image(systemName: country.systemIcon)
                .foregroundColor(country.iconColor)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(country.localizedName)
                    .font(.body)

                Text(String(format: "supported_services_count".localized, country.supportedServicesCount))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if selectedCountry == country {
                Image(systemName: "checkmark")
                    .foregroundColor(.blue)
                    .font(.body.weight(.semibold))
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onCountrySelected(country)
        }
    }
}

// MARK: - 带回调的国家检测视图
struct CountryDetectionViewWithCallback: View {
    let onCountryChanged: (DeliveryCountry) -> Void
    @State private var detectionState: CountryDetectionState = .detecting
    @State private var showCountrySelector = false

    var body: some View {
        VStack(spacing: 16) {
            switch detectionState {
            case .detecting:
                detectingView
            case .detected(let country, let confidence):
                detectedView(country: country, confidence: confidence)
            case .userConfirmed(let country), .userOverride(let country):
                confirmedView(country: country)
            }
        }
        .onAppear {
            detectUserCountry()
        }
        .sheet(isPresented: $showCountrySelector) {
            CountrySelector(
                selectedCountry: getCurrentCountry(),
                onCountrySelected: { country in
                    detectionState = .userOverride(country: country)
                    onCountryChanged(country)
                    showCountrySelector = false
                }
            )
        }
    }

    // MARK: - 检测中视图
    private var detectingView: some View {
        HStack(spacing: 12) {
            ProgressView()
                .scaleEffect(0.8)

            Text("detecting_location".localized)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding()
        .background(Color.adaptiveSecondaryBackground)
        .cornerRadius(12)
    }

    // MARK: - 检测结果视图 - 更醒目的设计
    private func detectedView(country: DeliveryCountry, confidence: Float) -> some View {
        VStack(spacing: 16) {
            // 检测结果卡片
            VStack(spacing: 12) {
                // 检测结果标题 - 更醒目
                HStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.adaptivePrimaryIcon.opacity(0.15))
                            .frame(width: 40, height: 40)

                        Image(systemName: "location.fill")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.adaptivePrimaryIcon)
                    }

                    VStack(alignment: .leading, spacing: 2) {
                        Text(String(format: "detected_in_country".localized, country.localizedName))
                            .font(.headline.weight(.bold))
                            .foregroundColor(.adaptivePrimaryText)

                        Text(country.localizedName)
                            .font(.subheadline)
                            .foregroundColor(country.themeColor)
                    }

                    Spacer()

                    Image(systemName: "pencil")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.adaptivePrimaryIcon)
                }

                // 置信度警告（如果需要）
                if confidence < 0.8 {
                    HStack(spacing: 8) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.adaptiveWarning)
                            .font(.caption)

                        Text("low_confidence_detection".localized)
                            .font(.caption)
                            .foregroundColor(.adaptiveWarning)

                        Spacer()
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.adaptiveWarning.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.adaptiveCardBackground)
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(country.themeColor.opacity(0.3), lineWidth: 2)
            )
            .contentShape(Rectangle()) // 使整个卡片区域可点击
            .onTapGesture {
                showCountrySelector = true
            }

            // 确认按钮
            HStack(spacing: 12) {
                Button(action: {
                    detectionState = .userConfirmed(country: country)
                    onCountryChanged(country)
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .semibold))
                        Text("confirm_country".localized)
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.adaptivePrimaryButton)
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    showCountrySelector = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "globe")
                            .font(.system(size: 14, weight: .medium))
                        Text(NSLocalizedString("change_country", comment: ""))
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.adaptivePrimaryText)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.adaptiveSecondaryButton)
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    // MARK: - 确认后视图
    private func confirmedView(country: DeliveryCountry) -> some View {
        HStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(country.themeColor.opacity(0.15))
                    .frame(width: 32, height: 32)

                Image(systemName: country.systemIcon)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(country.themeColor)
            }

            Text("\(country.localizedName)\("delivery_service".localized)")
                .font(.subheadline.weight(.semibold))
                .foregroundColor(.adaptivePrimaryText)

            Spacer()

            Image(systemName: "pencil")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.adaptivePrimaryIcon)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(country.themeColor.opacity(0.1))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(country.themeColor.opacity(0.3), lineWidth: 1)
        )
        .contentShape(Rectangle()) // 使整个区域可点击
        .onTapGesture {
            showCountrySelector = true
        }
    }

    // MARK: - 私有方法
    private func detectUserCountry() {
        // 模拟检测过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let deviceCountry = Locale.current.region?.identifier ?? "US"
            let country = DeliveryCountry(rawValue: deviceCountry) ?? .unitedStates
            let confidence = calculateConfidence(deviceCountry)

            detectionState = .detected(country: country, confidence: confidence)
            onCountryChanged(country) // 通知父组件
        }
    }

    private func calculateConfidence(_ countryCode: String) -> Float {
        // 简单的置信度计算 - 目前只支持美国和澳洲
        let supportedCountries = ["US", "AU"]
        return supportedCountries.contains(countryCode) ? 0.9 : 0.6
    }

    private func getCurrentCountry() -> DeliveryCountry {
        switch detectionState {
        case .detected(let country, _), .userConfirmed(let country), .userOverride(let country):
            return country
        case .detecting:
            return .unitedStates
        }
    }
}

// MARK: - 预览
#Preview("Country Detection") {
    CountryDetectionView()
        .padding()
}

#Preview("Country Detection With Callback") {
    CountryDetectionViewWithCallback { country in
        print("Selected country: \(country.localizedName)")
    }
    .padding()
}

#Preview("Country Selector") {
    CountrySelector(
        selectedCountry: .unitedStates,
        onCountrySelected: { _ in }
    )
}
