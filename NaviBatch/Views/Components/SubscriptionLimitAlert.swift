import SwiftUI

struct SubscriptionLimitAlert: View {
    let totalPoints: Int
    let optimizablePoints: Int
    let onUpgrade: () -> Void
    let onContinue: () -> Void
    let onCancel: () -> Void

    @State private var showOneClickPromo = false

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text("free_version_optimization_limit".localized)
                .font(.headline)
                .padding(.top)

            // 分隔线
            Divider()

            // 内容
            VStack(alignment: .leading, spacing: 12) {
                Text("free_version_supports_max_addresses".localized(with: optimizablePoints))
                    .font(.subheadline)

                Text("current_route_contains_addresses".localized(with: totalPoints))
                    .font(.subheadline)

                Button(action: {
                    showOneClickPromo = true
                }) {
                    HStack {
                        Text("upgrade_to_pro_unlimited_addresses".localized)
                            .font(.subheadline)
                            .foregroundColor(.blue)

                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                            .font(.caption)
                    }
                }
                .sheet(isPresented: $showOneClickPromo) {
                    OneClickNavigationPromoView()
                }
            }
            .padding(.horizontal)

            // 分隔线
            Divider()

            // 按钮
            HStack(spacing: 15) {
                // 取消按钮
                Button(action: onCancel) {
                    Text("cancel".localized)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }

                // 继续按钮
                Button(action: onContinue) {
                    Text("continue_optimization".localized)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal)

            // 升级按钮
            Button(action: onUpgrade) {
                HStack {
                    Image(systemName: "bolt.fill")
                    Text("upgrade_unlock_one_click_navigation".localized)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .padding(.horizontal)

            // 了解更多按钮
            Button(action: {
                showOneClickPromo = true
            }) {
                HStack {
                    Image(systemName: "info.circle")
                    Text("learn_one_click_navigation_grouping".localized)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 10)
                .background(Color.yellow.opacity(0.2))
                .foregroundColor(.orange)
                .cornerRadius(8)
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .frame(width: 300)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 10)
    }
}

// 包装器视图，用于在SwiftUI中显示自定义警告框
struct SubscriptionLimitAlertWrapper: ViewModifier {
    @Binding var isPresented: Bool
    let totalPoints: Int
    let optimizablePoints: Int
    let onUpgrade: () -> Void
    let onContinue: () -> Void

    func body(content: Content) -> some View {
        ZStack {
            content
                .blur(radius: isPresented ? 3 : 0)

            if isPresented {
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .transition(.opacity)

                SubscriptionLimitAlert(
                    totalPoints: totalPoints,
                    optimizablePoints: optimizablePoints,
                    onUpgrade: {
                        withAnimation {
                            isPresented = false
                        }
                        onUpgrade()
                    },
                    onContinue: {
                        withAnimation {
                            isPresented = false
                        }
                        onContinue()
                    },
                    onCancel: {
                        withAnimation {
                            isPresented = false
                        }
                    }
                )
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut, value: isPresented)
    }
}

// 扩展View以添加自定义警告框
extension View {
    func subscriptionLimitAlert(
        isPresented: Binding<Bool>,
        totalPoints: Int,
        optimizablePoints: Int,
        onUpgrade: @escaping () -> Void,
        onContinue: @escaping () -> Void
    ) -> some View {
        self.modifier(
            SubscriptionLimitAlertWrapper(
                isPresented: isPresented,
                totalPoints: totalPoints,
                optimizablePoints: optimizablePoints,
                onUpgrade: onUpgrade,
                onContinue: onContinue
            )
        )
    }
}

#Preview("SubscriptionLimitAlert") {
    ZStack {
        Color.gray.opacity(0.2).edgesIgnoringSafeArea(.all)

        SubscriptionLimitAlert(
            totalPoints: 35,
            optimizablePoints: 20,
            onUpgrade: {},
            onContinue: {},
            onCancel: {}
        )
    }
}
