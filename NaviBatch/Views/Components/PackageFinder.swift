import SwiftUI
import CoreLocation

/// 包裹设置视图
/// 用于配置包裹的大小、类型、位置等信息
struct PackageFinder: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext

    // 绑定的配送点
    @Bindable var deliveryPoint: DeliveryPoint

    // 包裹信息
    @State private var packageCount: Int
    @State private var selectedPackageSize: PackageSize?
    @State private var selectedPackageType: PackageType?
    @State private var selectedVehiclePositions: Set<VehiclePosition> = []
    @State private var selectedDeliveryType: DeliveryType
    @State private var trackingNumber: String = ""

    // 状态变量

    init(deliveryPoint: DeliveryPoint) {
        self.deliveryPoint = deliveryPoint

        // 初始化状态变量
        self._packageCount = State(initialValue: deliveryPoint.packageCount)
        self._selectedPackageSize = State(initialValue: deliveryPoint.packageSizeEnum)
        self._selectedPackageType = State(initialValue: deliveryPoint.packageTypeEnum)
        self._selectedDeliveryType = State(initialValue: deliveryPoint.deliveryTypeEnum)

        // 初始化车辆位置
        var positions: Set<VehiclePosition> = []
        for position in deliveryPoint.vehiclePositions {
            positions.insert(position)
        }
        self._selectedVehiclePositions = State(initialValue: positions)

        // 初始化追踪号码
        self._trackingNumber = State(initialValue: deliveryPoint.trackingNumber ?? "")
    }

    var body: some View {
        NavigationStack {
            Form {
                // 包裹描述部分
                Section(header: Text("包裹描述")) {
                    // 包裹数量
                    Stepper(value: $packageCount, in: 1...99) {
                        HStack {
                            Label("包裹数量", systemImage: "cube.box")
                            Spacer()
                            Text("\(packageCount)")
                                .foregroundColor(.secondary)
                        }
                    }

                    // 包裹大小选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("包裹大小")
                            .font(.subheadline)

                        HStack(spacing: 0) {
                            ForEach(PackageSize.allCases, id: \.self) { size in
                                Button(action: {
                                    selectedPackageSize = size
                                }) {
                                    Text(size.localizedName)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 10)
                                        .background(selectedPackageSize == size ? Color.blue : Color(.systemGray6))
                                        .foregroundColor(selectedPackageSize == size ? .white : .primary)
                                }
                                .buttonStyle(PlainButtonStyle())

                                if size != PackageSize.allCases.last {
                                    Divider()
                                        .background(Color(.systemGray4))
                                        .frame(height: 36)
                                }
                            }
                        }
                        .cornerRadius(8)
                    }

                    // 包裹类型选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("package_type".localized)
                            .font(.subheadline)

                        HStack(spacing: 0) {
                            ForEach(PackageType.allCases, id: \.self) { type in
                                Button(action: {
                                    selectedPackageType = type
                                }) {
                                    Text(type.localizedName)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 10)
                                        .background(selectedPackageType == type ? Color.blue : Color(.systemGray6))
                                        .foregroundColor(selectedPackageType == type ? .white : .primary)
                                }
                                .buttonStyle(PlainButtonStyle())

                                if type != PackageType.allCases.last {
                                    Divider()
                                        .background(Color(.systemGray4))
                                        .frame(height: 36)
                                }
                            }
                        }
                        .cornerRadius(8)
                    }
                }

                // 车辆位置部分 - 紧凑网格布局
                Section(header: Text("select_package_position".localized)) {
                    VStack(spacing: 8) {
                        // 第一行：前中后 + 左右
                        HStack(spacing: 12) {
                            // 前中后位置
                            VStack(alignment: .leading, spacing: 4) {
                                Text("vehicle_area".localized)
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Picker("vehicle_area".localized, selection: Binding<VehiclePosition?>(
                                    get: {
                                        if selectedVehiclePositions.contains(.front) { return .front }
                                        if selectedVehiclePositions.contains(.middle) { return .middle }
                                        if selectedVehiclePositions.contains(.back) { return .back }
                                        return nil
                                    },
                                    set: { newValue in
                                        // 移除其他区域位置
                                        selectedVehiclePositions.remove(.front)
                                        selectedVehiclePositions.remove(.middle)
                                        selectedVehiclePositions.remove(.back)
                                        // 添加新选择的位置
                                        if let position = newValue {
                                            selectedVehiclePositions.insert(position)
                                        }
                                    }
                                )) {
                                    Text("vehicle_position_none".localized).tag(Optional<VehiclePosition>.none)
                                    ForEach([VehiclePosition.front, .middle, .back], id: \.self) { position in
                                        Text(position.localizedName).tag(Optional(position))
                                    }
                                }
                                .pickerStyle(MenuPickerStyle())
                            }
                            .frame(maxWidth: .infinity)

                            // 左右位置
                            VStack(alignment: .leading, spacing: 4) {
                                Text("left_right_position".localized)
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Picker("left_right_position".localized, selection: Binding<VehiclePosition?>(
                                    get: {
                                        if selectedVehiclePositions.contains(.left) { return .left }
                                        if selectedVehiclePositions.contains(.right) { return .right }
                                        return nil
                                    },
                                    set: { newValue in
                                        // 移除其他左右位置
                                        selectedVehiclePositions.remove(.left)
                                        selectedVehiclePositions.remove(.right)
                                        // 添加新选择的位置
                                        if let position = newValue {
                                            selectedVehiclePositions.insert(position)
                                        }
                                    }
                                )) {
                                    Text("vehicle_position_none".localized).tag(Optional<VehiclePosition>.none)
                                    ForEach([VehiclePosition.left, .right], id: \.self) { position in
                                        Text(position.localizedName).tag(Optional(position))
                                    }
                                }
                                .pickerStyle(MenuPickerStyle())
                            }
                            .frame(maxWidth: .infinity)
                        }

                        // 第二行：高度位置（居中）
                        HStack {
                            Spacer()
                            VStack(alignment: .leading, spacing: 4) {
                                Text("height_position".localized)
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Picker("height_position".localized, selection: Binding<VehiclePosition?>(
                                    get: {
                                        if selectedVehiclePositions.contains(.floor) { return .floor }
                                        if selectedVehiclePositions.contains(.shelf) { return .shelf }
                                        return nil
                                    },
                                    set: { newValue in
                                        // 移除其他高度位置
                                        selectedVehiclePositions.remove(.floor)
                                        selectedVehiclePositions.remove(.shelf)
                                        // 添加新选择的位置
                                        if let position = newValue {
                                            selectedVehiclePositions.insert(position)
                                        }
                                    }
                                )) {
                                    Text("vehicle_position_none".localized).tag(Optional<VehiclePosition>.none)
                                    ForEach([VehiclePosition.floor, .shelf], id: \.self) { position in
                                        Text(position.localizedName).tag(Optional(position))
                                    }
                                }
                                .pickerStyle(MenuPickerStyle())
                            }
                            .frame(maxWidth: .infinity)
                            Spacer()
                        }
                    }
                }

                // 配送信息部分
                Section(header: Text("配送信息")) {
                    // 配送类型选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("配送类型")
                            .font(.subheadline)

                        HStack(spacing: 2) {
                            ForEach(DeliveryType.allCases, id: \.self) { type in
                                Button(action: {
                                    selectedDeliveryType = type
                                }) {
                                    HStack(spacing: 6) {
                                        Image(systemName: type == .delivery ? "shippingbox.fill" : "arrow.up.bin.fill")
                                            .font(.system(size: 14))
                                        Text(type.localizedName)
                                            .font(.system(size: 14, weight: .medium))
                                            .lineLimit(1)
                                            .minimumScaleFactor(0.8)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 10)
                                    .background(selectedDeliveryType == type ? Color.blue : Color(.systemGray6))
                                    .foregroundColor(selectedDeliveryType == type ? .white : .primary)
                                }
                                .buttonStyle(PlainButtonStyle())

                                if type != DeliveryType.allCases.last {
                                    Divider()
                                        .background(Color(.systemGray4))
                                        .frame(height: 36)
                                }
                            }
                        }
                        .cornerRadius(8)
                    }

                    // 追踪号码
                    TextField("追踪号码", text: $trackingNumber)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
            }
            .navigationTitle("包裹设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("保存") {
                        saveChanges()
                        dismiss()
                    }
                }
            }
        }
    }

    // 切换车辆位置选择
    private func toggleVehiclePosition(_ position: VehiclePosition) {
        if selectedVehiclePositions.contains(position) {
            selectedVehiclePositions.remove(position)
        } else {
            selectedVehiclePositions.insert(position)
        }
    }



    // 保存更改
    private func saveChanges() {
        // 更新包裹数量
        deliveryPoint.packageCount = packageCount

        // 更新包裹大小
        deliveryPoint.packageSize = selectedPackageSize?.rawValue

        // 更新包裹类型
        deliveryPoint.packageType = selectedPackageType?.rawValue

        // 更新车辆位置
        deliveryPoint.vehiclePosition = selectedVehiclePositions.isEmpty ? nil : selectedVehiclePositions.map { $0.rawValue }.joined(separator: ",")

        // 更新配送类型
        deliveryPoint.deliveryType = selectedDeliveryType.rawValue

        // 更新追踪号码
        deliveryPoint.trackingNumber = trackingNumber.isEmpty ? nil : trackingNumber

        // 保存到数据库
        do {
            try modelContext.save()
            print("[INFO] PackageFinder.swift:317 - Successfully saved package settings, ID: \(deliveryPoint.id)")
        } catch {
            print("[ERROR] PackageFinder.swift:319 - Failed to save package settings: \(error.localizedDescription)")
        }
    }
}

// 预览
struct PackageFinder_Previews: PreviewProvider {
    static var previews: some View {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try! ModelContainer(for: DeliveryPoint.self, configurations: config)

        let samplePoint = DeliveryPoint(
            sort_number: 1,
            streetName: "42 Blackburn Road, Glen Waverley, VIC 3150",
            latitude: -37.8815,
            longitude: 145.1642
        )

        container.mainContext.insert(samplePoint)

        return PackageFinder(deliveryPoint: samplePoint)
            .modelContainer(container)
    }
}
