import SwiftUI
import SwiftData
import CoreLocation

/// 配送状态管理视图
struct DeliveryStatusManager: View {
    // 回调函数，当添加第一个配送点时触发
    var onAddFirstPoint: ((DeliveryPoint?) -> Void)? = nil
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @Query private var deliveryPoints: [DeliveryPoint]
    @State private var selectedStatuses: Set<DeliveryStatus> = []
    @State private var searchText: String = ""
    @State private var selectedPoint: DeliveryPoint?
    @State private var showingPointDetail: Bool = false
    @State private var showingAddPointSheet: Bool = false

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // 状态筛选器
                DeliveryStatusFilterView(selectedStatuses: $selectedStatuses)
                    .padding(.top, 8)

                // 搜索栏和添加按钮
                HStack {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)

                        TextField("搜索地址", text: $searchText)
                            .autocorrectionDisabled()

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(10)
                    .background(Color(.systemGray6))
                    .cornerRadius(10)

                    // 添加配送点按钮
                    Button(action: {
                        showingAddPointSheet = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 22))
                            .foregroundColor(.blue)
                    }
                    .padding(.horizontal, 4)
                }
                .padding(.horizontal)
                .padding(.top, 8)
                .padding(.bottom, 4)

                // 配送点列表
                if filteredPoints.isEmpty {
                    // 空状态视图
                    VStack(spacing: 20) {
                        Spacer()

                        Image(systemName: "shippingbox.circle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray.opacity(0.6))

                        Text("暂无配送点")
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)

                        Text("点击右上角的加号按钮添加您的第一个配送点")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 40)

                        Button(action: {
                            showingAddPointSheet = true
                        }) {
                            HStack {
                                Image(systemName: "plus.circle.fill")
                                Text("添加配送点")
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .padding(.top, 10)

                        Spacer()
                    }
                    .frame(maxWidth: .infinity)
                } else {
                    // 有配送点时显示列表
                    List {
                        ForEach(filteredPoints) { point in
                            DeliveryStatusPointRow(point: point)
                                .contentShape(Rectangle())
                                .onTapGesture {
                                    selectedPoint = point
                                    showingPointDetail = true
                                }
                                .onLongPressGesture {
                                    // 长按删除功能
                                    print("🗑️ 长按删除配送点被触发: \(point.primaryAddress)")
                                    // 添加触觉反馈
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                    impactFeedback.impactOccurred()
                                    if let index = filteredPoints.firstIndex(of: point) {
                                        deletePoints(at: IndexSet(integer: index))
                                    }
                                }
                                .contextMenu {
                                    // 保留上下文菜单作为备选删除方式
                                    Button(role: .destructive) {
                                        print("🗑️ 上下文菜单删除配送点被触发: \(point.primaryAddress)")
                                        if let index = filteredPoints.firstIndex(of: point) {
                                            deletePoints(at: IndexSet(integer: index))
                                        }
                                    } label: {
                                        Label("删除", systemImage: "trash")
                                    }
                                }
                        }
                    }
                    .listStyle(.plain)
                }
            }
            .navigationTitle("配送管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .topBarLeading) {
                    EditButton()
                }
            }
            .sheet(isPresented: $showingPointDetail, onDismiss: {
                selectedPoint = nil
            }) {
                if let point = selectedPoint {
                    DeliveryPointDetailView(point: point)
                }
            }
            .sheet(isPresented: $showingAddPointSheet) {
                AddDeliveryPointSheet(onPointAdded: { newPoint in
                    // 更新配送点编号
                    updateDeliveryPointNumbers()
                    Logger.info("添加了新的配送点: \(newPoint.primaryAddress)", type: .data)

                    // 如果这是第一个配送点，触发回调
                    if deliveryPoints.count == 1 {
                        onAddFirstPoint?(newPoint)
                    }
                })
            }
        }
        .onAppear {
            // 默认显示所有状态
            selectedStatuses = Set([.pending, .inProgress, .completed, .failed])

            // 确保配送点编号正确
            if !deliveryPoints.isEmpty {
                updateDeliveryPointNumbers()
            }
        }
    }

    // 根据筛选条件过滤配送点
    private var filteredPoints: [DeliveryPoint] {
        let statusFiltered = selectedStatuses.isEmpty
            ? deliveryPoints
            : deliveryPoints.filter { selectedStatuses.contains($0.deliveryStatus) }

        if searchText.isEmpty {
            return statusFiltered
        } else {
            return statusFiltered.filter {
                $0.primaryAddress.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // 删除配送点
    private func deletePoints(at offsets: IndexSet) {
        let pointsToDelete = offsets.map { filteredPoints[$0] }

        for point in pointsToDelete {
            modelContext.delete(point)
        }

        do {
            try modelContext.save()
            Logger.info("成功删除\(pointsToDelete.count)个配送点", type: .data)
            // 更新剩余点的编号
            updateDeliveryPointNumbers()
        } catch {
            Logger.error("删除配送点失败: \(error.localizedDescription)", type: .data)
        }
    }

    // 更新配送点编号
    private func updateDeliveryPointNumbers() {
        // 获取所有配送点
        let allPoints = deliveryPoints

        // 按照创建时间排序
        let sortedPoints = allPoints.sorted {
            ($0.statusUpdateTime ?? Date.distantPast) < ($1.statusUpdateTime ?? Date.distantPast)
        }

        // 重新编号
        for (index, point) in sortedPoints.enumerated() {
            point.sort_number = index + 1
            point.sorted_number = index + 1
        }

        // 保存更改
        do {
            try modelContext.save()
            Logger.info("成功更新\(sortedPoints.count)个配送点编号", type: .data)
        } catch {
            Logger.error("更新配送点编号失败: \(error.localizedDescription)", type: .data)
        }
    }
}

/// 配送点行视图
struct DeliveryStatusPointRow: View {
    @Bindable var point: DeliveryPoint

    var body: some View {
        HStack(spacing: 12) {
            // 状态指示器
            DeliveryStatusView(status: point.deliveryStatus, showText: false)

            // 地址信息
            VStack(alignment: .leading, spacing: 4) {
                Text(point.primaryAddress.components(separatedBy: ",").first ?? point.primaryAddress)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)

                HStack(spacing: 8) {
                    if point.packageCount > 1 {
                        Label("\(point.packageCount)个包裹", systemImage: "cube.box")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // 时间显示已移除
                }
            }

            Spacer()

            // 状态文本
            Text(point.deliveryStatus.localizedName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(point.deliveryStatus.color)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(point.deliveryStatus.color.opacity(0.1))
                .cornerRadius(8)

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

struct DeliveryStatusManagerPreview: View {
    var body: some View {
        let config = ModelConfiguration(isStoredInMemoryOnly: false)
        var container: ModelContainer

        do {
            let tempContainer = try ModelContainer(for: DeliveryPoint.self, configurations: config)
            container = tempContainer

            // 检查是否已有示例数据
            let descriptor = FetchDescriptor<DeliveryPoint>()
            let existingPoints = try container.mainContext.fetch(descriptor)

            // 如果没有示例数据，创建新的
            if existingPoints.isEmpty {
                // 创建示例数据
                let statuses: [DeliveryStatus] = [.pending, .inProgress, .completed, .failed]
                let addresses = [
                    "Address 1",
                    "Address 2",
                    "Address 3",
                    "Address 4",
                    "Address 5"
                ]

                for (index, address) in addresses.enumerated() {
                    // 🎯 修复：使用结构化地址创建示例配送点，设置完整地址
                    let point = DeliveryPoint(
                        sort_number: index + 1,
                        streetName: address, // 设置完整地址，让primaryAddress能正确显示
                        originalAddress: address, // 🎯 确保originalAddress也被设置，避免显示管道符号
                        coordinate: CLLocationCoordinate2D(latitude: -37.8815 + Double(index) * 0.005, longitude: 145.1642)
                    )
                    point.status = statuses[index % statuses.count].rawValue
                    point.packageCount = (index % 3) + 1
                    point.statusUpdateTime = Date().addingTimeInterval(-Double(index * 3600))

                    container.mainContext.insert(point)
                }

                try container.mainContext.save()
            }
        } catch {
            print("[INFO] 预览创建容器失败，使用共享容器")
            container = getPersistentContainer()
        }

        return DeliveryStatusManager()
            .modelContainer(container)
    }
}

#Preview("DeliveryStatusManager") {
    DeliveryStatusManagerPreview()
}
