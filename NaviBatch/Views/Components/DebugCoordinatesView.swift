import SwiftUI
import SwiftData
import MapKit

// 调试坐标视图 - 用于检查和修复坐标问题
struct DebugCoordinatesView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Query private var routes: [Route]

    @State private var selectedRoute: Route?
    @State private var isLoading = false
    @State private var message = ""
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    // 日志辅助函数
    private func logInfo(_ message: String) {
        print("[INFO] DebugCoordinatesView - \(message)")
    }

    private func logError(_ message: String) {
        print("[ERROR] DebugCoordinatesView - \(message)")
    }

    var body: some View {
        NavigationStack {
            List {
                Section(header: Text("选择路线")) {
                    ForEach(routes) { route in
                        Button(action: {
                            selectedRoute = route
                        }) {
                            HStack {
                                Text(route.localizedName)
                                Spacer()
                                if selectedRoute?.id == route.id {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                            }
                        }
                    }
                }

                if let route = selectedRoute {
                    Section(header: Text("路线详情")) {
                        Text("路线名称: \(route.localizedName)")
                        Text("创建时间: \(route.createdAt.formatted())")
                        Text("地址点数量: \(route.points.count)")
                    }

                    Section(header: Text("地址点坐标")) {
                        ForEach(route.points.sorted(by: { $0.sort_number < $1.sort_number })) { point in
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    if point.isStartPoint {
                                        Text("起点")
                                            .font(.caption)
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(Color.blue.opacity(0.2))
                                            .cornerRadius(4)
                                    } else if point.isEndPoint {
                                        Text("终点")
                                            .font(.caption)
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(Color.green.opacity(0.2))
                                            .cornerRadius(4)
                                    } else {
                                        Text("停靠点 #\(point.sort_number)")
                                            .font(.caption)
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(Color.gray.opacity(0.2))
                                            .cornerRadius(4)
                                    }

                                    Spacer()

                                    if point.geocodingWarning != nil {
                                        Text("⚠️")
                                            .foregroundColor(.orange)
                                    }
                                }

                                Text(point.primaryAddress)
                                    .font(.subheadline)

                                HStack {
                                    Text("纬度: \(point.latitude)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    Spacer()

                                    Text("经度: \(point.longitude)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                if let warning = point.geocodingWarning {
                                    Text(warning)
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }

                    Section {
                        Button(action: {
                            recalculateDistances()
                        }) {
                            Label("重新计算距离", systemImage: "arrow.triangle.2.circlepath")
                        }

                        Button(action: {
                            clearWarnings()
                        }) {
                            Label("清除所有警告", systemImage: "exclamationmark.triangle")
                        }
                    }
                }
            }
            .navigationTitle("坐标调试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .overlay {
                if isLoading {
                    ProgressView()
                        .scaleEffect(1.5)
                        .frame(width: 100, height: 100)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .shadow(radius: 10)
                }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
    }

    // 计算两个地理坐标点之间的距离（单位：米）
    private func distance(from coord1: CLLocationCoordinate2D, to coord2: CLLocationCoordinate2D) -> CLLocationDistance {
        // 检查坐标是否有效
        guard coord1.latitude >= -90 && coord1.latitude <= 90 &&
              coord1.longitude >= -180 && coord1.longitude <= 180 &&
              coord2.latitude >= -90 && coord2.latitude <= 90 &&
              coord2.longitude >= -180 && coord2.longitude <= 180 else {
            logError("距离计算错误：无效坐标 - 源: (\(coord1.latitude), \(coord1.longitude)), 目标: (\(coord2.latitude), \(coord2.longitude))")
            return 0 // 返回0表示无效距离
        }

        // 使用 Haversine 公式计算球面距离
        let earthRadius = 6371000.0 // 地球半径，单位：米

        let lat1 = coord1.latitude * .pi / 180
        let lon1 = coord1.longitude * .pi / 180
        let lat2 = coord2.latitude * .pi / 180
        let lon2 = coord2.longitude * .pi / 180

        let dLat = lat2 - lat1
        let dLon = lon2 - lon1

        let a = sin(dLat/2) * sin(dLat/2) + cos(lat1) * cos(lat2) * sin(dLon/2) * sin(dLon/2)
        let c = 2 * atan2(sqrt(a), sqrt(1-a))
        let distance = earthRadius * c

        // 记录计算结果
        logInfo("距离计算: 源: (\(coord1.latitude), \(coord1.longitude)), 目标: (\(coord2.latitude), \(coord2.longitude)), 距离: \(distance)米")

        return distance
    }

    // 重新计算距离
    private func recalculateDistances() {
        guard let route = selectedRoute else { return }

        isLoading = true

        // 获取起点
        guard let startPoint = route.points.first(where: { $0.isStartPoint }) else {
            isLoading = false
            alertTitle = "错误"
            alertMessage = "路线中没有设置起点"
            showAlert = true
            return
        }

        // 计算每个点与起点的距离
        var updatedCount = 0
        let distanceThreshold: CLLocationDistance = 10_000_000 // 10000公里阈值（基本禁用）

        for point in route.points where !point.isStartPoint {
            // 检查坐标是否有效
            if point.latitude < -90 || point.latitude > 90 ||
               point.longitude < -180 || point.longitude > 180 ||
               startPoint.latitude < -90 || startPoint.latitude > 90 ||
               startPoint.longitude < -180 || startPoint.longitude > 180 {
                // 坐标无效，设置警告
                point.geocodingWarning = "坐标无效，请重新添加地址"
                logError(String(format: "DebugCoordinatesView - recalculateDistances: 点 '%@' 或起点坐标无效，已设置警告。", point.primaryAddress))
                updatedCount += 1
                continue
            }

            // 计算距离
            let dist = distance(from: point.coordinate, to: startPoint.coordinate)

            // 检查距离是否异常
            if dist > distanceThreshold {
                // 如果距离超过阈值，设置警告
                point.geocodingWarning = String(format: "距起点 %.0f 公里，请核实地址", dist / 1000)
                logInfo(String(format: "DebugCoordinatesView - recalculateDistances: 点 '%@' 距离起点过远 (%.0f km)，已设置警告。",
                              point.primaryAddress, dist / 1000))
                updatedCount += 1
            } else if dist == 0 {
                // 如果距离为0，可能是计算错误
                point.geocodingWarning = "距离计算错误，请重新添加地址"
                logError(String(format: "DebugCoordinatesView - recalculateDistances: 点 '%@' 距离计算错误，已设置警告。", point.primaryAddress))
                updatedCount += 1
            } else {
                // 距离正常，清除警告
                if point.geocodingWarning != nil {
                    point.geocodingWarning = nil
                    updatedCount += 1
                }
            }
        }

        // 保存更改
        try? modelContext.save()

        isLoading = false
        alertTitle = "完成"
        alertMessage = "已重新计算距离，更新了 \(updatedCount) 个点的警告信息"
        showAlert = true
    }

    // 清除所有警告
    private func clearWarnings() {
        guard let route = selectedRoute else { return }

        isLoading = true

        var clearedCount = 0
        for point in route.points {
            if point.geocodingWarning != nil {
                point.geocodingWarning = nil
                clearedCount += 1
            }
        }

        // 保存更改
        try? modelContext.save()

        isLoading = false
        alertTitle = "完成"
        alertMessage = "已清除 \(clearedCount) 个点的警告信息"
        showAlert = true
    }
}
