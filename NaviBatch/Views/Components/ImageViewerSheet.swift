import SwiftUI

// MARK: - 🎯 图片查看器组件
struct ImageViewerSheet: View {
    let images: [UIImage]
    @Binding var selectedIndex: Int
    let onDelete: (Int) -> Void
    let onReorder: (Int, Int) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex: Int = 0
    
    var body: some View {
        NavigationView {
            TabView(selection: $currentIndex) {
                ForEach(0..<images.count, id: \.self) { index in
                    ZoomableImageView(image: images[index])
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
            .navigationTitle("image_viewer".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("close".localized) {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(role: .destructive) {
                            onDelete(currentIndex)
                        } label: {
                            Label("delete_image".localized, systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
        .onAppear {
            currentIndex = selectedIndex
        }
    }
}

// MARK: - 预览
#Preview("ImageViewerSheet") {
    ImageViewerSheet(
        images: [
            UIImage(systemName: "photo")!,
            UIImage(systemName: "photo.fill")!,
            UIImage(systemName: "camera")!
        ],
        selectedIndex: .constant(0),
        onDelete: { _ in },
        onReorder: { _, _ in }
    )
}
