import SwiftUI
import MapKit
import SwiftData
import os.log
import CoreLocation

/// 添加保存地址表单
/// 使用统一的SimpleAddressSheet组件实现地址输入功能
struct AddSavedAddressSheet: View {
    @Environment(\.modelContext) private var modelContext
    @State private var isFavorite: Bool = false

    var onAddressAdded: ((SavedAddress) -> Void)? = nil

    var body: some View {
        simpleAddressSheet
    }

    private var simpleAddressSheet: some View {
        // 使用非绑定初始化方法
        SimpleAddressSheet(
            onAddressAdded: handleAddressAdded,
            saveToAddressBook: true,
            pointToEdit: nil
        )
    }

    private func handleAddressAdded(savedAddress: SavedAddress) {
        Task {
            do {
                let container = getPersistentContainer()
                let lat = savedAddress.latitude
                let lon = savedAddress.longitude
                let descriptor = FetchDescriptor<SavedAddress>(
                    predicate: #Predicate { $0.latitude == lat && $0.longitude == lon }
                )
                let existing = try container.mainContext.fetch(descriptor)
                if let exist = existing.first {
                    logInfo("AddSavedAddressSheet - 地址已存在，不重复保存: \(exist.address), ID: \(exist.id.uuidString)")
                    return
                }
                // 🎯 标准化地址后保存
                let standardizedAddress = AddressStandardizer.standardizeAddress(savedAddress.address)
                let newAddress = SavedAddress(
                    address: standardizedAddress,
                    coordinate: savedAddress.coordinate
                )
                newAddress.isFavorite = isFavorite
                container.mainContext.insert(newAddress)
                modelContext.insert(newAddress)
                try container.mainContext.save()
                try modelContext.save()
                logInfo("AddSavedAddressSheet - 成功保存地址到数据库: ID: \(newAddress.id.uuidString)")
                NotificationCenter.default.post(
                    name: Notification.Name("AddressAddedNotification"),
                    object: newAddress.id.uuidString
                )
                logInfo("成功添加保存地址: \(savedAddress.address), ID: \(newAddress.id.uuidString)")
                onAddressAdded?(newAddress)
            } catch {
                logError("添加地址失败: \(error.localizedDescription)")
            }
        }
    }
}

#Preview("AddSavedAddressSheet") {
    // 使用持久化存储
    let schema = Schema([SavedAddress.self])
    let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
    let container = try! ModelContainer(for: schema, configurations: [config])

    // 打印数据库路径
    if let url = container.configurations.first?.url {
        print("AddSavedAddressSheet Preview - 数据库路径: \(url.path)")
    }

    return AddSavedAddressSheet()
        .modelContainer(container)
}
