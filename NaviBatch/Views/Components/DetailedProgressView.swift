import SwiftUI

/// 详细进度显示视图
/// 用于显示文件导入的详细进度信息
struct DetailedProgressView: View {
    let progress: Double
    let currentBatch: Int
    let totalBatches: Int
    let processedCount: Int
    let totalCount: Int
    let successCount: Int
    let warningCount: Int
    let currentAddress: String
    let estimatedTimeRemaining: TimeInterval

    
    var body: some View {
        VStack(spacing: 16) {
            // 主进度条
            VStack(spacing: 8) {
                ProgressView(value: progress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle())
                    .animation(.easeInOut(duration: 0.3), value: progress)
                
                // 进度百分比和状态
                HStack {
                    Text("processing_addresses".localized)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("\(Int(progress * 100))%")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                }
            }
            
            // 详细统计信息
            VStack(spacing: 12) {
                // 地址处理统计
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("addresses_processed".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(processedCount)/\(totalCount)")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    
                    Spacer()
                    
                    // 批次信息
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("current_batch".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(currentBatch)/\(totalBatches)")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                }
                


                // 成功和警告统计
                if successCount > 0 || warningCount > 0 {
                    HStack {
                        // 成功数量
                        HStack(spacing: 6) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)

                            Text("\(successCount) " + "successful".localized)
                                .font(.caption)
                                .foregroundColor(.green)
                        }

                        Spacer()

                        // 警告数量
                        if warningCount > 0 {
                            HStack(spacing: 6) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)

                                Text("\(warningCount) " + "warnings".localized)
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }
                
                // 预估剩余时间
                if estimatedTimeRemaining > 0 {
                    HStack {
                        Image(systemName: "clock")
                            .foregroundColor(.secondary)
                            .font(.caption)
                        
                        Text("estimated_time_remaining".localized + ": " + formatTimeRemaining(estimatedTimeRemaining))
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                }
                
                // 当前处理的地址
                if !currentAddress.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("currently_processing".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(currentAddress)
                            .font(.caption)
                            .foregroundColor(.primary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            
            // API限制说明
            VStack(spacing: 6) {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                        .font(.caption)
                    
                    Text("api_rate_limit_info".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                
                HStack {
                    Image(systemName: "map")
                        .foregroundColor(.blue)
                        .font(.caption)
                    
                    Text("geocoding_accuracy_info".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    /// 格式化剩余时间显示
    private func formatTimeRemaining(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        
        if minutes > 0 {
            return String(format: "%d " + "minutes".localized + " %d " + "seconds".localized, minutes, seconds)
        } else {
            return String(format: "%d " + "seconds".localized, seconds)
        }
    }
}

#Preview("DetailedProgressView") {
    VStack(spacing: 20) {
        // 进行中的进度
        DetailedProgressView(
            progress: 0.65,
            currentBatch: 13,
            totalBatches: 20,
            processedCount: 65,
            totalCount: 100,
            successCount: 58,
            warningCount: 7,
            currentAddress: "123 Main Street, Sydney NSW 2000, Australia",
            estimatedTimeRemaining: 180
        )

        // 早期阶段的进度
        DetailedProgressView(
            progress: 0.15,
            currentBatch: 3,
            totalBatches: 20,
            processedCount: 15,
            totalCount: 100,
            successCount: 14,
            warningCount: 1,
            currentAddress: "456 George Street, Melbourne VIC 3000",
            estimatedTimeRemaining: 420
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
