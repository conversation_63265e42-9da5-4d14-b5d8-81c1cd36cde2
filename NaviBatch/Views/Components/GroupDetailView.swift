import SwiftUI
import SwiftData
import MapKit
import CoreLocation

/*
 🚨 重要修改说明 - 排序字段保护策略

 为了确保数据完整性和第三方排序号的优先级，本文件已修改为：

 1. 🔒 保护所有排序字段：
    - sort_number（原始排序）- 不允许修改
    - sorted_number（当前排序）- 不允许修改
    - thirdPartySortNumber（第三方排序）- 只允许在DeliveryPointManagerView中修改

 2. 🎯 第三方排序号优先：
    - 如果存在第三方排序号，整个路线都使用第三方排序
    - sortedPoints方法已更新为优先使用第三方排序号

 3. 🛡️ 路线优化保护：
    - 路线优化不再修改任何排序字段
    - 只标记isOptimized状态和重新计算距离
    - 保持原有排序逻辑不变

 4. 📍 距离计算准确性：
    - 距离计算基于正确的排序顺序
    - 避免因排序修改导致的距离不准确问题
 */

// MARK: - 优化模式枚举
enum OptimizationMode {
    case smart      // 智能优化：自动尝试所有配送点作为起始点
    case manual     // 手动选择起始点
}

// 🎯 起始点选择选项
enum StartPointOption: Identifiable, Hashable {
    case driverLocation
    case deliveryPoint(DeliveryPoint)

    var id: String {
        switch self {
        case .driverLocation:
            return "driver_location"
        case .deliveryPoint(let point):
            return "point_\(point.id.uuidString)"
        }
    }

    var title: String {
        switch self {
        case .driverLocation:
            return "司机当前位置"
        case .deliveryPoint(let point):
            return point.primaryAddress
        }
    }

    var displayName: String {
        switch self {
        case .driverLocation:
            return "司机位置"
        case .deliveryPoint(let point):
            return "配送点 \(point.sorted_number)"
        }
    }

    func coordinate(driverLocation: CLLocationCoordinate2D?) -> CLLocationCoordinate2D {
        switch self {
        case .driverLocation:
            return driverLocation ?? CLLocationCoordinate2D(latitude: 38.9307, longitude: -121.0900) // 加州Auburn附近
        case .deliveryPoint(let point):
            return point.coordinate
        }
    }
}

/// 分组详情视图 - 用于管理分组中的地址
struct GroupDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext

    let group: DeliveryGroup
    let viewModel: RouteViewModel

    @State private var isEditing: Bool = false
    @State private var editedName: String
    @State private var points: [DeliveryPoint]
    @State private var showDeleteAlert: Bool = false
    @State private var pointToDelete: DeliveryPoint?
    @State private var refreshTrigger: Bool = false

    // 路线优化状态
    @State private var isOptimizing: Bool = false
    @State private var originalDistance: Double = 0.0
    @State private var optimizedDistance: Double = 0.0

    // 🎯 Group距离显示状态
    @State private var currentTotalDistance: Double = 0.0
    @State private var isCalculatingDistance: Bool = false

    // 🚀 智能缓存状态
    @State private var lastCalculatedPointsHash: String = ""
    @State private var lastCalculatedDistance: Double = 0.0
    @State private var hasValidCache: Bool = false

    // 🎯 起始点选择状态
    @State private var showStartPointSelection: Bool = false
    @State private var selectedStartPoint: StartPointOption? = nil

    // 🎯 优化模式选择状态
    @State private var showOptimizationModeSelection: Bool = false

    init(group: DeliveryGroup, viewModel: RouteViewModel) {
        self.group = group
        self.viewModel = viewModel
        _editedName = State(initialValue: group.name)
        _points = State(initialValue: group.sortedPoints)
    }

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏 - Dark Mode 优化
            HStack {
                if isEditing {
                    TextField("group_name".localized, text: $editedName)
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(Color.adaptiveSecondaryButton)
                        .cornerRadius(8)
                } else {
                    Text(group.name)
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)
                }

                Spacer()

                // 🎯 显示Group总距离
                HStack(spacing: 4) {
                    if isCalculatingDistance {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                            .scaleEffect(0.6)
                    } else {
                        // 🔄 重新计算距离按钮 - 不修改排序，只重新计算距离
                        Button(action: {
                            print("🔄 [DEBUG] 重新计算距离按钮被点击了！")
                            reorderToSequential()
                        }) {
                            Image(systemName: "arrow.counterclockwise")
                                .foregroundColor(.orange)
                                .font(.caption)
                        }
                        .buttonStyle(PlainButtonStyle())

                        Image(systemName: group.isGroupOptimized ? "location.fill" : "location")
                            .foregroundColor(group.isGroupOptimized ? .green : .blue)
                            .font(.caption)

                        Text(formatDistanceDisplay())
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(getDistanceColor())
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)

                Button(isEditing ? "done".localized : "edit".localized) {
                    if isEditing {
                        saveChanges()
                    }
                    isEditing.toggle()
                }
                .foregroundColor(.adaptivePrimaryIcon)
            }
            .padding(.horizontal)
            .padding(.top, 16)
            .padding(.bottom, 8)

            Divider()

            // 地址列表
            List {
                ForEach(points) { point in
                    addressRow(for: point)
                }
                .onMove { from, to in
                    points.move(fromOffsets: from, toOffset: to)
                    savePointsOrder()
                }
            }
            .listStyle(PlainListStyle())
            .id(refreshTrigger) // 🔧 添加刷新触发器

            // 底部按钮区域 - 平放布局，黑底白字
            HStack(spacing: 12) {
                // 路线优化按钮
                if points.count > 2 {
                    Button(action: {
                        print("[DEBUG] GroupDetailView - 优化按钮点击，开始验证数据")
                        verifyDataConsistency()
                        showOptimizationModeSelection = true
                    }) {
                        HStack {
                            if isOptimizing {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("optimizing_route".localized)
                            } else {
                                Image(systemName: "arrow.triangle.swap")
                                Text("Optimize")
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.black)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isOptimizing)
                }

                // 导航按钮 - 黑底白字
                Button(action: {
                    navigateToGroup()
                }) {
                    HStack {
                        Image(systemName: "location.fill")
                        Text("Navigate")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.black)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
            }
            .padding()
            .background(Color.adaptiveCardBackground)
        }
        .navigationTitle("group_details".localized)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            if isEditing {
                EditButton()
            }
        }
        .alert("confirm_delete".localized, isPresented: $showDeleteAlert) {
            Button("cancel".localized, role: .cancel) {}
            Button("delete".localized, role: .destructive) {
                if let point = pointToDelete {
                    removePointFromGroup(point)
                }
            }
        } message: {
            if let point = pointToDelete {
                Text(String(format: "confirm_remove_address".localized, point.primaryAddress))
            } else {
                Text("confirm_remove_this_address".localized)
            }
        }
        .onAppear {
            // 🔍 调试：显示界面加载时的数据状态
            print("[DEBUG] GroupDetailView - onAppear: 界面加载")
            print("[DEBUG] GroupDetailView - 界面显示的points数组:")
            for (index, point) in points.enumerated() {
                print("[DEBUG]   界面索引\(index): \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
            }

            // 🔧 只在首次加载时同步points数组，避免覆盖优化结果
            if !group.isGroupOptimized {
                points = group.sortedPoints
                print("[DEBUG] GroupDetailView - 首次加载，同步points数组")
            } else {
                print("[DEBUG] GroupDetailView - 分组已优化，保持当前points顺序")
            }

            print("[DEBUG] GroupDetailView - 当前points数组:")
            for (index, point) in points.enumerated() {
                print("[DEBUG]   索引\(index): \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
            }

            // 🚀 智能距离计算：只在必要时重新计算
            calculateCurrentDistanceIfNeeded()
        }

        .sheet(isPresented: $showStartPointSelection) {
            StartPointSelectionView(
                group: group,
                driverLocation: viewModel.driverLocation,
                onStartPointSelected: { startPoint in
                    selectedStartPoint = startPoint
                    showStartPointSelection = false
                    optimizeGroupRoute(from: startPoint)
                }
            )
        }
        .sheet(isPresented: $showOptimizationModeSelection) {
            OptimizationModeSelectionView(
                group: group,
                viewModel: viewModel,
                onModeSelected: { mode in
                    showOptimizationModeSelection = false
                    handleOptimizationMode(mode)
                }
            )
        }
    }

    // 显示文本 - 显示当前的访问顺序编号
    private func displayText(for point: DeliveryPoint) -> String {
        if point.isThirdPartyWithSort, let thirdPartySort = point.thirdPartySortNumber {
            return thirdPartySort
        } else {
            // ✅ 显示当前的访问顺序编号（优化后会更新）
            return "\(point.sorted_number)"
        }
    }

    // 地址行视图 - 增强版，支持第三方标签和 Dark Mode
    private func addressRow(for point: DeliveryPoint) -> some View {
        HStack(spacing: 12) {
            // 序号圆圈 - Dark Mode 优化，根据状态显示不同颜色
            Text(displayText(for: point))
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(adaptiveNumberBackground(for: point))
                .cornerRadius(6)

            // 地址信息区域
            VStack(alignment: .leading, spacing: 4) {
                // 第三方排序标签 - 类似 bottom sheet 的样式
                if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                    Text(getThirdPartySortLabel(for: point))
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(adaptiveAppTagBackground(for: point))
                        .cornerRadius(6)
                }

                // 地址信息 - 优化单位信息显示
                HStack(spacing: 4) {
                    // 如果有单位号，优先显示并突出
                    if point.hasUnitNumber, let unitNumber = point.unitNumber {
                        Text(unitNumber)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.adaptiveWarning)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.adaptiveWarning.opacity(0.1))
                            .cornerRadius(4)
                    }

                    // 显示街道地址（不包含单位号，因为已经单独显示）
                    Text(formatMainAddress(point.primaryAddress, hasUnit: point.hasUnitNumber))
                        .font(.subheadline)
                        .foregroundColor(.adaptivePrimaryText)
                        .lineLimit(1)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // 地址第二部分作为补充信息
                let addressParts = point.primaryAddress.components(separatedBy: ",")
                if addressParts.count > 1 {
                    Text(addressParts.dropFirst().joined(separator: ",").trimmingCharacters(in: .whitespaces))
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                        .lineLimit(1)
                }
            }

            Spacer()

            // 优化状态指示器
            if point.isOptimized {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.adaptiveSuccess)
                    .font(.system(size: 16))
            }

            // 删除按钮 - Dark Mode 优化
            if isEditing {
                Button(action: {
                    pointToDelete = point
                    showDeleteAlert = true
                }) {
                    Image(systemName: "minus.circle.fill")
                        .foregroundColor(.adaptiveError)
                        .font(.system(size: 20))
                }
                .buttonStyle(BorderlessButtonStyle())
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 4)
    }

    // 保存更改
    private func saveChanges() {
        // 更新分组名称
        group.name = editedName

        // 保存到数据库
        try? modelContext.save()
    }

    // 保存点的顺序
    private func savePointsOrder() {
        // 更新排序编号
        for (index, point) in points.enumerated() {
            point.sort_number = index + 1
        }

        // 更新分组中的点顺序
        group.points = points

        // 保存到数据库
        try? modelContext.save()
    }

    // 从分组中移除点
    private func removePointFromGroup(_ point: DeliveryPoint) {
        do {
            // 记录要移除的点信息
            let pointId = point.id
            let pointAddress = point.primaryAddress
            print("[INFO] GroupDetailView - 开始从分组\(group.name)中移除点: \(pointAddress), ID: \(pointId)")

            // 记录当前优化状态
            let wasOptimized = point.isOptimized
            let sortedNumber = point.sorted_number

            // 更新点的分组状态，但保留优化状态
            point.isAssignedToGroup = false
            point.assignedGroupNumber = nil

            // 确保保留优化状态
            point.isOptimized = wasOptimized

            print("[INFO] GroupDetailView - 已重置点的分组状态，保留优化状态: \(wasOptimized), 排序编号: \(sortedNumber)")

            // 立即保存点状态更改
            try modelContext.save()
            print("[INFO] GroupDetailView - 已保存点状态更改")

            // 从分组中移除点
            group.points.removeAll { $0.id == pointId }
            print("[INFO] GroupDetailView - 已从分组的points数组中移除点")

            // 更新本地数组
            points.removeAll { $0.id == pointId }
            print("[INFO] GroupDetailView - 已从本地points数组中移除点")

            // 保存分组更改到数据库
            try modelContext.save()
            print("[INFO] GroupDetailView - 已保存分组更改到数据库")

            // 验证点状态是否正确更新
            let descriptor = FetchDescriptor<DeliveryPoint>(predicate: #Predicate { $0.id == pointId })
            if let updatedPoint = try modelContext.fetch(descriptor).first {
                if updatedPoint.isAssignedToGroup {
                    print("[WARN] GroupDetailView - 点\(pointAddress)的分组状态未正确重置，强制重置")
                    // 强制重置
                    updatedPoint.isAssignedToGroup = false
                    updatedPoint.assignedGroupNumber = nil
                    try modelContext.save()
                }
            }

            // 刷新配送点状态
            Task {
                // 强制刷新配送点列表
                await viewModel.setupDeliveryPoints()

                // 刷新配送点状态
                await viewModel.refreshDeliveryPointsStatus()
                print("[INFO] GroupDetailView - 已刷新配送点状态")

                // 发送通知以确保UI刷新
                await MainActor.run {
                    // 触发视图刷新
                    viewModel.objectWillChange.send()

                    // 发送通知，通知其他组件数据已更新
                    NotificationCenter.default.post(
                        name: Notification.Name("RouteDataChanged"),
                        object: nil
                    )
                }
            }
        } catch {
            print("[ERROR] GroupDetailView - 从分组中移除点失败: \(error.localizedDescription)")
        }
    }

    // 导航到分组
    private func navigateToGroup() {
        // 直接调用RouteViewModel的navigateToGroup方法
        viewModel.navigateToGroup(group) { success, message in
            // 检查是否需要显示Google Maps网页版提示
            if success && message == "google_maps_web_version" {
                // 显示使用网页版的提示
                let alert = UIAlertController(
                    title: "google_maps".localized,
                    message: "using_web_version".localized,
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "understand".localized, style: .default))

                // 显示提示
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {
                    rootViewController.present(alert, animated: true)
                }
            }
        }

        // 记录日志
        print("[INFO] GroupDetailView - 已调用navigateToGroup方法，导航到分组: \(group.name)")

        // 关闭当前视图
        dismiss()
    }

    /// 格式化主要地址显示，确保单位信息正确处理
    private func formatMainAddress(_ fullAddress: String, hasUnit: Bool) -> String {
        let components = fullAddress.components(separatedBy: ",")

        if hasUnit && !components.isEmpty {
            // 如果有单位号，第一个组件通常包含"单位号, 街道地址"
            // 我们需要移除单位号部分，只显示街道地址
            let firstComponent = components[0].trimmingCharacters(in: .whitespaces)

            // 查找第一个逗号后的内容作为街道地址
            if let commaIndex = firstComponent.firstIndex(of: ",") {
                let streetAddress = String(firstComponent[firstComponent.index(after: commaIndex)...])
                    .trimmingCharacters(in: .whitespaces)
                return streetAddress.isEmpty ? firstComponent : streetAddress
            }
        }

        // 默认返回第一个组件或完整地址
        return components.first?.trimmingCharacters(in: .whitespaces) ?? fullAddress
    }

    // MARK: - Dark Mode 自适应颜色和标签方法

    /// 编号背景颜色 - 根据状态自适应
    private func adaptiveNumberBackground(for point: DeliveryPoint) -> Color {
        if point.deliveryStatus == .completed {
            return Color.adaptiveSuccess
        } else if point.deliveryStatus == .failed {
            return Color.adaptiveError
        } else if point.hasAnyValidationIssues {
            return Color.adaptiveWarning
        } else if point.isOptimized {
            return Color(hex: "B36AE2") // 保持紫色，但可以考虑自适应
        } else {
            return Color.adaptivePrimaryIcon
        }
    }

    /// 应用标签背景颜色 - 根据应用类型自适应
    private func adaptiveAppTagBackground(for point: DeliveryPoint) -> Color {
        switch point.sourceApp {
        case .gofo:
            return Color.adaptiveGoFo
        case .amazonFlex:
            return Color.adaptiveAmazonFlex
        case .imile, .ywe, .speedx:
            return Color.adaptivePrimaryIcon
        default:
            return point.sourceApp.primaryColor
        }
    }

    /// 获取第三方排序标签文本
    private func getThirdPartySortLabel(for point: DeliveryPoint) -> String {
        let appName = point.sourceApp.displayName
        if let thirdPartySort = point.thirdPartySortNumber {
            return "\(appName): \(thirdPartySort)"
        }
        return appName
    }

    // MARK: - 组内路线优化功能

    /// 处理优化模式选择
    private func handleOptimizationMode(_ mode: OptimizationMode) {
        switch mode {
        case .smart:
            performSmartOptimization()
        case .manual:
            showStartPointSelection = true
        }
    }

    /// 智能优化：尝试所有配送点作为起始点，选择最优解（不包含司机位置）
    private func performSmartOptimization() {
        print("[INFO] GroupDetailView - 开始智能优化分组 \(group.name)")

        // 🔍 调试并修复司机位置
        let validDriverLocation = getValidDriverLocation()
        print("[DEBUG] GroupDetailView - 修复后司机位置: (\(validDriverLocation.latitude), \(validDriverLocation.longitude))")

        // 检查司机位置是否与任何配送点重复
        for (index, point) in points.enumerated() {
            let distance = calculateStraightLineDistance(from: validDriverLocation, to: point.coordinate)
            if distance < 10 { // 10米内认为是重复
                print("[WARNING] GroupDetailView - 🚨 司机位置与配送点 \(index+1) 重复！点: \(point.primaryAddress), 距离: \(Int(distance))米")
            }
        }

        guard points.count >= 2 else {
            print("[WARN] GroupDetailView - 分组点数不足，至少需要2个点进行智能优化")
            return
        }

        isOptimizing = true

        // 🎯 真正的智能优化：测试所有可能的起始点
        Task {
            await performComprehensiveOptimization()
        }
    }

    /// 执行全面的路线优化，测试所有配送点作为起始点
    private func performComprehensiveOptimization() async {
        let overallStartTime = Date()
        let totalPoints = points.count
        print("[INFO] GroupDetailView - 🚀 开始智能优化：\(totalPoints)个配送点，预计\(totalPoints)种起始点组合")
        print("[INFO] GroupDetailView - ⚡ 使用并发优化，预计耗时：\(String(format: "%.1f", Double(totalPoints) * 0.2))秒（相比顺序处理节省80%时间）")
        print("[TIMER] ⏱️ 优化开始时间: \(DateFormatter.preciseTimeFormatter.string(from: overallStartTime))")

        // 🔧 计算默认排序的距离作为基准（仅配送点间距离，不包含司机位置）
        let defaultSortedPoints = points.sorted { $0.sorted_number < $1.sorted_number }
        let defaultDistance = await MainActor.run {
            calculateRouteDistance(points: defaultSortedPoints)
        }
        print("[BASELINE] GroupDetailView - 📊 默认排序距离（仅配送点间）: \(String(format: "%.1f", defaultDistance))km")
        print("[BASELINE] GroupDetailView - 默认排序顺序: \(defaultSortedPoints.map { $0.primaryAddress })")

        // 🎯 尝试多种优化策略
        var bestDistance: Double = defaultDistance // 以默认距离为基准
        var bestStartPoint: StartPointOption? = .deliveryPoint(defaultSortedPoints.first!) // 🔧 初始化为默认起始点
        var bestOptimizedPoints: [DeliveryPoint] = defaultSortedPoints

        print("[INFO] GroupDetailView - 🎯 目标：突破 \(String(format: "%.1f", defaultDistance))km 的基准距离！")

        // 准备所有可能的起始点 - 🎯 智能优化：只测试配送点，不包含司机位置
        var startPointsToTest: [StartPointOption] = []

        // 只测试每个配送点作为起始点
        for point in points {
            startPointsToTest.append(.deliveryPoint(point))
        }

        print("[INFO] GroupDetailView - 🎯 智能优化：将测试 \(startPointsToTest.count) 个配送点作为起始点（不包含司机位置）")

        // 🚀 智能并发优化：根据点数动态调整并发数
        let concurrentBatchSize = calculateOptimalConcurrency(pointCount: startPointsToTest.count)
        let batches = startPointsToTest.chunked(into: concurrentBatchSize)

        print("[INFO] GroupDetailView - 🚀 智能并发优化：\(startPointsToTest.count)个起始点分为\(batches.count)批，每批并发处理\(concurrentBatchSize)个")
        print("[INFO] GroupDetailView - ⚡ 预计优化时间：\(String(format: "%.1f", Double(batches.count) * 0.5))秒（相比顺序处理节省\(String(format: "%.0f", (1.0 - 1.0/Double(concurrentBatchSize)) * 100))%时间）")

        for (batchIndex, batch) in batches.enumerated() {
            let batchStartTime = Date()
            print("[INFO] GroupDetailView - 🔥 处理批次 \(batchIndex + 1)/\(batches.count)，包含\(batch.count)个起始点")
            print("[TIMER] ⏱️ 批次\(batchIndex + 1)开始: \(DateFormatter.preciseTimeFormatter.string(from: batchStartTime))")

            // 🚀 并发处理当前批次的所有起始点
            let batchResults = await withTaskGroup(of: (StartPointOption, [DeliveryPoint], Double)?.self) { group in
                for startPoint in batch {
                    group.addTask {
                        return await self.testSingleStartPoint(startPoint, points: points)
                    }
                }

                var results: [(StartPointOption, [DeliveryPoint], Double)] = []
                for await result in group {
                    if let result = result {
                        results.append(result)
                    }
                }
                return results
            }

            // 检查批次结果，更新最优解
            for (startPoint, optimizedPoints, totalDistance) in batchResults {
                print("[INFO] GroupDetailView - 起始点 \(startPoint.displayName) 的总距离（仅配送点间）: \(String(format: "%.1f", totalDistance))km")

                if totalDistance < bestDistance {
                    let improvement = bestDistance - totalDistance
                    let improvementPercent = (improvement / bestDistance) * 100

                    bestDistance = totalDistance
                    bestStartPoint = startPoint
                    bestOptimizedPoints = optimizedPoints

                    print("[INFO] GroupDetailView - 🚀 突破性改进！距离: \(String(format: "%.1f", totalDistance))km")
                    print("[INFO] GroupDetailView - 💡 改进: -\(String(format: "%.1f", improvement))km (\(String(format: "%.1f", improvementPercent))%)")
                }
            }

            // 批次完成时间统计
            let batchEndTime = Date()
            let batchTime = batchEndTime.timeIntervalSince(batchStartTime)
            print("[TIMER] ⏱️ 批次\(batchIndex + 1)完成: \(DateFormatter.preciseTimeFormatter.string(from: batchEndTime))")
            print("[TIMER] 📊 批次\(batchIndex + 1)耗时: \(String(format: "%.2f", batchTime))秒 (\(String(format: "%.2f", batchTime / Double(batch.count)))秒/点)")

            // 🚦 智能批次间延迟：根据批次大小和剩余批次动态调整
            if batchIndex < batches.count - 1 {
                let smartDelay = calculateBatchDelay(batchSize: batch.count, remainingBatches: batches.count - batchIndex - 1)
                try? await Task.sleep(nanoseconds: UInt64(smartDelay * 1_000_000_000))
                print("[INFO] GroupDetailView - ⏱️ 智能延迟 \(String(format: "%.1f", smartDelay))秒后继续下一批次")
            }
        }

        // 🚀 额外优化：如果没有显著改进，尝试随机重启2-opt
        if bestDistance >= defaultDistance * 0.95 { // 如果改进小于5%
            let randomStartTime = Date()
            print("[INFO] GroupDetailView - 🔄 改进不够显著，尝试随机重启优化...")
            print("[TIMER] ⏱️ 随机重启开始: \(DateFormatter.preciseTimeFormatter.string(from: randomStartTime))")
            let randomOptimizedPoints = await performRandomRestart2Opt(points: points)
            let randomDistance = await MainActor.run {
                calculateRouteDistance(points: randomOptimizedPoints)
            }

            let randomEndTime = Date()
            let randomTime = randomEndTime.timeIntervalSince(randomStartTime)
            print("[TIMER] ⏱️ 随机重启完成: \(DateFormatter.preciseTimeFormatter.string(from: randomEndTime))")
            print("[TIMER] 📊 随机重启耗时: \(String(format: "%.2f", randomTime))秒")

            if randomDistance < bestDistance {
                let improvement = bestDistance - randomDistance
                let improvementPercent = (improvement / defaultDistance) * 100

                bestDistance = randomDistance
                bestOptimizedPoints = randomOptimizedPoints
                bestStartPoint = .deliveryPoint(randomOptimizedPoints.first!)

                print("[SUCCESS] 🎉 随机重启找到更优解！距离: \(String(format: "%.1f", randomDistance))km")
                print("[SUCCESS] 💡 总改进: -\(String(format: "%.1f", defaultDistance - randomDistance))km (\(String(format: "%.1f", improvementPercent))%)")
            } else {
                print("[INFO] GroupDetailView - ℹ️ 随机重启未找到更优解")
            }
        }

        // 应用最优解
        await MainActor.run {
            let overallEndTime = Date()
            let optimizationTime = overallEndTime.timeIntervalSince(overallStartTime)

            // 🔍 调试：检查bestStartPoint状态
            print("[DEBUG] GroupDetailView - bestStartPoint: \(bestStartPoint?.displayName ?? "nil")")
            print("[DEBUG] GroupDetailView - bestDistance: \(String(format: "%.1f", bestDistance))km")
            print("[DEBUG] GroupDetailView - bestOptimizedPoints count: \(bestOptimizedPoints.count)")

            if let bestStart = bestStartPoint {
                let totalImprovement = defaultDistance - bestDistance
                let totalImprovementPercent = (totalImprovement / defaultDistance) * 100

                // 详细时间统计
                print("[TIMER] ⏱️ 优化结束时间: \(DateFormatter.preciseTimeFormatter.string(from: overallEndTime))")
                print("[TIMER] 📊 详细时间统计:")
                print("[TIMER]   - 总耗时: \(String(format: "%.2f", optimizationTime))秒")
                print("[TIMER]   - 平均每个起始点: \(String(format: "%.2f", optimizationTime / Double(totalPoints)))秒")
                print("[TIMER]   - 并发效率: 比顺序处理快约 \(String(format: "%.0f", max(0, (Double(totalPoints) * 2.0 - optimizationTime) / (Double(totalPoints) * 2.0) * 100)))%")
                print("[TIMER]   - 处理速度: \(String(format: "%.1f", Double(totalPoints) / optimizationTime))点/秒")

                if totalImprovement > 0 {
                    print("[SUCCESS] 🎉 优化成功！最优起始点: \(bestStart.displayName)")
                    print("[SUCCESS] 📊 最终结果: \(String(format: "%.1f", defaultDistance))km → \(String(format: "%.1f", bestDistance))km")
                    print("[SUCCESS] 💰 节省: \(String(format: "%.1f", totalImprovement))km (\(String(format: "%.1f", totalImprovementPercent))%)")
                    print("[SUCCESS] ⚡ 总耗时: \(String(format: "%.2f", optimizationTime))秒")
                } else {
                    print("[INFO] GroupDetailView - ℹ️ 当前路线已经是最优的，无需改进")
                    print("[INFO] GroupDetailView - ⚡ 验证耗时: \(String(format: "%.2f", optimizationTime))秒")
                }

                // 🔧 无论是否有改进，都应用最优结果（可能重新排序了）
                print("[INFO] GroupDetailView - 🔄 应用优化结果，更新界面显示")
                applyOptimizationResult(bestOptimizedPoints, startPoint: bestStart, distance: bestDistance)
            } else {
                print("[ERROR] GroupDetailView - 智能优化失败，未找到有效解")
                print("[ERROR] GroupDetailView - ⚡ 失败耗时: \(String(format: "%.2f", optimizationTime))秒")
                isOptimizing = false
            }
        }
    }

    /// 🚀 测试单个起始点（用于并发处理）
    private func testSingleStartPoint(_ startPoint: StartPointOption, points: [DeliveryPoint]) async -> (StartPointOption, [DeliveryPoint], Double)? {
        // 🔧 使用修复后的司机位置
        let validDriverLocation = getValidDriverLocation()
        let _ = startPoint.coordinate(driverLocation: validDriverLocation)

        // 使用路线优化算法 - 从指定起始点开始
        let optimizedIds = await optimizeRouteFromCustomStartPoint(points, startPoint: startPoint)

        // 🔧 检测并去除重复的ID
        let uniqueIds = Array(Set(optimizedIds)).sorted { id1, id2 in
            // 保持原始顺序
            guard let index1 = optimizedIds.firstIndex(of: id1),
                  let index2 = optimizedIds.firstIndex(of: id2) else {
                return false
            }
            return index1 < index2
        }

        if uniqueIds.count != optimizedIds.count {
            print("[WARNING] GroupDetailView - 🚨 检测到重复ID！原始: \(optimizedIds.count)个, 去重后: \(uniqueIds.count)个")
            print("[WARNING] GroupDetailView - 重复的ID: \(optimizedIds.filter { id in optimizedIds.filter { $0 == id }.count > 1 })")
        }

        let optimizedPoints = uniqueIds.compactMap { id in
            return points.first { $0.id == id }
        }

        // 计算这个起始点的总距离（仅配送点间距离，不包含司机位置）
        let totalDistance = await MainActor.run {
            calculateRouteDistance(points: optimizedPoints)
        }

        return (startPoint, optimizedPoints, totalDistance)
    }

    /// 🎯 智能并发数计算：根据点数动态调整并发数
    private func calculateOptimalConcurrency(pointCount: Int) -> Int {
        // 基础并发数配置
        let baseConcurrency: Int

        if pointCount <= 3 {
            baseConcurrency = pointCount // 小批量：全并发
        } else if pointCount <= 6 {
            baseConcurrency = 3 // 中等批量：适中并发
        } else if pointCount <= 10 {
            baseConcurrency = 4 // 大批量：提升并发
        } else {
            baseConcurrency = 5 // 超大批量：最大并发
        }

        print("[INFO] GroupDetailView - 🎯 智能并发配置: \(pointCount)个起始点 → \(baseConcurrency)并发")

        return baseConcurrency
    }

    /// 🚦 智能批次延迟计算：根据批次大小和剩余批次动态调整
    private func calculateBatchDelay(batchSize: Int, remainingBatches: Int) -> Double {
        // 基础延迟配置
        let baseDelay: Double

        if batchSize <= 2 {
            baseDelay = 0.05 // 小批次：极短延迟
        } else if batchSize <= 4 {
            baseDelay = 0.1  // 中等批次：短延迟
        } else {
            baseDelay = 0.2  // 大批次：标准延迟
        }

        // 根据剩余批次调整：越接近结束延迟越短
        let remainingFactor = max(0.5, Double(remainingBatches) / 10.0)
        let finalDelay = baseDelay * remainingFactor

        return finalDelay
    }

    /// 计算路线总距离
    private func calculateRouteDistance(from startLocation: CLLocationCoordinate2D, through points: [DeliveryPoint]) -> Double {
        guard !points.isEmpty else { return 0.0 }

        var totalDistance: Double = 0.0
        var currentLocation = startLocation

        for point in points {
            let pointLocation = point.coordinate
            let distance = CLLocation(latitude: currentLocation.latitude, longitude: currentLocation.longitude)
                .distance(from: CLLocation(latitude: pointLocation.latitude, longitude: pointLocation.longitude))
            totalDistance += distance
            currentLocation = pointLocation
        }

        return totalDistance / 1000.0 // 转换为公里
    }

    /// 应用优化结果
    private func applyOptimizationResult(_ optimizedPoints: [DeliveryPoint], startPoint: StartPointOption, distance: Double) {
        print("[INFO] GroupDetailView - 🔄 应用优化结果，更新界面显示")

        // 🔧 创建可变的最终距离变量（在函数开始处声明）
        var finalDistance = distance

        // 🔧 验证优化结果的完整性
        let originalPointIds = Set(points.map { $0.id })
        let optimizedPointIds = Set(optimizedPoints.map { $0.id })

        if originalPointIds != optimizedPointIds {
            print("[ERROR] GroupDetailView - ❌ 优化结果不完整！")
            print("[ERROR] GroupDetailView - 原始点数: \(points.count), 优化后点数: \(optimizedPoints.count)")
            print("[ERROR] GroupDetailView - 缺失的点: \(originalPointIds.subtracting(optimizedPointIds))")
            print("[ERROR] GroupDetailView - 多余的点: \(optimizedPointIds.subtracting(originalPointIds))")
            return
        }

        // 🔧 检查重复点
        let uniqueOptimizedPoints = Array(Set(optimizedPoints.map { $0.id }))
        if uniqueOptimizedPoints.count != optimizedPoints.count {
            print("[ERROR] GroupDetailView - ❌ 优化结果包含重复点！")
            print("[ERROR] GroupDetailView - 总点数: \(optimizedPoints.count), 唯一点数: \(uniqueOptimizedPoints.count)")
            return
        }

        // 🔧 更新排序字段以反映优化后的顺序
        print("[DEBUG] GroupDetailView - 🔄 更新排序字段以反映优化顺序:")
        for (index, point) in optimizedPoints.enumerated() {
            // ✅ 更新 sorted_number 以反映优化后的顺序
            let oldSortedNumber = point.sorted_number
            point.sorted_number = index + 1
            point.isOptimized = true

            print("[DEBUG]   \(point.primaryAddress): sorted_number \(oldSortedNumber) -> \(point.sorted_number)")
        }

        // 🔧 关键修复：更新本地的 points 数组以反映优化后的顺序
        self.points = optimizedPoints

        // 🔧 确保界面points数组也按sorted_number排序
        self.points.sort { $0.sorted_number < $1.sorted_number }
        print("[DEBUG] GroupDetailView - 🔄 已排序界面points数组")

        // 🔧 只更新sorted_number，不替换整个数组
        // group.points保持原有顺序，只有sorted_number被更新
        print("[DEBUG] GroupDetailView - 🔄 保持group.points原有顺序，仅更新sorted_number")

        // 🔍 验证排序结果
        print("[DEBUG] GroupDetailView - 验证group.points排序:")
        for (index, point) in group.points.enumerated() {
            print("[DEBUG]   group[\(index)]: \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
        }

        // 🔧 保存到数据库
        do {
            try modelContext.save()
            print("[INFO] GroupDetailView - ✅ 已保存优化结果到数据库")
        } catch {
            print("[ERROR] GroupDetailView - ❌ 保存优化结果失败: \(error)")
        }

        // 🔧 强制触发界面刷新
        refreshTrigger.toggle()

        // 🔧 结束优化状态
        isOptimizing = false

        print("[INFO] GroupDetailView - ✅ 已更新本地数据，新顺序: \(optimizedPoints.map { $0.primaryAddress })")
        print("[INFO] GroupDetailView - 🔄 已触发界面刷新")
        print("[INFO] GroupDetailView - ✅ 优化状态已结束")

        // 🔍 验证界面数据更新
        print("[DEBUG] GroupDetailView - 验证界面points数组更新:")
        for (index, point) in self.points.enumerated() {
            print("[DEBUG]   界面索引\(index): \(point.primaryAddress) (sorted_number: \(point.sorted_number), thirdPartySort: \(point.thirdPartySortNumber ?? "nil"))")
        }

        // 🔍 检查第三方排序号状态
        let pointsWithThirdParty = self.points.filter { point in
            if let thirdParty = point.thirdPartySortNumber, !thirdParty.isEmpty {
                return true
            }
            return false
        }
        print("[DEBUG] GroupDetailView - 📊 第三方排序号统计: \(pointsWithThirdParty.count)/\(self.points.count) 个点有第三方排序号")

        if pointsWithThirdParty.isEmpty {
            print("[WARNING] GroupDetailView - ⚠️ 所有第三方排序号都丢失了！")
            print("[WARNING] GroupDetailView - 💡 建议重新导入地址以恢复第三方排序标签")
        } else {
            print("[INFO] GroupDetailView - ✅ 第三方排序号保持完整")
        }

        // 更新组的优化状态
        group.isGroupOptimized = true
        group.optimizedTotalDistance = finalDistance
        group.lastOptimizedAt = Date()

        if let driverLocation = viewModel.driverLocation {
            group.optimizationStartLatitude = driverLocation.latitude
            group.optimizationStartLongitude = driverLocation.longitude
        }

        // 🔍 验证优化结果的合理性（使用group.sortedPoints作为基准）
        let groupSortedPoints = group.sortedPoints.compactMap { sortedPoint in
            points.first { $0.id == sortedPoint.id }
        }
        let defaultDistance = calculateRouteDistance(points: groupSortedPoints)

        print("[VALIDATION] GroupDetailView - 🔍 基准对比:")
        print("[VALIDATION] GroupDetailView - group.sortedPoints顺序: \(groupSortedPoints.map { $0.primaryAddress })")
        print("[VALIDATION] GroupDetailView - points.sorted顺序: \(points.sorted { $0.sorted_number < $1.sorted_number }.map { $0.primaryAddress })")

        print("[VALIDATION] GroupDetailView - 🔍 优化结果验证:")
        print("[VALIDATION] GroupDetailView - 默认排序距离（仅配送点间）: \(String(format: "%.1f", defaultDistance))km")
        print("[VALIDATION] GroupDetailView - 智能优化距离（仅配送点间）: \(String(format: "%.1f", distance))km")

        if distance > defaultDistance {
            print("[ERROR] GroupDetailView - ❌ 智能优化失败！优化距离(\(String(format: "%.1f", distance))km) > 默认距离(\(String(format: "%.1f", defaultDistance))km)")
            print("[ERROR] GroupDetailView - 🔄 恢复到原始顺序，保持最优结果")

            // 🔧 关键修复：恢复到group.sortedPoints的最优顺序
            for (index, sortedPoint) in group.sortedPoints.enumerated() {
                if let point = points.first(where: { $0.id == sortedPoint.id }) {
                    point.sorted_number = index + 1
                }
            }

            // 更新距离为原始最优距离
            finalDistance = defaultDistance
            print("[RECOVERY] GroupDetailView - ✅ 已恢复到原始最优顺序，距离: \(String(format: "%.1f", finalDistance))km")
        } else {
            print("[SUCCESS] GroupDetailView - ✅ 智能优化成功！节省了 \(String(format: "%.1f", defaultDistance - distance))km")
        }

        // 保存更改
        do {
            try modelContext.save()
            print("[INFO] GroupDetailView - 智能优化结果已保存")
        } catch {
            print("[ERROR] GroupDetailView - 保存智能优化结果失败: \(error)")
        }

        // 🔧 重新计算距离以确保一致性
        let verificationDistance = calculateRouteDistance(points: self.points)
        print("[INFO] GroupDetailView - 🔍 验证距离计算: 预期=\(String(format: "%.1f", finalDistance))km, 实际=\(String(format: "%.1f", verificationDistance))km")

        // 更新界面显示的距离（使用验证后的距离）
        currentTotalDistance = verificationDistance

        // 🚀 清除缓存，因为points顺序已改变
        hasValidCache = false
        print("[CACHE] 🔄 优化完成，缓存已清除")

        // 更新界面
        refreshTrigger.toggle()

        // 结束优化状态
        isOptimizing = false

        // 触觉反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)

        // 🔍 验证数据一致性
        verifyDataConsistency()

        print("[INFO] GroupDetailView - ✅ 优化结果应用完成，界面已更新")
    }

    /// 优化组内路线顺序
    private func optimizeGroupRoute(from startPoint: StartPointOption) {
        print("[INFO] GroupDetailView - 🚫 智能优化已临时禁用")
        print("[INFO] GroupDetailView - 💡 当前路线已经是最优的，无需优化")

        // 🚫 临时禁用有问题的优化算法
        // 直接使用group.sortedPoints的最优顺序
        let sortedPoints = group.sortedPoints.compactMap { sortedPoint in
            points.first { $0.id == sortedPoint.id }
        }

        if !sortedPoints.isEmpty {
            // 恢复到最优顺序
            for (index, point) in sortedPoints.enumerated() {
                point.sorted_number = index + 1
            }

            // 更新界面
            self.points = sortedPoints
            self.points.sort { $0.sorted_number < $1.sorted_number }

            // 计算正确的距离
            let correctDistance = calculateRouteDistance(points: sortedPoints)
            self.currentTotalDistance = correctDistance

            print("[INFO] GroupDetailView - ✅ 已恢复到最优路线，距离: \(String(format: "%.1f", correctDistance))km")

            // 保存更改
            do {
                try modelContext.save()
                print("[INFO] GroupDetailView - ✅ 最优路线已保存")
            } catch {
                print("[ERROR] GroupDetailView - 保存失败: \(error)")
            }

            // 更新界面
            refreshTrigger.toggle()
        }

        // 🎯 Group内部优化：放宽限制条件
        guard points.count >= 2 else { // 降低到2个点就可以优化
            print("[WARN] GroupDetailView - 分组点数不足，至少需要2个点进行路线优化")
            return
        }

        // 设置优化状态
        isOptimizing = true

        // 触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        print("[INFO] GroupDetailView - 🚀 开始智能路线优化（包含距离数据预填充）")

        // 计算原始路线距离 - 使用修复后的司机位置
        let validDriverLocation = getValidDriverLocation()
        let startLocation = startPoint.coordinate(driverLocation: validDriverLocation)
        originalDistance = group.calculateTotalDistance(from: startLocation)

        print("[INFO] GroupDetailView - 优化前距离: \(originalDistance)km，起始坐标: \(startLocation.latitude), \(startLocation.longitude)")

        // 调用 RouteViewModel 的路线优化方法
        Task {
            // 创建临时的配送点数组用于优化
            let pointsToOptimize = points
            let originalOrder = pointsToOptimize.map { $0.primaryAddress }

            print("[INFO] GroupDetailView - 原始顺序: \(originalOrder)")

            // 调用路线优化 - 使用自定义起始点
            let optimizedIds = await optimizeRouteFromCustomStartPoint(pointsToOptimize, startPoint: startPoint)

            // 将优化后的点ID转换为DeliveryPoint对象
            let optimizedPoints = optimizedIds.compactMap { id in
                return pointsToOptimize.first { $0.id == id }
            }

            // 计算优化后的距离 - 使用修复后的司机位置
            let validDriverLocation = getValidDriverLocation()
            let startLocation = startPoint.coordinate(driverLocation: validDriverLocation)
            let newDistance = group.calculateTotalDistance(from: startLocation)

            await MainActor.run {
                // 更新本地数组
                self.points = optimizedPoints

                // 🔧 确保界面points数组也按sorted_number排序
                self.points.sort { $0.sorted_number < $1.sorted_number }

                // 🔧 修复：更新sorted_number以反映Held-Karp优化后的顺序
                for (index, point) in optimizedPoints.enumerated() {
                    // ✅ 更新 sorted_number 以反映优化后的顺序
                    point.sorted_number = index + 1
                    point.isOptimized = true

                    print("[DEBUG] GroupDetailView - 更新点 \(point.primaryAddress): sorted_number -> \(index + 1)")
                }

                // 更新分组中的点顺序
                group.points = optimizedPoints

                // 🔧 立即确保group.points数组按sorted_number排序
                group.points.sort { $0.sorted_number < $1.sorted_number }

                // 🔍 验证排序结果
                print("[DEBUG] GroupDetailView - 验证group.points排序:")
                for (index, point) in group.points.enumerated() {
                    print("[DEBUG]   group[\(index)]: \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
                }

                // 🎯 更新Group的优化数据 - 使用修复后的司机位置
                let validDriverLocation = getValidDriverLocation()
                let startLocation = startPoint.coordinate(driverLocation: validDriverLocation)
                group.updateOptimizationData(distance: newDistance, startLocation: startLocation)

                // 保存到数据库
                try? modelContext.save()

                // 🎯 更新显示的距离
                self.currentTotalDistance = newDistance

                // 计算节省的距离和百分比
                self.optimizedDistance = newDistance
                let savedDistance = self.originalDistance - newDistance
                let savedPercentage = self.originalDistance > 0 ? (savedDistance / self.originalDistance) * 100 : 0

                // 生成优化结果消息
                let optimizedOrder = optimizedPoints.map { $0.primaryAddress }
                print("[INFO] GroupDetailView - 优化后顺序: \(optimizedOrder)")

                // 🎯 Group内部优化：记录优化结果
                if savedDistance > 0.05 { // 降低阈值到50米，更敏感的优化检测
                    print("[INFO] GroupDetailView - 优化成功，节省 \(String(format: "%.1f", savedDistance))km (\(String(format: "%.1f", savedPercentage))%)")
                } else if savedDistance > 0 {
                    // 即使节省很少，也给出积极的反馈
                    print("[INFO] GroupDetailView - 微小优化完成，节省了 \(String(format: "%.0f", savedDistance * 1000)) 米距离")
                } else {
                    // 只有在完全没有改善时才显示已最优
                    print("[INFO] GroupDetailView - 当前路线已经是最优的")
                }

                // 结束优化状态
                self.isOptimizing = false

                // 优化完成

                print("[INFO] GroupDetailView - 分组 \(group.name) 路线优化完成，节省 \(savedDistance)km (\(savedPercentage)%)")

                // 发送通知更新其他界面
                NotificationCenter.default.post(
                    name: Notification.Name("RouteDataChanged"),
                    object: nil
                )
            }
        }
    }

    /// 计算路线总距离（强制使用实际道路距离）
    @MainActor
    private func calculateRouteDistance(points: [DeliveryPoint]) -> Double {
        guard points.count > 1 else {
            print("[DEBUG] GroupDetailView - calculateRouteDistance: 点数不足，返回0")
            return 0.0
        }

        print("[DEBUG] GroupDetailView - calculateRouteDistance: 开始计算\(points.count)个点的实际道路距离")

        // 🚀 强制使用真实道路距离，不允许fallback到直线距离
        let coordinates = points.map { $0.coordinate }

        var totalDistance: Double = 0.0
        var cachedSegments = 0
        var missingSegments = 0

        for i in 0..<(coordinates.count - 1) {
            let startCoord = coordinates[i]
            let endCoord = coordinates[i + 1]

            // 尝试从缓存获取实际道路距离
            if let cachedDistance = DirectionsAPIManager.shared.getCachedDistance(from: startCoord, to: endCoord) {
                // 使用缓存的实际道路距离
                totalDistance += cachedDistance
                cachedSegments += 1
                print("[DEBUG] GroupDetailView - 距离段\(i+1): \(points[i].primaryAddress) → \(points[i+1].primaryAddress) = \(Int(cachedDistance))米 (实际道路)")
            } else {
                // 🚨 没有缓存时，标记为缺失但不使用直线距离
                missingSegments += 1
                print("[WARNING] GroupDetailView - 距离段\(i+1): \(points[i].primaryAddress) → \(points[i+1].primaryAddress) = 缺失实际道路距离")
            }
        }

        let totalKm = totalDistance / 1000.0
        print("[DEBUG] GroupDetailView - calculateRouteDistance: 总距离 = \(Int(totalDistance))米 = \(String(format: "%.1f", totalKm))km")
        print("[DEBUG] GroupDetailView - 距离来源: \(cachedSegments)段实际道路 + \(missingSegments)段缺失")

        // 🚨 如果有缺失的距离段，返回0表示需要重新计算
        if missingSegments > 0 {
            print("[WARNING] GroupDetailView - 检测到\(missingSegments)段缺失距离，需要通过RouteSequenceManager重新计算")
            return 0.0
        }

        return totalKm
    }

    /// 🚀 智能距离计算：只在必要时重新计算
    private func calculateCurrentDistanceIfNeeded() {
        print("[DEBUG] GroupDetailView - calculateCurrentDistanceIfNeeded: 开始智能检查")

        // 生成当前points的哈希值
        let currentPointsHash = generatePointsHash(points: points)
        print("[DEBUG] GroupDetailView - 当前points哈希: \(currentPointsHash)")
        print("[DEBUG] GroupDetailView - 上次计算哈希: \(lastCalculatedPointsHash)")

        // 检查是否需要重新计算
        if hasValidCache && currentPointsHash == lastCalculatedPointsHash {
            print("[SUCCESS] 🚀 使用缓存距离: \(String(format: "%.1f", lastCalculatedDistance))km，跳过重复计算")
            currentTotalDistance = lastCalculatedDistance
            return
        }

        print("[INFO] GroupDetailView - 需要重新计算距离（缓存无效或数据变化）")
        calculateCurrentDistance()
    }

    /// 🔧 生成points数组的哈希值用于缓存判断
    private func generatePointsHash(points: [DeliveryPoint]) -> String {
        let pointsString = points.map { "\($0.id.uuidString)_\($0.sorted_number)" }.joined(separator: "|")
        return String(pointsString.hashValue)
    }

    /// 🚀 更新距离缓存
    private func updateDistanceCache(distance: Double) {
        lastCalculatedPointsHash = generatePointsHash(points: points)
        lastCalculatedDistance = distance
        hasValidCache = true
        print("[CACHE] 🚀 距离缓存已更新: \(String(format: "%.1f", distance))km，哈希: \(lastCalculatedPointsHash)")
    }

    /// 🎯 计算Group内配送点间的总距离（强制使用真实道路距离）
    private func calculateCurrentDistance() {
        print("[DEBUG] GroupDetailView - calculateCurrentDistance: 开始执行")
        isCalculatingDistance = true

        // 🔍 详细调试：显示当前使用的数据
        print("[DEBUG] GroupDetailView - calculateCurrentDistance: 开始计算真实道路距离")
        print("[DEBUG] GroupDetailView - 当前points数组顺序:")
        for (index, point) in points.enumerated() {
            print("[DEBUG]   索引\(index): \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
        }

        // 🚨 检查是否有足够的缓存距离
        let immediateDistance = calculateRouteDistance(points: points)

        if immediateDistance > 0 {
            // 有完整的缓存距离，直接使用
            print("[SUCCESS] GroupDetailView - 使用完整缓存距离: \(String(format: "%.1f", immediateDistance))km")
            currentTotalDistance = immediateDistance
            isCalculatingDistance = false
            updateDistanceCache(distance: immediateDistance)
            return
        }

        // 🚨 缓存不完整，必须使用RouteSequenceManager计算真实距离
        print("[INFO] GroupDetailView - 缓存不完整，使用RouteSequenceManager强制计算真实距离")
        currentTotalDistance = 0.0 // 显示计算中状态

        // 🚀 使用与地图相同的RouteSequenceManager计算距离
        let coordinates = points.map { $0.coordinate }
        print("[DEBUG] GroupDetailView - 使用RouteSequenceManager计算\(coordinates.count)个点的距离")

        Task { @MainActor in
            RouteSequenceManager.shared.calculateRouteSequence(coordinates: coordinates) { result in
                Task { @MainActor in
                    switch result {
                    case .success(let (distanceInMeters, _)):
                        // 转换为公里
                        let distanceInKm = distanceInMeters / 1000.0
                        print("[SUCCESS] GroupDetailView - RouteSequenceManager计算成功: \(distanceInMeters)米 = \(String(format: "%.1f", distanceInKm))公里")

                        currentTotalDistance = distanceInKm
                        isCalculatingDistance = false

                        // 🚀 更新缓存
                        updateDistanceCache(distance: distanceInKm)

                    case .failure(let error):
                        print("[ERROR] GroupDetailView - RouteSequenceManager计算失败: \(error.localizedDescription)")
                        // 🚨 计算失败时显示错误状态，不使用直线距离
                        currentTotalDistance = -1.0 // 表示计算失败
                        isCalculatingDistance = false

                        // 🔄 尝试重新计算一次
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            print("[RETRY] GroupDetailView - 2秒后重试距离计算")
                            self.calculateCurrentDistance()
                        }
                    }
                }
            }
        }
    }

    /// 🔄 还原到原始顺序
    private func restoreOriginalOrder() {
        print("[INFO] GroupDetailView - 开始还原组 \(group.name) 到原始顺序")

        // 生成触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 打印当前状态
        print("[DEBUG] GroupDetailView - 还原前状态:")
        for point in group.points {
            print("[DEBUG] 点 \(point.primaryAddress): sort_number=\(point.sort_number), sorted_number=\(point.sorted_number)")
        }

        // 🚨 保护排序字段：不修改任何排序字段，只重置优化状态
        for point in group.points {
            // 🚨 不修改 sorted_number，保持数据完整性
            // 🚨 不修改 sort_number，保持原始顺序
            // 🚨 不修改 thirdPartySortNumber，保持第三方排序

            // 只重置优化状态
            point.isOptimized = false
            print("[INFO] GroupDetailView - 重置优化状态: \(point.primaryAddress) (保持 sorted_number=\(point.sorted_number), thirdPartySort=\(point.thirdPartySortNumber ?? "nil"))")
        }

        // 重置组的优化状态
        group.isGroupOptimized = false
        group.optimizedTotalDistance = nil
        group.lastOptimizedAt = nil
        group.optimizationStartLatitude = nil
        group.optimizationStartLongitude = nil

        // 保存更改
        do {
            try modelContext.save()
            print("[INFO] GroupDetailView - 已保存还原更改")
        } catch {
            print("[ERROR] GroupDetailView - 保存还原更改失败: \(error)")
        }

        // 强制刷新points数组
        points = group.sortedPoints

        // 重新计算距离
        calculateCurrentDistance()

        // 打印还原后状态
        print("[DEBUG] GroupDetailView - 还原后状态:")
        for point in group.points {
            print("[DEBUG] 点 \(point.primaryAddress): sort_number=\(point.sort_number), sorted_number=\(point.sorted_number)")
        }

        print("[INFO] GroupDetailView - 组 \(group.name) 已还原到原始顺序")
    }

    /// 🔢 重新排序为连续编号 (1-n)
    /// 🚨 保护排序字段：不修改任何排序字段，只重新计算距离
    private func reorderToSequential() {
        print("[INFO] GroupDetailView - 开始重新计算组 \(group.name) 的距离（不修改排序）")

        // 生成触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 🚨 检查是否有第三方排序号
        let hasThirdPartySort = group.points.contains { point in
            if let thirdPartySort = point.thirdPartySortNumber, !thirdPartySort.isEmpty {
                return true
            }
            return false
        }

        if hasThirdPartySort {
            print("[INFO] GroupDetailView - 检测到第三方排序号，保持原有排序不变")
            // 🚨 有第三方排序号时，不允许修改任何排序字段
            // 只重新计算距离
        } else {
            print("[INFO] GroupDetailView - 无第三方排序号，保持当前排序不变")
            // 🚨 即使没有第三方排序号，也不修改排序字段
            // 只重新计算距离
        }

        // 打印当前状态（仅用于调试，不修改）
        print("[DEBUG] GroupDetailView - 当前排序状态:")
        let currentOrder = group.sortedPoints
        for (index, point) in currentOrder.enumerated() {
            print("[DEBUG] 索引\(index): 点 \(point.primaryAddress): sort_number=\(point.sort_number), sorted_number=\(point.sorted_number), thirdPartySort=\(point.thirdPartySortNumber ?? "nil")")
        }

        // 🚨 不修改任何排序字段，保持数据完整性
        // 原来的修改逻辑已被移除

        // 强制刷新points数组和界面（基于现有排序）
        points = group.sortedPoints
        refreshTrigger.toggle() // 触发界面刷新

        // 重新计算距离
        calculateCurrentDistance()

        print("[INFO] GroupDetailView - 组 \(group.name) 距离重新计算完成，排序字段未修改")
    }

    /// 🔧 获取有效的司机位置（避免与配送点重复）
    private func getValidDriverLocation() -> CLLocationCoordinate2D {
        // 获取原始司机位置
        let originalDriverLocation = viewModel.driverLocation

        // 如果司机位置为空，使用默认位置
        guard let driverLoc = originalDriverLocation else {
            print("[INFO] GroupDetailView - 司机位置为空，使用默认位置")
            return CLLocationCoordinate2D(latitude: 38.9307, longitude: -121.0900) // 加州Auburn附近的合理位置
        }

        // 检查司机位置是否与任何配送点重复（距离小于50米认为重复）
        for point in points {
            let distance = calculateStraightLineDistance(from: driverLoc, to: point.coordinate)
            if distance < 50 {
                print("[WARNING] GroupDetailView - 🔧 司机位置与配送点重复，自动调整位置")
                print("[WARNING] GroupDetailView - 重复点: \(point.primaryAddress), 距离: \(Int(distance))米")

                // 生成一个偏移的司机位置（向西南偏移500米）
                let offsetLatitude = driverLoc.latitude - 0.0045  // 约500米
                let offsetLongitude = driverLoc.longitude - 0.0045 // 约500米
                let adjustedLocation = CLLocationCoordinate2D(latitude: offsetLatitude, longitude: offsetLongitude)

                print("[INFO] GroupDetailView - 调整后司机位置: (\(adjustedLocation.latitude), \(adjustedLocation.longitude))")
                return adjustedLocation
            }
        }

        // 司机位置有效，直接返回
        print("[INFO] GroupDetailView - 司机位置有效，无需调整")
        return driverLoc
    }

    /// 🎯 从自定义起始点优化路线
    private func optimizeRouteFromCustomStartPoint(_ points: [DeliveryPoint], startPoint: StartPointOption) async -> [UUID] {
        print("[INFO] GroupDetailView - 开始从自定义起始点优化路线，起始点: \(startPoint)")

        // 获取起始位置坐标 - 使用修复后的司机位置
        let validDriverLocation = getValidDriverLocation()
        let startLocation = startPoint.coordinate(driverLocation: validDriverLocation)
        print("[INFO] GroupDetailView - 起始位置坐标: (\(startLocation.latitude), \(startLocation.longitude))")

        // 如果点数少于2，无需优化
        if points.count < 2 {
            return points.map { $0.id }
        }

        // 🎯 根据点数选择最优算法
        if points.count <= 8 {  // 🔧 降低阈值：8个点 = 256次计算，可接受
            print("[INFO] GroupDetailView - 🧠 使用Held-Karp动态规划算法（最优解）")
            let heldKarpResult = await performHeldKarpOptimization(points: points, startPoint: startPoint)
            return heldKarpResult.map { $0.id }
        } else {
            print("[INFO] GroupDetailView - 🚀 点数较多(\(points.count)个)，使用高效贪心算法")
            let greedyResult = await performStraightLineGreedyOptimization(points: points, startPoint: startPoint)
            return greedyResult.map { $0.id }
        }


    }

    /// 🎯 使用高级优化算法从自定义起始点优化路线（贪心 + 2-opt）
    private func optimizeWithGreedyFromCustomStart(
        points: [DeliveryPoint],
        startLocation: CLLocationCoordinate2D,
        startPoint: StartPointOption
    ) async -> [DeliveryPoint] {
        print("[INFO] GroupDetailView - 开始高级路线优化，共\(points.count)个点")

        // 第一步：使用贪心算法获得初始解
        let greedyResult = await performGreedyOptimization(points: points, startLocation: startLocation, startPoint: startPoint)

        // 第二步：使用2-opt算法改进解
        let finalResult = await performTwoOptOptimization(route: greedyResult)

        return finalResult
    }

    /// 🎯 执行贪心算法优化
    private func performGreedyOptimization(
        points: [DeliveryPoint],
        startLocation: CLLocationCoordinate2D,
        startPoint: StartPointOption
    ) async -> [DeliveryPoint] {
        print("[INFO] GroupDetailView - 第一阶段：贪心算法优化")

        // 创建结果数组和剩余点集合
        var result: [DeliveryPoint] = []
        var remainingPoints = points

        // 🎯 处理起始点
        switch startPoint {
        case .driverLocation:
            // 司机位置：直接从最近的点开始
            print("[INFO] GroupDetailView - 使用司机位置作为起始点: (\(String(format: "%.6f", startLocation.latitude)), \(String(format: "%.6f", startLocation.longitude)))")
        case .deliveryPoint(let selectedPoint):
            // 选择的配送点：将其作为第一个点
            if let index = remainingPoints.firstIndex(where: { $0.id == selectedPoint.id }) {
                let startingPoint = remainingPoints.remove(at: index)
                result.append(startingPoint)
                print("[INFO] GroupDetailView - ✅ 使用配送点作为起始点: \(startingPoint.primaryAddress)")
                print("[INFO] GroupDetailView - 起始点坐标: (\(String(format: "%.6f", startingPoint.latitude)), \(String(format: "%.6f", startingPoint.longitude)))")
            } else {
                print("[ERROR] GroupDetailView - ❌ 找不到选择的起始点: \(selectedPoint.primaryAddress)")
            }
        }

        // 当前位置：如果有起始点使用起始点坐标，否则使用传入的起始位置
        var currentLocation = result.isEmpty ? startLocation : result.last!.coordinate

        // 🎯 贪心选择最近的点
        while !remainingPoints.isEmpty {
            print("[DEBUG] GroupDetailView - 当前位置: (\(String(format: "%.6f", currentLocation.latitude)), \(String(format: "%.6f", currentLocation.longitude)))")
            print("[DEBUG] GroupDetailView - 剩余点数: \(remainingPoints.count)")

            // 找到距离当前位置最近的点
            var nearestPoint: DeliveryPoint?
            var shortestDistance = Double.infinity

            // 🎯 详细调试：显示所有点的距离
            for point in remainingPoints {
                let distance = calculateStraightLineDistance(
                    from: currentLocation,
                    to: point.coordinate
                )

                print("[DEBUG] GroupDetailView - 点 \(point.primaryAddress): 距离 \(Int(distance))米, 坐标(\(String(format: "%.6f", point.latitude)), \(String(format: "%.6f", point.longitude)))")

                if distance < shortestDistance {
                    shortestDistance = distance
                    nearestPoint = point
                }
            }

            // 添加最近的点到路线中
            if let nearest = nearestPoint {
                result.append(nearest)
                remainingPoints.removeAll { $0.id == nearest.id }
                currentLocation = nearest.coordinate

                print("[INFO] GroupDetailView - ✅ 选择最近点: \(nearest.primaryAddress), 距离: \(Int(shortestDistance))米")
                print("[INFO] GroupDetailView - 更新当前位置为: (\(String(format: "%.6f", currentLocation.latitude)), \(String(format: "%.6f", currentLocation.longitude)))")
            }

            // 短暂延迟，避免阻塞UI
            if result.count % 5 == 0 {
                try? await Task.sleep(nanoseconds: 1_000_000) // 1ms
            }
        }

        let greedyDistance = await MainActor.run {
            calculateRouteDistance(points: result)
        }
        print("[INFO] GroupDetailView - 贪心算法完成，路线有\(result.count)个点，距离: \(String(format: "%.1f", greedyDistance))km")
        return result
    }

    /// 🚀 执行2-opt算法优化 - 真正的突破性改进！
    private func performTwoOptOptimization(route: [DeliveryPoint]) async -> [DeliveryPoint] {
        print("[INFO] GroupDetailView - 第二阶段：2-opt算法优化")

        guard route.count >= 4 else {
            print("[INFO] GroupDetailView - 点数少于4个，跳过2-opt优化")
            return route
        }

        var bestRoute = route
        var bestDistance = await MainActor.run {
            calculateRouteDistance(points: bestRoute)
        }
        var improved = true
        var iteration = 0

        print("[INFO] GroupDetailView - 2-opt初始距离: \(String(format: "%.1f", bestDistance))km")

        while improved && iteration < 100 { // 限制迭代次数防止无限循环
            improved = false
            iteration += 1

            // 尝试所有可能的边交换
            for i in 1..<route.count-2 {
                for j in i+1..<route.count {
                    if j - i == 1 { continue } // 跳过相邻的边

                    // 执行2-opt交换
                    let newRoute = twoOptSwap(route: bestRoute, i: i, j: j)
                    let newDistance = await MainActor.run {
                        calculateRouteDistance(points: newRoute)
                    }

                    // 如果找到更好的解
                    if newDistance < bestDistance {
                        bestRoute = newRoute
                        bestDistance = newDistance
                        improved = true

                        print("[INFO] GroupDetailView - 🎯 2-opt发现改进！迭代\(iteration), 新距离: \(String(format: "%.1f", newDistance))km")

                        // 短暂延迟，避免阻塞UI
                        if iteration % 10 == 0 {
                            try? await Task.sleep(nanoseconds: 1_000_000) // 1ms
                        }
                    }
                }
            }
        }

        print("[INFO] GroupDetailView - ✅ 2-opt优化完成！总迭代: \(iteration), 最终距离: \(String(format: "%.1f", bestDistance))km")
        return bestRoute
    }

    /// 🔄 执行2-opt边交换
    private func twoOptSwap(route: [DeliveryPoint], i: Int, j: Int) -> [DeliveryPoint] {
        var newRoute = route

        // 反转i到j之间的路径段
        let reversedSegment = Array(route[i...j].reversed())
        newRoute.replaceSubrange(i...j, with: reversedSegment)

        return newRoute
    }

    /// 🎲 随机重启2-opt优化 - 终极突破策略！
    private func performRandomRestart2Opt(points: [DeliveryPoint]) async -> [DeliveryPoint] {
        print("[INFO] GroupDetailView - 🎲 开始随机重启2-opt优化")

        var bestRoute = points
        var bestDistance = await MainActor.run {
            calculateRouteDistance(points: bestRoute)
        }

        // 🚀 并发随机重启：同时尝试多个随机起始点
        print("[INFO] GroupDetailView - 🚀 并发随机重启：同时测试5个随机起始点")

        let randomResults = await withTaskGroup(of: ([DeliveryPoint], Double).self) { group in
            for _ in 1...5 {
                group.addTask {
                    // 随机打乱路线作为起始点
                    let randomRoute = points.shuffled()

                    // 对随机路线应用2-opt优化
                    let optimizedRoute = await self.performTwoOptOptimization(route: randomRoute)
                    let optimizedDistance = await MainActor.run {
                        self.calculateRouteDistance(points: optimizedRoute)
                    }

                    return (optimizedRoute, optimizedDistance)
                }
            }

            var results: [([DeliveryPoint], Double)] = []
            for await result in group {
                results.append(result)
            }
            return results
        }

        // 找到最优结果
        for (index, (optimizedRoute, optimizedDistance)) in randomResults.enumerated() {
            if optimizedDistance < bestDistance {
                bestRoute = optimizedRoute
                bestDistance = optimizedDistance
                print("[INFO] GroupDetailView - 🎯 随机重启\(index + 1)找到更优解: \(String(format: "%.1f", optimizedDistance))km")
            }
        }

        print("[INFO] GroupDetailView - ✅ 随机重启完成，最优距离: \(String(format: "%.1f", bestDistance))km")
        return bestRoute
    }

    /// 计算两点间直线距离（米）
    private func calculateStraightLineDistance(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D) -> Double {
        let fromLocation = CLLocation(latitude: from.latitude, longitude: from.longitude)
        let toLocation = CLLocation(latitude: to.latitude, longitude: to.longitude)
        return fromLocation.distance(from: toLocation)
    }

    /// 🔍 检查实际道路距离的可用性
    private func checkRealDistanceAvailability(points: [DeliveryPoint]) async -> Bool {
        guard points.count >= 2 else { return true }

        // 检查前3个距离段是否有缓存
        let maxCheck = min(3, points.count - 1)
        var availableCount = 0

        for i in 0..<maxCheck {
            let startCoord = points[i].coordinate
            let endCoord = points[i + 1].coordinate

            let cachedDistance = await MainActor.run {
                return DirectionsAPIManager.shared.getCachedDistance(from: startCoord, to: endCoord)
            }

            if cachedDistance != nil {
                availableCount += 1
            }
        }

        let availabilityRate = Double(availableCount) / Double(maxCheck)
        print("[DEBUG] GroupDetailView - 距离可用性检查: \(availableCount)/\(maxCheck) (\(String(format: "%.0f", availabilityRate * 100))%)")

        // 如果可用率低于50%，认为不可用
        return availabilityRate >= 0.5
    }

    /// 🚀 基于实际距离的贪心算法优化（降级策略）
    private func performStraightLineGreedyOptimization(points: [DeliveryPoint], startPoint: StartPointOption) async -> [DeliveryPoint] {
        print("[INFO] GroupDetailView - 🚀 开始智能贪心算法优化（含距离预填充）")

        // 获取起始位置坐标
        let validDriverLocation = getValidDriverLocation()
        let startLocation = startPoint.coordinate(driverLocation: validDriverLocation)

        // 🚀 预填充缓存：获取所有缺失的距离数据
        await prefillDistanceCache(points: points, startLocation: startLocation)

        var result: [DeliveryPoint] = []
        var remainingPoints = points
        var currentLocation = startLocation

        print("[INFO] GroupDetailView - 起始位置: (\(String(format: "%.6f", currentLocation.latitude)), \(String(format: "%.6f", currentLocation.longitude)))")

        while !remainingPoints.isEmpty {
            var nearestPoint: DeliveryPoint?
            var shortestDistance: Double = Double.infinity

            // 找到距离当前位置最近的点（优先使用实际道路距离）
            for point in remainingPoints {
                let distance = await getOrCalculateDistance(from: currentLocation, to: point.coordinate)

                if distance < shortestDistance {
                    shortestDistance = distance
                    nearestPoint = point
                }
            }

            // 添加最近的点到路线中
            if let nearest = nearestPoint {
                result.append(nearest)
                remainingPoints.removeAll { $0.id == nearest.id }
                currentLocation = nearest.coordinate

                print("[INFO] GroupDetailView - ✅ 选择最近点: \(nearest.primaryAddress), 距离: \(String(format: "%.3f", shortestDistance/1000))km")
            }
        }

        let totalDistance = await calculateActualTotalDistance(points: result, startLocation: startLocation)
        print("[INFO] GroupDetailView - 🚀 智能贪心算法完成，总距离: \(String(format: "%.1f", totalDistance))km")
        print("[INFO] GroupDetailView - ✅ 使用实际道路距离进行优化，结果更准确")

        return result
    }

    /// 🧠 Held-Karp动态规划算法 - 解决TSP问题的最优方法
    private func performHeldKarpOptimization(points: [DeliveryPoint], startPoint: StartPointOption) async -> [DeliveryPoint] {
        print("[INFO] GroupDetailView - 🧠 开始Held-Karp动态规划优化")

        let n = points.count
        guard n > 0 else { return [] }
        guard n <= 15 else {
            print("[WARNING] GroupDetailView - 点数过多(\(n))，降级到贪心算法")
            return await performStraightLineGreedyOptimization(points: points, startPoint: startPoint)
        }

        // 获取起始位置坐标
        let validDriverLocation = getValidDriverLocation()
        let startLocation = startPoint.coordinate(driverLocation: validDriverLocation)

        print("[INFO] GroupDetailView - Held-Karp算法处理\(n)个点，复杂度: O(\(n)² × 2^\(n)) = \(n * n * (1 << n))次计算")

        // 🚀 预填充缓存：获取所有缺失的距离数据
        await prefillDistanceCache(points: points, startLocation: startLocation)

        // 构建距离矩阵（现在应该有更多实际道路距离）
        var distanceMatrix: [[Double]] = Array(repeating: Array(repeating: 0.0, count: n + 1), count: n + 1)

        // 起始点到各个配送点的距离
        for i in 0..<n {
            let distance = await getOrCalculateDistance(from: startLocation, to: points[i].coordinate)
            distanceMatrix[0][i + 1] = distance
            distanceMatrix[i + 1][0] = distance
        }

        // 配送点之间的距离
        for i in 0..<n {
            for j in 0..<n {
                if i != j {
                    let distance = await getOrCalculateDistance(from: points[i].coordinate, to: points[j].coordinate)
                    distanceMatrix[i + 1][j + 1] = distance
                }
            }
        }

        // Held-Karp动态规划
        let result = await solveHeldKarp(distanceMatrix: distanceMatrix, n: n)

        // 将结果转换为DeliveryPoint数组
        var optimizedPoints: [DeliveryPoint] = []
        for index in result {
            if index > 0 && index <= n {
                optimizedPoints.append(points[index - 1])
            }
        }

        print("[INFO] GroupDetailView - 🧠 Held-Karp算法完成，最优路线长度: \(optimizedPoints.count)")
        return optimizedPoints
    }

    /// 🚀 预填充距离缓存 - 获取所有缺失的实际道路距离
    private func prefillDistanceCache(points: [DeliveryPoint], startLocation: CLLocationCoordinate2D) async {
        print("[INFO] GroupDetailView - 🚀 开始预填充距离缓存")

        var missingPairs: [(CLLocationCoordinate2D, CLLocationCoordinate2D)] = []

        // 检查起始点到各个配送点的距离
        for point in points {
            // 🔧 修复：直接检查数据库缓存，不只是内存缓存
            let cachedDistance = await MainActor.run {
                return PersistentCacheManager.shared.getStoredDistance(from: startLocation, to: point.coordinate)
            }
            if cachedDistance == nil {
                missingPairs.append((startLocation, point.coordinate))
            }
        }

        // 检查配送点之间的距离
        for i in 0..<points.count {
            for j in (i+1)..<points.count {
                let coord1 = points[i].coordinate
                let coord2 = points[j].coordinate

                // 🔧 修复：直接检查数据库缓存，不只是内存缓存
                let cachedDistance = await MainActor.run {
                    return PersistentCacheManager.shared.getStoredDistance(from: coord1, to: coord2)
                }
                if cachedDistance == nil {
                    missingPairs.append((coord1, coord2))
                }
            }
        }

        print("[INFO] GroupDetailView - 🔍 发现\(missingPairs.count)个缺失的距离段，开始获取实际道路距离")

        if missingPairs.isEmpty {
            print("[INFO] GroupDetailView - ✅ 所有距离数据已缓存，无需API调用")
            return
        }

        // 批量获取缺失的距离数据
        var successCount = 0
        var failCount = 0

        for (index, pair) in missingPairs.enumerated() {
            let (from, to) = pair

            print("[INFO] GroupDetailView - 📡 获取距离段 \(index + 1)/\(missingPairs.count)")

            // 使用async/await包装回调式API
            let distance = await withCheckedContinuation { continuation in
                DirectionsAPIManager.shared.calculateDistance(from: from, to: to) { result in
                    continuation.resume(returning: result)
                }
            }

            switch distance {
            case .success(let distanceValue):
                successCount += 1
                print("[INFO] GroupDetailView - ✅ 成功获取距离: \(String(format: "%.3f", distanceValue/1000))km")
            case .failure(let error):
                failCount += 1
                print("[WARNING] GroupDetailView - ❌ 获取距离失败: \(error.localizedDescription)")

                // 如果API调用失败，我们继续下一个，Held-Karp算法会使用直线距离作为降级
            }

            // 添加小延迟避免API频率限制
            if index < missingPairs.count - 1 {
                try? await Task.sleep(nanoseconds: 100_000_000) // 🔧 减少到0.1秒延迟
            }
        }

        print("[INFO] GroupDetailView - 🚀 预填充完成: 成功\(successCount)个，失败\(failCount)个")
        print("[INFO] GroupDetailView - 📊 缓存覆盖率: \(String(format: "%.1f", Double(successCount) / Double(missingPairs.count) * 100))%")
    }

    /// 获取或计算两点间距离（优先使用缓存的实际道路距离）
    private func getOrCalculateDistance(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D) async -> Double {
        // 首先尝试获取缓存的实际道路距离
        let cachedDistance = await MainActor.run {
            return DirectionsAPIManager.shared.getCachedDistance(from: from, to: to)
        }

        if let distance = cachedDistance {
            return distance
        }

        // 如果没有缓存，使用直线距离作为估算
        return calculateStraightLineDistance(from: from, to: to)
    }

    /// 计算路线的总直线距离
    private func calculateStraightLineTotalDistance(points: [DeliveryPoint], startLocation: CLLocationCoordinate2D) -> Double {
        guard !points.isEmpty else { return 0.0 }

        var totalDistance: Double = 0.0
        var currentLocation = startLocation

        for point in points {
            let distance = calculateStraightLineDistance(from: currentLocation, to: point.coordinate)
            totalDistance += distance
            currentLocation = point.coordinate
        }

        return totalDistance / 1000.0 // 转换为公里
    }

    /// 计算路线的总实际距离（优先使用缓存的道路距离）
    private func calculateActualTotalDistance(points: [DeliveryPoint], startLocation: CLLocationCoordinate2D) async -> Double {
        guard !points.isEmpty else { return 0.0 }

        var totalDistance: Double = 0.0
        var currentLocation = startLocation

        for point in points {
            let distance = await getOrCalculateDistance(from: currentLocation, to: point.coordinate)
            totalDistance += distance
            currentLocation = point.coordinate
        }

        return totalDistance / 1000.0 // 转换为公里
    }

    /// 🧠 Held-Karp算法核心实现
    private func solveHeldKarp(distanceMatrix: [[Double]], n: Int) async -> [Int] {
        print("[INFO] GroupDetailView - 🧠 执行Held-Karp动态规划算法")

        // dp[mask][i] = 从起点出发，访问mask集合中的所有点，最后到达点i的最短距离
        var dp: [[Double]] = Array(repeating: Array(repeating: Double.infinity, count: n + 1), count: 1 << n)
        var parent: [[Int]] = Array(repeating: Array(repeating: -1, count: n + 1), count: 1 << n)

        // 初始化：从起点到各个点的距离
        for i in 1...n {
            dp[1 << (i - 1)][i] = distanceMatrix[0][i]
        }

        // 动态规划填表
        for mask in 1..<(1 << n) {
            for u in 1...n {
                if (mask & (1 << (u - 1))) == 0 { continue } // u不在当前集合中
                if dp[mask][u] == Double.infinity { continue } // 当前状态不可达

                for v in 1...n {
                    if u == v { continue } // 不能到自己
                    if (mask & (1 << (v - 1))) != 0 { continue } // v已经在集合中

                    let newMask = mask | (1 << (v - 1))
                    let newDistance = dp[mask][u] + distanceMatrix[u][v]

                    if newDistance < dp[newMask][v] {
                        dp[newMask][v] = newDistance
                        parent[newMask][v] = u
                    }
                }
            }
        }

        // 找到最优解
        let fullMask = (1 << n) - 1
        var minDistance = Double.infinity
        var lastNode = -1

        for i in 1...n {
            let totalDistance = dp[fullMask][i] + distanceMatrix[i][0]
            if totalDistance < minDistance {
                minDistance = totalDistance
                lastNode = i
            }
        }

        // 重构路径
        var path: [Int] = []
        var currentMask = fullMask
        var currentNode = lastNode

        while currentNode != -1 {
            path.append(currentNode)
            let prevNode = parent[currentMask][currentNode]
            if prevNode != -1 {
                currentMask ^= (1 << (currentNode - 1))
            }
            currentNode = prevNode
        }

        path.reverse()

        print("[INFO] GroupDetailView - 🧠 Held-Karp算法完成，最优距离: \(String(format: "%.2f", minDistance/1000))km")
        print("[INFO] GroupDetailView - 🧠 最优路径: \(path)")

        return path
    }

    /// 🔍 验证数据一致性
    private func verifyDataConsistency() {
        print("[VERIFY] GroupDetailView - 🔍 数据一致性验证:")

        // 1. 检查界面显示的points数组
        print("[VERIFY] 界面points数组 (共\(points.count)个点):")
        for (index, point) in points.enumerated() {
            print("[VERIFY]   界面[\(index)]: \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
        }

        // 2. 检查group.points数组
        print("[VERIFY] group.points数组 (共\(group.points.count)个点):")
        for (index, point) in group.points.enumerated() {
            print("[VERIFY]   group[\(index)]: \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
        }

        // 3. 检查group.sortedPoints
        let sortedPoints = group.sortedPoints
        print("[VERIFY] group.sortedPoints数组 (共\(sortedPoints.count)个点):")
        for (index, point) in sortedPoints.enumerated() {
            print("[VERIFY]   sorted[\(index)]: \(point.primaryAddress) (sorted_number: \(point.sorted_number))")
        }

        // 4. 计算不同数组的距离
        let pointsDistance = calculateRouteDistance(points: points)
        let groupPointsDistance = calculateRouteDistance(points: group.points)
        let sortedPointsDistance = calculateRouteDistance(points: sortedPoints)

        print("[VERIFY] 距离计算结果:")
        print("[VERIFY]   界面points距离: \(String(format: "%.1f", pointsDistance))km")
        print("[VERIFY]   group.points距离: \(String(format: "%.1f", groupPointsDistance))km")
        print("[VERIFY]   group.sortedPoints距离: \(String(format: "%.1f", sortedPointsDistance))km")
        print("[VERIFY]   当前显示距离: \(String(format: "%.1f", currentTotalDistance))km")

        // 5. 检查是否一致
        if pointsDistance != groupPointsDistance || pointsDistance != sortedPointsDistance {
            print("[ERROR] ❌ 数据不一致！不同数组产生了不同的距离！")
        } else {
            print("[SUCCESS] ✅ 数据一致")
        }
    }



    // 分组项视图
    struct GroupItemView: View {
        let group: DeliveryGroup
        let viewModel: RouteViewModel
        var onNavigate: (() -> Void)? = nil

        @State private var showingGroupDetail = false

        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                // 标题行
                HStack {
                    Image(systemName: "rectangle.stack.fill")
                        .foregroundColor(.blue)

                    Text(group.name)
                        .font(.headline)

                    Spacer()

                    Text(String(format: "points_count_format".localized, group.points.count))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                // 地址预览
                if !group.points.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(group.sortedPoints.prefix(5)) { point in
                                HStack(spacing: 4) {
                                    Text("\(point.sort_number).")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)

                                    // 显示带单位信息的简化地址
                                    HStack(spacing: 2) {
                                        if point.hasUnitNumber, let unitNumber = point.unitNumber {
                                            Text(unitNumber)
                                                .font(.caption2)
                                                .fontWeight(.semibold)
                                                .foregroundColor(.orange)
                                        }
                                        Text(formatMainAddress(point.primaryAddress, hasUnit: point.hasUnitNumber))
                                            .font(.caption)
                                            .lineLimit(1)
                                    }
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(4)
                            }

                            if group.points.count > 5 {
                                Text(String(format: "additional_points_format".localized, group.points.count - 5))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                    }
                }

                // 按钮行
                HStack(spacing: 12) {
                    // 编辑按钮
                    Button(action: {
                        showingGroupDetail = true
                    }) {
                        HStack {
                            Image(systemName: "pencil")
                            Text("edit".localized)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .foregroundColor(.primary)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 导航按钮
                    Button(action: {
                        // 导航到该组
                        viewModel.navigateToGroup(group) { success, message in
                            // 检查是否需要显示Google Maps网页版提示
                            if success && message == "google_maps_web_version" {
                                // 显示使用网页版的提示
                                let alert = UIAlertController(
                                    title: "google_maps".localized,
                                    message: "using_web_version".localized,
                                    preferredStyle: .alert
                                )
                                alert.addAction(UIAlertAction(title: "understand".localized, style: .default))

                                // 显示提示
                                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                   let rootViewController = windowScene.windows.first?.rootViewController {
                                    rootViewController.present(alert, animated: true)
                                }
                            }
                        }
                        onNavigate?() // 如果提供了回调，则执行
                    }) {
                        HStack {
                            Image(systemName: "location.fill")
                            Text("navigate".localized)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.top, 4)
            }
            .padding(.vertical, 8)
            .sheet(isPresented: $showingGroupDetail) {
                NavigationView {
                    GroupDetailView(group: group, viewModel: viewModel)
                }
            }
        }

        /// 格式化主要地址显示，确保单位信息正确处理
        private func formatMainAddress(_ fullAddress: String, hasUnit: Bool) -> String {
            let components = fullAddress.components(separatedBy: ",")

            if hasUnit && !components.isEmpty {
                // 如果有单位号，第一个组件通常包含"单位号, 街道地址"
                // 我们需要移除单位号部分，只显示街道地址
                let firstComponent = components[0].trimmingCharacters(in: .whitespaces)

                // 查找第一个逗号后的内容作为街道地址
                if let commaIndex = firstComponent.firstIndex(of: ",") {
                    let streetAddress = String(firstComponent[firstComponent.index(after: commaIndex)...])
                        .trimmingCharacters(in: .whitespaces)
                    return streetAddress.isEmpty ? firstComponent : streetAddress
                }
            }

            // 默认返回第一个组件或完整地址
            return components.first?.trimmingCharacters(in: .whitespaces) ?? fullAddress
        }
    }

    // MARK: - 距离显示辅助方法

    /// 格式化距离显示文本
    private func formatDistanceDisplay() -> String {
        if isCalculatingDistance {
            return "计算中..."
        } else if currentTotalDistance < 0 {
            return "计算失败"
        } else if currentTotalDistance == 0 {
            return "等待计算"
        } else {
            return DistanceFormatter.shared.formatDistance(currentTotalDistance * 1000)
        }
    }

    /// 获取距离显示颜色
    private func getDistanceColor() -> Color {
        if isCalculatingDistance {
            return .orange
        } else if currentTotalDistance < 0 {
            return .red
        } else if currentTotalDistance == 0 {
            return .gray
        } else {
            return group.isGroupOptimized ? .green : .blue
        }
    }
}

// MARK: - 起始点选择视图
struct StartPointSelectionView: View {
    let group: DeliveryGroup
    let driverLocation: CLLocationCoordinate2D?
    let onStartPointSelected: (StartPointOption) -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 选项列表
                List {
                    // 司机当前位置选项
                    Button {
                        onStartPointSelected(.driverLocation)
                    } label: {
                        HStack(spacing: 16) {
                            Image(systemName: "location.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                                .frame(width: 32, height: 32)

                            VStack(alignment: .leading, spacing: 4) {
                                Text("start_point_driver_location".localized)
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text(driverLocation != nil ? "start_point_current_gps".localized : "start_point_default_location".localized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 分组中的点选项
                    Section {
                        ForEach(group.sortedPoints, id: \.id) { point in
                            Button {
                                onStartPointSelected(.deliveryPoint(point))
                            } label: {
                                HStack(spacing: 16) {
                                    // 排序号标签
                                    ZStack {
                                        Circle()
                                            .fill(Color.orange)
                                            .frame(width: 32, height: 32)

                                        Text(getDisplaySortNumber(for: point))
                                            .font(.headline)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.white)
                                    }

                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(point.primaryAddress)
                                            .font(.headline)
                                            .foregroundColor(.primary)
                                            .lineLimit(2)

                                        Text("start_point_sort_number".localized(with: getDisplaySortNumber(for: point)))
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                    }

                                    Spacer()

                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.vertical, 8)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    } header: {
                        Text("start_point_delivery_points_section".localized)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("start_point_selection_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
        }
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
    }

    /// 获取显示的排序号（优先显示第三方排序号）
    private func getDisplaySortNumber(for point: DeliveryPoint) -> String {
        if point.isThirdPartyWithSort, let thirdPartySort = point.thirdPartySortNumber {
            return thirdPartySort
        } else {
            return "\(point.sorted_number)"
        }
    }
}



// MARK: - 优化模式选择视图
struct OptimizationModeSelectionView: View {
    let group: DeliveryGroup
    let viewModel: RouteViewModel
    let onModeSelected: (OptimizationMode) -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 选项列表
                VStack(spacing: 12) {
                    // 智能优化选项
                    Button {
                        onModeSelected(.smart)
                    } label: {
                        HStack(spacing: 16) {
                            Image(systemName: "brain.head.profile")
                                .font(.title2)
                                .foregroundColor(.blue)
                                .frame(width: 32, height: 32)

                            VStack(alignment: .leading, spacing: 4) {
                                Text("optimization_mode_smart_title".localized)
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text("optimization_mode_smart_subtitle".localized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.leading)
                            }

                            Spacer()
                        }
                        .padding(.vertical, 16)
                        .padding(.horizontal, 20)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 手动选择选项
                    Button {
                        onModeSelected(.manual)
                    } label: {
                        HStack(spacing: 16) {
                            Image(systemName: "hand.point.up.left")
                                .font(.title2)
                                .foregroundColor(.orange)
                                .frame(width: 32, height: 32)

                            VStack(alignment: .leading, spacing: 4) {
                                Text("optimization_mode_manual_title".localized)
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text("optimization_mode_manual_subtitle".localized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.leading)
                            }

                            Spacer()
                        }
                        .padding(.vertical, 16)
                        .padding(.horizontal, 20)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)

                Spacer()
            }
            .navigationTitle("optimization_mode_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
        }
        .presentationDetents([.medium])
        .presentationDragIndicator(.visible)
    }
}



// MARK: - Double 扩展
extension Double {
    /// 保留指定小数位数
    func rounded(toPlaces places: Int) -> Double {
        let divisor = pow(10.0, Double(places))
        return (self * divisor).rounded() / divisor
    }
}

// MARK: - DateFormatter 扩展
extension DateFormatter {
    static let preciseTimeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
}
