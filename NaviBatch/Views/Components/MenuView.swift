import SwiftUI
import SwiftData
import MapKit
import Combine

// 添加ModernSubscriptionView的导入
import StoreKit

// 自定义环境键用于传递dismiss action
struct DismissActionKey: EnvironmentKey {
    static let defaultValue: () -> Void = {
        print("[DEBUG] DismissActionKey - 使用默认的dismiss action，这可能表示环境值未被正确设置")
    }
}

extension EnvironmentValues {
    var customDismiss: () -> Void {
        get { self[DismissActionKey.self] }
        set { self[DismissActionKey.self] = newValue }
    }
}

// 订阅视图选项环境键
struct SubscriptionViewOptionsKey: EnvironmentKey {
    static let defaultValue = SubscriptionViewOptions()
}

// 订阅视图选项
struct SubscriptionViewOptions {
    var showTitle: Bool = true
    var showCloseButton: Bool = true
}

// 环境值扩展
extension EnvironmentValues {
    var subscriptionViewOptions: SubscriptionViewOptions {
        get { self[SubscriptionViewOptionsKey.self] }
        set { self[SubscriptionViewOptionsKey.self] = newValue }
    }
}

struct MenuView: View {
    // 使用环境中的 ModelContext
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme

    @State private var routes: [Route] = []
    @State private var deliveryGroups: [DeliveryGroup] = []
    @State private var deliveryPoints: [DeliveryPoint] = []
    @State private var savedAddresses: [SavedAddress] = []
    @State private var addressCount: Int = 0
    @State private var routeCount: Int = 0
    @State private var selectedTab: MenuTab = .routes
    @State private var animateCards: Bool = false
    @State private var showSubscriptionPrompt: Bool = false // 添加订阅提示状态变量
    @State private var showSubscriptionView: Bool = false // 直接显示订阅视图的状态变量
    @State private var showDeveloperTools: Bool = false // 开发者工具状态变量
    @State private var showingAddressHistory: Bool = false // 🎯 优化：历史地址sheet状态变量

    // 用于强制刷新UI
    @StateObject private var viewModel = MenuViewModel()

    var onDismiss: () -> Void

    // 初始化方法
    init(onDismiss: @escaping () -> Void) {
        self.onDismiss = onDismiss
    }

    // 定义菜单标签页
    enum MenuTab {
        case routes, addresses
    }

    // 纯Apple原生风格的菜单设计 - 标准 Sheet 样式
    var body: some View {
        // 主要内容 - 移除自定义分割线，使用系统提供的 presentationDragIndicator
        List {
            // 用户状态 - 更简洁的设计
            Section {
                NativeUserStatusRow()
            }
            .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))

            // 快速访问
            Section {
                NavigationLink(destination: SavedGroupsView()) {
                    Label {
                        VStack(alignment: .leading, spacing: 1) {
                            Text("saved_routes".localized)
                                .font(.body)
                            Text("\(routeCount) " + "routes".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } icon: {
                        Image(systemName: "map")
                            .foregroundColor(.blue)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())

                NavigationLink(destination: AddressBookView()) {
                    Label {
                        VStack(alignment: .leading, spacing: 1) {
                            Text("address_book".localized)
                                .font(.body)
                            Text("\(addressCount) " + "addresses".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } icon: {
                        Image(systemName: "book")
                            .foregroundColor(.green)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    // 🎯 优化：改为sheet显示，提供更好的用户体验
                    showingAddressHistory = true
                }) {
                    Label {
                        VStack(alignment: .leading, spacing: 1) {
                            Text("address_history".localized)
                                .font(.body)
                            Text("manage_address_history".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } icon: {
                        Image(systemName: "clock.arrow.circlepath")
                            .foregroundColor(.orange)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))

            // 设置
            Section {
                NavigationLink(destination: PreferencesView()) {
                    Label("preferences".localized, systemImage: "gearshape")
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))

            // 订阅
            Section {
                Button(action: {
                    showSubscriptionPrompt = true
                }) {
                    HStack {
                        Label("upgrade_to_pro".localized, systemImage: "crown")
                            .foregroundColor(.primary)
                        Spacer()
                        Text("NEW")
                            .font(.caption2)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(Color.orange)
                            )
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    Task {
                        do {
                            try await StoreKitManager.shared.restorePurchases()
                        } catch {
                            print("[ERROR] 恢复购买失败: \(error.localizedDescription)")
                        }
                    }
                }) {
                    Label("restore_purchases".localized, systemImage: "arrow.clockwise")
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))

            // 支持
            Section {
                NavigationLink(destination: ContactUsView()) {
                    Label("contact_us".localized, systemImage: "bubble.left")
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())

                NavigationLink(destination: AboutAppView()) {
                    Label("about_app".localized, systemImage: "info.circle")
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))

            // 开发者工具（仅调试模式 - App Store版本中完全移除）
            #if DEBUG && !APPSTORE_BUILD
            Section("Developer") {
                Button(action: {
                    print("[DEBUG] 开发者工具按钮被点击")
                    showDeveloperTools = true
                }) {
                    Label("dev_tools".localized, systemImage: "hammer")
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
            #endif

            // 版本信息
            Section {
                HStack {
                    Text("version".localized)
                    Spacer()
                    Text(AppEnvironment.versionInfoEnglish)
                        .foregroundColor(.secondary)
                }
                .font(.footnote)
            }
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
        }
        .sheet(isPresented: $showSubscriptionPrompt) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
        .sheet(isPresented: $showSubscriptionView) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
        .sheet(isPresented: $showDeveloperTools) {
            DeveloperToolsView()
                .environmentObject(LocalizationManager.shared)
        }
        .sheet(isPresented: $showingAddressHistory) {
            // 🎯 优化：历史地址作为sheet显示，提供系统drag indicator
            AddressHistoryView()
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
                .presentationCornerRadius(12)
                .presentationBackground(.regularMaterial)
        }
        .onAppear {
            print("MenuView - 视图出现，立即刷新数据")
            refreshData()
        }
    }

    // 刷新所有数据
    private func refreshData() {
        print("[INFO] MenuView - 开始刷新所有数据")

        // 避免多次UI刷新，只在所有数据加载完成后刷新一次
        Task {
            // 并行加载数据以提高效率
            await withTaskGroup(of: Void.self) { group in
                group.addTask { await self.refreshRouteCount() }
                group.addTask { await self.refreshAddressCount() }

                // 等待所有任务完成
                for await _ in group { }
            }

            // 所有数据都加载完成后，在主线程刷新一次UI
            await MainActor.run {
                print("[INFO] MenuView - 所有数据加载完成，刷新UI")
                self.viewModel.objectWillChange.send()
            }
        }
    }

    // 刷新地址数量
    private func refreshAddressCount() async {
        print("[INFO] MenuView - 开始刷新地址数量")

        // 使用持久化容器
        let container = getPersistentContainer()

        do {
            // 使用与其他视图相同的查询方式
            let descriptor = FetchDescriptor<SavedAddress>()
            let addresses = try container.mainContext.fetch(descriptor)

            print("[INFO] MenuView - 查询到 \(addresses.count) 个地址")

            // 在主线程更新UI
            await MainActor.run {
                // 直接更新计数
                self.addressCount = addresses.count
                self.savedAddresses = addresses
            }
        } catch {
            print("[ERROR] MenuView - 查询地址失败: \(error.localizedDescription)")
        }
    }

    // 刷新路线数量
    private func refreshRouteCount() async {
        print("[INFO] MenuView - 开始刷新路线数量")

        // 使用持久化容器
        let container = getPersistentContainer()

        do {
            // 首先同步路线计数器
            print("[INFO] MenuView - 开始同步路线计数器")
            await Route.syncRouteCount()

            // 查询所有路线
            let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
            let fetchedRoutes = try container.mainContext.fetch(descriptor)

            print("[INFO] MenuView - 查询到 \(fetchedRoutes.count) 条路线")

            // 在主线程更新UI
            await MainActor.run {
                // 更新路线数据
                self.routeCount = fetchedRoutes.count
                self.routes = fetchedRoutes
            }
        } catch {
            print("[ERROR] MenuView - 查询路线失败: \(error.localizedDescription)")
        }
    }
}

#Preview("MenuView") {
    // 创建一个正确配置的 ModelContainer
    let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self])
    let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: schema, configurations: [modelConfiguration])

    // 创建示例数据
    let context = container.mainContext
    let route = Route(name: "Route 2025-04-21")
    context.insert(route)

    let address = SavedAddress(
        address: "123 Main Street, Melbourne VIC 3000, Australia",
        coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631),
        phoneNumber: "0412345678"
    )
    context.insert(address)

    try! context.save()

    return MenuView(onDismiss: {})
        .modelContainer(container)
}

// 菜单行组件 - 现代化设计
struct MenuRow: View {
    var icon: String
    var title: String
    var description: String? = nil
    var showChevron: Bool = true
    var iconColor: Color = .blue
    var isHighlighted: Bool = false
    var action: (() -> Void)? = nil

    @Environment(\.colorScheme) private var colorScheme
    @State private var isPressed = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                // 图标容器
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.15))
                        .frame(width: 32, height: 32)

                    Image(systemName: icon)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(iconColor)
                }
                .padding(.leading, 16)

                // 标题
                Text(title)
                    .font(.system(size: 16, weight: isHighlighted ? .semibold : .medium))
                    .foregroundColor(isHighlighted ? .primary : .primary)
                    .padding(.leading, 12)

                Spacer()

                // 右侧箭头
                if showChevron {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 13))
                        .foregroundColor(.gray)
                        .padding(.trailing, 16)
                }
            }
            .padding(.vertical, description == nil ? 10 : 6) // 进一步减少垂直间距

            // 描述文本（如果有）
            if let description = description {
                Text(description)
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
                    .padding(.leading, 60)
                    .padding(.trailing, 16)
                    .padding(.bottom, 10)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(isPressed ?
                      (colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.1)) :
                      (colorScheme == .dark ? Color(.systemBackground) : Color.white))
                .animation(.easeInOut(duration: 0.2), value: isPressed)
        )
        .contentShape(Rectangle())
        .simultaneousGesture(TapGesture().onEnded {
            // 只有当action不为nil时才处理点击
            if action != nil {
                print("[DEBUG] MenuRow 被点击: \(title)")

                // 提供触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()

                isPressed = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    isPressed = false

                    // 执行传入的操作
                    action?()
                }
            }
        })
    }
}

// 菜单卡片组件
struct MenuCard: View {
    var icon: String
    var title: String
    var subtitle: String
    var count: Int
    var action: (() -> Void)? = nil

    @Environment(\.colorScheme) private var colorScheme
    @State private var isPressed = false

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color.blue.opacity(0.15))
                        .frame(width: 48, height: 48)

                    Image(systemName: icon)
                        .font(.system(size: 22))
                        .foregroundColor(.blue)
                }

                // 中间文本
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
                .padding(.leading, 12)

                Spacer()

                // 右侧计数
                ZStack {
                    Circle()
                        .fill(Color.blue.opacity(0.15))
                        .frame(width: 40, height: 40)

                    Text("\(count)")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.blue)
                }

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.gray)
                    .padding(.leading, 8)
            }
        }
        .padding(16)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isPressed ?
                      (colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.1)) :
                      (colorScheme == .dark ? Color(.systemBackground) : Color.white))
                .animation(.easeInOut(duration: 0.2), value: isPressed)
        )
        .contentShape(Rectangle())
        .onTapGesture {
            // 提供触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            isPressed = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                isPressed = false
                // 执行传入的操作
                action?()
            }
        }
    }
}

// 部分标题组件 - 现代化设计
struct MenuSectionHeader: View {
    var title: String
    var iconName: String? = nil

    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        HStack(spacing: 8) {
            if let icon = iconName {
                Image(systemName: icon)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }

            Text(title)
                .font(.system(size: 15, weight: .semibold))
                .foregroundColor(.secondary)
                .textCase(.uppercase)

            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.top, 12) // 进一步减少顶部间距
        .padding(.bottom, 4) // 进一步减少底部间距
        .background(colorScheme == .dark ? Color.black.opacity(0.2) : Color(.systemGroupedBackground))
    }
}

// 顶部标题栏视图
struct MenuHeaderView: View {
    var onDismiss: () -> Void

    var body: some View {
        HStack {
            // 返回按钮
            Button(action: {
                // 确保在调用onDismiss前记录日志
                print("[INFO] MenuView - 返回按钮被点击，准备关闭菜单")

                // 添加轻微的触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()

                // 使用主线程调用onDismiss，确保UI操作在主线程执行
                DispatchQueue.main.async {
                    onDismiss()
                }
            }) {
                ZStack {
                    Circle()
                        .fill(Color.blue.opacity(0.1))
                        .frame(width: 32, height: 32)

                    Image(systemName: "chevron.left")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.blue)
                }
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            // 中间标题
            Text("menu".localized)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)

            Spacer()

            // 平衡布局的空白区域
            Circle()
                .fill(Color.clear)
                .frame(width: 32, height: 32)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 20)
        .background(Color(.systemBackground))
        .shadow(color: Color.black.opacity(0.05), radius: 5, y: 2)
    }
}

// 顶部标签视图
struct MenuTabsView: View {
    @Binding var selectedTab: MenuView.MenuTab
    var routeCount: Int
    var addressCount: Int
    var animateCards: Bool

    // 添加命名空间以支持matchedGeometryEffect
    @Namespace private var namespace

    // 添加状态变量
    @State private var isNavigating = false
    @State private var isLoading = false

    // 添加环境变量以获取导航控制器
    @Environment(\.colorScheme) private var colorScheme

    // 导航到路线页面
    private func navigateToRoutes() {
        // 显示加载状态
        isLoading = true
        isNavigating = true

        // 延迟执行导航，提供更好的用户体验
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // 创建一个NavigationLink并以编程方式触发它
            let hostingController = UIHostingController(rootView: SavedGroupsView().onDisappear {
                print("[INFO] MenuView - SavedGroupsView已消失，检查是否需要关闭菜单")
                isNavigating = false
            })

            // 获取当前视图控制器
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController,
               let navigationController = rootViewController.findNavigationController() {
                navigationController.pushViewController(hostingController, animated: true)

                // 导航完成后重置加载状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    isLoading = false
                }
            } else {
                isLoading = false
                isNavigating = false
            }
        }
    }

    // 导航到地址簿页面
    private func navigateToAddressBook() {
        // 显示加载状态
        isLoading = true
        isNavigating = true

        // 延迟执行导航，提供更好的用户体验
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // 创建一个NavigationLink并以编程方式触发它
            let hostingController = UIHostingController(rootView: AddressBookView().onDisappear {
                isNavigating = false
            })

            // 获取当前视图控制器
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController,
               let navigationController = rootViewController.findNavigationController() {
                navigationController.pushViewController(hostingController, animated: true)

                // 导航完成后重置加载状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    isLoading = false
                }
            } else {
                isLoading = false
                isNavigating = false
            }
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // 标签选择器 - 只切换标签，不导航
            HStack(spacing: 0) {
                // 路线标签
                Button(action: {
                    if selectedTab != .routes {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            selectedTab = .routes
                        }

                        // 提供触觉反馈
                        UIImpactFeedbackGenerator(style: .light).impactOccurred()
                    }
                }) {
                    VStack(spacing: 8) {
                        Text("routes".localized)
                            .font(.system(size: 16, weight: selectedTab == .routes ? .semibold : .regular))
                            .foregroundColor(selectedTab == .routes ? .primary : .secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        ZStack {
                            if selectedTab == .routes {
                                Color(.systemBackground)
                                    .matchedGeometryEffect(id: "TabBackground", in: namespace)
                            } else {
                                Color(.systemGroupedBackground)
                            }
                        }
                    )
                }

                // 地址簿标签
                Button(action: {
                    if selectedTab != .addresses {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            selectedTab = .addresses
                        }

                        // 提供触觉反馈
                        UIImpactFeedbackGenerator(style: .light).impactOccurred()
                    }
                }) {
                    VStack(spacing: 8) {
                        Text("address_book".localized)
                            .font(.system(size: 16, weight: selectedTab == .addresses ? .semibold : .regular))
                            .foregroundColor(selectedTab == .addresses ? .primary : .secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        ZStack {
                            if selectedTab == .addresses {
                                Color(.systemBackground)
                                    .matchedGeometryEffect(id: "TabBackground", in: namespace)
                            } else {
                                Color(.systemGroupedBackground)
                            }
                        }
                    )
                }
            }
            .background(Color(.systemGroupedBackground))
            .cornerRadius(12, corners: [.topLeft, .topRight])
            .disabled(isNavigating) // 导航过程中禁用标签切换

            // 卡片内容区域
            ZStack {
                // 路线卡片
                Button(action: {
                    if !isNavigating {
                        navigateToRoutes()
                    }
                }) {
                    HStack {
                        // 左侧图标
                        ZStack {
                            Circle()
                                .fill(Color.blue.opacity(0.15))
                                .frame(width: 48, height: 48)

                            Image(systemName: "map.fill")
                                .font(.system(size: 22))
                                .foregroundColor(.blue)
                        }

                        // 中间文本
                        VStack(alignment: .leading, spacing: 4) {
                            Text("saved_routes".localized)
                                .font(.system(size: 17, weight: .semibold))
                                .foregroundColor(.primary)

                            Text("manage_your_routes".localized)
                                .font(.system(size: 14))
                                .foregroundColor(.secondary)
                        }
                        .padding(.leading, 12)

                        Spacer()

                        // 右侧计数和加载指示器
                        HStack(spacing: 8) {
                            ZStack {
                                Circle()
                                    .fill(Color.blue.opacity(0.15))
                                    .frame(width: 40, height: 40)

                                Text("\(routeCount)")
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(.blue)
                            }

                            if isLoading && selectedTab == .routes {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .padding(16)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(isNavigating)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(colorScheme == .dark ? Color(.systemBackground) : Color.white)
                )
                .opacity(selectedTab == .routes ? 1 : 0)
                .offset(x: selectedTab == .routes ? 0 : -30)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedTab)

                // 地址簿卡片
                Button(action: {
                    if !isNavigating {
                        navigateToAddressBook()
                    }
                }) {
                    HStack {
                        // 左侧图标
                        ZStack {
                            Circle()
                                .fill(Color.blue.opacity(0.15))
                                .frame(width: 48, height: 48)

                            Image(systemName: "book.fill")
                                .font(.system(size: 22))
                                .foregroundColor(.blue)
                        }

                        // 中间文本
                        VStack(alignment: .leading, spacing: 4) {
                            Text("address_book".localized)
                                .font(.system(size: 17, weight: .semibold))
                                .foregroundColor(.primary)

                            Text("manage_your_addresses".localized)
                                .font(.system(size: 14))
                                .foregroundColor(.secondary)
                        }
                        .padding(.leading, 12)

                        Spacer()

                        // 右侧计数和加载指示器
                        HStack(spacing: 8) {
                            ZStack {
                                Circle()
                                    .fill(Color.blue.opacity(0.15))
                                    .frame(width: 40, height: 40)

                                Text("\(addressCount)")
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(.blue)
                            }

                            if isLoading && selectedTab == .addresses {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .padding(16)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(isNavigating)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(colorScheme == .dark ? Color(.systemBackground) : Color.white)
                )
                .opacity(selectedTab == .addresses ? 1 : 0)
                .offset(x: selectedTab == .addresses ? 0 : 30)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedTab)
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12, corners: [.bottomLeft, .bottomRight])
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 2)
        .padding(.horizontal, 16)
        .padding(.top, 6) // 减少顶部间距
        .scaleEffect(animateCards ? 1 : 0.95)
        .opacity(animateCards ? 1 : 0)
    }
}

// 设置部分
struct MenuSettingsSection: View {

    var body: some View {
        MenuSectionHeader(title: "settings".localized, iconName: "gearshape.fill")

        // 使用直接的NavigationLink实现，简化导航逻辑
        NavigationLink(destination:
            PreferencesView()
                .onAppear { print("[DEBUG] PreferencesView 已出现") }
        ) {
            MenuRow(
                icon: "gearshape",
                title: "preferences".localized,
                iconColor: .blue,
                // 明确设置action为nil，确保不拦截点击事件
                action: nil
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 2) // 减少底部间距
        .buttonStyle(PlainButtonStyle()) // 保持按钮样式一致
        .contentShape(Rectangle())
        .onTapGesture {
            print("[DEBUG] NavigationLink 点击检测 - Preferences")
        }
    }
}

// 支持部分
struct MenuSupportSection: View {

    var body: some View {
        MenuSectionHeader(title: "support".localized, iconName: "lifepreserver")

        // 使用直接的NavigationLink实现，简化导航逻辑
        NavigationLink(destination: ContactUsView()) {
            MenuRow(
                icon: "bubble.left.fill",
                title: "contact_us".localized,
                iconColor: .green,
                action: nil
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 4)
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
    }
}

// 订阅部分
struct MenuSubscriptionSection: View {
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared
    @Binding var showSubscriptionPrompt: Bool // 改为绑定变量，从父视图传入
    @Binding var showSubscriptionView: Bool // 添加直接显示订阅视图的绑定变量

    var body: some View {
        VStack {
            MenuSectionHeader(title: "subscription".localized, iconName: "crown.fill")

            // 使用单独的 MenuRow，不包装在 Button 中
            MenuRow(
                icon: "crown.fill",
                title: "upgrade_to_pro".localized,
                iconColor: .orange,
                isHighlighted: true,
                action: {
                    print("[DEBUG] 升级至高级版 MenuRow action 被执行")
                    // 直接在 action 中设置状态变量
                    DispatchQueue.main.async {
                        print("[DEBUG] 设置 showSubscriptionPrompt = true")
                        showSubscriptionPrompt = true
                    }
                }
            )
            .padding(.horizontal, 16)
            .padding(.bottom, 4)

            // 恢复购买按钮
            MenuRow(
                icon: "arrow.clockwise",
                title: "restore_purchases".localized,
                showChevron: false,
                iconColor: .blue,
                action: {
                    print("[DEBUG] 恢复购买 MenuRow action 被执行")

                    // 添加触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()

                    // 调用 StoreKitManager 的恢复购买方法
                    Task {
                        do {
                            try await StoreKitManager.shared.restorePurchases()
                        } catch {
                            print("[ERROR] MenuRow - 恢复购买失败: \(error.localizedDescription)")
                            // 显示错误提示
                            NotificationCenter.default.post(
                                name: Notification.Name("ShowToast"),
                                object: "restore_purchases_failed".localized(with: error.localizedDescription)
                            )
                        }
                    }
                }
            )
            .padding(.horizontal, 16)
            .padding(.bottom, 4)
        }
    }
}

// 关于部分
struct MenuAboutSection: View {

    var body: some View {
        MenuSectionHeader(title: "about".localized, iconName: "info.circle.fill")

        // 使用直接的NavigationLink实现，简化导航逻辑
        NavigationLink(destination: AboutAppView()) {
            MenuRow(
                icon: "info.circle",
                title: "about_app".localized,
                iconColor: .blue,
                action: nil
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 4)
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
    }
}

// 开发者工具部分
struct MenuDeveloperSection: View {
    @State private var showDeveloperTools = false

    var body: some View {
        MenuSectionHeader(title: "developer_tools".localized, iconName: "hammer.fill")

        // 开发工具入口
        MenuRow(
            icon: "hammer",
            title: "developer_tools".localized,
            iconColor: .blue,
            action: {
                print("[DEBUG] 开发工具 MenuRow action 被执行")
                showDeveloperTools = true
            }
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 4)
        .sheet(isPresented: $showDeveloperTools) {
            DeveloperToolsView()
                .environmentObject(LocalizationManager.shared)
        }

        // 订阅状态切换功能
        MenuRow(
            icon: "crown.circle",
            title: "toggle_subscription_status".localized,
            description: "toggle_subscription_description".localized,
            iconColor: .yellow,
            action: {
                print("[DEBUG] 订阅状态切换按钮被点击")

                let subscriptionManager = SubscriptionManager.shared
                // 在免费和专业版之间切换
                let newTier: SubscriptionTier = subscriptionManager.currentTier == .free ? .pro : .free
                subscriptionManager.updateSubscription(to: newTier)

                // 记录订阅状态变更
                print("[DEBUG] 订阅状态已切换: \(subscriptionManager.currentTier.rawValue)")

                // 显示切换成功的提示
                DispatchQueue.main.async {
                    // 可以这里添加提示或通知
                    NotificationCenter.default.post(name: NSNotification.Name("SubscriptionStatusChanged"), object: nil)
                }
            }
        )

        // 重置为免费版按钮
        MenuRow(
            icon: "arrow.counterclockwise.circle",
            title: "重置为免费版",
            description: "将订阅状态重置为免费版本",
            iconColor: .orange,
            action: {
                print("[DEBUG] 重置为免费版按钮被点击")

                let subscriptionManager = SubscriptionManager.shared
                subscriptionManager.updateSubscription(to: .free)

                print("[DEBUG] 订阅状态已重置为免费版")

                // 显示重置成功的提示
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("SubscriptionStatusChanged"), object: nil)
                }
            }
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 4)

        NavigationLink(destination: DebugCoordinatesView()) {
            MenuRow(
                icon: "location.magnifyingglass",
                title: "coordinate_debug_tool".localized,
                iconColor: .gray,
                action: {
                    print("[DEBUG] 坐标调试工具 MenuRow action 被执行")
                }
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 4)
        .buttonStyle(PlainButtonStyle())
        .simultaneousGesture(TapGesture().onEnded {
            print("[DEBUG] 坐标调试工具 NavigationLink 被点击")
        })

        // 批量修正地址按钮
        Button(action: {
            print("[DEBUG] 批量修正地址按钮被点击")

            // 添加触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            // 发送通知以打开批量修正地址表单
            NotificationCenter.default.post(
                name: Notification.Name("OpenBatchAddressFixSheet"),
                object: nil
            )

            // 关闭菜单
            DispatchQueue.main.async {
                // 确保在主线程执行UI操作
                NotificationCenter.default.post(
                    name: Notification.Name("CloseMenuView"),
                    object: nil
                )
            }
        }) {
            MenuRow(
                icon: "exclamationmark.triangle",
                title: "batch_fix_addresses".localized,
                iconColor: .orange,
                action: {
                    print("[DEBUG] 批量修正地址 MenuRow action 被执行")
                }
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 4)
        .buttonStyle(PlainButtonStyle())

        // 清空数据库按钮
        Button(action: {
            print("[DEBUG] 清空数据库按钮被点击")

            // 添加触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            // 显示确认对话框
            let alert = UIAlertController(
                title: "clear_database".localized,
                message: "clear_database_confirmation".localized,
                preferredStyle: .alert
            )

            // 取消按钮
            alert.addAction(UIAlertAction(
                title: "cancel".localized,
                style: .cancel
            ))

            // 确认按钮
            alert.addAction(UIAlertAction(
                title: "confirm_clear".localized,
                style: .destructive,
                handler: { _ in
                    // 发送通知以清空数据库
                    NotificationCenter.default.post(
                        name: Notification.Name("ClearDatabase"),
                        object: nil
                    )
                }
            ))

            // 显示对话框
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {
                rootViewController.present(alert, animated: true)
            }
        }) {
            MenuRow(
                icon: "trash.fill",
                title: "clear_database".localized,
                iconColor: .red,
                action: {
                    print("[DEBUG] 清空数据库 MenuRow action 被执行")
                }
            )
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 4)
        .buttonStyle(PlainButtonStyle())
    }
}

// 版本信息
struct MenuVersionInfo: View {
    var body: some View {
        HStack {
            Spacer()
            VStack(spacing: 4) {
                Text("NaviBatch")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)

                Text(String(format: "version_info".localized,
                    Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                    Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"))
                    .font(.system(size: 12))
                    .foregroundColor(.secondary.opacity(0.8))
            }
            Spacer()
        }
        .padding(.top, 32)
        .padding(.bottom, 24)
    }
}

// 扩展UIViewController以查找导航控制器
extension UIViewController {
    func findNavigationController() -> UINavigationController? {
        // 如果当前控制器就是导航控制器，直接返回
        if let navController = self as? UINavigationController {
            return navController
        }

        // 检查当前控制器的导航控制器
        if let navController = self.navigationController {
            return navController
        }

        // 递归检查父视图控制器
        if let parent = self.parent {
            return parent.findNavigationController()
        }

        // 检查presented控制器
        if let presented = self.presentedViewController {
            return presented.findNavigationController()
        }

        // 如果是TabBarController，检查选中的控制器
        if let tabBarController = self as? UITabBarController,
           let selectedVC = tabBarController.selectedViewController {
            return selectedVC.findNavigationController()
        }

        // 如果是容器控制器，检查子控制器
        for child in self.children {
            if let navController = child.findNavigationController() {
                return navController
            }
        }

        return nil
    }
}

// 菜单通知处理器
struct MenuNotificationHandler: ViewModifier {
    var refreshData: () -> Void
    var onDismiss: () -> Void
    @Binding var showSubscriptionPrompt: Bool
    @Binding var showSubscriptionView: Bool

    func body(content: Content) -> some View {
        content
            .task {
                // 设置所有需要的通知观察者
                setupNotificationObservers()
            }
            // 添加新的通知监听
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("CloseModernSubscriptionView"))) { _ in
                print("[DEBUG] MenuNotificationHandler - 接收到CloseModernSubscriptionView通知")
                withAnimation {
                    showSubscriptionPrompt = false
                    showSubscriptionView = false
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ShowSubscriptionPrompt"))) { _ in
                print("[DEBUG] MenuNotificationHandler - 接收到ShowSubscriptionPrompt通知")
                withAnimation {
                    showSubscriptionPrompt = true
                }
            }

    }

    private func setupNotificationObservers() {
        // 数据刷新相关通知
        let dataChangeNotifications: [Notification.Name] = [
            .init("RouteDataChanged"),
            .init("AddressAddedNotification"),
            .init("RefreshAddressesNotification"),
            .init("RefreshData")
        ]

        // 为每个数据变更通知添加观察者
        for notification in dataChangeNotifications {
            NotificationCenter.default.addObserver(
                forName: notification,
                object: nil,
                queue: .main
            ) { _ in
                print("[INFO] MenuView - 收到数据刷新通知: \(notification.rawValue)")
                // 稍微延迟刷新，避免在数据更新过程中刷新
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    refreshData()
                }
            }
        }

        // 关闭菜单通知
        NotificationCenter.default.addObserver(
            forName: .init("CloseMenuAndShowRoute"),
            object: nil,
            queue: .main
        ) { _ in
            print("[INFO] MenuView - 收到关闭菜单通知")
            DispatchQueue.main.async {
                onDismiss()
            }
        }

        // DismissMenu通知
        NotificationCenter.default.addObserver(
            forName: .init("DismissMenu"),
            object: nil,
            queue: .main
        ) { _ in
            print("[DEBUG] MenuNotificationHandler - 接收到DismissMenu通知")
            DispatchQueue.main.async {
                onDismiss()
            }
        }
    }
}

struct PreferencesView: View {
    @State private var showLanguageSettings = false
    @State private var showLocationResetAlert = false
    @ObservedObject private var localizationManager = LocalizationManager.shared
    @ObservedObject private var locationManager = LocationManager.shared

    // 导航应用选择状态 - 只使用Apple Maps
    @AppStorage("preferredNavigationApp") private var preferredNavigationApp = "Apple Maps"
    let navigationAppOptions = ["Apple Maps"]

    // 距离单位选择状态
    @AppStorage("preferredDistanceUnit") private var preferredDistanceUnit = "km"
    let distanceUnitOptions = ["km", "mi", "m"]

    // 移除Google Maps相关状态

    // 移除Google Maps安装检查函数

    var body: some View {
        List {
            // 应用偏好设置
            Section(header: Text("app_preferences".localized)) {
                // 语言设置 - 使用Button直接触发Sheet
                Button(action: {
                    showLanguageSettings = true
                }) {
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(.blue)
                        Text("language_settings".localized)
                        Spacer()
                        Text(localizationManager.selectedLanguage.displayName)
                            .foregroundColor(.secondary)
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .foregroundColor(.primary)

                // 重置为系统语言按钮
                Button(action: {
                    localizationManager.resetToSystemLanguage()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                            .foregroundColor(.blue)
                        Text("reset_to_system_language".localized)
                        Spacer()
                    }
                }

                // 导航应用显示 - 只显示Apple Maps，不可选择
                HStack {
                    Image(systemName: "map")
                        .foregroundColor(.blue)
                    Text("navigation_app".localized)
                    Spacer()
                    Text("apple_maps".localized)
                        .foregroundColor(.secondary)
                }

                // 距离单位选择
                NavigationLink {
                    // 距离单位选择视图
                    List {
                        ForEach(distanceUnitOptions, id: \.self) { unit in
                            Button(action: {
                                preferredDistanceUnit = unit
                            }) {
                                HStack {
                                    Text(unit)
                                    Spacer()
                                    if preferredDistanceUnit == unit {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.blue)
                                    }
                                }
                            }
                            .foregroundColor(.primary)
                        }
                    }
                    .navigationTitle("distance_unit".localized)
                } label: {
                    HStack {
                        Image(systemName: "ruler")
                            .foregroundColor(.blue)
                        Text("distance_unit".localized)
                        Spacer()
                        Text(preferredDistanceUnit)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // 位置设置
            Section(header: Text("location_settings".localized)) {
                // 当前区域显示
                HStack {
                    Image(systemName: "location")
                        .foregroundColor(.green)
                    Text("current_region".localized)
                    Spacer()
                    Text(locationManager.getCurrentRegion().uppercased())
                        .foregroundColor(.secondary)
                }

                // 重置位置设置按钮
                Button(action: {
                    showLocationResetAlert = true
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                            .foregroundColor(.orange)
                        Text("reset_location_settings".localized)
                        Spacer()
                    }
                }
                .foregroundColor(.primary)
            }

            // 关于语言的说明
            Section(header: Text("info".localized), footer: Text("language_info_description".localized)) {
                // 显示当前选择的语言
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                    Text("current_language".localized)
                    Spacer()
                    Text(localizationManager.selectedLanguage.displayName)
                        .foregroundColor(.secondary)
                }
            }

            // 其他设置部分可以在这里添加
        }
        .navigationTitle("settings".localized)
        .sheet(isPresented: $showLanguageSettings) {
            LanguageSettingsView()
        }
        .alert("reset_location_settings".localized, isPresented: $showLocationResetAlert) {
            Button("cancel".localized, role: .cancel) { }
            Button("reset".localized, role: .destructive) {
                resetLocationSettings()
            }
        } message: {
            Text("reset_location_settings_description".localized)
        }
        .onAppear {
            // 移除Google Maps检查
        }
        .onDisappear {
            // 离开页面时清除缓存状态
            print("[DEBUG] PreferencesView - 视图消失，清除缓存状态")
        }
    }

    // 重置位置设置
    private func resetLocationSettings() {
        locationManager.resetRegionSettings()

        // 显示成功提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 可以添加成功提示的逻辑
        }
    }
}

struct ContactUsView: View {
    var body: some View {
        List {
            Section {
                VStack(alignment: .center, spacing: 15) {
                    Image(systemName: "bubble.left.and.bubble.right.fill")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 70, height: 70)
                        .foregroundColor(.green)
                        .padding(.top, 20)

                    Text("contact_us_header".localized)
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("contact_us_subheader".localized)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.bottom, 20)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity)
                .listRowInsets(EdgeInsets())
                .listRowBackground(Color.clear)
            }

            Section(header: Text("contact_options".localized)) {
                // 邮件联系选项
                Button(action: {
                    if let url = URL(string: "mailto:<EMAIL>") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "envelope.fill")
                            .foregroundColor(.blue)
                            .frame(width: 25)
                        Text("email_us".localized)
                        Spacer()
                        Text("<EMAIL>")
                            .foregroundColor(.secondary)
                            .font(.footnote)
                    }
                }

                // 网站联系表单
                Link(destination: URL(string: "https://www.navibatch.com/contact")!) {
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(.blue)
                            .frame(width: 25)
                        Text("contact_form".localized)
                        Spacer()
                        Image(systemName: "arrow.up.right.square")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // 帮助中心内容
            Section(header: Text("common_questions".localized)) {
                NavigationLink(destination: HelpTopicView(title: "how_to_use".localized, content: "help_howto_content".localized)) {
                    HelpRowView(icon: "questionmark.circle", title: "how_to_use".localized)
                }

                NavigationLink(destination: HelpTopicView(title: "subscription_faq".localized, content: "help_subscription_content".localized)) {
                    HelpRowView(icon: "creditcard", title: "subscription_faq".localized)
                }

                NavigationLink(destination: HelpTopicView(title: "navigation_help".localized, content: "help_navigation_content".localized)) {
                    HelpRowView(icon: "map", title: "navigation_help".localized)
                }

                NavigationLink(destination: HelpTopicView(title: "troubleshooting".localized, content: "help_troubleshooting_content".localized)) {
                    HelpRowView(icon: "wrench", title: "troubleshooting".localized)
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("contact_and_support".localized)
    }
}

// 帮助主题视图
struct HelpTopicView: View {
    let title: String
    let content: String

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                Text(content)
                    .padding()
            }
        }
        .navigationTitle(title)
    }
}

// 帮助行视图组件
struct HelpRowView: View {
    let icon: String
    let title: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 25)
            Text(title)
            Spacer()
        }
    }
}

// 关于应用视图
struct AboutAppView: View {
    var body: some View {
        List {
            Section {
                // 应用信息
                VStack(alignment: .center, spacing: 16) {
                    Text("NaviBatch")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .padding(.top, 30)

                    Text(String(format: "version_info".localized,
                        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"))
                        .font(.footnote)
                        .foregroundColor(.secondary)
                        .padding(.bottom, 20)
                }
                .frame(maxWidth: .infinity)
                .listRowInsets(EdgeInsets())
                .listRowBackground(Color.clear)
            }

            Section {
                // 评价应用
                Button(action: {
                    // 评价应用的操作
                    print("[DEBUG] 给我们评分按钮被点击")

                    // 添加触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()

                    // 打开 App Store 评分页面
                    if let appStoreURL = URL(string: "itms-apps://itunes.apple.com/app/id1234567890?action=write-review") {
                        UIApplication.shared.open(appStoreURL)
                    }
                }) {
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .frame(width: 25)
                        Text("rate_us".localized)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // 分享应用
                Button(action: {
                    // 分享应用的操作
                    print("[DEBUG] 分享应用按钮被点击")

                    // 添加触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .light)
                    generator.impactOccurred()

                    // 分享应用
                    let shareText = "share_app_text".localized
                    let shareURL = URL(string: "https://apps.apple.com/app/id1234567890")!

                    let activityViewController = UIActivityViewController(
                        activityItems: [shareText, shareURL],
                        applicationActivities: nil
                    )

                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.present(activityViewController, animated: true)
                    }
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.blue)
                            .frame(width: 25)
                        Text("share_app".localized)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            } header: {
                Text("actions".localized)
            }

            Section {
                // 隐私政策
                Link(destination: URL(string: "https://www.navibatch.com/privacy.html")!) {
                    HStack {
                        Image(systemName: "hand.raised")
                            .foregroundColor(.blue)
                            .frame(width: 25)
                        Text("privacy_policy".localized)
                        Spacer()
                        Image(systemName: "arrow.up.right.square")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // 使用条款
                Link(destination: URL(string: "https://www.navibatch.com/terms.html")!) {
                    HStack {
                        Image(systemName: "doc.text")
                            .foregroundColor(.blue)
                            .frame(width: 25)
                        Text("terms_of_use".localized)
                        Spacer()
                        Image(systemName: "arrow.up.right.square")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            } header: {
                Text("legal".localized)
            }

            Section {
                Text("© 2025 NaviBatch. All rights reserved.")
                    .font(.footnote)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .listRowBackground(Color.clear)
            }
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("about_app".localized)
    }
}

// 用于刷新UI的ViewModel
class MenuViewModel: ObservableObject {
    // 这个类只用于提供ObservableObject协议，以便我们可以使用objectWillChange.send()
}

// MARK: - Apple原生风格菜单组件

// 原生用户状态行
struct NativeUserStatusRow: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showSubscriptionView = false

    var body: some View {
        Button(action: {
            if subscriptionManager.currentTier == .free {
                showSubscriptionView = true
            }
        }) {
            HStack {
                Label {
                    VStack(alignment: .leading, spacing: 1) {
                        Text(statusTitle)
                            .font(.body)
                            .foregroundColor(.primary)
                        Text(statusDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } icon: {
                    Image(systemName: statusIcon)
                        .foregroundColor(statusColor)
                }

                Spacer()

                if subscriptionManager.currentTier != .free {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.title3)
                }
            }
            .padding(.vertical, 2)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showSubscriptionView) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
    }

    private var statusColor: Color {
        switch subscriptionManager.currentTier {
        case .free: return .gray
        case .pro: return .blue
        case .expert: return .purple
        }
    }

    private var statusIcon: String {
        switch subscriptionManager.currentTier {
        case .free: return "person.circle"
        case .pro: return "crown.fill"
        case .expert: return "star.circle.fill"
        }
    }

    private var statusTitle: String {
        switch subscriptionManager.currentTier {
        case .free: return "free_plan".localized
        case .pro: return "pro_plan".localized
        case .expert: return "expert_plan".localized
        }
    }

    private var statusDescription: String {
        switch subscriptionManager.currentTier {
        case .free:
            return "limited_to_20_addresses".localized
        case .pro, .expert:
            // 如果有到期时间信息，显示到期时间
            if let expirationDescription = subscriptionManager.expirationDescription() {
                return expirationDescription
            } else {
                return subscriptionManager.currentTier == .pro ? "unlimited_addresses".localized : "all_premium_features".localized
            }
        }
    }
}

// Apple风格用户状态行（旧版本）
struct AppleStyleUserStatusRow: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showSubscriptionView = false

    var body: some View {
        Button(action: {
            if subscriptionManager.currentTier == .free {
                showSubscriptionView = true
            }
        }) {
            HStack(spacing: 12) {
                // 状态图标
                ZStack {
                    Circle()
                        .fill(statusColor)
                        .frame(width: 40, height: 40)

                    Image(systemName: statusIcon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                }

                // 状态信息
                VStack(alignment: .leading, spacing: 2) {
                    Text(statusTitle)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text(statusDescription)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 状态指示器
                if subscriptionManager.currentTier == .free {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.green)
                }
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showSubscriptionView) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
    }

    private var statusColor: Color {
        switch subscriptionManager.currentTier {
        case .free: return .gray
        case .pro: return .blue
        case .expert: return .purple
        }
    }

    private var statusIcon: String {
        switch subscriptionManager.currentTier {
        case .free: return "person.circle"
        case .pro: return "crown.fill"
        case .expert: return "star.circle.fill"
        }
    }

    private var statusTitle: String {
        switch subscriptionManager.currentTier {
        case .free: return "free_plan".localized
        case .pro: return "pro_plan".localized
        case .expert: return "expert_plan".localized
        }
    }

    private var statusDescription: String {
        switch subscriptionManager.currentTier {
        case .free:
            return "limited_to_20_addresses".localized
        case .pro, .expert:
            // 如果有到期时间信息，显示到期时间
            if let expirationDescription = subscriptionManager.expirationDescription() {
                return expirationDescription
            } else {
                return subscriptionManager.currentTier == .pro ? "unlimited_addresses".localized : "all_premium_features".localized
            }
        }
    }
}

// Apple风格快速操作行
struct AppleStyleQuickActionRow: View {
    var icon: String
    var title: String
    var subtitle: String
    var color: Color
    var destination: AnyView

    var body: some View {
        NavigationLink(destination: destination) {
            HStack(spacing: 12) {
                // 图标
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(color)
                        .frame(width: 32, height: 32)

                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }

                // 文本内容
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
    }
}

// Apple风格菜单行
struct AppleStyleMenuRow: View {
    var icon: String
    var title: String
    var color: Color
    var showBadge: Bool = false
    var destination: AnyView? = nil
    var action: (() -> Void)? = nil

    var body: some View {
        Group {
            if let destination = destination {
                NavigationLink(destination: destination) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle())
            } else {
                Button(action: action ?? {}) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle())
            }
        }
    }

    private var rowContent: some View {
        HStack(spacing: 12) {
            // 图标
            ZStack {
                RoundedRectangle(cornerRadius: 6)
                    .fill(color)
                    .frame(width: 28, height: 28)

                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
            }

            // 标题
            Text(title)
                .font(.body)
                .foregroundColor(.primary)

            Spacer()

            // 徽章或箭头
            if showBadge {
                Text("NEW")
                    .font(.caption2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(Color.orange)
                    )
            }

            if destination != nil {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 2)
    }
}

// MARK: - 旧组件（待清理）

// 现代化标题栏（保留备用）
struct ModernMenuHeader: View {
    var onDismiss: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        HStack {
            // 现代化返回按钮
            Button(action: onDismiss) {
                HStack(spacing: 6) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .semibold))
                    Text("back".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.blue.opacity(0.1))
                )
            }

            Spacer()

            // 现代化标题
            VStack(spacing: 2) {
                Text("menu".localized)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.primary)

                Text("manage_your_routes".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            }

            Spacer()

            // 设置快捷按钮
            NavigationLink(destination: PreferencesView()) {
                Image(systemName: "gearshape.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.gray)
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color(.systemGray6))
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .contentShape(Circle())
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            Rectangle()
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.03), radius: 1, y: 1)
        )
    }
}

// 现代化用户状态卡片
struct ModernUserStatusCard: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @State private var showSubscriptionView = false

    var body: some View {
        Button(action: {
            if subscriptionManager.currentTier == .free {
                showSubscriptionView = true
            }
        }) {
            HStack(spacing: 16) {
                // 状态图标
                ZStack {
                    Circle()
                        .fill(statusGradient)
                        .frame(width: 50, height: 50)

                    Image(systemName: statusIcon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }

                // 状态信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(statusTitle)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.primary)

                    Text(statusDescription)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                // 状态指示器
                if subscriptionManager.currentTier == .free {
                    VStack(spacing: 4) {
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.blue)

                        Text("upgrade".localized)
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.blue)
                    }
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.green)
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showSubscriptionView) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
    }

    private var statusGradient: LinearGradient {
        switch subscriptionManager.currentTier {
        case .free:
            return LinearGradient(colors: [.gray, .gray.opacity(0.7)], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .pro:
            return LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .expert:
            return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }

    private var statusIcon: String {
        switch subscriptionManager.currentTier {
        case .free: return "person.circle"
        case .pro: return "crown.fill"
        case .expert: return "star.circle.fill"
        }
    }

    private var statusTitle: String {
        switch subscriptionManager.currentTier {
        case .free: return "free_plan".localized
        case .pro: return "pro_plan".localized
        case .expert: return "expert_plan".localized
        }
    }

    private var statusDescription: String {
        switch subscriptionManager.currentTier {
        case .free:
            return "limited_to_20_addresses".localized
        case .pro, .expert:
            // 如果有到期时间信息，显示到期时间
            if let expirationDescription = subscriptionManager.expirationDescription() {
                return expirationDescription
            } else {
                return subscriptionManager.currentTier == .pro ? "unlimited_addresses".localized : "all_premium_features".localized
            }
        }
    }
}

// 现代化快速操作卡片
struct ModernQuickActionsCard: View {
    var routeCount: Int
    var addressCount: Int
    @State private var selectedAction: QuickAction? = nil

    enum QuickAction {
        case routes, addresses
    }

    var body: some View {
        VStack(spacing: 12) {
            // 标题
            HStack {
                Text("quick_actions".localized)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.primary)
                Spacer()
            }

            // 操作按钮
            HStack(spacing: 12) {
                // 路线按钮
                NavigationLink(destination: SavedGroupsView()) {
                    QuickActionButton(
                        icon: "map.fill",
                        title: "routes".localized,
                        count: routeCount,
                        color: .blue
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle())

                // 地址按钮
                NavigationLink(destination: AddressBookView()) {
                    QuickActionButton(
                        icon: "book.fill",
                        title: "addresses".localized,
                        count: addressCount,
                        color: .green
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle())
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
    }
}

// 快速操作按钮
struct QuickActionButton: View {
    var icon: String
    var title: String
    var count: Int
    var color: Color
    @State private var isPressed = false

    var body: some View {
        VStack(spacing: 12) {
            // 图标和计数
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 60, height: 60)

                VStack(spacing: 2) {
                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(color)

                    Text("\(count)")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(color)
                }
            }

            // 标题
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        }
    }
}

// 现代化菜单功能区域
struct ModernMenuSections: View {
    @Binding var showSubscriptionPrompt: Bool
    @Binding var showSubscriptionView: Bool

    var body: some View {
        VStack(spacing: 16) {
            // 主要功能
            ModernMenuSection(title: "main_features".localized, icon: "star.fill") {
                VStack(spacing: 8) {
                    ModernMenuRow(
                        icon: "gearshape.fill",
                        title: "preferences".localized,
                        subtitle: "customize_app_settings".localized,
                        color: .blue,
                        destination: AnyView(PreferencesView())
                    )

                    ModernMenuRow(
                        icon: "crown.fill",
                        title: "upgrade_to_pro".localized,
                        subtitle: "unlock_all_features".localized,
                        color: .orange,
                        isHighlighted: true,
                        action: {
                            showSubscriptionPrompt = true
                        }
                    )
                }
            }

            // 支持与帮助
            ModernMenuSection(title: "support_help".localized, icon: "questionmark.circle.fill") {
                VStack(spacing: 8) {
                    ModernMenuRow(
                        icon: "bubble.left.fill",
                        title: "contact_us".localized,
                        subtitle: "get_help_support".localized,
                        color: .green,
                        destination: AnyView(ContactUsView())
                    )

                    ModernMenuRow(
                        icon: "info.circle.fill",
                        title: "about_app".localized,
                        subtitle: "app_info_version".localized,
                        color: .blue,
                        destination: AnyView(AboutAppView())
                    )
                }
            }

            // 开发者工具（仅调试模式 - App Store版本中完全移除）
            #if DEBUG && !APPSTORE_BUILD
            ModernMenuSection(title: "developer_tools".localized, icon: "hammer.fill") {
                ModernMenuRow(
                    icon: "hammer.fill",
                    title: "dev_tools".localized,
                    subtitle: "debug_testing_tools".localized,
                    color: .purple,
                    destination: AnyView(
                        DeveloperToolsView()
                            .environmentObject(LocalizationManager.shared)
                    )
                )
            }
            #endif
        }
    }
}

// 现代化菜单区域
struct ModernMenuSection<Content: View>: View {
    var title: String
    var icon: String
    @ViewBuilder var content: Content

    var body: some View {
        VStack(spacing: 12) {
            // 区域标题
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)

                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)

                Spacer()
            }

            // 内容
            content
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
    }
}

// 现代化菜单行
struct ModernMenuRow: View {
    var icon: String
    var title: String
    var subtitle: String
    var color: Color
    var isHighlighted: Bool = false
    var destination: AnyView? = nil
    var action: (() -> Void)? = nil

    @State private var isPressed = false

    var body: some View {
        Group {
            if let destination = destination {
                NavigationLink(destination: destination) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle())
            } else {
                Button(action: action ?? {}) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle())
            }
        }
    }

    private var rowContent: some View {
        HStack(spacing: 16) {
            // 图标
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 44, height: 44)

                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(color)
            }

            // 文本内容
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 16, weight: isHighlighted ? .semibold : .medium))
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Spacer()

            // 箭头或高亮指示器
            if isHighlighted {
                Image(systemName: "star.fill")
                    .font(.system(size: 14))
                    .foregroundColor(.orange)
            } else {
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.gray)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isHighlighted ? color.opacity(0.05) : Color(.systemGray6).opacity(0.5))
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        }
    }
}

// 现代化菜单底部
struct ModernMenuFooter: View {
    var body: some View {
        VStack(spacing: 12) {
            // 版本信息
            HStack {
                Text("version".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)

                Spacer()

                Text(AppEnvironment.versionInfoEnglish)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }

            // 版权信息
            Text("© 2025 NaviBatch. All rights reserved.")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.3))
        )
    }
}

// MARK: - 预览
#Preview("Modern Menu") {
    NavigationStack {
        MenuView(onDismiss: {})
            .environmentObject(LocalizationManager.shared)
    }
}

#Preview("Modern User Status Card") {
    ModernUserStatusCard()
        .padding()
        .background(Color(.systemGroupedBackground))
}

#Preview("Modern Quick Actions") {
    ModernQuickActionsCard(routeCount: 5, addressCount: 23)
        .padding()
        .background(Color(.systemGroupedBackground))
}

#Preview("Modern Menu Sections") {
    ModernMenuSections(
        showSubscriptionPrompt: .constant(false),
        showSubscriptionView: .constant(false)
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}

// 订阅状态卡片组件
struct MenuSubscriptionStatusCard: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @State private var isPressed = false
    @State private var showSubscriptionView = false

    var body: some View {
        HStack(spacing: 12) {
            // 左侧状态图标
            ZStack {
                Circle()
                    .fill(statusColor.opacity(0.15))
                    .frame(width: 32, height: 32)

                Image(systemName: statusIcon)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(statusColor)
            }

            // 中间状态信息
            VStack(alignment: .leading, spacing: 1) {
                HStack(spacing: 4) {
                    Text(statusTitle)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.primary)

                    if subscriptionManager.isInFreeTrial {
                        Text("trial".localized)
                            .font(.system(size: 9, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 1)
                            .background(
                                Capsule()
                                    .fill(Color.orange)
                            )
                    }
                }

                Text(statusSubtitle)
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Spacer()

            // 右侧状态指示器
            HStack(spacing: 6) {
                if subscriptionManager.currentTier != .free || subscriptionManager.isInFreeTrial {
                    // 显示到期时间或试用剩余时间
                    if subscriptionManager.isInFreeTrial, let endDate = subscriptionManager.freeTrialEndDate {
                        let daysLeft = Calendar.current.dateComponents([.day], from: Date(), to: endDate).day ?? 0
                        VStack(spacing: 0) {
                            Text("\(max(0, daysLeft))")
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(statusColor)
                            Text("days_left".localized)
                                .font(.system(size: 8, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 18))
                            .foregroundColor(.green)
                    }
                } else {
                    // 免费版显示升级箭头
                    Image(systemName: "arrow.up.circle")
                        .font(.system(size: 18))
                        .foregroundColor(.blue)
                }

                // 右侧箭头指示器
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.gray)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isPressed ? pressedBackgroundColor : cardBackgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(borderColor, lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .contentShape(Rectangle())
        .onTapGesture {
            // 触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            // 按压动画
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }

                // 显示订阅视图
                showSubscriptionView = true
            }
        }
        .sheet(isPresented: $showSubscriptionView) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
    }

    // 计算属性
    private var statusIcon: String {
        if subscriptionManager.isInFreeTrial {
            return "clock.badge.checkmark"
        }

        switch subscriptionManager.currentTier {
        case .free:
            return "person.circle"
        case .pro:
            return "crown.fill"
        case .expert:
            return "star.circle.fill"
        }
    }

    private var statusColor: Color {
        if subscriptionManager.isInFreeTrial {
            return .orange
        }

        switch subscriptionManager.currentTier {
        case .free:
            return .gray
        case .pro:
            return .blue
        case .expert:
            return .purple
        }
    }

    private var statusTitle: String {
        if subscriptionManager.isInFreeTrial {
            return "free_trial".localized
        }

        switch subscriptionManager.currentTier {
        case .free:
            return "free_plan".localized
        case .pro:
            return "pro_plan".localized
        case .expert:
            return "expert_plan".localized
        }
    }

    private var statusSubtitle: String {
        if subscriptionManager.isInFreeTrial {
            if let endDate = subscriptionManager.freeTrialEndDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return String(format: "trial_expires_on".localized, formatter.string(from: endDate))
            }
            return "trial_active".localized
        }

        switch subscriptionManager.currentTier {
        case .free:
            return "free_plan_description".localized
        case .pro, .expert:
            // 如果有到期时间信息，显示详细的到期时间
            if let formattedDate = subscriptionManager.formattedExpirationDate() {
                return "subscription_active_until".localized(with: formattedDate)
            } else {
                return subscriptionManager.currentTier == .pro ? "pro_plan_active".localized : "expert_plan_active".localized
            }
        }
    }

    private var cardBackgroundColor: Color {
        if subscriptionManager.isInFreeTrial {
            return colorScheme == .dark ? Color.orange.opacity(0.1) : Color.orange.opacity(0.05)
        }

        switch subscriptionManager.currentTier {
        case .free:
            return colorScheme == .dark ? Color(.systemBackground) : Color.white
        case .pro:
            return colorScheme == .dark ? Color.blue.opacity(0.1) : Color.blue.opacity(0.05)
        case .expert:
            return colorScheme == .dark ? Color.purple.opacity(0.1) : Color.purple.opacity(0.05)
        }
    }

    private var borderColor: Color {
        if subscriptionManager.isInFreeTrial {
            return Color.orange.opacity(0.2)
        }

        switch subscriptionManager.currentTier {
        case .free:
            return Color.gray.opacity(0.2)
        case .pro:
            return Color.blue.opacity(0.2)
        case .expert:
            return Color.purple.opacity(0.2)
        }
    }

    private var pressedBackgroundColor: Color {
        if subscriptionManager.isInFreeTrial {
            return colorScheme == .dark ? Color.orange.opacity(0.2) : Color.orange.opacity(0.1)
        }

        switch subscriptionManager.currentTier {
        case .free:
            return colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.1)
        case .pro:
            return colorScheme == .dark ? Color.blue.opacity(0.2) : Color.blue.opacity(0.1)
        case .expert:
            return colorScheme == .dark ? Color.purple.opacity(0.2) : Color.purple.opacity(0.1)
        }
    }
}

// 添加自定义视图修饰符，解决iOS 17 onChange API变更
extension View {
    @ViewBuilder
    func onValueChange<T: Equatable>(of value: T, perform action: @escaping (T) -> Void) -> some View {
        if #available(iOS 17.0, *) {
            self.onChange(of: value) { oldValue, newValue in
                action(newValue)
            }
        } else {
            self.onChange(of: value) { newValue in
                action(newValue)
            }
        }
    }
}
