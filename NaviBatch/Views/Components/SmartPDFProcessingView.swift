//
//  SmartPDFProcessingView.swift
//  NaviBatch
//
//  Created by AI Assistant on 2025-06-23.
//  智能PDF处理界面 - 提供直观的PDF上传和处理体验
//

import SwiftUI
import UniformTypeIdentifiers

struct SmartPDFProcessingView: View {

    // MARK: - Properties

    @StateObject private var processor = BatchProcessingQueue()
    @State private var selectedPDF: Data?
    @State private var selectedFileName: String = ""
    @State private var analysisResult: PDFAnalysisResult?
    @State private var showingFilePicker = false
    @State private var showingResults = false
    @State private var finalResults: [String] = []

    let appType: DeliveryAppType
    let onComplete: ([String]) -> Void

    // MARK: - Computed Properties

    private var isProcessingIdle: Bool {
        switch processor.processingStatus {
        case .idle:
            return true
        default:
            return false
        }
    }

    // MARK: - Body

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 标题区域
                    headerSection

                    // PDF选择区域
                    pdfSelectionSection

                    // 分析结果展示
                    if let analysis = analysisResult {
                        analysisResultSection(analysis)
                    }

                    // 处理进度
                    if processor.isProcessing || !isProcessingIdle {
                        processingProgressSection
                    }

                    // 处理按钮
                    if selectedPDF != nil && !processor.isProcessing {
                        processButtonSection
                    }

                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .navigationTitle("智能PDF处理")
            .navigationBarTitleDisplayMode(.large)
            .fileImporter(
                isPresented: $showingFilePicker,
                allowedContentTypes: [UTType.pdf],
                allowsMultipleSelection: false
            ) { result in
                handleFileSelection(result)
            }
            .sheet(isPresented: $showingResults) {
                Text("处理完成！识别了\(finalResults.count)个地址")
                    .padding()
                    .onAppear {
                        onComplete(finalResults)
                    }
            }
        }
    }

    // MARK: - View Components

    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.blue)

            Text("智能PDF批量处理")
                .font(.title2)
                .fontWeight(.bold)

            Text("上传任意大小的PDF，系统自动分批处理，确保100%识别率")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 20)
    }

    private var pdfSelectionSection: some View {
        VStack(spacing: 16) {
            // PDF选择卡片
            Button(action: { showingFilePicker = true }) {
                VStack(spacing: 16) {
                    if selectedPDF == nil {
                        // 未选择状态
                        VStack(spacing: 12) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.blue)

                            Text("选择PDF文件")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Text("支持任意大小的PDF文件")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 120)
                    } else {
                        // 已选择状态
                        VStack(spacing: 12) {
                            Image(systemName: "doc.fill")
                                .font(.system(size: 32))
                                .foregroundColor(.green)

                            Text(selectedFileName)
                                .font(.headline)
                                .foregroundColor(.primary)
                                .lineLimit(2)

                            if let data = selectedPDF {
                                Text("文件大小: \(formatFileSize(Int64(data.count)))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Text("点击重新选择")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 120)
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(selectedPDF == nil ? Color.blue.opacity(0.3) : Color.green.opacity(0.5),
                                       style: StrokeStyle(lineWidth: 2, dash: selectedPDF == nil ? [8, 4] : []))
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    private func analysisResultSection(_ analysis: PDFAnalysisResult) -> some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.doc.horizontal")
                    .foregroundColor(.blue)
                Text("PDF分析结果")
                    .font(.headline)
                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                AnalysisCard(
                    icon: "doc.text",
                    title: "页数",
                    value: "\(analysis.totalPages)",
                    color: .blue
                )

                AnalysisCard(
                    icon: "location",
                    title: "预计地址",
                    value: "\(analysis.estimatedAddressCount)",
                    color: .green
                )

                AnalysisCard(
                    icon: "square.stack.3d.up",
                    title: "处理批次",
                    value: "\(analysis.optimalBatchCount)",
                    color: .orange
                )

                AnalysisCard(
                    icon: "clock",
                    title: "预计时间",
                    value: "\(Int(analysis.estimatedProcessingTime))秒",
                    color: .purple
                )
            }

            // 处理策略说明
            HStack {
                Image(systemName: "gear")
                    .foregroundColor(.secondary)
                Text("处理策略: \(analysis.processingStrategy.displayName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
        }
        .padding(16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var processingProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "gearshape.2")
                    .foregroundColor(.blue)
                Text("处理进度")
                    .font(.headline)
                Spacer()
            }

            // 状态显示
            HStack {
                Circle()
                    .fill(processor.isProcessing ? Color.green : Color.gray)
                    .frame(width: 8, height: 8)

                Text(processor.processingStatus.displayText)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()
            }

            // 进度条
            if processor.totalBatches > 0 {
                VStack(spacing: 8) {
                    ProgressView(value: processor.processingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))

                    HStack {
                        Text(String(format: "batch_progress".localized, processor.currentBatch, processor.totalBatches))
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text("\(Int(processor.processingProgress * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                }
            }

            // 状态消息
            if !processor.statusMessage.isEmpty {
                Text(processor.statusMessage)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var processButtonSection: some View {
        Button(action: startProcessing) {
            HStack {
                Image(systemName: "play.fill")
                Text("开始智能处理")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(selectedPDF == nil || processor.isProcessing)
    }

    // MARK: - Methods

    private func handleFileSelection(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }

            do {
                let data = try Data(contentsOf: url)
                selectedPDF = data
                selectedFileName = url.lastPathComponent

                // 立即分析PDF
                analyzePDF(data)

                Logger.info("📁 PDF文件选择成功: \(selectedFileName), 大小: \(formatFileSize(Int64(data.count)))")

            } catch {
                Logger.error("❌ PDF文件读取失败: \(error)")
            }

        case .failure(let error):
            Logger.error("❌ PDF文件选择失败: \(error)")
        }
    }

    private func analyzePDF(_ data: Data) {
        let analyzer = PDFIntelligentAnalyzer()
        analysisResult = analyzer.analyzePDF(data)

        Logger.info("📊 PDF分析完成: \(analysisResult?.estimatedAddressCount ?? 0)个地址")
    }

    private func startProcessing() {
        guard let pdfData = selectedPDF else { return }

        Task {
            let results = await processor.processPDF(pdfData, appType: appType)

            await MainActor.run {
                finalResults = results
                if !results.isEmpty {
                    showingResults = true
                }
            }
        }
    }

    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - Supporting Views

struct AnalysisCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 80)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

// MARK: - Extensions

extension PDFAnalysisResult.ProcessingStrategy {
    var displayName: String {
        switch self {
        case .direct: return "直接处理"
        case .pageBased: return "按页面分批"
        case .contentBased: return "按内容分批"
        case .hybrid: return "混合策略"
        }
    }
}
