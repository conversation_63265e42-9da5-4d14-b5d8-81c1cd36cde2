import SwiftUI
import CoreLocation
import os.log
import SwiftData
import UIKit
import UniformTypeIdentifiers
import Combine
import Foundation
import MapKit

// 定义Apple地理编码选项常量
private let CLGeocodeAppleOptionCountryKey = "country"
private let CLGeocodeAppleOptionStateKey = "state"
private let CLGeocodeAppleOptionPostalCodeKey = "postalCode"

// 警告项
fileprivate struct FileImportAlertItem: Identifiable {
    let id = UUID()
    let title: String
    let message: String
    let primaryButton: Alert.Button
    let secondaryButton: Alert.Button?
    let cancelButton: Alert.Button?

    // 基本初始化方法，只有一个按钮
    init(title: String = NSLocalizedString("prompt", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "提示", comment: ""), message: String, primaryButton: Alert.Button = .default(Text(NSLocalizedString("confirm", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "确认", comment: "")))) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = nil
        self.cancelButton = nil
    }

    // 带两个按钮的初始化方法
    init(title: String = NSLocalizedString("prompt", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "提示", comment: ""), message: String, primaryButton: Alert.Button, cancelButton: Alert.Button) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = nil
        self.cancelButton = cancelButton
    }

    // 带三个按钮的初始化方法
    init(title: String = NSLocalizedString("prompt", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "提示", comment: ""), message: String, primaryButton: Alert.Button, secondaryButton: Alert.Button, cancelButton: Alert.Button) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = secondaryButton
        self.cancelButton = cancelButton
    }
}

/// 文件导入表单
/// 支持从CSV文件导入地址数据
struct FileImportSheet: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isImporting: Bool = false
    @State private var isProcessing: Bool = false
    @State private var importedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = [] // 地址, 坐标, 是否选中, 警告信息
    @State private var processingProgress: Double = 0
    @State private var errorMessage: String? = nil

    // 详细进度信息
    @State private var currentBatch: Int = 0
    @State private var totalBatches: Int = 0
    @State private var processedCount: Int = 0
    @State private var totalCount: Int = 0
    @State private var successCount: Int = 0
    @State private var warningCount: Int = 0
    @State private var currentAddress: String = ""
    @State private var estimatedTimeRemaining: TimeInterval = 0
    @State private var processingStartTime: Date?
    @State private var alertItem: FileImportAlertItem? = nil
    @State private var showLimitExceededSheet: Bool = false
    @State private var limitExceededInfo: (currentCount: Int, remainingSlots: Int, maxAllowed: Int, selectedCount: Int, selectedAddresses: [(String, CLLocationCoordinate2D, String)])? = nil
    @State private var showSubscriptionView = false
    // 保留fileType变量但不再使用选择器，自动检测文件类型
    @State private var fileType: FileType = .csv // 默认值，实际上不再使用
    @State private var showingDocumentPicker: Bool = false
    @State private var companyName: String = ""

    // 获取用户位置
    @ObservedObject private var locationManager = LocationManager.shared

    // 回调函数
    var onAddressesImported: ([(String, CLLocationCoordinate2D, String)]) -> Void

    // 文件类型
    enum FileType: String, CaseIterable, Identifiable {
        case csv = "CSV"
        case txt = "TXT"
        case json = "JSON"

        var id: String { self.rawValue }

        var utType: UTType {
            switch self {
            case .csv: return UTType.commaSeparatedText
            case .txt: return UTType.plainText
            case .json: return UTType.json
            }
        }

        var icon: String {
            switch self {
            case .csv: return "tablecells"
            case .txt: return "doc.text"
            case .json: return "curlybraces"
            }
        }
    }

    // 计算选中的地址数量
    private var selectedAddressCount: Int {
        importedAddresses.filter { $0.2 }.count
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {

                VStack(spacing: 16) {
                // 支持的文件类型信息
                Text("supports_file_types".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)

                // 示例文件下载区域
                VStack(spacing: 12) {
                    // 标题和说明 - 居中对齐
                    VStack(spacing: 4) {
                        HStack {
                            Spacer()

                            Image(systemName: "arrow.down.doc")
                                .foregroundColor(.blue)
                                .font(.caption)

                            Text("download_sample_files".localized)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)

                            Spacer()
                        }

                        Text("sample_files_help_text".localized)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal)

                    // 示例文件下载按钮
                    HStack(spacing: 12) {
                        ForEach(FileType.allCases, id: \.id) { fileType in
                            Button(action: {
                                downloadSampleFile(for: fileType)
                            }) {
                                VStack(spacing: 4) {
                                    HStack(spacing: 4) {
                                        Image(systemName: "arrow.down.circle")
                                            .font(.caption2)
                                        Image(systemName: fileType.icon)
                                            .font(.caption2)
                                    }

                                    Text("sample_file_format".localized(with: fileType.rawValue.uppercased()))
                                        .font(.caption2)
                                        .fontWeight(.medium)
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 6)
                                .background(Color.green.opacity(0.1))
                                .foregroundColor(.green)
                                .cornerRadius(6)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
                .background(Color(.systemGray6).opacity(0.5))
                .cornerRadius(12)
                .padding(.horizontal)

                // 文件选择区域
                VStack {
                    if isProcessing {
                        // 详细进度显示
                        DetailedProgressView(
                            progress: processingProgress,
                            currentBatch: currentBatch,
                            totalBatches: totalBatches,
                            processedCount: processedCount,
                            totalCount: totalCount,
                            successCount: successCount,
                            warningCount: warningCount,
                            currentAddress: currentAddress,
                            estimatedTimeRemaining: estimatedTimeRemaining
                        )
                        .padding()
                    } else if !importedAddresses.isEmpty {
                        // 已导入地址列表
                        VStack(alignment: .leading) {
                            HStack {
                                Text(String(format: NSLocalizedString("imported_addresses_count", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "已导入 %d 个地址", comment: ""), importedAddresses.count))
                                    .font(.headline)

                                Spacer()

                                // 全选/全不选切换按钮 - 使用单个切换按钮
                                Toggle(isOn: Binding(
                                    get: { importedAddresses.allSatisfy { $0.2 } },
                                    set: { selectAllAddresses($0) }
                                )) {
                                    Text(NSLocalizedString("select_all", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "全选", comment: ""))
                                        .font(.subheadline)
                                        .foregroundColor(.blue)
                                }
                                .toggleStyle(SwitchToggleStyle(tint: .blue))
                                .labelsHidden()
                                .fixedSize()
                            }
                            .padding(.horizontal)

                            // 地址列表
                            List {
                                ForEach(0..<importedAddresses.count, id: \.self) { index in
                                    let (address, coordinate, isSelected, warning) = importedAddresses[index]
                                    HStack(spacing: 10) {
                                        // 使用Toggle替代Button，更符合iOS设计规范
                                        Toggle(isOn: Binding(
                                            get: { isSelected },
                                            set: { _ in toggleAddressSelection(at: index) }
                                        )) {}
                                        .toggleStyle(CheckboxToggleStyle())
                                        .labelsHidden()

                                        // 地址信息 - 简化显示
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(address)
                                                .lineLimit(1)
                                                .font(.subheadline)

                                            // 坐标信息和警告信息在同一行
                                            HStack(spacing: 6) {
                                                // 简化坐标显示
                                                Text(String(format: "(%.4f, %.4f)", coordinate.latitude, coordinate.longitude))
                                                    .font(.caption)
                                                    .foregroundColor(.secondary)

                                                // 如果有警告，显示警告图标和文本
                                                if !warning.isEmpty || (coordinate.latitude == 0 && coordinate.longitude == 0) {
                                                    Image(systemName: "exclamationmark.triangle")
                                                        .foregroundColor(.orange)
                                                        .font(.caption)

                                                    if !warning.isEmpty {
                                                        Text(warning)
                                                            .font(.caption)
                                                            .foregroundColor(.orange)
                                                            .lineLimit(1)
                                                    }
                                                }
                                            }

                                            // 显示与用户位置的距离（如果有）
                                            if let userLocation = locationManager.userLocation {
                                                let userLocationObj = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
                                                let pointLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
                                                let distance = userLocationObj.distance(from: pointLocation)

                                                Text(formatDistance(distance))
                                                    .font(.caption)
                                                    .foregroundColor(distance <= 20_000_000 ? .green : .orange)
                                            }
                                        }

                                        Spacer()
                                    }
                                    .padding(.vertical, 2)
                                }
                            }
                            .listStyle(PlainListStyle())
                        }
                    } else {
                        // 文件选择按钮
                        Button(action: {
                            // 直接触发文件选择器
                            showingDocumentPicker = true
                        }) {
                            VStack(spacing: 16) {
                                Image(systemName: "doc.badge.plus")
                                    .font(.system(size: 50))
                                    .foregroundColor(.blue)

                                Text(NSLocalizedString("tap_to_select_file", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "点击选择文件", comment: ""))
                                    .font(.headline)
                                    .foregroundColor(.blue)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 50)
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.horizontal)
                        // 移除自动打开文件选择器的代码
                        .onAppear {
                            Task {
                                await logInfo("FileImportSheet - 视图已加载，等待用户选择文件")
                            }
                        }
                    }
                }
                .frame(maxHeight: .infinity)

                // 公司名称输入
                if !importedAddresses.isEmpty {
                    VStack(alignment: .leading) {
                        Text(NSLocalizedString("company_name_optional", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "公司名称（可选）", comment: ""))
                            .font(.caption)
                            .foregroundColor(.secondary)

                        TextField(NSLocalizedString("input_company_name", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "输入公司名称", comment: ""), text: $companyName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    .padding(.horizontal)
                }

                // 移除底部按钮区域，按钮已移至导航栏
                Spacer().frame(height: 16)
                }
            }
            .navigationTitle("import_addresses".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 左侧：取消按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }

                // 右侧：导入按钮（仅在有导入地址时显示）
                if !importedAddresses.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(NSLocalizedString("import", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入", comment: "")) {
                            // 添加防重复点击保护
                            guard !isProcessing else { return }
                            isProcessing = true

                            // 使用Task确保异步处理正确
                            Task {
                                // 添加小延迟，让UI更新有时间响应
                                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                                await MainActor.run {
                                    importSelectedAddresses()
                                    // 重置处理状态
                                    isProcessing = false
                                }
                            }
                        }
                        .disabled(selectedAddressCount == 0 || isProcessing)
                        // 🎯 优化确认按钮的视觉设计，使其更加突出和明显
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(
                                    (selectedAddressCount == 0 || isProcessing)
                                    ? Color.gray.opacity(0.5)
                                    : Color.blue
                                )
                        )
                        .shadow(
                            color: (selectedAddressCount == 0 || isProcessing)
                            ? Color.clear
                            : Color.blue.opacity(0.3),
                            radius: 4,
                            x: 0,
                            y: 2
                        )
                        .scaleEffect((selectedAddressCount == 0 || isProcessing) ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: selectedAddressCount == 0 || isProcessing)
                    }
                }
            }
        }

            .alert(item: $alertItem) { item in
                if let secondaryButton = item.secondaryButton, let _ = item.cancelButton {
                    // 三个按钮的警告框
                    return Alert(
                        title: Text(item.title),
                        message: Text(item.message),
                        primaryButton: item.primaryButton,
                        secondaryButton: secondaryButton
                    )
                } else if let secondaryButton = item.cancelButton {
                    // 两个按钮的警告框
                    return Alert(
                        title: Text(item.title),
                        message: Text(item.message),
                        primaryButton: item.primaryButton,
                        secondaryButton: secondaryButton
                    )
                } else {
                    // 单个按钮的警告框
                    return Alert(
                        title: Text(item.title),
                        message: Text(item.message),
                        dismissButton: item.primaryButton
                    )
                }
            }
            .fileImporter(
                isPresented: $showingDocumentPicker,
                allowedContentTypes: FileType.allCases.map { $0.utType },
                allowsMultipleSelection: false
            ) { result in
                handleFileImport(result)
            }
            .onDisappear {
                // 清理资源
                cleanupResources()
            }
            .sheet(isPresented: $showLimitExceededSheet) {
                if let info = limitExceededInfo {
                    FileImportAddressLimitExceededSheet(
                        currentCount: info.currentCount,
                        remainingSlots: info.remainingSlots,
                        maxAllowed: info.maxAllowed,
                        selectedCount: info.selectedCount,
                        isFreeUser: SubscriptionManager.shared.currentTier == .free,
                        selectedAddresses: info.selectedAddresses,
                        onImportLimited: { limitedAddresses in
                            // 记录实际导入的地址数量
                            Task {
                                await logInfo("FileImportSheet - 导入地址数量限制为\(info.remainingSlots)个，实际导入\(limitedAddresses.count)个")
                            }
                            if !limitedAddresses.isEmpty {
                                self.onAddressesImported(limitedAddresses)

                                // 🚨 检查并提示问题地址
                                Task {
                                    // 延迟一下让地址处理完成
                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                                    await MainActor.run {
                                        ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入（限制数量）")
                                    }
                                }
                            } else {
                                // 显示错误提示
                                alertItem = FileImportAlertItem(
                                    title: NSLocalizedString("import_failed", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入失败", comment: ""),
                                    message: NSLocalizedString("no_importable_addresses", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "没有可导入的地址", comment: "")
                                )
                                return
                            }
                            self.dismiss()
                        },
                        onUpgrade: {
                            // 关闭当前sheet并显示订阅界面
                            showLimitExceededSheet = false
                            showSubscriptionView = true
                        },
                        onCancel: {
                            // 关闭表单
                            showLimitExceededSheet = false
                        }
                    )
                    // 为限制超出提示表单添加半屏Sheet样式
                    .presentationDetents([.medium, .large])
                    .presentationDragIndicator(.visible)
                    .interactiveDismissDisabled(false) // 允许交互式关闭
                }
            }
            .sheet(isPresented: $showSubscriptionView) {
                // 显示订阅界面
                UnifiedSubscriptionView(config: SubscriptionViewConfig(
                    style: .modern,
                    showTitle: true,
                    showCloseButton: true,
                    showFeatureComparison: true,
                    showOneClickPromo: true,
                    presentationMode: .sheet
                ))
                .presentationDetents([.large])
                .presentationDragIndicator(.visible)
            }
        }

    // 地址限制超出提示表单
    struct FileImportAddressLimitExceededSheet: View {
        let currentCount: Int
        let remainingSlots: Int
        let maxAllowed: Int
        let selectedCount: Int
        let isFreeUser: Bool
        let selectedAddresses: [(String, CLLocationCoordinate2D, String)]
        let onImportLimited: ([(String, CLLocationCoordinate2D, String)]) -> Void
        let onUpgrade: () -> Void
        let onCancel: () -> Void

        @Environment(\.dismiss) private var dismiss

        var body: some View {
            NavigationView {
                VStack(spacing: 20) {
                    // 图标
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                        .padding()

                    // 信息
                    VStack(alignment: .leading, spacing: 10) {
                        if isFreeUser {
                            // 🎯 针对非Pro用户超过20个地址的情况，显示更清晰的信息
                            if currentCount == 0 && maxAllowed == 20 {
                                // 这是我们新的逻辑：非Pro用户选择超过20个地址
                                Text("免费版最多允许20个地址，当前已选择\(selectedCount)个地址。")
                                    .font(.body)

                                Text("您选择了\(selectedCount)个地址，超出了\(selectedCount - 20)个。")
                                    .font(.body)
                                    .foregroundColor(.red)

                                Text("升级到高级版可享受无限地址！")
                                    .font(.body)
                                    .bold()
                                    .padding(.top, 5)
                            } else {
                                // 原有的逻辑：基于当前路线的限制
                                Text("free_version_address_limit".localized(with: maxAllowed))
                                    .font(.body)

                                Text("current_address_count".localized(with: currentCount, remainingSlots))
                                    .font(.body)

                                if remainingSlots <= 0 {
                                    Text("can_import_selected".localized(with: selectedCount))
                                        .font(.body)
                                        .foregroundColor(.blue)
                                } else {
                                    Text("selected_exceeds_limit".localized(with: selectedCount, selectedCount - remainingSlots))
                                        .font(.body)
                                        .foregroundColor(.red)
                                }

                                Text("upgrade_to_premium_unlimited".localized)
                                    .font(.body)
                                    .bold()
                                    .padding(.top, 5)
                            }
                        } else {
                            Text(String(format: NSLocalizedString("route_address_limit", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "路线地址限制：最多 %d 个，剩余槽位：%d 个，最大允许：%d 个", comment: ""), currentCount, remainingSlots, maxAllowed))
                                .font(.body)

                            if remainingSlots <= 0 {
                                Text(String(format: NSLocalizedString("can_import_selected", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "您选择了%d个地址，可以导入这些地址。", comment: ""), selectedCount))
                                    .font(.body)
                                    .foregroundColor(.blue)
                            } else {
                                Text(String(format: NSLocalizedString("selected_exceeds_limit", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "您选择了%d个地址，超出可添加数量%d个。", comment: ""), selectedCount, selectedCount - remainingSlots))
                                    .font(.body)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    .padding(.horizontal)

                    Spacer()
                }
                .padding()
                .toolbar {
                    // 左侧取消按钮
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button(NSLocalizedString("cancel", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "取消", comment: "")) {
                            onCancel()
                        }
                    }

                    // 右侧按钮
                    ToolbarItem(placement: .navigationBarTrailing) {
                        // 🎯 针对非Pro用户超过20个地址的情况，优化按钮显示
                        if isFreeUser && currentCount == 0 && maxAllowed == 20 && selectedCount > 20 {
                            // 非Pro用户选择超过20个地址的情况
                            Button("导入前20个") {
                                // 创建限制后的地址列表（前20个）
                                let limitedAddresses = Array(selectedAddresses.prefix(20))
                                onImportLimited(limitedAddresses)

                                // 🚨 检查并提示问题地址
                                Task {
                                    // 延迟一下让地址处理完成
                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                                    await MainActor.run {
                                        ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入（前20个）")
                                    }
                                }
                            }
                            .foregroundColor(.blue)
                        } else if remainingSlots <= 0 {
                            // 当剩余槽位为0时，但仍然允许导入已选择的地址
                            Button(NSLocalizedString("import_selected_addresses", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入已选择的地址", comment: "")) {
                                // 直接导入所有选择的地址
                                onImportLimited(selectedAddresses)

                                // 🚨 检查并提示问题地址
                                Task {
                                    // 延迟一下让地址处理完成
                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                                    await MainActor.run {
                                        ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入（已选择）")
                                    }
                                }
                            }
                            .foregroundColor(.blue)
                        } else if selectedCount > remainingSlots {
                            Button(String(format: NSLocalizedString("import_first_n", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入前 %d 个", comment: ""), remainingSlots)) {
                                // 创建限制后的地址列表
                                let limitedAddresses = Array(selectedAddresses.prefix(remainingSlots))
                                onImportLimited(limitedAddresses)

                                // 🚨 检查并提示问题地址
                                Task {
                                    // 延迟一下让地址处理完成
                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                                    await MainActor.run {
                                        ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入（前\(remainingSlots)个）")
                                    }
                                }
                            }
                            .foregroundColor(.blue)
                        } else {
                            Button(String(format: NSLocalizedString("import_all_n", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入所有 %d 个", comment: ""), selectedCount)) {
                                // 导入所有选择的地址
                                onImportLimited(selectedAddresses)

                                // 🚨 检查并提示问题地址
                                Task {
                                    // 延迟一下让地址处理完成
                                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                                    await MainActor.run {
                                        ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入（全部\(selectedCount)个）")
                                    }
                                }
                            }
                            .foregroundColor(.blue)
                        }
                    }
                }
                .toolbar {
                    // 底部工具栏 - 仅显示升级按钮（如果是免费用户）
                    ToolbarItemGroup(placement: .bottomBar) {
                        if isFreeUser {
                            Spacer()
                            Button(action: {
                                onUpgrade()
                            }) {
                                Text(NSLocalizedString("upgrade_to_premium", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "升级到高级版", comment: ""))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.green)
                                    .cornerRadius(8)
                            }
                            Spacer()
                        }
                    }
                }
            }
        }
    }

    // 清理资源
    private func cleanupResources() {
        // 取消所有正在进行的地理编码请求
        CLGeocoder().cancelGeocode()

        // 记录清理操作
        Task {
            await logInfo("FileImportSheet - 已清理资源，取消所有地理编码请求")
        }
    }

    // 全选/全不选地址
    private func selectAllAddresses(_ selected: Bool) {
        importedAddresses = importedAddresses.map { (address, coordinate, _, warning) in
            (address, coordinate, selected, warning)
        }
    }

    // 切换地址选择状态
    private func toggleAddressSelection(at index: Int) {
        guard index < importedAddresses.count else { return }

        let (address, coordinate, isSelected, warning) = importedAddresses[index]
        importedAddresses[index] = (address, coordinate, !isSelected, warning)
    }

    // 导入选中的地址
    private func importSelectedAddresses() {
        let selectedAddresses = importedAddresses
            .filter { $0.2 } // 只选择已选中的地址
            .map { ($0.0, $0.1, $0.3) } // 转换为(地址, 坐标, 警告)元组

        if selectedAddresses.isEmpty {
            // 显示错误提示
            alertItem = FileImportAlertItem(
                title: NSLocalizedString("cannot_import", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法导入", comment: ""),
                message: NSLocalizedString("select_at_least_one", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "请至少选择一个地址", comment: "")
            )
            return
        }

        // 🎯 优先检查：非Pro用户且选择地址超过20个时的处理
        let subscriptionManager = SubscriptionManager.shared
        let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial

        if isFreeUser && selectedAddresses.count > 20 {
            // 非Pro用户且选择地址超过20个，弹出选择对话框
            limitExceededInfo = (
                currentCount: 0, // 这里不关心当前路线的地址数
                remainingSlots: 20, // 免费用户限制为20个
                maxAllowed: 20,
                selectedCount: selectedAddresses.count,
                selectedAddresses: selectedAddresses
            )

            Task {
                await logInfo("FileImportSheet - 非Pro用户选择了\(selectedAddresses.count)个地址，超过20个限制，弹出选择对话框")
            }

            // 显示选择对话框
            showLimitExceededSheet = true
            return
        }

        // 检查订阅限制（Pro用户或地址数量在20个以内的情况）
        let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute

        Task {
            await logInfo("FileImportSheet - 当前订阅级别: \(subscriptionManager.currentTier.rawValue), 最大允许地址数: \(maxAllowed)")
        }

        // 获取当前路线
        if let currentRoute = getCurrentRoute() {
            let currentCount = currentRoute.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
            let remainingSlots = maxAllowed - currentCount

            Task {
                await logInfo("FileImportSheet - 当前路线: \(currentRoute.name), 当前地址数: \(currentCount), 剩余槽位: \(remainingSlots)")
                await logInfo("FileImportSheet - 选择的地址数: \(selectedAddresses.count), 是否超出限制: \(selectedAddresses.count > remainingSlots)")

                // 检查是否超出限制
                if selectedAddresses.count > 0 && remainingSlots <= 0 {
                    await logInfo("FileImportSheet - 剩余槽位为0，任何数量的地址都无法添加")
                }
            }

            if selectedAddresses.count > remainingSlots {
                // 保存超出限制的信息，并显示自定义表单
                limitExceededInfo = (
                    currentCount: currentCount,
                    remainingSlots: remainingSlots,
                    maxAllowed: maxAllowed,
                    selectedCount: selectedAddresses.count,
                    selectedAddresses: selectedAddresses
                )

                // 显示自定义表单
                showLimitExceededSheet = true
                return
            }
        }

        // 🎯 第三方快递导入：同步第三方排序号到sort_number和sorted_number
        let processedAddresses = processThirdPartyImportAddresses(selectedAddresses)

        // 调用回调函数
        onAddressesImported(processedAddresses)

        // 🚨 检查并提示问题地址
        Task {
            // 延迟一下让地址处理完成
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            await MainActor.run {
                ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "文件导入")
            }
        }

        // 关闭表单
        dismiss()
    }

    // 🎯 处理第三方快递导入地址：同步第三方排序号到sort_number和sorted_number
    private func processThirdPartyImportAddresses(_ addresses: [(String, CLLocationCoordinate2D, String)]) -> [(String, CLLocationCoordinate2D, String)] {
        return addresses.map { (address, coordinate, warning) in
            // 分离地址信息，检查是否有第三方排序号
            let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)

            // 如果有第三方排序号，同步到sort_number和sorted_number
            if !separatedInfo.thirdPartySortNumber.isEmpty {
                // 提取第三方排序号的数字部分
                let thirdPartyNumber = extractNumberFromThirdPartySort(separatedInfo.thirdPartySortNumber)

                if thirdPartyNumber > 0 {
                    // 构建新的地址字符串，将第三方排序号同步到sort_number和sorted_number
                    var newAddress = separatedInfo.address

                    // 添加同步的排序号
                    newAddress += SortNumberConstants.internalSortTag(thirdPartyNumber)  // sort_number
                    newAddress += SortNumberConstants.thirdPartySortTag(separatedInfo.thirdPartySortNumber)  // 保留原始第三方排序号

                    // 添加其他信息
                    if !separatedInfo.tracking.isEmpty {
                        newAddress += SortNumberConstants.trackingTag(separatedInfo.tracking)
                    }
                    if !separatedInfo.customer.isEmpty {
                        newAddress += SortNumberConstants.customerTag(separatedInfo.customer)
                    }

                    Logger.aiInfo("🎯 第三方快递导入同步：第三方排序号 '\(separatedInfo.thirdPartySortNumber)' -> sort_number: \(thirdPartyNumber)")

                    return (newAddress, coordinate, warning)
                }
            }

            // 如果没有第三方排序号或提取失败，返回原地址
            return (address, coordinate, warning)
        }
    }

    // 从第三方排序号中提取数字
    private func extractNumberFromThirdPartySort(_ sortNumber: String) -> Int {
        // 使用正则表达式提取数字
        let pattern = "\\d+"
        if let regex = try? NSRegularExpression(pattern: pattern, options: []),
           let match = regex.firstMatch(in: sortNumber, options: [], range: NSRange(location: 0, length: sortNumber.count)) {
            let numberString = (sortNumber as NSString).substring(with: match.range)
            return Int(numberString) ?? 0
        }
        return 0
    }

    // 获取当前路线
    private func getCurrentRoute() -> Route? {
        // 使用共享的持久化ModelContainer
        let container = getPersistentContainer()
        let context = container.mainContext

        do {
            let descriptor = FetchDescriptor<Route>()
            let routes = try context.fetch(descriptor)

            if routes.isEmpty {
                Task {
                    await logInfo("FileImportSheet - 数据库中没有找到路线")
                }
                return nil
            }

            Task {
                await logInfo("FileImportSheet - 成功获取路线: \(routes.first?.name ?? "未命名"), ID=\(routes.first?.id.uuidString ?? "无ID")")
            }
            return routes.first
        } catch {
            Task {
                await logError("FileImportSheet - 获取当前路线失败: \(error.localizedDescription)")
            }
            return nil
        }
    }

    // 格式化距离
    private func formatDistance(_ distance: Double) -> String {
        if distance >= 1000 {
            return String(format: NSLocalizedString("kilometers_format", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "%.2f 公里", comment: ""), distance / 1000)
        } else {
            return String(format: NSLocalizedString("meters_format", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "%.0f 米", comment: ""), distance)
        }
    }

    // 处理文件导入
    private func handleFileImport(_ result: Result<[URL], Error>) {
        do {
            // 获取文件URL
            guard let selectedFile = try result.get().first else {
                Task {
                    await logError("FileImportSheet - No file selected")
                }
                return
            }

            // 开始导入
            isImporting = true

            // 读取文件内容
            if selectedFile.startAccessingSecurityScopedResource() {
                defer { selectedFile.stopAccessingSecurityScopedResource() }

                // 读取文件数据
                let data = try Data(contentsOf: selectedFile)

                // 自动检测文件类型并处理数据
                let fileExtension = selectedFile.pathExtension.lowercased()

                Task {
                    await logInfo("FileImportSheet - 检测到文件类型: \(fileExtension)")
                }

                if fileExtension == "csv" {
                    processCSVData(data)
                } else if fileExtension == "txt" {
                    processTextData(data)
                } else if fileExtension == "json" {
                    processJSONData(data)
                } else if fileExtension == "xlsx" || fileExtension == "xls" {
                    // Excel格式暂不支持
                    processExcelData(data)
                    return
                } else {
                    // 尝试根据内容判断文件类型
                    if let content = String(data: data, encoding: .utf8) {
                        if content.contains("{") && content.contains("}") && (content.contains("\"address\"") || content.contains("\"addresses\"")) {
                            Task {
                                await logInfo("FileImportSheet - 根据内容判断为JSON文件")
                            }
                            processJSONData(data)
                        } else if content.contains(",") {
                            Task {
                                await logInfo("FileImportSheet - 根据内容判断为CSV文件")
                            }
                            processCSVData(data)
                        } else {
                            Task {
                                await logInfo("FileImportSheet - 根据内容判断为TXT文件")
                            }
                            processTextData(data)
                        }
                    } else {
                        // 无法识别的文件类型
                        processExcelData(data)
                        return
                    }
                }
            } else {
                throw NSError(domain: "FileImportSheet", code: 1, userInfo: [NSLocalizedDescriptionKey: NSLocalizedString("无法访问文件", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法访问文件", comment: "")])
            }
        } catch {
            isImporting = false
            alertItem = FileImportAlertItem(
                title: NSLocalizedString("prompt", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "提示", comment: ""),
                message: String(format: NSLocalizedString("cannot_read_file", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法读取文件：%@", comment: ""), error.localizedDescription)
            )
            Task {
                await logError("FileImportSheet - File import failed: \(error.localizedDescription)")
            }
        }
    }

    // 处理CSV数据
    private func processCSVData(_ data: Data) {
        // 将数据转换为字符串
        guard let content = String(data: data, encoding: .utf8) else {
            Task { @MainActor in
                showError(NSLocalizedString("无法读取CSV文件内容", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法读取CSV文件内容", comment: ""))
            }
            return
        }

        // 开始处理 - 确保在主线程上更新UI
        Task { @MainActor in
            isProcessing = true
            processingProgress = 0.0
        }

        // 分割行
        let lines = content.components(separatedBy: .newlines)
            .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        // 处理每一行
        Task {
            var processedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = []
            var successCount = 0
            var warningCount = 0

            // 分批处理地址，避免同时发送太多地理编码请求
            let batchSize = 5 // 每批处理5个地址
            let totalLines = lines.count

            for batchStart in stride(from: 0, to: totalLines, by: batchSize) {
                let batchEnd = min(batchStart + batchSize, totalLines)
                let currentBatch = lines[batchStart..<batchEnd]

                // 更新进度
                let progress = Double(batchStart) / Double(totalLines)
                await MainActor.run {
                    processingProgress = progress
                }

                // 并行处理当前批次中的每一行
                await withTaskGroup(of: (Int, String, CLLocationCoordinate2D, Bool, String).self) { group in
                    for (batchIndex, line) in currentBatch.enumerated() {
                        let lineIndex = batchStart + batchIndex
                        group.addTask {
                            return await self.processCSVLine(line, index: lineIndex, totalLines: totalLines)
                        }
                    }

                    // 收集结果
                    for await (index, address, coordinate, isSelected, warning) in group {
                        // 记录处理结果
                        if warning.isEmpty {
                            successCount += 1
                        } else {
                            warningCount += 1
                        }

                        // 保存处理结果，确保按原始顺序
                        while processedAddresses.count <= index {
                            // 填充空位，确保索引对齐
                            processedAddresses.append(("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, ""))
                        }
                        processedAddresses[index] = (address, coordinate, isSelected, warning)
                    }
                }

                // 在批次之间添加延迟，避免超出API限制
                if batchEnd < totalLines {
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                }
            }

            // 移除任何空位
            processedAddresses = processedAddresses.filter { !$0.0.isEmpty }

            // 更新UI
            await MainActor.run {
                importedAddresses = processedAddresses
                isProcessing = false
                isImporting = false

                // 显示导入结果
                if processedAddresses.isEmpty {
                    alertItem = FileImportAlertItem(
                        title: NSLocalizedString("import_result", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入结果", comment: ""),
                        message: NSLocalizedString("未找到有效地址", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "未找到有效地址", comment: "")
                    )
                } else {
                    // 显示导入结果摘要
                    let totalCount = processedAddresses.count

                    if warningCount > 0 {
                        alertItem = FileImportAlertItem(
                            title: NSLocalizedString("prompt", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "提示", comment: ""),
                            message: String(format: NSLocalizedString("import_success_with_warnings", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入成功，警告 %d 个，有效 %d 个，警告 %d 个", comment: ""), totalCount, successCount, warningCount)
                        )
                    }
                }
            }
        }
    }

    // 处理单行CSV数据
    private func processCSVLine(_ line: String, index: Int, totalLines: Int) async -> (Int, String, CLLocationCoordinate2D, Bool, String) {
        // 分割CSV行
        let components = line.components(separatedBy: ",")
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        // 检查是否有足够的组件
        guard !components.isEmpty else {
            return (index, "", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, NSLocalizedString("无效的CSV行", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无效的CSV行", comment: ""))
        }

        // 尝试提取地址和坐标
        if components.count >= 3,
           let lat = Double(components[1]),
           let lon = Double(components[2]) {
            // CSV包含坐标
            let address = components[0]

            // 检查坐标是否有效
            if lat != 0 || lon != 0 {
                let coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lon)

                // 使用全球化坐标验证方法
                let userLocation = LocationManager.shared.userLocation
                let validationResult = DeliveryPoint.validateCoordinatesGlobally(
                    latitude: coordinate.latitude,
                    longitude: coordinate.longitude,
                    userLocation: userLocation
                )

                // 根据验证结果设置警告
                if !validationResult.isValid {
                    let warningMessage = validationResult.warning ?? "invalid_coordinates".localized
                    return (index, address, coordinate, true, warningMessage)
                } else if validationResult.validationStatus == .warning {
                    let warningMessage = validationResult.warning ?? "coordinate_warning".localized
                    return (index, address, coordinate, true, warningMessage)
                } else {
                    // 坐标在有效范围内
                    return (index, address, coordinate, true, "")
                }
            } else {
                // 地理编码失败，返回无效坐标让用户知道需要修正
                let invalidCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                return (index, address, invalidCoordinate, true, NSLocalizedString("cannot_get_coordinates", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法获取坐标", comment: ""))
            }
        } else if !components[0].isEmpty {
            // 只有地址，需要地理编码
            let address = components[0]

            // 使用地理编码获取坐标
            let coordinate = await geocodeAddressWithRetry(address)

            // 🌍 使用统一验证服务验证地址（与Batch Paste保持一致）
            let validationResult = await UnifiedAddressValidationService.shared.validateAddress(address, existingCoordinate: coordinate)

            // 判断地址是否有问题
            var warningMessage = ""

            // 检查验证警告
            if let warning = validationResult.warningMessage, !warning.isEmpty {
                warningMessage = warning
            }

            // 检查坐标有效性
            if validationResult.coordinate.latitude == 0 && validationResult.coordinate.longitude == 0 {
                if warningMessage.isEmpty {
                    warningMessage = "无法获取坐标"
                }
            }

            // 检查置信度
            if validationResult.confidence == .veryLow || validationResult.confidence == .low {
                if warningMessage.isEmpty {
                    warningMessage = "地址验证置信度较低"
                }
            }

            // 检查是否有效
            if !validationResult.isValid {
                if warningMessage.isEmpty {
                    warningMessage = "地址验证失败"
                }
            }

            return (index, address, validationResult.coordinate, true, warningMessage)
        } else {
            // 无效行
            return (index, "", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, NSLocalizedString("无效的CSV行", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无效的CSV行", comment: ""))
        }
    }

    // 带重试机制的地理编码 - 集成智能缓存系统
    private func geocodeAddressWithRetry(_ address: String, retryCount: Int = 3) async -> CLLocationCoordinate2D {
        // 🚀 静默检查用户地址数据库
        if let validatedResult = await UserAddressDatabase.shared.getValidatedAddress(for: address) {
            return validatedResult.coordinate
        }
        // 增强地址，确保包含澳大利亚信息
        var enhancedAddress = address
        if !address.lowercased().contains("australia") {
            // 检查是否包含Glen Waverley
            if address.lowercased().contains("glen waverley") {
                enhancedAddress = "\(address), VIC 3150, Australia"
            } else if address.lowercased().contains("mount waverley") || address.lowercased().contains("mt waverley") {
                enhancedAddress = "\(address), VIC 3149, Australia"
            } else if address.lowercased().contains("clayton") {
                enhancedAddress = "\(address), VIC 3168, Australia"
            } else if address.lowercased().contains("oakleigh") {
                enhancedAddress = "\(address), VIC 3166, Australia"
            } else {
                enhancedAddress = "\(address), Glen Waverley, VIC 3150, Australia"
            }
        }

        // 创建墨尔本区域限制
        let melbourneCenter = CLLocationCoordinate2D(latitude: -37.8796, longitude: 145.1631)
        let melbourneRegion = CLCircularRegion(center: melbourneCenter, radius: 50000, identifier: "Melbourne_SE")

        // 尝试地理编码
        for attempt in 1...retryCount {
            // 🚦 使用全局速率限制
            await GlobalGeocodingRateLimiter.shared.waitForRateLimit()

            do {
                let geocoder = CLGeocoder()

                // 尝试使用区域限制进行地理编码
                var placemarks: [CLPlacemark] = []

                do {
                    // 首先尝试使用区域限制和澳大利亚语言环境
                    placemarks = try await geocoder.geocodeAddressString(
                        enhancedAddress,
                        in: melbourneRegion,
                        preferredLocale: Locale(identifier: "en_AU")
                    )
                } catch {
                    // 如果失败，尝试不使用区域限制
                    await logInfo("使用区域限制地理编码失败，尝试不使用区域限制: \(enhancedAddress)")
                    placemarks = try await geocoder.geocodeAddressString(
                        enhancedAddress,
                        in: nil,
                        preferredLocale: Locale(identifier: "en_AU")
                    )
                }

                // 优先选择澳大利亚的结果
                if let australiaPlacemark = placemarks.first(where: { $0.country == "Australia" }),
                   let location = australiaPlacemark.location?.coordinate {
                    // 🚀 静默保存到用户地址数据库
                    await UserAddressDatabase.shared.saveValidatedAddress(address, coordinate: location, source: .fileImport, confidence: 0.9)
                    return location
                } else if let placemark = placemarks.first,
                         let location = placemark.location?.coordinate {
                    // 非澳大利亚结果 - 较低置信度
                    await UserAddressDatabase.shared.saveValidatedAddress(address, coordinate: location, source: .fileImport, confidence: 0.7)
                    return location
                }

                // 没有找到结果，准备重试
                await logError("地理编码未返回结果: \(address)，尝试 \(attempt)/\(retryCount)")
            } catch {
                // 地理编码失败
                await logError("Geocoding failed: \(address), error: \(error.localizedDescription), attempt \(attempt)/\(retryCount)")

                // 检查是否是速率限制错误
                let nsError = error as NSError
                if nsError.domain == "GEOErrorDomain" && nsError.code == -3 {
                    // 速率限制错误，等待更长时间
                    await logError("遇到API速率限制错误，等待60秒: \(address)")
                    try? await Task.sleep(nanoseconds: 60_000_000_000) // 等待60秒
                } else if nsError.domain == kCLErrorDomain && nsError.code == 8 {
                    // 超时错误，等待更长时间
                    try? await Task.sleep(nanoseconds: UInt64(attempt) * 1_000_000_000) // 1-3秒，随重试次数增加
                } else {
                    // 其他错误，等待较短时间
                    try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
                }
            }
        }

        // 所有重试都失败，返回无效坐标让用户知道需要修正
        return CLLocationCoordinate2D(latitude: 0, longitude: 0)
    }

    // 注意：速率限制管理已迁移到GlobalGeocodingRateLimiter

    // 检查地址匹配度的简化版本
    private func checkAddressMatch(originalAddress: String, coordinate: CLLocationCoordinate2D) async -> Bool {
        // 简化的地址匹配检查
        // 如果坐标有效（不为0,0），我们认为匹配是可接受的
        // 这里可以根据需要实现更复杂的匹配逻辑

        // 检查坐标是否有效
        if coordinate.latitude == 0 && coordinate.longitude == 0 {
            return false
        }

        // 检查坐标是否在合理范围内（澳大利亚）
        let isInAustralia = coordinate.latitude >= -44.0 && coordinate.latitude <= -10.0 &&
                           coordinate.longitude >= 113.0 && coordinate.longitude <= 154.0

        if !isInAustralia {
            await logInfo("地址匹配检查：坐标不在澳大利亚范围内 - \(originalAddress)")
            return false
        }

        // 对于Glen Waverley地址，检查是否在墨尔本东南部区域
        if originalAddress.lowercased().contains("glen waverley") {
            let glenWaverleyCenter = CLLocationCoordinate2D(latitude: -37.8794, longitude: 145.1498)
            let distance = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
                .distance(from: CLLocation(latitude: glenWaverleyCenter.latitude, longitude: glenWaverleyCenter.longitude))

            // 如果距离Glen Waverley中心超过20公里，可能有问题
            if distance > 20000 {
                await logInfo("地址匹配检查：坐标距离Glen Waverley中心过远(\(Int(distance/1000))km) - \(originalAddress)")
                return false
            }
        }

        // 基本匹配检查通过
        return true
    }

    // 处理文本数据
    private func processTextData(_ data: Data) {
        // 将数据转换为字符串
        guard let content = String(data: data, encoding: .utf8) else {
            Task { @MainActor in
                showError(NSLocalizedString("无法读取文本文件内容", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法读取文本文件内容", comment: ""))
            }
            return
        }

        // 分割行 - 保留所有行，包括空行，让后续处理决定如何处理
        let allLines = content.components(separatedBy: .newlines)
        let lines = allLines.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        // 开始处理
        Task { @MainActor in
            isProcessing = true
            processingProgress = 0.0
            // 重置详细进度信息
            currentBatch = 0
            totalBatches = 0
            processedCount = 0
            totalCount = lines.count
            successCount = 0
            warningCount = 0
            currentAddress = ""
            estimatedTimeRemaining = 0
            processingStartTime = Date()
        }

        // 记录行数信息
        Task {
            await logInfo("FileImportSheet - 文件总行数: \(allLines.count), 非空行数: \(lines.count)")
        }

        // 处理每一行
        Task {
            var processedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = []
            var successCount = 0
            var warningCount = 0

            // 🚀 静默批量检查用户地址数据库（不显示给用户）
            _ = await UserAddressDatabase.shared.batchGetValidatedAddresses(for: lines)
            // 数据库在后台静默工作，用户只感受到速度提升

            // 分批处理地址，避免同时发送太多地理编码请求
            let batchSize = 5 // 每批处理5个地址
            let totalLines = lines.count
            let totalBatchCount = Int(ceil(Double(totalLines) / Double(batchSize)))

            // 更新总批次数
            await MainActor.run {
                totalBatches = totalBatchCount
            }

            for batchStart in stride(from: 0, to: totalLines, by: batchSize) {
                let batchEnd = min(batchStart + batchSize, totalLines)
                let currentBatchData = lines[batchStart..<batchEnd]
                let currentBatchNumber = (batchStart / batchSize) + 1

                // 更新进度
                let progress = Double(batchStart) / Double(totalLines)
                await MainActor.run {
                    processingProgress = progress
                    currentBatch = currentBatchNumber
                    processedCount = batchStart

                    // 计算预估剩余时间
                    if let startTime = processingStartTime, batchStart > 0 {
                        let elapsedTime = Date().timeIntervalSince(startTime)
                        let averageTimePerAddress = elapsedTime / Double(batchStart)
                        let remainingAddresses = totalLines - batchStart
                        estimatedTimeRemaining = averageTimePerAddress * Double(remainingAddresses)
                    }
                }

                // 并行处理当前批次中的每一行
                await withTaskGroup(of: (Int, String, CLLocationCoordinate2D, Bool, String).self) { group in
                    for (batchIndex, line) in currentBatchData.enumerated() {
                        let lineIndex = batchStart + batchIndex

                        // 更新当前处理的地址
                        await MainActor.run {
                            currentAddress = String(line.prefix(50)) + (line.count > 50 ? "..." : "")
                        }
                        group.addTask {
                            let address = line.trimmingCharacters(in: .whitespacesAndNewlines)

                            // 跳过空行
                            guard !address.isEmpty else {
                                return (lineIndex, "", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, NSLocalizedString("empty_address", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "空地址", comment: ""))
                            }

                            // 使用地理编码获取坐标
                            let coordinate = await geocodeAddressWithRetry(address)

                            // 🌍 使用统一验证服务验证地址（与Batch Paste保持一致）
                            let validationResult = await UnifiedAddressValidationService.shared.validateAddress(address, existingCoordinate: coordinate)

                            // 判断地址是否有问题
                            var warningMessage = ""

                            // 检查验证警告
                            if let warning = validationResult.warningMessage, !warning.isEmpty {
                                warningMessage = warning
                            }

                            // 检查坐标有效性
                            if validationResult.coordinate.latitude == 0 && validationResult.coordinate.longitude == 0 {
                                if warningMessage.isEmpty {
                                    warningMessage = "无法获取坐标"
                                }
                            }

                            // 检查置信度
                            if validationResult.confidence == .veryLow || validationResult.confidence == .low {
                                if warningMessage.isEmpty {
                                    warningMessage = "地址验证置信度较低"
                                }
                            }

                            // 检查是否有效
                            if !validationResult.isValid {
                                if warningMessage.isEmpty {
                                    warningMessage = "地址验证失败"
                                }
                            }

                            return (lineIndex, address, validationResult.coordinate, true, warningMessage)
                        }
                    }

                    // 收集结果
                    for await (index, address, coordinate, isSelected, warning) in group {
                        // 记录处理结果 - 不跳过任何地址，包括空地址
                        if warning.isEmpty && !address.isEmpty {
                            await MainActor.run {
                                successCount += 1
                            }
                        } else {
                            await MainActor.run {
                                warningCount += 1
                            }
                        }

                        // 保存处理结果，确保按原始顺序
                        while processedAddresses.count <= index {
                            // 填充空位，确保索引对齐
                            processedAddresses.append(("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, ""))
                        }
                        processedAddresses[index] = (address, coordinate, isSelected, warning)

                        // 更新已处理数量
                        await MainActor.run {
                            processedCount = min(processedAddresses.count, totalCount)
                        }
                    }
                }

                // 在批次之间添加延迟，避免超出API限制
                if batchEnd < totalLines {
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                }
            }

            // 记录处理前的地址数量
            await logInfo("FileImportSheet - 处理前地址数量: \(processedAddresses.count)")

            // 只移除完全空白的占位符，保留所有实际地址（包括验证失败的）
            let beforeFilterCount = processedAddresses.count
            processedAddresses = processedAddresses.enumerated().compactMap { index, item in
                // 如果是占位符（空地址且坐标为0,0且无警告），则移除
                if item.0.isEmpty && item.1.latitude == 0 && item.1.longitude == 0 && item.3.isEmpty {
                    return nil
                }
                return item
            }

            // 记录过滤后的地址数量
            await logInfo("FileImportSheet - 过滤前: \(beforeFilterCount), 过滤后: \(processedAddresses.count), 移除了 \(beforeFilterCount - processedAddresses.count) 个空占位符")

            // 更新UI
            await MainActor.run {
                importedAddresses = processedAddresses
                isProcessing = false
                isImporting = false
                processingProgress = 1.0
                processedCount = totalCount
                currentAddress = ""

                // 显示导入结果
                if processedAddresses.isEmpty {
                    alertItem = FileImportAlertItem(
                        title: NSLocalizedString("import_result", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入结果", comment: ""),
                        message: NSLocalizedString("未找到有效地址", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "未找到有效地址", comment: "")
                    )
                } else {
                    // 显示导入结果摘要
                    let totalCount = processedAddresses.count

                    if warningCount > 0 {
                        alertItem = FileImportAlertItem(
                            title: NSLocalizedString("prompt", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "提示", comment: ""),
                            message: String(format: NSLocalizedString("import_success_some_warnings", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入成功，警告 %d 个，有效 %d 个，警告 %d 个", comment: ""), totalCount, successCount, warningCount)
                        )
                    } else {
                        alertItem = FileImportAlertItem(
                            title: NSLocalizedString("import_result", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入结果", comment: ""),
                            message: String(format: NSLocalizedString("import_success_all_valid", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入成功，有效地址 %d 个", comment: ""), totalCount)
                        )
                    }
                }
            }
        }
    }

    // 处理JSON数据
    private func processJSONData(_ data: Data) {
        Task {
            await logInfo("FileImportSheet - 开始处理JSON数据")
        }

        // 开始处理 - 确保在主线程上更新UI
        Task { @MainActor in
            isProcessing = true
            processingProgress = 0.0
        }

        Task {
            var processedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = []
            var successCount = 0
            var warningCount = 0

            do {
                // 尝试解析JSON数据
                if let jsonObject = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    // 单个地址对象
                    if let address = jsonObject["address"] as? String {
                        var coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)

                        // 检查是否有坐标信息
                        if let lat = jsonObject["latitude"] as? Double, let lng = jsonObject["longitude"] as? Double {
                            coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lng)
                            // 添加到处理列表，无需地理编码
                            processedAddresses.append((address, coordinate, true, ""))
                            successCount += 1
                        } else {
                            // 需要地理编码
                            coordinate = await geocodeAddressWithRetry(address)

                            if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                // 地理编码失败，使用默认坐标
                                let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16)
                                processedAddresses.append((address, defaultCoordinate, true, NSLocalizedString("无法获取地址坐标", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法获取地址坐标", comment: "")))
                                warningCount += 1
                            } else {
                                // 地理编码成功
                                processedAddresses.append((address, coordinate, true, ""))
                                successCount += 1
                            }
                        }
                    }
                    // 地址数组
                    else if let addresses = jsonObject["addresses"] as? [[String: Any]] {
                        await processJSONAddressArray(addresses, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                    }
                    // 地址字符串数组
                    else if let addresses = jsonObject["addresses"] as? [String] {
                        await processJSONStringArray(addresses, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                    }
                    else {
                        // 未找到有效的地址数据
                        await MainActor.run {
                            showError(NSLocalizedString("未找到有效的地址数据，请确保JSON文件包含地址信息", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "未找到有效的地址数据，请确保JSON文件包含地址信息", comment: ""))
                        }
                    }
                }
                // 尝试解析为数组
                else if let jsonArray = try JSONSerialization.jsonObject(with: data, options: []) as? [[String: Any]] {
                    // 对象数组，每个对象包含地址信息
                    await processJSONAddressArray(jsonArray, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                }
                else if let stringArray = try JSONSerialization.jsonObject(with: data, options: []) as? [String] {
                    // 字符串数组，每个字符串是一个地址
                    await processJSONStringArray(stringArray, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                }
                else {
                    // 无法解析的JSON
                    await MainActor.run {
                        showError(NSLocalizedString("cannot_parse_json", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法解析JSON", comment: ""))
                    }
                }
            } catch {
                // JSON解析错误
                Task {
                    await logError("FileImportSheet - JSON parsing error: \(error.localizedDescription)")
                }
                await MainActor.run {
                    showError(String(format: NSLocalizedString("cannot_parse_json_with_error", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法解析JSON，错误：%@", comment: ""), error.localizedDescription))
                }
            }

            // 更新UI
            await MainActor.run {
                importedAddresses = processedAddresses
                isProcessing = false
                isImporting = false

                // 显示导入结果
                if processedAddresses.isEmpty {
                    alertItem = FileImportAlertItem(
                        title: NSLocalizedString("import_result", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入结果", comment: ""),
                        message: NSLocalizedString("no_valid_addresses_found", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "未找到有效地址", comment: "")
                    )
                } else {
                    // 显示导入结果摘要
                    let totalCount = processedAddresses.count

                    if warningCount > 0 {
                        alertItem = FileImportAlertItem(
                            title: NSLocalizedString("import_result", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入结果", comment: ""),
                            message: String(format: NSLocalizedString("import_success_some_warnings", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入成功，警告 %d 个，有效 %d 个，警告 %d 个", comment: ""), totalCount, successCount, warningCount)
                        )
                    } else {
                        alertItem = FileImportAlertItem(
                            title: NSLocalizedString("import_result", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入结果", comment: ""),
                            message: String(format: NSLocalizedString("import_success_all_valid", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入成功，有效地址 %d 个", comment: ""), totalCount)
                        )
                    }
                }
            }
        }
    }

    // 处理JSON地址对象数组
    private func processJSONAddressArray(_ addresses: [[String: Any]], processedAddresses: inout [(String, CLLocationCoordinate2D, Bool, String)], successCount: inout Int, warningCount: inout Int) async {
        // 分批处理地址，避免同时发送太多地理编码请求
        let batchSize = 5 // 每批处理5个地址
        let totalAddresses = addresses.count

        for batchStart in stride(from: 0, to: totalAddresses, by: batchSize) {
            let batchEnd = min(batchStart + batchSize, totalAddresses)
            let currentBatch = addresses[batchStart..<batchEnd]

            // 更新进度
            let progress = Double(batchStart) / Double(totalAddresses)
            await MainActor.run {
                processingProgress = progress
            }

            // 并行处理当前批次
            await withTaskGroup(of: (String, CLLocationCoordinate2D, Bool, String).self) { group in
                for addressObj in currentBatch {
                    group.addTask {
                        if let address = addressObj["address"] as? String {
                            // 检查是否有坐标信息
                            if let lat = addressObj["latitude"] as? Double, let lng = addressObj["longitude"] as? Double {
                                let coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lng)
                                // 无需地理编码
                                return (address, coordinate, true, "")
                            } else {
                                // 需要地理编码
                                let coordinate = await self.geocodeAddressWithRetry(address)

                                if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                    // 地理编码失败，返回无效坐标让用户知道需要修正
                                    return (address, coordinate, true, NSLocalizedString("cannot_get_coordinates", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法获取坐标", comment: ""))
                                } else {
                                    // 地理编码成功，验证坐标有效性
                                    let userLocation = LocationManager.shared.userLocation
                                    let validationResult = DeliveryPoint.validateCoordinatesGlobally(
                                        latitude: coordinate.latitude,
                                        longitude: coordinate.longitude,
                                        userLocation: userLocation
                                    )

                                    // 根据验证结果设置警告
                                    if !validationResult.isValid {
                                        let warningMessage = validationResult.warning ?? "invalid_coordinates".localized
                                        return (address, coordinate, true, warningMessage)
                                    } else if validationResult.validationStatus == LocationValidationStatus.warning {
                                        let warningMessage = validationResult.warning ?? "coordinate_warning".localized
                                        return (address, coordinate, true, warningMessage)
                                    } else {
                                        return (address, coordinate, true, "")
                                    }
                                }
                            }
                        } else {
                            return ("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, NSLocalizedString("invalid_address_data", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无效地址数据", comment: ""))
                        }
                    }
                }

                // 收集结果
                for await (address, coordinate, isSelected, warning) in group {
                    // 记录处理结果 - 不跳过任何地址，包括空地址
                    if warning.isEmpty && !address.isEmpty {
                        successCount += 1
                    } else {
                        warningCount += 1
                    }

                    // 保存处理结果 - 保留所有地址
                    processedAddresses.append((address, coordinate, isSelected, warning))
                }
            }

            // 在批次之间添加延迟，避免超出API限制
            if batchEnd < totalAddresses {
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }
        }
    }

    // 处理JSON字符串数组
    private func processJSONStringArray(_ addresses: [String], processedAddresses: inout [(String, CLLocationCoordinate2D, Bool, String)], successCount: inout Int, warningCount: inout Int) async {
        // 分批处理地址，避免同时发送太多地理编码请求
        let batchSize = 5 // 每批处理5个地址
        let totalAddresses = addresses.count

        for batchStart in stride(from: 0, to: totalAddresses, by: batchSize) {
            let batchEnd = min(batchStart + batchSize, totalAddresses)
            let currentBatch = addresses[batchStart..<batchEnd]

            // 更新进度
            let progress = Double(batchStart) / Double(totalAddresses)
            await MainActor.run {
                processingProgress = progress
            }

            // 并行处理当前批次
            await withTaskGroup(of: (String, CLLocationCoordinate2D, Bool, String).self) { group in
                for address in currentBatch {
                    group.addTask {
                        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)

                        // 跳过空地址
                        guard !trimmedAddress.isEmpty else {
                            return ("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, NSLocalizedString("empty_address", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "空地址", comment: ""))
                        }

                        // 需要地理编码
                        let coordinate = await self.geocodeAddressWithRetry(trimmedAddress)

                        if coordinate.latitude == 0 && coordinate.longitude == 0 {
                            // 地理编码失败，返回无效坐标让用户知道需要修正
                            return (trimmedAddress, coordinate, true, NSLocalizedString("cannot_get_address_coordinates", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "无法获取地址坐标", comment: ""))
                        } else {
                            // 地理编码成功，验证坐标有效性
                            let userLocation = LocationManager.shared.userLocation
                            let validationResult = DeliveryPoint.validateCoordinatesGlobally(
                                latitude: coordinate.latitude,
                                longitude: coordinate.longitude,
                                userLocation: userLocation
                            )

                            // 根据验证结果设置警告
                            if !validationResult.isValid {
                                let warningMessage = validationResult.warning ?? "invalid_coordinates".localized
                                return (trimmedAddress, coordinate, true, warningMessage)
                            } else if validationResult.validationStatus == LocationValidationStatus.warning {
                                let warningMessage = validationResult.warning ?? "coordinate_warning".localized
                                return (trimmedAddress, coordinate, true, warningMessage)
                            } else {
                                return (trimmedAddress, coordinate, true, "")
                            }
                        }
                    }
                }

                // 收集结果
                for await (address, coordinate, isSelected, warning) in group {
                    // 记录处理结果 - 不跳过任何地址，包括空地址
                    if warning.isEmpty && !address.isEmpty {
                        successCount += 1
                    } else {
                        warningCount += 1
                    }

                    // 保存处理结果 - 保留所有地址
                    processedAddresses.append((address, coordinate, isSelected, warning))
                }
            }

            // 在批次之间添加延迟，避免超出API限制
            if batchEnd < totalAddresses {
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }
        }
    }

    // Excel格式不再支持，简化处理函数
    private func processExcelData(_ data: Data) {
        Task { @MainActor in
            isImporting = false
            isProcessing = false
            showError(NSLocalizedString("excel_format_not_supported", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "Excel格式不支持", comment: ""))
        }
    }

    // 显示错误
    @MainActor private func showError(_ message: String) {
        isImporting = false
        isProcessing = false
        alertItem = FileImportAlertItem(
            title: NSLocalizedString("import_failed", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "导入失败", comment: ""),
            message: message
        )
        Task {
            await logError("FileImportSheet - \(message)")
        }
    }

    // 验证坐标有效性
    private func isValidCoordinate(_ coordinate: CLLocationCoordinate2D) async -> Bool {
        // 检查坐标是否为NaN
        if coordinate.latitude.isNaN || coordinate.longitude.isNaN {
            return false
        }

        // 检查坐标是否在有效范围内
        if coordinate.latitude < -90 || coordinate.latitude > 90 ||
           coordinate.longitude < -180 || coordinate.longitude > 180 {
            return false
        }

        // 检查坐标是否为0,0（无效点）
        if abs(coordinate.latitude) < 0.0001 && abs(coordinate.longitude) < 0.0001 {
            return false
        }

        return true
    }

    // 日志辅助函数
    private func logInfo(_ message: String) async {
        os_log("%{public}@", log: Logger.app, type: .info, message)
    }

    private func logError(_ message: String) async {
        os_log("%{public}@", log: Logger.app, type: .error, message)
    }

    // 下载示例文件
    private func downloadSampleFile(for fileType: FileImportSheet.FileType) {
        Task {
            await logInfo("FileImportSheet - 用户请求下载 \(fileType.rawValue) 示例文件")
        }

        let fileName: String
        let resourceName: String
        switch fileType {
        case .csv:
            fileName = "SampleAddresses.csv"
            resourceName = "SampleAddresses"
        case .txt:
            fileName = "SampleAddresses.txt"
            resourceName = "SampleAddresses"
        case .json:
            fileName = "SampleAddresses.json"
            resourceName = "SampleAddresses"
        }

        // 尝试多种路径查找示例文件
        var fileURL: URL?
        let possibleSubdirectories = [
            "Resources/SampleFiles",
            "SampleFiles",
            nil  // 直接在主 Bundle 中查找
        ]

        for subdirectory in possibleSubdirectories {
            if let url = Bundle.main.url(forResource: resourceName, withExtension: fileType.rawValue.lowercased(), subdirectory: subdirectory) {
                fileURL = url
                Task {
                    await logInfo("FileImportSheet - 在路径 '\(subdirectory ?? "main bundle")' 中找到示例文件: \(fileName)")
                }
                break
            }
        }

        guard let fileURL = fileURL else {
            Task {
                await logError("FileImportSheet - 找不到示例文件: \(fileName)")
                // 记录更详细的调试信息
                await logError("FileImportSheet - 已尝试的路径: Resources/SampleFiles, SampleFiles, main bundle")
                await logError("FileImportSheet - Bundle 路径: \(Bundle.main.bundlePath)")
                if let resourcePath = Bundle.main.resourcePath {
                    await logError("FileImportSheet - Resource 路径: \(resourcePath)")
                }
            }

            // 显示错误提示
            alertItem = FileImportAlertItem(
                title: NSLocalizedString("file_not_found", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "File Not Found", comment: ""),
                message: NSLocalizedString("sample_file_not_available", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "Sample file is temporarily unavailable", comment: "")
            )
            return
        }

        // 将 Bundle 中的文件复制到临时目录，然后分享
        Task {
            do {
                // 创建临时文件路径
                let tempDirectory = FileManager.default.temporaryDirectory
                let tempFileURL = tempDirectory.appendingPathComponent(fileName)

                // 如果临时文件已存在，先删除
                if FileManager.default.fileExists(atPath: tempFileURL.path) {
                    try FileManager.default.removeItem(at: tempFileURL)
                }

                // 复制文件到临时目录
                try FileManager.default.copyItem(at: fileURL, to: tempFileURL)

                await logInfo("FileImportSheet - 已复制示例文件到临时目录: \(tempFileURL.path)")

                // 在主线程上显示分享界面
                await MainActor.run {
                    let activityViewController = UIActivityViewController(activityItems: [tempFileURL], applicationActivities: nil)

                    // 为 iPad 设置 popover
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let window = windowScene.windows.first {

                        // 找到最顶层的视图控制器
                        var topViewController = window.rootViewController
                        while let presentedViewController = topViewController?.presentedViewController {
                            topViewController = presentedViewController
                        }

                        if let popover = activityViewController.popoverPresentationController {
                            popover.sourceView = window
                            popover.sourceRect = CGRect(x: window.bounds.midX, y: window.bounds.midY, width: 0, height: 0)
                            popover.permittedArrowDirections = []
                        }

                        topViewController?.present(activityViewController, animated: true)

                        Task {
                            await logInfo("FileImportSheet - 已显示 \(fileType.rawValue) 示例文件分享界面")
                        }
                    }
                }

            } catch {
                await logError("FileImportSheet - 复制示例文件失败: \(error.localizedDescription)")

                // 显示错误提示
                await MainActor.run {
                    alertItem = FileImportAlertItem(
                        title: NSLocalizedString("error", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "Error", comment: ""),
                        message: NSLocalizedString("file_copy_failed", tableName: nil, bundle: LocalizationManager.shared.localizationBundle, value: "Failed to prepare sample file for sharing", comment: "")
                    )
                }
            }
        }
    }

// FileImportSheet 结构体结束
}

// CheckboxToggleStyle 已在 BatchAddressInputSheet.swift 中定义

// 预览
struct FileImportSheetPreview: View {
    @State private var showSheet = true

    var body: some View {
        Button("Show Import Sheet") {
            showSheet = true
        }
        .sheet(isPresented: $showSheet) {
            FileImportSheet { addresses in
                print("Imported \(addresses.count) addresses")
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
            .interactiveDismissDisabled(true) // 禁用拖拽关闭，只能通过Cancel或Import按钮关闭
        }
    }
}

#Preview("FileImportSheet") {
    FileImportSheetPreview()
}
