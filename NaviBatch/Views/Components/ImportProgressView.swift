import SwiftUI

/// 导入进度视图
/// 显示导入进度和结果
struct ImportProgressView: View {
    // 进度值（0-1）
    let progress: Double

    // 已完成数量
    let completed: Int

    // 总数量
    let total: Int

    // 成功数量
    let success: Int

    // 失败数量
    let failed: Int

    // 是否显示详细信息
    let showDetails: Bool

    // 状态消息
    let statusMessage: String?

    // 初始化方法
    init(
        progress: Double,
        completed: Int,
        total: Int,
        success: Int = 0,
        failed: Int = 0,
        showDetails: Bool = true,
        statusMessage: String? = nil
    ) {
        self.progress = progress
        self.completed = completed
        self.total = total
        self.success = success
        self.failed = failed
        self.showDetails = showDetails
        self.statusMessage = statusMessage
    }

    var body: some View {
        VStack(spacing: 12) {
            // 进度条
            ProgressView(value: progress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle())
                .animation(.easeInOut, value: progress)

            // 进度文本
            HStack {
                Text("处理中...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(completed)/\(total)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // 详细信息
            if showDetails && (success > 0 || failed > 0) {
                HStack {
                    // 成功数量
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)

                        Text("\(success) 成功")
                            .font(.caption)
                            .foregroundColor(.green)
                    }

                    Spacer()

                    // 失败数量
                    if failed > 0 {
                        HStack(spacing: 4) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)

                            Text("\(failed) 警告")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
            }

            // 状态消息
            if let message = statusMessage {
                Text(message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.top, 4)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

/// 导入结果视图
/// 显示导入结果和警告
struct ImportResultView: View {
    // 成功数量
    let success: Int

    // 警告数量
    let warnings: Int

    // 失败数量
    let failed: Int

    // 警告消息
    let warningMessage: String?

    // 失败消息
    let failureMessage: String?

    // 操作按钮
    let actionButton: (String, () -> Void)?

    // 初始化方法
    init(
        success: Int,
        warnings: Int = 0,
        failed: Int = 0,
        warningMessage: String? = nil,
        failureMessage: String? = nil,
        actionButton: (String, () -> Void)? = nil
    ) {
        self.success = success
        self.warnings = warnings
        self.failed = failed
        self.warningMessage = warningMessage
        self.failureMessage = failureMessage
        self.actionButton = actionButton
    }

    var body: some View {
        VStack(spacing: 16) {
            // 标题
            Text("import_result".localized)
                .font(.headline)

            // 结果统计
            HStack(spacing: 20) {
                // 成功数量
                VStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.green)

                    Text("\(success)")
                        .font(.title3)
                        .fontWeight(.bold)

                    Text("success".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // 警告数量
                VStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.title)
                        .foregroundColor(.orange)

                    Text("\(warnings)")
                        .font(.title3)
                        .fontWeight(.bold)

                    Text("warning".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // 失败数量
                VStack {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.red)

                    Text("\(failed)")
                        .font(.title3)
                        .fontWeight(.bold)

                    Text("failed".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 警告消息
            if let message = warningMessage, warnings > 0 {
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)

                    Text(message)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }

            // 失败消息
            if let message = failureMessage, failed > 0 {
                HStack(alignment: .top, spacing: 8) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)

                    Text(message)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
            }

            // 操作按钮
            if let (title, action) = actionButton {
                Button(action: action) {
                    Text(title)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview(traits: .sizeThatFitsLayout) {
    VStack(spacing: 20) {
        // 进度视图
        ImportProgressView(
            progress: 0.7,
            completed: 7,
            total: 10,
            success: 5,
            failed: 2,
            statusMessage: "正在处理地址..."
        )

        // 结果视图
        ImportResultView(
            success: 8,
            warnings: 3,
            failed: 2,
            warningMessage: "部分地址无法获取准确坐标，但已添加到列表中。请检查标记为警告的地址。",
            failureMessage: "2个地址无法导入，请检查格式是否正确。",
            actionButton: ("查看详情", {})
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
