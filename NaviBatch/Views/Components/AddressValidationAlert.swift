import SwiftUI

struct AddressValidationAlert: View {
    let invalidIndices: [Int]
    let onFix: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text("address_validation_failed_title".localized)
                .font(.headline)
                .foregroundColor(.red)
                .padding(.top)
            
            // 分隔线
            Divider()
            
            // 内容
            VStack(alignment: .leading, spacing: 12) {
                Text("address_validation_failed_message".localized)
                    .font(.subheadline)
                
                // 显示有问题的地址索引
                if invalidIndices.count <= 5 {
                    // 如果问题地址少于5个，直接显示所有
                    Text("第 \(formatIndices(invalidIndices)) 号地址")
                        .font(.system(.subheadline, design: .monospaced))
                        .foregroundColor(.red)
                } else {
                    // 如果问题地址超过5个，只显示前5个并加省略号
                    let displayIndices = Array(invalidIndices.prefix(5))
                    Text("第 \(formatIndices(displayIndices)) 等 \(invalidIndices.count) 个地址")
                        .font(.system(.subheadline, design: .monospaced))
                        .foregroundColor(.red)
                }
                
                Text("address_validation_failed_fix_message".localized)
                    .font(.subheadline)
            }
            .padding(.horizontal)
            
            // 分隔线
            Divider()
            
            // 按钮
            HStack(spacing: 15) {
                // 取消按钮
                Button(action: onCancel) {
                    Text("cancel".localized)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }

                // 修复按钮
                Button(action: onFix) {
                    Text("fix_addresses".localized)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .frame(width: 300)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 10)
    }
    
    // 格式化索引列表为可读字符串
    private func formatIndices(_ indices: [Int]) -> String {
        // 将索引+1（因为UI中显示的是从1开始的序号）
        let displayIndices = indices.map { $0 + 1 }
        return displayIndices.map { String($0) }.joined(separator: "、")
    }
}

// 包装器视图，用于在SwiftUI中显示自定义警告框
struct AddressValidationAlertWrapper: ViewModifier {
    @Binding var isPresented: Bool
    let invalidIndices: [Int]
    let onFix: () -> Void
    
    func body(content: Content) -> some View {
        ZStack {
            content
                .blur(radius: isPresented ? 3 : 0)
            
            if isPresented {
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .transition(.opacity)
                
                AddressValidationAlert(
                    invalidIndices: invalidIndices,
                    onFix: {
                        withAnimation {
                            isPresented = false
                        }
                        onFix()
                    },
                    onCancel: {
                        withAnimation {
                            isPresented = false
                        }
                    }
                )
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut, value: isPresented)
    }
}

// 扩展View以添加自定义警告框
extension View {
    func addressValidationAlert(
        isPresented: Binding<Bool>,
        invalidIndices: [Int],
        onFix: @escaping () -> Void
    ) -> some View {
        self.modifier(
            AddressValidationAlertWrapper(
                isPresented: isPresented,
                invalidIndices: invalidIndices,
                onFix: onFix
            )
        )
    }
}

#Preview("AddressValidationAlert") {
    ZStack {
        Color.gray.opacity(0.2).edgesIgnoringSafeArea(.all)
        
        AddressValidationAlert(
            invalidIndices: [2, 4, 6, 8, 10, 12, 14],
            onFix: {},
            onCancel: {}
        )
    }
}
