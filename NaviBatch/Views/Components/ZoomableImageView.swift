import SwiftUI
import UIKit

/// 可缩放的图片视图
/// 支持双指缩放和双击放大功能
struct ZoomableImageView: UIViewRepresentable {
    // 图片
    let image: UIImage

    // 最小缩放比例
    var minZoom: CGFloat = 1.0

    // 最大缩放比例
    var maxZoom: CGFloat = 3.0

    // 双击缩放比例
    var doubleTapZoom: CGFloat = 2.0
    
    // 内容模式 - 默认为填充模式
    var contentMode: UIView.ContentMode = .scaleAspectFill

    // 创建UIView
    func makeUIView(context: Context) -> UIScrollView {
        // 创建滚动视图
        let scrollView = UIScrollView()
        scrollView.delegate = context.coordinator
        scrollView.maximumZoomScale = maxZoom
        scrollView.minimumZoomScale = minZoom
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.bouncesZoom = true
        scrollView.decelerationRate = .fast

        // 添加双击手势
        let doubleTapGesture = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleDoubleTap(_:)))
        doubleTapGesture.numberOfTapsRequired = 2
        scrollView.addGestureRecognizer(doubleTapGesture)

        // 创建图片视图
        let imageView = UIImageView(image: image)
        imageView.contentMode = contentMode // 使用指定的内容模式
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.isUserInteractionEnabled = true
        imageView.clipsToBounds = true // 确保内容不超出边界

        // 将图片视图添加到滚动视图
        scrollView.addSubview(imageView)
        context.coordinator.imageView = imageView

        // 设置约束
        NSLayoutConstraint.activate([
            imageView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            imageView.heightAnchor.constraint(equalTo: scrollView.heightAnchor),
            imageView.centerXAnchor.constraint(equalTo: scrollView.centerXAnchor),
            imageView.centerYAnchor.constraint(equalTo: scrollView.centerYAnchor)
        ])

        return scrollView
    }

    // 更新UIView
    func updateUIView(_ scrollView: UIScrollView, context: Context) {
        // 更新图片和内容模式
        if let imageView = context.coordinator.imageView {
            imageView.image = image
            imageView.contentMode = contentMode
        }
    }

    // 创建协调器
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // 协调器类
    class Coordinator: NSObject, UIScrollViewDelegate {
        // 父视图
        let parent: ZoomableImageView

        // 图片视图
        var imageView: UIImageView?

        // 初始化
        init(_ parent: ZoomableImageView) {
            self.parent = parent
        }

        // 处理双击手势
        @objc func handleDoubleTap(_ gesture: UITapGestureRecognizer) {
            guard let scrollView = gesture.view as? UIScrollView else { return }

            // 如果当前缩放比例大于最小缩放比例，则缩小到最小比例
            // 否则放大到指定比例
            if scrollView.zoomScale > scrollView.minimumZoomScale {
                scrollView.setZoomScale(scrollView.minimumZoomScale, animated: true)
            } else {
                // 获取双击位置
                let point = gesture.location(in: imageView)

                // 计算缩放区域
                let width = scrollView.frame.width / parent.doubleTapZoom
                let height = scrollView.frame.height / parent.doubleTapZoom
                let x = point.x - width / 2
                let y = point.y - height / 2

                // 放大到指定区域
                let rect = CGRect(x: x, y: y, width: width, height: height)
                scrollView.zoom(to: rect, animated: true)
            }
        }

        // 返回要缩放的视图
        func viewForZooming(in scrollView: UIScrollView) -> UIView? {
            return imageView
        }

        // 缩放结束后调整内容位置
        func scrollViewDidZoom(_ scrollView: UIScrollView) {
            // 确保图像视图存在
            if imageView == nil { return }

            // 计算内容偏移量，使图片保持居中
            let offsetX = max((scrollView.bounds.width - scrollView.contentSize.width) * 0.5, 0)
            let offsetY = max((scrollView.bounds.height - scrollView.contentSize.height) * 0.5, 0)

            // 调整内容边距
            scrollView.contentInset = UIEdgeInsets(
                top: offsetY,
                left: offsetX,
                bottom: offsetY,
                right: offsetX
            )
        }
    }
}

// 预览
#Preview("ZoomableImageView") {
    ZoomableImageView(image: UIImage(systemName: "photo")!)
        .frame(height: 300)
        .padding()
}
