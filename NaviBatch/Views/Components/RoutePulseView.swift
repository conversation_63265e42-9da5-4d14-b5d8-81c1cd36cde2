import SwiftUI
import MapKit

/// 路线脉动点视图
/// 显示带有脉动动画的白色标记，用于在路线上移动
struct RoutePulseView: View {
    @State private var animationAmount: CGFloat = 1
    
    var body: some View {
        ZStack {
            // 外部脉动白色圆圈
            Circle()
                .fill(Color.white.opacity(0.6))
                .frame(width: 30, height: 30)
                .scaleEffect(animationAmount)
                .opacity(2 - animationAmount)
                .animation(
                    .easeInOut(duration: 1.2)
                    .repeatForever(autoreverses: false),
                    value: animationAmount
                )
            
            // 灰色圆环边框
            Circle()
                .stroke(Color.gray.opacity(0.8), lineWidth: 1.5)
                .frame(width: 16, height: 16)
            
            // 内部白色实心点
            Circle()
                .fill(Color.white)
                .frame(width: 14, height: 14)
        }
        .onAppear {
            animationAmount = 2
        }
    }
}

#Preview("RoutePulseView") {
    ZStack {
        Color.green.opacity(0.3) // 模拟地图背景
        RoutePulseView()
    }
    .frame(width: 100, height: 100)
}
