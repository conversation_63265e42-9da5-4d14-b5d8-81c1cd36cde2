import SwiftUI

/// SpeedX序号缺失警告组件
struct SpeedXSequenceAlert: View {
    let missingNumbers: [Int]
    let incompleteNumbers: [Int]
    let onRetakePhoto: () -> Void
    let onContinue: () -> Void
    let onCancel: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                    .font(.title2)

                Text(NSLocalizedString("speedx_sequence_check", comment: "SpeedX序号检查"))
                    .font(.headline)
                    .foregroundColor(.primary)
            }
            .padding(.top)

            // 分隔线
            Divider()

            // 内容
            VStack(alignment: .leading, spacing: 16) {
                if !missingNumbers.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                                .font(.caption)

                            Text(NSLocalizedString("missing_stops", comment: "缺失的停靠点:"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }

                        Text(String(format: NSLocalizedString("stops_not_found", comment: "停靠点未找到"), formatNumbers(missingNumbers)))
                            .font(.system(.subheadline, design: .monospaced))
                            .foregroundColor(.red)
                            .padding(.leading, 20)
                    }
                }

                if !incompleteNumbers.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "exclamationmark.circle.fill")
                                .foregroundColor(.orange)
                                .font(.caption)

                            Text(NSLocalizedString("incomplete_stops", comment: "不完整的停靠点:"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }

                        Text(String(format: NSLocalizedString("stops_truncated", comment: "停靠点信息被截断"), formatNumbers(incompleteNumbers)))
                            .font(.system(.subheadline, design: .monospaced))
                            .foregroundColor(.orange)
                            .padding(.leading, 20)
                    }
                }

                // 建议
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.blue)
                            .font(.caption)

                        Text(NSLocalizedString("suggestions", comment: "建议:"))
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(NSLocalizedString("retake_photo_include_all", comment: "重新截图确保包含所有停靠点"))
                        Text(NSLocalizedString("check_missing_info", comment: "确保图片没有遗漏相关信息"))
                        Text(NSLocalizedString("process_in_batches", comment: "可以分段截图然后合并处理"))
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 20)
                }
            }
            .padding(.horizontal)

            // 按钮
            VStack(spacing: 12) {
                // 主要操作按钮
                Button(action: onRetakePhoto) {
                    HStack {
                        Image(systemName: "camera.fill")
                        Text(NSLocalizedString("retake_photo", comment: "重新截图"))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }

                // 次要操作按钮
                HStack(spacing: 12) {
                    Button(action: onContinue) {
                        Text(NSLocalizedString("continue_use", comment: "继续使用"))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                    }

                    Button(action: onCancel) {
                        Text(NSLocalizedString("cancel", comment: "取消"))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 10)
        .padding(.horizontal, 20)
    }

    private func formatNumbers(_ numbers: [Int]) -> String {
        if numbers.count <= 5 {
            return numbers.map(String.init).joined(separator: ", ")
        } else {
            let first5 = Array(numbers.prefix(5))
            let formattedFirst5 = first5.map(String.init).joined(separator: ", ")
            return String(format: NSLocalizedString("and_more_stops", comment: "等更多停靠点"), formattedFirst5, numbers.count)
        }
    }
}

// 包装器视图，用于在SwiftUI中显示SpeedX序号警告
struct SpeedXSequenceAlertWrapper: ViewModifier {
    @Binding var isPresented: Bool
    let missingNumbers: [Int]
    let incompleteNumbers: [Int]
    let onRetakePhoto: () -> Void
    let onContinue: () -> Void

    func body(content: Content) -> some View {
        ZStack {
            content
                .blur(radius: isPresented ? 3 : 0)

            if isPresented {
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .transition(.opacity)

                SpeedXSequenceAlert(
                    missingNumbers: missingNumbers,
                    incompleteNumbers: incompleteNumbers,
                    onRetakePhoto: {
                        withAnimation {
                            isPresented = false
                        }
                        onRetakePhoto()
                    },
                    onContinue: {
                        withAnimation {
                            isPresented = false
                        }
                        onContinue()
                    },
                    onCancel: {
                        withAnimation {
                            isPresented = false
                        }
                    }
                )
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut, value: isPresented)
    }
}

// 扩展View以添加SpeedX序号警告
extension View {
    func speedXSequenceAlert(
        isPresented: Binding<Bool>,
        missingNumbers: [Int],
        incompleteNumbers: [Int] = [],
        onRetakePhoto: @escaping () -> Void,
        onContinue: @escaping () -> Void
    ) -> some View {
        self.modifier(
            SpeedXSequenceAlertWrapper(
                isPresented: isPresented,
                missingNumbers: missingNumbers,
                incompleteNumbers: incompleteNumbers,
                onRetakePhoto: onRetakePhoto,
                onContinue: onContinue
            )
        )
    }
}

#Preview("SpeedXSequenceAlert") {
    ZStack {
        Color.gray.opacity(0.2).edgesIgnoringSafeArea(.all)

        SpeedXSequenceAlert(
            missingNumbers: [3, 5, 7],
            incompleteNumbers: [9],
            onRetakePhoto: {},
            onContinue: {},
            onCancel: {}
        )
    }
}
