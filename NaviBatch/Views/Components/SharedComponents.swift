import SwiftUI

/// 过滤器芯片组件 - 用于各种列表的过滤选择
struct FilterChip: View {
    let title: String
    var icon: String? = nil
    var color: Color = .blue
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.caption)
                }
                
                Text(title)
                    .font(.caption)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? color.opacity(0.2) : Color.gray.opacity(0.1))
            .foregroundColor(isSelected ? color : .primary)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? color : Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
    }
}

#Preview("SharedComponents") {
    HStack {
        FilterChip(
            title: "全部",
            isSelected: true,
            action: {}
        )
        
        FilterChip(
            title: "家",
            icon: "house.fill",
            color: .blue,
            isSelected: false,
            action: {}
        )
        
        FilterChip(
            title: "公司",
            icon: "briefcase.fill",
            color: .orange,
            isSelected: false,
            action: {}
        )
    }
    .padding()
}
