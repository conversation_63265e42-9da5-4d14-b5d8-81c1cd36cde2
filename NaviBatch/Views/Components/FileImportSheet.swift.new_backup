import SwiftUI
import CoreLocation
import os.log
import SwiftData
import UIKit
import UniformTypeIdentifiers
import Combine
import Foundation
import MapKit

// 定义Apple地理编码选项常量
private let CLGeocodeAppleOptionCountryKey = "country"
private let CLGeocodeAppleOptionStateKey = "state"
private let CLGeocodeAppleOptionPostalCodeKey = "postalCode"

// 警告项
fileprivate struct FileImportAlertItem: Identifiable {
    let id = UUID()
    let title: String
    let message: String
    let primaryButton: Alert.Button
    let secondaryButton: Alert.Button?
    let cancelButton: Alert.Button?

    // 基本初始化方法，只有一个按钮
    init(title: String = "prompt".localized, message: String, primaryButton: Alert.Button = .default(Text("confirm".localized))) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = nil
        self.cancelButton = nil
    }

    // 带两个按钮的初始化方法
    init(title: String = "prompt".localized, message: String, primaryButton: Alert.Button, cancelButton: Alert.Button) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = nil
        self.cancelButton = cancelButton
    }

    // 带三个按钮的初始化方法
    init(title: String = "prompt".localized, message: String, primaryButton: Alert.Button, secondaryButton: Alert.Button, cancelButton: Alert.Button) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = secondaryButton
        self.cancelButton = cancelButton
    }
}

/// 文件导入表单
/// 支持从CSV文件导入地址数据
struct FileImportSheet: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isImporting: Bool = false
    @State private var isProcessing: Bool = false
    @State private var importedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = [] // 地址, 坐标, 是否选中, 警告信息
    @State private var processingProgress: Double = 0
    @State private var errorMessage: String? = nil
    @State private var alertItem: FileImportAlertItem? = nil
    @State private var showLimitExceededSheet: Bool = false
    @State private var limitExceededInfo: (currentCount: Int, remainingSlots: Int, maxAllowed: Int, selectedCount: Int, selectedAddresses: [(String, CLLocationCoordinate2D)])? = nil
    // 保留fileType变量但不再使用选择器，自动检测文件类型
    @State private var fileType: FileType = .csv // 默认值，实际上不再使用
    @State private var showingDocumentPicker: Bool = false
    @State private var companyName: String = ""
    
    // 获取用户位置
    @ObservedObject private var locationManager = LocationManager.shared
    
    // 回调函数
    var onAddressesImported: ([(String, CLLocationCoordinate2D)]) -> Void
    
    // 文件类型
    enum FileType: String, CaseIterable, Identifiable {
        case csv = "CSV"
        case txt = "TXT"
        case json = "JSON"
        
        var id: String { self.rawValue }
        
        var utType: UTType {
            switch self {
            case .csv: return UTType.commaSeparatedText
            case .txt: return UTType.plainText
            case .json: return UTType.json
            }
        }
        
        var icon: String {
            switch self {
            case .csv: return "tablecells"
            case .txt: return "doc.text"
            case .json: return "curlybraces"
            }
        }
    }
    
    // 计算选中的地址数量
    private var selectedAddressCount: Int {
        importedAddresses.filter { $0.2 }.count
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 移除文件类型选择器，改为显示支持的文件类型信息
                Text("supports_file_types".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                
                // 文件选择区域
                VStack {
                    if isProcessing {
                        // 处理中显示进度
                        ProgressView(value: processingProgress, total: 1.0) {
                            Text("正在处理地址...")
                        }
                        .progressViewStyle(LinearProgressViewStyle())
                        .padding()
                    } else if !importedAddresses.isEmpty {
                        // 已导入地址列表
                        VStack(alignment: .leading) {
                            HStack {
                                Text(String(format: "imported_addresses_count".localized, importedAddresses.count))
                                    .font(.headline)
                                
                                Spacer()
                                
                                // 全选/全不选切换按钮 - 使用单个切换按钮
                                Toggle(isOn: Binding(
                                    get: { importedAddresses.allSatisfy { $0.2 } },
                                    set: { selectAllAddresses($0) }
                                )) {
                                    Text("select_all".localized)
                                        .font(.subheadline)
                                        .foregroundColor(.blue)
                                }
                                .toggleStyle(SwitchToggleStyle(tint: .blue))
                                .labelsHidden()
                                .fixedSize()
                            }
                            .padding(.horizontal)
                            
                            // 地址列表
                            List {
                                ForEach(0..<importedAddresses.count, id: \.self) { index in
                                    let (address, coordinate, isSelected, warning) = importedAddresses[index]
                                    HStack(spacing: 10) {
                                        // 使用Toggle替代Button，更符合iOS设计规范
                                        Toggle(isOn: Binding(
                                            get: { isSelected },
                                            set: { _ in toggleAddressSelection(at: index) }
                                        )) {}
                                            .toggleStyle(CheckboxToggleStyle())
                                            .labelsHidden()
                                        
                                        // 地址信息 - 简化显示
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(address)
                                                .lineLimit(1)
                                                .font(.subheadline)
                                            
                                            // 坐标信息和警告信息在同一行
                                            HStack(spacing: 6) {
                                                // 简化坐标显示
                                                Text(String(format: "(%.4f, %.4f)", coordinate.latitude, coordinate.longitude))
                                                    .font(.caption)
                                                    .foregroundColor(.secondary)
                                                
                                                // 如果有警告，显示警告图标和文本
                                                if !warning.isEmpty || (coordinate.latitude == 0 && coordinate.longitude == 0) {
                                                    Image(systemName: "exclamationmark.triangle")
                                                        .foregroundColor(.orange)
                                                        .font(.caption)
                                                    
                                                    if !warning.isEmpty {
                                                        Text(warning)
                                                            .font(.caption)
                                                            .foregroundColor(.orange)
                                                            .lineLimit(1)
                                                    }
                                                }
                                            }
                                            
                                            // 显示与用户位置的距离（如果有）
                                            if let userLocation = locationManager.userLocation {
                                                let userLocationObj = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
                                                let pointLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
                                                let distance = userLocationObj.distance(from: pointLocation)
                                                
                                                Text(formatDistance(distance))
                                                    .font(.caption)
                                                    .foregroundColor(distance <= 200_000 ? .green : .orange)
                                            }
                                        }
                                        
                                        Spacer()
                                    }
                                    .padding(.vertical, 2)
                                }
                            }
                            .listStyle(PlainListStyle())
                        }
                    } else {
                        VStack(spacing: 16) {
                            // 文件选择按钮
                            Button {
                                // 直接触发文件选择器
                                showingDocumentPicker = true
                            } label: {
                                VStack(spacing: 16) {
                                    Image(systemName: "doc.badge.plus")
                                        .font(.system(size: 50))
                                        .foregroundColor(.blue)
                                    
                                    Text("tap_to_select_file".localized)
                                        .font(.headline)
                                        .foregroundColor(.blue)
                                }
                                .frame(maxWidth: .infinity)
                                .aspectRatio(1.5, contentMode: .fit)
                                .padding()
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(12)
                                
                                // 样例文件下载选项
                                Menu {
                                    Button {
                                        downloadSampleFile("csv")
                                    } label: {
                                        Label("csv_format".localized, systemImage: "tablecells")
                                    }
                                    
                                    Button {
                                        downloadSampleFile("txt")
                                    } label: {
                                        Label("txt_format".localized, systemImage: "doc.text")
                                    }
                                    
                                    Button {
                                        downloadSampleFile("json")
                                    } label: {
                                        Label("json_format".localized, systemImage: "curlybraces")
                                    }
                                } label: {
                                    Label("download_sample_files".localized, systemImage: "arrow.down.doc")
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(Color.blue.opacity(0.8))
                                        .foregroundColor(.white)
                                        .cornerRadius(8)
                                }
                                .buttonStyle(BorderlessButtonStyle())
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                            .padding(.horizontal)
                            // 移除自动打开文件选择器的代码
                            .onAppear {
                                Task {
                                    await logInfo("FileImportSheet - 视图已加载，等待用户选择文件")
                                }
                            }
                        }
                    }
                        .frame(maxHeight: .infinity)
                    
                    // 公司名称输入
                    if !importedAddresses.isEmpty {
                        VStack(alignment: .leading) {
                            Text("company_name_optional".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            TextField("input_company_name".localized, text: $companyName)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        .padding(.horizontal)
                    }
                    
                    // 移除底部按钮区域，按钮已移至导航栏
                    Spacer().frame(height: 16)
                }
                .navigationTitle("import_addresses".localized)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    // 左侧取消按钮
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("cancel".localized) {
                            dismiss()
                        }
                    }
                    
                    // 右侧导入按钮 - 移除条件判断，确保按钮始终显示
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("import_addresses".localized) {
                            importSelectedAddresses()
                        }
                        .font(.headline)
                        .disabled(selectedAddressCount == 0)
                        .opacity(!importedAddresses.isEmpty ? 1.0 : 0.5) // 使用透明度而不是条件渲染
                    }
                }
                .alert(item: $alertItem) { item in
                    if let secondaryButton = item.secondaryButton, let _ = item.cancelButton {
                        // 三个按钮的警告框
                        return Alert(
                            title: Text(item.title),
                            message: Text(item.message),
                            primaryButton: item.primaryButton,
                            secondaryButton: secondaryButton
                        )
                    } else if let secondaryButton = item.cancelButton {
                        // 两个按钮的警告框
                        return Alert(
                            title: Text(item.title),
                            message: Text(item.message),
                            primaryButton: item.primaryButton,
                            secondaryButton: secondaryButton
                        )
                    } else {
                        // 单个按钮的警告框
                        return Alert(
                            title: Text(item.title),
                            message: Text(item.message),
                            dismissButton: item.primaryButton
                        )
                    }
                }
                .fileImporter(
                    isPresented: $showingDocumentPicker,
                    allowedContentTypes: FileType.allCases.map { $0.utType },
                    allowsMultipleSelection: false
                ) { result in
                    handleFileImport(result)
                }
                .onDisappear {
                    // 清理资源
                    cleanupResources()
                }
                .sheet(isPresented: $showLimitExceededSheet) {
                    if let info = limitExceededInfo {
                        AddressLimitExceededSheet(
                            currentCount: info.currentCount,
                            remainingSlots: info.remainingSlots,
                            maxAllowed: info.maxAllowed,
                            selectedCount: info.selectedCount,
                            isFreeUser: SubscriptionManager.shared.currentTier == .free,
                            selectedAddresses: info.selectedAddresses,
                            onImportLimited: { limitedAddresses in
                                // 记录实际导入的地址数量
                                Task {
                                    await logInfo("FileImportSheet - 导入地址数量限制为\(info.remainingSlots)个，实际导入\(limitedAddresses.count)个")
                                }
                                if !limitedAddresses.isEmpty {
                                    self.onAddressesImported(limitedAddresses)
                                } else {
                                    // 显示错误提示
                                    alertItem = FileImportAlertItem(
                                        title: "导入失败",
                                        message: "没有可导入的地址，请检查地址限制"
                                    )
                                    return
                                }
                                self.dismiss()
                            },
                            onUpgrade: {
                                // 显示订阅界面
                                NotificationCenter.default.post(
                                    name: Notification.Name("ShowSubscriptionView"),
                                    object: nil
                                )
                                self.dismiss()
                            },
                            onCancel: {
                                // 关闭表单
                                showLimitExceededSheet = false
                            }
                        )
                        // 为限制超出提示表单添加半屏Sheet样式
                        .presentationDetents([.medium])
                        .presentationDragIndicator(.visible)
                        .interactiveDismissDisabled(false) // 允许交互式关闭
                    }
                }
            }
            // 添加半屏Sheet样式 - 只使用medium尺寸
            .presentationDetents([.medium])
            .presentationDragIndicator(.visible)
        }
        
        // 地址限制超出提示表单
        struct AddressLimitExceededSheet: View {
            let currentCount: Int
            let remainingSlots: Int
            let maxAllowed: Int
            let selectedCount: Int
            let isFreeUser: Bool
            let selectedAddresses: [(String, CLLocationCoordinate2D)]
            let onImportLimited: ([(String, CLLocationCoordinate2D)]) -> Void
            let onUpgrade: () -> Void
            let onCancel: () -> Void
            
            @Environment(\.dismiss) private var dismiss
            
            var body: some View {
                NavigationView {
                    VStack(spacing: 20) {
                        // 图标
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)
                            .padding()
                        
                        // 信息
                        VStack(alignment: .leading, spacing: 10) {
                            if isFreeUser {
                                Text("free_version_address_limit".localized)
                                    .font(.body)
                                
                                Text("current_address_count".localized, currentCount)
                                    .font(.body)
                                
                                if remainingSlots <= 0 {
                                    Text("selected_addresses_count".localized, selectedCount)
                                        .font(.body)
                                        .foregroundColor(.blue)
                                } else {
                                    Text("exceed_address_limit".localized, selectedCount - remainingSlots)
                                        .font(.body)
                                        .foregroundColor(.red)
                                }
                                
                                Text("upgrade_to_pro".localized)
                                    .font(.body)
                                    .bold()
                                    .padding(.top, 5)
                            } else {
                                Text("address_limit_exceeded".localized)
                                    .font(.body)
                                
                                if remainingSlots <= 0 {
                                    Text("selected_addresses_count".localized, selectedCount)
                                        .font(.body)
                                        .foregroundColor(.blue)
                                } else {
                                    Text("exceed_address_limit".localized, selectedCount - remainingSlots)
                                        .font(.body)
                                        .foregroundColor(.red)
                                }
                            }
                        }
                        .padding(.horizontal)
                        
                        Spacer()
                    }
                    .padding()
                    .navigationTitle(isFreeUser ? "free_version_address_limit" : "address_limit_exceeded")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        // 左侧取消按钮
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("cancel".localized) {
                                onCancel()
                            }
                        }
                        
                        // 右侧按钮
                        ToolbarItem(placement: .navigationBarTrailing) {
                            if remainingSlots <= 0 {
                                // 当剩余槽位为0时，但仍然允许导入已选择的地址
                                Button("import_selected_addresses".localized) {
                                    // 直接导入所有选择的地址
                                    onImportLimited(selectedAddresses)
                                }
                                .foregroundColor(.blue)
                            } else if selectedCount > remainingSlots {
                                Button(String(format: "import_limited_addresses".localized, remainingSlots)) {
                                    // 创建限制后的地址列表
                                    let limitedAddresses = Array(selectedAddresses.prefix(remainingSlots))
                                    onImportLimited(limitedAddresses)
                                }
                                .foregroundColor(.blue)
                            } else {
                                Button("导入全部\(selectedCount)个") {
                                    // 导入所有选择的地址
                                    onImportLimited(selectedAddresses)
                                }
                                .foregroundColor(.blue)
                            }
                        }
                    }
                    .toolbar {
                        // 底部工具栏 - 仅显示升级按钮（如果是免费用户）
                        ToolbarItemGroup(placement: .bottomBar) {
                            if isFreeUser {
                                Spacer()
                                Button(action: {
                                    onUpgrade()
                                }) {
                                    Text("升级到高级版")
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 20)
                                        .padding(.vertical, 10)
                                        .background(Color.green)
                                        .cornerRadius(8)
                                }
                                Spacer()
                            }
                        }
                    }
                }
            }
        }
        
        // 清理资源
        private func cleanupResources() {
            // 取消所有正在进行的地理编码请求
            CLGeocoder().cancelGeocode()
            
            // 记录清理操作
            Task {
                await logInfo("FileImportSheet - 已清理资源，取消所有地理编码请求")
            }
        }
        
        // 全选/全不选地址
        private func selectAllAddresses(_ selected: Bool) {
            importedAddresses = importedAddresses.map { (address, coordinate, _, warning) in
                (address, coordinate, selected, warning)
            }
        }
        
        // 切换地址选择状态
        private func toggleAddressSelection(at index: Int) {
            guard index < importedAddresses.count else { return }
            
            let (address, coordinate, isSelected, warning) = importedAddresses[index]
            importedAddresses[index] = (address, coordinate, !isSelected, warning)
        }
        
        // 导入选中的地址
        private func importSelectedAddresses() {
            let selectedAddresses = importedAddresses
                .filter { $0.2 } // 只选择已选中的地址
                .map { ($0.0, $0.1) } // 转换为(地址, 坐标)元组
            
            if selectedAddresses.isEmpty {
                // 显示错误提示
                alertItem = FileImportAlertItem(
                    title: "无法导入",
                    message: "请至少选择一个地址"
                )
                return
            }
            
            // 检查订阅限制
            let subscriptionManager = SubscriptionManager.shared
            let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute
            
            Task {
                await logInfo("FileImportSheet - 当前订阅级别: \(subscriptionManager.currentTier.rawValue), 最大允许地址数: \(maxAllowed)")
            }
            
            // 获取当前路线
            if let currentRoute = getCurrentRoute() {
                let currentCount = currentRoute.points.count
                let remainingSlots = maxAllowed - currentCount
                
                Task {
                    await logInfo("FileImportSheet - 当前路线: \(currentRoute.name), 当前地址数: \(currentCount), 剩余槽位: \(remainingSlots)")
                    await logInfo("FileImportSheet - 选择的地址数: \(selectedAddresses.count), 是否超出限制: \(selectedAddresses.count > remainingSlots)")
                    
                    // 检查是否超出限制
                    if selectedAddresses.count > 0 && remainingSlots <= 0 {
                        await logInfo("FileImportSheet - 剩余槽位为0，任何数量的地址都无法添加")
                    }
                }
                
                if selectedAddresses.count > remainingSlots {
                    // 保存超出限制的信息，并显示自定义表单
                    limitExceededInfo = (
                        currentCount: currentCount,
                        remainingSlots: remainingSlots,
                        maxAllowed: maxAllowed,
                        selectedCount: selectedAddresses.count,
                        selectedAddresses: selectedAddresses
                    )
                    
                    // 显示自定义表单
                    showLimitExceededSheet = true
                    return
                }
            }
            
            // 调用回调函数
            onAddressesImported(selectedAddresses)
            
            // 关闭表单
            dismiss()
        }
        
        // 获取当前路线
        private func getCurrentRoute() -> Route? {
            // 使用共享的持久化ModelContainer
            let container = getPersistentContainer()
            let context = container.mainContext
            
            do {
                let descriptor = FetchDescriptor<Route>()
                let routes = try context.fetch(descriptor)
                
                if routes.isEmpty {
                    Task {
                        await logInfo("FileImportSheet - 数据库中没有找到路线")
                    }
                    return nil
                }
                
                Task {
                    await logInfo("FileImportSheet - 成功获取路线: \(routes.first?.name ?? "未命名"), ID=\(routes.first?.id.uuidString ?? "无ID")")
                }
                return routes.first
            } catch {
                Task {
                    await logError("FileImportSheet - 获取当前路线失败: \(error.localizedDescription)")
                }
                return nil
            }
        }
        
        // 格式化距离
        private func formatDistance(_ distance: Double) -> String {
            if distance >= 1000 {
                return String(format: "距离: %.1f公里", distance / 1000)
            } else {
                return String(format: "距离: %.0f米", distance)
            }
        }
        
        // 处理文件导入
        private func handleFileImport(_ result: Result<[URL], Error>) {
            do {
                // 获取文件URL
                guard let selectedFile = try result.get().first else {
                    Task {
                        await logError("FileImportSheet - 未选择文件")
                    }
                    return
                }
                
                // 开始导入
                isImporting = true
                
                // 读取文件内容
                if selectedFile.startAccessingSecurityScopedResource() {
                    defer { selectedFile.stopAccessingSecurityScopedResource() }
                    
                    // 读取文件数据
                    let data = try Data(contentsOf: selectedFile)
                    
                    // 自动检测文件类型并处理数据
                    let fileExtension = selectedFile.pathExtension.lowercased()
                    
                    Task {
                        await logInfo("FileImportSheet - 检测到文件类型: \(fileExtension)")
                    }
                    
                    if fileExtension == "csv" {
                        processCSVData(data)
                    } else if fileExtension == "txt" {
                        processTextData(data)
                    } else if fileExtension == "json" {
                        processJSONData(data)
                    } else if fileExtension == "xlsx" || fileExtension == "xls" {
                        // Excel格式暂不支持
                        processExcelData(data)
                        return
                    } else {
                        // 尝试根据内容判断文件类型
                        if let content = String(data: data, encoding: .utf8) {
                            if content.contains("{") && content.contains("}") && (content.contains("\"address\"") || content.contains("\"addresses\"")) {
                                Task {
                                    await logInfo("FileImportSheet - 根据内容判断为JSON文件")
                                }
                                processJSONData(data)
                            } else if content.contains(",") {
                                Task {
                                    await logInfo("FileImportSheet - 根据内容判断为CSV文件")
                                }
                                processCSVData(data)
                            } else {
                                Task {
                                    await logInfo("FileImportSheet - 根据内容判断为TXT文件")
                                }
                                processTextData(data)
                            }
                        } else {
                            // 无法识别的文件类型
                            processExcelData(data)
                            return
                        }
                    }
                } else {
                    throw NSError(domain: "FileImportSheet", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法访问文件"])
                }
            } catch {
                isImporting = false
                alertItem = FileImportAlertItem(
                    title: "导入失败",
                    message: "无法读取文件: \(error.localizedDescription)"
                )
                Task {
                    await logError("FileImportSheet - 文件导入失败: \(error.localizedDescription)")
                }
            }
        }
        
        // 处理CSV数据
        private func processCSVData(_ data: Data) {
            // 将数据转换为字符串
            guard let content = String(data: data, encoding: .utf8) else {
                Task { @MainActor in
                    showError("无法读取CSV文件内容")
                }
                return
            }
            
            // 开始处理 - 确保在主线程上更新UI
            Task { @MainActor in
                isProcessing = true
                processingProgress = 0.0
            }
            
            // 分割行
            let lines = content.components(separatedBy: .newlines)
                .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            
            // 处理每一行
            Task {
                var processedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = []
                var successCount = 0
                var warningCount = 0
                
                // 分批处理地址，避免同时发送太多地理编码请求
                let batchSize = 5 // 每批处理5个地址
                let totalLines = lines.count
                
                for batchStart in stride(from: 0, to: totalLines, by: batchSize) {
                    let batchEnd = min(batchStart + batchSize, totalLines)
                    let currentBatch = lines[batchStart..<batchEnd]
                    
                    // 更新进度
                    let progress = Double(batchStart) / Double(totalLines)
                    await MainActor.run {
                        processingProgress = progress
                    }
                    
                    // 并行处理当前批次中的每一行
                    await withTaskGroup(of: (Int, String, CLLocationCoordinate2D, Bool, String).self) { group in
                        for (batchIndex, line) in currentBatch.enumerated() {
                            let lineIndex = batchStart + batchIndex
                            group.addTask {
                                return await self.processCSVLine(line, index: lineIndex, totalLines: totalLines)
                            }
                        }
                        
                        // 收集结果
                        for await (index, address, coordinate, isSelected, warning) in group {
                            // 记录处理结果
                            if warning.isEmpty {
                                successCount += 1
                            } else {
                                warningCount += 1
                            }
                            
                            // 保存处理结果，确保按原始顺序
                            while processedAddresses.count <= index {
                                // 填充空位，确保索引对齐
                                processedAddresses.append(("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, ""))
                            }
                            processedAddresses[index] = (address, coordinate, isSelected, warning)
                        }
                    }
                    
                    // 在批次之间添加延迟，避免超出API限制
                    if batchEnd < totalLines {
                        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                    }
                }
                
                // 移除任何空位
                processedAddresses = processedAddresses.filter { !$0.0.isEmpty }
                
                // 更新UI
                await MainActor.run {
                    importedAddresses = processedAddresses
                    isProcessing = false
                    isImporting = false
                    
                    // 显示导入结果
                    if processedAddresses.isEmpty {
                        alertItem = FileImportAlertItem(
                            title: "导入结果",
                            message: "未找到有效地址"
                        )
                    } else {
                        // 显示导入结果摘要
                        let totalCount = processedAddresses.count
                        
                        if warningCount > 0 {
                            alertItem = FileImportAlertItem(
                                title: "导入结果",
                                message: "成功导入 \(totalCount) 个地址，其中 \(successCount) 个地址坐标正常，\(warningCount) 个地址有警告。\n\n有警告的地址已标记，您可以在导入后手动修复。"
                            )
                        }
                    }
                }
            }
        }
        
        // 处理单行CSV数据
        private func processCSVLine(_ line: String, index: Int, totalLines: Int) async -> (Int, String, CLLocationCoordinate2D, Bool, String) {
            // 分割CSV行
            let components = line.components(separatedBy: ",")
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            
            // 检查是否有足够的组件
            guard !components.isEmpty else {
                return (index, "", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, "无效的CSV行")
            }
            
            // 尝试提取地址和坐标
            if components.count >= 3,
               let lat = Double(components[1]),
               let lon = Double(components[2]) {
                // CSV包含坐标
                let address = components[0]
                let coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lon)
                
                // 基于用户位置验证坐标
                var warningMessage = ""
                
                if let userLocation = locationManager.userLocation {
                    // 计算与用户位置的距离
                    let userLocationObj = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
                    let pointLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
                    let distance = userLocationObj.distance(from: pointLocation)
                    
                    // 验证范围（200公里 = 200,000米）
                    if distance > 200_000 {
                        warningMessage = "距离当前位置超过200公里"
                        
                        // 尝试地理编码获取更准确的坐标
                        let newCoordinate = await geocodeAddressWithRetry(address)
                        if newCoordinate.latitude != 0 || newCoordinate.longitude != 0 {
                            // 使用地理编码获取的新坐标
                            return (index, address, newCoordinate, true, "")
                        } else {
                            // 使用原始坐标，但带有警告
                            return (index, address, coordinate, true, warningMessage)
                        }
                    } else {
                        // 坐标在有效范围内
                        return (index, address, coordinate, true, "")
                    }
                } else {
                    // 没有用户位置，使用传统的澳大利亚范围验证
                    let australiaLatRange = (-43.**********, -10.**********)
                    let australiaLonRange = (113.*********, 153.*********)
                    let isInAustralia = lat >= australiaLatRange.0 && lat <= australiaLatRange.1 &&
                    lon >= australiaLonRange.0 && lon <= australiaLonRange.1
                    
                    if isInAustralia {
                        return (index, address, coordinate, true, "")
                    } else {
                        warningMessage = "坐标不在澳大利亚范围内"
                        return (index, address, coordinate, true, warningMessage)
                    }
                }
            } else if !components[0].isEmpty {
                // 只有地址，需要地理编码
                let address = components[0]
                
                // 使用地理编码获取坐标
                let coordinate = await geocodeAddressWithRetry(address)
                
                if coordinate.latitude == 0 && coordinate.longitude == 0 {
                    // 地理编码失败，使用默认坐标
                    let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16) // Glen Waverley中心点
                    return (index, address, defaultCoordinate, true, "无法获取地址坐标")
                } else {
                    // 地理编码成功
                    return (index, address, coordinate, true, "")
                }
            } else {
                // 无效行
                return (index, "", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, "无效的CSV行")
            }
        }
        
        // 带重试机制的地理编码
        private func geocodeAddressWithRetry(_ address: String, retryCount: Int = 3) async -> CLLocationCoordinate2D {
            // 增强地址，确保包含澳大利亚信息
            var enhancedAddress = address
            if !address.lowercased().contains("australia") {
                // 检查是否包含Glen Waverley
                if address.lowercased().contains("glen waverley") {
                    enhancedAddress = "\(address), VIC 3150, Australia"
                } else if address.lowercased().contains("mount waverley") || address.lowercased().contains("mt waverley") {
                    enhancedAddress = "\(address), VIC 3149, Australia"
                } else if address.lowercased().contains("clayton") {
                    enhancedAddress = "\(address), VIC 3168, Australia"
                } else if address.lowercased().contains("oakleigh") {
                    enhancedAddress = "\(address), VIC 3166, Australia"
                } else {
                    enhancedAddress = "\(address), Glen Waverley, VIC 3150, Australia"
                }
            }
            
            // 创建墨尔本区域限制
            let melbourneCenter = CLLocationCoordinate2D(latitude: -37.8796, longitude: 145.1631)
            let melbourneRegion = CLCircularRegion(center: melbourneCenter, radius: 50000, identifier: "Melbourne_SE")
            
            // 尝试地理编码
            for attempt in 1...retryCount {
                do {
                    let geocoder = CLGeocoder()
                    
                    // 尝试使用区域限制进行地理编码
                    var placemarks: [CLPlacemark] = []
                    
                    do {
                        // 首先尝试使用区域限制和澳大利亚语言环境
                        placemarks = try await geocoder.geocodeAddressString(
                            enhancedAddress,
                            in: melbourneRegion,
                            preferredLocale: Locale(identifier: "en_AU")
                        )
                    } catch {
                        // 如果失败，尝试不使用区域限制
                        await logInfo("使用区域限制地理编码失败，尝试不使用区域限制: \(enhancedAddress)")
                        placemarks = try await geocoder.geocodeAddressString(
                            enhancedAddress,
                            in: nil,
                            preferredLocale: Locale(identifier: "en_AU")
                        )
                    }
                    
                    // 优先选择澳大利亚的结果
                    if let australiaPlacemark = placemarks.first(where: { $0.country == "Australia" }),
                       let location = australiaPlacemark.location?.coordinate {
                        return location
                    } else if let placemark = placemarks.first,
                              let location = placemark.location?.coordinate {
                        // 非澳大利亚结果
                        return location
                    }
                    
                    // 没有找到结果，准备重试
                    await logError("地理编码未返回结果: \(address)，尝试 \(attempt)/\(retryCount)")
                } catch {
                    // 地理编码失败
                    await logError("地理编码失败: \(address), 错误: \(error.localizedDescription)，尝试 \(attempt)/\(retryCount)")
                    
                    // 如果是超时错误(kCLErrorDomain error 8)，等待更长时间再重试
                    let nsError = error as NSError
                    if nsError.domain == kCLErrorDomain && nsError.code == 8 {
                        // 超时错误，等待更长时间
                        try? await Task.sleep(nanoseconds: UInt64(attempt) * 1_000_000_000) // 1-3秒，随重试次数增加
                    } else {
                        // 其他错误，等待较短时间
                        try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
                    }
                }
            }
            
            // 所有重试都失败，返回默认坐标
            return CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16) // Glen Waverley中心点
        }
        
        // 处理文本数据
        private func processTextData(_ data: Data) {
            // 将数据转换为字符串
            guard let content = String(data: data, encoding: .utf8) else {
                Task { @MainActor in
                    showError("无法读取文本文件内容")
                }
                return
            }
            
            // 开始处理
            Task { @MainActor in
                isProcessing = true
                processingProgress = 0.0
            }
            
            // 分割行
            let lines = content.components(separatedBy: .newlines)
                .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            
            // 处理每一行
            Task {
                var processedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = []
                var successCount = 0
                var warningCount = 0
                
                // 分批处理地址，避免同时发送太多地理编码请求
                let batchSize = 5 // 每批处理5个地址
                let totalLines = lines.count
                
                for batchStart in stride(from: 0, to: totalLines, by: batchSize) {
                    let batchEnd = min(batchStart + batchSize, totalLines)
                    let currentBatch = lines[batchStart..<batchEnd]
                    
                    // 更新进度
                    let progress = Double(batchStart) / Double(totalLines)
                    await MainActor.run {
                        processingProgress = progress
                    }
                    
                    // 并行处理当前批次中的每一行
                    await withTaskGroup(of: (Int, String, CLLocationCoordinate2D, Bool, String).self) { group in
                        for (batchIndex, line) in currentBatch.enumerated() {
                            let lineIndex = batchStart + batchIndex
                            group.addTask {
                                let address = line.trimmingCharacters(in: .whitespacesAndNewlines)
                                
                                // 跳过空行
                                guard !address.isEmpty else {
                                    return (lineIndex, "", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, "空地址")
                                }
                                
                                // 使用地理编码获取坐标
                                let coordinate = await geocodeAddressWithRetry(address)
                                
                                if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                    // 地理编码失败，使用默认坐标
                                    let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16) // Glen Waverley中心点
                                    return (lineIndex, address, defaultCoordinate, true, "无法获取地址坐标")
                                } else {
                                    // 地理编码成功
                                    return (lineIndex, address, coordinate, true, "")
                                }
                            }
                        }
                        
                        // 收集结果
                        for await (index, address, coordinate, isSelected, warning) in group {
                            // 跳过空地址
                            if address.isEmpty { continue }
                            
                            // 记录处理结果
                            if warning.isEmpty {
                                successCount += 1
                            } else {
                                warningCount += 1
                            }
                            
                            // 保存处理结果，确保按原始顺序
                            while processedAddresses.count <= index {
                                // 填充空位，确保索引对齐
                                processedAddresses.append(("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, ""))
                            }
                            processedAddresses[index] = (address, coordinate, isSelected, warning)
                        }
                    }
                    
                    // 在批次之间添加延迟，避免超出API限制
                    if batchEnd < totalLines {
                        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                    }
                }
                
                // 移除任何空位
                processedAddresses = processedAddresses.filter { !$0.0.isEmpty }
                
                // 更新UI
                await MainActor.run {
                    importedAddresses = processedAddresses
                    isProcessing = false
                    isImporting = false
                    
                    // 显示导入结果
                    if processedAddresses.isEmpty {
                        alertItem = FileImportAlertItem(
                            title: "导入结果",
                            message: "未找到有效地址"
                        )
                    } else {
                        // 显示导入结果摘要
                        let totalCount = processedAddresses.count
                        
                        if warningCount > 0 {
                            alertItem = FileImportAlertItem(
                                title: "导入结果",
                                message: "成功导入 \(totalCount) 个地址，其中 \(successCount) 个地址坐标正常，\(warningCount) 个地址无法获取坐标。\n\n无法获取坐标的地址已标记为警告，您可以在导入后手动修复。"
                            )
                        } else {
                            alertItem = FileImportAlertItem(
                                title: "导入结果",
                                message: "成功导入 \(totalCount) 个地址，所有地址坐标正常。"
                            )
                        }
                    }
                }
            }
        }
        
        // 处理JSON数据
        private func processJSONData(_ data: Data) {
            Task {
                await logInfo("FileImportSheet - 开始处理JSON数据")
            }
            
            // 开始处理 - 确保在主线程上更新UI
            Task { @MainActor in
                isProcessing = true
                processingProgress = 0.0
            }
            
            Task {
                var processedAddresses: [(String, CLLocationCoordinate2D, Bool, String)] = []
                var successCount = 0
                var warningCount = 0
                
                do {
                    // 尝试解析JSON数据
                    if let jsonObject = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        // 单个地址对象
                        if let address = jsonObject["address"] as? String {
                            var coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
                            
                            // 检查是否有坐标信息
                            if let lat = jsonObject["latitude"] as? Double, let lng = jsonObject["longitude"] as? Double {
                                coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lng)
                                // 添加到处理列表，无需地理编码
                                processedAddresses.append((address, coordinate, true, ""))
                                successCount += 1
                            } else {
                                // 需要地理编码
                                coordinate = await geocodeAddressWithRetry(address)
                                
                                if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                    // 地理编码失败，使用默认坐标
                                    let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16)
                                    processedAddresses.append((address, defaultCoordinate, true, "无法获取地址坐标"))
                                    warningCount += 1
                                } else {
                                    // 地理编码成功
                                    processedAddresses.append((address, coordinate, true, ""))
                                    successCount += 1
                                }
                            }
                        }
                        // 地址数组
                        else if let addresses = jsonObject["addresses"] as? [[String: Any]] {
                            await processJSONAddressArray(addresses, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                        }
                        // 地址字符串数组
                        else if let addresses = jsonObject["addresses"] as? [String] {
                            await processJSONStringArray(addresses, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                        }
                        else {
                            // 未找到有效的地址数据
                            await MainActor.run {
                                showError("未找到有效的地址数据，请确保JSON文件包含地址信息")
                            }
                        }
                    }
                    // 尝试解析为数组
                    else if let jsonArray = try JSONSerialization.jsonObject(with: data, options: []) as? [[String: Any]] {
                        // 对象数组，每个对象包含地址信息
                        await processJSONAddressArray(jsonArray, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                    }
                    else if let stringArray = try JSONSerialization.jsonObject(with: data, options: []) as? [String] {
                        // 字符串数组，每个字符串是一个地址
                        await processJSONStringArray(stringArray, processedAddresses: &processedAddresses, successCount: &successCount, warningCount: &warningCount)
                    }
                    else {
                        // 无法解析的JSON
                        await MainActor.run {
                            showError("无法解析JSON数据，请检查文件格式")
                        }
                    }
                } catch {
                    // JSON解析错误
                    Task {
                        await logError("FileImportSheet - JSON解析错误: \(error.localizedDescription)")
                    }
                    await MainActor.run {
                        showError("无法解析JSON数据: \(error.localizedDescription)")
                    }
                }
                
                // 更新UI
                await MainActor.run {
                    importedAddresses = processedAddresses
                    isProcessing = false
                    isImporting = false
                    
                    // 显示导入结果
                    if processedAddresses.isEmpty {
                        alertItem = FileImportAlertItem(
                            title: "导入结果",
                            message: "未找到有效地址"
                        )
                    } else {
                        // 显示导入结果摘要
                        let totalCount = processedAddresses.count
                        
                        if warningCount > 0 {
                            alertItem = FileImportAlertItem(
                                title: "导入结果",
                                message: "成功导入 \(totalCount) 个地址，其中 \(successCount) 个地址坐标正常，\(warningCount) 个地址无法获取坐标。\n\n无法获取坐标的地址已标记为警告，您可以在导入后手动修复。"
                            )
                        } else {
                            alertItem = FileImportAlertItem(
                                title: "导入结果",
                                message: "成功导入 \(totalCount) 个地址，所有地址坐标正常。"
                            )
                        }
                    }
                }
            }
        }
        
        // 处理JSON地址对象数组
        private func processJSONAddressArray(_ addresses: [[String: Any]], processedAddresses: inout [(String, CLLocationCoordinate2D, Bool, String)], successCount: inout Int, warningCount: inout Int) async {
            // 分批处理地址，避免同时发送太多地理编码请求
            let batchSize = 5 // 每批处理5个地址
            let totalAddresses = addresses.count
            
            for batchStart in stride(from: 0, to: totalAddresses, by: batchSize) {
                let batchEnd = min(batchStart + batchSize, totalAddresses)
                let currentBatch = addresses[batchStart..<batchEnd]
                
                // 更新进度
                let progress = Double(batchStart) / Double(totalAddresses)
                await MainActor.run {
                    processingProgress = progress
                }
                
                // 并行处理当前批次
                await withTaskGroup(of: (String, CLLocationCoordinate2D, Bool, String).self) { group in
                    for addressObj in currentBatch {
                        group.addTask {
                            if let address = addressObj["address"] as? String {
                                // 检查是否有坐标信息
                                if let lat = addressObj["latitude"] as? Double, let lng = addressObj["longitude"] as? Double {
                                    let coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lng)
                                    // 无需地理编码
                                    return (address, coordinate, true, "")
                                } else {
                                    // 需要地理编码
                                    let coordinate = await self.geocodeAddressWithRetry(address)
                                    
                                    if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                        // 地理编码失败，使用默认坐标
                                        let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16)
                                        return (address, defaultCoordinate, true, "无法获取地址坐标")
                                    } else {
                                        // 地理编码成功
                                        return (address, coordinate, true, "")
                                    }
                                }
                            } else {
                                return ("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, "无效的地址数据")
                            }
                        }
                    }
                    
                    // 收集结果
                    for await (address, coordinate, isSelected, warning) in group {
                        // 跳过无效地址
                        if address.isEmpty { continue }
                        
                        // 记录处理结果
                        if warning.isEmpty {
                            successCount += 1
                        } else {
                            warningCount += 1
                        }
                        
                        // 保存处理结果
                        processedAddresses.append((address, coordinate, isSelected, warning))
                    }
                }
                
                // 在批次之间添加延迟，避免超出API限制
                if batchEnd < totalAddresses {
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                }
            }
        }
        
        // 处理JSON字符串数组
        private func processJSONStringArray(_ addresses: [String], processedAddresses: inout [(String, CLLocationCoordinate2D, Bool, String)], successCount: inout Int, warningCount: inout Int) async {
            // 分批处理地址，避免同时发送太多地理编码请求
            let batchSize = 5 // 每批处理5个地址
            let totalAddresses = addresses.count
            
            for batchStart in stride(from: 0, to: totalAddresses, by: batchSize) {
                let batchEnd = min(batchStart + batchSize, totalAddresses)
                let currentBatch = addresses[batchStart..<batchEnd]
                
                // 更新进度
                let progress = Double(batchStart) / Double(totalAddresses)
                await MainActor.run {
                    processingProgress = progress
                }
                
                // 并行处理当前批次
                await withTaskGroup(of: (String, CLLocationCoordinate2D, Bool, String).self) { group in
                    for address in currentBatch {
                        group.addTask {
                            let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
                            
                            // 跳过空地址
                            guard !trimmedAddress.isEmpty else {
                                return ("", CLLocationCoordinate2D(latitude: 0, longitude: 0), false, "空地址")
                            }
                            
                            // 需要地理编码
                            let coordinate = await self.geocodeAddressWithRetry(trimmedAddress)
                            
                            if coordinate.latitude == 0 && coordinate.longitude == 0 {
                                // 地理编码失败，使用默认坐标
                                let defaultCoordinate = CLLocationCoordinate2D(latitude: -37.88, longitude: 145.16)
                                return (trimmedAddress, defaultCoordinate, true, "无法获取地址坐标")
                            } else {
                                // 地理编码成功
                                return (trimmedAddress, coordinate, true, "")
                            }
                        }
                    }
                    
                    // 收集结果
                    for await (address, coordinate, isSelected, warning) in group {
                        // 跳过无效地址
                        if address.isEmpty { continue }
                        
                        // 记录处理结果
                        if warning.isEmpty {
                            successCount += 1
                        } else {
                            warningCount += 1
                        }
                        
                        // 保存处理结果
                        processedAddresses.append((address, coordinate, isSelected, warning))
                    }
                }
                
                // 在批次之间添加延迟，避免超出API限制
                if batchEnd < totalAddresses {
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                }
            }
        }
        
        // Excel格式不再支持，简化处理函数
        private func processExcelData(_ data: Data) {
            Task { @MainActor in
                isImporting = false
                isProcessing = false
                showError("excel_format_not_supported".localized)
            }
        }
        
        // 下载示例文件
        private func downloadSampleFile(_ type: String) {
            // 构建示例文件路径
            guard let filePath = Bundle.main.path(forResource: "SampleAddresses", ofType: type, inDirectory: "SampleFiles") else {
                Task { @MainActor in
                    alertItem = FileImportAlertItem(
                        title: "error".localized,
                        message: "sample_file_not_found".localized
                    )
                }
                return
            }
            
            let fileURL = URL(fileURLWithPath: filePath)
            
            // 创建目标文件路径
            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let destinationURL = documentsDirectory.appendingPathComponent("SampleAddresses."+type)
            
            do {
                // 如果目标文件已存在，删除它
                if FileManager.default.fileExists(atPath: destinationURL.path) {
                    try FileManager.default.removeItem(at: destinationURL)
                }
                
                // 复制文件
                try FileManager.default.copyItem(at: fileURL, to: destinationURL)
                
                // 使用文件分享控制器分享文件
                let activityViewController = UIActivityViewController(activityItems: [destinationURL], applicationActivities: nil)
                
                // 在iPad上正确地展示分享控制器
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let controller = windowScene.windows.first?.rootViewController {
                    if let popover = activityViewController.popoverPresentationController {
                        popover.sourceView = controller.view
                        popover.sourceRect = CGRect(x: controller.view.bounds.midX, y: controller.view.bounds.midY, width: 0, height: 0)
                        popover.permittedArrowDirections = []
                    }
                    controller.present(activityViewController, animated: true, completion: nil)
                }
                
                Task { @MainActor in
                    alertItem = FileImportAlertItem(
                        title: "success".localized,
                        message: String(format: "sample_file_downloaded".localized, type.uppercased())
                    )
                }
            } catch {
                Task { @MainActor in
                    alertItem = FileImportAlertItem(
                        title: "error".localized,
                        message: String(format: "sample_file_download_failed".localized, error.localizedDescription)
                    )
                }
            }
        }
        
        // 显示错误
        @MainActor private func showError(_ message: String) {
            isImporting = false
            isProcessing = false
            alertItem = FileImportAlertItem(
                title: "import_failed".localized,
                message: message
            )
            Task {
                await logError("FileImportSheet - \(message)")
            }
        }
        
        // 验证坐标有效性
        private func isValidCoordinate(_ coordinate: CLLocationCoordinate2D) async -> Bool {
            // 检查坐标是否为NaN
            if coordinate.latitude.isNaN || coordinate.longitude.isNaN {
                return false
            }
            
            // 检查坐标是否在有效范围内
            if coordinate.latitude < -90 || coordinate.latitude > 90 ||
                coordinate.longitude < -180 || coordinate.longitude > 180 {
                return false
            }
            
            // 检查坐标是否为0,0（无效点）
            if abs(coordinate.latitude) < 0.0001 && abs(coordinate.longitude) < 0.0001 {
                return false
            }
            
            return true
        }
        
        // 日志辅助函数
        private func logInfo(_ message: String) async {
            os_log("%{public}@", log: Logger.app, type: .info, message)
        }
        
        private func logError(_ message: String) async {
            os_log("%{public}@", log: Logger.app, type: .error, message)
        }
    }
    
    // 自定义复选框样式
    struct CheckboxToggleStyle: ToggleStyle {
        func makeBody(configuration: Configuration) -> some View {
            HStack {
                Image(systemName: configuration.isOn ? "checkmark.square.fill" : "square")
                    .foregroundColor(configuration.isOn ? .blue : .gray)
                    .font(.system(size: 20))
                    .onTapGesture {
                        configuration.isOn.toggle()
                    }
                
                configuration.label
            }
        }
    }
    
    // 预览
    struct FileImportSheetPreview: View {
        @State private var showSheet = true
        
        var body: some View {
            Button("Show Import Sheet") {
                showSheet = true
            }
            .sheet(isPresented: $showSheet) {
                FileImportSheet { addresses in
                    print("Imported \(addresses.count) addresses")
                }
            }
        }
    }
    
    #Preview {
        FileImportSheetPreview()
    }
}
