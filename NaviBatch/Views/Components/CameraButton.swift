import SwiftUI

/// 自定义相机拍照按钮
/// 模仿iOS原生相机按钮样式
struct CameraButton: View {
    // 点击动作
    var action: () -> Void
    
    // 按钮大小
    var size: CGFloat = 72
    
    // 按钮颜色
    var color: Color = .white
    
    // 按钮边框颜色
    var borderColor: Color = .gray
    
    // 按钮按下状态
    @State private var isPressed: Bool = false
    
    var body: some View {
        Button(action: {
            // 添加触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.impactOccurred()
            
            // 执行动作
            action()
        }) {
            ZStack {
                // 外圈
                Circle()
                    .strokeBorder(borderColor.opacity(0.3), lineWidth: 5)
                    .frame(width: size, height: size)
                
                // 内圈
                Circle()
                    .fill(color)
                    .frame(width: size * 0.85, height: size * 0.85)
                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                
                // 相机图标（可选）
                Image(systemName: "camera.fill")
                    .font(.system(size: size * 0.3))
                    .foregroundColor(.blue)
                    .opacity(0.8)
            }
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .pressEvents(onPress: {
            isPressed = true
        }, onRelease: {
            isPressed = false
        })
    }
}

// 按钮按压状态修饰器
extension View {
    func pressEvents(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        modifier(PressActions(onPress: onPress, onRelease: onRelease))
    }
}

// 按钮按压状态修饰器实现
struct PressActions: ViewModifier {
    var onPress: () -> Void
    var onRelease: () -> Void
    
    func body(content: Content) -> some View {
        content
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        onPress()
                    }
                    .onEnded { _ in
                        onRelease()
                    }
            )
    }
}

// 预览
#Preview("CameraButton") {
    VStack(spacing: 30) {
        CameraButton(action: {})
        
        CameraButton(action: {}, size: 60, color: .white, borderColor: .blue)
        
        CameraButton(action: {}, size: 80, color: .white, borderColor: .red)
    }
    .padding()
    .background(Color.black.opacity(0.1))
}
