import SwiftUI
import MapKit

/// 增强版地址自动完成组件
struct EnhancedAddressAutocomplete: View {
    @Binding var searchText: String
    @Binding var selectedCoordinate: CLLocationCoordinate2D?
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var searchCompleter = MKLocalSearchCompleter()
    @State private var isSearching: Bool = false
    @State private var region: MKCoordinateRegion

    // 保持对代理的强引用
    @State private var completerDelegate: CompleterDelegate?

    // 回调函数
    var onAddressSelected: ((String, CLLocationCoordinate2D) -> Void)?

    // 🎯 触发搜索的标志
    @State private var shouldTriggerSearch = false

    // 🚀 防抖和取消机制
    @State private var debounceTask: Task<Void, Never>? = nil
    @State private var currentSearchTask: Task<Void, Never>? = nil

    init(
        searchText: Binding<String>,
        selectedCoordinate: Binding<CLLocationCoordinate2D?>,
        initialRegion: MKCoordinateRegion? = nil,
        onAddressSelected: ((String, CLLocationCoordinate2D) -> Void)? = nil
    ) {
        self._searchText = searchText
        self._selectedCoordinate = selectedCoordinate

        // 如果提供了初始区域，使用它；否则尝试使用用户当前位置或默认区域
        let regionToUse: MKCoordinateRegion
        if let providedRegion = initialRegion {
            regionToUse = providedRegion
        } else if let userLocation = LocationManager.shared.userLocation {
            // 使用用户当前位置
            regionToUse = MKCoordinateRegion(
                center: userLocation,
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            )
            Logger.info("使用用户当前位置作为搜索区域: \(userLocation.latitude), \(userLocation.longitude)", type: .location)
        } else {
            // 使用默认位置作为搜索区域
            let defaultLocation = LocationManager.shared.getDefaultCoordinateForCurrentRegion()
            regionToUse = MKCoordinateRegion(
                center: defaultLocation,
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            )
            Logger.info("无法获取用户位置，使用默认位置作为搜索区域: \(defaultLocation.latitude), \(defaultLocation.longitude)", type: .location)
        }

        self._region = State(initialValue: regionToUse)
        self.onAddressSelected = onAddressSelected
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 搜索输入框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("enter_address".localized, text: $searchText)
                    .autocorrectionDisabled()
                    .lineLimit(2...4)  // 允许2-4行显示，确保长地址能完整显示
                    .multilineTextAlignment(.leading)
                    .onChange(of: searchText) { _, newValue in
                        // 🚀 取消之前的防抖任务
                        debounceTask?.cancel()

                        if !newValue.isEmpty {
                            Logger.info("🔍 [地址编辑调试] 用户输入地址: '\(newValue)'", type: .location)
                            Logger.info("🔍 [地址编辑调试] 当前搜索结果数量: \(searchResults.count)", type: .location)

                            // 🚀 使用防抖机制，避免频繁搜索 - 增加到500ms防止hang
                            debounceTask = Task {
                                Logger.info("🔍 [地址编辑调试] 开始防抖延迟 500ms", type: .location)
                                try? await Task.sleep(nanoseconds: 500_000_000) // 500ms 防抖

                                guard !Task.isCancelled else {
                                    Logger.info("🚫 [地址编辑调试] 搜索任务被取消", type: .location)
                                    return
                                }

                                await MainActor.run {
                                    Logger.info("🔍 [地址编辑调试] 防抖完成，开始处理搜索", type: .location)

                                    // 翻译地址为英文以提高搜索成功率
                                    let translatedQuery = AddressStandardizer.translateAddressToEnglish(newValue)
                                    Logger.info("🌍 [地址编辑调试] 地址翻译结果: '\(newValue)' -> '\(translatedQuery)'", type: .location)

                                    // 🎯 强制使用英文语言环境进行搜索
                                    Logger.info("🎯 [地址编辑调试] 调用performSearchWithEnglishLocale", type: .location)
                                    self.performSearchWithEnglishLocale(query: translatedQuery)
                                }
                            }
                        } else {
                            Logger.info("🔍 [地址编辑调试] 搜索文本为空，清空结果", type: .location)
                            searchResults = []
                            // 取消正在进行的搜索
                            currentSearchTask?.cancel()
                        }
                    }
                    .onSubmit {
                        Logger.info("提交搜索: \(searchText)", type: .location)
                    }

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        searchResults = []
                        // 🚀 取消正在进行的搜索
                        debounceTask?.cancel()
                        currentSearchTask?.cancel()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }

                // 🚀 显示搜索状态和取消按钮
                if isSearching {
                    Button(action: {
                        // 取消正在进行的搜索
                        currentSearchTask?.cancel()
                        isSearching = false
                    }) {
                        HStack(spacing: 4) {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("cancel_search".localized)
                                .font(.caption)
                        }
                        .foregroundColor(.blue)
                    }
                }
            }

            // 搜索结果列表
            if !searchResults.isEmpty {
                Divider()
                    .padding(.top, 8)

                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 0) {
                        ForEach(searchResults, id: \.self) { result in
                            Button(action: {
                                Logger.info("点击了搜索结果: \(result.title)", type: .location)
                                selectSearchResult(result)
                            }) {
                                HStack {
                                    Image(systemName: "mappin.circle.fill")
                                        .foregroundColor(.red)
                                        .font(.title3)

                                    VStack(alignment: .leading, spacing: 3) {
                                        Text(result.title)
                                            .foregroundColor(.primary)
                                            .font(.subheadline)
                                            .fontWeight(.medium)

                                        if !result.subtitle.isEmpty {
                                            Text(result.subtitle)
                                                .foregroundColor(.secondary)
                                                .font(.caption)
                                        }
                                    }

                                    Spacer()
                                }
                                .padding(.vertical, 12)
                            }
                            .buttonStyle(PlainButtonStyle())

                            if searchResults.last != result {
                                Divider()
                                    .padding(.leading, 40)
                            }
                        }
                    }
                }
                .frame(maxHeight: min(120, CGFloat(min(searchResults.count, 3) * 50))) // 🛡️ 限制最多显示3个结果，防止手势冲突
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                .scrollDisabled(searchResults.count <= 3) // 🛡️ 少于3个结果时禁用滚动，减少手势冲突
            } else if !searchText.isEmpty && !isSearching {
                // 🎯 移除"未找到有效地址"提示，因为：
                // 1. 这个组件主要用于搜索建议，不是地址验证
                // 2. 地址有效性应该由UniversalAddressProcessor判断
                // 3. 用户应该能直接输入地址，即使没有搜索建议
                EmptyView()
            }
        }
        .onAppear {
            setupSearchCompleter()
        }
        .onDisappear {
            // 🚀 清理任务，防止内存泄漏和hang
            Logger.info("🧹 EnhancedAddressAutocomplete - 开始清理资源", type: .location)

            debounceTask?.cancel()
            debounceTask = nil

            currentSearchTask?.cancel()
            currentSearchTask = nil

            // 清理搜索完成器
            searchCompleter.cancel()
            searchCompleter.delegate = nil

            // 重置状态
            isSearching = false
            searchResults = []

            Logger.info("✅ EnhancedAddressAutocomplete - 资源清理完成", type: .location)
        }
        .onChange(of: shouldTriggerSearch) { _, newValue in
            if newValue && !searchText.isEmpty {
                // 🎯 直接触发搜索，使用新的MKLocalSearch方法
                Logger.info("🎯 丝滑触发搜索: \(searchText)", type: .location)
                let translatedQuery = AddressStandardizer.translateAddressToEnglish(searchText)
                updateSearchRegionForQuery(translatedQuery)
                performDirectSearch(translatedQuery)
                shouldTriggerSearch = false // 重置标志
            }
        }
    }

    // 设置搜索自动完成
    private func setupSearchCompleter() {
        Logger.info("🔧 初始化地址自动完成组件", type: .location)

        // 🌍 强制使用英文语言环境，避免中文本地化结果
        let originalLocale = Locale.current
        Logger.info("🌍 当前系统语言环境: \(originalLocale.identifier)", type: .location)

        // 临时设置为英文语言环境
        let englishLocale = Locale(identifier: "en_US")
        Logger.info("🇺🇸 强制设置为英文语言环境: \(englishLocale.identifier)", type: .location)

        // 重置搜索器
        searchCompleter = MKLocalSearchCompleter()

        // 创建并保存代理实例
        completerDelegate = CompleterDelegate(
            onUpdateResults: { results in
                DispatchQueue.main.async {

                    Logger.info("🔍 CompleterDelegate收到原始结果: \(results.count)个", type: .location)

                    // 🛡️ 限制结果数量防止hang
                    let limitedResults = Array(results.prefix(5))

                    // 打印前3个原始结果
                    for (index, result) in limitedResults.prefix(3).enumerated() {
                        let fullText = "\(result.title) \(result.subtitle)"
                        let hasChinese = AddressStandardizer.containsChineseCharacters(fullText)
                        let flag = hasChinese ? "🇨🇳" : "🇺🇸"
                        Logger.info("🔍 原始结果[\(index+1)] \(flag): \(fullText)", type: .location)
                    }

                    // 🎯 过滤掉包含中文的搜索结果，确保英文地址
                    let englishResults = limitedResults.filter { result in
                        let fullText = "\(result.title) \(result.subtitle)"
                        let hasChineseCharacters = AddressStandardizer.containsChineseCharacters(fullText)
                        if hasChineseCharacters {
                            Logger.info("🚫 过滤中文搜索结果: \(fullText)", type: .location)
                        }
                        return !hasChineseCharacters
                    }

                    self.searchResults = englishResults
                    Logger.info("✅ 更新搜索结果: 找到\(results.count)个结果，限制为\(limitedResults.count)个，过滤后\(englishResults.count)个英文结果", type: .location)
                }
            },
            onAutoSelect: { result in
                DispatchQueue.main.async {
                    // 注意：这里不能使用weak self，因为View是struct
                    // 实际的selectSearchResult调用会在主线程上安全执行
                }
            }
        )

        // 设置代理
        searchCompleter.delegate = completerDelegate
        Logger.info("🔧 已设置搜索代理", type: .location)

        // 设置搜索类型和区域 - 🎯 使用全球搜索，避免地理位置限制
        searchCompleter.resultTypes = .address
        Logger.info("🔧 已设置搜索类型为地址", type: .location)

        // 🌍 使用全球搜索区域，确保能找到任何地址
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        searchCompleter.region = globalRegion

        // 打印当前区域信息
        Logger.info("🌍 搜索区域设置为全球搜索: center(\(globalRegion.center.latitude), \(globalRegion.center.longitude)), span(\(globalRegion.span.latitudeDelta), \(globalRegion.span.longitudeDelta))", type: .location)
        Logger.info("✅ 地址自动完成组件初始化完成", type: .location)
    }

    // 🇺🇸 强制使用英文语言环境进行搜索 - 直接使用MKLocalSearch避免MKLocalSearchCompleter超时问题
    private func performSearchWithEnglishLocale(query: String) {
        Logger.info("🚀 [地址编辑调试] 开始直接MKLocalSearch搜索: '\(query)'", type: .location)
        Logger.info("🚀 [地址编辑调试] 跳过MKLocalSearchCompleter，直接使用MKLocalSearch避免超时", type: .location)

        // 取消之前的搜索任务
        currentSearchTask?.cancel()

        // 直接使用MKLocalSearch进行搜索，避免MKLocalSearchCompleter的超时问题
        currentSearchTask = Task {
            await performDirectMKLocalSearch(query: query)
        }
    }

    // 🎯 直接使用MKLocalSearch进行搜索
    private func performDirectMKLocalSearch(query: String) async {
        Logger.info("🎯 [地址编辑调试] 开始直接MKLocalSearch: '\(query)'", type: .location)

        // 保存当前语言环境
        let currentLocale = Locale.current

        // 临时设置英文语言环境
        let englishLocale = Locale(identifier: "en_US")
        await MainActor.run {
            Thread.current.threadDictionary["NSCurrentLocale"] = englishLocale
        }
        Logger.info("🇺🇸 [地址编辑调试] 设置英文语言环境", type: .location)

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = query
        request.resultTypes = [.address]

        // 设置全球搜索区域
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        request.region = globalRegion
        Logger.info("🌍 [地址编辑调试] 设置全球搜索区域", type: .location)

        let search = MKLocalSearch(request: request)

        do {
            Logger.info("🚀 [地址编辑调试] 执行MKLocalSearch", type: .location)
            let response = try await search.start()

            // 恢复原始语言环境
            await MainActor.run {
                Thread.current.threadDictionary["NSCurrentLocale"] = currentLocale
            }
            Logger.info("🔄 [地址编辑调试] 已恢复原始语言环境", type: .location)

            // 转换MKMapItem为MKLocalSearchCompletion
            var mockCompletions: [MKLocalSearchCompletion] = []

            Logger.info("🔄 [地址编辑调试] 找到 \(response.mapItems.count) 个结果", type: .location)

            for (index, mapItem) in response.mapItems.prefix(10).enumerated() {
                if let completion = createMockCompletionFromMapItem(mapItem) {
                    mockCompletions.append(completion)
                    Logger.info("✅ [地址编辑调试] 结果\(index+1): \(completion.title) | \(completion.subtitle)", type: .location)
                }
            }

            await MainActor.run {
                Logger.info("🎉 [地址编辑调试] 直接MKLocalSearch成功: \(mockCompletions.count)个有效结果", type: .location)
                self.searchResults = mockCompletions
            }

        } catch {
            // 恢复原始语言环境
            await MainActor.run {
                Thread.current.threadDictionary["NSCurrentLocale"] = currentLocale
            }

            await MainActor.run {
                Logger.error("❌ [地址编辑调试] 直接MKLocalSearch失败: \(error.localizedDescription)", type: .location)

                // 降级到LocaleAwareAddressSearchService
                Logger.info("� [地址编辑调试] 降级到LocaleAwareAddressSearchService", type: .location)
                LocaleAwareAddressSearchService.shared.performSearch(query: query) { results in
                    DispatchQueue.main.async {
                        Logger.info("� [地址编辑调试] 降级搜索完成: \(results.count)个结果", type: .location)
                        self.searchResults = results
                    }
                }
            }
        }
    }

    // 🔄 混合搜索策略：MKLocalSearchCompleter + MKLocalSearch备用
    private func performHybridSearch(query: String) {
        Logger.info("🔄 开始混合搜索策略: '\(query)'", type: .location)

        // 1. 首先尝试MKLocalSearchCompleter（更快，更适合自动完成）
        searchCompleter.queryFragment = ""  // 先清空

        // 确保搜索器配置正确
        searchCompleter.resultTypes = .address

        // 设置全球搜索区域
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        searchCompleter.region = globalRegion

        Logger.info("🔧 配置MKLocalSearchCompleter: resultTypes=.address, region=global", type: .location)

        // 执行搜索
        searchCompleter.queryFragment = query
        Logger.info("✅ 已触发MKLocalSearchCompleter搜索: '\(query)'", type: .location)

        // 2. 设置备用搜索（如果MKLocalSearchCompleter在3秒内没有结果）
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            guard !query.isEmpty else { return }

            // 如果MKLocalSearchCompleter还没有返回结果，使用MKLocalSearch作为备用
            if searchResults.isEmpty && !query.isEmpty {
                Logger.info("🔄 MKLocalSearchCompleter超时，降级到MKLocalSearch", type: .location)
                performBackupSearch(query: query)
            }
        }
    }

    // 🆘 备用搜索方法：使用MKLocalSearch
    private func performBackupSearch(query: String) {
        Logger.info("🆘 开始备用搜索: '\(query)'", type: .location)

        Task {
            let request = MKLocalSearch.Request()
            request.naturalLanguageQuery = query
            request.resultTypes = [.address]

            // 设置全球搜索区域
            let globalRegion = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
            )
            request.region = globalRegion

            let search = MKLocalSearch(request: request)

            do {
                let response = try await search.start()

                // 转换为MKLocalSearchCompletion格式
                var mockCompletions: [MKLocalSearchCompletion] = []

                for mapItem in response.mapItems.prefix(5) {
                    if let completion = createMockCompletionSync(from: mapItem) {
                        mockCompletions.append(completion)
                    }
                }

                await MainActor.run {
                    // 只有在MKLocalSearchCompleter还没有结果时才更新
                    if self.searchResults.isEmpty {
                        self.searchResults = mockCompletions
                        Logger.info("🆘 备用搜索成功: 找到\(mockCompletions.count)个结果", type: .location)
                    } else {
                        Logger.info("🆘 备用搜索完成，但MKLocalSearchCompleter已有结果，跳过", type: .location)
                    }
                }

            } catch {
                Logger.error("🆘 备用搜索失败: \(error.localizedDescription)", type: .location)
            }
        }
    }



    // 🎯 公共方法：触发搜索
    func triggerSearch() {
        shouldTriggerSearch = true
    }

    // 🚀 轻量级搜索方法 - 专门用于交互式搜索，更快的超时和更少的重试
    private func performLightweightSearch(_ query: String) {
        Logger.info("🚀 轻量级搜索: \(query)", type: .location)

        // 取消之前的搜索任务
        currentSearchTask?.cancel()
        currentSearchTask = nil

        currentSearchTask = Task {
            let request = MKLocalSearch.Request()
            request.naturalLanguageQuery = query
            request.resultTypes = [.address]
            request.region = region

            let search = MKLocalSearch(request: request)

            // 🚀 添加更短的超时保护 (3秒防止hang)
            let timeoutTask = Task {
                try await Task.sleep(nanoseconds: 3_000_000_000) // 3秒超时
                search.cancel()
                Logger.warning("⏰ 轻量级搜索超时，已取消", type: .location)
            }

            defer {
                timeoutTask.cancel()
            }

            do {
                let response = try await search.start()

                guard !Task.isCancelled else { return }

                // 🚀 只处理前3个结果，进一步提高响应速度
                var mockCompletions: [MKLocalSearchCompletion] = []

                for mapItem in response.mapItems.prefix(3) {
                    guard !Task.isCancelled else { break }

                    // 只使用同步方法，避免额外的异步操作
                    if let completion = createMockCompletionSync(from: mapItem) {
                        mockCompletions.append(completion)
                    }
                }

                await MainActor.run {
                    guard !Task.isCancelled else { return }

                    self.searchResults = mockCompletions
                    Logger.info("🚀 轻量级搜索完成: 找到\(mockCompletions.count)个结果", type: .location)
                }

            } catch {
                guard !Task.isCancelled else { return }

                Logger.error("🚀 轻量级搜索失败: \(error.localizedDescription)", type: .location)

                // 降级到原有的MKLocalSearchCompleter
                await MainActor.run {
                    searchCompleter.queryFragment = query
                }
            }
        }
    }

    // 🎯 保留原有的直接搜索方法，用于手动触发的搜索
    private func performDirectSearch(_ query: String) {
        Logger.info("🎯 使用MKLocalSearch直接搜索: \(query)", type: .location)

        Task {
            let request = MKLocalSearch.Request()
            request.naturalLanguageQuery = query
            request.resultTypes = [.address]

            // 设置搜索区域
            request.region = region

            let search = MKLocalSearch(request: request)

            do {
                let response = try await search.start()

                // 🎯 优化：限制结果数量并处理中文本地化问题
                var mockCompletions: [MKLocalSearchCompletion] = []

                // 只处理前5个结果，提高响应速度
                for mapItem in response.mapItems.prefix(5) {
                    // 首先尝试同步方法
                    if let completion = createMockCompletionSync(from: mapItem) {
                        mockCompletions.append(completion)
                    } else {
                        // 如果同步方法返回nil（检测到中文），使用异步方法
                        if let completion = await createMockCompletionAsync(from: mapItem) {
                            mockCompletions.append(completion)
                        }
                    }
                }

                await MainActor.run {
                    self.searchResults = mockCompletions
                    Logger.info("🎯 MKLocalSearch搜索完成: 找到\(mockCompletions.count)个结果", type: .location)

                    // 打印第一个结果
                    if let firstResult = mockCompletions.first {
                        Logger.info("🎯 第一个结果: \(firstResult.title), \(firstResult.subtitle)", type: .location)
                    }
                }

            } catch {
                Logger.error("🎯 MKLocalSearch搜索失败: \(error.localizedDescription)", type: .location)

                // 降级到原有的MKLocalSearchCompleter
                await MainActor.run {
                    searchCompleter.queryFragment = query
                }
            }
        }
    }

    // 🎯 从MKMapItem创建模拟的MKLocalSearchCompletion
    private func createMockCompletionFromMapItem(_ mapItem: MKMapItem) -> MKLocalSearchCompletion? {
        let placemark = mapItem.placemark

        // 构建标题（通常是门牌号和街道名）
        var titleComponents: [String] = []

        if let subThoroughfare = placemark.subThoroughfare {
            titleComponents.append(subThoroughfare)
        }

        if let thoroughfare = placemark.thoroughfare {
            titleComponents.append(thoroughfare)
        }

        let title = titleComponents.isEmpty ? (mapItem.name ?? "Unknown") : titleComponents.joined(separator: " ")

        // 构建副标题（城市、州、国家）
        var subtitleComponents: [String] = []

        if let locality = placemark.locality {
            subtitleComponents.append(locality)
        }

        if let administrativeArea = placemark.administrativeArea {
            subtitleComponents.append(administrativeArea)
        }

        if let postalCode = placemark.postalCode {
            subtitleComponents.append(postalCode)
        }

        if let country = placemark.country, country != "United States" {
            subtitleComponents.append(country)
        }

        let subtitle = subtitleComponents.joined(separator: ", ")

        // 创建模拟的completion对象
        let mockCompletion = EnhancedMockLocalSearchCompletion()
        mockCompletion.title = title
        mockCompletion.subtitle = subtitle

        Logger.info("🔧 [地址编辑调试] 创建模拟completion: '\(title)' | '\(subtitle)'", type: .location)

        return mockCompletion
    }

    // 🎯 同步创建模拟的MKLocalSearchCompletion - 优化性能，强制英文
    private func createMockCompletionSync(from mapItem: MKMapItem) -> MKLocalSearchCompletion? {
        let completion = EnhancedMockLocalSearchCompletion()

        // 检查placemark信息是否被本地化为中文
        let placemark = mapItem.placemark

        // 如果locality包含中文字符，使用异步方法获取英文地址
        if let locality = placemark.locality,
           AddressStandardizer.containsChineseCharacters(locality) {
            Logger.info("🎯 检测到中文本地化地址，降级到异步方法: \(locality)", type: .location)
            // 返回nil，让调用方使用异步方法
            return nil
        }

        // 构建地址标题
        var addressComponents: [String] = []

        // 门牌号 + 街道
        if let subThoroughfare = placemark.subThoroughfare,
           let thoroughfare = placemark.thoroughfare {
            addressComponents.append("\(subThoroughfare) \(thoroughfare)")
        } else if let thoroughfare = placemark.thoroughfare {
            addressComponents.append(thoroughfare)
        } else if let name = mapItem.name {
            addressComponents.append(name)
        }

        completion.title = addressComponents.first ?? ""

        // 城市, 州, 邮编
        var subtitleComponents: [String] = []
        if let locality = placemark.locality {
            subtitleComponents.append(locality)
        }
        if let administrativeArea = placemark.administrativeArea {
            subtitleComponents.append(administrativeArea)
        }
        if let postalCode = placemark.postalCode {
            subtitleComponents.append(postalCode)
        }

        completion.subtitle = subtitleComponents.joined(separator: ", ")

        Logger.info("🎯 快速创建英文地址: \(completion.title), \(completion.subtitle)", type: .location)
        return completion
    }

    // 🎯 异步创建模拟的MKLocalSearchCompletion - 保留备用
    private func createMockCompletionAsync(from mapItem: MKMapItem) async -> MKLocalSearchCompletion? {
        guard let location = mapItem.placemark.location else {
            Logger.warning("🎯 MapItem没有位置信息", type: .location)
            return nil
        }

        do {
            let geocoder = CLGeocoder()
            let placemarks = try await geocoder.reverseGeocodeLocation(
                location,
                preferredLocale: Locale(identifier: "en_US")
            )

            if let placemark = placemarks.first {
                let completion = EnhancedMockLocalSearchCompletion()

                // 构建英文格式的地址
                var addressComponents: [String] = []

                // 门牌号 + 街道
                if let subThoroughfare = placemark.subThoroughfare,
                   let thoroughfare = placemark.thoroughfare {
                    addressComponents.append("\(subThoroughfare) \(thoroughfare)")
                } else if let thoroughfare = placemark.thoroughfare {
                    addressComponents.append(thoroughfare)
                }

                completion.title = addressComponents.first ?? mapItem.name ?? ""

                // 城市, 州, 邮编
                var subtitleComponents: [String] = []
                if let locality = placemark.locality {
                    subtitleComponents.append(locality)
                }
                if let administrativeArea = placemark.administrativeArea {
                    subtitleComponents.append(administrativeArea)
                }
                if let postalCode = placemark.postalCode {
                    subtitleComponents.append(postalCode)
                }

                completion.subtitle = subtitleComponents.joined(separator: ", ")

                Logger.info("🎯 成功创建英文地址: \(completion.title), \(completion.subtitle)", type: .location)
                return completion
            }
        } catch {
            Logger.error("🎯 反向地理编码失败: \(error.localizedDescription)", type: .location)
        }

        // 如果反向地理编码失败，使用原始数据
        let completion = EnhancedMockLocalSearchCompletion()
        completion.title = mapItem.name ?? ""
        completion.subtitle = mapItem.placemark.title ?? ""
        Logger.info("🎯 使用原始数据创建地址: \(completion.title), \(completion.subtitle)", type: .location)
        return completion
    }

    // 🚀 选择搜索结果 - 优化版本，减少对UniversalAddressProcessor的依赖
    internal func selectSearchResult(_ result: MKLocalSearchCompletion) {
        Logger.info("🚀 [地址编辑调试] 用户选择了搜索结果: 标题='\(result.title)', 副标题='\(result.subtitle)'", type: .location)

        // 隐藏搜索结果并显示加载状态
        Logger.info("🚀 [地址编辑调试] 清空搜索结果并显示加载状态", type: .location)
        searchResults = []
        isSearching = true

        // 构建完整地址字符串
        let fullAddress = result.subtitle.isEmpty ? result.title : "\(result.title), \(result.subtitle)"
        Logger.info("🚀 [地址编辑调试] 构建完整地址: '\(fullAddress)'", type: .location)

        // 🎯 检查是否是MockLocalSearchCompletion，如果是则使用存储的坐标
        if let mockCompletion = result as? MockLocalSearchCompletion,
           let storedCoordinate = mockCompletion.storedCoordinate {
            Logger.info("🎯 [地址编辑调试] 发现MockLocalSearchCompletion存储的坐标: (\(storedCoordinate.latitude), \(storedCoordinate.longitude))", type: .location)
            self.selectedCoordinate = storedCoordinate
        }

        Task {
            Logger.info("🚀 [地址编辑调试] 开始异步处理地址", type: .location)

            // 🎯 首先检查并修复缺少州信息的地址
            Logger.info("🚀 [地址编辑调试] 检查并修复州信息", type: .location)
            let fixedAddress = await fixAddressStateIfNeeded(fullAddress)
            let addressToProcess = fixedAddress != fullAddress ? fixedAddress : fullAddress

            // 如果地址被修复，记录日志
            if fixedAddress != fullAddress {
                Logger.info("🔧 [地址编辑调试] 地址已自动修复: '\(fullAddress)' -> '\(fixedAddress)'", type: .location)
            } else {
                Logger.info("🔧 [地址编辑调试] 地址无需修复: '\(fullAddress)'", type: .location)
            }

            // 🏠 首先检查地址库中是否已存在该地址（使用修复后的地址）
            Logger.info("🏠 [地址编辑调试] 检查地址库: '\(addressToProcess)'", type: .location)

            let existingAddress = await UserAddressDatabase.shared.getValidatedAddress(for: addressToProcess)
            Logger.info("🏠 [地址编辑调试] 地址库查询完成", type: .location)

            if let existing = existingAddress {
                Logger.info("🏠 [地址编辑调试] ✅ 地址库命中: '\(addressToProcess)' -> (\(existing.coordinate.latitude), \(existing.coordinate.longitude))", type: .location)

                await MainActor.run {
                    Logger.info("🏠 [地址编辑调试] 在主线程更新UI - 使用地址库数据", type: .location)
                    self.isSearching = false
                    self.searchText = addressToProcess
                    self.safeSetCoordinate(existing.coordinate)
                    self.onAddressSelected?(addressToProcess, existing.coordinate)
                    Logger.info("🏠 [地址编辑调试] 使用地址库数据完成地址选择: '\(addressToProcess)'", type: .location)
                }
                return
            }

            Logger.info("🏠 [地址编辑调试] 地址库未命中，使用超快速地址处理: '\(addressToProcess)'", type: .location)

            // ⚡ 使用超快速地址处理，避免hang问题
            Logger.info("⚡ [地址编辑调试] 开始调用超快速地址处理", type: .location)
            let quickResult = await processAddressUltraFast(addressToProcess)
            Logger.info("⚡ [地址编辑调试] 超快速地址处理完成", type: .location)

            await MainActor.run {
                Logger.info("⚡ [地址编辑调试] 在主线程处理超快速地址处理结果", type: .location)
                self.isSearching = false

                switch quickResult {
                case .success(_, let formattedAddress, let coordinate, _):
                    Logger.info("⚡ [地址编辑调试] ✅ 超快速地址处理成功: '\(formattedAddress)' -> (\(coordinate.latitude), \(coordinate.longitude))", type: .location)

                    // 🏠 异步保存到地址库，不阻塞UI
                    Task.detached {
                        Logger.info("🏠 [地址编辑调试] 开始保存新地址到地址库: '\(formattedAddress)' -> (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                        await UserAddressDatabase.shared.saveValidatedAddress(
                            formattedAddress,
                            coordinate: coordinate,
                            source: .manual,
                            confidence: 0.95 // 用户主动选择的地址，置信度很高
                        )
                        Logger.info("🏠 [地址编辑调试] ✅ 地址库更新完成: '\(formattedAddress)'", type: .location)
                    }

                    // 使用修复后的地址
                    Logger.info("⚡ [地址编辑调试] 更新UI组件状态", type: .location)
                    self.searchText = addressToProcess
                    self.safeSetCoordinate(coordinate)

                    Logger.info("⚡ [地址编辑调试] 调用onAddressSelected回调", type: .location)
                    self.onAddressSelected?(addressToProcess, coordinate)

                    Logger.info("⚡ [地址编辑调试] ✅ 地址选择完成: '\(result.title)' -> '\(addressToProcess)'", type: .location)

                case .failed(let reason):
                    Logger.warning("⚡ [地址编辑调试] ❌ 超快速地址处理失败: \(reason)，降级到原有逻辑", type: .location)

                    // 降级到原有逻辑
                    self.fallbackToOriginalSearch(result)
                }
            }
        }
    }


    /// 降级到原有搜索逻辑
    private func fallbackToOriginalSearch(_ result: MKLocalSearchCompletion) {
        Logger.info("🔄 降级到原有搜索逻辑", type: .location)

        isSearching = true

        // 从搜索结果获取坐标
        let searchRequest = MKLocalSearch.Request(completion: result)
        let search = MKLocalSearch(request: searchRequest)

        search.start { response, error in
            // 切换到主线程更新UI
            DispatchQueue.main.async {
                self.isSearching = false

                if let error = error {
                    Logger.error("地址搜索错误: \(error.localizedDescription)", type: .location)
                    return
                }

                guard let response = response else {
                    Logger.error("地址搜索无响应", type: .location)
                    return
                }

                guard let mapItem = response.mapItems.first else {
                    Logger.error("地址搜索无结果", type: .location)
                    return
                }

                let coordinate = mapItem.placemark.coordinate
                let country = mapItem.placemark.country ?? "未知国家"
                Logger.info("获取到地址坐标: \(coordinate.latitude), \(coordinate.longitude), 国家: \(country)", type: .location)

                // 🏠 异步保存到地址库（降级逻辑），不阻塞UI
                Task.detached {
                    Logger.info("🏠 EnhancedAddressAutocomplete - 降级逻辑保存地址到地址库: \(result.title) -> (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                    await UserAddressDatabase.shared.saveValidatedAddress(
                        result.title,
                        coordinate: coordinate,
                        source: .manual,
                        confidence: 0.85 // 降级逻辑的置信度稍低
                    )
                    Logger.info("🏠 EnhancedAddressAutocomplete - ✅ 降级逻辑地址库更新完成: \(result.title)", type: .location)
                }

                // 🎯 保持原始地址格式，避免被系统语言影响
                let originalAddress = result.subtitle.isEmpty ? result.title : "\(result.title), \(result.subtitle)"
                self.searchText = originalAddress
                self.safeSetCoordinate(coordinate)

                // 调用回调函数，传递原始完整地址
                self.onAddressSelected?(originalAddress, coordinate)

                Logger.info("降级逻辑地址选择完成: \(originalAddress)", type: .location)
            }
        }
    }

    // 🎯 标准化街道地址（将缩写转换为完整形式）
    private func standardizeStreetAddress(_ address: String) -> String {
        return AddressStandardizer.standardizeAddress(address)
    }

    // 检测是否可能是香港地址
    private func isLikelyHongKongAddress(_ query: String) -> Bool {
        let lowercaseQuery = query.lowercased()

        // 香港地址特征
        let hkIndicators = [
            // 中文特征
            "荃湾", "荃灣", "中环", "中環", "铜锣湾", "銅鑼灣", "旺角", "尖沙咀",
            "湾仔", "灣仔", "深水埗", "观塘", "觀塘", "黄大仙", "黃大仙",
            "沙田", "屯门", "屯門", "元朗", "大埔", "西贡", "西貢",
            // 英文特征
            "hong kong", "hk", "central", "tsim sha tsui", "causeway bay",
            "wan chai", "mong kok", "tsuen wan", "sha tin", "tuen mun",
            // 道路特征
            "道", "街", "路", "里", "坊", "徑", "巷",
            // 楼层特征
            "楼", "室", "号", "座"
        ]

        return hkIndicators.contains { lowercaseQuery.contains($0) }
    }

    // 根据搜索查询动态更新搜索区域
    private func updateSearchRegionForQuery(_ query: String) {
        let isHongKongAddress = isLikelyHongKongAddress(query)

        if isHongKongAddress {
            // 香港地址：创建一个覆盖全球的大区域，相当于不限制
            let globalRegion = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
            )
            searchCompleter.region = globalRegion
            Logger.info("检测到香港地址特征，使用全球搜索区域: \(query)", type: .location)
        } else {
            // 其他地址：使用原有区域设置
            searchCompleter.region = region
            Logger.info("使用区域限制搜索: \(query)", type: .location)
        }
    }

    // 🛡️ 安全设置坐标，防止NaN值导致UI卡死
    private func safeSetCoordinate(_ coordinate: CLLocationCoordinate2D) {
        // 检查坐标是否包含NaN值
        if coordinate.latitude.isNaN || coordinate.longitude.isNaN {
            Logger.error("⚠️ 检测到NaN坐标，拒绝设置: lat=\(coordinate.latitude), lon=\(coordinate.longitude)", type: .location)
            // 设置为无效坐标而不是NaN
            self.selectedCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
            return
        }

        // 检查坐标是否在有效范围内
        if !CLLocationCoordinate2DIsValid(coordinate) {
            Logger.error("⚠️ 检测到无效坐标，拒绝设置: lat=\(coordinate.latitude), lon=\(coordinate.longitude)", type: .location)
            self.selectedCoordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
            return
        }

        // 坐标有效，安全设置
        self.selectedCoordinate = coordinate
        Logger.info("✅ 安全设置坐标: lat=\(coordinate.latitude), lon=\(coordinate.longitude)", type: .location)
    }

    // MARK: - 超快速地址处理

    /// 超快速地址处理 - 避免hang问题
    private func processAddressUltraFast(_ address: String) async -> LightweightGeocodingResult {
        Logger.info("⚡ 开始超快速地址处理: \(address)", type: .location)

        // 检查是否已经有坐标信息（从搜索结果中获得）
        if let existingCoordinate = self.selectedCoordinate {
            Logger.info("⚡ 使用现有坐标信息，跳过地理编码", type: .location)

            // 创建一个简单的placemark
            let placemark = MKPlacemark(coordinate: existingCoordinate)

            return .success(
                originalAddress: address,
                formattedAddress: address,
                coordinate: existingCoordinate,
                placemark: placemark
            )
        }

        // 如果没有坐标，进行最简单的地理编码（无速率限制等待）
        Logger.info("⚡ 执行简单地理编码（无等待）", type: .location)

        let geocoder = CLGeocoder()

        // 1秒超时
        let timeoutTask = Task {
            try await Task.sleep(nanoseconds: 1_000_000_000)
            geocoder.cancelGeocode()
        }

        defer {
            timeoutTask.cancel()
        }

        do {
            let placemarks = try await geocoder.geocodeAddressString(
                address,
                in: nil,
                preferredLocale: Locale(identifier: "en_US")
            )

            guard let placemark = placemarks.first,
                  let location = placemark.location else {
                Logger.warning("⚡ 地理编码无结果，使用默认坐标", type: .location)
                return .failed(reason: "无法获取地址坐标")
            }

            Logger.info("⚡ 地理编码成功", type: .location)
            return .success(
                originalAddress: address,
                formattedAddress: address,
                coordinate: location.coordinate,
                placemark: placemark
            )

        } catch {
            Logger.warning("⚡ 地理编码失败，使用默认处理: \(error.localizedDescription)", type: .location)
            return .failed(reason: "地理编码失败")
        }
    }
}

// MARK: - 搜索自动完成代理
class CompleterDelegate: NSObject, MKLocalSearchCompleterDelegate {
    var onUpdateResults: ([MKLocalSearchCompletion]) -> Void
    var onAutoSelect: ((MKLocalSearchCompletion) -> Void)?

    init(onUpdateResults: @escaping ([MKLocalSearchCompletion]) -> Void, onAutoSelect: ((MKLocalSearchCompletion) -> Void)? = nil) {
        self.onUpdateResults = onUpdateResults
        self.onAutoSelect = onAutoSelect
        Logger.info("创建了CompleterDelegate实例", type: .location)
    }

    func completerDidUpdateResults(_ completer: MKLocalSearchCompleter) {
        let results = completer.results
        Logger.info("🎯 CompleterDelegate.completerDidUpdateResults被调用", type: .location)
        Logger.info("📍 搜索查询: '\(completer.queryFragment)'", type: .location)
        Logger.info("📊 获取到\(results.count)个地址搜索结果", type: .location)

        if !results.isEmpty {
            let firstResult = results[0]
            Logger.info("🥇 第一个结果: \(firstResult.title), \(firstResult.subtitle)", type: .location)

            // 打印前3个结果的详细信息
            let count = min(3, results.count)
            for i in 0..<count {
                let result = results[i]
                Logger.info("📍 [结果\(i+1)] 标题: \(result.title), 副标题: \(result.subtitle)", type: .location)
            }

            // 🎯 智能自动识别：检查是否有完全匹配的地址
            checkForAutoSelection(results: results, searchQuery: completer.queryFragment)
        } else {
            Logger.warning("⚠️ 搜索结果为空，查询: '\(completer.queryFragment)'", type: .location)
        }

        // 调用回调函数更新结果
        Logger.info("🔄 调用onUpdateResults回调", type: .location)
        onUpdateResults(results)
    }

    func completer(_ completer: MKLocalSearchCompleter, didFailWithError error: Error) {
        Logger.error("❌ [地址编辑调试] CompleterDelegate搜索失败", type: .location)
        Logger.error("❌ [地址编辑调试] 错误详情: \(error.localizedDescription)", type: .location)
        Logger.error("❌ [地址编辑调试] 查询内容: '\(completer.queryFragment)'", type: .location)
        Logger.error("❌ [地址编辑调试] 错误类型: \(type(of: error))", type: .location)

        // 如果是网络错误，提供更详细的信息
        if let nsError = error as NSError? {
            Logger.error("❌ [地址编辑调试] NSError域: \(nsError.domain)", type: .location)
            Logger.error("❌ [地址编辑调试] NSError代码: \(nsError.code)", type: .location)
            Logger.error("❌ [地址编辑调试] NSError用户信息: \(nsError.userInfo)", type: .location)
        }
    }

    // 🎯 检查是否应该自动选择地址
    private func checkForAutoSelection(results: [MKLocalSearchCompletion], searchQuery: String) {
        guard !results.isEmpty else { return }

        // 标准化搜索查询
        let normalizedQuery = normalizeAddressForComparison(searchQuery)
        Logger.info("🔍 检查自动识别: 查询='\(normalizedQuery)'", type: .location)

        // 检查每个结果是否完全匹配
        for (index, result) in results.enumerated() {
            let fullResultAddress = "\(result.title), \(result.subtitle)"
            let normalizedResult = normalizeAddressForComparison(fullResultAddress)

            Logger.info("🔍 比较结果[\(index)]: '\(normalizedResult)'", type: .location)

            // 检查是否完全匹配
            if isAddressMatch(normalizedQuery, normalizedResult) {
                Logger.info("✅ 找到完全匹配的地址，自动选择: \(result.title)", type: .location)

                // 🎯 移除自动选择功能，避免界面hang
                // 让用户手动选择搜索结果
                Logger.info("✅ 找到完全匹配的地址，但不自动选择: \(result.title)", type: .location)
                return
            }
        }

        Logger.info("ℹ️ 没有找到完全匹配的地址，显示搜索结果供用户选择", type: .location)
    }

    // 🎯 自动选择结果
    private func autoSelectResult(_ result: MKLocalSearchCompletion) {
        Logger.info("🎯 自动选择地址: \(result.title)", type: .location)

        // 通知父组件自动选择了地址
        onAutoSelect?(result)
    }

    // 🎯 标准化地址用于比较
    private func normalizeAddressForComparison(_ address: String) -> String {
        return address.lowercased()
            .replacingOccurrences(of: "street", with: "st")
            .replacingOccurrences(of: "road", with: "rd")
            .replacingOccurrences(of: "avenue", with: "ave")
            .replacingOccurrences(of: "drive", with: "dr")
            .replacingOccurrences(of: "court", with: "ct")
            .replacingOccurrences(of: "place", with: "pl")
            .replacingOccurrences(of: "lane", with: "ln")
            .replacingOccurrences(of: "close", with: "cl")
            .replacingOccurrences(of: "crescent", with: "cres")
            .replacingOccurrences(of: "mount", with: "mt")
            .replacingOccurrences(of: "victoria", with: "vic")
            .replacingOccurrences(of: ",", with: " ")
            .replacingOccurrences(of: "  ", with: " ")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 🎯 检查地址是否匹配
    private func isAddressMatch(_ query: String, _ result: String) -> Bool {
        // 完全匹配
        if query == result {
            return true
        }

        // 🎯 智能匹配：处理缩写和格式差异
        let queryComponents = extractAddressComponents(query)
        let resultComponents = extractAddressComponents(result)

        Logger.info("🔍 地址组件比较:", type: .location)
        Logger.info("  查询: 门牌号='\(queryComponents.streetNumber)', 街道='\(queryComponents.streetName)', 郊区='\(queryComponents.suburb)'", type: .location)
        Logger.info("  结果: 门牌号='\(resultComponents.streetNumber)', 街道='\(resultComponents.streetName)', 郊区='\(resultComponents.suburb)'", type: .location)

        // 检查关键组件匹配
        let streetNumberMatch = queryComponents.streetNumber == resultComponents.streetNumber
        let streetNameMatch = isStreetNameEquivalent(queryComponents.streetName, resultComponents.streetName)
        let suburbMatch = isSuburbEquivalent(queryComponents.suburb, resultComponents.suburb)

        Logger.info("  匹配结果: 门牌号=\(streetNumberMatch), 街道=\(streetNameMatch), 郊区=\(suburbMatch)", type: .location)

        // 所有关键组件都匹配才认为是同一地址
        return streetNumberMatch && streetNameMatch && suburbMatch
    }

    // 🎯 提取地址组件
    private func extractAddressComponents(_ address: String) -> (streetNumber: String, streetName: String, suburb: String) {
        let components = address.components(separatedBy: " ")

        // 提取门牌号（第一个数字部分）
        let streetNumber = components.first { $0.range(of: "^[0-9]+", options: .regularExpression) != nil } ?? ""

        // 提取街道名（门牌号后到第一个逗号前的部分）
        var streetName = ""
        if let numberIndex = components.firstIndex(where: { $0.range(of: "^[0-9]+", options: .regularExpression) != nil }) {
            let streetComponents = Array(components[(numberIndex + 1)...])
            streetName = streetComponents.joined(separator: " ").components(separatedBy: ",").first ?? ""
        }

        // 提取郊区（通常在第一个逗号后）
        let suburb = address.components(separatedBy: ",").dropFirst().first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        return (
            streetNumber: streetNumber.trimmingCharacters(in: .whitespacesAndNewlines),
            streetName: streetName.trimmingCharacters(in: .whitespacesAndNewlines),
            suburb: suburb.trimmingCharacters(in: .whitespacesAndNewlines)
        )
    }

    // 🎯 检查街道名是否等效（处理缩写）
    private func isStreetNameEquivalent(_ name1: String, _ name2: String) -> Bool {
        let normalized1 = normalizeStreetName(name1)
        let normalized2 = normalizeStreetName(name2)

        return normalized1 == normalized2
    }

    // 🎯 标准化街道名（处理缩写）
    private func normalizeStreetName(_ name: String) -> String {
        return name.lowercased()
            .replacingOccurrences(of: "street", with: "st")
            .replacingOccurrences(of: "road", with: "rd")
            .replacingOccurrences(of: "avenue", with: "ave")
            .replacingOccurrences(of: "drive", with: "dr")
            .replacingOccurrences(of: "court", with: "ct")
            .replacingOccurrences(of: "place", with: "pl")
            .replacingOccurrences(of: "lane", with: "ln")
            .replacingOccurrences(of: "close", with: "cl")
            .replacingOccurrences(of: "crescent", with: "cres")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 🎯 检查郊区是否等效
    private func isSuburbEquivalent(_ suburb1: String, _ suburb2: String) -> Bool {
        let normalized1 = suburb1.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        let normalized2 = suburb2.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        // 完全匹配
        if normalized1 == normalized2 {
            return true
        }

        // 检查是否是已知的等效郊区
        let equivalentSuburbs = [
            ["glen waverley", "glenwaverley"],
            ["mount waverley", "mt waverley", "mountwaverley"],
            ["wheelers hill", "wheelershill"]
        ]

        for group in equivalentSuburbs {
            if group.contains(normalized1) && group.contains(normalized2) {
                return true
            }
        }

        return false
    }
}

// 预览
struct EnhancedAddressAutocompletePreview: View {
    @State private var searchText: String = ""
    @State private var selectedCoordinate: CLLocationCoordinate2D? = nil

    var body: some View {
        VStack {
            EnhancedAddressAutocomplete(
                searchText: $searchText,
                selectedCoordinate: $selectedCoordinate
            )
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            .padding()

            if let coordinate = selectedCoordinate {
                Text("已选择坐标: \(coordinate.latitude), \(coordinate.longitude)")
                    .font(.caption)
                    .padding()
            }

            Spacer()
        }
        .background(Color(.systemGroupedBackground))
    }
}
// MARK: - EnhancedAddressAutocomplete 地址州修复扩展
extension EnhancedAddressAutocomplete {
    /// 检查并修复缺少州信息的地址
    /// - Parameter address: 原始地址
    /// - Returns: 修复后的地址（如果不需要修复则返回原地址）
    private func fixAddressStateIfNeeded(_ address: String) async -> String {
        // 使用 AddressStateFixService 检测并修复地址
        if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: address) {
            Logger.info("🔧 EnhancedAddressAutocomplete - 地址州修复成功: \(address) -> \(fixedAddress)", type: .location)
            return fixedAddress
        }

        // 如果不需要修复，返回原地址
        return address
    }
}

// MARK: - 模拟MKLocalSearchCompletion

private class EnhancedMockLocalSearchCompletion: MKLocalSearchCompletion {
    private var _title: String = ""
    private var _subtitle: String = ""

    override var title: String {
        get { return _title }
        set { _title = newValue }
    }

    override var subtitle: String {
        get { return _subtitle }
        set { _subtitle = newValue }
    }
}

#Preview("EnhancedAddressAutocomplete") {
    EnhancedAddressAutocompletePreview()
}
