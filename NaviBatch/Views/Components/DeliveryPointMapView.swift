import SwiftUI
import MapKit

/// 配送点地图标记视图
/// 在地图上显示配送点，并显示地理编码警告
struct DeliveryPointMapView: View {
    let point: DeliveryPoint
    let number: Int
    let isSelected: Bool
    let showWarning: Bool

    init(point: DeliveryPoint, number: Int, isSelected: Bool = false, showWarning: Bool = true) {
        self.point = point
        self.number = number
        self.isSelected = isSelected
        self.showWarning = showWarning
    }

    // 确定点的类型
    private var pointType: PointType {
        if point.isStartPoint && point.isEndPoint {
            // 同时是起点和终点
            return .startEnd
        } else if point.isStartPoint {
            return .start
        } else if point.isEndPoint {
            return .end
        } else {
            return .waypoint
        }
    }

    var body: some View {
        ZStack {
            // 使用MarkerView组件，传递点类型和优化状态
            // 如果是第三方快递且有排序号，使用第三方排序号
            let customText = point.isThirdPartyWithSort ? point.thirdPartySortNumber : nil

            MarkerView(
                number: number,
                packageCount: point.packageCount,
                // 如果是途经点且已优化，使用紫色；否则使用状态颜色作为备用
                color: (pointType == .waypoint && point.isOptimized) ? Color(hex: "B36AE2") : point.deliveryStatus.color,
                isAssignedToGroup: point.isAssignedToGroup,
                groupNumber: point.assignedGroupNumber,
                shouldFade: false,
                pointType: pointType, // 传递点类型
                isCompleted: point.deliveryStatus == .completed,
                isFailed: point.deliveryStatus == .failed,
                customText: customText,
                hasCoordinateWarning: showWarning && point.geocodingWarning != nil // 🚨 传递坐标警告状态
            )

            // 警告图标（如果有）
            if showWarning && point.geocodingWarning != nil {
                warningIcon
                    .offset(x: 12, y: -12)
            }

            // 起点/终点标记
            if point.isStartPoint {
                startPointBadge
            } else if point.isEndPoint {
                endPointBadge
            }

            // 选中状态指示器
            if isSelected {
                Circle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: 36, height: 36)
            }
        }
    }

    // 背景颜色 - 保留以兼容其他代码
    private var backgroundColor: Color {
        if point.isStartPoint {
            return .blue // 起点使用蓝色
        } else if point.isEndPoint {
            return .green // 终点使用绿色
        } else {
            return Color(hex: "B36AE2") // 途经点使用紫色
        }
    }

    // 警告图标
    private var warningIcon: some View {
        ZStack {
            Circle()
                .fill(Color.white)
                .frame(width: 16, height: 16)

            Image(systemName: point.geocodingWarningType.iconName)
                .font(.system(size: 10))
                .foregroundColor(point.geocodingWarningType.color)
        }
    }

    // 起点标记
    private var startPointBadge: some View {
        ZStack {
            Circle()
                .fill(Color.white)
                .frame(width: 16, height: 16)
                .offset(x: -12, y: -12)

            Image(systemName: "flag.fill")
                .font(.system(size: 10))
                .foregroundColor(.green)
                .offset(x: -12, y: -12)
        }
    }

    // 终点标记
    private var endPointBadge: some View {
        ZStack {
            Circle()
                .fill(Color.white)
                .frame(width: 16, height: 16)
                .offset(x: -12, y: -12)

            Image(systemName: "flag.checkered")
                .font(.system(size: 10))
                .foregroundColor(.red)
                .offset(x: -12, y: -12)
        }
    }
}



/// 配送点详情气泡视图
struct DeliveryPointCalloutView: View {
    let point: DeliveryPoint

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 排序编号和地址 - 确保左对齐
            HStack(spacing: 8) {
                // 排序编号
                Text("#\(point.sorted_number)")
                    .font(.headline)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(4)

                // 地址
                Text(point.primaryAddress)
                    .font(.headline)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 包裹信息已移除

            // 警告信息（如果有）
            if point.geocodingWarning != nil {
                HStack {
                    Image(systemName: point.geocodingWarningType.iconName)
                        .foregroundColor(point.geocodingWarningType.color)

                    Text(point.geocodingWarningType.message)
                        .font(.caption)
                        .foregroundColor(point.geocodingWarningType.color)
                }
            }

            // 备注（如果有）
            if let notes = point.notes, !notes.isEmpty {
                HStack {
                    Image(systemName: "note.text")
                    Text(notes)
                        .font(.caption)
                        .lineLimit(2)
                }
            }
        }
        .padding(8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
    }
}

#Preview(traits: .sizeThatFitsLayout) {
    let point = DeliveryPoint(
        sort_number: 1,
        streetName: "1 Kerferd Road, Glen Waverley, VIC 3150, Australia",
        coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642)
    )
    let _ = {
        point.geocodingWarning = DeliveryPoint.GeocodingWarningType.partialMatch.rawValue
    }()

    return VStack(spacing: 20) {
        DeliveryPointMapView(point: point, number: 1)
        DeliveryPointMapView(point: point, number: 2, isSelected: true)
        DeliveryPointCalloutView(point: point)
    }
    .padding()
}
