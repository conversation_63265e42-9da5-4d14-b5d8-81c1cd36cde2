import SwiftUI
import MapKit
import CoreLocation

/// 简单地址输入组件 - 专门用于地址编辑，支持直接输入和可选的搜索建议
struct SimpleAddressInputView: View {
    @Binding var address: String
    @Binding var selectedCoordinate: CLLocationCoordinate2D?

    @State private var showingSuggestions = false
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var searchCompleter = MKLocalSearchCompleter()
    @State private var completerDelegate: SimpleCompleterDelegate?
    @State private var debounceTask: Task<Void, Never>? = nil

    var placeholder: String = "enter_address".localized
    var onAddressSelected: ((String, CLLocationCoordinate2D) -> Void)?

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 地址输入框
            HStack {
                Image(systemName: "location")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))

                TextField(placeholder, text: $address, axis: .vertical)
                    .autocorrectionDisabled()
                    .font(.system(size: 16))
                    .lineLimit(2...4)  // 允许2-4行显示，确保长地址能完整显示
                    .multilineTextAlignment(.leading)
                    .textFieldStyle(.plain)  // 使用plain样式避免截断
                    .onChange(of: address) { _, newValue in
                        handleAddressChange(newValue)
                    }
                    .onSubmit {
                        // 用户按回车时隐藏建议
                        showingSuggestions = false
                    }

                if !address.isEmpty {
                    Button(action: {
                        address = ""
                        searchResults = []
                        showingSuggestions = false
                        debounceTask?.cancel()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                            .font(.system(size: 16))
                    }
                }

                // 搜索建议开关
                Button(action: {
                    showingSuggestions.toggle()
                    if showingSuggestions && !address.isEmpty {
                        performSearch(address)
                    }
                }) {
                    Image(systemName: showingSuggestions ? "chevron.up" : "chevron.down")
                        .foregroundColor(.blue)
                        .font(.system(size: 14))
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color(.systemGray6))
            .cornerRadius(8)

            // 搜索建议列表 - 🔧 修复hang问题：限制高度，避免覆盖地图
            if showingSuggestions && !searchResults.isEmpty {
                VStack(spacing: 0) {
                    ForEach(searchResults.prefix(3), id: \.self) { result in
                        Button(action: {
                            selectSearchResult(result)
                        }) {
                            HStack {
                                Image(systemName: "mappin.circle.fill")
                                    .foregroundColor(.red)
                                    .font(.system(size: 14))

                                VStack(alignment: .leading, spacing: 2) {
                                    Text(result.title)
                                        .foregroundColor(.primary)
                                        .font(.system(size: 14))
                                        .fontWeight(.medium)
                                        .lineLimit(1)  // 🔧 限制行数避免过长

                                    if !result.subtitle.isEmpty {
                                        Text(result.subtitle)
                                            .foregroundColor(.secondary)
                                            .font(.system(size: 12))
                                            .lineLimit(1)  // 🔧 限制行数避免过长
                                    }
                                }

                                Spacer()
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                        }
                        .buttonStyle(PlainButtonStyle())

                        if result != searchResults.prefix(3).last {
                            Divider()
                                .padding(.leading, 32)
                        }
                    }
                }
                .frame(maxHeight: 150)  // 🔧 限制最大高度，防止覆盖地图
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                .padding(.top, 4)
                .clipped()  // 🔧 确保内容不会溢出
                .zIndex(1000)  // 🔧 确保搜索结果在最上层，但不干扰地图手势
            }
        }
        .onAppear {
            setupSearchCompleter()
        }
        .onDisappear {
            // 🔧 完善的资源清理，防止hang
            cleanupResources()
        }
    }

    // MARK: - 私有方法

    private func handleAddressChange(_ newValue: String) {
        // 取消之前的防抖任务
        debounceTask?.cancel()

        // 如果显示建议且有输入内容，进行搜索
        if showingSuggestions && !newValue.isEmpty {
            debounceTask = Task {
                try? await Task.sleep(nanoseconds: 500_000_000) // 500ms 防抖

                guard !Task.isCancelled else { return }

                await MainActor.run {
                    performSearch(newValue)
                }
            }
        } else {
            searchResults = []
        }
    }

    private func setupSearchCompleter() {
        completerDelegate = SimpleCompleterDelegate { results in
            DispatchQueue.main.async {
                self.searchResults = results
            }
        }

        searchCompleter.delegate = completerDelegate
        searchCompleter.resultTypes = .address

        // 设置全球搜索区域
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        searchCompleter.region = globalRegion
    }

    private func performSearch(_ query: String) {
        // 使用语言环境感知搜索服务
        LocaleAwareAddressSearchService.shared.performSearch(query: query) { results in
            DispatchQueue.main.async {
                self.searchResults = results
                print("SimpleAddressInputView - 语言环境感知搜索完成: \(results.count)个结果")
            }
        }
    }

    private func selectSearchResult(_ result: MKLocalSearchCompletion) {
        let fullAddress = result.subtitle.isEmpty ? result.title : "\(result.title), \(result.subtitle)"
        let cleanedAddress = cleanAddressFormat(fullAddress)
        address = cleanedAddress
        showingSuggestions = false
        searchResults = []

        print("🔍 选择搜索结果: '\(fullAddress)' -> 清理后: '\(cleanedAddress)'")

        // 🎯 使用与SimpleAddressSheet相同的地址处理逻辑
        Task {
            // 🏠 首先检查地址库中是否已存在该地址
            print("🏠 SimpleAddressInputView - 检查地址库: \(cleanedAddress)")
            let existingAddress = await UserAddressDatabase.shared.getValidatedAddress(for: cleanedAddress)

            if let existing = existingAddress {
                print("🏠 SimpleAddressInputView - ✅ 地址库命中: \(cleanedAddress) -> (\(existing.coordinate.latitude), \(existing.coordinate.longitude))")

                await MainActor.run {
                    self.selectedCoordinate = existing.coordinate
                    self.onAddressSelected?(cleanedAddress, existing.coordinate)
                    print("🏠 SimpleAddressInputView - 使用地址库数据完成地址选择: \(cleanedAddress)")
                }
                return
            }

            print("🏠 SimpleAddressInputView - 地址库未命中，进行全球地址处理: \(cleanedAddress)")

            // 🌍 使用全球地址处理器
            let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(cleanedAddress)

            await MainActor.run {
                switch globalResult {
                case .success(_, let formattedAddress, let coordinate, _, let strategy, let confidence):
                    print("🌍 SimpleAddressInputView - 全球地址处理成功: \(strategy) - \(formattedAddress) (置信度: \(confidence))")

                    // 🏠 保存到地址库
                    Task {
                        print("🏠 SimpleAddressInputView - 保存新地址到地址库: \(formattedAddress) -> (\(coordinate.latitude), \(coordinate.longitude))")
                        await UserAddressDatabase.shared.saveValidatedAddress(
                            formattedAddress,
                            coordinate: coordinate,
                            source: .manual,
                            confidence: 0.95
                        )
                        print("🏠 SimpleAddressInputView - ✅ 地址库更新完成: \(formattedAddress)")
                    }

                    self.selectedCoordinate = coordinate
                    self.onAddressSelected?(cleanedAddress, coordinate)
                    print("✅ SimpleAddressInputView - 全球地址处理成功: \(cleanedAddress)")

                case .failed(_, let reason):
                    print("❌ SimpleAddressInputView - 全球地址处理失败: \(reason)")
                    // 降级到基本搜索
                    self.fallbackToBasicSearch(result)
                }
            }
        }
    }

    private func fallbackToBasicSearch(_ result: MKLocalSearchCompletion) {
        let searchRequest = MKLocalSearch.Request(completion: result)
        let search = MKLocalSearch(request: searchRequest)

        search.start { response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("地址搜索失败: \(error.localizedDescription)")
                    return
                }

                guard let mapItem = response?.mapItems.first else {
                    print("未找到地址结果")
                    return
                }

                let coordinate = mapItem.placemark.coordinate

                // 🎯 使用反向地理编码获取英文格式的地址，避免中文本地化
                Task {
                    let geocoder = CLGeocoder()
                    let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

                    do {
                        let _ = try await geocoder.reverseGeocodeLocation(
                            location,
                            preferredLocale: Locale(identifier: "en_US")
                        )

                        await MainActor.run {
                            self.selectedCoordinate = coordinate
                            self.onAddressSelected?(self.address, coordinate)
                        }
                    } catch {
                        print("反向地理编码失败，使用原始坐标: \(error.localizedDescription)")
                        await MainActor.run {
                            self.selectedCoordinate = coordinate
                            self.onAddressSelected?(self.address, coordinate)
                        }
                    }
                }
            }
        }
    }

    /// 安全的地址格式清理，避免意外截断
    private func cleanAddressFormat(_ address: String) -> String {
        print("🔧 SimpleAddressInputView: 开始清理地址格式: '\(address)'")

        var cleaned = address.trimmingCharacters(in: .whitespacesAndNewlines)
        let originalLength = cleaned.count

        // 只进行最基本的清理，避免过度处理

        // 1. 清理多余空格
        cleaned = cleaned.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        )

        // 2. 只在明确需要时进行门牌号逗号清理
        if cleaned.range(of: "^\\d+,\\s+", options: .regularExpression) != nil {
            print("🔧 检测到门牌号后的逗号，进行清理")
            cleaned = cleaned.replacingOccurrences(
                of: "^(\\d+),\\s+",
                with: "$1 ",
                options: .regularExpression
            )
        }

        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        print("🔧 SimpleAddressInputView: 地址清理完成: '\(cleaned)'")
        print("🔧 原始长度: \(originalLength), 清理后长度: \(cleaned.count)")

        // 安全检查：如果清理后地址明显变短，返回原始地址
        if cleaned.count < Int(Double(originalLength) * 0.9) {
            print("⚠️ 警告: 地址清理后长度减少过多，返回原始地址")
            return address.trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return cleaned
    }
}

// MARK: - 简单搜索代理

class SimpleCompleterDelegate: NSObject, MKLocalSearchCompleterDelegate {
    private let onResultsUpdated: ([MKLocalSearchCompletion]) -> Void

    init(onResultsUpdated: @escaping ([MKLocalSearchCompletion]) -> Void) {
        self.onResultsUpdated = onResultsUpdated
    }

    func completerDidUpdateResults(_ completer: MKLocalSearchCompleter) {
        let results = completer.results

        // 🎯 过滤掉包含中文的搜索结果，确保英文地址
        let englishResults = results.filter { result in
            let fullText = "\(result.title) \(result.subtitle)"
            let hasChineseCharacters = AddressStandardizer.containsChineseCharacters(fullText)
            if hasChineseCharacters {
                print("过滤中文搜索结果: \(fullText)")
            }
            return !hasChineseCharacters
        }

        // 限制结果数量，提高性能
        let limitedResults = Array(englishResults.prefix(5))
        onResultsUpdated(limitedResults)
    }

    func completer(_ completer: MKLocalSearchCompleter, didFailWithError error: Error) {
        print("地址搜索失败: \(error.localizedDescription)")
        onResultsUpdated([])
    }
}

#Preview {
    @Previewable @State var address = "32 Plymouth Circle, Daly City, 94015"
    @Previewable @State var coordinate: CLLocationCoordinate2D? = nil

    return VStack {
        SimpleAddressInputView(
            address: $address,
            selectedCoordinate: $coordinate,
            placeholder: "输入地址",
            onAddressSelected: { addr, coord in
                print("选择了地址: \(addr), 坐标: \(coord)")
            }
        )
        .padding()

        if let coord = coordinate {
            Text("坐标: \(coord.latitude), \(coord.longitude)")
                .font(.caption)
                .foregroundColor(.secondary)
        }

        Spacer()
    }
}

// MARK: - SimpleAddressInputView Extension
extension SimpleAddressInputView {
    /// 🔧 完善的资源清理方法，防止hang问题
    private func cleanupResources() {
        print("🧹 SimpleAddressInputView - 开始清理资源")

        // 取消所有异步任务
        debounceTask?.cancel()
        debounceTask = nil

        // 清理搜索相关状态
        searchCompleter.cancel()
        searchCompleter.delegate = nil
        searchResults = []
        showingSuggestions = false

        print("✅ SimpleAddressInputView - 资源清理完成")
    }
}
