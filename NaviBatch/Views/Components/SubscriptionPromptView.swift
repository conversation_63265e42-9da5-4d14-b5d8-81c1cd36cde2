import SwiftUI
import StoreKit
import UIKit

/// 订阅提示视图
struct SubscriptionPromptView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @Environment(\.subscriptionViewOptions) private var options
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared
    @ObservedObject private var storeKitManager = StoreKitManager.shared
    @State private var showOneClickPromo = false
    @State private var showSubscriptionView = false

    // 添加关闭回调函数
    var onClose: (() -> Void)?

    // 用于调试
    private let viewID = UUID().uuidString

    var body: some View {
        VStack(spacing: 12) {  // 从16减少到12，使整体更紧凑
            // 标题栏 - 将标题与关闭按钮放在同一行，更加紧凑
            if options.showTitle || options.showCloseButton {
                HStack(alignment: .center) {
                    if options.showTitle {
                        Text("Subscription Plans")
                            .font(.title2)  // 减小字体大小
                            .fontWeight(.bold)
                            .minimumScaleFactor(0.7)
                            .lineLimit(1)
                            .frame(maxWidth: .infinity, alignment: .leading)  // 确保左对齐
                    }

                    Spacer()

                    if options.showCloseButton {
                        Button(action: {
                            // 如果提供了关闭回调，则使用回调
                            if let onClose = onClose {
                                onClose()
                            } else {
                                // 否则使用环境变量
                                dismiss()
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.gray)
                        }
                        .padding(8)  // 减小内边距
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 12)  // 减少顶部空间
            }

            // 副标题 - 减少与标题之间的间距
            if options.showTitle {
                Text("Choose a plan and enjoy premium features to save time and fuel")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .minimumScaleFactor(0.8)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 8)  // 从12减少到8
            }

            // 订阅计划展示 - 根据屏幕尺寸调整布局
            if horizontalSizeClass == .regular {
                // 平板布局 - 横向排列
                HStack(spacing: 16) {
                    freeSubscriptionCard()
                    proSubscriptionCard()
                    expertSubscriptionCard()
                }
                .padding(.horizontal)
            } else {
                // 手机布局 - 使用ScrollView实现横向滚动
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        freeSubscriptionCard()
                            .frame(width: UIScreen.main.bounds.width * 0.85)

                        proSubscriptionCard()
                            .frame(width: UIScreen.main.bounds.width * 0.85)

                        expertSubscriptionCard()
                            .frame(width: UIScreen.main.bounds.width * 0.85)
                    }
                    .padding(.horizontal)
                }
                .padding(.bottom, 0)  // 减少底部间距，从4改为0
            }

            Button(action: {
                // 使用 StoreKitManager 恢复购买
                Task {
                    do {
                        try await StoreKitManager.shared.restorePurchases()
                    } catch {
                        print("[ERROR] SubscriptionPromptView - 恢复购买失败: \(error.localizedDescription)")
                        // 显示错误提示
                        NotificationCenter.default.post(
                            name: Notification.Name("ShowToast"),
                            object: "恢复购买失败: \(error.localizedDescription)"
                        )
                    }
                }
            }) {
                Text("restore_purchases".localized)
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .padding(.vertical, 6)  // 减小垂直内边距
                    .padding(.horizontal, 12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
            }
            .frame(maxWidth: .infinity, alignment: .center)  // 使按钮居中
            .padding(.top, 4)  // 减少顶部间距
            .padding(.bottom, 4)  // 减少底部间距
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
        .sheet(isPresented: $showOneClickPromo) {
            OneClickNavigationPromoView()
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.hidden)
        }
        .sheet(isPresented: $showSubscriptionView) {
            // 使用统一订阅视图 - 现代风格，适合sheet展示
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,

                showOneClickPromo: true,
                presentationMode: .sheet
            ))
            .presentationDetents([.large], selection: .constant(.large))
            .presentationDragIndicator(.hidden)
        }
    }

    // 免费版卡片
    private func freeSubscriptionCard() -> some View {
        VStack(spacing: 10) {  // 减少间距为10
            // 将Free和$0合并在一行
            HStack(alignment: .firstTextBaseline, spacing: 8) {
                Text("Free")
                    .font(.title)
                    .fontWeight(.bold)
                    .minimumScaleFactor(0.7)
                    .lineLimit(1)

                Text("$0")
                    .font(.system(size: 32))
                    .fontWeight(.bold)
                    .minimumScaleFactor(0.6)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(.top, 4)

            Text("Free Plan")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 6)  // 减少底部间距

            // 功能列表更紧凑
            VStack(alignment: .leading, spacing: 8) {  // 减少每个功能项间的间距
                featureRow(text: "Route Optimization", available: true)
                featureRow(text: "Unlimited Routes", available: true)
                featureRow(text: "Unlimited Optimizations", available: true)
                featureRow(text: "Max 20 Addresses", available: true)
                featureRow(text: "Save Fuel by 30%", available: true)
                featureRow(text: "File Import", available: true, isNew: true)
                featureRow(text: "Batch Address Paste", available: true)
                featureRow(text: "Online Address Download", available: true)
                featureRow(text: "Photo Verification", available: true)
                featureRow(text: "Smart Photo Albums", available: true, isNew: true)
                featureRow(text: "One-Click Batch Navigation", available: false)
                featureRow(text: "Package Finder", available: false)
            }
            .padding(.vertical, 6)  // 整体减少垂直内边距

            Spacer(minLength: 8)  // 确保有最小间距

            // 按钮 - 如果当前是免费版则显示"当前计划"，否则显示"选择免费版"
            Button(action: {
                // 选择免费版
                subscriptionManager.updateSubscription(to: .free)
                if let onClose = onClose {
                    onClose()
                } else {
                    dismiss()
                }
            }) {
                Text(subscriptionManager.currentTier == .free ? "Current Plan" : "Select Free Plan")
                    .font(.headline)
                    .foregroundColor(subscriptionManager.currentTier == .free ? .green : .white)
                    .minimumScaleFactor(0.8)
                    .lineLimit(1)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(subscriptionManager.currentTier == .free ? Color.green.opacity(0.2) : Color.blue)
                    .cornerRadius(10)
            }
            .disabled(subscriptionManager.currentTier == .free)
            .padding(.top, 4)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(subscriptionManager.currentTier == .free ? Color.green : Color.clear, lineWidth: 2)
        )
    }

    // Pro版卡片
    private func proSubscriptionCard() -> some View {
        VStack(spacing: 10) {  // 减少间距为10
            // Pro计划标签
            Text("Pro")
                .font(.title)
                .fontWeight(.bold)
                .minimumScaleFactor(0.7)
                .lineLimit(1)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.top, 4)

            // 最受欢迎标签
            Text("Most Popular")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .minimumScaleFactor(0.8)
                .lineLimit(1)
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(Color.blue)
                .cornerRadius(16)
                .padding(.bottom, 0)  // 减少底部间距

            // 价格显示 - 更清晰的层次
            (Text("$9.99").fontWeight(.bold) + Text("/month"))
                .font(.system(size: 32))  // 略微减小字体
                .lineLimit(1)
                .minimumScaleFactor(0.6)
                .padding(.bottom, 0)  // 减少底部间距

            Text("In-app Purchase")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 0)  // 减少间距

            // 免费试用标签 - 保持网站样式但减少间距
            HStack {
                Image(systemName: "gift")
                    .foregroundColor(.white)
                Text("Free 60-Day Trial")
                    .foregroundColor(.white)
                    .fontWeight(.medium)
                    .minimumScaleFactor(0.8)
                    .lineLimit(1)
            }
            .font(.subheadline)
            .padding(.horizontal, 12)
            .padding(.vertical, 5)  // 减少垂直内边距
            .background(Color.green)
            .cornerRadius(20)
            .padding(.bottom, 6)  // 减少底部间距

            // 功能列表更紧凑
            VStack(alignment: .leading, spacing: 8) {  // 减少每个功能项间的间距
                featureRow(text: "Route Optimization", available: true)
                featureRow(text: "Unlimited Routes", available: true)
                featureRow(text: "Unlimited Optimizations", available: true)
                featureRow(text: "Unlimited Addresses", available: true, highlighted: true)
                featureRow(text: "Save Fuel by 30%", available: true)
                featureRow(text: "File Import", available: true)
                featureRow(text: "Batch Address Paste", available: true)
                featureRow(text: "Online Address Download", available: true)
                featureRow(text: "Photo Verification", available: true)
                featureRow(text: "Smart Photo Albums", available: true)
                featureRowWithBadge(text: "One-Click Batch Navigation", available: true, badge: "Up to 60x faster!")
                featureRow(text: "Package Finder", available: true)
            }
            .padding(.vertical, 6)  // 整体减少垂直内边距

            Spacer()

            // 购买按钮
            Button(action: {
                // 如果当前已是Pro版则不做任何操作
                if subscriptionManager.currentTier == .pro {
                    return
                }

                // 否则显示订阅视图或直接订阅
                Task {
                    do {
                        try await storeKitManager.loadProducts()
                        await MainActor.run {
                            showSubscriptionView = true
                        }
                    } catch {
                        print("[ERROR] SubscriptionPromptView - 加载产品失败: \(error.localizedDescription)")
                        #if DEBUG
                        await MainActor.run {
                            showSubscriptionView = true
                        }
                        #endif
                    }
                }
            }) {
                Text(subscriptionManager.currentTier == .pro ? "Current Plan" : "Subscribe Now")
                    .font(.headline)
                    .foregroundColor(subscriptionManager.currentTier == .pro ? .green : .white)
                    .minimumScaleFactor(0.8)
                    .lineLimit(1)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(subscriptionManager.currentTier == .pro ? Color.green.opacity(0.2) : Color.blue)
                    .cornerRadius(10)
            }
            .disabled(subscriptionManager.currentTier == .pro)
            .padding(.top, 4)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.blue, lineWidth: 2)
        )
    }

    // Expert版卡片
    private func expertSubscriptionCard() -> some View {
        VStack(spacing: 10) {  // 减少间距为10
            Text("Expert")
                .font(.title)
                .fontWeight(.bold)
                .minimumScaleFactor(0.7)
                .lineLimit(1)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.top, 4)

            // 价格显示 - 更清晰的层次
            (Text("$59.99").fontWeight(.bold) + Text("/year"))
                .font(.system(size: 32))  // 略微减小字体
                .lineLimit(1)
                .minimumScaleFactor(0.6)
                .padding(.bottom, 0)  // 减少底部间距

            Text("In-app Purchase")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 6)  // 减少底部间距

            // 功能列表更紧凑
            VStack(alignment: .leading, spacing: 8) {  // 减少每个功能项间的间距
                featureRow(text: "All Pro Features", available: true)
                featureRow(text: "Unlimited Routes", available: true)
                featureRow(text: "Unlimited Optimizations", available: true)
                featureRow(text: "Unlimited Addresses", available: true)
                featureRow(text: "Save Fuel by 30%", available: true)
                featureRow(text: "File Import", available: true)
                featureRow(text: "Batch Address Paste", available: true)
                featureRow(text: "Online Address Download", available: true)
                featureRow(text: "Photo Verification", available: true)
                featureRow(text: "Smart Photo Albums", available: true)
                featureRow(text: "One-Click Batch Navigation", available: true)
                featureRow(text: "Package Finder", available: true)
                featureRow(text: "Annual Savings of 30%", available: true, highlighted: true)
            }
            .padding(.vertical, 6)  // 整体减少垂直内边距

            Spacer()

            // 购买按钮
            Button(action: {
                // 如果当前已是Expert版则不做任何操作
                if subscriptionManager.currentTier == .expert {
                    return
                }

                // 否则显示订阅视图或直接订阅
                Task {
                    do {
                        try await storeKitManager.loadProducts()
                        await MainActor.run {
                            showSubscriptionView = true
                        }
                    } catch {
                        print("[ERROR] SubscriptionPromptView - 加载产品失败: \(error.localizedDescription)")
                        #if DEBUG
                        await MainActor.run {
                            showSubscriptionView = true
                        }
                        #endif
                    }
                }
            }) {
                Text(subscriptionManager.currentTier == .expert ? "Current Plan" : "Subscribe Annually")
                    .font(.headline)
                    .foregroundColor(subscriptionManager.currentTier == .expert ? .green : .white)
                    .minimumScaleFactor(0.8)
                    .lineLimit(1)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(subscriptionManager.currentTier == .expert ? Color.green.opacity(0.2) : Color.blue)
                    .cornerRadius(10)
            }
            .disabled(subscriptionManager.currentTier == .expert)
            .padding(.top, 4)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(subscriptionManager.currentTier == .expert ? Color.green : Color.clear, lineWidth: 2)
        )
    }

    // 功能行
    private func featureRow(text: String, available: Bool, isNew: Bool = false, highlighted: Bool = false) -> some View {
        HStack(alignment: .top, spacing: 6) {  // 修改为顶部对齐，减小间距
            // 可用性标志
            if available {
                Image(systemName: "checkmark")
                    .foregroundColor(.green)
                    .frame(width: 14)  // 减小图标尺寸
            } else {
                Image(systemName: "xmark")
                    .foregroundColor(.red)
                    .frame(width: 14)  // 减小图标尺寸
            }

            // 功能文本
            Text(text)
                .font(.subheadline)
                .foregroundColor(highlighted ? .yellow : .primary)
                .fontWeight(highlighted ? .semibold : .regular)
                .minimumScaleFactor(0.8)
                .fixedSize(horizontal: false, vertical: true)  // 允许文本换行

            // NEW 标签
            if isNew {
                Text("NEW")
                    .font(.system(size: 9))
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 4)
                    .padding(.vertical, 1)
                    .background(Color.orange)
                    .cornerRadius(3)
            }

            Spacer()
        }
    }

    // 带徽章的功能行
    private func featureRowWithBadge(text: String, available: Bool, badge: String) -> some View {
        HStack(alignment: .top, spacing: 6) {  // 修改为顶部对齐，减小间距
            // 可用性标志
            if available {
                Image(systemName: "checkmark")
                    .foregroundColor(.green)
                    .frame(width: 14)  // 减小图标尺寸
            } else {
                Image(systemName: "xmark")
                    .foregroundColor(.red)
                    .frame(width: 14)  // 减小图标尺寸
            }

            // 功能文本 - 允许换行
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
                .minimumScaleFactor(0.8)
                .fixedSize(horizontal: false, vertical: true)  // 允许文本换行

            Spacer()

            // 徽章 - 更新为更紧凑的样式
            Text(badge)
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(.white)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)  // 减少垂直内边距
                .background(Color.blue)
                .cornerRadius(10)
        }
    }
}

#if DEBUG
struct SubscriptionPromptView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // iPhone预览
            SubscriptionPromptView()
                .previewDevice("iPhone 13")
                .previewDisplayName("iPhone 13")

            // iPad预览
            SubscriptionPromptView()
                .previewDevice("iPad Pro (11-inch) (3rd generation)")
                .previewDisplayName("iPad Pro 11")
        }
    }
}
#endif
