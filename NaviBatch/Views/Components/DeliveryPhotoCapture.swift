import SwiftUI
import Photos
import UIKit
import Foundation
import AVFoundation

/// 配送照片类型
enum DeliveryPhotoType: Int, CaseIterable, Identifiable {
    case doorNumber = 0   // 门牌号照片
    case packageLabel = 1 // 包裹标签照片
    case placement = 2    // 放置位置照片

    var id: Int { rawValue }

    var title: String {
        switch self {
        case .doorNumber:
            return "door_number_photo_title".localized
        case .packageLabel:
            return "package_label_photo_title".localized
        case .placement:
            return "placement_photo_title".localized
        }
    }

    var description: String {
        switch self {
        case .doorNumber:
            return "door_number_photo_desc".localized
        case .packageLabel:
            return "package_label_photo_desc".localized
        case .placement:
            return "placement_photo_desc".localized
        }
    }

    var iconName: String {
        switch self {
        case .doorNumber:
            return "door.left.hand.open"
        case .packageLabel:
            return "tag"
        case .placement:
            return "cube.box"
        }
    }

    var accentColor: Color {
        switch self {
        case .doorNumber:
            return .blue
        case .packageLabel:
            return .orange
        case .placement:
            return .green
        }
    }
}

/// 配送照片捕获视图
struct DeliveryPhotoCapture: View {
    // 环境变量
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme

    // 配送点
    let deliveryPoint: DeliveryPoint

    // 状态变量
    @State private var currentPhotoType: DeliveryPhotoType = .doorNumber
    @State private var capturedImages: [DeliveryPhotoType: UIImage] = [:]
    @State private var isShowingCamera = false
    @State private var isSaving = false
    @State private var errorMessage: String? = nil
    @State private var albumName: String = ""
    @State private var showingGuide = false
    @State private var showingCompletionAnimation = false

    // 计算属性
    private var allPhotosCompleted: Bool {
        DeliveryPhotoType.allCases.allSatisfy { capturedImages[$0] != nil }
    }

    // 初始化方法
    init(deliveryPoint: DeliveryPoint) {
        self.deliveryPoint = deliveryPoint

        // 生成相册名称
        if let route = deliveryPoint.route {
            _albumName = State(initialValue: PhotoAlbumService.shared.generateAlbumName(routeName: route.name))
        } else {
            _albumName = State(initialValue: PhotoAlbumService.shared.generateAlbumName(routeName: "配送"))
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景色
                backgroundGradient
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // 顶部进度指示器
                    StepProgressView(
                        currentStep: currentPhotoType.rawValue,
                        stepTitles: DeliveryPhotoType.allCases.map { $0.title },
                        stepIcons: DeliveryPhotoType.allCases.map { $0.iconName },
                        activeColor: currentPhotoType.accentColor,
                        completedColor: .green
                    )
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .padding(.bottom, 4)

                    // 照片预览区域
                    photoPreviewArea
                        .padding(.horizontal)

                    Spacer(minLength: 16)

                    // 底部按钮区域
                    bottomButtonArea
                }
                .navigationTitle("photo_capture_title".localized)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .cancellationAction) {
                        Button("cancel".localized) {
                            // 添加触觉反馈
                            let generator = UIImpactFeedbackGenerator(style: .light)
                            generator.impactOccurred()

                            dismiss()
                        }
                    }

                    ToolbarItem(placement: .confirmationAction) {
                        Button {
                            // 添加触觉反馈
                            let generator = UIImpactFeedbackGenerator(style: .medium)
                            generator.impactOccurred()

                            savePhotos()
                        } label: {
                            Text("done".localized)
                                .fontWeight(.semibold)
                        }
                        .disabled(!allPhotosCompleted || isSaving)
                        .opacity(allPhotosCompleted ? 1.0 : 0.5)
                    }
                }
                .fullScreenCover(isPresented: $isShowingCamera) {
                    CameraView(
                        currentPhotoType: currentPhotoType,
                        remainingPhotoTypes: getIncompletePhotoTypes().filter { $0 != currentPhotoType },
                        onImageCaptured: { image in
                            // 添加触觉反馈
                            let generator = UINotificationFeedbackGenerator()
                            generator.notificationOccurred(.success)

                            // 保存图片
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                capturedImages[currentPhotoType] = image
                            }

                            // 自动前进到下一个未完成的照片类型
                            if let nextType = getNextIncompletePhotoType() {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    currentPhotoType = nextType
                                }
                            }
                        },
                        onImagesCaptured: { images in
                            // 添加触觉反馈
                            let generator = UINotificationFeedbackGenerator()
                            generator.notificationOccurred(.success)

                            // 保存所有照片
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                capturedImages = images

                                // 设置当前类型为第一个类型（方便UI显示）
                                if let firstType = DeliveryPhotoType.allCases.first {
                                    currentPhotoType = firstType
                                }

                                // 如果所有照片都已完成，自动保存
                                if allPhotosCompleted {
                                    savePhotos()
                                }
                            }
                        }
                    )
                }
                .alert("error".localized, isPresented: .init(get: { errorMessage != nil }, set: { if !$0 { errorMessage = nil } })) {
                    Button("ok".localized, role: .cancel) {
                        errorMessage = nil
                    }
                } message: {
                    if let errorMessage = errorMessage {
                        Text(errorMessage)
                    }
                }

                // 保存中遮罩
                if isSaving {
                    savingOverlay
                }

                // 完成动画
                if showingCompletionAnimation {
                    completionAnimation
                }
            }
        }
    }

    // MARK: - 子视图

    // 背景渐变
    private var backgroundGradient: some View {
        Group {
            if colorScheme == .dark {
                LinearGradient(
                    gradient: Gradient(colors: [Color(hex: "1A1A1A"), Color(hex: "2A2A2A")]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            } else {
                LinearGradient(
                    gradient: Gradient(colors: [Color(hex: "F8F8F8"), Color(hex: "F0F0F0")]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            }
        }
    }

    // 顶部滑动提示
    private var slideInstructions: some View {
        HStack(spacing: 8) {
            if currentPhotoType.rawValue > 0 {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
            }

            Text("swipe_to_switch".localized)
                .font(.caption)
                .foregroundColor(.secondary)

            if currentPhotoType.rawValue < DeliveryPhotoType.allCases.count - 1 {
                Image(systemName: "chevron.right")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 6)
        .background(Color(.systemBackground).opacity(0.6))
        .cornerRadius(12)
        .padding(.bottom, 8)
    }

    // 相册名称指示器
    private var albumNameIndicator: some View {
        HStack(spacing: 8) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.caption)
                .foregroundColor(.blue)

            Text("photos_will_be_saved_to".localized)
                .font(.caption)
                .foregroundColor(.secondary)

            Text(albumName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.blue)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(8)
        .padding(.bottom, 8)
    }

    // 照片预览区域
    private var photoPreviewArea: some View {
        VStack(spacing: 0) {
            // 添加顶部滑动指示器，使滑动意图更明确
            slideInstructions

            // 相册名称提示
            albumNameIndicator

            // 照片预览卡片
            TabView(selection: $currentPhotoType) {
                ForEach(DeliveryPhotoType.allCases) { photoType in
                    photoCard(for: photoType)
                        .tag(photoType)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .background(Color.clear)
            .cornerRadius(16)
            .frame(height: 350)
        }
        .padding(.vertical, 8)
    }

    // 单个照片卡片视图
    private func photoCard(for photoType: DeliveryPhotoType) -> some View {
        ZStack {
            if let image = capturedImages[photoType] {
                // 已拍摄照片预览
                ZStack {
                    // 可缩放的图片视图 - 设置为填满整个卡片区域
                    ZoomableImageView(image: image)
                        .aspectRatio(contentMode: .fill) // 确保图片填满整个区域
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .clipped() // 裁剪超出部分
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(photoType.accentColor.opacity(0.3), lineWidth: 1)
                        )

                    // 重拍按钮
                    VStack {
                        HStack {
                            Spacer()

                            Button(action: {
                                // 添加触觉反馈
                                let generator = UIImpactFeedbackGenerator(style: .light)
                                generator.impactOccurred()

                                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                    capturedImages[photoType] = nil
                                }
                            }) {
                                Image(systemName: "arrow.triangle.2.circlepath.camera")
                                    .font(.system(size: 20, weight: .semibold))
                                    .foregroundColor(.white)
                                    .padding(10)
                                    .background(Circle().fill(Color.black.opacity(0.7)))
                                    .shadow(color: Color.black.opacity(0.3), radius: 3, x: 0, y: 2)
                            }
                            .padding(16)
                        }

                        Spacer()
                    }
                }
                .transition(.opacity.combined(with: .scale(scale: 0.95, anchor: .center)))
            } else {
                // 未拍摄状态 - 点击卡片拍照
                Button(action: {
                    // 添加触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()

                    // 显示相机
                    isShowingCamera = true
                }) {
                    VStack {
                        Spacer()

                        Image(systemName: photoType.iconName)
                            .font(.system(size: 60))
                            .foregroundColor(photoType.accentColor.opacity(0.5))

                        // 简洁的提示文本
                        Image(systemName: "camera.fill")
                            .font(.system(size: 24))
                            .padding(8)
                            .foregroundColor(.white)
                            .background(Circle().fill(photoType.accentColor.opacity(0.8)))
                            .padding(.top, 16)

                        Spacer()
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemBackground))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(photoType.accentColor.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(ScaleButtonStyle()) // 添加缩放效果反馈
                .transition(.opacity.combined(with: .scale(scale: 0.95, anchor: .center)))
            }
        }
        .frame(height: 350) // 固定卡片高度，与TabView高度一致
    }

    // 底部按钮区域
    private var bottomButtonArea: some View {
        VStack(spacing: 16) {
            // 完成按钮（仅当所有照片都已完成时显示）
            if allPhotosCompleted {
                Button(action: {
                    // 添加触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    savePhotos()
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 20))
                        Text("complete_photos".localized)
                            .font(.headline)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(16)
                    .shadow(color: Color.green.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .padding(.horizontal)
                .padding(.top, 8)
                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
        }
        .padding(.bottom, 16)
    }

    // 缩放按钮样式 - 为按钮添加轻微的缩放效果，提供视觉反馈
    struct ScaleButtonStyle: ButtonStyle {
        func makeBody(configuration: Self.Configuration) -> some View {
            configuration.label
                .scaleEffect(configuration.isPressed ? 0.96 : 1)
                .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
        }
    }

    // 保存中遮罩
    private var savingOverlay: some View {
        ZStack {
            Color.black.opacity(0.5)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)

                Text("saving_photos".localized)
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6).opacity(0.8))
                    .blur(radius: 0.5)
            )
        }
        .transition(.opacity)
    }

    // 完成动画
    private var completionAnimation: some View {
        ZStack {
            Color.black.opacity(0.5)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)

                Text("photo_save_success".localized)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground).opacity(0.9))
            )
            .scaleEffect(showingCompletionAnimation ? 1.0 : 0.8)
            .opacity(showingCompletionAnimation ? 1.0 : 0)
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: showingCompletionAnimation)
        }
        .transition(.opacity)
    }

    // MARK: - 辅助方法

    // 获取下一个未完成的照片类型
    private func getNextIncompletePhotoType() -> DeliveryPhotoType? {
        for type in DeliveryPhotoType.allCases {
            if capturedImages[type] == nil {
                return type
            }
        }
        return nil
    }

    // 保存照片
    private func savePhotos() {
        guard allPhotosCompleted else { return }

        withAnimation {
            isSaving = true
        }

        // 创建任务组
        Task {
            do {
                // 保存门牌号照片
                if let doorImage = capturedImages[.doorNumber] {
                    try await savePhoto(doorImage, type: .doorNumber)
                }

                // 保存包裹标签照片
                if let packageImage = capturedImages[.packageLabel] {
                    try await savePhoto(packageImage, type: .packageLabel)
                }

                // 保存放置位置照片
                if let placementImage = capturedImages[.placement] {
                    try await savePhoto(placementImage, type: .placement)
                }

                // 更新配送点状态
                await updateDeliveryPointStatus()

                // 显示完成动画
                await MainActor.run {
                    withAnimation {
                    isSaving = false
                    showingCompletionAnimation = true
                    }

                    // 延迟关闭视图
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        withAnimation {
                        showingCompletionAnimation = false
                        }

                        // 再延迟一点关闭视图，让动画完成
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        dismiss()
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    withAnimation {
                    isSaving = false
                    }
                    errorMessage = "保存照片失败: \(error.localizedDescription)"
                }
            }
        }
    }

    // 保存单张照片
    private func savePhoto(_ image: UIImage, type: DeliveryPhotoType) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PhotoAlbumService.shared.savePhotoToAlbum(image: image, albumName: albumName) { path, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }

                guard let path = path else {
                    continuation.resume(throwing: NSError(domain: "DeliveryPhotoCapture", code: 500, userInfo: [NSLocalizedDescriptionKey: "保存照片失败"]))
                    return
                }

                // 更新配送点的照片路径
                Task { @MainActor in
                    switch type {
                    case .doorNumber:
                        deliveryPoint.doorPhotoPath = path
                    case .packageLabel:
                        deliveryPoint.packagePhotoPath = path
                    case .placement:
                        deliveryPoint.placementPhotoPath = path
                    }

                    // 保存相册名称
                    deliveryPoint.photosAlbumName = albumName

                    // 尝试保存到数据库
                    do {
                        try modelContext.save()
                        continuation.resume(returning: ())
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        }
    }

    // 更新配送点状态
    private func updateDeliveryPointStatus() async {
        await MainActor.run {
            // 更新配送状态为已完成
            deliveryPoint.updateStatus(.completed)

            // 记录照片完成时间
            deliveryPoint.photosCompletionDate = Date()

            // 保存到数据库
            try? modelContext.save()
        }
    }

    // 获取所有未完成的照片类型
    private func getIncompletePhotoTypes() -> [DeliveryPhotoType] {
        return DeliveryPhotoType.allCases.filter { capturedImages[$0] == nil }
    }
}

// MARK: - 预览
#Preview("DeliveryPhotoCapture") {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: DeliveryPoint.self, configurations: config)

    let samplePoint = DeliveryPoint(
        sort_number: 1,
        streetName: "42 Blackburn Road, Glen Waverley, VIC 3150",
        latitude: -37.8815,
        longitude: 145.1642
    )
    let _ = {
        samplePoint.isOptimized = true
        samplePoint.sorted_number = 3
        container.mainContext.insert(samplePoint)
    }()

    return DeliveryPhotoCapture(deliveryPoint: samplePoint)
        .modelContainer(container)
}

// MARK: - 相机视图
struct CameraView: UIViewControllerRepresentable {
    var currentPhotoType: DeliveryPhotoType
    var remainingPhotoTypes: [DeliveryPhotoType]
    var onImageCaptured: (UIImage) -> Void
    var onImagesCaptured: ([DeliveryPhotoType: UIImage]) -> Void

    // 创建UIViewController
    func makeUIViewController(context: Context) -> UIViewController {
        // 在预览环境中返回空视图控制器
        if AppEnvironment.isPreview {
            let dummyVC = UIViewController()
            dummyVC.view.backgroundColor = .black

            // 添加提示标签
            let infoLabel = UILabel()
            infoLabel.text = "预览模式 - 相机模拟"
            infoLabel.textColor = .white
            infoLabel.textAlignment = .center
            infoLabel.font = .systemFont(ofSize: 18, weight: .medium)
            infoLabel.translatesAutoresizingMaskIntoConstraints = false

            dummyVC.view.addSubview(infoLabel)
            NSLayoutConstraint.activate([
                infoLabel.centerXAnchor.constraint(equalTo: dummyVC.view.centerXAnchor),
                infoLabel.centerYAnchor.constraint(equalTo: dummyVC.view.centerYAnchor)
            ])

            // 添加取消按钮
            let cancelButton = UIButton(type: .system)
            cancelButton.setTitle("关闭", for: .normal)
            cancelButton.tintColor = .white
            cancelButton.translatesAutoresizingMaskIntoConstraints = false
            cancelButton.addTarget(context.coordinator, action: #selector(Coordinator.fakeCancelTapped), for: .touchUpInside)

            dummyVC.view.addSubview(cancelButton)
            NSLayoutConstraint.activate([
                cancelButton.bottomAnchor.constraint(equalTo: dummyVC.view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
                cancelButton.centerXAnchor.constraint(equalTo: dummyVC.view.centerXAnchor)
            ])

            return dummyVC
        }

        // 创建自定义相机控制器
        print("创建自定义相机控制器")
        let cameraVC = AVCameraViewController()
        cameraVC.delegate = context.coordinator
        cameraVC.photoType = currentPhotoType

        // 设置连续拍摄模式的剩余类型
        if !remainingPhotoTypes.isEmpty {
            print("设置连续拍摄模式，剩余类型：\(remainingPhotoTypes.count)个")
            for type in remainingPhotoTypes {
                print("  - \(type.title)")
            }
            cameraVC.remainingPhotoTypes = remainingPhotoTypes
        }

        return cameraVC
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        // 更新类型
        if let cameraVC = uiViewController as? AVCameraViewController {
            if cameraVC.photoType != currentPhotoType {
                print("更新相机类型：\(cameraVC.photoType.title) -> \(currentPhotoType.title)")
                cameraVC.photoType = currentPhotoType
            }

            // 更新连续拍摄模式的剩余类型
            cameraVC.remainingPhotoTypes = remainingPhotoTypes
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }

    class Coordinator: NSObject, AVCameraViewControllerDelegate {
        var parent: CameraView
        var cameraViewController: AVCameraViewController?

        init(parent: CameraView) {
            self.parent = parent
            super.init()
        }

        func didCaptureImage(_ image: UIImage) {
            print("协调器接收到图像，调用单张回调函数")
            parent.onImageCaptured(image)
        }

        func didCaptureImages(_ images: [DeliveryPhotoType: UIImage]) {
            print("协调器接收到\(images.count)张图像，调用批量回调函数")
            parent.onImagesCaptured(images)
        }

        @objc func fakeCancelTapped() {
            // 仅用于预览模式
        }
    }
}

// MARK: - 自定义相机控制器代理
protocol AVCameraViewControllerDelegate: AnyObject {
    func didCaptureImage(_ image: UIImage)
    func didCaptureImages(_ images: [DeliveryPhotoType: UIImage])
}

// 添加默认实现，确保兼容性
extension AVCameraViewControllerDelegate {
    func didCaptureImages(_ images: [DeliveryPhotoType: UIImage]) {
        // 默认实现：如果只有一张照片，调用单张方法
        if let image = images.values.first {
            didCaptureImage(image)
        }
    }
}

// MARK: - 自定义相机控制器
class AVCameraViewController: UIViewController {
    // 相机会话
    private let captureSession = AVCaptureSession()
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput = AVCapturePhotoOutput()
    private var flashMode: AVCaptureDevice.FlashMode = .off
    private var captureDevice: AVCaptureDevice?

    // 相机UI元素
    private var captureButton: UIButton!
    private var cancelButton: UIButton!
    private var flashButton: UIButton!
    private var photoTypeLabel: UILabel!

    // 新增：已捕获的照片字典
    private var capturedPhotos: [DeliveryPhotoType: UIImage] = [:]

    // 新增：待拍摄的照片类型数组
    private var photoTypesToCapture: [DeliveryPhotoType] = DeliveryPhotoType.allCases

    // 代理和类型
    weak var delegate: AVCameraViewControllerDelegate?
    var photoType: DeliveryPhotoType = .doorNumber {
        didSet {
            if photoTypeLabel != nil {
                photoTypeLabel.text = photoType.title

                // 更新界面以反映当前照片类型
                updateUIForCurrentPhotoType()
            }
        }
    }

    // 连续拍照模式下的剩余照片类型
    var remainingPhotoTypes: [DeliveryPhotoType] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        setupCamera()
        setupUI()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        print("相机视图即将出现")

        // 在后台线程启动相机会话
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 添加超时检查，防止会话启动无响应
            let startTime = Date()

            // 启动会话
            self.captureSession.startRunning()

            // 检查启动是否成功
            if self.captureSession.isRunning {
                let duration = Date().timeIntervalSince(startTime)
                print("相机会话启动成功，耗时：\(duration)秒")
            } else {
                print("警告：相机会话可能未成功启动")

                // 在主线程显示错误
                DispatchQueue.main.async {
                    let alert = UIAlertController(
                        title: "相机启动失败",
                        message: "无法启动相机，请重试",
                        preferredStyle: .alert
                    )
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    self.present(alert, animated: true)
                }
            }
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        print("相机视图即将消失")

        // 停止相机会话
        if captureSession.isRunning {
            print("停止相机会话")

            // 在后台线程停止会话
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                self?.captureSession.stopRunning()
                print("相机会话已停止")
            }
        } else {
            print("相机会话已经停止，无需再次停止")
        }
    }

    override var prefersStatusBarHidden: Bool {
        return true
    }

    // 设置相机
    private func setupCamera() {
        // 打印日志
        print("开始设置相机")

        // 检查当前设备是否支持相机
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            print("错误：当前设备不支持相机")
            return
        }

        // 设置捕获会话品质
        captureSession.sessionPreset = .photo

        // 获取后置摄像头
        guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            print("错误：无法获取后置摄像头")
            return
        }
        captureDevice = device

        // 使用do-catch处理可能的错误
        do {
            // 创建设备输入
            let input = try AVCaptureDeviceInput(device: device)

            // 添加输入到会话
            if captureSession.canAddInput(input) {
                captureSession.addInput(input)
                print("成功添加相机输入到会话")
            } else {
                print("错误：无法添加相机输入到会话")
                return
            }

            // 添加照片输出
            if captureSession.canAddOutput(photoOutput) {
                captureSession.addOutput(photoOutput)
                print("成功添加照片输出到会话")

                // 启用高分辨率图片 - 使用适合iOS 16的API
                if #available(iOS 16.0, *) {
                    // 获取设备支持的最大照片尺寸
                    if let lastDimension = device.activeFormat.supportedMaxPhotoDimensions.last {
                        // 使用设备支持的最大尺寸
                        photoOutput.maxPhotoDimensions = lastDimension
                        print("已设置照片尺寸为：宽度=\(lastDimension.width)，高度=\(lastDimension.height)")
                    } else {
                        print("设备不支持设置最大照片尺寸，使用默认值")
                    }
                } else {
                    // 旧版iOS继续使用已弃用的API
                    photoOutput.isHighResolutionCaptureEnabled = true
                    print("已启用高分辨率照片")
                }

                // 设置照片质量优先级
                photoOutput.maxPhotoQualityPrioritization = .quality
            } else {
                print("错误：无法添加照片输出到会话")
                return
            }

            // 创建预览层
            setupPreviewLayer()
            print("相机设置完成")

        } catch {
            print("相机设置错误: \(error.localizedDescription)")
            // 显示错误提示
            DispatchQueue.main.async {
                let alert = UIAlertController(
                    title: "相机初始化失败",
                    message: "无法访问相机：\(error.localizedDescription)",
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                    // 关闭相机视图
                    self.dismiss(animated: true)
                })
                self.present(alert, animated: true)
            }
        }
    }

    // 设置预览层
    private func setupPreviewLayer() {
        print("设置相机预览层")

        // 创建预览层
        let videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer.videoGravity = .resizeAspectFill
        videoPreviewLayer.frame = view.bounds

        // 添加到视图
        view.layer.insertSublayer(videoPreviewLayer, at: 0)
        self.videoPreviewLayer = videoPreviewLayer

        print("相机预览层设置完成")
    }

    // 设置用户界面
    private func setupUI() {
        // 设置底色
        view.backgroundColor = .black

        // 创建拍照按钮
        setupCaptureButton()

        // 创建闪光灯按钮
        setupFlashButton()

        // 创建取消按钮
        setupCancelButton()

        // 创建照片类型标签
        setupPhotoTypeLabel()
    }

    // 设置拍照按钮
    private func setupCaptureButton() {
        // 增大按钮尺寸以提高可点击区域
        let buttonSize: CGFloat = 100
        let buttonX = (view.bounds.width - buttonSize) / 2

        // 按钮位置调整 - 固定位置而非基于安全区域
        // 固定距离底部120像素，保证在各种设备上位置明确且可见
        let buttonY = view.bounds.height - buttonSize - 120

        // 创建按钮
        captureButton = UIButton(type: .custom)
        captureButton.frame = CGRect(x: buttonX, y: buttonY, width: buttonSize, height: buttonSize)

        // 使用清晰的背景颜色和边框
        captureButton.backgroundColor = .white
        captureButton.layer.cornerRadius = buttonSize / 2
        captureButton.layer.borderWidth = 6
        captureButton.layer.borderColor = UIColor.white.cgColor
        captureButton.layer.shadowColor = UIColor.black.cgColor
        captureButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        captureButton.layer.shadowRadius = 8
        captureButton.layer.shadowOpacity = 0.3

        // 内部圆环
        let innerCircle = UIView(frame: CGRect(
            x: buttonSize * 0.2,
            y: buttonSize * 0.2,
            width: buttonSize * 0.6,
            height: buttonSize * 0.6
        ))
        innerCircle.backgroundColor = .white
        innerCircle.layer.cornerRadius = (buttonSize * 0.6) / 2
        innerCircle.layer.borderWidth = 3
        innerCircle.layer.borderColor = UIColor.gray.cgColor
        innerCircle.isUserInteractionEnabled = false // 禁用内部圆环的用户交互
        captureButton.addSubview(innerCircle)

        // 确保点击区域正确并添加点击事件
        captureButton.isUserInteractionEnabled = true
        captureButton.addTarget(self, action: #selector(capturePhoto), for: .touchUpInside)

        // 调试信息
        print("拍照按钮设置: 大小=\(buttonSize), 位置=(\(buttonX), \(buttonY))")

        // 添加到视图
        view.addSubview(captureButton)
    }

    // 设置闪光灯按钮
    private func setupFlashButton() {
        let buttonSize: CGFloat = 70

        // 固定位置于右下角，与拍照按钮在同一水平线上
        let buttonY = view.bounds.height - buttonSize - 130
        let buttonX = view.bounds.width - buttonSize - 30

        // 根据iOS版本使用适当的按钮配置
        if #available(iOS 15.0, *) {
            // 使用新的UIButtonConfiguration API
            flashButton = UIButton(configuration: .plain())
            flashButton.frame = CGRect(x: buttonX, y: buttonY, width: buttonSize, height: buttonSize)

            // 设置基本样式
            var config = UIButton.Configuration.plain()
            config.contentInsets = NSDirectionalEdgeInsets(top: 15, leading: 15, bottom: 15, trailing: 15)

            // 设置图像和颜色
            let flashOffImage = UIImage(systemName: "bolt.slash.fill")?.withRenderingMode(.alwaysTemplate)
            config.image = flashOffImage
            config.baseForegroundColor = .white

            // 应用配置
            flashButton.configuration = config
        } else {
            // 旧版iOS使用传统方法
            flashButton = UIButton(type: .custom)
            flashButton.frame = CGRect(x: buttonX, y: buttonY, width: buttonSize, height: buttonSize)

            // 设置图标
            let flashOffImage = UIImage(systemName: "bolt.slash.fill")?.withRenderingMode(.alwaysTemplate)
            flashButton.setImage(flashOffImage, for: .normal)
            flashButton.tintColor = .white
            flashButton.imageView?.contentMode = .scaleAspectFit
            flashButton.imageEdgeInsets = UIEdgeInsets(top: 15, left: 15, bottom: 15, right: 15)
        }

        // 共有的样式设置
        flashButton.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        flashButton.layer.cornerRadius = buttonSize / 2
        flashButton.layer.borderWidth = 2
        flashButton.layer.borderColor = UIColor.white.withAlphaComponent(0.5).cgColor
        flashButton.layer.shadowColor = UIColor.black.cgColor
        flashButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        flashButton.layer.shadowRadius = 4
        flashButton.layer.shadowOpacity = 0.3

        // 确保点击区域正确
        flashButton.isUserInteractionEnabled = true
        flashButton.addTarget(self, action: #selector(toggleFlash), for: .touchUpInside)

        // 添加到视图
        view.addSubview(flashButton)
    }

    // 设置关闭按钮
    private func setupCancelButton() {
        let buttonSize: CGFloat = 44
        let padding: CGFloat = 16

        // 创建关闭按钮
        cancelButton = UIButton(type: .system)
        cancelButton.frame = CGRect(
            x: view.bounds.width - buttonSize - padding,
            y: view.safeAreaInsets.top + padding,
            width: buttonSize,
            height: buttonSize
        )

        // 设置按钮样式
        let config = UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        let closeImage = UIImage(systemName: "xmark.circle.fill", withConfiguration: config)
        cancelButton.setImage(closeImage, for: .normal)
        cancelButton.tintColor = .white

        // 添加阴影
        cancelButton.layer.shadowColor = UIColor.black.cgColor
        cancelButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        cancelButton.layer.shadowRadius = 3
        cancelButton.layer.shadowOpacity = 0.5

        // 确保点击区域正确
        cancelButton.isUserInteractionEnabled = true
        cancelButton.addTarget(self, action: #selector(cancelCamera), for: .touchUpInside)

        // 添加到视图
        view.addSubview(cancelButton)
    }

    // MARK: - 相机操作
    @objc private func capturePhoto() {
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 打印调试信息
        print("拍照按钮被点击")

        // 检查相机会话是否在运行
        guard captureSession.isRunning else {
            print("错误：相机会话未运行，无法拍照")
            // 尝试重新启动会话
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                self?.captureSession.startRunning()
                // 延迟一点时间再尝试拍照
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    self?.capturePhoto()
                }
            }
            return
        }

        // 检查输出是否已添加到会话
        guard captureSession.outputs.contains(photoOutput) else {
            print("错误：照片输出未添加到会话")
            return
        }

        // 相机拍照动画效果
        UIView.animate(withDuration: 0.1, animations: {
            self.captureButton.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.captureButton.transform = CGAffineTransform.identity
            }
        }

        // 设置照片设置
        let photoSettings: AVCapturePhotoSettings

        // 使用合适的照片设置
        if photoOutput.availablePhotoCodecTypes.contains(.jpeg) {
            photoSettings = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])
        } else {
            photoSettings = AVCapturePhotoSettings()
        }

        // 检查闪光灯设置是否可用
        photoSettings.flashMode = flashMode

        // 捕获照片
        print("开始捕获照片")
        photoOutput.capturePhoto(with: photoSettings, delegate: self)
    }

    @objc private func toggleFlash() {
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        // 切换闪光灯模式
        flashMode = flashMode == .off ? .on : .off

        // 更新UI
        if #available(iOS 15.0, *) {
            // 使用新的配置API更新按钮外观
            var config = flashButton.configuration ?? UIButton.Configuration.plain()

            if flashMode == .on {
                let flashOnImage = UIImage(systemName: "bolt.fill")?.withRenderingMode(.alwaysTemplate)
                config.image = flashOnImage
                config.baseForegroundColor = .yellow
            } else {
                let flashOffImage = UIImage(systemName: "bolt.slash.fill")?.withRenderingMode(.alwaysTemplate)
                config.image = flashOffImage
                config.baseForegroundColor = .white
            }

            // 应用更新的配置
            flashButton.configuration = config
        } else {
            // 旧版iOS使用传统方法更新按钮外观
            if flashMode == .on {
                let flashOnImage = UIImage(systemName: "bolt.fill")?.withRenderingMode(.alwaysTemplate)
                flashButton.setImage(flashOnImage, for: .normal)
                flashButton.tintColor = .yellow
            } else {
                let flashOffImage = UIImage(systemName: "bolt.slash.fill")?.withRenderingMode(.alwaysTemplate)
                flashButton.setImage(flashOffImage, for: .normal)
                flashButton.tintColor = .white
            }
        }

        // 更新背景色
        flashButton.backgroundColor = UIColor.black.withAlphaComponent(flashMode == .on ? 0.6 : 0.5)

        // 实际控制设备闪光灯
        if let device = captureDevice {
            do {
                try device.lockForConfiguration()

                if device.isTorchModeSupported(.on) && flashMode == .on {
                    try device.setTorchModeOn(level: AVCaptureDevice.maxAvailableTorchLevel)
                } else {
                    device.torchMode = .off
                }

                device.unlockForConfiguration()
            } catch {
                print("无法控制闪光灯: \(error.localizedDescription)")
            }
        }
    }

    @objc private func cancelCamera() {
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 关闭相机
        dismiss(animated: true, completion: nil)
    }

    // 添加一个方法，在视图布局变化时调整按钮位置
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 更新视频预览层的frame
        videoPreviewLayer?.frame = view.bounds

        // 如果按钮已经初始化，更新它们的位置
        if captureButton != nil && flashButton != nil {
            // 拍照按钮位置更新
            let buttonSize: CGFloat = 100
            let buttonX = (view.bounds.width - buttonSize) / 2
            let buttonY = view.bounds.height - buttonSize - 120
            captureButton.frame = CGRect(x: buttonX, y: buttonY, width: buttonSize, height: buttonSize)

            // 闪光灯按钮位置更新
            let flashButtonSize: CGFloat = 70
            let flashButtonY = view.bounds.height - flashButtonSize - 130
            let flashButtonX = view.bounds.width - flashButtonSize - 30
            flashButton.frame = CGRect(x: flashButtonX, y: flashButtonY, width: flashButtonSize, height: flashButtonSize)
        }
    }

    // 设置照片类型标签
    private func setupPhotoTypeLabel() {
        // 调整位置和样式
        let labelHeight: CGFloat = 40
        let labelY: CGFloat = 150

        photoTypeLabel = UILabel(frame: CGRect(x: 20, y: labelY, width: view.bounds.width - 40, height: labelHeight))
        photoTypeLabel.text = photoType.title
        photoTypeLabel.textAlignment = .center
        photoTypeLabel.textColor = .white
        photoTypeLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        photoTypeLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        photoTypeLabel.layer.cornerRadius = 12
        photoTypeLabel.layer.masksToBounds = true
        photoTypeLabel.layer.borderWidth = 1
        photoTypeLabel.layer.borderColor = UIColor.white.withAlphaComponent(0.3).cgColor

        // 添加阴影容器（因为masksToBounds=true时无法直接添加阴影）
        let shadowView = UIView(frame: photoTypeLabel.frame)
        shadowView.backgroundColor = .clear
        shadowView.layer.shadowColor = UIColor.black.cgColor
        shadowView.layer.shadowOffset = CGSize(width: 0, height: 3)
        shadowView.layer.shadowOpacity = 0.3
        shadowView.layer.shadowRadius = 5

        view.addSubview(shadowView)
        view.addSubview(photoTypeLabel)
    }

    // MARK: - 新增方法

    // 新增：显示拍摄成功的提示
    private func showCaptureSuccess(message: String) {
        let alertView = UIView(frame: CGRect(x: 0, y: 0, width: 250, height: 100))
        alertView.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        alertView.layer.cornerRadius = 15

        let iconView = UIImageView(image: UIImage(systemName: "checkmark.circle.fill"))
        iconView.tintColor = .green
        iconView.translatesAutoresizingMaskIntoConstraints = false
        alertView.addSubview(iconView)

        let label = UILabel()
        label.text = message
        label.textColor = .white
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 15)
        label.numberOfLines = 0
        label.translatesAutoresizingMaskIntoConstraints = false
        alertView.addSubview(label)

        NSLayoutConstraint.activate([
            iconView.topAnchor.constraint(equalTo: alertView.topAnchor, constant: 15),
            iconView.centerXAnchor.constraint(equalTo: alertView.centerXAnchor),
            iconView.widthAnchor.constraint(equalToConstant: 30),
            iconView.heightAnchor.constraint(equalToConstant: 30),

            label.topAnchor.constraint(equalTo: iconView.bottomAnchor, constant: 10),
            label.leadingAnchor.constraint(equalTo: alertView.leadingAnchor, constant: 10),
            label.trailingAnchor.constraint(equalTo: alertView.trailingAnchor, constant: -10),
            label.bottomAnchor.constraint(lessThanOrEqualTo: alertView.bottomAnchor, constant: -15)
        ])

        alertView.center = view.center
        alertView.alpha = 0
        view.addSubview(alertView)

        UIView.animate(withDuration: 0.3, animations: {
            alertView.alpha = 1
        }) { _ in
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                UIView.animate(withDuration: 0.3, animations: {
                    alertView.alpha = 0
                }) { _ in
                    alertView.removeFromSuperview()
                }
            }
        }
    }

    // 新增：更新UI以反映当前照片类型
    private func updateUIForCurrentPhotoType() {
        // 更新照片类型标签
        photoTypeLabel.text = photoType.title

        // 如果需要，还可以进一步更新UI元素，如更改按钮颜色等
        let progressText = "进度: \(capturedPhotos.count + 1)/\(DeliveryPhotoType.allCases.count)"

        // 可以考虑添加进度提示
        if let existingLabel = view.viewWithTag(999) as? UILabel {
            existingLabel.text = progressText
        } else {
            let progressLabel = UILabel(frame: CGRect(x: 0, y: 50, width: view.bounds.width, height: 30))
            progressLabel.text = progressText
            progressLabel.textAlignment = .center
            progressLabel.textColor = .white
            progressLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
            progressLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            progressLabel.tag = 999
            view.addSubview(progressLabel)
        }
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension AVCameraViewController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        // 立即检查错误
        if let error = error {
            print("照片捕获错误: \(error.localizedDescription)")

            // 在主线程通知用户
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                // 显示错误提示
                let alert = UIAlertController(
                    title: "拍照失败",
                    message: "无法完成照片处理，请重试",
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "确定", style: .default))
                self.present(alert, animated: true)
            }
            return
        }

        print("照片处理成功，准备转换为图像")

        // 获取图像数据
        guard let imageData = photo.fileDataRepresentation() else {
            print("无法获取照片数据")
            return
        }

        // 将数据转换为图像
        guard let originalImage = UIImage(data: imageData) else {
            print("无法从数据创建图像")
            return
        }

        print("成功创建图像，宽度：\(originalImage.size.width)，高度：\(originalImage.size.height)")

        // 在后台线程处理图像，避免阻塞主线程
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 处理图像以更好地适应卡片显示区域
            let processedImage = self.processImageForDisplay(originalImage)
            print("图像处理完成")

            // 切换回主线程进行UI更新和反馈
            DispatchQueue.main.async {
                // 添加成功反馈
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.success)

                // 保存当前类型的照片
                self.capturedPhotos[self.photoType] = processedImage

                // 判断是否在连续拍摄模式
                if !self.remainingPhotoTypes.isEmpty {
                    print("连续拍摄模式：已拍摄\(self.photoType.title)，剩余\(self.remainingPhotoTypes.count)种类型")

                    // 切换到下一个照片类型
                    let nextType = self.remainingPhotoTypes.removeFirst()

                    // 更新UI以反映成功拍摄
                    self.showCaptureSuccess(message: "已拍摄完成，继续拍摄\(nextType.title)")

                    // 更新当前类型
                    self.photoType = nextType

                    print("照片拍摄成功，切换到下一类型：\(nextType.title)")
                } else if self.capturedPhotos.count >= DeliveryPhotoType.allCases.count {
                    // 所有照片拍摄完毕，回调并关闭视图
                    print("所有照片拍摄完成，总计\(self.capturedPhotos.count)张")
                    self.delegate?.didCaptureImages(self.capturedPhotos)
                    self.dismiss(animated: true)
                } else {
                    // 单张照片模式
                    print("单张拍摄模式：已拍摄\(self.photoType.title)")
                    // 调用代理方法
                    self.delegate?.didCaptureImage(processedImage)
                    self.dismiss(animated: true)
                }
            }
        }
    }

    // 处理图像以更好地适应卡片显示
    private func processImageForDisplay(_ image: UIImage) -> UIImage {
        // 获取原始图像尺寸
        let originalSize = image.size

        print("处理图像：原始尺寸 = \(originalSize)")

        // 卡片显示比例约为3:4 (宽:高)，接近屏幕比例
        let targetAspect: CGFloat = 3.0 / 4.0

        // 计算原始图像比例
        let originalAspect = originalSize.width / originalSize.height

        // 如果原始比例接近目标比例，则不需要处理
        if abs(originalAspect - targetAspect) < 0.1 {
            print("图像比例接近目标比例，无需裁剪")
            return image
        }

        // 安全检查图像是否有效
        guard let cgImage = image.cgImage else {
            print("警告：无法获取CGImage，返回原图")
            return image
        }

        // 计算裁剪区域
        var cropRect: CGRect

        if originalAspect > targetAspect {
            // 原图较宽，需要裁剪两侧
            let newWidth = originalSize.height * targetAspect
            let xOffset = (originalSize.width - newWidth) / 2
            cropRect = CGRect(x: xOffset, y: 0, width: newWidth, height: originalSize.height)
            print("图像较宽，裁剪两侧：\(cropRect)")
        } else {
            // 原图较高，需要裁剪上下
            let newHeight = originalSize.width / targetAspect
            let yOffset = (originalSize.height - newHeight) / 2
            cropRect = CGRect(x: 0, y: yOffset, width: originalSize.width, height: newHeight)
            print("图像较高，裁剪上下：\(cropRect)")
        }

        // 确保裁剪区域有效
        if cropRect.width <= 0 || cropRect.height <= 0 ||
           cropRect.origin.x < 0 || cropRect.origin.y < 0 ||
           cropRect.maxX > originalSize.width || cropRect.maxY > originalSize.height {
            print("警告：裁剪区域无效，返回原图")
            return image
        }

        // 执行裁剪
        if let croppedImage = cgImage.cropping(to: cropRect) {
            let result = UIImage(cgImage: croppedImage, scale: image.scale, orientation: image.imageOrientation)
            print("裁剪成功，新尺寸 = \(result.size)")
            return result
        } else {
            print("裁剪失败，返回原图")
            return image
        }
    }
}

