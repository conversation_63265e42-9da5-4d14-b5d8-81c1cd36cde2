import SwiftUI
import CoreLocation
import Combine

// 地址确认表单
struct AddressConfirmationSheet: View {
    @Environment(\.dismiss) private var dismiss

    let validationResult: AddressValidationResult
    let onConfirm: (Bool, String) -> Void // (useOriginal, finalAddress)

    @State private var selectedOption: AddressOption = .geocoded
    @State private var customAddress: String = ""
    @State private var showCustomInput: Bool = false

    enum AddressOption {
        case original    // 使用原始地址
        case geocoded    // 使用地理编码地址
        case custom      // 自定义地址
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标题区域
                headerSection

                ScrollView {
                    VStack(spacing: 20) {
                        // 警告信息
                        if let warningMessage = validationResult.warningMessage {
                            warningSection(warningMessage)
                        }

                        // 地址比较
                        addressComparisonSection

                        // 选项区域
                        optionsSection

                        // 自定义地址输入
                        if showCustomInput {
                            customAddressSection
                        }

                        // 建议
                        if !validationResult.suggestions.isEmpty {
                            suggestionsSection
                        }
                    }
                    .padding()
                }

                // 底部按钮
                bottomButtonsSection
            }
            .navigationTitle("地址确认")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
        }
    }

    // MARK: - 视图组件

    private var headerSection: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: confidenceIcon)
                    .foregroundColor(confidenceColor)
                    .font(.title2)

                Text("地址验证")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()
            }

            Text(confidenceDescription)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }

    private func warningSection(_ message: String) -> some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.orange)

            Text(message)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
    }

    private var addressComparisonSection: some View {
        VStack(spacing: 12) {
            Text("地址比较")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 原始地址
            addressCard(
                title: "您输入的地址",
                address: validationResult.originalAddress,
                icon: "pencil",
                color: .blue,
                isSelected: selectedOption == .original
            ) {
                selectedOption = .original
                showCustomInput = false
            }

            // 地理编码地址
            if !validationResult.geocodedAddress.isEmpty {
                addressCard(
                    title: "系统找到的地址",
                    address: validationResult.geocodedAddress,
                    icon: "location.fill",
                    color: .green,
                    isSelected: selectedOption == .geocoded
                ) {
                    selectedOption = .geocoded
                    showCustomInput = false
                }
            }
        }
    }

    private func addressCard(
        title: String,
        address: String,
        icon: String,
        color: Color,
        isSelected: Bool,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: icon)
                            .foregroundColor(color)

                        Text(title)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)

                        Spacer()

                        if isSelected {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }

                    Text(address)
                        .font(.body)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                Spacer()
            }
            .padding()
            .background(isSelected ? Color.blue.opacity(0.1) : Color(.secondarySystemGroupedBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var optionsSection: some View {
        VStack(spacing: 12) {
            // 自定义地址选项
            Button {
                selectedOption = .custom
                showCustomInput = true
                if customAddress.isEmpty {
                    customAddress = validationResult.originalAddress
                }
            } label: {
                HStack {
                    Image(systemName: "square.and.pencil")
                        .foregroundColor(.purple)

                    Text("手动输入地址")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Spacer()

                    if selectedOption == .custom {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.blue)
                    }
                }
                .padding()
                .background(selectedOption == .custom ? Color.blue.opacity(0.1) : Color(.secondarySystemGroupedBackground))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(selectedOption == .custom ? Color.blue : Color.clear, lineWidth: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    private var customAddressSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("自定义地址")
                .font(.headline)

            TextField("输入完整地址", text: $customAddress)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .autocapitalization(.words)

            Text("请输入完整准确的地址，包括门牌号、街道名、城市和邮编")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    private var suggestionsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("建议")
                .font(.headline)

            ForEach(validationResult.suggestions, id: \.self) { suggestion in
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)
                        .font(.caption)

                    Text(suggestion)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }
        }
        .padding()
        .background(Color.yellow.opacity(0.1))
        .cornerRadius(8)
    }

    private var bottomButtonsSection: some View {
        VStack(spacing: 12) {
            Divider()

            HStack(spacing: 16) {
                Button("取消") {
                    dismiss()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray5))
                .foregroundColor(.primary)
                .cornerRadius(8)

                Button("确认使用") {
                    let finalAddress: String
                    let useOriginal: Bool

                    switch selectedOption {
                    case .original:
                        finalAddress = validationResult.originalAddress
                        useOriginal = true
                    case .geocoded:
                        finalAddress = validationResult.geocodedAddress
                        useOriginal = false
                    case .custom:
                        finalAddress = customAddress.trimmingCharacters(in: .whitespacesAndNewlines)
                        useOriginal = false
                    }

                    onConfirm(useOriginal, finalAddress)
                    dismiss()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
                .disabled(selectedOption == .custom && customAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            .padding()
        }
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - 计算属性

    private var confidenceIcon: String {
        switch validationResult.confidence {
        case .high:
            return "checkmark.circle.fill"
        case .medium:
            return "exclamationmark.triangle.fill"
        case .low:
            return "xmark.circle.fill"
        case .veryLow:
            return "questionmark.circle.fill"
        }
    }

    private var confidenceColor: Color {
        switch validationResult.confidence {
        case .high:
            return .green
        case .medium:
            return .orange
        case .low:
            return .red
        case .veryLow:
            return .gray
        }
    }

    private var confidenceDescription: String {
        switch validationResult.confidence {
        case .high:
            return "地址验证通过，可以安全使用"
        case .medium:
            return "地址可能有轻微修正，请确认"
        case .low:
            return "地址有重大修正，请仔细检查"
        case .veryLow:
            return "地址可能不正确，建议重新输入"
        }
    }
}

// MARK: - 预览
struct AddressConfirmationSheet_Previews: PreviewProvider {
    static var previews: some View {
        AddressConfirmationSheet(
            validationResult: AddressValidationResult(
                originalAddress: "84 Kin, Gen Waverley, VIC 3150",
                geocodedAddress: "84 Kim Cl, Wheelers Hill, VIC, 3150, Australia",
                coordinate: CLLocationCoordinate2D(latitude: -37.9164826, longitude: 145.1976709),
                isModified: true,
                confidence: .low,
                modificationType: .streetName,
                suggestions: ["请确认街道名是否正确：Kim Cl", "请确认城市/郊区是否正确：Wheelers Hill"],
                warningMessage: "街道名被修正：'Kin' → 'Kim Cl'"
            )
        ) { (useOriginal: Bool, finalAddress: String) in
            let choice = useOriginal ? "原始" : "修正"
            print("选择: \(choice), 地址: \(finalAddress)")
        }
    }
}
