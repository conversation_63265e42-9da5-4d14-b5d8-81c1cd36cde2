import SwiftUI

/// 步骤进度指示器视图
/// 用于显示多步骤流程的当前进度
struct StepProgressView: View {
    // 当前步骤（从0开始）
    let currentStep: Int

    // 总步骤数
    let totalSteps: Int

    // 步骤标题
    let stepTitles: [String]

    // 步骤图标名称
    let stepIcons: [String]

    // 自定义颜色
    var activeColor: Color = .blue
    var inactiveColor: Color = Color(.systemGray5)
    var completedColor: Color = .green

    // 初始化方法
    init(currentStep: Int, stepTitles: [String], stepIcons: [String], activeColor: Color = .blue, inactiveColor: Color = Color(.systemGray5), completedColor: Color = .green) {
        self.currentStep = currentStep
        self.totalSteps = stepTitles.count
        self.stepTitles = stepTitles
        self.stepIcons = stepIcons
        self.activeColor = activeColor
        self.inactiveColor = inactiveColor
        self.completedColor = completedColor
    }

    var body: some View {
        VStack(spacing: 8) {
            // 步骤指示器
            HStack(spacing: 0) {
                ForEach(0..<totalSteps, id: \.self) { step in
                    stepView(for: step)

                    // 添加连接线（除了最后一个步骤）
                    if step < totalSteps - 1 {
                        connectorLine(for: step)
                    }
                }
            }
            .padding(.horizontal, 16)

            // 步骤标题
            HStack(spacing: 0) {
                ForEach(0..<totalSteps, id: \.self) { step in
                    Text(stepTitles[step])
                        .font(.caption)
                        .foregroundColor(stepColor(for: step))
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: .infinity)
                        .padding(.horizontal, 4)
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }

    // 单个步骤视图
    private func stepView(for step: Int) -> some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(stepColor(for: step))
                .frame(width: 36, height: 36)

            // 步骤数字或完成图标
            if step < currentStep {
                // 已完成步骤显示对勾
                Image(systemName: "checkmark")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            } else {
                // 当前或未完成步骤显示图标
                Image(systemName: stepIcons[step])
                    .font(.system(size: 14))
                    .foregroundColor(step == currentStep ? .white : .gray)
            }
        }
        .frame(maxWidth: .infinity)
        .overlay(
            // 添加脉动动画效果（仅当前步骤）
            step == currentStep ?
            Circle()
                .stroke(activeColor, lineWidth: 2)
                .scaleEffect(1.1)
                .opacity(0.7)
                .frame(width: 36, height: 36)
            : nil
        )
    }

    // 连接线
    private func connectorLine(for step: Int) -> some View {
        Group {
            if step < currentStep {
                // 如果当前步骤大于此步骤，则连接线为已完成颜色
                Rectangle()
                    .fill(completedColor)
            } else if step == currentStep {
                // 如果当前步骤等于此步骤，则连接线为渐变色
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [completedColor, inactiveColor]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            } else {
                // 否则为未完成颜色
                Rectangle()
                    .fill(inactiveColor)
            }
        }
        .frame(height: 2)
        .frame(maxWidth: .infinity)
    }

    // 步骤颜色
    private func stepColor(for step: Int) -> Color {
        if step < currentStep {
            return completedColor // 已完成
        } else if step == currentStep {
            return activeColor // 当前步骤
        } else {
            return inactiveColor // 未完成
        }
    }
}

// 预览
#Preview("StepProgressView") {
    VStack(spacing: 20) {
        StepProgressView(
            currentStep: 0,
            stepTitles: ["门牌号照片", "包裹标签照片", "放置位置照片"],
            stepIcons: ["door.left.hand.open", "tag", "cube.box"]
        )

        StepProgressView(
            currentStep: 1,
            stepTitles: ["门牌号照片", "包裹标签照片", "放置位置照片"],
            stepIcons: ["door.left.hand.open", "tag", "cube.box"]
        )

        StepProgressView(
            currentStep: 2,
            stepTitles: ["门牌号照片", "包裹标签照片", "放置位置照片"],
            stepIcons: ["door.left.hand.open", "tag", "cube.box"]
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
