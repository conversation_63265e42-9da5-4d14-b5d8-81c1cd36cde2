import SwiftUI
import MapKit
import UIKit

/// 实景图预览视图
/// 使用 UIViewControllerRepresentable 包装 MKLookAroundViewController
@available(iOS 16.0, *)
struct LookAroundPreviewView: UIViewControllerRepresentable {
    let scene: MKLookAroundScene

    func makeUIViewController(context: Context) -> MKLookAroundViewController {
        print("[DEBUG] LookAroundPreviewView - 创建MKLookAroundViewController")
        print("[DEBUG] LookAroundPreviewView - 场景信息: \(scene)")

        let controller = MKLookAroundViewController(scene: scene)

        // 配置控制器
        controller.showsRoadLabels = false

        // 添加更多配置以防止意外的UI状态变化
        controller.modalPresentationStyle = .none
        controller.modalTransitionStyle = .crossDissolve

        // 设置coordinator
        context.coordinator.controller = controller

        print("[DEBUG] LookAroundPreviewView - MKLookAroundViewController配置完成")
        print("[DEBUG] LookAroundPreviewView - Controller: \(controller)")

        return controller
    }

    func updateUIViewController(_ uiViewController: MKLookAroundViewController, context: Context) {
        print("[DEBUG] LookAroundPreviewView - 更新MKLookAroundViewController场景")

        // 只有当场景真的不同时才更新
        if uiViewController.scene != scene {
            uiViewController.scene = scene
            print("[DEBUG] LookAroundPreviewView - 场景已更新")
        } else {
            print("[DEBUG] LookAroundPreviewView - 场景相同，跳过更新")
        }
    }

    func makeCoordinator() -> Coordinator {
        print("[DEBUG] LookAroundPreviewView - 创建Coordinator")
        return Coordinator()
    }

    class Coordinator: NSObject {
        weak var controller: MKLookAroundViewController?

        override init() {
            super.init()
            print("[DEBUG] LookAroundPreviewView.Coordinator - 初始化")
        }

        deinit {
            print("[DEBUG] LookAroundPreviewView.Coordinator - 销毁")
        }
    }

    // 实现 UIViewControllerRepresentable 协议所需的类型别名
    typealias UIViewControllerType = MKLookAroundViewController
}

// 移除不再需要的 LookAroundFullScreenView

/// 向下兼容的实景图预览视图
/// 在 iOS 16 以下版本使用静态图片代替
struct LookAroundPreview: View {
    let initialScene: MKLookAroundScene

    var body: some View {
        if #available(iOS 16.0, *) {
            LookAroundPreviewView(scene: initialScene)
        } else {
            // 在不支持 MKLookAroundViewController 的系统版本上显示替代视图
            VStack {
                Text("实景图在 iOS 16 及以上版本可用")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .frame(maxWidth: .infinity)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
}

#Preview("LookAroundPreviewView") {
    // 由于 MKLookAroundScene 无法在预览中创建，这里只展示一个占位视图
    Text("实景图预览")
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(Color.gray.opacity(0.2))
        .cornerRadius(8)
}
