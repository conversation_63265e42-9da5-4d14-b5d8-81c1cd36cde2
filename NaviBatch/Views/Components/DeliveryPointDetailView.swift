import SwiftUI
import MapKit
import SwiftData

/// 配送点详情视图
struct DeliveryPointDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext

    @Bindable var point: DeliveryPoint
    @State private var selectedStatus: DeliveryStatus
    @State private var notes: String
    @State private var showingMapPreview: Bool = false
    @State private var showingDeleteConfirmation: Bool = false
    @State private var showingPhotoViewer: Bool = false
    @State private var hasPhotos: Bool = false

    init(point: DeliveryPoint) {
        self.point = point
        self._selectedStatus = State(initialValue: point.deliveryStatus)
        self._notes = State(initialValue: point.notes ?? "")
    }

    var body: some View {
        NavigationStack {
            Form {
                // 地址信息
                Section("address_information".localized) {
                    VStack(alignment: .leading, spacing: 8) {
                        // 排序编号和地址 - 优化单位信息显示
                        VStack(alignment: .leading, spacing: 4) {
                            HStack(spacing: 8) {
                                // 排序编号 - 如果是第三方快递且有排序号，显示第三方排序号
                                Text(getDisplayText())
                                    .font(.headline)
                                    .foregroundColor(point.isThirdPartyWithSort ? .green : .blue)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background((point.isThirdPartyWithSort ? Color.green : Color.blue).opacity(0.1))
                                    .cornerRadius(4)

                                // 如果有单位号，优先显示并突出
                                if point.hasUnitNumber, let unitNumber = point.unitNumber {
                                    Text(unitNumber)
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 2)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(4)
                                }
                            }

                            // 地址（不包含单位号，因为已经单独显示）
                            Text(formatMainAddress(point.primaryAddress, hasUnit: point.hasUnitNumber))
                                .font(.headline)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)

                        // 包裹信息已移除

                        if let groupNumber = point.assignedGroupNumber {
                            HStack {
                                Label("group_belonging".localized, systemImage: "rectangle.stack")
                                Spacer()
                                Text("G\(groupNumber)")
                                    .foregroundColor(.secondary)
                            }
                        }

                        Button(action: {
                            showingMapPreview = true
                        }) {
                            Label("view_map".localized, systemImage: "map")
                        }
                    }
                }

                // 新增：照片预览和查看入口
                Section("delivery_photos".localized) {
                    if hasPhotos {
                        Button(action: {
                            showingPhotoViewer = true
                        }) {
                            HStack {
                                Label("view_delivery_photos".localized, systemImage: "photo.on.rectangle")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    } else {
                        HStack {
                            Label("no_photos_taken".localized, systemImage: "photo.on.rectangle.angled")
                                .foregroundColor(.secondary)
                            Spacer()
                            Button(action: {
                                // 打开拍照界面的逻辑（如果需要）
                            }) {
                                Text("take_photos".localized)
                            }
                            .disabled(point.deliveryStatus == .completed)
                        }
                    }
                }

                // 配送状态
                Section("delivery_status".localized) {
                    DeliveryStatusSelector(selectedStatus: $selectedStatus) { newStatus in
                        updateStatus(newStatus)
                    }

                    // 更新时间显示已移除

                    // 配送状态
                    HStack {
                        Text("delivery_status".localized)
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Spacer()

                        // 显示状态图标和文本
                        HStack(spacing: 5) {
                            Image(systemName: point.deliveryStatus.iconName)
                                .foregroundColor(point.deliveryStatus.color)

                            Text(point.deliveryStatus.localizedName)
                                .foregroundColor(point.deliveryStatus.color)
                        }
                        .padding(5)
                        .background(point.deliveryStatus.color.opacity(0.1))
                        .cornerRadius(5)
                    }

                    // 如果是失败状态，显示失败原因
                    if point.deliveryStatus == .failed, let failureReason = point.deliveryFailureReasonEnum {
                        HStack {
                            Text("failure_reason".localized)
                                .font(.headline)
                                .foregroundColor(.secondary)

                            Spacer()

                            Text(failureReason.localizedName)
                                .foregroundColor(.red)
                        }
                    }
                }

                // 备注
                Section("notes".localized) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                        .onChange(of: notes) { _, newValue in
                            point.notes = newValue.isEmpty ? nil : newValue
                        }
                }

                // 危险操作
                Section {
                    Button(role: .destructive, action: {
                        showingDeleteConfirmation = true
                    }) {
                        Label("delete_delivery_point".localized, systemImage: "trash")
                    }
                }
            }
            .navigationTitle("delivery_point_details".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("done".localized) {
                        saveChanges()
                        dismiss()
                    }
                }

                ToolbarItem(placement: .topBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingMapPreview) {
                MapLocationPreview(coordinate: point.coordinate)
                    .edgesIgnoringSafeArea(.all)
            }
            .sheet(isPresented: $showingPhotoViewer) {
                NavigationStack {
                    DeliveryPhotoViewer(deliveryPoint: point)
                }
            }
            .alert("confirm_deletion".localized, isPresented: $showingDeleteConfirmation) {
                Button("cancel".localized, role: .cancel) { }
                Button("delete".localized, role: .destructive) {
                    deletePoint()
                    dismiss()
                }
            } message: {
                Text("delete_delivery_point_confirmation".localized)
            }
            .onAppear {
                checkForPhotos()
            }
        }
    }

    // 获取显示文本
    private func getDisplayText() -> String {
        if point.isThirdPartyWithSort, let thirdPartySort = point.thirdPartySortNumber {
            return "#\(thirdPartySort)"
        } else {
            return "#\(point.sort_number)"
        }
    }

    private func checkForPhotos() {
        Task {
            let hasAnyPhoto =
                (point.doorPhotoPath != nil) ||
                (point.packagePhotoPath != nil) ||
                (point.placementPhotoPath != nil)

            await MainActor.run {
                self.hasPhotos = hasAnyPhoto
            }
        }
    }

    private func updateStatus(_ newStatus: DeliveryStatus) {
        point.updateStatus(newStatus)
    }

    private func saveChanges() {
        point.updateStatus(selectedStatus)

        point.notes = notes.isEmpty ? nil : notes

        do {
            try modelContext.save()
            Logger.info("成功保存配送点更改，ID: \(point.id)", type: .data)
        } catch {
            Logger.error("保存配送点更改失败: \(error.localizedDescription)", type: .data)
        }
    }

    private func deletePoint() {
        modelContext.delete(point)

        do {
            try modelContext.save()
            Logger.info("成功删除配送点，ID: \(point.id)", type: .data)
        } catch {
            Logger.error("删除配送点失败: \(error.localizedDescription)", type: .data)
        }
    }

    /// 格式化主要地址显示，确保单位信息正确处理
    private func formatMainAddress(_ fullAddress: String, hasUnit: Bool) -> String {
        let components = fullAddress.components(separatedBy: ",")

        if hasUnit && !components.isEmpty {
            // 如果有单位号，第一个组件通常包含"单位号, 街道地址"
            // 我们需要移除单位号部分，只显示街道地址
            let firstComponent = components[0].trimmingCharacters(in: .whitespaces)

            // 查找第一个逗号后的内容作为街道地址
            if let commaIndex = firstComponent.firstIndex(of: ",") {
                let streetAddress = String(firstComponent[firstComponent.index(after: commaIndex)...])
                    .trimmingCharacters(in: .whitespaces)
                return streetAddress.isEmpty ? firstComponent : streetAddress
            }
        }

        // 默认返回第一个组件或完整地址
        return components.first?.trimmingCharacters(in: .whitespaces) ?? fullAddress
    }
}

struct DeliveryPointDetailPreview: View {
    var body: some View {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try! ModelContainer(for: DeliveryPoint.self, configurations: config)

        let samplePoint = DeliveryPoint(
            sort_number: 1,
            streetName: "Address 1",
            coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642)
        )
        samplePoint.packageCount = 3
        samplePoint.assignedGroupNumber = 2
        samplePoint.notes = "客户要求在下午3点前送达"

        container.mainContext.insert(samplePoint)

        return DeliveryPointDetailView(point: samplePoint)
            .modelContainer(container)
    }
}

#Preview("DeliveryPointDetailView") {
    DeliveryPointDetailPreview()
}
