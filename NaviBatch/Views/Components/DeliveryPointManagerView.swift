import SwiftUI
import CoreLocation
import MapKit
import SwiftData
import Combine
import Photos
import UIKit

/// 配送点管理视图
/// 用于管理已优化的配送点信息，包括地址信息、包裹信息、车辆位置、配送状态等
struct DeliveryPointManagerView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext

    // 绑定的配送点
    @Bindable var deliveryPoint: DeliveryPoint

    // 地址信息
    @State private var isAddressEditable: Bool = false
    @State private var editedAddress: String
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var selectedCoordinate: CLLocationCoordinate2D?

    // 包裹信息
    @State private var packageCount: Int
    @State private var selectedPackageSize: PackageSize?
    @State private var selectedPackageType: PackageType?
    @State private var selectedPriority: DeliveryPriority

    // 车辆位置信息
    @State private var selectedVehiclePositions: Set<VehiclePosition> = []

    // 用于存储Combine订阅
    @State private var cancellables = Set<AnyCancellable>()

    // 配送信息
    @State private var selectedDeliveryType: DeliveryType
    @State private var selectedDeliveryStatus: DeliveryStatus
    @State private var trackingNumber: String = ""
    @State private var thirdPartySortNumber: String = ""
    @State private var arrivalTime: String = "随时"
    @State private var timeAtStop: Int = 1

    // 界面状态
    @State private var activeTab: Int = 0
    @State private var showingCamera: Bool = false
    @State private var showingPhotoViewer: Bool = false
    @State private var initialPhotoType: DeliveryPhotoType = .doorNumber
    @State private var showingStatusSheet: Bool = false
    @State private var selectedFailureReason: DeliveryFailureReason? = nil

    // 第三方号码编辑状态
    @State private var showingThirdPartyEditSheet: Bool = false
    @State private var editingThirdPartyNumber: String = ""
    @State private var showingDuplicateAlert: Bool = false
    @State private var duplicateConfirmationMessage: String = ""

    // 初始化方法
    init(deliveryPoint: DeliveryPoint) {
        print("[INFO] DeliveryPointManagerView - 初始化，地址=\(deliveryPoint.primaryAddress), ID=\(deliveryPoint.id)")
        self.deliveryPoint = deliveryPoint

        // 初始化地址信息 - 现在primaryAddress已统一使用国家代码
        self._editedAddress = State(initialValue: deliveryPoint.primaryAddress)

        // 初始化包裹信息
        self._packageCount = State(initialValue: deliveryPoint.packageCount)
        self._selectedPackageSize = State(initialValue: deliveryPoint.packageSizeEnum)
        self._selectedPackageType = State(initialValue: deliveryPoint.packageTypeEnum)
        self._selectedPriority = State(initialValue: deliveryPoint.priorityEnum)

        // 初始化车辆位置
        var positions: Set<VehiclePosition> = []
        for position in deliveryPoint.vehiclePositions {
            positions.insert(position)
        }
        self._selectedVehiclePositions = State(initialValue: positions)

        // 初始化配送信息
        self._selectedDeliveryType = State(initialValue: deliveryPoint.deliveryTypeEnum)
        self._selectedDeliveryStatus = State(initialValue: deliveryPoint.deliveryStatus)
        self._trackingNumber = State(initialValue: deliveryPoint.trackingNumber ?? "")
        self._thirdPartySortNumber = State(initialValue: deliveryPoint.thirdPartySortNumber ?? "")
        self._arrivalTime = State(initialValue: deliveryPoint.arrivalTime ?? "随时")
        self._timeAtStop = State(initialValue: deliveryPoint.timeAtStop ?? 1)

        print("[INFO] DeliveryPointManagerView - 初始化完成")
    }

    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            ZStack {
                Text("delivery_point_management".localized)
                    .font(.headline)

                HStack {
                    Button("cancel".localized) {
                        dismiss()
                    }
                    .foregroundColor(.red)

                    Spacer()

                    Button("save".localized) {
                        saveChanges()
                        dismiss()
                    }
                    .foregroundColor(.blue)
                    .fontWeight(.bold)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .overlay(
                Divider(),
                alignment: .bottom
            )

            // 统一滚动视图，合并所有部分
            ScrollView {
                VStack(spacing: 3) {
                    // 地址信息部分
                    VStack(alignment: .leading, spacing: 16) {
                        // 标题
                        HStack {
                            Image(systemName: "mappin.circle.fill")
                                .foregroundColor(.blue)
                            Text("address_info".localized)
                                .font(.headline)
                        }

                        // 地址信息内容
                        addressInfoSection
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 16)

//                    Divider()

                    // 包裹信息部分 - 简化版（包含标题）
                    simplifiedPackageInfoSection
                        .padding(.horizontal, 16)
                        .padding(.vertical, 16)

//                    Divider()

                    // 配送状态部分
                    simplifiedDeliveryInfoSection
                        .padding(.horizontal, 16)
                        .padding(.vertical, 16)

                    Divider()

                    // 车辆位置信息部分
                    vehiclePositionSection
                        .padding(.horizontal, 16)
                        .padding(.vertical, 16)

                    // 照片记录部分
                    VStack(alignment: .leading, spacing: 16) {
                        // 标题
                        HStack {
                            Image(systemName: "camera.fill")
                                .foregroundColor(.blue)
                            Text("photo_record".localized)
                                .font(.headline)
                        }
                        .padding(.bottom, 4)

                        // 照片记录内容
                        photoRecordSection
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 16)
                    .padding(.bottom, 100) // 额外下边距，确保底部内容完全可见
                }
            }
            .background(Color(.systemBackground))

            // 底部操作按钮
            HStack(spacing: 0) {
                // 导航按钮
                Button(action: openInMaps) {
                    VStack(spacing: 4) {
                        Image(systemName: "location.fill")
                            .font(.system(size: 24))
                        Text("navigation".localized)
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .foregroundColor(.blue)
                }

                // 拍照记录按钮
                Button(action: { showingCamera = true }) {
                    VStack(spacing: 4) {
                        Image(systemName: "camera.fill")
                            .font(.system(size: 24))
                        Text("take_photo_record".localized)
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .foregroundColor(.green)
                }

                // 更新状态按钮
                Button(action: updateDeliveryStatus) {
                    VStack(spacing: 4) {
                        Image(systemName: "arrow.triangle.2.circlepath")
                            .font(.system(size: 24))
                        Text("update_status".localized)
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .foregroundColor(.orange)
                }
            }
            .padding(.vertical, 8)
            .background(Color(.systemBackground))
            .overlay(
                Divider(),
                alignment: .top
            )
            .sheet(isPresented: $showingCamera) {
                DeliveryPhotoCapture(deliveryPoint: deliveryPoint)
            }
            .sheet(isPresented: $showingPhotoViewer) {
                NavigationStack {
                    DeliveryPhotoViewer(
                        deliveryPoint: deliveryPoint,
                        initialPhotoType: initialPhotoType
                    )
                }
            }
            .sheet(isPresented: $showingStatusSheet) {
                StatusUpdateSheet(
                    currentStatus: $selectedDeliveryStatus,
                    selectedFailureReason: $selectedFailureReason,
                    deliveryPoint: deliveryPoint,
                    onStatusSelected: { newStatus, failureReason, customReason in
                        updateDeliveryStatusWithReason(newStatus, failureReason: failureReason, customReason: customReason)
                    }
                )
            }
            .sheet(isPresented: $showingThirdPartyEditSheet) {
                ThirdPartyNumberEditSheet(
                    currentNumber: editingThirdPartyNumber,
                    appName: deliveryPoint.sourceApp.displayName,
                    onSave: { newNumber in
                        saveThirdPartyNumber(newNumber)
                    }
                )
            }
            .alert("duplicate_third_party_number".localized, isPresented: $showingDuplicateAlert) {
                Button("cancel".localized, role: .cancel) { }
                Button("confirm_save".localized) {
                    // 强制保存，即使重复
                    deliveryPoint.thirdPartySortNumber = editingThirdPartyNumber
                    try? modelContext.save()
                }
            } message: {
                Text(duplicateConfirmationMessage)
            }
            .onDisappear {
                // 取消所有订阅
                cancellables.removeAll()
            }
        }
        .background(Color(.systemBackground)) // 为整个视图添加背景色
        .onAppear {
            print("[INFO] DeliveryPointManagerView - onAppear，地址=\(deliveryPoint.primaryAddress), ID=\(deliveryPoint.id)")
        }
    }

    // MARK: - 地址信息区
    private var addressInfoSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            // 地址编号和编辑按钮
            HStack {
                // 显示编号（去掉#号）
                Text("\(deliveryPoint.sorted_number)")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 28, height: 28)
                    .background(Circle().fill(Color.blue))

                // 第三方应用标签 - 可点击编辑
                if deliveryPoint.sourceApp != .manual && deliveryPoint.sourceApp != .justPhoto {
                    if let thirdPartySortNumber = deliveryPoint.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                        Button(action: {
                            editingThirdPartyNumber = thirdPartySortNumber
                            showingThirdPartyEditSheet = true
                        }) {
                            Text("third_party_sort_label".localized(with: deliveryPoint.sourceApp.displayName, thirdPartySortNumber))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.blue)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(4)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    // 🎯 移除UNIUNI特殊显示逻辑 - 统一使用thirdPartySortNumber判断
                }

                Spacer()

                Button(action: { isAddressEditable.toggle() }) {
                    Label(isAddressEditable ? "done".localized : "edit_address_button".localized, systemImage: isAddressEditable ? "checkmark.circle" : "pencil")
                        .font(.subheadline)
                }
                .foregroundColor(isAddressEditable ? .green : .blue)
            }

            // 地址信息
            if isAddressEditable {
                // 使用增强版地址自动完成组件 - 使用全球搜索区域
                EnhancedAddressAutocomplete(
                    searchText: $editedAddress,
                    selectedCoordinate: $selectedCoordinate,
                    initialRegion: MKCoordinateRegion(
                        center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                        span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
                    ),
                    onAddressSelected: { address, coordinate in
                        // 🎯 修复：确保只更新现有的DeliveryPoint，不创建新的
                        // 🛡️ 防hang：使用异步任务而不是DispatchQueue，避免主线程阻塞
                        Task { @MainActor in
                            print("🔧 DeliveryPointManagerView - 开始更新地址: \(address)")
                            print("🔧 DeliveryPointManagerView - 当前DeliveryPoint ID: \(self.deliveryPoint.id)")

                            // 使用完整地址
                            self.editedAddress = address
                            self.selectedCoordinate = coordinate

                            // 🎯 关键修复：先更新坐标，然后通过结构化地址处理来正确设置各个字段
                            self.deliveryPoint.latitude = coordinate.latitude
                            self.deliveryPoint.longitude = coordinate.longitude

                            // 🔧 修复：更新 originalAddress 字段，确保界面显示新地址
                            self.deliveryPoint.originalAddress = address
                            print("🔧 DeliveryPointManagerView - 已更新 originalAddress: \(address)")

                            // 🎯 清除验证错误和警告
                            self.deliveryPoint.geocodingWarning = nil
                            self.deliveryPoint.addressValidationScore = 100.0
                            self.deliveryPoint.addressValidationIssues = nil

                            // 验证坐标
                            _ = self.deliveryPoint.validateCoordinates()

                            // 如果有用户位置，基于用户位置验证
                            if let userLocation = CLLocationManager().location?.coordinate {
                                self.deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                            }

                            // 🎯 使用全球地址处理器获取结构化地址信息
                            Task {
                                let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(address)

                                await MainActor.run {
                                    switch globalResult {
                                    case .success(_, _, _, let placemark, _, _):
                                        // 使用 placemark 填充结构化地址字段
                                        self.deliveryPoint.populateStructuredAddress(from: placemark)
                                        print("🌍 DeliveryPointManagerView - 已填充结构化地址")
                                    case .failed(_, _):
                                        print("⚠️ DeliveryPointManagerView - 地址处理失败，保持基本地址")
                                        break // 保持原地址
                                    }

                                    // 🎯 立即保存到数据库，确保更新而不是创建新记录
                                    do {
                                        try self.modelContext.save()
                                        print("✅ DeliveryPointManagerView - 地址更新成功保存到数据库")

                                        // 通知RouteViewModel检查优化按钮可用性
                                        RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()

                                        // 自动退出编辑模式
                                        self.isAddressEditable = false
                                    } catch {
                                        print("❌ DeliveryPointManagerView - 保存地址更新失败: \(error.localizedDescription)")
                                    }
                                }
                            }

                            print("✅ DeliveryPointManagerView - 已更新地址: \(self.editedAddress)")
                        }
                    }
                )
                .padding(.vertical, 4)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    Text(deliveryPoint.primaryAddress)
                        .font(.body)
                        .fixedSize(horizontal: true, vertical: false)
                }
                .frame(height: 20) // 设置一个固定高度
            }

            Divider()

            // 坐标信息
            HStack {
                Image(systemName: "location")
                    .foregroundColor(.blue)
                    .font(.system(size: 16))

                Text("coordinates".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("(\(String(format: "%.6f", deliveryPoint.latitude)), \(String(format: "%.6f", deliveryPoint.longitude)))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // 地址验证状态 - 不显示unknown状态
            let validationStatus = LocationValidationStatus(rawValue: deliveryPoint.locationValidationStatus) ?? .unknown
            if validationStatus != .unknown && validationStatus != .valid {
                HStack {
                    Image(systemName: validationStatus.iconName)
                        .foregroundColor(validationStatus.color)
                        .font(.system(size: 16))

                    Text(validationStatus.localizedName)
                        .font(.subheadline)
                        .foregroundColor(validationStatus.color)
                }
            }

            // 地理编码警告（如果有）
            if let warning = deliveryPoint.geocodingWarning, !warning.isEmpty {
                HStack {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.orange)
                        .font(.system(size: 16))

                    Text(warning)
                        .font(.subheadline)
                        .foregroundColor(.orange)
                        .lineLimit(2)
                }
            }

            Divider()

            // 门禁密码 - 单行布局
            HStack(spacing: 8) {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                    .font(.system(size: 16))

                Text("access_instructions".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                TextField("access code", text: Binding(
                    get: { deliveryPoint.accessInstructions ?? "" },
                    set: { deliveryPoint.accessInstructions = $0.isEmpty ? nil : $0 }
                ))
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .frame(maxWidth: 150)
                .multilineTextAlignment(.trailing)
            }
        }
        .background(Color(.systemBackground))
        .cornerRadius(10)

    }

    // MARK: - 包裹信息区
    private var packageInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 包裹数量 - 与标题在同一行
            HStack {
                // 标题
                HStack {
                    Image(systemName: "cube.box")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))
                    Text("package_count".localized)
                        .font(.headline)
                }

                Spacer()

                // 包裹数量控件
                HStack {
                    Button(action: { if packageCount > 1 { packageCount -= 1 } }) {
                        Image(systemName: "minus")
                            .foregroundColor(.blue)
                            .frame(width: 40, height: 40)
                            .background(Color(.systemGray6))
                            .clipShape(Circle())
                    }
                    .disabled(packageCount <= 1)

                    Text("\(packageCount)")
                        .font(.title2)
                        .fontWeight(.medium)
                        .frame(width: 60)
                        .multilineTextAlignment(.center)

                    Button(action: { packageCount += 1 }) {
                        Image(systemName: "plus")
                            .foregroundColor(.blue)
                            .frame(width: 40, height: 40)
                            .background(Color(.systemGray6))
                            .clipShape(Circle())
                    }
                }
            }
//            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)

            // 包裹大小
            VStack(alignment: .leading) {
                HStack {
                    Image(systemName: "ruler")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))

                    Text("package_size".localized)
                        .font(.headline)
                }
                .padding(.bottom, 8)

                Picker("package_size".localized, selection: $selectedPackageSize) {
                    ForEach(PackageSize.allCases, id: \.self) { size in
                        Text(size.localizedName).tag(Optional(size))
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)

            // 包裹类型
            VStack(alignment: .leading) {
                HStack {
                    Image(systemName: "shippingbox")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))

                    Text("package_type".localized)
                        .font(.headline)
                }
                .padding(.bottom, 8)

                Picker("package_type".localized, selection: $selectedPackageType) {
                    ForEach(PackageType.allCases, id: \.self) { type in
                        Text(type.localizedName).tag(Optional(type))
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)

            // 优先级设置
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "flag.fill")
                        .foregroundColor(.orange)
                        .font(.system(size: 20))

                    Text("priority_delivery".localized)
                        .font(.headline)
                }

                Picker("priority_level".localized, selection: $selectedPriority) {
                    ForEach(DeliveryPriority.allCases, id: \.self) { priority in
                        Text(priority.localizedName).tag(priority)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: selectedPriority) { _, newPriority in
                    updatePriority(newPriority)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
        }
        .padding(.horizontal)
        .padding(.top)
    }

    // MARK: - 车辆位置信息区
    private var vehiclePositionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 车辆位置说明
            HStack {
                Image(systemName: "shippingbox.fill")
                    .foregroundColor(.blue)
                Text("select_package_position".localized)
                    .font(.headline)
            }

            // 车辆位置选择 - 紧凑网格布局
            VStack(alignment: .leading, spacing: 8) {
                // 三个维度的选择器，使用网格布局
                VStack(spacing: 8) {
                    // 第一行：前中后 + 左右
                    HStack(spacing: 12) {
                        // 前中后位置
                        VStack(alignment: .leading, spacing: 4) {
                            Text("vehicle_area".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Picker("vehicle_area".localized, selection: Binding<VehiclePosition?>(
                                get: {
                                    if selectedVehiclePositions.contains(.front) { return .front }
                                    if selectedVehiclePositions.contains(.middle) { return .middle }
                                    if selectedVehiclePositions.contains(.back) { return .back }
                                    return nil
                                },
                                set: { newValue in
                                    // 移除其他区域位置
                                    selectedVehiclePositions.remove(.front)
                                    selectedVehiclePositions.remove(.middle)
                                    selectedVehiclePositions.remove(.back)
                                    // 添加新选择的位置
                                    if let position = newValue {
                                        selectedVehiclePositions.insert(position)
                                    }
                                }
                            )) {
                                Text("vehicle_position_none".localized).tag(Optional<VehiclePosition>.none)
                                ForEach([VehiclePosition.front, .middle, .back], id: \.self) { position in
                                    Text(position.localizedName).tag(Optional(position))
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                        }
                        .frame(maxWidth: .infinity)

                        // 左右位置
                        VStack(alignment: .leading, spacing: 4) {
                            Text("left_right_position".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Picker("left_right_position".localized, selection: Binding<VehiclePosition?>(
                                get: {
                                    if selectedVehiclePositions.contains(.left) { return .left }
                                    if selectedVehiclePositions.contains(.right) { return .right }
                                    return nil
                                },
                                set: { newValue in
                                    // 移除其他左右位置
                                    selectedVehiclePositions.remove(.left)
                                    selectedVehiclePositions.remove(.right)
                                    // 添加新选择的位置
                                    if let position = newValue {
                                        selectedVehiclePositions.insert(position)
                                    }
                                }
                            )) {
                                Text("vehicle_position_none".localized).tag(Optional<VehiclePosition>.none)
                                ForEach([VehiclePosition.left, .right], id: \.self) { position in
                                    Text(position.localizedName).tag(Optional(position))
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                        }
                        .frame(maxWidth: .infinity)
                    }

                    // 第二行：高度位置（居中）
                    HStack {
                        Spacer()
                        VStack(alignment: .leading, spacing: 4) {
                            Text("height_position".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Picker("height_position".localized, selection: Binding<VehiclePosition?>(
                                get: {
                                    if selectedVehiclePositions.contains(.floor) { return .floor }
                                    if selectedVehiclePositions.contains(.shelf) { return .shelf }
                                    return nil
                                },
                                set: { newValue in
                                    // 移除其他高度位置
                                    selectedVehiclePositions.remove(.floor)
                                    selectedVehiclePositions.remove(.shelf)
                                    // 添加新选择的位置
                                    if let position = newValue {
                                        selectedVehiclePositions.insert(position)
                                    }
                                }
                            )) {
                                Text("vehicle_position_none".localized).tag(Optional<VehiclePosition>.none)
                                ForEach([VehiclePosition.floor, .shelf], id: \.self) { position in
                                    Text(position.localizedName).tag(Optional(position))
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                        }
                        .frame(maxWidth: .infinity)
                        Spacer()
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
        }
    }

    // MARK: - 配送信息区
    private var deliveryInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 配送类型
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "shippingbox")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))

                    Text("delivery_type".localized)
                        .font(.headline)
                }
                .padding(.bottom, 4)

                Picker("delivery_type".localized, selection: $selectedDeliveryType) {
                    ForEach(DeliveryType.allCases, id: \.self) { type in
                        HStack {
                            Image(systemName: type == .delivery ? "shippingbox.fill" : "arrow.up.bin.fill")
                            Text(type.localizedName)
                        }.tag(type)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .padding(.horizontal)
            .padding(.top)



            // 追踪信息
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "doc.text")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))

                    Text("tracking_info".localized)
                        .font(.headline)
                }
                .padding(.bottom, 4)

                HStack {
                    Text("tracking_number".localized)
                        .foregroundColor(.secondary)
                    Spacer()
                    TextField("enter_tracking_number".localized, text: $trackingNumber)
                        .multilineTextAlignment(.trailing)
                        .frame(maxWidth: 200)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                // 🏷️ 第三方排序号编辑（仅对第三方应用显示）
                if deliveryPoint.sourceApp != .manual {
                    HStack {
                        HStack {
                            Text("\(deliveryPoint.sourceApp.displayName) 排序号")
                                .foregroundColor(.secondary)

                            if thirdPartySortNumber == "missing" {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.red)
                                    .font(.title3)
                            }
                        }

                        Spacer()

                        TextField("输入排序号", text: $thirdPartySortNumber)
                            .multilineTextAlignment(.trailing)
                            .frame(maxWidth: 200)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.numberPad)
                    }

                    if thirdPartySortNumber == "missing" {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                                .font(.title3)
                            Text("AI识别时发现重复的第三方排序号，请根据原始图片手动输入正确的序号")
                                .font(.caption)
                                .foregroundColor(.red)
                                .fontWeight(.medium)
                            Spacer()
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(6)
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .padding(.horizontal)

            // 时间信息 - 暂时屏蔽
            // VStack(alignment: .leading, spacing: 12) {
            //     HStack {
            //         Image(systemName: "timer")
            //             .foregroundColor(.blue)
            //             .font(.system(size: 20))
            //
            //         Text("time_info".localized)
            //             .font(.headline)
            //     }
            //     .padding(.bottom, 4)
            //
            //     // 到达时间
            //     HStack {
            //         Text("estimated_arrival_time".localized)
            //             .foregroundColor(.secondary)
            //         Spacer()
            //         TextField("anytime".localized, text: $arrivalTime)
            //             .multilineTextAlignment(.trailing)
            //             .frame(width: 120)
            //             .textFieldStyle(RoundedBorderTextFieldStyle())
            //     }
            //
            //     // 停留时间
            //     HStack {
            //         Text("stop_time".localized)
            //             .foregroundColor(.secondary)
            //         Spacer()
            //         Stepper(String(format: "minutes_format".localized, timeAtStop), value: $timeAtStop, in: 1...60)
            //             .frame(width: 150)
            //     }
            // }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .padding(.horizontal)

            // 照片信息
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "photo.stack")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))

                    Text("photo_record".localized)
                        .font(.headline)
                }
                .padding(.bottom, 4)

                // 照片状态
                VStack(spacing: 8) {
                    photoStatusRow(title: "door_number_photo".localized, path: deliveryPoint.doorPhotoPath, iconName: "door.left.hand.open")
                    photoStatusRow(title: "package_label_photo".localized, path: deliveryPoint.packagePhotoPath, iconName: "tag")
                    photoStatusRow(title: "placement_photo".localized, path: deliveryPoint.placementPhotoPath, iconName: "cube.box")

                    if let completionDate = deliveryPoint.photosCompletionDate {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("photo_record_completed".localized)
                                .font(.subheadline)
                            Spacer()
                            Text(completionDate, style: .date)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .padding(.horizontal)
        }
    }

    // MARK: - 辅助方法

    // 切换车辆位置选择
    private func toggleVehiclePosition(_ position: VehiclePosition) {
        if selectedVehiclePositions.contains(position) {
            selectedVehiclePositions.remove(position)
        } else {
            selectedVehiclePositions.insert(position)
        }
    }



    // 保存更改
    private func saveChanges() {
        // 更新地址（如果已编辑）
        if isAddressEditable && editedAddress != deliveryPoint.primaryAddress {
            // 🔧 修复：更新 originalAddress 字段，确保界面显示新地址
            deliveryPoint.originalAddress = editedAddress
            print("🔧 DeliveryPointManagerView - saveChanges: 已更新 originalAddress: \(editedAddress)")

            // 🎯 修复：不直接设置完整地址到streetName，而是通过结构化地址处理
            // 使用全球地址处理器获取结构化地址信息
            Task {
                let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(editedAddress)

                await MainActor.run {
                    switch globalResult {
                    case .success(_, _, _, let placemark, _, _):
                        // 使用 placemark 填充结构化地址字段
                        self.deliveryPoint.populateStructuredAddress(from: placemark)
                        print("🌍 DeliveryPointManagerView - saveChanges: 已填充结构化地址")
                    case .failed(_, _):
                        // 如果处理失败，尝试从完整地址中提取街道名称部分
                        let addressComponents = self.editedAddress.components(separatedBy: ",")
                        if let firstComponent = addressComponents.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
                            self.deliveryPoint.streetName = firstComponent
                            print("🔧 DeliveryPointManagerView - saveChanges: 提取的街道名称: \(firstComponent)")
                        }
                    }

                    // 保存更改
                    do {
                        try self.modelContext.save()
                        print("✅ DeliveryPointManagerView - saveChanges: 地址更新成功保存")
                    } catch {
                        print("❌ DeliveryPointManagerView - saveChanges: 保存失败: \(error.localizedDescription)")
                    }
                }
            }

            // 如果通过自动完成选择了坐标，则使用选择的坐标
            if let coordinate = selectedCoordinate {
                deliveryPoint.latitude = coordinate.latitude
                deliveryPoint.longitude = coordinate.longitude

                // 验证坐标
                _ = deliveryPoint.validateCoordinates()

                // 如果有用户位置，基于用户位置验证
                if let userLocation = CLLocationManager().location?.coordinate {
                    deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                }
            } else {
                // 如果没有通过自动完成选择坐标，则尝试进行地理编码
                // 使用GeocodingService的Combine API
                GeocodingService.shared.geocodeAddress(deliveryPoint.primaryAddress)
                    .first() // 只取第一个结果
                    .sink { result in
                        if let coordinate = result.coordinate {
                            deliveryPoint.latitude = coordinate.latitude
                            deliveryPoint.longitude = coordinate.longitude

                            // 验证坐标
                            _ = deliveryPoint.validateCoordinates()

                            // 如果有用户位置，基于用户位置验证
                            if let userLocation = CLLocationManager().location?.coordinate {
                                deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                            }

                            // 保存更新的坐标
                            do {
                                try self.modelContext.save()
                                print("成功更新配送点坐标，ID: \(self.deliveryPoint.id)")
                            } catch {
                                print("更新配送点坐标失败: \(error.localizedDescription)")
                            }
                        }
                    }
                    .store(in: &cancellables)
            }
        }

        // 更新包裹信息
        deliveryPoint.packageCount = packageCount
        deliveryPoint.packageSize = selectedPackageSize?.rawValue
        deliveryPoint.packageType = selectedPackageType?.rawValue
        deliveryPoint.priority = selectedPriority.rawValue

        // 更新车辆位置
        deliveryPoint.vehiclePosition = selectedVehiclePositions.isEmpty ? "" : selectedVehiclePositions.map { $0.rawValue }.joined(separator: ",")

        // 更新配送信息
        deliveryPoint.deliveryType = selectedDeliveryType.rawValue
        deliveryPoint.updateStatus(selectedDeliveryStatus)
        deliveryPoint.trackingNumber = trackingNumber.isEmpty ? nil : trackingNumber

        // 🏷️ 更新第三方排序号
        if deliveryPoint.sourceApp != .manual {
            let cleanedSortNumber = thirdPartySortNumber.trimmingCharacters(in: .whitespacesAndNewlines)
            deliveryPoint.thirdPartySortNumber = cleanedSortNumber.isEmpty ? nil : cleanedSortNumber
            print("🏷️ 更新第三方排序号: \(cleanedSortNumber)")
        }

        deliveryPoint.arrivalTime = arrivalTime.isEmpty ? nil : arrivalTime
        deliveryPoint.timeAtStop = timeAtStop

        // 保存到数据库
        do {
            try modelContext.save()
            print("成功保存配送点管理信息，ID: \(deliveryPoint.id)")
        } catch {
            print("保存配送点管理信息失败: \(error.localizedDescription)")
        }
    }

    // 更新配送状态 - 显示状态表单
    private func updateDeliveryStatus() {
        // 显示状态更新表单
        showingStatusSheet = true
    }

    // 更新配送状态并保存 - 表单选择完成后调用
    private func updateDeliveryStatusWithReason(_ newStatus: DeliveryStatus, failureReason: DeliveryFailureReason? = nil, customReason: String? = nil) {
        selectedDeliveryStatus = newStatus
        selectedFailureReason = failureReason

        // 更新状态和可能的失败原因
        deliveryPoint.updateStatus(newStatus, failureReason: failureReason, customReason: customReason)

        // 保存到数据库
        try? modelContext.save()
    }

    // 保存第三方号码
    private func saveThirdPartyNumber(_ newNumber: String) {
        let trimmedNumber = newNumber.trimmingCharacters(in: .whitespacesAndNewlines)

        // 检查是否有重复
        if let duplicateInfo = checkForDuplicateThirdPartyNumber(trimmedNumber) {
            duplicateConfirmationMessage = duplicateInfo
            showingDuplicateAlert = true
        } else {
            // 没有重复，直接保存
            deliveryPoint.thirdPartySortNumber = trimmedNumber.isEmpty ? nil : trimmedNumber
            try? modelContext.save()
            Logger.info("✅ 用户手动更新第三方排序号: '\(deliveryPoint.thirdPartySortNumber ?? "nil")'", type: .data)
        }
    }

    // 检查第三方号码重复
    private func checkForDuplicateThirdPartyNumber(_ number: String) -> String? {
        guard !number.isEmpty else { return nil }

        // 获取当前路线的所有配送点
        guard let route = deliveryPoint.route else { return nil }

        // 查找是否有其他配送点使用相同的第三方号码
        let duplicatePoints = route.points.filter { point in
            point.id != deliveryPoint.id && // 排除当前点
            point.thirdPartySortNumber == number
        }

        if !duplicatePoints.isEmpty {
            let addresses = duplicatePoints.map { $0.primaryAddress }.joined(separator: "\n")
            return "duplicate_number_message".localized(with: number, addresses)
        }

        return nil
    }

    // 照片状态行 - 已修改Door Number为Road Number
    private func photoStatusRow(title: String, path: String?, iconName: String) -> some View {
        HStack {
            Image(systemName: path != nil ? "checkmark.circle.fill" : "circle")
                .foregroundColor(path != nil ? .green : .gray)

            Image(systemName: iconName)
                .foregroundColor(.blue)
                .font(.caption)

            Text(title)
                .font(.subheadline)

            Spacer()

            if path != nil {
                Text("completed".localized)
                    .font(.caption)
                    .foregroundColor(.green)
            } else {
                Text("not_taken".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    // 简化的包裹信息部分
    private var simplifiedPackageInfoSection: some View {
        VStack(spacing: 24) {
            // 包裹数量 - 一行布局：标题在左，控件在右
            HStack {
                // 左侧：图标 + 标题
                HStack {
                    Image(systemName: "cube.box.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))
                    Text("package_count".localized)
                        .font(.headline)
                }

                Spacer()

                // 右侧：包裹数量控件（无额外图标）
                HStack(spacing: 8) {
                    Button(action: {
                        if packageCount > 1 {
                            packageCount -= 1
                        }
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                    .disabled(packageCount <= 1)

                    Text("\(packageCount)")
                        .font(.title3)
                        .frame(minWidth: 40)

                    Button(action: {
                        packageCount += 1
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }

            // 配送类型 - 一行布局：标题在左，选择器在右
            HStack {
                // 左侧：图标 + 标题
                HStack {
                    Image(systemName: "shippingbox.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))
                    Text("delivery_type".localized)
                        .font(.headline)
                }

                Spacer()

                // 右侧：紧凑的选择器 - 优化小屏幕布局
                HStack(spacing: 4) {
                    // 取件
                    Button(action: { selectedDeliveryType = .pickup }) {
                        HStack(spacing: 3) {
                            Image(systemName: "arrow.up.circle")
                                .font(.caption)
                            Text("pickup".localized)
                                .font(.caption)
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .frame(minWidth: 60)
                        .background(selectedDeliveryType == .pickup ? Color.blue.opacity(0.2) : Color.clear)
                        .foregroundColor(selectedDeliveryType == .pickup ? .blue : .secondary)
                        .cornerRadius(6)
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .stroke(selectedDeliveryType == .pickup ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                        )
                    }

                    // 配送
                    Button(action: { selectedDeliveryType = .delivery }) {
                        HStack(spacing: 3) {
                            Image(systemName: "arrow.down.circle")
                                .font(.caption)
                            Text("delivery".localized)
                                .font(.caption)
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .frame(minWidth: 60)
                        .background(selectedDeliveryType == .delivery ? Color.blue.opacity(0.2) : Color.clear)
                        .foregroundColor(selectedDeliveryType == .delivery ? .blue : .secondary)
                        .cornerRadius(6)
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .stroke(selectedDeliveryType == .delivery ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                        )
                    }
                }
            }

            // 优先级设置 - 一行布局：标题在左，选择器在右
            HStack {
                // 左侧：图标 + 标题
                HStack {
                    Image(systemName: "flag.fill")
                        .foregroundColor(.orange)
                        .font(.system(size: 20))
                    Text("priority_delivery".localized)
                        .font(.headline)
                }

                Spacer()

                // 右侧：紧凑的优先级按钮组（只显示1-3级）
                HStack(spacing: 6) {
                    // 无优先级
                    Button(action: {
                        selectedPriority = .none
                        updatePriority(.none)
                    }) {
                        Text("no_priority_short".localized)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(selectedPriority == .none ? Color.gray.opacity(0.2) : Color.clear)
                            .foregroundColor(selectedPriority == .none ? .primary : .secondary)
                            .cornerRadius(6)
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(selectedPriority == .none ? Color.gray : Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    }

                    // 优先级1-3
                    ForEach([DeliveryPriority.priority1, .priority2, .priority3], id: \.self) { priority in
                        Button(action: {
                            selectedPriority = priority
                            updatePriority(priority)
                        }) {
                            Text("\(priority.rawValue)")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(selectedPriority == priority ? Color.orange.opacity(0.2) : Color.clear)
                                .foregroundColor(selectedPriority == priority ? .orange : .secondary)
                                .cornerRadius(6)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(selectedPriority == priority ? Color.orange : Color.gray.opacity(0.3), lineWidth: 1)
                                )
                        }
                    }
                }
            }
        }
    }

    // 简化的配送信息部分
    private var simplifiedDeliveryInfoSection: some View {
        VStack(spacing: 10) {
            // 追踪信息
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "doc.text.fill")
                        .foregroundColor(.blue)
                    Text("tracking_information".localized)
                        .font(.headline)
                }

                // 追踪号
                TextField("enter_tracking_number".localized, text: $trackingNumber)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }

            // 时间信息 - 暂时屏蔽
            // VStack(alignment: .leading, spacing: 12) {
            //     HStack {
            //         Image(systemName: "clock.fill")
            //             .foregroundColor(.blue)
            //         Text("time_information".localized)
            //             .font(.headline)
            //     }
            //
            //     // 停留时间
            //     HStack {
            //         Text("stop_time".localized)
            //         Spacer()
            //         HStack(alignment: .center, spacing: 10) {
            //             Button(action: {
            //                 if timeAtStop > 1 {
            //                     timeAtStop -= 1
            //                 }
            //             }) {
            //                 Image(systemName: "minus")
            //                     .frame(width: 18, height: 18)
            //                     .padding(8)
            //                     .background(Color.gray.opacity(0.2))
            //                     .cornerRadius(8)
            //             }
            //
            //             Text("\(timeAtStop)")
            //                 .frame(minWidth: 20)
            //                 .font(.headline)
            //
            //             Text("minutes".localized)
            //                 .foregroundColor(.secondary)
            //                 .frame(minWidth: 40)
            //
            //             Button(action: {
            //                 timeAtStop += 1
            //             }) {
            //                 Image(systemName: "plus")
            //                     .frame(width: 18, height: 18)
            //                     .padding(8)
            //                     .background(Color.gray.opacity(0.2))
            //                     .cornerRadius(8)
            //             }
            //         }
            //     }
            // }
        }
    }

    // 照片记录部分
    private var photoRecordSection: some View {
        VStack(spacing: 16) {
            // 门牌号照片
            Group {
                if let doorPhotoPath = deliveryPoint.doorPhotoPath {
                    // 已拍摄照片状态行 - 添加查看和重拍按钮
                    HStack {
                        photoStatusRow(
                            title: "door_number_photo".localized,
                            path: doorPhotoPath,
                            iconName: "door.left.hand.open")

                        Button(action: {
                            // 显示照片查看器
                            presentPhotoViewer(initialPhotoType: .doorNumber)
                        }) {
                            Image(systemName: "eye")
                                .foregroundColor(.blue)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                        .padding(.horizontal, 4)

                        Button(action: {
                            // 显示相机重拍
                            showingCamera = true
                        }) {
                            Image(systemName: "arrow.triangle.2.circlepath.camera")
                                .foregroundColor(.green)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    }
                    .contentShape(Rectangle())
                } else {
                    // 未拍摄照片状态行 - 点击拍照
                    photoStatusRow(
                        title: "door_number_photo".localized,
                        path: nil,
                        iconName: "door.left.hand.open")
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // 显示相机
                            showingCamera = true
                        }
                }
            }

            // 包裹标签照片
            Group {
                if let packagePhotoPath = deliveryPoint.packagePhotoPath {
                    // 已拍摄照片状态行 - 添加查看和重拍按钮
                    HStack {
                        photoStatusRow(
                            title: "package_label_photo".localized,
                            path: packagePhotoPath,
                            iconName: "tag")

                        Button(action: {
                            // 显示照片查看器
                            presentPhotoViewer(initialPhotoType: .packageLabel)
                        }) {
                            Image(systemName: "eye")
                                .foregroundColor(.blue)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                        .padding(.horizontal, 4)

                        Button(action: {
                            // 显示相机重拍
                            showingCamera = true
                        }) {
                            Image(systemName: "arrow.triangle.2.circlepath.camera")
                                .foregroundColor(.green)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    }
                    .contentShape(Rectangle())
                } else {
                    // 未拍摄照片状态行 - 点击拍照
                    photoStatusRow(
                        title: "package_label_photo".localized,
                        path: nil,
                        iconName: "tag")
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // 显示相机
                            showingCamera = true
                        }
                }
            }

            // 放置位置照片
            Group {
                if let placementPhotoPath = deliveryPoint.placementPhotoPath {
                    // 已拍摄照片状态行 - 添加查看和重拍按钮
                    HStack {
                        photoStatusRow(
                            title: "placement_photo".localized,
                            path: placementPhotoPath,
                            iconName: "cube.box")

                        Button(action: {
                            // 显示照片查看器
                            presentPhotoViewer(initialPhotoType: .placement)
                        }) {
                            Image(systemName: "eye")
                                .foregroundColor(.blue)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                        .padding(.horizontal, 4)

                        Button(action: {
                            // 显示相机重拍
                            showingCamera = true
                        }) {
                            Image(systemName: "arrow.triangle.2.circlepath.camera")
                                .foregroundColor(.green)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    }
                    .contentShape(Rectangle())
                } else {
                    // 未拍摄照片状态行 - 点击拍照
                    photoStatusRow(
                        title: "placement_photo".localized,
                        path: nil,
                        iconName: "cube.box")
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // 显示相机
                            showingCamera = true
                        }
                }
            }
        }
    }

    // 在地图中打开导航
    private func openInMaps() {
        // 使用NavigationAppHandler打开导航，确保遵循用户选择的导航应用偏好
        print("[DEBUG] DeliveryPointManagerView - 打开导航到: \(deliveryPoint.primaryAddress)")
        NavigationAppHandler.shared.openNavigation(to: deliveryPoint.coordinate, name: deliveryPoint.primaryAddress)
    }

    // 显示照片查看器
    private func presentPhotoViewer(initialPhotoType: DeliveryPhotoType) {
        self.initialPhotoType = initialPhotoType
        showingPhotoViewer = true
    }

    // 更新优先级并重新排序
    private func updatePriority(_ newPriority: DeliveryPriority) {
        // 更新当前配送点的优先级
        deliveryPoint.priority = newPriority.rawValue

        // 如果设置了优先级，需要重新排序所有配送点
        if newPriority != .none {
            reorderDeliveryPointsByPriority(targetPriority: newPriority)
        }

        // 保存更改
        do {
            try modelContext.save()
            print("成功更新优先级: \(newPriority.localizedName)")
        } catch {
            print("保存优先级更新失败: \(error.localizedDescription)")
        }
    }

    // 根据优先级重新排序配送点
    private func reorderDeliveryPointsByPriority(targetPriority: DeliveryPriority) {
        // 获取当前路线的所有配送点
        guard let route = deliveryPoint.route else {
            print("配送点没有关联路线，无法重新排序")
            return
        }

        // 获取所有非起点非终点的配送点
        let allPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }

        // 按优先级和原有顺序排序
        let sortedPoints = allPoints.sorted { point1, point2 in
            let priority1 = DeliveryPriority(rawValue: point1.priority) ?? .none
            let priority2 = DeliveryPriority(rawValue: point2.priority) ?? .none

            // 优先级不同时，按优先级排序
            if priority1.displayOrder != priority2.displayOrder {
                return priority1.displayOrder < priority2.displayOrder
            }

            // 优先级相同时，按原有的sort_number排序
            return point1.sort_number < point2.sort_number
        }

        // 重新分配sorted_number
        for (index, point) in sortedPoints.enumerated() {
            point.sorted_number = index + 1
            print("重新排序: \(point.primaryAddress) -> sorted_number: \(point.sorted_number), priority: \(point.priority)")
        }

        print("完成优先级重新排序，共处理 \(sortedPoints.count) 个配送点")
    }
}

// MARK: - 预览
#Preview("DeliveryPointManagerView") {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: DeliveryPoint.self, configurations: config)

    // 🎯 使用结构化地址创建示例点
    let samplePoint = DeliveryPoint(
        sort_number: 1,
        streetName: "42 Blackburn Road, Glen Waverley, VIC 3150",
        latitude: -37.8815,
        longitude: 145.1642
    )
    let _ = {
        samplePoint.isOptimized = true
        samplePoint.sorted_number = 3
        container.mainContext.insert(samplePoint)
    }()

    return DeliveryPointManagerView(deliveryPoint: samplePoint)
        .modelContainer(container)
}

// 状态更新表单视图
struct StatusUpdateSheet: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var currentStatus: DeliveryStatus
    @Binding var selectedFailureReason: DeliveryFailureReason?
    @State private var showFailureReasons: Bool = false
    @State private var tempStatus: DeliveryStatus
    @State private var tempFailureReason: DeliveryFailureReason?
    @State private var customFailureReason: String = ""

    // 绑定到当前编辑的配送点
    var deliveryPoint: DeliveryPoint?
    var onStatusSelected: (DeliveryStatus, DeliveryFailureReason?, String?) -> Void

    // 新的简化初始化器 - 直接接收DeliveryPoint对象
    init(deliveryPoint: DeliveryPoint, onStatusSelected: @escaping (DeliveryStatus, DeliveryFailureReason?, String?) -> Void) {
        self.deliveryPoint = deliveryPoint
        self.onStatusSelected = onStatusSelected

        // 直接从DeliveryPoint获取当前状态
        let currentStatus = deliveryPoint.deliveryStatus
        let currentFailureReason = deliveryPoint.deliveryFailureReasonEnum

        self._currentStatus = .constant(currentStatus)
        self._selectedFailureReason = .constant(currentFailureReason)
        self._tempStatus = State(initialValue: currentStatus)
        self._tempFailureReason = State(initialValue: currentFailureReason)
        self._showFailureReasons = State(initialValue: currentStatus == .failed)

        print("[INFO] StatusUpdateSheet - 简化初始化: currentStatus=\(currentStatus), failureReason=\(currentFailureReason?.rawValue ?? "nil"), deliveryPoint=\(deliveryPoint.primaryAddress)")

        // 如果是其他原因，初始化自定义原因文本
        if currentFailureReason == .other {
            self._customFailureReason = State(initialValue: deliveryPoint.customFailureReason ?? "")
        }
    }

    // 保留原有的初始化器以兼容现有代码
    init(currentStatus: Binding<DeliveryStatus>, selectedFailureReason: Binding<DeliveryFailureReason?>, deliveryPoint: DeliveryPoint? = nil, onStatusSelected: @escaping (DeliveryStatus, DeliveryFailureReason?, String?) -> Void) {
        self._currentStatus = currentStatus
        self._selectedFailureReason = selectedFailureReason
        self.deliveryPoint = deliveryPoint
        self.onStatusSelected = onStatusSelected
        self._tempStatus = State(initialValue: currentStatus.wrappedValue)
        self._tempFailureReason = State(initialValue: selectedFailureReason.wrappedValue)
        self._showFailureReasons = State(initialValue: currentStatus.wrappedValue == .failed)

        print("[INFO] StatusUpdateSheet - 兼容初始化: currentStatus=\(currentStatus.wrappedValue), failureReason=\(selectedFailureReason.wrappedValue?.rawValue ?? "nil"), deliveryPoint=\(deliveryPoint?.primaryAddress ?? "nil")")

        // 如果是其他原因，初始化自定义原因文本
        if let point = deliveryPoint,
           selectedFailureReason.wrappedValue == .other {
            self._customFailureReason = State(initialValue: point.customFailureReason ?? "")
        }
    }

    // 计算保存按钮是否应该启用的逻辑
    private var saveButtonEnabled: Bool {
        // 如果不是失败状态，则可以保存
        if tempStatus != .failed {
            return true
        }
        // 如果是失败状态但未选择原因，不能保存
        if tempFailureReason == nil {
            return false
        }
        // 如果选择"其他原因"，必须输入自定义原因文本
        if tempFailureReason == .other && customFailureReason.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return false
        }
        // 其他情况可以保存
        return true
    }

    var body: some View {
        NavigationStack {
            Form {
                // 状态选择部分
                Section(header: Text("select_delivery_status".localized)) {
                    // 已送达
                    Button(action: {
                        tempStatus = .completed
                        tempFailureReason = nil
                        customFailureReason = ""
                        showFailureReasons = false
                    }) {
                        HStack {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                                .font(.system(size: 22))

                            Text("delivered".localized)
                                .foregroundColor(.primary)

                            Spacer()

                            if tempStatus == .completed {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(BorderlessButtonStyle())

                    // 派送失败
                    Button(action: {
                        tempStatus = .failed
                        showFailureReasons = true
                    }) {
                        HStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.red)
                                .font(.system(size: 22))

                            Text("delivery_failed".localized)
                                .foregroundColor(.primary)

                            Spacer()

                            if tempStatus == .failed {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(BorderlessButtonStyle())

                    // 待派送
                    Button(action: {
                        tempStatus = .pending
                        tempFailureReason = nil
                        customFailureReason = ""
                        showFailureReasons = false
                    }) {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.orange)
                                .font(.system(size: 22))

                            Text("pending".localized)
                                .foregroundColor(.primary)

                            Spacer()

                            if tempStatus == .pending {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(BorderlessButtonStyle())


                }

                // 失败原因选择部分
                if showFailureReasons {
                    Section(header: Text("select_failure_reason".localized)) {
                        ForEach(DeliveryFailureReason.allCases, id: \.self) { reason in
                            Button(action: {
                                tempFailureReason = reason
                            }) {
                                HStack {
                                    Text(reason.localizedName)
                                        .foregroundColor(.primary)

                                    Spacer()

                                    if tempFailureReason == reason {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.blue)
                                    }
                                }
                                .contentShape(Rectangle())
                            }
                            .buttonStyle(BorderlessButtonStyle())
                        }

                        // 当是失败状态但还未选择原因时显示提示
                        if tempStatus == .failed && tempFailureReason == nil {
                            Text("failure_reason_required".localized)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.horizontal)
                        }
                    }

                    // 当选择"其他原因"时显示自定义原因输入框
                    if tempFailureReason == .other {
                        Section(header: Text("enter_custom_reason".localized)) {
                            TextField("custom_reason_placeholder".localized, text: $customFailureReason)
                                .padding(.vertical, 8)

                            if customFailureReason.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                                Text("custom_reason_required".localized)
                                    .font(.caption)
                                    .foregroundColor(.red)
                                    .padding(.top, 4)
                            }
                        }
                    }
                }
            }
            .navigationTitle("update_status".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 取消按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }

                // 保存按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("save".localized) {
                        // 调用状态更新回调
                        onStatusSelected(tempStatus, tempStatus == .failed ? tempFailureReason : nil, customFailureReason)
                        dismiss()
                    }
                    .bold()
                    .disabled(!saveButtonEnabled)  // 根据条件禁用按钮
                }
            }
        }
        .onAppear {
            print("[INFO] StatusUpdateSheet - onAppear: currentStatus=\(currentStatus), tempStatus=\(tempStatus), failureReason=\(selectedFailureReason?.rawValue ?? "nil")")

            // 初始化时，如果当前状态已经是失败状态，显示失败原因选择器
            if currentStatus == .failed {
                tempStatus = .failed
                showFailureReasons = true
            }
        }
    }
}

// MARK: - 第三方号码编辑Sheet
struct ThirdPartyNumberEditSheet: View {
    @Environment(\.dismiss) private var dismiss

    let currentNumber: String
    let appName: String
    let onSave: (String) -> Void

    @State private var editedNumber: String = ""

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                // 标题说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("edit_third_party_number".localized)
                        .font(.headline)

                    Text("edit_third_party_number_description".localized(with: appName))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 输入框
                VStack(alignment: .leading, spacing: 8) {
                    Text("sort_number".localized)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextField("enter_sort_number".localized, text: $editedNumber)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                        .font(.body)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("edit_third_party_number".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("save".localized) {
                        onSave(editedNumber)
                        dismiss()
                    }
                    .fontWeight(.bold)
                    .disabled(editedNumber.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
        .onAppear {
            editedNumber = currentNumber
        }
    }
}
