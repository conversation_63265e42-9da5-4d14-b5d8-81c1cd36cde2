import SwiftUI
import CoreLocation
import SwiftData
import MapKit

/// 地址编辑底部表单
/// 用于修正问题地址
struct AddressEditBottomSheet: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    // 使用State而不是ObservedObject，因为DeliveryPoint不是ObservableObject
    var deliveryPoint: DeliveryPoint
    @State private var address: String
    @State private var unitNumber: String
    @State private var thirdPartySortNumber: String
    @State private var selectedCoordinate: CLLocationCoordinate2D?
    @State private var isUpdatingCoordinates = false
    @State private var showingAlert = false
    @State private var processingTask: Task<Void, Never>? // 用于取消操作
    @State private var processingStatus = "正在处理地址..." // 处理状态文本

    // 🏢 移除智能检测 - Apple Maps 原生支持 1/12 格式
    @State private var alertMessage = ""

    // 获取用户位置
    @StateObject private var locationManager = LocationManager.shared

    init(deliveryPoint: DeliveryPoint) {
        self.deliveryPoint = deliveryPoint
        self._address = State(initialValue: deliveryPoint.primaryAddress)
        self._unitNumber = State(initialValue: deliveryPoint.unitNumber ?? "")
        self._thirdPartySortNumber = State(initialValue: deliveryPoint.thirdPartySortNumber ?? "")
    }

    // 🍎 Apple Maps 原生支持 1/12 格式，无需特殊处理

    var body: some View {
        NavigationView {
            Form {
                Section {
                    // 🎯 使用智能地址自动完成组件 - 使用全球搜索区域，添加防hang措施
                    EnhancedAddressAutocomplete(
                        searchText: $address,
                        selectedCoordinate: $selectedCoordinate,
                        initialRegion: MKCoordinateRegion(
                            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
                        ),
                        onAddressSelected: { selectedAddress, coordinate in
                            // 🛡️ 防hang：使用异步更新，避免界面阻塞
                            Logger.info("🎯 [地址编辑调试] AddressEditBottomSheet收到地址选择回调", type: .location)
                            Logger.info("🎯 [地址编辑调试] 选择的地址: '\(selectedAddress)'", type: .location)
                            Logger.info("🎯 [地址编辑调试] 选择的坐标: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)

                            Task { @MainActor in
                                Logger.info("🎯 [地址编辑调试] 在主线程更新AddressEditBottomSheet状态", type: .location)

                                // 当用户选择地址时更新坐标，但不自动保存，让用户可以继续编辑
                                self.address = selectedAddress
                                self.selectedCoordinate = coordinate

                                Logger.info("🎯 [地址编辑调试] ✅ 用户选择了地址: '\(selectedAddress)'，坐标: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                                Logger.info("📝 [地址编辑调试] 用户可以继续编辑地址或点击保存按钮", type: .location)
                            }
                        }
                    )
                    .frame(minHeight: 44) // 🛡️ 设置最小高度，防止布局问题导致hang

                    // 🏢 单元号/房间号输入字段
                    HStack {
                        Text("unit_number".localized)
                        TextField("unit_placeholder".localized, text: $unitNumber)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    .help("unit_help".localized)

                    // 🏷️ 第三方排序号编辑（仅对第三方应用显示）
                    if deliveryPoint.sourceApp != .manual {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("\(deliveryPoint.sourceApp.displayName) 排序号")
                                    .font(.subheadline)
                                    .foregroundColor(.primary)

                                if thirdPartySortNumber == "missing" {
                                    Text("(需要修正)")
                                        .font(.caption)
                                        .foregroundColor(.red)
                                        .fontWeight(.bold)
                                }
                            }

                            HStack {
                                TextField("输入排序号", text: $thirdPartySortNumber)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                    .keyboardType(.numberPad)

                                if thirdPartySortNumber == "missing" {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.red)
                                        .font(.title2)
                                }
                            }

                            if thirdPartySortNumber == "missing" {
                                Text("AI识别时发现重复的第三方排序号，请根据原始图片手动输入正确的序号")
                                    .font(.caption)
                                    .foregroundColor(.red)
                                    .fontWeight(.medium)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.red.opacity(0.1))
                                    .cornerRadius(6)
                            }
                        }
                        .padding(.vertical, 4)
                    }

                    // 🍎 保持简洁 - Apple Maps 原生支持各种地址格式

                    // 显示警告信息
                    if let warning = deliveryPoint.geocodingWarning, !warning.isEmpty {
                        HStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.orange)
                            Text(warning)
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }

                    // 显示位置验证状态 - 不显示unknown状态
                    let validationStatus = LocationValidationStatus(rawValue: deliveryPoint.locationValidationStatus) ?? .unknown
                    if validationStatus != .unknown && validationStatus != .valid {
                        HStack {
                            Image(systemName: validationStatus.iconName)
                                .foregroundColor(validationStatus.color)
                            Text(validationStatus.localizedName)
                                .foregroundColor(validationStatus.color)
                        }
                    }

                    // 显示坐标
                    HStack {
                        Text("coordinates".localized)
                        Spacer()
                        Text("(\(deliveryPoint.latitude), \(deliveryPoint.longitude))")
                            .foregroundColor(.secondary)
                    }

                    // 显示与用户位置的距离
                    if let distance = deliveryPoint.distanceFromUserLocation {
                        HStack {
                            Text("distance_from_current_location".localized)
                            Spacer()
                            Text(formatDistance(distance))
                                .foregroundColor(distance <= 200_000 ? .green : .orange)
                        }
                    }
                } header: {
                    Text("address_info".localized)
                }

                Section {
                    Button(action: updateCoordinates) {
                        HStack {
                            Text("update_coordinates".localized)
                            Spacer()
                            if isUpdatingCoordinates {
                                VStack(alignment: .trailing, spacing: 4) {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text(processingStatus)
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .disabled(isUpdatingCoordinates)

                    // 取消按钮（仅在处理时显示）
                    if isUpdatingCoordinates {
                        Button("取消处理") {
                            cancelProcessing()
                        }
                        .foregroundColor(.red)
                    }
                }
            }
            .navigationTitle("fix_address".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        Logger.info("💾 [地址编辑调试] 用户点击了保存按钮", type: .location)
                        Logger.info("💾 [地址编辑调试] 当前地址: '\(address)'", type: .location)
                        Logger.info("💾 [地址编辑调试] 当前坐标: \(selectedCoordinate?.latitude ?? 0), \(selectedCoordinate?.longitude ?? 0)", type: .location)
                        saveChanges()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("prompt".localized),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("confirm".localized))
                )
            }
        }
        // 🛡️ 添加presentation配置防止手势冲突和hang
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
        .presentationCornerRadius(12)
        .presentationBackground(.regularMaterial)
        .interactiveDismissDisabled(false) // 允许拖拽关闭
        .onDisappear {
            // 🧹 清理所有正在进行的任务，防止内存泄漏
            cleanupOnDisappear()
        }
    }

    // MARK: - 清理方法

    /// 清理所有正在进行的任务和资源
    private func cleanupOnDisappear() {
        Logger.info("🧹 AddressEditBottomSheet - 开始清理资源", type: .location)

        // 取消正在进行的处理任务
        processingTask?.cancel()
        processingTask = nil

        // 重置状态
        isUpdatingCoordinates = false
        processingStatus = "正在处理地址..."

        Logger.info("✅ AddressEditBottomSheet - 资源清理完成", type: .location)
    }

    // 格式化距离
    private func formatDistance(_ distance: Double) -> String {
        if distance >= 1000 {
            return String(format: "kilometers_format".localized, distance / 1000)
        } else {
            return String(format: "meters_format".localized, distance)
        }
    }

    // 取消处理
    private func cancelProcessing() {
        Logger.info("🚫 AddressEditBottomSheet - 用户取消处理", type: .location)

        processingTask?.cancel()
        processingTask = nil
        isUpdatingCoordinates = false
        processingStatus = "正在处理地址..."

        Logger.info("✅ AddressEditBottomSheet - 处理已取消", type: .location)
    }

    // 更新坐标
    private func updateCoordinates() {
        guard address != deliveryPoint.primaryAddress else {
            alertMessage = "modify_address_first".localized
            showingAlert = true
            return
        }

        isUpdatingCoordinates = true
        processingStatus = "正在验证地址..."

        // 使用DeliveryPointManager更新坐标，添加超时机制防止hang
        processingTask = Task {
            // 🛡️ 添加超时机制，防止长时间hang
            let timeoutTask = Task {
                try? await Task.sleep(nanoseconds: 30_000_000_000) // 30秒超时
                if !Task.isCancelled {
                    await MainActor.run {
                        Logger.info("⏰ AddressEditBottomSheet - 处理超时，自动取消", type: .location)
                        self.cancelProcessing()
                        self.alertMessage = "处理超时，请重试"
                        self.showingAlert = true
                    }
                }
            }

            defer {
                timeoutTask.cancel()
            }

            // 检查是否被取消
            guard !Task.isCancelled else { return }

            await MainActor.run {
                processingStatus = "正在修复地址格式..."
            }

            // 🎯 首先检查并修复缺少州信息的地址
            let fixedAddress = await fixAddressStateIfNeeded(address)

            // 检查是否被取消
            guard !Task.isCancelled else { return }

            // 如果地址被修复，更新界面显示
            if fixedAddress != address {
                await MainActor.run {
                    self.address = fixedAddress
                    Logger.info("🔧 地址已自动修复: \(address) -> \(fixedAddress)", type: .location)
                }
            }

            await MainActor.run {
                processingStatus = "正在获取地址信息..."
            }

            // 🎯 使用修复后的地址进行处理
            deliveryPoint.streetName = fixedAddress // 临时设置

            // 🏗️ 使用全球地址处理器获取结构化地址信息
            let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(fixedAddress)

            // 检查是否被取消
            guard !Task.isCancelled else { return }

            await MainActor.run {
                switch globalResult {
                case .success(_, _, _, let placemark, _, _):
                    // 使用 placemark 填充结构化地址字段
                    deliveryPoint.populateStructuredAddress(from: placemark)
                case .failed(_, _):
                    break // 保持原地址
                }
                processingStatus = "正在更新坐标..."
            }

            // 更新坐标
            DeliveryPointManager.shared.updateDeliveryPointCoordinates(
                deliveryPoint: deliveryPoint,
                modelContext: modelContext
            ) { success in
                // 检查是否被取消
                guard !Task.isCancelled else { return }

                // 更新基于用户位置的验证
                if let userLocation = self.locationManager.userLocation {
                    self.deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                }

                self.isUpdatingCoordinates = false
                self.processingTask = nil
                self.processingStatus = "正在处理地址..."

                if success {
                    self.alertMessage = "coordinates_update_success".localized

                    // 通知RouteViewModel检查优化按钮可用性
                    DispatchQueue.main.async {
                        RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()
                    }
                } else {
                    self.alertMessage = "coordinates_update_failure".localized
                }
                self.showingAlert = true
            }
        }
    }

    // 🎯 使用完整地址信息更新DeliveryPoint的所有字段
    private func updateDeliveryPointWithCompleteAddressInfo(selectedAddress: String, coordinate: CLLocationCoordinate2D) async {
        // 使用反向地理编码获取完整的地址信息
        let geocoder = CLGeocoder()

        do {
            let placemarks = try await geocoder.reverseGeocodeLocation(
                CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
            )

            guard let placemark = placemarks.first else {
                // 如果反向地理编码失败，使用基本更新
                await updateBasicAddressInfo(selectedAddress: selectedAddress, coordinate: coordinate)
                return
            }

            await MainActor.run {
                // 🎯 使用AddressDataStandardizer标准化地址数据
                let standardizedData = AddressDataStandardizer.shared.standardizeFromPlacemark(
                    placemark,
                    originalAddress: selectedAddress
                )

                // 🎯 使用placemark填充结构化地址字段（使用Apple Maps优化格式）
                self.deliveryPoint.populateStructuredAddress(from: placemark)
                self.deliveryPoint.latitude = coordinate.latitude
                self.deliveryPoint.longitude = coordinate.longitude

                // 🏛️ 确保主地址也使用USPS标准格式（用于数据库存储）
                let standardizedAddress = AppleMapsAddressFormatter.formatForDatabaseStorage(selectedAddress)
                self.deliveryPoint.originalAddress = standardizedAddress

                // 🎯 清除所有验证错误和警告
                self.deliveryPoint.geocodingWarning = nil
                self.deliveryPoint.addressValidationScore = 100.0
                self.deliveryPoint.addressValidationIssues = nil

                // 重新验证坐标
                _ = self.deliveryPoint.validateCoordinates()

                // 基于用户位置重新验证
                if let userLocation = self.locationManager.userLocation {
                    self.deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
                }

                // 🏠 保存到地址库
                Task {
                    Logger.info("🏠 AddressEditBottomSheet - updateDeliveryPoint保存地址到地址库: \(selectedAddress) -> (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                    await UserAddressDatabase.shared.saveValidatedAddress(
                        selectedAddress,
                        coordinate: coordinate,
                        source: .manual,
                        confidence: 0.95 // 用户选择的地址，置信度很高
                    )
                    Logger.info("🏠 AddressEditBottomSheet - ✅ updateDeliveryPoint地址库更新完成: \(selectedAddress)", type: .location)
                }

                // 保存更改到数据库
                do {
                    try self.modelContext.save()

                    // 通知RouteViewModel检查优化按钮可用性
                    DispatchQueue.main.async {
                        RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()
                    }

                    Logger.info("✅ 地址标准化更新成功: \(standardizedData.fullAddress)", type: .location)
                } catch {
                    self.alertMessage = String(format: "save_failure".localized, error.localizedDescription)
                    self.showingAlert = true
                }
            }
        } catch {
            Logger.warning("⚠️ 反向地理编码失败，使用基本更新: \(error.localizedDescription)", type: .location)
            await updateBasicAddressInfo(selectedAddress: selectedAddress, coordinate: coordinate)
        }
    }

    // 基本地址信息更新（反向地理编码失败时的降级方案）
    private func updateBasicAddressInfo(selectedAddress: String, coordinate: CLLocationCoordinate2D) async {
        await MainActor.run {
            // 🎯 修复：不直接设置完整地址到streetName，而是通过结构化地址处理

            // 🏗️ 使用全球地址处理器获取结构化地址信息
            Task {
                let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(selectedAddress)

                await MainActor.run {
                    switch globalResult {
                    case .success(_, _, _, let placemark, _, _):
                        // 使用 placemark 填充结构化地址字段
                        self.deliveryPoint.populateStructuredAddress(from: placemark)
                    case .failed(_, _):
                        break // 保持原地址
                    }
                }
            }
            self.deliveryPoint.latitude = coordinate.latitude
            self.deliveryPoint.longitude = coordinate.longitude

            // 🎯 清除所有验证错误和警告
            self.deliveryPoint.geocodingWarning = nil
            self.deliveryPoint.addressValidationScore = 100.0
            self.deliveryPoint.addressValidationIssues = nil

            // 重新验证坐标
            _ = self.deliveryPoint.validateCoordinates()

            // 基于用户位置重新验证
            if let userLocation = self.locationManager.userLocation {
                self.deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
            }

            // 🏠 保存到地址库
            Task {
                Logger.info("🏠 AddressEditBottomSheet - updateBasicAddress保存地址到地址库: \(selectedAddress) -> (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                await UserAddressDatabase.shared.saveValidatedAddress(
                    selectedAddress,
                    coordinate: coordinate,
                    source: .manual,
                    confidence: 0.90 // 基本地址信息的置信度稍低
                )
                Logger.info("🏠 AddressEditBottomSheet - ✅ updateBasicAddress地址库更新完成: \(selectedAddress)", type: .location)
            }

            // 保存更改到数据库
            do {
                try self.modelContext.save()

                // 通知RouteViewModel检查优化按钮可用性
                DispatchQueue.main.async {
                    RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()
                }

                Logger.info("✅ 基本地址信息更新成功: \(selectedAddress)", type: .location)
            } catch {
                self.alertMessage = String(format: "save_failure".localized, error.localizedDescription)
                self.showingAlert = true
            }
        }
    }

    // 保存更改
    private func saveChanges() {
        Logger.info("💾 [地址编辑调试] saveChanges方法开始执行", type: .location)
        Logger.info("💾 [地址编辑调试] 原始地址: '\(deliveryPoint.primaryAddress)'", type: .location)
        Logger.info("💾 [地址编辑调试] 编辑后地址: '\(address)'", type: .location)
        Logger.info("💾 [地址编辑调试] 是否有选中的坐标: \(selectedCoordinate != nil)", type: .location)

        Task {
            Logger.info("💾 [地址编辑调试] 开始异步保存任务", type: .location)

            // 🎯 首先检查并修复缺少州信息的地址
            Logger.info("💾 [地址编辑调试] 检查并修复州信息", type: .location)
            let fixedAddress = await fixAddressStateIfNeeded(address)

            // 如果地址被修复，更新界面显示
            if fixedAddress != address {
                Logger.info("🔧 [地址编辑调试] 地址需要修复: '\(address)' -> '\(fixedAddress)'", type: .location)
                await MainActor.run {
                    self.address = fixedAddress
                    Logger.info("🔧 [地址编辑调试] 地址已自动修复并更新UI", type: .location)
                }
            } else {
                Logger.info("🔧 [地址编辑调试] 地址无需修复", type: .location)
            }

            // 🏢 首先从地址中提取单位信息（如果用户没有手动设置）
            let extractedUnit = unitNumber.isEmpty ? DeliveryPoint.extractUnitNumber(from: fixedAddress) : unitNumber

            await MainActor.run {
                deliveryPoint.unitNumber = extractedUnit?.isEmpty == false ? extractedUnit : nil

                // 🔧 修复：更新 originalAddress 字段，确保界面显示新地址
                deliveryPoint.originalAddress = fixedAddress
                Logger.info("🔧 AddressEditBottomSheet - 已更新 originalAddress: \(fixedAddress)", type: .location)

                // 🚨 重要：thirdPartySortNumber 是AI识别的原始数据，不允许修改
                // 这个字段应该保持AI识别时的原始值，作为历史记录
                Logger.info("🔒 保护第三方排序号不被修改: \(deliveryPoint.thirdPartySortNumber ?? "nil")", type: .data)
            }

            // 🏗️ 使用全球地址处理器获取结构化地址信息
            let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(fixedAddress)

            await MainActor.run {
                switch globalResult {
                case .success(_, _, let coordinateResult, let placemark, _, _):
                    // 使用 placemark 填充结构化地址字段
                    deliveryPoint.populateStructuredAddress(from: placemark)
                    // 保持提取到的或用户手动设置的单元号
                    if let unit = extractedUnit, !unit.isEmpty {
                        deliveryPoint.unitNumber = unit
                    }

                    // 🎯 关键优化：使用专门的服务更新地址库
                    Task {
                        // 🔍 调试：检查数据库状态
                        let dbEnabled = UserAddressDatabase.shared.isEnabled
                        logInfo("🔍 AddressEditBottomSheet - 数据库状态: \(dbEnabled ? "启用" : "禁用")")

                        // 如果用户修改了地址，说明原地址可能有问题，标记原地址需要重新验证
                        if deliveryPoint.primaryAddress != address {
                            await AddressQualityValidator.shared.markAddressForRevalidation(deliveryPoint.primaryAddress)
                            logInfo("🔄 AddressEditBottomSheet - 已标记原地址需要重新验证: \(deliveryPoint.primaryAddress)")
                        }

                        await AddressDatabaseUpdateService.shared.updateAddressAfterUserCorrection(
                            originalAddress: deliveryPoint.primaryAddress,
                            correctedAddress: address,
                            coordinate: coordinateResult
                        )
                        logInfo("✅ AddressEditBottomSheet - 已通过服务更新地址库: \(address)")
                    }

                case .failed(_, _):
                    break // 保持原地址
                }
            }
        }

        // 🎯 清除所有验证错误和警告（用户手动修改地址后）
        deliveryPoint.geocodingWarning = nil
        deliveryPoint.addressValidationScore = 100.0  // 设置为满分
        deliveryPoint.addressValidationIssues = nil   // 清除验证问题

        // 重新验证坐标
        _ = deliveryPoint.validateCoordinates()

        // 基于用户位置重新验证
        if let userLocation = locationManager.userLocation {
            deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
        }

        // 保存更改
        do {
            try modelContext.save()

            // 通知RouteViewModel检查优化按钮可用性
            RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()

            dismiss()
        } catch {
            alertMessage = String(format: "save_failure".localized, error.localizedDescription)
            showingAlert = true
        }
    }

    // 🚀 自动保存并退出（用户选择搜索结果时调用）
    private func autoSaveAndDismiss() {
        Task {
            // 🎯 首先检查并修复缺少州信息的地址
            let fixedAddress = await fixAddressStateIfNeeded(address)

            // 如果地址被修复，更新界面显示
            if fixedAddress != address {
                await MainActor.run {
                    self.address = fixedAddress
                    Logger.info("🔧 地址已自动修复: \(address) -> \(fixedAddress)", type: .location)
                }
            }

            // 🏢 首先从地址中提取单位信息（如果用户没有手动设置）
            let extractedUnit = unitNumber.isEmpty ? DeliveryPoint.extractUnitNumber(from: fixedAddress) : unitNumber

            await MainActor.run {
                deliveryPoint.unitNumber = extractedUnit?.isEmpty == false ? extractedUnit : nil

                // 🔧 修复：更新 originalAddress 字段，确保界面显示新地址
                deliveryPoint.originalAddress = fixedAddress
                Logger.info("🔧 AddressEditBottomSheet - autoSave已更新 originalAddress: \(fixedAddress)", type: .location)
            }

            // 🏗️ 使用全球地址处理器获取结构化地址信息
            let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(fixedAddress)

            await MainActor.run {
                switch globalResult {
                case .success(_, _, let coordinateResult, let placemark, _, _):
                    // 使用 placemark 填充结构化地址字段
                    deliveryPoint.populateStructuredAddress(from: placemark)
                    // 保持提取到的或用户手动设置的单元号
                    if let unit = extractedUnit, !unit.isEmpty {
                        deliveryPoint.unitNumber = unit
                    }

                    // 🎯 关键修复：自动保存时也要更新地址库
                    Task {
                        // 🔍 调试：检查数据库状态
                        let dbEnabled = UserAddressDatabase.shared.isEnabled
                        logInfo("🔍 AddressEditBottomSheet - autoSave数据库状态: \(dbEnabled ? "启用" : "禁用")")

                        // 如果用户修改了地址，说明原地址可能有问题，标记原地址需要重新验证
                        if deliveryPoint.primaryAddress != address {
                            await AddressQualityValidator.shared.markAddressForRevalidation(deliveryPoint.primaryAddress)
                            logInfo("🔄 AddressEditBottomSheet - autoSave已标记原地址需要重新验证: \(deliveryPoint.primaryAddress)")
                        }

                        await AddressDatabaseUpdateService.shared.updateAddressAfterUserCorrection(
                            originalAddress: deliveryPoint.primaryAddress,
                            correctedAddress: address,
                            coordinate: coordinateResult
                        )
                        logInfo("✅ AddressEditBottomSheet - autoSave已通过服务更新地址库: \(address)")
                    }

                case .failed(_, _):
                    break // 保持原地址
                }
            }
        }

        // 🎯 清除所有验证错误和警告（用户选择地址后）
        deliveryPoint.geocodingWarning = nil
        deliveryPoint.addressValidationScore = 100.0  // 设置为满分
        deliveryPoint.addressValidationIssues = nil   // 清除验证问题

        // 重新验证坐标
        _ = deliveryPoint.validateCoordinates()

        // 基于用户位置重新验证
        if let userLocation = locationManager.userLocation {
            deliveryPoint.validateLocationBasedOnUserPosition(userLocation)
        }

        // 保存更改并退出
        do {
            try modelContext.save()

            // 通知RouteViewModel检查优化按钮可用性
            RouteViewModel.shared.checkOptimizationAvailabilityAfterAddressUpdate()

            // 🚀 自动退出界面
            dismiss()
        } catch {
            // 如果保存失败，显示错误信息但不退出
            alertMessage = String(format: "save_failure".localized, error.localizedDescription)
            showingAlert = true
        }
    }

    // MARK: - 地址州修复辅助方法

    /// 检查并修复缺少州信息的地址
    /// - Parameter address: 原始地址
    /// - Returns: 修复后的地址（如果不需要修复则返回原地址）
    private func fixAddressStateIfNeeded(_ address: String) async -> String {
        // 使用 AddressStateFixService 检测并修复地址
        if let fixedAddress = await AddressStateFixService.shared.detectAndFixMissingState(for: address) {
            Logger.info("🔧 AddressEditBottomSheet - 地址州修复成功: \(address) -> \(fixedAddress)", type: .location)
            return fixedAddress
        }

        // 如果不需要修复，返回原地址
        return address
    }

    // MARK: - 简化地址处理（移除复杂的地址解析逻辑）
}
