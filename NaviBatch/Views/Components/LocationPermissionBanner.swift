import SwiftUI
import CoreLocation

/// 位置权限横幅提示
/// 在主界面顶部显示位置权限状态和引导
struct LocationPermissionBanner: View {
    @StateObject private var locationManager = LocationManager.shared
    @State private var showingLocationSettings = false
    @State private var isExpanded = false
    
    var body: some View {
        Group {
            if shouldShowBanner {
                VStack(spacing: 0) {
                    // 主要横幅内容
                    HStack(spacing: 12) {
                        // 状态图标
                        Image(systemName: statusIcon)
                            .foregroundColor(statusColor)
                            .font(.system(size: 16, weight: .medium))
                        
                        // 状态文本
                        VStack(alignment: .leading, spacing: 2) {
                            Text(statusTitle)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.primary)
                            
                            Text(statusMessage)
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                                .lineLimit(isExpanded ? nil : 1)
                        }
                        
                        Spacer()
                        
                        // 操作按钮
                        if canTakeAction {
                            But<PERSON>(actionButtonTitle) {
                                handleAction()
                            }
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(statusColor)
                            .cornerRadius(6)
                        }
                        
                        // 展开/收起按钮
                        Button {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isExpanded.toggle()
                            }
                        } label: {
                            Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(bannerBackgroundColor)
                    
                    // 展开的详细信息
                    if isExpanded {
                        VStack(alignment: .leading, spacing: 8) {
                            Divider()
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("当前状态详情:")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.primary)
                                
                                HStack {
                                    Text("位置权限:")
                                    Spacer()
                                    Text(authorizationStatusText)
                                        .foregroundColor(statusColor)
                                }
                                .font(.system(size: 11))
                                
                                HStack {
                                    Text("位置来源:")
                                    Spacer()
                                    Text(locationManager.diagnoseLocationSource())
                                        .foregroundColor(.secondary)
                                }
                                .font(.system(size: 11))
                                
                                if let location = locationManager.userLocation {
                                    HStack {
                                        Text("当前坐标:")
                                        Spacer()
                                        Text(String(format: "%.4f, %.4f", location.latitude, location.longitude))
                                            .foregroundColor(.secondary)
                                            .font(.system(.caption, design: .monospaced))
                                    }
                                    .font(.system(size: 11))
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.bottom, 12)
                        }
                        .background(bannerBackgroundColor)
                    }
                }
                .overlay(
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color(.separator))
                        .opacity(0.5),
                    alignment: .bottom
                )
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: shouldShowBanner)
        .sheet(isPresented: $showingLocationSettings) {
            LocationPermissionGuideView()
        }
    }
    
    // MARK: - 计算属性
    
    private var shouldShowBanner: Bool {
        // 总是显示横幅，让用户了解位置状态
        return true
    }
    
    private var statusIcon: String {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return "location.circle"
        case .denied, .restricted:
            return "location.slash"
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                return "location.circle.fill"
            } else {
                return "location.fill"
            }
        @unknown default:
            return "location.circle"
        }
    }
    
    private var statusColor: Color {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return .orange
        case .denied, .restricted:
            return .red
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                return .orange
            } else {
                return .green
            }
        @unknown default:
            return .gray
        }
    }
    
    private var statusTitle: String {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return "需要位置权限"
        case .denied, .restricted:
            return "位置权限被拒绝"
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                return "使用默认位置"
            } else {
                return "GPS定位正常"
            }
        @unknown default:
            return "位置状态未知"
        }
    }
    
    private var statusMessage: String {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return "NaviBatch需要位置权限来提供路线规划服务"
        case .denied, .restricted:
            return "无法获取您的位置，将使用默认位置进行路线规划"
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                return "正在使用基于设备区域的默认位置"
            } else {
                return "正在使用您的实际GPS位置"
            }
        @unknown default:
            return "位置服务状态异常"
        }
    }
    
    private var canTakeAction: Bool {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return true
        case .denied, .restricted:
            return true
        case .authorizedWhenInUse, .authorizedAlways:
            return locationManager.diagnoseLocationSource().contains("默认")
        @unknown default:
            return false
        }
    }
    
    private var actionButtonTitle: String {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return "授权"
        case .denied, .restricted:
            return "设置"
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                return "重试"
            } else {
                return ""
            }
        @unknown default:
            return "修复"
        }
    }
    
    private var authorizationStatusText: String {
        switch locationManager.authorizationStatus {
        case .notDetermined: return "未确定"
        case .denied: return "已拒绝"
        case .restricted: return "受限制"
        case .authorizedWhenInUse: return "使用时允许"
        case .authorizedAlways: return "始终允许"
        @unknown default: return "未知"
        }
    }
    
    private var bannerBackgroundColor: Color {
        switch locationManager.authorizationStatus {
        case .denied, .restricted:
            return Color.red.opacity(0.1)
        case .notDetermined:
            return Color.orange.opacity(0.1)
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                return Color.orange.opacity(0.1)
            } else {
                return Color.green.opacity(0.1)
            }
        @unknown default:
            return Color.gray.opacity(0.1)
        }
    }
    
    // MARK: - 方法
    
    private func handleAction() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestLocationPermission()
        case .denied, .restricted:
            showingLocationSettings = true
        case .authorizedWhenInUse, .authorizedAlways:
            if locationManager.diagnoseLocationSource().contains("默认") {
                locationManager.forceRefreshRealLocation()
            }
        @unknown default:
            showingLocationSettings = true
        }
    }
}

#Preview("LocationPermissionBanner") {
    VStack {
        LocationPermissionBanner()
        Spacer()
    }
}
