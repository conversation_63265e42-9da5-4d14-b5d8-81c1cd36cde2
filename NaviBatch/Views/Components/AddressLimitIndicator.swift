import SwiftUI

/// 地址限制指示器组件
/// 用于显示当前路线的地址数量和限制状态
struct AddressLimitIndicator: View {
    let currentCount: Int
    let maxCount: Int
    let showUpgradeButton: Bool
    var onUpgrade: (() -> Void)? = nil

    private var percentage: Double {
        Double(currentCount) / Double(maxCount)
    }

    private var progressColor: Color {
        if percentage < 0.7 {
            return .blue
        } else if percentage < 0.9 {
            return .orange
        } else {
            return .red
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("地址数量: \(currentCount)/\(maxCount)")
                    .font(.caption)
                    .foregroundColor(percentage >= 0.9 ? .red : .primary)

                Spacer()

                if showUpgradeButton && percentage >= 0.7 {
                    Button(action: {
                        onUpgrade?()
                    }) {
                        Text("升级")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.blue)
                            .cornerRadius(4)
                    }
                }
            }

            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: geometry.size.width, height: 6)
                        .cornerRadius(3)

                    // 进度
                    Rectangle()
                        .fill(progressColor)
                        .frame(width: min(CGFloat(percentage) * geometry.size.width, geometry.size.width), height: 6)
                        .cornerRadius(3)
                }
            }
            .frame(height: 6)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color(.systemBackground).opacity(0.8))
        .cornerRadius(8)
    }
}

// 扩展View以便于添加地址限制指示器
extension View {
    func addressLimitIndicator(
        currentCount: Int,
        maxCount: Int,
        showUpgradeButton: Bool = true,
        onUpgrade: (() -> Void)? = nil
    ) -> some View {
        self.overlay(
            VStack {
                AddressLimitIndicator(
                    currentCount: currentCount,
                    maxCount: maxCount,
                    showUpgradeButton: showUpgradeButton,
                    onUpgrade: onUpgrade
                )
                .padding(.horizontal)
                .padding(.top, 8)

                Spacer()
            },
            alignment: .top
        )
    }
}

#Preview("低使用率") {
    AddressLimitIndicator(currentCount: 5, maxCount: 15, showUpgradeButton: true)
        .padding()
        .frame(width: 300, height: 100)
}

#Preview("高使用率") {
    AddressLimitIndicator(currentCount: 12, maxCount: 15, showUpgradeButton: true)
        .padding()
        .frame(width: 300, height: 100)
}

#Preview("已达上限") {
    AddressLimitIndicator(currentCount: 15, maxCount: 15, showUpgradeButton: true)
        .padding()
        .frame(width: 300, height: 100)
}
