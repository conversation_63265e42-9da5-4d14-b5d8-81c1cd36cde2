import SwiftUI
import MapKit
import CoreLocation

/// 统一的地址输入组件，整合搜索和扫描功能
struct AddressInputView: View {
    // 输入和输出参数
    @Binding var address: String
    @Binding var coordinate: CLLocationCoordinate2D?
    var onAddressSelected: ((String, CLLocationCoordinate2D) -> Void)?

    // 内部状态
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var searchCompleter = MKLocalSearchCompleter()
    @State private var completerDelegate: AddressCompleterDelegate? = nil
    @State private var isShowingScanner: Bool = false
    @State private var scannedCode: String = ""
    @State private var errorMessage: String? = nil
    @State private var isProcessing: Bool = false

    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏 - 统一样式
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)

                TextField("enter_or_search_address".localized, text: $address)
                    .autocorrectionDisabled()
                    .padding(.vertical, 10)
                    .onChange(of: address) { oldValue, newValue in
                        updateSearchResults(query: newValue)
                    }

                if !address.isEmpty {
                    Button(action: {
                        address = ""
                        searchResults = []
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }



                // 扫描按钮 - 使用扫描图标而非相机图标
                Button(action: {
                    isShowingScanner = true
                }) {
                    Image(systemName: "qrcode.viewfinder")
                        .foregroundColor(.blue)
                }
                .padding(.trailing, 8)
            }
            .background(Color(.systemGray5).opacity(0.5))
            .cornerRadius(10)
            .padding(.horizontal)

            // 错误信息
            if let error = errorMessage {
                Text(error)
                    .foregroundColor(.red)
                    .font(.caption)
                    .padding(.horizontal)
                    .padding(.top, 4)
            }

            // 搜索结果计数
            if !address.isEmpty && !searchResults.isEmpty {
                HStack {
                    Spacer()
                    Text("search_results_count".localized(with: searchResults.count))
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.trailing)
                        .padding(.top, 4)
                }
            }

            // 搜索结果列表
            if !searchResults.isEmpty {
                List {
                    ForEach(searchResults, id: \.self) { result in
                        Button(action: {
                            selectAddress(result)
                        }) {
                            AddressResultRow(result: result)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .listStyle(PlainListStyle())
                .background(Color.clear)
                .frame(height: min(CGFloat(searchResults.count) * 60, 300))
            } else if !address.isEmpty && !isProcessing {
                Text("no_matching_addresses".localized)
                    .foregroundColor(.secondary)
                    .padding()
            }

            // 处理中指示器
            if isProcessing {
                HStack {
                    Spacer()
                    ProgressView()
                        .padding()
                    Spacer()
                }
            }
        }
        .sheet(isPresented: $isShowingScanner) {
            AddressScannerSheet { address, coordinate in
                // 🧹 清理地址元数据，只显示纯净地址给用户
                let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
                let cleanAddress = separatedInfo.address.trimmingCharacters(in: .whitespacesAndNewlines)

                self.address = cleanAddress  // 使用清理后的地址
                self.coordinate = coordinate
            }
        }
        .onAppear {
            setupSearchCompleter()
        }
    }

    // MARK: - 搜索相关方法

    /// 设置地址搜索器
    private func setupSearchCompleter() {
        completerDelegate = AddressCompleterDelegate()
        completerDelegate?.onResultsUpdated = { results in
            DispatchQueue.main.async {
                self.searchResults = results
                logInfo(String(format: "AddressInputView - %@", String(format: "log_search_results_updated".localized, results.count)))
            }
        }

        searchCompleter.delegate = completerDelegate
        searchCompleter.resultTypes = .address

        // 🌍 使用全球搜索区域，避免地理位置限制
        let globalRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 0, longitude: 0),
            span: MKCoordinateSpan(latitudeDelta: 180, longitudeDelta: 360)
        )
        searchCompleter.region = globalRegion
        logInfo("AddressInputView - 设置全球搜索区域")
    }

    /// 更新搜索结果 - 使用语言环境感知搜索
    private func updateSearchResults(query: String) {
        if !query.isEmpty {
            // 使用语言环境感知搜索服务
            LocaleAwareAddressSearchService.shared.performSearch(query: query) { results in
                DispatchQueue.main.async {
                    self.searchResults = results
                    logInfo("AddressInputView - 语言环境感知搜索完成: \(results.count)个结果")
                }
            }
            logInfo(String(format: "AddressInputView - %@", String(format: "log_search_text_updated".localized, query)))
        } else {
            searchResults = []
        }
    }

    /// 选择地址 - 使用全球地址处理器
    private func selectAddress(_ result: MKLocalSearchCompletion) {
        isProcessing = true
        errorMessage = nil

        // 构建完整地址字符串
        let fullAddress = result.subtitle.isEmpty ? result.title : "\(result.title), \(result.subtitle)"

        // 使用全球地址处理器处理地址
        Task {
            let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(fullAddress)

            await MainActor.run {
                self.isProcessing = false

                switch globalResult {
                case .success(_, let formattedAddress, let coordinate, _, let strategy, _):
                    logInfo("🌍 AddressInputView - 全球地址处理成功: \(strategy) - \(formattedAddress)")

                    // 更新绑定值
                    self.address = formattedAddress
                    self.coordinate = coordinate

                    // 清空搜索结果
                    self.searchResults = []

                    // 立即调用回调，实现选中即保存
                    self.onAddressSelected?(formattedAddress, coordinate)

                    // 添加触觉反馈，表示已选中
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()

                case .failed(_, let reason):
                    logError("🌍 AddressInputView - 全球地址处理失败: \(reason)，降级到原有逻辑")

                    // 降级到原有逻辑
                    self.fallbackToOriginalSearch(result)

                    // 🚨 检查并提示问题地址
                    Task {
                        // 延迟一下让地址处理完成
                        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                        await MainActor.run {
                            ProblemAddressCollector.shared.checkAndShowProblemAddresses(source: "地址搜索输入")
                        }
                    }
                }
            }
        }
    }

    /// 降级到原有搜索逻辑
    private func fallbackToOriginalSearch(_ result: MKLocalSearchCompletion) {
        isProcessing = true

        // 创建搜索请求
        let searchRequest = MKLocalSearch.Request(completion: result)
        let search = MKLocalSearch(request: searchRequest)

        // 执行搜索
        search.start { response, error in
            DispatchQueue.main.async {
                self.isProcessing = false

                if let error = error {
                    self.errorMessage = "search_address_failed".localized(with: error.localizedDescription)
                    logError(String(format: "AddressInputView - %@", String(format: "log_address_search_failed".localized, error.localizedDescription)))
                    return
                }

                guard let response = response else {
                    self.errorMessage = "address_search_no_response".localized
                    logError("AddressInputView - " + "log_address_search_no_response".localized)
                    return
                }

                guard let mapItem = response.mapItems.first else {
                    self.errorMessage = "cannot_get_address_coordinates".localized
                    logError("AddressInputView - " + "log_cannot_get_address_coordinates".localized)
                    return
                }

                // 格式化地址
                let streetName = result.title
                let locationDetails = result.subtitle
                let formattedAddress = "\(streetName)\n\(locationDetails)"
                let coordinate = mapItem.placemark.coordinate

                logInfo(String(format: "AddressInputView - %@", String(format: "log_address_selected".localized, formattedAddress, coordinate.latitude, coordinate.longitude)))

                // 更新绑定值
                self.address = formattedAddress
                self.coordinate = coordinate

                // 清空搜索结果
                self.searchResults = []

                // 立即调用回调，实现选中即保存
                self.onAddressSelected?(formattedAddress, coordinate)

                // 添加触觉反馈，表示已选中
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()
            }
        }
    }









    // MARK: - 地理编码辅助方法

    /// 地理编码辅助方法
    private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
        // 如果地址为空，直接返回 nil
        if address.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            logError("AddressInputView - 地理编码失败: 地址为空")
            return nil
        }

        logInfo("AddressInputView - 开始地理编码地址: \(address)")

        // 🎯 使用智能地理编码候选地址
        let candidates = AddressStandardizer.generateGeocodingCandidates(address)
        let geocoder = CLGeocoder()

        for (index, candidate) in candidates.enumerated() {
            do {
                logInfo("AddressInputView - 尝试地理编码候选地址[\(index)]: '\(candidate)'")

                // 添加超时处理
                let task = Task {
                    return try await geocoder.geocodeAddressString(candidate)
                }

                // 设置 5 秒超时 - 优化用户体验
                let placemarks = try await withTimeout(seconds: 5) { try await task.value }

                logInfo("AddressInputView - 候选地址[\(index)]地理编码返回了 \(placemarks.count) 个结果")

                // 打印所有结果的详细信息以便调试
                for (resultIndex, placemark) in placemarks.enumerated() {
                    if let location = placemark.location?.coordinate {
                        let country = placemark.country ?? "未知国家"
                        let locality = placemark.locality ?? "未知城市"
                        logInfo("AddressInputView - 候选地址[\(index)]结果[\(resultIndex)]: \(candidate) -> (\(location.latitude), \(location.longitude)), 国家: \(country), 城市: \(locality)")
                    }
                }

                if let placemark = placemarks.first, let location = placemark.location?.coordinate {
                    let formattedAddress = [placemark.name, placemark.locality, placemark.administrativeArea, placemark.country]
                        .compactMap { $0 }
                        .joined(separator: ", ")
                    let country = placemark.country ?? "未知国家"

                    logInfo("AddressInputView - 候选地址[\(index)]地理编码成功: \(candidate) -> \(formattedAddress), 坐标: \(location.latitude), \(location.longitude), 国家: \(country)")

                    if candidate != address {
                        logInfo("AddressInputView - 使用了扩展后的地址: '\(address)' -> '\(candidate)'")
                    }

                    return location
                } else {
                    logError("AddressInputView - 候选地址[\(index)]地理编码成功但没有返回坐标: \(candidate)")
                }

            } catch let timeoutError as TimeoutError {
                logError("AddressInputView - 候选地址[\(index)]地理编码超时: \(candidate), 错误: \(timeoutError.localizedDescription)")
                continue // 尝试下一个候选地址
            } catch {
                logError("AddressInputView - 候选地址[\(index)]地理编码失败: \(candidate), 错误: \(error.localizedDescription)")
                continue // 尝试下一个候选地址
            }
        }

        // 所有候选地址都失败了
        logError("AddressInputView - 所有候选地址都无法地理编码: \(address)")
        return nil
    }

    // 超时错误
    struct TimeoutError: Error, LocalizedError {
        let seconds: TimeInterval

        var errorDescription: String? {
            return "Operation timed out after \(seconds) seconds"
        }
    }

    // 添加超时功能的辅助方法
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        return try await withThrowingTaskGroup(of: T.self) { group in
            // 添加主操作
            group.addTask {
                return try await operation()
            }

            // 添加超时任务
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                throw TimeoutError(seconds: seconds)
            }

            // 返回第一个完成的任务的结果
            let result = try await group.next()!

            // 取消其他任务
            group.cancelAll()

            return result
        }
    }
}

// MARK: - 地址结果行组件

/// 地址搜索结果行组件
struct AddressResultRow: View {
    let result: MKLocalSearchCompletion

    var body: some View {
        HStack {
            Circle()
                .fill(Color.red)
                .frame(width: 24, height: 24)
                .overlay(
                    Text("i")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                )

            VStack(alignment: .leading, spacing: 3) {
                Text(result.title)
                    .foregroundColor(.primary)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)

                Text(result.subtitle)
                    .foregroundColor(.secondary)
                    .font(.caption)
                    .lineLimit(1)
            }
            .padding(.leading, 8)

            Spacer()

            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
                .font(.caption)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 地址搜索器代理

/// 地址搜索器代理
class AddressCompleterDelegate: NSObject, MKLocalSearchCompleterDelegate {
    var onResultsUpdated: (([MKLocalSearchCompletion]) -> Void)?

    func completerDidUpdateResults(_ completer: MKLocalSearchCompleter) {
        let results = completer.results
        print("AddressCompleterDelegate - 搜索结果更新: \(results.count) 个结果")

        // 如果有结果，将前15个结果传递给回调函数
        if !results.isEmpty {
            let limitedResults = Array(results.prefix(15))
            onResultsUpdated?(limitedResults)
        } else {
            onResultsUpdated?([])
        }
    }

    func completer(_ completer: MKLocalSearchCompleter, didFailWithError error: Error) {
        print("AddressCompleterDelegate - 地址搜索失败: \(error.localizedDescription)")
        onResultsUpdated?([]) // 失败时清空结果
    }
}

// MARK: - 预览

#Preview("AddressInputView") {
    struct PreviewWrapper: View {
        @State private var address: String = ""
        @State private var coordinate: CLLocationCoordinate2D? = nil

        var body: some View {
            NavigationStack {
                VStack {
                    AddressInputView(
                        address: $address,
                        coordinate: $coordinate,
                        onAddressSelected: { address, coordinate in
                            print("选择了地址: \(address), 坐标: \(coordinate.latitude), \(coordinate.longitude)")
                        }
                    )

                    if let coordinate = coordinate {
                        VStack {
                            Text("已选择地址:")
                                .font(.headline)
                                .padding(.top)

                            Text(address)
                                .padding(.horizontal)

                            Text("坐标: \(coordinate.latitude), \(coordinate.longitude)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }

                    Spacer()
                }
                .navigationTitle("地址输入测试")
            }
        }
    }

    return PreviewWrapper()
}
