import SwiftUI

/// 可折叠的按钮组组件
/// 支持用户自定义常用/非常用按钮
struct CollapsibleButtonGroup: View {
    @Binding var isExpanded: Bool
    @State private var showCustomizationSheet = false
    
    // 按钮配置
    @AppStorage("favoriteButtons") private var favoriteButtonsData: Data = Data()
    @State private var favoriteButtons: Set<ButtonType> = []
    
    // 按钮动作回调
    let onSideToggle: () -> Void
    let onMapModeToggle: () -> Void
    let onSearch: () -> Void
    let onVisibilityToggle: () -> Void
    let onCounterTap: () -> Void
    
    // 外部状态
    let buttonsOnLeftSide: Bool
    let selectedMapMode: MapDisplayMode
    let showCompletedDeliveries: Bool
    let deliveryCount: Int
    
    var body: some View {
        VStack(spacing: 10) {
            // 主控制按钮（...）
            mainControlButton
            
            // 展开的按钮组
            if isExpanded {
                expandedButtons
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
            }
        }
        .onAppear {
            loadFavoriteButtons()
        }
    }
    
    // 主控制按钮
    private var mainControlButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                isExpanded.toggle()
            }
            
            // 触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.impactOccurred()
        }) {
            ZStack {
                Circle()
                    .fill(Color.black)
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                
                Image(systemName: isExpanded ? "xmark" : "ellipsis")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                    .rotationEffect(.degrees(isExpanded ? 0 : 0))
                    .animation(.spring(response: 0.3), value: isExpanded)
            }
        }
        .frame(width: 58, height: 58)
        .contentShape(Rectangle())
        .buttonStyle(PlainButtonStyle())
    }
    
    // 展开的按钮组
    private var expandedButtons: some View {
        VStack(spacing: 10) {
            // 显示常用按钮
            ForEach(Array(favoriteButtons.sorted(by: { $0.order < $1.order })), id: \.self) { buttonType in
                createButton(for: buttonType)
            }
            
            // 自定义按钮
            customizeButton
        }
    }
    
    // 创建具体按钮
    @ViewBuilder
    private func createButton(for type: ButtonType) -> some View {
        switch type {
        case .sideToggle:
            circleButton(action: onSideToggle) {
                Image(systemName: buttonsOnLeftSide ? "arrow.right" : "arrow.left")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }
            
        case .mapMode:
            circleButton(action: onMapModeToggle) {
                Image(systemName: selectedMapMode.iconName)
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }
            
        case .search:
            circleButton(action: onSearch) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }
            
        case .visibility:
            circleButton(action: onVisibilityToggle) {
                Image(systemName: showCompletedDeliveries ? "eye" : "eye.slash")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }
            
        case .counter:
            circleButton(action: onCounterTap) {
                Text("\(deliveryCount)")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)
            }
        }
    }
    
    // 自定义按钮
    private var customizeButton: some View {
        Button(action: {
            showCustomizationSheet = true
        }) {
            ZStack {
                Circle()
                    .fill(Color.gray.opacity(0.8))
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                
                Image(systemName: "gear")
                    .font(.system(size: 16))
                    .foregroundColor(.white)
            }
        }
        .frame(width: 58, height: 58)
        .contentShape(Rectangle())
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showCustomizationSheet) {
            ButtonCustomizationSheet(
                favoriteButtons: $favoriteButtons,
                onSave: saveFavoriteButtons
            )
        }
    }
    
    // 统一的圆形按钮样式
    private func circleButton(
        backgroundColor: Color = .black,
        action: @escaping () -> Void,
        @ViewBuilder content: () -> some View
    ) -> some View {
        Button(action: {
            action()
            
            // 触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.impactOccurred()
        }) {
            ZStack {
                Circle()
                    .fill(backgroundColor)
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                
                content()
            }
        }
        .frame(width: 58, height: 58)
        .contentShape(Rectangle())
        .buttonStyle(PlainButtonStyle())
    }
    
    // 加载用户偏好
    private func loadFavoriteButtons() {
        if let decoded = try? JSONDecoder().decode(Set<ButtonType>.self, from: favoriteButtonsData) {
            favoriteButtons = decoded
        } else {
            // 默认显示前3个按钮
            favoriteButtons = [.sideToggle, .mapMode, .search]
        }
    }
    
    // 保存用户偏好
    private func saveFavoriteButtons() {
        if let encoded = try? JSONEncoder().encode(favoriteButtons) {
            favoriteButtonsData = encoded
        }
    }
}

// 按钮类型枚举
enum ButtonType: String, CaseIterable, Codable, Hashable {
    case sideToggle = "sideToggle"
    case mapMode = "mapMode"
    case search = "search"
    case visibility = "visibility"
    case counter = "counter"
    
    var displayName: String {
        switch self {
        case .sideToggle:
            return "collapsible_button_side_toggle".localized
        case .mapMode:
            return "collapsible_button_map_mode".localized
        case .search:
            return "collapsible_button_search".localized
        case .visibility:
            return "collapsible_button_visibility".localized
        case .counter:
            return "collapsible_button_counter".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .sideToggle:
            return "arrow.left.arrow.right"
        case .mapMode:
            return "map"
        case .search:
            return "magnifyingglass"
        case .visibility:
            return "eye"
        case .counter:
            return "number"
        }
    }
    
    var order: Int {
        switch self {
        case .sideToggle:
            return 0
        case .mapMode:
            return 1
        case .search:
            return 2
        case .visibility:
            return 3
        case .counter:
            return 4
        }
    }
}

// 按钮自定义界面
struct ButtonCustomizationSheet: View {
    @Binding var favoriteButtons: Set<ButtonType>
    let onSave: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("collapsible_button_customization_title".localized)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding(.top)

                Text("collapsible_button_customization_subtitle".localized)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                VStack(spacing: 16) {
                    ForEach(ButtonType.allCases, id: \.self) { buttonType in
                        ButtonSelectionRow(
                            buttonType: buttonType,
                            isSelected: favoriteButtons.contains(buttonType),
                            canSelect: favoriteButtons.count < 4 || favoriteButtons.contains(buttonType)
                        ) { isSelected in
                            if isSelected {
                                favoriteButtons.insert(buttonType)
                            } else {
                                favoriteButtons.remove(buttonType)
                            }
                        }
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("collapsible_button_customization_navigation_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("save".localized) {
                        onSave()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// 按钮选择行
struct ButtonSelectionRow: View {
    let buttonType: ButtonType
    let isSelected: Bool
    let canSelect: Bool
    let onToggle: (Bool) -> Void

    var body: some View {
        Button(action: {
            if canSelect || isSelected {
                onToggle(!isSelected)
            }
        }) {
            HStack(spacing: 16) {
                // 按钮图标
                ZStack {
                    Circle()
                        .fill(isSelected ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 40, height: 40)

                    Image(systemName: buttonType.iconName)
                        .font(.system(size: 18))
                        .foregroundColor(isSelected ? .white : .gray)
                }

                // 按钮信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(buttonType.displayName)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text(buttonDescription(for: buttonType))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 选择状态
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(isSelected ? .blue : .gray.opacity(0.5))
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
            .opacity(canSelect || isSelected ? 1.0 : 0.5)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!canSelect && !isSelected)
    }

    private func buttonDescription(for type: ButtonType) -> String {
        switch type {
        case .sideToggle:
            return "collapsible_button_side_toggle_description".localized
        case .mapMode:
            return "collapsible_button_map_mode_description".localized
        case .search:
            return "collapsible_button_search_description".localized
        case .visibility:
            return "collapsible_button_visibility_description".localized
        case .counter:
            return "collapsible_button_counter_description".localized
        }
    }
}

#Preview {
    @Previewable @State var isExpanded = false

    ZStack {
        Color.gray.opacity(0.2).ignoresSafeArea()

        VStack {
            Spacer()
            HStack {
                Spacer()
                CollapsibleButtonGroup(
                    isExpanded: $isExpanded,
                    onSideToggle: { print("Side toggle") },
                    onMapModeToggle: { print("Map mode toggle") },
                    onSearch: { print("Search") },
                    onVisibilityToggle: { print("Visibility toggle") },
                    onCounterTap: { print("Counter tap") },
                    buttonsOnLeftSide: false,
                    selectedMapMode: .driving,
                    showCompletedDeliveries: true,
                    deliveryCount: 119
                )
                .padding(.trailing, 16)
                .padding(.bottom, 100)
            }
        }
    }
}
