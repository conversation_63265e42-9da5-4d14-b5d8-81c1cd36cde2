import SwiftUI

/// AI网络错误恢复视图
/// 专门处理AI服务的网络连接问题
struct AINetworkErrorView: View {
    let error: Error
    let onRetry: () -> Void
    let onSwitchToOCR: () -> Void
    let onDismiss: () -> Void
    
    @State private var isCheckingNetwork = false
    @State private var networkStatus: Bool?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 错误说明
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Image(systemName: "wifi.exclamationmark")
                                .foregroundColor(.orange)
                                .font(.title)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("AI服务网络错误")
                                    .font(.headline)
                                    .foregroundColor(.orange)
                                
                                Text("连接Firebase AI服务时出现问题")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // 错误详情
                        VStack(alignment: .leading, spacing: 8) {
                            Text("错误详情:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text(error.localizedDescription)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(8)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }
                        
                        // 网络状态检查
                        networkStatusView
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 2)
                    
                    // 解决方案
                    VStack(alignment: .leading, spacing: 16) {
                        Text("解决方案")
                            .font(.headline)
                        
                        // 方案1：重试AI识别
                        SolutionCard(
                            icon: "arrow.clockwise",
                            title: "重试AI识别",
                            description: "系统已自动重试3次，您可以再次尝试",
                            action: onRetry
                        )
                        
                        // 方案2：使用离线OCR
                        SolutionCard(
                            icon: "doc.text.viewfinder",
                            title: "使用离线OCR模式",
                            description: "不需要网络连接，使用设备本地OCR识别",
                            action: onSwitchToOCR,
                            isRecommended: true
                        )
                        
                        // 方案3：检查网络设置
                        SolutionCard(
                            icon: "gear",
                            title: "检查网络设置",
                            description: "打开系统设置检查WiFi或移动数据连接",
                            action: {
                                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                                    UIApplication.shared.open(settingsUrl)
                                }
                            }
                        )
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 2)
                    
                    // 技术说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("技术说明")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            InfoRow(
                                icon: "brain.head.profile",
                                text: "AI识别需要连接Google Firebase服务"
                            )
                            
                            InfoRow(
                                icon: "doc.text.viewfinder",
                                text: "OCR模式完全离线，不受网络影响"
                            )
                            
                            InfoRow(
                                icon: "arrow.clockwise",
                                text: "系统已自动重试，包含递增延迟机制"
                            )
                            
                            InfoRow(
                                icon: "shield.checkered",
                                text: "网络错误不会影响已识别的数据"
                            )
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("网络错误")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        onDismiss()
                    }
                }
            }
        }
        .onAppear {
            checkNetworkStatus()
        }
    }
    
    // 网络状态检查视图
    private var networkStatusView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("网络状态检查:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Button("重新检查") {
                    checkNetworkStatus()
                }
                .font(.caption)
                .disabled(isCheckingNetwork)
            }
            
            HStack {
                if isCheckingNetwork {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("检查中...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else if let status = networkStatus {
                    Image(systemName: status ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .foregroundColor(status ? .green : .red)
                    
                    Text(status ? "网络连接正常" : "网络连接异常")
                        .font(.caption)
                        .foregroundColor(status ? .green : .red)
                } else {
                    Image(systemName: "questionmark.circle")
                        .foregroundColor(.gray)
                    Text("未检查")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Spacer()
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    private func checkNetworkStatus() {
        isCheckingNetwork = true
        networkStatus = nil
        
        Task {
            let status = await FirebaseAIService.shared.checkNetworkStatus()
            
            await MainActor.run {
                networkStatus = status
                isCheckingNetwork = false
            }
        }
    }
}

// 信息行组件
struct InfoRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 16)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// 解决方案卡片组件
struct SolutionCard: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void
    let isRecommended: Bool
    
    init(icon: String, title: String, description: String, action: @escaping () -> Void, isRecommended: Bool = false) {
        self.icon = icon
        self.title = title
        self.description = description
        self.action = action
        self.isRecommended = isRecommended
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isRecommended ? .orange : .blue)
                    .frame(width: 32)
                
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(title)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        if isRecommended {
                            Text("推荐")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange)
                                .cornerRadius(4)
                        }
                        
                        Spacer()
                    }
                    
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AINetworkErrorView(
        error: NSError(domain: NSURLErrorDomain, code: NSURLErrorNetworkConnectionLost, userInfo: [NSLocalizedDescriptionKey: "The network connection was lost."]),
        onRetry: { print("重试") },
        onSwitchToOCR: { print("切换到OCR") },
        onDismiss: { print("关闭") }
    )
}
