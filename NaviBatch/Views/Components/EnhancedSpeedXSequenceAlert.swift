import SwiftUI

/// 增强版SpeedX序号缺失警告组件 - 集成AI地址预测功能
struct EnhancedSpeedXSequenceAlert: View {
    let missingNumbers: [Int]
    let incompleteNumbers: [Int]
    let existingAddresses: [String]  // 现有地址用于AI分析
    let onRetakePhoto: () -> Void
    let onSupplementPhoto: () -> Void  // 补充截图回调
    let onContinue: () -> Void
    let onCancel: () -> Void
    let onAddressConfirmed: (Int, String) -> Void  // 确认AI预测地址的回调

    @State private var showingAIPrediction = false
    @State private var aiPredictions: [AddressPatternAnalyzer.PredictedAddress] = []
    @State private var isAnalyzing = false
    @State private var analysisError: String?

    private let addressAnalyzer = AddressPatternAnalyzer()

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                    .font(.title2)

                Text(NSLocalizedString("speedx_sequence_check", comment: "SpeedX序号检查"))
                    .font(.headline)
                    .foregroundColor(.primary)
            }
            .padding(.top)

            // 分隔线
            Divider()

            // 内容
            VStack(alignment: .leading, spacing: 16) {
                if !missingNumbers.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                                .font(.caption)

                            Text(NSLocalizedString("missing_stops", comment: "缺失的停靠点:"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }

                        Text(String(format: NSLocalizedString("stops_not_found", comment: "停靠点未找到"), formatNumbers(missingNumbers)))
                            .font(.system(.subheadline, design: .monospaced))
                            .foregroundColor(.red)
                            .padding(.leading, 20)
                    }
                }

                if !incompleteNumbers.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "exclamationmark.circle.fill")
                                .foregroundColor(.orange)
                                .font(.caption)

                            Text(NSLocalizedString("incomplete_stops", comment: "不完整的停靠点:"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }

                        Text(String(format: NSLocalizedString("stops_truncated", comment: "停靠点信息被截断"), formatNumbers(incompleteNumbers)))
                            .font(.system(.subheadline, design: .monospaced))
                            .foregroundColor(.orange)
                            .padding(.leading, 20)
                    }
                }

                // 补充截图功能提示
                if !missingNumbers.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "camera.fill")
                                .foregroundColor(.green)
                                .font(.caption)

                            Text(NSLocalizedString("supplement_photo", comment: "补充截图:"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                        }

                        Text(String(format: NSLocalizedString("supplement_photo_description", comment: "上传包含缺失停靠点的截图，系统将只处理缺失的号码"), formatNumbers(missingNumbers)))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.leading, 20)
                    }
                }

                // AI预测功能提示
                if !missingNumbers.isEmpty && existingAddresses.count >= 2 {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "brain.head.profile")
                                .foregroundColor(.blue)
                                .font(.caption)

                            Text(NSLocalizedString("ai_prediction", comment: "AI智能预测:"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }

                        Text(NSLocalizedString("ai_prediction_description", comment: "基于现有地址模式，AI可以预测缺失停靠点的地址"))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.leading, 20)

                        if isAnalyzing {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text(NSLocalizedString("analyzing_pattern", comment: "正在分析地址模式..."))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.leading, 20)
                        }

                        if let error = analysisError {
                            Text(String(format: NSLocalizedString("analysis_failed", comment: "分析失败: %@"), error))
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.leading, 20)
                        }
                    }
                }

                // 建议
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.blue)
                            .font(.caption)

                        Text(NSLocalizedString("suggestions", comment: "建议:"))
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        if !missingNumbers.isEmpty && existingAddresses.count >= 2 {
                            Text(NSLocalizedString("try_ai_prediction", comment: "• 尝试AI智能预测缺失地址"))
                                .foregroundColor(.blue)
                        }
                        Text(NSLocalizedString("retake_photo_include_all", comment: "• 重新截图确保包含所有停靠点"))
                        Text(NSLocalizedString("check_missing_info", comment: "• 确保图片没有遗漏相关信息"))
                        Text(NSLocalizedString("process_in_batches", comment: "• 可以分段截图然后合并处理"))
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 20)
                }
            }
            .padding(.horizontal)

            // 按钮
            VStack(spacing: 12) {
                // 补充截图按钮（主要推荐）
                if !missingNumbers.isEmpty {
                    Button(action: onSupplementPhoto) {
                        HStack {
                            Image(systemName: "camera.badge.plus")
                            Text(NSLocalizedString("supplement_photo_recommended", comment: "补充截图 (推荐)"))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }

                // AI预测按钮（备选方案）
                if !missingNumbers.isEmpty && existingAddresses.count >= 2 && !isAnalyzing {
                    Button(action: startAIPrediction) {
                        HStack {
                            Image(systemName: "brain.head.profile")
                            Text(NSLocalizedString("ai_predict_address", comment: "AI智能预测地址"))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }

                // 重新截图按钮（完全重新开始）
                Button(action: onRetakePhoto) {
                    HStack {
                        Image(systemName: "camera.fill")
                        Text(NSLocalizedString("retake_photo_restart", comment: "重新截图 (全部重新开始)"))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }

                // 次要操作按钮
                HStack(spacing: 12) {
                    Button(action: onContinue) {
                        Text(NSLocalizedString("continue_use", comment: "继续使用"))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                    }

                    Button(action: onCancel) {
                        Text(NSLocalizedString("cancel", comment: "取消"))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 10)
        .padding(.horizontal, 20)
        .sheet(isPresented: $showingAIPrediction) {
            SmartAddressCompletionView(
                predictions: aiPredictions,
                onConfirmAddress: { stopNumber, address in
                    onAddressConfirmed(stopNumber, address)
                    showingAIPrediction = false
                },
                onUseAppleMaps: { stopNumber in
                    // Apple Maps functionality handled in SmartAddressCompletionView
                },
                onSkip: { stopNumber in
                    // Skip this stop
                },
                onCancel: {
                    showingAIPrediction = false
                }
            )
        }
    }

    // MARK: - Private Methods

    private func formatNumbers(_ numbers: [Int]) -> String {
        if numbers.count <= 5 {
            return numbers.map(String.init).joined(separator: ", ")
        } else {
            let first5 = Array(numbers.prefix(5))
            let formattedFirst5 = first5.map(String.init).joined(separator: ", ")
            return String(format: NSLocalizedString("and_more_stops", comment: "等更多停靠点"), formattedFirst5, numbers.count)
        }
    }

    private func startAIPrediction() {
        isAnalyzing = true
        analysisError = nil

        DispatchQueue.global(qos: .userInitiated).async {
            // 分析现有地址模式
            let patterns = addressAnalyzer.analyzeAddressPatterns(from: existingAddresses)

            guard !patterns.isEmpty else {
                DispatchQueue.main.async {
                    isAnalyzing = false
                    analysisError = NSLocalizedString("cannot_identify_pattern", comment: "无法识别地址模式")
                }
                return
            }

            // 预测缺失地址
            let predictions = addressAnalyzer.predictMissingAddresses(
                missingStopNumbers: missingNumbers,
                patterns: patterns
            )

            DispatchQueue.main.async {
                isAnalyzing = false

                if predictions.isEmpty {
                    analysisError = NSLocalizedString("cannot_predict_address", comment: "无法预测缺失地址")
                } else {
                    aiPredictions = predictions
                    showingAIPrediction = true
                }
            }
        }
    }
}

// 包装器视图，用于在SwiftUI中显示增强版SpeedX序号警告
struct EnhancedSpeedXSequenceAlertWrapper: ViewModifier {
    @Binding var isPresented: Bool
    let missingNumbers: [Int]
    let incompleteNumbers: [Int]
    let existingAddresses: [String]
    let onRetakePhoto: () -> Void
    let onSupplementPhoto: () -> Void
    let onContinue: () -> Void
    let onAddressConfirmed: (Int, String) -> Void

    func body(content: Content) -> some View {
        ZStack {
            content
                .blur(radius: isPresented ? 3 : 0)

            if isPresented {
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .transition(.opacity)

                EnhancedSpeedXSequenceAlert(
                    missingNumbers: missingNumbers,
                    incompleteNumbers: incompleteNumbers,
                    existingAddresses: existingAddresses,
                    onRetakePhoto: {
                        withAnimation {
                            isPresented = false
                        }
                        onRetakePhoto()
                    },
                    onSupplementPhoto: {
                        withAnimation {
                            isPresented = false
                        }
                        onSupplementPhoto()
                    },
                    onContinue: {
                        withAnimation {
                            isPresented = false
                        }
                        onContinue()
                    },
                    onCancel: {
                        withAnimation {
                            isPresented = false
                        }
                    },
                    onAddressConfirmed: onAddressConfirmed
                )
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut, value: isPresented)
    }
}

// 扩展View以添加增强版SpeedX序号警告
extension View {
    func enhancedSpeedXSequenceAlert(
        isPresented: Binding<Bool>,
        missingNumbers: [Int],
        incompleteNumbers: [Int] = [],
        existingAddresses: [String],
        onRetakePhoto: @escaping () -> Void,
        onSupplementPhoto: @escaping () -> Void,
        onContinue: @escaping () -> Void,
        onAddressConfirmed: @escaping (Int, String) -> Void
    ) -> some View {
        self.modifier(
            EnhancedSpeedXSequenceAlertWrapper(
                isPresented: isPresented,
                missingNumbers: missingNumbers,
                incompleteNumbers: incompleteNumbers,
                existingAddresses: existingAddresses,
                onRetakePhoto: onRetakePhoto,
                onSupplementPhoto: onSupplementPhoto,
                onContinue: onContinue,
                onAddressConfirmed: onAddressConfirmed
            )
        )
    }
}

#Preview("EnhancedSpeedXSequenceAlert") {
    ZStack {
        Color.gray.opacity(0.2).edgesIgnoringSafeArea(.all)

        EnhancedSpeedXSequenceAlert(
            missingNumbers: [4, 6],
            incompleteNumbers: [9],
            existingAddresses: [
                "397 Imperial Way Apt 238, Daly City, CA, 94015|5|SPXSF00567511770",
                "399 Imperial Way Apt 240, Daly City, CA, 94015|7|SPXSF00567511771"
            ],
            onRetakePhoto: {},
            onSupplementPhoto: {},
            onContinue: {},
            onCancel: {},
            onAddressConfirmed: { _, _ in }
        )
    }
}
