import SwiftUI
import CoreLocation

/// 网络错误恢复建议视图
/// 当地理编码因网络问题失败时，提供恢复建议
struct NetworkErrorRecoveryView: View {
    let failedAddresses: [String]
    let onRetry: () -> Void
    let onDismiss: () -> Void

    @StateObject private var addressDatabase = UserAddressDatabase.shared
    @State private var showingDatabaseStats = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 问题说明
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "wifi.exclamationmark")
                                .foregroundColor(.orange)
                                .font(.title2)

                            Text("网络连接问题")
                                .font(.headline)
                                .foregroundColor(.orange)
                        }

                        Text("由于网络连接不稳定，以下地址无法验证坐标：")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        // 失败的地址列表
                        VStack(alignment: .leading, spacing: 8) {
                            ForEach(failedAddresses, id: \.self) { address in
                                HStack {
                                    Image(systemName: "location.slash")
                                        .foregroundColor(.orange)
                                        .font(.caption)

                                    Text(address)
                                        .font(.caption)
                                        .lineLimit(2)

                                    Spacer()

                                    Text("0.000000, 0.000000")
                                        .font(.caption2)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(4)
                                }
                                .padding(.vertical, 4)
                                .padding(.horizontal, 12)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 2)

                    // 解决方案
                    VStack(alignment: .leading, spacing: 16) {
                        Text("解决方案")
                            .font(.headline)

                        // 方案1：重试
                        SolutionCard(
                            icon: "arrow.clockwise",
                            title: "重新尝试",
                            description: "检查网络连接后重新验证地址",
                            action: {
                                onRetry()
                            }
                        )

                        // 方案2：地址数据库状态
                        SolutionCard(
                            icon: "externaldrive.connected",
                            title: "地址数据库状态",
                            description: "查看本地地址数据库，了解已保存的地址数量",
                            action: {
                                showingDatabaseStats = true
                            }
                        )

                        // 方案3：手动更新
                        SolutionCard(
                            icon: "hand.point.up",
                            title: "手动更新坐标",
                            description: "导入地址后，可以在地址列表中手动编辑坐标",
                            action: {
                                onDismiss()
                            }
                        )
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 2)

                    // 技术说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("技术说明")
                            .font(.headline)

                        Text("• iPhone 16 Pro 可能有更完整的地址数据库，因此无需网络请求")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("• iPhone 12 mini 需要调用 Apple 地理编码服务，受网络状况影响")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("• 地址数据库会自动保存验证过的地址，避免重复网络请求")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("网络错误恢复")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        onDismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingDatabaseStats) {
            AddressDatabaseStatsView()
        }
    }
}

// SolutionCard已在AINetworkErrorView.swift中定义，这里不需要重复定义

/// 地址数据库统计视图
struct AddressDatabaseStatsView: View {
    @StateObject private var addressDatabase = UserAddressDatabase.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                Section(header: Text(NSLocalizedString("database_status", comment: ""))) {
                    HStack {
                        Text(NSLocalizedString("total_addresses", comment: ""))
                        Spacer()
                        Text("\(addressDatabase.databaseStats.totalCount)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text(NSLocalizedString("hit_rate", comment: ""))
                        Spacer()
                        Text(String(format: "%.1f%%", addressDatabase.databaseStats.hitRate * 100))
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text(NSLocalizedString("database_size", comment: ""))
                        Spacer()
                        Text(formatBytes(Int(addressDatabase.databaseStats.databaseSize)))
                            .foregroundColor(.secondary)
                    }
                }

                Section(header: Text(NSLocalizedString("most_used_addresses", comment: ""))) {
                    ForEach(addressDatabase.databaseStats.mostUsedAddresses, id: \.self) { address in
                        VStack(alignment: .leading, spacing: 4) {
                            Text(address)
                                .font(.subheadline)

                            HStack {
                                Text(NSLocalizedString("address", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Spacer()

                                Text(NSLocalizedString("frequently_used", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
            .navigationTitle("地址数据库")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
        .task {
            await addressDatabase.updateDatabaseStats()
        }
    }

    private func formatBytes(_ bytes: Int) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

#Preview("NetworkErrorRecoveryView") {
    NetworkErrorRecoveryView(
        failedAddresses: [
            "3420 Tupelo Dr, 95209",
            "4265 Maddie Cir, 95209"
        ],
        onRetry: {},
        onDismiss: {}
    )
}

#Preview("AddressDatabaseStatsView") {
    AddressDatabaseStatsView()
}
