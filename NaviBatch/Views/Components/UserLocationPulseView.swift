import SwiftUI
import MapKit

/// 自定义用户位置视图
/// 显示带有脉动动画的用户位置标记，模仿苹果地图官方样式
struct UserLocationPulseView: View {
    @State private var animationAmount: CGFloat = 1
    
    var body: some View {
        ZStack {
            // 外部脉动蓝色圆圈
            Circle()
                .fill(Color.blue.opacity(0.3))
                .frame(width: 40, height: 40)
                .scaleEffect(animationAmount)
                .opacity(2 - animationAmount)
                .animation(
                    .easeInOut(duration: 1.5)
                    .repeatForever(autoreverses: false),
                    value: animationAmount
                )
            
            // 白色圆环
            Circle()
                .stroke(Color.white, lineWidth: 2)
                .frame(width: 22, height: 22)
            
            // 内部蓝色实心点
            Circle()
                .fill(Color.blue)
                .frame(width: 18, height: 18)
        }
        .onAppear {
            animationAmount = 2
        }
    }
}

#Preview("UserLocationPulseView") {
    ZStack {
        Color.gray.opacity(0.5)
        UserLocationPulseView()
    }
    .frame(width: 100, height: 100)
}
