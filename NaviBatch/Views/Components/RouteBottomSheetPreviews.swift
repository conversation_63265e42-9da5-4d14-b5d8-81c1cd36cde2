import SwiftUI
import SwiftData
import MapKit

struct RouteBottomSheetPreviews: PreviewProvider {
    static var previews: some View {
        // 在预览中使用内存配置，避免崩溃
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        var container: ModelContainer

        // 创建并配置视图模型（移到外部作用域）
        let viewModel = RouteViewModel()

        do {
            let tempContainer = try ModelContainer(for: Route.self, DeliveryPoint.self, DeliveryGroup.self, configurations: config)
            container = tempContainer

            // 创建一个示例路线
            let route = Route(name: "路线 2025-04-21")

            // 添加示例点 - 仅用于预览
            let points = [
                DeliveryPoint(sort_number: 1, streetName: "Address 1", coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642), isStartPoint: true),
                DeliveryPoint(sort_number: 2, streetName: "Address 2", coordinate: CLLocationCoordinate2D(latitude: -37.8842, longitude: 145.1642)),
                DeliveryPoint(sort_number: 3, streetName: "Address 3", coordinate: CLLocationCoordinate2D(latitude: -37.8798, longitude: 145.1642), isEndPoint: true)
            ]

            for point in points {
                container.mainContext.insert(point)
                route.addPoint(point)
            }

            container.mainContext.insert(route)
            try? container.mainContext.save()

            // 设置当前路线
            viewModel.currentRoute = route

            // 使用持久化的ModelContext
            viewModel.modelContext = container.mainContext

        } catch {
            // 如果创建失败，使用应用程序的共享容器
            print("[INFO] 预览创建容器失败，使用共享容器")
            container = getPersistentContainer()

            // 尝试从共享容器加载当前路线
            let context = container.mainContext
            viewModel.modelContext = context

            // 尝试加载最近的路线
            Task {
                do {
                    let descriptor = FetchDescriptor<Route>(sortBy: [SortDescriptor(\Route.createdAt, order: .reverse)])
                    let routes = try context.fetch(descriptor)
                    if let route = routes.first {
                        viewModel.currentRoute = route
                    }
                } catch {
                    print("[ERROR] 无法加载路线: \(error.localizedDescription)")
                }
            }
        }

        return RouteBottomSheet(viewModel: viewModel)
            .modelContainer(container)
            .frame(height: 600)
            .previewDisplayName("RouteBottomSheet")
    }
}


