import SwiftUI
import CoreLocation
import SwiftData
import MapKit

/// 历史地址编辑界面
/// 用于修复和更新历史地址的坐标信息
struct AddressHistoryEditView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    let address: ValidatedAddress
    let onSave: () -> Void

    @State private var editedAddress: String
    @State private var selectedCoordinate: CLLocationCoordinate2D?
    @State private var isUpdatingCoordinates = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var addressModified = false  // 跟踪地址是否被修改

    init(address: ValidatedAddress, onSave: @escaping () -> Void) {
        self.address = address
        self.onSave = onSave
        // 🔧 使用原始地址，避免过度处理导致截断
        let originalAddress = address.originalAddress
        print("🔍 DEBUG: 初始化地址编辑界面")
        print("🔍 DEBUG: address.originalAddress: '\(originalAddress)'")
        print("🔍 DEBUG: address.normalizedAddress: '\(address.normalizedAddress)'")

        // 只在必要时进行最小化清理
        let cleanedAddress = originalAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        print("🔍 DEBUG: cleanedAddress: '\(cleanedAddress)'")

        self._editedAddress = State(initialValue: cleanedAddress)
        self._selectedCoordinate = State(initialValue: CLLocationCoordinate2D(
            latitude: address.latitude,
            longitude: address.longitude
        ))
    }

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    // 地址编辑区域 - 🔧 改进布局，防止hang问题
                    VStack(alignment: .leading, spacing: 8) {
                        Text("address".localized)
                            .font(.headline)
                            .foregroundColor(.primary)

                        EnhancedAddressAutocomplete(
                            searchText: $editedAddress,
                            selectedCoordinate: $selectedCoordinate,
                            onAddressSelected: { selectedAddress, coordinate in
                                self.editedAddress = selectedAddress
                                self.selectedCoordinate = coordinate
                                self.addressModified = false  // 选择地址后重置修改标记
                                print("🎯 用户选择了地址: \(selectedAddress)")
                            }
                        )
                        .onChange(of: editedAddress) { oldValue, newValue in
                            // 检测地址是否被手动修改
                            if newValue != address.originalAddress && !addressModified {
                                addressModified = true
                                print("🔄 地址已修改，需要更新坐标")
                            }
                        }
                    }
                    .padding(.horizontal, 16)  // 🔧 添加水平边距
                    .padding(.vertical, 12)    // 🔧 添加垂直边距
                    .background(Color(.systemBackground))  // 🔧 明确的背景色
                    .cornerRadius(12)          // 🔧 圆角边框
                    .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)  // 🔧 轻微阴影

                    // 地址修改状态提示 - 🔧 移到容器外部，避免布局冲突
                    if addressModified {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                                .font(.system(size: 14))

                            Text("address_modified_update_required".localized)
                                .font(.caption)
                                .foregroundColor(.orange)

                            Spacer()
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(6)
                        .padding(.horizontal, 16)  // 🔧 与上面的容器对齐
                    }

                    // 坐标信息区域 - 简洁设计
                    VStack(alignment: .leading, spacing: 12) {
                        Text("coordinates".localized)
                            .font(.headline)
                            .foregroundColor(.primary)

                        coordinateInfoSection
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(Color(.secondarySystemGroupedBackground))
                            .cornerRadius(10)
                    }

                    // 统计信息区域 - 简洁设计
                    VStack(alignment: .leading, spacing: 12) {
                        Text("statistics".localized)
                            .font(.headline)
                            .foregroundColor(.primary)

                        statisticsSection
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(Color(.secondarySystemGroupedBackground))
                            .cornerRadius(10)
                    }

                    // 操作按钮区域
                    actionButtons
                        .padding(.top, 8)
                }
                .padding()
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("edit_address".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        print("🔧 取消按钮被点击，准备关闭编辑界面")
                        dismiss()
                    }
                    .foregroundColor(.adaptivePrimaryIcon)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("save".localized) {
                        saveChanges()
                    }
                    .disabled(isUpdatingCoordinates || editedAddress.isEmpty)
                    .foregroundColor(isUpdatingCoordinates || editedAddress.isEmpty ? .adaptiveSecondaryIcon : .adaptivePrimaryIcon)
                }
            }
            .alert("prompt".localized, isPresented: $showingAlert) {
                Button("ok".localized) { }
            } message: {
                Text(alertMessage)
            }
            .onAppear {
                print("🎯 地址编辑界面已加载")
                print("🔍 DEBUG: editedAddress当前值: '\(editedAddress)'")
                print("🔍 DEBUG: editedAddress字符数: \(editedAddress.count)")
            }
        }
    }

    // MARK: - 计算属性

    private var coordinateInfoSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("current_coordinates".localized)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
            }

            if let coordinate = selectedCoordinate {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\("latitude".localized): \(coordinate.latitude, specifier: "%.6f")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\("longitude".localized): \(coordinate.longitude, specifier: "%.6f")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                Text("no_coordinates".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    private var statisticsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("address_info".localized)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
            }

            VStack(alignment: .leading, spacing: 4) {
                Text("\("created".localized): \(formatDate(address.createdAt))")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("\("last_used".localized): \(formatDate(address.lastUsedAt))")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("\("usage_count".localized): \(address.usageCount)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    private var actionButtons: some View {
        VStack(spacing: 12) {
            Button(action: updateCoordinates) {
                HStack {
                    if isUpdatingCoordinates {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: addressModified ? "location.fill" : "location")
                    }
                    Text(addressModified ? "update_coordinates".localized : "verify_coordinates".localized)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(isUpdatingCoordinates || editedAddress.isEmpty ? Color.gray.opacity(0.3) : Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isUpdatingCoordinates || editedAddress.isEmpty)

            HStack(spacing: 12) {
                Button(action: copyAddress) {
                    HStack {
                        Image(systemName: "doc.on.doc")
                        Text("copy_address".localized)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
                }

                Button(action: openInMaps) {
                    HStack {
                        Image(systemName: "map")
                        Text("open_in_maps".localized)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
                }
            }
        }
    }

    // MARK: - 方法

    private func saveChanges() {
        guard !editedAddress.isEmpty else {
            alertMessage = "address_cannot_be_empty".localized
            showingAlert = true
            return
        }

        // 更新地址信息
        address.originalAddress = editedAddress
        address.normalizedAddress = editedAddress

        if let coordinate = selectedCoordinate {
            address.latitude = coordinate.latitude
            address.longitude = coordinate.longitude
        }

        // 保存到数据库
        do {
            try modelContext.save()
            print("✅ 地址更新成功: \(editedAddress)")
            onSave()
            dismiss()
        } catch {
            alertMessage = "\("save_failed".localized): \(error.localizedDescription)"
            showingAlert = true
        }
    }

    private func updateCoordinates() {
        guard !editedAddress.isEmpty else {
            alertMessage = "modify_address_first".localized
            showingAlert = true
            return
        }

        print("🎯 开始更新坐标: '\(editedAddress)'")
        print("🎯 地址长度: \(editedAddress.count) 字符")
        print("🎯 当前坐标: \(selectedCoordinate?.latitude ?? 0), \(selectedCoordinate?.longitude ?? 0)")

        isUpdatingCoordinates = true

        Task {
            // 🏠 首先检查地址库中是否已存在该地址
            print("🏠 AddressHistoryEditView - 检查地址库: \(editedAddress)")
            let existingAddress = await UserAddressDatabase.shared.getValidatedAddress(for: editedAddress)

            if let existing = existingAddress {
                print("🏠 AddressHistoryEditView - ✅ 地址库命中: \(editedAddress) -> (\(existing.coordinate.latitude), \(existing.coordinate.longitude))")
                print("🏠 AddressHistoryEditView - 地址库信息: 使用次数=\(existing.usageCount), 置信度=\(existing.confidence), 来源=\(existing.source)")

                await MainActor.run {
                    selectedCoordinate = existing.coordinate
                    isUpdatingCoordinates = false
                    addressModified = false
                    alertMessage = "coordinates_update_success".localized
                    showingAlert = true
                    print("✅ 使用地址库数据更新坐标: \(existing.coordinate.latitude), \(existing.coordinate.longitude)")
                }
                return
            }

            print("🏠 AddressHistoryEditView - 地址库未命中，进行全球地址处理: \(editedAddress)")

            // 🌍 使用与SimpleAddressSheet相同的全球地址处理器
            let globalResult = await UniversalAddressProcessor.shared.processGlobalAddress(editedAddress)

            await MainActor.run {
                switch globalResult {
                case .success(_, let formattedAddress, let coordinate, _, let strategy, let confidence):
                    print("🌍 AddressHistoryEditView - 全球地址处理成功: \(strategy) - \(formattedAddress) (置信度: \(confidence))")

                    // 🏠 保存到地址库
                    Task {
                        print("🏠 AddressHistoryEditView - 保存新地址到地址库: \(formattedAddress) -> (\(coordinate.latitude), \(coordinate.longitude))")
                        await UserAddressDatabase.shared.saveValidatedAddress(
                            formattedAddress,
                            coordinate: coordinate,
                            source: .manual,
                            confidence: 0.95 // 用户主动编辑的地址，置信度很高
                        )
                        print("🏠 AddressHistoryEditView - ✅ 地址库更新完成: \(formattedAddress)")
                    }

                    selectedCoordinate = coordinate
                    isUpdatingCoordinates = false
                    addressModified = false
                    alertMessage = "coordinates_update_success".localized
                    showingAlert = true
                    print("✅ 全球地址处理成功，新坐标: \(coordinate.latitude), \(coordinate.longitude)")

                case .failed(_, let reason):
                    print("❌ AddressHistoryEditView - 全球地址处理失败: \(reason)")
                    isUpdatingCoordinates = false
                    alertMessage = "\("coordinates_update_failure".localized): \(reason)"
                    showingAlert = true
                }
            }
        }
    }

    private func copyAddress() {
        UIPasteboard.general.string = editedAddress
        alertMessage = "address_copied".localized
        showingAlert = true
    }

    private func openInMaps() {
        guard let coordinate = selectedCoordinate else { return }
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: coordinate))
        mapItem.name = editedAddress
        mapItem.openInMaps()
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}
