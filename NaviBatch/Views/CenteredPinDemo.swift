import SwiftUI

// 演示数字在矩形中完全居中的效果
struct CenteredPinDemo: View {
    var body: some View {
        VStack(spacing: 30) {
            Text("数字在矩形中完全居中演示")
                .font(.title2)
                .padding()
            
            HStack(spacing: 40) {
                // 单位数
                VStack {
                    PinExample(number: 1, color: .blue)
                    Text("单位数")
                        .font(.caption)
                }
                
                // 两位数
                VStack {
                    PinExample(number: 25, color: .green)
                    Text("两位数")
                        .font(.caption)
                }
                
                // 三位数
                VStack {
                    PinExample(number: 999, color: .red)
                    Text("三位数")
                        .font(.caption)
                }
            }
            
            HStack(spacing: 40) {
                // 字母
                VStack {
                    PinExampleWithText(text: "G", color: .orange)
                    Text("字母")
                        .font(.caption)
                }
                
                // 图标
                VStack {
                    PinExampleWithIcon(icon: "house.fill", color: .purple)
                    Text("图标")
                        .font(.caption)
                }
                
                // 对勾
                VStack {
                    PinExampleWithIcon(icon: "checkmark", color: .green)
                    Text("对勾")
                        .font(.caption)
                }
            }
            
            Text("特点：")
                .font(.headline)
                .padding(.top)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("数字在矩形中水平和垂直完全居中")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("三角形指针与矩形完美连接")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("使用SF Symbols，性能极佳")
                }
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("支持不同尺寸的数字和内容")
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
        .padding()
    }
}

// 数字Pin示例
struct PinExample: View {
    let number: Int
    let color: Color
    let size: CGFloat = 24
    
    var body: some View {
        VStack(spacing: 0) {
            // 矩形部分 - 包含数字内容
            ZStack {
                // 主体矩形
                Image(systemName: "rectangle")
                    .font(.system(size: size, weight: .medium))
                    .foregroundStyle(color)

                // 数字 - 在矩形中完全居中
                Text("\(number)")
                    .font(.system(size: size * 0.5, weight: .bold))
                    .foregroundColor(color)
            }
            
            // 向下的实心三角形指针
            Image(systemName: "arrowtriangle.down.fill")
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundStyle(color)
                .offset(y: -2)
        }
    }
}

// 文字Pin示例
struct PinExampleWithText: View {
    let text: String
    let color: Color
    let size: CGFloat = 24
    
    var body: some View {
        VStack(spacing: 0) {
            // 矩形部分 - 包含文字内容
            ZStack {
                // 主体矩形
                Image(systemName: "rectangle")
                    .font(.system(size: size, weight: .medium))
                    .foregroundStyle(color)

                // 文字 - 在矩形中完全居中
                Text(text)
                    .font(.system(size: size * 0.5, weight: .bold))
                    .foregroundColor(color)
            }
            
            // 向下的实心三角形指针
            Image(systemName: "arrowtriangle.down.fill")
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundStyle(color)
                .offset(y: -2)
        }
    }
}

// 图标Pin示例
struct PinExampleWithIcon: View {
    let icon: String
    let color: Color
    let size: CGFloat = 24
    
    var body: some View {
        VStack(spacing: 0) {
            // 矩形部分 - 包含图标内容
            ZStack {
                // 主体矩形
                Image(systemName: "rectangle")
                    .font(.system(size: size, weight: .medium))
                    .foregroundStyle(color)

                // 图标 - 在矩形中完全居中
                Image(systemName: icon)
                    .font(.system(size: size * 0.4, weight: .bold))
                    .foregroundColor(color)
            }
            
            // 向下的实心三角形指针
            Image(systemName: "arrowtriangle.down.fill")
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundStyle(color)
                .offset(y: -2)
        }
    }
}

#Preview("CenteredPinDemo") {
    CenteredPinDemo()
}
