import SwiftUI

// 优化的标记形状 - 将矩形和三角形合并为一个形状
struct MarkerShape: Shape {
    var strokeOnly: Bool = false

    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 矩形部分的高度（总高度减去三角形高度）
        let rectHeight = rect.height - 8

        if !strokeOnly {
            // 填充版本 - 绘制圆角矩形
            let cornerRadius: CGFloat = 8
            let rectRect = CGRect(x: rect.minX, y: rect.minY, width: rect.width, height: rectHeight)

            // 添加圆角矩形
            path.addRoundedRect(in: rectRect, cornerSize: CGSize(width: cornerRadius, height: cornerRadius))

            // 添加三角形
            path.move(to: CGPoint(x: rect.midX, y: rect.maxY))
            path.addLine(to: CGPoint(x: rect.midX - 7, y: rectHeight))
            path.addLine(to: CGPoint(x: rect.midX + 7, y: rectHeight))
            path.closeSubpath()
        } else {
            // 描边版本 - 只需要轮廓
            let cornerRadius: CGFloat = 8
            let rectRect = CGRect(x: rect.minX, y: rect.minY, width: rect.width, height: rectHeight)

            // 添加圆角矩形轮廓
            path.addRoundedRect(in: rectRect, cornerSize: CGSize(width: cornerRadius, height: cornerRadius))

            // 添加三角形轮廓
            path.move(to: CGPoint(x: rect.midX, y: rect.maxY))
            path.addLine(to: CGPoint(x: rect.midX - 7, y: rectHeight))
            path.addLine(to: CGPoint(x: rect.midX + 7, y: rectHeight))
            path.closeSubpath()
        }

        return path
    }
}
