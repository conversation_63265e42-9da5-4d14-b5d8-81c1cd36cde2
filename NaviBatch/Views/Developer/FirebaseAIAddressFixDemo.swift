import SwiftUI

/// Firebase AI 地址州修复演示界面
/// 展示地址修复功能的实际效果
struct FirebaseAIAddressFixDemo: View {
    @State private var testAddress = "1220 Taylor Lane, 95603"
    @State private var fixedAddress = ""
    @State private var isProcessing = false
    @State private var processingLog: [String] = []

    private let testCases = [
        "1220 Taylor Lane, 95603",
        "10624 Pleasant Valley Circ, 95209",
        "500 King Dr, 94015",
        "1721 Marina Ct, 94403",
        "123 Main St, 90210",
        "456 Broadway, 10001"
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(alignment: .leading, spacing: 10) {
                    Text("Firebase AI 地址州修复演示")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("演示如何自动修复 Firebase AI 识别的缺少州信息的地址")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 输入区域
                VStack(alignment: .leading, spacing: 10) {
                    Text("测试地址")
                        .font(.headline)

                    TextField("输入缺少州信息的地址", text: $testAddress)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    // 预设测试用例
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 10) {
                            ForEach(testCases, id: \.self) { testCase in
                                Button(testCase) {
                                    testAddress = testCase
                                }
                                .font(.caption)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal)
                    }
                }

                // 测试按钮
                Button(action: testAddressFix) {
                    HStack {
                        if isProcessing {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text(isProcessing ? "正在修复..." : "测试地址修复")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isProcessing || testAddress.isEmpty)

                // 结果显示
                if !fixedAddress.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("修复结果")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("原始地址:")
                                    .fontWeight(.medium)
                                Spacer()
                            }
                            Text(testAddress)
                                .padding()
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(8)

                            HStack {
                                Text("修复后:")
                                    .fontWeight(.medium)
                                Spacer()
                            }
                            Text(fixedAddress)
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(8)

                            // 修复状态
                            HStack {
                                Image(systemName: fixedAddress != testAddress ? "checkmark.circle.fill" : "info.circle.fill")
                                    .foregroundColor(fixedAddress != testAddress ? .green : .blue)
                                Text(fixedAddress != testAddress ? "地址已修复" : "地址无需修复")
                                    .font(.caption)
                                Spacer()
                            }
                        }
                    }
                }

                // 处理日志
                if !processingLog.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("处理日志")
                            .font(.headline)

                        ScrollView {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(processingLog, id: \.self) { log in
                                    Text(log)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        .frame(maxHeight: 150)
                    }
                }

                Spacer()

                // 说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("💡 功能说明")
                        .font(.headline)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("• 自动检测缺少州信息的美国地址")
                        Text("• 使用反向地理编码获取准确的州信息")
                        Text("• 备用使用邮政编码映射推断州信息")
                        Text("• 在地理编码前修复地址，确保坐标准确性")
                        Text("• 在存储前再次修复，确保数据完整性")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.blue.opacity(0.05))
                .cornerRadius(10)
            }
            .padding()
            .navigationTitle("地址修复演示")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func testAddressFix() {
        isProcessing = true
        fixedAddress = ""
        processingLog = []

        addLog("🚀 开始测试地址修复...")
        addLog("📥 输入地址: \(testAddress)")

        Task {
            // 使用 AddressStateFixService 进行修复
            addLog("🔍 检测地址是否需要修复...")

            if let result = await AddressStateFixService.shared.detectAndFixMissingState(for: testAddress) {
                await MainActor.run {
                    fixedAddress = result
                    addLog("✅ 地址修复成功!")
                    addLog("📤 修复后地址: \(result)")
                    addLog("🎯 修复完成，地址包含完整的州信息")
                }
            } else {
                await MainActor.run {
                    fixedAddress = testAddress
                    addLog("ℹ️ 地址无需修复")
                    addLog("📤 原始地址: \(testAddress)")
                    addLog("💡 地址已包含州信息或不是美国地址")
                }
            }

            await MainActor.run {
                isProcessing = false
                addLog("🏁 测试完成!")
            }
        }
    }

    private func addLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        processingLog.append("[\(timestamp)] \(message)")
    }
}

#Preview {
    FirebaseAIAddressFixDemo()
}
