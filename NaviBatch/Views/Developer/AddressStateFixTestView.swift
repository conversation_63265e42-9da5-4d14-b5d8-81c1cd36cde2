import SwiftUI
import os.log

struct TestResult: Identifiable {
    let id = UUID()
    let input: String
    let output: String?
    let expected: String?
    let passed: Bool
    let description: String
}

/// 地址州名修复功能测试视图
/// 用于验证AddressStateFixService是否正常工作
struct AddressStateFixTestView: View {
    @State private var testResults: [TestResult] = []
    @State private var isRunning = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(alignment: .leading, spacing: 10) {
                    Text("地址州名修复测试")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("测试AddressStateFixService是否能正确为缺少州名的美国地址添加州名")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)

                // 测试按钮
                Button(action: runTests) {
                    HStack {
                        if isRunning {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "play.circle.fill")
                        }
                        Text(isRunning ? "测试中..." : "运行测试")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isRunning)

                // 测试结果
                if !testResults.isEmpty {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(testResults) { result in
                                TestResultCard(result: result)
                            }
                        }
                        .padding()
                    }
                }

                Spacer()
            }
            .padding()
            .navigationTitle("州名修复测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func runTests() {
        isRunning = true
        testResults.removeAll()

        Task {
            await performTests()

            await MainActor.run {
                isRunning = false
            }
        }
    }

    private func performTests() async {
        let service = AddressStateFixService.shared

        // 测试用例
        let testCases: [(input: String, expected: String?, description: String)] = [
            (
                input: "15325 Luther Road, 95603",
                expected: "15325 Luther Road, CA, 95603",
                description: "加州地址缺少州名"
            ),
            (
                input: "15325 Luther Road, CA, 95603",
                expected: nil,
                description: "已有州名的地址（不需要修复）"
            ),
            (
                input: "123 Main Street, 10001",
                expected: "123 Main Street, NY, 10001",
                description: "纽约地址缺少州名"
            ),
            (
                input: "456 Oak Avenue, 75001",
                expected: "456 Oak Avenue, TX, 75001",
                description: "德州地址缺少州名"
            ),
            (
                input: "789 Pine Street, 33101",
                expected: "789 Pine Street, FL, 33101",
                description: "佛州地址缺少州名"
            ),
            (
                input: "321 Elm Drive, 60601",
                expected: "321 Elm Drive, IL, 60601",
                description: "伊利诺伊州地址缺少州名"
            ),
            (
                input: "555 Maple Lane, 84101",
                expected: "555 Maple Lane, UT, 84101",
                description: "犹他州地址缺少州名"
            ),
            (
                input: "999 Cedar Court, 12345",
                expected: nil,
                description: "未知ZIP码范围（应该返回nil）"
            )
        ]

        for testCase in testCases {
            Logger.info("🧪 测试: \(testCase.description)", type: .data)
            Logger.info("📥 输入: \(testCase.input)", type: .data)

            let result = await service.detectAndFixMissingState(for: testCase.input)

            Logger.info("📤 输出: \(result ?? "nil")", type: .data)
            Logger.info("🎯 期望: \(testCase.expected ?? "nil")", type: .data)

            let passed = result == testCase.expected
            Logger.info("✅ 结果: \(passed ? "通过" : "失败")", type: .data)

            let testResult = TestResult(
                input: testCase.input,
                output: result,
                expected: testCase.expected,
                passed: passed,
                description: testCase.description
            )

            await MainActor.run {
                testResults.append(testResult)
            }

            // 添加小延迟避免过快的请求
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        }

        // 测试完整的地址后处理流程
        await testAddressPostProcessing()
    }

    private func testAddressPostProcessing() async {
        Logger.info("🧪 测试完整地址后处理流程", type: .data)

        let manager = DeliveryPointManager.shared
        let testAddress = "15325 Luther Road, 95603"

        Logger.info("📥 原始地址: \(testAddress)", type: .data)

        let processedAddress = await manager.postProcessAddressForStorage(testAddress)

        Logger.info("📤 处理后地址: \(processedAddress)", type: .data)

        let passed = processedAddress.contains("CA")
        Logger.info("✅ 包含州名: \(passed ? "是" : "否")", type: .data)

        let testResult = TestResult(
            input: testAddress,
            output: processedAddress,
            expected: "15325 Luther Road, CA, 95603",
            passed: passed,
            description: "完整地址后处理流程"
        )

        await MainActor.run {
            testResults.append(testResult)
        }
    }
}

struct TestResultCard: View {
    let result: TestResult

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 测试描述和结果状态
            HStack {
                Text(result.description)
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: result.passed ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.passed ? .green : .red)
                    .font(.title2)
            }

            // 输入地址
            VStack(alignment: .leading, spacing: 4) {
                Text("输入:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(result.input)
                    .font(.body)
                    .padding(8)
                    .background(Color(.systemGray6))
                    .cornerRadius(6)
            }

            // 输出地址
            VStack(alignment: .leading, spacing: 4) {
                Text("输出:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(result.output ?? "nil")
                    .font(.body)
                    .padding(8)
                    .background(result.passed ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
                    .cornerRadius(6)
            }

            // 期望结果
            if let expected = result.expected {
                VStack(alignment: .leading, spacing: 4) {
                    Text("期望:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(expected)
                        .font(.body)
                        .padding(8)
                        .background(Color(.systemGray5))
                        .cornerRadius(6)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    AddressStateFixTestView()
}
