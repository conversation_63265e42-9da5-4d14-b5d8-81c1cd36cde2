import SwiftUI
import SwiftData

/// 地址数据修复工具界面
struct AddressDataFixerView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var isFixing = false
    @State private var statistics: AddressFixStatistics?
    @State private var showingResults = false
    @State private var errorMessage: String?
    @State private var selectedAddresses: [SavedAddress] = []
    @State private var showingPreview = false

    @Query private var allAddresses: [SavedAddress]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("🔧 地址数据标准化工具")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("此工具用于修复数据库中与Apple Maps标准格式不一致的地址数据")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 当前数据统计
                VStack(alignment: .leading, spacing: 8) {
                    Text("📊 当前数据统计")
                        .font(.headline)

                    HStack {
                        VStack(alignment: .leading) {
                            Text("总地址数")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(allAddresses.count)")
                                .font(.title3)
                                .fontWeight(.semibold)
                        }

                        Spacer()

                        VStack(alignment: .leading) {
                            Text("可能需要修复")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(problematicAddressesCount)")
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)
                        }
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)

                // 问题地址预览
                if problematicAddressesCount > 0 {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("⚠️ 发现的问题地址")
                                .font(.headline)

                            Spacer()

                            Button("查看详情") {
                                showingPreview = true
                            }
                            .font(.caption)
                        }

                        ForEach(Array(problematicAddresses.prefix(3)), id: \.id) { address in
                            VStack(alignment: .leading, spacing: 4) {
                                Text(address.address)
                                    .font(.subheadline)
                                    .lineLimit(1)

                                Text(identifyProblem(address))
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                            .padding(.vertical, 4)
                        }

                        if problematicAddressesCount > 3 {
                            Text("还有 \(problematicAddressesCount - 3) 个地址...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(12)
                }

                Spacer()

                // 操作按钮
                VStack(spacing: 12) {
                    Button(action: startFixing) {
                        HStack {
                            if isFixing {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("正在修复...")
                            } else {
                                Image(systemName: "wrench.and.screwdriver")
                                Text("开始修复地址数据")
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isFixing ? Color.gray : Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(isFixing || allAddresses.isEmpty)

                    if statistics != nil {
                        Button("查看修复结果") {
                            showingResults = true
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                }

                // 错误信息
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }
            }
            .padding()
            .navigationTitle("地址数据修复")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingResults) {
            if let statistics = statistics {
                AddressFixResultsView(statistics: statistics)
            }
        }
        .sheet(isPresented: $showingPreview) {
            ProblematicAddressesView(addresses: problematicAddresses)
        }
    }

    // MARK: - 计算属性

    private var problematicAddresses: [SavedAddress] {
        return allAddresses.filter { address in
            // 检查常见问题
            let addressLower = address.address.lowercased()

            // 1. 国家不一致
            if addressLower.contains("united states") && addressLower.contains("australia") {
                return true
            }

            // 2. 格式问题
            if address.address.contains(", ,") || address.address.contains(",,") {
                return true
            }

            // 3. 门牌号格式问题 (如 "105, Cedar Street")
            if address.address.matches("^\\d+,\\s") {
                return true
            }

            return false
        }
    }

    private var problematicAddressesCount: Int {
        return problematicAddresses.count
    }

    // MARK: - 方法

    private func identifyProblem(_ address: SavedAddress) -> String {
        let addressLower = address.address.lowercased()

        if addressLower.contains("united states") && addressLower.contains("australia") {
            return "国家信息不一致"
        }

        if address.address.contains(", ,") || address.address.contains(",,") {
            return "格式错误：多余逗号"
        }

        if address.address.matches("^\\d+,\\s") {
            return "门牌号格式问题"
        }

        return "格式不标准"
    }

    private func startFixing() {
        isFixing = true
        errorMessage = nil

        Task {
            let fixStatistics = await AddressDataStandardizer.shared.batchFixAddressData(modelContext: modelContext)

            await MainActor.run {
                self.statistics = fixStatistics
                self.isFixing = false
                self.showingResults = true

                Logger.info("✅ 地址数据修复完成: \(fixStatistics.summary)", type: .location)
            }
        }
    }
}

// MARK: - 修复结果视图
struct AddressFixResultsView: View {
    let statistics: AddressFixStatistics
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 成功图标
                Image(systemName: statistics.addressesFixed > 0 ? "checkmark.circle.fill" : "info.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(statistics.addressesFixed > 0 ? .green : .blue)

                // 统计信息
                VStack(spacing: 16) {
                    Text("修复完成")
                        .font(.title2)
                        .fontWeight(.bold)

                    VStack(spacing: 12) {
                        StatisticRow(title: "总地址数", value: "\(statistics.totalAddresses)")
                        StatisticRow(title: "需要修复", value: "\(statistics.addressesNeedingFix)", color: .orange)
                        StatisticRow(title: "修复成功", value: "\(statistics.addressesFixed)", color: .green)
                        StatisticRow(title: "修复失败", value: "\(statistics.addressesFailedToFix)", color: .red)
                        StatisticRow(title: "已经正确", value: "\(statistics.addressesAlreadyCorrect)", color: .blue)

                        Divider()

                        StatisticRow(
                            title: "成功率",
                            value: "\(String(format: "%.1f", statistics.successRate * 100))%",
                            color: statistics.successRate > 0.8 ? .green : .orange
                        )
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                Spacer()

                Button("完成") {
                    dismiss()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
            .padding()
            .navigationTitle("修复结果")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - 统计行视图
struct StatisticRow: View {
    let title: String
    let value: String
    var color: Color = .primary

    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

// MARK: - 问题地址列表视图
struct ProblematicAddressesView: View {
    let addresses: [SavedAddress]
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List(addresses, id: \.id) { address in
                VStack(alignment: .leading, spacing: 4) {
                    Text(address.address)
                        .font(.subheadline)

                    Text(identifyProblem(address))
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                .padding(.vertical, 2)
            }
            .navigationTitle("问题地址列表")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func identifyProblem(_ address: SavedAddress) -> String {
        let addressLower = address.address.lowercased()

        if addressLower.contains("united states") && addressLower.contains("australia") {
            return "国家信息不一致"
        }

        if address.address.contains(", ,") || address.address.contains(",,") {
            return "格式错误：多余逗号"
        }

        if address.address.matches("^\\d+,\\s") {
            return "门牌号格式问题"
        }

        return "格式不标准"
    }
}

#Preview("AddressDataFixerView") {
    AddressDataFixerView()
}
