//
//  TestAddressCleaning.swift
//  NaviBatch
//
//  Created for v1.0.4 - Address Cleaning Test Tool
//  Copyright © 2024 NaviBatch. All rights reserved.
//

import SwiftUI
import SwiftData

/// 🧪 地址清理功能测试工具
struct AddressCleaningTestView: View {
    @Environment(\.dismiss) private var dismiss
    
    @State private var testResults: [TestResult] = []
    @State private var isRunningTests = false
    
    struct TestResult {
        let original: String
        let cleaned: String
        let passed: Bool
        let description: String
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 测试说明
                    testDescriptionSection
                    
                    // 测试按钮
                    testButtonSection

                    // DeliveryPointManager测试按钮
                    deliveryPointManagerTestSection

                    // 测试结果
                    testResultsSection
                }
                .padding()
            }
            .navigationTitle("地址清理测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var testDescriptionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🧪 地址清理功能测试")
                .font(.headline)
                .fontWeight(.bold)
            
            Text("此工具测试地址清理逻辑，验证是否能正确移除元数据信息")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("测试内容:")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("• 移除管道符号(|)后的元数据")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("• 移除第三方排序号(ISORT:8, D90等)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("• 清理多余的空格和标点符号")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    private var testButtonSection: some View {
        Button(action: runTests) {
            HStack {
                if isRunningTests {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("运行测试中...")
                } else {
                    Image(systemName: "play.circle")
                    Text("运行地址清理测试")
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(isRunningTests ? Color.gray : Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(isRunningTests)
    }

    private var deliveryPointManagerTestSection: some View {
        Button(action: runDeliveryPointManagerTest) {
            HStack {
                Image(systemName: "gear.circle")
                Text("测试 DeliveryPointManager 地址解析")
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.green)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(isRunningTests)
    }
    
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            if !testResults.isEmpty {
                Text("📊 测试结果")
                    .font(.headline)
                    .fontWeight(.bold)
                
                let passedCount = testResults.filter { $0.passed }.count
                let totalCount = testResults.count
                
                Text("通过: \(passedCount)/\(totalCount)")
                    .font(.subheadline)
                    .foregroundColor(passedCount == totalCount ? .green : .orange)
                
                LazyVStack(spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        testResultRow(result, index: index + 1)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    private func testResultRow(_ result: TestResult, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("测试 \(index)")
                    .font(.caption)
                    .fontWeight(.medium)
                
                Spacer()
                
                Image(systemName: result.passed ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.passed ? .green : .red)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("原始: \(result.original)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("清理后: \(result.cleaned)")
                    .font(.caption)
                    .foregroundColor(.primary)
                
                Text(result.description)
                    .font(.caption)
                    .foregroundColor(result.passed ? .green : .red)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    // MARK: - 测试方法
    
    private func runTests() {
        isRunningTests = true
        testResults.removeAll()
        
        // 模拟异步测试
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            performTests()
            isRunningTests = false
        }
    }
    
    private func performTests() {
        let testCases = [
            // 🎯 YWE地址测试用例
            (
                original: "2200 Cabrillo Path, LEANDER, TX|SORT:1|TRACK:YWAUS010000147255|CUSTOMER:Tarun karra|APP:ywe|APP:ywe",
                expected: "2200 Cabrillo Path, LEANDER, TX",
                description: "YWE地址 - 移除所有元数据"
            ),
            (
                original: "14801 ronald w reagan blvd, LEANDER, TX|SORT:2|TRACK:YWAUS010000143325|CUSTOMER:Karthik Vemula|APP:ywe|APP:ywe",
                expected: "14801 ronald w reagan blvd, LEANDER, TX",
                description: "YWE地址 - 移除复杂元数据"
            ),
            // 原有测试用例
            (
                original: "24 n quebec st san mateo ca usa|sort:2|track:#b.111.ov|time:...",
                expected: "24 n quebec st san mateo ca usa",
                description: "移除管道符号后的元数据"
            ),
            (
                original: "123 Main St ISORT:8 Melbourne VIC",
                expected: "123 Main St Melbourne VIC",
                description: "移除ISORT排序号"
            ),
            (
                original: "456 Oak Ave D90 Sydney NSW",
                expected: "456 Oak Ave Sydney NSW",
                description: "移除D90排序号"
            ),
            (
                original: "789 Pine Rd D146 Brisbane QLD",
                expected: "789 Pine Rd Brisbane QLD",
                description: "移除D146排序号"
            ),
            (
                original: "Clean Address Without Metadata",
                expected: "Clean Address Without Metadata",
                description: "保持干净地址不变"
            ),
            (
                original: "  Extra   Spaces   Address  ",
                expected: "Extra Spaces Address",
                description: "清理多余空格"
            )
        ]
        
        for testCase in testCases {
            let cleaned = cleanAddressMetadata(testCase.original)
            let passed = cleaned == testCase.expected
            
            let result = TestResult(
                original: testCase.original,
                cleaned: cleaned,
                passed: passed,
                description: testCase.description + (passed ? " ✓" : " ✗")
            )
            
            testResults.append(result)
        }
    }

    private func runDeliveryPointManagerTest() {
        print("🧪 开始测试 DeliveryPointManager.separateAddressAndTracking...")

        let manager = DeliveryPointManager.shared

        // 测试YWE地址
        let yweAddresses = [
            "2200 Cabrillo Path, LEANDER, TX|SORT:1|TRACK:YWAUS010000147255|CUSTOMER:Tarun karra|APP:ywe|APP:ywe",
            "14801 ronald w reagan blvd, LEANDER, TX|SORT:2|TRACK:YWAUS010000143325|CUSTOMER:Karthik Vemula|APP:ywe|APP:ywe"
        ]

        for (index, address) in yweAddresses.enumerated() {
            let result = manager.separateAddressAndTracking(address)

            print("🎯 测试 \(index + 1):")
            print("   输入: \(address)")
            print("   输出地址: '\(result.address)'")
            print("   包含管道符号: \(result.address.contains("|") ? "❌ 是" : "✅ 否")")
            print("   排序号: '\(result.sortNumber)'")
            print("   追踪号: '\(result.tracking)'")
            print("   客户: '\(result.customer)'")
            print("   应用类型: '\(result.appType)'")
            print("")

            // 添加到测试结果
            let passed = !result.address.contains("|")
            let testResult = TestResult(
                original: address,
                cleaned: result.address,
                passed: passed,
                description: "DeliveryPointManager解析测试 \(index + 1) - \(passed ? "通过" : "失败")"
            )
            testResults.append(testResult)
        }

        print("🧪 DeliveryPointManager 测试完成!")
    }

    // MARK: - 地址清理逻辑 (复制自UserAddressDatabase)
    
    private func cleanAddressMetadata(_ address: String) -> String {
        var cleanedAddress = address
        
        // 🎯 关键：移除所有管道符号（|）后的元数据信息
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )
        
        // 移除第三方sort number模式（如 ISORT:8, D90, D91等）
        let sortPatterns = [
            "\\bISORT:\\d+\\b",  // ISORT:8
            "\\bD\\d+\\b",       // D90, D91, D146等
            "\\bSORT:\\d+\\b"    // SORT:2
        ]
        
        for pattern in sortPatterns {
            cleanedAddress = cleanedAddress.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )
        }
        
        // 清理多余的空格
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        )
        
        // 移除首尾空格
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        
        return cleanedAddress.isEmpty ? address : cleanedAddress
    }
}

#Preview("TestAddressCleaning") {
    AddressCleaningTestView()
}
