import SwiftUI
import UIKit

/// 视频帧调试查看器
/// 显示保存的视频帧，方便用户检查AI分析的准确性
struct VideoFrameDebugView: View {
    @State private var savedFrameURLs: [URL] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var selectedFrame: UIImage?
    @State private var showingImageViewer = false

    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("正在加载保存的帧...")
                        .padding()
                } else if savedFrameURLs.isEmpty {
                    emptyStateView
                } else {
                    frameGridView
                }
            }
            .navigationTitle("视频帧调试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("刷新") {
                        loadSavedFrames()
                    }
                }
            }
            .onAppear {
                loadSavedFrames()
            }
            .sheet(isPresented: $showingImageViewer) {
                if let selectedFrame = selectedFrame {
                    VideoFrameImageViewerSheet(image: selectedFrame)
                }
            }
        }
    }

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo.stack")
                .font(.system(size: 60))
                .foregroundColor(.gray)

            Text("暂无保存的视频帧")
                .font(.title2)
                .foregroundColor(.secondary)

            Text("处理视频后，保留的帧将显示在这里")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            if let errorMessage = errorMessage {
                Text("错误: \(errorMessage)")
                    .foregroundColor(.red)
                    .padding()
            }
        }
        .padding()
    }

    private var frameGridView: some View {
        VStack {
            // 统计信息
            HStack {
                VStack(alignment: .leading) {
                    Text("保存的帧数")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(savedFrameURLs.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                }

                Spacer()

                Button("打开文件夹") {
                    openFramesFolder()
                }
                .buttonStyle(.bordered)
            }
            .padding()

            // 帧网格
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 10) {
                    ForEach(Array(savedFrameURLs.enumerated()), id: \.offset) { index, url in
                        FrameThumbnailView(
                            frameURL: url,
                            frameNumber: index + 1
                        ) { image in
                            selectedFrame = image
                            showingImageViewer = true
                        }
                    }
                }
                .padding()
            }
        }
    }

    private func loadSavedFrames() {
        isLoading = true
        errorMessage = nil

        Task {
            do {
                let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let debugFolder = documentsPath.appendingPathComponent("VideoFrames_Debug")

                if FileManager.default.fileExists(atPath: debugFolder.path) {
                    let contents = try FileManager.default.contentsOfDirectory(at: debugFolder, includingPropertiesForKeys: nil)
                    let imageURLs = contents.filter { url in
                        ["jpg", "jpeg", "png"].contains(url.pathExtension.lowercased())
                    }.sorted { $0.lastPathComponent < $1.lastPathComponent }

                    await MainActor.run {
                        self.savedFrameURLs = imageURLs
                        self.isLoading = false
                    }
                } else {
                    await MainActor.run {
                        self.savedFrameURLs = []
                        self.isLoading = false
                    }
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = error.localizedDescription
                    self.isLoading = false
                }
            }
        }
    }

    private func openFramesFolder() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let debugFolder = documentsPath.appendingPathComponent("VideoFrames_Debug")

        if FileManager.default.fileExists(atPath: debugFolder.path) {
            // 在Files应用中打开文件夹
            if UIApplication.shared.canOpenURL(debugFolder) {
                UIApplication.shared.open(debugFolder)
            }
        }
    }
}

/// 帧缩略图视图
struct FrameThumbnailView: View {
    let frameURL: URL
    let frameNumber: Int
    let onTap: (UIImage) -> Void

    @State private var image: UIImage?
    @State private var isLoading = true

    var body: some View {
        VStack {
            ZStack {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .aspectRatio(16/9, contentMode: .fit)

                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                } else if let image = image {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .clipped()
                } else {
                    Image(systemName: "photo")
                        .foregroundColor(.gray)
                }
            }
            .cornerRadius(8)
            .onTapGesture {
                if let image = image {
                    onTap(image)
                }
            }

            Text("帧 \(frameNumber)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .onAppear {
            loadImage()
        }
    }

    private func loadImage() {
        Task {
            do {
                let data = try Data(contentsOf: frameURL)
                let loadedImage = UIImage(data: data)

                await MainActor.run {
                    self.image = loadedImage
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
}

/// 视频帧图片查看器
struct VideoFrameImageViewerSheet: View {
    let image: UIImage
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VideoFrameZoomableImageView(image: image)
                .navigationTitle("帧详情")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            dismiss()
                        }
                    }
                }
        }
    }
}

/// 视频帧专用的可缩放图片视图
struct VideoFrameZoomableImageView: UIViewRepresentable {
    let image: UIImage

    func makeUIView(context: Context) -> UIScrollView {
        let scrollView = UIScrollView()
        let imageView = UIImageView(image: image)

        scrollView.addSubview(imageView)
        scrollView.minimumZoomScale = 0.1
        scrollView.maximumZoomScale = 5.0
        scrollView.delegate = context.coordinator

        imageView.frame = scrollView.bounds
        imageView.contentMode = .scaleAspectFit

        return scrollView
    }

    func updateUIView(_ uiView: UIScrollView, context: Context) {
        // 更新视图
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIScrollViewDelegate {
        let parent: VideoFrameZoomableImageView

        init(_ parent: VideoFrameZoomableImageView) {
            self.parent = parent
        }

        func viewForZooming(in scrollView: UIScrollView) -> UIView? {
            return scrollView.subviews.first
        }
    }
}

#Preview {
    VideoFrameDebugView()
}
