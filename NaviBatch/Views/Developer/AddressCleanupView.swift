import SwiftUI
import SwiftData

/// 🧹 地址清理工具视图 - 开发者工具
struct AddressCleanupView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var cleanupManager: AddressCleanupManager
    @State private var needCleanupCount = 0
    @State private var showingConfirmation = false
    
    init() {
        // 使用全局的 ModelContext
        let context = getPersistentContainer().mainContext
        self._cleanupManager = StateObject(wrappedValue: AddressCleanupManager(modelContext: context))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(alignment: .leading, spacing: 10) {
                    Text("🧹 地址清理工具")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("此工具用于清理数据库中地址字段的管道符号（|）和其他元数据信息，确保地址显示正常。")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 状态信息
                VStack(spacing: 15) {
                    HStack {
                        Text("需要清理的地址数量:")
                            .fontWeight(.medium)
                        Spacer()
                        Text("\(needCleanupCount)")
                            .fontWeight(.bold)
                            .foregroundColor(needCleanupCount > 0 ? .orange : .green)
                    }
                    
                    if cleanupManager.isProcessing {
                        VStack(spacing: 10) {
                            ProgressView(value: cleanupManager.progress)
                                .progressViewStyle(LinearProgressViewStyle())
                            
                            Text(cleanupManager.statusMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Text("进度:")
                                Spacer()
                                Text("\(cleanupManager.cleanedCount)/\(cleanupManager.totalCount)")
                                    .fontWeight(.medium)
                            }
                        }
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                
                // 操作按钮
                VStack(spacing: 15) {
                    Button(action: checkCleanupNeeded) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                            Text("检查需要清理的地址")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(cleanupManager.isProcessing)
                    
                    Button(action: {
                        if needCleanupCount > 0 {
                            showingConfirmation = true
                        }
                    }) {
                        HStack {
                            Image(systemName: "trash.fill")
                            Text("开始清理地址")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(needCleanupCount > 0 ? Color.orange : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(cleanupManager.isProcessing || needCleanupCount == 0)
                    
                    if cleanupManager.isProcessing {
                        Button(action: {
                            cleanupManager.resetStatus()
                        }) {
                            HStack {
                                Image(systemName: "stop.fill")
                                Text("重置状态")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                    }
                }
                
                Spacer()
                
                // 警告信息
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("注意事项")
                            .fontWeight(.bold)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("• 此操作会修改数据库中的地址数据")
                        Text("• 建议在执行前备份数据")
                        Text("• 清理过程中请勿关闭应用")
                        Text("• 清理完成后地址显示将恢复正常")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            }
            .padding()
            .navigationTitle("地址清理工具")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                checkCleanupNeeded()
            }
            .alert("确认清理", isPresented: $showingConfirmation) {
                Button("取消", role: .cancel) { }
                Button("开始清理", role: .destructive) {
                    Task {
                        await cleanupManager.startCleanup()
                        // 清理完成后重新检查
                        checkCleanupNeeded()
                    }
                }
            } message: {
                Text("即将清理 \(needCleanupCount) 个地址中的管道符号和元数据信息。此操作不可撤销，确定要继续吗？")
            }
        }
    }
    
    private func checkCleanupNeeded() {
        needCleanupCount = cleanupManager.checkCleanupNeeded()
    }
}

#Preview {
    AddressCleanupView()
}
