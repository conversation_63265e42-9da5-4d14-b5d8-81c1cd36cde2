import SwiftUI

/// 地址数据库修复工具界面
/// 提供扫描和修复数据库中有问题地址的功能
struct AddressDatabaseRepairView: View {
    @State private var isScanning = false
    @State private var isRepairing = false
    @State private var problematicAddresses: [String] = []
    @State private var repairResults: [String] = []
    @State private var showingResults = false
    @State private var specificAddress = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(alignment: .leading, spacing: 10) {
                    Text("地址数据库修复工具")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("检测和修复数据库中缺少州缩写的美国地址")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                Divider()
                
                // 扫描功能
                VStack(alignment: .leading, spacing: 15) {
                    Text("1. 扫描问题地址")
                        .font(.headline)
                    
                    Text("扫描数据库中所有缺少州缩写的美国地址")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Button(action: scanAddresses) {
                        HStack {
                            if isScanning {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "magnifyingglass")
                            }
                            Text(isScanning ? "扫描中..." : "开始扫描")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(isScanning || isRepairing)
                    
                    if !problematicAddresses.isEmpty {
                        Text("发现 \(problematicAddresses.count) 个需要修复的地址")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                Divider()
                
                // 批量修复功能
                VStack(alignment: .leading, spacing: 15) {
                    Text("2. 批量修复")
                        .font(.headline)
                    
                    Text("自动修复所有发现的问题地址")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Button(action: repairAllAddresses) {
                        HStack {
                            if isRepairing {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "wrench.and.screwdriver")
                            }
                            Text(isRepairing ? "修复中..." : "修复所有地址")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(problematicAddresses.isEmpty ? Color.gray : Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(problematicAddresses.isEmpty || isScanning || isRepairing)
                }
                
                Divider()
                
                // 单个地址修复功能
                VStack(alignment: .leading, spacing: 15) {
                    Text("3. 修复指定地址")
                        .font(.headline)
                    
                    Text("输入特定地址进行修复")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextField("输入地址，例如：3420 Tupelo Dr, 95209", text: $specificAddress)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button(action: repairSpecificAddress) {
                        HStack {
                            Image(systemName: "wrench")
                            Text("修复此地址")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(specificAddress.isEmpty ? Color.gray : Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(specificAddress.isEmpty || isScanning || isRepairing)
                }
                
                Spacer()
                
                // 问题地址列表
                if !problematicAddresses.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("发现的问题地址:")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 5) {
                                ForEach(problematicAddresses, id: \.self) { address in
                                    Text(address)
                                        .font(.caption)
                                        .padding(.horizontal, 10)
                                        .padding(.vertical, 5)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(5)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                }
            }
            .padding()
            .navigationTitle("地址修复工具")
            .navigationBarTitleDisplayMode(.inline)
            .alert("修复结果", isPresented: $showingResults) {
                Button("确定") { }
            } message: {
                Text(repairResults.joined(separator: "\n"))
            }
        }
    }
    
    // MARK: - 功能方法
    
    /// 扫描问题地址
    private func scanAddresses() {
        isScanning = true
        problematicAddresses.removeAll()
        
        Task {
            let addresses = await AddressDatabaseRepairService.shared.scanForProblematicAddresses()
            
            await MainActor.run {
                self.problematicAddresses = addresses
                self.isScanning = false
            }
        }
    }
    
    /// 修复所有地址
    private func repairAllAddresses() {
        guard !problematicAddresses.isEmpty else { return }
        
        isRepairing = true
        repairResults.removeAll()
        
        Task {
            await AddressDatabaseRepairService.shared.repairSpecificAddresses(problematicAddresses)
            
            await MainActor.run {
                self.repairResults = ["批量修复完成", "已处理 \(problematicAddresses.count) 个地址"]
                self.isRepairing = false
                self.showingResults = true
                
                // 重新扫描以更新列表
                scanAddresses()
            }
        }
    }
    
    /// 修复指定地址
    private func repairSpecificAddress() {
        guard !specificAddress.isEmpty else { return }
        
        isRepairing = true
        repairResults.removeAll()
        
        Task {
            await AddressDatabaseRepairService.shared.repairMissingStateAddresses(specificAddress: specificAddress)
            
            await MainActor.run {
                self.repairResults = ["指定地址修复完成", "地址: \(specificAddress)"]
                self.isRepairing = false
                self.showingResults = true
                self.specificAddress = ""
                
                // 重新扫描以更新列表
                scanAddresses()
            }
        }
    }
}

#Preview {
    AddressDatabaseRepairView()
}
