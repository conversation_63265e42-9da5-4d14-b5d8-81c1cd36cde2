//
//  UserAddressDatabaseManagerView.swift
//  NaviBatch
//
//  Created for v1.0.4 - User Address Database Management
//  Copyright © 2024 NaviBatch. All rights reserved.
//

import SwiftUI
import SwiftData
import CoreLocation

/// 🏠 用户地址数据库管理工具界面
struct UserAddressDatabaseManagerView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    
    @State private var isLoading = false
    @State private var isProcessing = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    @State private var databaseStats: AddressDatabaseStats?
    @State private var cleaningResult: (cleaned: Int, total: Int)?
    @State private var showingConfirmation = false
    @State private var confirmationAction: (() -> Void)?
    @State private var confirmationTitle = ""
    @State private var confirmationMessage = ""
    
    @Query private var allValidatedAddresses: [ValidatedAddress]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 📊 数据库统计信息
                    databaseStatsSection
                    
                    // 🧹 数据清理工具
                    dataCleaningSection
                    
                    // 🔧 数据库管理工具
                    databaseManagementSection
                    
                    // 📋 地址列表预览
                    addressPreviewSection
                    
                    // 📝 结果显示
                    resultsSection
                }
                .padding()
            }
            .navigationTitle("用户地址数据库管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadDatabaseStats()
            }
            .alert(confirmationTitle, isPresented: $showingConfirmation) {
                Button("取消", role: .cancel) { }
                Button("确认", role: .destructive) {
                    confirmationAction?()
                }
            } message: {
                Text(confirmationMessage)
            }
        }
    }
    
    // MARK: - 数据库统计信息
    private var databaseStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📊 数据库统计")
                .font(.headline)
                .fontWeight(.bold)
            
            if let stats = databaseStats {
                VStack(spacing: 8) {
                    statRow(title: "总地址数", value: "\(stats.totalCount)")
                    statRow(title: "命中率", value: String(format: "%.1f%%", stats.hitRate * 100))
                    statRow(title: "数据库大小", value: formatBytes(stats.databaseSize))
                }
            } else {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("加载统计信息...")
                        .foregroundColor(.secondary)
                }
            }
            
            Button("刷新统计") {
                loadDatabaseStats()
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - 数据清理工具
    private var dataCleaningSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🧹 数据清理工具")
                .font(.headline)
                .fontWeight(.bold)
            
            Text("清理地址中的元数据信息（如订单号、排序信息等）")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            if let result = cleaningResult {
                VStack(alignment: .leading, spacing: 4) {
                    Text("上次清理结果:")
                        .font(.caption)
                        .fontWeight(.medium)
                    Text("清理了 \(result.cleaned)/\(result.total) 条记录")
                        .font(.caption)
                        .foregroundColor(result.cleaned > 0 ? .green : .blue)
                }
                .padding(.vertical, 4)
            }
            
            Button(action: startAddressCleaning) {
                HStack {
                    if isProcessing {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("正在清理...")
                    } else {
                        Image(systemName: "sparkles")
                        Text("清理地址元数据")
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(isProcessing ? Color.gray : Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(isProcessing || allValidatedAddresses.isEmpty)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - 数据库管理工具
    private var databaseManagementSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🔧 数据库管理")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                Button(action: exportDatabase) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("导出数据库")
                        Spacer()
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                }
                
                Button(action: confirmClearDatabase) {
                    HStack {
                        Image(systemName: "trash")
                        Text("清空数据库")
                        Spacer()
                    }
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - 地址列表预览
    private var addressPreviewSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📋 地址预览 (最近10条)")
                .font(.headline)
                .fontWeight(.bold)
            
            if allValidatedAddresses.isEmpty {
                Text("数据库为空")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(allValidatedAddresses.prefix(10)), id: \.id) { address in
                        addressRow(address)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - 结果显示
    private var resultsSection: some View {
        VStack(spacing: 8) {
            if let successMessage = successMessage {
                Text(successMessage)
                    .font(.caption)
                    .foregroundColor(.green)
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
            }
            
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
    
    // MARK: - 辅助视图
    private func statRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
    
    private func addressRow(_ address: ValidatedAddress) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(address.originalAddress)
                .font(.subheadline)
                .lineLimit(2)
            
            HStack {
                Text("来源: \(AddressSource(rawValue: address.source)?.rawValue ?? "未知")")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("使用: \(address.usageCount)次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("置信度: \(String(format: "%.1f", address.confidence))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(Color(.systemGray6))
        .cornerRadius(6)
    }
    
    // MARK: - 方法
    private func loadDatabaseStats() {
        isLoading = true
        Task {
            await UserAddressDatabase.shared.updateDatabaseStats()
            await MainActor.run {
                self.databaseStats = UserAddressDatabase.shared.databaseStats
                self.isLoading = false
            }
        }
    }
    
    private func startAddressCleaning() {
        isProcessing = true
        errorMessage = nil
        successMessage = nil
        
        Task {
            let result = await UserAddressDatabase.shared.cleanExistingAddressMetadata()
            
            await MainActor.run {
                self.cleaningResult = result
                self.isProcessing = false
                
                if result.cleaned > 0 {
                    self.successMessage = "✅ 成功清理了 \(result.cleaned)/\(result.total) 条地址记录"
                } else {
                    self.successMessage = "✅ 所有地址都是纯净的，无需清理"
                }
                
                // 刷新统计信息
                loadDatabaseStats()
            }
        }
    }
    
    private func confirmClearDatabase() {
        confirmationTitle = "清空用户地址数据库"
        confirmationMessage = "此操作将删除所有用户地址数据库记录，无法恢复。确定要继续吗？"
        confirmationAction = clearDatabase
        showingConfirmation = true
    }
    
    private func clearDatabase() {
        isProcessing = true
        errorMessage = nil
        successMessage = nil
        
        Task {
            await UserAddressDatabase.shared.clearDatabase()
            
            await MainActor.run {
                self.isProcessing = false
                self.successMessage = "✅ 用户地址数据库已清空"
                self.cleaningResult = nil
                
                // 刷新统计信息
                loadDatabaseStats()
            }
        }
    }
    
    private func exportDatabase() {
        // TODO: 实现数据库导出功能
        successMessage = "📤 导出功能开发中..."
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

#Preview {
    UserAddressDatabaseManagerView()
}
