import SwiftUI
import CoreLocation

// 演示免费用户和付费用户的地图访问权限差异
struct MapAccessDemo: View {
    @State private var showFreeUserView = true
    
    var body: some View {
        VStack(spacing: 30) {
            Text("地图访问权限演示")
                .font(.title2)
                .padding()
            
            Text("现在所有用户都可以看到相同的界面")
                .font(.subheadline)
                .foregroundColor(.green)
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(8)

            // 显示统一的界面
            UnifiedMapView()
            
            Spacer()
        }
        .padding()
    }
}

// 所有用户看到的统一地图界面
struct UnifiedMapView: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("统一用户界面")
                .font(.headline)
                .foregroundColor(.blue)
            
            // 模拟MapMarkerCalloutView - 免费用户版本
            VStack(spacing: 0) {
                // 基本信息区域 - 免费用户可见
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("#1")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                        
                        Spacer()
                        
                        Text("1个包裹")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.orange.opacity(0.2))
                            .foregroundColor(.orange)
                            .cornerRadius(4)
                    }
                    
                    Text("18 Kerferd Road")
                        .font(.headline)
                    
                    Text("Glen Waverley, VIC, 3150, AU")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                
                Divider()
                
                // 底部按钮区域 - 导航 + 实景图按钮
                HStack(spacing: 0) {
                    Button(action: {}) {
                        HStack {
                            Image(systemName: "location.fill")
                            Text("导航")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                    }

                    Button(action: {}) {
                        HStack {
                            Image(systemName: "binoculars.fill")
                            Text("查看实景")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                    }
                }
            }
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
            
            // 功能说明
            VStack(alignment: .leading, spacing: 8) {
                Text("所有用户现在都可以：")
                    .font(.headline)

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("查看地址详细信息")
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("点击导航按钮打开地图导航")
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("查看包裹数量和排序编号")
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("查看实景图（已移除Pro限制）")
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
    }
}

// 已移除PaidUserMapView，现在所有用户看到相同界面

#Preview("MapAccessDemo") {
    MapAccessDemo()
}
