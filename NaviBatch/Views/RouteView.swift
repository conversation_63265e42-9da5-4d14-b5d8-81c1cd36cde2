import SwiftUI
import MapKit
import SwiftData
import os.log
import UIKit
import CoreLocation
import Combine
import Foundation

// 用于跟踪sheet高度的PreferenceKey
struct SheetHeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}
import VisionKit

struct RouteView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Query(sort: \DeliveryGroup.groupNumber, order: .forward) private var deliveryGroups: [DeliveryGroup]
    @StateObject private var viewModel = RouteViewModel.shared
    @State private var selectedPoint: DeliveryPoint?
    @State private var selectedPointForEdit: DeliveryPoint?
    @State private var showingPointPopover = false
    @State private var showRouteInfoSheet = false
    @State private var showAddressEditSheet = false
    @State private var showBatchFixSheet = false
    @State private var showingDeliveryPointManagerSheet = false
    @State private var deliveryPointWrapper: DeliveryPointWrapper? = nil // 使用包装类保存配送点
    @State private var routeSheetHeight: CGFloat = 0
    @State private var showMapPreview = false
    @State private var showSaveSuccessToast = false
    @State private var showLocationToast = false
    @State private var showRouteOptimizedToast = false
    @State private var showGroupCreatedToast = false
    @State private var showOrderSavedToast = false
    @State private var showAutoGroupToast = false
    @State private var isAutoGrouping = false
    @State private var showDeliveryCounter = true
    @State private var deliveryCountAnimation: Bool = false
    @State private var isCollapsibleButtonGroupExpanded = false
    // showSubscriptionToast已移除 - 不再显示调试信息
    @Environment(\.scenePhase) private var scenePhase
    @State private var routeChangedObserver: NSObjectProtocol? = nil
    @State private var statusChangedObserver: NSObjectProtocol?  // 新增：配送点状态变化观察者
    @State private var showingTextImportSheet = false
    // 添加状态变量跟踪是否显示已派送点卡片
    @State private var showingDeliveredPointCard: Bool = false



    // 添加状态变量跟踪是否显示优化按钮
    @State private var showOptimizeButton = false
    @State private var optimizeButtonTimer: Timer? = nil

    // 地址验证和订阅限制相关状态
    @State private var showingInvalidAddressAlert = false
    @State private var invalidAddressIndices: [Int] = []
    @State private var showingSubscriptionLimitAlert = false
    @State private var totalPoints: Int = 0
    @State private var optimizablePoints: Int = 0

    // 按钮组位置控制
    @State private var buttonsOnLeftSide = UserDefaults.standard.bool(forKey: "buttonsOnLeftSide")

    // 日志辅助函数 - 使用统一的Logger系统
    private func logInfo(_ message: String, function: String = #function) {
        Logger.info("[RouteView.\(function)] \(message)", type: .lifecycle)
    }

    private func logError(_ message: String, function: String = #function) {
        Logger.error("[RouteView.\(function)] \(message)", type: .error)
    }

    // 🎯 查找在相同坐标的所有点
    private func findPointsAtSameCoordinate(_ coordinate: CLLocationCoordinate2D) -> [DeliveryPoint] {
        let tolerance = 0.000001 // 坐标容差
        return viewModel.deliveryPoints.filter { point in
            abs(point.coordinate.latitude - coordinate.latitude) < tolerance &&
            abs(point.coordinate.longitude - coordinate.longitude) < tolerance
        }
    }

    // 🎯 显示重叠标记选择菜单
    private func showOverlapSelectionMenu(for points: [DeliveryPoint], at coordinate: CLLocationCoordinate2D) {
        overlappingPoints = points.sorted { $0.sorted_number < $1.sorted_number }
        showingOverlapMenu = true
        logInfo("🎯 显示重叠选择菜单，包含 \(points.count) 个点: \(points.map { $0.sorted_number })")
    }

    @State private var searchText = ""
    @State private var isSearching = false
    @State private var searchResults: [DeliveryPoint] = []

    @State private var addressText = ""
    @State private var isAddingAddress = false
    @State private var isAppFirstLaunch = true
    @State private var showingMenuSheet = false

    // 统一的 sheet 管理
    enum ActiveSheet: Identifiable, Equatable {
        case menu
        case addressEdit(DeliveryPoint)
        case deliveryPointManager(DeliveryPointWrapper)
        case statusUpdate(DeliveryPoint)
        case batchFix
        case groupNameInput
        case savedGroups
        case subscription
        case routeInfo
        case routeSheet
        case numberInput
        case mapModeSelector

        var id: String {
            switch self {
            case .menu: return "menu"
            case .addressEdit: return "addressEdit"
            case .deliveryPointManager: return "deliveryPointManager"
            case .statusUpdate: return "statusUpdate"
            case .batchFix: return "batchFix"
            case .groupNameInput: return "groupNameInput"
            case .savedGroups: return "savedGroups"
            case .subscription: return "subscription"
            case .routeInfo: return "routeInfo"
            case .routeSheet: return "routeSheet"
            case .numberInput: return "numberInput"
            case .mapModeSelector: return "mapModeSelector"
            }
        }

        // 实现Equatable协议
        static func == (lhs: ActiveSheet, rhs: ActiveSheet) -> Bool {
            switch (lhs, rhs) {
            case (.menu, .menu),
                 (.batchFix, .batchFix),
                 (.groupNameInput, .groupNameInput),
                 (.savedGroups, .savedGroups),
                 (.subscription, .subscription),
                 (.routeInfo, .routeInfo),
                 (.routeSheet, .routeSheet),
                 (.numberInput, .numberInput),
                 (.mapModeSelector, .mapModeSelector):
                return true
            case (.addressEdit(let lhsPoint), .addressEdit(let rhsPoint)):
                return lhsPoint.id == rhsPoint.id
            case (.deliveryPointManager(let lhsWrapper), .deliveryPointManager(let rhsWrapper)):
                return lhsWrapper.point.id == rhsWrapper.point.id
            case (.statusUpdate(let lhsPoint), .statusUpdate(let rhsPoint)):
                return lhsPoint.id == rhsPoint.id
            default:
                return false
            }
        }
    }

    @State private var activeSheet: ActiveSheet? = nil

    // 地图样式状态
    @State private var isStandardMapStyle: Bool = true
    @State private var selectedMapMode: MapDisplayMode = .driving
    @State private var showMapModeSelector = false

    // 添加状态变量来跟踪bottom sheet是否应该显示
    @State private var shouldShowRouteSheetButton = false
    @State private var userDismissedBottomSheet = false // 用户是否主动关闭了bottom sheet
    @State private var savedGroupsSheetDismissedByUser = false // 跟踪SavedGroups sheet是否被用户主动关闭
    @State private var menuSheetDismissedByUser = false // 跟踪Menu sheet是否被用户主动关闭
    @State private var subscriptionDismissedByUser = false // 跟踪Subscription是否被用户主动关闭
    @State private var isLookAroundActive = false // 是否有实景图正在加载或显示
    @State private var statusUpdateJustClosed = false // 临时标记，防止StatusUpdate关闭后立即关闭RouteBottomSheet

    @State private var isPresentingRouteModal = false
    @State private var shouldShowBottomSheet = false
    @State private var shouldShowGroupResultsSheet = false
    @State private var isEditing = false
    @State private var showSelectionLimitAlert = false
    @State private var hasFinishedInitialLoad = false
    @State private var lastMapChangeLogTime: TimeInterval = 0 // 添加地图变化日志时间戳
    @State private var lastMapState: String = "" // 地图状态缓存，用于减少重复日志
    @State private var selectedMarkerID: UUID? // 原生地图标记选择状态

    // 定位功能相关状态
    @State private var showingNumberInput = false
    @State private var inputNumber = ""

    // 状态更新相关
    @State private var pointForStatusUpdate: DeliveryPoint? = nil

    // 问题地址Alert相关
    @State private var showingProblemAddressAlert = false
    @State private var problemAddressAlertTitle = ""
    @State private var problemAddressAlertMessage = ""

    // API限制降级方案提示
    @State private var showingAPILimitAlert = false
    @State private var apiLimitMessage = ""

    // 真实路线更新提示
    @State private var showingRealRouteToast = false

    // 清空所有分组Alert相关
    @State private var showDeleteAllGroupsAlert = false

    // 🎯 重叠标记选择菜单状态
    @State private var showingOverlapMenu = false
    @State private var overlappingPoints: [DeliveryPoint] = []
    @State private var overlapMenuPosition: CGPoint = .zero

    var body: some View {
        contentWithLifecycle
    }

    private var contentWithAlerts: some View {
        mainContentView
            .addressValidationAlert(
                isPresented: $showingInvalidAddressAlert,
                invalidIndices: invalidAddressIndices,
                onFix: { showBatchFixSheet = true }
            )
            .subscriptionLimitAlert(
                isPresented: $showingSubscriptionLimitAlert,
                totalPoints: totalPoints,
                optimizablePoints: optimizablePoints,
                onUpgrade: {
                    logInfo("RouteView - 从订阅限制Alert打开Menu界面")
                    activeSheet = .menu
                },
                onContinue: { startOptimization() }
            )
            .alert(problemAddressAlertTitle, isPresented: $showingProblemAddressAlert) {
                Button("知道了") {
                    // 清空问题地址收集器
                    ProblemAddressCollector.shared.clearAllProblemAddresses()
                    logInfo("RouteView - 用户确认问题地址Alert，已清空问题地址收集器")
                }
                Button("查看详情") {
                    // 可以在这里添加跳转到问题地址详情页面的逻辑
                    logInfo("RouteView - 用户选择查看问题地址详情")
                    // 暂时也清空收集器
                    ProblemAddressCollector.shared.clearAllProblemAddresses()
                }
            } message: {
                Text(problemAddressAlertMessage)
            }
            .alert("路线计算提示", isPresented: $showingAPILimitAlert) {
                Button("知道了") { }
            } message: {
                Text(apiLimitMessage)
            }
    }

    // 主要内容视图
    private var mainContentView: some View {
        GeometryReader { geometry in
            ZStack {
                backgroundView
                mapView
                overlayControls
                navigationPopover
                bottomButtonsLayout
                toastNotifications
                progressIndicators
                selectionLimitAlert
            }
            .edgesIgnoringSafeArea(.all)
        }
    }

    private var contentWithLifecycle: some View {
        contentWithStateChanges
            .onAppear {
                setupViewModelAndObservers()
                setupAsyncTasks()
            }
            .onDisappear {
                cleanupObservers()
            }
            .environment(\.scenePhase, scenePhase)
            .navigationBarHidden(true)
            .sheet(item: Binding(
                get: {
                    // 只有非subscription的sheet才使用sheet展示
                    activeSheet?.id == "subscription" ? nil : activeSheet
                },
                set: { activeSheet = $0 }
            )) { sheet in
                sheetContent(for: sheet)
            }
            // 🎯 重叠标记选择菜单
            .sheet(isPresented: $showingOverlapMenu) {
                OverlapSelectionMenuView(
                    points: overlappingPoints,
                    onPointSelected: { point in
                        showingOverlapMenu = false
                        handleMarkerTap(point)
                    },
                    onCancel: {
                        showingOverlapMenu = false
                    }
                )
                .presentationDetents([.height(300)])
                .presentationDragIndicator(.visible)
            }

    }

    private var contentWithStateChanges: some View {
        contentWithFirstGroup
            // 注意：订阅提示现在通过打开Menu界面处理，不再使用Alert
            .onChange(of: viewModel.showRouteSheet) { oldValue, newValue in
                if newValue {
                    activeSheet = .routeSheet
                    viewModel.showRouteSheet = false
                }
            }
            .onChange(of: viewModel.showRouteInfo) { oldValue, newValue in
                if newValue {
                    activeSheet = .routeInfo
                    viewModel.showRouteInfo = false
                }
            }
            .onChange(of: showBatchFixSheet) { oldValue, newValue in
                if newValue {
                    activeSheet = .batchFix
                    showBatchFixSheet = false
                }
            }
            .onChange(of: selectedPointForEdit) { oldValue, newValue in
                if let point = newValue {
                    activeSheet = .addressEdit(point)
                    selectedPointForEdit = nil
                }
            }
            .onChange(of: deliveryPointWrapper) { oldValue, newValue in
                if let wrapper = newValue {
                    activeSheet = .deliveryPointManager(wrapper)
                    deliveryPointWrapper = nil
                }
            }
            .onChange(of: pointForStatusUpdate) { oldValue, newValue in
                if let point = newValue {
                    activeSheet = .statusUpdate(point)
                    pointForStatusUpdate = nil
                }
            }
            .onChange(of: showingNumberInput) { oldValue, newValue in
                if newValue {
                    activeSheet = .numberInput
                    showingNumberInput = false
                }
            }
            .onChange(of: showMapModeSelector) { oldValue, newValue in
                if newValue {
                    activeSheet = .mapModeSelector
                    showMapModeSelector = false
                }
            }
            .onChange(of: selectedMapMode) { oldValue, newValue in
                // 同步更新旧的地图样式状态以保持兼容性
                isStandardMapStyle = (newValue == .driving)
                logInfo("地图模式切换到: \(newValue.displayName)")
            }
    }

    private var contentWithFirstGroup: some View {
        contentWithNotifications
            .onChange(of: activeSheet) { oldValue, newValue in
                // 更专业的备用按钮逻辑：只有当没有任何sheet显示且用户主动关闭了bottom sheet时才显示
                shouldShowRouteSheetButton = (newValue == nil && userDismissedBottomSheet)

                // 记录sheet状态变化
                logInfo("ActiveSheet状态变化: \(oldValue?.id ?? "nil") -> \(newValue?.id ?? "nil")")
                logInfo("备用按钮显示状态: \(shouldShowRouteSheetButton) (activeSheet=\(newValue?.id ?? "nil"), userDismissed=\(userDismissedBottomSheet))")

                // 当没有任何sheet显示时，自动显示默认的bottom sheet
                if newValue == nil && !userDismissedBottomSheet && !isLookAroundActive {
                    logInfo("检测到所有sheet已关闭，准备自动显示默认bottom sheet")

                    // 减少延迟并添加更强的状态检查
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        // 再次检查条件，确保没有新的sheet被显示，且实景图不活跃
                        if activeSheet == nil && !userDismissedBottomSheet && !isLookAroundActive {
                            logInfo("自动显示默认bottom sheet")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                activeSheet = .routeSheet
                            }
                        } else {
                            logInfo("跳过自动显示: activeSheet=\(activeSheet?.id ?? "nil"), userDismissed=\(userDismissedBottomSheet), lookAroundActive=\(isLookAroundActive)")
                        }
                    }
                } else if newValue != nil {
                    // 当显示其他sheet时，重置用户关闭标记（除非是routeSheet）
                    if newValue != .routeSheet && userDismissedBottomSheet {
                        logInfo("显示其他sheet，重置用户关闭标记")
                        userDismissedBottomSheet = false
                    }
                }
            }

            .onChange(of: viewModel.showingGroupNameInput) { oldValue, newValue in
                if newValue {
                    activeSheet = .groupNameInput
                    viewModel.showingGroupNameInput = false
                }
            }
    }

    private var contentWithNotifications: some View {
        contentWithAlerts
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("LookAroundActivated"))) { _ in
                print("[DEBUG] RouteView - 收到实景图激活通知")
                isLookAroundActive = true
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("LookAroundDeactivated"))) { _ in
                print("[DEBUG] RouteView - 收到实景图关闭通知")
                isLookAroundActive = false
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ShowStatusUpdateSheet"))) { notification in
                if let point = notification.object as? DeliveryPoint {
                    logInfo("RouteView - 收到状态更新请求: \(point.primaryAddress)")
                    pointForStatusUpdate = point

                    // 保存展开状态信息，以便关闭时恢复
                    if let userInfo = notification.userInfo,
                       let expandedIdString = userInfo["expandedPointId"] as? String {
                        UserDefaults.standard.set(expandedIdString, forKey: "StatusUpdateExpandedPointId")
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ShowProblemAddressAlert"))) { notification in
                if let userInfo = notification.userInfo,
                   let title = userInfo["title"] as? String,
                   let message = userInfo["message"] as? String {
                    logInfo("RouteView - 收到问题地址Alert请求")
                    problemAddressAlertTitle = title
                    problemAddressAlertMessage = message
                    showingProblemAddressAlert = true
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RouteCalculationFallback"))) { notification in
                if let userInfo = notification.userInfo,
                   let message = userInfo["message"] as? String {
                    logInfo("RouteView - 收到路线计算降级通知")
                    apiLimitMessage = message
                    showingAPILimitAlert = true
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ForceRefreshRouteView"))) { _ in
            // 当收到强制刷新通知时，确保视图被正确刷新
            logInfo("RouteView - 收到强制刷新通知")

            // 确保当前路线数据已正确加载
            if let route = viewModel.currentRoute {
                logInfo("RouteView - 当前路线: \(route.name), ID: \(route.id.uuidString)")

                // 验证路线是否仍然存在于数据库中
                Task {
                    do {
                        // 从当前ModelContext中查询路线
                        let routeId = route.id
                        let descriptor = FetchDescriptor<Route>(predicate: #Predicate { $0.id == routeId })

                        if let validRoute = try modelContext.fetch(descriptor).first {
                            logInfo("RouteView - 已验证路线存在: \(validRoute.name), ID: \(validRoute.id.uuidString)")

                            // 确保使用有效的路线
                            viewModel.currentRoute = validRoute

                            // 强制刷新配送点
                            await viewModel.setupDeliveryPoints()
                            logInfo("RouteView - 已刷新配送点数据")
                        } else {
                            logInfo("RouteView - 路线不存在于数据库中，尝试创建新路线")

                            // 创建新路线
                            let newRoute = Route(name: "新路线 \(Date().formatted(.dateTime))")
                            modelContext.insert(newRoute)
                            try modelContext.save()

                            // 设置为当前路线
                            viewModel.currentRoute = newRoute
                            viewModel.isRouteNewlyCreated = true

                            // 刷新配送点
                            await viewModel.setupDeliveryPoints()
                            logInfo("RouteView - 已创建新路线并刷新配送点数据")
                        }
                    } catch {
                        logError("RouteView - 验证路线时出错: \(error.localizedDescription)")

                        // 出错时尝试创建新路线
                        do {
                            let newRoute = Route(name: "新路线 \(Date().formatted(.dateTime))")
                            modelContext.insert(newRoute)
                            try modelContext.save()

                            // 设置为当前路线
                            viewModel.currentRoute = newRoute
                            viewModel.isRouteNewlyCreated = true

                            // 刷新配送点
                            await viewModel.setupDeliveryPoints()
                            logInfo("RouteView - 出错后已创建新路线并刷新配送点数据")
                        } catch {
                            logError("RouteView - 创建新路线时出错: \(error.localizedDescription)")
                        }
                    }
                }
            } else {
                logInfo("RouteView - 当前没有选中的路线，尝试创建新路线")

                // 如果没有当前路线，尝试创建新路线
                Task {
                    do {
                        let newRoute = Route(name: "新路线 \(Date().formatted(.dateTime))")
                        modelContext.insert(newRoute)
                        try modelContext.save()

                        // 设置为当前路线
                        viewModel.currentRoute = newRoute
                        viewModel.isRouteNewlyCreated = true

                        // 刷新配送点
                        await viewModel.setupDeliveryPoints()
                        logInfo("RouteView - 已创建新路线并刷新配送点数据")
                    } catch {
                        logError("RouteView - 创建新路线时出错: \(error.localizedDescription)")
                    }
                }
            }
        }
    }

    // 统一的 sheet 内容管理
    @ViewBuilder
    private func sheetContent(for sheet: ActiveSheet) -> some View {
        switch sheet {
        case .menu:
            NavigationView {
                MenuView(onDismiss: {
                    menuSheetDismissedByUser = true
                    activeSheet = .routeSheet // 恢复bottom sheet
                    logInfo("RouteView - Menu已关闭，恢复bottom sheet")
                })
                .navigationTitle("Menu")
                .navigationBarTitleDisplayMode(.inline)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .topBarTrailing) {
                        Button(action: {
                            menuSheetDismissedByUser = true
                            activeSheet = .routeSheet // 恢复bottom sheet
                            logInfo("RouteView - Menu通过X按钮关闭，恢复bottom sheet")
                         }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                                .font(.system(size: 20))
                        }
                    }
                }
            }
            .presentationDetents([.medium, .large], selection: .constant(.large))
            .presentationDragIndicator(.visible) // ✅ 显示Apple标准的拖拽指示器
            .presentationCornerRadius(12)
            .presentationBackground(.regularMaterial)
            .interactiveDismissDisabled(false) // ✅ 允许所有标准的关闭方式
            .onDisappear {
                // 处理拖拽关闭的情况，使用延迟避免与其他关闭方式冲突
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    // 只有当activeSheet不再是menu时，才认为是真正的关闭
                    if activeSheet != .menu {
                        if menuSheetDismissedByUser {
                            logInfo("Menu sheet被用户主动关闭，重置标记")
                            menuSheetDismissedByUser = false
                        } else {
                            logInfo("Menu sheet被拖拽关闭，恢复bottom sheet")
                        }
                        // 无论如何关闭，都恢复bottom sheet
                        if activeSheet == nil {
                            activeSheet = .routeSheet
                        }
                    } else {
                        logInfo("Menu sheet仍然活跃，跳过关闭处理")
                    }
                }
            }

        case .addressEdit(let point):
            AddressEditBottomSheet(deliveryPoint: point)

        case .deliveryPointManager(let wrapper):
            ZStack {
                Color(.systemBackground)
                    .ignoresSafeArea()

                DeliveryPointManagerView(deliveryPoint: wrapper.point)
                    .onAppear {
                        logInfo("RouteView - DeliveryPointManagerSheet出现，point=\(wrapper.point.primaryAddress)")
                    }
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
            .presentationCornerRadius(12)
            .presentationBackground(.regularMaterial)

        case .statusUpdate(let point):
            StatusUpdateSheet(
                deliveryPoint: point,
                onStatusSelected: { newStatus, failureReason, customReason in
                    // 更新配送状态
                    point.updateStatus(newStatus, failureReason: failureReason, customReason: customReason)

                    // 保存到数据库
                    do {
                        try modelContext.save()
                        logInfo("RouteView - 状态更新成功保存: \(point.primaryAddress) -> \(newStatus)")
                    } catch {
                        logError("RouteView - 状态更新保存失败: \(error.localizedDescription)")
                    }

                    // 关闭sheet并恢复展开状态
                    statusUpdateJustClosed = true
                    activeSheet = .routeSheet

                    // 发送通知恢复展开状态
                    let expandedIdString = UserDefaults.standard.string(forKey: "StatusUpdateExpandedPointId") ?? ""
                    NotificationCenter.default.post(
                        name: Notification.Name("StatusUpdateSheetClosed"),
                        object: nil,
                        userInfo: ["expandedPointId": expandedIdString]
                    )

                    // 清理临时保存的展开状态
                    UserDefaults.standard.removeObject(forKey: "StatusUpdateExpandedPointId")

                    // 1秒后重置保护标记
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        statusUpdateJustClosed = false
                    }
                }
            )
            .presentationDetents([.medium])
            .presentationDragIndicator(.visible)
            .presentationCornerRadius(12)
            .presentationBackground(.regularMaterial)
            .onDisappear {
                // 确保StatusUpdateSheet关闭后总是恢复到RouteBottomSheet
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    if activeSheet != .statusUpdate(point) {
                        logInfo("RouteView - StatusUpdateSheet关闭，恢复RouteBottomSheet")

                        statusUpdateJustClosed = true
                        activeSheet = .routeSheet

                        // 发送通知恢复展开状态
                        let expandedIdString = UserDefaults.standard.string(forKey: "StatusUpdateExpandedPointId") ?? ""
                        NotificationCenter.default.post(
                            name: Notification.Name("StatusUpdateSheetClosed"),
                            object: nil,
                            userInfo: ["expandedPointId": expandedIdString]
                        )

                        // 清理临时保存的展开状态
                        UserDefaults.standard.removeObject(forKey: "StatusUpdateExpandedPointId")

                        // 1秒后重置保护标记
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            statusUpdateJustClosed = false
                        }
                    }
                }
            }

        case .batchFix:
            if let problematicPoints = getProblematicPoints() {
                BatchAddressFixSheet(problematicPoints: problematicPoints)
            }

        case .groupNameInput:
            groupNameInputSheet

        case .savedGroups:
            optimizedSavedGroupsSheet
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible) // ✅ 显示Apple标准的拖拽指示器
            .presentationCornerRadius(12)
            .presentationBackground(.regularMaterial)
            .interactiveDismissDisabled(false) // ✅ 允许所有标准的关闭方式
            .onDisappear {
                // 处理拖拽关闭的情况，使用延迟避免与其他关闭方式冲突
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    // 只有当activeSheet不再是savedGroups时，才认为是真正的关闭
                    if activeSheet != .savedGroups {
                        if savedGroupsSheetDismissedByUser {
                            logInfo("SavedGroups sheet被用户主动关闭，重置标记")
                            savedGroupsSheetDismissedByUser = false
                        } else {
                            logInfo("SavedGroups sheet被拖拽关闭，恢复bottom sheet")
                        }
                        // 无论如何关闭，都恢复bottom sheet
                        if activeSheet == nil {
                            activeSheet = .routeSheet
                        }
                    } else {
                        logInfo("SavedGroups sheet仍然活跃，跳过关闭处理")
                    }
                }
            }

        case .subscription:
            // subscription现在使用Alert，这里不应该被调用
            EmptyView()

        case .routeInfo:
            routeInfoBottomSheet
                .presentationBackgroundInteraction(.enabled)
                .interactiveDismissDisabled(false)
                .onAppear {
                    routeSheetHeight = 300
                }
                .background(
                    GeometryReader { geo in
                        Color.clear
                            .preference(key: SheetHeightPreferenceKey.self, value: geo.size.height)
                            .onPreferenceChange(SheetHeightPreferenceKey.self) { height in
                                routeSheetHeight = height
                            }
                    }
                )

        case .numberInput:
            VStack(spacing: 20) {
                // 简化标题
                Text("quick_locate".localized)
                    .font(.title3)
                    .fontWeight(.semibold)

                // 输入框
                TextField("please_enter_number".localized, text: $inputNumber)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.numberPad)
                    .font(.title2)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 24)

                // 按钮组
                HStack(spacing: 16) {
                    Button("cancel".localized) {
                        inputNumber = ""
                        activeSheet = .routeSheet
                    }
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)

                    Button("locate".localized) {
                        guard let number = Int(inputNumber) else {
                            logError("输入的编号无效: \(inputNumber)")
                            return
                        }

                        // 查找对应的地址点 - 统一使用sorted_number搜索
                        let targetPoint = viewModel.deliveryPoints.first { point in
                            // 🎯 统一使用sorted_number进行搜索，与显示逻辑保持一致
                            return point.sorted_number == number
                        }

                        if let point = targetPoint {
                            // 定位到该地址点
                            withAnimation(.easeInOut(duration: 0.8)) {
                                viewModel.cameraPosition = .region(MKCoordinateRegion(
                                    center: point.coordinate,
                                    span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
                                ))
                            }

                            // 🎯 修复：正确设置所有选中状态，确保后续点击能正常工作
                            selectedPoint = point
                            selectedMarkerID = point.id
                            showingPointPopover = true  // 关键修复：设置为true，表示callout应该显示
                            showingDeliveredPointCard = false  // 重置其他状态

                            logInfo("成功定位到编号 \(number) 的地址: \(point.primaryAddress)")
                            logInfo("快速定位 - 设置选中状态: selectedPoint=\(point.primaryAddress), showingPointPopover=true")

                            // 关闭输入框并返回地图
                            inputNumber = ""
                            activeSheet = nil
                        } else {
                            logError(String(format: "number_not_found".localized, number))
                        }
                    }
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(inputNumber.isEmpty ? Color.gray : Color.blue)
                    .cornerRadius(12)
                    .disabled(inputNumber.isEmpty)
                }
                .padding(.horizontal, 24)
            }
            .padding(.vertical, 24)
            .presentationDetents([.height(200)])
            .presentationDragIndicator(.visible)
            .presentationCornerRadius(16)
            .presentationBackground(.regularMaterial)

        case .routeSheet:
            RouteBottomSheet(viewModel: viewModel)
                .presentationDetents([.height(25), .medium, .large])
                .presentationDragIndicator(.visible)
                .presentationBackgroundInteraction(.enabled(upThrough: .height(25)))
                .interactiveDismissDisabled(true) // 🔒 像Apple Maps一样，不允许下拉关闭
                .presentationCornerRadius(12)

        case .mapModeSelector:
            MapModeSelector(
                selectedMapMode: $selectedMapMode,
                isPresented: Binding(
                    get: { activeSheet == .mapModeSelector },
                    set: { newValue in
                        if !newValue {
                            // 关闭地图模式选择器，恢复到路线sheet
                            activeSheet = .routeSheet
                        }
                    }
                )
            )
            .presentationDetents([.height(300)])
            .presentationDragIndicator(.visible)
            .presentationCornerRadius(16)
            .presentationBackground(.regularMaterial)
        }
    }



    // 简化的备用按钮点击处理
    private func showRouteSheetFromButton() {
        userDismissedBottomSheet = false
        activeSheet = .routeSheet
    }

    // MARK: - 分解的子视图组件

    // 背景视图
    private var backgroundView: some View {
        Color(.systemBackground)
            .ignoresSafeArea(.all)
            .overlay {
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(1.5)
                }
            }
    }

    // 底部按钮布局
    private var bottomButtonsLayout: some View {
        VStack {
            Spacer()
            HStack {
                if buttonsOnLeftSide {
                    rightBottomButtons
                    Spacer()
                } else {
                    Spacer()
                    rightBottomButtons
                }
            }
            .padding(.horizontal, 8)
            .padding(.bottom, viewModel.showRouteSheet ? 200 : 80)
        }
    }

    // Toast通知组
    private var toastNotifications: some View {
        Group {
            routeOptimizedToast
            saveSuccessToast
            groupCreatedToast
            autoGroupToast
        }
    }

    // 进度指示器组
    private var progressIndicators: some View {
        Group {
            autoGroupingProgressIndicator
            routeOptimizationProgressIndicator
        }
    }

    // 路线优化提示Toast
    private var routeOptimizedToast: some View {
        Group {
            if showRouteOptimizedToast {
                VStack {
                    Spacer()
                    HStack {
                        Image(systemName: "arrow.triangle.swap")
                            .foregroundColor(.white)
                        Text("route_optimized".localized)
                            .foregroundColor(.white)
                            .font(.subheadline)
                            .bold()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.orange.opacity(0.9))
                    .cornerRadius(20)
                    .padding(.bottom, 100)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .animation(.easeInOut, value: showRouteOptimizedToast)
                .zIndex(1000)
            }
        }
    }

    // 保存路线提示Toast
    private var saveSuccessToast: some View {
        Group {
            if showSaveSuccessToast {
                VStack {
                    Spacer()
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)
                        Text("route_saved".localized)
                            .foregroundColor(.white)
                            .font(.subheadline)
                            .bold()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.green.opacity(0.9))
                    .cornerRadius(20)
                    .padding(.bottom, 100)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .animation(.easeInOut, value: showSaveSuccessToast)
                .zIndex(1000)
            }
        }
    }

    // 分组创建成功提示Toast
    private var groupCreatedToast: some View {
        Group {
            if showGroupCreatedToast {
                VStack {
                    Spacer()
                    HStack {
                        Image(systemName: "rectangle.stack.badge.plus.fill")
                            .foregroundColor(.white)
                        Text("group_created".localized)
                            .foregroundColor(.white)
                            .font(.subheadline)
                            .bold()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.blue.opacity(0.9))
                    .cornerRadius(20)
                    .padding(.bottom, 100)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .animation(.easeInOut, value: showGroupCreatedToast)
                .zIndex(1000)
            }
        }
    }

    // 自动分组成功提示Toast
    private var autoGroupToast: some View {
        Group {
            if showAutoGroupToast {
                VStack {
                    Spacer()
                    HStack {
                        Image(systemName: "rectangle.stack.fill.badge.plus")
                            .foregroundColor(.white)
                        Text("auto_grouping_completed".localized)
                            .foregroundColor(.white)
                            .font(.subheadline)
                            .bold()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.purple.opacity(0.9))
                    .cornerRadius(20)
                    .padding(.bottom, 100)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .animation(.easeInOut, value: showAutoGroupToast)
                .zIndex(1000)
            }
        }
    }

    // 自动分组进度指示器
    private var autoGroupingProgressIndicator: some View {
        Group {
            if isAutoGrouping {
                ZStack {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)

                    VStack(spacing: 16) {
                        Image(systemName: "rectangle.stack.fill.badge.plus")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(viewModel.optimizingRotationDegrees))
                            .onAppear {
                                withAnimation(Animation.linear(duration: 2).repeatForever(autoreverses: false)) {
                                    viewModel.optimizingRotationDegrees = 360
                                }
                            }

                        Text("auto_grouping_in_progress".localized)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.5)
                            .padding(.vertical, 8)

                        Text("create_group_every_14_addresses".localized)
                            .foregroundColor(.white.opacity(0.9))
                            .font(.footnote)
                            .padding(.top, 4)
                    }
                    .padding(24)
                    .background(Color.purple.opacity(0.6))
                    .cornerRadius(16)
                    .shadow(radius: 10)
                }
                .zIndex(2000)
                .transition(.opacity)
                .animation(.easeInOut, value: isAutoGrouping)
            }
        }
    }

    // 路线优化进度指示器
    private var routeOptimizationProgressIndicator: some View {
        Group {
            if viewModel.isOptimizingRoute {
                ZStack {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)

                    VStack(spacing: 16) {
                        Image(systemName: "arrow.triangle.swap")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(viewModel.optimizingRotationDegrees))
                            .onAppear {
                                withAnimation(Animation.linear(duration: 2).repeatForever(autoreverses: false)) {
                                    viewModel.optimizingRotationDegrees = 360
                                }
                            }

                        Text("optimizing_route".localized)
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        ProgressView(value: viewModel.optimizationProgress)
                            .progressViewStyle(LinearProgressViewStyle())
                            .frame(width: 200)
                            .tint(.blue)

                        Text(String(format: "completed_percent".localized, Int(viewModel.optimizationProgress * 100)))
                            .foregroundColor(.white)
                            .font(.callout)

                        if viewModel.totalPoints > 0 {
                            Text(String(format: "processing_points".localized, viewModel.currentProcessingPoint, viewModel.totalPoints))
                                .foregroundColor(.white.opacity(0.8))
                                .font(.subheadline)
                        }

                        Text(viewModel.currentOptimizationPhase.description)
                            .foregroundColor(.white.opacity(0.9))
                            .font(.footnote)
                            .padding(.top, 4)

                        if let remainingTime = viewModel.estimatedRemainingTime {
                            Text(String(format: "estimated_remaining_time".localized, remainingTime))
                                .foregroundColor(.white.opacity(0.8))
                                .font(.subheadline)
                        }
                    }
                    .padding(24)
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(16)
                    .shadow(radius: 10)
                }
                .zIndex(2000)
                .transition(.opacity)
                .animation(.easeInOut, value: viewModel.isOptimizingRoute)
            }
        }
    }

    // 选择上限提示弹窗
    private var selectionLimitAlert: some View {
        Group {
            if viewModel.showSelectionLimitAlert {
                VStack {
                    Text("selection_limit_reached".localized)
                        .font(.headline)
                        .padding(.bottom, 4)
                    Text(String(format: "selection_limit_description".localized, viewModel.remainingSelectableCount + viewModel.selectedPoints.count, viewModel.selectedPoints.count))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    Button(action: {
                        viewModel.showSelectionLimitAlert = false
                    }) {
                        Text("understand".localized)
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.blue)
                            .cornerRadius(8)
                    }
                    .padding(.top, 12)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(Color(.systemBackground).opacity(0.95))
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 2)
                .padding(.horizontal, 40)
                .padding(.vertical, 20)
                .transition(.scale.combined(with: .opacity))
                .animation(.spring(), value: viewModel.showSelectionLimitAlert)
                .zIndex(1001)
            }
        }
    }

    // MARK: - 生命周期方法

    private func setupViewModelAndObservers() {
        logInfo("RouteView - .onAppear: 设置ViewModel上下文和观察者")

        let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

        if viewModel.modelContext == nil {
            viewModel.setModelContext(modelContext)
            logInfo("RouteView - .onAppear: 已设置ModelContext")
        } else {
            if let url = viewModel.modelContext?.container.configurations.first?.url, url.path == "/dev/null" && !isPreview {
                logInfo("RouteView - .onAppear: 当前使用内存数据库，切换到持久化存储")
                viewModel.setModelContext(modelContext)
            } else {
                if isPreview {
                    logInfo("RouteView - .onAppear: 预览环境中使用当前 ModelContext")
                } else {
                    logInfo("RouteView - .onAppear: 当前已使用持久化存储")
                }
            }
        }

        setupRouteChangeObserver()
        setupStatusChangeObserver()
    }

    private func setupRouteChangeObserver() {
        if routeChangedObserver == nil {
            routeChangedObserver = NotificationCenter.default.addObserver(
                forName: Notification.Name("SelectedRouteChanged"),
                object: nil,
                queue: .main
            ) { [self] notification in
                handleRouteChangeNotification(notification)
            }
            logInfo("RouteView - .onAppear: Notification observer added")
        } else {
            logInfo("RouteView - .onAppear: Observer already exists, skipping")
        }
    }

    private func setupStatusChangeObserver() {
        if statusChangedObserver == nil {
            statusChangedObserver = NotificationCenter.default.addObserver(
                forName: .deliveryStatusChanged,
                object: nil,
                queue: .main
            ) { [self] notification in
                handleStatusChangeNotification(notification)
            }
            logInfo("RouteView - .onAppear: Status change observer added")
        }
    }

    private func setupAsyncTasks() {
        Task { @MainActor in
            logInfo("RouteView - .onAppear: Starting async setup Task")

            if viewModel.currentRoute == nil {
                logInfo("RouteView - .onAppear Task: currentRoute is nil, checking/creating default route...")
                await viewModel.checkAndCreateDefaultRoute()
                if let currentRoute = viewModel.currentRoute {
                    logInfo("RouteView - .onAppear Task: Default route check/create resulted in route: \(currentRoute.name), ID: \(currentRoute.id)")
                } else {
                    logError("RouteView - .onAppear Task: checkAndCreateDefaultRoute did NOT result in a route.")
                }
            } else {
                logInfo("RouteView - .onAppear Task: currentRoute was already set (by notification or sync setup), ID: \(viewModel.currentRoute!.id)")
            }

            if let currentRoute = viewModel.currentRoute {
                logInfo("RouteView - .onAppear Task: Loading delivery points for route: \(currentRoute.name), \(currentRoute.points.count) points")
                await viewModel.setupDeliveryPoints()
                logInfo("RouteView - .onAppear Task: Delivery points setup complete: \(viewModel.deliveryPoints.count)")

                logInfo("RouteView - .onAppear Task: 准备显示默认bottom sheet")
                // 确保在应用启动时显示默认的bottom sheet
                // 重置用户关闭标记，确保应用启动时能正常显示bottom sheet
                userDismissedBottomSheet = false
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    if activeSheet == nil && !userDismissedBottomSheet {
                        logInfo("RouteView - .onAppear Task: 自动显示默认bottom sheet")
                        activeSheet = .routeSheet
                    } else {
                        logInfo("RouteView - .onAppear Task: 跳过自动显示: activeSheet=\(activeSheet?.id ?? "nil"), userDismissed=\(userDismissedBottomSheet)")
                    }
                }

                if let bounds = viewModel.getDeliveryPointsBounds() {
                    withAnimation {
                        viewModel.cameraPosition = .region(bounds)
                    }
                    logInfo("RouteView - .onAppear Task: 已更新地图位置以显示路线")
                }
            }
            logInfo("RouteView - .onAppear finished")
        }
    }

    private func cleanupObservers() {
        if let observer = routeChangedObserver {
            NotificationCenter.default.removeObserver(observer)
            routeChangedObserver = nil
        }

        if let observer = statusChangedObserver {
            NotificationCenter.default.removeObserver(observer)
            statusChangedObserver = nil
        }
    }



    // MARK: - 通知处理方法

    private func handleRouteChangeNotification(_ notification: Notification) {
        if let routeId = notification.object as? String,
           let uuid = UUID(uuidString: routeId) {
            logInfo("RouteView - 接收到路线选择变更通知: \(routeId)")

            Task { @MainActor in
                do {
                    let descriptor = FetchDescriptor<Route>(predicate: #Predicate { $0.id == uuid })
                    if let selectedRoute = try modelContext.fetch(descriptor).first {
                        logInfo("RouteView - 找到选定的路线: \(selectedRoute.name)")

                        viewModel.setModelContext(modelContext)
                        viewModel.currentRoute = selectedRoute
                        viewModel.isRouteNewlyCreated = false

                        let pointCount = selectedRoute.points.count
                        logInfo("RouteView - 选定的路线'\(selectedRoute.name)'包含 \(pointCount) 个地址点")

                        await viewModel.setupDeliveryPoints()

                        NotificationCenter.default.post(
                            name: Notification.Name("ForceRefreshRouteView"),
                            object: nil
                        )

                        if let bounds = viewModel.getDeliveryPointsBounds() {
                            withAnimation {
                                viewModel.cameraPosition = .region(bounds)
                            }
                            logInfo("RouteView - 已更新地图位置以显示选定路线")
                        } else {
                            logInfo("RouteView - 无法获取路线地图边界，使用默认位置")
                            if viewModel.driverLocation == nil {
                                viewModel.useFallbackLocation()
                            }

                            let defaultRegion = MKCoordinateRegion(
                                center: viewModel.driverLocation ?? CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631),
                                span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
                            )
                            withAnimation {
                                viewModel.cameraPosition = .region(defaultRegion)
                            }
                        }

                        logInfo("RouteView - 已将路线'\(selectedRoute.name)'设置为当前路线")
                    } else {
                        logError("RouteView - 找不到ID为\(routeId)的路线，尝试创建新路线")
                        await createNewRoute()
                    }
                } catch {
                    logError("RouteView - 处理路线选择时出错: \(error.localizedDescription)")
                }
            }
        }
    }

    private func handleStatusChangeNotification(_ notification: Notification) {
        if let pointID = notification.object as? UUID {
            logInfo("RouteView - 收到配送点状态变更通知，点ID: \(pointID)")

            Task { @MainActor in
                if let point = viewModel.deliveryPoints.first(where: { $0.id == pointID }) {
                    logInfo("RouteView - 找到状态变更的配送点: \(point.primaryAddress)，状态: \(point.status)，是否已分配到组: \(point.isAssignedToGroup)，组号: \(point.assignedGroupNumber ?? -1)")

                    if (point.deliveryStatus == .completed || point.deliveryStatus == .failed) && point.isAssignedToGroup {
                        let statusText = point.deliveryStatus == .completed ? "已完成" : "失败"
                        logInfo("RouteView - 配送点 \(point.primaryAddress) 状态为\(statusText)且已分配到组，准备从组中移除")

                        await viewModel.removeCompletedPointFromGroup(point)
                        logInfo("RouteView - 已调用 removeCompletedPointFromGroup 来移除点 \(point.primaryAddress)")

                        if point.isAssignedToGroup == false && point.assignedGroupNumber == nil {
                            logInfo("RouteView - 配送点 \(point.primaryAddress) 成功从组中移除")
                        } else {
                            logError("RouteView - 配送点 \(point.primaryAddress) 移除失败，仍显示为属于组")
                        }
                    }

                    await viewModel.setupDeliveryPoints()
                    logInfo("RouteView - 已更新配送点数据")

                    if viewModel.selectedPoints.contains(pointID) && (point.deliveryStatus == .completed || point.deliveryStatus == .failed) {
                        viewModel.selectedPoints.removeAll(where: { $0 == pointID })
                        viewModel.selectedPointsOrder.removeAll(where: { $0 == pointID })
                        let statusText = point.deliveryStatus == .completed ? "已完成" : "失败"
                        logInfo("RouteView - 已从选中列表中移除\(statusText)的配送点 \(point.primaryAddress)")
                    }

                    viewModel.objectWillChange.send()
                    logInfo("RouteView - 配送点 \(point.primaryAddress) 状态已变更为 \(point.status)，地图视图已更新")
                } else {
                    logError("RouteView - 未找到ID为 \(pointID) 的配送点")
                }
            }
        }
    }

    private func createNewRoute() async {
        do {
            let newRoute = Route(name: "新路线 \(Date().formatted(.dateTime))")
            modelContext.insert(newRoute)
            try modelContext.save()

            viewModel.modelContext = modelContext
            viewModel.currentRoute = newRoute
            viewModel.isRouteNewlyCreated = true

            await viewModel.setupDeliveryPoints()
            logInfo("RouteView - 已创建新路线: \(newRoute.name)")
        } catch {
            logError("RouteView - 创建新路线时出错: \(error.localizedDescription)")
        }
    }

    // setupSheetObservers方法已移除，改用onChange监听器

    // 用于地图注解的数据结构
    private struct PointAnnotation: Identifiable {
        // 使用DeliveryPoint的ID作为标记点的ID，确保稳定性
        var id: UUID
        let coordinate: CLLocationCoordinate2D // 显示坐标（可能是偏移后的）
        let originalCoordinate: CLLocationCoordinate2D // 原始坐标
        let displayNumber: Int
        let packageCount: Int
        let color: Color
        let isAssignedToGroup: Bool
        let groupNumber: Int?
        let pointType: PointType // 添加点类型
        let isCompleted: Bool // 添加是否已完成
        let isFailed: Bool // 添加是否失败
        let hasCoordinateWarning: Bool // 🚨 添加坐标警告标识
        let isOffset: Bool // 是否是偏移的标记
    }

    private struct ClusterAnnotation: Identifiable {
        let id = UUID()
        let coordinate: CLLocationCoordinate2D
        let count: Int
    }

    // 获取点的颜色 - 适配暗黑模式
    private func getPointColor(_ point: DeliveryPoint) -> Color {
        // 首先检查配送状态
        if point.deliveryStatus == .completed {
            // 已完成配送使用绿色 - 在暗黑模式下使用更亮的绿色
            return colorScheme == .dark ? Color.green.opacity(0.9) : Color.green
        } else if point.deliveryStatus == .failed {
            // 配送失败使用红色 - 在暗黑模式下使用更亮的红色
            return colorScheme == .dark ? Color.red.opacity(0.9) : Color.red
        } else if point.isStartPoint {
            // 起点使用蓝色 - 在暗黑模式下使用更亮的蓝色
            return colorScheme == .dark ? Color.blue.opacity(0.9) : Color.blue
        } else if point.isEndPoint {
            // 终点使用绿色 - 在暗黑模式下使用更亮的绿色
            return colorScheme == .dark ? Color.green.opacity(0.9) : Color.green
        } else if point.isOptimized {
            // 已优化的途经点使用主题色 - 黑底白字
            return Color.black
        } else {
            // 未优化的途经点使用蓝色 - 在暗黑模式下使用更亮的蓝色
            return colorScheme == .dark ? Color.blue.opacity(0.9) : Color.blue
        }
    }

    // 预处理地图标记数据
    private func prepareMapAnnotations() -> (singlePoints: [PointAnnotation], clusters: [ClusterAnnotation]) {
        var singlePointAnnotations: [PointAnnotation] = []
        var clusterAnnotations: [ClusterAnnotation] = []



        guard let currentRoute = viewModel.currentRoute else {
            logInfo("prepareMapAnnotations - 当前路线为空，无法显示标记点")
            return (singlePointAnnotations, clusterAnnotations)
        }

        // 🎯 计算相同坐标点的分离偏移
        let coordinateOffsets = calculateCoordinateOffsets(for: currentRoute.points)

        // 🎯 对点进行排序，优先使用第三方排序号，然后AI扫描地址保持原始顺序，手动地址实现错误地址优先
        let sortedPoints = currentRoute.points.sorted { point1, point2 in
            // 1. 起点总是最前面
            let point1IsStart = point1.isStartPoint || point1.sort_number == 0
            let point2IsStart = point2.isStartPoint || point2.sort_number == 0

            if point1IsStart && !point2IsStart { return true }
            if !point1IsStart && point2IsStart { return false }

            // 2. 🎯 新逻辑：优先使用第三方排序号
            let point1ThirdParty = point1.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            let point2ThirdParty = point2.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

            let point1HasThirdParty = !point1ThirdParty.isEmpty
            let point2HasThirdParty = !point2ThirdParty.isEmpty

            // 如果都有第三方排序号，按第三方排序号排序
            if point1HasThirdParty && point2HasThirdParty {
                let num1 = extractNumber(from: point1ThirdParty)
                let num2 = extractNumber(from: point2ThirdParty)
                return num1 < num2
            }

            // 有第三方排序号的优先排在前面
            if point1HasThirdParty && !point2HasThirdParty { return true }
            if !point1HasThirdParty && point2HasThirdParty { return false }

            // 3. 都没有第三方排序号时，使用原有逻辑
            let point1IsAIScanned = point1.sourceApp != .manual
            let point2IsAIScanned = point2.sourceApp != .manual

            // 如果都是AI扫描的地址，直接按sort_number排序，不考虑错误状态
            if point1IsAIScanned && point2IsAIScanned {
                if currentRoute.isOptimized {
                    return point1.sorted_number < point2.sorted_number
                } else {
                    return point1.sort_number < point2.sort_number
                }
            }

            // 如果都是手动输入的地址，使用错误地址优先逻辑
            if !point1IsAIScanned && !point2IsAIScanned {
                let point1HasError = (point1.geocodingWarning != nil && !point1.geocodingWarning!.isEmpty) ||
                                     (LocationValidationStatus(rawValue: point1.locationValidationStatus) ?? .unknown) != .valid
                let point2HasError = (point2.geocodingWarning != nil && !point2.geocodingWarning!.isEmpty) ||
                                     (LocationValidationStatus(rawValue: point2.locationValidationStatus) ?? .unknown) != .valid

                if point1HasError && !point2HasError { return true }
                if !point1HasError && point2HasError { return false }
            }

            // 3. 根据路线是否优化选择排序字段
            if currentRoute.isOptimized {
                return point1.sorted_number < point2.sorted_number
            } else {
                return point1.sort_number < point2.sort_number
            }
        }

        // 恢复过滤已完成派送的地址点，除非用户选择显示它们
        let statusFilteredPoints = sortedPoints.filter { point in
            // 始终显示起点和终点，不过滤
            if point.isStartPoint || point.isEndPoint {
                return true
            }

            // 在多选模式下，显示所有地址点
            if viewModel.isMultiSelectMode {
                return true
            }

            // 如果是已完成状态，只有当showCompletedDeliveries=true时才显示
            if point.deliveryStatus == .completed {
                return viewModel.showCompletedDeliveries
            }

            // 如果是失败状态，只有当showCompletedDeliveries=true时才显示
            if point.deliveryStatus == .failed {
                return viewModel.showCompletedDeliveries
            }

            // 其他状态的地址点正常显示
            return true
        }

        // 移除性能优化逻辑，始终显示所有过滤后的点
        let filteredPoints = statusFilteredPoints

        if statusFilteredPoints.count > 100 {
            logInfo("prepareMapAnnotations - 显示所有\(statusFilteredPoints.count)个点")
        }

        // 创建单个标记点，不需要聚类
        for point in filteredPoints {
            // 确定点类型
            let pointType: PointType
            if point.isStartPoint && point.isEndPoint {
                // 同时是起点和终点
                pointType = .startEnd
            } else if point.isStartPoint {
                pointType = .start
            } else if point.isEndPoint {
                pointType = .end
            } else {
                pointType = .waypoint
            }

            // 检查是否为已完成的配送点
            let isCompleted = point.deliveryStatus == .completed
            // 检查是否为失败的配送点
            let isFailed = point.deliveryStatus == .failed

            // 🎯 优先显示第三方排序号的数字部分
            let displayNumber: Int = {
                if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                    let extractedNumber = extractNumber(from: thirdPartySortNumber)
                    logInfo("🎯 使用第三方号码: point.id=\(point.id), thirdPartySortNumber='\(thirdPartySortNumber)', extractedNumber=\(extractedNumber)")
                    return extractedNumber
                } else {
                    logInfo("🎯 使用sorted_number: point.id=\(point.id), sorted_number=\(point.sorted_number), thirdPartySortNumber=\(point.thirdPartySortNumber ?? "nil")")
                    return point.sorted_number // 统一使用sorted_number
                }
            }()

            // 🔍 调试：记录显示编号
            if displayNumber < 0 || displayNumber > 99 {
                logInfo("🔍 异常显示编号: point.id=\(point.id), displayNumber=\(displayNumber), sort_number=\(point.sort_number), sorted_number=\(point.sorted_number), isOptimized=\(point.isOptimized)")
            }

            // 🚨 检查是否有坐标警告 - 暂时禁用以恢复正常颜色显示
            let hasCoordinateWarning = false
            // 原始检测逻辑（暂时注释）：
            // let hasCoordinateWarning = (point.geocodingWarning != nil && !point.geocodingWarning!.isEmpty) ||
            //                          (LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown) != .valid ||
            //                          (point.latitude == 0 && point.longitude == 0) ||
            //                          point.latitude < -90 || point.latitude > 90 ||
            //                          point.longitude < -180 || point.longitude > 180

            // 🔍 调试：记录标记创建信息
            if displayNumber == 35 || displayNumber == 36 {
                logInfo("🔍 创建标记: displayNumber=\(displayNumber), point.id=\(point.id), sort_number=\(point.sort_number), sorted_number=\(point.sorted_number)")
            }

            // 🎯 使用偏移后的坐标来分离重叠标记
            let offsetCoordinate = coordinateOffsets[point.id] ?? point.coordinate
            let isOffset = coordinateOffsets[point.id] != nil

            singlePointAnnotations.append(
                PointAnnotation(
                    id: point.id, // 使用DeliveryPoint的ID作为标记点的ID
                    coordinate: offsetCoordinate, // 使用偏移后的坐标
                    originalCoordinate: point.coordinate, // 保存原始坐标
                    displayNumber: displayNumber,
                    packageCount: point.packageCount,
                    color: getPointColor(point),
                    isAssignedToGroup: point.isAssignedToGroup,
                    groupNumber: point.assignedGroupNumber,
                    pointType: pointType, // 传递点类型
                    isCompleted: isCompleted, // 传递配送完成状态
                    isFailed: isFailed, // 传递配送失败状态
                    hasCoordinateWarning: hasCoordinateWarning, // 🚨 传递坐标警告状态
                    isOffset: isOffset // 标记是否被偏移
                )
            )
        }

        // 🔍 验证数据一致性
        validateAnnotationConsistency(singlePointAnnotations, filteredPoints)

        // 🎯 暂时禁用聚类功能，先确保基础数字显示正常
        // 当地址数量较多时，使用聚类来避免标记重叠
        if false && filteredPoints.count > 15 {
            // 根据地址数量动态调整聚类半径
            let clusterRadius: Double
            if filteredPoints.count > 100 {
                clusterRadius = 0.02  // 大量地址时使用更大的聚类半径
            } else if filteredPoints.count > 50 {
                clusterRadius = 0.015 // 中等数量时使用中等半径
            } else {
                clusterRadius = 0.01  // 较少地址时使用较小半径
            }

            // 创建聚类
            let clusters = createClusters(from: filteredPoints, clusterRadius: clusterRadius)

            logInfo("🎯 聚类功能启用: 总点数=\(filteredPoints.count), 聚类数=\(clusters.count), 半径=\(clusterRadius)")

            // 处理聚类结果
            for cluster in clusters {
                if cluster.points.count == 1 {
                    // 单个点，添加到单点数组
                    let point = cluster.points[0]

                    // 🎯 优先显示第三方排序号的数字部分
                    let displayNumber: Int = {
                        if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                            return extractNumber(from: thirdPartySortNumber)
                        } else {
                            return point.sorted_number // 统一使用sorted_number
                        }
                    }()

                    // 确定点的类型
                    let pointType: PointType
                    if point.isStartPoint && point.isEndPoint {
                        pointType = .startEnd
                    } else if point.isStartPoint {
                        pointType = .start
                    } else if point.isEndPoint {
                        pointType = .end
                    } else {
                        pointType = .waypoint
                    }

                    let isCompleted = point.deliveryStatus == .completed
                    let isFailed = point.deliveryStatus == .failed
                    let hasCoordinateWarning = point.geocodingWarning != nil

                    // 🎯 使用偏移后的坐标来分离重叠标记
                    let offsetCoordinate = coordinateOffsets[point.id] ?? point.coordinate
                    let isOffset = coordinateOffsets[point.id] != nil

                    singlePointAnnotations.append(
                        PointAnnotation(
                            id: point.id,
                            coordinate: offsetCoordinate, // 使用偏移后的坐标
                            originalCoordinate: point.coordinate, // 保存原始坐标
                            displayNumber: displayNumber,
                            packageCount: point.packageCount,
                            color: getPointColor(point),
                            isAssignedToGroup: point.isAssignedToGroup,
                            groupNumber: point.assignedGroupNumber,
                            pointType: pointType,
                            isCompleted: isCompleted,
                            isFailed: isFailed,
                            hasCoordinateWarning: hasCoordinateWarning,
                            isOffset: isOffset // 标记是否被偏移
                        )
                    )
                } else {
                    // 多个点，创建聚类标记
                    clusterAnnotations.append(
                        ClusterAnnotation(
                            coordinate: cluster.coordinate,
                            count: cluster.points.count
                        )
                    )
                }
            }
        }

        return (singlePointAnnotations, clusterAnnotations)
    }

    // 地图视图
    private var mapView: some View {
        // 将数据预处理移到 Map 视图之外
        let (singlePointAnnotations, clusterAnnotations) = prepareMapAnnotations()



        // 记录当前地图状态 - 仅在DEBUG模式输出详细日志
        #if DEBUG
        // 避免警告 - 使用下划线忽略未使用的变量或直接移除
        _ = viewModel.driverLocation != nil ?
            "(\(viewModel.driverLocation!.latitude), \(viewModel.driverLocation!.longitude))" : "nil"

        // 只在实际需要使用的地方创建变量
        #if VERBOSE_LOGGING
        // 创建相机位置的字符串表示
        var cameraPositionString = "unknown"

        // 使用字符串插值直接描述相机位置
        if viewModel.cameraPosition == .automatic {
            cameraPositionString = "automatic"
        } else {
            // 获取相机位置的区域信息（如果有）
            if let region = viewModel.cameraPosition.region {
                cameraPositionString = "region(center: (\(region.center.latitude), \(region.center.longitude)), span: (\(region.span.latitudeDelta), \(region.span.longitudeDelta)))"
            } else {
                cameraPositionString = "custom position"
            }
        }

        logInfo("当前相机位置: \(cameraPositionString)")
        #endif

        // 🎯 优化：减少地图渲染日志频率，避免日志过度冗余
        // 只在点数发生变化或模式切换时记录日志
        let currentMapState = "\(singlePointAnnotations.count)_\(viewModel.isMultiSelectMode)"

        // 使用DispatchQueue.main.async避免在视图更新期间修改状态
        DispatchQueue.main.async {
            if currentMapState != lastMapState {
                logInfo("mapView - 渲染地图: 点数: \(singlePointAnnotations.count), 多选模式: \(viewModel.isMultiSelectMode)")
                lastMapState = currentMapState

                // 在多选模式下添加额外日志
                if viewModel.isMultiSelectMode {
                    logInfo("mapView - 多选模式下的标记点数量: \(singlePointAnnotations.count), 当前路线点数: \(viewModel.currentRoute?.points.count ?? 0)")
                }
            }
        }
        #endif

        return ZStack(alignment: .bottom) {
            Map(position: $viewModel.cameraPosition, selection: $selectedMarkerID) {
                // 🎯 为偏移的标记添加连接线，显示原始位置（仅在非多选模式下显示，避免与路线连接线混淆）
                if !viewModel.isMultiSelectMode {
                    ForEach(singlePointAnnotations.filter { $0.isOffset }, id: \.id) { annotation in
                        MapPolyline(coordinates: [annotation.originalCoordinate, annotation.coordinate])
                            .stroke(.gray.opacity(0.5), lineWidth: 1)
                    }
                }

                // 聚类标记
                ForEach(clusterAnnotations, id: \.id) { cluster in
                    Annotation("", coordinate: cluster.coordinate) {
                        // 根据聚类大小选择颜色
                        let clusterColor: Color = {
                            if cluster.count >= 10 {
                                return .red      // 大聚类用红色
                            } else if cluster.count >= 5 {
                                return .orange   // 中聚类用橙色
                            } else {
                                return .blue     // 小聚类用蓝色
                            }
                        }()

                        ClusterMarkerView(count: cluster.count, color: clusterColor)
                            .onTapGesture {
                                // 点击聚类标记时放大地图
                                zoomToCluster(coordinate: cluster.coordinate)
                            }
                    }
                }

                // 🚨 选中点路线连接线（使用实际路线坐标，跟随道路）
                if viewModel.isMultiSelectMode && !viewModel.routeConnections.isEmpty {
                    ForEach(viewModel.routeConnections) { connection in
                        // 🚨 使用完整的路线坐标点，确保跟随实际道路
                        MapPolyline(coordinates: connection.routeCoordinates)
                            .stroke(
                                LinearGradient(
                                    colors: [.blue, .cyan],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ),
                                style: StrokeStyle(lineWidth: 5, lineCap: .round, lineJoin: .round)
                            )
                    }
                }

                // 路线脉动点
                if viewModel.isMultiSelectMode && viewModel.selectedPoints.count > 1,
                   let pulsePosition = viewModel.routePulsePosition {
                    Annotation("", coordinate: pulsePosition) {
                        RoutePulseView()
                    }
                }

                // 显示用户位置 - 使用自定义的脉动标记
                if let location = viewModel.driverLocation {
                    Annotation("", coordinate: location) {
                        UserLocationPulsingView()
                    }
                }

                // 🎯 配送点标记放在最后渲染，确保数字显示在最上层（包括用户定位之上）
                ForEach(singlePointAnnotations, id: \.id) { annotation in
                    Annotation("", coordinate: annotation.coordinate) {
                        // 使用高密度小圆点标记
                        let isSelected = viewModel.isMultiSelectMode && viewModel.selectedPoints.contains(annotation.id)

                        // 🎯 RouteView统一显示sorted_number，不显示第三方排序号
                        // 与RouteMapView保持一致的显示策略
                        let customText: String? = nil

                        ZStack {
                            // 分解复杂表达式以避免编译器超时
                            let shouldFadeMarker = viewModel.isMultiSelectMode &&
                                                  viewModel.isMaxSelectionReached &&
                                                  !viewModel.selectedPoints.contains(annotation.id)
                            let markerColor = isSelected ? Color.orange : annotation.color

                            MarkerView(
                                number: annotation.displayNumber,
                                packageCount: annotation.packageCount,
                                color: markerColor,
                                isAssignedToGroup: annotation.isAssignedToGroup,
                                groupNumber: annotation.groupNumber,
                                shouldFade: shouldFadeMarker,
                                pointType: annotation.pointType,
                                isCompleted: annotation.isCompleted,
                                isFailed: annotation.isFailed,
                                customText: customText,
                                hasCoordinateWarning: annotation.hasCoordinateWarning
                            )

                            // 🎯 为偏移的标记添加虚线边框，表示它们被分离了
                            if annotation.isOffset {
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.blue.opacity(0.6), style: StrokeStyle(lineWidth: 2, dash: [5, 3]))
                                    .frame(width: 50, height: 50)
                                    .allowsHitTesting(false) // 🎯 关键：边框不拦截点击事件
                            }
                        }
                        .frame(width: 60, height: 60) // 🎯 确保足够大的点击区域
                        .contentShape(Rectangle()) // 🎯 整个区域都可以点击
                        .onTapGesture {
                            // 🔧 修复：处理标记点击，支持重复点击切换
                            // 添加调试日志来追踪点击问题
                            logInfo("🔍 点击标记: annotation.id=\(annotation.id), displayNumber=\(annotation.displayNumber), coordinate=(\(annotation.coordinate.latitude), \(annotation.coordinate.longitude)), isOffset=\(annotation.isOffset)")

                            if let point = viewModel.deliveryPoints.first(where: { $0.id == annotation.id }) {
                                logInfo("🔍 找到对应点: point.sorted_number=\(point.sorted_number), point.sort_number=\(point.sort_number)")

                                // 🎯 检查是否有其他标记在相同位置（真正重叠的情况）
                                let overlappingPoints = findPointsAtSameCoordinate(point.coordinate)
                                if overlappingPoints.count > 1 {
                                    // 检查是否所有重叠点都已经被分离
                                    let allPointsOffset = overlappingPoints.allSatisfy { overlappingPoint in
                                        singlePointAnnotations.first { $0.id == overlappingPoint.id }?.isOffset == true
                                    }

                                    if !allPointsOffset {
                                        // 有未分离的重叠点，显示选择菜单
                                        showOverlapSelectionMenu(for: overlappingPoints, at: point.coordinate)
                                        return
                                    }
                                }

                                // 正常处理单个点击
                                handleMarkerTap(point)
                            } else {
                                logError("🚨 未找到对应的DeliveryPoint: annotation.id=\(annotation.id), displayNumber=\(annotation.displayNumber)")

                                // 🔧 备用查找：尝试通过sorted_number查找
                                if let pointBySortedNumber = viewModel.deliveryPoints.first(where: { $0.sorted_number == annotation.displayNumber }) {
                                    logInfo("🔧 通过sorted_number找到点: \(pointBySortedNumber.sorted_number)")
                                    handleMarkerTap(pointBySortedNumber)
                                } else {
                                    logError("🚨 通过sorted_number也未找到对应点: displayNumber=\(annotation.displayNumber)")
                                }
                            }
                        }
                    }
                }


            }
            .onChange(of: selectedMarkerID) { _, newValue in
                // 这个onChange主要用于处理非多选模式下的点击选择
                if let markerID = newValue,
                   let point = viewModel.deliveryPoints.first(where: { $0.id == markerID }) {
                    // 在多选模式下，不通过onChange处理，避免重复处理
                    if !viewModel.isMultiSelectMode && selectedPoint?.id != markerID {
                        // 只有当选中的点确实发生变化时才处理
                        handlePointSelection(point)
                    }
                }
            }
            .mapStyle(selectedMapMode.mapStyle)
            // 移除内置的地图控件
            .mapControlVisibility(.hidden)
            .onMapCameraChange { context in
                // 仅记录重要的相机变化
                #if DEBUG
                // 防抖处理 - 使用节流方式减少日志输出
                let currentTime = Date().timeIntervalSince1970
                if currentTime - self.lastMapChangeLogTime > 1.0 { // 每秒最多记录一次
                    self.lastMapChangeLogTime = currentTime
                    logInfo("地图相机变化: 中心坐标 = (\(context.region.center.latitude), \(context.region.center.longitude))")
                }
                #endif
            }

            // 底部控制栏已移除，改为只使用顶部的 multiSelectToolbar
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.vertical, 4)
        .overlay(alignment: .topTrailing) {
            // 右上角地图控制按钮
            mapControlButtons
        }
    }

    // 地图右上角控制按钮
    private var mapControlButtons: some View {
        VStack(spacing: 8) {
            // 显示所有点切换按钮已移除 - 展示全部点，不进行性能优化
        }
        .padding(.top, 60) // 避免与状态栏重叠
        .padding(.trailing, 16)
    }

    // 覆盖控件
    private var overlayControls: some View {
        GeometryReader { geometry in
            ZStack {
                VStack(spacing: 0) {
                    // 顶部搜索栏和功能按钮区域
                    VStack(spacing: 0) {
                        // 安全区域空白 - 确保内容不会被状态栏遮挡
                        Color.clear
                            .frame(height: geometry.safeAreaInsets.top)

                        // 顶部空间 - 不添加额外的返回按钮，因为界面已经有返回按钮
                        Color.clear
                            .frame(height: 8)

                        // 多选模式工具栏 - 根据模式显示不同内容
                        if viewModel.isMultiSelectMode {
                            // 多选模式工具栏 - 保持与普通模式卡片相同位置
                            VStack {
                                // 使用相同的顶部间距，保持视觉连贯性
                                Color.clear.frame(height: 50)

                                multiSelectToolbar
                            }
                        }

                        // 路线优化卡片 - 如果有优化结果且不是多选模式时显示
                        if let info = viewModel.routeOptimizationInfo, !viewModel.isMultiSelectMode {
                            routeOptimizationCard(info: info)
                                .padding(.horizontal, 16)
                                .padding(.top, 8)
                                .transition(.move(edge: .top).combined(with: .opacity))
                                .animation(.spring(response: 0.4, dampingFraction: 0.75), value: viewModel.routeOptimizationInfo != nil)
                        }
                    }
                    // 确保中间区域不是黑色
                    .background(Color.clear)

                    Spacer()

                    // 移除底部控制按钮，因为我们已经直接在body中添加了按钮
                    Spacer()
                }

                // 已将待派送地址计数器移动到底部工具栏
            }
            .ignoresSafeArea(.keyboard)
        }
    }

    // 多选模式工具栏 - 移动到这里以保持代码组织结构

    // 多选模式工具栏
    private var multiSelectToolbar: some View {
        VStack(spacing: 8) {
            // 顶部信息栏
            HStack {
                // 左侧内容 - 合并显示选择数量和剩余分组数量
                VStack(alignment: .leading, spacing: 4) {
                    // 主标题：已选择地址数量
                    Text(String(format: "selected_addresses".localized, viewModel.selectedPoints.count))
                        .font(.headline)
                        .foregroundColor(.white)
                        .lineLimit(1)

                    // 显示剩余可选数量和距离信息 - 合并在一行显示
                    if !viewModel.isMaxSelectionReached {
                        HStack(spacing: 8) {
                            Text(String(format: "can_select_more".localized, viewModel.remainingSelectableCount))
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                                .lineLimit(1)

                            // 显示路线距离（当选择了2个或以上地点时）
                            if viewModel.selectedPoints.count > 1 {
                                HStack(spacing: 4) {
                                    Image(systemName: "point.topleft.down.curvedto.point.bottomright.up")
                                        .font(.caption)
                                        .foregroundColor(.white.opacity(0.8))

                                    // 根据计算状态显示不同内容
                                    switch viewModel.selectedPointsDistanceStatus {
                                    case .notCalculated, .calculating:
                                        HStack(spacing: 4) {
                                            ProgressView()
                                                .scaleEffect(0.5)
                                                .progressViewStyle(CircularProgressViewStyle(tint: .white.opacity(0.6)))
                                            Text("计算真实距离中...")
                                                .font(.caption)
                                                .foregroundColor(.white.opacity(0.8))
                                        }
                                    case .completed:
                                        HStack(spacing: 4) {
                                            Text(viewModel.getFormattedSelectedPointsDistance())
                                                .font(.caption)
                                                .foregroundColor(.white.opacity(0.8))
                                                .lineLimit(1)
                                            Image(systemName: "checkmark.circle")
                                                .font(.caption)
                                                .foregroundColor(.green.opacity(0.8))
                                        }
                                    case .fallback:
                                        HStack(spacing: 4) {
                                            Text("距离计算失败")
                                                .font(.caption)
                                                .foregroundColor(.red.opacity(0.8))
                                            Image(systemName: "exclamationmark.triangle")
                                                .font(.caption)
                                                .foregroundColor(.red.opacity(0.8))
                                        }
                                    }
                                }
                            }
                        }
                    } else if viewModel.selectedPoints.count > 1 {
                        // 当达到最大选择数量时，仍然显示距离信息
                        HStack(spacing: 4) {
                            Image(systemName: "point.topleft.down.curvedto.point.bottomright.up")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))

                            // 根据计算状态显示不同内容
                            switch viewModel.selectedPointsDistanceStatus {
                            case .notCalculated, .calculating:
                                HStack(spacing: 4) {
                                    ProgressView()
                                        .scaleEffect(0.5)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white.opacity(0.6)))
                                    Text("计算真实距离中...")
                                        .font(.caption)
                                        .foregroundColor(.white.opacity(0.8))
                                }
                            case .completed:
                                HStack(spacing: 4) {
                                    Text(viewModel.getFormattedSelectedPointsDistance())
                                        .font(.caption)
                                        .foregroundColor(.white.opacity(0.8))
                                        .lineLimit(1)
                                    Image(systemName: "checkmark.circle")
                                        .font(.caption)
                                        .foregroundColor(.green.opacity(0.8))
                                }
                            case .fallback:
                                HStack(spacing: 4) {
                                    Text("距离计算失败")
                                        .font(.caption)
                                        .foregroundColor(.red.opacity(0.8))
                                    Image(systemName: "exclamationmark.triangle")
                                        .font(.caption)
                                        .foregroundColor(.red.opacity(0.8))
                                }
                            }
                        }
                    }
                }

                Spacer()

                // 右上角关闭图标 - 调整位置
                Button(action: { viewModel.cancelMultiSelect() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.white.opacity(0.8))
                }
                .buttonStyle(BorderlessButtonStyle())
                .offset(y: -4) // 向上移动
            }
            .padding(.vertical, 8)

            // 底部按钮栏 - 只有当选择了至少一个点时才显示
            if !viewModel.selectedPoints.isEmpty {
                HStack(spacing: 16) {
                    // 创建分组按钮 - 添加分组数量限制显示
                    let subscriptionManager = SubscriptionManager.shared
                    let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial
                    let currentGroupCount = deliveryGroups.count
                    let maxGroups = subscriptionManager.currentTier.maxGroupsPerRoute
                    let remainingGroups = isFreeUser ? max(0, maxGroups - currentGroupCount) : Int.max

                    Button(action: {
                        // 检查分组数量限制
                        if isFreeUser && currentGroupCount >= maxGroups {
                            // 显示toast提示用户订阅获取更多分组
                            logInfo("RouteView - 免费用户达到分组数量限制，显示订阅提示")
                            NotificationCenter.default.post(
                                name: Notification.Name("ShowToast"),
                                object: String(format: "free_user_max_groups_reached".localized, maxGroups)
                            )
                        } else {
                            viewModel.showingGroupNameInput = true
                        }
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "folder.badge.plus")
                                .font(.system(size: 15))
                            Text("create_group".localized)
                                .fontWeight(.semibold)
                                .lineLimit(1)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(remainingGroups > 0 || !isFreeUser ? Color.white.opacity(0.2) : Color.white.opacity(0.1))
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                    }

                    // GO按钮 - 直接导航，只有选择了至少2个点时才启用
                    if viewModel.selectedPoints.count >= 2 {
                        Button(action: {
                            let selectedDeliveryPoints = viewModel.deliveryPoints
                                .filter { viewModel.selectedPoints.contains($0.id) }
                                .sorted { viewModel.selectedPointsOrder.firstIndex(of: $0.id) ?? 0 < viewModel.selectedPointsOrder.firstIndex(of: $1.id) ?? 0 }

                            let tempGroup = DeliveryGroup(
                                name: "Temp Navigation Group",
                                points: selectedDeliveryPoints,
                                groupNumber: -1
                            )

                            viewModel.navigateToGroup(tempGroup) { success, message in
                                showingPointPopover = false

                                // 检查是否需要显示Google Maps网页版提示
                                if success && message == "google_maps_web_version" {
                                    // 显示使用网页版的提示
                                    let alert = UIAlertController(
                                        title: "google_maps".localized,
                                        message: "using_web_version".localized,
                                        preferredStyle: .alert
                                    )
                                    alert.addAction(UIAlertAction(title: "understand".localized, style: .default))

                                    // 显示提示
                                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                       let rootViewController = windowScene.windows.first?.rootViewController {
                                        rootViewController.present(alert, animated: true)
                                    }
                                }
                            }
                        }) {
                            HStack(spacing: 6) {
                                Image(systemName: "arrow.right.circle")
                                    .font(.system(size: 15))
                                Text("navigate_button".localized)
                                    .fontWeight(.bold)
                                    .lineLimit(1)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .background(Color.white.opacity(0.2))
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                    }
                }
                .padding(.top, 4)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black)
                .shadow(color: .black.opacity(0.3), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
        .safeAreaInset(edge: .top) { // 确保工具栏不会覆盖状态栏
            Color.clear.frame(height: 0)
        }
    }

    // 路线优化结果卡片视图
    private func routeOptimizationCard(info: RouteOptimizationInfo) -> some View {
        VStack(spacing: 0) {
            // 顶部标题栏 - 新布局，标题在左侧，按钮在右侧
            HStack {
                // 左侧标题区域
                HStack(spacing: 6) {
                    Image(systemName: "arrow.triangle.swap")
                        .foregroundColor(.orange)
                        .font(.system(size: 16))

                    Text("路线优化结果")
                        .font(.headline)
                        .foregroundColor(.primary)
                }

                Spacer()

                // 右侧按钮区域
                HStack(spacing: 8) {
                    // 保存路线按钮
                    Button(action: {
                        Task {
                            await viewModel.confirmOptimizedRoute()
                            // 显示保存成功提示
                            withAnimation {
                                showSaveSuccessToast = true
                            }
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                                withAnimation {
                                    showSaveSuccessToast = false
                                }
                            }
                        }
                    }) {
                        Text("保存路线")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 10)
                            .padding(.vertical, 6)
                            .background(Color.blue)
                            .cornerRadius(6)
                    }

                    // 折叠/展开按钮
                    Button(action: {
                        withAnimation(.spring()) {
                            viewModel.isOptimizationCardExpanded.toggle()
                        }
                    }) {
                        Image(systemName: viewModel.isOptimizationCardExpanded ? "chevron.up" : "chevron.down")
                            .font(.system(size: 14, weight: .semibold))
                            .padding(4)
                            .foregroundColor(.secondary)
                    }

                    // 关闭按钮
                    Button(action: {
                        withAnimation(.spring()) {
                            viewModel.clearRouteOptimizationInfo()
                        }
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 14, weight: .semibold))
                            .padding(4)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 12)

            // 内容区域 - 只在展开状态下显示
            if viewModel.isOptimizationCardExpanded {
                // 分隔线
                Divider()
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)

                // 优化前后对比内容
                HStack(alignment: .top, spacing: 0) {
                    // 优化前
                    VStack(alignment: .leading, spacing: 4) {
                        Text("优化前")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        HStack(spacing: 2) {
                            Image(systemName: "arrow.up.arrow.down")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)

                            // 使用公里单位显示
                            Text(String(format: "%.1f 公里", info.beforeDistanceKm))
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.primary)
                        }

                        HStack(spacing: 2) {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)

                            Text(info.formattedBeforeTime)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.primary)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // 箭头
                    Image(systemName: "arrow.right")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                        .padding(.horizontal, 4)

                    // 优化后
                    VStack(alignment: .leading, spacing: 4) {
                        Text("优化后")
                            .font(.subheadline)
                            .foregroundColor(.green)

                        HStack(spacing: 2) {
                            Image(systemName: "arrow.up.arrow.down")
                                .font(.system(size: 12))
                                .foregroundColor(.green)

                            // 使用公里单位显示
                            Text(String(format: "%.1f 公里", info.afterDistanceKm))
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.green)
                        }

                        HStack(spacing: 2) {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(.green)

                            Text(info.formattedAfterTime)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.green)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // 节省百分比
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("节省")
                            .font(.subheadline)
                            .foregroundColor(.orange)

                        HStack(spacing: 2) {
                            Image(systemName: "arrow.down")
                                .font(.system(size: 12))
                                .foregroundColor(.orange)

                            Text("\(Int(info.distanceSavedPercentage))%")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.orange)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .trailing)
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 8)

                // 添加时间计算说明
                Text("时间计算基于平均行驶速度估算")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.bottom, 8)
                    .padding(.horizontal, 12)
            }
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
        .onAppear {
            // 默认展开状态
            if !viewModel.isOptimizationCardExpanded {
                withAnimation(.spring()) {
                    viewModel.isOptimizationCardExpanded = true
                }
            }
        }
    }

    // 顶部地址搜索栏
    private var addressInputBar: some View {
        Button(action: {
            // 已移除底部表单相关代码
        }) {
            HStack(spacing: 12) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)
                    .font(.system(size: 18))

                Text("点击添加停靠点")
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }



    // 地址输入表单
    private var addressInputSheet: some View {
        NavigationView {
            VStack {
                // 搜索栏
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)

                    TextField("输入地址", text: $addressText)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)

                    if !addressText.isEmpty {
                        Button(action: {
                            addressText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                        }
                    }
                }
                .padding(10)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .padding(.horizontal)

                // 添加地址按钮
                Button(action: {
                    addNewAddress()
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.blue)
                        Text("添加当前地址")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(addressText.isEmpty)
                .padding()

                Spacer()
            }
            .navigationTitle("添加地址")
            .navigationBarItems(trailing: Button("完成") {
                // 已移除底部表单相关代码
            })
        }
    }

    // 添加新地址
    private func addNewAddress() {
        guard !addressText.isEmpty else { return }

        // 显示加载状态
        isAddingAddress = true

        // 地理编码地址
        Task {
            if let coordinate = await viewModel.geocodeAddress(addressText) {
                // 添加地址和坐标
                addNewAddressWithCoordinate(address: addressText, coordinate: coordinate)
            } else {
                // 地理编码失败
                await MainActor.run {
                    isAddingAddress = false
                    // 这里可以添加错误提示
                }
            }
        }
    }

    // 添加多个地址到路线
    private func addMultipleAddresses(addresses: [String]) {
        // 显示加载状态
        isAddingAddress = true

        Task {
            for address in addresses {
                if let coordinate = await viewModel.geocodeAddress(address) {
                    addNewAddressWithCoordinate(address: address, coordinate: coordinate)
                }
            }
            await MainActor.run {
                isAddingAddress = false
                // 显示添加成功的提示
                withAnimation {
                    showSaveSuccessToast = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                    withAnimation {
                        showSaveSuccessToast = false
                    }
                }
            }
        }
    }

    // 使用地址和坐标添加新的配送点
    private func addNewAddressWithCoordinate(address: String, coordinate: CLLocationCoordinate2D) {
        // 创建新的配送点
        let newPoint = DeliveryPoint(
            sort_number: viewModel.deliveryPoints.count + 1,
            streetName: address, // 临时设置为街道名
            coordinate: coordinate
        )

        // 🎯 为手动添加的地址设置默认验证分数，避免阻止优化
        newPoint.addressValidationScore = 100.0  // 设置为满分
        newPoint.addressValidationIssues = nil   // 清除验证问题
        newPoint.geocodingWarning = nil          // 清除警告

        // 验证坐标
        _ = newPoint.validateCoordinates()

        // 基于用户位置验证
        if let userLocation = LocationManager.shared.userLocation {
            newPoint.validateLocationBasedOnUserPosition(userLocation)
        }

        // 添加到配送点列表
        viewModel.deliveryPoints.append(newPoint)

        // 如果有ModelContext，将点保存到数据库并关联到当前路线
        if let modelContext = viewModel.modelContext {
            modelContext.insert(newPoint)
            if let currentRoute = viewModel.currentRoute {
                currentRoute.points.append(newPoint)
                Logger.data("已将新配送点关联到当前路线: \(address), 路线: \(currentRoute.name)")
            } else {
                Logger.dataWarning("没有当前路线，无法关联新配送点: \(address)")
            }
            try? modelContext.save()
            Logger.data("已将新配送点保存到数据库: \(address)")
        }

        addressText = ""
        isAddingAddress = false

        // 移动地图到新添加的点
        withAnimation {
            viewModel.cameraPosition = .region(MKCoordinateRegion(
                center: coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
            ))
        }

        Logger.action("成功添加新配送点: \(address)")
    }

    // 配送地址计数器视图
    private var deliveryCounterView: some View {
        let totalDeliveryCount = viewModel.getTotalDeliveryCount()

        return Button(action: {
            // 点击时触发动画效果
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                deliveryCountAnimation.toggle()
            }

            // 延迟重置动画状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    deliveryCountAnimation = false
                }
            }
        }) {
            HStack(spacing: 10) {
                // 位置图标 - 使用圆形红色背景
                ZStack {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 32, height: 32)

                    Image(systemName: "mappin")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .scaleEffect(deliveryCountAnimation ? 1.2 : 1.0)
                }

                // 文本内容
                VStack(alignment: .leading, spacing: 2) {
                    Text("配送地址")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.primary.opacity(0.7))

                    HStack(alignment: .firstTextBaseline, spacing: 4) {
                        Text("\(totalDeliveryCount)")
                            .font(.system(size: 22, weight: .bold))
                            .foregroundColor(.red)
                            .scaleEffect(deliveryCountAnimation ? 1.2 : 1.0)

                        Text("个")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.primary.opacity(0.7))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 22)
                    .fill(Color(.systemBackground).opacity(0.9))
                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 22)
                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
            )
            .scaleEffect(deliveryCountAnimation ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 统一的圆形按钮样式 - 优化点击区域
    private func circleButton(
        backgroundColor: Color = .black,
        action: @escaping () -> Void,
        @ViewBuilder content: () -> some View
    ) -> some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(backgroundColor)
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)

                content()
            }
        }
        .frame(width: 58, height: 58) // 扩大点击区域到58x58
        .contentShape(Rectangle()) // 确保整个58x58区域都可点击
        .buttonStyle(PlainButtonStyle())
    }

    // Pro版圆形按钮样式（带皇冠和彩色）- 优化点击区域
    private func proCircleButton(
        backgroundColor: Color = .black,
        iconColor: Color = .white,
        action: @escaping () -> Void,
        @ViewBuilder content: () -> some View
    ) -> some View {
        Button(action: action) {
            ZStack {
                // 主圆圈
                Circle()
                    .fill(backgroundColor)
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)

                content()

                // 皇冠图标
                Image(systemName: "crown.fill")
                    .font(.system(size: 12))
                    .foregroundColor(.white)
                    .background(
                        Circle()
                            .fill(Color.black)
                            .frame(width: 14, height: 14)
                    )
                    .offset(x: 17, y: -17)
            }
        }
        .frame(width: 58, height: 58) // 扩大点击区域到58x58
        .contentShape(Rectangle()) // 确保整个58x58区域都可点击
        .buttonStyle(PlainButtonStyle())
    }

    // 简化的顶部导航栏（仅显示标题）
    private var simpleTopNavigationBar: some View {
        HStack {
            // 左侧标题
            Text("NaviBatch")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.primary)

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            Color(.systemBackground)
                .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
        )
    }

    // 右下角按钮组 - 改为垂直排列，统一样式
    private var rightBottomButtons: some View {
        VStack(spacing: 10) {
            // 可折叠按钮组 - 替换原来的5个按钮
            CollapsibleButtonGroup(
                isExpanded: $isCollapsibleButtonGroupExpanded,
                onSideToggle: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        buttonsOnLeftSide.toggle()
                        UserDefaults.standard.set(buttonsOnLeftSide, forKey: "buttonsOnLeftSide")
                    }
                    logInfo("按钮组位置切换到: \(buttonsOnLeftSide ? "左侧" : "右侧")")
                },
                onMapModeToggle: {
                    showMapModeSelector = true
                    logInfo("显示地图模式选择器")
                },
                onSearch: {
                    showingNumberInput = true
                    logInfo("显示编号定位输入框")
                },
                onVisibilityToggle: {
                    viewModel.showCompletedDeliveries.toggle()
                    let statusMessage = viewModel.showCompletedDeliveries ? "已显示已完成和失败地址" : "已隐藏已完成和失败地址"
                    logInfo("切换了已完成和失败地址显示状态: \(statusMessage)")
                },
                onCounterTap: {
                    // 点击计数器时的操作，可以添加功能如显示配送点列表
                },
                buttonsOnLeftSide: buttonsOnLeftSide,
                selectedMapMode: selectedMapMode,
                showCompletedDeliveries: viewModel.showCompletedDeliveries,
                deliveryCount: viewModel.getTotalDeliveryCount()
            )

            // 定位按钮 - 始终排在第二个位置
            circleButton(action: {
                // 给予触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                // 移除Toast提示 - 不再显示调试信息

                // 记录当前状态
                let currentLocationString = viewModel.driverLocation != nil ?
                    "(\(viewModel.driverLocation!.latitude), \(viewModel.driverLocation!.longitude))" : "nil"

                // 创建相机位置的字符串表示
                let currentCameraString = formatCameraPosition(viewModel.cameraPosition)

                logInfo("定位按钮点击 - 当前driverLocation: \(currentLocationString)")
                logInfo("定位按钮点击 - 当前cameraPosition: \(currentCameraString)")

                // 直接执行定位操作 - 不使用Task以避免异步问题
                viewModel.moveToUserLocation()

                // 记录操作后状态
                let updatedLocationString = viewModel.driverLocation != nil ?
                    "(\(viewModel.driverLocation!.latitude), \(viewModel.driverLocation!.longitude))" : "nil"

                // 创建更新后相机位置的字符串表示
                let updatedCameraString = formatCameraPosition(viewModel.cameraPosition)

                logInfo("定位按钮点击 - 操作后driverLocation: \(updatedLocationString)")
                logInfo("定位按钮点击 - 操作后cameraPosition: \(updatedCameraString)")

                // 移除Toast隐藏逻辑 - 不再需要
            }) {
                Image(systemName: "dot.scope")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }

            // 一键分组按钮 - 所有用户都显示，但免费版功能受限
            let pendingCount = viewModel.getPendingDeliveryCount()
            let notInMultiSelect = !viewModel.isMultiSelectMode
            let subscriptionManager = SubscriptionManager.shared
            let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial
            let canAutoGroup = viewModel.canUseAutoGrouping()

            // 使用onAppear记录日志，而不是直接在视图构建中调用
            Color.clear
                .frame(width: 0, height: 0)
                .onAppear {
                    // 记录一键分组按钮的显示条件
                    logInfo("一键分组按钮显示条件: 可以自动分组=\(canAutoGroup), 未分组地址数=\(pendingCount), 非多选模式=\(notInMultiSelect)")
                }

            // 始终显示一键分组按钮
            if notInMultiSelect {
                if !isFreeUser {
                    // Pro用户 - 白色样式带皇冠
                    proCircleButton(
                        backgroundColor: .black,
                        iconColor: .white,
                        action: {
                            if pendingCount > 0 {
                                performAutoGrouping()
                            }
                        }
                    ) {
                        VStack(spacing: 2) {
                            Image(systemName: "rectangle.stack.fill.badge.plus")
                                .font(.system(size: 16))
                                .foregroundColor(.white)

                            if pendingCount > 0 {
                                Text("\(pendingCount)")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            }
                        }
                    }
                } else {
                    // 免费用户 - 黑色样式，无皇冠，但可以使用功能（有10个地址限制）
                    circleButton(action: {
                        if pendingCount > 0 {
                            performAutoGrouping()
                        }
                    }) {
                        VStack(spacing: 2) {
                            Image(systemName: "rectangle.stack.fill.badge.plus")
                                .font(.system(size: 16))
                                .foregroundColor(.white)

                            if pendingCount > 0 {
                                Text("\(pendingCount)")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            }
                        }
                    }
                }
            }

            // 选择地址点按钮 - 所有用户都显示，通过颜色和皇冠区分版本
            if !isFreeUser {
                // Pro用户 - 白色样式带皇冠
                proCircleButton(
                    backgroundColor: .black,
                    iconColor: .white,
                    action: {
                        Task {
                            await applyThirdPartySortingIfNeeded()
                            await MainActor.run {
                                viewModel.startMultiSelect()
                            }
                        }
                    }
                ) {
                    Image(systemName: "dot.circle.and.hand.point.up.left.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                }
            } else {
                // 免费用户 - 灰色样式，无皇冠，但可以使用功能（有10个地址限制）
                circleButton(action: {
                    Task {
                        await applyThirdPartySortingIfNeeded()
                        await MainActor.run {
                            viewModel.startMultiSelect()
                        }
                    }
                }) {
                    Image(systemName: "dot.circle.and.hand.point.up.left.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                }
            }

            // 已保存分组按钮 - 所有用户都可以看到，但免费用户只能导航第一组
            // 使用稳定的状态检查，避免UI闪烁
            let hasGroups = !deliveryGroups.isEmpty
            if hasGroups {
                if !isFreeUser {
                    // Pro用户 - 白色样式带皇冠
                    proCircleButton(
                        backgroundColor: .black,
                        iconColor: .white,
                        action: {
                            logInfo("Pro用户folder按钮被点击")
                            // 使用与Menu相同的直接逻辑
                            if activeSheet == .routeSheet {
                                userDismissedBottomSheet = true
                            }
                            savedGroupsSheetDismissedByUser = false // 重置标记，因为用户主动打开savedGroups
                            activeSheet = .savedGroups
                        }
                    ) {
                        Image(systemName: "folder.fill")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                    }
                } else {
                    // 免费用户 - 灰色样式
                    circleButton(action: {
                        logInfo("免费用户folder按钮被点击")
                        // 使用与Menu相同的直接逻辑
                        if activeSheet == .routeSheet {
                            userDismissedBottomSheet = true
                        }
                        savedGroupsSheetDismissedByUser = false // 重置标记，因为用户主动打开savedGroups
                        activeSheet = .savedGroups
                    }) {
                        VStack(spacing: 2) {
                            Image(systemName: "folder.fill")
                                .font(.system(size: 18))
                                .foregroundColor(.white)

                            // 免费用户显示"1"标识，表示只能导航第一组
                            Text("1")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                }
            }

            // 原来的地图切换、显示/隐藏、配送计数器按钮已移动到可折叠按钮组中

            // 路线信息备用按钮 - 只有在bottom sheet没有显示时才显示
            if shouldShowRouteSheetButton {
                circleButton(action: {
                    showRouteSheetFromButton()
                }) {
                    Image(systemName: "info.circle")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.blue.opacity(0.8))
                }
                .accessibilityLabel("route_sheet_backup_button".localized)
                .accessibilityHint("route_sheet_backup_button_tooltip".localized)
            }

            // 菜单按钮 - 始终保持在最后一个位置，优化点击区域
            Button(action: {
                // 简化的Menu按钮逻辑
                if activeSheet == .routeSheet {
                    userDismissedBottomSheet = true
                }
                menuSheetDismissedByUser = false // 重置标记，因为用户主动打开menu
                activeSheet = .menu
            }) {
                ZStack {
                    Circle()
                        .fill(Color.black)
                        .frame(width: 50, height: 50)
                        .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)

                    Image(systemName: "line.3.horizontal")
                        .font(.system(size: 18))
                        .foregroundColor(.white)
                }
            }
            .frame(width: 48, height: 48) // 扩大点击区域到48x48
            .contentShape(Rectangle()) // 确保整个48x48区域都可点击
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 8)
        .frame(width: 64) // 增加宽度以适应更大的点击区域
    }

    // 导航选项覆盖层
    private var navigationPopover: some View {
        Group {
            if showingPointPopover, let point = selectedPoint {
                GeometryReader { geometry in
                    Color.black.opacity(0.2) // 降低背景遮罩的不透明度
                        .edgesIgnoringSafeArea(.all)
                        .onTapGesture {
                            showingPointPopover = false
                            showingDeliveredPointCard = false
                        }

                    if showingDeliveredPointCard {
                        // 已派送点的特殊卡片
                        VStack(spacing: 12) {
                            // 添加排序号码 - 统一显示sorted_number
                            let statusColor = point.deliveryStatus == .completed ? Color.green : Color.red
                            Text("#\(point.sorted_number)")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(statusColor)
                                .padding(.vertical, 4)
                                .padding(.horizontal, 16)
                                .background(statusColor.opacity(0.1))
                                .cornerRadius(8)
                                .padding(.top, 4)

                            // 地址信息
                            Text(point.primaryAddress)
                                .font(.headline)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)

                            // 状态指示器
                            HStack {
                                if point.deliveryStatus == .completed {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.system(size: 18))
                                    Text("已派送")
                                        .foregroundColor(.green)
                                        .fontWeight(.medium)
                                } else {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.red)
                                        .font(.system(size: 18))
                                    Text("派送失败")
                                        .foregroundColor(.red)
                                        .fontWeight(.medium)
                                }
                            }
                            .padding(.vertical, 8)
                            .padding(.horizontal, 16)
                            .background(statusColor.opacity(0.1))
                            .cornerRadius(8)
                            // 已移除"查看详情"按钮
                        }
                        .padding(.vertical, 20)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(radius: 5)
                        .frame(width: min(geometry.size.width - 40, 300))
                        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    } else {
                        // 普通点的现有卡片 - 所有用户都可以查看地图信息和实景图
                        MapMarkerCalloutView(
                            point: point,
                            isSubscriber: true // 所有用户都可以使用实景图功能
                        )
                        .frame(width: min(geometry.size.width - 40, 360))
                        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    }
                }
                .transition(.opacity)
                .zIndex(1000)
            }
        }
    }

    // 控制按钮组
    private var controlButtons: some View {
        GeometryReader { geometry in
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    VStack(spacing: 16) {
                        // 已将定位按钮移至右侧图标群组

                        // 路线优化按钮
                        if !viewModel.isMultiSelectMode {
                            Button(action: {
                                Task {
                                    await viewModel.recalculateRouteFromDriverLocation()
                                    await MainActor.run {
                                        self.showGroupCreatedToast = false
                                        self.showOrderSavedToast = false
                                        self.showSaveSuccessToast = false
                                        withAnimation {
                                            showRouteOptimizedToast = true
                                        }
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                                            withAnimation {
                                                showRouteOptimizedToast = false
                                            }
                                        }

                                        withAnimation(.easeInOut(duration: 0.6)) {
                                            if let bounds = viewModel.getDeliveryPointsBounds() {
                                                viewModel.cameraPosition = .region(bounds)
                                            }
                                        }
                                    }
                                }
                            }) {
                                ZStack {
                                    // 根据优化状态设置背景颜色
                                    Circle()
                                        .fill(viewModel.isOptimizingRoute ?
                                              Color.blue.opacity(0.3) :
                                              (viewModel.currentRoute?.isOptimized ?? false ?
                                               Color(hex: "B36AE2").opacity(0.3) : // 已优化使用紫色
                                               Color(.systemBackground)))
                                        .frame(width: 44, height: 44)
                                        .shadow(radius: 2)

                                    if viewModel.isOptimizingRoute {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                                            .scaleEffect(1.2)
                                    } else {
                                        Image(systemName: "arrow.triangle.swap")
                                            .font(.title2)
                                            .foregroundColor(viewModel.currentRoute?.isOptimized ?? false ?
                                                            Color(hex: "B36AE2") : // 已优化使用紫色
                                                            .orange)
                                    }
                                }
                            }
                            .disabled(viewModel.isOptimizingRoute)
                        }

                        // 多选模式按钮已移至右侧按钮群
                    }
                    .padding(.trailing, 16)
                }
                .padding(.bottom, -20) // 使用负值强制按钮更靠近底部
            }
        }
    }

    // 输入组名的表单
    private var groupNameInputSheet: some View {
        NavigationView {
            Form {
                Section(header: Text("group_name".localized)) {
                    TextField("enter_group_name".localized, text: $viewModel.groupNameInput)
                        .autocapitalization(.none)
                }

                Section(header: deliveryPointsHeader) {
                    deliveryPointsList
                }
            }
            .navigationTitle("create_delivery_group".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        // 只关闭界面和清空输入框，保持已选择的地址
                        viewModel.groupNameInput = ""
                        activeSheet = nil
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("save".localized) {
                        saveDeliveryGroup()
                    }
                    .disabled(viewModel.groupNameInput.isEmpty)
                }
            }
            .environment(\.editMode, .constant(.active))
        }
    }

    // 配送点列表标题
    private var deliveryPointsHeader: some View {
        HStack {
            Text("selected_delivery_points".localized)
            Spacer()
            Text("drag_to_adjust_order".localized)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    // 配送点列表
    private var deliveryPointsList: some View {
        List {
            ForEach(viewModel.selectedPointsOrder, id: \.self) { pointId in
                if let point = viewModel.deliveryPoints.first(where: { $0.id == pointId }) {
                    HStack(spacing: 12) {
                        // 序号标记
                        ZStack {
                            Circle()
                                .fill(Color.red.opacity(0.2))
                                .frame(width: 28, height: 28)

                            Text("\(viewModel.selectedPointsOrder.firstIndex(of: pointId)! + 1)")
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(.red)
                        }

                        // 地址信息 - 优化单位信息显示
                        VStack(alignment: .leading, spacing: 2) {
                            // 第一行：智能显示地址，确保单位信息可见
                            HStack(spacing: 4) {
                                // 如果有单位号，优先显示并突出
                                if point.hasUnitNumber, let unitNumber = point.unitNumber {
                                    Text(unitNumber)
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(4)
                                }

                                // 显示街道地址（不包含单位号，因为已经单独显示）- 支持自动换行
                                let addressParts = getSmartAddressParts(formatMainAddress(point.primaryAddress, hasUnit: point.hasUnitNumber))

                                VStack(alignment: .leading, spacing: 1) {
                                    Text(addressParts.firstLine)
                                        .font(.subheadline)
                                        .frame(maxWidth: .infinity, alignment: .leading)

                                    if !addressParts.secondLine.isEmpty {
                                        Text(addressParts.secondLine)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                }
                            }

                            // 第二行显示地区、邮编和国家信息
                            if let addressParts = point.primaryAddress.components(separatedBy: ",").dropFirst().joined(separator: ",").trimmingCharacters(in: .whitespaces).isEmpty ? nil : point.primaryAddress.components(separatedBy: ",").dropFirst().joined(separator: ",").trimmingCharacters(in: .whitespaces) {
                                Text(addressParts)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                        }

                        Spacer()

                        // 包裹数量
                        if point.packageCount > 1 {
                            Text("\(point.packageCount)个包裹")
                                .font(.caption)
                                .foregroundColor(.orange)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(4)
                        }
                    }
                    .padding(.vertical, 4)
                }
            }
            .onMove(perform: viewModel.reorderSelectedPoints)
        }
    }

    // 路线信息底部表单
    private var routeInfoBottomSheet: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 顶部标题栏
            VStack(spacing: 0) {
                // 中间的横条指示器
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 40, height: 4)
                    .cornerRadius(2)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.top, 12)
                    .padding(.bottom, 12)

                // 标题区域
                HStack {
                    HStack(spacing: 8) {
                        Image(systemName: "map")
                            .foregroundColor(.blue)
                            .font(.system(size: 18))

                        Text("Route 20/4/2025")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    }

                    Spacer()

                    Button(action: {
                        showRouteInfoSheet = false
                    }) {
                        Text("完成")
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 8)

                Divider()
            }

            // 内容区域 - 使用ScrollView而不是List以获得更好的自定义控制
            ScrollView {
                VStack(spacing: 20) {
                    // 路线信息卡片
                    VStack(alignment: .leading, spacing: 16) {
                        Text("当前路线信息")
                            .font(.headline)
                            .padding(.horizontal)

                        VStack(spacing: 12) {
                            HStack {
                                Text("总配送点")
                                Spacer()
                                Text("\(viewModel.getTotalDeliveryCount())")
                                    .foregroundColor(.secondary)
                            }

                            Divider()

                            HStack {
                                Text("待派送点")
                                Spacer()
                                Text("\(viewModel.getPendingDeliveryCount())")
                                    .foregroundColor(.secondary)
                            }

                            Divider()

                            HStack {
                                Text("已分组点")
                                Spacer()
                                Text("\(viewModel.getGroupedDeliveryCount())")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6).opacity(0.5))
                        .cornerRadius(12)
                        .padding(.horizontal)
                    }

                    // 操作按钮区域
                    VStack(alignment: .leading, spacing: 16) {
                        Text("操作")
                            .font(.headline)
                            .padding(.horizontal)

                        VStack(spacing: 12) {
                            // 🚨 新增：修复回环连接问题的调试按钮
                            Button(action: {
                                viewModel.forceRecalculateRoute()
                                showRouteInfoSheet = false
                            }) {
                                HStack {
                                    Image(systemName: "arrow.clockwise.circle.fill")
                                        .foregroundColor(.orange)
                                    Text("修复路线回环问题")
                                        .foregroundColor(.primary)
                                    Spacer()
                                }
                                .padding()
                                .background(Color(.systemGray6))
                                .cornerRadius(12)
                            }

                            Button(action: {
                                Task {
                                    await viewModel.recalculateRouteFromDriverLocation()
                                }
                                showRouteInfoSheet = false
                            }) {
                                HStack {
                                    Image(systemName: "arrow.triangle.swap")
                                        .foregroundColor(.orange)
                                    Text("优化路线")
                                    Spacer()
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding()
                                .background(Color(.systemGray6).opacity(0.5))
                                .cornerRadius(12)
                            }
                            .buttonStyle(PlainButtonStyle())

                            Button(action: {
                                Task {
                                    await applyThirdPartySortingIfNeeded()
                                    await MainActor.run {
                                        viewModel.startMultiSelect()
                                        showRouteInfoSheet = false
                                    }
                                }
                            }) {
                                HStack {
                                    Image(systemName: "checkmark.circle")
                                        .foregroundColor(.blue)
                                    Text("select_delivery_points".localized)
                                    Spacer()
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding()
                                .background(Color(.systemGray6).opacity(0.5))
                                .cornerRadius(12)
                            }
                            .buttonStyle(PlainButtonStyle())

                            // 分组按钮 - 所有用户都可以看到，但免费用户只能导航第一组
                            // 使用稳定的状态检查，避免UI闪烁
                            let hasGroups = !deliveryGroups.isEmpty
                            if hasGroups {
                                Button(action: {
                                    // 使用与Menu相同的直接逻辑
                                    if activeSheet == .routeSheet {
                                        userDismissedBottomSheet = true
                                    }
                                    savedGroupsSheetDismissedByUser = false // 重置标记，因为用户主动打开savedGroups
                                    activeSheet = .savedGroups
                                    showRouteInfoSheet = false
                                }) {
                                    HStack {
                                        Image(systemName: "folder")
                                            .foregroundColor(.green)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("view_saved_routes".localized)

                                            // 免费用户显示限制提示
                                            let subscriptionManager = SubscriptionManager.shared
                                            let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial
                                            if isFreeUser && deliveryGroups.count > 1 {
                                                Text("仅可导航第一组")
                                                    .font(.caption2)
                                                    .foregroundColor(.orange)
                                            }
                                        }

                                        Spacer()
                                        Image(systemName: "chevron.right")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .padding()
                                    .background(Color(.systemGray6).opacity(0.5))
                                    .cornerRadius(12)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.bottom, 20)
            }
        }
        .presentationDetents([.height(300), .medium, .large])
        .presentationDragIndicator(.visible) // ✅ 使用系统原生拖拽指示器
        .presentationBackground(Color(.systemBackground))
        .presentationCornerRadius(16)
    }

    // 保存配送分组
    private func saveDeliveryGroup() {
        guard !viewModel.selectedPoints.isEmpty else {
            return
        }

        guard let modelContext = viewModel.modelContext else {
            Logger.dataError("无法保存分组：没有ModelContext")
            return
        }

        // 检查分组数量限制
        let subscriptionManager = SubscriptionManager.shared
        if subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial {
            let currentGroupCount = deliveryGroups.count
            if currentGroupCount >= subscriptionManager.currentTier.maxGroupsPerRoute {
                // 显示toast提示用户订阅获取更多分组
                logInfo("RouteView - 免费用户达到分组数量限制，显示订阅提示")

                // 取消多选模式
                viewModel.cancelMultiSelect()
                viewModel.showingGroupNameInput = false

                // 显示提示消息
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: String(format: "free_user_max_groups_reached".localized, subscriptionManager.currentTier.maxGroupsPerRoute)
                )
                return
            }
        }

        // 获取下一个组号
        let nextGroupNumber = viewModel.getNextGroupNumber()

        // 获取选中的配送点，并过滤掉已完成和失败的点
        let selectedPoints = viewModel.selectedPoints.compactMap { pointId in
            viewModel.deliveryPoints.first(where: {
                $0.id == pointId &&
                $0.status != DeliveryStatus.completed.rawValue &&
                $0.status != DeliveryStatus.failed.rawValue
            })
        }

        // 按sorted_number排序，确保分组内顺序正确（不依赖用户点击顺序）
        let orderedPoints = selectedPoints.sorted { $0.sorted_number < $1.sorted_number }

        // 调试信息：检查选中的点
        logInfo("RouteView - createGroupFromSelectedPoints: 选中了\(selectedPoints.count)个点")
        for point in orderedPoints {
            logInfo("RouteView - 选中点: \(point.primaryAddress) - sorted_number: \(point.sorted_number), thirdParty: \(point.thirdPartySortNumber ?? "无")")
        }

        // 检查过滤后是否还有点
        if orderedPoints.isEmpty {
            // 显示提示：所有选中的点都已完成或失败
            withAnimation {
                // 可以在这里添加一个警告弹窗或提示
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: "所有选中的点都已完成或失败，无法创建分组"
                )
            }
            return
        }

        // 更新点的编号以反映顺序
        for (index, point) in orderedPoints.enumerated() {
            point.sort_number = index + 1
            point.isAssignedToGroup = true
            point.assignedGroupNumber = nextGroupNumber
        }

        // 创建新的配送组
        let newGroup = DeliveryGroup(
            name: viewModel.groupNameInput.isEmpty ? String(format: "default_group_name_format".localized, nextGroupNumber) : viewModel.groupNameInput,
            points: orderedPoints,
            groupNumber: nextGroupNumber
        )

        // 保存到数据库
        modelContext.insert(newGroup)

        do {
            try modelContext.save()

            // 保存成功后立即关闭界面并重置状态
            activeSheet = nil
            viewModel.groupNameInput = ""
            viewModel.cancelMultiSelect()

            // 显示创建成功提示
            withAnimation {
                showGroupCreatedToast = true
            }

            // 3秒后隐藏提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                withAnimation {
                    showGroupCreatedToast = false
                }
            }

            Logger.data("成功创建配送分组：\(newGroup.name)，包含\(orderedPoints.count)个配送点")

        } catch {
            Logger.dataError("保存配送分组失败: \(error.localizedDescription)")
        }

        Logger.data("成功创建配送分组：\(newGroup.name)，包含\(orderedPoints.count)个配送点") // 保留中文日志用于调试
    }

    // 已保存的组列表视图
    private var optimizedSavedGroupsSheet: some View {
        NavigationView {
            Group {
                if deliveryGroups.isEmpty {
                    VStack(spacing: 20) {
                        Image(systemName: "folder")
                            .font(.system(size: 60))
                            .foregroundColor(.gray.opacity(0.5))

                        Text("no_saved_groups".localized)
                            .font(.headline)
                            .foregroundColor(.gray)

                        Text("select_points_create_groups".localized)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 32)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color(.systemBackground))
                } else {
                    let subscriptionManager = SubscriptionManager.shared
                    let isFreeUser = subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial

                    List {
                        ForEach(Array(deliveryGroups.enumerated()), id: \.element.id) { index, group in
                            let isAccessible = !isFreeUser || index == 0
                            HStack {
                                // 左侧内容和导航链接
                                if isAccessible {
                                    NavigationLink(destination: GroupDetailView(group: group, viewModel: viewModel)) {
                                        HStack {
                                            Image(systemName: "rectangle.stack.fill")
                                                .foregroundColor(.blue)

                                            VStack(alignment: .leading, spacing: 4) {
                                                Text(group.name)
                                                    .font(.headline)

                                                Text(String(format: "address_count_format".localized, group.points.count))
                                                    .font(.caption)
                                                    .foregroundColor(.secondary)
                                            }
                                        }
                                    }
                                } else {
                                    HStack {
                                        Image(systemName: "rectangle.stack.fill")
                                            .foregroundColor(.gray.opacity(0.5))

                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(group.name)
                                                .font(.headline)
                                                .foregroundColor(.gray.opacity(0.6))

                                            Text(String(format: "address_count_format".localized, group.points.count))
                                                .font(.caption)
                                                .foregroundColor(.gray.opacity(0.5))
                                        }

                                        Spacer()

                                        Image(systemName: "lock.fill")
                                            .foregroundColor(.gray.opacity(0.5))
                                            .font(.system(size: 12))
                                    }
                                }

                                Spacer()

                                // 右侧导航按钮
                                if isAccessible {
                                    Button(action: {
                                    // 导航到该分组
                                    viewModel.navigateToGroup(group) { success, message in
                                        // 检查是否需要显示Google Maps网页版提示
                                        if success && message == "google_maps_web_version" {
                                            // 显示使用网页版的提示
                                            let alert = UIAlertController(
                                                title: "google_maps".localized,
                                                message: "using_web_version".localized,
                                                preferredStyle: .alert
                                            )
                                            alert.addAction(UIAlertAction(title: "understand".localized, style: .default))

                                            // 显示提示
                                            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                               let rootViewController = windowScene.windows.first?.rootViewController {
                                                rootViewController.present(alert, animated: true)
                                            }
                                        }
                                    }
                                    // 关闭已保存分组表单，与Menu保持一致的逻辑
                                    savedGroupsSheetDismissedByUser = true
                                    activeSheet = .routeSheet // 恢复bottom sheet
                                }) {
                                    HStack(spacing: 4) {
                                        Image(systemName: "location.fill.viewfinder")
                                            .font(.system(size: 14))
                                            .foregroundColor(.white)
                                        Text("navigate_button".localized)
                                            .font(.system(size: 12, weight: .medium))
                                            .foregroundColor(.white)
                                    }
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 6)
                                    .background(Color.blue)
                                    .cornerRadius(8)
                                }
                                .buttonStyle(BorderlessButtonStyle())
                                } else {
                                    // 不可访问的group显示灰色按钮
                                    HStack(spacing: 4) {
                                        Image(systemName: "location.fill.viewfinder")
                                            .font(.system(size: 14))
                                            .foregroundColor(.gray.opacity(0.5))
                                        Text("navigate_button".localized)
                                            .font(.system(size: 12, weight: .medium))
                                            .foregroundColor(.gray.opacity(0.5))
                                    }
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 6)
                                    .background(Color.gray.opacity(0.2))
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .onDelete(perform: deleteGroup)

                        // 如果是免费用户且有更多分组，显示升级提示
                        if isFreeUser && deliveryGroups.count > 1 {
                            VStack(spacing: 12) {
                                HStack {
                                    Image(systemName: "lock.fill")
                                        .foregroundColor(.orange)
                                    Text(String(format: "remaining_groups_locked".localized, deliveryGroups.count - 1))
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                    Spacer()
                                }

                                Text("upgrade_to_unlock_all_groups".localized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.leading)

                                Button(action: {
                                    // 打开Menu界面让用户查看订阅选项
                                    logInfo("RouteView - 从已保存分组打开Menu界面")
                                    // 关闭已保存分组表单，然后打开Menu
                                    savedGroupsSheetDismissedByUser = true
                                    activeSheet = .menu
                                }) {
                                    HStack {
                                        Image(systemName: "crown.fill")
                                        Text("upgrade_now".localized)
                                    }
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(Color.orange)
                                    .cornerRadius(8)
                                }
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                            .listRowInsets(EdgeInsets())
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                }
            }
            .navigationTitle("saved_groups".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarLeading) {
                    // Clear All Button
                    Button(action: {
                        logInfo("Clear All button tapped")
                        showDeleteAllGroupsAlert = true
                    }) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                    .disabled(deliveryGroups.isEmpty)

                    // Sort Button
                    Button(action: {
                        logInfo("Sort button tapped")
                        sortGroups()
                    }) {
                        Image(systemName: "arrow.up.arrow.down")
                            .foregroundColor(.blue)
                    }
                    .disabled(deliveryGroups.isEmpty)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        savedGroupsSheetDismissedByUser = true
                        activeSheet = .routeSheet // 恢复bottom sheet
                        logInfo("RouteView - SavedGroups通过X按钮关闭，恢复bottom sheet")
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 20))
                    }
                }
            }
            .alert("clear_all_groups".localized, isPresented: $showDeleteAllGroupsAlert) {
                Button("cancel".localized, role: .cancel) { }
                Button("clear_all_groups_button".localized, role: .destructive) {
                    performClearAllGroups()
                }
            } message: {
                Text("clear_all_groups_confirmation".localized)
            }
        }
    }

    // 删除分组
    private func deleteGroup(at offsets: IndexSet) {
        guard let modelContext = viewModel.modelContext else {
            Logger.dataError("无法删除分组：没有ModelContext")
            return
        }

        do {
            for index in offsets {
                let group = deliveryGroups[index]

                // 记录要删除的分组信息
                let groupName = group.name
                let groupId = group.id
                let pointCount = group.points.count

                Logger.data("开始删除分组: \(groupName), ID: \(groupId), 包含\(pointCount)个点")

                // 获取组内所有点的副本，因为删除组后可能无法访问
                let pointsInGroup = Array(group.points)

                // 记录所有点的ID，用于后续验证
                let pointIds = pointsInGroup.map { $0.id }

                // 重置组内所有点的分组状态，但保留优化状态
                for point in pointsInGroup {
                    // 记录当前优化状态
                    let wasOptimized = point.isOptimized
                    let sortedNumber = point.sorted_number

                    // 重置分组状态
                    point.isAssignedToGroup = false
                    point.assignedGroupNumber = nil

                    // 确保保留优化状态
                    point.isOptimized = wasOptimized

                    Logger.data("重置点状态: \(point.primaryAddress), ID: \(point.id), 保留优化状态: \(wasOptimized), 排序编号: \(sortedNumber)")
                }

                // 立即保存点状态更改
                try modelContext.save()
                Logger.data("已保存点状态更改")

                // 删除组
                modelContext.delete(group)

                // 立即保存删除操作
                try modelContext.save()

                Logger.data("成功删除分组: \(groupName)")

                // 验证点状态是否正确更新
                for pointId in pointIds {
                    let descriptor = FetchDescriptor<DeliveryPoint>(predicate: #Predicate { $0.id == pointId })
                    if let point = try modelContext.fetch(descriptor).first {
                        if point.isAssignedToGroup {
                            Logger.dataError("警告：点\(point.primaryAddress)的分组状态未正确重置")
                            // 强制重置
                            point.isAssignedToGroup = false
                            point.assignedGroupNumber = nil
                            try modelContext.save()
                        }
                    }
                }
            }

            // 刷新配送点状态
            Task {
                Logger.data("删除分组后开始刷新数据 - 开始重新加载配送点")

                // 强制刷新配送点列表 - 使用强制刷新参数
                await viewModel.setupDeliveryPoints(forceRefresh: true)
                Logger.data("删除分组后 - 已完成setupDeliveryPoints(forceRefresh: true)，当前有\(viewModel.deliveryPoints.count)个点")

                // 记录所有点的状态
                for (index, point) in viewModel.deliveryPoints.enumerated() {
                    Logger.data("点\(index+1): \(point.primaryAddress) - 已分组: \(point.isAssignedToGroup), 组号: \(point.assignedGroupNumber ?? -1), 优化状态: \(point.isOptimized)")
                }

                // 刷新配送点状态
                await viewModel.refreshDeliveryPointsStatus()
                Logger.data("删除分组后 - 已完成refreshDeliveryPointsStatus()")

                // 发送通知以确保UI刷新
                await MainActor.run {
                    // 触发视图刷新
                    viewModel.objectWillChange.send()
                    Logger.data("删除分组后 - 已发送objectWillChange通知")

                    // 发送通知，通知其他组件数据已更新
                    NotificationCenter.default.post(
                        name: Notification.Name("RouteDataChanged"),
                        object: nil
                    )
                    Logger.data("删除分组后 - 已发送RouteDataChanged通知")

                    // 强制刷新地图视图
                    refreshMapView()
                    Logger.data("删除分组后 - 已调用refreshMapView()强制刷新地图")
                }
            }
        } catch {
            Logger.dataError("删除分组失败: \(error.localizedDescription)")
        }
    }

    // 清空所有分组
    private func performClearAllGroups() {
        guard let modelContext = viewModel.modelContext else {
            logError("无法清空分组：没有ModelContext")
            return
        }

        logInfo("开始清空所有分组，当前有\(deliveryGroups.count)个分组")

        do {
            // 获取所有分组
            let allGroups = Array(deliveryGroups)
            logInfo("获取到\(allGroups.count)个分组准备删除")

            for group in allGroups {
                logInfo("开始删除分组: \(group.name)，包含\(group.points.count)个点")

                // 重置组内所有点的分组状态，但保留优化状态
                for point in group.points {
                    let wasOptimized = point.isOptimized
                    point.isAssignedToGroup = false
                    point.assignedGroupNumber = nil
                    point.isOptimized = wasOptimized
                    logInfo("重置点\(point.primaryAddress)的分组状态")
                }

                // 删除组
                modelContext.delete(group)
                logInfo("已删除分组: \(group.name)")
            }

            // 保存更改
            try modelContext.save()
            logInfo("成功清空所有分组并保存到数据库")

            // 刷新数据
            Task {
                logInfo("开始刷新数据...")
                await viewModel.setupDeliveryPoints(forceRefresh: true)
                await viewModel.refreshDeliveryPointsStatus()

                await MainActor.run {
                    logInfo("触发UI刷新...")
                    viewModel.objectWillChange.send()
                    NotificationCenter.default.post(name: Notification.Name("RouteDataChanged"), object: nil)
                    refreshMapView()
                    logInfo("清空分组操作完成")
                }
            }
        } catch {
            logError("清空分组失败: \(error.localizedDescription)")
        }
    }

    // 对分组进行排序
    private func sortGroups() {
        guard let modelContext = viewModel.modelContext else {
            logError("无法排序分组：没有ModelContext")
            return
        }

        logInfo("开始排序分组，当前有\(deliveryGroups.count)个分组")

        do {
            // 根据每个分组内地址的最小sorted_number进行排序
            let sortedGroups = deliveryGroups.sorted { group1, group2 in
                let group1MinSortedNumber = group1.points.compactMap { $0.sorted_number }.min() ?? Int.max
                let group2MinSortedNumber = group2.points.compactMap { $0.sorted_number }.min() ?? Int.max
                logInfo("分组\(group1.name)最小排序号: \(group1MinSortedNumber), 分组\(group2.name)最小排序号: \(group2MinSortedNumber)")
                return group1MinSortedNumber < group2MinSortedNumber
            }

            // 更新分组的groupNumber以反映新的排序
            for (index, group) in sortedGroups.enumerated() {
                let newGroupNumber = index + 1
                logInfo("更新分组\(group.name)的groupNumber从\(group.groupNumber)到\(newGroupNumber)")
                group.groupNumber = newGroupNumber
            }

            // 保存更改
            try modelContext.save()
            logInfo("分组排序完成并已保存")

            // 触发视图刷新
            viewModel.objectWillChange.send()

            // 发送通知
            NotificationCenter.default.post(name: Notification.Name("RouteDataChanged"), object: nil)

        } catch {
            logError("排序分组失败: \(error.localizedDescription)")
        }
    }

    // 获取问题地址
    private func getProblematicPoints() -> [DeliveryPoint]? {
        // 筛选出有警告或验证状态不是有效的地址点
        let problematicPoints = viewModel.deliveryPoints.filter { point in
            // 🎯 排除起点的验证检查（sort_number = 0 或 isStartPoint = true）
            if point.isStartPoint || point.sort_number == 0 {
                return false
            }

            // 检查是否有地理编码警告
            let hasGeocodingWarning = point.geocodingWarning != nil && !point.geocodingWarning!.isEmpty

            // 检查位置验证状态 - 只有明确的invalid状态才算有问题，不考虑unknown状态
            let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
            let hasLocationIssue = validationStatus == .invalid

            // 如果有任一问题，则认为是问题地址
            return hasGeocodingWarning || hasLocationIssue
        }

        return problematicPoints.isEmpty ? nil : problematicPoints
    }

    // 打开地址编辑表单
    private func showEditSheet(for point: DeliveryPoint) {
        selectedPointForEdit = point
    }

    // MARK: - 辅助函数

    // 🔍 验证标记数据一致性
    private func validateAnnotationConsistency(_ annotations: [PointAnnotation], _ points: [DeliveryPoint]) {
        // 检查是否有重复的displayNumber
        let displayNumbers = annotations.map { $0.displayNumber }
        let uniqueDisplayNumbers = Set(displayNumbers)

        if displayNumbers.count != uniqueDisplayNumbers.count {
            logError("🚨 发现重复的displayNumber: \(displayNumbers)")

            // 找出重复的编号
            var numberCounts: [Int: Int] = [:]
            for number in displayNumbers {
                numberCounts[number, default: 0] += 1
            }

            for (number, count) in numberCounts where count > 1 {
                logError("🚨 编号 \(number) 重复了 \(count) 次")

                // 找出所有使用这个编号的annotation
                let duplicateAnnotations = annotations.filter { $0.displayNumber == number }
                for annotation in duplicateAnnotations {
                    if let point = points.first(where: { $0.id == annotation.id }) {
                        logError("🚨 重复编号详情: displayNumber=\(number), point.id=\(point.id), sort_number=\(point.sort_number), sorted_number=\(point.sorted_number)")
                    }
                }
            }
        }

        // 检查ID映射是否正确
        for annotation in annotations {
            if let point = points.first(where: { $0.id == annotation.id }) {
                // 🎯 计算期望的displayNumber（与创建逻辑保持一致）
                let expectedDisplayNumber: Int = {
                    if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                        return extractNumber(from: thirdPartySortNumber)
                    } else {
                        return point.sorted_number
                    }
                }()

                if expectedDisplayNumber != annotation.displayNumber {
                    logError("🚨 ID映射错误: annotation.id=\(annotation.id), displayNumber=\(annotation.displayNumber), expectedDisplayNumber=\(expectedDisplayNumber), point.sorted_number=\(point.sorted_number), thirdPartySortNumber=\(point.thirdPartySortNumber ?? "nil")")
                }
            } else {
                logError("🚨 找不到对应的DeliveryPoint: annotation.id=\(annotation.id), displayNumber=\(annotation.displayNumber)")
            }
        }
    }

    // 处理标记点击，支持重复点击切换
    private func handleMarkerTap(_ point: DeliveryPoint) {
        // 在多选模式下，直接处理选择逻辑，不设置selectedMarkerID避免重复处理
        if viewModel.isMultiSelectMode {
            handlePointSelection(point)
            return
        }

        // 非多选模式下，检查是否是重复点击同一个标记
        if showingPointPopover && selectedPoint?.id == point.id {
            // 重复点击同一个标记，隐藏callout
            showingPointPopover = false
            selectedPoint = nil
            showingDeliveredPointCard = false
            selectedMarkerID = nil

            logInfo("RouteView - 重复点击标记，隐藏callout: \(point.primaryAddress)")
        } else {
            // 🎯 修复：正常点击处理 - 直接调用handlePointSelection确保状态正确设置
            selectedMarkerID = point.id
            handlePointSelection(point)

            logInfo("RouteView - 点击标记，显示callout: \(point.primaryAddress)")
        }
    }

    // 处理地图点选择
    private func handlePointSelection(_ point: DeliveryPoint) {
        if viewModel.isMultiSelectMode {
            // 多选模式下，切换点的选择状态

            // 如果点已分配到组，不允许选择
            if point.isAssignedToGroup && !viewModel.selectedPoints.contains(point.id) {
                // 给予触觉反馈提示已分组
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                // 记录日志
                logInfo("RouteView - 多选模式：地址点 #\(point.sorted_number): \(point.primaryAddress) 已分配到组，无法选择")
                return
            }

            // 如果已达到选择上限且该点未被选中，则不执行任何操作
            if viewModel.isMaxSelectionReached && !viewModel.selectedPoints.contains(point.id) {
                // 给予触觉反馈提示已达上限
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                // 显示选择上限提示
                viewModel.showSelectionLimitAlert = true

                // 记录日志
                logInfo("RouteView - 已达到选择上限(14个)，无法选择更多地址点")
                return
            }

            // 切换点的选择状态
            viewModel.togglePointSelection(point)

            // 给予触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            // 记录日志
            if viewModel.selectedPoints.contains(point.id) {
                logInfo("RouteView - 多选模式：选中地址点 #\(point.sorted_number): \(point.primaryAddress), 当前已选择 \(viewModel.selectedPoints.count) 个")
            } else {
                logInfo("RouteView - 多选模式：取消选中地址点 #\(point.sorted_number): \(point.primaryAddress), 当前已选择 \(viewModel.selectedPoints.count) 个")
            }
        } else {
            // 检查是否是已完成或失败的配送点
            let isCompleted = point.deliveryStatus == .completed
            let isFailed = point.deliveryStatus == .failed

            if (isCompleted || isFailed) && viewModel.showCompletedDeliveries {
                // 如果是已完成或失败的配送点，显示已派送点卡片
                selectedPoint = point
                showingPointPopover = true
                showingDeliveredPointCard = true

                // 记录日志
                let statusText = isCompleted ? "已完成" : "失败"
                logInfo("RouteView - 显示\(statusText)配送点卡片: \(point.primaryAddress)")
            } else {
                // 普通点击行为：显示标准信息卡片
                selectedPoint = point
                showingPointPopover = true
                showingDeliveredPointCard = false

                // 记录日志
                logInfo("RouteView - 显示地址点信息卡片: \(point.primaryAddress), 优化状态: \(point.isOptimized ? "已优化" : "未优化")")
            }
        }
    }

    // 显示配送点管理界面
    private func showPackageFinder(_ point: DeliveryPoint) {
        // 设置要编辑的点并显示配送点管理表单
        logInfo("RouteView - 准备显示配送点管理界面，设置deliveryPointWrapper=\(point.primaryAddress)")

        // 创建一个包装对象，确保数据不会丢失
        let wrapper = DeliveryPointWrapper(point: point)
        deliveryPointWrapper = wrapper

        // 添加短暂延迟，确保所有状态更新都已完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            logInfo("RouteView - 在主线程设置showingDeliveryPointManagerSheet=true，deliveryPointWrapper=\(self.deliveryPointWrapper?.point.primaryAddress ?? "nil")")
            self.showingDeliveryPointManagerSheet = true
        }

        logInfo("RouteView - 显示配送点管理界面: \(point.primaryAddress)")
    }

    // 优化路线方法
    private func optimizeRoute() {
        // 给予触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 检查地址验证（使用带日志的版本，因为这是用户主动触发的操作）
        if viewModel.hasInvalidAddressesWithLogging() {
            // 获取无效地址索引
            let invalidIndices = viewModel.getInvalidAddressIndices()

            // 显示地址验证警告
            showingInvalidAddressAlert = true
            invalidAddressIndices = invalidIndices
            return
        }

        // 检查订阅限制
        if let currentRoute = viewModel.currentRoute {
            let subscriptionManager = SubscriptionManager.shared
            let limitInfo = subscriptionManager.getOptimizationLimitInfo(for: currentRoute)

            if limitInfo.exceedsLimit {
                // 显示订阅限制警告
                showingSubscriptionLimitAlert = true
                totalPoints = limitInfo.totalPoints
                optimizablePoints = limitInfo.optimizablePoints
                return
            }
        }

        // 开始路线优化
        startOptimization()
    }

    // 开始路线优化
    private func startOptimization() {
        Task {
            await viewModel.recalculateRouteFromDriverLocation()

            // 显示优化完成提示
            withAnimation {
                showRouteOptimizedToast = true
            }

            // 3秒后隐藏提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                withAnimation {
                    showRouteOptimizedToast = false
                }
            }

            // 更新地图视图
            withAnimation(.easeInOut(duration: 0.6)) {
                if let bounds = viewModel.getDeliveryPointsBounds() {
                    viewModel.cameraPosition = .region(bounds)
                }
            }
        }
    }

    // 在分组前检查并应用第三方排序
    private func applyThirdPartySortingIfNeeded() async {
        guard let currentRoute = viewModel.currentRoute else { return }

        // 检查是否有第三方排序数据
        let hasThirdPartySort = currentRoute.points.contains { point in
            !point.isStartPoint && !point.isEndPoint &&
            point.thirdPartySortNumber != nil && !point.thirdPartySortNumber!.isEmpty
        }

        if hasThirdPartySort {
            logInfo("RouteView - 检测到第三方排序数据，在分组前应用第三方排序")

            // 获取所有非起点的点
            let nonStartPoints = currentRoute.points.filter { !$0.isStartPoint && $0.sort_number != 0 }

            // 按第三方排序号排序
            let sortedPoints = nonStartPoints.sorted { point1, point2 in
                let sort1 = point1.thirdPartySortNumber ?? ""
                let sort2 = point2.thirdPartySortNumber ?? ""

                // 如果都有第三方排序号，按数字排序
                if !sort1.isEmpty && !sort2.isEmpty {
                    let num1 = extractNumber(from: sort1)
                    let num2 = extractNumber(from: sort2)
                    return num1 < num2
                }

                // 有第三方排序号的排在前面
                if !sort1.isEmpty && sort2.isEmpty { return true }
                if sort1.isEmpty && !sort2.isEmpty { return false }

                // 都没有第三方排序号时，按sort_number排序
                return point1.sort_number < point2.sort_number
            }

            // 🚨 保护排序字段：检测到第三方排序号时，不修改任何排序字段
            logInfo("🚨 检测到第三方排序号，保护所有排序字段不被修改")
            for point in sortedPoints {
                if let thirdPartySortNumber = point.thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
                    // 🚨 不修改 sorted_number，保持数据完整性
                    // 🚨 不修改 sort_number，保持原始顺序
                    // 🚨 不修改 thirdPartySortNumber，保持第三方排序
                    logInfo("🚨 保护排序字段: point.id=\(point.id), thirdPartySortNumber='\(thirdPartySortNumber)', sorted_number=\(point.sorted_number) (未修改)")
                } else {
                    // 🚨 即使没有第三方号码，也不修改排序字段
                    logInfo("🚨 保护排序字段: point.id=\(point.id), sorted_number=\(point.sorted_number) (未修改)")
                }
                // 只标记为已优化
                point.isOptimized = true
            }

            // 标记路线为已优化
            currentRoute.isOptimized = true

            // 保存更改
            try? modelContext.save()

            // 更新ViewModel中的deliveryPoints数组
            await MainActor.run {
                viewModel.deliveryPoints = currentRoute.points.sorted { point1, point2 in
                    // 起点排在最前面
                    if (point1.isStartPoint || point1.sort_number == 0) && !(point2.isStartPoint || point2.sort_number == 0) {
                        return true
                    } else if !(point1.isStartPoint || point1.sort_number == 0) && (point2.isStartPoint || point2.sort_number == 0) {
                        return false
                    }

                    // 其他点按sorted_number排序
                    return point1.sorted_number < point2.sorted_number
                }
                viewModel.objectWillChange.send()

                logInfo("RouteView - 已应用第三方排序，重新分配了\(sortedPoints.count)个点的sorted_number")
                logInfo("RouteView - 已更新ViewModel.deliveryPoints，前5个点的sorted_number: \(viewModel.deliveryPoints.prefix(5).map { $0.sorted_number })")
                logInfo("RouteView - 前5个点的第三方排序号: \(viewModel.deliveryPoints.prefix(5).map { $0.thirdPartySortNumber ?? "无" })")
                logInfo("RouteView - 前5个点的地址: \(viewModel.deliveryPoints.prefix(5).map { $0.primaryAddress })")
            }
        }
    }

    // 提取数字的辅助函数
    private func extractNumber(from string: String) -> Int {
        let numbers = string.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return Int(numbers) ?? 0
    }

    // 执行自动分组
    private func performAutoGrouping() {
        // 检查订阅状态
        if !viewModel.canUseAutoGrouping() {
            // 显示toast提示用户订阅获取更多分组功能
            logInfo("RouteView - 用户无法使用自动分组，显示订阅提示")
            NotificationCenter.default.post(
                name: Notification.Name("ShowToast"),
                object: "upgrade_to_unlock_auto_grouping".localized
            )
            return
        }

        // 检查免费用户是否已达到分组限制
        let subscriptionManager = SubscriptionManager.shared
        if subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial {
            let groupedCount = subscriptionManager.getCurrentGroupedAddressCount(for: viewModel.currentRoute ?? Route(name: ""))
            if groupedCount >= subscriptionManager.currentTier.maxAutoGroupAddresses {
                // 免费用户已达到分组限制，显示toast提示
                logInfo("RouteView - 免费用户已达到分组限制(\(groupedCount)/\(subscriptionManager.currentTier.maxAutoGroupAddresses))，显示订阅提示")
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: String(format: "free_user_grouping_limit_reached".localized, subscriptionManager.currentTier.maxAutoGroupAddresses)
                )
                return
            }

            // 检查是否有足够的剩余槽位来分组地址
            let remainingSlots = subscriptionManager.remainingGroupAddressSlots(for: viewModel.currentRoute ?? Route(name: ""))
            if remainingSlots == 0 {
                // 没有剩余槽位，显示toast提示
                logInfo("RouteView - 免费用户没有剩余分组槽位(已分组\(groupedCount)/\(subscriptionManager.currentTier.maxAutoGroupAddresses))，显示订阅提示")
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: "upgrade_to_unlock_more_grouping".localized
                )
                return
            }
        }

        // 检查是否有未分组的地址
        let pendingCount = viewModel.getPendingDeliveryCount()
        if pendingCount == 0 {
            // 没有未分组的地址，不执行操作
            return
        }

        // 给予触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 显示进度指示器
        withAnimation {
            isAutoGrouping = true
        }

        // 执行自动分组
        Task {
            // 在分组前检查是否需要应用第三方排序
            await applyThirdPartySortingIfNeeded()

            // 执行自动分组
            let groupsCreated = await viewModel.autoGroupDeliveryPoints()

            // 隐藏进度指示器
            withAnimation {
                isAutoGrouping = false
            }

            // 显示成功提示
            if groupsCreated > 0 {
                withAnimation {
                    showAutoGroupToast = true
                }

                // 3秒后隐藏提示
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                    withAnimation {
                        showAutoGroupToast = false
                    }
                }

                // 记录日志
                logInfo("自动分组完成，创建了 \(groupsCreated) 个分组")

                // 检查是否是免费用户，如果是则在适当时候显示升级提示
                let subscriptionManager = SubscriptionManager.shared
                if subscriptionManager.currentTier == .free && !subscriptionManager.isInFreeTrial {
                    let remainingSlots = subscriptionManager.remainingGroupAddressSlots(for: viewModel.currentRoute ?? Route(name: ""))
                    if remainingSlots == 0 {
                        // 免费版额度已用完，延迟显示升级提示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            logInfo("RouteView - 免费版分组额度已用完，显示升级提示")
                            NotificationCenter.default.post(
                                name: Notification.Name("ShowToast"),
                                object: "upgrade_to_unlock_more_grouping".localized
                            )
                        }
                    }
                }
            }
        }
    }

    // 已移除calculateBottomPadding函数，因为我们使用了新的底部工具栏布局





    // 分组项视图
    struct GroupItemView: View {
        let group: DeliveryGroup
        let viewModel: RouteViewModel
        var onNavigate: (() -> Void)? = nil

        @State private var showingGroupDetail = false

        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                // 标题行
                HStack {
                    Image(systemName: "rectangle.stack.fill")
                        .foregroundColor(.blue)

                    Text(group.name)
                        .font(.headline)

                    Spacer()

                    Text(String(format: "points_count_format".localized, group.points.count))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                // 地址预览
                if !group.points.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(group.sortedPoints.prefix(5)) { point in
                                HStack(spacing: 4) {
                                    Text("\(point.sort_number).")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)

                                    // 显示带单位信息的简化地址
                                    HStack(spacing: 2) {
                                        if point.hasUnitNumber, let unitNumber = point.unitNumber {
                                            Text(unitNumber)
                                                .font(.caption2)
                                                .fontWeight(.semibold)
                                                .foregroundColor(.orange)
                                        }
                                        Text(formatMainAddress(point.primaryAddress, hasUnit: point.hasUnitNumber))
                                            .font(.caption)
                                            .lineLimit(1)
                                    }
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(4)
                            }

                            if group.points.count > 5 {
                                Text(String(format: "additional_points_format".localized, group.points.count - 5))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                    }
                }

                // 按钮行
                HStack(spacing: 12) {
                    // 编辑按钮
                    Button(action: {
                        showingGroupDetail = true
                    }) {
                        HStack {
                            Image(systemName: "pencil")
                            Text("edit".localized)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .foregroundColor(.primary)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 导航按钮
                    Button(action: {
                        // 导航到该组
                        viewModel.navigateToGroup(group) { success, message in
                            // 检查是否需要显示Google Maps网页版提示
                            if success && message == "google_maps_web_version" {
                                // 显示使用网页版的提示
                                let alert = UIAlertController(
                                    title: "google_maps".localized,
                                    message: "using_web_version".localized,
                                    preferredStyle: .alert
                                )
                                alert.addAction(UIAlertAction(title: "understand".localized, style: .default))

                                // 显示提示
                                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                   let rootViewController = windowScene.windows.first?.rootViewController {
                                    rootViewController.present(alert, animated: true)
                                }
                            }
                        }
                        onNavigate?() // 如果提供了回调，则执行
                    }) {
                        HStack {
                            Image(systemName: "location.fill")
                            Text("navigate".localized)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.top, 4)
            }
            .padding(.vertical, 8)
            .sheet(isPresented: $showingGroupDetail) {
                NavigationView {
                    GroupDetailView(group: group, viewModel: viewModel)
                }
            }
        }

        /// 格式化主要地址显示，确保单位信息正确处理
        private func formatMainAddress(_ fullAddress: String, hasUnit: Bool) -> String {
            let components = fullAddress.components(separatedBy: ",")

            if hasUnit && !components.isEmpty {
                // 如果有单位号，第一个组件通常包含"单位号, 街道地址"
                // 我们需要移除单位号部分，只显示街道地址
                let firstComponent = components[0].trimmingCharacters(in: .whitespaces)

                // 查找第一个逗号后的内容作为街道地址
                if let commaIndex = firstComponent.firstIndex(of: ",") {
                    let streetAddress = String(firstComponent[firstComponent.index(after: commaIndex)...])
                        .trimmingCharacters(in: .whitespaces)
                    return streetAddress.isEmpty ? firstComponent : streetAddress
                }
            }

            // 默认返回第一个组件或完整地址
            return components.first?.trimmingCharacters(in: .whitespaces) ?? fullAddress
        }
    }

    // 已移除优化按钮可见性检查函数，因为我们使用了新的悬浮按钮组

    // 强制刷新地图视图 - 优化版本，减少不必要的动画和重绘
    private func refreshMapView() {
        // 记录当前相机位置
        let currentPosition = viewModel.cameraPosition
        Logger.data("刷新地图 - 当前相机位置: \(formatCameraPosition(currentPosition))")

        // 获取当前所有点的边界
        if let bounds = viewModel.getDeliveryPointsBounds() {
            // 直接更新相机位置，避免不必要的动画和重绘
            // 只有当当前位置与目标位置差异较大时才进行动画
            let currentRegion = currentPosition.region
            let shouldAnimate = currentRegion == nil ||
                abs(currentRegion!.center.latitude - bounds.center.latitude) > 0.01 ||
                abs(currentRegion!.center.longitude - bounds.center.longitude) > 0.01 ||
                abs(currentRegion!.span.latitudeDelta - bounds.span.latitudeDelta) > 0.01

            if shouldAnimate {
                withAnimation(.easeInOut(duration: 0.5)) {
                    viewModel.cameraPosition = .region(bounds)
                    Logger.data("刷新地图 - 已更新相机位置到所有点的边界")
                }
            } else {
                // 如果位置差异不大，直接触发视图模型的objectWillChange
                viewModel.objectWillChange.send()
                Logger.data("刷新地图 - 位置差异不大，仅触发视图刷新")
            }
        } else {
            Logger.data("刷新地图 - 无法获取点边界，可能没有点或点坐标无效")
        }
    }

    // 辅助函数：格式化相机位置为字符串
    private func formatCameraPosition(_ position: MapCameraPosition) -> String {
        // 使用字符串插值直接描述相机位置
        if position == .automatic {
            return "automatic"
        } else {
            // 获取相机位置的区域信息（如果有）
            if let region = position.region {
                return "region(center: (\(region.center.latitude), \(region.center.longitude)), span: (\(region.span.latitudeDelta), \(region.span.longitudeDelta)))"
            } else {
                return "custom position"
            }
        }
    }

    // MARK: - 地图标记聚类

    // 点击聚类标记时放大地图
    private func zoomToCluster(coordinate: CLLocationCoordinate2D) {
        withAnimation(.easeInOut(duration: 0.5)) {
            viewModel.cameraPosition = .region(
                MKCoordinateRegion(
                    center: coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
                )
            )
        }
    }

    // 地图标记聚类结构
    struct PointCluster: Identifiable {
        let id = UUID()
        let key: String
        var points: [DeliveryPoint]
        var coordinate: CLLocationCoordinate2D {
            // 计算聚类中所有点的平均位置
            let latSum = points.reduce(0.0) { $0 + $1.coordinate.latitude }
            let lngSum = points.reduce(0.0) { $0 + $1.coordinate.longitude }
            return CLLocationCoordinate2D(
                latitude: latSum / Double(points.count),
                longitude: lngSum / Double(points.count)
            )
        }
    }

    // 创建点位聚类，用于优化地图渲染
    private func createClusters(from points: [DeliveryPoint], clusterRadius: Double) -> [PointCluster] {
        var clusters: [String: PointCluster] = [:]

        // 首先检查当前地图缩放级别以动态调整聚类半径
        let region = viewModel.cameraPosition.region ?? MKCoordinateRegion()
        let zoomLevel = log2(360 / region.span.longitudeDelta) // 估算当前缩放级别

        // 根据缩放级别动态调整聚类半径 - 缩放越小，聚类半径越大
        // 增加基础系数以更积极地聚合点
        let dynamicRadius = max(clusterRadius * (18 / max(zoomLevel, 1)), 0.003) // 设置最小半径避免过度聚集

        // 过滤掉坐标异常的点，防止无效坐标导致聚类错误
        let validPoints = points.filter {
            abs($0.coordinate.latitude) <= 90 && abs($0.coordinate.longitude) <= 180
        }

        // 检测并处理重复坐标点
        var uniqueCoordinates = Set<String>()
        var uniquePoints: [DeliveryPoint] = []

        for point in validPoints {
            // 创建坐标标识符（保留6位小数精度，足够识别非常接近的点）
            let coordKey = String(format: "%.6f,%.6f", point.coordinate.latitude, point.coordinate.longitude)

            // 如果这个坐标是新的，添加到唯一点列表
            if uniqueCoordinates.insert(coordKey).inserted {
                uniquePoints.append(point)
            }
        }

        // 第一步：按网格进行初步聚类
        for point in uniquePoints {
            // 排除起点和终点，这些点不应参与聚类
            if point.isStartPoint || point.isEndPoint {
                continue
            }

            // 创建一个网格索引，每个clusterRadius大小的区域属于一个聚类
            let gridX = Int(point.coordinate.longitude / dynamicRadius)
            let gridY = Int(point.coordinate.latitude / dynamicRadius)
            let key = "\(gridX)_\(gridY)"

            if var cluster = clusters[key] {
                // 将点添加到现有聚类，避免添加重复点
                if !cluster.points.contains(where: { $0.id == point.id }) {
                    cluster.points.append(point)
                    clusters[key] = cluster
                }
            } else {
                // 创建新的聚类
                clusters[key] = PointCluster(key: key, points: [point])
            }
        }

        // 第二步：合并距离过近的聚类，避免标记重叠
        var mergedClusters = mergeNearbyClusters(clusters: Array(clusters.values), mergeDistance: dynamicRadius * 1.8)

        // 第三步：添加起点和终点（不参与聚类）
        for point in points where point.isStartPoint || point.isEndPoint {
            let singlePointCluster = PointCluster(key: "single_\(point.id)", points: [point])
            mergedClusters.append(singlePointCluster)
        }

        return mergedClusters
    }

    // 合并距离过近的聚类以避免UI重叠
    private func mergeNearbyClusters(clusters: [PointCluster], mergeDistance: Double) -> [PointCluster] {
        var result = [PointCluster]()
        var processedClusters = clusters

        // 按点数量降序排序，优先处理大聚类
        processedClusters.sort { $0.points.count > $1.points.count }

        // 用于标记已处理的点，避免一个点被添加到多个聚类
        var processedPointIds = Set<UUID>()

        while !processedClusters.isEmpty {
            let current = processedClusters.removeFirst()

            // 确认当前聚类中的点还没有被处理
            var uniquePoints = [DeliveryPoint]()
            for point in current.points {
                if !processedPointIds.contains(point.id) {
                    uniquePoints.append(point)
                    processedPointIds.insert(point.id)
                }
            }

            // 如果没有有效点，跳过此聚类
            if uniquePoints.isEmpty {
                continue
            }

            var merged = PointCluster(key: current.key, points: uniquePoints)

            // 查找所有与当前聚类距离小于阈值的聚类
            var i = 0
            while i < processedClusters.count {
                let distance = calculateDistance(coord1: merged.coordinate, coord2: processedClusters[i].coordinate)

                if distance < mergeDistance {
                    // 合并聚类，但只添加未处理的点
                    for point in processedClusters[i].points {
                        if !processedPointIds.contains(point.id) {
                            merged.points.append(point)
                            processedPointIds.insert(point.id)
                        }
                    }
                    processedClusters.remove(at: i)
                } else {
                    i += 1
                }
            }

            // 只有当聚类包含至少一个点时才添加到结果中
            if !merged.points.isEmpty {
                result.append(merged)
            }
        }

        return result
    }

    // 计算两个坐标之间的距离（简化版）
    private func calculateDistance(coord1: CLLocationCoordinate2D, coord2: CLLocationCoordinate2D) -> Double {
        let latDiff = coord1.latitude - coord2.latitude
        let lonDiff = coord1.longitude - coord2.longitude
        return sqrt(latDiff * latDiff + lonDiff * lonDiff)
    }

    /// 格式化主要地址显示，确保单位信息正确处理
    private func formatMainAddress(_ fullAddress: String, hasUnit: Bool) -> String {
        let components = fullAddress.components(separatedBy: ",")

        if hasUnit && !components.isEmpty {
            // 如果有单位号，第一个组件通常包含"单位号, 街道地址"
            // 我们需要移除单位号部分，只显示街道地址
            let firstComponent = components[0].trimmingCharacters(in: .whitespaces)

            // 查找第一个逗号后的内容作为街道地址
            if let commaIndex = firstComponent.firstIndex(of: ",") {
                let streetAddress = String(firstComponent[firstComponent.index(after: commaIndex)...])
                    .trimmingCharacters(in: .whitespaces)
                return streetAddress.isEmpty ? firstComponent : streetAddress
            }
        }

        // 默认返回第一个组件或完整地址
        return components.first?.trimmingCharacters(in: .whitespaces) ?? fullAddress
    }

    /// 智能分割地址为两行显示
    private func getSmartAddressParts(_ address: String) -> (firstLine: String, secondLine: String) {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

        // 如果地址很短或只有一个组件，直接返回单行
        if address.count <= 25 || components.count <= 1 {
            return (address, "")
        }

        // 如果有两个组件，第一个为第一行，第二个为第二行
        if components.count == 2 {
            return (components[0], components[1])
        }

        // 如果有多个组件，第一个为第一行，其余为第二行
        if components.count > 2 {
            let firstLine = components[0]
            let secondLine = components.dropFirst().joined(separator: ", ")
            return (firstLine, secondLine)
        }

        // 单个长组件的情况，在空格处分割
        let words = address.components(separatedBy: " ")
        if words.count > 3 {
            let midPoint = words.count / 2
            let firstLine = words.prefix(midPoint).joined(separator: " ")
            let secondLine = words.suffix(from: midPoint).joined(separator: " ")
            return (firstLine, secondLine)
        }

        return (address, "")
    }

    // MARK: - 地址排序方法

    /// 统一的地址排序方法
    /// 🎯 第三方排序号具有最高优先级，完全覆盖其他所有排序逻辑
    private func sortDeliveryPoints(_ points: [DeliveryPoint], isOptimized: Bool) -> [DeliveryPoint] {
        return points.sorted { point1, point2 in
            // 1. 起点总是最前面
            let point1IsStart = point1.isStartPoint || point1.sort_number == 0
            let point2IsStart = point2.isStartPoint || point2.sort_number == 0

            if point1IsStart && !point2IsStart { return true }
            if !point1IsStart && point2IsStart { return false }

            // 2. 🎯 第三方排序号最高优先级 - 覆盖一切其他逻辑
            let point1ThirdParty = point1.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            let point2ThirdParty = point2.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

            let point1HasThirdParty = !point1ThirdParty.isEmpty
            let point2HasThirdParty = !point2ThirdParty.isEmpty

            // 🚀 如果都有第三方排序号，严格按第三方排序号排序，忽略其他所有因素
            if point1HasThirdParty && point2HasThirdParty {
                let num1 = extractNumber(from: point1ThirdParty)
                let num2 = extractNumber(from: point2ThirdParty)
                return num1 < num2
            }

            // 🚀 有第三方排序号的绝对优先，无论其他任何条件
            if point1HasThirdParty && !point2HasThirdParty { return true }
            if !point1HasThirdParty && point2HasThirdParty { return false }

            // 3. 都没有第三方排序号时，错误地址优先（有警告或验证状态不是valid的）
            let point1HasError = hasAddressError(point1)
            let point2HasError = hasAddressError(point2)

            if point1HasError && !point2HasError { return true }
            if !point1HasError && point2HasError { return false }

            // 4. 最后按sort_number或sorted_number排序
            if isOptimized {
                return point1.sorted_number < point2.sorted_number
            } else {
                return point1.sort_number < point2.sort_number
            }
        }
    }

    /// 检查地址是否有错误
    private func hasAddressError(_ point: DeliveryPoint) -> Bool {
        // 🎯 排除起点的验证检查（sort_number = 0 或 isStartPoint = true）
        if point.isStartPoint || point.sort_number == 0 {
            return false
        }

        // 检查地理编码警告
        if let warning = point.geocodingWarning, !warning.isEmpty {
            return true
        }

        // 检查位置验证状态 - 只有明确的invalid状态才算有问题
        let validationStatus = LocationValidationStatus(rawValue: point.locationValidationStatus) ?? .unknown
        return validationStatus == .invalid
    }

    // MARK: - 相同坐标标记分离

    /// 计算相同坐标点的分离偏移，确保每个标记都有独立的显示位置
    private func calculateCoordinateOffsets(for points: [DeliveryPoint]) -> [UUID: CLLocationCoordinate2D] {
        var offsets: [UUID: CLLocationCoordinate2D] = [:]

        // 按坐标分组，找出相同坐标的点
        var coordinateGroups: [String: [DeliveryPoint]] = [:]

        for point in points {
            // 创建坐标键，精确到6位小数（约1米精度）
            let coordKey = String(format: "%.6f,%.6f", point.coordinate.latitude, point.coordinate.longitude)

            if coordinateGroups[coordKey] == nil {
                coordinateGroups[coordKey] = []
            }
            coordinateGroups[coordKey]?.append(point)
        }

        // 为每组相同坐标的点计算分离偏移
        for (_, groupPoints) in coordinateGroups {
            if groupPoints.count <= 1 {
                // 单个点不需要偏移
                continue
            }

            // 多个点需要分离
            let baseCoordinate = groupPoints[0].coordinate
            let separationDistance: Double = 0.0003 // 约33米的分离距离，确保点击区域不重叠

            // 按sorted_number排序，确保编号小的在前面
            let sortedPoints = groupPoints.sorted { $0.sorted_number < $1.sorted_number }

            for (index, point) in sortedPoints.enumerated() {
                if sortedPoints.count == 2 {
                    // 两个点：左右分布，使用完整的偏移距离确保点击区域不重叠
                    if index == 0 {
                        // 第一个点向左偏移
                        offsets[point.id] = CLLocationCoordinate2D(
                            latitude: baseCoordinate.latitude,
                            longitude: baseCoordinate.longitude - separationDistance
                        )
                    } else {
                        // 第二个点向右偏移
                        offsets[point.id] = CLLocationCoordinate2D(
                            latitude: baseCoordinate.latitude,
                            longitude: baseCoordinate.longitude + separationDistance
                        )
                    }
                } else {
                    // 多个点：圆形分布
                    let angle = Double(index) * (2.0 * Double.pi / Double(sortedPoints.count))
                    let offsetLat = baseCoordinate.latitude + separationDistance * cos(angle)
                    let offsetLng = baseCoordinate.longitude + separationDistance * sin(angle)

                    offsets[point.id] = CLLocationCoordinate2D(
                        latitude: offsetLat,
                        longitude: offsetLng
                    )
                }
            }

            // 记录分离信息
            let numbers = groupPoints.map { $0.sorted_number }.sorted()
            logInfo("🎯 分离相同坐标标记: 编号 \(numbers) 在坐标 (\(String(format: "%.6f", baseCoordinate.latitude)), \(String(format: "%.6f", baseCoordinate.longitude)))")

            // 记录每个点的偏移坐标
            for point in sortedPoints {
                if let offsetCoord = offsets[point.id] {
                    logInfo("   - 编号 \(point.sorted_number): 偏移到 (\(String(format: "%.6f", offsetCoord.latitude)), \(String(format: "%.6f", offsetCoord.longitude)))")
                }
            }
        }

        return offsets
    }
}

// 在文件末尾，替换 #Preview 宏代码块
struct RouteView_Previews: PreviewProvider {
    static var previews: some View {
        // 使用内存数据库，避免预览崩溃
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        var container: ModelContainer
        do {
            let tempContainer = try ModelContainer(for: DeliveryPoint.self, DeliveryGroup.self, Route.self, configurations: config)
            container = tempContainer

            // 检查是否已有示例数据
            let routeDescriptor = FetchDescriptor<Route>()
            let existingRoutes = try container.mainContext.fetch(routeDescriptor)

            // 如果没有示例数据，创建新的
            if existingRoutes.isEmpty {
                // 添加示例配送点 - 仅用于预览，包含单位信息测试
                let samplePoints = [
                    DeliveryPoint(sort_number: 0, streetName: "123 Main Street", coordinate: CLLocationCoordinate2D(latitude: -37.8815, longitude: 145.1642), isStartPoint: true),
                    DeliveryPoint(sort_number: 1, streetName: "456 Oak Avenue, Apt 12B", coordinate: CLLocationCoordinate2D(latitude: -37.8842, longitude: 145.1642)),
                    DeliveryPoint(sort_number: 2, streetName: "789 Pine Road, Unit 5", coordinate: CLLocationCoordinate2D(latitude: -37.8798, longitude: 145.1642)),
                    DeliveryPoint(sort_number: 3, streetName: "321 Elm Street, Suite 456", coordinate: CLLocationCoordinate2D(latitude: -37.8756, longitude: 145.1553)),
                    DeliveryPoint(sort_number: 4, streetName: "654 Maple Drive", coordinate: CLLocationCoordinate2D(latitude: -37.8756, longitude: 145.1342), isEndPoint: true)
                ]

                // 🧪 测试单位信息提取功能
                for point in samplePoints {
                    if let extractedUnit = DeliveryPoint.extractUnitNumber(from: point.streetName ?? "") {
                        point.unitNumber = extractedUnit
                        print("🧪 预览测试 - 提取单位信息: \(extractedUnit) <- \(point.streetName ?? "")")
                    }
                }

                // 添加示例路线
                let route = Route(name: "路线 2025-04-21")
                container.mainContext.insert(route)

                // 添加示例分组
                let sampleGroup = DeliveryGroup(name: "示例分组", points: [samplePoints[0], samplePoints[1]], groupNumber: 1)

                // 插入数据
                for point in samplePoints {
                    container.mainContext.insert(point)
                    // 将点添加到路线中
                    route.addPoint(point) // 使用辅助方法添加
                }
                container.mainContext.insert(sampleGroup)
                try container.mainContext.save()


                // 设置分组状态
                samplePoints[0].isAssignedToGroup = true
                samplePoints[0].assignedGroupNumber = 1
                samplePoints[1].isAssignedToGroup = true
                samplePoints[1].assignedGroupNumber = 1

                try container.mainContext.save()
            }
        } catch {
            print("[INFO] 预览创建容器失败，使用共享容器")
            container = getPersistentContainer()
        }

        return RouteView()
            .modelContainer(container)
            .environment(\.colorScheme, .light) // 可以切换为.dark来预览暗色模式
    }
}

// MARK: - RouteViewModel Extension
extension RouteViewModel {
    func hasEnoughPointsForOptimization() -> Bool {
        guard let currentRoute = self.currentRoute else { return false }

        // 移除日志输出，因为这个方法在SwiftUI body中被频繁调用
        let totalPoints = currentRoute.points.count
        let startPoints = currentRoute.points.filter { $0.isStartPoint }.count
        let endPoints = currentRoute.points.filter { $0.isEndPoint }.count
        let stopPoints = currentRoute.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count

        // 修改条件：如果没有明确的起点和终点，只要总点数>=3就允许优化
        if startPoints == 0 && endPoints == 0 {
            return totalPoints >= 3
        } else {
            // 原始逻辑：只有当有至少3个停靠点（非起点非终点）时才启用优化
            return stopPoints >= 3
        }
    }

    func showOptimizationSheet() {
        // 显示底部面板
        self.showRouteSheet = true

        // 可以添加额外代码来直接触发优化功能
        // 例如：直接滚动到底部面板中的优化按钮位置或调用优化方法
    }








}

