import SwiftUI

/// Dark Mode 优化效果测试视图
/// 用于展示和测试新的 Dark Mode 颜色方案
struct DarkModeTestView: View {
    @State private var sampleText = ""
    @State private var isToggleOn = false
    @State private var selectedOption = 0

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {

                    // 标题区域
                    headerSection

                    // 输入框测试
                    inputSection

                    // 按钮测试
                    buttonSection

                    // 卡片测试
                    cardSection

                    // 状态色测试
                    statusSection

                    // 文字层次测试
                    textSection

                    // 扫描器界面测试
                    scannerSection

                    // 路线界面测试
                    routeSection

                    // 地址卡片测试
                    mapCardSection

                    Spacer(minLength: 50)
                }
                .padding()
            }
            .background(Color.adaptiveBackground)
            .navigationTitle("Dark Mode 测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    // MARK: - 标题区域
    private var headerSection: some View {
        VStack(spacing: 12) {
            Text("NaviBatch Dark Mode 优化")
                .font(.title2.bold())
                .foregroundColor(.adaptivePrimaryText)

            Text("专为司机用户优化的深色主题")
                .font(.subheadline)
                .foregroundColor(.adaptiveSecondaryText)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 输入框测试
    private var inputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("输入框优化")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            // 搜索框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.adaptiveSecondaryIcon)

                TextField("输入地址搜索...", text: $sampleText)
                    .foregroundColor(.adaptivePrimaryText)

                if !sampleText.isEmpty {
                    Button(action: { sampleText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.adaptiveSecondaryIcon)
                    }
                }
            }
            .padding()
            .adaptiveInputStyle()

            Text("✓ 增强对比度，更清晰的边框")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 按钮测试
    private var buttonSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("按钮优化")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            // 主要按钮
            Button(action: {}) {
                HStack {
                    Image(systemName: "location.fill")
                    Text("使用当前位置")
                }
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .adaptiveButtonStyle(isPrimary: true)
            }

            // 功能按钮组
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(["地址簿", "批量贴上", "文件导入", "线上下载"], id: \.self) { title in
                        Button(action: {}) {
                            HStack(spacing: 4) {
                                Image(systemName: "doc.on.clipboard")
                                    .font(.system(size: 14))
                                Text(title)
                                    .font(.system(size: 14, weight: .medium))
                            }
                            .foregroundColor(.adaptivePrimaryText)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .adaptiveFunctionButtonStyle()
                        }
                    }
                }
                .padding(.horizontal, 1)
            }

            Text("✓ 更亮的蓝色，增强可见性")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 卡片测试
    private var cardSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("卡片层次")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "location.circle.fill")
                        .foregroundColor(.adaptiveSuccess)
                    Text("已选择坐标")
                        .foregroundColor(.adaptivePrimaryText)
                    Spacer()
                }

                Text("37.7749, -122.4194")
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.adaptiveSecondaryText)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Toggle("收藏地址", isOn: $isToggleOn)
                    .toggleStyle(SwitchToggleStyle(tint: .adaptivePrimaryButton))
            }
            .padding()
            .background(Color.adaptiveSecondaryBackground)
            .cornerRadius(8)

            Text("✓ 清晰的层次区分，更好的边框效果")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 状态色测试
    private var statusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("状态颜色")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            VStack(spacing: 8) {
                statusRow(icon: "checkmark.circle.fill",
                         text: "地址验证成功",
                         color: .adaptiveSuccess)

                statusRow(icon: "exclamationmark.triangle.fill",
                         text: "地址需要确认",
                         color: .adaptiveWarning)

                statusRow(icon: "xmark.circle.fill",
                         text: "地址验证失败",
                         color: .adaptiveError)
            }

            Text("✓ 更亮的状态色，确保夜间可见性")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 文字层次测试
    private var textSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("文字层次")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            VStack(alignment: .leading, spacing: 6) {
                Text("主要文字 - 最高可读性")
                    .font(.body)
                    .foregroundColor(.adaptivePrimaryText)

                Text("次要文字 - 更明亮的灰色")
                    .font(.subheadline)
                    .foregroundColor(.adaptiveSecondaryText)

                Text("占位符文字 - 适中的对比度")
                    .font(.caption)
                    .foregroundColor(.adaptivePlaceholderText)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            Text("✓ 优化的文字对比度，减少视觉疲劳")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 辅助方法
    private func statusRow(icon: String, text: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.system(size: 16))

            Text(text)
                .foregroundColor(.adaptivePrimaryText)
                .font(.system(size: 14))

            Spacer()
        }
        .padding(.vertical, 4)
    }

    // MARK: - 扫描器界面测试
    private var scannerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("扫描器界面优化")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            // 国家选择区域模拟
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.adaptivePrimaryIcon.opacity(0.15))
                            .frame(width: 32, height: 32)

                        Image(systemName: "location.fill")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.adaptivePrimaryIcon)
                    }

                    Text("美国快递")
                        .font(.subheadline.weight(.semibold))
                        .foregroundColor(.adaptivePrimaryText)

                    Spacer()

                    Image(systemName: "pencil")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.adaptivePrimaryIcon)
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )

                // 应用类型按钮组
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(["Just Photo", "Amazon Flex", "iMile", "GoFo"], id: \.self) { appName in
                            Button(action: {}) {
                                Text(appName)
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(appName == "Just Photo" ? .white : .adaptivePrimaryText)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(appName == "Just Photo" ? Color.orange : Color.adaptiveSecondaryButton)
                                    .cornerRadius(16)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(appName == "Just Photo" ? Color.orange : Color.adaptiveBorder,
                                                   lineWidth: appName == "Just Photo" ? 2 : 1)
                                    )
                            }
                        }
                    }
                    .padding(.horizontal, 1)
                }

                // 选择照片区域
                VStack(spacing: 12) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.largeTitle)
                        .foregroundColor(.adaptivePrimaryIcon)

                    Text("选择照片")
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryIcon)

                    Text("点击从照片库选择")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 120)
                .background(Color.adaptivePrimaryIcon.opacity(0.1))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.adaptivePrimaryIcon, style: StrokeStyle(lineWidth: 2, dash: [5]))
                )
            }

            Text("✓ 优化了国家选择、应用按钮和照片选择区域的 Dark Mode 显示")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 路线界面测试
    private var routeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("路线界面优化")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            // 路线头部区域模拟
            VStack(spacing: 12) {
                HStack {
                    // GoFo 标签 - 使用优化后的颜色
                    Text("GoFo")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.adaptiveGoFo)
                        .cornerRadius(6)

                    Spacer()

                    // 全部清除按钮
                    Text("全部清除")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.adaptiveError)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.adaptiveError.opacity(0.1))
                        .cornerRadius(6)
                }

                // 地址列表项模拟
                HStack(spacing: 12) {
                    // 编号圆圈
                    ZStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.adaptivePrimaryIcon)
                            .frame(width: 36, height: 36)

                        Text("1")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.white)
                    }

                    // 地址信息
                    VStack(alignment: .leading, spacing: 4) {
                        Text("GoFo: 1")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.adaptiveGoFo)
                            .cornerRadius(6)

                        Text("3420 Tupelo Drive, 95209")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.adaptivePrimaryText)
                            .lineLimit(2)
                    }

                    Spacer()

                    // 操作按钮组
                    HStack(spacing: 8) {
                        // GO 按钮
                        Button(action: {}) {
                            Image(systemName: "location.fill")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(Color.adaptivePrimaryIcon)
                                .cornerRadius(6)
                        }

                        // Deliver 按钮
                        Button(action: {}) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(Color.adaptiveSuccess)
                                .cornerRadius(6)
                        }

                        // More 按钮
                        Button(action: {}) {
                            Image(systemName: "ellipsis")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.adaptiveSecondaryText)
                                .frame(width: 32, height: 32)
                                .background(Color.adaptiveSecondaryButton)
                                .cornerRadius(6)
                        }
                    }
                }
                .padding()
                .adaptiveCardStyle()
            }

            Text("✓ GoFo 标签使用深金色，按钮对比度增强，地址文字更清晰")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }

    // MARK: - 地址卡片测试
    private var mapCardSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("地址卡片优化")
                .font(.headline)
                .foregroundColor(.adaptivePrimaryText)

            // 地址卡片模拟
            VStack(spacing: 0) {
                // 地址信息部分
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        // 编号圆圈
                        Text("3")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(Color.adaptivePrimaryIcon)
                            .cornerRadius(6)

                        // 地址信息
                        VStack(alignment: .leading, spacing: 2) {
                            // GoFo 标签
                            Text("GoFo: 4")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.adaptiveGoFo)
                                .cornerRadius(6)

                            // 地址文字
                            Text("4265 Maddie Circle")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.adaptivePrimaryText)

                            Text("95209")
                                .font(.subheadline)
                                .foregroundColor(.adaptiveSecondaryText)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)

                // 底部按钮区域
                HStack(spacing: 0) {
                    // 导航按钮
                    Button(action: {}) {
                        HStack {
                            Image(systemName: "location.fill")
                            Text("导航")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.adaptivePrimaryIcon)
                        .foregroundColor(.white)
                    }

                    // 查看实景图按钮
                    Button(action: {}) {
                        HStack {
                            Image(systemName: "binoculars.fill")
                            Text("查看实景图")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.adaptivePrimaryIcon)
                        .foregroundColor(.white)
                    }
                }
            }
            .background(Color.adaptiveCardBackground)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)

            Text("✓ GoFo 标签深金色，按钮自适应蓝色，卡片背景协调")
                .font(.caption)
                .foregroundColor(.adaptiveSuccess)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .adaptiveCardStyle()
    }
}

#Preview("Dark Mode Test - Light") {
    DarkModeTestView()
        .preferredColorScheme(.light)
}

#Preview("Dark Mode Test - Dark") {
    DarkModeTestView()
        .preferredColorScheme(.dark)
}
