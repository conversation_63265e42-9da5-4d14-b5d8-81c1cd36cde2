import SwiftUI
import CoreLocation

/// 地址翻译测试视图
struct AddressTranslationTestView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var testInput = ""
    @State private var translationResult = ""
    @State private var testResults: [TestResult] = []
    @State private var isRunningTests = false

    struct TestResult {
        let original: String
        let translated: String
        let passed: Bool
        let description: String
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 手动测试区域
                    manualTestSection

                    // 预设测试区域
                    presetTestSection

                    // 测试结果区域
                    testResultsSection
                }
                .padding()
            }
            .navigationTitle("地址翻译测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - 视图组件

    private var manualTestSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("手动测试")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 12) {
                TextField("输入中文地址", text: $testInput)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                Button("翻译") {
                    translateManualInput()
                }
                .buttonStyle(.borderedProminent)
                .disabled(testInput.isEmpty)

                if !translationResult.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("翻译结果:")
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        Text(translationResult)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                            .font(.system(.body, design: .monospaced))
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var presetTestSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("预设测试")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                Button("运行Wing ning house测试") {
                    runWingNingHouseTest()
                }
                .buttonStyle(.bordered)
                .disabled(isRunningTests)

                Button("运行香港地址翻译测试") {
                    runHongKongAddressTest()
                }
                .buttonStyle(.bordered)
                .disabled(isRunningTests)

                Button("测试荃湾海盛路3号") {
                    runTsuenWanTest()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isRunningTests)

                Button("运行完整翻译测试") {
                    runFullTranslationTest()
                }
                .buttonStyle(.bordered)
                .disabled(isRunningTests)

                Button("测试坐标系转换") {
                    runCoordinateConversionTest()
                }
                .buttonStyle(.bordered)
                .disabled(isRunningTests)

                Button("清除测试结果") {
                    clearTestResults()
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("测试结果")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                if !testResults.isEmpty {
                    let passedCount = testResults.filter { $0.passed }.count
                    Text("\(passedCount)/\(testResults.count) 通过")
                        .font(.caption)
                        .foregroundColor(passedCount == testResults.count ? .green : .orange)
                }
            }

            if testResults.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                LazyVStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        testResultRow(result: result, index: index + 1)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private func testResultRow(result: TestResult, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("\(index). \(result.description)")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Spacer()

                Image(systemName: result.passed ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.passed ? .green : .red)
            }

            Text("原始: \(result.original)")
                .font(.caption)
                .foregroundColor(.secondary)

            Text("翻译: \(result.translated)")
                .font(.caption)
                .foregroundColor(.primary)
        }
        .padding(8)
        .background(Color(.systemBackground))
        .cornerRadius(6)
    }

    // MARK: - 测试方法

    private func translateManualInput() {
        translationResult = AddressStandardizer.translateAddressToEnglish(testInput)
    }

    private func runWingNingHouseTest() {
        isRunningTests = true

        let testCases = [
            ("Wing ning house", "Wing ning house", "英文地址保持不变"),
            ("恒来邨永宁楼", "Wing Ning Building", "香港公屋翻译"),
            ("永宁楼", "Wing Ning Building", "楼宇名称翻译"),
            ("恒来邨", "Hang Lai Estate", "屋邨名称翻译")
        ]

        for (original, expectedKeyword, description) in testCases {
            let translated = AddressStandardizer.translateAddressToEnglish(original)
            let passed = translated.contains(expectedKeyword) || original == translated

            testResults.append(TestResult(
                original: original,
                translated: translated,
                passed: passed,
                description: description
            ))
        }

        isRunningTests = false
    }

    private func runTsuenWanTest() {
        isRunningTests = true
        testResults.removeAll()

        let testAddress = "荃湾海盛路3号"

        // 1. 基本翻译测试
        let translated = AddressStandardizer.translateAddressToEnglish(testAddress)
        testResults.append(TestResult(
            original: testAddress,
            translated: translated,
            passed: translated != testAddress,
            description: "基本翻译功能"
        ))

        // 2. 国家检测测试
        let countryInfo = AddressCountryDetector.detectCountry(from: testAddress)
        testResults.append(TestResult(
            original: "国家检测",
            translated: "\(countryInfo?.name ?? "未知") (\(countryInfo?.code ?? "N/A"))",
            passed: countryInfo?.code == "HK",
            description: "应该检测为香港地址"
        ))

        // 3. 候选地址生成测试
        let candidates = AddressStandardizer.generateGeocodingCandidates(testAddress)
        testResults.append(TestResult(
            original: "候选地址数量",
            translated: "\(candidates.count) 个候选地址",
            passed: candidates.count > 1,
            description: "应该生成多个候选地址"
        ))

        // 4. 显示所有候选地址
        for (index, candidate) in candidates.enumerated() {
            testResults.append(TestResult(
                original: "候选地址 \(index + 1)",
                translated: candidate,
                passed: true,
                description: "生成的候选地址"
            ))
        }

        // 5. 中文字符检测
        let containsChinese = AddressStandardizer.containsChineseCharacters(testAddress)
        testResults.append(TestResult(
            original: "中文字符检测",
            translated: containsChinese ? "包含中文" : "不包含中文",
            passed: containsChinese,
            description: "应该检测到中文字符"
        ))

        isRunningTests = false
    }

    private func runHongKongAddressTest() {
        isRunningTests = true

        let testCases = [
            ("中环金钟道", "Central Admiralty Road", "香港地区+道路翻译"),
            ("旺角弥敦道", "Mong Kok Nathan Road", "香港繁忙区域翻译"),
            ("铜锣湾时代广场", "Causeway Bay Times Plaza", "香港商业区翻译"),
            ("深水埗长沙湾道", "Sham Shui Po Cheung Sha Wan Road", "香港区域+道路翻译"),
            ("九龙塘又一村", "Kowloon Tong Yau Yat Chuen", "香港住宅区翻译"),
            ("沙田大围道", "Sha Tin Tai Wai Road", "新界地址翻译")
        ]

        for (original, expectedKeyword, description) in testCases {
            let translated = AddressStandardizer.translateAddressToEnglish(original)
            let passed = translated.contains(expectedKeyword.components(separatedBy: " ").first!)

            testResults.append(TestResult(
                original: original,
                translated: translated,
                passed: passed,
                description: description
            ))
        }

        isRunningTests = false
    }

    private func runFullTranslationTest() {
        isRunningTests = true

        // 运行内置的翻译测试
        runWingNingHouseTest()
        runHongKongAddressTest()

        // 添加更多测试案例
        runAdditionalTranslationTests()

        isRunningTests = false
    }

    private func runAdditionalTranslationTests() {
        // 添加更多国际地址翻译测试
        let additionalTests = [
            ("永寧樓", "Wing Ning House", "香港地址翻译"),
            ("中環", "Central", "香港地区翻译"),
            ("尖沙咀", "Tsim Sha Tsui", "香港地区翻译"),
            ("北京市朝阳区", "Chaoyang District, Beijing", "中国地址翻译"),
            ("上海市浦东新区", "Pudong New Area, Shanghai", "中国地址翻译")
        ]

        for (original, expected, description) in additionalTests {
            let translated = AddressStandardizer.translateAddressToEnglish(original)
            let passed = translated.lowercased().contains(expected.lowercased()) ||
                        expected.lowercased().contains(translated.lowercased())

            testResults.append(TestResult(
                original: original,
                translated: translated,
                passed: passed,
                description: description
            ))
        }
    }

    private func runCoordinateConversionTest() {
        isRunningTests = true

        // 测试坐标转换功能
        let coordinateTests = [
            // 香港坐标测试
            (22.3193, 114.1694, "香港中环", "香港地址坐标转换"),
            (22.2783, 114.1747, "香港尖沙咀", "香港旺角坐标转换"),

            // 北京坐标测试
            (39.9042, 116.4074, "北京天安门", "北京地址坐标转换"),
            (31.2304, 121.4737, "上海外滩", "上海地址坐标转换"),

            // 澳大利亚坐标测试（不应该转换）
            (-37.8136, 144.9631, "墨尔本市中心", "澳大利亚地址坐标（不转换）"),
            (-33.8688, 151.2093, "悉尼歌剧院", "澳大利亚地址坐标（不转换）")
        ]

        for (lat, lon, location, description) in coordinateTests {
            let originalCoordinate = CLLocationCoordinate2D(latitude: lat, longitude: lon)
            let convertedCoordinate = originalCoordinate.smartConverted(fromWgs84: true)

            let isInChina = originalCoordinate.isInChina()
            let hasChanged = abs(originalCoordinate.latitude - convertedCoordinate.latitude) > 0.0001 ||
                           abs(originalCoordinate.longitude - convertedCoordinate.longitude) > 0.0001

            let passed = isInChina ? hasChanged : !hasChanged

            testResults.append(TestResult(
                original: "\(location): (\(lat), \(lon))",
                translated: "转换后: (\(String(format: "%.6f", convertedCoordinate.latitude)), \(String(format: "%.6f", convertedCoordinate.longitude)))",
                passed: passed,
                description: description
            ))
        }

        isRunningTests = false
    }

    private func clearTestResults() {
        testResults.removeAll()
        translationResult = ""
    }
}

#Preview("AddressTranslationTestView") {
    AddressTranslationTestView()
}
