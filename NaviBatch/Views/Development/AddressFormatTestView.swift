import SwiftUI
import CoreLocation

struct AddressFormatTestView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var testResults: [AddressFormatTestResult] = []
    @State private var isRunningTest = false
    
    // 测试地址样本
    private let testAddresses = [
        // 美国地址 - 格式问题
        "272, Cedar Street, Fremont, CA, United States",
        "105 Cedar St, Fremont, CA, 94539, United States",
        "290 Cedar Street, Fremont, CA 94538",
        "123, Main Street, San Francisco, CA",
        "456 Oak Ave, Los Angeles, CA, 90210, USA",
        
        // 澳大利亚地址 - 格式问题
        "15, Collins Street, Melbourne, VIC, Australia",
        "42 George St, Sydney, NSW 2000, Australia",
        "789, Queen Street, Brisbane, QLD, 4000",
        
        // 加拿大地址
        "100, King Street, Toronto, ON, M5H 1A1, Canada",
        "250 University Ave, Toronto, ON, Canada",
        
        // 其他格式问题
        "999,   Main   Street,   City,   State",
        "123 Street Name, , City, State",
        "456 Road,City,State,12345"
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 说明文本
                    explanationSection
                    
                    // 测试按钮
                    testButtonSection
                    
                    // 测试结果
                    testResultsSection
                }
                .padding()
            }
            .navigationTitle("地址格式测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var explanationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("功能说明")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("此工具测试地址格式统一化功能，解决以下问题：")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("•")
                        .foregroundColor(.blue)
                    Text("门牌号后多余逗号：\"272, Cedar Street\" → \"272 Cedar Street\"")
                        .font(.caption)
                }
                
                HStack {
                    Text("•")
                        .foregroundColor(.blue)
                    Text("多余空格和逗号：\"City,   State\" → \"City, State\"")
                        .font(.caption)
                }
                
                HStack {
                    Text("•")
                        .foregroundColor(.blue)
                    Text("缺失邮编：自动从反向地理编码获取")
                        .font(.caption)
                }
                
                HStack {
                    Text("•")
                        .foregroundColor(.blue)
                    Text("统一格式：确保所有地址显示一致")
                        .font(.caption)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var testButtonSection: some View {
        VStack(spacing: 12) {
            Button(action: runFormatTest) {
                HStack {
                    Image(systemName: "play.circle.fill")
                    Text("运行格式测试")
                    Spacer()
                    if isRunningTest {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
            .disabled(isRunningTest)
            
            Button(action: clearResults) {
                HStack {
                    Image(systemName: "trash")
                    Text("清除结果")
                    Spacer()
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试结果")
                .font(.headline)
                .foregroundColor(.primary)
            
            if testResults.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                LazyVStack(alignment: .leading, spacing: 12) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        testResultCard(result: result, index: index + 1)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func testResultCard(result: AddressFormatTestResult, index: Int) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("测试 \(index)")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: result.hasChanges ? "checkmark.circle.fill" : "minus.circle.fill")
                    .foregroundColor(result.hasChanges ? .green : .orange)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("原始地址:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(result.originalAddress)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(4)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("标准化后:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(result.standardizedAddress)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(4)
            }
            
            if result.hasChanges {
                Text("✅ 格式已优化")
                    .font(.caption)
                    .foregroundColor(.green)
            } else {
                Text("ℹ️ 格式已正确")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(radius: 1)
    }
    
    // MARK: - 测试方法
    
    private func runFormatTest() {
        isRunningTest = true
        testResults.removeAll()
        
        Task {
            for address in testAddresses {
                let standardized = AddressStandardizer.standardizeAddress(address)
                let result = AddressFormatTestResult(
                    originalAddress: address,
                    standardizedAddress: standardized,
                    hasChanges: address != standardized
                )
                
                await MainActor.run {
                    testResults.append(result)
                }
                
                // 添加小延迟以显示进度
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
            }
            
            await MainActor.run {
                isRunningTest = false
            }
        }
    }
    
    private func clearResults() {
        testResults.removeAll()
    }
}

// MARK: - 数据模型

struct AddressFormatTestResult {
    let originalAddress: String
    let standardizedAddress: String
    let hasChanges: Bool
}

// MARK: - 预览

#Preview("AddressFormatTestView") {
    AddressFormatTestView()
}
