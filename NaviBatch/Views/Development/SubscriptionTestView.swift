import SwiftUI
import StoreKit

/// 订阅功能测试视图
/// 提供完整的订阅测试工具，包括状态切换、购买流程测试、收据验证等
struct SubscriptionTestView: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var storeKitManager = StoreKitManager.shared
    @State private var showSubscriptionView = false
    @State private var testResults: [String] = []
    @State private var isRunningTest = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前状态显示
                    currentStatusSection

                    // 快速操作
                    quickActionsSection

                    // 测试场景
                    testScenariosSection

                    // 测试结果
                    testResultsSection
                }
                .padding()
            }
            .navigationTitle("subscription_function_test".localized)
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showSubscriptionView) {
            UnifiedSubscriptionView(config: SubscriptionViewConfig(
                style: .modern,
                showTitle: true,
                showCloseButton: true,
                showFeatureComparison: true,
                showOneClickPromo: true,
                presentationMode: .sheet
            ))
        }
    }

    // MARK: - 视图组件

    private var currentStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("当前订阅状态")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("订阅等级:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(subscriptionManager.currentTier.rawValue)
                        .fontWeight(.semibold)
                        .foregroundColor(subscriptionManager.currentTier == .free ? .orange : .green)
                }

                HStack {
                    Text("最大地址数:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(subscriptionManager.currentTier.maxStopsPerRoute == Int.max ? "无限" : "\(subscriptionManager.currentTier.maxStopsPerRoute)")
                        .fontWeight(.semibold)
                }

                HStack {
                    Text("试用状态:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(subscriptionManager.isInFreeTrial ? "试用中" : "未试用")
                        .fontWeight(.semibold)
                        .foregroundColor(subscriptionManager.isInFreeTrial ? .blue : .gray)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("快速操作")
                .font(.headline)
                .foregroundColor(.primary)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {

                // 切换到免费版
                Button(action: {
                    subscriptionManager.updateSubscription(to: .free)
                    addTestResult("✅ 已切换到免费版")
                }) {
                    VStack {
                        Image(systemName: "person.circle")
                            .font(.title2)
                        Text("免费版")
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(subscriptionManager.currentTier == .free ? Color.orange.opacity(0.2) : Color(.systemGray5))
                    .cornerRadius(8)
                }

                // 切换到Pro版
                Button(action: {
                    subscriptionManager.updateSubscription(to: .pro)
                    addTestResult("✅ 已切换到Pro版")
                }) {
                    VStack {
                        Image(systemName: "crown.fill")
                            .font(.title2)
                        Text("Pro版")
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(subscriptionManager.currentTier == .pro ? Color.blue.opacity(0.2) : Color(.systemGray5))
                    .cornerRadius(8)
                }

                // 开始试用（已移除，现在依赖App Store Connect配置）
                Button(action: {
                    addTestResult("ℹ️ 试用期现在由App Store Connect自动管理")
                    addTestResult("📱 请通过订阅购买界面开始试用")
                }) {
                    VStack {
                        Image(systemName: "gift.circle")
                            .font(.title2)
                        Text("试用说明")
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(8)
                }

                // 显示订阅界面
                Button(action: {
                    showSubscriptionView = true
                    addTestResult("📱 打开订阅购买界面")
                }) {
                    VStack {
                        Image(systemName: "creditcard.circle")
                            .font(.title2)
                        Text("购买界面")
                            .font(.caption)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.purple.opacity(0.2))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var testScenariosSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试场景")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                // 测试地址限制
                Button(action: {
                    runAddressLimitTest()
                }) {
                    HStack {
                        Image(systemName: "location.circle")
                        Text("测试地址数量限制")
                        Spacer()
                        if isRunningTest {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningTest)

                // 测试收据验证
                Button(action: {
                    runReceiptValidationTest()
                }) {
                    HStack {
                        Image(systemName: "checkmark.shield")
                        Text("测试收据验证")
                        Spacer()
                        if isRunningTest {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningTest)

                // 测试恢复购买
                Button(action: {
                    runRestorePurchaseTest()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise.circle")
                        Text("测试恢复购买")
                        Spacer()
                        if isRunningTest {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningTest)

                // 测试产品加载
                Button(action: {
                    runProductLoadTest()
                }) {
                    HStack {
                        Image(systemName: "cube.box")
                        Text("测试产品加载")
                        Spacer()
                        if isRunningTest {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.purple.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningTest)

                // 测试年费购买
                Button(action: {
                    runYearlyPurchaseTest()
                }) {
                    HStack {
                        Image(systemName: "calendar.circle")
                        Text("测试年费购买")
                        Spacer()
                        if isRunningTest {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningTest)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("测试结果")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Button("清除") {
                    testResults.removeAll()
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if testResults.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                            Text("\(index + 1). \(result)")
                                .font(.caption)
                                .foregroundColor(.primary)
                                .padding(.vertical, 2)
                        }
                    }
                }
                .frame(maxHeight: 200)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 测试方法

    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testResults.append("[\(timestamp)] \(result)")
    }

    private func runAddressLimitTest() {
        isRunningTest = true
        addTestResult("🧪 开始测试地址数量限制...")

        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            let maxStops = subscriptionManager.currentTier.maxStopsPerRoute
            if maxStops == Int.max {
                addTestResult("✅ Pro版：支持无限地址")
            } else {
                addTestResult("✅ 免费版：最多支持\(maxStops)个地址")
            }

            // 模拟检查功能权限
            let canUseAutoGrouping = subscriptionManager.canUseAutoGrouping()
            addTestResult(canUseAutoGrouping ? "✅ 可使用自动分组功能" : "❌ 不可使用自动分组功能")

            isRunningTest = false
        }
    }

    private func runReceiptValidationTest() {
        isRunningTest = true
        addTestResult("🧪 开始测试收据验证...")

        subscriptionManager.validateSubscriptionReceipt()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            addTestResult("✅ 收据验证测试完成")
            isRunningTest = false
        }
    }

    private func runRestorePurchaseTest() {
        isRunningTest = true
        addTestResult("🧪 开始测试恢复购买...")

        subscriptionManager.restorePurchases()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            addTestResult("✅ 恢复购买测试完成")
            isRunningTest = false
        }
    }

    private func runProductLoadTest() {
        isRunningTest = true
        addTestResult("🧪 开始测试产品加载...")

        Task {
            do {
                try await storeKitManager.loadProducts(forceReload: true)

                await MainActor.run {
                    let products = storeKitManager.products
                    addTestResult("✅ 成功加载 \(products.count) 个产品")

                    for product in products {
                        addTestResult("📦 产品: \(product.id) - \(product.displayPrice)")
                        addTestResult("   名称: \(product.displayName)")
                        addTestResult("   类型: \(product.type)")
                    }

                    // 检查年费产品
                    if let yearlyProduct = storeKitManager.product(for: .expert) {
                        addTestResult("✅ 年费产品已找到: \(yearlyProduct.id)")
                    } else {
                        addTestResult("❌ 年费产品未找到")
                    }

                    isRunningTest = false
                }
            } catch {
                await MainActor.run {
                    addTestResult("❌ 产品加载失败: \(error.localizedDescription)")
                    isRunningTest = false
                }
            }
        }
    }

    private func runYearlyPurchaseTest() {
        isRunningTest = true
        addTestResult("🧪 开始测试年费购买...")

        Task {
            do {
                // 先确保产品已加载
                try await storeKitManager.loadProducts(forceReload: true)

                guard let yearlyProduct = storeKitManager.product(for: .expert) else {
                    await MainActor.run {
                        addTestResult("❌ 年费产品未找到")
                        isRunningTest = false
                    }
                    return
                }

                await MainActor.run {
                    addTestResult("📦 找到年费产品: \(yearlyProduct.id)")
                    addTestResult("💰 价格: \(yearlyProduct.displayPrice)")
                    addTestResult("🔄 开始购买流程...")
                }

                let success = try await storeKitManager.purchase(yearlyProduct)

                await MainActor.run {
                    if success {
                        addTestResult("✅ 年费购买成功！")
                    } else {
                        addTestResult("❌ 年费购买失败")
                    }
                    isRunningTest = false
                }
            } catch {
                await MainActor.run {
                    addTestResult("❌ 年费购买错误: \(error.localizedDescription)")
                    isRunningTest = false
                }
            }
        }
    }
}

#Preview("SubscriptionTestView") {
    SubscriptionTestView()
}
