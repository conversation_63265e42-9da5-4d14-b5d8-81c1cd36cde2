import SwiftUI

/// 测试简洁等待视图的开发视图
struct SimpleLoadingTestView: View {
    @State private var showLoading = false
    @State private var showRateLimit = false
    @State private var showProgress = false
    @State private var showMerged = false
    @State private var progress: Double = 0.0
    @StateObject private var rateLimitManager = RateLimitStatusManager()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("简洁等待视图测试")
                    .font(.title)
                    .padding()
                
                VStack(spacing: 20) {
                    <PERSON><PERSON>("显示简洁加载视图") {
                        showLoading.toggle()
                    }
                    .buttonStyle(.borderedProminent)

                    But<PERSON>("显示进度加载视图") {
                        showProgress.toggle()
                        if showProgress {
                            startProgressAnimation()
                        }
                    }
                    .buttonStyle(.borderedProminent)

                    Button("显示合并状态（速率限制+进度）") {
                        showMerged.toggle()
                        if showMerged {
                            startProgressAnimation()
                        }
                    }
                    .buttonStyle(.borderedProminent)

                    Button("显示速率限制等待视图") {
                        rateLimitManager.triggerRateLimitWarning()
                    }
                    .buttonStyle(.bordered)

                    But<PERSON>("隐藏所有视图") {
                        showLoading = false
                        showProgress = false
                        showMerged = false
                        rateLimitManager.dismissWarning()
                    }
                    .buttonStyle(.bordered)
                }
                
                Spacer()
            }
            .navigationTitle("等待视图测试")
            .overlay {
                // 简洁加载视图
                SimpleLoadingView(
                    isVisible: showLoading,
                    message: "正在导入地址，请稍候...",
                    subtitle: "请保持应用开启"
                )
            }
            .overlay {
                // 进度加载视图
                SimpleLoadingView(
                    isVisible: showProgress,
                    message: "validating_addresses".localized,
                    subtitle: nil,
                    progress: progress,
                    showPercentage: true
                )
            }
            .overlay {
                // 合并状态视图（速率限制 + 进度）
                SimpleLoadingView(
                    isVisible: showMerged,
                    message: "validating_addresses".localized,
                    subtitle: nil,
                    progress: progress,
                    showPercentage: true,
                    isRateLimited: true
                )
            }
            .overlay(alignment: .top) {
                // 速率限制等待视图
                RateLimitWarningView(
                    isVisible: rateLimitManager.showWarning,
                    onDismiss: {
                        rateLimitManager.dismissWarning()
                    }
                )
                .padding(.top, 100)
            }
        }
    }

    private func startProgressAnimation() {
        progress = 0.0
        Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            progress += 0.02
            if progress >= 1.0 {
                progress = 1.0
                timer.invalidate()
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    showProgress = false
                    showMerged = false
                    progress = 0.0
                }
            }
        }
    }
}

#Preview("SimpleLoadingTestView") {
    SimpleLoadingTestView()
}
