import SwiftUI
import MapKit

/// 用户位置演示视图 - 专门用于测试苹果地图风格的定位动画
struct UserLocationDemo: View {
    @State private var showUserLocation = true
    @State private var mapPosition = MapCameraPosition.automatic

    var body: some View {
        VStack {
            Text("user_location_marker_test".localized)
                .font(.headline)
                .padding(.top)

            // 地图区域，使用新的定位标记
            ZStack {
                Map(position: $mapPosition) {
                    if showUserLocation, let userLocation = LocationManager.shared.userLocation {
                        Annotation("", coordinate: userLocation) {
                            // 使用自定义定位视图
                            UserLocationPulsingView()
                        }
                    }
                }
                .mapStyle(.standard)
                .frame(height: 500)

                // 右上角控制按钮
                VStack {
                    HStack {
                        Spacer()
                        Button {
                            // 切换用户位置的显示
                            showUserLocation.toggle()
                        } label: {
                            Image(systemName: showUserLocation ? "location.fill" : "location")
                                .padding(8)
                                .background(Color(.systemBackground))
                                .clipShape(Circle())
                                .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                        .padding()
                    }
                    Spacer()
                }
            }

            // 底部控制区域
            VStack(spacing: 15) {
                Text("location_animation_control".localized)
                    .font(.headline)

                Button("start_location_updates".localized) {
                    LocationManager.shared.startUpdatingLocation()
                }
                .buttonStyle(.borderedProminent)

                Button("stop_location_updates".localized) {
                    LocationManager.shared.stopUpdatingLocation()
                }
                .buttonStyle(.bordered)

                // 显示当前位置信息
                if let location = LocationManager.shared.userLocation {
                    Text("current_location_format".localized(with: "\(location.latitude)", "\(location.longitude)"))
                        .font(.caption)
                } else {
                    Text("waiting_for_location".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
        }
        .onAppear {
            // 页面显示时立即开始更新位置
            LocationManager.shared.startUpdatingLocation()
        }
    }
}

/// 脉动动画的位置标记视图 - 模仿苹果地图样式
struct UserLocationPulsingView: View {
    @State private var animationScale: CGFloat = 1.0
    @State private var opacity: Double = 0.6

    var body: some View {
        ZStack {
            // 外部脉动蓝色圆圈
            Circle()
                .fill(Color.blue.opacity(opacity))
                .frame(width: 40, height: 40)
                .scaleEffect(animationScale)
                .opacity(2 - animationScale) // 随着缩放变大而降低透明度

            // 白色圆环
            Circle()
                .stroke(Color.white, lineWidth: 2)
                .frame(width: 22, height: 22)

            // 内部蓝色实心点
            Circle()
                .fill(Color.blue)
                .frame(width: 18, height: 18)
        }
        .onAppear {
            // 使用重复的动画创建脉动效果
            withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: false)) {
                animationScale = 2.0
                opacity = 0.2
            }
        }
    }
}

#Preview("UserLocationDemo") {
    UserLocationDemo()
}
