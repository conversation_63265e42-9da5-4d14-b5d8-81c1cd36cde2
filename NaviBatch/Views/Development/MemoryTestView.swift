import SwiftUI

struct MemoryTestView: View {
    @State private var isMonitoring = false
    @State private var currentMemoryUsage: Double = 0
    @State private var memoryPressure: String = "正常"
    @State private var testResults: [String] = []
    @State private var showingResults = false
    
    private let profiler = MemoryProfiler.shared
    private let timer = Timer.publish(every: 1.0, on: .main, in: .common).autoconnect()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前内存状态
                    memoryStatusCard
                    
                    // 监控控制
                    monitoringControlCard
                    
                    // 测试场景
                    testScenariosCard
                    
                    // 测试结果
                    if !testResults.isEmpty {
                        testResultsCard
                    }
                }
                .padding()
            }
            .navigationTitle("内存测试")
            .navigationBarTitleDisplayMode(.large)
            .onReceive(timer) { _ in
                updateMemoryStatus()
            }
            .sheet(isPresented: $showingResults) {
                MemoryTestResultsView(results: testResults)
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var memoryStatusCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "memorychip")
                    .foregroundColor(.blue)
                Text("当前内存状态")
                    .font(.headline)
                Spacer()
                Circle()
                    .fill(memoryPressureColor)
                    .frame(width: 12, height: 12)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("使用内存:")
                    Spacer()
                    Text("\(String(format: "%.1f", currentMemoryUsage)) MB")
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Text("内存压力:")
                    Spacer()
                    Text(memoryPressure)
                        .fontWeight(.semibold)
                        .foregroundColor(memoryPressureColor)
                }
            }
            .font(.subheadline)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var monitoringControlCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("内存监控")
                    .font(.headline)
            }
            
            HStack {
                Button(action: toggleMonitoring) {
                    HStack {
                        Image(systemName: isMonitoring ? "stop.circle" : "play.circle")
                        Text(isMonitoring ? "停止监控" : "开始监控")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(isMonitoring ? Color.red : Color.green)
                    .cornerRadius(8)
                }
                
                Spacer()
                
                Button("手动快照") {
                    captureManualSnapshot()
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var testScenariosCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "testtube.2")
                    .foregroundColor(.orange)
                Text("测试场景")
                    .font(.headline)
            }
            
            VStack(spacing: 10) {
                testButton(
                    title: "批量地址导入测试",
                    subtitle: "测试导入1000个地址的内存使用",
                    icon: "square.and.arrow.down"
                ) {
                    runBulkImportTest()
                }
                
                testButton(
                    title: "地图渲染测试",
                    subtitle: "测试地图显示大量标记的内存使用",
                    icon: "map"
                ) {
                    runMapRenderingTest()
                }
                
                testButton(
                    title: "路线优化测试",
                    subtitle: "测试优化100个点路线的内存使用",
                    icon: "point.topleft.down.curvedto.point.bottomright.up"
                ) {
                    runRouteOptimizationTest()
                }
                
                testButton(
                    title: "内存压力测试",
                    subtitle: "模拟高内存使用场景",
                    icon: "exclamationmark.triangle"
                ) {
                    runMemoryStressTest()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var testResultsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "doc.text")
                    .foregroundColor(.purple)
                Text("测试结果")
                    .font(.headline)
                Spacer()
                Button("查看详情") {
                    showingResults = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(testResults.suffix(5), id: \.self) { result in
                        Text(result)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(maxHeight: 100)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 辅助方法
    
    private func testButton(title: String, subtitle: String, icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .frame(width: 24)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(Color(.systemBackground))
            .cornerRadius(8)
        }
    }
    
    private var memoryPressureColor: Color {
        switch memoryPressure {
        case "正常":
            return .green
        case "警告":
            return .orange
        case "危险":
            return .red
        default:
            return .gray
        }
    }
    
    // MARK: - 操作方法
    
    private func updateMemoryStatus() {
        let memory = profiler.getCurrentMemoryUsage()
        currentMemoryUsage = memory.used
        
        let usagePercentage = (memory.used / memory.total) * 100
        switch usagePercentage {
        case 0..<70:
            memoryPressure = "正常"
        case 70..<85:
            memoryPressure = "警告"
        default:
            memoryPressure = "危险"
        }
    }
    
    private func toggleMonitoring() {
        if isMonitoring {
            profiler.stopMonitoring()
            addTestResult("内存监控已停止")
        } else {
            profiler.startMonitoring()
            addTestResult("内存监控已开始")
        }
        isMonitoring.toggle()
    }
    
    private func captureManualSnapshot() {
        profiler.captureMemorySnapshot(context: "手动快照")
        addTestResult("已捕获手动内存快照")
    }
    
    private func runBulkImportTest() {
        addTestResult("开始批量地址导入测试...")
        profiler.testBulkAddressImport(addressCount: 1000)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            addTestResult("批量地址导入测试完成")
        }
    }
    
    private func runMapRenderingTest() {
        addTestResult("开始地图渲染测试...")
        profiler.testMapRendering()
        addTestResult("地图渲染测试完成")
    }
    
    private func runRouteOptimizationTest() {
        addTestResult("开始路线优化测试...")
        profiler.testRouteOptimization(pointCount: 100)
        addTestResult("路线优化测试完成")
    }
    
    private func runMemoryStressTest() {
        addTestResult("开始内存压力测试...")
        
        // 创建大量临时对象来模拟内存压力
        DispatchQueue.global(qos: .background).async {
            var largeArrays: [[String]] = []
            
            for i in 0..<100 {
                var largeArray: [String] = []
                for j in 0..<10000 {
                    largeArray.append("测试数据 \(i)-\(j)")
                }
                largeArrays.append(largeArray)
                
                DispatchQueue.main.async {
                    profiler.captureMemorySnapshot(context: "压力测试 - 第\(i+1)轮")
                }
                
                Thread.sleep(forTimeInterval: 0.1)
            }
            
            // 清理数据
            largeArrays.removeAll()
            
            DispatchQueue.main.async {
                addTestResult("内存压力测试完成")
                profiler.captureMemorySnapshot(context: "压力测试清理后")
            }
        }
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testResults.append("[\(timestamp)] \(result)")
    }
}

// MARK: - 测试结果详情视图

struct MemoryTestResultsView: View {
    let results: [String]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(results, id: \.self) { result in
                        Text(result)
                            .font(.system(.caption, design: .monospaced))
                            .padding(.horizontal)
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle("测试结果详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("关闭") { dismiss() })
        }
    }
}

#if DEBUG
struct MemoryTestView_Previews: PreviewProvider {
    static var previews: some View {
        MemoryTestView()
    }
}
#endif
