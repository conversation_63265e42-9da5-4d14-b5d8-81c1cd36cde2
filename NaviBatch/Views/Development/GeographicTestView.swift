import SwiftUI

/// 地理位置限制测试视图
/// 用于诊断AI服务在不同地区的可用性
struct GeographicTestView: View {
    @State private var testResults: [TestResult] = []
    @State private var isRunning = false
    @State private var currentTest = ""
    
    struct TestResult {
        let name: String
        let success: Bool
        let details: String
        let timestamp: Date
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题和说明
                VStack(spacing: 8) {
                    Text("🌍 地理位置诊断")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("检测AI服务在香港地区的可用性")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // 当前测试状态
                if isRunning {
                    VStack(spacing: 12) {
                        ProgressView()
                            .scaleEffect(1.2)
                        
                        Text("正在测试: \(currentTest)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                
                // 测试结果
                if !testResults.isEmpty {
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                                TestResultRow(result: result)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                Spacer()
                
                // 操作按钮
                VStack(spacing: 12) {
                    Button(action: runAllTests) {
                        HStack {
                            Image(systemName: "play.circle.fill")
                            Text("开始诊断")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(isRunning)
                    
                    if !testResults.isEmpty {
                        Button("清除结果") {
                            testResults.removeAll()
                        }
                        .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal)
            }
            .padding()
            .navigationTitle("地理诊断")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 测试执行
    private func runAllTests() {
        isRunning = true
        testResults.removeAll()
        
        Task {
            await runTest("网络连接", test: testNetworkConnection)
            await runTest("OpenRouter API", test: testOpenRouterAPI)
            await runTest("Gemma模型访问", test: testGemmaAccess)
            await runTest("地理位置检测", test: testLocationDetection)
            await runTest("配置服务", test: testConfigService)
            
            await MainActor.run {
                isRunning = false
                currentTest = ""
            }
        }
    }
    
    private func runTest(_ name: String, test: @escaping () async -> (Bool, String)) async {
        await MainActor.run {
            currentTest = name
        }
        
        let (success, details) = await test()
        
        await MainActor.run {
            testResults.append(TestResult(
                name: name,
                success: success,
                details: details,
                timestamp: Date()
            ))
        }
        
        // 测试间隔
        try? await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    // MARK: - 具体测试
    private func testNetworkConnection() async -> (Bool, String) {
        do {
            let url = URL(string: "https://www.google.com")!
            let (_, response) = try await URLSession.shared.data(from: url)
            
            if let httpResponse = response as? HTTPURLResponse {
                return (httpResponse.statusCode == 200, "状态码: \(httpResponse.statusCode)")
            }
            return (false, "无效响应")
        } catch {
            return (false, "错误: \(error.localizedDescription)")
        }
    }
    
    private func testOpenRouterAPI() async -> (Bool, String) {
        do {
            let url = URL(string: "https://openrouter.ai/api/v1/models")!
            let (_, response) = try await URLSession.shared.data(from: url)
            
            if let httpResponse = response as? HTTPURLResponse {
                return (httpResponse.statusCode == 200, "状态码: \(httpResponse.statusCode)")
            }
            return (false, "无效响应")
        } catch {
            return (false, "错误: \(error.localizedDescription)")
        }
    }
    
    private func testGemmaAccess() async -> (Bool, String) {
        do {
            let apiKey = ConfigService.shared.apiKey
            let url = URL(string: "https://openrouter.ai/api/v1/chat/completions")!
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let testBody: [String: Any] = [
                "model": "google/gemma-3-27b-it:free",
                "messages": [["role": "user", "content": "Hi"]],
                "max_tokens": 1
            ]
            
            request.httpBody = try JSONSerialization.data(withJSONObject: testBody)
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 403 {
                    return (false, "地理位置限制 (403)")
                } else if httpResponse.statusCode == 451 {
                    return (false, "地区不可用 (451)")
                } else if httpResponse.statusCode == 429 {
                    return (true, "频率限制 (429) - 服务可用")
                } else if httpResponse.statusCode == 200 {
                    return (true, "成功访问")
                } else {
                    // 尝试解析错误信息
                    if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let error = errorData["error"] as? [String: Any],
                       let message = error["message"] as? String {
                        return (false, "错误: \(message)")
                    }
                    return (false, "状态码: \(httpResponse.statusCode)")
                }
            }
            return (false, "无效响应")
        } catch {
            return (false, "错误: \(error.localizedDescription)")
        }
    }
    
    private func testLocationDetection() async -> (Bool, String) {
        let timeZone = TimeZone.current.identifier
        let locale = Locale.current.identifier
        
        let location = if timeZone.contains("Hong_Kong") || locale.contains("HK") {
            "香港"
        } else if timeZone.contains("Shanghai") || timeZone.contains("Beijing") {
            "中国大陆"
        } else {
            "其他地区"
        }
        
        return (true, "检测位置: \(location) (时区: \(timeZone))")
    }
    
    private func testConfigService() async -> (Bool, String) {
        let apiKey = ConfigService.shared.apiKey
        let models = ConfigService.shared.gemmaModels

        return (!apiKey.isEmpty, "API密钥: \(apiKey.isEmpty ? "未获取" : "已获取"), 模型数: \(models.count)")
    }
}

// MARK: - 测试结果行
struct TestResultRow: View {
    let result: GeographicTestView.TestResult
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(result.success ? .green : .red)
                .font(.title3)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(result.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(result.details)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            Text(DateFormatter.timeFormatter.string(from: result.timestamp))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(radius: 1)
    }
}

extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter
    }()
}

#Preview("GeographicTestView") {
    GeographicTestView()
}
