import SwiftUI
import CoreLocation
import Combine

// 地址验证演示视图
struct AddressValidationDemoView: View {
    @State private var testAddress = "84 Kin, Gen Waverley, VIC 3150"
    @State private var validationResult: AddressValidationResult?
    @State private var isValidating = false
    @State private var showConfirmationSheet = false
    @State private var testDeliveryPoints: [DeliveryPoint] = []

    private let geocodingService = GeocodingService.shared

    // 预设测试地址
    private let testAddresses = [
        "84 Kin, Gen Waverley, VIC 3150",  // 会被修正的地址（严格模式会拒绝）
        "123 Collins Street, Melbourne, VIC 3000",  // 完整正确的地址
        "84 Kim Close, Wheelers Hill, VIC 3150",  // 完整正确的地址
        "456 Fake Street, Nowhere",  // 不存在的地址
        "1 Flinders St",  // 不完整的地址（缺少城市）
        "Glen Waverley Station",  // 地标名称（格式不正确）
        "Collins Street",  // 缺少门牌号
        "123",  // 只有门牌号
    ]

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题说明
                    headerSection

                    // 测试地址选择
                    testAddressSection

                    // 智能地址输入组件演示
                    smartInputSection

                    // 严格地址输入组件演示
                    strictInputSection

                    // 验证结果显示
                    if let result = validationResult {
                        resultSection(result)
                    }

                    // 手动验证按钮
                    manualValidationSection

                    // 测试 DeliveryPoint 验证问题显示
                    testDeliveryPointSection

                    Spacer()
                }
                .padding()
            }
            .navigationTitle("地址验证演示")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showConfirmationSheet) {
            if let result = validationResult {
                AddressConfirmationSheet(validationResult: result) { useOriginal, finalAddress in
                    print("用户选择: \(useOriginal ? "原始地址" : "修正地址")")
                    print("最终地址: \(finalAddress)")
                }
            }
        }
    }

    // MARK: - 视图组件

    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("地址验证解决方案")
                .font(.title2)
                .fontWeight(.bold)

            Text("这个演示展示了如何解决Apple Maps自动修正地址的问题。当系统检测到地址被修正时，会提示用户确认。")
                .font(.body)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }

    private var testAddressSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("预设测试地址")
                .font(.headline)

            Text("选择一个测试地址来体验不同的验证场景：")
                .font(.subheadline)
                .foregroundColor(.secondary)

            LazyVGrid(columns: [GridItem(.flexible())], spacing: 8) {
                ForEach(testAddresses, id: \.self) { address in
                    Button {
                        testAddress = address
                        validationResult = nil
                    } label: {
                        HStack {
                            Text(address)
                                .font(.subheadline)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)

                            Spacer()

                            if testAddress == address {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding()
                        .background(testAddress == address ? Color.blue.opacity(0.1) : Color(.secondarySystemGroupedBackground))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(testAddress == address ? Color.blue : Color.clear, lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    private var smartInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("智能地址输入组件（允许修正）")
                .font(.headline)

            Text("这个组件会自动验证地址并在检测到修正时提示用户：")
                .font(.subheadline)
                .foregroundColor(.secondary)

            SmartAddressInputView(
                address: $testAddress,
                placeholder: "输入地址进行测试"
            ) { result in
                validationResult = result
                print("智能验证完成: \(result)")
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }

    private var strictInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("严格地址输入组件（阻止修正）")
                .font(.headline)

            Text("这个组件会拒绝不完整的地址，阻止Apple Maps自动修正：")
                .font(.subheadline)
                .foregroundColor(.secondary)

            StrictAddressInputView(
                address: $testAddress,
                placeholder: "输入完整准确的地址"
            ) { result in
                validationResult = result
                print("严格验证完成: \(result)")
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }

    private func resultSection(_ result: AddressValidationResult) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("验证结果")
                    .font(.headline)

                Spacer()

                Button("查看详情") {
                    showConfirmationSheet = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }

            // 置信度指示器
            HStack {
                Image(systemName: confidenceIcon(for: result.confidence))
                    .foregroundColor(confidenceColor(for: result.confidence))

                Text(result.confidence.description)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()
            }

            // 修正信息
            if result.isModified {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)

                        Text("地址已被修正")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                    }

                    Text("修正类型: \(result.modificationType.description)")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    if let warningMessage = result.warningMessage {
                        Text(warningMessage)
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }

            // 地址比较
            VStack(alignment: .leading, spacing: 8) {
                Text("原始地址:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(result.originalAddress)
                    .font(.subheadline)
                    .padding(.bottom, 4)

                if !result.geocodedAddress.isEmpty {
                    Text("系统返回地址:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(result.geocodedAddress)
                        .font(.subheadline)
                        .foregroundColor(result.isModified ? .orange : .green)
                }
            }

            // 坐标信息
            if result.coordinate.latitude != 0 && result.coordinate.longitude != 0 {
                Text("坐标: \(String(format: "%.6f", result.coordinate.latitude)), \(String(format: "%.6f", result.coordinate.longitude))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 建议
            if !result.suggestions.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("建议:")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    ForEach(result.suggestions, id: \.self) { suggestion in
                        Text("• \(suggestion)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }

    private var manualValidationSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // 智能验证按钮
                Button {
                    validateAddress(strict: false)
                } label: {
                    HStack {
                        if isValidating {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "checkmark.circle")
                        }

                        Text(isValidating ? "验证中..." : "智能验证")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(isValidating || testAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

                // 严格验证按钮
                Button {
                    validateAddress(strict: true)
                } label: {
                    HStack {
                        if isValidating {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "checkmark.shield")
                        }

                        Text(isValidating ? "验证中..." : "严格验证")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(isValidating || testAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text("• 智能验证：允许Apple Maps修正地址，然后提示用户确认")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("• 严格验证：拒绝不完整地址，阻止自动修正")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 测试严格门牌号验证按钮
            Button {
                testStrictStreetNumberValidation()
            } label: {
                HStack {
                    Image(systemName: "exclamationmark.octagon")
                    Text("测试严格门牌号验证")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(8)
            }

            // 测试连号地址验证按钮
            Button {
                testRangeAddressValidation()
            } label: {
                HStack {
                    Image(systemName: "number.circle")
                    Text("测试连号地址验证")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.purple)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
    }

    private var testDeliveryPointSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("DeliveryPoint 验证问题显示测试")
                .font(.headline)

            Text("这里展示了在 bottom sheet 中如何显示地址验证问题：")
                .font(.subheadline)
                .foregroundColor(.secondary)

            Button("创建测试数据") {
                createTestDeliveryPoints()
            }
            .padding()
            .background(Color.green)
            .foregroundColor(.white)
            .cornerRadius(8)

            if !testDeliveryPoints.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("测试地址列表（模拟 RoutePointRow 显示）:")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    ForEach(Array(testDeliveryPoints.enumerated()), id: \.element.id) { index, point in
                        TestRoutePointRow(point: point, index: index)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }

    // MARK: - 私有方法

    private func validateAddress(strict: Bool = false) {
        let trimmedAddress = testAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedAddress.isEmpty else { return }

        isValidating = true
        validationResult = nil

        let validationPublisher = strict ?
            geocodingService.strictValidateAddress(trimmedAddress) :
            geocodingService.validateAddress(trimmedAddress)

        validationPublisher
            .receive(on: DispatchQueue.main)
            .sink { result in
                isValidating = false
                validationResult = result

                // 如果是智能验证且地址被修正且置信度较低，自动显示确认界面
                if !strict && result.isModified && (result.confidence == .low || result.confidence == .veryLow) {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        showConfirmationSheet = true
                    }
                }
            }
            .store(in: &cancellables)
    }

    @State private var cancellables = Set<AnyCancellable>()

    private func testStrictStreetNumberValidation() {
        Task {
            await ReverseGeocodingValidationService.shared.testStrictStreetNumberValidation()
        }
    }

    private func testRangeAddressValidation() {
        print("🔢 开始测试连号地址验证")

        // 测试连号地址的各种情况，包括复合门牌号
        let testCases = [
            ("1-5", "2", "连号内的2号"),
            ("1-5", "3", "连号内的3号"),
            ("1-5", "4", "连号内的4号"),
            ("1-5", "7", "连号外的7号 (距离2号)"),
            ("1-5", "8", "连号外的8号 (距离3号)"),
            ("10", "8-12", "10号在8-12范围内"),
            ("15", "8-12", "15号距离12号3个"),
            ("6", "8-12", "6号距离8号2个"),

            // 复合门牌号测试（您提到的实际案例）
            ("M104/25", "22-32", "M104/25号在22-32范围内"),
            ("A5/30", "28-35", "A5/30号在28-35范围内"),
            ("B12/40", "35-38", "B12/40号距离38号2个"),
            ("C3/50", "35-45", "C3/50号距离45号5个"),
        ]

        let service = ReverseGeocodingValidationService.shared

        for (original, reverse, description) in testCases {
            let difference = service.getStreetNumberDifference(original: original, reverse: reverse)
            let status = difference <= 2 ? "✅ 通过" : "❌ 失效"
            print("  🔍 \(description): '\(original)' vs '\(reverse)' = 差异\(difference)号 \(status)")
        }

        print("🎯 连号地址验证测试完成")

        // 特别测试您提到的案例
        print("")
        print("🔍 特别测试您的案例:")
        let yourCase = service.getStreetNumberDifference(original: "M104/25", reverse: "22-32")
        let yourStatus = yourCase <= 2 ? "✅ 通过" : "❌ 失效"
        print("  📍 M104/25 vs 22-32 = 差异\(yourCase)号 \(yourStatus)")

        // 测试提取函数
        if let extracted = service.extractMainStreetNumber(from: "M104/25") {
            print("  🔧 M104/25 提取主要号码: \(extracted)")
        }

        let inRange = service.isNumberInRange("M104/25", range: "22-32")
        print("  📊 M104/25 是否在 22-32 范围内: \(inRange ? "是" : "否")")
    }

    private func confidenceIcon(for confidence: AddressConfidence) -> String {
        switch confidence {
        case .high:
            return "checkmark.circle.fill"
        case .medium:
            return "exclamationmark.triangle.fill"
        case .low:
            return "xmark.circle.fill"
        case .veryLow:
            return "questionmark.circle.fill"
        }
    }

    private func confidenceColor(for confidence: AddressConfidence) -> Color {
        switch confidence {
        case .high:
            return .green
        case .medium:
            return .orange
        case .low:
            return .red
        case .veryLow:
            return .gray
        }
    }

    private func createTestDeliveryPoints() {
        testDeliveryPoints = [
            // 正常地址
            createTestPoint(
                address: "123 Collins Street, Melbourne, VIC 3000",
                latitude: -37.8136, longitude: 144.9631,
                validationScore: 95.0,
                issues: nil,
                warning: nil
            ),

            // 地理编码警告
            createTestPoint(
                address: "84 Kin, Gen Waverley, VIC 3150",
                latitude: -37.8796, longitude: 145.1656,
                validationScore: 65.0,
                issues: "地址部分匹配，可能不准确",
                warning: "partialMatch"
            ),

            // 低验证分数
            createTestPoint(
                address: "456 Fake Street, Nowhere",
                latitude: 0, longitude: 0,
                validationScore: 25.0,
                issues: "地址验证分数较低 (25分)",
                warning: "lowAccuracy"
            ),

            // 门牌号差异过大的问题（模拟55 vs 5的情况）- 新的严格验证直接失效
            createTestPoint(
                address: "55 Batten Street, Glen Waverley, VIC 3150, Australia",
                latitude: -37.8770, longitude: 145.1610,
                validationScore: 0.0,  // 门牌号差异超过±2，直接失效
                issues: "地址差异很大 (差50号): '55' vs '5' - 高风险，建议修正地址",
                warning: "lowAccuracy"
            ),

            // 坐标缺失
            createTestPoint(
                address: "Collins Street",
                latitude: 0, longitude: 0,
                validationScore: 0.0,
                issues: "地址坐标缺失",
                warning: "coordinateMissing"
            ),

            // 距离较远
            createTestPoint(
                address: "1 Main Street, Sydney, NSW 2000",
                latitude: -33.8688, longitude: 151.2093,
                validationScore: 85.0,
                issues: "地址距离较远，请确认",
                warning: nil,
                locationStatus: "warning"
            )
        ]
    }

    private func createTestPoint(
        address: String,
        latitude: Double,
        longitude: Double,
        validationScore: Double,
        issues: String?,
        warning: String?,
        locationStatus: String = "valid"
    ) -> DeliveryPoint {
        let point = DeliveryPoint(
            sort_number: testDeliveryPoints.count + 1,
            streetName: address,
            latitude: latitude,
            longitude: longitude
        )

        point.addressValidationScore = validationScore
        if let issues = issues {
            point.addressValidationIssues = issues
        }
        if let warning = warning {
            point.geocodingWarning = warning
        }
        point.locationValidationStatus = locationStatus

        return point
    }
}

// 测试用的 RoutePointRow 组件
struct TestRoutePointRow: View {
    let point: DeliveryPoint
    let index: Int

    var body: some View {
        HStack(spacing: 12) {
            // 编号圆圈
            ZStack {
                Circle()
                    .fill(point.hasAnyValidationIssues ? .white : .blue)
                    .frame(width: 30, height: 30)
                    .overlay(
                        Circle()
                            .stroke(point.hasAnyValidationIssues ? .red : .clear, lineWidth: 2)
                    )

                if point.hasAnyValidationIssues {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 12))
                        .foregroundColor(.red)
                } else {
                    Text("\(index + 1)")
                        .font(.headline)
                        .foregroundColor(.white)
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                // 地址
                HStack {
                    Text(point.primaryAddress)
                        .font(.system(size: 16, weight: .medium))
                        .lineLimit(1)

                    if point.hasAnyValidationIssues {
                        Text("请检查")
                            .font(.caption)
                            .foregroundColor(.red)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 2)
                            .background(Color.red.opacity(0.1))
                            .cornerRadius(4)
                    }
                }

                // 验证问题描述
                if let issueDescription = point.validationIssueDescription {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.caption)

                        Text(issueDescription)
                            .font(.caption)
                            .foregroundColor(.orange)
                            .lineLimit(2)
                    }
                    .padding(.vertical, 2)
                    .padding(.horizontal, 4)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(4)
                }
            }

            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray4), lineWidth: 0.5)
        )
    }
}

// MARK: - 预览
struct AddressValidationDemoView_Previews: PreviewProvider {
    static var previews: some View {
        AddressValidationDemoView()
    }
}
