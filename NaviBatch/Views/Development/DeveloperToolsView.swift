import SwiftUI
import SwiftData
import Foundation
import os.log
import CoreLocation

/// 开发工具视图，提供各种开发和测试工具
struct DeveloperToolsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var localizationManager: LocalizationManager

    enum DeveloperTool: String, CaseIterable, Identifiable {
        case localizationTools = "本地化工具"
        case systemLanguageTest = "系统语言测试"
        case addressValidation = "地址验证演示"
        case addressTranslation = "地址翻译测试"
        case addressFormatTest = "地址格式测试"
        case globalAddressTest = "全球地址测试"
        case hongKongAddressTest = "香港地址测试"
        case smartExpansionTest = "智能简称扩展测试"

        case appUpdateTest = "版本更新测试"
        case coordinateDebug = "坐标调试工具"
        case locationDebug = "位置调试工具"
        case locationTest = "位置快速测试"
        case locationFixTest = "位置修复验证"
        case versionTest = "版本信息测试"
        case batchFixAddresses = "批量修复地址"
        case addressCleanup = "地址清理工具"
        case addressDatabaseRepair = "地址数据库修复工具"
        case userAddressDatabase = "用户地址数据库管理"
        case addressStateFixTest = "州名修复测试"
        case addressEditDebug = "地址编辑功能调试"
        case localeSearchTest = "语言环境搜索测试"
        case videoFrameDebug = "视频帧调试查看器"
        case memoryTest = "内存测试"
        case proToggle = "Pro版本切换"
        case trialReset = "重置试用期状态"
        case subscriptionTest = "订阅功能测试"
        case storeKitDiagnostic = "StoreKit诊断"
        case trialPromotionShow = "显示试用推广页面"
        case trialPromotionReset = "重置推广状态"
        case clearDatabase = "清除数据库"

        var id: String { self.rawValue }

        var iconName: String {
            switch self {
            case .localizationTools: return "globe"
            case .systemLanguageTest: return "globe.badge.chevron.backward"
            case .addressValidation: return "checkmark.shield"
            case .addressTranslation: return "translate"
            case .addressFormatTest: return "text.alignleft"
            case .globalAddressTest: return "globe.americas.fill"
            case .hongKongAddressTest: return "building.2.crop.circle"
            case .smartExpansionTest: return "text.magnifyingglass"

            case .appUpdateTest: return "app.badge"
            case .coordinateDebug: return "location.circle"
            case .locationDebug: return "location.fill.viewfinder"
            case .locationTest: return "location.magnifyingglass"
            case .locationFixTest: return "checkmark.shield"
            case .versionTest: return "info.circle.fill"
            case .batchFixAddresses: return "wrench.and.screwdriver"
            case .addressCleanup: return "trash.slash"
            case .addressDatabaseRepair: return "stethoscope.circle"
            case .userAddressDatabase: return "house.and.flag"
            case .addressStateFixTest: return "location.badge.plus"
            case .addressEditDebug: return "pencil.and.outline"
            case .localeSearchTest: return "globe.americas"
            case .videoFrameDebug: return "photo.stack"
            case .memoryTest: return "memorychip"
            case .proToggle: return "crown.circle"
            case .trialReset: return "arrow.clockwise.circle"
            case .subscriptionTest: return "creditcard.circle"
            case .storeKitDiagnostic: return "stethoscope"
            case .trialPromotionShow: return "megaphone"
            case .trialPromotionReset: return "megaphone.fill"
            case .clearDatabase: return "trash"
            }
        }

        var description: String {
            switch self {
            case .localizationTools: return "本地化测试、验证和更新工具"
            case .systemLanguageTest: return "测试系统语言检测和显示功能"
            case .addressValidation: return "演示地址验证和修正检测功能"
            case .addressTranslation: return "测试中文地址翻译为英文功能，提高搜索成功率"
            case .addressFormatTest: return "测试地址格式统一化功能，确保显示一致性"
            case .globalAddressTest: return "测试175个国家的全球地址处理能力，支持任何语言输入"
            case .hongKongAddressTest: return "测试香港地址解析和验证功能"
            case .smartExpansionTest: return "测试智能简称扩展功能，查看候选地址生成"


            case .appUpdateTest: return "测试版本更新提示功能，模拟不同更新场景"
            case .coordinateDebug: return "调试地址坐标和地理编码问题"
            case .locationDebug: return "诊断和修复位置定位问题"
            case .locationTest: return "快速测试位置功能和状态"
            case .locationFixTest: return "验证位置硬编码问题修复效果"
            case .versionTest: return "检查应用版本号显示是否正确"
            case .batchFixAddresses: return "标准化地址数据，确保与Apple Maps格式一致"
            case .addressCleanup: return "清理地址字段中的管道符号和元数据信息"
            case .addressDatabaseRepair: return "检测和修复数据库中缺少州缩写的美国地址"
            case .userAddressDatabase: return "管理用户地址数据库，清理元数据，查看统计信息"
            case .addressStateFixTest: return "测试地址州名自动添加功能，验证AddressStateFixService"
            case .addressEditDebug: return "调试地址编辑功能，测试地址搜索和识别问题"
            case .localeSearchTest: return "测试语言环境搜索问题，诊断MKLocalSearchCompleter超时"
            case .videoFrameDebug: return "查看视频处理保留的帧，验证AI分析准确性"
            case .memoryTest: return "监控和测试应用内存使用情况"
            case .proToggle: return "在免费版和Pro版之间切换，方便测试功能"
            case .trialReset: return "重置沙盒环境的试用期状态，用于测试试用期功能"
            case .subscriptionTest: return "测试订阅功能和购买流程"
            case .storeKitDiagnostic: return "诊断订阅恢复问题和StoreKit配置"
            case .trialPromotionShow: return "手动显示60天试用推广页面，测试推广效果"
            case .trialPromotionReset: return "重置推广状态，清除显示记录和拒绝标记"
            case .clearDatabase: return "清除所有数据，包括路线、地址和分组"
            }
        }
    }

    @State private var showingClearDatabaseConfirmation = false
    @State private var selectedTool: DeveloperTool? = nil
    @State private var locationServicesStatus = "检查中..."

    var body: some View {
        NavigationView {
            contentView
                .navigationTitle("developer_tools".localized)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("close".localized) {
                            dismiss()
                        }
                    }
                }
                .sheet(item: $selectedTool) { tool in
                    toolView(for: tool)
                }
                .alert(isPresented: $showingClearDatabaseConfirmation) {
                    Alert(
                        title: Text("clear_database".localized),
                        message: Text("clear_database_confirmation".localized),
                        primaryButton: .destructive(Text("confirm_clear".localized)) {
                            clearDatabase()
                        },
                        secondaryButton: .cancel(Text("cancel".localized))
                    )
                }
                .onAppear {
                    checkLocationServicesStatus()
                }
        }
    }

    private var contentView: some View {
        List {
            Section(header: Text("开发工具")) {
                toolsSection
            }

            Section(header: Text("应用信息")) {
                infoSection
            }
        }
    }

    private var toolsSection: some View {
        ForEach(DeveloperTool.allCases) { tool in
            Button(action: {
                if tool == .clearDatabase {
                    showingClearDatabaseConfirmation = true
                } else if tool == .proToggle {
                    // 直接处理 Pro 切换，不需要打开新视图
                    toggleProStatus()
                } else if tool == .trialReset {
                    // 直接处理试用期重置，不需要打开新视图
                    resetTrialStatus()
                } else if tool == .trialPromotionShow {
                    // 显示试用推广页面
                    showTrialPromotion()
                } else if tool == .trialPromotionReset {
                    // 重置推广状态
                    resetPromotionStatus()
                } else {
                    selectedTool = tool
                }
            }) {
                HStack {
                    Image(systemName: tool.iconName)
                        .foregroundColor(.blue)
                        .frame(width: 30)

                    VStack(alignment: .leading) {
                        Text(tool.rawValue)
                            .font(.headline)

                        Text(tool.description)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                .padding(.vertical, 8)
            }
        }
    }

    private var infoSection: some View {
        VStack {
            HStack {
                Text("版本")
                Spacer()
                Text(getVersionInfo())
                    .foregroundColor(.gray)
            }

            HStack {
                Text("当前语言")
                Spacer()
                // 使用 EnvironmentObject 的正确访问方式
                Text(getCurrentLanguageDisplayName())
                    .foregroundColor(.gray)
            }
        }
    }

    // 根据选择的工具返回相应的视图
    @ViewBuilder
    private func toolView(for tool: DeveloperTool) -> some View {
        switch tool {
        case .localizationTools:
            LocalizationToolsView()
                .environmentObject(localizationManager)
        case .systemLanguageTest:
            SystemLanguageTestView()
        case .addressValidation:
            AddressValidationDemoView()
        case .addressTranslation:
            AddressTranslationTestView()
        case .addressFormatTest:
            AddressFormatTestView()
        case .globalAddressTest:
            GlobalAddressTestView()
        case .hongKongAddressTest:
            HongKongAddressTestView()
        case .smartExpansionTest:
            SmartExpansionTestView()

        case .appUpdateTest:
            AppUpdateTestView()
        case .coordinateDebug:
            CoordinateDebugToolView()
        case .locationDebug:
            LocationDebugView()
        case .locationTest:
            LocationTestView()
        case .locationFixTest:
            LocationFixTestView()
        case .versionTest:
            VersionTestView()
        case .batchFixAddresses:
            AddressDataFixerView()
        case .addressCleanup:
            AddressCleanupView()
        case .addressDatabaseRepair:
            AddressDatabaseRepairView()
        case .userAddressDatabase:
            UserAddressDatabaseManagerView()
        case .addressStateFixTest:
            AddressStateFixTestView()
        case .addressEditDebug:
            AddressEditDebugView()
        case .localeSearchTest:
            LocaleSearchTestView()
        case .videoFrameDebug:
            VideoFrameDebugView()
        case .memoryTest:
            MemoryTestView()
        case .proToggle:
            EmptyView() // 不应该到达这里，因为Pro切换是直接处理的
        case .trialReset:
            EmptyView() // 不应该到达这里，因为试用期重置是直接处理的
        case .subscriptionTest:
            SubscriptionTestView()
        case .storeKitDiagnostic:
            StoreKitDiagnosticView()
        case .trialPromotionShow:
            EmptyView() // 不应该到达这里，因为试用推广显示是直接处理的
        case .trialPromotionReset:
            EmptyView() // 不应该到达这里，因为推广状态重置是直接处理的
        case .clearDatabase:
            EmptyView() // 不应该到达这里，因为清除数据库是通过Alert处理的
        }
    }

    // 获取应用版本信息
    private func getVersionInfo() -> String {
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
        return String(format: "version_info".localized, version, build)
    }

    // 清除数据库
    @MainActor
    private func clearDatabase() {
        // 获取持久化容器
        let container = getPersistentContainer()
        let modelContext = container.mainContext

        do {
            // 1. 删除所有配送点
            let deliveryPointDescriptor = FetchDescriptor<DeliveryPoint>()
            let deliveryPoints = try modelContext.fetch(deliveryPointDescriptor)
            for point in deliveryPoints {
                modelContext.delete(point)
            }
            print("已删除 \(deliveryPoints.count) 个配送点")

            // 2. 删除所有配送分组
            let groupDescriptor = FetchDescriptor<DeliveryGroup>()
            let groups = try modelContext.fetch(groupDescriptor)
            for group in groups {
                modelContext.delete(group)
            }
            print("已删除 \(groups.count) 个配送分组")

            // 3. 删除所有路线
            let routeDescriptor = FetchDescriptor<Route>()
            let routes = try modelContext.fetch(routeDescriptor)
            for route in routes {
                modelContext.delete(route)
            }
            print("已删除 \(routes.count) 条路线")

            // 4. 删除所有保存的地址
            let addressDescriptor = FetchDescriptor<SavedAddress>()
            let addresses = try modelContext.fetch(addressDescriptor)
            for address in addresses {
                modelContext.delete(address)
            }
            print("已删除 \(addresses.count) 个保存的地址")

            // 保存更改
            try modelContext.save()

            // 显示成功提示
            NotificationCenter.default.post(
                name: Notification.Name("ShowToast"),
                object: "数据库已清空，请重启应用"
            )
        } catch {
            print("清除数据库失败: \(error.localizedDescription)")

            // 显示错误提示
            NotificationCenter.default.post(
                name: Notification.Name("ShowToast"),
                object: "清除数据库失败: \(error.localizedDescription)"
            )
        }
    }

    // 获取当前语言显示名称
    private func getCurrentLanguageDisplayName() -> String {
        // 安全地访问 EnvironmentObject
        return localizationManager.selectedLanguage.displayName
    }

    // Pro 状态切换
    private func toggleProStatus() {
        print("[DEBUG] DeveloperToolsView - Pro状态切换按钮被点击")

        let subscriptionManager = SubscriptionManager.shared
        let currentTier = subscriptionManager.currentTier

        // 在免费版和Pro版之间切换
        let newTier: SubscriptionTier = currentTier == .free ? .pro : .free
        subscriptionManager.updateSubscription(to: newTier)

        print("[DEBUG] DeveloperToolsView - 订阅状态已从 \(currentTier.rawValue) 切换到 \(newTier.rawValue)")

        // 移除切换成功的Toast提示 - 不再显示调试信息
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: NSNotification.Name("SubscriptionStatusChanged"),
                object: nil
            )
        }
    }

    // 重置试用期状态
    private func resetTrialStatus() {
        print("[DEBUG] DeveloperToolsView - 重置试用期状态按钮被点击")

        Task {
            let subscriptionManager = SubscriptionManager.shared

            // 重置订阅状态为免费版
            await subscriptionManager.resetSubscriptionForTesting()

            // 显示成功提示
            await MainActor.run {
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: "试用期状态已重置，现在可以重新开始试用"
                )

                // 通知订阅状态变更
                NotificationCenter.default.post(
                    name: NSNotification.Name("SubscriptionStatusChanged"),
                    object: nil
                )
            }

            print("[DEBUG] DeveloperToolsView - 试用期状态重置完成")
        }
    }

    // 显示试用推广页面
    private func showTrialPromotion() {
        print("[DEBUG] DeveloperToolsView - 手动显示试用推广页面")

        // 检查当前订阅状态
        let subscriptionManager = SubscriptionManager.shared
        print("[DEBUG] 当前订阅层级: \(subscriptionManager.currentTier)")
        print("[DEBUG] 是否在试用期: \(subscriptionManager.isInFreeTrial)")

        // 强制显示，绕过订阅检查
        TrialPromotionManager.shared.shouldShowPromotion = true
        print("[DEBUG] 已设置shouldShowPromotion = true")

        // 显示成功提示
        NotificationCenter.default.post(
            name: NSNotification.Name("ShowToast"),
            object: "试用推广页面已显示"
        )
    }

    // 重置推广状态
    private func resetPromotionStatus() {
        print("[DEBUG] DeveloperToolsView - 重置推广状态")
        TrialPromotionManager.shared.resetPromotionState()

        // 显示成功提示
        NotificationCenter.default.post(
            name: NSNotification.Name("ShowToast"),
            object: "推广状态已重置"
        )
    }

    // 检查位置服务状态 - 在后台队列执行以避免主线程阻塞
    private func checkLocationServicesStatus() {
        Task {
            let isEnabled = await Task.detached {
                CLLocationManager.locationServicesEnabled()
            }.value

            await MainActor.run {
                locationServicesStatus = isEnabled ? "已启用" : "已禁用"
            }
        }
    }
}

// 坐标调试工具视图
struct CoordinateDebugToolView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationManager = LocationManager.shared
    @State private var diagnosticResults: [String] = []
    @State private var isRunningDiagnostic = false
    @State private var locationServicesStatus = "检查中..."

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前状态显示
                    currentStatusSection

                    // 诊断按钮
                    diagnosticButtonsSection

                    // 诊断结果
                    diagnosticResultsSection
                }
                .padding()
            }
            .navigationTitle("坐标调试工具")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                checkLocationServicesStatus()
            }
        }
    }

    // MARK: - 视图组件

    private var currentStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("当前状态")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("位置权限:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(authorizationStatusText)
                        .fontWeight(.semibold)
                        .foregroundColor(authorizationStatusColor)
                }

                HStack {
                    Text("当前位置:")
                        .foregroundColor(.secondary)
                    Spacer()
                    if let location = locationManager.userLocation {
                        Text(String(format: "%.6f, %.6f", location.latitude, location.longitude))
                            .font(.caption)
                            .fontWeight(.semibold)
                    } else {
                        Text("未获取")
                            .foregroundColor(.orange)
                    }
                }

                HStack {
                    Text("强制默认位置:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(forceUseDefaultLocationStatus)
                        .fontWeight(.semibold)
                        .foregroundColor(forceUseDefaultLocationColor)
                }

                HStack {
                    Text("当前区域:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(locationManager.getCurrentRegion())
                        .fontWeight(.semibold)
                }

                HStack {
                    Text("位置服务:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(locationServicesStatus)
                        .fontWeight(.semibold)
                        .foregroundColor(locationServicesStatus == "已启用" ? .green : .red)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var diagnosticButtonsSection: some View {
        VStack(spacing: 12) {
            Text("诊断工具")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                Button(action: runLocationDiagnostic) {
                    HStack {
                        Image(systemName: "location.magnifyingglass")
                        Text("运行位置诊断")
                        Spacer()
                        if isRunningDiagnostic {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningDiagnostic)

                Button(action: resetLocationSettings) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("重置区域设置")
                        Spacer()
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }

                Button(action: requestLocationPermission) {
                    HStack {
                        Image(systemName: "location.circle")
                        Text("重新请求位置权限")
                        Spacer()
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                }

                Button(action: clearResults) {
                    HStack {
                        Image(systemName: "trash")
                        Text("清除结果")
                        Spacer()
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var diagnosticResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("诊断结果")
                .font(.headline)
                .foregroundColor(.primary)

            if diagnosticResults.isEmpty {
                Text("暂无诊断结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(diagnosticResults.enumerated()), id: \.offset) { index, result in
                            Text("\(index + 1). \(result)")
                                .font(.caption)
                                .foregroundColor(.primary)
                                .padding(.vertical, 2)
                        }
                    }
                }
                .frame(maxHeight: 300)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 计算属性

    private var authorizationStatusText: String {
        switch locationManager.authorizationStatus {
        case .notDetermined: return "未确定"
        case .denied: return "已拒绝"
        case .restricted: return "受限制"
        case .authorizedWhenInUse: return "使用时允许"
        case .authorizedAlways: return "始终允许"
        @unknown default: return "未知"
        }
    }

    private var authorizationStatusColor: Color {
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways: return .green
        case .denied, .restricted: return .red
        case .notDetermined: return .orange
        @unknown default: return .gray
        }
    }

    private var forceUseDefaultLocationStatus: String {
        UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation") ? "是" : "否"
    }

    private var forceUseDefaultLocationColor: Color {
        UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation") ? .red : .green
    }

    // 检查位置服务状态 - 在后台队列执行以避免主线程阻塞
    private func checkLocationServicesStatus() {
        Task {
            let isEnabled = await Task.detached {
                CLLocationManager.locationServicesEnabled()
            }.value

            await MainActor.run {
                locationServicesStatus = isEnabled ? "已启用" : "已禁用"
            }
        }
    }

    // MARK: - 诊断方法

    private func addResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        diagnosticResults.append("[\(timestamp)] \(result)")
    }

    private func runLocationDiagnostic() {
        isRunningDiagnostic = true
        diagnosticResults.removeAll()
        addResult("🔍 开始运行位置诊断...")

        // 首先运行LocationManager的调试方法
        locationManager.debugLocationSettings()

        Task {
            await performLocationDiagnostic()

            await MainActor.run {
                addResult("✅ 位置诊断完成")
                isRunningDiagnostic = false
            }
        }
    }

    private func resetLocationSettings() {
        addResult("🔄 重置区域设置...")

        // 调用LocationManager的重置方法
        locationManager.resetRegionSettings()

        // 重置所有位置相关的UserDefaults
        UserDefaults.standard.set(false, forKey: "ForceUseDefaultLocation")
        UserDefaults.standard.set(false, forKey: "IsRunningInSimulator")

        addResult("✅ 已重置区域设置")
        addResult("✅ 已重置ForceUseDefaultLocation为false")
        addResult("✅ 已重置IsRunningInSimulator为false")

        // 重新启动位置更新
        locationManager.startUpdatingLocation()
        addResult("🔄 已重新启动位置更新")

        addResult("💡 请测试位置功能是否恢复正常")
    }

    private func requestLocationPermission() {
        addResult("🔐 重新请求位置权限...")
        locationManager.requestLocationPermission()
        addResult("📱 已发送权限请求，请在弹窗中选择允许")
    }

    private func clearResults() {
        diagnosticResults.removeAll()
    }

    private func performLocationDiagnostic() async {
        await MainActor.run {
            // 1. 检查位置服务状态 - 在后台队列执行以避免主线程阻塞
            addResult("📍 检查位置服务状态...")
        }

        // 在后台队列检查位置服务状态
        let locationServicesEnabled = await Task.detached {
            CLLocationManager.locationServicesEnabled()
        }.value

        await MainActor.run {
            addResult("  位置服务: \(locationServicesEnabled ? "已启用" : "已禁用")")

            // 2. 检查权限状态
            addResult("🔐 检查权限状态...")
            addResult("  当前权限: \(authorizationStatusText)")

            // 3. 检查强制默认位置标志
            addResult("⚠️ 检查强制默认位置标志...")
            let forceDefault = UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation")
            addResult("  ForceUseDefaultLocation: \(forceDefault)")

            if forceDefault {
                addResult("❌ 发现问题：强制使用默认位置标志被设置为true")
                addResult("💡 这会导致应用忽略真实位置更新")
            }

            // 4. 检查模拟器标志
            addResult("📱 检查运行环境...")
            let isSimulator = UserDefaults.standard.bool(forKey: "IsRunningInSimulator")
            addResult("  IsRunningInSimulator: \(isSimulator)")

            #if targetEnvironment(simulator)
            addResult("  实际环境: 模拟器")
            #else
            addResult("  实际环境: 真机")
            if isSimulator {
                addResult("❌ 发现问题：真机上模拟器标志仍为true")
            }
            #endif

            // 5. 检查当前位置
            addResult("📍 检查当前位置...")
            if let location = locationManager.userLocation {
                addResult("  当前坐标: \(String(format: "%.6f, %.6f", location.latitude, location.longitude))")

                // 检查是否是默认坐标
                let defaultCoord = locationManager.getDefaultCoordinateForCurrentRegion()
                let isDefaultLocation = abs(location.latitude - defaultCoord.latitude) < 0.001 &&
                                      abs(location.longitude - defaultCoord.longitude) < 0.001

                if isDefaultLocation {
                    addResult("⚠️ 当前使用的是默认坐标")
                } else {
                    addResult("✅ 当前使用的是真实坐标")
                }
            } else {
                addResult("❌ 未获取到位置信息")
            }

            // 6. 检查区域设置
            addResult("🌍 检查区域设置...")
            let currentRegion = locationManager.getCurrentRegion()
            addResult("  当前区域: \(currentRegion)")

            // 7. 提供修复建议
            addResult("💡 修复建议...")
            if forceDefault {
                addResult("  1. 重置位置设置以清除强制默认位置标志")
            }
            if locationManager.authorizationStatus == .denied {
                addResult("  2. 在系统设置中重新授权位置权限")
            }
            if !locationServicesEnabled {
                addResult("  3. 在系统设置中启用位置服务")
            }
        }
    }
}



// 智能简称扩展测试视图
struct SmartExpansionTestView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var testResults: [String] = []
    @State private var isRunning = false

    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                // 说明文本
                VStack(alignment: .leading, spacing: 8) {
                    Text("智能简称扩展测试")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("此工具测试智能简称扩展功能，展示如何将包含简称的地址转换为多个候选地址以提高地理编码成功率。")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 测试按钮
                Button(action: runTest) {
                    HStack {
                        if isRunning {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "play.circle.fill")
                        }
                        Text(isRunning ? "测试中..." : "运行测试")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(isRunning)

                // 测试结果
                if !testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("测试结果")
                            .font(.headline)

                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(testResults, id: \.self) { result in
                                    Text(result)
                                        .font(.system(.caption, design: .monospaced))
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 2)
                                }
                            }
                        }
                        .frame(maxHeight: 300)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }

                Spacer()
            }
            .padding()
            .navigationTitle("智能简称扩展测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func runTest() {
        isRunning = true
        testResults.removeAll()

        // 在后台线程运行测试
        DispatchQueue.global(qos: .userInitiated).async {
            // 捕获日志输出
            let testAddresses = [
                "123 Smith La, Melbourne VIC 3000",
                "45 Main St, Sydney NSW 2000",
                "2/21 Park Rd, Brisbane QLD 4000",
                "Unit 5/67 Queen Ave, Perth WA 6000",
                "789 Collins Ct, Adelaide SA 5000",
                "12 Ocean Pl, Darwin NT 0800"
            ]

            var results: [String] = []
            results.append("🧪 开始智能简称扩展测试")
            results.append("")

            for address in testAddresses {
                results.append("📍 测试地址: '\(address)'")
                let candidates = AddressStandardizer.generateGeocodingCandidates(address)
                results.append("🎯 生成 \(candidates.count) 个候选地址:")
                for (index, candidate) in candidates.enumerated() {
                    results.append("   \(index + 1). \(candidate)")
                }
                results.append("")
            }

            results.append("✅ 测试完成")

            // 回到主线程更新UI
            DispatchQueue.main.async {
                self.testResults = results
                self.isRunning = false
            }
        }
    }
}

// 香港地址测试视图
struct HongKongAddressTestView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var testResults: [String] = []
    @State private var isRunningTest = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 测试按钮
                    testButtonsSection

                    // 测试结果
                    testResultsSection
                }
                .padding()
            }
            .navigationTitle("香港地址测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    private var testButtonsSection: some View {
        VStack(spacing: 12) {
            Text("测试工具")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                Button(action: runAddressParsingTest) {
                    HStack {
                        Image(systemName: "building.2.crop.circle")
                        Text("测试地址解析")
                        Spacer()
                        if isRunningTest {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningTest)

                Button(action: runCountryDetectionTest) {
                    HStack {
                        Image(systemName: "globe.asia.australia")
                        Text("测试国家检测")
                        Spacer()
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                }

                Button(action: clearResults) {
                    HStack {
                        Image(systemName: "trash")
                        Text("清除结果")
                        Spacer()
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试结果")
                .font(.headline)
                .foregroundColor(.primary)

            if testResults.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                            Text(result)
                                .font(.caption)
                                .foregroundColor(.primary)
                                .padding(.vertical, 2)
                        }
                    }
                }
                .frame(maxHeight: 400)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 测试方法

    private func runAddressParsingTest() {
        isRunningTest = true
        testResults.removeAll()

        DispatchQueue.global(qos: .userInitiated).async {
            // 运行香港地址解析测试（内置版本）

            DispatchQueue.main.async {
                // 模拟测试结果（实际应该从测试函数获取）
                let testCases = [
                    "荃湾海盛路3号荃新天地2座15楼A室",
                    "荃湾大河道99号99广场28楼2801室",
                    "荃湾青山公路荃湾段398号中染大厦12楼B座",
                    "荃湾沙咀道68号荃湾千色汇I期商场3楼301号铺"
                ]

                testResults.append("🇭🇰 香港地址解析测试结果:")
                testResults.append("")

                for address in testCases {
                    let components = AddressCountryDetector.parseHongKongAddress(address)

                    testResults.append("📍 地址: \(address)")
                    testResults.append("   门牌号: '\(components.streetNumber)'")
                    testResults.append("   街道名: '\(components.streetName)'")
                    testResults.append("   建筑: '\(components.building)'")
                    testResults.append("   楼层: '\(components.floor)'")
                    testResults.append("   房间: '\(components.room)'")

                    // 验证街道名匹配
                    let expectedStreetNames = ["海盛路", "大河道", "青山公路荃湾段", "沙咀道"]
                    let addressIndex = testCases.firstIndex(of: address) ?? 0
                    let expectedStreetName = addressIndex < expectedStreetNames.count ? expectedStreetNames[addressIndex] : ""
                    let streetNameMatch = components.streetName == expectedStreetName || components.streetName.contains(expectedStreetName.replacingOccurrences(of: "荃湾段", with: ""))

                    testResults.append("   ✅ 解析成功: \(!components.streetNumber.isEmpty && !components.streetName.isEmpty)")
                    testResults.append("   🛣️ 街道名匹配: \(streetNameMatch ? "✅" : "❌") (期望: '\(expectedStreetName)', 实际: '\(components.streetName)')")
                    testResults.append("")
                }

                isRunningTest = false
            }
        }
    }

    private func runCountryDetectionTest() {
        testResults.removeAll()

        let testAddresses = [
            "荃湾海盛路3号荃新天地2座15楼A室",
            "Central, Hong Kong",
            "荃湾大河道99号99广场28楼2801室, Hong Kong",
            "123 Collins St, Melbourne, VIC 3000",
            "456 Main St, New York, NY 10001"
        ]

        testResults.append("🌍 国家检测测试结果:")
        testResults.append("")

        for address in testAddresses {
            let country = AddressCountryDetector.detectCountry(from: address)

            testResults.append("📍 地址: \(address)")
            testResults.append("   国家: \(country?.name ?? "未识别") (\(country?.code ?? "N/A"))")
            testResults.append("   ✅ 检测正确: \(getExpectedCountry(for: address) == country?.code ? "是" : "否")")
            testResults.append("")
        }
    }

    private func getExpectedCountry(for address: String) -> String? {
        if address.contains("荃湾") || address.contains("Hong Kong") {
            return "HK"
        } else if address.contains("Melbourne") || address.contains("VIC") {
            return "AU"
        } else if address.contains("New York") || address.contains("NY") {
            return "US"
        }
        return nil
    }

    private func clearResults() {
        testResults.removeAll()
    }
}

struct DeveloperToolsView_Previews: PreviewProvider {
    static var previews: some View {
        DeveloperToolsView()
            .environmentObject(LocalizationManager.shared)
    }
}
