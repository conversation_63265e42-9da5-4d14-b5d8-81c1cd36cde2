import SwiftUI
import CoreLocation

/// 全球地址测试视图 - 测试175个国家的地址处理能力
struct GlobalAddressTestView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var testResults: [GlobalAddressTestResult] = []
    @State private var isRunningTests = false
    @State private var customAddress = ""
    @State private var selectedCountry = "全球混合"

    let countries = ["全球混合", "香港", "中国大陆", "澳大利亚", "美国", "英国", "日本", "新加坡", "加拿大", "德国"]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                headerSection
                testControlSection
                resultsSection
                Spacer()
            }
            .padding()
            .navigationTitle("全球地址测试")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") { dismiss() }
                }
            }
        }
    }

    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "globe.americas.fill")
                .font(.system(size: 50))
                .foregroundColor(.blue)

            Text("全球地址处理测试")
                .font(.title2)
                .fontWeight(.bold)

            Text("测试NaviBatch在175个国家的地址处理能力")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    private var testControlSection: some View {
        VStack(spacing: 16) {
            // 自定义地址测试
            VStack(alignment: .leading, spacing: 8) {
                Text("自定义地址测试")
                    .font(.headline)

                TextField("输入任何国家的地址...", text: $customAddress)
                    .textFieldStyle(.roundedBorder)

                Button("测试这个地址") {
                    testCustomAddress()
                }
                .buttonStyle(.borderedProminent)
                .disabled(customAddress.isEmpty || isRunningTests)
            }

            Divider()

            // 预设测试
            VStack(alignment: .leading, spacing: 8) {
                Text("预设地址测试")
                    .font(.headline)

                Picker("选择测试集", selection: $selectedCountry) {
                    ForEach(countries, id: \.self) { country in
                        Text(country).tag(country)
                    }
                }
                .pickerStyle(.segmented)

                Button("运行预设测试") {
                    runPresetTests()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isRunningTests)
            }

            Button("测试928 Gellert Blvd修复") {
                testGellertBlvdFix()
            }
            .buttonStyle(.borderedProminent)
            .tint(.orange)
            .disabled(isRunningTests)

            Button("测试英文地址结果") {
                testEnglishAddressResults()
            }
            .buttonStyle(.borderedProminent)
            .tint(.green)
            .disabled(isRunningTests)

            Button("清除结果") {
                testResults.removeAll()
            }
            .buttonStyle(.bordered)
            .disabled(testResults.isEmpty)
        }
    }

    private var resultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            if !testResults.isEmpty {
                Text("测试结果 (\(testResults.count))")
                    .font(.headline)

                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(testResults.indices, id: \.self) { index in
                            resultCard(testResults[index])
                        }
                    }
                }
            }

            if isRunningTests {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("正在测试地址...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }

    private func resultCard(_ result: GlobalAddressTestResult) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(result.success ? .green : .red)

                Text(result.originalAddress)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(2)

                Spacer()

                if let strategy = result.strategy {
                    Text(strategy)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                }
            }

            if result.success {
                if let formattedAddress = result.formattedAddress {
                    Text("📍 \(formattedAddress)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                if let coordinate = result.coordinate {
                    Text("🌐 (\(String(format: "%.6f", coordinate.latitude)), \(String(format: "%.6f", coordinate.longitude)))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                Text("❌ \(result.errorMessage ?? "未知错误")")
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }

    // MARK: - 测试方法

    private func testCustomAddress() {
        guard !customAddress.isEmpty else { return }

        isRunningTests = true

        Task {
            let result = await UniversalAddressProcessor.shared.processGlobalAddress(customAddress)

            await MainActor.run {
                let testResult = GlobalAddressTestResult(from: result)
                testResults.insert(testResult, at: 0)
                isRunningTests = false
            }
        }
    }

    private func runPresetTests() {
        isRunningTests = true

        Task {
            let addresses = getPresetAddresses(for: selectedCountry)

            for address in addresses {
                let result = await UniversalAddressProcessor.shared.processGlobalAddress(address)

                await MainActor.run {
                    let testResult = GlobalAddressTestResult(from: result)
                    testResults.append(testResult)
                }

                // 避免过快请求
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            }

            await MainActor.run {
                isRunningTests = false
            }
        }
    }

    /// 测试928 Gellert Blvd修复
    private func testGellertBlvdFix() {
        isRunningTests = true

        Task {
            let problematicAddress = "928 Gellert Blvd, Daly City, CA, 94015"

            // 清除之前的结果
            await MainActor.run {
                testResults.removeAll()
            }

            // 添加测试开始标记
            await MainActor.run {
                let startResult = GlobalAddressTestResult(
                    originalAddress: "🧪 开始测试928 Gellert Blvd修复",
                    success: true,
                    formattedAddress: "测试目标：验证全球MKLocalSearch策略是否能找到正确地址",
                    coordinate: nil,
                    strategy: "测试开始",
                    errorMessage: nil
                )
                testResults.append(startResult)
            }

            // 测试问题地址
            let result = await UniversalAddressProcessor.shared.processGlobalAddress(problematicAddress)

            await MainActor.run {
                let testResult = GlobalAddressTestResult(from: result)
                testResults.append(testResult)

                // 添加验证结果
                switch result {
                case .success(_, _, let coordinate, let placemark, let strategy, let confidence):
                    // 验证结果是否正确
                    let isCorrectStreet = placemark.thoroughfare?.lowercased().contains("gellert") ?? false
                    let isCorrectCity = placemark.locality?.lowercased().contains("daly city") ?? false

                    let verificationResult = GlobalAddressTestResult(
                        originalAddress: "🔍 验证结果",
                        success: isCorrectStreet && isCorrectCity,
                        formattedAddress: isCorrectStreet && isCorrectCity ?
                            "✅ 修复成功！找到正确的Gellert Blvd地址" :
                            "⚠️ 结果可能不正确，需要进一步检查",
                        coordinate: coordinate,
                        strategy: "策略: \(strategy), 置信度: \(confidence)",
                        errorMessage: isCorrectStreet && isCorrectCity ? nil :
                            "街道匹配: \(isCorrectStreet), 城市匹配: \(isCorrectCity)"
                    )
                    testResults.append(verificationResult)

                case .failed(_, let reason):
                    let failureResult = GlobalAddressTestResult(
                        originalAddress: "❌ 测试失败",
                        success: false,
                        formattedAddress: "修复未成功，地址仍然无法找到",
                        coordinate: nil,
                        strategy: "失败",
                        errorMessage: reason
                    )
                    testResults.append(failureResult)
                }

                isRunningTests = false
            }
        }
    }

    /// 测试英文地址结果
    private func testEnglishAddressResults() {
        isRunningTests = true

        Task {
            let testAddresses = [
                "928 Gellert Blvd, Daly City, CA, 94015",
                "32 Plymouth Cir, Daly City, CA, 94015",
                "500 King Dr, Daly City, CA, 94015"
            ]

            // 清除之前的结果
            await MainActor.run {
                testResults.removeAll()
            }

            // 添加测试开始标记
            await MainActor.run {
                let startResult = GlobalAddressTestResult(
                    originalAddress: "🧪 测试英文地址结果",
                    success: true,
                    formattedAddress: "验证地址是否返回英文格式（不含中文字符）",
                    coordinate: nil,
                    strategy: "英文测试开始",
                    errorMessage: nil
                )
                testResults.append(startResult)
            }

            for address in testAddresses {
                let result = await UniversalAddressProcessor.shared.processGlobalAddress(address)

                await MainActor.run {
                    let testResult = GlobalAddressTestResult(from: result)
                    testResults.append(testResult)

                    // 检查结果是否为英文
                    switch result {
                    case .success(_, let formattedAddress, _, let placemark, let strategy, _):
                        let hasChineseInFormatted = AddressStandardizer.containsChineseCharacters(formattedAddress)
                        let hasChineseInLocality = placemark.locality.map { AddressStandardizer.containsChineseCharacters($0) } ?? false
                        let hasChineseInCountry = placemark.country.map { AddressStandardizer.containsChineseCharacters($0) } ?? false

                        let isEnglishResult = !hasChineseInFormatted && !hasChineseInLocality && !hasChineseInCountry

                        let verificationResult = GlobalAddressTestResult(
                            originalAddress: "🔍 语言验证: \(address)",
                            success: isEnglishResult,
                            formattedAddress: isEnglishResult ?
                                "✅ 结果为英文格式" :
                                "❌ 结果包含中文字符",
                            coordinate: nil,
                            strategy: "策略: \(strategy)",
                            errorMessage: isEnglishResult ? nil :
                                "格式化地址中文: \(hasChineseInFormatted), 城市中文: \(hasChineseInLocality), 国家中文: \(hasChineseInCountry)"
                        )
                        testResults.append(verificationResult)

                    case .failed(_, _):
                        break // 失败的情况已经在上面添加了
                    }
                }

                // 避免过快请求
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }

            await MainActor.run {
                isRunningTests = false
            }
        }
    }

    private func getPresetAddresses(for country: String) -> [String] {
        switch country {
        case "香港":
            return [
                "恒来邨永宁楼",
                "中环国际金融中心",
                "尖沙咀海港城",
                "铜锣湾时代广场",
                "旺角朗豪坊"
            ]

        case "中国大陆":
            return [
                "北京市朝阳区三里屯",
                "上海市浦东新区陆家嘴",
                "广州市天河区珠江新城",
                "深圳市南山区科技园",
                "杭州市西湖区文三路"
            ]

        case "澳大利亚":
            return [
                "123 Collins Street, Melbourne VIC 3000",
                "Circular Quay, Sydney NSW 2000",
                "Queen Street Mall, Brisbane QLD 4000",
                "Rundle Mall, Adelaide SA 5000",
                "Murray Street, Perth WA 6000"
            ]

        case "美国":
            return [
                "Times Square, New York, NY",
                "Hollywood Boulevard, Los Angeles, CA",
                "Michigan Avenue, Chicago, IL",
                "Union Square, San Francisco, CA",
                "Las Vegas Strip, Las Vegas, NV"
            ]

        case "英国":
            return [
                "Big Ben, London",
                "Piccadilly Circus, London",
                "Oxford Street, London",
                "Edinburgh Castle, Edinburgh",
                "Albert Dock, Liverpool"
            ]

        case "日本":
            return [
                "東京駅",
                "渋谷スクランブル交差点",
                "大阪城",
                "京都清水寺",
                "横浜中華街"
            ]

        case "新加坡":
            return [
                "Marina Bay Sands",
                "Orchard Road",
                "Sentosa Island",
                "Clarke Quay",
                "Chinatown"
            ]

        case "加拿大":
            return [
                "CN Tower, Toronto",
                "Stanley Park, Vancouver",
                "Old Quebec, Quebec City",
                "Parliament Hill, Ottawa",
                "Calgary Tower, Calgary"
            ]

        case "德国":
            return [
                "Brandenburg Gate, Berlin",
                "Marienplatz, Munich",
                "Cologne Cathedral, Cologne",
                "Speicherstadt, Hamburg",
                "Römerberg, Frankfurt"
            ]

        default: // 全球混合
            return [
                "恒来邨永宁楼", // 香港
                "Times Square, New York", // 美国
                "Big Ben, London", // 英国
                "東京駅", // 日本
                "123 Collins Street, Melbourne", // 澳大利亚
                "Marina Bay Sands, Singapore", // 新加坡
                "CN Tower, Toronto", // 加拿大
                "Brandenburg Gate, Berlin", // 德国
                "北京市天安门广场", // 中国
                "Eiffel Tower, Paris" // 法国
            ]
        }
    }
}

// MARK: - 测试结果数据结构

struct GlobalAddressTestResult {
    let originalAddress: String
    let success: Bool
    let formattedAddress: String?
    let coordinate: CLLocationCoordinate2D?
    let strategy: String?
    let errorMessage: String?

    init(from result: GlobalGeocodingResult) {
        switch result {
        case .success(let originalAddress, let formattedAddress, let coordinate, _, let strategy, _):
            self.originalAddress = originalAddress
            self.success = true
            self.formattedAddress = formattedAddress
            self.coordinate = coordinate
            self.strategy = strategy
            self.errorMessage = nil

        case .failed(let address, let reason):
            self.originalAddress = address
            self.success = false
            self.formattedAddress = nil
            self.coordinate = nil
            self.strategy = nil
            self.errorMessage = reason
        }
    }

    /// 自定义初始化方法，用于测试结果
    init(originalAddress: String, success: Bool, formattedAddress: String?, coordinate: CLLocationCoordinate2D?, strategy: String?, errorMessage: String?) {
        self.originalAddress = originalAddress
        self.success = success
        self.formattedAddress = formattedAddress
        self.coordinate = coordinate
        self.strategy = strategy
        self.errorMessage = errorMessage
    }
}

#Preview("GlobalAddressTestView") {
    GlobalAddressTestView()
}
