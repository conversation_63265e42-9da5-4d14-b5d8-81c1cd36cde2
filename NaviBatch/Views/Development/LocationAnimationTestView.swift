import SwiftUI
import MapKit

/// u5b9au4f4du52a8u753bu6d4bu8bd5u89c6u56fe
/// u7528u4e8eu6d4bu8bd5u5b9au4f4du6807u8bb0u7684u52a8u753bu6548u679c
struct LocationAnimationTestView: View {
    @StateObject private var locationManager = LocationManager.shared
    @State private var mapType: MKMapType = .standard
    
    var body: some View {
        VStack {
            // u5730u56feu6d4bu8bd5u533au57df
            Map {
                // u4f7fu7528u6211u4eecu7684u81eau5b9au4e49u4f4du7f6eu6807u8bb0
                if let userLocation = locationManager.userLocation {
                    Annotation("", coordinate: userLocation) {
                        UserLocationPulseView()
                    }
                }
            }
            .mapStyle(.standard)
            .frame(height: 450)
            
            // u63a7u5236u533au57df
            VStack(spacing: 16) {
                Text("u4f4du7f6eu6807u8bb0u6d4bu8bd5")
                    .font(.headline)
                
                Button("u5f00u59cbu4f4du7f6eu66f4u65b0") {
                    locationManager.startUpdatingLocation()
                }
                .buttonStyle(.borderedProminent)
                
                Button("u505cu6b62u4f4du7f6eu66f4u65b0") {
                    locationManager.stopUpdatingLocation()
                }
                .buttonStyle(.bordered)
                
                // u663eu793au5f53u524du4f4du7f6eu4fe1u606f
                if let location = locationManager.userLocation {
                    Text("u5f53u524du4f4du7f6e: \(location.latitude), \(location.longitude)")
                        .font(.caption)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal)
                } else {
                    Text("u6b63u5728u83b7u53d6u4f4du7f6e...")
                        .font(.caption)
                }
            }
            .padding()
        }
        .onAppear {
            // u7acbu5373u5f00u59cbu4f4du7f6eu66f4u65b0
            locationManager.startUpdatingLocation()
        }
    }
}

#Preview("LocationAnimationTestView") {
    LocationAnimationTestView()
}
