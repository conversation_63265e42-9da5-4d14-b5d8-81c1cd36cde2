import SwiftUI

/// 版本更新测试视图 - 用于开发者测试版本更新功能
struct AppUpdateTestView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var updateService = AppUpdateService.shared
    @ObservedObject private var configService = ConfigService.shared

    @State private var testResults: [String] = []
    @State private var isRunningTest = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前状态
                    currentStatusSection

                    // 测试按钮
                    testButtonsSection

                    // 配置信息
                    configInfoSection

                    // 测试结果
                    testResultsSection
                }
                .padding()
            }
            .navigationTitle("版本更新测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - 当前状态区域
    private var currentStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📱 当前状态")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                AppUpdateStatusRow(title: "应用版本", value: AppEnvironment.appVersion, color: .primary)
                AppUpdateStatusRow(title: "构建版本", value: AppEnvironment.buildVersion, color: .primary)
                AppUpdateStatusRow(title: "更新提示状态", value: updateService.showUpdatePrompt ? "显示中" : "隐藏", color: .primary)
                AppUpdateStatusRow(title: "配置加载状态", value: configService.isLoading ? "加载中" : "已完成", color: .primary)

                if let config = configService.config {
                    AppUpdateStatusRow(title: "服务器版本", value: config.version)
                    if let updateInfo = config.updateAvailable {
                        AppUpdateStatusRow(title: "可用更新", value: updateInfo.latestVersion, color: .blue)
                    } else {
                        AppUpdateStatusRow(title: "可用更新", value: "无", color: .green)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 测试按钮区域
    private var testButtonsSection: some View {
        VStack(spacing: 12) {
            Text("🧪 测试功能")
                .font(.headline)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 8) {
                // 模拟更新提示
                TestButton(
                    title: "模拟版本更新提示",
                    subtitle: "显示模拟的更新弹窗",
                    icon: "app.badge",
                    color: .blue
                ) {
                    addResult("🎭 模拟版本更新提示")
                    updateService.simulateUpdate()
                }

                // 调试模式检查更新
                TestButton(
                    title: "调试模式检查更新",
                    subtitle: "使用调试模式从服务器检查更新",
                    icon: "network",
                    color: .green
                ) {
                    addResult("🔍 开始调试模式更新检查...")
                    isRunningTest = true
                    updateService.triggerUpdateCheck(debugMode: true)

                    // 延迟结束测试状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        isRunningTest = false
                        addResult("✅ 调试模式更新检查完成")
                    }
                }
                .disabled(isRunningTest)

                // 正常模式检查更新
                TestButton(
                    title: "正常模式检查更新",
                    subtitle: "使用正常模式检查更新",
                    icon: "arrow.clockwise",
                    color: .orange
                ) {
                    addResult("🔄 开始正常模式更新检查...")
                    isRunningTest = true
                    updateService.triggerUpdateCheck(debugMode: false)

                    // 延迟结束测试状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        isRunningTest = false
                        addResult("✅ 正常模式更新检查完成")
                    }
                }
                .disabled(isRunningTest)

                // 重置更新状态
                TestButton(
                    title: "重置更新状态",
                    subtitle: "清除跳过版本和提醒记录",
                    icon: "arrow.counterclockwise",
                    color: .red
                ) {
                    addResult("🔄 重置更新状态")
                    updateService.resetUpdateState()
                    addResult("✅ 更新状态已重置")
                }

                // 刷新配置
                TestButton(
                    title: "刷新服务器配置",
                    subtitle: "从Cloudflare Worker获取最新配置",
                    icon: "icloud.and.arrow.down",
                    color: .purple
                ) {
                    addResult("☁️ 刷新服务器配置...")
                    configService.refreshConfig()
                    addResult("✅ 配置刷新请求已发送")
                }
            }
        }
    }

    // MARK: - 配置信息区域
    private var configInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("⚙️ 配置信息")
                .font(.headline)
                .foregroundColor(.primary)

            if let config = configService.config {
                VStack(alignment: .leading, spacing: 8) {
                    ConfigRow(title: "配置版本", value: config.version)
                    ConfigRow(title: "最小支持版本", value: config.minAppVersion)
                    ConfigRow(title: "最后更新", value: formatDate(config.lastUpdated))

                    if let updateInfo = config.updateAvailable {
                        Divider()
                        Text("📱 更新信息")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)

                        ConfigRow(title: "最新版本", value: updateInfo.latestVersion)
                        ConfigRow(title: "强制更新", value: updateInfo.forceUpdate ? "是" : "否")
                        ConfigRow(title: "调试模式", value: updateInfo.debugMode == true ? "是" : "否")
                        ConfigRow(title: "更新标题", value: updateInfo.updateInfo.updateTitle)
                    }
                }
            } else {
                Text("配置未加载")
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 测试结果区域
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("📋 测试结果")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Button("清除") {
                    testResults.removeAll()
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if testResults.isEmpty {
                Text("暂无测试结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(testResults.indices, id: \.self) { index in
                        HStack(alignment: .top) {
                            Text("\(index + 1).")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 20, alignment: .leading)

                            Text(testResults[index])
                                .font(.caption)
                                .foregroundColor(.primary)
                                .fixedSize(horizontal: false, vertical: true)

                            Spacer()
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 辅助方法
    private func addResult(_ message: String) {
        let timestamp = DateFormatter.timeOnly.string(from: Date())
        testResults.append("[\(timestamp)] \(message)")
    }

    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateStyle = .short
            displayFormatter.timeStyle = .short
            return displayFormatter.string(from: date)
        }
        return dateString
    }
}

// MARK: - 辅助视图组件
struct AppUpdateStatusRow: View {
    let title: String
    let value: String
    let color: Color

    init(title: String, value: String, color: Color = .primary) {
        self.title = title
        self.value = value
        self.color = color
    }

    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(color)
                .fontWeight(.medium)
        }
        .font(.caption)
    }
}

struct ConfigRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(.primary)
                .fontWeight(.medium)
        }
        .font(.caption)
    }
}

struct TestButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 日期格式化扩展
extension DateFormatter {
    static let timeOnly: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
}

// MARK: - 预览
#Preview {
    AppUpdateTestView()
}
