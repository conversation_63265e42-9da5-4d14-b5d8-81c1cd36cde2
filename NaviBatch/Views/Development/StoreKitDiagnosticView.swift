import SwiftUI
import StoreKit

/// StoreKit诊断视图
/// 专门用于诊断年费购买问题
struct StoreKitDiagnosticView: View {
    @StateObject private var storeKitManager = StoreKitManager.shared
    @State private var diagnosticResults: [String] = []
    @State private var isRunningDiagnostic = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 诊断按钮
                    diagnosticButtonsSection

                    // 产品信息
                    productInfoSection

                    // 诊断结果
                    diagnosticResultsSection
                }
                .padding()
            }
            .navigationTitle("storekit_diagnostics".localized)
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    // MARK: - 视图组件

    private var diagnosticButtonsSection: some View {
        VStack(spacing: 12) {
            Text("诊断工具")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                Button(action: runFullDiagnostic) {
                    HStack {
                        Image(systemName: "stethoscope")
                        Text("运行完整诊断")
                        Spacer()
                        if isRunningDiagnostic {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningDiagnostic)

                Button(action: testYearlyPurchase) {
                    HStack {
                        Image(systemName: "calendar.circle")
                        Text("测试年费购买")
                        Spacer()
                        if isRunningDiagnostic {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningDiagnostic)

                Button(action: clearResults) {
                    HStack {
                        Image(systemName: "trash")
                        Text("清除结果")
                        Spacer()
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var productInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("产品信息")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("已加载产品数:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(storeKitManager.products.count)")
                        .fontWeight(.semibold)
                }

                ForEach(storeKitManager.products, id: \.id) { product in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("产品ID:")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(product.id)
                                .font(.caption)
                                .fontWeight(.semibold)
                        }

                        HStack {
                            Text("名称:")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(product.displayName)
                                .font(.caption)
                        }

                        HStack {
                            Text("价格:")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(product.displayPrice)
                                .font(.caption)
                                .foregroundColor(.green)
                        }

                        HStack {
                            Text("类型:")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("\(product.type)")
                                .font(.caption)
                        }
                    }
                    .padding(.vertical, 4)
                    .padding(.horizontal, 8)
                    .background(Color(.systemGray5))
                    .cornerRadius(6)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var diagnosticResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("诊断结果")
                .font(.headline)
                .foregroundColor(.primary)

            if diagnosticResults.isEmpty {
                Text("暂无诊断结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(diagnosticResults.enumerated()), id: \.offset) { index, result in
                            Text("\(index + 1). \(result)")
                                .font(.caption)
                                .foregroundColor(.primary)
                                .padding(.vertical, 2)
                        }
                    }
                }
                .frame(maxHeight: 300)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 诊断方法

    private func addResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        diagnosticResults.append("[\(timestamp)] \(result)")
    }

    private func runFullDiagnostic() {
        isRunningDiagnostic = true
        addResult("🔍 开始运行完整StoreKit诊断...")

        Task {
            // 1. 检查StoreKit配置
            await checkStoreKitConfiguration()

            // 2. 加载产品
            await loadAndAnalyzeProducts()

            // 3. 检查产品可用性
            await checkProductAvailability()

            // 4. 检查购买状态
            await checkPurchaseStatus()

            await MainActor.run {
                addResult("✅ 完整诊断完成")
                isRunningDiagnostic = false
            }
        }
    }

    private func testYearlyPurchase() {
        isRunningDiagnostic = true
        addResult("🧪 开始测试年费购买...")

        Task {
            do {
                // 确保产品已加载
                try await storeKitManager.loadProducts(forceReload: true)

                guard let yearlyProduct = storeKitManager.product(for: .expert) else {
                    await MainActor.run {
                        addResult("❌ 年费产品未找到")
                        isRunningDiagnostic = false
                    }
                    return
                }

                await MainActor.run {
                    addResult("📦 找到年费产品: \(yearlyProduct.id)")
                    addResult("💰 价格: \(yearlyProduct.displayPrice)")
                    addResult("📝 名称: \(yearlyProduct.displayName)")
                    addResult("🔄 开始购买流程...")
                }

                let success = try await storeKitManager.purchase(yearlyProduct)

                await MainActor.run {
                    if success {
                        addResult("✅ 年费购买成功！")
                    } else {
                        addResult("❌ 年费购买失败")
                    }
                    isRunningDiagnostic = false
                }
            } catch {
                await MainActor.run {
                    addResult("❌ 年费购买错误: \(error.localizedDescription)")
                    addResult("🔍 错误详情: \(error)")
                    isRunningDiagnostic = false
                }
            }
        }
    }

    private func clearResults() {
        diagnosticResults.removeAll()
    }

    // MARK: - 诊断辅助方法

    private func checkStoreKitConfiguration() async {
        await MainActor.run {
            addResult("🔧 检查StoreKit配置...")

            // 检查产品ID
            let productIDs = SubscriptionTier.allCases.map { $0.productID }.filter { !$0.isEmpty }
            addResult("📋 配置的产品ID: \(productIDs)")

            // 检查环境
            #if DEBUG
            addResult("🧪 当前环境: DEBUG (测试环境)")
            #else
            addResult("🏭 当前环境: RELEASE (生产环境)")
            #endif
        }
    }

    private func loadAndAnalyzeProducts() async {
        await MainActor.run {
            addResult("📦 加载和分析产品...")
        }

        do {
            try await storeKitManager.loadProducts(forceReload: true)

            await MainActor.run {
                let products = storeKitManager.products
                addResult("✅ 成功加载 \(products.count) 个产品")

                for product in products {
                    addResult("  📦 \(product.id): \(product.displayName) - \(product.displayPrice)")
                }

                // 特别检查年费产品
                if let yearlyProduct = storeKitManager.product(for: .expert) {
                    addResult("✅ 年费产品已找到: \(yearlyProduct.id)")
                } else {
                    addResult("❌ 年费产品未找到")
                }
            }
        } catch {
            await MainActor.run {
                addResult("❌ 产品加载失败: \(error.localizedDescription)")
            }
        }
    }

    private func checkProductAvailability() async {
        await MainActor.run {
            addResult("🔍 检查产品可用性...")

            for tier in SubscriptionTier.allCases {
                if tier == .free { continue }

                if storeKitManager.product(for: tier) != nil {
                    addResult("✅ \(tier.rawValue) 产品可用")
                } else {
                    addResult("❌ \(tier.rawValue) 产品不可用")
                }
            }
        }
    }

    private func checkPurchaseStatus() async {
        await MainActor.run {
            addResult("💳 检查购买状态...")

            let purchasedIDs = storeKitManager.purchasedProductIDs
            addResult("📋 已购买产品: \(Array(purchasedIDs))")

            for tier in SubscriptionTier.allCases {
                if tier == .free { continue }

                let isPurchased = storeKitManager.isPurchased(productID: tier.productID)
                addResult("  \(tier.rawValue): \(isPurchased ? "已购买" : "未购买")")
            }
        }
    }
}

#Preview("StoreKitDiagnosticView") {
    StoreKitDiagnosticView()
}
