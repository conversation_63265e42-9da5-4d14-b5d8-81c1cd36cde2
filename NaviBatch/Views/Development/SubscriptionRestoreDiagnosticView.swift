import SwiftUI
import StoreKit
import os.log

/// 订阅恢复诊断工具
struct SubscriptionRestoreDiagnosticView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var storeKitManager = StoreKitManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared

    @State private var diagnosticResults: [String] = []
    @State private var isRunningDiagnostic = false
    @State private var showingDetailedLogs = false

    private let logger = os.Logger(subsystem: "com.navibatch.app", category: "SubscriptionDiagnostic")

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前状态显示
                    currentStatusSection

                    // 诊断按钮
                    diagnosticButtonsSection

                    // 诊断结果
                    diagnosticResultsSection
                }
                .padding()
            }
            .navigationTitle("订阅恢复诊断")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - 视图组件

    private var currentStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("当前订阅状态")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("订阅级别:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(subscriptionManager.currentTier.rawValue)
                        .fontWeight(.semibold)
                        .foregroundColor(subscriptionStatusColor)
                }

                HStack {
                    Text("试用期状态:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(subscriptionManager.isInFreeTrial ? "试用中" : "未试用")
                        .fontWeight(.semibold)
                        .foregroundColor(subscriptionManager.isInFreeTrial ? .orange : .gray)
                }

                if subscriptionManager.isInFreeTrial, let endDate = subscriptionManager.freeTrialEndDate {
                    HStack {
                        Text("试用期结束:")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(DateFormatter.localizedString(from: endDate, dateStyle: .short, timeStyle: .short))
                            .font(.caption)
                            .fontWeight(.semibold)
                    }
                }

                HStack {
                    Text("已购买产品:")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(storeKitManager.purchasedProductIDs.count)")
                        .fontWeight(.semibold)
                        .foregroundColor(storeKitManager.purchasedProductIDs.isEmpty ? .red : .green)
                }

                if !storeKitManager.purchasedProductIDs.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("产品ID:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        ForEach(Array(storeKitManager.purchasedProductIDs), id: \.self) { productID in
                            Text("• \(productID)")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var diagnosticButtonsSection: some View {
        VStack(spacing: 12) {
            Text("诊断工具")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 8) {
                Button(action: runFullDiagnostic) {
                    HStack {
                        Image(systemName: "stethoscope")
                        Text("运行完整诊断")
                        Spacer()
                        if isRunningDiagnostic {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningDiagnostic)

                Button(action: testRestorePurchases) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("测试恢复购买")
                        Spacer()
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningDiagnostic)

                Button(action: validateReceipt) {
                    HStack {
                        Image(systemName: "checkmark.seal")
                        Text("验证收据")
                        Spacer()
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isRunningDiagnostic)

                Button(action: clearResults) {
                    HStack {
                        Image(systemName: "trash")
                        Text("清除结果")
                        Spacer()
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var diagnosticResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("诊断结果")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Button(action: {
                    showingDetailedLogs.toggle()
                }) {
                    Text(showingDetailedLogs ? "隐藏详细日志" : "显示详细日志")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }

            if diagnosticResults.isEmpty {
                Text("暂无诊断结果")
                    .foregroundColor(.secondary)
                    .italic()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(diagnosticResults.enumerated()), id: \.offset) { index, result in
                            Text("\(index + 1). \(result)")
                                .font(.caption)
                                .foregroundColor(.primary)
                                .padding(.vertical, 2)
                        }
                    }
                }
                .frame(maxHeight: 300)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 计算属性

    private var subscriptionStatusColor: Color {
        switch subscriptionManager.currentTier {
        case .free: return .gray
        case .pro: return .blue
        case .expert: return .purple
        }
    }

    // MARK: - 诊断方法

    private func addResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        diagnosticResults.append("[\(timestamp)] \(result)")
        logger.info("\(result)")
    }

    private func runFullDiagnostic() {
        isRunningDiagnostic = true
        addResult("🔍 开始运行完整订阅诊断...")

        Task {
            await performFullDiagnostic()

            await MainActor.run {
                addResult("✅ 完整诊断完成")
                isRunningDiagnostic = false
            }
        }
    }

    private func testRestorePurchases() {
        addResult("🔄 测试恢复购买...")

        Task {
            do {
                try await storeKitManager.restorePurchases()
                await MainActor.run {
                    addResult("✅ 恢复购买成功")
                }
            } catch {
                await MainActor.run {
                    addResult("❌ 恢复购买失败: \(error.localizedDescription)")
                }
            }
        }
    }

    private func validateReceipt() {
        addResult("📋 验证收据...")
        subscriptionManager.validateSubscriptionReceipt()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            addResult("✅ 收据验证请求已发送")
        }
    }

    private func clearResults() {
        diagnosticResults.removeAll()
    }

    private func performFullDiagnostic() async {
        await MainActor.run {
            // 1. 检查当前订阅状态
            addResult("📊 检查当前订阅状态...")
            addResult("  当前级别: \(subscriptionManager.currentTier.rawValue)")
            addResult("  试用状态: \(subscriptionManager.isInFreeTrial ? "试用中" : "未试用")")
            addResult("  已购买产品数: \(storeKitManager.purchasedProductIDs.count)")

            // 2. 检查产品加载状态
            addResult("🛍️ 检查产品加载状态...")
            addResult("  可用产品数: \(storeKitManager.products.count)")
            for product in storeKitManager.products {
                addResult("    • \(product.id): \(product.displayName)")
            }

            // 3. 检查StoreKit配置
            addResult("⚙️ 检查StoreKit配置...")
            addResult("  沙盒状态: \(sandboxStatusDescription(storeKitManager.sandboxStatus))")
            addResult("  加载状态: \(storeKitManager.isLoading ? "加载中" : "已完成")")
            if let errorMessage = storeKitManager.errorMessage {
                addResult("  错误信息: \(errorMessage)")
            }
        }

        // 4. 检查交易历史
        await checkTransactionHistory()

        // 5. 检查订阅状态
        await checkSubscriptionStatus()
    }

    private func checkTransactionHistory() async {
        await MainActor.run {
            addResult("📜 检查交易历史...")
        }

        var transactionCount = 0
        for await result in Transaction.currentEntitlements {
            transactionCount += 1
            do {
                let transaction = try checkVerificationResult(result)
                await MainActor.run {
                    addResult("  交易 \(transactionCount): \(transaction.productID)")
                    addResult("    购买日期: \(DateFormatter.localizedString(from: transaction.purchaseDate, dateStyle: .short, timeStyle: .short))")
                }
            } catch {
                await MainActor.run {
                    addResult("  交易 \(transactionCount) 验证失败: \(error.localizedDescription)")
                }
            }
        }

        await MainActor.run {
            if transactionCount == 0 {
                addResult("  ❌ 未找到任何交易记录")
            } else {
                addResult("  ✅ 找到 \(transactionCount) 个交易记录")
            }
        }
    }

    private func checkSubscriptionStatus() async {
        await MainActor.run {
            addResult("🔔 检查订阅状态...")
        }

        for tier in SubscriptionTier.allCases {
            guard !tier.productID.isEmpty else { continue }

            do {
                let statuses = try await Product.SubscriptionInfo.status(for: tier.productID)
                await MainActor.run {
                    addResult("  \(tier.rawValue) (\(tier.productID)):")
                    if statuses.isEmpty {
                        addResult("    无订阅状态")
                    } else {
                        for status in statuses {
                            addResult("    状态: \(status.state)")
                            // 检查续费信息
                            switch status.renewalInfo {
                            case .verified(let renewalInfo):
                                addResult("    自动续费: \(renewalInfo.willAutoRenew)")
                            case .unverified(_, let error):
                                addResult("    续费信息验证失败: \(error.localizedDescription)")
                            }
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    addResult("  \(tier.rawValue): 获取状态失败 - \(error.localizedDescription)")
                }
            }
        }
    }

    private func checkVerificationResult<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .verified(let safe):
            return safe
        case .unverified(_, let error):
            throw error
        }
    }

    private func sandboxStatusDescription(_ status: SandboxStatus) -> String {
        switch status {
        case .unknown:
            return "未知"
        case .working:
            return "正常工作"
        case .authError:
            return "认证错误"
        case .noReceipt:
            return "无收据"
        }
    }
}

#Preview("SubscriptionRestoreDiagnosticView") {
    SubscriptionRestoreDiagnosticView()
}
