import SwiftUI

/// 本地化测试视图，用于在开发过程中测试不同语言环境下的应用界面
struct LocalizationTestView: View {
    @EnvironmentObject private var localizationManager: LocalizationManager
    @State private var selectedLanguage: LocalizationManager.Language = .system
    @State private var selectedSection: TestSection = .common
    @State private var showingLanguageSelector = false

    enum TestSection: String, CaseIterable, Identifiable {
        case common = "通用界面元素"
        case addresses = "地址管理"
        case routes = "路线管理"
        case delivery = "配送管理"
        case groups = "分组管理"
        case subscription = "订阅"
        case formatting = "格式化字符串"
        case errors = "错误和警告"

        var id: String { self.rawValue }
    }

    var body: some View {
        NavigationView {
            VStack {
                // 语言选择器
                HStack {
                    Text("当前语言：")
                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack {
                            Text(selectedLanguage.displayName)
                            Image(systemName: "chevron.down")
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 5)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .actionSheet(isPresented: $showingLanguageSelector) {
                        ActionSheet(
                            title: Text("选择测试语言"),
                            buttons: languageButtons()
                        )
                    }
                }
                .padding()

                // 测试部分选择器
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 10) {
                        ForEach(TestSection.allCases) { section in
                            Button(action: {
                                selectedSection = section
                            }) {
                                Text(section.rawValue)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(selectedSection == section ? Color.blue : Color.gray.opacity(0.2))
                                    .foregroundColor(selectedSection == section ? .white : .primary)
                                    .cornerRadius(8)
                            }
                        }
                    }
                    .padding(.horizontal)
                }

                // 测试内容
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        switch selectedSection {
                        case .common:
                            commonUIElementsSection
                        case .addresses:
                            addressesSection
                        case .routes:
                            routesSection
                        case .delivery:
                            deliverySection
                        case .groups:
                            groupsSection
                        case .subscription:
                            subscriptionSection
                        case .formatting:
                            formattingSection
                        case .errors:
                            errorsSection
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("本地化测试")
            .onChange(of: selectedLanguage) { oldLanguage, newLanguage in
                if newLanguage != .system {
                    // 更新 localizationManager 的 selectedLanguage 属性
                    localizationManager.selectedLanguage = newLanguage
                } else {
                    // 重置为系统语言
                    localizationManager.resetToSystemLanguage()
                }
            }
        }
    }

    // 语言选择按钮
    private func languageButtons() -> [ActionSheet.Button] {
        var buttons: [ActionSheet.Button] = []

        buttons.append(.default(Text("系统语言")) {
            selectedLanguage = .system
        })

        for language in LocalizationManager.Language.allCases where language != .system {
            buttons.append(.default(Text(language.displayName)) {
                selectedLanguage = language
            })
        }

        buttons.append(.cancel(Text("取消")))

        return buttons
    }

    // 通用界面元素测试部分
    private var commonUIElementsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("通用界面元素")

            Group {
                testItem("close", "关闭按钮")
                testItem("cancel", "取消按钮")
                testItem("save", "保存按钮")
                testItem("edit", "编辑按钮")
                testItem("delete", "删除按钮")
                testItem("done", "完成按钮")
                testItem("next", "下一步按钮")
                testItem("back", "返回按钮")
                testItem("confirm", "确认按钮")
            }

            Group {
                testItem("error", "错误标签")
                testItem("success", "成功标签")
                testItem("warning", "警告标签")
                testItem("loading", "加载中标签")
                testItem("search", "搜索标签")
                testItem("settings", "设置标签")
                testItem("help", "帮助标签")
                testItem("about", "关于标签")
                testItem("menu", "菜单标签")
            }
        }
    }

    // 地址管理测试部分
    private var addressesSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("地址管理")

            Group {
                testItem("addresses", "地址标题")
                testItem("add_address", "添加地址按钮")
                testItem("edit_address", "编辑地址按钮")
                testItem("delete_address", "删除地址按钮")
                testItem("address_details", "地址详情标题")
            }

            Group {
                testItem("street", "街道标签")
                testItem("city", "城市标签")
                testItem("state", "州/省标签")
                testItem("country", "国家标签")
                testItem("postal_code", "邮政编码标签")
            }

            Group {
                testItem("phone", "电话标签")
                testItem("email", "电子邮件标签")
                testItem("website", "网站标签")
                testItem("company", "公司标签")
                testItem("notes", "备注标签")
            }

            Group {
                testItem("coordinates", "坐标标签")
                testItem("latitude", "纬度标签")
                testItem("longitude", "经度标签")
                testItem("geocoding_error", "地理编码错误")
                testItem("address_validation", "地址验证")
            }
        }
    }

    // 路线管理测试部分
    private var routesSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("路线管理")

            Group {
                testItem("routes", "路线标题")
                testItem("create_route", "创建路线按钮")
                testItem("edit_route", "编辑路线按钮")
                testItem("delete_route", "删除路线按钮")
                testItem("route_name", "路线名称标签")
            }

            Group {
                testItem("route_details", "路线详情标题")
                testItem("start_point", "起点标签")
                testItem("end_point", "终点标签")
                testItem("waypoints", "途经点标签")
                testItem("total_distance", "总距离标签")
            }

            Group {
                testItem("estimated_time", "预计时间标签")
                testItem("route_summary", "路线摘要标题")
                testItem("route_options", "路线选项标题")
                testItem("optimize_route", "优化路线按钮")
                testItem("optimizing", "优化中标签")
            }

            Group {
                testItem("optimization_complete", "优化完成标签")
                testItem("route_saved", "路线已保存")
                testItem("route_optimized", "路线已优化")
                testItem("optimizing_route", "正在优化路线...")
                testItem("completed_percent", String(format: "completed_percent".localized, 50))
            }
        }
    }

    // 配送管理测试部分
    private var deliverySection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("配送管理")

            Group {
                testItem("delivery", "配送标题")
                testItem("delivery_confirmation", "配送确认标题")
                testItem("take_photo", "拍照按钮")
                testItem("signature", "签名标签")
                testItem("delivery_notes", "配送备注标签")
            }

            Group {
                testItem("delivery_status", "配送状态标签")
                testItem("delivered", "已配送标签")
                testItem("not_delivered", "未配送标签")
                testItem("delivery_time", "配送时间标签")
                testItem("delivery_date", "配送日期标签")
            }

            Group {
                testItem("package_details", "包裹详情标题")
                testItem("package_id", "包裹ID标签")
                testItem("package_weight", "包裹重量标签")
                testItem("package_dimensions", "包裹尺寸标签")
                testItem("recipient_name", "收件人姓名标签")
            }

            Group {
                testItem("recipient_phone", "收件人电话标签")
                testItem("delivery_point_management", "配送点管理")
                testItem("package_info", "包裹信息")
                testItem("vehicle_position", "车辆位置")
                testItem("door_number_photo", "门牌号照片")
            }
        }
    }

    // 分组管理测试部分
    private var groupsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("分组管理")

            Group {
                testItem("groups", "分组标题")
                testItem("create_group", "创建分组按钮")
                testItem("edit_group", "编辑分组按钮")
                testItem("delete_group", "删除分组按钮")
                testItem("group_name", "分组名称标签")
            }

            Group {
                testItem("group_details", "分组详情标题")
                testItem("auto_grouping", "自动分组按钮")
                testItem("group_by", "分组依据标签")
                testItem("add_to_group", "添加到分组按钮")
                testItem("remove_from_group", "从分组中移除按钮")
            }

            Group {
                testItem("group_created", "分组已创建")
                testItem("auto_grouping_completed", "自动分组已完成")
                testItem("auto_grouping_in_progress", "自动分组进行中...")
                testItem("create_group_every_14_addresses", "每14个地址创建一个分组")
            }
        }
    }

    // 订阅测试部分
    private var subscriptionSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("订阅")

            Group {
                testItem("subscription", "订阅标题")
                testItem("free_plan", "免费版标签")
                testItem("pro_plan", "专业版标签")
                testItem("expert_plan", "专家版标签")
                testItem("monthly", "月度计划标签")
            }

            Group {
                testItem("yearly", "年度计划标签")
                testItem("subscribe", "订阅按钮")
                testItem("upgrade", "升级按钮")
                testItem("upgrade_to_pro", "升级至专业版按钮")
                testItem("manage_subscription", "管理订阅按钮")
            }

            Group {
                testItem("restore_purchases", "恢复购买按钮")
                testItem("subscription_benefits", "订阅权益标题")
                testItem("free_trial", "免费试用标签")
                testItem("price_per_month", String(format: "price_per_month".localized, "$9.99"))
                testItem("price_per_year", String(format: "price_per_year".localized, "$99.99"))
            }
        }
    }

    // 格式化字符串测试部分
    private var formattingSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("格式化字符串")

            Group {
                testItem("completed_percent", String(format: "completed_percent".localized, 75))
                testItem("processing_points", String(format: "processing_points".localized, 8, 10))
                testItem("estimated_remaining_time", String(format: "estimated_remaining_time".localized, "2:30"))
                testItem("current_start_point", String(format: "current_start_point".localized, "123 Main St"))
                testItem("processing_addresses", String(format: "processing_addresses".localized, 5, 10))
            }

            Group {
                testItem("kilometers_format", String(format: "kilometers_format".localized, 12.5))
                testItem("meters_format", String(format: "meters_format".localized, 850))
                testItem("save_failure", String(format: "save_failure".localized, "数据库错误"))
                testItem("current_search_text", String(format: "current_search_text".localized, "测试搜索"))
                testItem("search_results_count", String(format: "search_results_count".localized, 42))
            }

            Group {
                testItem("minutes_format", String(format: "minutes_format".localized, 15))
                testItem("selection_limit_description", String(format: "selection_limit_description".localized, 15, 10))
                testItem("version_info", String(format: "version_info".localized, "1.0.0", "123"))
            }
        }
    }

    // 错误和警告测试部分
    private var errorsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            sectionHeader("错误和警告")

            Group {
                testItem("network_error", "网络错误标签")
                testItem("location_error", "位置错误标签")
                testItem("permission_denied", "权限被拒绝标签")
                testItem("location_permission_required", "需要位置权限标签")
                testItem("camera_permission_required", "需要相机权限标签")
            }

            Group {
                testItem("photo_library_permission_required", "需要照片库权限标签")
                testItem("please_try_again", "请重试标签")
                testItem("something_went_wrong", "出现错误标签")
                testItem("invalid_input", "无效输入标签")
                testItem("required_field", "必填字段标签")
            }

            Group {
                testItem("no_internet_connection", "无网络连接标签")
                testItem("server_error", "服务器错误标签")
                testItem("timeout_error", "请求超时标签")
                testItem("data_not_found", "未找到数据标签")
                testItem("selection_limit_reached", "已达到选择限制")
            }
        }
    }

    // 辅助函数：创建测试项
    private func testItem(_ key: String, _ description: String) -> some View {
        VStack(alignment: .leading) {
            Text(description)
                .font(.caption)
                .foregroundColor(.gray)

            HStack {
                Text(key.localized)
                    .padding(.vertical, 8)
                    .padding(.horizontal, 12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(6)

                Spacer()

                Text(key)
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            Divider()
        }
    }

    // 辅助函数：创建部分标题
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(.headline)
            .padding(.vertical, 8)
    }
}

struct LocalizationTestView_Previews: PreviewProvider {
    static var previews: some View {
        LocalizationTestView()
            .environmentObject(LocalizationManager.shared)
    }
}
