import SwiftUI

/// 本地化工具视图，提供各种本地化相关的工具
struct LocalizationToolsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var localizationManager: LocalizationManager

    enum LocalizationTool: String, CaseIterable, Identifiable {
        case localizationTest = "本地化测试"
        case localizationChecker = "硬编码文本检查"
        case localizationValidator = "本地化文件验证"
        case localizationUpdater = "本地化文件更新"

        var id: String { self.rawValue }

        var iconName: String {
            switch self {
            case .localizationTest: return "globe"
            case .localizationChecker: return "doc.text.magnifyingglass"
            case .localizationValidator: return "checkmark.circle"
            case .localizationUpdater: return "arrow.triangle.2.circlepath"
            }
        }

        var description: String {
            switch self {
            case .localizationTest: return "测试应用在不同语言环境下的界面显示"
            case .localizationChecker: return "检测代码中的硬编码文本"
            case .localizationValidator: return "验证所有语言文件包含相同的键"
            case .localizationUpdater: return "从基准语言文件复制缺失的键到其他语言文件"
            }
        }
    }

    @State private var selectedTool: LocalizationTool? = nil
    @State private var showingLocalizationTest = false
    @State private var reportText = ""
    @State private var isRunningTool = false

    var body: some View {
        NavigationView {
            VStack {
                // 工具选择
                List {
                    Section(header: Text("本地化工具")) {
                        ForEach(LocalizationTool.allCases) { tool in
                            Button(action: {
                                selectedTool = tool
                                if tool == .localizationTest {
                                    showingLocalizationTest = true
                                } else {
                                    runTool(tool)
                                }
                            }) {
                                HStack {
                                    Image(systemName: tool.iconName)
                                        .foregroundColor(.blue)
                                        .frame(width: 30)

                                    VStack(alignment: .leading) {
                                        Text(tool.rawValue)
                                            .font(.headline)

                                        Text(tool.description)
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                    }
                                }
                                .padding(.vertical, 8)
                            }
                            .disabled(isRunningTool)
                        }
                    }
                }

                // 结果显示
                if !reportText.isEmpty {
                    VStack {
                        HStack {
                            Text("结果报告")
                                .font(.headline)

                            Spacer()

                            Button(action: {
                                reportText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.horizontal)

                        ScrollView {
                            Text(reportText)
                                .font(.system(.body, design: .monospaced))
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                }
            }
            .navigationTitle("本地化工具")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("close".localized) {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingLocalizationTest) {
                LocalizationTestView()
                    .environmentObject(localizationManager)
            }
            .overlay(
                Group {
                    if isRunningTool {
                        ProgressView("正在运行工具...")
                            .padding()
                            .background(Color.white.opacity(0.9))
                            .cornerRadius(10)
                            .shadow(radius: 5)
                    }
                }
            )
        }
    }

    // 运行选择的工具
    private func runTool(_ tool: LocalizationTool) {
        isRunningTool = true
        reportText = ""

        // 在后台线程中运行工具，避免阻塞主线程
        DispatchQueue.global(qos: .userInitiated).async {
            var result = ""

            switch tool {
            case .localizationChecker:
                result = LocalizationChecker.run()
            case .localizationValidator:
                result = LocalizationValidator.run()
            case .localizationUpdater:
                result = LocalizationUpdater.run()
            case .localizationTest:
                // 这个工具是通过 sheet 显示的，不需要在这里处理
                break
            }

            // 在主线程中更新 UI
            DispatchQueue.main.async {
                reportText = result
                isRunningTool = false
            }
        }
    }
}

// 使用 Scripts/LocalizationValidator.swift 中的 LocalizationValidator 类

struct LocalizationToolsView_Previews: PreviewProvider {
    static var previews: some View {
        LocalizationToolsView()
            .environmentObject(LocalizationManager.shared)
    }
}
