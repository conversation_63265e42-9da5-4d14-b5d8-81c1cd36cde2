//
//  SystemLanguageTestView.swift
//  NaviBatch
//
//  Created on 2025-06-04.
//

import SwiftUI

struct SystemLanguageTestView: View {
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        NavigationView {
            List {
                Section("当前语言设置") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("选择的语言:")
                            .font(.headline)
                        Text(localizationManager.selectedLanguage.displayName)
                            .font(.body)
                            .foregroundColor(.blue)
                    }
                    .padding(.vertical, 4)
                }
                
                Section("系统语言信息") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("系统首选语言:")
                            .font(.headline)
                        Text(Locale.preferredLanguages.first ?? "未知")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Text("系统语言代码:")
                            .font(.headline)
                        Text(Locale.current.language.languageCode?.identifier ?? "未知")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Text("系统地区代码:")
                            .font(.headline)
                        Text(Locale.current.language.region?.identifier ?? "未知")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Text("系统文字系统:")
                            .font(.headline)
                        Text(Locale.current.language.script?.identifier ?? "未知")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                
                Section("测试按钮") {
                    Button("设置为系统语言") {
                        localizationManager.selectedLanguage = .system
                    }
                    .foregroundColor(.blue)
                    
                    Button("设置为简体中文") {
                        localizationManager.selectedLanguage = .simplifiedChinese
                    }
                    .foregroundColor(.blue)
                    
                    Button("设置为英语") {
                        localizationManager.selectedLanguage = .english
                    }
                    .foregroundColor(.blue)
                }
                
                Section("系统语言显示名称测试") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("getSystemLanguageDisplayName() 结果:")
                            .font(.headline)
                        Text(localizationManager.getSystemLanguageDisplayName())
                            .font(.body)
                            .foregroundColor(.green)
                    }
                    .padding(.vertical, 4)
                }
            }
            .navigationTitle("系统语言测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview("SystemLanguageTestView") {
    SystemLanguageTestView()
}
