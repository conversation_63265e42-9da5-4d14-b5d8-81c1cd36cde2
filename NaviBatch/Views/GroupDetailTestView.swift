import SwiftUI
import SwiftData

/// 分组详情界面测试视图
/// 用于测试分组详情界面的新功能和 Dark Mode 优化
struct GroupDetailTestView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var viewModel = RouteViewModel()
    @State private var testGroup: DeliveryGroup?

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("分组详情界面测试")
                    .font(.title)
                    .foregroundColor(.adaptivePrimaryText)
                    .padding()

                // 创建测试分组按钮
                Button(action: createTestGroup) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("创建测试分组")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.adaptivePrimaryButton)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .padding(.horizontal)

                // 显示分组详情按钮
                if let group = testGroup {
                    NavigationLink(destination: GroupDetailView(group: group, viewModel: viewModel)) {
                        HStack {
                            Image(systemName: "eye.fill")
                            Text("查看分组详情")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.adaptiveSuccess)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)

                    // 分组信息预览
                    VStack(alignment: .leading, spacing: 8) {
                        Text("测试分组信息")
                            .font(.headline)
                            .foregroundColor(.adaptivePrimaryText)

                        Text("分组名称: \(group.name)")
                            .font(.subheadline)
                            .foregroundColor(.adaptiveSecondaryText)

                        Text("地址数量: \(group.points.count)")
                            .font(.subheadline)
                            .foregroundColor(.adaptiveSecondaryText)

                        Text("包含第三方排序标签的地址: \(group.points.filter { $0.thirdPartySortNumber != nil }.count)")
                            .font(.caption)
                            .foregroundColor(.adaptiveSecondaryText)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.adaptiveCardBackground)
                    .cornerRadius(12)
                    .padding(.horizontal)
                }

                // 功能说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("🚀 新功能测试")
                        .font(.headline)
                        .foregroundColor(.adaptivePrimaryText)

                    Text("• 第三方排序标签显示")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)

                    Text("• Dark Mode 优化颜色")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)

                    Text("• 组内路线优化功能")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)

                    Text("• 状态颜色区分")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)

                    Text("• 拖拽排序支持")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)

                Spacer()
            }
            .background(Color.adaptiveBackground)
            .navigationTitle("分组详情测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            setupViewModel()
        }
    }

    // MARK: - 辅助方法

    /// 设置 ViewModel
    private func setupViewModel() {
        viewModel.modelContext = modelContext
    }

    /// 创建测试分组
    private func createTestGroup() {
        // 创建测试地址点
        let testPoints = createTestDeliveryPoints()

        // 创建测试分组
        let group = DeliveryGroup(
            name: "测试分组 - 第三方标签",
            points: testPoints,
            groupNumber: 1
        )

        // 保存到数据库
        modelContext.insert(group)

        // 设置地址点的分组信息
        for (index, point) in testPoints.enumerated() {
            point.isAssignedToGroup = true
            point.assignedGroupNumber = 1
            point.sort_number = index + 1
            point.sorted_number = index + 1
            modelContext.insert(point)
        }

        // 保存更改
        try? modelContext.save()

        // 更新测试分组
        testGroup = group

        print("[INFO] GroupDetailTestView - 创建测试分组成功，包含 \(testPoints.count) 个地址")
    }

    /// 创建测试配送点
    private func createTestDeliveryPoints() -> [DeliveryPoint] {
        var points: [DeliveryPoint] = []

        // 测试地址数据
        let testAddresses = [
            ("3420 Tupelo Drive, Stockton, CA 95209", "GoFo", "4", DeliveryAppType.gofo),
            ("10727 Trevor Drive, Stockton, CA 95209", "Amazon", "2", DeliveryAppType.amazonFlex),
            ("4265 Maddie Circle, Stockton, CA 95209", "iMile", "7", DeliveryAppType.imile),
            ("4167 Maddie Circle, Stockton, CA 95209", nil, nil, DeliveryAppType.manual),
            ("10624 Pleasant Valley Circle, Stockton, CA 95209", "YWE", "12", DeliveryAppType.ywe),
            ("10948 Dutch Tulip Drive, Stockton, CA 95209", "SpeedX", "8", DeliveryAppType.speedx)
        ]

        for (index, (address, _, sortNumber, appType)) in testAddresses.enumerated() {
            let point = DeliveryPoint(
                sort_number: index + 1,
                originalAddress: address,
                latitude: 37.9577 + Double(index) * 0.001, // 模拟不同坐标
                longitude: -121.2908 + Double(index) * 0.001,
                isStartPoint: false,
                isEndPoint: false
            )

            // 设置第三方排序信息
            if let sortNum = sortNumber {
                point.thirdPartySortNumber = sortNum
            }

            // 设置应用类型
            point.sourceAppRaw = appType.rawValue

            // 设置不同的状态用于测试颜色
            switch index {
            case 0:
                point.status = DeliveryStatus.completed.rawValue
            case 1:
                point.status = DeliveryStatus.failed.rawValue
            case 2:
                point.geocodingWarning = "坐标验证警告"
            default:
                point.status = DeliveryStatus.pending.rawValue
                point.isOptimized = true
            }

            points.append(point)
        }

        return points
    }
}

#Preview("Group Detail Test - Light") {
    GroupDetailTestView()
        .modelContainer(for: [DeliveryPoint.self, DeliveryGroup.self], inMemory: true)
        .preferredColorScheme(.light)
}

#Preview("Group Detail Test - Dark") {
    GroupDetailTestView()
        .modelContainer(for: [DeliveryPoint.self, DeliveryGroup.self], inMemory: true)
        .preferredColorScheme(.dark)
}
