import SwiftUI
import os.log

// MARK: - 识别日志查看器
struct AILogViewer: View {
    @State private var logEntries: [LogEntry] = []
    @State private var isAutoRefresh = true
    @State private var selectedLogType: LogType? = nil
    @State private var searchText = ""
    
    private let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 控制栏
                HStack {
                    // 日志类型筛选
                    Picker("日志类型", selection: $selectedLogType) {
                        Text("全部").tag(LogType?.none)
                        Text("🔍 智能").tag(LogType?.some(.ai))
                        Text("📷 OCR").tag(LogType?.some(.ocr))
                        Text("🖼️ 图片").tag(LogType?.some(.imageProcessing))
                        Text("🌐 网络").tag(LogType?.some(.network))
                        Text("❌ 错误").tag(LogType?.some(.error))
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Spacer()
                    
                    // 自动刷新开关
                    Toggle("自动刷新", isOn: $isAutoRefresh)
                        .toggleStyle(SwitchToggleStyle())
                    
                    // 清空按钮
                    Button("清空") {
                        logEntries.removeAll()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
                .background(Color(.systemGray6))
                
                // 搜索栏
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                    
                    TextField("搜索日志...", text: $searchText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                
                // 日志列表
                List {
                    ForEach(filteredLogEntries, id: \.id) { entry in
                        LogEntryRow(entry: entry)
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("识别日志查看器")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("导出日志") {
                        exportLogs()
                    }
                }
            }
        }
        .onReceive(timer) { _ in
            if isAutoRefresh {
                refreshLogs()
            }
        }
        .onAppear {
            refreshLogs()
        }
    }
    
    // 筛选后的日志条目
    private var filteredLogEntries: [LogEntry] {
        var filtered = logEntries
        
        // 按类型筛选
        if let selectedType = selectedLogType {
            filtered = filtered.filter { $0.type == selectedType }
        }
        
        // 按搜索文本筛选
        if !searchText.isEmpty {
            filtered = filtered.filter { 
                $0.message.localizedCaseInsensitiveContains(searchText) 
            }
        }
        
        return filtered.sorted { $0.timestamp > $1.timestamp }
    }
    
    // 刷新日志（这里模拟从系统日志读取）
    private func refreshLogs() {
        // 在实际应用中，这里会从OSLog读取日志
        // 现在我们添加一些模拟日志来演示
        let newEntry = LogEntry(
            timestamp: Date(),
            type: .ai,
            message: "模拟智能识别日志条目 - \(Date().formatted(.dateTime.hour().minute().second()))"
        )
        
        if logEntries.count > 100 {
            logEntries.removeFirst()
        }
        
        // 只在有新内容时添加
        if logEntries.isEmpty || logEntries.last?.message != newEntry.message {
            logEntries.append(newEntry)
        }
    }
    
    // 导出日志
    private func exportLogs() {
        let logText = filteredLogEntries.map { entry in
            "[\(entry.timestamp.formatted(.dateTime))] \(getTypePrefix(entry.type)): \(entry.message)"
        }.joined(separator: "\n")
        
        // 这里可以实现导出到文件或分享功能
        print("导出日志:\n\(logText)")
    }
    
    private func getTypePrefix(_ type: LogType) -> String {
        switch type {
        case .ai: return "🔍 智能"
        case .ocr: return "📷 OCR"
        case .imageProcessing: return "🖼️ IMAGE"
        case .network: return "🌐 NETWORK"
        case .error: return "❌ ERROR"
        case .info: return "ℹ️ INFO"
        case .debug: return "🔍 DEBUG"
        case .warning: return "⚠️ WARNING"
        default: return "📝 LOG"
        }
    }
}

// MARK: - 日志条目数据模型
struct LogEntry {
    let id = UUID()
    let timestamp: Date
    let type: LogType
    let message: String
}

// MARK: - 日志条目行视图
struct LogEntryRow: View {
    let entry: LogEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                // 类型图标
                Text(getTypeIcon(entry.type))
                    .font(.caption)
                
                // 时间戳
                Text(entry.timestamp.formatted(.dateTime.hour().minute().second()))
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 类型标签
                Text(getTypeName(entry.type))
                    .font(.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(getTypeColor(entry.type).opacity(0.2))
                    .foregroundColor(getTypeColor(entry.type))
                    .cornerRadius(4)
            }
            
            // 消息内容
            Text(entry.message)
                .font(.system(.body, design: .monospaced))
                .foregroundColor(getMessageColor(entry.type))
        }
        .padding(.vertical, 2)
    }
    
    private func getTypeIcon(_ type: LogType) -> String {
        switch type {
        case .ai: return "🔍"
        case .ocr: return "📷"
        case .imageProcessing: return "🖼️"
        case .network: return "🌐"
        case .error: return "❌"
        case .warning: return "⚠️"
        case .info: return "ℹ️"
        case .debug: return "🔍"
        default: return "📝"
        }
    }
    
    private func getTypeName(_ type: LogType) -> String {
        switch type {
        case .ai: return "智能"
        case .ocr: return "OCR"
        case .imageProcessing: return "IMAGE"
        case .network: return "NET"
        case .error: return "ERROR"
        case .warning: return "WARN"
        case .info: return "INFO"
        case .debug: return "DEBUG"
        default: return "LOG"
        }
    }
    
    private func getTypeColor(_ type: LogType) -> Color {
        switch type {
        case .ai: return .purple
        case .ocr: return .orange
        case .imageProcessing: return .blue
        case .network: return .green
        case .error: return .red
        case .warning: return .yellow
        case .info: return .blue
        case .debug: return .gray
        default: return .primary
        }
    }
    
    private func getMessageColor(_ type: LogType) -> Color {
        switch type {
        case .error: return .red
        case .warning: return .orange
        default: return .primary
        }
    }
}

// MARK: - 预览
#Preview("AILogViewer") {
    AILogViewer()
}
