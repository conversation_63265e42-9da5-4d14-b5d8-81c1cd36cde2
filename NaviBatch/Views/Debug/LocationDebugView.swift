import SwiftUI
import CoreLocation

/// 位置调试视图
/// 用于诊断和修复位置相关问题
struct LocationDebugView: View {
    @StateObject private var locationManager = LocationManager.shared
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            List {
                // 当前位置状态
                Section("当前位置状态") {
                    LocationStatusRow(
                        title: "位置权限",
                        value: authorizationStatusText,
                        color: authorizationStatusColor
                    )
                    
                    LocationStatusRow(
                        title: "位置来源",
                        value: locationManager.diagnoseLocationSource(),
                        color: locationSourceColor
                    )
                    
                    if let location = locationManager.userLocation {
                        LocationStatusRow(
                            title: "当前坐标",
                            value: String(format: "%.6f, %.6f", location.latitude, location.longitude),
                            color: .primary
                        )
                    } else {
                        LocationStatusRow(
                            title: "当前坐标",
                            value: "未获取",
                            color: .red
                        )
                    }
                    
                    LocationStatusRow(
                        title: "设备区域",
                        value: deviceRegionCode,
                        color: .primary
                    )
                    
                    LocationStatusRow(
                        title: "选择区域",
                        value: locationManager.getCurrentRegion(),
                        color: .primary
                    )
                }
                
                // 诊断信息
                Section("诊断信息") {
                    Button("打印详细调试信息") {
                        locationManager.debugLocationSettings()
                        showAlert("调试信息已打印到控制台")
                    }
                    
                    Button("检查位置合理性") {
                        checkLocationReasonableness()
                    }
                }
                
                // 修复操作
                Section("修复操作") {
                    Button("强制重新获取GPS位置") {
                        locationManager.forceRefreshRealLocation()
                        showAlert("已开始重新获取GPS位置")
                    }
                    .foregroundColor(.blue)
                    
                    Button("重置区域设置") {
                        locationManager.resetRegionSettings()
                        showAlert("区域设置已重置")
                    }
                    .foregroundColor(.orange)
                    
                    Button("清除所有位置缓存") {
                        clearAllLocationCache()
                        showAlert("位置缓存已清除")
                    }
                    .foregroundColor(.red)
                }
                
                // 权限操作
                Section("权限操作") {
                    Button("请求位置权限") {
                        locationManager.requestLocationPermission()
                    }
                    .foregroundColor(.green)
                    
                    Button("打开系统设置") {
                        openSystemSettings()
                    }
                    .foregroundColor(.blue)
                }
            }
            .navigationTitle("位置调试")
            .navigationBarTitleDisplayMode(.inline)
            .alert("提示", isPresented: $showingAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var authorizationStatusText: String {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            return "未确定"
        case .denied:
            return "已拒绝"
        case .restricted:
            return "受限制"
        case .authorizedWhenInUse:
            return "使用时允许"
        case .authorizedAlways:
            return "始终允许"
        @unknown default:
            return "未知"
        }
    }
    
    private var authorizationStatusColor: Color {
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways:
            return .green
        case .denied, .restricted:
            return .red
        case .notDetermined:
            return .orange
        @unknown default:
            return .gray
        }
    }
    
    private var locationSourceColor: Color {
        let source = locationManager.diagnoseLocationSource()
        if source.contains("GPS") {
            return .green
        } else if source.contains("默认") {
            return .orange
        } else {
            return .red
        }
    }
    
    private var deviceRegionCode: String {
        if #available(iOS 16, *) {
            return Locale.current.region?.identifier ?? "未知"
        } else {
            return Locale.current.regionCode ?? "未知"
        }
    }
    
    // MARK: - 方法
    
    private func showAlert(_ message: String) {
        alertMessage = message
        showingAlert = true
    }
    
    private func checkLocationReasonableness() {
        guard let location = locationManager.userLocation else {
            showAlert("当前没有位置信息")
            return
        }
        
        let deviceRegion = deviceRegionCode.lowercased()
        let selectedRegion = locationManager.getCurrentRegion()
        
        var message = "位置检查结果:\n"
        message += "设备区域: \(deviceRegion)\n"
        message += "选择区域: \(selectedRegion)\n"
        message += "坐标: \(String(format: "%.6f, %.6f", location.latitude, location.longitude))\n"
        
        // 检查是否是澳洲坐标
        if abs(location.latitude - (-37.90219)) < 0.001 && abs(location.longitude - 145.16034) < 0.001 {
            message += "\n⚠️ 检测到澳洲硬编码坐标！"
            if deviceRegion != "au" {
                message += "\n❌ 设备不在澳洲但使用了澳洲坐标"
            }
        } else {
            message += "\n✅ 坐标看起来正常"
        }
        
        showAlert(message)
    }
    
    private func clearAllLocationCache() {
        UserDefaults.standard.removeObject(forKey: "ForceUseDefaultLocation")
        UserDefaults.standard.removeObject(forKey: "SelectedRegion")
        locationManager.resetRegionSettings()
    }
    
    private func openSystemSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

// MARK: - 辅助视图

struct LocationStatusRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(color)
                .fontWeight(.medium)
        }
    }
}

#Preview("LocationDebugView") {
    LocationDebugView()
}
