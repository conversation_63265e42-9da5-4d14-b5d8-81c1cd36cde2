//
//  AddressEditDebugView.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/25.
//  地址编辑功能调试工具
//

import SwiftUI
import MapKit
import CoreLocation

/// 地址编辑功能调试工具
/// 用于测试和诊断地址编辑功能的问题
struct AddressEditDebugView: View {
    @State private var testAddress = ""
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var isSearching = false
    @State private var selectedCoordinate: CLLocationCoordinate2D?
    @State private var debugLogs: [String] = []
    @State private var showingLogs = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 测试输入区域
                VStack(alignment: .leading, spacing: 8) {
                    Text("🔍 地址编辑功能测试")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("输入地址来测试搜索功能：")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    TextField("输入测试地址", text: $testAddress)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .onSubmit {
                            performDebugSearch()
                        }
                    
                    HStack {
                        Button("🚀 开始测试") {
                            performDebugSearch()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(testAddress.isEmpty || isSearching)
                        
                        Button("🗑️ 清空") {
                            clearResults()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("📋 查看日志") {
                            showingLogs = true
                        }
                        .buttonStyle(.bordered)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 搜索状态
                if isSearching {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("正在搜索...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
                
                // 搜索结果
                if !searchResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("🎯 搜索结果 (\(searchResults.count)个)")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 8) {
                                ForEach(Array(searchResults.enumerated()), id: \.offset) { index, result in
                                    VStack(alignment: .leading, spacing: 4) {
                                        HStack {
                                            Text("\(index + 1).")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                                .frame(width: 20, alignment: .leading)
                                            
                                            VStack(alignment: .leading, spacing: 2) {
                                                Text(result.title)
                                                    .font(.subheadline)
                                                    .fontWeight(.medium)
                                                
                                                if !result.subtitle.isEmpty {
                                                    Text(result.subtitle)
                                                        .font(.caption)
                                                        .foregroundColor(.secondary)
                                                }
                                            }
                                            
                                            Spacer()
                                            
                                            Button("选择") {
                                                selectResult(result)
                                            }
                                            .buttonStyle(.bordered)
                                            .controlSize(.small)
                                        }
                                        
                                        Divider()
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 300)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )
                }
                
                // 选中的坐标信息
                if let coordinate = selectedCoordinate {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("📍 选中的坐标")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("纬度: \(coordinate.latitude, specifier: "%.6f")")
                            .font(.system(.body, design: .monospaced))
                        
                        Text("经度: \(coordinate.longitude, specifier: "%.6f")")
                            .font(.system(.body, design: .monospaced))
                    }
                    .padding()
                    .background(Color(.systemGreen).opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGreen), lineWidth: 1)
                    )
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("地址编辑调试")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingLogs) {
                DebugLogsView(logs: debugLogs)
            }
        }
    }
    
    // MARK: - 方法
    
    private func performDebugSearch() {
        guard !testAddress.isEmpty else { return }
        
        Logger.info("🔍 [地址编辑调试] AddressEditDebugView开始测试搜索: '\(testAddress)'", type: .location)
        addDebugLog("🔍 开始测试搜索: '\(testAddress)'")
        
        isSearching = true
        searchResults = []
        selectedCoordinate = nil
        
        // 使用与EnhancedAddressAutocomplete相同的搜索服务
        LocaleAwareAddressSearchService.shared.performSearch(query: testAddress) { results in
            DispatchQueue.main.async {
                self.isSearching = false
                self.searchResults = results
                
                let message = "✅ 搜索完成: 找到 \(results.count) 个结果"
                Logger.info("🔍 [地址编辑调试] AddressEditDebugView搜索完成: \(results.count)个结果", type: .location)
                self.addDebugLog(message)
                
                // 记录每个结果
                for (index, result) in results.enumerated() {
                    let resultMessage = "结果\(index + 1): \(result.title) | \(result.subtitle)"
                    self.addDebugLog(resultMessage)
                }
            }
        }
    }
    
    private func selectResult(_ result: MKLocalSearchCompletion) {
        Logger.info("🎯 [地址编辑调试] AddressEditDebugView用户选择结果: \(result.title)", type: .location)
        addDebugLog("🎯 选择结果: \(result.title)")
        
        // 获取坐标
        let searchRequest = MKLocalSearch.Request(completion: result)
        let search = MKLocalSearch(request: searchRequest)
        
        search.start { response, error in
            DispatchQueue.main.async {
                if let error = error {
                    let errorMessage = "❌ 获取坐标失败: \(error.localizedDescription)"
                    Logger.error("🎯 [地址编辑调试] AddressEditDebugView获取坐标失败: \(error.localizedDescription)", type: .location)
                    self.addDebugLog(errorMessage)
                    return
                }
                
                guard let response = response,
                      let mapItem = response.mapItems.first else {
                    let message = "❌ 无法获取坐标信息"
                    Logger.warning("🎯 [地址编辑调试] AddressEditDebugView无法获取坐标信息", type: .location)
                    self.addDebugLog(message)
                    return
                }
                
                let coordinate = mapItem.placemark.coordinate
                self.selectedCoordinate = coordinate
                
                let message = "✅ 获取坐标成功: (\(coordinate.latitude), \(coordinate.longitude))"
                Logger.info("🎯 [地址编辑调试] AddressEditDebugView获取坐标成功: (\(coordinate.latitude), \(coordinate.longitude))", type: .location)
                self.addDebugLog(message)
            }
        }
    }
    
    private func clearResults() {
        testAddress = ""
        searchResults = []
        selectedCoordinate = nil
        debugLogs = []
        addDebugLog("🗑️ 清空所有结果")
    }
    
    private func addDebugLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        debugLogs.append("[\(timestamp)] \(message)")
    }
}

// MARK: - 调试日志视图

struct DebugLogsView: View {
    let logs: [String]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(logs, id: \.self) { log in
                        Text(log)
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                    }
                }
                .padding()
            }
            .navigationTitle("调试日志")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AddressEditDebugView()
}
