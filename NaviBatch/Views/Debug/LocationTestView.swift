import SwiftUI
import CoreLocation

/// 位置测试视图
/// 用于快速测试位置功能
struct LocationTestView: View {
    @StateObject private var locationManager = LocationManager.shared
    @State private var testResults: [String] = []
    @State private var isRunningTest = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 当前状态显示
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前位置状态")
                        .font(.headline)
                    
                    HStack {
                        Text("权限状态:")
                        Spacer()
                        Text(authorizationStatusText)
                            .foregroundColor(authorizationStatusColor)
                    }
                    
                    HStack {
                        Text("位置来源:")
                        Spacer()
                        Text(locationManager.diagnoseLocationSource())
                            .foregroundColor(.blue)
                    }
                    
                    if let location = locationManager.userLocation {
                        HStack {
                            Text("当前坐标:")
                            Spacer()
                            Text(String(format: "%.6f, %.6f", location.latitude, location.longitude))
                                .font(.system(.caption, design: .monospaced))
                        }
                    }
                    
                    HStack {
                        Text("设备区域:")
                        Spacer()
                        Text(deviceRegionCode)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                
                // 测试按钮
                VStack(spacing: 12) {
                    Button("运行位置测试") {
                        runLocationTest()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(isRunningTest)
                    
                    Button("强制重新获取GPS") {
                        locationManager.forceRefreshRealLocation()
                        addTestResult("已请求重新获取GPS位置")
                    }
                    .buttonStyle(.bordered)
                    
                    Button("重置位置设置") {
                        locationManager.resetRegionSettings()
                        addTestResult("位置设置已重置")
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.orange)
                    
                    Button("清除测试结果") {
                        testResults.removeAll()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
                
                // 测试结果
                if !testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("测试结果")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                                    Text("\(index + 1). \(result)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("位置测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 计算属性
    
    private var authorizationStatusText: String {
        switch locationManager.authorizationStatus {
        case .notDetermined: return "未确定"
        case .denied: return "已拒绝"
        case .restricted: return "受限制"
        case .authorizedWhenInUse: return "使用时允许"
        case .authorizedAlways: return "始终允许"
        @unknown default: return "未知"
        }
    }
    
    private var authorizationStatusColor: Color {
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways: return .green
        case .denied, .restricted: return .red
        case .notDetermined: return .orange
        @unknown default: return .gray
        }
    }
    
    private var deviceRegionCode: String {
        if #available(iOS 16, *) {
            return Locale.current.region?.identifier ?? "未知"
        } else {
            return Locale.current.regionCode ?? "未知"
        }
    }
    
    // MARK: - 方法
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testResults.append("[\(timestamp)] \(result)")
    }
    
    private func runLocationTest() {
        isRunningTest = true
        addTestResult("开始位置测试...")
        
        // 检查设备区域
        let deviceRegion = deviceRegionCode
        addTestResult("设备区域: \(deviceRegion)")
        
        // 检查选择的区域
        let selectedRegion = locationManager.getCurrentRegion()
        addTestResult("选择区域: \(selectedRegion)")
        
        // 检查位置权限
        addTestResult("位置权限: \(authorizationStatusText)")
        
        // 检查当前位置
        if let location = locationManager.userLocation {
            addTestResult("当前坐标: \(String(format: "%.6f, %.6f", location.latitude, location.longitude))")
            
            // 检查是否是澳洲硬编码坐标
            if abs(location.latitude - (-37.90219)) < 0.001 && abs(location.longitude - 145.16034) < 0.001 {
                addTestResult("⚠️ 检测到澳洲硬编码坐标")
                if deviceRegion.lowercased() != "au" {
                    addTestResult("❌ 设备不在澳洲但使用了澳洲坐标")
                }
            } else {
                addTestResult("✅ 坐标看起来正常")
            }
        } else {
            addTestResult("❌ 没有位置信息")
        }
        
        // 检查位置来源
        let locationSource = locationManager.diagnoseLocationSource()
        addTestResult("位置来源: \(locationSource)")
        
        // 检查强制默认位置标志
        let forceDefault = UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation")
        addTestResult("强制默认位置: \(forceDefault ? "是" : "否")")
        
        addTestResult("测试完成")
        isRunningTest = false
    }
}

#Preview("LocationTestView") {
    LocationTestView()
}
