import SwiftUI

/// 版本信息测试视图
/// 用于验证版本号显示是否正确
struct VersionTestView: View {
    var body: some View {
        NavigationView {
            List {
                Section("当前版本信息") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("应用版本:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(AppEnvironment.appVersion)
                                .foregroundColor(.blue)
                                .fontWeight(.medium)
                        }

                        HStack {
                            Text("构建版本:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(AppEnvironment.buildVersion)
                                .foregroundColor(.blue)
                                .fontWeight(.medium)
                        }

                        HStack {
                            Text("完整版本信息:")
                                .fontWeight(.medium)
                            Spacer()
                            Text(AppEnvironment.versionInfoEnglish)
                                .foregroundColor(.blue)
                                .fontWeight(.medium)
                        }
                    }
                    .padding(.vertical, 8)
                }

                Section("Bundle信息") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("CFBundleShortVersionString:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未找到")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }

                        HStack {
                            Text("CFBundleVersion:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "未找到")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }

                        HStack {
                            Text("Bundle Identifier:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(Bundle.main.bundleIdentifier ?? "未找到")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }
                    .padding(.vertical, 8)
                }

                Section("项目配置信息") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("根据项目配置文件:")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        HStack {
                            Text("MARKETING_VERSION:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("1.0.0")
                                .font(.caption)
                                .foregroundColor(.green)
                        }

                        HStack {
                            Text("CURRENT_PROJECT_VERSION:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("5295")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                    .padding(.vertical, 8)
                }

                Section("预期显示") {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("About App 页面应该显示:")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        // 模拟About页面的显示
                        VStack(spacing: 12) {
                            Text("NaviBatch")
                                .font(.largeTitle)
                                .fontWeight(.bold)

                            Text("Version 1.0.0 (5295)")
                                .font(.footnote)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)

                        Text("版权信息: © 2025 NaviBatch. All rights reserved.")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("如果显示的不是这个版本号，说明需要重新构建应用。")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    .padding(.vertical, 8)
                }

                Section("验证步骤") {
                    VStack(alignment: .leading, spacing: 12) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("1. 检查当前显示")
                                .fontWeight(.medium)
                            Text("查看上方的版本信息是否正确")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            Text("2. 验证About页面")
                                .fontWeight(.medium)
                            Text("菜单 → 关于应用 → 检查版本号")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            Text("3. 如果版本号不正确")
                                .fontWeight(.medium)
                            Text("需要重新构建应用以获取最新的版本信息")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("版本信息测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview("VersionTestView") {
    VersionTestView()
}
