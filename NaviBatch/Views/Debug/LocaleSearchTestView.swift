//
//  LocaleSearchTestView.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/25.
//  语言环境搜索问题测试工具
//

import SwiftUI
import MapKit
import CoreLocation

/// 专门测试语言环境搜索问题的工具
struct LocaleSearchTestView: View {
    @State private var testAddress = "6015 E 149Th St Grandview Mo 64030"
    @State private var searchResults: [MKLocalSearchCompletion] = []
    @State private var isSearching = false
    @State private var testResults: [String] = []
    @State private var currentLocale = Locale.current.identifier
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 系统信息
                VStack(alignment: .leading, spacing: 8) {
                    Text("🌍 系统语言环境信息")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("当前语言环境: \(currentLocale)")
                            .font(.system(.body, design: .monospaced))
                        
                        Text("系统语言: \(Locale.current.language.languageCode?.identifier ?? "未知")")
                            .font(.system(.body, design: .monospaced))

                        Text("地区代码: \(Locale.current.region?.identifier ?? "未知")")
                            .font(.system(.body, design: .monospaced))
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // 测试地址输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("🔍 测试地址")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    TextField("输入测试地址", text: $testAddress)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    HStack {
                        Button("🚀 测试搜索") {
                            performTest()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(isSearching)
                        
                        Button("🔄 重置") {
                            resetTest()
                        }
                        .buttonStyle(.bordered)
                        
                        if isSearching {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
                
                // 测试结果
                if !testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("📊 测试结果")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                                    Text(result)
                                        .font(.system(.caption, design: .monospaced))
                                        .foregroundColor(.primary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 2)
                                        .background(index % 2 == 0 ? Color(.systemGray6) : Color.clear)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )
                }
                
                // 搜索结果
                if !searchResults.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("🎯 搜索结果 (\(searchResults.count)个)")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 8) {
                                ForEach(Array(searchResults.enumerated()), id: \.offset) { index, result in
                                    VStack(alignment: .leading, spacing: 4) {
                                        HStack {
                                            Text("\(index + 1).")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                                .frame(width: 20, alignment: .leading)
                                            
                                            VStack(alignment: .leading, spacing: 2) {
                                                Text(result.title)
                                                    .font(.subheadline)
                                                    .fontWeight(.medium)
                                                
                                                if !result.subtitle.isEmpty {
                                                    Text(result.subtitle)
                                                        .font(.caption)
                                                        .foregroundColor(.secondary)
                                                }
                                            }
                                            
                                            Spacer()
                                        }
                                        
                                        if index < searchResults.count - 1 {
                                            Divider()
                                        }
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("语言环境搜索测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            updateLocaleInfo()
        }
    }
    
    // MARK: - 方法
    
    private func updateLocaleInfo() {
        currentLocale = Locale.current.identifier
    }
    
    private func performTest() {
        guard !testAddress.isEmpty else { return }
        
        isSearching = true
        testResults = []
        searchResults = []
        
        addTestResult("🚀 开始测试地址: '\(testAddress)'")
        addTestResult("🌍 当前系统语言环境: \(Locale.current.identifier)")
        addTestResult("📱 设备语言: \(Locale.current.language.languageCode?.identifier ?? "未知")")
        addTestResult("🗺️ 地区代码: \(Locale.current.region?.identifier ?? "未知")")
        addTestResult("⏰ 测试开始时间: \(DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium))")
        
        // 使用LocaleAwareAddressSearchService进行测试
        LocaleAwareAddressSearchService.shared.performSearch(query: testAddress) { results in
            DispatchQueue.main.async {
                self.isSearching = false
                self.searchResults = results
                
                let endTime = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
                self.addTestResult("⏰ 测试结束时间: \(endTime)")
                self.addTestResult("📊 搜索结果数量: \(results.count)")
                
                if results.isEmpty {
                    self.addTestResult("❌ 搜索失败：无结果返回")
                    self.addTestResult("🔍 可能原因：")
                    self.addTestResult("   1. Apple Maps API在当前语言环境下响应超时")
                    self.addTestResult("   2. 网络连接问题")
                    self.addTestResult("   3. 地址格式不被识别")
                    self.addTestResult("   4. 系统语言环境影响搜索结果")
                } else {
                    self.addTestResult("✅ 搜索成功")
                    for (index, result) in results.enumerated() {
                        self.addTestResult("   结果\(index+1): \(result.title) | \(result.subtitle)")
                    }
                }
                
                // 添加建议
                self.addTestResult("")
                self.addTestResult("💡 建议：")
                if results.isEmpty {
                    self.addTestResult("   1. 尝试简化地址格式")
                    self.addTestResult("   2. 检查网络连接")
                    self.addTestResult("   3. 使用英文地址格式")
                    self.addTestResult("   4. 考虑更换系统语言为英文进行测试")
                } else {
                    self.addTestResult("   搜索功能正常，可以正常使用")
                }
            }
        }
    }
    
    private func resetTest() {
        testAddress = "6015 E 149Th St Grandview Mo 64030"
        testResults = []
        searchResults = []
        updateLocaleInfo()
    }
    
    private func addTestResult(_ message: String) {
        testResults.append(message)
    }
}

#Preview {
    LocaleSearchTestView()
}
