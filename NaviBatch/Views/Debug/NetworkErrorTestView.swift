import SwiftUI
import CoreLocation

/// 网络错误测试视图
/// 用于测试网络错误恢复功能
struct NetworkErrorTestView: View {
    @State private var showingNetworkErrorRecovery = false
    @State private var testFailedAddresses = [
        "3420 Tupelo Dr, 95209",
        "4265 Maddie Cir, 95209",
        "1234 Network Error Street, 12345"
    ]
    
    var body: some View {
        NavigationView {
            List {
                Section("网络错误模拟") {
                    Button("模拟网络错误") {
                        showingNetworkErrorRecovery = true
                    }
                    .foregroundColor(.orange)
                    
                    Text("这将显示网络错误恢复界面，模拟地理编码失败的情况。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Section("测试地址") {
                    ForEach(testFailedAddresses, id: \.self) { address in
                        HStack {
                            Image(systemName: "location.slash")
                                .foregroundColor(.orange)
                            
                            VStack(alignment: .leading) {
                                Text(address)
                                    .font(.subheadline)
                                
                                Text("0.000000, 0.000000 (approximate_location)")
                                    .font(.caption2)
                                    .foregroundColor(.orange)
                            }
                        }
                    }
                }
                
                Section("说明") {
                    Text("当iPhone 12 mini等设备因网络问题导致地理编码失败时，会显示坐标为 (0.000000, 0.000000) 的地址。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("iPhone 16 Pro等设备可能有更完整的地址数据库，因此不会遇到这个问题。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("网络错误测试")
        }
        .sheet(isPresented: $showingNetworkErrorRecovery) {
            NetworkErrorRecoveryView(
                failedAddresses: testFailedAddresses,
                onRetry: {
                    showingNetworkErrorRecovery = false
                    // 模拟重试
                    print("🔄 用户点击重试")
                },
                onDismiss: {
                    showingNetworkErrorRecovery = false
                }
            )
        }
    }
}

#Preview {
    NetworkErrorTestView()
}
