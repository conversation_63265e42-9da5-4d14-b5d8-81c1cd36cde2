import SwiftUI
import CoreLocation

/// 位置修复测试视图
/// 用于验证位置硬编码问题的修复效果
struct LocationFixTestView: View {
    @StateObject private var locationManager = LocationManager.shared
    @State private var testLog: [String] = []
    @State private var isRunningTest = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 测试标题
                Text("位置硬编码问题修复验证")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding()
                
                // 当前状态
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前状态")
                        .font(.headline)
                    
                    Group {
                        HStack {
                            Text("设备区域:")
                            Spacer()
                            Text(deviceRegionCode)
                                .foregroundColor(.blue)
                        }
                        
                        HStack {
                            Text("位置权限:")
                            Spacer()
                            Text(authorizationStatusText)
                                .foregroundColor(authorizationStatusColor)
                        }
                        
                        HStack {
                            Text("位置来源:")
                            Spacer()
                            Text(locationManager.diagnoseLocationSource())
                                .foregroundColor(.orange)
                        }
                        
                        if let location = locationManager.userLocation {
                            HStack {
                                Text("当前坐标:")
                                Spacer()
                                Text(String(format: "%.6f, %.6f", location.latitude, location.longitude))
                                    .font(.system(.caption, design: .monospaced))
                                    .foregroundColor(.primary)
                            }
                            
                            // 检查是否是澳洲硬编码坐标
                            if isAustraliaHardcodedLocation(location) {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.red)
                                    Text("检测到澳洲硬编码坐标！")
                                        .foregroundColor(.red)
                                        .fontWeight(.medium)
                                }
                            } else {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                    Text("坐标正常")
                                        .foregroundColor(.green)
                                }
                            }
                        } else {
                            HStack {
                                Text("当前坐标:")
                                Spacer()
                                Text("未获取")
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    .font(.system(size: 14))
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                
                // 测试按钮
                VStack(spacing: 12) {
                    Button("运行完整测试") {
                        runFullTest()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(isRunningTest)
                    
                    HStack(spacing: 12) {
                        Button("重新获取GPS") {
                            locationManager.forceRefreshRealLocation()
                            addLog("已请求重新获取GPS位置")
                        }
                        .buttonStyle(.bordered)
                        
                        Button("重置设置") {
                            locationManager.resetRegionSettings()
                            addLog("位置设置已重置")
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.orange)
                    }
                    
                    Button("清除日志") {
                        testLog.removeAll()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
                
                // 测试日志
                if !testLog.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("测试日志")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 2) {
                                ForEach(Array(testLog.enumerated()), id: \.offset) { index, log in
                                    Text(log)
                                        .font(.system(size: 11, design: .monospaced))
                                        .foregroundColor(.secondary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                            }
                            .padding(.horizontal, 8)
                        }
                        .frame(maxHeight: 200)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("位置修复测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 计算属性
    
    private var deviceRegionCode: String {
        if #available(iOS 16, *) {
            return Locale.current.region?.identifier ?? "未知"
        } else {
            return Locale.current.regionCode ?? "未知"
        }
    }
    
    private var authorizationStatusText: String {
        switch locationManager.authorizationStatus {
        case .notDetermined: return "未确定"
        case .denied: return "已拒绝"
        case .restricted: return "受限制"
        case .authorizedWhenInUse: return "使用时允许"
        case .authorizedAlways: return "始终允许"
        @unknown default: return "未知"
        }
    }
    
    private var authorizationStatusColor: Color {
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways: return .green
        case .denied, .restricted: return .red
        case .notDetermined: return .orange
        @unknown default: return .gray
        }
    }
    
    // MARK: - 方法
    
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testLog.append("[\(timestamp)] \(message)")
    }
    
    private func isAustraliaHardcodedLocation(_ location: CLLocationCoordinate2D) -> Bool {
        // 检查是否是Glen Waverley, Melbourne的硬编码坐标
        return abs(location.latitude - (-37.90219)) < 0.001 && 
               abs(location.longitude - 145.16034) < 0.001
    }
    
    private func runFullTest() {
        isRunningTest = true
        addLog("=== 开始位置硬编码问题修复验证 ===")
        
        // 1. 检查设备区域
        let deviceRegion = deviceRegionCode
        addLog("1. 设备区域: \(deviceRegion)")
        
        // 2. 检查LocationManager设置
        let selectedRegion = locationManager.getCurrentRegion()
        addLog("2. LocationManager选择区域: \(selectedRegion)")
        
        // 3. 检查位置权限
        addLog("3. 位置权限: \(authorizationStatusText)")
        
        // 4. 检查当前位置
        if let location = locationManager.userLocation {
            addLog("4. 当前坐标: \(String(format: "%.6f, %.6f", location.latitude, location.longitude))")
            
            // 5. 检查是否是硬编码坐标
            if isAustraliaHardcodedLocation(location) {
                addLog("❌ 检测到澳洲硬编码坐标 (-37.90219, 145.16034)")
                
                if deviceRegion.lowercased() != "au" {
                    addLog("❌ 问题确认: 设备区域(\(deviceRegion))不是澳洲但使用了澳洲坐标")
                    addLog("🔧 建议: 需要修复位置检测逻辑")
                } else {
                    addLog("✅ 设备在澳洲，使用澳洲坐标是正确的")
                }
            } else {
                addLog("✅ 坐标正常，不是硬编码的澳洲坐标")
            }
        } else {
            addLog("4. ❌ 无法获取当前位置")
        }
        
        // 6. 检查位置来源
        let locationSource = locationManager.diagnoseLocationSource()
        addLog("5. 位置来源: \(locationSource)")
        
        // 7. 检查强制默认位置标志
        let forceDefault = UserDefaults.standard.bool(forKey: "ForceUseDefaultLocation")
        addLog("6. 强制默认位置标志: \(forceDefault)")
        
        // 8. 检查区域映射
        if deviceRegion.lowercased() != selectedRegion.lowercased() {
            addLog("⚠️ 设备区域(\(deviceRegion))与选择区域(\(selectedRegion))不匹配")
        } else {
            addLog("✅ 设备区域与选择区域匹配")
        }
        
        addLog("=== 测试完成 ===")
        isRunningTest = false
    }
}

#Preview("LocationFixTestView") {
    LocationFixTestView()
}
