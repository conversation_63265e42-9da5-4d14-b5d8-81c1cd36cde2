import SwiftUI

/// 日志配置管理界面
/// 让用户可以方便地控制哪些日志要输出，避免不必要的日志spam
struct LoggerConfigView: View {
    @ObservedObject private var config = LoggerConfig.shared
    @State private var newSilentKeyword = ""
    @State private var showingAddKeyword = false

    var body: some View {
        NavigationView {
            Form {
                quickControlsSection
                logTypesSection
                silentKeywordsSection
                presetConfigsSection
                performanceConfigSection
                statusSection
            }
            .navigationTitle("日志配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        config.saveConfiguration()
                    }
                }
            }
        }
        .alert("添加静音关键词", isPresented: $showingAddKeyword) {
            TextField("关键词", text: $newSilentKeyword)
            Button("添加") {
                if !newSilentKeyword.isEmpty {
                    config.addSilentKeyword(newSilentKeyword)
                    config.saveConfiguration()
                    newSilentKeyword = ""
                }
            }
            Button("取消", role: .cancel) {
                newSilentKeyword = ""
            }
        } message: {
            Text("输入要静音的日志关键词，包含此关键词的日志将不会输出")
        }
    }

    // MARK: - 视图组件

    private var quickControlsSection: some View {
        Section("🚀 快速控制") {
            Toggle("速率限制器日志", isOn: Binding(
                get: { config.isRateLimiterLoggingEnabled },
                set: { enabled in
                    if enabled {
                        config.enableRateLimiterLogs()
                    } else {
                        config.disableRateLimiterLogs()
                    }
                }
            ))

            Toggle("地理编码详细日志", isOn: Binding(
                get: { config.isGeocodingDetailLoggingEnabled },
                set: { enabled in
                    if enabled {
                        config.enableGeocodingDetailLogs()
                    } else {
                        config.disableGeocodingDetailLogs()
                    }
                }
            ))

            Toggle("AI识别详细日志", isOn: $config.isAIDetailLoggingEnabled)
            Toggle("OCR详细日志", isOn: $config.isOCRDetailLoggingEnabled)
            Toggle("图片处理详细日志", isOn: $config.isImageProcessingDetailLoggingEnabled)
        }
    }

    private var logTypesSection: some View {
        Section("📝 日志类型控制") {
            ForEach(LogType.allCases, id: \.self) { logType in
                Toggle(logType.displayName, isOn: Binding(
                    get: { config.enabledLogTypes.contains(logType) },
                    set: { enabled in
                        if enabled {
                            config.enableLogType(logType)
                        } else {
                            config.disableLogType(logType)
                        }
                    }
                ))
            }
        }
    }

    private var silentKeywordsSection: some View {
        Section("🔇 静音关键词") {
            ForEach(Array(config.silentKeywords), id: \.self) { keyword in
                HStack {
                    Text(keyword)
                    Spacer()
                    Button("删除") {
                        config.removeSilentKeyword(keyword)
                        config.saveConfiguration()
                    }
                    .foregroundColor(.red)
                }
            }

            Button("添加关键词") {
                showingAddKeyword = true
            }
            .foregroundColor(.blue)
        }
    }

    private var presetConfigsSection: some View {
        Section("⚙️ 预设配置") {
            Button("🔇 静音模式（只显示错误）") {
                config.disabledLogTypes = [.debug, .info, .warning, .action, .data, .network, .lifecycle, .location, .validation, .auth, .system, .ai, .ocr, .imageProcessing]
                config.enabledLogTypes = [.error]
                config.saveConfiguration()
            }

            Button("🔍 调试模式（显示所有日志）") {
                config.enabledLogTypes = Set(LogType.allCases)
                config.disabledLogTypes = []
                config.isRateLimiterLoggingEnabled = true
                config.isGeocodingDetailLoggingEnabled = true
                config.saveConfiguration()
            }

            Button("⚖️ 平衡模式（推荐设置）") {
                config.resetToDefaults()
                config.saveConfiguration()
            }

            Button("🚀 性能模式（最少日志）") {
                config.disabledLogTypes = [.debug, .info, .location]
                config.enabledLogTypes = [.warning, .error, .ai, .ocr]
                config.isRateLimiterLoggingEnabled = false
                config.isGeocodingDetailLoggingEnabled = false
                config.saveConfiguration()
            }
        }
    }

    private var performanceConfigSection: some View {
        Section("🚀 性能优化配置") {
            VStack(alignment: .leading, spacing: 12) {
                // 标记渲染模式
                VStack(alignment: .leading, spacing: 8) {
                    Text("地图标记渲染模式")
                        .font(.headline)

                    Picker("渲染模式", selection: $config.markerRenderMode) {
                        Text("🎯 Rectangle.Fill (清晰)").tag(MarkerRenderMode.numbersRectangle)
                        Text("🚀 Drop模式 (高性能)").tag(MarkerRenderMode.drop)
                        Text("📦 矩形模式 (传统)").tag(MarkerRenderMode.rectangle)
                    }
                    .pickerStyle(MenuPickerStyle())

                    Text("Rectangle.Fill模式可显示任意数字，第三方号码会显示为square.badge.plus图标")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // GPU加速
                Toggle("启用GPU硬件加速", isOn: $config.isGPUAccelerationEnabled)

                // 标记优化
                Toggle("启用标记渲染优化", isOn: $config.isMarkerOptimizationEnabled)

                // 性能对比按钮
                NavigationLink("查看性能对比演示") {
                    MarkerPerformanceComparisonView()
                }
                .foregroundColor(.blue)
            }
        }
    }

    private var statusSection: some View {
        Section("📊 当前状态") {
            HStack {
                Text("启用的日志类型")
                Spacer()
                Text("\(config.enabledLogTypes.count)/\(LogType.allCases.count)")
                    .foregroundColor(.secondary)
            }

            HStack {
                Text("静音关键词")
                Spacer()
                Text("\(config.silentKeywords.count)")
                    .foregroundColor(.secondary)
            }

            HStack {
                Text("静音文件")
                Spacer()
                Text("\(config.silentFiles.count)")
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - LogType扩展

extension LogType: CaseIterable {
    public static var allCases: [LogType] {
        return [.info, .debug, .warning, .error, .action, .data, .network, .lifecycle, .location, .validation, .auth, .system, .ai, .ocr, .imageProcessing]
    }

    var displayName: String {
        switch self {
        case .info: return "ℹ️ 信息"
        case .debug: return "🔍 调试"
        case .warning: return "⚠️ 警告"
        case .error: return "❌ 错误"
        case .action: return "👆 操作"
        case .data: return "💾 数据"
        case .network: return "🌐 网络"
        case .lifecycle: return "♻️ 生命周期"
        case .location: return "📍 位置"
        case .validation: return "✅ 验证"
        case .auth: return "🔐 认证"
        case .system: return "⚙️ 系统"
        case .ai: return "🤖 AI"
        case .ocr: return "📷 OCR"
        case .imageProcessing: return "🖼️ 图片处理"
        }
    }
}

#Preview {
    LoggerConfigView()
}
