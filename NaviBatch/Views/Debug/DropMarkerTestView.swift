import SwiftUI

/// Drop标记测试视图
/// 用于验证新的Drop标记渲染是否正常工作
struct DropMarkerTestView: View {
    @State private var selectedMode: MarkerRenderMode = .drop
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 模式选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("渲染模式")
                        .font(.headline)
                    
                    Picker("渲染模式", selection: $selectedMode) {
                        Text("🚀 Drop模式").tag(MarkerRenderMode.drop)
                        Text("📦 矩形模式").tag(MarkerRenderMode.rectangle)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                // 测试标记网格
                ScrollView {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 16) {
                        // 基本数字标记
                        ForEach(1...20, id: \.self) { number in
                            VStack(spacing: 4) {
                                MarkerView(
                                    number: number,
                                    packageCount: 1,
                                    color: .blue,
                                    isAssignedToGroup: false,
                                    groupNumber: nil,
                                    shouldFade: false,
                                    renderMode: selectedMode
                                )
                                Text("\(number)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // 特殊状态标记
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 21,
                                packageCount: 1,
                                color: .green,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                isCompleted: true,
                                renderMode: selectedMode
                            )
                            Text("完成")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 22,
                                packageCount: 1,
                                color: .red,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                isFailed: true,
                                renderMode: selectedMode
                            )
                            Text("失败")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 23,
                                packageCount: 1,
                                color: .purple,
                                isAssignedToGroup: true,
                                groupNumber: 3,
                                shouldFade: false,
                                renderMode: selectedMode
                            )
                            Text("分组")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 24,
                                packageCount: 1,
                                color: .orange,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                customText: "SF",
                                renderMode: selectedMode
                            )
                            Text("自定义")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // 起点和终点
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 0,
                                packageCount: 1,
                                color: .blue,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                pointType: .start,
                                renderMode: selectedMode
                            )
                            Text("起点")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(spacing: 4) {
                            MarkerView(
                                number: -1,
                                packageCount: 1,
                                color: .green,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                pointType: .end,
                                renderMode: selectedMode
                            )
                            Text("终点")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // 警告状态
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 25,
                                packageCount: 1,
                                color: .orange,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: false,
                                hasCoordinateWarning: true,
                                renderMode: selectedMode
                            )
                            Text("警告")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // 淡化状态
                        VStack(spacing: 4) {
                            MarkerView(
                                number: 26,
                                packageCount: 1,
                                color: .gray,
                                isAssignedToGroup: false,
                                groupNumber: nil,
                                shouldFade: true,
                                renderMode: selectedMode
                            )
                            Text("淡化")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                }
                
                // 性能信息
                VStack(alignment: .leading, spacing: 8) {
                    Text("性能对比")
                        .font(.headline)
                    
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Drop模式:")
                                .fontWeight(.semibold)
                            Text("• 轮廓设计，尖端朝下")
                            Text("• 大容器+小字体=完整显示")
                            Text("• 边框灰色+数字状态色")
                            Text("• GPU硬件加速")
                            Text("• 减少90%渲染消耗")
                        }
                        .font(.caption)
                        
                        Spacer()
                        
                        VStack(alignment: .leading) {
                            Text("矩形模式:")
                                .fontWeight(.semibold)
                            Text("• 多个组合视图")
                            Text("• 传统渲染方式")
                            Text("• 兼容性更好")
                        }
                        .font(.caption)
                    }
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Drop标记测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview("Drop标记测试") {
    DropMarkerTestView()
}
