import SwiftUI

/// 第三方标记测试视图 - 展示第三方号码和普通号码的视觉区别
struct ThirdPartyMarkerTestView: View {
    @State private var selectedMode: MarkerRenderMode = .numbersRectangle
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 渲染模式选择
                    VStack(alignment: .leading, spacing: 8) {
                        Text("渲染模式")
                            .font(.headline)
                        
                        Picker("渲染模式", selection: $selectedMode) {
                            Text("🎯 Rectangle.Fill (清晰)").tag(MarkerRenderMode.numbersRectangle)
                            Text("🚀 Drop模式 (高性能)").tag(MarkerRenderMode.drop)
                            Text("📦 矩形模式 (传统)").tag(MarkerRenderMode.rectangle)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    
                    // 标记对比展示
                    VStack(alignment: .leading, spacing: 16) {
                        Text("标记对比")
                            .font(.headline)
                        
                        // 普通号码 vs 第三方号码
                        HStack(spacing: 40) {
                            VStack(spacing: 12) {
                                Text("普通号码")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                HStack(spacing: 16) {
                                    ForEach(1...3, id: \.self) { number in
                                        MarkerView(
                                            number: number,
                                            packageCount: 1,
                                            color: .blue,
                                            isAssignedToGroup: false,
                                            groupNumber: nil,
                                            shouldFade: false,
                                            renderMode: selectedMode
                                        )
                                    }
                                }
                            }
                            
                            VStack(spacing: 12) {
                                Text("第三方号码")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                HStack(spacing: 16) {
                                    ForEach(["A1", "B2", "C3"], id: \.self) { thirdPartyNumber in
                                        MarkerView(
                                            number: 0, // 这个会被customText覆盖
                                            packageCount: 1,
                                            color: .green,
                                            isAssignedToGroup: false,
                                            groupNumber: nil,
                                            shouldFade: false,
                                            customText: thirdPartyNumber, // 第三方号码
                                            renderMode: selectedMode
                                        )
                                    }
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    
                    // 说明文字
                    VStack(alignment: .leading, spacing: 8) {
                        Text("视觉区别说明")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: "rectangle.fill")
                                    .foregroundColor(.blue)
                                Text("普通号码使用 rectangle.fill 图标，可显示任意数字")
                                    .font(.caption)
                            }

                            HStack {
                                Image(systemName: "square.badge.plus")
                                    .foregroundColor(.green)
                                Text("第三方号码使用 square.badge.plus 图标，带+标识")
                                    .font(.caption)
                            }
                        }
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("第三方标记测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ThirdPartyMarkerTestView()
}
