import SwiftUI

/// 分组详情界面 Dark Mode 优化对比预览
/// 展示分组详情界面在 Dark Mode 下的优化效果
struct GroupDetailDarkModePreview: View {
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签切换器
                Picker("版本", selection: $selectedTab) {
                    Text("优化前").tag(0)
                    Text("优化后").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    // 优化前版本
                    oldVersionView
                        .tag(0)
                    
                    // 优化后版本
                    newVersionView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("分组详情 Dark Mode 对比")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    // MARK: - 优化前版本
    private var oldVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化前 - 存在的问题")
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding()
                
                // 分组详情界面 - 优化前
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("分组 1")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Button("编辑") {}
                            .foregroundColor(.blue)
                    }
                    .padding()
                    
                    Divider()
                    
                    // 地址列表
                    VStack(spacing: 8) {
                        ForEach(1...4, id: \.self) { index in
                            HStack {
                                // 序号圆圈 - 优化前（固定灰色）
                                Text("\(index)")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .frame(width: 24, height: 24)
                                    .background(Circle().fill(Color.gray))
                                
                                // 地址信息
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("342\(index) Tupelo Drive")
                                        .font(.subheadline)
                                        .foregroundColor(.primary)
                                    
                                    Text("95209")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                            }
                            .padding(.vertical, 4)
                            .padding(.horizontal)
                        }
                    }
                    
                    // 底部按钮
                    Button(action: {}) {
                        HStack {
                            Image(systemName: "location.fill")
                            Text("导航到这些点")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .padding()
                }
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 问题说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("❌ 存在的问题：")
                        .font(.subheadline.bold())
                        .foregroundColor(.red)
                    
                    Text("• 缺少第三方排序标签（如 GoFo: 4）")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 序号圆圈颜色单调，无状态区分")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• 缺少组内路线优化功能")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• Dark Mode 下颜色对比度不足")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 优化后版本
    private var newVersionView: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("优化后 - 改进效果")
                    .font(.headline)
                    .foregroundColor(.green)
                    .padding()
                
                // 分组详情界面 - 优化后
                VStack(spacing: 0) {
                    // 标题栏 - Dark Mode 优化
                    HStack {
                        Text("分组 1")
                            .font(.headline)
                            .foregroundColor(.adaptivePrimaryText)
                        
                        Spacer()
                        
                        Button("编辑") {}
                            .foregroundColor(.adaptivePrimaryIcon)
                    }
                    .padding()
                    
                    Divider()
                    
                    // 地址列表 - 增强版
                    VStack(spacing: 12) {
                        ForEach(1...4, id: \.self) { index in
                            HStack(spacing: 12) {
                                // 序号圆圈 - 根据状态显示不同颜色
                                Text("\(index)")
                                    .font(.system(size: 14, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(width: 28, height: 28)
                                    .background(statusColor(for: index))
                                    .cornerRadius(6)
                                
                                // 地址信息区域
                                VStack(alignment: .leading, spacing: 4) {
                                    // 第三方排序标签
                                    if index <= 2 {
                                        Text(appLabel(for: index))
                                            .font(.system(size: 12, weight: .bold))
                                            .foregroundColor(.white)
                                            .padding(.horizontal, 8)
                                            .padding(.vertical, 4)
                                            .background(appColor(for: index))
                                            .cornerRadius(6)
                                    }
                                    
                                    // 地址文字
                                    Text("342\(index) Tupelo Drive")
                                        .font(.subheadline)
                                        .foregroundColor(.adaptivePrimaryText)
                                    
                                    Text("95209")
                                        .font(.caption)
                                        .foregroundColor(.adaptiveSecondaryText)
                                }
                                
                                Spacer()
                            }
                            .padding(.vertical, 6)
                            .padding(.horizontal, 4)
                        }
                    }
                    .padding(.horizontal)
                    
                    // 底部按钮区域 - 增强版
                    VStack(spacing: 12) {
                        // 路线优化按钮
                        Button(action: {}) {
                            HStack {
                                Image(systemName: "arrow.triangle.swap")
                                Text("优化组内路线")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.adaptiveWarning)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        
                        // 导航按钮
                        Button(action: {}) {
                            HStack {
                                Image(systemName: "location.fill")
                                Text("导航到这些点")
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.adaptivePrimaryIcon)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                    }
                    .padding()
                }
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 改进说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("✅ 改进效果：")
                        .font(.subheadline.bold())
                        .foregroundColor(.green)
                    
                    Text("• 添加第三方排序标签（GoFo、Amazon 等）")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 序号圆圈根据状态显示不同颜色")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 新增组内路线优化功能")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• Dark Mode 下所有元素清晰可见")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 司机可针对单个分组优化路线")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveSuccess.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 功能说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("🚀 新功能说明：")
                        .font(.subheadline.bold())
                        .foregroundColor(.adaptivePrimaryText)
                    
                    Text("• 第三方标签：显示 GoFo: 4、Amazon: 2 等")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 状态颜色：绿色(已完成)、红色(失败)、橙色(警告)、蓝色(正常)")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 路线优化：司机可对单个分组进行路线优化")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                    
                    Text("• 拖拽排序：支持手动调整地址顺序")
                        .font(.caption)
                        .foregroundColor(.adaptiveSecondaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.adaptiveCardBackground)
                .cornerRadius(12)
                .padding(.horizontal)
            }
            .padding()
        }
        .background(Color.adaptiveBackground)
    }
    
    // MARK: - 辅助方法
    private func statusColor(for index: Int) -> Color {
        switch index {
        case 1:
            return Color.adaptiveSuccess // 已完成
        case 2:
            return Color.adaptiveError   // 失败
        case 3:
            return Color.adaptiveWarning // 警告
        default:
            return Color.adaptivePrimaryIcon // 正常
        }
    }
    
    private func appLabel(for index: Int) -> String {
        switch index {
        case 1:
            return "GoFo: 4"
        case 2:
            return "Amazon: 2"
        default:
            return "iMile: \(index)"
        }
    }
    
    private func appColor(for index: Int) -> Color {
        switch index {
        case 1:
            return Color.adaptiveGoFo
        case 2:
            return Color.adaptiveAmazonFlex
        default:
            return Color.adaptivePrimaryIcon
        }
    }
}

#Preview("Group Detail Dark Mode - Light") {
    GroupDetailDarkModePreview()
        .preferredColorScheme(.light)
}

#Preview("Group Detail Dark Mode - Dark") {
    GroupDetailDarkModePreview()
        .preferredColorScheme(.dark)
}
