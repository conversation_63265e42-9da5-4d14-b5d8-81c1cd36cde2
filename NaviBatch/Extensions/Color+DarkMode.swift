import SwiftUI

/// Dark Mode 优化的颜色扩展
/// 为司机用户优化的深色主题颜色方案
extension Color {

    // MARK: - Dark Mode 优化背景色

    /// 主要背景色 - 针对 Dark Mode 优化
    static var adaptiveBackground: Color {
        Color(.systemBackground)
    }

    /// 次要背景色 - 更好的层次感
    static var adaptiveSecondaryBackground: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.11, green: 0.11, blue: 0.12, alpha: 1.0) // 更深的灰色
            default:
                return UIColor.systemGray6
            }
        })
    }

    /// 输入框背景色 - 增强对比度
    static var adaptiveInputBackground: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.15, green: 0.15, blue: 0.16, alpha: 1.0) // 更明显的输入框
            default:
                return UIColor.systemGray5
            }
        })
    }

    /// 卡片背景色 - 更好的层次感
    static var adaptiveCardBackground: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.13, green: 0.13, blue: 0.14, alpha: 1.0) // 卡片背景
            default:
                return UIColor.systemBackground
            }
        })
    }

    // MARK: - Dark Mode 优化按钮色

    /// 主要按钮背景色 - 更突出的蓝色
    static var adaptivePrimaryButton: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0) // 更亮的蓝色
            default:
                return UIColor.systemBlue
            }
        })
    }

    /// 次要按钮背景色 - 更好的可见性
    static var adaptiveSecondaryButton: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.17, green: 0.17, blue: 0.18, alpha: 1.0) // 更明显的次要按钮
            default:
                return UIColor.systemGray6
            }
        })
    }

    /// 功能按钮背景色 - 标签按钮专用
    static var adaptiveFunctionButton: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.19, green: 0.19, blue: 0.20, alpha: 1.0) // 功能按钮背景
            default:
                return UIColor.systemGray6
            }
        })
    }

    // MARK: - Dark Mode 优化文字色

    /// 主要文字颜色 - 确保可读性
    static var adaptivePrimaryText: Color {
        Color(.label)
    }

    /// 次要文字颜色 - 更好的层次感
    static var adaptiveSecondaryText: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.78, green: 0.78, blue: 0.80, alpha: 1.0) // 更明亮的次要文字
            default:
                return UIColor.secondaryLabel
            }
        })
    }

    /// 占位符文字颜色
    static var adaptivePlaceholderText: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.55, green: 0.55, blue: 0.58, alpha: 1.0) // 更明显的占位符
            default:
                return UIColor.placeholderText
            }
        })
    }

    // MARK: - Dark Mode 优化边框色

    /// 分隔线颜色 - 更好的可见性
    static var adaptiveSeparator: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.23, green: 0.23, blue: 0.25, alpha: 1.0) // 更明显的分隔线
            default:
                return UIColor.separator
            }
        })
    }

    /// 边框颜色
    static var adaptiveBorder: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.25, green: 0.25, blue: 0.27, alpha: 1.0) // 边框颜色
            default:
                return UIColor.systemGray4
            }
        })
    }

    // MARK: - Dark Mode 优化状态色

    /// 成功状态色 - 更亮的绿色
    static var adaptiveSuccess: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.20, green: 0.78, blue: 0.35, alpha: 1.0) // 更亮的绿色
            default:
                return UIColor.systemGreen
            }
        })
    }

    /// 警告状态色 - 更亮的橙色
    static var adaptiveWarning: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 1.0, green: 0.62, blue: 0.04, alpha: 1.0) // 更亮的橙色
            default:
                return UIColor.systemOrange
            }
        })
    }

    /// 错误状态色 - 更亮的红色
    static var adaptiveError: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 1.0, green: 0.27, blue: 0.23, alpha: 1.0) // 更亮的红色
            default:
                return UIColor.systemRed
            }
        })
    }

    // MARK: - Dark Mode 优化图标色

    /// 主要图标颜色
    static var adaptivePrimaryIcon: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0) // 更亮的蓝色图标
            default:
                return UIColor.systemBlue
            }
        })
    }

    /// 次要图标颜色
    static var adaptiveSecondaryIcon: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.68, green: 0.68, blue: 0.70, alpha: 1.0) // 更明亮的次要图标
            default:
                return UIColor.secondaryLabel
            }
        })
    }

    // MARK: - 配送应用专用自适应颜色

    /// GoFo 应用自适应颜色 - 黄色系
    static var adaptiveGoFo: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.9, green: 0.7, blue: 0.1, alpha: 1.0) // Dark Mode: 深金色，更易读
            default:
                return UIColor.systemYellow // Light Mode: 标准黄色
            }
        })
    }

    /// Amazon Flex 应用自适应颜色 - 橙色系
    static var adaptiveAmazonFlex: Color {
        Color(UIColor { traitCollection in
            switch traitCollection.userInterfaceStyle {
            case .dark:
                return UIColor(red: 0.9, green: 0.5, blue: 0.1, alpha: 1.0) // Dark Mode: 稍深的橙色
            default:
                return UIColor.systemOrange // Light Mode: 标准橙色
            }
        })
    }
}

// MARK: - Dark Mode 优化的视图修饰符
extension View {

    /// 应用 Dark Mode 优化的卡片样式
    func adaptiveCardStyle() -> some View {
        self
            .background(Color.adaptiveCardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.adaptiveBorder, lineWidth: 0.5)
            )
    }

    /// 应用 Dark Mode 优化的输入框样式
    func adaptiveInputStyle() -> some View {
        self
            .background(Color.adaptiveInputBackground)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.adaptiveBorder, lineWidth: 0.5)
            )
    }

    /// 应用 Dark Mode 优化的按钮样式
    func adaptiveButtonStyle(isPrimary: Bool = false) -> some View {
        self
            .background(isPrimary ? Color.adaptivePrimaryButton : Color.adaptiveSecondaryButton)
            .cornerRadius(10)
    }

    /// 应用 Dark Mode 优化的功能按钮样式 - 黑底白字风格
    func adaptiveFunctionButtonStyle() -> some View {
        self
            .foregroundColor(.white)
            .background(Color.black)
            .cornerRadius(15)
            .overlay(
                RoundedRectangle(cornerRadius: 15)
                    .stroke(Color.black, lineWidth: 0.5)
            )
    }
}
