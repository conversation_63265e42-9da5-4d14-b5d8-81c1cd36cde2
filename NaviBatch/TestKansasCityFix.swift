import Foundation
import MapKit

/// 测试堪萨斯城地址搜索修复的演示类
class TestKansasCityFix {
    
    /// 测试地址搜索功能
    static func testAddressSearch() {
        print("🧪 开始测试堪萨斯城地址搜索修复...")
        
        let testAddress = "9536 Drury Ave 107, 64137"
        print("🧪 测试地址: '\(testAddress)'")
        
        // 创建搜索请求
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = testAddress
        request.region = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 39.0997, longitude: -94.5786), // Kansas City坐标
            span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
        )
        
        let search = MKLocalSearch(request: request)
        
        search.start { response, error in
            if let error = error {
                print("❌ 搜索失败: \(error.localizedDescription)")
                return
            }
            
            guard let response = response else {
                print("❌ 没有搜索结果")
                return
            }
            
            print("✅ 找到 \(response.mapItems.count) 个结果:")
            
            for (index, item) in response.mapItems.enumerated() {
                let placemark = item.placemark
                print("📍 结果 \(index + 1):")
                print("   名称: \(item.name ?? "未知")")
                print("   地址: \(placemark.title ?? "未知地址")")
                
                if let locality = placemark.locality {
                    print("   城市: \(locality)")
                    
                    // 检查是否包含中文字符
                    let hasChinese = locality.range(of: "\\p{Script=Han}", options: .regularExpression) != nil
                    if hasChinese {
                        print("   ⚠️  城市名包含中文字符")
                    } else {
                        print("   ✅ 城市名为英文")
                    }
                }
                
                if let administrativeArea = placemark.administrativeArea {
                    print("   州: \(administrativeArea)")
                }
                
                if let country = placemark.country {
                    print("   国家: \(country)")
                    
                    // 检查是否包含中文字符
                    let hasChinese = country.range(of: "\\p{Script=Han}", options: .regularExpression) != nil
                    if hasChinese {
                        print("   ⚠️  国家名包含中文字符")
                    } else {
                        print("   ✅ 国家名为英文")
                    }
                }
                
                print("   坐标: \(placemark.coordinate.latitude), \(placemark.coordinate.longitude)")
                print("")
            }
            
            // 测试城市名映射
            testCityNameMapping()
        }
    }
    
    /// 测试城市名映射功能
    static func testCityNameMapping() {
        print("🧪 测试城市名映射功能...")
        
        let cityMappings = [
            "堪萨斯城": "Kansas City",
            "堪薩斯城": "Kansas City",  // 繁体
            "旧金山": "San Francisco",
            "纽约": "New York"
        ]
        
        for (chinese, expectedEnglish) in cityMappings {
            // 检查中文字符检测
            let hasChinese = chinese.range(of: "\\p{Script=Han}", options: .regularExpression) != nil
            print("📝 '\(chinese)' -> 期望: '\(expectedEnglish)'")
            print("   包含中文字符: \(hasChinese ? "是" : "否")")
            
            if hasChinese {
                print("   ✅ 正确检测到中文字符，应该进行转换")
            } else {
                print("   ❌ 未检测到中文字符")
            }
        }
        
        print("🧪 城市名映射测试完成")
    }
    
    /// 测试完整地址格式
    static func testFullAddressFormat() {
        print("🧪 测试完整地址格式...")
        
        let testCases = [
            ("9536 Drury Ave，堪萨斯城, MO 64130，美国", true),  // 包含中文
            ("9536 Drury Ave, Kansas City, MO 64130, United States", false)  // 纯英文
        ]
        
        for (address, shouldHaveChinese) in testCases {
            let hasChinese = address.range(of: "\\p{Script=Han}", options: .regularExpression) != nil
            print("📝 地址: '\(address)'")
            print("   包含中文字符: \(hasChinese ? "是" : "否")")
            print("   预期包含中文: \(shouldHaveChinese ? "是" : "否")")
            
            if hasChinese == shouldHaveChinese {
                print("   ✅ 检测结果正确")
            } else {
                print("   ❌ 检测结果错误")
            }
            print("")
        }
        
        print("🧪 完整地址格式测试完成")
    }
}
