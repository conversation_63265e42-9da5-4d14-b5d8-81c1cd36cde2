import Foundation
import CoreLocation
import os.log

/// 本地距离计算工具类
/// 使用数学公式计算距离，不依赖任何API
class LocalDistanceCalculator {
    static let shared = LocalDistanceCalculator()
    
    private init() {}
    
    // MARK: - 距离计算

    // 已移除直线距离计算方法
    // 司机需要的是真实道路距离，不是直线距离

    /// 计算真实道路距离（异步）
    /// - Parameters:
    ///   - from: 起点坐标
    ///   - to: 终点坐标
    ///   - completion: 完成回调，返回真实距离或错误
    func calculateRealDistance(
        from: CLLocationCoordinate2D,
        to: CLLocationCoordinate2D,
        completion: @escaping (Result<CLLocationDistance, Error>) -> Void
    ) {
        DirectionsAPIManager.shared.calculateDistance(from: from, to: to, completion: completion)
    }

    /// 计算多点路线的真实道路总距离（异步）
    /// - Parameters:
    ///   - coordinates: 坐标点数组
    ///   - completion: 完成回调，返回总距离或错误
    func calculateTotalRealDistance(
        coordinates: [CLLocationCoordinate2D],
        completion: @escaping (Result<CLLocationDistance, Error>) -> Void
    ) {
        guard coordinates.count >= 2 else {
            completion(.success(0))
            return
        }

        print("🔍 LocalDistanceCalculator - 即将调用DirectionsAPIManager.calculateMultipleDistances")
        DirectionsAPIManager.shared.calculateMultipleDistances(waypoints: coordinates) { result in
            switch result {
            case .success(let distances):
                // 🔍 调试：打印接收到的距离数组
                print("🔍 LocalDistanceCalculator - 接收到距离数组: \(distances)")
                for (index, distance) in distances.enumerated() {
                    print("🔍 LocalDistanceCalculator - 距离段 \(index + 1): \(distance) 米 (\(distance/1000) 公里)")
                }

                let totalDistance = distances.reduce(0, +)
                print("🔍 LocalDistanceCalculator - 总距离: \(totalDistance) 米 (\(totalDistance/1000) 公里)")
                completion(.success(totalDistance))
            case .failure(let error):
                print("🔍 LocalDistanceCalculator - 距离计算失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }

    /// 计算配送点之间的真实道路总距离（异步）
    /// - Parameters:
    ///   - points: 配送点数组
    ///   - completion: 完成回调，返回总距离或错误
    func calculateTotalRealDistance(
        points: [DeliveryPoint],
        completion: @escaping (Result<CLLocationDistance, Error>) -> Void
    ) {
        let coordinates = points.map { $0.coordinate }
        calculateTotalRealDistance(coordinates: coordinates, completion: completion)
    }
    
    // 已移除所有直线距离计算方法
    // 现在只提供真实道路距离计算
    
    // MARK: - 路线优化相关
    
    /// 计算路线优化前后的距离对比（异步版本，使用真实道路距离）
    /// - Parameters:
    ///   - originalPoints: 原始点顺序
    ///   - optimizedPoints: 优化后点顺序
    ///   - driverPosition: 司机位置（可选）
    ///   - completion: 完成回调，返回(优化前距离, 优化后距离)
    func calculateOptimizationComparison(
        originalPoints: [DeliveryPoint],
        optimizedPoints: [DeliveryPoint],
        driverPosition: CLLocationCoordinate2D? = nil,
        completion: @escaping (Result<(before: CLLocationDistance, after: CLLocationDistance), Error>) -> Void
    ) {
        let group = DispatchGroup()
        var beforeDistance: CLLocationDistance = 0
        var afterDistance: CLLocationDistance = 0
        var hasError = false

        // 计算优化前距离
        group.enter()
        calculateTotalRealDistance(points: originalPoints) { result in
            switch result {
            case .success(let distance):
                beforeDistance = distance
            case .failure(_):
                hasError = true
            }
            group.leave()
        }

        // 计算优化后距离
        group.enter()
        calculateTotalRealDistance(points: optimizedPoints) { result in
            switch result {
            case .success(let distance):
                afterDistance = distance
            case .failure(_):
                hasError = true
            }
            group.leave()
        }

        group.notify(queue: .main) {
            if hasError {
                completion(.failure(NSError(domain: "LocalDistanceCalculator", code: -1, userInfo: [NSLocalizedDescriptionKey: "距离计算失败"])))
            } else {
                completion(.success((before: beforeDistance, after: afterDistance)))
            }
        }
    }
    
    // MARK: - 距离格式化
    
    /// 格式化距离显示
    /// - Parameter distance: 距离（米）
    /// - Returns: 格式化的距离字符串
    func formatDistance(_ distance: CLLocationDistance) -> String {
        return DistanceFormatter.shared.formatDistance(distance)
    }
    
    /// 估算行驶时间（基于平均速度）
    /// - Parameters:
    ///   - distance: 距离（米）
    ///   - averageSpeed: 平均速度（公里/小时），默认40km/h
    /// - Returns: 估算时间（秒）
    func estimateTravelTime(distance: CLLocationDistance, averageSpeed: Double = 40.0) -> TimeInterval {
        let distanceInKm = distance / 1000.0
        let timeInHours = distanceInKm / averageSpeed
        return timeInHours * 3600 // 转换为秒
    }
    
    /// 格式化时间显示
    /// - Parameter timeInterval: 时间间隔（秒）
    /// - Returns: 格式化的时间字符串
    func formatTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = (Int(timeInterval) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    // MARK: - 路线分析
    
    /// 分析路线效率（异步版本，使用真实道路距离）
    /// - Parameters:
    ///   - points: 配送点数组
    ///   - completion: 完成回调，返回路线分析结果
    func analyzeRoute(_ points: [DeliveryPoint], completion: @escaping (RouteAnalysis) -> Void) {
        guard points.count >= 2 else {
            let analysis = RouteAnalysis(
                totalDistance: 0,
                estimatedTime: 0,
                averageDistancePerStop: 0,
                efficiency: .unknown
            )
            completion(analysis)
            return
        }

        calculateTotalRealDistance(points: points) { [weak self] result in
            guard let self = self else { return }

            let totalDistance: CLLocationDistance
            let efficiency: RouteEfficiency

            switch result {
            case .success(let distance):
                totalDistance = distance
                let averageDistancePerStop = totalDistance / Double(points.count - 1)

                // 简单的效率评估
                if averageDistancePerStop < 2000 { // 平均每站小于2公里
                    efficiency = .excellent
                } else if averageDistancePerStop < 5000 { // 平均每站小于5公里
                    efficiency = .good
                } else if averageDistancePerStop < 10000 { // 平均每站小于10公里
                    efficiency = .fair
                } else {
                    efficiency = .poor
                }

            case .failure(_):
                totalDistance = 0
                efficiency = .unknown
            }

            let estimatedTime = self.estimateTravelTime(distance: totalDistance)
            let averageDistancePerStop = totalDistance > 0 ? totalDistance / Double(points.count - 1) : 0

            let analysis = RouteAnalysis(
                totalDistance: totalDistance,
                estimatedTime: estimatedTime,
                averageDistancePerStop: averageDistancePerStop,
                efficiency: efficiency
            )

            DispatchQueue.main.async {
                completion(analysis)
            }
        }
    }
    
    // MARK: - 日志
    private func logInfo(_ message: String) {
        Logger.info("[LocalDistanceCalculator] \(message)", type: .data)
    }
}

// MARK: - 支持类型

/// 路线效率等级
enum RouteEfficiency {
    case excellent  // 优秀
    case good      // 良好
    case fair      // 一般
    case poor      // 较差
    case unknown   // 未知
    
    var description: String {
        switch self {
        case .excellent: return "优秀"
        case .good: return "良好"
        case .fair: return "一般"
        case .poor: return "较差"
        case .unknown: return "未知"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .fair: return "orange"
        case .poor: return "red"
        case .unknown: return "gray"
        }
    }
}

/// 路线分析结果
struct RouteAnalysis {
    let totalDistance: CLLocationDistance      // 总距离
    let estimatedTime: TimeInterval           // 估算时间
    let averageDistancePerStop: CLLocationDistance // 平均每站距离
    let efficiency: RouteEfficiency          // 效率等级
    
    var formattedDistance: String {
        return LocalDistanceCalculator.shared.formatDistance(totalDistance)
    }
    
    var formattedTime: String {
        return LocalDistanceCalculator.shared.formatTime(estimatedTime)
    }
    
    var formattedAverageDistance: String {
        return LocalDistanceCalculator.shared.formatDistance(averageDistancePerStop)
    }
}
