import Foundation
import os.log

// 为RouteView提供的日志扩展
extension Logger {
    // 记录数据操作日志
    static func data(_ message: String) {
        info(message, type: .data)
    }

    // 记录用户操作日志
    static func action(_ message: String) {
        info(message, type: .action)
    }

    // 记录信息日志（特定类型）
    static func infoLog(_ message: String) {
        info(message, type: .info)
    }

    // 记录数据错误日志
    static func dataError(_ message: String) {
        error(message, type: .data)
    }

    // 记录操作错误日志
    static func actionError(_ message: String) {
        error(message, type: .action)
    }

    // 记录数据警告日志
    static func dataWarning(_ message: String) {
        warning(message, type: .data)
    }

    // 记录操作警告日志
    static func actionWarning(_ message: String) {
        warning(message, type: .action)
    }
}
