import Foundation
import MapKit
import CoreLocation

/// 🎯 模拟MKLocalSearchCompletion的类
/// 用于创建可修改的搜索结果，支持自定义title、subtitle和坐标信息
class MockLocalSearchCompletion: MKLocalSearchCompletion {
    private var _title: String = ""
    private var _subtitle: String = ""
    private var _coordinate: CLLocationCoordinate2D?

    override var title: String {
        get { return _title }
        set { _title = newValue }
    }

    override var subtitle: String {
        get { return _subtitle }
        set { _subtitle = newValue }
    }

    /// 🎯 存储坐标信息，避免重复地理编码
    var storedCoordinate: CLLocationCoordinate2D? {
        get { return _coordinate }
        set { _coordinate = newValue }
    }

    /// 🎯 检查是否有存储的坐标信息
    var hasStoredCoordinate: Bool {
        return _coordinate != nil
    }
}
