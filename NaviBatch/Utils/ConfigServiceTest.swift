import Foundation

// MARK: - 配置服务测试
// 这个文件用于测试和验证动态配置是否正常工作

class ConfigServiceTest {
    
    static func testConfigurationSource() async {
        print("🧪 开始测试配置服务...")
        
        // 测试ConfigService
        let configService = await MainActor.run { ConfigService.shared }

        // 强制刷新配置
        await MainActor.run { configService.refreshConfig() }

        // 等待配置加载
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

        let config = await MainActor.run { configService.config }
        if let config = config {
            print("✅ 配置服务测试成功")
            print("📊 配置详情:")
            print("  🔑 API密钥前缀: \(String(config.ai.openRouter.apiKey.prefix(20)))...")
            print("  🌐 API URL: \(config.ai.openRouter.baseURL)")
            print("  🤖 模型列表: \(config.ai.openRouter.gemmaModels.joined(separator: ", "))")
            print("  📅 最后更新: \(config.lastUpdated)")
            print("  🏷️ 版本: \(config.version)")
            
            // 验证是否使用了Cloudflare配置
            if config.ai.openRouter.apiKey.hasPrefix("sk-or-v1-") {
                print("🎉 确认：正在使用Cloudflare动态配置的API密钥")
            } else {
                print("⚠️ 警告：可能使用了备用配置")
            }
            
            if config.ai.openRouter.gemmaModels.count == 2 {
                print("🎯 确认：使用了更新后的2个模型配置")
            }
            
        } else {
            print("❌ 配置服务测试失败")
        }
        
        // 测试GemmaVisionService配置获取
        print("\n🧪 测试GemmaVisionService配置获取...")

        // 这里我们无法直接调用私有方法，但可以通过日志观察
        print("💡 提示：运行AI功能时观察日志，应该看到：")
        print("  ✅ 使用Cloudflare动态配置的API密钥")
        print("  ✅ 使用Cloudflare动态配置的API URL")
        print("  ✅ 使用Cloudflare动态配置的模型列表")
    }
    
    static func printExpectedLogMessages() {
        print("\n📋 预期的日志消息（证明使用Cloudflare配置）:")
        print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        print("[ConfigService] 🚀 Cloudflare配置已更新")
        print("[ConfigService] 🔑 API密钥前缀: sk-or-v1-4a0c26a0a12...")
        print("[ConfigService] 🤖 可用模型: google/gemma-3-27b-it:free, google/gemma-3-12b-it:free")
        print("[ConfigService] 🌐 API URL: https://openrouter.ai/api/v1/chat/completions")
        print("")
        print("⚙️ 正在获取Cloudflare动态配置...")
        print("✅ 使用Cloudflare动态配置的API密钥")
        print("🔑 API密钥前缀: sk-or-v1-4a0c26a0a12...")
        print("✅ 使用Cloudflare动态配置的API URL")
        print("🌐 API URL: https://openrouter.ai/api/v1/chat/completions")
        print("✅ 使用Cloudflare动态配置的模型列表")
        print("🤖 配置的模型: google/gemma-3-27b-it:free, google/gemma-3-12b-it:free")
        print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        print("\n如果看到这些日志，说明成功使用了Cloudflare动态配置！")
    }
}

// MARK: - 使用说明
/*
 如何验证动态配置是否工作：
 
 1. 在应用启动时调用：
    Task {
        await ConfigServiceTest.testConfigurationSource()
        ConfigServiceTest.printExpectedLogMessages()
    }
 
 2. 使用AI功能时观察控制台日志，应该看到：
    - "✅ 使用Cloudflare动态配置的API密钥"
    - "✅ 使用Cloudflare动态配置的API URL"
    - "✅ 使用Cloudflare动态配置的模型列表"
 
 3. 如果看到以下消息，说明使用了备用配置：
    - "⚠️ 使用备用API密钥（配置服务失败）"
    - "⚠️ 使用备用API URL（配置服务失败）"
    - "⚠️ 使用备用模型列表（配置服务失败）"
 
 4. 验证Cloudflare配置是否生效：
    - 检查API密钥前缀是否为 "sk-or-v1-4a0c26a0a12..."
    - 检查模型列表是否只包含2个模型
    - 检查配置更新时间是否为最新
 */
