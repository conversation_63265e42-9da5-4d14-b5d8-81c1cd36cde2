//
//  APP_STORE_LOCALIZED_TEMPLATE.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/23.
//  App Store更新说明本地化模板
//

import Foundation

/// App Store更新说明本地化模板
struct AppStoreUpdateTemplate {

    /// 生成v1.0.8版本的更新说明
    static func generateV108UpdateNotes() -> String {
        let title = NSLocalizedString("update_v108_title", comment: "")
        let subtitle = NSLocalizedString("update_v108_subtitle", comment: "")

        let features = """
        ✨ \(NSLocalizedString("update_v108_feature_regional_classification", comment: ""))
        • \(NSLocalizedString("update_v108_us_delivery", comment: ""))
        • \(NSLocalizedString("update_v108_au_delivery", comment: ""))
        • \(NSLocalizedString("update_v108_auto_detect", comment: ""))

        🤖 \(NSLocalizedString("update_v108_feature_ai_enhancement", comment: ""))
        • \(NSLocalizedString("update_v108_region_rules", comment: ""))
        • \(NSLocalizedString("update_v108_company_features", comment: ""))
        • \(NSLocalizedString("update_v108_imile_support", comment: ""))

        🎯 \(NSLocalizedString("update_v108_feature_user_experience", comment: ""))
        • \(NSLocalizedString("update_v108_gofo_priority", comment: ""))
        • \(NSLocalizedString("update_v108_clean_interface", comment: ""))
        • \(NSLocalizedString("update_v108_faster_selection", comment: ""))

        \(NSLocalizedString("update_v108_improvements_title", comment: ""))
        • \(NSLocalizedString("update_v108_better_detection", comment: ""))
        • \(NSLocalizedString("update_v108_reduced_confusion", comment: ""))
        • \(NSLocalizedString("update_v108_enhanced_accuracy", comment: ""))
        • \(NSLocalizedString("update_v108_optimized_workflow", comment: ""))

        \(NSLocalizedString("update_v108_perfect_for", comment: ""))
        """

        return """
        🚀 \(title)

        \(subtitle)

        \(features)
        """
    }

    /// 生成简短版本的更新说明
    static func generateShortUpdateNotes() -> String {
        return """
        🚀 \(NSLocalizedString("update_v108_title", comment: ""))

        ✨ \(NSLocalizedString("update_v108_feature_regional_classification", comment: ""))
        🤖 \(NSLocalizedString("update_v108_feature_ai_enhancement", comment: ""))
        🎯 \(NSLocalizedString("update_v108_feature_user_experience", comment: ""))

        \(NSLocalizedString("update_v108_perfect_for", comment: ""))
        """
    }

    /// 生成特定语言的更新说明
    static func generateUpdateNotes(for language: String) -> String {
        // 临时切换语言环境
        let currentLanguage: String?
        if #available(iOS 16, *) {
            currentLanguage = Locale.current.language.languageCode?.identifier
        } else {
            currentLanguage = Locale.current.languageCode
        }

        // 这里可以根据需要实现语言切换逻辑
        // 目前直接返回当前语言的版本
        _ = currentLanguage // 标记为已使用，避免警告
        return generateV108UpdateNotes()
    }
}

/// App Store推广文本本地化模板
struct AppStorePromotionTemplate {

    /// 生成App Store描述文本
    static func generateAppStoreDescription() -> String {
        return """
        🚀 NaviBatch: \("delivery_driver_smart_assistant".localized)

        ✨ \("main_features".localized):
        • \("smart_scanning".localized) - \("smart_scanning_description".localized)
        • \("batch_navigation".localized) - \("batch_navigation_description".localized)
        • \("time_saving".localized) - \("time_saving_description".localized)
        • \("professional_tools".localized) - \("professional_tools_description".localized)

        🌍 \("supported_delivery_apps".localized):
        \("amazon_flex_imile_etc".localized)

        💰 \("subscription_benefits".localized):
        • \("save_fuel_30".localized)
        • \("unlimited_addresses".localized)
        • \("one_click_navigation".localized)
        • \("smart_optimization".localized)

        🎁 \("free_tier_name".localized):
        • \("max_20_addresses".localized)
        • \("route_optimization".localized)
        • \("address_validation".localized)
        • \("export_route".localized)

        ⭐ \("pro_tier_name".localized):
        • \("unlimited_addresses".localized)
        • \("one_click_navigation".localized)
        • \("batch_navigation".localized)
        • \("support".localized)

        \("upgrade_to_pro".localized)!
        """
    }

    /// 生成关键词列表
    static func generateKeywords() -> [String] {
        return [
            "delivery_optimization".localized,
            "route_optimization".localized,
            "batch_navigation".localized,
            "smart_scanning".localized,
            "address_validation".localized,
            "delivery".localized,
            "navigation".localized,
            "optimization".localized
        ]
    }

    /// 生成截图文案
    static func generateScreenshotCaptions() -> [String] {
        return [
            "professional_tools_description".localized,
            "smart_scanning_description".localized,
            "batch_navigation_description".localized,
            "time_saving_description".localized,
            "one_click_navigation_description".localized
        ]
    }
}

/// 使用示例
extension AppStoreUpdateTemplate {

    /// 打印所有语言的更新说明（用于测试）
    static func printAllLanguageVersions() {
        print("=== App Store Update Notes v1.0.8 ===")
        print("\n📱 Current Language Version:")
        print(generateV108UpdateNotes())

        print("\n📝 Short Version:")
        print(generateShortUpdateNotes())

        print("\n🌍 App Store Description:")
        print(AppStorePromotionTemplate.generateAppStoreDescription())
    }
}

// MARK: - 本地化辅助函数
private func localizedString(_ key: String) -> String {
    return NSLocalizedString(key, comment: "")
}

// MARK: - 使用说明
/*
 使用方法：

 1. 生成完整更新说明：
    let updateNotes = AppStoreUpdateTemplate.generateV108UpdateNotes()

 2. 生成简短更新说明：
    let shortNotes = AppStoreUpdateTemplate.generateShortUpdateNotes()

 3. 生成App Store描述：
    let description = AppStorePromotionTemplate.generateAppStoreDescription()

 4. 测试所有版本：
    AppStoreUpdateTemplate.printAllLanguageVersions()

 优势：
 - ✅ 完全使用本地化键值，无硬编码
 - ✅ 支持多语言自动切换
 - ✅ 易于维护和更新
 - ✅ 统一的文本管理
 - ✅ 可重用的模板结构
 */
