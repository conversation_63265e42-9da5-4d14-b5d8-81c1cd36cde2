# 第三方快递导入确认按钮同步排序号功能

## 🎯 **功能概述**

实现了第三方快递导入时的智能排序号同步功能：
1. **确认按钮逻辑**：第三方图片导入时，将第三方sort number同步到sort_number和sorted_number
2. **智能默认排序**：bottom sheet检测到第三方app时，默认按第三方号码排序

## ✅ **完成的功能实现**

### **1. 确认按钮同步逻辑** - 🔄 **核心功能**

#### **修改文件**: `NaviBatch/Views/Components/FileImportSheet.swift`

#### **主要修改**:

**确认按钮处理逻辑**:
```swift
// 🎯 第三方快递导入：同步第三方排序号到sort_number和sorted_number
let processedAddresses = processThirdPartyImportAddresses(selectedAddresses)

// 调用回调函数
onAddressesImported(processedAddresses)
```

**第三方地址处理方法**:
```swift
// 🎯 处理第三方快递导入地址：同步第三方排序号到sort_number和sorted_number
private func processThirdPartyImportAddresses(_ addresses: [(String, CLLocationCoordinate2D, String)]) -> [(String, CLLocationCoordinate2D, String)] {
    return addresses.map { (address, coordinate, warning) in
        // 分离地址信息，检查是否有第三方排序号
        let separatedInfo = DeliveryPointManager.shared.separateAddressAndTracking(address)
        
        // 如果有第三方排序号，同步到sort_number和sorted_number
        if !separatedInfo.thirdPartySortNumber.isEmpty {
            // 提取第三方排序号的数字部分
            let thirdPartyNumber = extractNumberFromThirdPartySort(separatedInfo.thirdPartySortNumber)
            
            if thirdPartyNumber > 0 {
                // 构建新的地址字符串，将第三方排序号同步到sort_number和sorted_number
                var newAddress = separatedInfo.address
                
                // 添加同步的排序号
                newAddress += SortNumberConstants.internalSortTag(thirdPartyNumber)  // sort_number
                newAddress += SortNumberConstants.thirdPartySortTag(separatedInfo.thirdPartySortNumber)  // 保留原始第三方排序号
                
                // 添加其他信息
                if !separatedInfo.tracking.isEmpty {
                    newAddress += SortNumberConstants.trackingTag(separatedInfo.tracking)
                }
                if !separatedInfo.customer.isEmpty {
                    newAddress += SortNumberConstants.customerTag(separatedInfo.customer)
                }
                
                Logger.aiInfo("🎯 第三方快递导入同步：第三方排序号 '\(separatedInfo.thirdPartySortNumber)' -> sort_number: \(thirdPartyNumber)")
                
                return (newAddress, coordinate, warning)
            }
        }
        
        // 如果没有第三方排序号或提取失败，返回原地址
        return (address, coordinate, warning)
    }
}
```

**数字提取方法**:
```swift
// 从第三方排序号中提取数字
private func extractNumberFromThirdPartySort(_ sortNumber: String) -> Int {
    // 使用正则表达式提取数字
    let pattern = "\\d+"
    if let regex = try? NSRegularExpression(pattern: pattern, options: []),
       let match = regex.firstMatch(in: sortNumber, options: [], range: NSRange(location: 0, length: sortNumber.count)) {
        let numberString = (sortNumber as NSString).substring(with: match.range)
        return Int(numberString) ?? 0
    }
    return 0
}
```

### **2. 智能默认排序** - 🧠 **智能功能**

#### **修改文件**: `NaviBatch/Views/Components/RouteBottomSheet.swift`

#### **主要修改**:

**添加初始化标记**:
```swift
// 🎯 新增：排序状态 - 智能默认排序
@State private var currentSortType: SortType = .sortNumber
@State private var showingSortOptions = false
@State private var hasInitializedSortType = false  // 标记是否已初始化排序类型
```

**智能默认排序设置**:
```swift
// 🎯 智能设置默认排序类型：如果有第三方app就默认按第三方号码排序
private func setupSmartDefaultSortType() {
    // 只在首次初始化时设置
    guard !hasInitializedSortType else { return }
    
    // 检查是否有第三方快递
    if let dominantApp = viewModel.getDominantThirdPartyApp() {
        currentSortType = .thirdPartySortNumber
        Logger.aiInfo("🎯 检测到第三方快递 \(dominantApp.displayName)，默认使用第三方排序")
    } else {
        currentSortType = .sortNumber
        Logger.aiInfo("🎯 未检测到第三方快递，使用默认排序")
    }
    
    hasInitializedSortType = true
}
```

**路线数据变化时重新检查**:
```swift
// 在路线数据变化监听器中添加：
// 🎯 路线数据变化时重新检查排序类型
setupSmartDefaultSortType()
```

## 🔄 **工作流程**

### **第三方快递导入流程**

1. **用户导入第三方快递图片** → AI识别出地址和第三方排序号
2. **用户点击确认按钮** → 触发`processThirdPartyImportAddresses`方法
3. **检测第三方排序号** → 如果存在，提取数字部分
4. **同步排序号** → 将第三方排序号同步到`sort_number`字段
5. **保留原始信息** → 同时保留原始第三方排序号
6. **导入完成** → 地址添加到路线中

### **智能默认排序流程**

1. **路线初始化** → 检查路线中的快递类型
2. **检测第三方快递** → 使用`getDominantThirdPartyApp()`方法
3. **设置默认排序** → 如果有第三方快递，默认使用第三方排序
4. **动态更新** → 路线数据变化时重新检查排序类型

## 📊 **功能效果**

### **同步排序号的好处**

| 功能 | 修改前 | 修改后 | 优势 |
|------|--------|--------|------|
| **排序一致性** | 第三方号码独立存在 | 同步到sort_number | 统一排序逻辑 |
| **用户体验** | 需要手动选择排序 | 自动使用第三方排序 | 简化操作 |
| **数据完整性** | 可能出现排序不一致 | 确保排序号同步 | 避免混乱 |

### **智能默认排序的好处**

| 场景 | 修改前 | 修改后 | 用户体验 |
|------|--------|--------|---------|
| **第三方快递导入** | 默认按添加顺序 | 默认按第三方号码 | 🎯 符合预期 |
| **混合快递** | 需要手动切换 | 智能检测主要类型 | 🧠 自动优化 |
| **纯手动输入** | 按添加顺序 | 仍按添加顺序 | ✅ 保持一致 |

## 🎯 **使用场景示例**

### **场景1：SpeedX快递导入**

**导入前**:
```
地址: 25 Hyde Ct #2, Daly City, CA
第三方排序号: SpeedX: 1
```

**确认按钮处理后**:
```
地址: 25 Hyde Ct #2, Daly City, CA [sort_number:1] [third_party_sort:SpeedX: 1]
sort_number: 1
third_party_sort: "SpeedX: 1"
```

**Bottom Sheet显示**:
- 默认排序类型: "SpeedX" (第三方排序)
- 显示顺序: 按SpeedX号码1, 2, 3...排序

### **场景2：混合快递导入**

**路线包含**:
- 5个SpeedX地址 (主要)
- 2个手动输入地址

**Bottom Sheet行为**:
- 检测到SpeedX为主要快递类型
- 默认排序: "SpeedX" 
- SpeedX地址按第三方号码排序
- 手动地址按sort_number排序

## 🔧 **技术实现细节**

### **排序号提取逻辑**

```swift
// 支持多种第三方排序号格式：
"SpeedX: 1" → 提取数字: 1
"GoFo: 25" → 提取数字: 25
"Amazon: 3" → 提取数字: 3
"#5" → 提取数字: 5
```

### **同步机制**

1. **保持原始信息** → 第三方排序号完整保留
2. **提取数字部分** → 用于sort_number字段
3. **构建新地址** → 包含所有标签信息
4. **日志记录** → 记录同步过程

### **智能检测机制**

1. **统计快递类型** → 计算各快递的地址数量
2. **找出主要类型** → 数量最多的非manual类型
3. **设置默认排序** → 根据主要类型智能选择
4. **动态更新** → 数据变化时重新检测

## 🚀 **预期效果**

### **用户体验提升**

1. **🎯 符合直觉** → 第三方快递自动按第三方号码排序
2. **🔄 自动同步** → 无需手动处理排序号不一致
3. **🧠 智能默认** → 自动选择最合适的排序方式
4. **⚡ 操作简化** → 减少手动选择排序的步骤

### **数据一致性保证**

1. **📊 排序统一** → sort_number和第三方排序号保持一致
2. **🔒 数据完整** → 保留所有原始信息
3. **🎯 逻辑清晰** → 排序逻辑简单明确
4. **🔄 动态适应** → 根据数据变化自动调整

## 📋 **总结**

### **实现的功能**

1. ✅ **确认按钮同步** → 第三方排序号自动同步到sort_number
2. ✅ **智能默认排序** → 检测第三方app时默认使用第三方排序
3. ✅ **数字提取** → 从各种格式的第三方排序号中提取数字
4. ✅ **动态检测** → 路线数据变化时重新检查排序类型
5. ✅ **日志记录** → 完整记录同步和检测过程

### **用户价值**

- **🎯 直观体验** → 第三方快递按第三方号码排序，符合用户预期
- **⚡ 操作简化** → 减少手动选择和调整的步骤
- **🔄 自动化** → 智能检测和自动同步，减少人工干预
- **📊 一致性** → 确保排序逻辑的统一和数据的完整性

---

**功能完成时间**: 2024-12-06  
**影响范围**: 第三方快递导入流程和bottom sheet排序逻辑  
**用户体验**: 显著简化第三方快递的使用流程
