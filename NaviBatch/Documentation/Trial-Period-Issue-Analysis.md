# 试用期显示问题分析与解决方案

## 问题描述

在iOS沙盒环境中测试订阅功能时，发现年费产品的购买确认界面没有显示试用期信息，而月费产品正常显示60天免费试用。

## 问题分析

### 根本原因

根据Apple StoreKit的试用期规则：

1. **订阅组限制**：同一订阅组中的用户只能享受一次试用期
2. **试用期资格共享**：月费和年费产品在同一订阅组(`subscriptionGroupID: "20001000"`)中，试用期资格共享
3. **已使用试用期**：如果用户已经在月费产品上使用了试用期，年费产品将不再提供试用期选项

### 配置确认

从`StoreKitConfig.storekit`文件可以看到：

```json
{
  "subscriptionGroupID": "20001000",  // 月费和年费在同一组
  "introductoryOffer": {
    "paymentMode": "free",
    "subscriptionPeriod": "P60D"      // 两个产品都配置了60天试用期
  }
}
```

## 解决方案

### 方案1：修改界面逻辑（已实现）

更新`SubscriptionView.swift`中的购买按钮文本生成逻辑：

```swift
private var purchaseButtonText: String {
    // 检查用户是否已经在试用期中
    let isCurrentlyInTrial = subscriptionManager.isInFreeTrial
    
    // 如果是免费试用期且用户当前不在试用期
    if introOffer.paymentMode == .freeTrial && !isCurrentlyInTrial {
        return "Start 60-day free trial"
    }
    
    // 如果用户已在试用期，显示切换文本
    if isCurrentlyInTrial {
        return selectedTier == .pro ? "Switch to Monthly" : "Switch to Annual"
    } else {
        return "upgrade".localized
    }
}
```

### 方案2：开发者工具支持（已实现）

在`DeveloperToolsView.swift`中添加了"重置试用期状态"工具：

```swift
case trialReset = "重置试用期状态"

private func resetTrialStatus() {
    // 重置订阅状态为免费版
    await subscriptionManager.resetSubscriptionForTesting()
    // 显示成功提示和通知状态变更
}
```

### 方案3：沙盒环境测试方法

1. **使用开发者工具**：通过"重置试用期状态"按钮重置
2. **删除应用重装**：完全清除沙盒数据
3. **使用不同Apple ID**：每个Apple ID都有独立的试用期资格

## 测试验证

### 测试步骤

1. 确保用户处于免费状态
2. 首次购买月费产品 → 应显示试用期
3. 取消后尝试购买年费产品 → 不显示试用期（正常行为）
4. 使用开发者工具重置试用期状态
5. 再次尝试购买 → 应重新显示试用期

### 预期结果

- 首次用户：月费和年费都显示试用期
- 已试用用户：两个产品都不显示试用期
- 重置后：恢复首次用户状态

## Apple官方文档参考

- [StoreKit Testing Guide](https://developer.apple.com/documentation/storekit/in-app_purchase/testing_at_all_stages_of_development)
- [Subscription Groups](https://developer.apple.com/app-store/subscriptions/#groups)
- [Introductory Offers](https://developer.apple.com/app-store/subscriptions/#introductory-offers)

## 结论

这不是一个bug，而是Apple StoreKit的正常行为。用户在同一订阅组中只能享受一次试用期，这是为了防止用户通过切换产品来重复享受试用期。

我们的解决方案确保了：
1. 界面正确反映用户的试用期资格状态
2. 开发者可以在测试环境中重置试用期状态
3. 用户体验符合Apple的设计规范
