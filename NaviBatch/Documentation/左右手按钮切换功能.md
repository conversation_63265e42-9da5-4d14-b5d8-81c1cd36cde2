# 左右手按钮切换功能

## 功能概述

为了提升左手用户的使用体验，在NaviBatch应用的地图界面顶部添加了一个按钮组位置切换功能。用户可以通过点击顶部的切换按钮，将右侧的按钮组移动到左侧，方便左手操作。

## 功能特点

### 1. 智能切换按钮
- **位置**: 位于右侧按钮组的最顶部，在定位按钮上方
- **图标**: 根据当前状态显示左箭头(⬅️)或右箭头(➡️)
- **样式**: 圆形黑色背景，白色箭头图标，与其他按钮保持一致的设计风格

### 2. 按钮组位置
- **默认位置**: 右侧（适合右手用户）
- **切换后位置**: 左侧（适合左手用户）
- **视觉样式**: 所有按钮采用统一的黑底白字设计，提供更好的视觉对比度
- **包含按钮**:
  - 左右手切换按钮（最顶部，黑底白色箭头）
  - 定位按钮（黑底白色location.fill图标）
  - 地图模式切换按钮（黑底白色图标）
  - 编号定位按钮（黑底白色magnifyingglass图标）
  - 一键分组按钮（黑底白色图标）
  - 已保存分组按钮（黑底白色folder.fill图标）
  - 显示/隐藏已完成派送地址按钮（黑底白色eye/eye.slash图标）
  - 配送计数器（黑底白色数字）
  - 菜单按钮（黑底白色line.3.horizontal图标）

### 3. 用户体验优化
- **动画效果**: 使用Spring动画，切换过程平滑自然
- **触觉反馈**: 点击切换按钮时提供中等强度的触觉反馈
- **状态保存**: 用户的选择会自动保存到UserDefaults，下次启动应用时恢复
- **日志记录**: 记录切换操作，便于调试和用户行为分析

## 技术实现

### 1. 状态管理
```swift
// 按钮组位置控制状态变量
@State private var buttonsOnLeftSide = UserDefaults.standard.bool(forKey: "buttonsOnLeftSide")
```

### 2. 切换逻辑
```swift
// 左右手切换按钮 - 在定位按钮上方
Button(action: {
    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
        buttonsOnLeftSide.toggle()
        UserDefaults.standard.set(buttonsOnLeftSide, forKey: "buttonsOnLeftSide")
    }

    // 给予触觉反馈
    let generator = UIImpactFeedbackGenerator(style: .medium)
    generator.impactOccurred()

    logInfo("按钮组位置切换到: \(buttonsOnLeftSide ? "左侧" : "右侧")")
}) {
    Image(systemName: buttonsOnLeftSide ? "arrow.right" : "arrow.left")
        .font(.system(size: 18))
        .foregroundColor(.white)
}
.frame(width: 40, height: 40)
.background(Color.black)
.clipShape(Circle())
.shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
```

## 技术实现

### 1. 按钮样式统一
所有按钮都使用统一的`circleButton`函数，确保一致的黑底白字外观：

```swift
private func circleButton(
    backgroundColor: Color = .black,  // 默认黑色背景
    action: @escaping () -> Void,
    @ViewBuilder content: () -> some View
) -> some View {
    // 统一的圆形按钮实现，黑底白字设计
    // 按钮尺寸：50x50，点击区域：58x58
}
```

### 数字显示优化
按钮上的数字显示已优化为更醒目的样式：
- **一键分组按钮数字**：12pt Bold 白色文字
- **配送计数器数字**：20pt Bold 白色文字
- **Saved Groups按钮数字**：12pt Bold 白色文字（免费用户显示"1"）
- 所有数字都使用白色确保在黑色背景上的可读性

### 2. Pro版按钮样式
Pro版按钮也采用黑底白字设计：

```swift
private func proCircleButton(
    backgroundColor: Color = .black,  // 默认黑色背景
    iconColor: Color = .white,        // 默认白色图标
    action: @escaping () -> Void,
    @ViewBuilder content: () -> some View
) -> some View {
    // Pro版按钮，黑底白字+白色皇冠
    // 按钮尺寸：50x50，点击区域：58x58
    // 皇冠尺寸：12pt，背景圆圈：14x14，位置：右上角偏移(17, -17)
    // 所有图标和皇冠都是白色，保持视觉一致性
}
```

### 3. Pro版按钮特色
Pro版用户的按钮具有以下特色：
- **皇冠标识**：右上角显示白色皇冠图标，突出Pro身份
- **统一设计**：所有图标和文字都是白色，保持视觉一致性
- **特殊功能**：一键分组和选择地址点功能
- **视觉区分**：通过皇冠标识区分免费用户

### 4. 布局适配
```swift
HStack {
    if buttonsOnLeftSide {
        rightBottomButtons
        Spacer()
    } else {
        Spacer()
        rightBottomButtons
    }
}
```

## 使用方法

1. **查看当前状态**: 观察右侧按钮组最顶部的切换按钮图标
   - 显示左箭头(⬅️)：当前按钮在右侧，点击可移动到左侧（适合左手用户）
   - 显示右箭头(➡️)：当前按钮在左侧，点击可移动到右侧（适合右手用户）

2. **切换按钮位置**: 点击按钮组顶部的切换按钮
   - 按钮组会平滑地从右侧移动到左侧，或从左侧移动到右侧
   - 设置会自动保存，下次打开应用时保持用户的选择

3. **恢复默认设置**: 如需恢复到默认的右侧位置，再次点击切换按钮即可

## 适用场景

- **左手用户**: 将按钮组移动到左侧，方便左手拇指操作
- **右手用户**: 保持默认的右侧位置
- **双手切换**: 根据当前使用情况灵活切换
- **单手操作**: 根据握持方式选择最舒适的按钮位置

## 注意事项

1. **兼容性**: 功能与现有的所有按钮功能完全兼容
2. **性能**: 切换操作轻量级，不影响应用性能
3. **持久化**: 用户选择会持久保存，无需重复设置
4. **可访问性**: 保持所有按钮的可访问性标签和功能

## 更新日期

2025-07-05 - 初始版本实现
2025-07-06 - 统一Pro版按钮颜色为白色（包括皇冠和图标）
