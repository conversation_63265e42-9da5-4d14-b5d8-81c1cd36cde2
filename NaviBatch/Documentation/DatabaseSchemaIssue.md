# 数据库模式问题解决方案

## 📋 问题描述

遇到了 `Thread 1: EXC_BREAKPOINT` 错误，发生在 `SchemaMigration.swift` 第152行的 `context.fetch(descriptor)` 调用中。

## 🔍 问题分析

### 错误原因
1. **数据库模式不匹配**：现有数据库结构与新的模型定义不兼容
2. **字段缺失**：数据库中缺少新添加的时间字段
3. **迁移失败**：SwiftData 无法自动处理模式变更

### 错误位置
```swift
// SchemaMigration.swift:152
let entities = try context.fetch(descriptor) // ❌ EXC_BREAKPOINT
```

## 🔧 解决方案

### 方案1：删除应用重新安装（推荐）
```bash
# 1. 在模拟器中删除 NaviBatch 应用
# 2. 重新运行项目
# 3. 新数据库将包含所有新字段
```

### 方案2：使用强制重建参数
```bash
# 在 Xcode 中添加启动参数
# Product -> Scheme -> Edit Scheme -> Arguments -> Arguments Passed On Launch
--force-recreate-db
```

### 方案3：手动删除数据库文件
```swift
// 数据库路径通常在：
// ~/Library/Developer/CoreSimulator/Devices/[DEVICE_ID]/data/Containers/Data/Application/[APP_ID]/Documents/
```

## 🛠️ 技术修复

### 1. 增强错误处理
```swift
// 在 SchemaMigration.swift 中添加了安全检查
static func migrateField<T: PersistentModel>(
    modelType: T.Type,
    in context: ModelContext,
    transform: (T) -> Void
) async {
    do {
        // 🔧 添加安全检查
        guard let _ = try? context.fetch(FetchDescriptor<T>(predicate: #Predicate { _ in false })) else {
            logError("无法访问\(modelType)数据表，可能需要数据库重建")
            return
        }
        
        let entities = try context.fetch(descriptor)
        // ... 迁移逻辑
    } catch {
        logError("迁移失败: \(error.localizedDescription)")
        
        // 🚨 检测模式问题并提供建议
        if error.localizedDescription.contains("schema") {
            logError("⚠️ 检测到数据库模式问题，建议删除应用重新安装")
        }
    }
}
```

### 2. 安全数据库初始化器
```swift
// SafeDatabaseInitializer.swift
class SafeDatabaseInitializer {
    static func createSafeContainer() -> ModelContainer {
        do {
            let container = try ModelContainer(for: schema, configurations: [config])
            // 测试数据库访问
            await testDatabaseAccess(container: container)
            return container
        } catch {
            // 自动重建数据库
            return recreateDatabase(schema: schema, configuration: config)
        }
    }
}
```

### 3. 应用启动保护
```swift
// NaviBatchApp.swift
private let sharedModelContainer: ModelContainer = {
    #if DEBUG
    if SafeDatabaseInitializer.shouldForceRecreate() {
        return SafeDatabaseInitializer.createSafeContainer()
    }
    #endif
    
    // 正常初始化...
}()
```

## ✅ 验证步骤

### 1. 检查数据库状态
```swift
// 在应用启动后查看日志
[INFO] 数据库路径: /path/to/database
[SUCCESS] DeliveryPoint 查询成功，共 X 条记录
[SUCCESS] 新时间字段测试成功
```

### 2. 验证新字段
```swift
// 创建新的配送点，检查时间字段是否可用
let point = DeliveryPoint(...)
point.scheduledDeliveryTime = "测试时间"  // 应该不会崩溃
```

### 3. 测试迁移功能
```swift
// 查看迁移日志
[INFO] 开始迁移DeliveryPoint数据，共X条记录
[SUCCESS] 成功迁移DeliveryPoint数据，共处理X条记录
```

## 🚨 紧急解决步骤

如果遇到此错误，立即执行：

1. **停止调试器**
2. **删除应用**：在模拟器中长按应用图标 -> 删除应用
3. **重新运行**：在 Xcode 中重新运行项目
4. **验证功能**：测试 Amazon Flex 时间提取功能

## 📝 预防措施

### 1. 开发阶段
- 使用 `--force-recreate-db` 参数进行测试
- 定期备份重要测试数据
- 在模式变更前创建迁移计划

### 2. 生产环境
- 实现渐进式模式迁移
- 提供数据备份和恢复功能
- 监控数据库健康状态

### 3. 调试工具
```swift
#if DEBUG
// 添加数据库诊断工具
DatabaseDiagnostics.checkSchemaCompatibility()
DatabaseDiagnostics.validateNewFields()
#endif
```

## 🎯 预期结果

修复后应该看到：
```
✅ 应用正常启动，无崩溃
✅ 数据库包含新的时间字段
✅ Amazon Flex 时间提取功能正常工作
✅ 路线优化界面隐藏第三方单号
```

## 📞 如果问题持续

如果上述方案都无法解决问题：
1. 检查 Xcode 控制台的完整错误日志
2. 确认 SwiftData 模型定义是否正确
3. 验证设备存储空间是否充足
4. 考虑重置模拟器环境
