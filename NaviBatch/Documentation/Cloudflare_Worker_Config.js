// Cloudflare Worker配置文件
// 部署到: https://navibatch-config.jasonkwok2018.workers.dev/config

export default {
  async fetch(request, env, ctx) {
    // 处理CORS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, X-App-Version',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    // 只允许GET请求
    if (request.method !== 'GET') {
      return new Response('Method not allowed', { status: 405 });
    }

    // 获取应用版本和调试模式（如果提供）
    const appVersion = request.headers.get('X-App-Version') || '1.0.0';
    const debugMode = request.headers.get('X-Debug-Mode') === 'true';

    // 🔑 更新的配置 - 使用新的API密钥
    const config = {
      ai: {
        openRouter: {
          apiKey: "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",
          baseURL: "https://openrouter.ai/api/v1/chat/completions",
          gemmaModels: [
            "google/gemma-3-27b-it:free",
            "google/gemma-3-12b-it:free",
            "deepseek/deepseek-r1-0528-qwen3-8b:free"  // 🚀 DeepSeek后备模型
          ],
          timeout: 30,
          maxRetries: 3,
          modelConfigs: {
            "google/gemma-3-27b-it:free": {
              maxTokens: 60000,
              temperature: 0.1,
              topP: 0.9
            },
            "google/gemma-3-12b-it:free": {
              maxTokens: 60000,
              temperature: 0.1,
              topP: 0.9
            },
            "deepseek/deepseek-r1-0528-qwen3-8b:free": {
              maxTokens: 60000,
              temperature: 0.1,
              topP: 0.9
            }
          }
        }
      },

      // 🚀 版本更新配置
      versionUpdate: {
        latestVersion: "1.0.4",
        forceUpdate: false,
        updateTitle: "车道级·真境时代",
        updateSubtitle: "AI 空间建模 刷新导航视界",
        updateNotes: [
          "🚀 全新版本更新提示功能",
          "🎯 智能版本检查机制",
          "✨ 优化用户体验",
          "🔧 修复已知问题"
        ],
        appStoreURL: "https://apps.apple.com/app/navibatch/id6738046894",
        releaseDate: "2024-12-27"
      },

      features: {
        lookAround: {
          enabled: true,
          cacheEnabled: true,
          maxCacheSize: 100
        },
        routeOptimization: {
          enabled: true,
          maxPoints: 300,
          algorithms: ["genetic", "nearest_neighbor", "two_opt"]
        },
        subscription: {
          enabled: true,
          trialDays: 60,
          features: {
            unlimitedAddresses: false,
            advancedOptimization: false,
            exportFeatures: true
          }
        }
      },
      version: "1.0.3",
      minAppVersion: "1.0.0",
      lastUpdated: new Date().toISOString()
    };

    // 根据应用版本调整配置（如果需要）
    if (appVersion === "1.0.2") {
      // 为旧版本提供兼容性配置
      config.ai.openRouter.gemmaModels = [
        "google/gemma-3-27b-it:free",
        "google/gemma-3-12b-it:free"
      ];
    }

    // 返回配置
    return new Response(JSON.stringify(config, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
        'X-Config-Version': config.version,
        'X-Last-Updated': config.lastUpdated,
      },
    });
  },
};

/*
部署说明：

1. 登录 Cloudflare Dashboard
2. 进入 Workers & Pages
3. 找到现有的 navibatch-config worker
4. 点击 "Edit code"
5. 替换为上面的代码
6. 点击 "Save and Deploy"

或者使用 Wrangler CLI：
```bash
wrangler deploy
```

配置特点：
- ✅ 新的API密钥：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
- ✅ 包含DeepSeek后备模型
- ✅ 支持CORS跨域请求
- ✅ 版本兼容性处理
- ✅ 缓存控制
- ✅ 错误处理

测试URL：
https://navibatch-config.jasonkwok2018.workers.dev/config
*/
