# SSIM算法升级：解决视频去重重复帧问题

## 问题背景

用户发现视频处理中存在重复帧问题：
- 第二张图片和第六、七张图片是重复的
- 说明视频切割和去重功能做得不好
- 导致AI分析相同内容多次，浪费token成本

## 根本原因分析

### 原始算法问题

```swift
// ❌ 原始算法：简单字节比较法
private func calculateFrameSimilarity(_ image1: UIImage, _ image2: UIImage) async -> Float {
    // 简单的字节比较（可以优化为更精确的图像相似度算法）
    let minLength = min(data1.count, data2.count)
    var matchingBytes = 0
    
    for i in 0..<minLength {
        if data1[i] == data2[i] {
            matchingBytes += 1
        }
    }
    
    return Float(matchingBytes) / Float(minLength)
}
```

**主要问题：**
1. **算法过于简单**：字节比较无法准确反映视觉相似性
2. **JPEG压缩影响**：相同图像经过JPEG压缩后字节数据不同
3. **阈值不当**：0.88的阈值对字节比较算法来说过高

## 解决方案：SSIM算法

### 新算法实现

```swift
// ✅ 新算法：基于SSIM的感知相似度计算
private func calculatePerceptualSimilarity(_ image1: UIImage, _ image2: UIImage) async -> Float {
    // 缩小到固定尺寸以提高计算速度和一致性
    let targetSize = CGSize(width: 64, height: 64)
    guard let resized1 = resizeImageForComparison(image1, to: targetSize),
          let resized2 = resizeImageForComparison(image2, to: targetSize) else {
        return 0.0
    }
    
    // 转换为灰度并获取像素数据
    guard let grayData1 = getGrayscalePixelData(from: resized1),
          let grayData2 = getGrayscalePixelData(from: resized2) else {
        return 0.0
    }
    
    // 计算结构相似性
    return calculateStructuralSimilarity(grayData1, grayData2, width: 64, height: 64)
}
```

### SSIM核心算法

```swift
private func calculateStructuralSimilarity(_ data1: [UInt8], _ data2: [UInt8], width: Int, height: Int) -> Float {
    // 计算均值
    let mean1 = Float(data1.reduce(0, { $0 + Int($1) })) / Float(totalPixels)
    let mean2 = Float(data2.reduce(0, { $0 + Int($1) })) / Float(totalPixels)
    
    // 计算方差和协方差
    var variance1: Float = 0
    var variance2: Float = 0
    var covariance: Float = 0
    
    for i in 0..<totalPixels {
        let diff1 = Float(data1[i]) - mean1
        let diff2 = Float(data2[i]) - mean2
        
        variance1 += diff1 * diff1
        variance2 += diff2 * diff2
        covariance += diff1 * diff2
    }
    
    // SSIM计算
    let c1: Float = 0.01 * 0.01 * 255 * 255
    let c2: Float = 0.03 * 0.03 * 255 * 255
    
    let numerator = (2 * mean1 * mean2 + c1) * (2 * covariance + c2)
    let denominator = (mean1 * mean1 + mean2 * mean2 + c1) * (variance1 + variance2 + c2)
    
    return max(0.0, min(1.0, numerator / denominator))
}
```

## 参数优化

### 阈值调整

```swift
// 原始配置
private var similarityThreshold: Float = 0.88 // 字节比较阈值

// 新配置
private var similarityThreshold: Float = 0.95 // 🎯 SSIM相似度阈值：95%相似才认为是重复帧
```

### 阈值范围更新

```swift
// 原始范围
private let minThreshold: Float = 0.82
private let maxThreshold: Float = 0.95

// SSIM优化范围
private let minThreshold: Float = 0.90 // 🎯 SSIM最小阈值：90%相似度
private let maxThreshold: Float = 0.98 // 🎯 SSIM最大阈值：98%相似度
```

## 分析系统优化

### 相似度分析更新

```swift
// 🎯 SSIM相似度分析（针对新算法调整阈值范围）
let highSimilarityCount = scores.filter { $0 > 0.95 }.count  // 极高相似度
let mediumSimilarityCount = scores.filter { $0 > 0.85 && $0 <= 0.95 }.count  // 中等相似度
let lowSimilarityCount = scores.filter { $0 <= 0.85 }.count  // 低相似度

print("📊 SSIM相似度分析:")
print("   极高相似度(>0.95): \(highSimilarityCount)帧 - 可能是重复帧")
print("   中等相似度(0.85-0.95): \(mediumSimilarityCount)帧 - 轻微变化")
print("   低相似度(<0.85): \(lowSimilarityCount)帧 - 明显不同")
```

## 预期效果

1. **更精确的去重**：SSIM算法能更准确识别视觉上相似的帧
2. **减少重复帧**：避免第二、六、七张图片重复的问题
3. **节省成本**：减少AI分析重复内容，降低token使用
4. **提高质量**：保留真正不同的帧，提升最终识别准确性

## 技术优势

- **感知一致性**：SSIM算法更符合人眼视觉感知
- **压缩无关**：基于像素级分析，不受JPEG压缩影响
- **结构感知**：考虑亮度、对比度和结构信息
- **自适应优化**：根据视频特征动态调整阈值

## 更新时间

2025-06-27：完成SSIM算法升级，解决视频去重重复帧问题
