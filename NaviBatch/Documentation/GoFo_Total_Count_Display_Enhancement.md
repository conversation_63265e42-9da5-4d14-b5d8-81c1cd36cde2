# GoFo总数显示增强

## 概述
为GoFo路线标签添加总数显示功能，参考SpeedX的设计，让用户清楚知道当前路线中有多少个GoFo配送订单。

## 实现细节

### 1. 新增计算属性

在 `RouteBottomSheet.swift` 中添加了以下计算属性：

```swift
// 🎯 计算GoFo地址数量
private var gofoAddressCount: Int {
    guard let route = viewModel.currentRoute else { return 0 }

    // 计算GoFo类型的配送点数量（排除起点和终点）
    let gofoPoints = route.points.filter { point in
        point.sourceApp == .gofo && !point.isStartPoint && !point.isEndPoint
    }

    return gofoPoints.count
}

// 🎯 计算指定快递类型的地址数量
private func getAddressCount(for appType: DeliveryAppType) -> Int {
    guard let route = viewModel.currentRoute else { return 0 }

    let points = route.points.filter { point in
        point.sourceApp == appType && !point.isStartPoint && !point.isEndPoint
    }

    return points.count
}
```

### 2. 修改标签显示逻辑

更新了GoFo标签的显示方式，参考SpeedX的设计：

```swift
} else if appType == .gofo {
    // 🎯 GoFo专用：标签和数量使用统一边框（参考SpeedX设计）
    HStack(spacing: 6) {
        Text(appType.displayName)
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.white)

        Text("\(gofoAddressCount)")
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.white)
    }
    .padding(.horizontal, 8)
    .padding(.vertical, 6)
    .background(appType.primaryColor)
    .cornerRadius(6)
    .frame(height: 36)
}
```

### 3. 视觉效果

- **背景颜色**: 使用GoFo的主色调（黄色）
- **文字颜色**: 白色，确保在黄色背景上清晰可见
- **布局**: 标签名称和数量在同一行显示，用空格分隔
- **尺寸**: 与SpeedX标签保持一致的高度（36pt）

## 用户体验改进

1. **清晰的数量显示**: 用户可以立即看到当前路线中有多少个GoFo订单
2. **一致的设计**: 与SpeedX标签保持相同的设计风格
3. **实时更新**: 当添加或删除GoFo订单时，数量会自动更新

## 技术优势

1. **性能优化**: 使用计算属性，只在需要时计算数量
2. **类型安全**: 通过过滤 `sourceApp == .gofo` 确保只统计GoFo订单
3. **可扩展性**: 新增的通用方法 `getAddressCount(for:)` 可用于其他快递类型

## 测试验证

- ✅ 构建成功，无编译错误
- ✅ 代码逻辑正确，正确过滤GoFo类型的配送点
- ✅ 排除起点和终点，只统计实际配送地址
- ✅ 与现有SpeedX功能保持一致的设计模式

## 后续扩展

这个实现为其他快递公司（如UNIUNI、Amazon Flex等）添加类似的总数显示功能奠定了基础。可以通过类似的方式为其他快递类型添加数量显示。
