# 可折叠按钮组功能说明

## 概述

为了解决右侧浮动按钮过多导致界面拥挤的问题，我们实现了一个可折叠的按钮组组件，将5个次要功能按钮收纳到一个"..."按钮中。

## 功能特点

### 🎯 主要改进
- **界面简洁**：将5个按钮收纳到1个"..."按钮中
- **用户自定义**：支持用户选择常用按钮（最多4个）
- **动画效果**：流畅的展开/收纳动画
- **触觉反馈**：每个按钮都有触觉反馈

### 📱 收纳的按钮
1. **左右切换** - 切换按钮组显示位置（左侧/右侧）
2. **地图模式** - 切换地图显示模式（驾驶/卫星）
3. **搜索定位** - 按编号搜索定位配送点
4. **显示/隐藏** - 显示/隐藏已完成的配送订单
5. **订单计数** - 显示当前订单总数

### 🔧 自定义功能
- 点击齿轮图标可进入自定义界面
- 用户可选择最多4个常用按钮
- 选择的按钮会保存到用户偏好设置中
- 默认显示前3个按钮（左右切换、地图模式、搜索定位）

## 技术实现

### 核心组件
- `CollapsibleButtonGroup.swift` - 主要的可折叠按钮组组件
- `ButtonCustomizationSheet.swift` - 按钮自定义界面
- `ButtonType` - 按钮类型枚举

### 集成方式
在 `RouteView.swift` 中替换了原来的5个独立按钮：

```swift
// 原来的代码（已移除）
// - 左右手切换按钮
// - 地图模式选择按钮  
// - 输入编号定位按钮
// - 显示/隐藏按钮
// - 配送计数器

// 新的可折叠按钮组
CollapsibleButtonGroup(
    isExpanded: $isCollapsibleButtonGroupExpanded,
    onSideToggle: { /* 左右切换逻辑 */ },
    onMapModeToggle: { /* 地图模式切换逻辑 */ },
    onSearch: { /* 搜索定位逻辑 */ },
    onVisibilityToggle: { /* 显示/隐藏逻辑 */ },
    onCounterTap: { /* 计数器点击逻辑 */ },
    buttonsOnLeftSide: buttonsOnLeftSide,
    selectedMapMode: selectedMapMode,
    showCompletedDeliveries: viewModel.showCompletedDeliveries,
    deliveryCount: viewModel.getTotalDeliveryCount()
)
```

### 数据持久化
- 使用 `@AppStorage` 保存用户的按钮偏好设置
- 数据以 JSON 格式存储在 UserDefaults 中
- 键名：`"favoriteButtons"`

### 本地化支持
- 所有用户界面文本都已本地化
- 支持中文（简体）和英文
- 本地化键值已添加到相应的 `.strings` 文件中
- 使用 `.localized` 扩展方法获取本地化文本

## 用户体验

### 操作流程
1. **展开按钮组**：点击"..."按钮展开所有功能按钮
2. **使用功能**：点击相应按钮执行功能
3. **收纳按钮组**：再次点击"..."按钮或点击"×"收纳
4. **自定义按钮**：点击齿轮图标进入自定义界面

### 视觉设计
- **主按钮**：黑色圆形背景，白色"..."图标
- **展开状态**：图标变为"×"，表示可以收纳
- **功能按钮**：与原来的按钮样式保持一致
- **自定义按钮**：灰色背景，齿轮图标

### 动画效果
- **展开动画**：按钮从下往上依次出现，带有缩放和透明度变化
- **收纳动画**：按钮从上往下依次消失
- **图标切换**：主按钮图标在"..."和"×"之间平滑切换

## 兼容性

- ✅ 保持所有原有功能不变
- ✅ 保持原有的触觉反馈
- ✅ 保持原有的日志记录
- ✅ 支持深色模式
- ✅ 支持所有设备尺寸
- ✅ 完整的中英文本地化支持

## 未来扩展

### 可能的改进
1. **更多自定义选项**：按钮顺序调整、图标自定义等
2. **手势支持**：长按展开、滑动操作等
3. **智能推荐**：根据使用频率自动调整常用按钮
4. **主题支持**：不同的按钮样式主题

### 性能优化
- 按钮状态缓存
- 动画性能优化
- 内存使用优化

---

**更新日期**：2025-07-09  
**版本**：v1.0  
**作者**：Augment Agent (Claude Sonnet 4)
