//
//  AddressDatabaseDemo.swift
//  NaviBatch
//
//  Created by Augment Agent on 2025-06-23.
//  演示地址库更新功能的示例代码
//

import Foundation
import CoreLocation

/// 演示地址库更新功能
class AddressDatabaseDemo {

    /// 演示用户点击地址搜索结果的完整流程
    static func demonstrateAddressSelection() async {
        print("🏠 === 地址库更新功能演示 ===")

        // 模拟用户点击的地址
        let selectedAddress = "928 Gellert Boulevard, Daly City, 94015"
        let coordinate = CLLocationCoordinate2D(latitude: 37.6879, longitude: -122.4702)

        print("🏠 用户点击地址: \(selectedAddress)")
        print("🏠 地址坐标: (\(coordinate.latitude), \(coordinate.longitude))")

        // 步骤1：检查地址库
        print("\n🏠 步骤1：检查地址库")
        let existingAddress = await UserAddressDatabase.shared.getValidatedAddress(for: selectedAddress)

        if let existing = existingAddress {
            print("🏠 ✅ 地址库命中!")
            print("🏠 缓存坐标: (\(existing.coordinate.latitude), \(existing.coordinate.longitude))")
            print("🏠 使用次数: \(existing.usageCount)")
            print("🏠 置信度: \(existing.confidence)")
            print("🏠 来源: \(existing.source.rawValue)")
            print("🏠 最后使用: \(existing.lastUsed)")
        } else {
            print("🏠 ❌ 地址库未命中，需要进行地理编码")

            // 步骤2：保存到地址库
            print("\n🏠 步骤2：保存新地址到地址库")
            await UserAddressDatabase.shared.saveValidatedAddress(
                selectedAddress,
                coordinate: coordinate,
                source: .manual,
                confidence: 0.95
            )
            print("🏠 ✅ 地址已保存到地址库")
        }

        // 步骤3：验证保存结果
        print("\n🏠 步骤3：验证地址库状态")
        let finalResult = await UserAddressDatabase.shared.getValidatedAddress(for: selectedAddress)

        if let result = finalResult {
            print("🏠 ✅ 验证成功!")
            print("🏠 地址: \(selectedAddress)")
            print("🏠 坐标: (\(result.coordinate.latitude), \(result.coordinate.longitude))")
            print("🏠 使用次数: \(result.usageCount)")
            print("🏠 置信度: \(result.confidence)")
            print("🏠 来源: \(result.source.rawValue)")
        } else {
            print("🏠 ❌ 验证失败，地址未找到")
        }

        print("\n🏠 === 演示完成 ===")
    }

    /// 演示多个地址的批量处理
    static func demonstrateBatchProcessing() async {
        print("\n🏠 === 批量地址处理演示 ===")

        let addresses = [
            ("123 Main Street, San Francisco, CA", CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)),
            ("456 Oak Avenue, Los Angeles, CA", CLLocationCoordinate2D(latitude: 34.0522, longitude: -118.2437)),
            ("789 Pine Road, Seattle, WA", CLLocationCoordinate2D(latitude: 47.6062, longitude: -122.3321))
        ]

        for (address, coordinate) in addresses {
            print("\n🏠 处理地址: \(address)")

            // 检查地址库
            let existing = await UserAddressDatabase.shared.getValidatedAddress(for: address)

            if existing != nil {
                print("🏠 ✅ 地址库命中，跳过地理编码")
            } else {
                print("🏠 ❌ 地址库未命中，保存新地址")
                await UserAddressDatabase.shared.saveValidatedAddress(
                    address,
                    coordinate: coordinate,
                    source: .manual,
                    confidence: 0.90
                )
                print("🏠 ✅ 地址已保存")
            }
        }

        print("\n🏠 === 批量处理完成 ===")
    }

    /// 演示地址库统计信息
    @MainActor
    static func demonstrateStatistics() async {
        print("\n🏠 === 地址库统计信息演示 ===")

        // 检查地址库状态
        let isEnabled = UserAddressDatabase.shared.isEnabled
        print("🏠 地址库状态: \(isEnabled ? "启用" : "禁用")")

        if !isEnabled {
            print("🏠 ⚠️ 地址库已禁用，无法获取统计信息")
            print("\n🏠 === 统计信息演示完成 ===")
            return
        }

        // 获取实际的统计信息
        await UserAddressDatabase.shared.updateDatabaseStats()
        let stats = UserAddressDatabase.shared.databaseStats

        print("🏠 地址总数: \(stats.totalCount)")
        print("🏠 命中率: \(String(format: "%.1f%%", stats.hitRate * 100))")
        print("🏠 数据库大小: \(stats.databaseSize) bytes")

        if !stats.mostUsedAddresses.isEmpty {
            print("🏠 最常用地址:")
            for (index, address) in stats.mostUsedAddresses.prefix(3).enumerated() {
                print("🏠   \(index + 1). \(address)")
            }
        } else {
            print("🏠 暂无常用地址记录")
        }

        print("\n🏠 === 统计信息演示完成 ===")
    }
}

/// 使用示例
/*
// 在适当的地方调用演示函数
Task { @MainActor in
    await AddressDatabaseDemo.demonstrateAddressSelection()
    await AddressDatabaseDemo.demonstrateBatchProcessing()
    await AddressDatabaseDemo.demonstrateStatistics()
}
*/
