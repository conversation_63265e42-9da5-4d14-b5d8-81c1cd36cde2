{"_comment": "NaviBatch Address Import Sample - JSON Format (based on structured address fields)", "_supported_fields": {"latitude": "Latitude (required)", "longitude": "Longitude (required)", "unitNumber": "Unit/Room number", "streetNumber": "Street number", "streetName": "Street name", "suburb": "Suburb/Area", "state": "State/Province", "postalCode": "Postal code", "country": "Country", "packageCount": "Package count", "packageSize": "Package size: Small, Medium, Large", "packageType": "Package type: Box, Bag, Letter", "deliveryType": "Delivery type: Delivery, Pickup", "notes": "Notes", "trackingNumber": "Tracking number", "accessInstructions": "Access instructions"}, "addresses": [{"latitude": 37.5485, "longitude": -121.9886, "streetNumber": "202", "streetName": "Cedar Street", "suburb": "Fremont", "state": "CA", "postalCode": "94539", "country": "United States", "packageCount": 1, "packageSize": "Small", "packageType": "Box", "deliveryType": "Delivery", "notes": "Front door delivery", "trackingNumber": "TN001", "accessInstructions": "Ring doorbell"}, {"latitude": -33.8688, "longitude": 151.2093, "unitNumber": null, "streetNumber": "42", "streetName": "Wallaby Way", "suburb": "Sydney", "state": "NSW", "postalCode": "2000", "country": "Australia", "packageCount": 1, "packageSize": "Small", "packageType": "Bag", "deliveryType": "Delivery", "notes": "Please leave at front door", "trackingNumber": "TN123456790", "accessInstructions": "Ring doorbell twice"}, {"latitude": -37.8136, "longitude": 144.9631, "unitNumber": "Level 5", "streetNumber": "380", "streetName": "Lonsdale Street", "suburb": "Melbourne", "state": "VIC", "postalCode": "3000", "country": "Australia", "packageCount": 3, "packageSize": "Large", "packageType": "Box", "deliveryType": "Delivery", "notes": "Requires signature", "trackingNumber": "TN123456791", "accessInstructions": "Business hours only"}, {"latitude": -33.8915, "longitude": 151.2767, "streetNumber": "75", "streetName": "Bondi Road", "suburb": "<PERSON><PERSON>", "state": "NSW", "postalCode": "2026", "country": "Australia", "packageCount": 1, "packageSize": "Medium", "packageType": "Bag", "deliveryType": "Delivery", "notes": "Call before delivery"}, {"latitude": -27.4698, "longitude": 153.0251, "streetNumber": "12", "streetName": "Adelaide Street", "suburb": "Brisbane", "state": "QLD", "postalCode": "4000", "country": "Australia", "packageCount": 4, "packageSize": "Small", "packageType": "Letter", "deliveryType": "Pickup", "notes": "Delivery before noon"}]}