# NaviBatch Complete Fields Example - CSV Format
# Based on actual structured address field design
# Note: latitude and longitude are required, all other fields are optional

Latitude,Longitude,UnitNumber,StreetNumber,StreetName,Suburb,State,PostalCode,Country,PackageCount,PackageSize,PackageType,DeliveryType,Notes,TrackingNumber,AccessInstructions
-33.8688,151.2093,"Suite 1205","200","George Street","Sydney","NSW","2000","Australia",2,Medium,Box,Delivery,"Office building reception - 9th floor",TN123456789,"Use main entrance, show ID at reception"
-33.8688,151.2093,,"42","Wallaby Way","Sydney","NSW","2000","Australia",1,<PERSON>,Bag,Delivery,"Please leave at front door if no answer",TN123456790,"Ring doorbell twice, side gate access available"
-37.8136,144.9631,"Level 5","380","Lonsdale Street","Melbourne","VIC","3000","Australia",3,<PERSON>,<PERSON>,Delivery,"Business delivery - requires signature",TN123456791,"Business hours only (9AM-5PM), loading dock entrance"
-33.8915,151.2767,"Apt 12","75","Bondi Road","<PERSON>i","NSW","2026","Australia",1,Medium,Bag,Delivery,"Call customer before delivery",TN123456792,"Use side entrance, intercom #12"
-27.4698,153.0251,,"12","Adelaide Street","Brisbane","QLD","4000","Australia",4,Small,Letter,Pickup,"Customer will collect from depot",TN123456793,"Reception desk, ask for John Smith"
-37.8136,144.9631,"Shop 23","456","Collins Street","Melbourne","VIC","3000","Australia",2,Medium,Box,Delivery,"Fragile items - handle with care",TN123456794,"Ground floor shop, loading zone available"
-33.8688,151.2093,"Unit 15B","789","Pitt Street","Sydney","NSW","2000","Australia",1,Large,Box,Delivery,"High value package - ID required",TN123456795,"Residential building, concierge service"
-27.4698,153.0251,,"321","Queen Street","Brisbane","QLD","4000","Australia",3,Small,Bag,Delivery,"Multiple packages for same address",TN123456796,"Office building, 15th floor"
-37.8136,144.9631,"Level 2","654","Flinders Street","Melbourne","VIC","3000","Australia",1,Medium,Letter,Delivery,"Express delivery required",TN123456797,"Medical center, urgent delivery"
-33.8688,151.2093,"Penthouse","987","King Street","Sydney","NSW","2000","Australia",2,Large,Box,Pickup,"Customer prefers pickup service",TN123456798,"Private elevator access, security code 1234"

# Simplified examples - coordinates and basic address information only
-37.8136,144.9631,,"147","Swanston Street","Melbourne","VIC","3000","Australia"
-37.8136,144.9631,,"258","Bourke Street","Melbourne","VIC","3000","Australia"
-37.8136,144.9631,,"369","Elizabeth Street","Melbourne","VIC","3000","Australia"
-37.8141,145.0183,,"741","Chapel Street","South Yarra","VIC","3141","Australia"
-37.8563,145.0270,,"852","High Street","Armadale","VIC","3143","Australia"

# Examples with basic information
-37.8563,145.0270,"Unit 7","963","Toorak Road","Toorak","VIC","3142","Australia",1,Small,Bag,Delivery,"Luxury apartment complex"
-37.8563,145.0270,,"159","Chapel Street","Prahran","VIC","3181","Australia",2,Medium,Box,Delivery,"Commercial delivery"
-37.8563,145.0270,"Shop 5","357","Smith Street","Collingwood","VIC","3066","Australia",1,Large,Box,Delivery,"Retail store delivery"
