import Foundation
import SwiftUI

// MARK: - AI配置管理
class AIConfiguration: ObservableObject {
    static let shared = AIConfiguration()

    // 使用AppStorage来持久化设置
    @AppStorage("useAIRecognition") var useAIRecognition: Bool = true
    @AppStorage("preferredAIModel") var preferredAIModel: String = "gemma-3-27b"
    @AppStorage("aiConfidenceThreshold") var aiConfidenceThreshold: Double = 0.7
    @AppStorage("enableFallbackOCR") var enableFallbackOCR: Bool = false
    @AppStorage("maxRetryAttempts") var maxRetryAttempts: Int = 3
    @AppStorage("recognitionMode") var recognitionMode: String = "aiOnly"

    private init() {}

    // MARK: - AI模型配置
    struct AIModel {
        let id: String
        let name: String
        let description: String
        let isFree: Bool
        let supportsVision: Bool
    }

    // 可用的识别引擎列表
    let availableModels: [AIModel] = [
        AIModel(
            id: "gemma-3-27b",
            name: "高精度模式",
            description: "最强的识别算法，准确率最高",
            isFree: true,
            supportsVision: true
        ),
        AIModel(
            id: "gemma-3-9b",
            name: "快速模式",
            description: "中等精度，识别速度快",
            isFree: true,
            supportsVision: true
        ),
        AIModel(
            id: "gemma-2-27b",
            name: "标准模式",
            description: "标准精度，稳定可靠",
            isFree: true,
            supportsVision: true
        )
    ]

    // MARK: - 配置方法
    func getSelectedModel() -> AIModel? {
        return availableModels.first { $0.id == preferredAIModel }
    }

    func updatePreferredModel(_ modelId: String) {
        preferredAIModel = modelId
    }

    func resetToDefaults() {
        useAIRecognition = true
        preferredAIModel = "gemma-3-27b"
        aiConfidenceThreshold = 0.7
        enableFallbackOCR = true
        maxRetryAttempts = 3
        recognitionMode = "aiOnly"  // 🎯 默认使用AI Only模式，完全跳过OCR
    }

    // 🎯 新增：设置为AI Only模式的便捷方法
    func setToAIOnlyMode() {
        recognitionMode = "aiOnly"
        useAIRecognition = true
        Logger.aiInfo("🚀 已设置为AI Only模式，将完全跳过OCR处理")
    }

    // 🎯 新增：快速切换到纯AI模式（用于SpeedX优化）
    func enablePureAIModeForSpeedX() {
        recognitionMode = "aiOnly"
        useAIRecognition = true
        preferredAIModel = "gemma-3-27b"  // 使用最高精度模型
        aiConfidenceThreshold = 0.8       // 提高置信度阈值
        enableFallbackOCR = false         // 禁用OCR降级
        Logger.aiInfo("🎯 SpeedX优化：已启用纯AI模式，禁用OCR，使用最高精度模型")
    }

    // MARK: - 识别模式配置
    enum RecognitionMode: String, CaseIterable {
        case ocrFirst = "ocrFirst"
        case aiFirst = "aiFirst"
        case ocrOnly = "ocrOnly"
        case aiOnly = "aiOnly"

        var displayName: String {
            switch self {
            case .ocrFirst:
                return "智能识别"
            case .aiFirst:
                return "高级识别 (推荐🎯)"
            case .ocrOnly:
                return "基础识别"
            case .aiOnly:
                return "快速识别"
            }
        }

        var description: String {
            switch self {
            case .ocrFirst:
                return "结合多种技术，提供最佳识别效果"
            case .aiFirst:
                return "使用先进算法，能提取更多信息，推荐使用"
            case .ocrOnly:
                return "使用本地技术，节省网络资源"
            case .aiOnly:
                return "使用高级算法，速度快，准确率高"
            }
        }
    }

    var currentRecognitionMode: RecognitionMode {
        return RecognitionMode(rawValue: recognitionMode) ?? .aiFirst
    }

    func setRecognitionMode(_ mode: RecognitionMode) {
        recognitionMode = mode.rawValue
    }

    // MARK: - 获取用户选择的识别模式
    func getOptimalRecognitionMode() -> RecognitionMode {
        // 🎯 尊重用户的手动设置，不再自动选择
        let userMode = currentRecognitionMode

        // 如果用户禁用了AI识别，强制使用OCR模式
        if !useAIRecognition {
            Logger.aiInfo("🔧 AI识别已禁用，强制使用OCR模式")
            return .ocrOnly
        }

        Logger.aiInfo("🎯 使用用户选择的识别模式: \(userMode.displayName)")
        return userMode
    }

    // MARK: - 验证方法
    func isValidConfiguration() -> Bool {
        return availableModels.contains { $0.id == preferredAIModel }
    }

    func getConfigurationSummary() -> String {
        let model = getSelectedModel()?.name ?? "unknown_algorithm".localized
        let status = useAIRecognition ? "enabled".localized : "disabled".localized
        return "advanced_recognition".localized + ": \(status), " + "algorithm".localized + ": \(model), " + "accuracy_threshold".localized + ": \(Int(aiConfidenceThreshold * 100))%"
    }
}

// MARK: - AI设置视图 (仅开发模式可见)
#if DEBUG
struct AISettingsView: View {
    @StateObject private var aiConfig = AIConfiguration.shared

    var body: some View {
        Form {
            Section("recognition_settings".localized) {
                // 识别模式选择
                Picker("recognition_mode".localized, selection: $aiConfig.recognitionMode) {
                    ForEach(AIConfiguration.RecognitionMode.allCases, id: \.rawValue) { mode in
                        VStack(alignment: .leading) {
                            Text(mode.displayName)
                                .font(.headline)
                            Text(mode.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .tag(mode.rawValue)
                    }
                }
                .pickerStyle(NavigationLinkPickerStyle())

                Toggle("enable_advanced_recognition".localized, isOn: $aiConfig.useAIRecognition)
                    .onChange(of: aiConfig.useAIRecognition) { _, newValue in
                        print("Advanced recognition setting: \(newValue ? "enabled" : "disabled")")
                    }

                if aiConfig.useAIRecognition {
                    Picker("algorithm_selection".localized, selection: $aiConfig.preferredAIModel) {
                        ForEach(aiConfig.availableModels, id: \.id) { model in
                            VStack(alignment: .leading) {
                                Text(model.name)
                                    .font(.headline)
                                Text(model.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .tag(model.id)
                        }
                    }
                    .pickerStyle(NavigationLinkPickerStyle())

                    VStack(alignment: .leading) {
                        Text("accuracy_threshold".localized + ": \(Int(aiConfig.aiConfidenceThreshold * 100))%")
                            .font(.caption)

                        Slider(value: $aiConfig.aiConfidenceThreshold, in: 0.5...1.0, step: 0.05)
                    }
                }
            }

            Section("fallback_options".localized) {
                Toggle("auto_fallback_to_basic".localized, isOn: $aiConfig.enableFallbackOCR)

                Stepper("max_retry_attempts".localized + ": \(aiConfig.maxRetryAttempts)",
                       value: $aiConfig.maxRetryAttempts,
                       in: 1...5)
            }

            Section("configuration_info".localized) {
                Text(aiConfig.getConfigurationSummary())
                    .font(.caption)
                    .foregroundColor(.secondary)

                NavigationLink("view_recognition_logs".localized) {
                    AILogViewer()
                }

                Button("reset_to_defaults".localized) {
                    aiConfig.resetToDefaults()
                }
                .foregroundColor(.red)
            }
        }
        .navigationTitle("recognition_settings".localized)
        .navigationBarTitleDisplayMode(.inline)
    }
}
#endif

// MARK: - 预览
#if DEBUG
#Preview {
    NavigationView {
        AISettingsView()
    }
}
#endif
