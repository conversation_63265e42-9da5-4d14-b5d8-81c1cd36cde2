//
//  Test_RegionClassification.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/23.
//  测试地区分类功能
//

import Foundation

/// 测试地区分类功能
class RegionClassificationTest {
    
    static func runTests() {
        print("🧪 开始测试地区分类功能...")
        
        // 测试美国快递
        testUSADelivery()
        
        // 测试澳洲快递
        testAustraliaDelivery()
        
        // 测试通用类型
        testUniversalTypes()
        
        print("✅ 地区分类测试完成!")
    }
    
    private static func testUSADelivery() {
        print("\n🇺🇸 测试美国快递分类:")
        
        let usaApps: [DeliveryAppType] = [.amazonFlex]
        
        for app in usaApps {
            assert(app.region == .usa, "❌ \(app.displayName) 应该属于美国地区")
            print("✅ \(app.displayName) -> 美国地区")
        }
        
        let usaCouriers = DeliveryAppType.courierTypesForRegion(.usa)
        print("📦 美国快递公司: \(usaCouriers.map { $0.displayName }.joined(separator: ", "))")
    }
    
    private static func testAustraliaDelivery() {
        print("\n🇦🇺 测试澳洲快递分类:")
        
        let australiaApps: [DeliveryAppType] = [.imile, .ldsEpod, .piggy, .uniuni, .gofo, .ywe, .speedx]
        
        for app in australiaApps {
            assert(app.region == .australia, "❌ \(app.displayName) 应该属于澳洲地区")
            print("✅ \(app.displayName) -> 澳洲地区")
        }
        
        let australiaCouriers = DeliveryAppType.courierTypesForRegion(.australia)
        print("📦 澳洲快递公司: \(australiaCouriers.map { $0.displayName }.joined(separator: ", "))")
    }
    
    private static func testUniversalTypes() {
        print("\n🌍 测试通用类型:")
        
        let universalApps: [DeliveryAppType] = [.justPhoto, .manual, .other]
        
        for app in universalApps {
            assert(app.region == .universal, "❌ \(app.displayName) 应该属于通用类型")
            print("✅ \(app.displayName) -> 通用类型")
        }
    }
}

// 如果直接运行此文件，执行测试
#if DEBUG
// RegionClassificationTest.runTests()
#endif
