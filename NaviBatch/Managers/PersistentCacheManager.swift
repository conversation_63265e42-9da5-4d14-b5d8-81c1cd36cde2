import Foundation
import SwiftData
import MapKit
import CoreLocation

/// 持久化距离数据管理器 - 管理路线和距离的数据库存储
@MainActor
class PersistentCacheManager {
    static let shared = PersistentCacheManager()

    private var modelContext: ModelContext?
    private let maxStoredEntries = 1000 // 最大存储条目数

    private init() {
        setupModelContext()
    }

    private func setupModelContext() {
        let container = getPersistentContainer()
        self.modelContext = container.mainContext
    }
    
    // MARK: - 距离数据存储

    /// 获取已存储的距离数据
    func getStoredDistance(from start: CLLocationCoordinate2D, to end: CLLocationCoordinate2D) -> Double? {
        guard let context = modelContext else { return nil }

        let dataKey = RouteCache.generateCacheKey(from: start, to: end)

        do {
            let descriptor = FetchDescriptor<DistanceCache>(
                predicate: #Predicate<DistanceCache> { data in
                    data.cacheKey == dataKey
                }
            )

            let results = try context.fetch(descriptor)
            // 手动过滤过期的数据
            if let storedDistance = results.first(where: { !$0.isExpired }) {
                // 更新使用统计
                storedDistance.updateUsage()
                try context.save()

                print("[PersistentCacheManager] 使用已存储距离: \(dataKey) -> \(storedDistance.distance/1000)km")
                return storedDistance.distance
            }
        } catch {
            print("[PersistentCacheManager] 获取存储距离失败: \(error)")
        }

        return nil
    }
    
    /// 存储距离数据 - 双向存储提高重用率
    func storeDistance(_ distance: Double, from start: CLLocationCoordinate2D, to end: CLLocationCoordinate2D) {
        guard let context = modelContext else { return }

        let dataKey1 = RouteCache.generateCacheKey(from: start, to: end)
        let dataKey2 = RouteCache.generateCacheKey(from: end, to: start) // 反向数据键

        do {
            // 存储正向距离
            let descriptor1 = FetchDescriptor<DistanceCache>(
                predicate: #Predicate<DistanceCache> { data in
                    data.cacheKey == dataKey1
                }
            )

            if let existing1 = try context.fetch(descriptor1).first {
                existing1.distance = distance
                existing1.updateUsage()
            } else {
                let newData1 = DistanceCache(cacheKey: dataKey1, distance: distance)
                context.insert(newData1)
            }

            // 存储反向距离（相同距离）
            let descriptor2 = FetchDescriptor<DistanceCache>(
                predicate: #Predicate<DistanceCache> { data in
                    data.cacheKey == dataKey2
                }
            )

            if let existing2 = try context.fetch(descriptor2).first {
                existing2.distance = distance
                existing2.updateUsage()
            } else {
                let newData2 = DistanceCache(cacheKey: dataKey2, distance: distance)
                context.insert(newData2)
            }

            try context.save()
            print("[PersistentCacheManager] 双向存储距离: \(dataKey1) <-> \(dataKey2) = \(distance/1000)km")

            // 清理过期数据
            cleanupExpiredCaches()

        } catch {
            print("[PersistentCacheManager] 存储距离失败: \(error)")
        }
    }
    
    // MARK: - 路线缓存
    
    /// 获取缓存的路线
    func getCachedRoute(from start: CLLocationCoordinate2D, to end: CLLocationCoordinate2D) -> (distance: Double, travelTime: Double, coordinates: [CLLocationCoordinate2D]?)? {
        guard let context = modelContext else { return nil }
        
        let cacheKey = RouteCache.generateCacheKey(from: start, to: end)
        
        do {
            let descriptor = FetchDescriptor<RouteCache>(
                predicate: #Predicate<RouteCache> { cache in
                    cache.cacheKey == cacheKey && cache.isValid
                }
            )
            
            let results = try context.fetch(descriptor)
            // 手动过滤过期的缓存
            if let cachedRoute = results.first(where: { !$0.isExpired }) {
                // 更新使用统计
                cachedRoute.updateUsage()
                try context.save()

                print("[PersistentCacheManager] 使用缓存路线: \(cacheKey)")
                return (
                    distance: cachedRoute.distance,
                    travelTime: cachedRoute.expectedTravelTime,
                    coordinates: cachedRoute.routeCoordinates
                )
            }
        } catch {
            print("[PersistentCacheManager] 获取缓存路线失败: \(error)")
        }
        
        return nil
    }
    
    /// 缓存路线
    func cacheRoute(
        _ route: MKRoute,
        from start: CLLocationCoordinate2D,
        to end: CLLocationCoordinate2D
    ) {
        guard let context = modelContext else { return }
        
        let cacheKey = RouteCache.generateCacheKey(from: start, to: end)
        
        do {
            // 提取路线坐标
            let coordinates = route.polyline.coordinates
            
            // 检查是否已存在
            let descriptor = FetchDescriptor<RouteCache>(
                predicate: #Predicate<RouteCache> { cache in
                    cache.cacheKey == cacheKey
                }
            )
            
            if let existing = try context.fetch(descriptor).first {
                // 更新现有缓存
                existing.distance = route.distance
                existing.expectedTravelTime = route.expectedTravelTime
                existing.updateUsage()
                existing.isValid = true
            } else {
                // 创建新缓存
                let newCache = RouteCache(
                    cacheKey: cacheKey,
                    startCoordinate: start,
                    endCoordinate: end,
                    distance: route.distance,
                    expectedTravelTime: route.expectedTravelTime,
                    routeCoordinates: coordinates
                )
                context.insert(newCache)
            }
            
            try context.save()
            print("[PersistentCacheManager] 缓存路线: \(cacheKey) -> \(route.distance/1000)km")
            
            // 清理过期缓存
            cleanupExpiredCaches()
            
        } catch {
            print("[PersistentCacheManager] 缓存路线失败: \(error)")
        }
    }
    
    // MARK: - 缓存管理
    
    /// 清理过期缓存
    private func cleanupExpiredCaches() {
        guard let context = modelContext else { return }
        
        do {
            // 清理过期的距离缓存
            let distanceDescriptor = FetchDescriptor<DistanceCache>()
            let allDistances = try context.fetch(distanceDescriptor)
            let expiredDistances = allDistances.filter { $0.isExpired }
            for cache in expiredDistances {
                context.delete(cache)
            }

            // 清理过期的路线缓存
            let routeDescriptor = FetchDescriptor<RouteCache>()
            let allRoutes = try context.fetch(routeDescriptor)
            let expiredRoutes = allRoutes.filter { $0.isExpired }
            for cache in expiredRoutes {
                context.delete(cache)
            }
            
            if !expiredDistances.isEmpty || !expiredRoutes.isEmpty {
                try context.save()
                print("[PersistentCacheManager] 清理过期缓存: 距离\(expiredDistances.count)条, 路线\(expiredRoutes.count)条")
            }
            
        } catch {
            print("[PersistentCacheManager] 清理过期缓存失败: \(error)")
        }
    }
    
    /// 获取缓存统计信息
    func getCacheStats() -> (distanceCount: Int, routeCount: Int) {
        guard let context = modelContext else { return (0, 0) }
        
        do {
            let distanceDescriptor = FetchDescriptor<DistanceCache>()
            let routeDescriptor = FetchDescriptor<RouteCache>()
            
            let distanceCount = try context.fetchCount(distanceDescriptor)
            let routeCount = try context.fetchCount(routeDescriptor)
            
            return (distanceCount, routeCount)
        } catch {
            print("[PersistentCacheManager] 获取缓存统计失败: \(error)")
            return (0, 0)
        }
    }
    
    /// 清除所有缓存
    func clearAllCaches() {
        guard let context = modelContext else { return }
        
        do {
            // 删除所有距离缓存
            let distanceDescriptor = FetchDescriptor<DistanceCache>()
            let distances = try context.fetch(distanceDescriptor)
            for cache in distances {
                context.delete(cache)
            }
            
            // 删除所有路线缓存
            let routeDescriptor = FetchDescriptor<RouteCache>()
            let routes = try context.fetch(routeDescriptor)
            for cache in routes {
                context.delete(cache)
            }
            
            try context.save()
            print("[PersistentCacheManager] 已清除所有缓存")
            
        } catch {
            print("[PersistentCacheManager] 清除缓存失败: \(error)")
        }
    }

    /// 清理旧格式的缓存数据（缓存键格式改变后使用）
    func clearLegacyCache() {
        guard let context = modelContext else { return }

        do {
            // 清理包含高精度坐标的旧缓存（超过4位小数的）
            let distanceFetchDescriptor = FetchDescriptor<DistanceCache>()
            let distanceCaches = try context.fetch(distanceFetchDescriptor)

            var deletedCount = 0
            for cache in distanceCaches {
                // 检查缓存键是否包含超过4位小数的坐标
                if cache.cacheKey.contains(where: { $0 == "." }) {
                    let components = cache.cacheKey.components(separatedBy: CharacterSet(charactersIn: ",-"))
                    var hasHighPrecision = false

                    for component in components {
                        if let dotIndex = component.firstIndex(of: ".") {
                            let decimalPart = String(component[component.index(after: dotIndex)...])
                            if decimalPart.count > 4 {
                                hasHighPrecision = true
                                break
                            }
                        }
                    }

                    if hasHighPrecision {
                        context.delete(cache)
                        deletedCount += 1
                    }
                }
            }

            try context.save()
            print("[PersistentCacheManager] 已清理 \(deletedCount) 个旧格式缓存项")
        } catch {
            print("[PersistentCacheManager] 清理旧缓存失败: \(error)")
        }
    }

    // MARK: - 两点距离缓存优化说明

    /// 🎯 新的缓存策略说明：
    /// 我们不再需要路线序列缓存，因为：
    /// 1. DirectionsAPIManager已经有完善的两点距离缓存（DistanceCache）
    /// 2. 任意路线都是两点距离的累加：A→B→C = distance(A,B) + distance(B,C)
    /// 3. 两点距离的重用率远高于整个路线序列
    /// 4. 随着使用，两点距离缓存覆盖率会越来越高
    ///
    /// 优势：
    /// - 最大化缓存重用率
    /// - 最小化API调用
    /// - 渐进式缓存建设
    /// - 精确的距离计算
}
