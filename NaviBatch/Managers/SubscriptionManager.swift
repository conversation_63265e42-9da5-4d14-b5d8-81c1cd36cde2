import Foundation
import SwiftUI

/// 订阅计划类型
enum SubscriptionTier: String, Codable, CaseIterable {
    case free = "Starter"
    case pro = "Pro"
    case expert = "Expert"

    // 产品ID - 用于StoreKit
    var productID: String {
        switch self {
        case .free:
            return ""  // 免费版没有产品ID
        case .pro:
            return "com.navibatch.subscription.monthly"
        case .expert:
            return "com.navibatch.subscription.annual"
        }
    }

    var maxStopsPerRoute: Int {
        switch self {
        case .free:
            return 20  // 免费版支持20个地址
        case .pro, .expert:
            return Int.max // 无限地址
        }
    }

    // 免费版优化限制
    var maxOptimizableStops: Int {
        switch self {
        case .free:
            return 20  // 与maxStopsPerRoute保持一致
        case .pro, .expert:
            return Int.max // 无限优化能力，与无限stops保持一致
        }
    }

    var allowsAutoGrouping: Bool {
        switch self {
        case .free:
            return true  // 免费版现在允许有限的Auto Group功能
        case .pro, .expert:
            return true
        }
    }

    // 免费版Auto Group限制：所有组内地址总数
    var maxAutoGroupAddresses: Int {
        switch self {
        case .free:
            return 14  // 免费版最多14个地址可以加入组
        case .pro, .expert:
            return 14  // 付费版每组最多14个地址（Apple推送限制）
        }
    }

    // 免费版分组数量限制
    var maxGroupsPerRoute: Int {
        switch self {
        case .free:
            return 1   // 免费版最多创建1个分组
        case .pro, .expert:
            return Int.max // 付费版无限分组
        }
    }

    var allowsLocationFeatures: Bool {
        switch self {
        case .free:
            return false
        case .pro, .expert:
            return true
        }
    }

    var allowsAddNewAddress: Bool {
        switch self {
        case .free:
            return true  // 免费用户可以添加地址，但有数量限制
        case .pro, .expert:
            return true
        }
    }

    var allowsTopButtonsAccess: Bool {
        switch self {
        case .free:
            return false
        case .pro, .expert:
            return true
        }
    }

    var allowsPackageFinder: Bool {
        switch self {
        case .free:
            return true  // 免费用户也可以使用包裹查找功能
        case .pro, .expert:
            return true
        }
    }

    var allowsVideoProcessing: Bool {
        switch self {
        case .free:
            return false  // 免费用户不能使用录屏转长图功能
        case .pro, .expert:
            return true   // Pro和Expert用户可以使用录屏转长图
        }
    }

    var price: String {
        switch self {
        case .free:
            return "free_tier_price".localized
        case .pro:
            return "pro_tier_price".localized
        case .expert:
            return "expert_tier_price".localized
        }
    }

    var rawPrice: Decimal {
        switch self {
        case .free:
            return 0
        case .pro:
            return 29.99  // 当前价格，明天降为$9.99
        case .expert:
            return 249.99  // 当前价格，明天降为$59.99
        }
    }

    var savingsPercentage: Int {
        switch self {
        case .expert:
            // 计算年度计划相比按月付费的节省比例
            // 按月付费一年 = $9.99 * 12 = $119.88
            // 年度计划 = $59.99
            // 节省 = ($119.88 - $59.99) / $119.88 ≈ 50%
            return 50
        default:
            return 0
        }
    }

    var description: String {
        switch self {
        case .free:
            return "free_tier_description".localized
        case .pro:
            return "pro_tier_description".localized
        case .expert:
            return "expert_tier_description".localized
        }
    }
}

/// 订阅管理器
class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()

    @Published private(set) var currentTier: SubscriptionTier
    @Published private(set) var isInFreeTrial: Bool = false
    @Published private(set) var freeTrialEndDate: Date?
    @Published private(set) var subscriptionExpirationDate: Date?

    // 这些状态变量已被移除，因为我们现在直接使用 sheet 修饰符显示订阅相关视图
    // 保留 currentTier 变量，因为它仍然被使用

    private let userDefaults = UserDefaults.standard
    private let tierKey = "userSubscriptionTier"
    private let freeTrialKey = "isInFreeTrial"
    private let freeTrialEndDateKey = "freeTrialEndDate"
    private let subscriptionExpirationKey = "subscriptionExpirationDate"

    private init() {
        // 从UserDefaults加载订阅状态，默认为免费版
        if let savedTier = userDefaults.string(forKey: tierKey),
           let tier = SubscriptionTier(rawValue: savedTier) {
            self.currentTier = tier
        } else {
            self.currentTier = .free
        }

        // 加载试用期状态
        self.isInFreeTrial = userDefaults.bool(forKey: freeTrialKey)
        if let endDateData = userDefaults.object(forKey: freeTrialEndDateKey) as? Date {
            self.freeTrialEndDate = endDateData

            // 检查试用期是否已过期
            if endDateData < Date() {
                #if DEBUG
                print("[DEBUG] SubscriptionManager - 试用期已过期，自动结束试用")
                #endif
                self.isInFreeTrial = false
                userDefaults.set(false, forKey: freeTrialKey)
                userDefaults.removeObject(forKey: freeTrialEndDateKey)
                self.freeTrialEndDate = nil

                // 发送试用期结束通知
                NotificationCenter.default.post(
                    name: Notification.Name("FreeTrialEnded"),
                    object: nil
                )
            }
        }

        // 加载订阅到期时间
        if let expirationDate = userDefaults.object(forKey: subscriptionExpirationKey) as? Date {
            self.subscriptionExpirationDate = expirationDate

            // 检查订阅是否已过期
            if expirationDate < Date() && currentTier != .free {
                #if DEBUG
                print("[DEBUG] SubscriptionManager - 订阅已过期，自动降级到免费版")
                #endif
                self.currentTier = .free
                userDefaults.set(SubscriptionTier.free.rawValue, forKey: tierKey)
                userDefaults.removeObject(forKey: subscriptionExpirationKey)
                self.subscriptionExpirationDate = nil

                // 发送订阅过期通知
                NotificationCenter.default.post(
                    name: Notification.Name("SubscriptionExpired"),
                    object: nil
                )
            }
        }

        #if DEBUG
        print("[DEBUG] SubscriptionManager - 初始化订阅管理器，当前订阅级别: \(currentTier.rawValue), 试用期状态: \(isInFreeTrial)")
        #endif
        logInfo("初始化订阅管理器，当前订阅级别: \(currentTier.rawValue), 试用期状态: \(isInFreeTrial)")
    }

    // 这些方法已被移除，因为我们现在直接使用 sheet 修饰符显示订阅相关视图

    // 更新订阅状态
    func updateSubscription(to tier: SubscriptionTier, expirationDate: Date? = nil) {
        #if DEBUG
        print("[DEBUG] SubscriptionManager - 开始更新订阅状态，从 \(currentTier.rawValue) 到 \(tier.rawValue)")
        #endif

        // 在实际应用中，这里应该连接到 StoreKit 进行实际的购买流程
        // 只有在购买成功后才更新订阅状态

        // 更新订阅状态
        currentTier = tier
        userDefaults.set(tier.rawValue, forKey: tierKey)

        // 更新订阅到期时间
        if let expirationDate = expirationDate {
            self.subscriptionExpirationDate = expirationDate
            userDefaults.set(expirationDate, forKey: subscriptionExpirationKey)
            #if DEBUG
            print("[DEBUG] SubscriptionManager - 设置订阅到期时间: \(expirationDate)")
            #endif
        } else if tier == .free {
            // 如果降级到免费版，清除到期时间
            self.subscriptionExpirationDate = nil
            userDefaults.removeObject(forKey: subscriptionExpirationKey)
        }

        // 同步到磁盘确保保存
        userDefaults.synchronize()

        #if DEBUG
        print("[DEBUG] SubscriptionManager - 用户订阅已更新为: \(tier.rawValue)")
        #endif
        logInfo("用户订阅已更新为: \(tier.rawValue)")

        // 发送通知，通知其他组件订阅状态已更新
        NotificationCenter.default.post(
            name: Notification.Name("SubscriptionStatusChanged"),
            object: tier.rawValue
        )
    }

    // 检查用户是否可以添加更多停靠点（不包括start和end点）
    func canAddMoreStops(to route: Route) -> Bool {
        let stopPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
        return stopPoints.count < currentTier.maxStopsPerRoute
    }

    // 获取剩余可添加的停靠点数量（不包括start和end点）
    func remainingStopsAllowed(for route: Route) -> Int {
        let stopPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
        return max(0, currentTier.maxStopsPerRoute - stopPoints.count)
    }

    // 检查用户是否可以使用自动分组功能（包含试用期）
    func canUseAutoGrouping() -> Bool {
        return currentTier.allowsAutoGrouping || isInFreeTrial
    }

    // 检查当前路线中组内地址总数
    func getCurrentGroupedAddressCount(for route: Route) -> Int {
        let groupedPoints = route.points.filter { $0.isAssignedToGroup }
        let count = groupedPoints.count

        // 添加详细日志
        logInfo("SubscriptionManager.getCurrentGroupedAddressCount - 总点数: \(route.points.count), 已分组点数: \(count)")

        // 列出已分组的点
        for (index, point) in groupedPoints.enumerated() {
            logInfo("已分组点\(index + 1): \(point.primaryAddress) - 组号: \(point.assignedGroupNumber ?? -1)")
        }

        return count
    }

    // 检查是否可以将更多地址加入组
    func canAddMoreAddressesToGroups(for route: Route, additionalCount: Int = 1) -> Bool {
        // 🎯 修复：试用期用户或付费用户都应该有无限制权限
        if currentTier != .free || isInFreeTrial {
            return true // 付费用户或试用期用户无限制
        }

        let currentGroupedCount = getCurrentGroupedAddressCount(for: route)
        let maxAllowed = currentTier.maxAutoGroupAddresses
        return (currentGroupedCount + additionalCount) <= maxAllowed
    }

    // 获取还可以加入组的地址数量
    func remainingGroupAddressSlots(for route: Route) -> Int {
        // 🎯 修复：试用期用户或付费用户都应该有无限制权限
        if currentTier != .free || isInFreeTrial {
            logInfo("SubscriptionManager.remainingGroupAddressSlots - 付费用户或试用期用户，无限制")
            return Int.max // 付费用户或试用期用户无限制
        }

        let currentGroupedCount = getCurrentGroupedAddressCount(for: route)
        let maxAllowed = currentTier.maxAutoGroupAddresses
        let remaining = max(0, maxAllowed - currentGroupedCount)

        logInfo("SubscriptionManager.remainingGroupAddressSlots - 免费用户: 已分组\(currentGroupedCount)/\(maxAllowed), 剩余\(remaining)个")

        return remaining
    }

    // 检查是否可以创建更多分组
    func canCreateMoreGroups(for route: Route) -> Bool {
        // 🎯 修复：试用期用户或付费用户都应该有无限制权限
        if currentTier != .free || isInFreeTrial {
            return true // 付费用户或试用期用户无限制
        }

        let currentGroupCount = getCurrentGroupCount(for: route)
        return currentGroupCount < currentTier.maxGroupsPerRoute
    }

    // 获取当前路线的分组数量
    func getCurrentGroupCount(for route: Route) -> Int {
        // 通过查询所有分组来获取当前路线的分组数量
        // 这里需要通过分组中的点来判断是否属于当前路线
        _ = Set(route.points.map { $0.id })

        // 获取所有分组，然后筛选出属于当前路线的分组
        // 注意：这里需要在调用处提供ModelContext来查询分组
        return 0 // 临时返回，实际实现需要在调用处处理
    }

    // 获取剩余可创建的分组数量
    func remainingGroupSlots(for route: Route) -> Int {
        // 🎯 修复：试用期用户或付费用户都应该有无限制权限
        if currentTier != .free || isInFreeTrial {
            return Int.max // 付费用户或试用期用户无限制
        }

        let currentGroupCount = getCurrentGroupCount(for: route)
        return max(0, currentTier.maxGroupsPerRoute - currentGroupCount)
    }

    // 检查单个分组是否可以添加更多地址
    func canAddMoreAddressesToSingleGroup(currentGroupSize: Int, additionalCount: Int = 1) -> Bool {
        let maxPerGroup = currentTier.maxAutoGroupAddresses
        return (currentGroupSize + additionalCount) <= maxPerGroup
    }

    // 获取单个分组剩余可添加的地址数量
    func remainingAddressesForSingleGroup(currentGroupSize: Int) -> Int {
        let maxPerGroup = currentTier.maxAutoGroupAddresses
        return max(0, maxPerGroup - currentGroupSize)
    }

    // 启动免费试用
    func startFreeTrial() {
        let trialEndDate = Calendar.current.date(byAdding: .day, value: 60, to: Date()) ?? Date()
        userDefaults.set(trialEndDate, forKey: "TrialEndDate")
        userDefaults.set(true, forKey: "IsInFreeTrial")
        userDefaults.synchronize()

        // 通知试用推广管理器
        TrialPromotionManager.shared.userStartedTrial()

        // 发送通知
        NotificationCenter.default.post(
            name: Notification.Name("TrialStarted"),
            object: trialEndDate
        )

        logInfo("用户开始60天免费试用，试用期至: \(trialEndDate)")
    }

    // 检查用户是否可以访问位置功能（包含试用期）
    func canAccessLocationFeatures() -> Bool {
        return currentTier.allowsLocationFeatures || isInFreeTrial
    }

    // 检查用户是否可以添加新地址（包含试用期）
    func canAddNewAddress() -> Bool {
        return currentTier.allowsAddNewAddress || isInFreeTrial
    }

    // 检查用户是否可以访问顶部按钮（包含试用期）
    func canAccessTopButtons() -> Bool {
        return currentTier.allowsTopButtonsAccess || isInFreeTrial
    }

    // 检查用户是否可以使用包裹查找功能（包含试用期）
    func canUsePackageFinder() -> Bool {
        return currentTier.allowsPackageFinder || isInFreeTrial
    }

    // 检查是否需要显示升级提示（不包括start和end点）
    func shouldShowUpgradePrompt(for route: Route) -> Bool {
        // 🎯 修复：试用期用户不应该看到升级提示
        if isInFreeTrial {
            return false
        }
        let stopPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
        return currentTier == .free && stopPoints.count >= (currentTier.maxStopsPerRoute - 3)
    }

    // 检查是否超出免费版优化限制（不包括start和end点）
    func exceedsOptimizationLimit(for route: Route) -> Bool {
        // 🎯 修复：试用期用户不应该受到优化限制
        if isInFreeTrial {
            return false
        }
        let stopPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
        return currentTier == .free && stopPoints.count > currentTier.maxOptimizableStops
    }

    // 获取优化限制信息（不包括start和end点）
    func getOptimizationLimitInfo(for route: Route) -> (exceedsLimit: Bool, totalPoints: Int, optimizablePoints: Int) {
        let stopPoints = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
        let exceedsLimit = exceedsOptimizationLimit(for: route)
        let totalPoints = stopPoints.count
        let optimizablePoints = min(totalPoints, currentTier.maxOptimizableStops)

        return (exceedsLimit, totalPoints, optimizablePoints)
    }

    // 恢复购买
    func restorePurchases() {
        #if DEBUG
        print("[DEBUG] SubscriptionManager - 尝试恢复购买...")
        #endif

        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        // 使用StoreKitManager恢复购买
        Task {
            do {
                try await StoreKitManager.shared.restorePurchases()
            } catch {
                #if DEBUG
                print("[ERROR] SubscriptionManager - 恢复购买失败: \(error.localizedDescription)")
                #endif
                // 显示错误提示
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: "恢复购买失败: \(error.localizedDescription)"
                )
            }
        }
    }

    // MARK: - 收据验证

    /// 验证当前订阅状态（使用应用专用共享密钥）
    func validateSubscriptionReceipt() {
        #if DEBUG
        print("[DEBUG] SubscriptionManager - 开始验证订阅收据...")
        #endif

        Task {
            do {
                let validationResult = try await StoreKitManager.shared.validateReceipt()

                await MainActor.run {
                    if validationResult.isValid {
                        #if DEBUG
                        print("[DEBUG] SubscriptionManager - 收据验证成功")
                        #endif

                        // 根据验证结果更新订阅状态
                        updateSubscriptionFromValidation(validationResult)

                        // 移除成功提示Toast - 不再显示调试信息
                    } else {
                        #if DEBUG
                        print("[DEBUG] SubscriptionManager - 收据验证失败")
                        #endif
                        // 验证失败，可能需要重新购买
                        updateSubscription(to: .free)
                    }
                }
            } catch {
                await MainActor.run {
                    #if DEBUG
                    print("[ERROR] SubscriptionManager - 收据验证错误: \(error.localizedDescription)")
                    #endif

                    // 移除错误提示Toast - 不再显示调试信息
                }
            }
        }
    }

    /// 根据收据验证结果更新订阅状态
    private func updateSubscriptionFromValidation(_ validationResult: ReceiptValidationResult) {
        // 如果没有活跃订阅，设置为免费版
        guard !validationResult.activeSubscriptions.isEmpty else {
            updateSubscription(to: .free)
            updateTrialStatus(isInTrial: false, trialEndDate: nil)
            return
        }

        // 查找最高级别的活跃订阅
        var highestTier: SubscriptionTier = .free
        var latestExpirationDate: Date?
        var isInTrial = false
        var trialEndDate: Date?

        for subscription in validationResult.activeSubscriptions {
            if let tier = SubscriptionTier.allCases.first(where: { $0.productID == subscription.productId }) {
                // 比较订阅级别，选择最高级别
                if tier.maxStopsPerRoute > highestTier.maxStopsPerRoute {
                    highestTier = tier
                    latestExpirationDate = subscription.expirationDate
                    isInTrial = subscription.isInFreeTrial
                    // 🎯 修复：试用期结束时间应该是购买时间+60天，而不是订阅到期时间
                    if isInTrial {
                        // 试用期结束时间需要从StoreKit获取，这里暂时使用订阅到期时间
                        // 实际的试用期结束时间会在StoreKitManager中正确计算
                        trialEndDate = subscription.expirationDate
                    }
                } else if tier == highestTier && subscription.expirationDate > (latestExpirationDate ?? Date.distantPast) {
                    // 如果是同级别订阅，选择到期时间最晚的
                    latestExpirationDate = subscription.expirationDate
                    isInTrial = subscription.isInFreeTrial
                    // 🎯 修复：同样的试用期时间问题
                    if isInTrial {
                        trialEndDate = subscription.expirationDate
                    }
                }

                #if DEBUG
                print("[DEBUG] SubscriptionManager - 发现活跃订阅: \(subscription.productId), 到期时间: \(subscription.expirationDate), 试用期: \(subscription.isInFreeTrial)")
                #endif
            }
        }

        // 更新到最高级别的订阅，包含到期时间
        updateSubscription(to: highestTier, expirationDate: latestExpirationDate)

        // 更新试用期状态
        updateTrialStatus(isInTrial: isInTrial, trialEndDate: trialEndDate)

        #if DEBUG
        print("[DEBUG] SubscriptionManager - 根据收据验证更新订阅为: \(highestTier.rawValue), 到期时间: \(latestExpirationDate?.description ?? "无"), 试用期: \(isInTrial)")
        #endif
    }

    // MARK: - 免费试用期管理（基于App Store Connect配置）

    /// 检查是否有试用期权限（包括App Store试用期和付费订阅）
    func hasTrialAccess() -> Bool {
        return isInFreeTrial || currentTier != .free
    }

    /// 获取剩余试用天数（从App Store获取）
    func remainingTrialDays() -> Int {
        guard isInFreeTrial, let endDate = freeTrialEndDate else { return 0 }
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: endDate)
        return max(0, components.day ?? 0)
    }

    /// 更新试用期状态（从StoreKit获取）
    func updateTrialStatus(isInTrial: Bool, trialEndDate: Date? = nil) {
        let wasInTrial = isInFreeTrial

        isInFreeTrial = isInTrial
        freeTrialEndDate = trialEndDate

        // 保存到UserDefaults
        userDefaults.set(isInTrial, forKey: freeTrialKey)
        if let endDate = trialEndDate {
            userDefaults.set(endDate, forKey: freeTrialEndDateKey)
        } else {
            userDefaults.removeObject(forKey: freeTrialEndDateKey)
        }
        userDefaults.synchronize()

        #if DEBUG
        print("[DEBUG] SubscriptionManager - 更新试用期状态: \(isInTrial), 结束时间: \(trialEndDate?.description ?? "无")")
        #endif

        // 发送状态变化通知
        if wasInTrial && !isInTrial {
            // 试用期结束
            NotificationCenter.default.post(
                name: Notification.Name("FreeTrialEnded"),
                object: nil
            )
        } else if !wasInTrial && isInTrial {
            // 试用期开始
            NotificationCenter.default.post(
                name: Notification.Name("FreeTrialStarted"),
                object: trialEndDate
            )
        }
    }

    // MARK: - 订阅到期时间管理

    /// 获取订阅剩余天数
    func remainingSubscriptionDays() -> Int {
        guard let expirationDate = subscriptionExpirationDate, currentTier != .free else { return 0 }
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: expirationDate)
        return max(0, components.day ?? 0)
    }

    /// 检查订阅是否即将到期（7天内）
    func isSubscriptionExpiringSoon() -> Bool {
        guard subscriptionExpirationDate != nil, currentTier != .free else { return false }
        let daysRemaining = remainingSubscriptionDays()
        return daysRemaining <= 7 && daysRemaining > 0
    }

    /// 获取格式化的到期时间字符串
    func formattedExpirationDate() -> String? {
        // 🎯 修复：如果在试用期，显示试用期结束时间；否则显示订阅到期时间
        if isInFreeTrial, let trialEndDate = freeTrialEndDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .none
            return formatter.string(from: trialEndDate)
        }

        guard let subscriptionExpirationDate = subscriptionExpirationDate else { return nil }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: subscriptionExpirationDate)
    }

    /// 获取到期时间的简短描述
    func expirationDescription() -> String? {
        guard currentTier != .free else { return nil }

        // 🎯 修复：优先显示试用期信息，但也要考虑订阅状态
        if isInFreeTrial, freeTrialEndDate != nil {
            let daysLeft = remainingTrialDays()
            if daysLeft > 0 {
                return "trial_expires_in_days".localized(with: "\(daysLeft)")
            } else {
                // 试用期已过期，检查是否有付费订阅
                if subscriptionExpirationDate != nil {
                    let subscriptionDaysLeft = remainingSubscriptionDays()
                    if subscriptionDaysLeft > 0 {
                        return "expires_in_days".localized(with: "\(subscriptionDaysLeft)")
                    } else {
                        return "expired".localized
                    }
                }
                return "trial_expired".localized
            }
        } else if subscriptionExpirationDate != nil {
            let daysLeft = remainingSubscriptionDays()
            if daysLeft > 0 {
                return "expires_in_days".localized(with: "\(daysLeft)")
            } else {
                return "expired".localized
            }
        }

        return nil
    }

    // MARK: - 测试和调试方法

    /// 重置订阅状态用于测试（仅在开发环境中使用）
    @MainActor
    func resetSubscriptionForTesting() async {
        #if DEBUG
        print("[DEBUG] SubscriptionManager - 重置订阅状态用于测试")

        // 重置为免费版
        updateSubscription(to: .free, expirationDate: nil)

        // 重置试用期状态
        updateTrialStatus(isInTrial: false, trialEndDate: nil)

        // 清除UserDefaults中的相关数据
        userDefaults.removeObject(forKey: tierKey)
        userDefaults.removeObject(forKey: subscriptionExpirationKey)
        userDefaults.removeObject(forKey: freeTrialKey)
        userDefaults.removeObject(forKey: freeTrialEndDateKey)

        print("[DEBUG] SubscriptionManager - 订阅状态重置完成")
        #endif
    }
}
