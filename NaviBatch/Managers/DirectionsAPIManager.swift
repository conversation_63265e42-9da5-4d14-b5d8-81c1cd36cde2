import Foundation
import MapKit
import os.log

/// Apple Maps Directions API请求管理器
/// 负责控制请求频率，避免超出API限制（50请求/60秒）
class DirectionsAPIManager: ObservableObject {
    static let shared = DirectionsAPIManager()
    
    // MARK: - 配置
    private let maxRequestsPerMinute = 50 // 恢复到Apple的实际限制
    private let timeWindow: TimeInterval = 60 // 60秒时间窗口
    private let requestDelay: TimeInterval = 1.2 // 减少请求间隔到1.2秒
    private let batchSize = 5 // 增加每批请求数到5个
    private let batchDelay: TimeInterval = 6.0 // 减少批次间隔到6秒
    
    // MARK: - 状态管理
    private var requestTimestamps: [Date] = []
    private var lastRequestTime: Date = Date.distantPast
    private let queue = DispatchQueue(label: "com.navibatch.directions", qos: .userInitiated)
    
    // MARK: - 缓存（仅使用持久化缓存）
    // 移除内存缓存，简化架构，只使用持久化缓存
    
    private init() {
        // 清理旧格式的缓存数据（一次性操作）
        Task { @MainActor in
            PersistentCacheManager.shared.clearLegacyCache()
        }
    }
    
    // MARK: - 公共接口

    /// 只计算距离（不获取完整路线，更高效）
    func calculateDistance(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        completion: @escaping (Result<CLLocationDistance, Error>) -> Void
    ) {
        let cacheKey = generateCacheKey(from: source, to: destination)

        // 🔍 详细记录坐标信息
        let sourceStr = String(format: "(%.6f, %.6f)", source.latitude, source.longitude)
        let destStr = String(format: "(%.6f, %.6f)", destination.latitude, destination.longitude)
        print("🔍🔍🔍 DirectionsAPIManager.calculateDistance 被调用")
        print("🔍🔍🔍 起点: \(sourceStr)")
        print("🔍🔍🔍 终点: \(destStr)")
        print("🔍🔍🔍 缓存键: \(cacheKey)")

        print("🔍🔍🔍 检查持久化缓存")
        logInfo("🔍 DirectionsAPIManager - 检查持久化缓存: \(cacheKey)")

        // 检查数据库中的距离数据（异步但立即执行）
        Task { @MainActor in
            if let storedDistance = PersistentCacheManager.shared.getStoredDistance(from: source, to: destination) {
                print("🔍🔍🔍 ✅ 使用持久化缓存距离: \(storedDistance/1000)km")
                logInfo("🔍 DirectionsAPIManager - 使用持久化缓存距离: \(cacheKey), 距离: \(storedDistance/1000)km")
                completion(.success(storedDistance))
                return
            }

            // 需要API调用
            print("🔍🔍🔍 缓存未命中，开始API调用")
            self.calculateRoute(from: source, to: destination) { result in
                switch result {
                case .success(let route):
                    let distance = route.distance
                    print("🔍🔍🔍 ✅ API调用成功，距离: \(distance/1000)km")
                    // 存储到数据库
                    PersistentCacheManager.shared.storeDistance(distance, from: source, to: destination)
                    completion(.success(distance))
                case .failure(let error):
                    print("🔍🔍🔍 🚨 API调用失败: \(error.localizedDescription)")
                    completion(.failure(error))
                }
            }
        }
    }

    /// 带重试机制的距离计算方法
    private func calculateDistanceWithRetry(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        retryCount: Int,
        completion: @escaping (Result<CLLocationDistance, Error>) -> Void
    ) {
        let maxRetries = 2

        calculateDistance(from: source, to: destination) { result in
            switch result {
            case .success(let distance):
                completion(.success(distance))
            case .failure(let error):
                if retryCount < maxRetries {
                    print("🔄 DirectionsAPIManager - 重试距离计算 (\(retryCount + 1)/\(maxRetries)): \(error.localizedDescription)")
                    // 等待后重试
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        self.calculateDistanceWithRetry(from: source, to: destination, retryCount: retryCount + 1, completion: completion)
                    }
                } else {
                    print("🚨 DirectionsAPIManager - 距离计算最终失败: \(error.localizedDescription)")
                    completion(.failure(error))
                }
            }
        }
    }

    /// 批量计算距离（专门用于距离计算，更高效）
    func calculateMultipleDistances(
        waypoints: [CLLocationCoordinate2D],
        completion: @escaping (Result<[CLLocationDistance], Error>) -> Void
    ) {
        print("🔍🔍🔍 DirectionsAPIManager.calculateMultipleDistances 被调用了！")

        guard waypoints.count >= 2 else {
            print("🔍🔍🔍 DirectionsAPIManager - 坐标点数不足，返回错误")
            completion(.failure(NSError(domain: "DirectionsAPIManager", code: -2, userInfo: [NSLocalizedDescriptionKey: "至少需要2个点"])))
            return
        }

        print("🔍🔍🔍 DirectionsAPIManager - 开始批量距离计算，总段数: \(waypoints.count - 1)")
        logInfo("🔍 DirectionsAPIManager - 开始批量距离计算，总段数: \(waypoints.count - 1)")

        // 🔍 调试：打印所有坐标点
        for (index, waypoint) in waypoints.enumerated() {
            logInfo("🔍 DirectionsAPIManager - 坐标点 \(index + 1): \(waypoint)")
        }

        var distances: [CLLocationDistance?] = Array(repeating: nil, count: waypoints.count - 1)
        var completedCount = 0
        var hasError = false
        let group = DispatchGroup()

        // 分批处理，避免API限制
        let totalSegments = waypoints.count - 1
        let batches = stride(from: 0, to: totalSegments, by: batchSize).map { batchStart in
            Array(batchStart..<min(batchStart + batchSize, totalSegments))
        }

        for (batchIndex, batch) in batches.enumerated() {
            let batchDelay = Double(batchIndex) * self.batchDelay

            DispatchQueue.main.asyncAfter(deadline: .now() + batchDelay) { [weak self] in
                guard let self = self else { return }

                for segmentIndex in batch {
                    group.enter()

                    let segmentDelay = Double(segmentIndex - batch.first!) * self.requestDelay

                    DispatchQueue.main.asyncAfter(deadline: .now() + segmentDelay) { [weak self] in
                        print("🔍🔍🔍 DirectionsAPIManager - 开始计算距离段 \(segmentIndex + 1)")
                        self?.calculateDistanceWithRetry(from: waypoints[segmentIndex], to: waypoints[segmentIndex + 1], retryCount: 0) { result in
                            defer { group.leave() }

                            switch result {
                            case .success(let distance):
                                distances[segmentIndex] = distance
                                completedCount += 1
                                print("🔍🔍🔍 DirectionsAPIManager - 距离段 \(segmentIndex + 1) 计算成功: \(distance/1000) 公里")
                                self?.logInfo("DirectionsAPIManager - 距离段 \(segmentIndex + 1) 计算成功: \(distance/1000) 公里")
                            case .failure(let error):
                                print("🔍🔍🔍 DirectionsAPIManager - 距离段 \(segmentIndex + 1) 最终失败: \(error.localizedDescription)")
                                self?.logInfo("DirectionsAPIManager - 距离段 \(segmentIndex + 1) 最终失败: \(error.localizedDescription)")
                                if !hasError {
                                    hasError = true
                                }
                            }
                        }
                    }
                }
            }
        }

        group.notify(queue: .main) { [weak self] in
            if hasError && completedCount == 0 {
                completion(.failure(NSError(domain: "DirectionsAPIManager", code: -3, userInfo: [NSLocalizedDescriptionKey: "所有距离计算失败"])))
            } else {
                // 🔧 修复：保持原始数组结构，用0替代失败的距离
                let finalDistances = distances.map { $0 ?? 0 }
                self?.logInfo("DirectionsAPIManager - 批量距离计算完成，成功: \(completedCount)/\(waypoints.count - 1)")

                // 🔍 调试：打印原始距离数组
                self?.logInfo("🔍 DirectionsAPIManager - 原始距离数组: \(distances)")

                // 🔍 调试：打印每段距离
                for (index, distance) in finalDistances.enumerated() {
                    let originalDistance = distances[index]
                    self?.logInfo("DirectionsAPIManager - 距离段 \(index + 1): \(distance/1000) 公里 (原始: \(originalDistance?.description ?? "nil"))")
                }

                let totalDistance = finalDistances.reduce(0, +)
                self?.logInfo("🔍 DirectionsAPIManager - 计算总距离: \(totalDistance/1000) 公里")

                completion(.success(finalDistances))
            }
        }
    }
    
    /// 计算两点之间的路线（带频率控制）
    func calculateRoute(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        completion: @escaping (Result<MKRoute, Error>) -> Void
    ) {
        let cacheKey = generateCacheKey(from: source, to: destination)

        // 检查持久化路线缓存（异步）
        Task { @MainActor in
            if PersistentCacheManager.shared.getCachedRoute(from: source, to: destination) != nil {
                logInfo("DirectionsAPIManager - 发现持久化缓存路线: \(cacheKey)")
                // 注意：由于MKRoute无法直接重建，我们暂时跳过路线缓存，只使用距离缓存
                // 继续执行API调用
            }
        }
        
        queue.async { [weak self] in
            guard let self = self else { return }
            
            // 检查是否可以发送请求
            if !self.canMakeRequest() {
                let waitTime = self.getWaitTime()
                logInfo("DirectionsAPIManager - API限制，需要等待 \(waitTime) 秒")
                
                DispatchQueue.main.async {
                    let error = NSError(
                        domain: "DirectionsAPIManager",
                        code: -1,
                        userInfo: [NSLocalizedDescriptionKey: "API请求频率限制，请等待 \(Int(waitTime)) 秒"]
                    )
                    completion(.failure(error))
                }
                return
            }
            
            // 等待最小间隔
            let timeSinceLastRequest = Date().timeIntervalSince(self.lastRequestTime)
            if timeSinceLastRequest < self.requestDelay {
                let waitTime = self.requestDelay - timeSinceLastRequest
                Thread.sleep(forTimeInterval: waitTime)
            }
            
            // 发送请求
            self.performDirectionsRequest(from: source, to: destination, cacheKey: cacheKey, completion: completion)
        }
    }
    
    /// 批量计算路线（智能调度）
    func calculateMultipleRoutes(
        waypoints: [CLLocationCoordinate2D],
        completion: @escaping (Result<[MKRoute], Error>) -> Void
    ) {
        guard waypoints.count >= 2 else {
            completion(.failure(NSError(domain: "DirectionsAPIManager", code: -2, userInfo: [NSLocalizedDescriptionKey: "至少需要2个点"])))
            return
        }

        logInfo("🔍 DirectionsAPIManager - 开始批量计算路线")
        logInfo("🔍 总点数: \(waypoints.count), 需要计算段数: \(waypoints.count - 1)")

        // 🔍 详细记录每个路线段
        for i in 0..<(waypoints.count - 1) {
            let from = waypoints[i]
            let to = waypoints[i + 1]
            logInfo("🔍 路线段 \(i + 1): (\(String(format: "%.6f", from.latitude)), \(String(format: "%.6f", from.longitude))) → (\(String(format: "%.6f", to.latitude)), \(String(format: "%.6f", to.longitude)))")
        }

        var routes: [MKRoute?] = Array(repeating: nil, count: waypoints.count - 1)
        var completedCount = 0
        var hasError = false
        let group = DispatchGroup()

        for i in 0..<(waypoints.count - 1) {
            group.enter()

            // 添加延迟，避免同时发送太多请求
            let delay = Double(i) * 0.5

            DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
                self?.calculateRoute(from: waypoints[i], to: waypoints[i + 1]) { result in
                    defer { group.leave() }

                    switch result {
                    case .success(let route):
                        routes[i] = route
                        completedCount += 1
                        self?.logInfo("✅ DirectionsAPIManager - 路线段 \(i + 1) 计算成功，距离: \(String(format: "%.1f", route.distance/1000))公里，坐标点数: \(route.polyline.pointCount)")
                    case .failure(let error):
                        self?.logInfo("🚨 DirectionsAPIManager - 路线段 \(i + 1) 计算失败: \(error.localizedDescription)")
                        if !hasError {
                            hasError = true
                        }
                    }
                }
            }
        }
        
        group.notify(queue: .main) { [weak self] in
            let successfulRoutes = routes.compactMap { $0 }
            let totalSegments = waypoints.count - 1

            self?.logInfo("🔍 DirectionsAPIManager - 批量计算完成")
            self?.logInfo("🔍 成功段数: \(successfulRoutes.count)/\(totalSegments)")
            self?.logInfo("🔍 失败段数: \(totalSegments - successfulRoutes.count)")
            self?.logInfo("🔍 是否有错误: \(hasError)")

            if hasError && completedCount == 0 {
                // 所有请求都失败了
                self?.logInfo("🚨 DirectionsAPIManager - 所有路线段都失败了")
                completion(.failure(NSError(domain: "DirectionsAPIManager", code: -3, userInfo: [NSLocalizedDescriptionKey: "所有路线计算失败"])))
            } else if successfulRoutes.count < totalSegments {
                // 部分失败
                self?.logInfo("🚨 DirectionsAPIManager - 部分路线段失败，返回成功的路线段")
                completion(.success(successfulRoutes))
            } else {
                // 全部成功
                self?.logInfo("✅ DirectionsAPIManager - 所有路线段都成功")
                completion(.success(successfulRoutes))
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func canMakeRequest() -> Bool {
        let now = Date()
        
        // 清理过期的时间戳
        requestTimestamps = requestTimestamps.filter { now.timeIntervalSince($0) < timeWindow }
        
        return requestTimestamps.count < maxRequestsPerMinute
    }
    
    private func getWaitTime() -> TimeInterval {
        guard let oldestRequest = requestTimestamps.first else { return 0 }
        let timeElapsed = Date().timeIntervalSince(oldestRequest)
        return max(0, timeWindow - timeElapsed)
    }
    
    private func recordRequest() {
        let now = Date()
        requestTimestamps.append(now)
        lastRequestTime = now
        
        // 清理过期的时间戳
        requestTimestamps = requestTimestamps.filter { now.timeIntervalSince($0) < timeWindow }
    }
    
    private func performDirectionsRequest(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        cacheKey: String,
        completion: @escaping (Result<MKRoute, Error>) -> Void
    ) {
        recordRequest()

        let request = MKDirections.Request()
        request.source = MKMapItem(placemark: MKPlacemark(coordinate: source))
        request.destination = MKMapItem(placemark: MKPlacemark(coordinate: destination))

        // 🚨 确保使用汽车导航模式，获取详细路线
        request.transportType = .automobile
        request.requestsAlternateRoutes = false  // 只要最佳路线

        // 🔍 调试：记录请求详情
        logInfo("🔍 DirectionsAPIManager - 发送路线请求")
        logInfo("🔍 起点: (\(String(format: "%.6f", source.latitude)), \(String(format: "%.6f", source.longitude)))")
        logInfo("🔍 终点: (\(String(format: "%.6f", destination.latitude)), \(String(format: "%.6f", destination.longitude)))")

        let directions = MKDirections(request: request)
        directions.calculate { [weak self] response, error in
            DispatchQueue.main.async {
                if let error = error {
                    // 🔍 详细错误分析
                    let nsError = error as NSError
                    self?.logInfo("🚨 DirectionsAPIManager - 路线计算失败")
                    self?.logInfo("🚨 错误域: \(nsError.domain)")
                    self?.logInfo("🚨 错误代码: \(nsError.code)")
                    self?.logInfo("🚨 错误描述: \(error.localizedDescription)")
                    self?.logInfo("🚨 起点: (\(String(format: "%.6f", source.latitude)), \(String(format: "%.6f", source.longitude)))")
                    self?.logInfo("🚨 终点: (\(String(format: "%.6f", destination.latitude)), \(String(format: "%.6f", destination.longitude)))")

                    // 🎯 根据错误类型提供具体解决方案
                    self?.handleDirectionsError(nsError, from: source, to: destination)

                    completion(.failure(error))
                } else if let route = response?.routes.first {
                    // 🔍 调试：记录路线详情
                    self?.logInfo("✅ DirectionsAPIManager - 路线计算成功")
                    self?.logInfo("🔍 起点: (\(String(format: "%.6f", source.latitude)), \(String(format: "%.6f", source.longitude)))")
                    self?.logInfo("🔍 终点: (\(String(format: "%.6f", destination.latitude)), \(String(format: "%.6f", destination.longitude)))")
                    self?.logInfo("🔍 路线距离: \(String(format: "%.1f", route.distance/1000))公里")
                    self?.logInfo("🔍 路线坐标点数: \(route.polyline.pointCount)")

                    // 🔍 提取并验证坐标
                    let coordinates = route.polyline.coordinates
                    self?.logInfo("🔍 提取的坐标数组长度: \(coordinates.count)")
                    if coordinates.count >= 2 {
                        self?.logInfo("🔍 第一个坐标: (\(String(format: "%.6f", coordinates[0].latitude)), \(String(format: "%.6f", coordinates[0].longitude)))")
                        self?.logInfo("🔍 最后一个坐标: (\(String(format: "%.6f", coordinates.last!.latitude)), \(String(format: "%.6f", coordinates.last!.longitude)))")

                        // 🔍 检查是否为直线（起点终点相同）
                        let isDirectLine = coordinates.count == 2 &&
                                         abs(coordinates[0].latitude - source.latitude) < 0.000001 &&
                                         abs(coordinates[0].longitude - source.longitude) < 0.000001 &&
                                         abs(coordinates[1].latitude - destination.latitude) < 0.000001 &&
                                         abs(coordinates[1].longitude - destination.longitude) < 0.000001

                        if isDirectLine {
                            self?.logInfo("🚨 警告：API返回的路线是直线连接！")
                        } else {
                            self?.logInfo("✅ API返回的是真实路线")
                        }
                    }

                    // 缓存结果到持久化存储
                    Task { @MainActor in
                        PersistentCacheManager.shared.cacheRoute(route, from: source, to: destination)
                    }
                    completion(.success(route))
                } else {
                    self?.logInfo("🚨 DirectionsAPIManager - 无法获取路线响应")
                    let error = NSError(domain: "DirectionsAPIManager", code: -4, userInfo: [NSLocalizedDescriptionKey: "无法获取路线"])
                    completion(.failure(error))
                }
            }
        }
    }
    
    private func generateCacheKey(from source: CLLocationCoordinate2D, to destination: CLLocationCoordinate2D) -> String {
        // 🚨 修复：使用与RouteCache相同的6位小数精度，确保缓存键一致
        return RouteCache.generateCacheKey(from: source, to: destination)
    }
    
    /// 获取缓存的距离（同步方法）
    @MainActor
    func getCachedDistance(from source: CLLocationCoordinate2D, to destination: CLLocationCoordinate2D) -> Double? {
        return PersistentCacheManager.shared.getStoredDistance(from: source, to: destination)
    }

    /// 清理缓存（现在只清理持久化缓存）
    func clearCache() {
        Task { @MainActor in
            PersistentCacheManager.shared.clearAllCaches()
        }
        logInfo("DirectionsAPIManager - 持久化缓存已清理")
    }
    
    // MARK: - 错误处理

    /// 🔍 处理MapKit Directions API错误
    private func handleDirectionsError(_ error: NSError, from source: CLLocationCoordinate2D, to destination: CLLocationCoordinate2D) {
        switch error.domain {
        case "MKErrorDomain":
            switch error.code {
            case 1: // MKErrorPlacemarkNotFound
                logInfo("🚨 MapKit错误: 无法找到地址对应的地点")
                logInfo("💡 建议: 检查坐标是否有效，是否在支持的地理区域内")
            case 2: // MKErrorDirectionsNotFound
                logInfo("🚨 MapKit错误: 无法找到路线")
                logInfo("💡 建议: 两点之间可能没有可行的道路连接")
            case 3: // MKErrorNetworkFailure
                logInfo("🚨 MapKit错误: 网络连接失败")
                logInfo("💡 建议: 检查网络连接，稍后重试")
            case 4: // MKErrorServerFailure
                logInfo("🚨 MapKit错误: 服务器错误")
                logInfo("💡 建议: Apple Maps服务暂时不可用，稍后重试")
            case 5: // MKErrorLoadingThrottled
                logInfo("🚨 MapKit错误: 请求被限制")
                logInfo("💡 建议: 降低请求频率，等待后重试")
            default:
                logInfo("🚨 MapKit错误: 未知错误代码 \(error.code)")
            }
        case "GEOErrorDomain":
            switch error.code {
            case -3: // 频率限制
                logInfo("🚨 GEO错误: API频率限制")
                logInfo("💡 建议: 等待60秒后重试，或减少并发请求数")
            default:
                logInfo("🚨 GEO错误: 错误代码 \(error.code)")
            }
        case "kCLErrorDomain":
            switch error.code {
            case 0: // kCLErrorLocationUnknown
                logInfo("🚨 CoreLocation错误: 位置未知")
            case 1: // kCLErrorDenied
                logInfo("🚨 CoreLocation错误: 位置权限被拒绝")
            case 2: // kCLErrorNetwork
                logInfo("🚨 CoreLocation错误: 网络错误")
                logInfo("💡 建议: 检查网络连接")
            default:
                logInfo("🚨 CoreLocation错误: 错误代码 \(error.code)")
            }
        default:
            logInfo("🚨 未知错误域: \(error.domain), 代码: \(error.code)")
        }

        // 🔍 坐标有效性检查
        if !CLLocationCoordinate2DIsValid(source) {
            logInfo("🚨 起点坐标无效: (\(source.latitude), \(source.longitude))")
        }
        if !CLLocationCoordinate2DIsValid(destination) {
            logInfo("🚨 终点坐标无效: (\(destination.latitude), \(destination.longitude))")
        }
    }

    // MARK: - 工具方法

    /// 获取当前请求状态
    func getRequestStatus() -> (current: Int, max: Int, waitTime: TimeInterval) {
        let now = Date()
        requestTimestamps = requestTimestamps.filter { now.timeIntervalSince($0) < timeWindow }
        return (requestTimestamps.count, maxRequestsPerMinute, getWaitTime())
    }

    /// 🔧 强制测试API连接（忽略缓存和频率限制）
    func testAPIConnection(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D,
        completion: @escaping (Result<CLLocationDistance, Error>) -> Void
    ) {
        logInfo("🔧 DirectionsAPIManager - 强制测试API连接")
        logInfo("🔧 起点: (\(String(format: "%.6f", source.latitude)), \(String(format: "%.6f", source.longitude)))")
        logInfo("🔧 终点: (\(String(format: "%.6f", destination.latitude)), \(String(format: "%.6f", destination.longitude)))")

        // 直接调用API，忽略所有限制
        let request = MKDirections.Request()
        request.source = MKMapItem(placemark: MKPlacemark(coordinate: source))
        request.destination = MKMapItem(placemark: MKPlacemark(coordinate: destination))
        request.transportType = .automobile
        request.requestsAlternateRoutes = false

        let directions = MKDirections(request: request)
        directions.calculate { [weak self] response, error in
            DispatchQueue.main.async {
                if let error = error {
                    let nsError = error as NSError
                    self?.logInfo("🔧 API测试失败: \(error.localizedDescription)")
                    self?.logInfo("🔧 错误域: \(nsError.domain), 代码: \(nsError.code)")
                    self?.handleDirectionsError(nsError, from: source, to: destination)
                    completion(.failure(error))
                } else if let route = response?.routes.first {
                    let distance = route.distance
                    self?.logInfo("🔧 API测试成功: 距离 \(distance/1000)km")
                    completion(.success(distance))
                } else {
                    let error = NSError(domain: "DirectionsAPIManager", code: -4, userInfo: [NSLocalizedDescriptionKey: "无法获取路线响应"])
                    self?.logInfo("🔧 API测试失败: 无响应")
                    completion(.failure(error))
                }
            }
        }
    }
    

    
    // MARK: - 日志
    private func logInfo(_ message: String) {
        Logger.info("[DirectionsAPIManager] \(message)", type: .network)
    }
}
