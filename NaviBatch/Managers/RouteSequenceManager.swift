import Foundation
import CoreLocation
import SwiftUI

/// 路线序列管理器 - 处理用户选择的地址顺序，计算并缓存总距离
@MainActor
class RouteSequenceManager: ObservableObject {
    static let shared = RouteSequenceManager()
    
    @Published var isCalculating = false
    @Published var lastCalculatedDistance: Double = 0
    @Published var lastCalculatedTime: Double = 0
    
    private init() {}
    
    // MARK: - 公共接口
    
    /// 计算路线序列的总距离（智能两点缓存策略）
    /// - Parameters:
    ///   - coordinates: 按顺序排列的坐标数组
    ///   - completion: 完成回调，返回总距离（米）和总时间（秒）
    func calculateRouteSequence(
        coordinates: [CLLocationCoordinate2D],
        completion: @escaping (Result<(distance: Double, time: Double), Error>) -> Void
    ) {
        guard coordinates.count >= 2 else {
            completion(.failure(RouteSequenceError.insufficientPoints))
            return
        }

        print("🔍🔍🔍 RouteSequenceManager - 开始智能计算路线序列，共\(coordinates.count)个点")

        // 🎯 新策略：检查每个两点段的缓存情况
        isCalculating = true

        Task {
            do {
                let result = try await calculateSequenceWithTwoPointCache(coordinates: coordinates)

                await MainActor.run {
                    self.isCalculating = false
                    self.lastCalculatedDistance = result.distance
                    self.lastCalculatedTime = result.time

                    print("🔍🔍🔍 RouteSequenceManager - 智能计算完成！总距离: \(String(format: "%.1f", result.distance/1000))km")
                    completion(.success(result))
                }
            } catch {
                await MainActor.run {
                    self.isCalculating = false
                    print("🔍🔍🔍 RouteSequenceManager - 计算失败: \(error)")
                    completion(.failure(error))
                }
            }
        }
    }
    
    // MARK: - 私有方法

    /// 🎯 智能两点缓存计算：优先使用缓存，只计算缺失的段
    private func calculateSequenceWithTwoPointCache(coordinates: [CLLocationCoordinate2D]) async throws -> (distance: Double, time: Double) {
        var totalDistance: Double = 0
        var totalTime: Double = 0

        print("🔍🔍🔍 RouteSequenceManager - 开始智能计算\(coordinates.count-1)段距离")

        // 🎯 关键优化：直接使用DirectionsAPIManager，它内部已经有完善的缓存机制
        // 每次调用calculateDistance都会：
        // 1. 先检查内存缓存
        // 2. 再检查持久化缓存
        // 3. 最后才调用API并缓存结果

        for i in 0..<(coordinates.count - 1) {
            let startCoord = coordinates[i]
            let endCoord = coordinates[i + 1]

            print("🔍🔍🔍 RouteSequenceManager - 计算第\(i+1)段: 点\(i+1) -> 点\(i+2)")

            // DirectionsAPIManager内部会自动处理缓存逻辑
            let segmentResult = try await calculateSingleSegment(from: startCoord, to: endCoord)
            totalDistance += segmentResult.distance
            totalTime += segmentResult.time

            print("🔍🔍🔍 RouteSequenceManager - 第\(i+1)段完成: \(String(format: "%.1f", segmentResult.distance/1000))km")
        }

        print("🔍🔍🔍 RouteSequenceManager - 智能计算完成，总距离: \(String(format: "%.1f", totalDistance/1000))km")
        return (distance: totalDistance, time: totalTime)
    }
    
    /// 计算单段距离（使用DirectionsAPIManager的缓存）
    private func calculateSingleSegment(from start: CLLocationCoordinate2D, to end: CLLocationCoordinate2D) async throws -> (distance: Double, time: Double) {
        return try await withCheckedThrowingContinuation { continuation in
            DirectionsAPIManager.shared.calculateDistance(from: start, to: end) { result in
                switch result {
                case .success(let distance):
                    // 注意：DirectionsAPIManager.calculateDistance只返回距离，时间暂时设为0
                    // 如果需要时间，可以使用calculateRoute方法
                    continuation.resume(returning: (distance: distance, time: 0))
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - 便捷方法
    
    /// 格式化距离显示
    func formatDistance(_ distance: Double) -> String {
        return DistanceFormatter.shared.formatDistance(distance)
    }
    
    /// 格式化时间显示
    func formatTime(_ time: Double) -> String {
        let minutes = Int(time / 60)
        if minutes >= 60 {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            return "\(hours)小时\(remainingMinutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 清除计算状态
    func clearCalculationState() {
        isCalculating = false
        lastCalculatedDistance = 0
        lastCalculatedTime = 0
    }
}

// MARK: - 错误定义

enum RouteSequenceError: LocalizedError {
    case insufficientPoints
    case calculationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .insufficientPoints:
            return "至少需要2个地址点才能计算路线"
        case .calculationFailed(let message):
            return "路线计算失败: \(message)"
        }
    }
}
