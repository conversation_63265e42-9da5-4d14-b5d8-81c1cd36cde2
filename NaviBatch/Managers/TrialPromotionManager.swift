import Foundation
import SwiftUI

/// 试用推广管理器 - 控制何时显示试用推广页面
class TrialPromotionManager: ObservableObject {
    static let shared = TrialPromotionManager()
    
    @Published var shouldShowPromotion = false
    
    private let userDefaults = UserDefaults.standard
    private let subscriptionManager = SubscriptionManager.shared
    
    // UserDefaults 键
    private let lastPromotionDateKey = "LastTrialPromotionDate"
    private let promotionCountKey = "TrialPromotionCount"
    private let appLaunchCountKey = "AppLaunchCount"
    private let hasDeclinedPromotionKey = "HasDeclinedTrialPromotion"
    
    // 配置参数
    private let minDaysBetweenPromotions = 3 // 最少间隔3天
    private let maxPromotionsPerMonth = 4    // 每月最多显示4次
    private let showAfterLaunches = 3        // 第3次启动后开始显示
    
    private init() {}
    
    /// 检查是否应该显示试用推广
    func checkShouldShowPromotion() {
        // 如果用户已经是付费用户或在试用期，不显示
        if subscriptionManager.currentTier != .free || subscriptionManager.isInFreeTrial {
            return
        }

        // 🎯 新策略：免费用户且未试用，每次启动都显示（除非用户明确拒绝且在冷却期内）
        logInfo("TrialPromotionManager - 免费用户且未试用，检查是否显示试用推广")

        // 如果用户已经明确拒绝，检查冷却期
        if userDefaults.bool(forKey: hasDeclinedPromotionKey) {
            if let lastDeclineDate = userDefaults.object(forKey: "LastDeclineDate") as? Date {
                let daysSinceDecline = Calendar.current.dateComponents([.day], from: lastDeclineDate, to: Date()).day ?? 0
                if daysSinceDecline < 7 {
                    logInfo("TrialPromotionManager - 用户拒绝后冷却期内，不显示")
                    return
                }
                // 冷却期已过，清除拒绝标记
                userDefaults.removeObject(forKey: hasDeclinedPromotionKey)
                userDefaults.removeObject(forKey: "LastDeclineDate")
                logInfo("TrialPromotionManager - 冷却期已过，清除拒绝标记")
            }
        }

        // 免费用户且未试用，每次启动都显示
        DispatchQueue.main.async {
            self.shouldShowPromotion = true
        }

        // 记录显示时间和次数（用于统计）
        userDefaults.set(Date(), forKey: lastPromotionDateKey)
        let currentCount = userDefaults.integer(forKey: promotionCountKey)
        userDefaults.set(currentCount + 1, forKey: promotionCountKey)

        logInfo("TrialPromotionManager - 免费用户且未试用，显示试用推广")
    }
    
    /// 用户开始试用
    func userStartedTrial() {
        shouldShowPromotion = false
        // 清除拒绝标记
        userDefaults.removeObject(forKey: hasDeclinedPromotionKey)
        userDefaults.removeObject(forKey: "LastDeclineDate")
    }
    
    /// 用户关闭推广页面
    func userDismissedPromotion() {
        shouldShowPromotion = false
        userDefaults.set(true, forKey: hasDeclinedPromotionKey)
        userDefaults.set(Date(), forKey: "LastDeclineDate")
    }
    
    /// 重置推广状态（用于测试）
    func resetPromotionState() {
        userDefaults.removeObject(forKey: lastPromotionDateKey)
        userDefaults.removeObject(forKey: promotionCountKey)
        userDefaults.removeObject(forKey: appLaunchCountKey)
        userDefaults.removeObject(forKey: hasDeclinedPromotionKey)
        userDefaults.removeObject(forKey: "LastDeclineDate")
        
        // 清除月度计数
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())
        let monthKey = "promotions_\(currentYear)_\(currentMonth)"
        userDefaults.removeObject(forKey: monthKey)
    }
    
    /// 获取推广统计信息
    func getPromotionStats() -> (totalShown: Int, lastShown: Date?) {
        let totalShown = userDefaults.integer(forKey: promotionCountKey)
        let lastShown = userDefaults.object(forKey: lastPromotionDateKey) as? Date

        return (totalShown, lastShown)
    }
}
