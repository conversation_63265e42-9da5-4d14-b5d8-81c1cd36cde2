import Foundation
import SwiftData
import MapKit
import CoreLocation



class RouteManager: ObservableObject {
    var modelContext: ModelContext



    // 主初始化方法
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }



    // 工厂方法 - 创建一个带有共享 ModelContext 的 RouteManager
    @MainActor
    static func createWithTemporaryContext() -> RouteManager {
        // 使用持久化存储而非内存存储
        print("RouteManager - 使用共享持久化数据库容器")
        let container = getPersistentContainer()
        return RouteManager(modelContext: container.mainContext)
    }

    // MARK: - 配送组管理

    // 获取所有配送组
    func getAllDeliveryGroups() -> [DeliveryGroup] {
        let descriptor = FetchDescriptor<DeliveryGroup>(sortBy: [SortDescriptor(\.createdAt, order: .reverse)])
        return (try? modelContext.fetch(descriptor)) ?? []
    }

    // 创建新配送组
    func createDeliveryGroup(name: String, points: [DeliveryPoint], groupNumber: Int) -> DeliveryGroup {
        let group = DeliveryGroup(name: name, points: points, groupNumber: groupNumber)

        // 设置每个点的分组状态
        for point in points {
            point.isAssignedToGroup = true
            point.assignedGroupNumber = groupNumber
        }

        modelContext.insert(group)
        try? modelContext.save()
        return group
    }

    // 删除配送组
    func deleteDeliveryGroup(_ group: DeliveryGroup) throws {
        print("[INFO] RouteManager - 开始删除配送组: \(group.name), ID: \(group.id)")

        // 获取组内所有点的副本，因为删除组后可能无法访问
        let pointsInGroup = Array(group.points)
        print("[INFO] RouteManager - 组内有\(pointsInGroup.count)个点")

        // 重置组内所有点的分组状态，但保留优化状态
        for point in pointsInGroup {
            // 记录当前优化状态
            let wasOptimized = point.isOptimized
            let sortedNumber = point.sorted_number

            // 重置分组状态
            point.isAssignedToGroup = false
            point.assignedGroupNumber = nil

            // 确保保留优化状态
            point.isOptimized = wasOptimized

            print("[INFO] RouteManager - 重置点状态: \(point.primaryAddress), 保留优化状态: \(wasOptimized), 排序编号: \(sortedNumber)")
        }

        // 立即保存点状态更改
        try? modelContext.save()
        print("[INFO] RouteManager - 已保存点状态更改")

        // 删除组
        modelContext.delete(group)
        print("[INFO] RouteManager - 已标记组为删除")

        // 保存更改
        try modelContext.save()
        print("[INFO] RouteManager - 成功删除配送组")
    }

    // MARK: - 配送点管理

    // 创建新配送点
    func createDeliveryPoint(sort_number: Int, address: String, coordinate: CLLocationCoordinate2D,
                            packageCount: Int = 1, notes: String? = nil) -> DeliveryPoint {
        // 🎯 使用结构化地址创建DeliveryPoint，确保originalAddress也被设置
        let point = DeliveryPoint(sort_number: sort_number, streetName: address, originalAddress: address, coordinate: coordinate)
        point.packageCount = packageCount
        point.notes = notes

        modelContext.insert(point)
        try? modelContext.save()
        return point
    }

    // 🚨 新增：创建配送点时支持原始地址和处理后地址分离
    func createDeliveryPoint(sort_number: Int, processedAddress: String, originalAddress: String,
                            coordinate: CLLocationCoordinate2D, packageCount: Int = 1, notes: String? = nil) -> DeliveryPoint {
        // 🎯 分别保存原始地址和处理后地址
        let point = DeliveryPoint(sort_number: sort_number, streetName: processedAddress,
                                 originalAddress: originalAddress, coordinate: coordinate)
        point.packageCount = packageCount
        point.notes = notes

        // 🚨 记录地址处理信息用于调试
        if processedAddress != originalAddress {
            print("[INFO] RouteManager - 地址已处理: 原始='\(originalAddress)' -> 处理后='\(processedAddress)'")
        }

        modelContext.insert(point)
        try? modelContext.save()
        return point
    }

    // 向配送组添加点
    func addPointToGroup(_ point: DeliveryPoint, group: DeliveryGroup) {
        point.isAssignedToGroup = true
        point.assignedGroupNumber = group.groupNumber
        group.points.append(point)
        try? modelContext.save()
    }

    // 从配送组移除点
    func removePointFromGroup(_ point: DeliveryPoint, group: DeliveryGroup) {
        // 记录当前优化状态
        let wasOptimized = point.isOptimized
        let sortedNumber = point.sorted_number

        // 重置分组状态
        point.isAssignedToGroup = false
        point.assignedGroupNumber = nil

        // 确保保留优化状态
        point.isOptimized = wasOptimized

        print("[INFO] RouteManager - 已重置点\(point.primaryAddress)的分组状态，保留优化状态: \(wasOptimized), 排序编号: \(sortedNumber)")

        // 立即保存点状态更改
        try? modelContext.save()
        print("[INFO] RouteManager - 已保存点状态更改")

        // 从组中移除点
        group.points.removeAll { $0.id == point.id }

        // 保存分组更改
        try? modelContext.save()
        print("[INFO] RouteManager - 已从分组中移除点并保存更改")
    }

    // 更新配送点状态
    func updateDeliveryPointStatus(_ point: DeliveryPoint, status: DeliveryStatus) {
        point.updateStatus(status)
        try? modelContext.save()
    }

    // 重新排序配送组中的点
    func reorderPointsInGroup(_ group: DeliveryGroup, newOrder: [DeliveryPoint]) {
        // 更新点的顺序
        group.points = newOrder
        try? modelContext.save()
    }

    // MARK: - 路线计算

    // 计算配送组的总距离 - 使用真实道路距离
    func calculateGroupDistance(_ group: DeliveryGroup, completion: @escaping (Double?) -> Void) {
        guard group.points.count >= 2 else {
            completion(nil)
            return
        }

        // 使用真实道路距离计算
        LocalDistanceCalculator.shared.calculateTotalRealDistance(points: group.points) { result in
            switch result {
            case .success(let distance):
                completion(distance)
            case .failure(let error):
                Logger.error("配送组距离计算失败: \(error.localizedDescription)")
                // 不使用直线距离，返回nil表示计算失败
                completion(nil)
            }
        }
    }

    // 已废弃：原本使用API的路线计算方法
    // 现在使用LocalDistanceCalculator进行本地计算，避免API限制
    /*
    private func performRouteCalculation(for group: DeliveryGroup, completion: @escaping (Double?) -> Void) async {
        // 此方法已被本地距离计算替代
    }

    private func calculateSegmentDistance(
        from startPoint: DeliveryPoint,
        to endPoint: DeliveryPoint
    ) async -> CLLocationDistance {
        // 此方法已被本地距离计算替代
    }
    */

    // 计算两点间的直线距离（备选方案）
    private func calculateStraightLineDistance(
        from source: CLLocationCoordinate2D,
        to destination: CLLocationCoordinate2D
    ) -> CLLocationDistance {
        let sourceLocation = CLLocation(latitude: source.latitude, longitude: source.longitude)
        let destinationLocation = CLLocation(latitude: destination.latitude, longitude: destination.longitude)
        return sourceLocation.distance(from: destinationLocation)
    }

    // 计算包含所有点的地图区域
    func calculateRegion(for points: [DeliveryPoint], padding: Double = 1.5) -> MKCoordinateRegion? {
        guard !points.isEmpty else { return nil }

        var minLat = points[0].latitude
        var maxLat = points[0].latitude
        var minLon = points[0].longitude
        var maxLon = points[0].longitude

        for point in points {
            minLat = min(minLat, point.latitude)
            maxLat = max(maxLat, point.latitude)
            minLon = min(minLon, point.longitude)
            maxLon = max(maxLon, point.longitude)
        }

        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )

        let span = MKCoordinateSpan(
            latitudeDelta: (maxLat - minLat) * padding,
            longitudeDelta: (maxLon - minLon) * padding
        )

        return MKCoordinateRegion(center: center, span: span)
    }

    // 更新 ModelContext
    func updateModelContext(_ newContext: ModelContext) {
        self.modelContext = newContext
    }
}
