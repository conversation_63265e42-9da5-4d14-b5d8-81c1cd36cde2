import Foundation
import SwiftUI

/// 本地化管理器 - 处理应用的多语言支持
class LocalizationManager: ObservableObject {
    static let shared = LocalizationManager()

    // 本地化文件的目录
    private let localizationsDirectory = "Localizations"

    // 支持的语言列表
    enum Language: String, CaseIterable, Identifiable {
        case system = "system"  // 使用系统语言
        case english = "en"
        case simplifiedChinese = "zh-Hans"
        case traditionalChinese = "zh-Hant"
        case arabic = "ar"
        case dutch = "nl"
        case french = "fr"
        case german = "de"
        case greek = "el"
        case hebrew = "he"
        case hungarian = "hu"
        case indonesian = "id"
        case italian = "it"
        case japanese = "ja"
        case korean = "ko"
        case malay = "ms"
        case polish = "pl"
        case portuguese = "pt"
        case romanian = "ro"
        case russian = "ru"
        case spanish = "es"
        case thai = "th"
        case turkish = "tr"

        var id: String { self.rawValue }





        // 语言的本地化显示名称
        var displayName: String {
            switch self {
            case .system:
                // 获取系统语言的具体名称
                return LocalizationManager.shared.getSystemLanguageDisplayName()
            case .english:
                return "English"
            case .simplifiedChinese:
                return "简体中文"
            case .traditionalChinese:
                return "繁體中文"
            case .arabic:
                return "العربية"
            case .dutch:
                return "Nederlands"
            case .french:
                return "Français"
            case .german:
                return "Deutsch"
            case .greek:
                return "Ελληνικά"
            case .hebrew:
                return "עברית"
            case .hungarian:
                return "Magyar"
            case .indonesian:
                return "Bahasa Indonesia"
            case .italian:
                return "Italiano"
            case .japanese:
                return "日本語"
            case .korean:
                return "한국어"
            case .malay:
                return "Bahasa Melayu"
            case .polish:
                return "Polski"
            case .portuguese:
                return "Português"
            case .romanian:
                return "Română"
            case .russian:
                return "Русский"
            case .spanish:
                return "Español"
            case .thai:
                return "ไทย"
            case .turkish:
                return "Türkçe"
            }
        }

        // 语言的原生显示名称（不依赖本地化系统）
        var nativeDisplayName: String {
            switch self {
            case .system:
                return "System Language" // 这个不应该被调用
            case .english:
                return "English"
            case .simplifiedChinese:
                return "简体中文"
            case .traditionalChinese:
                return "繁體中文"
            case .arabic:
                return "العربية"
            case .dutch:
                return "Nederlands"
            case .french:
                return "Français"
            case .german:
                return "Deutsch"
            case .greek:
                return "Ελληνικά"
            case .hebrew:
                return "עברית"
            case .hungarian:
                return "Magyar"
            case .indonesian:
                return "Bahasa Indonesia"
            case .italian:
                return "Italiano"
            case .japanese:
                return "日本語"
            case .korean:
                return "한국어"
            case .malay:
                return "Bahasa Melayu"
            case .polish:
                return "Polski"
            case .portuguese:
                return "Português"
            case .romanian:
                return "Română"
            case .russian:
                return "Русский"
            case .spanish:
                return "Español"
            case .thai:
                return "ไทย"
            case .turkish:
                return "Türkçe"
            }
        }

        // 语言的方向（从左到右或从右到左）
        var isRightToLeft: Bool {
            switch self {
            case .arabic, .hebrew:
                return true
            default:
                return false
            }
        }
    }

    // 获取本地化 Bundle
    var localizationBundle: Bundle {
        // 简化逻辑：直接使用主Bundle，让系统处理本地化
        if selectedLanguage == .system {
            return Bundle.main
        }

        // 对于特定语言，尝试获取对应的Bundle
        let targetLanguage = selectedLanguage.rawValue

        // 首先尝试从Localizations目录获取
        if let bundlePath = Bundle.main.path(forResource: targetLanguage, ofType: "lproj", inDirectory: localizationsDirectory),
           let bundle = Bundle(path: bundlePath) {
            return bundle
        }

        // 然后尝试从主Bundle获取
        if let bundlePath = Bundle.main.path(forResource: targetLanguage, ofType: "lproj"),
           let bundle = Bundle(path: bundlePath) {
            return bundle
        }

        // 回退到主Bundle
        return Bundle.main

    }

    // 当前选择的语言
    @Published var selectedLanguage: Language {
        didSet {
            // 保存语言选择
            if selectedLanguage != .system {
                UserDefaults.standard.set(selectedLanguage.rawValue, forKey: "AppLanguage")
            } else {
                UserDefaults.standard.removeObject(forKey: "AppLanguage")
            }

            // 应用语言更改
            applyLanguage()

            // 发送语言更改通知
            NotificationCenter.default.post(name: Notification.Name("LanguageChanged"), object: nil)
        }
    }

    // 初始化
    private init() {
        // 从 UserDefaults 加载保存的语言设置
        if let savedLanguage = UserDefaults.standard.string(forKey: "AppLanguage"),
           let language = Language(rawValue: savedLanguage) {
            self.selectedLanguage = language
        } else {
            self.selectedLanguage = .system
        }

        // 应用初始语言
        applyLanguage()
    }

    // 应用选择的语言
    private func applyLanguage() {
        // 如果选择了系统语言，则不需要做任何事情
        guard selectedLanguage != .system else {
            return
        }

        // 设置应用的语言
        UserDefaults.standard.set([selectedLanguage.rawValue], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize()
    }

    // 获取当前语言的方向
    var isRightToLeft: Bool {
        if selectedLanguage == .system {
            // 检查系统语言
            let systemLanguage = Locale.current.language.languageCode?.identifier ?? "en"
            return systemLanguage == "ar" || systemLanguage == "he"
        } else {
            return selectedLanguage.isRightToLeft
        }
    }

    // 重置为系统语言
    func resetToSystemLanguage() {
        selectedLanguage = .system
    }

    // 获取系统语言的显示名称
    func getSystemLanguageDisplayName() -> String {
        // 获取系统语言代码
        let preferredLanguage = Locale.preferredLanguages.first ?? "en"
        let languageCode = Locale(identifier: preferredLanguage).language.languageCode?.identifier ?? "en"

        // 检查是否支持系统语言
        let supportedLanguages = Language.allCases.filter { $0 != .system }

        // 尝试找到匹配的语言
        var matchedLanguage: Language? = nil

        // 完全匹配
        if let exactMatch = supportedLanguages.first(where: { $0.rawValue == languageCode }) {
            matchedLanguage = exactMatch
        }
        // 部分匹配（例如 zh-Hans-CN 匹配 zh-Hans）
        else if let partialMatch = supportedLanguages.first(where: { languageCode.hasPrefix($0.rawValue) || $0.rawValue.hasPrefix(languageCode) }) {
            matchedLanguage = partialMatch
        }
        // 中文特殊处理
        else if languageCode.hasPrefix("zh") {
            // 获取地区代码
            let regionCode = Locale(identifier: preferredLanguage).language.region?.identifier ?? ""
            let scriptCode = Locale(identifier: preferredLanguage).language.script?.identifier ?? ""

            // 根据地区和文字系统确定具体的中文变体
            switch regionCode {
            case "CN": // 中国大陆
                matchedLanguage = .simplifiedChinese
            case "HK", "MO": // 香港、澳门
                matchedLanguage = .traditionalChinese
            case "TW": // 台湾
                matchedLanguage = .traditionalChinese
            case "SG": // 新加坡
                matchedLanguage = .simplifiedChinese
            default:
                // 如果没有特定地区，根据文字系统判断
                if scriptCode == "Hant" {
                    matchedLanguage = .traditionalChinese
                } else {
                    matchedLanguage = .simplifiedChinese
                }
            }
        }

        // 如果找到匹配的语言，返回其显示名称
        if let matched = matchedLanguage {
            // 直接获取语言的原生显示名称，避免循环引用
            let nativeDisplayName = matched.nativeDisplayName
            return "system_language".localized + " (\(nativeDisplayName))"
        } else {
            // 如果没有找到匹配的语言，显示系统语言的本地化名称
            let locale = Locale(identifier: preferredLanguage)
            let displayName = locale.localizedString(forLanguageCode: languageCode) ?? languageCode
            return "system_language".localized + " (\(displayName))"
        }
    }
}

// MARK: - 本地化字符串扩展
extension String {
    var localized: String {
        // 首先尝试使用自定义Bundle
        let customBundle = LocalizationManager.shared.localizationBundle
        let customResult = NSLocalizedString(self, tableName: nil, bundle: customBundle, value: "___NOT_FOUND___", comment: "")

        if customResult != "___NOT_FOUND___" {
            // 调试信息 - 只对特定键输出
            if self == "analyzing_image_content" {
                print("🌐 本地化调试 - 键: '\(self)'")
                print("🌐 自定义Bundle路径: \(customBundle.bundlePath)")
                print("🌐 自定义Bundle结果: '\(customResult)'")
            }
            return customResult
        }

        // 如果自定义Bundle失败，尝试主Bundle
        let mainResult = NSLocalizedString(self, tableName: nil, bundle: Bundle.main, value: self, comment: "")

        // 调试信息 - 只对特定键输出
        if self == "analyzing_image_content" {
            print("🌐 本地化调试 - 键: '\(self)'")
            print("🌐 自定义Bundle失败，使用主Bundle")
            print("🌐 主Bundle结果: '\(mainResult)'")
            print("🌐 是否找到翻译: \(mainResult != self)")

            // 额外调试：检查主Bundle中的本地化文件
            if let path = Bundle.main.path(forResource: "Localizable", ofType: "strings") {
                print("🌐 主Bundle中找到Localizable.strings: \(path)")
            } else {
                print("🌐 主Bundle中未找到Localizable.strings")
            }
        }

        return mainResult
    }

    func localized(with arguments: CVarArg...) -> String {
        return String(format: self.localized, arguments: arguments)
    }
}

// MARK: - 本地化视图修饰符
struct LocalizedViewModifier: ViewModifier {
    @ObservedObject private var localizationManager = LocalizationManager.shared

    func body(content: Content) -> some View {
        content
            .environment(\.layoutDirection, localizationManager.isRightToLeft ? .rightToLeft : .leftToRight)
    }
}

extension View {
    func localizedLayout() -> some View {
        self.modifier(LocalizedViewModifier())
    }
}



