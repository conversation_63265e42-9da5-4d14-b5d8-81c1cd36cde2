import Foundation
import StoreKit
import SwiftUI

/// 全局函数：检查是否是沙盒认证错误（用于Task.detached中避免async推断）
func isSandboxErrorSync(_ error: Error) -> Bool {
    if let nsError = error as NSError? {
        // 检查ASDErrorDomain Code=500 (沙盒认证错误)
        if nsError.domain == "ASDErrorDomain" && nsError.code == 500 {
            return true
        }

        // 检查AMSErrorDomain Code=2 (认证错误)
        if nsError.domain == "AMSErrorDomain" && nsError.code == 2 {
            return true
        }

        // 检查嵌套的认证错误
        if let underlyingError = nsError.userInfo[NSUnderlyingErrorKey] as? NSError {
            return isSandboxErrorSync(underlyingError)
        }
    }
    return false
}

#if DEBUG && canImport(StoreKitTest)
// 注意：StoreKitTest 框架在 iOS 18 中有变化
import StoreKitTest
#endif

/// 沙盒状态枚举
enum SandboxStatus {
    case unknown
    case working
    case authError
    case noReceipt
}

// MARK: - 收据验证数据结构

/// 收据验证请求
struct ReceiptValidationRequest: Codable {
    let receiptData: String
    let password: String
    let excludeOldTransactions: Bool

    enum CodingKeys: String, CodingKey {
        case receiptData = "receipt-data"
        case password
        case excludeOldTransactions = "exclude-old-transactions"
    }
}

/// 收据验证响应
struct ReceiptValidationResponse: Codable {
    let status: Int
    let environment: String?
    let receipt: Receipt?
    let latestReceiptInfo: [ReceiptInfo]?
    let pendingRenewalInfo: [PendingRenewalInfo]?

    enum CodingKeys: String, CodingKey {
        case status
        case environment
        case receipt
        case latestReceiptInfo = "latest_receipt_info"
        case pendingRenewalInfo = "pending_renewal_info"
    }
}

/// 收据信息
struct Receipt: Codable {
    let receiptType: String?
    let bundleId: String?
    let applicationVersion: String?
    let inApp: [ReceiptInfo]?

    enum CodingKeys: String, CodingKey {
        case receiptType = "receipt_type"
        case bundleId = "bundle_id"
        case applicationVersion = "application_version"
        case inApp = "in_app"
    }
}

/// 收据详细信息
struct ReceiptInfo: Codable {
    let quantity: String?
    let productId: String
    let transactionId: String?
    let originalTransactionId: String?
    let purchaseDateMs: String?
    let originalPurchaseDateMs: String?
    let expiresDateMs: String?
    let isTrialPeriod: String?
    let isInIntroOfferPeriod: String?
    let autoRenewStatus: String?

    enum CodingKeys: String, CodingKey {
        case quantity
        case productId = "product_id"
        case transactionId = "transaction_id"
        case originalTransactionId = "original_transaction_id"
        case purchaseDateMs = "purchase_date_ms"
        case originalPurchaseDateMs = "original_purchase_date_ms"
        case expiresDateMs = "expires_date_ms"
        case isTrialPeriod = "is_trial_period"
        case isInIntroOfferPeriod = "is_in_intro_offer_period"
        case autoRenewStatus = "auto_renew_status"
    }
}

/// 待续费信息
struct PendingRenewalInfo: Codable {
    let autoRenewProductId: String?
    let autoRenewStatus: String?
    let expirationIntent: String?

    enum CodingKeys: String, CodingKey {
        case autoRenewProductId = "auto_renew_product_id"
        case autoRenewStatus = "auto_renew_status"
        case expirationIntent = "expiration_intent"
    }
}

/// 收据验证结果
struct ReceiptValidationResult {
    let isValid: Bool
    let activeSubscriptions: [ActiveSubscription]
    let originalResponse: ReceiptValidationResponse
}

/// 活跃订阅信息
struct ActiveSubscription {
    let productId: String
    let expirationDate: Date
    let isInFreeTrial: Bool
    let autoRenewStatus: Bool
}

/// 收据验证错误
enum ReceiptValidationError: Error, LocalizedError {
    case noReceipt
    case invalidURL
    case encodingError(Error)
    case networkError
    case testReceipt
    case validationFailed(Int)

    var errorDescription: String? {
        switch self {
        case .noReceipt:
            return "无法获取应用收据"
        case .invalidURL:
            return "无效的验证URL"
        case .encodingError(let error):
            return "编码错误: \(error.localizedDescription)"
        case .networkError:
            return "网络错误"
        case .testReceipt:
            return "测试收据需要在沙盒环境验证"
        case .validationFailed(let status):
            return "收据验证失败，状态码: \(status)"
        }
    }
}

/// StoreKit 管理器 - 处理应用内购买和订阅
@MainActor
class StoreKitManager: ObservableObject {
    static let shared = StoreKitManager()

    // MARK: - 应用专用共享密钥
    /// 应用专用共享密钥 - 用于收据验证
    /// 这个密钥来自 App Store Connect 中的应用专用共享密钥
    private static let appSpecificSharedSecret = "f219f583b92c4eccb3929e451457829a"

    // 产品
    @Published private(set) var products: [Product] = []
    @Published private(set) var purchasedProductIDs = Set<String>()

    // 状态
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var sandboxStatus: SandboxStatus = .unknown

    // 交易监听器
    private var updateListenerTask: Task<Void, Error>?

    init() {
        // 启动交易监听器
        updateListenerTask = listenForTransactions()

        // 加载产品
        Task {
            do {
                try await loadProducts()
            } catch {
                self.errorMessage = "加载产品失败: \(error.localizedDescription)"
            }
        }

        // 恢复购买
        Task {
            do {
                try await updatePurchasedProducts()
            } catch {
                // 忽略错误，稍后会重试
            }
        }
    }

    deinit {
        updateListenerTask?.cancel()
    }

    // MARK: - 公共方法

    #if DEBUG
    /// 打印 StoreKit 测试配置信息
    func printStoreKitTestConfiguration() {
        // 检查是否在 StoreKit 测试环境中运行
        let isRunningInStoreKitTestEnvironment = ProcessInfo.processInfo.environment["STORE_KIT_TEST_MODE"] != nil

        // 在发布版本中，我们不打印调试信息
        #if DEBUG
        if isRunningInStoreKitTestEnvironment {
            // 尝试获取测试配置文件路径
            if ProcessInfo.processInfo.environment["STORE_KIT_CONFIG_FILE_PATH"] != nil {
                // 配置文件路径可用
            }

            // 获取请求的产品 ID
            _ = SubscriptionTier.allCases
                .map { $0.productID }
                .filter { !$0.isEmpty }
        }
        #endif
    }

    /// 在测试环境中创建模拟产品
    @MainActor
    func createTestProducts() {
        // 由于 Product 类没有公开的初始化器，我们需要使用 StoreKit 的 API 来获取产品
        // 在测试环境中，我们可以使用一个替代方案：创建一个模拟的产品列表

        // 创建一个模拟的产品字典
        // 注意：这些模拟产品在当前实现中未直接使用，但保留作为参考
        _ = [
            (
                id: SubscriptionTier.pro.productID,
                displayName: "月度高级版",
                description: "解锁一键导航分组和无限地址",
                price: Decimal(9.99),
                displayPrice: "¥9.99"
            ),
            (
                id: SubscriptionTier.expert.productID,
                displayName: "年度高级版",
                description: "解锁一键导航分组和无限地址，节省83%",
                price: Decimal(59.99),
                displayPrice: "¥59.99"
            )
        ]

        // 在实际应用中，我们需要使用 Product.products(for:) 方法获取产品
        // 在测试环境中，我们可以尝试从配置文件加载产品
        Task {
            do {
                // 获取所有产品ID
                let productIDs = SubscriptionTier.allCases
                    .map { $0.productID }
                    .filter { !$0.isEmpty }

                // 尝试加载产品
                let storeProducts = try await Product.products(for: Set(productIDs))

                await MainActor.run {
                    if !storeProducts.isEmpty {
                        // 如果成功加载产品，更新产品列表
                        self.products = storeProducts
                        print("[DEBUG] StoreKit - 测试环境中成功加载 \(storeProducts.count) 个产品")
                    } else {
                        // 使用模拟产品数据创建测试环境
                        print("[DEBUG] StoreKit - 测试环境中没有找到产品，使用模拟环境")
                        createTestSubscriptionEnvironment()
                    }
                }
            } catch {
                await MainActor.run {
                    // 使用模拟产品数据创建测试环境
                    print("[DEBUG] StoreKit - 测试环境中加载产品失败: \(error.localizedDescription)，使用模拟环境")
                    createTestSubscriptionEnvironment()
                }
            }
        }
    }

    /// 创建测试订阅环境
    @MainActor
    private func createTestSubscriptionEnvironment() {
        // 在测试环境中，我们无法创建真正的 Product 实例
        // 但我们可以模拟订阅状态，以便测试应用的订阅功能

        // 移除自动切换逻辑 - 现在只通知测试环境，不自动更改订阅状态
        // 用户可以通过开发者工具手动切换订阅状态

        // 移除Toast通知，避免干扰订阅界面显示
        // NotificationCenter.default.post(
        //     name: Notification.Name("ShowToast"),
        //     object: "正在使用测试订阅环境 - 请使用开发者工具手动切换订阅状态"
        // )

        #if DEBUG
        print("[DEBUG] StoreKit - 测试环境已激活，但不会自动切换订阅状态")
        print("[DEBUG] StoreKit - 请使用开发者工具中的Pro切换功能来测试不同订阅状态")
        #endif
    }
    #endif

    /// 加载产品
    @MainActor
    func loadProducts(forceReload: Bool = false) async throws {
        isLoading = true
        errorMessage = nil

        do {
            // 获取所有产品ID
            let productIDs = SubscriptionTier.allCases
                .map { $0.productID }
                .filter { !$0.isEmpty }

            // 请求产品
            if productIDs.isEmpty {
                self.products = []
                isLoading = false
                return
            }

            #if DEBUG
            // 打印 StoreKit 测试配置信息
            printStoreKitTestConfiguration()
            #endif

            // 如果已经加载了产品且不是强制重新加载，直接返回
            if !self.products.isEmpty && !forceReload {
                isLoading = false
                return
            }

            #if DEBUG
            // 检查是否在测试环境中运行
            // 允许通过环境变量控制是否使用测试环境
            let forceTestEnvironment = ProcessInfo.processInfo.environment["FORCE_TEST_ENVIRONMENT"]
            let isTestEnvironment = forceTestEnvironment == "false" ? false :
                                   (ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" ||
                                   ProcessInfo.processInfo.environment["STORE_KIT_TEST_MODE"] != nil)

            if isTestEnvironment && self.products.isEmpty {
                #if DEBUG
                print("[DEBUG] StoreKit - 在测试环境中运行，使用模拟产品")
                #endif
                // 创建测试产品
                createTestProducts()

                // 移除自动Pro设置逻辑 - 不再自动设置Pro订阅状态
                // 用户可以通过开发者工具手动切换订阅状态进行测试
                #if DEBUG
                print("[DEBUG] StoreKit - 测试环境已创建测试产品，当前订阅状态: \(SubscriptionManager.shared.currentTier.rawValue)")
                print("[DEBUG] StoreKit - 如需测试Pro功能，请使用开发者工具手动切换")
                #endif

                // 在测试环境中模拟一些购买记录用于恢复购买测试
                #if DEBUG
                print("[DEBUG] StoreKit - 测试环境：模拟购买记录用于恢复购买测试")
                // 这里可以添加一些模拟的购买记录到purchasedProductIDs
                // 但只在特定条件下（比如用户已经手动购买过）
                #endif

                #if DEBUG
                print("[DEBUG] StoreKit - 使用模拟产品完成")
                #endif
                isLoading = false
                return
            }
            #endif

            // 请求产品
            let storeProducts = try await Product.products(for: Set(productIDs))

            // 更新产品列表
            self.products = storeProducts

            isLoading = false
        } catch {
            errorMessage = "无法加载产品信息: \(error.localizedDescription)"
            isLoading = false
            throw error
        }
    }

    /// 购买产品
    @MainActor
    func purchase(_ product: Product) async throws -> Bool {
        isLoading = true
        errorMessage = nil

        #if DEBUG
        print("[DEBUG] StoreKit - 🚨 开始购买产品: \(product.id)")
        print("[DEBUG] StoreKit - 产品名称: \(product.displayName)")
        print("[DEBUG] StoreKit - 产品价格: \(product.displayPrice)")
        print("[DEBUG] StoreKit - 产品类型: \(product.type)")

        // 🚨 关键检查：验证产品是否有试用期配置
        if let subscription = product.subscription {
            print("[DEBUG] StoreKit - 订阅信息:")
            print("  订阅组ID: \(subscription.subscriptionGroupID)")
            print("  订阅周期: \(subscription.subscriptionPeriod)")
            if let introOffer = subscription.introductoryOffer {
                print("  试用期配置: \(introOffer)")
                print("  试用期类型: \(introOffer.paymentMode)")
                print("  试用期长度: \(introOffer.period)")
            } else {
                print("  ⚠️ 警告：没有找到试用期配置！")
            }
        }
        #endif

        do {
            // 创建购买选项
            let purchaseResult = try await product.purchase()

            #if DEBUG
            print("[DEBUG] StoreKit - 购买结果: \(purchaseResult)")
            #endif

            switch purchaseResult {
            case .success(let verificationResult):
                // 验证交易
                switch verificationResult {
                case .verified(let transaction):
                    // 交易成功
                    #if DEBUG
                    print("[DEBUG] StoreKit - 购买成功: \(product.id)")
                    print("[DEBUG] StoreKit - 交易ID: \(transaction.id)")
                    print("[DEBUG] StoreKit - 交易日期: \(transaction.purchaseDate)")
                    #endif

                    // 验证收据（可选，用于额外的安全验证）
                    do {
                        let validationResult = try await validateReceipt()
                        #if DEBUG
                        print("[DEBUG] StoreKit - 收据验证成功，活跃订阅数: \(validationResult.activeSubscriptions.count)")
                        #endif
                    } catch {
                        #if DEBUG
                        print("[DEBUG] StoreKit - 收据验证失败: \(error.localizedDescription)")
                        #endif
                        // 收据验证失败不影响购买流程，因为 StoreKit 2 已经提供了内置验证
                    }

                    // 更新购买状态
                    try await updatePurchasedProducts()

                    // 完成交易
                    await transaction.finish()

                    // 🚨 关键修复：统一处理订阅状态和试用期
                    // 只调用一个方法来处理所有状态更新，避免重复和冲突
                    await updateSubscriptionStatusWithExpiration(for: product.id)

                    isLoading = false
                    return true

                case .unverified(_, let error):
                    // 交易验证失败
                    #if DEBUG
                    print("[ERROR] StoreKit - 交易验证失败: \(error.localizedDescription)")
                    #endif
                    errorMessage = "交易验证失败: \(error.localizedDescription)"
                    isLoading = false
                    throw error
                }

            case .userCancelled:
                // 用户取消
                #if DEBUG
                print("[DEBUG] StoreKit - 用户取消购买")
                #endif
                isLoading = false
                return false

            case .pending:
                // 交易待处理
                #if DEBUG
                print("[DEBUG] StoreKit - 交易待处理")
                #endif
                errorMessage = "交易正在处理中，请稍后再试"
                isLoading = false
                throw NSError(domain: "StoreKitManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "交易正在处理中，请稍后再试"])

            @unknown default:
                // 未知状态
                #if DEBUG
                print("[ERROR] StoreKit - 未知购买状态")
                #endif
                errorMessage = "未知购买状态，请稍后再试"
                isLoading = false
                throw NSError(domain: "StoreKitManager", code: 2, userInfo: [NSLocalizedDescriptionKey: "未知购买状态，请稍后再试"])
            }
        } catch {
            // 购买失败
            #if DEBUG
            print("[ERROR] StoreKit - 购买异常: \(error)")
            print("[ERROR] StoreKit - 错误类型: \(type(of: error))")
            print("[ERROR] StoreKit - 错误描述: \(error.localizedDescription)")
            if let storeKitError = error as? StoreKitError {
                print("[ERROR] StoreKit - StoreKitError: \(storeKitError)")
            }
            if let nsError = error as NSError? {
                print("[ERROR] StoreKit - NSError domain: \(nsError.domain)")
                print("[ERROR] StoreKit - NSError code: \(nsError.code)")
                print("[ERROR] StoreKit - NSError userInfo: \(nsError.userInfo)")
            }
            #endif
            errorMessage = "购买失败: \(error.localizedDescription)"
            isLoading = false
            throw error
        }
    }

    /// 恢复购买
    @MainActor
    func restorePurchases() async throws {
        isLoading = true
        errorMessage = nil

        #if DEBUG
        print("[DEBUG] StoreKit - 开始恢复购买")
        #endif

        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        do {
            // 记录恢复前的购买状态
            let previousPurchasedProducts = Set(purchasedProductIDs)

            // 请求App Store恢复购买
            try await AppStore.sync()
            #if DEBUG
            print("[DEBUG] StoreKit - AppStore.sync() 成功")
            #endif

            // 更新购买状态
            try await updatePurchasedProducts()
            #if DEBUG
            print("[DEBUG] StoreKit - 更新购买状态成功")
            #endif

            // 检查是否恢复了任何购买
            let newPurchasedProducts = Set(purchasedProductIDs)
            let restoredProducts = newPurchasedProducts.subtracting(previousPurchasedProducts)

            if !restoredProducts.isEmpty {
                // 成功恢复了购买
                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: "restore_purchases_success".localized + "\n" + "restore_purchases_success_message".localized
                )

                #if DEBUG
                print("[DEBUG] StoreKit - 恢复了购买: \(restoredProducts)")
                #endif
            } else {
                // 没有找到可恢复的购买 - 检查环境并提供说明
                let actualSandboxStatus = isRunningInSandbox()

                var message = "没有找到可恢复的购买记录"
                if actualSandboxStatus {
                    message += "\n\n沙盒环境说明：\n• 订阅会快速过期（月度5分钟，年度1小时）\n• 过期的交易记录会被自动清理\n• 请先购买订阅再测试恢复功能"
                } else {
                    // 🚨 生产环境警告
                    message += "\n\n⚠️ 注意：您当前在生产环境中，如果之前没有真实购买记录，这是正常的。"
                }

                NotificationCenter.default.post(
                    name: Notification.Name("ShowToast"),
                    object: message
                )

                #if DEBUG
                print("[DEBUG] StoreKit - 没有找到可恢复的购买")
                print("[DEBUG] StoreKit - 当前环境: \(actualSandboxStatus ? "沙盒" : "生产")")
                if actualSandboxStatus {
                    print("[DEBUG] StoreKit - 沙盒环境：订阅可能已过期或被清理")
                    print("[DEBUG] StoreKit - 沙盒环境：月度订阅5分钟过期，年度订阅1小时过期")
                } else {
                    print("[DEBUG] StoreKit - 生产环境：如果之前没有真实购买，这是正常的")
                }
                #endif
            }

            isLoading = false
        } catch {
            #if DEBUG
            print("[ERROR] StoreKit - 恢复购买失败: \(error.localizedDescription)")
            #endif
            errorMessage = "恢复购买失败: \(error.localizedDescription)"

            // 显示错误提示
            NotificationCenter.default.post(
                name: Notification.Name("ShowToast"),
                object: "恢复购买失败: \(error.localizedDescription)"
            )

            isLoading = false
            throw error
        }
    }

    /// 检查是否已购买产品
    func isPurchased(productID: String) -> Bool {
        return purchasedProductIDs.contains(productID)
    }

    /// 获取产品
    func product(for tier: SubscriptionTier) -> Product? {
        guard !tier.productID.isEmpty else { return nil }
        return products.first(where: { $0.id == tier.productID })
    }

    // MARK: - 私有方法

    /// 监听交易
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached { [weak self] in
            #if DEBUG
            print("[DEBUG] StoreKit - 开始监听交易更新")
            #endif

            // 监听交易更新
            for await result in Transaction.updates {
                do {
                    guard let self = self else { return }
                    let transaction = try await self.checkVerificationResult(result)
                    #if DEBUG
                    print("[DEBUG] StoreKit - 收到交易更新: \(transaction.productID)")
                    #endif

                    // 处理交易 - 使用 nonisolated 方法
                    let productID = transaction.productID

                    // 在主线程上执行更新
                    await self.handleTransaction(productID: productID)

                    // 完成交易
                    await transaction.finish()

                    #if DEBUG
                    print("[DEBUG] StoreKit - 交易处理完成: \(transaction.productID)")
                    #endif
                } catch {
                    // 静默处理交易处理错误 - 使用全局函数避免async推断
                    if !isSandboxErrorSync(error) {
                        print("[ERROR] StoreKit - 处理交易更新失败: \(error.localizedDescription)")
                    }
                }
            }
        }
    }

    /// 处理交易 - 这个方法在主线程上执行
    @MainActor
    private func handleTransaction(productID: String) async {
        do {
            try await self.updatePurchasedProducts()
            // 🚨 修复：使用统一的状态更新方法
            await self.updateSubscriptionStatusWithExpiration(for: productID)
        } catch {
            print("[ERROR] StoreKit - 更新购买状态失败: \(error.localizedDescription)")
        }
    }

    /// 检查验证结果
    private func checkVerificationResult<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .verified(let safe):
            return safe
        case .unverified(_, let error):
            throw error
        }
    }

    /// 检查是否是沙盒认证错误（异步版本）
    private func isSandboxAuthError(_ error: Error) async -> Bool {
        if let nsError = error as NSError? {
            // 检查ASDErrorDomain Code=500 (沙盒认证错误)
            if nsError.domain == "ASDErrorDomain" && nsError.code == 500 {
                await MainActor.run {
                    self.sandboxStatus = .authError
                }
                return true
            }

            // 检查AMSErrorDomain Code=2 (认证错误)
            if nsError.domain == "AMSErrorDomain" && nsError.code == 2 {
                await MainActor.run {
                    self.sandboxStatus = .authError
                }
                return true
            }

            // 检查嵌套的认证错误
            if let underlyingError = nsError.userInfo[NSUnderlyingErrorKey] as? NSError {
                return await isSandboxAuthError(underlyingError)
            }
        }
        return false
    }

    /// 检查是否是沙盒认证错误（同步版本，用于Task.detached中）
    private func isSandboxAuthErrorSync(_ error: Error) -> Bool {
        return Self.isSandboxError(error)
    }

    /// 静态方法：纯同步的沙盒错误检查（用于Task.detached中避免self访问）
    static func isSandboxError(_ error: Error) -> Bool {
        if let nsError = error as NSError? {
            // 检查ASDErrorDomain Code=500 (沙盒认证错误)
            if nsError.domain == "ASDErrorDomain" && nsError.code == 500 {
                return true
            }

            // 检查AMSErrorDomain Code=2 (认证错误)
            if nsError.domain == "AMSErrorDomain" && nsError.code == 2 {
                return true
            }

            // 检查嵌套的认证错误
            if let underlyingError = nsError.userInfo[NSUnderlyingErrorKey] as? NSError {
                return isSandboxError(underlyingError)
            }
        }
        return false
    }

    /// 更新已购买产品
    @MainActor
    private func updatePurchasedProducts() async throws {
        #if DEBUG
        print("[DEBUG] StoreKit - 开始更新已购买产品")
        #endif

        // 清空已购买产品
        purchasedProductIDs.removeAll()

        // 获取所有交易，添加错误处理
        var hasTransactions = false

        for await result in Transaction.currentEntitlements {
            hasTransactions = true
            do {
                // 验证交易
                let transaction = try checkVerificationResult(result)
                #if DEBUG
                print("[DEBUG] StoreKit - 找到交易: \(transaction.productID)")
                #endif

                // 如果是订阅，检查是否过期
                do {
                    let subscriptionStatuses = try await Product.SubscriptionInfo.status(for: transaction.productID)
                    #if DEBUG
                    print("[DEBUG] StoreKit - 获取到订阅状态: \(subscriptionStatuses.count) 个状态")
                    #endif

                    // 检查是否有任何活跃的订阅（包括免费试用期）
                    let hasActiveSubscription = subscriptionStatuses.contains(where: { (status: Product.SubscriptionInfo.Status) in
                        // 检查订阅是否处于活跃状态（包括免费试用期）
                        let isActive = status.state == .subscribed ||
                                      status.state == .inGracePeriod ||
                                      status.state == .inBillingRetryPeriod
                        #if DEBUG
                        print("[DEBUG] StoreKit - 订阅状态: \(status.state), 是否活跃: \(isActive)")
                        #endif
                        return isActive
                    })

                    if hasActiveSubscription {
                        // 添加到已购买产品
                        purchasedProductIDs.insert(transaction.productID)
                        #if DEBUG
                        print("[DEBUG] StoreKit - 有效订阅: \(transaction.productID)")
                        #endif
                    } else {
                        // 在沙盒环境中，如果没有状态信息但交易存在，可能是状态查询延迟
                        let isTestEnvironment = isRunningInSandbox()

                        if isTestEnvironment && subscriptionStatuses.isEmpty {
                            // 沙盒环境：如果交易存在但状态为空，暂时认为是有效的
                            purchasedProductIDs.insert(transaction.productID)
                            #if DEBUG
                            print("[DEBUG] StoreKit - 沙盒环境：状态查询为空，但交易存在，暂时认为有效: \(transaction.productID)")
                            #endif
                        } else if isTestEnvironment {
                            // 沙盒环境：即使状态显示过期，也给一些容错时间
                            let transactionAge = Date().timeIntervalSince(transaction.purchaseDate)
                            let maxAge: TimeInterval = 3600 // 1小时

                            if transactionAge < maxAge {
                                purchasedProductIDs.insert(transaction.productID)
                                #if DEBUG
                                print("[DEBUG] StoreKit - 沙盒环境：交易在容错时间内，认为有效: \(transaction.productID), 年龄: \(Int(transactionAge))秒")
                                #endif
                            } else {
                                #if DEBUG
                                print("[DEBUG] StoreKit - 沙盒环境：交易超过容错时间，认为过期: \(transaction.productID)")
                                #endif
                            }
                        } else {
                            #if DEBUG
                            print("[DEBUG] StoreKit - 订阅已过期: \(transaction.productID)")
                            #endif
                        }
                    }
                } catch {
                    #if DEBUG
                    print("[DEBUG] StoreKit - 无法获取订阅状态: \(transaction.productID), 错误: \(error.localizedDescription)")
                    #endif

                    // 如果无法获取状态，在沙盒环境中暂时认为交易有效
                    let isTestEnvironment = isRunningInSandbox()
                    if isTestEnvironment {
                        purchasedProductIDs.insert(transaction.productID)
                        #if DEBUG
                        print("[DEBUG] StoreKit - 沙盒环境：状态查询失败，暂时认为交易有效: \(transaction.productID)")
                        #endif
                    }
                }
            } catch {
                // 处理StoreKit认证错误
                if await isSandboxAuthError(error) {
                    #if DEBUG
                    print("[DEBUG] StoreKit - 检测到沙盒认证错误，静默处理")
                    #endif
                    // 沙盒认证错误，不抛出异常，继续使用默认状态
                } else {
                    #if DEBUG
                    print("[ERROR] StoreKit - 验证交易失败: \(error.localizedDescription)")
                    #endif
                }
            }
        }

        if !hasTransactions {
            #if DEBUG
            print("[DEBUG] StoreKit - 没有找到任何交易")
            #endif
        }

        // 更新订阅状态
        updateSubscriptionStatusFromPurchases()
        #if DEBUG
        print("[DEBUG] StoreKit - 已购买产品: \(purchasedProductIDs)")
        #endif
    }

    /// 根据产品ID更新订阅状态
    @MainActor
    private func updateSubscriptionStatus(for productID: String) {
        Task {
            await updateSubscriptionStatusWithExpiration(for: productID)
        }
    }

    /// 根据产品ID更新订阅状态（包含到期时间和试用期状态）
    @MainActor
    private func updateSubscriptionStatusWithExpiration(for productID: String) async {
        // 根据产品ID确定订阅级别
        var tier: SubscriptionTier?
        if productID == SubscriptionTier.pro.productID {
            tier = .pro
        } else if productID == SubscriptionTier.expert.productID {
            tier = .expert
        }

        guard let subscriptionTier = tier else { return }

        // 获取订阅到期时间和试用期状态
        var expirationDate: Date?
        var isInTrial = false
        var trialEndDate: Date?

        do {
            let subscriptionStatuses = try await Product.SubscriptionInfo.status(for: productID)
            for status in subscriptionStatuses {
                if status.state == .subscribed ||
                   status.state == .inGracePeriod ||
                   status.state == .inBillingRetryPeriod {

                    // 检查当前交易的试用期状态
                    switch status.transaction {
                    case .verified(let transaction):
                        isInTrial = transaction.offerType == .introductory

                        // 🚨 关键修复：正确计算试用期和订阅到期时间
                        let purchaseDate = transaction.purchaseDate

                        if isInTrial {
                            // 试用期结束时间 = 购买时间 + 60天
                            trialEndDate = Calendar.current.date(byAdding: .day, value: 60, to: purchaseDate)

                            // 🔧 修复：订阅到期时间应该是试用期结束后再加上订阅周期
                            if let trialEnd = trialEndDate {
                                if productID.contains("monthly") {
                                    expirationDate = Calendar.current.date(byAdding: .month, value: 1, to: trialEnd)
                                } else if productID.contains("annual") {
                                    expirationDate = Calendar.current.date(byAdding: .year, value: 1, to: trialEnd)
                                }
                            }
                        } else {
                            // 非试用期：直接从购买时间计算订阅到期时间
                            if productID.contains("monthly") {
                                expirationDate = Calendar.current.date(byAdding: .month, value: 1, to: purchaseDate)
                            } else if productID.contains("annual") {
                                expirationDate = Calendar.current.date(byAdding: .year, value: 1, to: purchaseDate)
                            }
                        }

                        #if DEBUG
                        print("[DEBUG] StoreKit - 🚨 关键订阅信息:")
                        print("  购买时间: \(purchaseDate)")
                        let offerTypeString: String
                        if let offerType = transaction.offerType {
                            switch offerType {
                            case .introductory:
                                offerTypeString = "试用期"
                            case .promotional:
                                offerTypeString = "促销优惠"
                            case .winBack:
                                offerTypeString = "回归优惠"
                            default:
                                offerTypeString = "其他类型"
                            }
                        } else {
                            offerTypeString = "无"
                        }
                        print("  交易类型: \(offerTypeString)")
                        print("  是否试用期: \(isInTrial)")
                        print("  试用结束时间: \(trialEndDate?.description ?? "无")")
                        print("  订阅到期时间: \(expirationDate?.description ?? "无")")
                        print("  产品ID: \(productID)")
                        #endif

                    case .unverified(_, let error):
                        #if DEBUG
                        print("[DEBUG] StoreKit - 交易验证失败: \(error.localizedDescription)")
                        #endif
                    }
                    break
                }
            }
        } catch {
            #if DEBUG
            print("[DEBUG] StoreKit - 获取订阅状态失败: \(error.localizedDescription)")
            #endif
        }

        // 更新订阅状态，包含到期时间
        SubscriptionManager.shared.updateSubscription(to: subscriptionTier, expirationDate: expirationDate)

        // 更新试用期状态
        SubscriptionManager.shared.updateTrialStatus(isInTrial: isInTrial, trialEndDate: trialEndDate)
    }

    /// 处理新订阅的试用期（基于App Store Connect配置）
    @MainActor
    private func handleTrialPeriodForNewSubscription(productID: String) async {
        #if DEBUG
        print("[DEBUG] StoreKit - 检查App Store试用期状态")
        #endif

        // 检查 StoreKit 的试用期状态
        do {
            let subscriptionStatuses = try await Product.SubscriptionInfo.status(for: productID)
            for status in subscriptionStatuses {
                #if DEBUG
                print("[DEBUG] StoreKit - 订阅状态: \(status.state)")
                #endif

                if status.state == .subscribed {
                    // 检查是否在试用期
                    switch status.transaction {
                    case .verified(let transaction):
                        let isInTrial = transaction.offerType == .introductory
                        let trialEndDate = await calculateTrialEndDate(for: transaction)

                        #if DEBUG
                        print("[DEBUG] StoreKit - 是否在试用期: \(isInTrial), 结束时间: \(trialEndDate?.description ?? "无")")
                        #endif

                        // 更新本地试用期状态
                        SubscriptionManager.shared.updateTrialStatus(
                            isInTrial: isInTrial,
                            trialEndDate: trialEndDate
                        )

                    case .unverified(_, let error):
                        #if DEBUG
                        print("[DEBUG] StoreKit - 交易验证失败: \(error.localizedDescription)")
                        #endif
                    }
                }
            }
        } catch {
            #if DEBUG
            print("[DEBUG] StoreKit - 获取订阅状态失败: \(error.localizedDescription)")
            #endif
        }
    }

    /// 计算试用期结束时间
    @MainActor
    private func calculateTrialEndDate(for transaction: StoreKit.Transaction) async -> Date? {
        // 如果是试用期，计算结束时间
        if transaction.offerType == .introductory {
            // 从购买时间开始计算60天
            return Calendar.current.date(byAdding: .day, value: 60, to: transaction.purchaseDate)
        }
        return nil
    }

    /// 根据已购买产品更新订阅状态
    @MainActor
    private func updateSubscriptionStatusFromPurchases() {
        // 如果没有购买任何产品，使用免费版
        if purchasedProductIDs.isEmpty {
            SubscriptionManager.shared.updateSubscription(to: .free)
            return
        }

        // 检查是否购买了专家版
        if purchasedProductIDs.contains(SubscriptionTier.expert.productID) {
            Task {
                await updateSubscriptionStatusWithExpiration(for: SubscriptionTier.expert.productID)
            }
            return
        }

        // 检查是否购买了专业版
        if purchasedProductIDs.contains(SubscriptionTier.pro.productID) {
            Task {
                await updateSubscriptionStatusWithExpiration(for: SubscriptionTier.pro.productID)
            }
            return
        }

        // 默认使用免费版
        SubscriptionManager.shared.updateSubscription(to: .free)
    }

    // MARK: - 沙盒环境检测

    /// 检测是否运行在沙盒环境中
    private func isRunningInSandbox() -> Bool {
        // 🔧 修复：统一使用收据URL检测，这是最可靠的方法
        let receiptURL = Bundle.main.appStoreReceiptURL?.path ?? ""
        let isSandboxReceipt = receiptURL.contains("sandboxReceipt")

        // 检查是否在模拟器中运行
        #if targetEnvironment(simulator)
        let isSimulator = true
        #else
        let isSimulator = false
        #endif

        // 检查环境变量（仅用于SwiftUI预览）
        let hasTestEnvironment = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"

        // 🚨 关键修复：只有收据URL包含sandboxReceipt才是真正的沙盒环境
        // 不再依赖DEBUG标志或Bundle ID，因为这些在生产环境中也可能存在
        let isSandbox = isSandboxReceipt || isSimulator || hasTestEnvironment

        #if DEBUG
        let bundleID = Bundle.main.bundleIdentifier ?? ""
        print("[DEBUG] StoreKit - 沙盒环境检测:")
        print("  Bundle ID: \(bundleID)")
        print("  收据路径: \(receiptURL)")
        print("  是否沙盒收据: \(isSandboxReceipt)")
        print("  是否模拟器: \(isSimulator)")
        print("  是否预览环境: \(hasTestEnvironment)")
        print("  最终判断: \(isSandbox ? "沙盒环境" : "生产环境")")
        #endif

        return isSandbox
    }

    // MARK: - 收据验证

    /// 验证应用收据
    /// - Returns: 验证结果，包含订阅状态信息
    @MainActor
    func validateReceipt() async throws -> ReceiptValidationResult {
        #if DEBUG
        print("[DEBUG] StoreKit - 开始验证应用收据")
        #endif

        // 获取应用收据
        guard let receiptURL = Bundle.main.appStoreReceiptURL,
              let receiptData = try? Data(contentsOf: receiptURL) else {
            #if DEBUG
            print("[DEBUG] StoreKit - 无法获取应用收据")
            #endif
            throw ReceiptValidationError.noReceipt
        }

        // 将收据数据转换为 Base64 字符串
        let receiptString = receiptData.base64EncodedString()

        // 创建验证请求
        let requestData = ReceiptValidationRequest(
            receiptData: receiptString,
            password: Self.appSpecificSharedSecret,
            excludeOldTransactions: true
        )

        // 发送验证请求到 Apple 服务器
        return try await sendReceiptValidationRequest(requestData)
    }

    /// 发送收据验证请求到 Apple 服务器
    private func sendReceiptValidationRequest(_ request: ReceiptValidationRequest) async throws -> ReceiptValidationResult {
        // 首先尝试生产环境
        do {
            return try await performReceiptValidation(request: request, isProduction: true)
        } catch ReceiptValidationError.testReceipt {
            // 如果是测试收据，尝试沙盒环境
            #if DEBUG
            print("[DEBUG] StoreKit - 检测到测试收据，切换到沙盒环境验证")
            #endif
            return try await performReceiptValidation(request: request, isProduction: false)
        }
    }

    /// 执行收据验证
    private func performReceiptValidation(request: ReceiptValidationRequest, isProduction: Bool) async throws -> ReceiptValidationResult {
        let urlString = isProduction
            ? "https://buy.itunes.apple.com/verifyReceipt"
            : "https://sandbox.itunes.apple.com/verifyReceipt"

        guard let url = URL(string: urlString) else {
            throw ReceiptValidationError.invalidURL
        }

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            throw ReceiptValidationError.encodingError(error)
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                throw ReceiptValidationError.networkError
            }

            let validationResponse = try JSONDecoder().decode(ReceiptValidationResponse.self, from: data)

            // 检查验证状态
            switch validationResponse.status {
            case 0:
                // 验证成功
                return ReceiptValidationResult(
                    isValid: true,
                    activeSubscriptions: extractActiveSubscriptions(from: validationResponse),
                    originalResponse: validationResponse
                )
            case 21007:
                // 测试收据在生产环境中验证
                throw ReceiptValidationError.testReceipt
            default:
                // 其他错误
                throw ReceiptValidationError.validationFailed(validationResponse.status)
            }
        } catch {
            if error is ReceiptValidationError {
                throw error
            } else {
                throw ReceiptValidationError.networkError
            }
        }
    }

    /// 从验证响应中提取活跃订阅
    private func extractActiveSubscriptions(from response: ReceiptValidationResponse) -> [ActiveSubscription] {
        var activeSubscriptions: [ActiveSubscription] = []

        // 检查最新收据信息中的应用内购买
        for purchase in response.latestReceiptInfo ?? [] {
            // 检查是否是订阅产品
            if SubscriptionTier.allCases.contains(where: { $0.productID == purchase.productId }) {
                // 检查订阅是否仍然有效
                if let expiresDate = purchase.expiresDateMs,
                   let expiresDateDouble = Double(expiresDate) {
                   let expirationDate = Date(timeIntervalSince1970: expiresDateDouble / 1000)
                   if expirationDate > Date() {

                    let subscription = ActiveSubscription(
                        productId: purchase.productId,
                        expirationDate: expirationDate,
                        isInFreeTrial: purchase.isTrialPeriod == "true",
                        autoRenewStatus: purchase.autoRenewStatus == "1"
                    )
                    activeSubscriptions.append(subscription)
                   }
                }
            }
        }

        return activeSubscriptions
    }

    /// 获取应用专用共享密钥（仅用于测试）
    #if DEBUG
    static func getSharedSecret() -> String {
        return appSpecificSharedSecret
    }

    /// 测试静态方法调用（用于验证编译）
    static func testStaticMethodCall() {
        let testError = NSError(domain: "TestDomain", code: 123, userInfo: nil)
        let _ = StoreKitManager.isSandboxError(testError)
    }
    #endif
}
