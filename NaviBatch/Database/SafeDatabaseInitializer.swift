//
//  SafeDatabaseInitializer.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//  安全的数据库初始化器，处理模式不匹配问题
//

import Foundation
import SwiftData
import os.log

#if DEBUG
/// 安全的数据库初始化器
class SafeDatabaseInitializer {
    
    /// 安全地初始化数据库容器
    static func createSafeContainer() -> ModelContainer {
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
        let modelConfiguration = ModelConfiguration(isStoredInMemoryOnly: false)

        do {
            // 尝试创建容器
            let container = try ModelContainer(for: schema, configurations: [modelConfiguration])

            // 测试数据库访问
            Task { @MainActor in
                await testDatabaseAccess(container: container)
            }

            return container
        } catch {
            print("[ERROR] SafeDatabaseInitializer: 创建数据库容器失败: \(error.localizedDescription)")

            // 如果创建失败，尝试删除数据库文件并重新创建
            return recreateDatabase(schema: schema, configuration: modelConfiguration)
        }
    }
    
    /// 重新创建数据库
    private static func recreateDatabase(schema: Schema, configuration: ModelConfiguration) -> ModelContainer {
        print("[WARNING] SafeDatabaseInitializer: 尝试重新创建数据库...")
        
        // 获取数据库文件路径
        if let url = getDefaultDatabaseURL() {
            print("[INFO] SafeDatabaseInitializer: 数据库路径: \(url.path)")
            
            // 删除现有数据库文件
            deleteExistingDatabaseFiles(at: url)
        }
        
        do {
            // 重新创建容器
            let newContainer = try ModelContainer(for: schema, configurations: [configuration])
            print("[SUCCESS] SafeDatabaseInitializer: ✅ 成功重新创建数据库")
            return newContainer
        } catch {
            print("[ERROR] SafeDatabaseInitializer: 重新创建数据库也失败: \(error.localizedDescription)")
            
            // 最后的回退：使用内存数据库
            print("[FALLBACK] SafeDatabaseInitializer: 回退到内存数据库")
            let fallbackConfig = ModelConfiguration(isStoredInMemoryOnly: true)
            return try! ModelContainer(for: schema, configurations: [fallbackConfig])
        }
    }
    
    /// 获取默认数据库URL
    private static func getDefaultDatabaseURL() -> URL? {
        do {
            let tempConfig = ModelConfiguration(isStoredInMemoryOnly: false)
            let tempContainer = try ModelContainer(for: Schema([DeliveryPoint.self]), configurations: [tempConfig])
            return tempContainer.configurations.first?.url
        } catch {
            return nil
        }
    }
    
    /// 删除现有数据库文件
    private static func deleteExistingDatabaseFiles(at url: URL) {
        let fileManager = FileManager.default
        
        // 删除主数据库文件
        if fileManager.fileExists(atPath: url.path) {
            do {
                try fileManager.removeItem(at: url)
                print("[INFO] SafeDatabaseInitializer: 已删除主数据库文件")
            } catch {
                print("[ERROR] SafeDatabaseInitializer: 删除主数据库文件失败: \(error.localizedDescription)")
            }
        }
        
        // 删除WAL文件
        let walURL = url.appendingPathExtension("wal")
        if fileManager.fileExists(atPath: walURL.path) {
            try? fileManager.removeItem(at: walURL)
            print("[INFO] SafeDatabaseInitializer: 已删除WAL文件")
        }
        
        // 删除SHM文件
        let shmURL = url.appendingPathExtension("shm")
        if fileManager.fileExists(atPath: shmURL.path) {
            try? fileManager.removeItem(at: shmURL)
            print("[INFO] SafeDatabaseInitializer: 已删除SHM文件")
        }
    }
    
    /// 测试数据库访问
    @MainActor
    private static func testDatabaseAccess(container: ModelContainer) async {
        print("[INFO] SafeDatabaseInitializer: 开始测试数据库访问...")
        
        let context = container.mainContext
        
        do {
            // 测试基本查询
            let pointDescriptor = FetchDescriptor<DeliveryPoint>()
            let points = try context.fetch(pointDescriptor)
            print("[SUCCESS] SafeDatabaseInitializer: ✅ DeliveryPoint 查询成功，共 \(points.count) 条记录")
            
            let routeDescriptor = FetchDescriptor<Route>()
            let routes = try context.fetch(routeDescriptor)
            print("[SUCCESS] SafeDatabaseInitializer: ✅ Route 查询成功，共 \(routes.count) 条记录")
            
            // 测试新字段访问
            await testNewTimeFields(context: context)
            
        } catch {
            print("[ERROR] SafeDatabaseInitializer: ❌ 数据库访问测试失败: \(error.localizedDescription)")
            print("[ERROR] SafeDatabaseInitializer: 错误详情: \(error)")
        }
    }
    
    /// 测试新的时间字段
    @MainActor
    private static func testNewTimeFields(context: ModelContext) async {
        print("[INFO] SafeDatabaseInitializer: 测试新的时间字段...")
        
        do {
            // 创建一个测试点来验证新字段
            let testPoint = DeliveryPoint(
                sort_number: 999999,
                streetName: "测试地址_SafeInit",
                latitude: 0,
                longitude: 0
            )
            
            // 尝试设置新的时间字段
            testPoint.scheduledDeliveryTime = "测试时间"
            testPoint.deliveryTimeSlot = "测试时间段"
            testPoint.deliveryDate = "测试日期"
            
            context.insert(testPoint)
            try context.save()
            
            print("[SUCCESS] SafeDatabaseInitializer: ✅ 新时间字段测试成功")
            
            // 清理测试数据
            context.delete(testPoint)
            try context.save()
            
        } catch {
            print("[ERROR] SafeDatabaseInitializer: ❌ 新时间字段测试失败: \(error.localizedDescription)")
            print("[WARNING] SafeDatabaseInitializer: 可能需要数据库迁移或重建")
        }
    }
    
    /// 检查是否需要强制重建数据库
    static func shouldForceRecreate() -> Bool {
        // 检查命令行参数
        if ProcessInfo.processInfo.arguments.contains("--force-recreate-db") {
            return true
        }
        
        // 检查是否存在已知的模式问题
        // 这里可以添加更多的检查逻辑
        
        return false
    }
}
#endif

// 辅助日志函数
private func logInfo(_ message: String) {
    print("[INFO] SafeDatabaseInitializer: \(message)")
}

private func logError(_ message: String) {
    print("[ERROR] SafeDatabaseInitializer: \(message)")
}
