import Foundation
import SwiftData
import os.log

// MARK: - 版本化Schema定义

/// NaviBatch数据库Schema V1 - 初始版本
enum NaviBatchSchemaV1: VersionedSchema {
    static var models: [any PersistentModel.Type] {
        [DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self]
    }

    static var versionIdentifier: Schema.Version {
        Schema.Version(1, 0, 0)
    }
}

/// NaviBatch数据库Schema V2 - 当前版本
enum NaviBatchSchemaV2: VersionedSchema {
    static var models: [any PersistentModel.Type] {
        [DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self]
    }

    static var versionIdentifier: Schema.Version {
        Schema.Version(2, 0, 0)
    }
}

/// NaviBatch数据库Schema V3 - 添加第三方排序号字段
/// 添加thirdPartySortNumber字段到DeliveryPoint
enum NaviBatchSchemaV3: VersionedSchema {
    static var models: [any PersistentModel.Type] {
        [DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self]
    }

    static var versionIdentifier: Schema.Version {
        Schema.Version(3, 0, 0)
    }
}

/// NaviBatch数据库Schema V4 - 添加Amazon Flex时间字段
/// 添加scheduledDeliveryTime, deliveryTimeSlot, deliveryDate字段到DeliveryPoint
enum NaviBatchSchemaV4: VersionedSchema {
    static var models: [any PersistentModel.Type] {
        [DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self]
    }

    static var versionIdentifier: Schema.Version {
        Schema.Version(4, 0, 0)
    }
}

/// NaviBatch数据库Schema V5 - 添加路线缓存
/// 添加RouteCache和DistanceCache模型
enum NaviBatchSchemaV5: VersionedSchema {
    static var models: [any PersistentModel.Type] {
        [DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self]
    }

    static var versionIdentifier: Schema.Version {
        Schema.Version(5, 0, 0)
    }
}

// MARK: - 迁移计划

/// NaviBatch数据库迁移计划
enum NaviBatchMigrationPlan: SchemaMigrationPlan {
    static var schemas: [any VersionedSchema.Type] {
        [NaviBatchSchemaV1.self, NaviBatchSchemaV2.self, NaviBatchSchemaV3.self, NaviBatchSchemaV4.self, NaviBatchSchemaV5.self]
    }

    static var stages: [MigrationStage] {
        [migrateV1toV2, migrateV2toV3, migrateV3toV4, migrateV4toV5]
    }

    /// 从V1迁移到V2的阶段
    static let migrateV1toV2 = MigrationStage.custom(
        fromVersion: NaviBatchSchemaV1.self,
        toVersion: NaviBatchSchemaV2.self,
        willMigrate: { context in
            // 在迁移开始前执行的代码
            logInfo("开始从Schema V1迁移到V2")
        },
        didMigrate: { context in
            // 在迁移完成后执行的代码
            logInfo("从Schema V1到V2的迁移已完成")

            // 执行数据修复或转换
            Task { @MainActor in
                // 执行vehiclePosition字段迁移
                await DatabaseMigrator.migrateVehiclePositions(in: context)
            }
        }
    )

    /// 从V2迁移到V3的阶段 - 添加第三方排序号字段
    static let migrateV2toV3 = MigrationStage.custom(
        fromVersion: NaviBatchSchemaV2.self,
        toVersion: NaviBatchSchemaV3.self,
        willMigrate: { context in
            // 在迁移开始前执行的代码
            logInfo("开始从Schema V2迁移到V3 - 添加第三方排序号字段")
        },
        didMigrate: { context in
            // 在迁移完成后执行的代码
            logInfo("从Schema V2到V3的迁移已完成 - 第三方排序号字段已添加")

            // 执行数据修复或转换
            Task { @MainActor in
                // 初始化第三方排序号字段
                await initializeThirdPartySortFields(in: context)
            }
        }
    )

    /// 从V3迁移到V4的阶段 - 添加Amazon Flex时间字段
    static let migrateV3toV4 = MigrationStage.custom(
        fromVersion: NaviBatchSchemaV3.self,
        toVersion: NaviBatchSchemaV4.self,
        willMigrate: { context in
            // 在迁移开始前执行的代码
            logInfo("开始从Schema V3迁移到V4 - 添加Amazon Flex时间字段")
        },
        didMigrate: { context in
            // 在迁移完成后执行的代码
            logInfo("从Schema V3到V4的迁移已完成 - Amazon Flex时间字段已添加")

            // 执行数据修复或转换
            Task { @MainActor in
                // 初始化新的时间字段
                await initializeDeliveryTimeFields(in: context)
            }
        }
    )

    /// 从V4迁移到V5的阶段 - 添加路线缓存
    static let migrateV4toV5 = MigrationStage.custom(
        fromVersion: NaviBatchSchemaV4.self,
        toVersion: NaviBatchSchemaV5.self,
        willMigrate: { context in
            logInfo("开始从Schema V4迁移到V5 - 添加路线缓存模型")
        },
        didMigrate: { context in
            logInfo("从Schema V4到V5的迁移已完成 - 路线缓存模型已添加")
            // 路线缓存模型是新增的，不需要数据迁移
            // SwiftData会自动创建新的表结构
        }
    )

    /// 初始化第三方排序号字段
    @MainActor
    static func initializeThirdPartySortFields(in context: ModelContext) async {
        await DatabaseMigrator.migrateField(modelType: DeliveryPoint.self, in: context) { point in
            // 初始化第三方排序号字段为nil（它已经是可选类型）
            // 这个字段会在扫描器导入或手动设置时被填充
            if point.thirdPartySortNumber == nil {
                point.thirdPartySortNumber = nil
                logInfo("DeliveryPoint[\(point.id)]: 初始化thirdPartySortNumber字段")
            }
        }
    }

    /// 初始化Amazon Flex时间字段
    @MainActor
    static func initializeDeliveryTimeFields(in context: ModelContext) async {
        await DatabaseMigrator.migrateField(modelType: DeliveryPoint.self, in: context) { point in
            // 初始化新的时间字段为nil（它们已经是可选类型）
            // 这些字段会在后续的AI识别中被填充
            if point.scheduledDeliveryTime == nil {
                point.scheduledDeliveryTime = nil
                logInfo("DeliveryPoint[\(point.id)]: 初始化scheduledDeliveryTime字段")
            }
            if point.deliveryTimeSlot == nil {
                point.deliveryTimeSlot = nil
                logInfo("DeliveryPoint[\(point.id)]: 初始化deliveryTimeSlot字段")
            }
            if point.deliveryDate == nil {
                point.deliveryDate = nil
                logInfo("DeliveryPoint[\(point.id)]: 初始化deliveryDate字段")
            }
        }
    }
}

// MARK: - 数据库迁移器

/// 数据库迁移工具类
class DatabaseMigrator {

    /// 将时间字符串转换为Date对象
    /// - Parameter timeString: 时间字符串，格式为"HH:mm"
    /// - Returns: 转换后的Date对象，如果转换失败则返回nil
    static func convertTimeStringToDate(_ timeString: String) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"

        if let date = formatter.date(from: timeString) {
            // 使用今天的日期和解析出的时间
            let calendar = Calendar.current
            let today = calendar.startOfDay(for: Date())
            let components = calendar.dateComponents([.hour, .minute], from: date)

            return calendar.date(bySettingHour: components.hour ?? 0,
                                minute: components.minute ?? 0,
                                second: 0,
                                of: today)
        }

        return nil
    }

    /// 通用字段迁移方法
    /// 使用@MainActor确保所有ModelContext操作都在主线程上执行
    @MainActor
    static func migrateField<T: PersistentModel>(
        modelType: T.Type,
        in context: ModelContext,
        transform: (T) -> Void
    ) async {
        do {
            logInfo("开始迁移\(modelType)数据...")

            // 🔧 添加错误处理和安全检查
            let descriptor = FetchDescriptor<T>()

            // 先检查是否能够访问数据库
            guard let _ = try? context.fetch(FetchDescriptor<T>(predicate: #Predicate { _ in false })) else {
                logError("无法访问\(modelType)数据表，可能需要数据库重建")
                return
            }

            let entities = try context.fetch(descriptor)
            var migratedCount = 0

            logInfo("开始迁移\(modelType)数据，共\(entities.count)条记录")

            for entity in entities {
                transform(entity)
                migratedCount += 1
            }

            if migratedCount > 0 {
                try context.save()
                logInfo("成功迁移\(modelType)数据，共处理\(migratedCount)条记录")
            } else {
                logInfo("没有\(modelType)数据需要迁移")
            }
        } catch {
            logError("迁移\(modelType)数据失败: \(error.localizedDescription)")
            logError("错误详情: \(error)")

            // 🚨 如果是模式不匹配错误，建议重建数据库
            if error.localizedDescription.contains("schema") ||
               error.localizedDescription.contains("model") ||
               error.localizedDescription.contains("migration") {
                logError("⚠️ 检测到数据库模式问题，建议删除应用重新安装或使用 --force-recreate-db 参数")
            }
        }
    }

    /// 迁移vehiclePosition字段
    @MainActor
    static func migrateVehiclePositions(in context: ModelContext) async {
        await migrateField(modelType: DeliveryPoint.self, in: context) { point in
            // 如果vehiclePosition为nil，则设置为空字符串
            if point.vehiclePosition == nil {
                point.vehiclePosition = ""
                logInfo("DeliveryPoint[\(point.id)]: vehiclePosition为nil，已设置为空字符串")
            }
        }
    }

    /// 迁移Route的isOptimized字段
    @MainActor
    static func migrateRouteOptimizedFlag(in context: ModelContext) async {
        await migrateField(modelType: Route.self, in: context) { route in
            // 根据现有数据推断默认值
            // 如果路线有sorted_number不等于sort_number的点，说明已经优化过
            let hasOptimizedPoints = route.points.contains { $0.sorted_number != $0.sort_number }
            route.isOptimized = hasOptimizedPoints
            logInfo("Route[\(route.id)]: isOptimized设置为\(hasOptimizedPoints)")
        }
    }
}

// MARK: - 测试工具

#if DEBUG
/// 数据库迁移测试工具
struct DatabaseMigrationTests {
    /// 运行所有迁移测试
    static func runAllTests() async {
        await testVehiclePositionMigration()
        await testRouteOptimizedFlagMigration()
        // 添加更多测试...
    }

    /// 测试vehiclePosition字段迁移
    static func testVehiclePositionMigration() async {
        logInfo("开始测试vehiclePosition字段迁移")

        // 创建测试容器
        let schema = Schema([DeliveryPoint.self])
        let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)

        do {
            let container = try ModelContainer(for: schema, configurations: [config])

            // 使用@MainActor隔离ModelContext操作
            await MainActor.run {
                let context = container.mainContext

                // 创建测试数据
                let point = DeliveryPoint(
                    sort_number: 1,
                    streetName: "测试地址",
                    latitude: 0,
                    longitude: 0
                )
                point.vehiclePosition = nil
                context.insert(point)
                try? context.save()

                // 执行迁移 - 在MainActor上下文中调用异步函数
                Task {
                    await DatabaseMigrator.migrateVehiclePositions(in: context)

                    // 验证结果
                    let descriptor = FetchDescriptor<DeliveryPoint>()
                    let points = try? context.fetch(descriptor)

                    if let migratedPoint = points?.first,
                       migratedPoint.vehiclePosition == "" {
                        logInfo("✅ vehiclePosition迁移测试通过")
                    } else {
                        logError("❌ vehiclePosition迁移测试失败")
                    }
                }
            }
        } catch {
            logError("❌ 测试失败: \(error.localizedDescription)")
        }
    }

    /// 测试Route的isOptimized字段迁移
    static func testRouteOptimizedFlagMigration() async {
        logInfo("开始测试Route的isOptimized字段迁移")

        // 创建测试容器
        let schema = Schema([Route.self, DeliveryPoint.self])
        let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)

        do {
            let container = try ModelContainer(for: schema, configurations: [config])

            // 使用@MainActor隔离ModelContext操作
            await MainActor.run {
                let context = container.mainContext

                // 创建测试数据
                let route = Route(name: "测试路线")

                // 添加一些已优化的点
                let point1 = DeliveryPoint(sort_number: 1, streetName: "地址1", latitude: 0, longitude: 0)
                point1.sort_number = 1
                point1.sorted_number = 2 // 不同的值表示已优化

                let point2 = DeliveryPoint(sort_number: 2, streetName: "地址2", latitude: 0, longitude: 0)
                point2.sort_number = 2
                point2.sorted_number = 1 // 不同的值表示已优化

                context.insert(route)
                context.insert(point1)
                context.insert(point2)

                route.addPoint(point1)
                route.addPoint(point2)

                try? context.save()

                // 执行迁移 - 在MainActor上下文中调用异步函数
                Task {
                    await DatabaseMigrator.migrateRouteOptimizedFlag(in: context)

                    // 验证结果
                    let descriptor = FetchDescriptor<Route>()
                    let routes = try? context.fetch(descriptor)

                    if let migratedRoute = routes?.first,
                       migratedRoute.isOptimized == true {
                        logInfo("✅ Route的isOptimized字段迁移测试通过")
                    } else {
                        logError("❌ Route的isOptimized字段迁移测试失败")
                    }
                }
            }
        } catch {
            logError("❌ 测试失败: \(error.localizedDescription)")
        }
    }
}
#endif
