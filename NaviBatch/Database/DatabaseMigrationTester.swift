import Foundation
import SwiftData
import os.log

/// 数据库迁移测试工具
/// 用于在开发环境中测试数据库迁移功能
class DatabaseMigrationTester {

    /// 运行所有迁移测试
    static func runAllTests() async {
        logInfo("开始运行数据库迁移测试...")

        // 运行各个测试
        await testVehiclePositionMigration()
        await testRouteOptimizedFlagMigration()

        logInfo("数据库迁移测试完成")
    }

    /// 测试vehiclePosition字段迁移
    static func testVehiclePositionMigration() async {
        logInfo("开始测试vehiclePosition字段迁移")

        // 创建测试容器
        let config = ModelConfiguration(isStoredInMemoryOnly: true)

        do {
            // 创建Schema对象而不是使用VersionedSchema
            let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
            let container = try ModelContainer(for: schema, configurations: [config])

            // 使用@MainActor隔离ModelContext操作
            await MainActor.run {
                // 创建测试数据
                let context = container.mainContext
                let point = DeliveryPoint(
                    sort_number: 1,
                    streetName: "测试地址",
                    latitude: 0,
                    longitude: 0
                )
                point.vehiclePosition = nil
                context.insert(point)
                try? context.save()

                // 执行迁移 - 在MainActor上下文中调用异步函数
                Task {
                    await DatabaseMigrator.migrateVehiclePositions(in: context)

                    // 验证结果
                    let descriptor = FetchDescriptor<DeliveryPoint>()
                    let points = try? context.fetch(descriptor)

                    if let migratedPoint = points?.first,
                       migratedPoint.vehiclePosition == "" {
                        logInfo("✅ vehiclePosition字段迁移测试通过")
                    } else {
                        logError("❌ vehiclePosition字段迁移测试失败")
                    }
                }
            }
        } catch {
            logError("❌ 测试失败: \(error.localizedDescription)")
        }
    }

    /// 测试Route的isOptimized字段迁移
    static func testRouteOptimizedFlagMigration() async {
        logInfo("开始测试Route的isOptimized字段迁移")

        // 创建测试容器
        let config = ModelConfiguration(isStoredInMemoryOnly: true)

        do {
            // 创建Schema对象而不是使用VersionedSchema
            let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
            let container = try ModelContainer(for: schema, configurations: [config])

            // 使用@MainActor隔离ModelContext操作
            await MainActor.run {
                let context = container.mainContext

                // 创建测试数据
                let route = Route(name: "测试路线")

                // 添加一些已优化的点
                let point1 = DeliveryPoint(sort_number: 1, streetName: "地址1", latitude: 0, longitude: 0)
                point1.sort_number = 1
                point1.sorted_number = 2 // 不同的值表示已优化

                let point2 = DeliveryPoint(sort_number: 2, streetName: "地址2", latitude: 0, longitude: 0)
                point2.sort_number = 2
                point2.sorted_number = 1 // 不同的值表示已优化

                context.insert(route)
                context.insert(point1)
                context.insert(point2)

                route.addPoint(point1)
                route.addPoint(point2)

                try? context.save()

                // 执行迁移 - 在MainActor上下文中调用异步函数
                Task {
                    await DatabaseMigrator.migrateRouteOptimizedFlag(in: context)

                    // 验证结果
                    let descriptor = FetchDescriptor<Route>()
                    let routes = try? context.fetch(descriptor)

                    if let migratedRoute = routes?.first,
                       migratedRoute.isOptimized == true {
                        logInfo("✅ Route的isOptimized字段迁移测试通过")
                    } else {
                        logError("❌ Route的isOptimized字段迁移测试失败")
                    }
                }
            }
        } catch {
            logError("❌ 测试失败: \(error.localizedDescription)")
        }
    }

    /// 添加新字段并测试迁移
    static func testAddNewField() async {
        logInfo("开始测试添加新字段并迁移")

        // 创建测试容器
        let config = ModelConfiguration(isStoredInMemoryOnly: true)

        do {
            // 创建Schema对象而不是使用VersionedSchema
            let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
            let container = try ModelContainer(for: schema, configurations: [config])

            // 使用@MainActor隔离ModelContext操作
            await MainActor.run {
                let context = container.mainContext

                // 创建测试数据
                let point = DeliveryPoint(
                    sort_number: 1,
                    streetName: "测试地址",
                    latitude: 0,
                    longitude: 0
                )
                context.insert(point)
                try? context.save()

                // 模拟添加新字段的迁移 - 在MainActor上下文中调用异步函数
                Task {
                    // 注意：在实际情况中，你需要创建一个新的Schema版本，并添加新字段
                    // 这里我们只是模拟这个过程
                    await DatabaseMigrator.migrateField(modelType: DeliveryPoint.self, in: context) { point in
                        // 假设我们要添加一个新字段estimatedArrivalTime并设置默认值
                        // 在实际情况中，这个字段会在模型中定义
                        logInfo("模拟为DeliveryPoint[\(point.id)]添加estimatedArrivalTime字段")
                    }

                    logInfo("✅ 添加新字段测试完成")
                }
            }
        } catch {
            logError("❌ 测试失败: \(error.localizedDescription)")
        }
    }
}

// 添加一个扩展，用于在开发环境中运行测试
extension DatabaseMigrationTester {
    /// 在开发环境中运行测试
    static func runTestsInDevelopment() {
        #if DEBUG
        Task {
            await runAllTests()
        }
        #endif
    }
}
