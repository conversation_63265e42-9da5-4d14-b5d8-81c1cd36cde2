//
//  ForceDBRecreation.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//  强制重新创建数据库以添加新字段
//

import Foundation
import SwiftData
import os.log

#if DEBUG
/// 强制数据库重新创建工具
class ForceDBRecreation {
    
    /// 强制重新创建数据库（仅调试模式）
    /// 这会删除所有现有数据，请谨慎使用
    static func recreateDatabase() {
        print("[INFO] ForceDBRecreation: 开始强制重新创建数据库")
        
        // 获取数据库文件路径
        let modelConfiguration = ModelConfiguration(isStoredInMemoryOnly: false)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self])
        
        do {
            // 创建临时容器来获取数据库路径
            let tempContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
            
            if let url = tempContainer.configurations.first?.url {
                print("[INFO] ForceDBRecreation: 数据库路径: \(url.path)")
                
                // 检查文件是否存在
                if FileManager.default.fileExists(atPath: url.path) {
                    do {
                        // 删除数据库文件
                        try FileManager.default.removeItem(at: url)
                        print("[INFO] ForceDBRecreation: ✅ 成功删除旧数据库文件")
                        
                        // 删除相关的WAL和SHM文件
                        let walURL = url.appendingPathExtension("wal")
                        let shmURL = url.appendingPathExtension("shm")
                        
                        if FileManager.default.fileExists(atPath: walURL.path) {
                            try? FileManager.default.removeItem(at: walURL)
                            print("[INFO] ForceDBRecreation: 删除WAL文件")
                        }
                        
                        if FileManager.default.fileExists(atPath: shmURL.path) {
                            try? FileManager.default.removeItem(at: shmURL)
                            print("[INFO] ForceDBRecreation: 删除SHM文件")
                        }
                        
                    } catch {
                        print("[ERROR] ForceDBRecreation: 删除数据库文件失败: \(error.localizedDescription)")
                    }
                } else {
                    print("[INFO] ForceDBRecreation: 数据库文件不存在，无需删除")
                }
                
                // 重新创建数据库
                do {
                    let newContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
                    print("[INFO] ForceDBRecreation: ✅ 成功重新创建数据库")
                    
                    // 验证新字段是否存在
                    Task { @MainActor in
                        await verifyNewFields(in: newContainer.mainContext)
                    }
                    
                } catch {
                    print("[ERROR] ForceDBRecreation: 重新创建数据库失败: \(error.localizedDescription)")
                }
                
            } else {
                print("[ERROR] ForceDBRecreation: 无法获取数据库路径")
            }
            
        } catch {
            print("[ERROR] ForceDBRecreation: 创建临时容器失败: \(error.localizedDescription)")
        }
    }
    
    /// 验证新字段是否存在
    @MainActor
    static func verifyNewFields(in context: ModelContext) async {
        print("[INFO] ForceDBRecreation: 开始验证新字段")
        
        // 创建一个测试点来验证字段
        let testPoint = DeliveryPoint(
            sort_number: 1,
            streetName: "测试地址",
            latitude: 0,
            longitude: 0
        )
        
        // 设置新的时间字段
        testPoint.scheduledDeliveryTime = "测试时间"
        testPoint.deliveryTimeSlot = "测试时间段"
        testPoint.deliveryDate = "测试日期"
        
        context.insert(testPoint)
        
        do {
            try context.save()
            print("[INFO] ForceDBRecreation: ✅ 新字段验证成功 - 可以保存时间信息")
            
            // 清理测试数据
            context.delete(testPoint)
            try context.save()
            
        } catch {
            print("[ERROR] ForceDBRecreation: ❌ 新字段验证失败: \(error.localizedDescription)")
        }
    }
    
    /// 检查是否需要重新创建数据库
    static func checkIfRecreationNeeded() -> Bool {
        // 这里可以添加逻辑来检查数据库版本或字段是否存在
        // 目前简单返回true，表示需要重新创建
        return true
    }
}
#endif
