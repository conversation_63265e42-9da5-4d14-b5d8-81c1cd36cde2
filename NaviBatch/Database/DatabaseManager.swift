import SwiftData
import Foundation

/// 数据库管理器
/// 提供全局的数据库容器访问和管理功能
class DatabaseManager {
    static let shared = DatabaseManager()
    
    // 全局变量，用于存储持久化容器
    private var globalPersistentContainer: ModelContainer? = nil
    
    private init() {}
    
    /// 获取持久化存储容器
    func getPersistentContainer() -> ModelContainer {
        // 检查是否在预览环境中运行
        let isPreview = ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"
        
        // 在预览环境中使用内存数据库
        if isPreview {
            logInfo("DatabaseManager - 预览环境中使用内存数据库")
            
            let modelConfiguration = ModelConfiguration(isStoredInMemoryOnly: true)
            let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
            
            do {
                let container = try ModelContainer(
                    for: schema,
                    configurations: [modelConfiguration]
                )
                if let url = container.configurations.first?.url {
                    logInfo("DatabaseManager - 预览环境数据库路径: \(url.path)")
                }
                return container
            } catch {
                logError("DatabaseManager - 创建预览容器失败: \(error.localizedDescription)")
                // 创建一个新的内存容器作为回退
                let fallbackConfig = ModelConfiguration(isStoredInMemoryOnly: true)
                let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
                return try! ModelContainer(for: schema, configurations: [fallbackConfig])
            }
        }
        
        // 如果已经创建了容器，直接返回
        if let container = globalPersistentContainer {
            logInfo("DatabaseManager - 返回现有容器")
            return container
        }
        
        // 创建一个新的容器，确保它使用持久化存储
        logInfo("DatabaseManager - 创建新容器")
        
        // 创建模型配置
        let modelConfiguration = ModelConfiguration(isStoredInMemoryOnly: false)
        let schema = Schema([DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self, ValidatedAddress.self, RouteCache.self, DistanceCache.self])
        
        do {
            let container = try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )
            // 在这里获取并打印路径
            if let url = container.configurations.first?.url {
                print("[INFO] DatabaseManager 数据库路径: \(url.path)")
            }
            
            // 不再执行迁移 - SwiftData自动处理字段变更
            print("[INFO] ✅ 数据库创建成功，SwiftData自动处理字段变更")
            
            // 保存到全局变量
            globalPersistentContainer = container
            return container
        } catch {
            logError("DatabaseManager - 创建新容器失败: \(error.localizedDescription)")
            
            // 尝试创建一个不使用迁移计划的容器
            do {
                logInfo("DatabaseManager - 尝试创建不使用迁移计划的容器")
                let fallbackContainer = try ModelContainer(
                    for: schema, // 使用前面定义的schema
                    configurations: [modelConfiguration]
                )
                
                // 保存到全局变量
                globalPersistentContainer = fallbackContainer
                return fallbackContainer
            } catch {
                logError("DatabaseManager - 创建回退容器也失败: \(error.localizedDescription)")
                
                // 最后的回退：创建内存容器
                logInfo("DatabaseManager - 创建内存容器作为最终回退")
                let memoryConfig = ModelConfiguration(isStoredInMemoryOnly: true)
                let memoryContainer = try! ModelContainer(
                    for: schema, // 使用前面定义的schema
                    configurations: [memoryConfig]
                )
                return memoryContainer
            }
        }
    }
}

// 全局函数，用于向后兼容
func getPersistentContainer() -> ModelContainer {
    return DatabaseManager.shared.getPersistentContainer()
}
