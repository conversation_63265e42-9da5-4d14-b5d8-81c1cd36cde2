//
//  DatabaseUpgradeHelper.swift
//  NaviBatch
//
//  Created by AI Assistant on 2024-12-06.
//  数据库升级助手
//
//  使用示例：
//  在应用启动时检查升级：
//  Task { @MainActor in
//      DatabaseUpgradeHelper.shared.checkForUpgrade()
//  }
//

import Foundation
import SwiftData
import SwiftUI

/// 数据库升级助手
class DatabaseUpgradeHelper: ObservableObject {
    @Published var needsUpgrade = false
    @Published var upgradeMessage = ""
    @Published var isUpgrading = false
    
    static let shared = DatabaseUpgradeHelper()
    
    private init() {}
    
    /// 检查是否需要数据库升级
    @MainActor
    func checkForUpgrade() {
        // 检查是否存在新的时间字段
        let needsTimeFields = checkTimeFieldsExist()

        if !needsTimeFields {
            needsUpgrade = true
            upgradeMessage = """
            NaviBatch 需要升级数据库以支持 Amazon Flex 预约时间功能。

            升级将添加以下新功能：
            • 📅 预约时间识别
            • ⏰ 时间段显示
            • 📋 配送日期管理

            注意：升级过程是安全的，不会丢失现有数据。
            """
        }
    }
    
    /// 检查时间字段是否存在
    @MainActor
    private func checkTimeFieldsExist() -> Bool {
        // 创建一个测试容器来检查字段
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let schema = Schema([DeliveryPoint.self])

        do {
            let container = try ModelContainer(for: schema, configurations: [config])
            let context = container.mainContext

            // 创建测试点
            let testPoint = DeliveryPoint(
                sort_number: 1,
                streetName: "测试",
                latitude: 0,
                longitude: 0
            )

            // 尝试设置新字段
            testPoint.scheduledDeliveryTime = "测试"
            testPoint.deliveryTimeSlot = "测试"
            testPoint.deliveryDate = "测试"

            context.insert(testPoint)
            try context.save()

            return true // 字段存在
        } catch {
            return false // 字段不存在或有问题
        }
    }
    
    /// 执行数据库升级
    @MainActor
    func performUpgrade() async {
        isUpgrading = true

        // 获取主容器
        let container = getPersistentContainer()

        // 执行时间字段迁移 - 在主线程上下文中执行
        await NaviBatchMigrationPlan.initializeDeliveryTimeFields(in: container.mainContext)

        // 验证升级结果 - 现在这个方法也是 @MainActor
        let success = checkTimeFieldsExist()

        if success {
            needsUpgrade = false
            upgradeMessage = "✅ 数据库升级成功！Amazon Flex 时间功能已启用。"
        } else {
            upgradeMessage = "❌ 数据库升级失败，请重启应用重试。"
        }

        isUpgrading = false
    }
    
    /// 跳过升级（用户选择稍后升级）
    func skipUpgrade() {
        needsUpgrade = false
        upgradeMessage = "已跳过升级。您可以稍后在设置中手动升级。"
    }
}

/// 数据库升级提示视图
struct DatabaseUpgradeView: View {
    @ObservedObject var upgradeHelper = DatabaseUpgradeHelper.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 图标
                Image(systemName: "cylinder.split.1x2")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                
                // 标题
                Text("数据库升级")
                    .font(.title)
                    .fontWeight(.bold)
                
                // 消息
                Text(upgradeHelper.upgradeMessage)
                    .multilineTextAlignment(.center)
                    .padding()
                
                if upgradeHelper.isUpgrading {
                    // 升级中
                    ProgressView("正在升级...")
                        .padding()
                } else if upgradeHelper.needsUpgrade {
                    // 升级按钮
                    VStack(spacing: 12) {
                        Button(action: {
                            Task {
                                await upgradeHelper.performUpgrade()
                            }
                        }) {
                            Text("立即升级")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                        
                        Button(action: {
                            upgradeHelper.skipUpgrade()
                            dismiss()
                        }) {
                            Text("稍后升级")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal)
                } else {
                    // 完成
                    Button(action: {
                        dismiss()
                    }) {
                        Text("完成")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(10)
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
}
