# NaviBatch 数据库迁移指南

本文档提供了 NaviBatch 应用中使用 SwiftData 进行数据库迁移的详细指南。

## 目录

1. [数据库架构概述](#数据库架构概述)
2. [版本控制](#版本控制)
3. [迁移计划](#迁移计划)
4. [迁移示例](#迁移示例)
5. [测试迁移](#测试迁移)
6. [最佳实践](#最佳实践)

## 数据库架构概述

NaviBatch 使用 SwiftData 作为持久化数据库，主要包含以下模型：

- `DeliveryPoint`：配送点信息
- `Route`：路线信息
- `DeliveryGroup`：配送组信息
- `SavedAddress`：保存的地址信息

## 版本控制

我们使用 `VersionedSchema` 和 `SchemaMigrationPlan` 来管理数据库版本。每个版本都有一个唯一的标识符，格式为 `major.minor.patch`。

### 当前版本

- V1 (1.0.0)：初始版本
- V2 (2.0.0)：当前版本，包含 vehiclePosition 字段迁移和 Route 的 isOptimized 字段
- V3 (3.0.0)：示例版本，展示如何添加新字段

## 迁移计划

迁移计划定义在 `NaviBatchMigrationPlan` 中，包含从一个版本迁移到另一个版本的所有步骤。

### 迁移阶段

每个迁移阶段都使用 `MigrationStage.custom` 定义，包含以下部分：

- `fromVersion`：起始版本
- `toVersion`：目标版本
- `willMigrate`：迁移开始前执行的代码
- `didMigrate`：迁移完成后执行的代码

## 迁移示例

### 示例1：将 nil 值转换为空字符串

```swift
// 迁移 vehiclePosition 字段
static func migrateVehiclePositions(in context: ModelContext) async {
    await migrateField(modelType: DeliveryPoint.self, in: context) { point in
        // 如果 vehiclePosition 为 nil，则设置为空字符串
        if point.vehiclePosition == nil {
            point.vehiclePosition = ""
            logInfo("DeliveryPoint[\(point.id)]: vehiclePosition 为 nil，已设置为空字符串")
        }
    }
}
```

### 示例2：根据现有数据推断字段值

```swift
// 迁移 Route 的 isOptimized 字段
static func migrateRouteOptimizedFlag(in context: ModelContext) async {
    await migrateField(modelType: Route.self, in: context) { route in
        // 根据现有数据推断默认值
        // 如果路线有 sorted_number 不等于 sort_number 的点，说明已经优化过
        let hasOptimizedPoints = route.points.contains { $0.sorted_number != $0.sort_number }
        route.isOptimized = hasOptimizedPoints
        logInfo("Route[\(route.id)]: isOptimized 设置为 \(hasOptimizedPoints)")
    }
}
```

### 示例3：添加新字段并初始化

```swift
// 初始化 estimatedArrivalTime 字段
static func initializeEstimatedArrivalTimes(in context: ModelContext) async {
    await migrateField(modelType: DeliveryPoint.self, in: context) { point in
        // 对于已经有 arrivalTime 字符串的点，尝试转换为 Date
        if let arrivalTimeStr = point.arrivalTime, arrivalTimeStr != "随时" {
            if let date = convertTimeStringToDate(arrivalTimeStr) {
                // 在实际代码中，这里会设置 estimatedArrivalTime 字段
                // point.estimatedArrivalTime = date
                logInfo("DeliveryPoint[\(point.id)]: 从 arrivalTime(\(arrivalTimeStr)) 初始化 estimatedArrivalTime")
            }
        }
    }
}
```

## 测试迁移

我们提供了测试工具，用于验证迁移功能是否正常工作。

### 运行测试

在开发环境中，可以通过以下方式运行测试：

1. 使用命令行参数：

```bash
# 在 Xcode 中，编辑 Scheme，添加 --test-migrations 参数
```

2. 直接调用测试方法：

```swift
// 在开发环境中运行测试
#if DEBUG
Task {
    await DatabaseMigrationTester.runAllTests()
}
#endif
```

### 测试用例

- `testVehiclePositionMigration`：测试 vehiclePosition 字段迁移
- `testRouteOptimizedFlagMigration`：测试 Route 的 isOptimized 字段迁移
- `testAddNewField`：测试添加新字段并迁移

## 最佳实践

### 1. 版本控制

- 每次修改数据模型时，增加版本号
- 使用语义化版本号（major.minor.patch）
- 在应用中使用 `AppStorage` 跟踪当前数据库版本

### 2. 数据迁移

- 创建通用的迁移辅助方法，减少重复代码
- 在迁移过程中记录详细日志，便于调试
- 使用事务确保迁移的原子性

### 3. 字段处理

- 为新字段提供合理的默认值
- 使用可选类型（Optional）处理可能缺失的数据
- 使用计算属性提供向后兼容性

### 4. 测试与验证

- 创建专门的测试环境验证迁移逻辑
- 使用模拟数据测试各种迁移场景
- 在开发环境中添加迁移测试命令行参数

### 5. 错误处理

- 实现健壮的错误处理机制
- 提供回退策略，确保应用在迁移失败时仍能运行
- 记录详细的错误信息，便于诊断问题

## 添加新字段的完整流程

1. 在模型中添加新字段：

```swift
@Model
final class DeliveryPoint: Identifiable {
    // 现有字段...
    
    // 新增字段
    var estimatedArrivalTime: Date?
    
    // 更新初始化方法
    init(sort_number: Int, address: String, latitude: Double, longitude: Double) {
        // 现有初始化...
        
        // 不需要初始化可选字段
        self.estimatedArrivalTime = nil
    }
}
```

2. 创建新版本的 Schema：

```swift
enum NaviBatchSchemaV3: VersionedSchema {
    static var models: [any PersistentModel.Type] {
        [DeliveryPoint.self, DeliveryGroup.self, Route.self, SavedAddress.self]
    }
    
    static var versionIdentifier: Schema.Version {
        Schema.Version(3, 0, 0)
    }
}
```

3. 更新迁移计划：

```swift
enum NaviBatchMigrationPlan: SchemaMigrationPlan {
    static var schemas: [any VersionedSchema.Type] {
        [NaviBatchSchemaV1.self, NaviBatchSchemaV2.self, NaviBatchSchemaV3.self]
    }
    
    static var stages: [MigrationStage] {
        [migrateV1toV2, migrateV2toV3]
    }
    
    // 现有迁移...
    
    static let migrateV2toV3 = MigrationStage.custom(
        fromVersion: NaviBatchSchemaV2.self,
        toVersion: NaviBatchSchemaV3.self,
        willMigrate: { context in
            logInfo("开始从 Schema V2 迁移到 V3")
        },
        didMigrate: { context in
            logInfo("从 Schema V2 到 V3 的迁移已完成")
            
            Task { @MainActor in
                await initializeEstimatedArrivalTimes(in: context)
            }
        }
    )
}
```

4. 更新应用版本检查：

```swift
@main
struct NaviBatchApp: App {
    @AppStorage("currentSchemaVersion") private var currentSchemaVersion: String = "2.0.0"
    private let latestSchemaVersion: String = "3.0.0"
    
    // 其余代码与前面相同...
}
```
