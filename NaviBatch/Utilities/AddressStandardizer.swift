import Foundation
import os.log

/// 地址标准化工具
/// 负责将地址中的缩写转换为完整形式，确保数据库中存储的地址格式一致
class AddressStandardizer {

    // 私有初始化，防止实例化
    private init() {}

    /// 标准化地址（将缩写转换为完整形式并统一格式）- 支持多国家
    /// - Parameter address: 原始地址
    /// - Returns: 标准化后的地址
    static func standardizeAddress(_ address: String) -> String {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        // 🎯 首先清理重复信息
        let cleanedAddress = cleanDuplicateInformation(address)

        let standardized = standardizeForCountry(cleanedAddress, countryInfo: countryInfo)

        // 统一地址格式
        return normalizeAddressFormat(standardized, countryInfo: countryInfo)
    }

    /// 🎯 清理地址中的重复信息
    /// - Parameter address: 原始地址
    /// - Returns: 清理后的地址
    static func cleanDuplicateInformation(_ address: String) -> String {
        var cleaned = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 1. 处理重复的城市名（如"SAN MATEO 1715 YORK AVE, SAN MATEO"）
        cleaned = cleanDuplicateCityNames(cleaned)

        // 2. 处理重复的街道名
        cleaned = cleanDuplicateStreetNames(cleaned)

        // 3. 处理重复的单位号
        cleaned = cleanDuplicateUnitNumbers(cleaned)

        // 4. 处理重复的国家信息
        cleaned = cleanDuplicateCountryInfo(cleaned)

        // 5. 清理多余的逗号和空格
        cleaned = cleanExtraCommasAndSpaces(cleaned)

        return cleaned
    }

    // MARK: - 🎯 重复信息清理方法

    /// 清理重复的城市名
    private static func cleanDuplicateCityNames(_ address: String) -> String {
        var cleaned = address

        // 处理"SAN MATEO 1715 YORK AVE, SAN MATEO"这种模式
        let cityPattern = #"^([A-Z\s]+)\s+(\d+.*),\s*\1$"#
        if let regex = try? NSRegularExpression(pattern: cityPattern, options: []) {
            let range = NSRange(location: 0, length: cleaned.count)
            if let match = regex.firstMatch(in: cleaned, options: [], range: range) {
                let cityRange = Range(match.range(at: 1), in: cleaned)!
                let addressRange = Range(match.range(at: 2), in: cleaned)!
                let city = String(cleaned[cityRange])
                let addressPart = String(cleaned[addressRange])
                cleaned = "\(addressPart), \(city)"
            }
        }

        return cleaned
    }

    /// 清理重复的街道名
    private static func cleanDuplicateStreetNames(_ address: String) -> String {
        let cleaned = address

        // 处理重复的街道名模式
        let components = cleaned.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
        var uniqueComponents: [String] = []
        var seenStreets: Set<String> = []

        for component in components {
            let normalizedComponent = component.lowercased()

            // 检查是否为街道名（包含Street, Road, Avenue等）
            let streetKeywords = ["street", "road", "avenue", "lane", "drive", "court", "place", "way"]
            let isStreet = streetKeywords.contains { normalizedComponent.contains($0) }

            if isStreet {
                if !seenStreets.contains(normalizedComponent) {
                    seenStreets.insert(normalizedComponent)
                    uniqueComponents.append(component)
                }
            } else {
                uniqueComponents.append(component)
            }
        }

        return uniqueComponents.joined(separator: ", ")
    }

    /// 清理重复的单位号
    private static func cleanDuplicateUnitNumbers(_ address: String) -> String {
        var cleaned = address

        // 处理重复的单位号模式（如"Unit 5, Unit 5"）
        let unitPattern = #"(Unit\s+\d+[A-Z]?),\s*\1"#
        if let regex = try? NSRegularExpression(pattern: unitPattern, options: .caseInsensitive) {
            cleaned = regex.stringByReplacingMatches(in: cleaned, options: [], range: NSRange(location: 0, length: cleaned.count), withTemplate: "$1")
        }

        // 处理重复的Apt模式
        let aptPattern = #"(Apt\s+\d+[A-Z]?),\s*\1"#
        if let regex = try? NSRegularExpression(pattern: aptPattern, options: .caseInsensitive) {
            cleaned = regex.stringByReplacingMatches(in: cleaned, options: [], range: NSRange(location: 0, length: cleaned.count), withTemplate: "$1")
        }

        return cleaned
    }

    /// 清理重复的国家信息
    private static func cleanDuplicateCountryInfo(_ address: String) -> String {
        var cleaned = address

        // 处理重复的国家名
        let countries = ["Australia", "USA", "United States", "Hong Kong", "China", "Canada"]

        for country in countries {
            let pattern = "\\b\(country)\\b.*?\\b\(country)\\b"
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
                let range = NSRange(location: 0, length: cleaned.count)
                if regex.firstMatch(in: cleaned, options: [], range: range) != nil {
                    // 只保留最后一个国家名
                    cleaned = regex.stringByReplacingMatches(in: cleaned, options: [], range: range, withTemplate: country)
                }
            }
        }

        return cleaned
    }

    /// 清理多余的逗号和空格
    private static func cleanExtraCommasAndSpaces(_ address: String) -> String {
        var cleaned = address

        // 移除多余的逗号
        cleaned = cleaned.replacingOccurrences(of: ",+", with: ",", options: .regularExpression)

        // 移除开头和结尾的逗号
        cleaned = cleaned.trimmingCharacters(in: CharacterSet(charactersIn: ", "))

        // 移除多余的空格
        cleaned = cleaned.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 标准化逗号后的空格
        cleaned = cleaned.replacingOccurrences(of: ",\\s*", with: ", ", options: .regularExpression)

        return cleaned
    }

    /// 翻译地址为英文（用于搜索和验证）
    /// - Parameter address: 原始地址
    /// - Returns: 翻译为英文的地址
    static func translateAddressToEnglish(_ address: String) -> String {
        // 检测地址国家
        let countryInfo = AddressCountryDetector.detectCountry(from: address)

        // 根据国家进行相应的翻译
        switch countryInfo?.code {
        case "HK":
            return translateHongKongAddressToEnglish(address)
        case "CN":
            return translateChineseAddressToEnglish(address)
        case "TW":
            return translateTaiwanAddressToEnglish(address)
        default:
            // 对于其他国家，检查是否包含中文字符
            if containsChineseCharacters(address) {
                return translateChineseAddressToEnglish(address)
            }
            return address // 已经是英文或其他语言，不需要翻译
        }
    }

    /// 根据国家标准化地址
    /// - Parameters:
    ///   - address: 原始地址
    ///   - countryInfo: 国家信息
    /// - Returns: 标准化后的地址
    static func standardizeForCountry(_ address: String, countryInfo: AddressCountryDetector.CountryInfo?) -> String {
        guard let country = countryInfo else {
            // 无法识别国家，使用通用标准化
            return standardizeGenericAddress(address)
        }

        switch country.code {
        case "AU":
            return standardizeAustralianAddress(address)
        case "US":
            return standardizeUSAddress(address)
        case "CA":
            return standardizeCanadianAddress(address)
        case "GB":
            return standardizeUKAddress(address)
        case "HK":
            return standardizeHongKongAddress(address)
        default:
            return standardizeGenericAddress(address)
        }
    }

    /// 标准化澳大利亚地址
    private static func standardizeAustralianAddress(_ address: String) -> String {
        var standardized = address

        // 澳大利亚街道类型缩写映射
        let ausStreetTypeMap = [
            "\\bRd\\b": "Road",
            "\\bSt\\b": "Street",
            "\\bAve\\b": "Avenue",
            "\\bDr\\b": "Drive",
            "\\bCt\\b": "Court",
            "\\bPl\\b": "Place",
            "\\bLn\\b": "Lane",
            "\\bLa\\b": "Lane",
            "\\bCl\\b": "Close",
            "\\bCres\\b": "Crescent",
            "\\bCr\\b": "Crescent",
            "\\bGr\\b": "Grove",
            "\\bPde\\b": "Parade",
            "\\bTce\\b": "Terrace",
            "\\bHwy\\b": "Highway",
            "\\bMt\\b": "Mount",
            "\\bBlvd\\b": "Boulevard",
            "\\bPkwy\\b": "Parkway",
            "\\bCir\\b": "Circle",
            "\\bSq\\b": "Square",
            "\\bEspl\\b": "Esplanade"
        ]

        // 应用澳大利亚标准化映射
        for (pattern, fullForm) in ausStreetTypeMap {
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: fullForm,
                options: .regularExpression
            )
        }

        // 记录标准化过程（仅在有变化时）
        if standardized != address {
            Logger.info("🇦🇺 澳大利亚地址标准化: '\(address)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 标准化美国地址 - 使用USPS标准缩写
    private static func standardizeUSAddress(_ address: String) -> String {
        var standardized = address

        // 🏛️ 美国USPS标准：将完整形式转换为标准缩写
        let usStreetTypeMap = [
            "\\bStreet\\b": "St",
            "\\bAvenue\\b": "Ave",
            "\\bBoulevard\\b": "Blvd",
            "\\bDrive\\b": "Dr",
            "\\bRoad\\b": "Rd",
            "\\bLane\\b": "Ln",
            "\\bCourt\\b": "Ct",
            "\\bPlace\\b": "Pl",
            "\\bParkway\\b": "Pkwy",
            "\\bHighway\\b": "Hwy",
            "\\bCircle\\b": "Cir",
            "\\bSquare\\b": "Sq"
        ]

        for (pattern, abbreviation) in usStreetTypeMap {
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: .regularExpression
            )
        }

        if standardized != address {
            Logger.info("🇺🇸 美国地址USPS标准化: '\(address)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 标准化加拿大地址
    private static func standardizeCanadianAddress(_ address: String) -> String {
        var standardized = address

        // 加拿大街道类型缩写映射（类似美国但有些差异）
        let caStreetTypeMap = [
            "\\bSt\\b": "Street",
            "\\bAve\\b": "Avenue",
            "\\bBlvd\\b": "Boulevard",
            "\\bDr\\b": "Drive",
            "\\bRd\\b": "Road",
            "\\bLn\\b": "Lane",
            "\\bCt\\b": "Court",
            "\\bPl\\b": "Place",
            "\\bCres\\b": "Crescent",
            "\\bTrl\\b": "Trail"
        ]

        for (pattern, fullForm) in caStreetTypeMap {
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: fullForm,
                options: .regularExpression
            )
        }

        if standardized != address {
            Logger.info("🇨🇦 加拿大地址标准化: '\(address)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 标准化英国地址
    private static func standardizeUKAddress(_ address: String) -> String {
        var standardized = address

        // 英国街道类型缩写映射
        let ukStreetTypeMap = [
            "\\bSt\\b": "Street",
            "\\bRd\\b": "Road",
            "\\bAve\\b": "Avenue",
            "\\bLn\\b": "Lane",
            "\\bCl\\b": "Close",
            "\\bDr\\b": "Drive",
            "\\bCres\\b": "Crescent",
            "\\bGdns\\b": "Gardens",
            "\\bPl\\b": "Place",
            "\\bSq\\b": "Square",
            "\\bTce\\b": "Terrace"
        ]

        for (pattern, fullForm) in ukStreetTypeMap {
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: fullForm,
                options: .regularExpression
            )
        }

        if standardized != address {
            Logger.info("🇬🇧 英国地址标准化: '\(address)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 标准化香港地址
    private static func standardizeHongKongAddress(_ address: String) -> String {
        var standardized = address

        // 香港地址通常不需要太多标准化，主要是格式整理
        // 标准化楼层表示
        standardized = standardized.replacingOccurrences(of: "/F", with: "楼", options: .caseInsensitive)
        standardized = standardized.replacingOccurrences(of: "Floor", with: "楼", options: .caseInsensitive)

        if standardized != address {
            Logger.info("🇭🇰 香港地址标准化: '\(address)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 通用地址标准化 - 优先使用USPS标准缩写
    private static func standardizeGenericAddress(_ address: String) -> String {
        var standardized = address

        // 🌍 通用标准化：优先使用USPS标准缩写（适用于大多数英语国家）
        let genericStreetTypeMap = [
            "\\bStreet\\b": "St",
            "\\bAvenue\\b": "Ave",
            "\\bRoad\\b": "Rd",
            "\\bDrive\\b": "Dr",
            "\\bLane\\b": "Ln",
            "\\bBoulevard\\b": "Blvd"
        ]

        for (pattern, abbreviation) in genericStreetTypeMap {
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: .regularExpression
            )
        }

        if standardized != address {
            Logger.info("🌍 通用地址标准化: '\(address)' -> '\(standardized)'", type: .location)
        }

        return standardized
    }

    /// 统一地址格式
    /// - Parameters:
    ///   - address: 标准化后的地址
    ///   - countryInfo: 国家信息
    /// - Returns: 格式统一的地址
    private static func normalizeAddressFormat(_ address: String, countryInfo: AddressCountryDetector.CountryInfo?) -> String {
        guard let country = countryInfo else {
            return normalizeGenericAddressFormat(address)
        }

        switch country.code {
        case "US":
            return normalizeUSAddressFormat(address)
        case "CA":
            return normalizeCanadianAddressFormat(address)
        case "AU":
            return normalizeAustralianAddressFormat(address)
        default:
            return normalizeGenericAddressFormat(address)
        }
    }

    /// 统一美国地址格式
    /// 标准格式：[门牌号] [街道名], [城市], [州] [邮编], [国家]
    private static func normalizeUSAddressFormat(_ address: String) -> String {
        var normalized = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除多余的逗号和空格
        normalized = normalized.replacingOccurrences(of: ",\\s*,", with: ",", options: .regularExpression)
        normalized = normalized.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 分割地址组件
        let components = normalized.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        if components.count >= 3 {
            var formattedComponents: [String] = []

            // 第一部分：街道地址（处理门牌号后的逗号问题）
            var streetAddress = components[0]
            // 移除门牌号后的逗号：如 "272, Cedar Street" -> "272 Cedar Street"
            streetAddress = streetAddress.replacingOccurrences(of: "^(\\d+),\\s*", with: "$1 ", options: .regularExpression)
            formattedComponents.append(streetAddress)

            // 第二部分：城市
            formattedComponents.append(components[1])

            // 第三部分及以后：州、邮编、国家
            for i in 2..<components.count {
                let component = components[i]
                if !component.isEmpty {
                    formattedComponents.append(component)
                }
            }

            let result = formattedComponents.joined(separator: ", ")

            if result != address {
                Logger.info("🇺🇸 美国地址格式统一: '\(address)' -> '\(result)'", type: .location)
            }

            return result
        }

        return normalized
    }

    /// 统一加拿大地址格式
    private static func normalizeCanadianAddressFormat(_ address: String) -> String {
        // 加拿大格式类似美国，使用相同的处理逻辑
        return normalizeUSAddressFormat(address)
    }

    /// 统一澳大利亚地址格式
    private static func normalizeAustralianAddressFormat(_ address: String) -> String {
        var normalized = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除多余的逗号和空格
        normalized = normalized.replacingOccurrences(of: ",\\s*,", with: ",", options: .regularExpression)
        normalized = normalized.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 处理门牌号后的逗号问题
        normalized = normalized.replacingOccurrences(of: "^(\\d+),\\s*", with: "$1 ", options: .regularExpression)

        if normalized != address {
            Logger.info("🇦🇺 澳大利亚地址格式统一: '\(address)' -> '\(normalized)'", type: .location)
        }

        return normalized
    }

    /// 统一通用地址格式
    private static func normalizeGenericAddressFormat(_ address: String) -> String {
        var normalized = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除多余的逗号和空格
        normalized = normalized.replacingOccurrences(of: ",\\s*,", with: ",", options: .regularExpression)
        normalized = normalized.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 处理门牌号后的逗号问题
        normalized = normalized.replacingOccurrences(of: "^(\\d+),\\s*", with: "$1 ", options: .regularExpression)

        if normalized != address {
            Logger.info("🌍 通用地址格式统一: '\(address)' -> '\(normalized)'", type: .location)
        }

        return normalized
    }

    /// 检查地址是否需要标准化
    /// - Parameter address: 要检查的地址
    /// - Returns: 如果地址包含缩写则返回true
    static func needsStandardization(_ address: String) -> Bool {
        // 🎯 使用与标准化方法相同的正则表达式模式
        let abbreviationPatterns = [
            "\\bRd\\b", "\\bSt\\b", "\\bAve\\b", "\\bDr\\b", "\\bCt\\b",
            "\\bPl\\b", "\\bLn\\b", "\\bLa\\b", "\\bCl\\b", "\\bCres\\b",
            "\\bCr\\b", "\\bGr\\b", "\\bPde\\b", "\\bTce\\b", "\\bHwy\\b",
            "\\bMt\\b", "\\bBlvd\\b", "\\bPkwy\\b", "\\bCir\\b", "\\bSq\\b",
            "\\bEspl\\b"
        ]

        // 检查是否包含任何缩写模式
        for pattern in abbreviationPatterns {
            if address.range(of: pattern, options: .regularExpression) != nil {
                return true
            }
        }

        return false
    }

    /// 批量标准化地址
    /// - Parameter addresses: 地址数组
    /// - Returns: 标准化后的地址数组
    static func standardizeAddresses(_ addresses: [String]) -> [String] {
        return addresses.map { standardizeAddress($0) }
    }

    /// 获取支持的缩写列表
    /// - Returns: 支持的缩写到完整形式的映射
    static func getSupportedAbbreviations() -> [String: String] {
        return [
            "Rd": "Road",
            "St": "Street",
            "Ave": "Avenue",
            "Dr": "Drive",
            "Ct": "Court",
            "Pl": "Place",
            "Ln": "Lane",
            "La": "Lane",
            "Cl": "Close",
            "Cres": "Crescent",
            "Cr": "Crescent",
            "Gr": "Grove",
            "Pde": "Parade",
            "Tce": "Terrace",
            "Hwy": "Highway",
            "Mt": "Mount",
            "Blvd": "Boulevard",
            "Pkwy": "Parkway",
            "Cir": "Circle",
            "Sq": "Square",
            "Espl": "Esplanade"
        ]
    }

    // MARK: - 地址翻译功能

    /// 检查字符串是否包含中文字符
    /// - Parameter text: 要检查的文本
    /// - Returns: 是否包含中文字符
    static func containsChineseCharacters(_ text: String) -> Bool {
        return text.range(of: "\\p{Script=Han}", options: .regularExpression) != nil
    }

    /// 翻译香港地址为英文
    /// - Parameter address: 香港地址
    /// - Returns: 英文翻译
    private static func translateHongKongAddressToEnglish(_ address: String) -> String {
        var translated = address

        // 香港地区名称翻译
        let hkAreaTranslations: [String: String] = [
            // 港岛区域
            "中环": "Central",
            "中環": "Central",
            "金钟": "Admiralty",
            "金鐘": "Admiralty",
            "湾仔": "Wan Chai",
            "灣仔": "Wan Chai",
            "铜锣湾": "Causeway Bay",
            "銅鑼灣": "Causeway Bay",
            "天后": "Tin Hau",
            "炮台山": "Fortress Hill",
            "北角": "North Point",
            "鰂鱼涌": "Quarry Bay",
            "鰂魚涌": "Quarry Bay",
            "太古": "Taikoo",
            "西湾河": "Sai Wan Ho",
            "西灣河": "Sai Wan Ho",
            "筲箕湾": "Shau Kei Wan",
            "筲箕灣": "Shau Kei Wan",

            // 九龙区域
            "尖沙咀": "Tsim Sha Tsui",
            "佐敦": "Jordan",
            "油麻地": "Yau Ma Tei",
            "旺角": "Mong Kok",
            "太子": "Prince Edward",
            "深水埗": "Sham Shui Po",
            "长沙湾": "Cheung Sha Wan",
            "長沙灣": "Cheung Sha Wan",
            "荔枝角": "Lai Chi Kok",
            "美孚": "Mei Foo",
            "九龙塘": "Kowloon Tong",
            "九龍塘": "Kowloon Tong",
            "红磡": "Hung Hom",
            "紅磡": "Hung Hom",
            "黄大仙": "Wong Tai Sin",
            "黃大仙": "Wong Tai Sin",
            "观塘": "Kwun Tong",
            "觀塘": "Kwun Tong",

            // 新界区域
            "荃湾": "Tsuen Wan",
            "荃灣": "Tsuen Wan",
            "葵涌": "Kwai Chung",
            "青衣": "Tsing Yi",
            "沙田": "Sha Tin",
            "大埔": "Tai Po",
            "粉岭": "Fanling",
            "粉嶺": "Fanling",
            "上水": "Sheung Shui",
            "元朗": "Yuen Long",
            "屯门": "Tuen Mun",
            "屯門": "Tuen Mun",
            "将军澳": "Tseung Kwan O",
            "將軍澳": "Tseung Kwan O",

            // 香港特定街道名称
            "海盛路": "Hoi Shing Road",
            "杨屋道": "Yeung Uk Road",
            "楊屋道": "Yeung Uk Road",
            "荃新天地": "Citywalk",
            "青山公路荃湾段": "Castle Peak Road Tsuen Wan Section",
            "青山公路荃灣段": "Castle Peak Road Tsuen Wan Section",
            "青山公路": "Castle Peak Road",
            "弥敦道": "Nathan Road",
            "彌敦道": "Nathan Road",
            "轩尼诗道": "Hennessy Road",
            "軒尼詩道": "Hennessy Road",
            "德辅道": "Des Voeux Road",
            "德輔道": "Des Voeux Road",
            "皇后大道": "Queen's Road",
            "告士打道": "Gloucester Road",
            "夏悫道": "Harcourt Road",
            "夏慤道": "Harcourt Road"
        ]

        // 道路类型翻译
        let roadTypeTranslations: [String: String] = [
            "道": " Road",
            "街": " Street",
            "路": " Road",
            "里": " Lane",
            "坊": " Square",
            "徑": " Path",
            "径": " Path",
            "巷": " Lane",
            "大道": " Avenue"
        ]

        // 建筑物相关翻译
        let buildingTranslations: [String: String] = [
            "楼": " Floor",
            "樓": " Floor",
            "室": " Room",
            "号": " No.",
            "號": " No.",
            "座": " Block",
            "栋": " Block",
            "棟": " Block",
            "邨": " Estate",
            "苑": " Court",
            "园": " Garden",
            "園": " Garden",
            "中心": " Centre",
            "广场": " Plaza",
            "廣場": " Plaza",
            "大厦": " Building",
            "大廈": " Building"
        ]

        // 应用翻译 - 改进顺序和空格处理
        // 1. 首先翻译具体的街道名称（避免被通用词汇覆盖）
        let sortedAreaTranslations = hkAreaTranslations.sorted { $0.key.count > $1.key.count }
        for (chinese, english) in sortedAreaTranslations {
            if translated.contains(chinese) {
                // 对于街道名称，不自动添加空格，保持原有格式
                if chinese.contains("公路") || chinese.contains("道") || chinese.contains("路") || chinese.contains("街") {
                    translated = translated.replacingOccurrences(of: chinese, with: english)
                } else {
                    // 对于地区名，添加空格分隔
                    translated = translated.replacingOccurrences(of: chinese, with: english + " ")
                }
            }
        }

        // 2. 然后翻译道路类型（只翻译未被具体街道名覆盖的部分）
        for (chinese, english) in roadTypeTranslations {
            translated = translated.replacingOccurrences(of: chinese, with: english)
        }

        // 3. 最后翻译建筑物相关词汇
        for (chinese, english) in buildingTranslations {
            translated = translated.replacingOccurrences(of: chinese, with: english)
        }

        // 清理多余空格 - 改进处理
        translated = translated.replacingOccurrences(of: "  +", with: " ", options: .regularExpression)
        translated = translated.replacingOccurrences(of: " +", with: " ", options: .regularExpression)
        translated = translated.trimmingCharacters(in: .whitespacesAndNewlines)

        // 确保数字和单位之间有空格
        translated = translated.replacingOccurrences(of: "(\\d+)([A-Za-z])", with: "$1 $2", options: .regularExpression)

        // 记录翻译过程
        if translated != address {
            Logger.info("🇭🇰 香港地址翻译: '\(address)' -> '\(translated)'", type: .location)
        }

        return translated
    }

    /// 翻译中国大陆地址为英文
    /// - Parameter address: 中国大陆地址
    /// - Returns: 英文翻译
    private static func translateChineseAddressToEnglish(_ address: String) -> String {
        var translated = address

        // 通用中文地址翻译
        let chineseTranslations: [String: String] = [
            // 道路类型
            "道": " Road",
            "街": " Street",
            "路": " Road",
            "里": " Lane",
            "坊": " Square",
            "徑": " Path",
            "径": " Path",
            "巷": " Lane",
            "大道": " Avenue",
            "公路": " Highway",

            // 建筑物
            "楼": " Floor",
            "樓": " Floor",
            "室": " Room",
            "号": " No.",
            "號": " No.",
            "座": " Block",
            "栋": " Block",
            "棟": " Block",
            "单元": " Unit",
            "單元": " Unit",
            "期": " Phase",
            "区": " District",
            "區": " District",
            "小区": " Community",
            "小區": " Community",
            "花园": " Garden",
            "花園": " Garden",
            "广场": " Plaza",
            "廣場": " Plaza",
            "中心": " Centre",
            "大厦": " Building",
            "大廈": " Building",
            "商场": " Mall",
            "商場": " Mall"
        ]

        // 应用翻译
        for (chinese, english) in chineseTranslations {
            translated = translated.replacingOccurrences(of: chinese, with: english)
        }

        // 清理多余空格
        translated = translated.replacingOccurrences(of: "  ", with: " ")
        translated = translated.trimmingCharacters(in: .whitespacesAndNewlines)

        // 记录翻译过程
        if translated != address {
            Logger.info("🇨🇳 中文地址翻译: '\(address)' -> '\(translated)'", type: .location)
        }

        return translated
    }

    /// 翻译台湾地址为英文
    /// - Parameter address: 台湾地址
    /// - Returns: 英文翻译
    private static func translateTaiwanAddressToEnglish(_ address: String) -> String {
        var translated = address

        // 台湾地址翻译（与中文类似但有些差异）
        let taiwanTranslations: [String: String] = [
            // 道路类型
            "路": " Road",
            "街": " Street",
            "巷": " Lane",
            "弄": " Alley",
            "段": " Section",
            "號": " No.",
            "号": " No.",
            "樓": " Floor",
            "楼": " Floor",
            "室": " Room",

            // 台湾特有
            "鄉": " Township",
            "鎮": " Town",
            "镇": " Town",
            "市": " City",
            "縣": " County",
            "县": " County",
            "區": " District",
            "区": " District"
        ]

        // 应用翻译
        for (chinese, english) in taiwanTranslations {
            translated = translated.replacingOccurrences(of: chinese, with: english)
        }

        // 清理多余空格
        translated = translated.replacingOccurrences(of: "  ", with: " ")
        translated = translated.trimmingCharacters(in: .whitespacesAndNewlines)

        // 记录翻译过程
        if translated != address {
            Logger.info("🇹🇼 台湾地址翻译: '\(address)' -> '\(translated)'", type: .location)
        }

        return translated
    }
}

// MARK: - 扩展：为常用类型添加便利方法
extension String {
    /// 标准化当前字符串中的地址缩写
    /// - Returns: 标准化后的地址字符串
    func standardizedAddress() -> String {
        return AddressStandardizer.standardizeAddress(self)
    }

    /// 检查当前字符串是否包含地址缩写
    /// - Returns: 如果包含缩写则返回true
    func needsAddressStandardization() -> Bool {
        return AddressStandardizer.needsStandardization(self)
    }
}



// MARK: - 智能地理编码扩展
extension AddressStandardizer {

    /// 智能地理编码预处理
    /// 在地理编码之前预处理地址，扩展常见简称以提高识别率
    /// - Parameter address: 原始地址
    /// - Returns: 预处理后的地址
    static func preprocessForGeocoding(_ address: String) -> String {
        var processed = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 智能简称扩展映射（专门用于地理编码前的预处理）
        let geocodingExpansions = [
            // 常见的地理编码问题简称
            " La ": " Lane ",
            " La,": " Lane,",
            " St ": " Street ",
            " St,": " Street,",
            " Rd ": " Road ",
            " Rd,": " Road,",
            " Ave ": " Avenue ",
            " Ave,": " Avenue,",
            " Dr ": " Drive ",
            " Dr,": " Drive,",
            " Ct ": " Court ",
            " Ct,": " Court,",
            " Pl ": " Place ",
            " Pl,": " Place,",
            " Ln ": " Lane ",
            " Ln,": " Lane,",
            " Cl ": " Close ",
            " Cl,": " Close,",

            // 处理末尾的简称（不带逗号）
            "\\bLa$": "Lane",
            "\\bSt$": "Street",
            "\\bRd$": "Road",
            "\\bAve$": "Avenue",
            "\\bDr$": "Drive",
            "\\bCt$": "Court",
            "\\bPl$": "Place",
            "\\bLn$": "Lane",
            "\\bCl$": "Close",
            "\\bCres$": "Crescent",
            "\\bCr$": "Crescent"
        ]

        // 应用扩展映射
        for (abbreviation, fullForm) in geocodingExpansions {
            if abbreviation.contains("\\b") || abbreviation.hasSuffix("$") {
                // 使用正则表达式处理词边界和末尾匹配
                processed = processed.replacingOccurrences(
                    of: abbreviation,
                    with: fullForm,
                    options: .regularExpression
                )
            } else {
                // 直接字符串替换
                processed = processed.replacingOccurrences(of: abbreviation, with: fullForm)
            }
        }

        // 记录预处理过程（仅在有变化时）
        if processed != address {
            Logger.info("🔍 地理编码预处理: '\(address)' -> '\(processed)'", type: .location)
        }

        return processed
    }

    /// 生成地理编码候选地址列表
    /// 当原始地址地理编码失败时，生成可能的替代地址进行尝试
    /// - Parameter address: 原始地址
    /// - Returns: 候选地址数组，按优先级排序
    static func generateGeocodingCandidates(_ address: String) -> [String] {
        var candidates: [String] = []

        // 1. 原始地址
        candidates.append(address)

        // 2. 🌍 中文地址翻译（核心改进）
        if containsChineseCharacters(address) {
            let translated = translateAddressToEnglish(address)
            if translated != address {
                candidates.append(translated)
                Logger.info("🇨🇳 添加翻译候选地址: '\(address)' -> '\(translated)'", type: .location)

                // 2.1 翻译后再预处理
                let translatedPreprocessed = preprocessForGeocoding(translated)
                if translatedPreprocessed != translated {
                    candidates.append(translatedPreprocessed)
                }

                // 2.2 翻译后再标准化
                let translatedStandardized = standardizeAddress(translated)
                if translatedStandardized != translated && translatedStandardized != translatedPreprocessed {
                    candidates.append(translatedStandardized)
                }
            }
        }

        // 3. 预处理后的地址
        let preprocessed = preprocessForGeocoding(address)
        if preprocessed != address {
            candidates.append(preprocessed)
        }

        // 4. 完全标准化的地址
        let standardized = AddressStandardizer.standardizeAddress(address)
        if standardized != address && standardized != preprocessed {
            candidates.append(standardized)
        }

        // 5. 🎯 智能简化策略（针对中文地址）
        if containsChineseCharacters(address) {
            // 5.1 只保留地区名的版本
            let areaOnly = extractAreaOnly(address)
            if !areaOnly.isEmpty && areaOnly != address {
                candidates.append(areaOnly)

                // 翻译地区名
                let translatedArea = translateAddressToEnglish(areaOnly)
                if translatedArea != areaOnly {
                    candidates.append(translatedArea)
                }
            }

            // 5.2 移除门牌号的版本
            let withoutNumber = removeHouseNumber(address)
            if withoutNumber != address {
                candidates.append(withoutNumber)

                // 翻译无门牌号版本
                let translatedWithoutNumber = translateAddressToEnglish(withoutNumber)
                if translatedWithoutNumber != withoutNumber {
                    candidates.append(translatedWithoutNumber)
                }
            }
        }

        // 🍎 保留单元号 - Apple Maps原生支持1/12格式
        // 移除了单元号移除逻辑，保持Apple Maps标准格式

        // 去重并保持顺序
        var uniqueCandidates: [String] = []
        for candidate in candidates {
            let trimmed = candidate.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmed.isEmpty && !uniqueCandidates.contains(trimmed) {
                uniqueCandidates.append(trimmed)
            }
        }

        Logger.info("🎯 生成地理编码候选地址 \(uniqueCandidates.count) 个: \(uniqueCandidates)", type: .location)

        return uniqueCandidates
    }

    /// 移除地址中的单元号
    /// - Parameter address: 包含单元号的地址
    /// - Returns: 移除单元号后的地址，如果没有单元号则返回nil
    private static func removeUnitNumber(_ address: String) -> String? {
        // 匹配常见的单元号格式：数字/数字、数字-数字、Unit 数字等
        let unitPatterns = [
            "^\\d+/\\d+\\s+",  // "123/45 Street Name" -> "45 Street Name"
            "^\\d+-\\d+\\s+",  // "123-45 Street Name" -> "45 Street Name"
            "^Unit\\s+\\d+/\\d+\\s+",  // "Unit 123/45 Street" -> "45 Street"
            "^Apt\\s+\\d+/\\d+\\s+",   // "Apt 123/45 Street" -> "45 Street"
        ]

        for pattern in unitPatterns {
            let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            if let match = regex?.firstMatch(in: address, options: [], range: NSRange(location: 0, length: address.count)) {
                let matchedString = String(address[Range(match.range, in: address)!])

                // 对于 "123/45 Street" 格式，提取 "45 Street"
                if matchedString.contains("/") {
                    let components = matchedString.components(separatedBy: "/")
                    if components.count >= 2 {
                        let mainAddress = components[1].trimmingCharacters(in: .whitespaces)
                        let remainingAddress = String(address.dropFirst(match.range.length))
                        return mainAddress + " " + remainingAddress
                    }
                }

                // 其他格式直接移除匹配的部分
                return String(address.dropFirst(match.range.length)).trimmingCharacters(in: .whitespaces)
            }
        }

        return nil
    }

    /// 提取地址中的地区名称（用于简化搜索）
    /// - Parameter address: 完整地址
    /// - Returns: 只包含地区名的地址
    private static func extractAreaOnly(_ address: String) -> String {
        // 香港地区名称列表
        let hkAreas = [
            "中环", "中環", "金钟", "金鐘", "湾仔", "灣仔", "铜锣湾", "銅鑼灣",
            "天后", "炮台山", "北角", "鰂鱼涌", "鰂魚涌", "太古", "西湾河", "西灣河",
            "筲箕湾", "筲箕灣", "尖沙咀", "佐敦", "油麻地", "旺角", "太子", "深水埗",
            "长沙湾", "長沙灣", "荔枝角", "美孚", "九龙塘", "九龍塘", "红磡", "紅磡",
            "黄大仙", "黃大仙", "观塘", "觀塘", "荃湾", "荃灣", "葵涌", "青衣",
            "沙田", "大埔", "粉岭", "粉嶺", "上水", "元朗", "屯门", "屯門",
            "将军澳", "將軍澳"
        ]

        // 查找地址中包含的地区名
        for area in hkAreas {
            if address.contains(area) {
                return area
            }
        }

        return ""
    }

    /// 移除地址中的门牌号
    /// - Parameter address: 包含门牌号的地址
    /// - Returns: 移除门牌号后的地址
    private static func removeHouseNumber(_ address: String) -> String {
        // 匹配开头的数字（门牌号）
        let pattern = "^\\d+\\s*"
        let regex = try? NSRegularExpression(pattern: pattern, options: [])

        if let match = regex?.firstMatch(in: address, options: [], range: NSRange(location: 0, length: address.count)) {
            return String(address.dropFirst(match.range.length)).trimmingCharacters(in: .whitespaces)
        }

        return address
    }

    /// 测试智能简称扩展功能
    /// 用于验证各种地址格式的处理效果
    static func testSmartExpansion() {
        let testAddresses = [
            "123 Smith La, Melbourne VIC 3000",
            "45 Main St, Sydney NSW 2000",
            "2/21 Park Rd, Brisbane QLD 4000",
            "Unit 5/67 Queen Ave, Perth WA 6000",
            "789 Collins Ct, Adelaide SA 5000",
            "12 Ocean Pl, Darwin NT 0800"
        ]

        Logger.info("🧪 开始测试智能简称扩展功能", type: .location)

        for address in testAddresses {
            Logger.info("📍 测试地址: '\(address)'", type: .location)
            let candidates = generateGeocodingCandidates(address)
            Logger.info("🎯 生成候选地址: \(candidates)", type: .location)
            Logger.info("---", type: .location)
        }

        Logger.info("✅ 智能简称扩展功能测试完成", type: .location)
    }

    /// 快速测试单个地址的智能扩展
    /// - Parameter address: 要测试的地址
    /// - Returns: 候选地址数组
    static func quickTest(_ address: String) -> [String] {
        print("🧪 测试地址: '\(address)'")
        let candidates = generateGeocodingCandidates(address)
        print("🎯 生成 \(candidates.count) 个候选地址:")
        for (index, candidate) in candidates.enumerated() {
            print("   \(index + 1). \(candidate)")
        }
        return candidates
    }

    /// 测试地址标准化修复
    /// 验证重复替换问题是否已解决
    static func testStandardizationFix() {
        let problematicAddresses = [
            "21 St",
            "31 Mimosa St",
            "63 Doon Ave",
            "21 Streetirling Cres",
            "45 Main Rd"
        ]

        print("🧪 测试地址标准化修复:")
        for address in problematicAddresses {
            let standardized = standardizeAddress(address)
            let hasIssue = standardized.contains("Streetreet") ||
                          standardized.contains("Roadoad") ||
                          standardized.contains("Avenuenue") ||
                          standardized.contains("Crescentcent")

            print("📍 '\(address)' -> '\(standardized)' \(hasIssue ? "❌ 有问题" : "✅ 正常")")
        }
    }

    /// 测试中文地址候选生成功能
    /// 验证整体改进是否有效
    static func testChineseAddressCandidates() {
        let chineseAddresses = [
            "荃湾海盛路3号",
            "中环德辅道中1号",
            "旺角弥敦道123号",
            "沙田新城市广场",
            "铜锣湾时代广场"
        ]

        print("🧪 测试中文地址候选生成:")
        for address in chineseAddresses {
            print("\n📍 测试地址: '\(address)'")
            let candidates = generateGeocodingCandidates(address)
            print("🎯 生成 \(candidates.count) 个候选地址:")
            for (index, candidate) in candidates.enumerated() {
                print("   \(index + 1). \(candidate)")
            }
        }
        print("\n✅ 中文地址候选生成测试完成")
    }
}