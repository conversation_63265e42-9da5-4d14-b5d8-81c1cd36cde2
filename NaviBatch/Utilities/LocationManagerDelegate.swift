import Foundation
import CoreLocation
import os.log

class LocationManagerDelegate: NSObject, CLLocationManagerDelegate {
    private weak var viewModel: RouteViewModel?
    private let logger = os.Logger(subsystem: "com.navibatch.app", category: "LocationManager")
    private var hasReceivedRealLocation = false

    init(viewModel: RouteViewModel) {
        self.viewModel = viewModel
        super.init()
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }

        // 移除强制默认位置检查 - 现在使用iOS模拟器的自定义位置

        // 记录位置信息（仅用于调试）
        let coordinate = location.coordinate
        logLocationInfo(coordinate)

        // 标记已收到位置
        hasReceivedRealLocation = true

        // 更新视图模型中的位置
        DispatchQueue.main.async { [weak self] in
            self?.viewModel?.updateDriverLocation(location.coordinate)
        }
    }

    // 记录位置信息（仅用于调试）
    private func logLocationInfo(_ coordinate: CLLocationCoordinate2D) {
        // 记录位置信息
        logger.info("LocationManagerDelegate - 当前位置坐标: (\(coordinate.latitude), \(coordinate.longitude))")
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        // 检查具体的错误类型
        if let clError = error as? CLError {
            switch clError.code {
            case .locationUnknown:
                logger.warning("位置更新失败: 无法确定当前位置 (kCLErrorLocationUnknown)。这可能是暂时的。 Error: \(error.localizedDescription)")
                // 如果从未收到过真实位置，使用默认位置
                if !hasReceivedRealLocation {
                    useFallbackLocation()
                }
            case .denied:
                logger.error("位置更新失败: 权限被拒绝 (kCLErrorDenied)。 Error: \(error.localizedDescription)")
                useFallbackLocation()
            case .network:
                logger.error("位置更新失败: 网络错误 (kCLErrorNetwork)。请检查网络连接。 Error: \(error.localizedDescription)")
                // 网络错误时，如果从未收到过真实位置，使用默认位置
                if !hasReceivedRealLocation {
                    useFallbackLocation()
                }
            default:
                logger.error("位置更新失败，错误码: \(clError.code.rawValue)。 Error: \(error.localizedDescription)")
                // 其他错误时，如果从未收到过真实位置，使用默认位置
                if !hasReceivedRealLocation {
                    useFallbackLocation()
                }
            }
        } else {
            logger.error("位置更新失败: \(error.localizedDescription)")
            // 未知错误时，如果从未收到过真实位置，使用默认位置
            if !hasReceivedRealLocation {
                useFallbackLocation()
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            manager.startUpdatingLocation()
            logger.info("位置权限已授权")
        case .denied, .restricted:
            logger.warning("位置权限被拒绝或受限")
            // 权限被拒绝时使用默认位置
            useFallbackLocation()
        case .notDetermined:
            logger.debug("位置权限未确定")
        @unknown default:
            logger.warning("未知的位置权限状态")
            // 未知状态时使用默认位置
            useFallbackLocation()
        }
    }

    // 使用默认位置
    private func useFallbackLocation() {
        // 先使用LocationManager的默认位置功能
        LocationManager.shared.useDefaultLocation()

        // 然后通知视图模型使用默认位置
        DispatchQueue.main.async { [weak self] in
            self?.viewModel?.useFallbackLocation()
        }

        logger.info("已使用默认位置（Glen Waverley）作为当前位置")
    }
}
