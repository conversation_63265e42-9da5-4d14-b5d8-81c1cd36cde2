//
//  AppleMapsAddressFormatter.swift
//  NaviBatch
//
//  Created by AI Assistant on 2025-06-30.
//  Apple Maps专用地址格式化器 - 优化地址格式以提高Apple Maps识别率
//

import Foundation
import os.log

/// Apple Maps专用地址格式化器
/// 将地址格式化为Apple Maps最佳识别的格式，包括街道名称简化
class AppleMapsAddressFormatter {

    // 私有初始化，防止实例化
    private init() {}

    // MARK: - 主要格式化方法

    /// 格式化地址为Apple Maps兼容格式
    /// - Parameter address: 原始地址
    /// - Returns: Apple Maps优化后的地址
    static func formatForAppleMaps(_ address: String) -> String {
        Logger.info("🍎 开始Apple Maps地址格式化: '\(address)'", type: .location)

        // 1. 基本清理
        var formatted = cleanBasicFormat(address)

        // 2. 简化街道名称
        formatted = simplifyStreetNames(formatted)

        // 3. 优化地址组件
        formatted = optimizeAddressComponents(formatted)

        // 4. 最终清理
        formatted = finalCleanup(formatted)

        if formatted != address {
            Logger.info("🍎 Apple Maps地址格式化完成: '\(address)' -> '\(formatted)'", type: .location)
        }

        return formatted
    }

    /// 简化街道名称为Apple Maps兼容的缩写形式
    /// - Parameter streetName: 原始街道名称
    /// - Returns: 简化后的街道名称
    static func simplifyStreetName(_ streetName: String) -> String {
        var simplified = streetName

        // 🏛️ USPS官方标准街道类型缩写映射 (基于Publication 28)
        let streetTypeSimplifications = [
            // 美国USPS标准街道类型 - 完整形式到标准缩写
            ("\\bStreet\\b", "ST"),
            ("\\bAvenue\\b", "AVE"),
            ("\\bRoad\\b", "RD"),
            ("\\bDrive\\b", "DR"),
            ("\\bCourt\\b", "CT"),
            ("\\bPlace\\b", "PL"),
            ("\\bLane\\b", "LN"),
            ("\\bCircle\\b", "CIR"),
            ("\\bBoulevard\\b", "BLVD"),
            ("\\bParkway\\b", "PKWY"),
            ("\\bHighway\\b", "HWY"),
            ("\\bTerrace\\b", "TER"),
            ("\\bSquare\\b", "SQ"),
            ("\\bWay\\b", "WAY"),
            ("\\bAlley\\b", "ALY"),
            ("\\bBridge\\b", "BRG"),
            ("\\bCenter\\b", "CTR"),
            ("\\bCrescent\\b", "CRES"),
            ("\\bEstate\\b", "EST"),
            ("\\bExpressway\\b", "EXPY"),
            ("\\bExtension\\b", "EXT"),
            ("\\bFreeway\\b", "FWY"),
            ("\\bGarden\\b", "GDN"),
            ("\\bGrove\\b", "GRV"),
            ("\\bHeights\\b", "HTS"),
            ("\\bHill\\b", "HL"),
            ("\\bJunction\\b", "JCT"),
            ("\\bManor\\b", "MNR"),
            ("\\bMeadow\\b", "MDW"),
            ("\\bMount\\b", "MT"),
            ("\\bMountain\\b", "MTN"),
            ("\\bPark\\b", "PARK"),
            ("\\bPoint\\b", "PT"),
            ("\\bRidge\\b", "RDG"),
            ("\\bRiver\\b", "RIV"),
            ("\\bSpring\\b", "SPG"),
            ("\\bTrail\\b", "TRL"),
            ("\\bValley\\b", "VLY"),
            ("\\bView\\b", "VW"),
            ("\\bVillage\\b", "VLG"),

            // 澳大利亚常用街道类型 (保持与当地标准一致)
            ("\\bClose\\b", "CL"),
            ("\\bCircuit\\b", "CCT"),
            ("\\bParade\\b", "PDE"),
            ("\\bEsplanade\\b", "ESPL"),

            // 方向缩写 (USPS标准)
            ("\\bNorth\\b", "N"),
            ("\\bSouth\\b", "S"),
            ("\\bEast\\b", "E"),
            ("\\bWest\\b", "W"),
            ("\\bNortheast\\b", "NE"),
            ("\\bNorthwest\\b", "NW"),
            ("\\bSoutheast\\b", "SE"),
            ("\\bSouthwest\\b", "SW")
        ]

        for (pattern, abbreviation) in streetTypeSimplifications {
            simplified = simplified.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        return simplified
    }

    // MARK: - 私有辅助方法

    /// 基本格式清理
    private static func cleanBasicFormat(_ address: String) -> String {
        var cleaned = address

        // 移除多余的空格
        cleaned = cleaned.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 清理逗号周围的空格
        cleaned = cleaned.replacingOccurrences(of: "\\s*,\\s*", with: ", ", options: .regularExpression)

        // 移除开头和结尾的空格
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleaned
    }

    /// 简化街道名称
    private static func simplifyStreetNames(_ address: String) -> String {
        let components = address.components(separatedBy: ",")
        var simplifiedComponents: [String] = []

        for (index, component) in components.enumerated() {
            let trimmedComponent = component.trimmingCharacters(in: .whitespacesAndNewlines)

            // 第一个组件通常是街道地址，需要简化街道名称
            if index == 0 {
                let simplified = simplifyStreetName(trimmedComponent)
                simplifiedComponents.append(simplified)
            } else {
                simplifiedComponents.append(trimmedComponent)
            }
        }

        return simplifiedComponents.joined(separator: ", ")
    }

    /// 优化地址组件
    private static func optimizeAddressComponents(_ address: String) -> String {
        var optimized = address

        // 标准化州名缩写（确保大写）
        let statePatterns = [
            ("\\b(ca|CA)\\b", "CA"),
            ("\\b(ny|NY)\\b", "NY"),
            ("\\b(tx|TX)\\b", "TX"),
            ("\\b(fl|FL)\\b", "FL"),
            ("\\b(vic|VIC)\\b", "VIC"),
            ("\\b(nsw|NSW)\\b", "NSW"),
            ("\\b(qld|QLD)\\b", "QLD"),
            ("\\b(wa|WA)\\b", "WA"),
            ("\\b(sa|SA)\\b", "SA"),
            ("\\b(tas|TAS)\\b", "TAS"),
            ("\\b(act|ACT)\\b", "ACT"),
            ("\\b(nt|NT)\\b", "NT")
        ]

        for (pattern, replacement) in statePatterns {
            optimized = optimized.replacingOccurrences(
                of: pattern,
                with: replacement,
                options: .regularExpression
            )
        }

        // 🎯 移除ZIP码（提高Apple Maps识别率，符合用户要求的格式）
        optimized = optimized.replacingOccurrences(
            of: "\\s*,?\\s*\\b\\d{5}(-\\d{4})?\\b",
            with: "",
            options: .regularExpression
        )

        // 移除冗余的国家标识（Apple Maps通常不需要）
        optimized = optimized.replacingOccurrences(of: ", United States", with: "", options: .caseInsensitive)
        optimized = optimized.replacingOccurrences(of: ", USA", with: "", options: .caseInsensitive)
        optimized = optimized.replacingOccurrences(of: ", US", with: "", options: .caseInsensitive)
        optimized = optimized.replacingOccurrences(of: ", Australia", with: "", options: .caseInsensitive)
        optimized = optimized.replacingOccurrences(of: ", AU", with: "", options: .caseInsensitive)

        return optimized
    }

    /// 最终清理
    private static func finalCleanup(_ address: String) -> String {
        var cleaned = address

        // 移除末尾的逗号
        cleaned = cleaned.replacingOccurrences(of: ",\\s*$", with: "", options: .regularExpression)

        // 移除多余的空格
        cleaned = cleaned.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // 清理连续的逗号
        cleaned = cleaned.replacingOccurrences(of: ",\\s*,", with: ",", options: .regularExpression)

        // 最终修剪
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleaned
    }

    // MARK: - 专用格式化方法

    /// 🏛️ 格式化地址用于数据库存储（使用USPS标准缩写）
    /// - Parameter address: 原始地址
    /// - Returns: 数据库存储优化的地址
    static func formatForDatabaseStorage(_ address: String) -> String {
        Logger.info("🏛️ 开始数据库存储地址格式化: '\(address)'", type: .location)

        // 1. 基本清理
        var formatted = cleanBasicFormat(address)

        // 2. 应用USPS标准街道名称缩写
        formatted = applyUSPSStandardAbbreviations(formatted)

        // 3. 优化地址组件
        formatted = optimizeAddressComponents(formatted)

        // 4. 最终清理
        formatted = finalCleanup(formatted)

        if formatted != address {
            Logger.info("🏛️ 数据库存储地址格式化完成: '\(address)' -> '\(formatted)'", type: .location)
        }

        return formatted
    }

    /// 应用USPS标准缩写（用于数据库存储）
    private static func applyUSPSStandardAbbreviations(_ address: String) -> String {
        let components = address.components(separatedBy: ",")
        var processedComponents: [String] = []

        for (index, component) in components.enumerated() {
            let trimmedComponent = component.trimmingCharacters(in: .whitespacesAndNewlines)

            // 第一个组件通常是街道地址，需要应用USPS标准缩写
            if index == 0 {
                let standardized = applyUSPSStreetAbbreviations(trimmedComponent)
                processedComponents.append(standardized)
            } else {
                processedComponents.append(trimmedComponent)
            }
        }

        // 🎯 SpeedX专用：应用特殊的地址分隔符格式
        let joinedAddress = processedComponents.joined(separator: ", ")
        return applySpeedXAddressSeparatorFormat(joinedAddress)
    }

    /// 🎯 SpeedX专用：应用地址分隔符格式优化
    private static func applySpeedXAddressSeparatorFormat(_ address: String) -> String {
        var formatted = address

        // 美国州简称列表
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]

        // 对于美国州简称，使用无空格格式：City,CA
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            formatted = formatted.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }

        return formatted
    }

    /// 应用USPS街道缩写
    private static func applyUSPSStreetAbbreviations(_ streetAddress: String) -> String {
        var processed = streetAddress

        // 🏛️ USPS标准街道类型缩写（大写形式，用于数据库存储）
        let uspsAbbreviations = [
            ("\\bStreet\\b", "ST"),
            ("\\bAvenue\\b", "AVE"),
            ("\\bRoad\\b", "RD"),
            ("\\bDrive\\b", "DR"),
            ("\\bCourt\\b", "CT"),
            ("\\bPlace\\b", "PL"),
            ("\\bLane\\b", "LN"),
            ("\\bCircle\\b", "CIR"),
            ("\\bBoulevard\\b", "BLVD"),
            ("\\bParkway\\b", "PKWY"),
            ("\\bHighway\\b", "HWY"),
            ("\\bTerrace\\b", "TER"),
            ("\\bSquare\\b", "SQ"),
            ("\\bAlley\\b", "ALY"),
            ("\\bBridge\\b", "BRG"),
            ("\\bCenter\\b", "CTR"),
            ("\\bCrescent\\b", "CRES"),
            ("\\bEstate\\b", "EST"),
            ("\\bExpressway\\b", "EXPY"),
            ("\\bExtension\\b", "EXT"),
            ("\\bFreeway\\b", "FWY"),
            ("\\bGarden\\b", "GDN"),
            ("\\bGrove\\b", "GRV"),
            ("\\bHeights\\b", "HTS"),
            ("\\bHill\\b", "HL"),
            ("\\bJunction\\b", "JCT"),
            ("\\bManor\\b", "MNR"),
            ("\\bMeadow\\b", "MDW"),
            ("\\bMount\\b", "MT"),
            ("\\bMountain\\b", "MTN"),
            ("\\bPark\\b", "PARK"),
            ("\\bPoint\\b", "PT"),
            ("\\bRidge\\b", "RDG"),
            ("\\bRiver\\b", "RIV"),
            ("\\bSpring\\b", "SPG"),
            ("\\bTrail\\b", "TRL"),
            ("\\bValley\\b", "VLY"),
            ("\\bView\\b", "VW"),
            ("\\bVillage\\b", "VLG")
        ]

        for (pattern, abbreviation) in uspsAbbreviations {
            processed = processed.replacingOccurrences(
                of: pattern,
                with: abbreviation,
                options: [.regularExpression, .caseInsensitive]
            )
        }

        return processed
    }

    /// 格式化街道地址部分（门牌号 + 街道名）
    /// - Parameters:
    ///   - streetNumber: 门牌号
    ///   - streetName: 街道名
    /// - Returns: 格式化的街道地址
    static func formatStreetAddress(streetNumber: String?, streetName: String?) -> String? {
        guard let streetName = streetName, !streetName.isEmpty else { return nil }

        let simplifiedStreetName = simplifyStreetName(streetName)

        if let streetNumber = streetNumber, !streetNumber.isEmpty {
            return "\(streetNumber) \(simplifiedStreetName)"
        } else {
            return simplifiedStreetName
        }
    }

    /// 检查地址是否需要Apple Maps格式化
    /// - Parameter address: 地址字符串
    /// - Returns: 如果需要格式化则返回true
    static func needsAppleMapsFormatting(_ address: String) -> Bool {
        // 检查是否包含完整的街道类型名称
        let fullStreetTypes = [
            "Street", "Avenue", "Road", "Drive", "Court", "Place", "Lane",
            "Circle", "Boulevard", "Parkway", "Highway", "Terrace", "Square",
            "Crescent", "Close", "Circuit", "Parade", "Esplanade", "Grove"
        ]

        for streetType in fullStreetTypes {
            if address.contains(streetType) {
                return true
            }
        }

        // 检查是否包含完整的方向名称
        let fullDirections = ["North", "South", "East", "West", "Northeast", "Northwest", "Southeast", "Southwest"]
        for direction in fullDirections {
            if address.contains(direction) {
                return true
            }
        }

        // 检查是否包含冗余的国家标识
        if address.contains("United States") || address.contains("Australia") {
            return true
        }

        return false
    }
}

// MARK: - String扩展
extension String {
    /// 格式化为Apple Maps兼容格式（用于导航和搜索）
    /// - Returns: Apple Maps优化后的地址字符串
    func formattedForAppleMaps() -> String {
        return AppleMapsAddressFormatter.formatForAppleMaps(self)
    }

    /// 格式化为数据库存储格式（使用USPS标准缩写）
    /// - Returns: 数据库存储优化的地址字符串
    func formattedForDatabaseStorage() -> String {
        return AppleMapsAddressFormatter.formatForDatabaseStorage(self)
    }

    /// 检查是否需要Apple Maps格式化
    /// - Returns: 如果需要格式化则返回true
    func needsAppleMapsFormatting() -> Bool {
        return AppleMapsAddressFormatter.needsAppleMapsFormatting(self)
    }
}
