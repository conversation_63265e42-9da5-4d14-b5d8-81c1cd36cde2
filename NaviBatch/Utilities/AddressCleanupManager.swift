import Foundation
import SwiftData
import CoreLocation

/// 🧹 地址清理管理器 - 清理数据库中地址字段的管道符号
class AddressCleanupManager: ObservableObject {
    private let modelContext: ModelContext
    
    @Published var isProcessing = false
    @Published var progress: Double = 0.0
    @Published var statusMessage = ""
    @Published var cleanedCount = 0
    @Published var totalCount = 0
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    /// 🔍 检查需要清理的地址数量
    func checkCleanupNeeded() -> Int {
        let descriptor = FetchDescriptor<DeliveryPoint>()
        
        do {
            let allPoints = try modelContext.fetch(descriptor)
            let needCleanup = allPoints.filter { point in
                // 检查originalAddress是否包含管道符号
                if let originalAddress = point.originalAddress, originalAddress.contains("|") {
                    return true
                }
                // 检查streetName是否包含管道符号
                if let streetName = point.streetName, streetName.contains("|") {
                    return true
                }
                return false
            }
            
            print("🔍 地址清理检查:")
            print("   总地址数: \(allPoints.count)")
            print("   需要清理: \(needCleanup.count)")
            print("   已清理: \(allPoints.count - needCleanup.count)")
            
            return needCleanup.count
        } catch {
            print("❌ 检查清理状态失败: \(error)")
            return 0
        }
    }
    
    /// 🚀 开始批量清理
    func startCleanup() async {
        guard !isProcessing else { return }
        
        await MainActor.run {
            isProcessing = true
            progress = 0.0
            statusMessage = "开始清理..."
            cleanedCount = 0
        }
        
        let descriptor = FetchDescriptor<DeliveryPoint>()
        
        do {
            let allPoints = try modelContext.fetch(descriptor)
            let needCleanup = allPoints.filter { point in
                // 检查originalAddress是否包含管道符号
                if let originalAddress = point.originalAddress, originalAddress.contains("|") {
                    return true
                }
                // 检查streetName是否包含管道符号
                if let streetName = point.streetName, streetName.contains("|") {
                    return true
                }
                return false
            }
            
            await MainActor.run {
                totalCount = needCleanup.count
                statusMessage = "找到 \(totalCount) 个需要清理的地址"
            }
            
            print("🧹 开始清理 \(needCleanup.count) 个地址")
            
            for (index, point) in needCleanup.enumerated() {
                await cleanupDeliveryPoint(point)
                
                await MainActor.run {
                    cleanedCount = index + 1
                    progress = Double(cleanedCount) / Double(totalCount)
                    statusMessage = "已清理 \(cleanedCount)/\(totalCount) 个地址"
                }
                
                // 每处理10个地址保存一次
                if (index + 1) % 10 == 0 {
                    try modelContext.save()
                    print("🧹 已保存进度: \(index + 1)/\(needCleanup.count)")
                }
            }
            
            // 最终保存
            try modelContext.save()
            
            await MainActor.run {
                statusMessage = "清理完成！共清理了 \(cleanedCount) 个地址"
                isProcessing = false
            }
            
            print("✅ 地址清理完成，共清理了 \(cleanedCount) 个地址")
            
        } catch {
            await MainActor.run {
                statusMessage = "清理失败: \(error.localizedDescription)"
                isProcessing = false
            }
            print("❌ 地址清理失败: \(error)")
        }
    }
    
    /// 🧹 清理单个DeliveryPoint的地址字段
    private func cleanupDeliveryPoint(_ point: DeliveryPoint) async {
        var hasChanges = false
        
        // 清理originalAddress
        if let originalAddress = point.originalAddress, originalAddress.contains("|") {
            let cleanedAddress = cleanAddressMetadata(originalAddress)
            if !cleanedAddress.isEmpty && cleanedAddress != originalAddress {
                point.originalAddress = cleanedAddress
                hasChanges = true
                print("🧹 清理originalAddress: '\(originalAddress)' -> '\(cleanedAddress)'")
            }
        }
        
        // 清理streetName
        if let streetName = point.streetName, streetName.contains("|") {
            let cleanedStreetName = cleanAddressMetadata(streetName)
            if !cleanedStreetName.isEmpty && cleanedStreetName != streetName {
                point.streetName = cleanedStreetName
                hasChanges = true
                print("🧹 清理streetName: '\(streetName)' -> '\(cleanedStreetName)'")
            }
        }
        
        if hasChanges {
            print("🧹 已清理地址: \(point.primaryAddress)")
        }
    }
    
    /// 🧹 清理地址中的元数据信息
    private func cleanAddressMetadata(_ address: String) -> String {
        var cleanedAddress = address
        
        // 🎯 关键：移除所有管道符号（|）后的元数据信息
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )
        
        // 移除第三方sort number模式（如 ISORT:8, D90, D91等）
        let sortPatterns = [
            "\\bISORT:\\d+\\b",  // ISORT:8
            "\\bD\\d+\\b",       // D90, D91, D146等
            "\\bSORT:\\d+\\b"    // SORT:2
        ]
        
        for pattern in sortPatterns {
            cleanedAddress = cleanedAddress.replacingOccurrences(
                of: pattern,
                with: "",
                options: .regularExpression
            )
        }
        
        // 清理多余的空格
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        )
        
        // 清理首尾空格
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        
        return cleanedAddress
    }
    
    /// 🔄 重置清理状态
    func resetStatus() {
        isProcessing = false
        progress = 0.0
        statusMessage = ""
        cleanedCount = 0
        totalCount = 0
    }
}
