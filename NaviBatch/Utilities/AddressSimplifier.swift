import Foundation
import CoreLocation

/// 地址简化工具
/// 专门用于解决Apple Maps地址识别问题，通过智能简化地址来提高识别成功率
class AddressSimplifier {

    // 私有初始化，防止实例化
    private init() {}

    /// 为地址识别优化地址
    /// - Parameter address: 原始地址
    /// - Returns: 优化后的地址候选列表，按优先级排序
    static func generateRecognitionCandidates(_ address: String) -> [String] {
        var candidates: [String] = []

        // 1. 原始地址
        candidates.append(address)

        // 2. 移除国家名称
        if let withoutCountry = removeCountryName(address) {
            candidates.append(withoutCountry)
        }

        // 3. 移除国家和州/省份
        if let withoutCountryAndState = removeCountryAndState(address) {
            candidates.append(withoutCountryAndState)
        }

        // 4. 处理复合门牌号（如847C -> 847）
        let complexHouseNumberCandidates = generateComplexHouseNumberCandidates(address)
        candidates.append(contentsOf: complexHouseNumberCandidates)

        // 5. 只保留核心地址（街道+城市）
        if let coreAddress = extractCoreAddress(address) {
            candidates.append(coreAddress)
        }

        // 6. 移除门牌号（适用于某些情况）
        if let withoutNumber = removeHouseNumber(address) {
            candidates.append(withoutNumber)
        }

        // 去重并保持顺序
        return Array(NSOrderedSet(array: candidates)) as! [String]
    }

    /// 移除国家名称
    /// - Parameter address: 原始地址
    /// - Returns: 移除国家名称后的地址
    static func removeCountryName(_ address: String) -> String? {
        let commonCountries = [
            "United States", "USA", "US",
            "Australia", "AU",
            "Canada", "CA",
            "United Kingdom", "UK", "GB",
            "Hong Kong", "HK",
            "China", "CN",
            "Taiwan", "TW"
        ]

        var result = address

        // 移除常见国家名称（不区分大小写）
        for country in commonCountries {
            let patterns = [
                ", \\s*\(country)\\s*$",  // 末尾的国家名称
                "\\s*,\\s*\(country)\\s*,", // 中间的国家名称
                "^\\s*\(country)\\s*,\\s*"  // 开头的国家名称
            ]

            for pattern in patterns {
                result = result.replacingOccurrences(
                    of: pattern,
                    with: "",
                    options: [.regularExpression, .caseInsensitive]
                )
            }
        }

        // 清理多余的逗号和空格
        result = cleanupAddress(result)

        return result != address ? result : nil
    }

    /// 移除国家和州/省份信息
    /// - Parameter address: 原始地址
    /// - Returns: 移除国家和州/省份后的地址
    static func removeCountryAndState(_ address: String) -> String? {
        // 先移除国家
        var result = removeCountryName(address) ?? address

        // 移除常见的州/省份缩写
        let statePatterns = [
            ", \\s*(VIC|NSW|QLD|WA|SA|TAS|ACT|NT)\\s*$", // 澳大利亚州
            ", \\s*(CA|NY|TX|FL|IL|PA|OH|GA|NC|MI|WA|OR|AZ|CO|NV|UT)\\s*$", // 美国州
            ", \\s*(ON|BC|AB|SK|MB|QC|NB|NS|PE|NL|YT|NT|NU)\\s*$" // 加拿大省
        ]

        for pattern in statePatterns {
            result = result.replacingOccurrences(
                of: pattern,
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
        }

        // 清理地址
        result = cleanupAddress(result)

        return result != address ? result : nil
    }

    /// 提取核心地址（街道+城市）
    /// - Parameter address: 原始地址
    /// - Returns: 核心地址
    static func extractCoreAddress(_ address: String) -> String? {
        let components = address.components(separatedBy: ",")
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }

        // 如果有多个组件，取前两个（通常是街道和城市）
        if components.count >= 2 {
            let coreAddress = components.prefix(2).joined(separator: ", ")
            return coreAddress != address ? coreAddress : nil
        }

        return nil
    }

    /// 移除门牌号
    /// - Parameter address: 原始地址
    /// - Returns: 移除门牌号后的地址
    static func removeHouseNumber(_ address: String) -> String? {
        // 🔧 修复：正确处理复合门牌号（如847C, 23A, 2/17等）
        // 移除开头的完整门牌号（包括数字+字母组合和复合格式）
        let result = address.replacingOccurrences(
            of: "^[\\d]+[a-zA-Z]*(?:/[\\d]+[a-zA-Z]*)?\\s*,?\\s*",
            with: "",
            options: .regularExpression
        )

        let cleaned = cleanupAddress(result)
        return cleaned != address ? cleaned : nil
    }

    /// 生成复合门牌号候选地址
    /// - Parameter address: 原始地址
    /// - Returns: 复合门牌号的候选地址列表
    static func generateComplexHouseNumberCandidates(_ address: String) -> [String] {
        var candidates: [String] = []

        print("🔧 COMPLEX: 开始处理复合门牌号: \(address)")

        // 检测复合门牌号模式：数字+字母（如847C, 23A）
        let letterSuffixPattern = "^(\\d+)[a-zA-Z]+(\\s+.+)$"
        if let letterSuffixMatch = address.range(of: letterSuffixPattern, options: .regularExpression) {
            print("🔧 COMPLEX: 检测到数字+字母格式")
            let regex = try! NSRegularExpression(pattern: letterSuffixPattern)
            let nsRange = NSRange(letterSuffixMatch, in: address)
            if let match = regex.firstMatch(in: address, range: nsRange) {
                if let numberRange = Range(match.range(at: 1), in: address),
                   let restRange = Range(match.range(at: 2), in: address) {
                    let numberOnly = String(address[numberRange])
                    let restOfAddress = String(address[restRange])
                    let candidate = numberOnly + restOfAddress
                    candidates.append(candidate)
                    print("🔧 COMPLEX: ✅ 生成候选地址: \(candidate)")
                }
            }
        }

        // 检测复合门牌号模式：单元号/主号（如2/17, 847/123）
        let unitPattern = "^(\\d+)/(\\d+)(\\s+.+)$"
        if let unitMatch = address.range(of: unitPattern, options: .regularExpression) {
            print("🔧 COMPLEX: 检测到单元号/主号格式")
            let regex = try! NSRegularExpression(pattern: unitPattern)
            let nsRange = NSRange(unitMatch, in: address)
            if let match = regex.firstMatch(in: address, range: nsRange) {
                if let mainNumberRange = Range(match.range(at: 2), in: address),
                   let restRange = Range(match.range(at: 3), in: address) {
                    let mainNumber = String(address[mainNumberRange])
                    let restOfAddress = String(address[restRange])
                    let candidate = mainNumber + restOfAddress
                    candidates.append(candidate)
                    print("🔧 COMPLEX: ✅ 生成候选地址: \(candidate)")
                }
            }
        }

        if candidates.isEmpty {
            print("🔧 COMPLEX: ❌ 未检测到复合门牌号格式")
        }

        return candidates
    }

    /// 清理地址格式
    /// - Parameter address: 需要清理的地址
    /// - Returns: 清理后的地址
    static func cleanupAddress(_ address: String) -> String {
        var result = address

        // 移除多余的逗号和空格
        result = result.replacingOccurrences(of: "\\s*,\\s*,\\s*", with: ", ", options: .regularExpression)
        result = result.replacingOccurrences(of: "^\\s*,\\s*", with: "", options: .regularExpression)
        result = result.replacingOccurrences(of: "\\s*,\\s*$", with: "", options: .regularExpression)
        result = result.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        result = result.trimmingCharacters(in: .whitespacesAndNewlines)

        // 🎯 SpeedX专用：应用地址分隔符格式优化
        result = applySpeedXAddressSeparatorFormat(result)

        return result
    }

    /// 🎯 SpeedX专用：应用地址分隔符格式优化
    private static func applySpeedXAddressSeparatorFormat(_ address: String) -> String {
        var formatted = address

        // 美国州简称列表
        let usStates = [
            "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
            "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
            "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
            "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
            "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "DC"
        ]

        // 对于美国州简称，使用无空格格式：City,CA
        for state in usStates {
            let pattern = ",\\s+\(state)\\b"
            formatted = formatted.replacingOccurrences(
                of: pattern,
                with: ",\(state)",
                options: .regularExpression
            )
        }

        return formatted
    }

    /// 检测地址是否可能导致识别问题
    /// - Parameter address: 地址字符串
    /// - Returns: 问题描述，如果没有问题返回nil
    static func detectRecognitionIssues(_ address: String) -> String? {
        // 检查地址长度
        if address.count > 100 {
            return "address_too_long_issue".localized
        }

        // 检查是否包含完整国家名称
        if address.lowercased().contains("united states") {
            return "address_contains_country_issue".localized
        }

        // 检查是否有多个逗号
        let commaCount = address.components(separatedBy: ",").count - 1
        if commaCount > 4 {
            return "address_too_many_components_issue".localized
        }

        return nil
    }

    /// 智能简化地址（一键简化）
    /// - Parameter address: 原始地址
    /// - Returns: 简化后的地址
    static func smartSimplify(_ address: String) -> String {
        // 优先级1: 将"United States"替换为"US"
        if address.lowercased().contains("united states") {
            let simplified = address.replacingOccurrences(
                of: "United States",
                with: "US",
                options: .caseInsensitive
            )
            return cleanupAddress(simplified)
        }

        // 优先级2: 移除国家名称
        if let simplified = removeCountryName(address) {
            return simplified
        }

        // 优先级3: 提取核心地址
        if let coreAddress = extractCoreAddress(address) {
            return coreAddress
        }

        return cleanupAddress(address)
    }
}
