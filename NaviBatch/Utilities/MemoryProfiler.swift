import Foundation
import UIKit
import os.log

/// 内存性能监控工具
class MemoryProfiler {
    static let shared = MemoryProfiler()
    private let logger = OSLog(subsystem: "com.navibatch.app", category: "MemoryProfiler")

    private var isMonitoring = false
    private var monitoringTimer: Timer?
    private var memorySnapshots: [MemorySnapshot] = []

    private init() {}

    /// 内存快照数据结构
    struct MemorySnapshot {
        let timestamp: Date
        let usedMemoryMB: Double
        let availableMemoryMB: Double
        let totalMemoryMB: Double
        let memoryPressure: MemoryPressure
        let context: String

        enum MemoryPressure {
            case normal
            case warning
            case critical
        }
    }

    // MARK: - 公共接口

    /// 开始内存监控
    func startMonitoring(interval: TimeInterval = 1.0) {
        guard !isMonitoring else { return }

        isMonitoring = true
        memorySnapshots.removeAll()

        monitoringTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.captureMemorySnapshot(context: "定期监控")
        }

        os_log("内存监控已启动，监控间隔: %{public}f秒", log: logger, type: .info, interval)
    }

    /// 停止内存监控
    func stopMonitoring() {
        guard isMonitoring else { return }

        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil

        os_log("内存监控已停止", log: logger, type: .info)
        generateMemoryReport()
    }

    /// 手动捕获内存快照
    func captureMemorySnapshot(context: String = "手动捕获") {
        let snapshot = getCurrentMemorySnapshot(context: context)
        memorySnapshots.append(snapshot)

        // 如果内存使用过高，立即记录警告
        if snapshot.memoryPressure != .normal {
            os_log("内存压力警告: %{public}@ - 使用内存: %{public}fMB", log: logger, type: .error, context, snapshot.usedMemoryMB)
        }
    }

    /// 获取当前内存使用情况
    func getCurrentMemoryUsage() -> (used: Double, available: Double, total: Double) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024.0 / 1024.0
            let totalMB = Double(ProcessInfo.processInfo.physicalMemory) / 1024.0 / 1024.0
            let availableMB = totalMB - usedMB

            return (used: usedMB, available: availableMB, total: totalMB)
        } else {
            return (used: 0, available: 0, total: 0)
        }
    }

    // MARK: - 私有方法

    private func getCurrentMemorySnapshot(context: String) -> MemorySnapshot {
        let memory = getCurrentMemoryUsage()
        let pressure = determineMemoryPressure(usedMB: memory.used, totalMB: memory.total)

        return MemorySnapshot(
            timestamp: Date(),
            usedMemoryMB: memory.used,
            availableMemoryMB: memory.available,
            totalMemoryMB: memory.total,
            memoryPressure: pressure,
            context: context
        )
    }

    private func determineMemoryPressure(usedMB: Double, totalMB: Double) -> MemorySnapshot.MemoryPressure {
        let usagePercentage = (usedMB / totalMB) * 100

        switch usagePercentage {
        case 0..<70:
            return .normal
        case 70..<85:
            return .warning
        default:
            return .critical
        }
    }

    private func generateMemoryReport() {
        guard !memorySnapshots.isEmpty else { return }

        let maxMemory = memorySnapshots.max { $0.usedMemoryMB < $1.usedMemoryMB }
        let avgMemory = memorySnapshots.reduce(0) { $0 + $1.usedMemoryMB } / Double(memorySnapshots.count)
        let warningCount = memorySnapshots.filter { $0.memoryPressure == .warning }.count
        let criticalCount = memorySnapshots.filter { $0.memoryPressure == .critical }.count

        let reportMessage = """
        📊 内存使用报告:
        - 监控时长: \(memorySnapshots.count) 个快照
        - 平均内存使用: \(String(format: "%.2f", avgMemory))MB
        - 峰值内存使用: \(String(format: "%.2f", maxMemory?.usedMemoryMB ?? 0))MB
        - 内存警告次数: \(warningCount)
        - 内存危险次数: \(criticalCount)
        """
        os_log("%{public}@", log: logger, type: .info, reportMessage)

        #if DEBUG
        // 在调试模式下，将详细报告写入文件
        saveDetailedReport()
        #endif
    }

    #if DEBUG
    private func saveDetailedReport() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = formatter.string(from: Date())

        var report = "NaviBatch 内存测试报告 - \(timestamp)\n"
        report += "=" * 50 + "\n\n"

        for (index, snapshot) in memorySnapshots.enumerated() {
            let timeFormatter = DateFormatter()
            timeFormatter.dateFormat = "HH:mm:ss"

            report += "快照 \(index + 1): \(timeFormatter.string(from: snapshot.timestamp))\n"
            report += "  上下文: \(snapshot.context)\n"
            report += "  使用内存: \(String(format: "%.2f", snapshot.usedMemoryMB))MB\n"
            report += "  可用内存: \(String(format: "%.2f", snapshot.availableMemoryMB))MB\n"
            report += "  内存压力: \(snapshot.memoryPressure)\n"
            report += "  使用率: \(String(format: "%.1f", (snapshot.usedMemoryMB / snapshot.totalMemoryMB) * 100))%\n\n"
        }

        // 保存到文档目录
        if let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentsPath.appendingPathComponent("memory_report_\(timestamp).txt")
            try? report.write(to: fileURL, atomically: true, encoding: .utf8)
            os_log("详细内存报告已保存到: %{public}@", log: logger, type: .info, fileURL.path)
        }
    }
    #endif
}

// MARK: - 内存测试场景

extension MemoryProfiler {

    /// 测试大量地址导入的内存使用
    func testBulkAddressImport(addressCount: Int) {
        captureMemorySnapshot(context: "开始批量导入测试")

        // 模拟大量地址数据
        var addresses: [String] = []
        for i in 1...addressCount {
            addresses.append("测试地址 \(i), 某某街道 \(i) 号")
        }

        captureMemorySnapshot(context: "创建 \(addressCount) 个地址后")

        // 清理数据
        addresses.removeAll()

        // 强制垃圾回收
        autoreleasepool {
            // 空的自动释放池，帮助清理临时对象
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.captureMemorySnapshot(context: "清理地址数据后")
        }
    }

    /// 测试地图渲染的内存使用
    func testMapRendering() {
        captureMemorySnapshot(context: "地图渲染测试开始")

        // 这里可以触发地图相关操作
        NotificationCenter.default.post(name: Notification.Name("StartMapMemoryTest"), object: nil)
    }

    /// 测试路线优化的内存使用
    func testRouteOptimization(pointCount: Int) {
        captureMemorySnapshot(context: "路线优化测试开始 - \(pointCount) 个点")

        // 这里可以触发路线优化操作
        NotificationCenter.default.post(
            name: Notification.Name("StartRouteOptimizationMemoryTest"),
            object: pointCount
        )
    }
}

// MARK: - 字符串扩展
private extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
