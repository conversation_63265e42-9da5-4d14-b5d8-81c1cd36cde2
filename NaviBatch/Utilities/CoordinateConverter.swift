import Foundation
import CoreLocation

/// 坐标系转换工具
/// 解决国内MapKit定位不准的问题：WGS-84 ↔ GCJ-02 坐标系转换
class CoordinateConverter {

    // 私有初始化，防止实例化
    private init() {}

    // 常量参数
    private static let a: Double = 6378245.0
    private static let ee: Double = 0.00669342162296594323

    /// WGS-84 转 GCJ-02 (GPS坐标转火星坐标)
    /// - Parameters:
    ///   - longitude: WGS-84经度
    ///   - latitude: WGS-84纬度
    /// - Returns: GCJ-02坐标 (经度, 纬度)
    static func wgs84ToGcj02(longitude: Double, latitude: Double) -> (Double, Double) {
        // 🛡️ 安全检查：防止NaN输入
        if longitude.isNaN || latitude.isNaN {
            return (longitude, latitude)
        }

        if outOfChina(longitude: longitude, latitude: latitude) {
            return (longitude, latitude)
        }

        var dLat = transformLat(x: longitude - 105.0, y: latitude - 35.0)
        var dLon = transformLon(x: longitude - 105.0, y: latitude - 35.0)
        let radLat = latitude / 180.0 * .pi
        var magic = sin(radLat)
        magic = 1 - ee * magic * magic

        // 🛡️ 安全检查：防止负数开方
        if magic <= 0 {
            return (longitude, latitude)
        }

        let sqrtMagic = sqrt(magic)

        // 🛡️ 安全检查：防止除零
        if sqrtMagic == 0 || magic == 0 {
            return (longitude, latitude)
        }

        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * .pi)
        dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * .pi)
        let mgLat = latitude + dLat
        let mgLon = longitude + dLon

        // 🛡️ 最终检查：确保结果不是NaN
        if mgLat.isNaN || mgLon.isNaN {
            return (longitude, latitude)
        }

        return (mgLon, mgLat)
    }

    /// GCJ-02 转 WGS-84 (火星坐标转GPS坐标)
    /// - Parameters:
    ///   - longitude: GCJ-02经度
    ///   - latitude: GCJ-02纬度
    /// - Returns: WGS-84坐标 (经度, 纬度)
    static func gcj02ToWgs84(longitude: Double, latitude: Double) -> (Double, Double) {
        // 🛡️ 安全检查：防止NaN输入
        if longitude.isNaN || latitude.isNaN {
            return (longitude, latitude)
        }

        if outOfChina(longitude: longitude, latitude: latitude) {
            return (longitude, latitude)
        }

        var dLat = transformLat(x: longitude - 105.0, y: latitude - 35.0)
        var dLon = transformLon(x: longitude - 105.0, y: latitude - 35.0)
        let radLat = latitude / 180.0 * .pi
        var magic = sin(radLat)
        magic = 1 - ee * magic * magic

        // 🛡️ 安全检查：防止负数开方
        if magic <= 0 {
            return (longitude, latitude)
        }

        let sqrtMagic = sqrt(magic)

        // 🛡️ 安全检查：防止除零
        if sqrtMagic == 0 || magic == 0 {
            return (longitude, latitude)
        }

        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * .pi)
        dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * .pi)
        let mgLat = latitude + dLat
        let mgLon = longitude + dLon

        let resultLon = longitude * 2 - mgLon
        let resultLat = latitude * 2 - mgLat

        // 🛡️ 最终检查：确保结果不是NaN
        if resultLat.isNaN || resultLon.isNaN {
            return (longitude, latitude)
        }

        return (resultLon, resultLat)
    }

    /// CLLocationCoordinate2D 版本：WGS-84 转 GCJ-02
    /// - Parameter coordinate: WGS-84坐标
    /// - Returns: GCJ-02坐标
    static func wgs84ToGcj02(coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        let (lon, lat) = wgs84ToGcj02(longitude: coordinate.longitude, latitude: coordinate.latitude)
        return CLLocationCoordinate2D(latitude: lat, longitude: lon)
    }

    /// CLLocationCoordinate2D 版本：GCJ-02 转 WGS-84
    /// - Parameter coordinate: GCJ-02坐标
    /// - Returns: WGS-84坐标
    static func gcj02ToWgs84(coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        let (lon, lat) = gcj02ToWgs84(longitude: coordinate.longitude, latitude: coordinate.latitude)
        return CLLocationCoordinate2D(latitude: lat, longitude: lon)
    }

    /// 智能坐标转换：根据地理位置自动选择是否需要转换
    /// - Parameters:
    ///   - coordinate: 原始坐标
    ///   - fromWgs84: 是否从WGS-84转换到GCJ-02
    /// - Returns: 转换后的坐标
    static func smartConvert(coordinate: CLLocationCoordinate2D, fromWgs84: Bool = true) -> CLLocationCoordinate2D {
        // 如果在国外，不需要转换
        if outOfChina(longitude: coordinate.longitude, latitude: coordinate.latitude) {
            return coordinate
        }

        // 国内需要转换
        if fromWgs84 {
            return wgs84ToGcj02(coordinate: coordinate)
        } else {
            return gcj02ToWgs84(coordinate: coordinate)
        }
    }

    // MARK: - 私有方法

    /// 转换纬度
    private static func transformLat(x: Double, y: Double) -> Double {
        // 🛡️ 安全检查：防止NaN输入
        if x.isNaN || y.isNaN {
            return 0.0
        }

        var ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(abs(x))
        ret += (20.0 * sin(6.0 * x * .pi) + 20.0 * sin(2.0 * x * .pi)) * 2.0 / 3.0
        ret += (20.0 * sin(y * .pi) + 40.0 * sin(y / 3.0 * .pi)) * 2.0 / 3.0
        ret += (160.0 * sin(y / 12.0 * .pi) + 320 * sin(y * .pi / 30.0)) * 2.0 / 3.0

        // 🛡️ 最终检查：确保结果不是NaN
        if ret.isNaN {
            return 0.0
        }

        return ret
    }

    /// 转换经度
    private static func transformLon(x: Double, y: Double) -> Double {
        // 🛡️ 安全检查：防止NaN输入
        if x.isNaN || y.isNaN {
            return 0.0
        }

        var ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(abs(x))
        ret += (20.0 * sin(2.0 * x * .pi) + 20.0 * sin(x * .pi)) * 2.0 / 3.0
        ret += (20.0 * sin(x * .pi) + 40.0 * sin(x / 3.0 * .pi)) * 2.0 / 3.0
        ret += (150.0 * sin(x / 12.0 * .pi) + 300 * sin(x * .pi / 30.0)) * 2.0 / 3.0

        // 🛡️ 最终检查：确保结果不是NaN
        if ret.isNaN {
            return 0.0
        }

        return ret
    }

    /// 判断是否在国内
    /// - Parameters:
    ///   - longitude: 经度
    ///   - latitude: 纬度
    /// - Returns: 是否在国外（国外不需要坐标转换）
    private static func outOfChina(longitude: Double, latitude: Double) -> Bool {
        return !(longitude > 73.66 && longitude < 135.05 && latitude > 3.86 && latitude < 53.55)
    }
}

// MARK: - 扩展：为CLLocationCoordinate2D添加便利方法
extension CLLocationCoordinate2D {

    /// 转换为GCJ-02坐标（火星坐标）
    /// - Returns: GCJ-02坐标
    func toGcj02() -> CLLocationCoordinate2D {
        return CoordinateConverter.wgs84ToGcj02(coordinate: self)
    }

    /// 转换为WGS-84坐标（GPS坐标）
    /// - Returns: WGS-84坐标
    func toWgs84() -> CLLocationCoordinate2D {
        return CoordinateConverter.gcj02ToWgs84(coordinate: self)
    }

    /// 智能坐标转换
    /// - Parameter fromWgs84: 是否从WGS-84转换
    /// - Returns: 转换后的坐标
    func smartConverted(fromWgs84: Bool = true) -> CLLocationCoordinate2D {
        return CoordinateConverter.smartConvert(coordinate: self, fromWgs84: fromWgs84)
    }

    /// 检查是否在中国境内
    /// - Returns: 是否在中国境内
    func isInChina() -> Bool {
        return longitude > 73.66 && longitude < 135.05 && latitude > 3.86 && latitude < 53.55
    }
}
