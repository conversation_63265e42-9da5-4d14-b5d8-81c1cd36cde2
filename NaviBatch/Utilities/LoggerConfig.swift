import Foundation
import SwiftUI

/// 日志配置管理器
/// 用于控制哪些日志类型和级别需要输出，避免不必要的日志spam
class LoggerConfig: ObservableObject {
    static let shared = LoggerConfig()

    // MARK: - 日志级别控制

    /// 是否启用调试日志
    @Published var isDebugEnabled: Bool = true

    /// 是否启用信息日志
    @Published var isInfoEnabled: Bool = true

    /// 是否启用警告日志
    @Published var isWarningEnabled: Bool = true

    /// 是否启用错误日志
    @Published var isErrorEnabled: Bool = true

    // MARK: - 日志类型控制

    /// 启用的日志类型 - 临时启用location日志用于调试地址编辑功能
    @Published var enabledLogTypes: Set<LogType> = [
        .error, .warning, .ai, .ocr, .location  // 临时启用location日志
    ]

    /// 禁用的日志类型 - 禁用大部分详细日志
    @Published var disabledLogTypes: Set<LogType> = [
        .debug, .info, .action, .data, .network, .lifecycle,
        .validation, .auth, .system, .imageProcessing
    ]

    // MARK: - 特定功能控制

    /// 是否启用速率限制器日志
    @Published var isRateLimiterLoggingEnabled: Bool = false  // 🔧 默认关闭，避免spam

    /// 是否启用地理编码详细日志
    @Published var isGeocodingDetailLoggingEnabled: Bool = false

    /// 是否启用AI识别详细日志
    @Published var isAIDetailLoggingEnabled: Bool = false  // 🔧 关闭AI详细日志

    /// 是否启用OCR详细日志
    @Published var isOCRDetailLoggingEnabled: Bool = false  // 🔧 关闭OCR详细日志

    /// 是否启用图片处理详细日志
    @Published var isImageProcessingDetailLoggingEnabled: Bool = false

    // MARK: - 文件和函数过滤

    /// 需要静音的文件（不输出日志）
    @Published var silentFiles: Set<String> = [
        "DatabaseDebugger",  // 数据库调试工具
        "AddressStandardizationTest",  // 地址标准化测试
        "TestGellertBlvd",  // 测试文件
        "ConfigServiceTest"  // 配置服务测试
    ]

    /// 需要静音的函数（不输出日志）
    @Published var silentFunctions: Set<String> = [
        "mapView",  // 地图视图渲染
        "setupAsyncTasks",  // 异步任务设置
        "logRouteInfo",  // 路线信息记录
        "setupRouteData"  // 路线数据设置
    ]

    /// 需要静音的日志关键词
    @Published var silentKeywords: Set<String> = [
        "渲染地图",  // 地图渲染相关
        "相机位置",  // 相机位置变化
        "组件出现",  // 组件生命周期
        "已设置ModelContext",  // ModelContext设置
        "当前相机位置",  // 相机位置信息
        "多选模式下的标记点数量",  // 多选模式详情
        "一键分组按钮显示条件"  // 按钮显示条件
    ]

    // MARK: - 性能优化配置

    /// 地图标记渲染模式 - 控制标记的渲染性能
    @Published var markerRenderMode: MarkerRenderMode = .numbersRectangle  // 🎯 默认使用清晰的rectangle.fill模式

    /// 是否启用GPU硬件加速
    @Published var isGPUAccelerationEnabled: Bool = true

    /// 是否启用标记渲染优化
    @Published var isMarkerOptimizationEnabled: Bool = true

    // MARK: - 公共方法

    private init() {
        loadConfiguration()
    }

    /// 检查是否应该输出日志
    func shouldLog(type: LogType, file: String, function: String, message: String) -> Bool {
        // 检查日志类型是否启用
        if disabledLogTypes.contains(type) || !enabledLogTypes.contains(type) {
            return false
        }

        // 检查特定功能控制
        if !isRateLimiterLoggingEnabled && message.contains("处理速度优化") {
            return false
        }

        if !isGeocodingDetailLoggingEnabled && type == .location && message.contains("地理编码") {
            return false
        }

        // 检查文件过滤
        let fileName = URL(fileURLWithPath: file).lastPathComponent.replacingOccurrences(of: ".swift", with: "")
        if silentFiles.contains(fileName) {
            return false
        }

        // 检查函数过滤
        if silentFunctions.contains(function) {
            return false
        }

        // 检查关键词过滤
        for keyword in silentKeywords {
            if message.contains(keyword) {
                return false
            }
        }

        return true
    }

    /// 启用日志类型
    func enableLogType(_ type: LogType) {
        enabledLogTypes.insert(type)
        disabledLogTypes.remove(type)
    }

    /// 禁用日志类型
    func disableLogType(_ type: LogType) {
        disabledLogTypes.insert(type)
        enabledLogTypes.remove(type)
    }

    /// 添加静音文件
    func addSilentFile(_ fileName: String) {
        silentFiles.insert(fileName)
    }

    /// 移除静音文件
    func removeSilentFile(_ fileName: String) {
        silentFiles.remove(fileName)
    }

    /// 添加静音关键词
    func addSilentKeyword(_ keyword: String) {
        silentKeywords.insert(keyword)
    }

    /// 移除静音关键词
    func removeSilentKeyword(_ keyword: String) {
        silentKeywords.remove(keyword)
    }

    /// 重置为默认配置（精简模式）
    func resetToDefaults() {
        isDebugEnabled = false
        isInfoEnabled = false  // 🔧 默认关闭info日志
        isWarningEnabled = true
        isErrorEnabled = true

        enabledLogTypes = [.error, .warning, .ai, .ocr]  // 只保留必要的日志类型
        disabledLogTypes = [.debug, .info, .action, .data, .network, .lifecycle, .location, .validation, .auth, .system, .imageProcessing]

        isRateLimiterLoggingEnabled = false
        isGeocodingDetailLoggingEnabled = false
        isAIDetailLoggingEnabled = false  // 🔧 默认关闭AI详细日志
        isOCRDetailLoggingEnabled = false  // 🔧 默认关闭OCR详细日志
        isImageProcessingDetailLoggingEnabled = false

        // 保持静音配置
        silentFiles = ["DatabaseDebugger", "AddressStandardizationTest", "TestGellertBlvd", "ConfigServiceTest"]
        silentFunctions = ["mapView", "setupAsyncTasks", "logRouteInfo", "setupRouteData"]
        silentKeywords = ["渲染地图", "相机位置", "组件出现", "已设置ModelContext", "当前相机位置", "多选模式下的标记点数量", "一键分组按钮显示条件"]

        // 性能配置
        markerRenderMode = .numbersRectangle  // 🎯 默认使用清晰的rectangle.fill模式
        isGPUAccelerationEnabled = true
        isMarkerOptimizationEnabled = true
    }

    // MARK: - 配置持久化

    private func loadConfiguration() {
        // 从UserDefaults加载配置
        let defaults = UserDefaults.standard

        isRateLimiterLoggingEnabled = defaults.bool(forKey: "LoggerConfig.isRateLimiterLoggingEnabled")
        isGeocodingDetailLoggingEnabled = defaults.bool(forKey: "LoggerConfig.isGeocodingDetailLoggingEnabled")
        isAIDetailLoggingEnabled = defaults.object(forKey: "LoggerConfig.isAIDetailLoggingEnabled") as? Bool ?? true
        isOCRDetailLoggingEnabled = defaults.object(forKey: "LoggerConfig.isOCRDetailLoggingEnabled") as? Bool ?? true
        isImageProcessingDetailLoggingEnabled = defaults.bool(forKey: "LoggerConfig.isImageProcessingDetailLoggingEnabled")

        // 加载静音关键词
        if let keywords = defaults.array(forKey: "LoggerConfig.silentKeywords") as? [String] {
            silentKeywords = Set(keywords)
        }
    }

    func saveConfiguration() {
        // 保存配置到UserDefaults
        let defaults = UserDefaults.standard

        defaults.set(isRateLimiterLoggingEnabled, forKey: "LoggerConfig.isRateLimiterLoggingEnabled")
        defaults.set(isGeocodingDetailLoggingEnabled, forKey: "LoggerConfig.isGeocodingDetailLoggingEnabled")
        defaults.set(isAIDetailLoggingEnabled, forKey: "LoggerConfig.isAIDetailLoggingEnabled")
        defaults.set(isOCRDetailLoggingEnabled, forKey: "LoggerConfig.isOCRDetailLoggingEnabled")
        defaults.set(isImageProcessingDetailLoggingEnabled, forKey: "LoggerConfig.isImageProcessingDetailLoggingEnabled")

        defaults.set(Array(silentKeywords), forKey: "LoggerConfig.silentKeywords")
    }
}

// MARK: - 便捷方法

extension LoggerConfig {
    /// 快速禁用速率限制器日志
    func disableRateLimiterLogs() {
        isRateLimiterLoggingEnabled = false
        addSilentKeyword("处理速度优化")
        addSilentKeyword("已优化")
        saveConfiguration()
    }

    /// 快速启用速率限制器日志
    func enableRateLimiterLogs() {
        isRateLimiterLoggingEnabled = true
        removeSilentKeyword("处理速度优化")
        removeSilentKeyword("已优化")
        saveConfiguration()
    }

    /// 快速禁用地理编码详细日志
    func disableGeocodingDetailLogs() {
        isGeocodingDetailLoggingEnabled = false
        saveConfiguration()
    }

    /// 快速启用地理编码详细日志
    func enableGeocodingDetailLogs() {
        isGeocodingDetailLoggingEnabled = true
        saveConfiguration()
    }
}
