import Foundation
import os.log

enum LogType {
    case info
    case debug
    case warning
    case error
    case action
    case data
    case network
    case lifecycle
    case location
    case validation
    case auth
    case system
    case ai           // AI相关日志
    case ocr          // OCR相关日志
    case imageProcessing  // 图片处理日志
}

class Logger {
    private static let osLog = OSLog(subsystem: Bundle.main.bundleIdentifier ?? "com.navibatch", category: "NaviBatch")

    // 应用日志实例，方便直接使用
    static let app = OSLog(subsystem: Bundle.main.bundleIdentifier ?? "com.navibatch", category: "NaviBatch")

    static func info(_ message: String, type: LogType = .info, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, type: type, level: .info, file: file, function: function, line: line)
    }

    static func debug(_ message: String, type: LogType = .debug, file: String = #file, function: String = #function, line: Int = #line) {
        #if DEBUG
        log(message, type: type, level: .debug, file: file, function: function, line: line)
        #endif
    }

    static func warning(_ message: String, type: LogType = .warning, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, type: type, level: .error, file: file, function: function, line: line)
    }

    static func error(_ message: String, type: LogType = .error, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, type: type, level: .error, file: file, function: function, line: line)
    }

    static func userAction(_ message: String, type: LogType = .action, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, type: type, level: .info, file: file, function: function, line: line)
    }

    private static func log(_ message: String, type: LogType, level: OSLogType, file: String = #file, function: String = #function, line: Int = #line) {
        // 🔧 检查是否应该输出此日志
        guard LoggerConfig.shared.shouldLog(type: type, file: file, function: function, message: message) else {
            return
        }

        let fileName = URL(fileURLWithPath: file).lastPathComponent.replacingOccurrences(of: ".swift", with: "")
        let typePrefix = getTypePrefix(type)
        let locationInfo = "[\(fileName).\(function):\(line)]"

        // 🔧 改进：包含文件名、函数名和行号的详细日志
        let fullMessage = "\(locationInfo) \(message)"

        // 🔧 只输出到控制台，避免重复
        #if DEBUG
        print("\(typePrefix): \(fullMessage)")
        #else
        os_log("%{public}@: %{public}@", log: osLog, type: level, typePrefix, fullMessage)
        #endif
    }

    private static func getTypePrefix(_ type: LogType) -> String {
        switch type {
        case .info: return "ℹ️ INFO"
        case .debug: return "🔍 DEBUG"
        case .warning: return "⚠️ WARNING"
        case .error: return "❌ ERROR"
        case .action: return "👆 ACTION"
        case .data: return "💾 DATA"
        case .network: return "🌐 NETWORK"
        case .lifecycle: return "♻️ LIFECYCLE"
        case .location: return "📍 LOCATION"
        case .validation: return "✅ VALIDATION"
        case .auth: return "🔐 AUTH"
        case .system: return "⚙️ SYSTEM"
        case .ai: return "🤖 AI"
        case .ocr: return "📷 OCR"
        case .imageProcessing: return "🖼️ IMAGE"
        }
    }

    // MARK: - AI专用日志方法（包含位置信息）
    static func aiInfo(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        info(message, type: .ai, file: file, function: function, line: line)
    }

    static func aiError(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        error(message, type: .ai, file: file, function: function, line: line)
    }

    static func aiDebug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        debug(message, type: .ai, file: file, function: function, line: line)
    }

    static func aiWarning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        warning(message, type: .ai, file: file, function: function, line: line)
    }

    static func ocrInfo(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        info(message, type: .ocr, file: file, function: function, line: line)
    }

    static func ocrError(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        error(message, type: .ocr, file: file, function: function, line: line)
    }

    static func ocrDebug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        debug(message, type: .ocr, file: file, function: function, line: line)
    }

    static func imageProcessing(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        info(message, type: .imageProcessing, file: file, function: function, line: line)
    }
}
