//
//  AddressMigrationManager.swift
//  NaviBatch
//
//  Created by System on 2025-01-27.
//  Copyright © 2025 NaviBatch. All rights reserved.
//

import Foundation
import SwiftData
import CoreLocation

/// 🔄 地址迁移管理器 - 负责将旧格式地址迁移到结构化格式
@MainActor
class AddressMigrationManager: ObservableObject {

    @Published var migrationProgress: Double = 0.0
    @Published var isMigrating: Bool = false
    @Published var migrationStatus: String = ""

    private let modelContext: ModelContext

    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    /// 🔍 检查需要迁移的地址数量
    func checkMigrationNeeded() -> Int {
        let descriptor = FetchDescriptor<DeliveryPoint>()

        do {
            let allPoints = try modelContext.fetch(descriptor)
            let needMigration = allPoints.filter { !$0.hasStructuredAddress }

            print("🔍 地址迁移检查:")
            print("   总地址数: \(allPoints.count)")
            print("   需要迁移: \(needMigration.count)")
            print("   已迁移: \(allPoints.count - needMigration.count)")

            return needMigration.count
        } catch {
            print("❌ 检查迁移状态失败: \(error)")
            return 0
        }
    }

    /// 🚀 开始批量迁移
    func startMigration() async {
        guard !isMigrating else { return }

        isMigrating = true
        migrationProgress = 0.0
        migrationStatus = "开始迁移..."

        let descriptor = FetchDescriptor<DeliveryPoint>()

        do {
            let allPoints = try modelContext.fetch(descriptor)
            let needMigration = allPoints.filter { !$0.hasStructuredAddress }

            guard !needMigration.isEmpty else {
                migrationStatus = "所有地址已经是结构化格式"
                isMigrating = false
                return
            }

            print("🚀 开始批量迁移 \(needMigration.count) 个地址")

            for (index, point) in needMigration.enumerated() {
                migrationStatus = "迁移地址 \(index + 1)/\(needMigration.count)"
                migrationProgress = Double(index) / Double(needMigration.count)

                await migratePoint(point)

                // 每10个地址保存一次
                if (index + 1) % 10 == 0 {
                    try modelContext.save()
                    print("💾 已保存 \(index + 1) 个迁移的地址")
                }
            }

            // 最终保存
            try modelContext.save()

            migrationProgress = 1.0
            migrationStatus = "迁移完成！"

            print("✅ 地址迁移完成: \(needMigration.count) 个地址")

        } catch {
            print("❌ 迁移失败: \(error)")
            migrationStatus = "迁移失败: \(error.localizedDescription)"
        }

        isMigrating = false
    }

    /// 🔄 迁移单个地址点
    private func migratePoint(_ point: DeliveryPoint) async {
        print("🔄 迁移地址: \(point.primaryAddress)")

        // 使用全球地址处理器重新地理编码
        let result = await UniversalAddressProcessor.shared.processGlobalAddress(point.primaryAddress)

        switch result {
        case .success(_, let formattedAddress, _, let placemark, let strategy, _):
            print("✅ 迁移成功 [\(strategy)]: \(formattedAddress)")

            // 填充结构化地址字段
            point.populateStructuredAddress(from: placemark)

            // 地址已通过 populateStructuredAddress 更新，无需额外操作

        case .failed(_, let reason):
            print("⚠️ 迁移失败，保持原地址: \(reason)")

            // 尝试基本解析
            parseBasicAddressComponents(point)
        }
    }

    /// 📝 基本地址组件解析（备用方案）
    private func parseBasicAddressComponents(_ point: DeliveryPoint) {
        let address = point.primaryAddress
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        guard components.count >= 2 else {
            print("⚠️ 地址格式太简单，无法解析: \(address)")
            return
        }

        // 尝试解析第一部分（街道地址）
        let streetComponent = components[0]
        let streetParts = streetComponent.components(separatedBy: " ")

        var parsedStreetNumber: String? = nil
        var parsedStreetName: String? = nil

        if streetParts.count >= 2 {
            // 假设第一部分是门牌号，其余是街道名
            parsedStreetNumber = streetParts[0]
            parsedStreetName = streetParts.dropFirst().joined(separator: " ")

            point.streetNumber = parsedStreetNumber
            point.streetName = parsedStreetName
        }

        // 尝试解析其他组件
        if components.count >= 2 {
            point.suburb = components[1]
        }

        if components.count >= 3 {
            point.state = components[2]
        }

        if components.count >= 4 {
            point.country = components[3]
        }

        print("📝 基本解析完成: \(parsedStreetNumber ?? "无") \(parsedStreetName ?? "无"), \(point.suburb ?? "无")")
    }

    /// 🧹 清理旧地址字段（最终步骤）
    func cleanupOldAddressFields() {
        print("🧹 注意: 清理旧地址字段需要在所有地址都迁移完成后进行")
        print("🧹 这个操作需要修改数据模型，建议在下个版本中实施")
    }
}
