import Foundation
import CoreLocation

/// 距离格式化工具，根据用户首选的距离单位格式化距离显示
class DistanceFormatter {
    
    static let shared = DistanceFormatter()
    
    private init() {}
    
    /// 获取用户首选的距离单位
    func getPreferredDistanceUnit() -> String {
        return UserDefaults.standard.string(forKey: "preferredDistanceUnit") ?? "km"
    }
    
    /// 格式化距离数值为用户首选的单位字符串
    /// - Parameter distance: 距离，单位为米
    /// - Returns: 格式化后的距离字符串
    func formatDistance(_ distance: CLLocationDistance) -> String {
        let preferredUnit = getPreferredDistanceUnit()
        
        switch preferredUnit {
        case "km":
            // 使用公里作为单位
            if distance >= 1000 {
                return String(format: "kilometers_format".localized, distance / 1000)
            } else {
                return String(format: "meters_format".localized, distance)
            }
            
        case "mi":
            // 使用英里作为单位
            let miles = distance / 1609.344
            if miles >= 0.1 {
                return String(format: "%.1f mi", miles)
            } else {
                // 小于0.1英里时使用英尺
                let feet = distance / 0.3048
                return String(format: "%.0f ft", feet)
            }
            
        case "m":
            // 始终使用米作为单位
            return String(format: "%.0f m", distance)
            
        default:
            // 默认使用公里/米
            if distance >= 1000 {
                return String(format: "kilometers_format".localized, distance / 1000)
            } else {
                return String(format: "meters_format".localized, distance)
            }
        }
    }
} 