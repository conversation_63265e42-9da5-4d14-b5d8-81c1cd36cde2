//
//  MemoryLeakDetector.swift
//  NaviBatch
//
//  Created by Augment Agent
//

import Foundation
import os.log

/// 内存泄漏检测器
/// 用于检测和防止常见的内存泄漏问题
class MemoryLeakDetector {
    static let shared = MemoryLeakDetector()
    
    private let logger = OSLog(subsystem: "com.navibatch.app", category: "MemoryLeak")
    private var trackedObjects: [String: WeakObjectContainer] = [:]
    private let queue = DispatchQueue(label: "memory.leak.detector", qos: .utility)
    
    private init() {}
    
    // MARK: - 对象追踪
    
    /// 开始追踪对象
    func trackObject<T: AnyObject>(_ object: T, identifier: String? = nil) {
        let id = identifier ?? "\(type(of: object))-\(ObjectIdentifier(object).hashValue)"
        
        queue.async { [weak self] in
            self?.trackedObjects[id] = WeakObjectContainer(object: object, className: String(describing: type(of: object)))
            os_log("开始追踪对象: %{public}@", log: self?.logger ?? OSLog.default, type: .info, id)
        }
    }
    
    /// 停止追踪对象
    func stopTracking(identifier: String) {
        queue.async { [weak self] in
            self?.trackedObjects.removeValue(forKey: identifier)
            os_log("停止追踪对象: %{public}@", log: self?.logger ?? OSLog.default, type: .info, identifier)
        }
    }
    
    /// 检查内存泄漏
    func checkForLeaks() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            var leakedObjects: [String] = []
            
            for (identifier, container) in self.trackedObjects {
                if container.object == nil {
                    // 对象已被释放，正常
                    continue
                } else {
                    // 对象仍然存在，可能泄漏
                    leakedObjects.append("\(identifier) (\(container.className))")
                }
            }
            
            if !leakedObjects.isEmpty {
                os_log("检测到可能的内存泄漏: %{public}@", log: self.logger, type: .error, leakedObjects.joined(separator: ", "))
            } else {
                os_log("未检测到内存泄漏", log: self.logger, type: .info)
            }
            
            // 清理已释放的对象
            self.trackedObjects = self.trackedObjects.filter { $0.value.object != nil }
        }
    }
    
    /// 获取当前追踪的对象数量
    func getTrackedObjectCount() -> Int {
        return trackedObjects.count
    }
}

// MARK: - 辅助类型

private class WeakObjectContainer {
    weak var object: AnyObject?
    let className: String
    
    init(object: AnyObject, className: String) {
        self.object = object
        self.className = className
    }
}

// MARK: - 便利扩展

extension NSObject {
    /// 自动追踪对象生命周期
    func enableMemoryTracking(identifier: String? = nil) {
        MemoryLeakDetector.shared.trackObject(self, identifier: identifier)
    }
}

// MARK: - 观察者模式内存安全包装器

/// 安全的通知观察者包装器
class SafeNotificationObserver {
    private weak var observer: NSObjectProtocol?
    private let notificationCenter: NotificationCenter
    
    init(notificationCenter: NotificationCenter = .default) {
        self.notificationCenter = notificationCenter
    }
    
    func observe(
        name: Notification.Name,
        object: Any? = nil,
        queue: OperationQueue? = nil,
        using block: @escaping (Notification) -> Void
    ) {
        observer = notificationCenter.addObserver(
            forName: name,
            object: object,
            queue: queue,
            using: block
        )
    }
    
    deinit {
        if let observer = observer {
            notificationCenter.removeObserver(observer)
        }
    }
}

// MARK: - Timer 内存安全包装器

/// 安全的Timer包装器，防止循环引用
class SafeTimer {
    private var timer: Timer?
    private weak var target: AnyObject?
    
    init() {}
    
    func scheduledTimer(
        withTimeInterval interval: TimeInterval,
        target: AnyObject,
        selector: Selector,
        userInfo: Any? = nil,
        repeats: Bool
    ) {
        self.target = target
        timer = Timer.scheduledTimer(
            timeInterval: interval,
            target: self,
            selector: #selector(timerFired(_:)),
            userInfo: TimerUserInfo(target: target, selector: selector, userInfo: userInfo),
            repeats: repeats
        )
    }
    
    @objc private func timerFired(_ timer: Timer) {
        guard let target = self.target,
              let userInfo = timer.userInfo as? TimerUserInfo else {
            timer.invalidate()
            return
        }
        
        _ = target.perform(userInfo.selector, with: userInfo.userInfo)
    }
    
    func invalidate() {
        timer?.invalidate()
        timer = nil
        target = nil
    }
    
    deinit {
        invalidate()
    }
}

private class TimerUserInfo {
    weak var target: AnyObject?
    let selector: Selector
    let userInfo: Any?
    
    init(target: AnyObject, selector: Selector, userInfo: Any?) {
        self.target = target
        self.selector = selector
        self.userInfo = userInfo
    }
}
