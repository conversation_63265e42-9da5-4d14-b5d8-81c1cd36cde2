import Foundation
import UIKit
import CoreLocation
import MapKit

/// 处理Apple Maps导航的工具类
class NavigationAppHandler {

    static let shared = NavigationAppHandler()

    private init() {}

    /// 使用Apple Maps打开导航
    func openNavigation(to destination: CLLocationCoordinate2D, name: String = "") {
        print("[DEBUG] NavigationAppHandler - 使用Apple Maps打开导航")
        openAppleMaps(to: destination, name: name)
    }

    /// 打开Apple Maps导航
    private func openAppleMaps(to destination: CLLocationCoordinate2D, name: String) {
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: destination, addressDictionary: nil))
        if !name.isEmpty {
            mapItem.name = name
        }
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving])
    }

    /// 使用Apple Maps打开多点导航
    func openNavigation(destinations: [CLLocationCoordinate2D], names: [String] = []) {
        print("[DEBUG] NavigationAppHandler - 使用Apple Maps打开多点导航")
        openAppleMapsMultiDestinations(destinations: destinations, names: names)
    }

    /// 打开Apple Maps多点导航
    private func openAppleMapsMultiDestinations(destinations: [CLLocationCoordinate2D], names: [String]) {
        guard !destinations.isEmpty else {
            print("[ERROR] NavigationAppHandler - 没有提供任何地址点进行Apple Maps导航")
            return
        }

        print("[DEBUG] NavigationAppHandler - 准备Apple Maps多点导航，共 \(destinations.count) 个地址点")

        // Apple Maps最多支持14个停靠点
        let maxStops = 14
        // 限制地址数量
        let validDestinations = Array(destinations.prefix(maxStops))
        let validNames = Array(names.prefix(min(maxStops, names.count)))

        // 记录实际使用的地址点数
        print("[INFO] NavigationAppHandler - 实际发送 \(validDestinations.count) 个地址点到Apple Maps")

        // 方法1: 使用URL Scheme导航（更可靠，支持多点）
        // 构建URL
        var urlString = "maps://?dirflg=d" // dirflg=d表示驾车导航模式

        // 添加第一个地址作为主要目的地
        if let first = validDestinations.first {
            let firstCoord = "\(first.latitude),\(first.longitude)"
            urlString += "&daddr=\(firstCoord)"

            // 如果有名称，添加给第一个点
            if validNames.count > 0 && !validNames[0].isEmpty {
                let encodedName = validNames[0].addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
                if !encodedName.isEmpty {
                    urlString += " (\(encodedName))"
                }
            }
        }

        // 添加其余地址作为额外停靠点
        for i in 1..<validDestinations.count {
            let coord = validDestinations[i]
            let coordStr = "\(coord.latitude),\(coord.longitude)"

            // 添加坐标和名称（如果有）
            var stopString = "+to:\(coordStr)"

            // 如果有对应的名称，添加到坐标后面
            if i < validNames.count && !validNames[i].isEmpty {
                let encodedName = validNames[i].addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
                if !encodedName.isEmpty {
                    stopString += " (\(encodedName))"
                }
            }

            urlString += stopString
        }

        print("[DEBUG] NavigationAppHandler - Apple Maps导航URL: \(urlString)")

        // 尝试打开Maps应用
        if let url = URL(string: urlString) {
            print("[DEBUG] NavigationAppHandler - 尝试使用URL Scheme打开Apple Maps")
            UIApplication.shared.open(url, options: [:]) { success in
                print("[DEBUG] NavigationAppHandler - Apple Maps URL Scheme导航结果: \(success ? "成功" : "失败")")

                // 如果URL方式失败，尝试使用MKMapItem API
                if !success {
                    print("[DEBUG] NavigationAppHandler - URL Scheme方法失败，尝试使用MKMapItem API备用方法")
                    self.openAppleMapsUsingMKMapItem(destinations: validDestinations, names: validNames)
                }
            }
        } else {
            print("[ERROR] NavigationAppHandler - 无法构建有效的Apple Maps URL")
            // 如果URL无效，使用MKMapItem API作为备用方法
            openAppleMapsUsingMKMapItem(destinations: validDestinations, names: validNames)
        }
    }

    /// 使用MKMapItem API打开Apple Maps多点导航（备用方法）
    private func openAppleMapsUsingMKMapItem(destinations: [CLLocationCoordinate2D], names: [String]) {
        print("[DEBUG] NavigationAppHandler - 尝试使用MKMapItem API方法进行Apple Maps多点导航")

        // 创建MapItem数组
        var mapItems: [MKMapItem] = []

        // 为每个目的地创建MapItem
        for (index, destination) in destinations.enumerated() {
            let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: destination, addressDictionary: nil))

            // 设置名称（如果有）
            if index < names.count && !names[index].isEmpty {
                mapItem.name = names[index]
            }

            mapItems.append(mapItem)
        }

        // 确保有足够的地址点
        guard !mapItems.isEmpty else {
            print("[ERROR] NavigationAppHandler - 无法创建有效的Apple Maps导航项")
            return
        }

        print("[DEBUG] NavigationAppHandler - 使用MKMapItem API打开Apple Maps，共 \(mapItems.count) 个地址点")

        // 打开Apple Maps进行导航
        MKMapItem.openMaps(
            with: mapItems,
            launchOptions: [
                MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving,
                MKLaunchOptionsShowsTrafficKey: true
            ]
        )
    }
}
