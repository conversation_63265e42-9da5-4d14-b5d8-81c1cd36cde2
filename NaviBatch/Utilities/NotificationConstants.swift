import Foundation

// 应用中使用的通知常量
extension Notification.Name {
    // 配送点状态变更通知
    static let deliveryStatusChanged = Notification.Name("deliveryStatusChanged")
    
    // 分组内容变更通知
    static let groupContentChanged = Notification.Name("groupContentChanged")
    
    // 已经存在的通知，不要修改
    static let routeDataChanged = Notification.Name("RouteDataChanged")
    static let forceRefreshRouteView = Notification.Name("ForceRefreshRouteView")
    static let selectedRouteChanged = Notification.Name("SelectedRouteChanged")
    static let closeMenuAndShowRoute = Notification.Name("CloseMenuAndShowRoute")
    static let restoreRouteBottomSheet = Notification.Name("RestoreRouteBottomSheet")
    static let openBatchAddressFixSheet = Notification.Name("OpenBatchAddressFixSheet")
    static let showSubscriptionView = Notification.Name("ShowSubscriptionView")
    static let navigateToGroup = Notification.Name("NavigateToGroup")
    static let resetBottomSheetPosition = Notification.Name("ResetBottomSheetPosition")
} 