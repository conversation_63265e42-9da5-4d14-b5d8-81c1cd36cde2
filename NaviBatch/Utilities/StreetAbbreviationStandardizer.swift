import Foundation

/// 街道简称标准化器
/// 确保所有delivery point中的街道简称使用统一的首字母大写格式（如Ave, Blvd, St）
/// 而不是全大写（AVE, BLVD, ST）或全小写（ave, blvd, st）
class StreetAbbreviationStandardizer {
    
    // 私有初始化，防止实例化
    private init() {}
    
    // MARK: - 标准街道简称映射
    
    /// 标准街道简称映射表 - 统一使用首字母大写格式
    /// 基于USPS标准，但格式化为首字母大写以提高可读性
    private static let standardAbbreviations: [String: String] = [
        // Avenue 相关
        "AVENUE": "Ave",
        "AVE": "Ave",
        "ave": "Ave",
        "Avenue": "Ave",
        
        // Street 相关
        "STREET": "St",
        "ST": "St",
        "st": "St",
        "Street": "St",
        
        // Boulevard 相关
        "BOULEVARD": "Blvd",
        "BLVD": "Blvd",
        "blvd": "Blvd",
        "Boulevard": "Blvd",
        
        // Road 相关
        "ROAD": "Rd",
        "RD": "Rd",
        "rd": "Rd",
        "Road": "Rd",
        
        // Drive 相关
        "DRIVE": "Dr",
        "DR": "Dr",
        "dr": "Dr",
        "Drive": "Dr",
        
        // Lane 相关
        "LANE": "Ln",
        "LN": "Ln",
        "ln": "Ln",
        "Lane": "Ln",
        
        // Court 相关
        "COURT": "Ct",
        "CT": "Ct",
        "ct": "Ct",
        "Court": "Ct",
        
        // Place 相关
        "PLACE": "Pl",
        "PL": "Pl",
        "pl": "Pl",
        "Place": "Pl",
        
        // Circle 相关
        "CIRCLE": "Cir",
        "CIR": "Cir",
        "cir": "Cir",
        "Circle": "Cir",
        
        // Parkway 相关
        "PARKWAY": "Pkwy",
        "PKWY": "Pkwy",
        "pkwy": "Pkwy",
        "Parkway": "Pkwy",
        
        // Highway 相关
        "HIGHWAY": "Hwy",
        "HWY": "Hwy",
        "hwy": "Hwy",
        "Highway": "Hwy",
        
        // Terrace 相关
        "TERRACE": "Ter",
        "TER": "Ter",
        "ter": "Ter",
        "Terrace": "Ter",
        
        // Square 相关
        "SQUARE": "Sq",
        "SQ": "Sq",
        "sq": "Sq",
        "Square": "Sq",
        
        // Way 相关
        "WAY": "Way",
        "way": "Way",
        "Way": "Way",
        
        // Trail 相关
        "TRAIL": "Trl",
        "TRL": "Trl",
        "trl": "Trl",
        "Trail": "Trl",
        
        // Extension 相关
        "EXTENSION": "Ext",
        "EXT": "Ext",
        "ext": "Ext",
        "Extension": "Ext",
        
        // Center 相关
        "CENTER": "Ctr",
        "CTR": "Ctr",
        "ctr": "Ctr",
        "Center": "Ctr",
        
        // Alley 相关
        "ALLEY": "Aly",
        "ALY": "Aly",
        "aly": "Aly",
        "Alley": "Aly"
    ]
    
    // MARK: - 主要标准化方法
    
    /// 标准化地址中的街道简称为首字母大写格式
    /// - Parameter address: 原始地址
    /// - Returns: 标准化后的地址
    static func standardizeStreetAbbreviations(_ address: String) -> String {
        var standardized = address
        
        // 使用词边界正则表达式确保只替换完整的单词
        for (variant, standard) in standardAbbreviations {
            let pattern = "\\b\(NSRegularExpression.escapedPattern(for: variant))\\b"
            standardized = standardized.replacingOccurrences(
                of: pattern,
                with: standard,
                options: .regularExpression
            )
        }
        
        return standardized
    }
    
    /// 标准化街道地址部分（通常是地址的第一个组件）
    /// - Parameter streetAddress: 街道地址字符串
    /// - Returns: 标准化后的街道地址
    static func standardizeStreetAddress(_ streetAddress: String) -> String {
        return standardizeStreetAbbreviations(streetAddress)
    }
    
    /// 检查地址是否需要街道简称标准化
    /// - Parameter address: 地址字符串
    /// - Returns: 如果需要标准化则返回true
    static func needsStandardization(_ address: String) -> Bool {
        for variant in standardAbbreviations.keys {
            let pattern = "\\b\(NSRegularExpression.escapedPattern(for: variant))\\b"
            if address.range(of: pattern, options: .regularExpression) != nil {
                return true
            }
        }
        return false
    }
    
    /// 获取标准化的街道简称
    /// - Parameter abbreviation: 原始简称
    /// - Returns: 标准化的简称，如果不在映射表中则返回原始值
    static func getStandardAbbreviation(_ abbreviation: String) -> String {
        return standardAbbreviations[abbreviation] ?? abbreviation
    }
    
    // MARK: - 批量处理方法
    
    /// 批量标准化地址数组
    /// - Parameter addresses: 地址数组
    /// - Returns: 标准化后的地址数组
    static func standardizeAddresses(_ addresses: [String]) -> [String] {
        return addresses.map { standardizeStreetAbbreviations($0) }
    }
}

// MARK: - String扩展
extension String {
    /// 标准化街道简称为首字母大写格式
    /// - Returns: 标准化后的地址字符串
    func standardizedStreetAbbreviations() -> String {
        return StreetAbbreviationStandardizer.standardizeStreetAbbreviations(self)
    }
    
    /// 检查是否需要街道简称标准化
    /// - Returns: 如果需要标准化则返回true
    func needsStreetAbbreviationStandardization() -> Bool {
        return StreetAbbreviationStandardizer.needsStandardization(self)
    }
}
