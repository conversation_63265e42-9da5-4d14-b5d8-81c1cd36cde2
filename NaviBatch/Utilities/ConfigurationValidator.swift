import Foundation

// MARK: - 配置验证器
// 用于验证和显示当前使用的配置来源

@MainActor
class ConfigurationValidator {
    
    /// 验证并显示当前配置状态
    static func validateAndLogConfiguration() {
        let configService = ConfigService.shared
        
        print("\n🔍 ===== NaviBatch配置验证 =====")
        
        if let config = configService.config {
            print("✅ 配置状态: 已加载")
            print("📊 配置详情:")
            print("  🔑 API密钥前缀: \(String(config.ai.openRouter.apiKey.prefix(20)))...")
            print("  🌐 API URL: \(config.ai.openRouter.baseURL)")
            print("  🤖 模型数量: \(config.ai.openRouter.gemmaModels.count)")
            
            for (index, model) in config.ai.openRouter.gemmaModels.enumerated() {
                print("    \(index + 1). \(model)")
            }
            
            print("  📅 最后更新: \(config.lastUpdated)")
            print("  🏷️ 版本: \(config.version)")
            
            // 验证配置来源
            validateConfigurationSource(config)
            
        } else {
            print("❌ 配置状态: 未加载")
            print("⚠️ 将使用备用配置")
        }
        
        print("================================\n")
    }
    
    /// 验证配置来源
    private static func validateConfigurationSource(_ config: AppConfig) {
        print("\n🔍 配置来源验证:")
        
        // 检查API密钥
        if config.ai.openRouter.apiKey.hasPrefix("sk-or-v1-") {
            print("  ✅ API密钥: 来自Cloudflare动态配置")
        } else {
            print("  ⚠️ API密钥: 可能使用备用配置")
        }
        
        // 检查模型数量（我们配置了2个模型）
        if config.ai.openRouter.gemmaModels.count == 2 {
            print("  ✅ 模型列表: 使用更新后的2个模型配置")
        } else {
            print("  ⚠️ 模型列表: 模型数量异常 (\(config.ai.openRouter.gemmaModels.count))")
        }
        
        // 检查特定模型
        let expectedModels = ["google/gemma-3-27b-it:free", "google/gemma-3-12b-it:free"]
        let hasExpectedModels = expectedModels.allSatisfy { config.ai.openRouter.gemmaModels.contains($0) }
        
        if hasExpectedModels {
            print("  ✅ 模型内容: 包含预期的Gemma 3模型")
        } else {
            print("  ⚠️ 模型内容: 模型列表与预期不符")
        }
        
        // 检查URL
        if config.ai.openRouter.baseURL == "https://openrouter.ai/api/v1/chat/completions" {
            print("  ✅ API URL: 正确的OpenRouter端点")
        } else {
            print("  ⚠️ API URL: 非标准端点")
        }
        
        // 总结
        if config.ai.openRouter.apiKey.hasPrefix("sk-or-v1-") && 
           config.ai.openRouter.gemmaModels.count == 2 && 
           hasExpectedModels {
            print("\n🎉 结论: 成功使用Cloudflare动态配置！")
        } else {
            print("\n⚠️ 结论: 可能使用了备用配置或配置异常")
        }
    }
    
    /// 异步验证配置（包含刷新）
    static func validateConfigurationAsync() async {
        let configService = ConfigService.shared
        
        print("🔄 正在刷新Cloudflare配置...")
        configService.refreshConfig()
        
        // 等待配置加载
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒
        
        validateAndLogConfiguration()
    }
    
    /// 监控配置变化
    static func startConfigurationMonitoring() {
        print("👀 开始监控配置变化...")

        // 这里可以添加配置变化的监听逻辑
        // 例如定期检查配置是否更新

        Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { _ in
            print("⏰ 定期配置检查...")
            Task { @MainActor in
                validateAndLogConfiguration()
            }
        }
    }
}

// MARK: - 使用说明
/*
 在应用启动时调用以验证配置：
 
 1. 同步验证（使用当前缓存的配置）:
    ConfigurationValidator.validateAndLogConfiguration()
 
 2. 异步验证（刷新后验证）:
    Task {
        await ConfigurationValidator.validateConfigurationAsync()
    }
 
 3. 开始监控:
    ConfigurationValidator.startConfigurationMonitoring()
 
 预期输出示例：
 ✅ 配置状态: 已加载
 📊 配置详情:
   🔑 API密钥前缀: sk-or-v1-4a0c26a0a12...
   🌐 API URL: https://openrouter.ai/api/v1/chat/completions
   🤖 模型数量: 2
     1. google/gemma-3-27b-it:free
     2. google/gemma-3-12b-it:free
   📅 最后更新: 2024-01-20T10:00:00Z
   🏷️ 版本: 1.0.0
 
 🔍 配置来源验证:
   ✅ API密钥: 来自Cloudflare动态配置
   ✅ 模型列表: 使用更新后的2个模型配置
   ✅ 模型内容: 包含预期的Gemma 3模型
   ✅ API URL: 正确的OpenRouter端点
 
 🎉 结论: 成功使用Cloudflare动态配置！
 */
