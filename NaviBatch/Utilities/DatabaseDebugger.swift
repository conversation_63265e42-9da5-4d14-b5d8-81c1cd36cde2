import Foundation
import SwiftData
import os.log

// 导入模型类
@_exported import class Foundation.DateFormatter
@_exported import struct Foundation.Date
@_exported import struct Foundation.UUID

// 导入应用程序的模型类
// SwiftData模型类已经通过SwiftData导入

// 全局日志函数
private let logger = os.Logger(subsystem: "com.jasonkwok.NaviBatch", category: "DatabaseDebugger")

/// 数据库调试工具类
class DatabaseDebugger {
    // 类内日志函数
    private static func logInfo(_ message: String) {
        print("[INFO] \(message)")
    }

    private static func logError(_ message: String) {
        print("[ERROR] \(message)")
    }

    /// 打印数据库中的所有路线及其详细信息
    @MainActor
    static func printAllRoutes() async {
        logInfo("===== 开始打印数据库中的所有路线 =====")

        await printDatabaseContent()
    }

    /// 打印数据库中的所有地址
    @MainActor
    static func printAllAddresses() async {
        logInfo("===== 开始打印数据库中的所有地址 =====")

        await printDatabaseContent()
    }

    /// 打印数据库内容
    @MainActor
    private static func printDatabaseContent() async {

        do {
            // 使用共享容器
            let container = getPersistentContainer()
            let context = container.mainContext

            // 打印数据库路径
            if let url = container.configurations.first?.url {
                logInfo("数据库路径: \(url.path)")
            }

            // 获取所有路线
            let routeDescriptor = FetchDescriptor<Route>()
            let routes = try context.fetch(routeDescriptor)

            logInfo("数据库中共有 \(routes.count) 条路线记录")

            // 打印每条路线的详细信息
            for (index, route) in routes.enumerated() {
                logInfo("路线 [\(index+1)/\(routes.count)] ==================")
                logInfo("  ID: \(route.id.uuidString)")
                logInfo("  名称: \(route.name)")
                logInfo("  创建时间: \(formatDate(route.createdAt))")
                logInfo("  地址点数量: \(route.points.count)")

                // 打印路线中的每个地址点
                for (pointIndex, point) in route.points.enumerated() {
                    logInfo("  地址点 [\(pointIndex+1)/\(route.points.count)] ----------")
                    logInfo("    ID: \(point.id.uuidString)")
                    logInfo("    编号: \(point.sort_number), 排序号: \(point.sorted_number)")
                    logInfo("    地址: \(point.primaryAddress)")
                    logInfo("    坐标: (\(point.latitude), \(point.longitude))")
                    logInfo("    是起点: \(point.isStartPoint)")
                    logInfo("    是终点: \(point.isEndPoint)")
                    logInfo("    包裹数量: \(point.packageCount)")
                    logInfo("    状态: \(point.deliveryStatus.rawValue)")
                    if let notes = point.notes {
                        logInfo("    备注: \(notes)")
                    }
                    if point.isAssignedToGroup {
                        logInfo("    分组编号: \(point.assignedGroupNumber ?? 0)")
                    }
                }
            }

            // 获取所有分组
            let groupDescriptor = FetchDescriptor<DeliveryGroup>()
            let groups = try context.fetch(groupDescriptor)

            logInfo("数据库中共有 \(groups.count) 个配送分组")

            // 打印每个分组的详细信息
            for (index, group) in groups.enumerated() {
                logInfo("分组 [\(index+1)/\(groups.count)] ==================")
                logInfo("  ID: \(group.id.uuidString)")
                logInfo("  名称: \(group.name)")
                logInfo("  分组编号: \(group.groupNumber)")
                logInfo("  创建时间: \(formatDate(group.createdAt))")
                logInfo("  地址点数量: \(group.points.count)")
            }

            // 获取所有保存的地址
            let addressDescriptor = FetchDescriptor<SavedAddress>()
            let addresses = try context.fetch(addressDescriptor)

            logInfo("数据库中共有 \(addresses.count) 个保存的地址")

            // 打印每个保存的地址的详细信息
            for (index, address) in addresses.enumerated() {
                logInfo("保存的地址 [\(index+1)/\(addresses.count)] ==================")
                logInfo("  ID: \(address.id.uuidString)")
                logInfo("  地址: \(address.address)")
                logInfo("  坐标: (\(address.latitude), \(address.longitude))")
                logInfo("  创建时间: \(formatDate(address.createdAt))")
                logInfo("  是否收藏: \(address.isFavorite)")
                if let notes = address.notes {
                    logInfo("  备注: \(notes)")
                }
                if let phoneNumber = address.phoneNumber {
                    logInfo("  电话: \(phoneNumber)")
                }
            }

            logInfo("===== 数据库内容打印完成 =====")
            return

        } catch {
            logError("打印数据库内容失败: \(error.localizedDescription)")
        }
    }

    /// 格式化日期为可读字符串
    private static func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        return formatter.string(from: date)
    }
}
