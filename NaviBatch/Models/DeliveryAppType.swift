//
//  DeliveryAppType.swift
//  NaviBatch
//
//  Created by NaviBatch on 2024/12/19.
//

import Foundation
import SwiftUI

/// 配送应用类型枚举
enum DeliveryAppType: String, CaseIterable, Codable {
    // 通用类型
    case justPhoto = "just_photo"  // 纯图片识别
    case manual = "manual"  // 手动输入

    // 美国快递
    case speedx = "speedx"         // SpeedX配送 (� 核心价值：停靠点号码=第三方排序号，绝对不能错/缺失/修改)
    case gofo = "gofo"             // GoFo配送 (排第一)
    case uniuni = "uniuni"         // UNIUNI配送
    case amazonFlex = "amazon_flex"
    case imile = "imile"           // iMile (美国+澳洲)
    case ldsEpod = "lds_epod"      // LDS EPOD配送
    case piggy = "piggy"           // PIGGY配送
    case ywe = "ywe"               // YWE配送

    // 澳洲快递 (目前只有iMile)

    // 餐饮配送（暂时保留）
    case uberEats = "ubereats"
    case doorDash = "doordash"
    case menulog = "menulog"
    case other = "other"

    /// 显示名称
    var displayName: String {
        switch self {
        case .justPhoto:
            return "just_photo".localized
        case .speedx:
            return "SpeedX"
        case .amazonFlex:
            return "Amazon Flex"
        case .imile:
            return "iMile"
        case .ldsEpod:
            return "LDS EPOD"
        case .piggy:
            return "PIGGY"
        case .uniuni:
            return "UNIUNI"
        case .gofo:
            return "GoFo"
        case .ywe:
            return "YWE"
        case .uberEats:
            return "UberEats"
        case .doorDash:
            return "DoorDash"
        case .menulog:
            return "Menulog"
        case .other:
            return "其他"
        case .manual:
            return "手动输入"
        }
    }

    /// 应用主色调
    var primaryColor: Color {
        switch self {
        case .justPhoto:
            return Color.indigo
        case .amazonFlex:
            return Color.orange
        case .imile:
            return Color.blue
        case .ldsEpod:
            return Color.teal
        case .piggy:
            return Color.pink
        case .uniuni:
            return Color.cyan
        case .gofo:
            return Color.yellow
        case .ywe:
            return Color.blue
        case .speedx:
            return Color.blue
        case .uberEats:
            return Color.green
        case .doorDash:
            return Color.red
        case .menulog:
            return Color.orange
        case .other:
            return Color.gray
        case .manual:
            return Color.purple
        }
    }

    /// 应用图标名称（SF Symbols）
    var iconName: String {
        switch self {
        case .justPhoto:
            return "photo"
        case .amazonFlex:
            return "shippingbox.fill"
        case .imile:
            return "airplane.departure"
        case .ldsEpod:
            return "truck.box.fill"
        case .piggy:
            return "car.fill"
        case .uniuni:
            return "location.fill"
        case .gofo:
            return "car.2.fill"
        case .ywe:
            return "shippingbox.fill"
        case .speedx:
            return "bolt.fill" // 🚀 闪电图标体现SpeedX的高速优化特性
        case .uberEats:
            return "fork.knife"
        case .doorDash:
            return "takeoutbag.and.cup.and.straw.fill"
        case .menulog:
            return "bag.fill"
        case .other:
            return "questionmark.app.fill"
        case .manual:
            return "hand.point.up.left.fill"
        }
    }

    /// 追踪号码格式说明
    var trackingFormat: String {
        switch self {
        case .justPhoto:
            return "通用图片识别 (自动检测格式)"
        case .amazonFlex:
            return "D系列 + 数字追踪号 (如: D82-6110124724982)"
        case .imile:
            return "iMile追踪号格式"
        case .ldsEpod:
            return "CNUSUP + 11位数字 (如: CNUSUP00011738482)"
        case .piggy:
            return "PG + 11位数字 或 14位纯数字 (如: PG10005375906)"
        case .uniuni:
            return "UUS + 16位数字 (如: UUS56D056436296171)"
        case .gofo:
            return "GoFo追踪号格式 (自动识别)"
        case .ywe:
            return "YWE追踪号格式 (自动识别)"
        case .speedx:
            return "SPXSF + 14位数字 (如: SPXSF00567490961577) 🚀 停靠点号码专用优化"
        case .uberEats:
            return "UberEats订单号"
        case .doorDash:
            return "DoorDash订单号"
        case .menulog:
            return "Menulog订单号"
        case .other:
            return "其他格式"
        case .manual:
            return "自定义格式"
        }
    }

    /// 是否支持批量导入
    var supportsBatchImport: Bool {
        switch self {
        case .justPhoto:
            return true
        case .amazonFlex, .imile, .ldsEpod, .piggy, .uniuni, .gofo, .ywe, .speedx:
            return true
        case .uberEats, .doorDash, .menulog:
            return true
        case .other, .manual:
            return false
        }
    }

    /// 🎯 统一AI Only模式：是否应该使用纯AI处理（跳过OCR）
    var shouldUseAIOnly: Bool {
        switch self {
        case .speedx, .gofo:
            return true  // 已经优化的快递
        case .amazonFlex, .imile, .ldsEpod, .piggy, .uniuni, .ywe:
            return true  // 统一使用AI Only模式
        case .justPhoto:
            return false // 通用图片识别保持灵活性
        case .uberEats, .doorDash, .menulog, .other, .manual:
            return false // 其他类型保持默认行为
        }
    }

    /// 获取应用类型的地区分类
    var region: DeliveryRegion {
        switch self {
        case .amazonFlex, .gofo, .ldsEpod, .piggy, .uniuni, .ywe, .speedx:
            return .usa
        case .imile:
            return .universal  // iMile服务美国+澳洲，设为通用
        case .justPhoto, .manual, .uberEats, .doorDash, .menulog, .other:
            return .universal
        }
    }

    /// 获取地区内的快递公司类型（排除Just Photo）
    static func courierTypesForRegion(_ region: DeliveryRegion) -> [DeliveryAppType] {
        switch region {
        case .usa:
            return [.speedx, .gofo, .uniuni, .amazonFlex, .imile, .ldsEpod, .piggy, .ywe] // SpeedX排第一：高度优化
        case .australia:
            return [.imile]  // 澳洲目前只有iMile
        case .universal:
            return []
        }
    }
}

/// 配送地区枚举
enum DeliveryRegion: String, CaseIterable {
    case usa = "usa"
    case australia = "australia"
    case universal = "universal"

    var displayName: String {
        switch self {
        case .usa:
            return "united_states_delivery".localized
        case .australia:
            return "australia_delivery".localized
        case .universal:
            return "universal_delivery".localized
        }
    }
}

/// 应用类型标签视图
struct DeliveryAppTypeTag: View {
    let appType: DeliveryAppType
    let size: TagSize

    enum TagSize {
        case small, medium, large

        var fontSize: CGFloat {
            switch self {
            case .small: return 14
            case .medium: return 12
            case .large: return 14
            }
        }

        var iconSize: CGFloat {
            switch self {
            case .small: return 14
            case .medium: return 14
            case .large: return 16
            }
        }

        var padding: CGFloat {
            switch self {
            case .small: return 6 // 🎯 调整为6以适配36pt高度
            case .medium: return 6
            case .large: return 8
            }
        }
    }

    var body: some View {
        HStack(spacing: 4) {
            // 移除所有图标显示
            Text(appType.displayName)
                .font(.system(size: size.fontSize, weight: .medium))
                .foregroundColor(.white)
        }
        .padding(.horizontal, size.padding + 4)
        .padding(.vertical, size.padding)
        .background(Color.black)
        .cornerRadius(6)
        .frame(height: 36)
    }

    // Dark Mode 自适应背景颜色
    private var adaptiveBackgroundColor: Color {
        switch appType {
        case .gofo:
            // GoFo 黄色在 Dark Mode 下调整为更深的金色
            return Color.adaptiveGoFo
        case .amazonFlex:
            // Amazon Flex 橙色保持，但在 Dark Mode 下稍微调暗
            return Color.adaptiveAmazonFlex
        case .imile, .ywe, .speedx:
            // 蓝色系在 Dark Mode 下使用自适应蓝色
            return Color.adaptivePrimaryIcon
        case .ldsEpod:
            // 青绿色保持
            return appType.primaryColor
        case .piggy:
            // 粉色保持
            return appType.primaryColor
        case .uniuni:
            // 青色保持
            return appType.primaryColor
        case .justPhoto:
            // 靛蓝色保持
            return appType.primaryColor
        default:
            // 其他颜色保持原样
            return appType.primaryColor
        }
    }
}

#Preview {
    VStack(spacing: 10) {
        DeliveryAppTypeTag(appType: .amazonFlex, size: .large)
        DeliveryAppTypeTag(appType: .imile, size: .medium)
        DeliveryAppTypeTag(appType: .uberEats, size: .small)
    }
    .padding()
}
