import Foundation
import MapKit
import CoreLocation

/// MKRoute包装类，用于创建自定义路线对象
class MKRouteWrapper: MKRoute {
    private let _polyline: MKPolyline
    private let _distance: CLLocationDistance
    private let _expectedTravelTime: TimeInterval
    
    init(polyline: MKPolyline, distance: CLLocationDistance, expectedTravelTime: TimeInterval) {
        self._polyline = polyline
        self._distance = distance
        self._expectedTravelTime = expectedTravelTime
        super.init()
    }
    
    override var polyline: MKPolyline {
        return _polyline
    }
    
    override var distance: CLLocationDistance {
        return _distance
    }
    
    override var expectedTravelTime: TimeInterval {
        return _expectedTravelTime
    }
    
    override var name: String {
        return "合并路线"
    }
    
    override var advisoryNotices: [String] {
        return []
    }
    
    override var transportType: MKDirectionsTransportType {
        return .automobile
    }
    
    override var steps: [MKRoute.Step] {
        return []
    }
}
