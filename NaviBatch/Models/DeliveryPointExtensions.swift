import Foundation
import CoreLocation
import SwiftData
import Combine
import SwiftUI

// DeliveryPoint扩展，用于处理地理编码和错误处理
extension DeliveryPoint {
    // 地理编码警告类型
    enum GeocodingWarningType: String {
        case none = "none"                     // 无警告
        case partialMatch = "partialMatch"     // 部分匹配
        case lowAccuracy = "lowAccuracy"       // 低精度
        case outsideRegion = "outsideRegion"   // 在目标区域外
        case coordinateMissing = "coordinateMissing" // 坐标缺失
        case apiLimitReached = "apiLimitReached" // API限制
        case networkError = "networkError"     // 网络错误
        case unknownError = "unknownError"     // 未知错误

        // 用户友好的警告消息
        var message: String {
            switch self {
            case .none:
                return ""
            case .partialMatch:
                return "address_partial_match".localized
            case .lowAccuracy:
                return "low_accuracy_address".localized
            case .outsideRegion:
                return "address_outside_region".localized
            case .coordinateMissing:
                return "cannot_get_address_coordinates".localized
            case .apiLimitReached:
                return "api_limit_reached".localized
            case .networkError:
                return "network_error".localized
            case .unknownError:
                return "unknown_error".localized
            }
        }

        // 警告图标
        var iconName: String {
            switch self {
            case .none:
                return ""
            case .partialMatch, .lowAccuracy:
                return "exclamationmark.triangle"
            case .outsideRegion:
                return "mappin.slash"
            case .coordinateMissing:
                return "location.slash"
            case .apiLimitReached:
                return "clock"
            case .networkError:
                return "wifi.slash"
            case .unknownError:
                return "questionmark.circle"
            }
        }

        // 警告颜色
        var color: Color {
            switch self {
            case .none:
                return .clear
            case .partialMatch, .lowAccuracy, .outsideRegion:
                return .orange
            case .coordinateMissing, .apiLimitReached, .networkError, .unknownError:
                return .red
            }
        }
    }

    // 获取当前警告类型
    var geocodingWarningType: GeocodingWarningType {
        guard let warning = geocodingWarning else {
            return .none
        }
        return GeocodingWarningType(rawValue: warning) ?? .unknownError
    }

    // 设置警告类型
    func setGeocodingWarning(_ type: GeocodingWarningType) {
        if type == .none {
            geocodingWarning = nil
        } else {
            geocodingWarning = type.rawValue
        }
    }

    // 使用GeocodingService更新坐标
    func updateCoordinates(completion: @escaping (Bool) -> Void) {
        // 使用GeocodingService获取坐标
        GeocodingService.shared.geocodeAddress(primaryAddress)
            .first()
            .sink { result in
                if let coordinate = result.coordinate {
                    // 更新坐标
                    self.latitude = coordinate.latitude
                    self.longitude = coordinate.longitude

                    // 根据结果状态设置警告
                    switch result.status {
                    case .success:
                        self.setGeocodingWarning(.none)
                    case .partialSuccess:
                        self.setGeocodingWarning(.partialMatch)
                    case .failed:
                        self.setGeocodingWarning(.unknownError)
                    case .rateLimited:
                        self.setGeocodingWarning(.apiLimitReached)
                    case .networkError:
                        self.setGeocodingWarning(.networkError)
                    case .timeout:
                        self.setGeocodingWarning(.networkError)
                    case .invalidAddress:
                        self.setGeocodingWarning(.lowAccuracy)
                    case .notFound:
                        self.setGeocodingWarning(.coordinateMissing)
                    }

                    completion(true)
                } else {
                    // 设置坐标缺失警告
                    self.setGeocodingWarning(.coordinateMissing)
                    completion(false)
                }
            }
            .store(in: &DeliveryPoint.cancellables)
    }

    // 批量更新坐标
    static func batchUpdateCoordinates(points: [DeliveryPoint], completion: @escaping ([DeliveryPoint], [DeliveryPoint]) -> Void) {
        let addresses = points.map { $0.primaryAddress }

        // 使用GeocodingService批量获取坐标
        GeocodingService.shared.geocodeBatch(addresses)
            .first()
            .sink { results in
                var successPoints: [DeliveryPoint] = []
                var failedPoints: [DeliveryPoint] = []

                // 更新每个点的坐标和警告
                for (index, result) in results.enumerated() {
                    if index < points.count {
                        let point = points[index]

                        if let coordinate = result.coordinate {
                            // 更新坐标
                            point.latitude = coordinate.latitude
                            point.longitude = coordinate.longitude

                            // 根据结果状态设置警告
                            switch result.status {
                            case .success:
                                point.setGeocodingWarning(.none)
                            case .partialSuccess:
                                point.setGeocodingWarning(.partialMatch)
                            case .failed:
                                point.setGeocodingWarning(.unknownError)
                            case .rateLimited:
                                point.setGeocodingWarning(.apiLimitReached)
                            case .networkError:
                                point.setGeocodingWarning(.networkError)
                            case .timeout:
                                point.setGeocodingWarning(.networkError)
                            case .invalidAddress:
                                point.setGeocodingWarning(.lowAccuracy)
                            case .notFound:
                                point.setGeocodingWarning(.coordinateMissing)
                            }

                            successPoints.append(point)
                        } else {
                            // 设置坐标缺失警告
                            point.setGeocodingWarning(.coordinateMissing)
                            failedPoints.append(point)
                        }
                    }
                }

                completion(successPoints, failedPoints)
            }
            .store(in: &DeliveryPoint.cancellables)
    }

    // 获取用户友好的地址验证问题描述
    var validationIssueDescription: String? {
        // 🚨 优先检查明显错误的坐标（如香港坐标用于美国地址）
        if hasObviouslyWrongCoordinates {
            return "approximate_location".localized
        }

        // 优先显示地理编码警告
        if let warning = geocodingWarning, !warning.isEmpty {
            let warningType = geocodingWarningType
            if warningType != .none {
                return warningType.message
            }
        }

        // 检查位置验证状态 - 不显示unknown状态
        let validationStatus = LocationValidationStatus(rawValue: locationValidationStatus) ?? .unknown
        if validationStatus != .valid && validationStatus != .unknown {
            switch validationStatus {
            case .invalid:
                return "invalid_coordinates".localized
            case .warning:
                return "distance_warning_confirm".localized
            case .unknown, .valid:
                return nil
            }
        }

        // 检查地址验证分数 - 统一显示地址验证问题
        // 🎯 修复：0分表示未验证，不应显示警告；只有1-79分才表示验证分数低
        if addressValidationScore >= 1 && addressValidationScore < 80 {
            // 统一显示地址验证问题，不显示具体的验证问题详情
            return "address_validation_issue".localized
        }

        // 具体的验证问题已在上面处理

        // 检查坐标是否为0,0（无效坐标）
        if latitude == 0 && longitude == 0 {
            return "coordinates_missing".localized
        }

        // 检查坐标是否在有效范围内
        if latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180 {
            return "coordinates_out_of_range".localized
        }

        return nil
    }

    // 🚨 检查是否有明显错误的坐标
    private var hasObviouslyWrongCoordinates: Bool {
        // 检查是否是香港坐标（22.319300, 114.169400）用于非香港地址
        let isHongKongCoordinate = abs(latitude - 22.319300) < 0.001 && abs(longitude - 114.169400) < 0.001

        if isHongKongCoordinate {
            // 检查地址是否包含香港相关信息
            let addressText = primaryAddress.lowercased()
            let isHongKongAddress = addressText.contains("hong kong") ||
                                  addressText.contains("hk") ||
                                  addressText.contains("香港")

            // 如果坐标是香港的但地址不是香港的，则认为是错误坐标
            return !isHongKongAddress
        }

        // 可以添加其他明显错误坐标的检查
        return false
    }

    // 检查是否有任何验证问题
    var hasAnyValidationIssues: Bool {
        return validationIssueDescription != nil
    }

    // 存储取消令牌的静态集合
    private static var cancellables = Set<AnyCancellable>()
}
