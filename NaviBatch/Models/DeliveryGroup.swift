import Foundation
import SwiftData
import CoreLocation



@Model
final class DeliveryGroup: Identifiable {
    var id: UUID
    var name: String
    var groupNumber: Int
    @Relationship(deleteRule: .noAction) var points: [DeliveryPoint]
    var createdAt: Date

    // 🎯 Group独立优化数据字段
    var optimizedTotalDistance: Double? = nil        // 从司机位置开始的优化后总距离（公里）
    var lastOptimizedAt: Date? = nil                 // 最后优化时间
    var isGroupOptimized: Bool = false               // 该Group是否已经过优化
    var optimizationStartLatitude: Double? = nil     // 优化时司机位置的纬度
    var optimizationStartLongitude: Double? = nil    // 优化时司机位置的经度

    // 用于存储监听器的引用，便于后续移除
    @Transient private var statusChangeObserver: NSObjectProtocol?

    init(name: String, points: [DeliveryPoint], groupNumber: Int, createdAt: Date = Date()) {
        self.id = UUID()
        self.name = name
        self.points = points
        self.groupNumber = groupNumber
        self.createdAt = createdAt
    }

    var sortedPoints: [DeliveryPoint] {
        // 🎯 优先使用第三方排序号进行排序
        return points.sorted { point1, point2 in
            // 1. 起点总是最前面
            let point1IsStart = point1.isStartPoint || point1.sort_number == 0
            let point2IsStart = point2.isStartPoint || point2.sort_number == 0

            if point1IsStart && !point2IsStart { return true }
            if !point1IsStart && point2IsStart { return false }

            // 2. 🎯 优先使用第三方排序号
            let point1ThirdParty = point1.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            let point2ThirdParty = point2.thirdPartySortNumber?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

            // 如果都有第三方排序号，按第三方排序号排序
            if !point1ThirdParty.isEmpty && !point2ThirdParty.isEmpty {
                let num1 = extractNumber(from: point1ThirdParty)
                let num2 = extractNumber(from: point2ThirdParty)
                return num1 < num2
            }

            // 有第三方排序号的排在前面
            if !point1ThirdParty.isEmpty && point2ThirdParty.isEmpty { return true }
            if point1ThirdParty.isEmpty && !point2ThirdParty.isEmpty { return false }

            // 3. 都没有第三方排序号时，使用sorted_number
            return point1.sorted_number < point2.sorted_number
        }
    }

    /// 从字符串中提取数字
    private func extractNumber(from string: String) -> Int {
        let numbers = string.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        return Int(numbers) ?? 0
    }

    func reorderPoints(fromIndex: Int, toIndex: Int) {
        points.move(fromOffsets: IndexSet(integer: fromIndex),
                   toOffset: toIndex)
    }

    func reverse() {
        points.reverse()
    }

    // 🎯 计算从指定位置到Group所有点的总距离
    func calculateTotalDistance(from startLocation: CLLocationCoordinate2D) -> Double {
        guard !points.isEmpty else { return 0.0 }

        let sortedPoints = self.sortedPoints
        var totalDistance: Double = 0.0

        // 从起始位置到第一个点的距离
        if let firstPoint = sortedPoints.first {
            let startCLLocation = CLLocation(latitude: startLocation.latitude, longitude: startLocation.longitude)
            let firstCLLocation = CLLocation(latitude: firstPoint.latitude, longitude: firstPoint.longitude)
            totalDistance += startCLLocation.distance(from: firstCLLocation)
        }

        // 计算点与点之间的距离
        for i in 0..<(sortedPoints.count - 1) {
            let point1 = sortedPoints[i]
            let point2 = sortedPoints[i + 1]

            let location1 = CLLocation(latitude: point1.latitude, longitude: point1.longitude)
            let location2 = CLLocation(latitude: point2.latitude, longitude: point2.longitude)

            totalDistance += location1.distance(from: location2)
        }

        // 转换为公里并保留一位小数
        return (totalDistance / 1000.0).rounded(toPlaces: 1)
    }

    // 🎯 更新优化数据
    func updateOptimizationData(distance: Double, startLocation: CLLocationCoordinate2D) {
        self.optimizedTotalDistance = distance
        self.lastOptimizedAt = Date()
        self.isGroupOptimized = true
        self.optimizationStartLatitude = startLocation.latitude
        self.optimizationStartLongitude = startLocation.longitude
    }

    // 🎯 获取优化起始位置
    var optimizationStartLocation: CLLocationCoordinate2D? {
        guard let lat = optimizationStartLatitude,
              let lng = optimizationStartLongitude else {
            return nil
        }
        return CLLocationCoordinate2D(latitude: lat, longitude: lng)
    }

    // 添加监听状态变更的函数
    func setupStatusChangeMonitoring(modelContext: ModelContext) {
        // 避免重复添加监听
        removeStatusChangeMonitoring()

        // 添加新的监听器
        statusChangeObserver = NotificationCenter.default.addObserver(
            forName: .deliveryStatusChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }

            if let pointID = notification.object as? UUID,
               let point = self.points.first(where: { $0.id == pointID }),
               point.status == "completed" {

                // 从分组中移除已完成的点
                self.removePoint(point, context: modelContext)

                // 记录日志
                print("[INFO] DeliveryGroup - 从分组 \(self.name) 中移除已完成的地址点: \(point.primaryAddress)")
            }
        }

        // 记录日志
        print("[INFO] DeliveryGroup - 为分组 \(self.name) 设置状态变更监听")
    }

    // 移除监听器
    func removeStatusChangeMonitoring() {
        if let observer = statusChangeObserver {
            NotificationCenter.default.removeObserver(observer)
            statusChangeObserver = nil

            // 记录日志
            print("[INFO] DeliveryGroup - 移除分组 \(self.name) 的状态变更监听")
        }
    }

    // 安全移除点的函数
    func removePoint(_ point: DeliveryPoint, context: ModelContext) {
        // 重置点的分组状态
        point.isAssignedToGroup = false
        point.assignedGroupNumber = nil

        // 从分组中移除该点
        points.removeAll(where: { $0.id == point.id })

        // 保存更改
        try? context.save()

        // 发送分组更新通知
        NotificationCenter.default.post(
            name: .groupContentChanged,
            object: id.uuidString
        )

        // 如果分组为空，可以考虑删除分组
        checkAndRemoveEmptyGroup(context: context)
    }

    // 检查并删除空分组
    private func checkAndRemoveEmptyGroup(context: ModelContext) {
        if points.isEmpty {
            // 记录日志
            print("[INFO] DeliveryGroup - 分组 \(name) 为空，将被删除")

            // 从数据库中删除
            context.delete(self)
            try? context.save()

            // 发送通知
            NotificationCenter.default.post(
                name: .routeDataChanged,
                object: nil
            )
        }
    }

    // 销毁时清理资源
    deinit {
        removeStatusChangeMonitoring()
    }
}
