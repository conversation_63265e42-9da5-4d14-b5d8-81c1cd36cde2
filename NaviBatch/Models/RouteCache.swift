import Foundation
import SwiftData
import MapKit
import CoreLocation

/// 路线缓存模型 - 持久化存储API调用结果
@Model
final class RouteCache: Identifiable {
    var id: UUID
    var cacheKey: String // 基于起点终点坐标生成的唯一键
    
    // 起点和终点坐标
    var startLatitude: Double
    var startLongitude: Double
    var endLatitude: Double
    var endLongitude: Double
    
    // 路线数据
    var distance: Double // 距离（米）
    var expectedTravelTime: Double // 预计行程时间（秒）
    var routeCoordinatesData: Data? // 序列化的路线坐标数据
    
    // 缓存管理
    var createdAt: Date
    var lastUsedAt: Date
    var usageCount: Int
    var isValid: Bool // 缓存是否有效
    
    init(
        cacheKey: String,
        startCoordinate: CLLocationCoordinate2D,
        endCoordinate: CLLocationCoordinate2D,
        distance: Double,
        expectedTravelTime: Double,
        routeCoordinates: [CLLocationCoordinate2D]? = nil
    ) {
        self.id = UUID()
        self.cacheKey = cacheKey
        self.startLatitude = startCoordinate.latitude
        self.startLongitude = startCoordinate.longitude
        self.endLatitude = endCoordinate.latitude
        self.endLongitude = endCoordinate.longitude
        self.distance = distance
        self.expectedTravelTime = expectedTravelTime
        self.createdAt = Date()
        self.lastUsedAt = Date()
        self.usageCount = 1
        self.isValid = true
        
        // 序列化路线坐标
        if let coordinates = routeCoordinates {
            self.routeCoordinatesData = try? JSONEncoder().encode(coordinates.map { 
                ["lat": $0.latitude, "lng": $0.longitude] 
            })
        }
    }
    
    /// 获取起点坐标
    var startCoordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: startLatitude, longitude: startLongitude)
    }
    
    /// 获取终点坐标
    var endCoordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: endLatitude, longitude: endLongitude)
    }
    
    /// 获取路线坐标数组
    var routeCoordinates: [CLLocationCoordinate2D]? {
        guard let data = routeCoordinatesData else { return nil }
        
        do {
            let coordinateData = try JSONDecoder().decode([[String: Double]].self, from: data)
            return coordinateData.compactMap { dict in
                guard let lat = dict["lat"], let lng = dict["lng"] else { return nil }
                return CLLocationCoordinate2D(latitude: lat, longitude: lng)
            }
        } catch {
            return nil
        }
    }
    
    /// 更新使用统计
    func updateUsage() {
        lastUsedAt = Date()
        usageCount += 1
    }
    
    /// 检查缓存是否过期（30天）
    var isExpired: Bool {
        let expirationDays: TimeInterval = 30 * 24 * 60 * 60 // 30天
        return Date().timeIntervalSince(createdAt) > expirationDays
    }
    
    /// 生成缓存键
    static func generateCacheKey(
        from start: CLLocationCoordinate2D, 
        to end: CLLocationCoordinate2D
    ) -> String {
        return "\(String(format: "%.6f", start.latitude)),\(String(format: "%.6f", start.longitude))-\(String(format: "%.6f", end.latitude)),\(String(format: "%.6f", end.longitude))"
    }
}

/// 距离缓存模型 - 轻量级距离存储
@Model
final class DistanceCache: Identifiable {
    var id: UUID
    var cacheKey: String
    var distance: Double
    var createdAt: Date
    var lastUsedAt: Date
    var usageCount: Int

    init(cacheKey: String, distance: Double) {
        self.id = UUID()
        self.cacheKey = cacheKey
        self.distance = distance
        self.createdAt = Date()
        self.lastUsedAt = Date()
        self.usageCount = 1
    }

    func updateUsage() {
        lastUsedAt = Date()
        usageCount += 1
    }

    var isExpired: Bool {
        let expirationDays: TimeInterval = 30 * 24 * 60 * 60
        return Date().timeIntervalSince(createdAt) > expirationDays
    }
}


