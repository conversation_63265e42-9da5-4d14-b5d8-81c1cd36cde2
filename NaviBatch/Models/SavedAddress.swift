import Foundation
import CoreLocation
import SwiftData
import SwiftUI

@Model
final class SavedAddress: Identifiable {
    var id: UUID
    var address: String
    var latitude: Double
    var longitude: Double
    var createdAt: Date
    var isFavorite: Bool
    var notes: String?
    // 移除地址类型字段
    var phoneNumber: String?
    // 添加公司名称和URL字段
    var companyName: String?
    var url: String?

    init(address: String, coordinate: CLLocationCoordinate2D, notes: String? = nil, phoneNumber: String? = nil, companyName: String? = nil, url: String? = nil) {
        self.id = UUID()
        // 🎯 标准化地址后保存
        self.address = AddressStandardizer.standardizeAddress(address)
        self.latitude = coordinate.latitude
        self.longitude = coordinate.longitude
        self.createdAt = Date()
        self.isFavorite = false
        self.notes = notes
        self.phoneNumber = phoneNumber
        self.companyName = companyName
        self.url = url
    }

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }

    // 移除addressType计算属性

    // 不再需要默认名称方法
}
