import Foundation

struct RouteOptimizationInfo {
    // 存储的距离单位为米
    let beforeOptimizationDistance: Double
    let beforeOptimizationTime: Double
    let afterOptimizationDistance: Double
    let afterOptimizationTime: Double

    // 用于UI显示的距离（公里）
    var beforeDistanceKm: Double {
        return beforeOptimizationDistance / 1000.0
    }

    var afterDistanceKm: Double {
        return afterOptimizationDistance / 1000.0
    }

    var distanceSaved: Double {
        return beforeOptimizationDistance - afterOptimizationDistance
    }

    var timeSaved: Double {
        return beforeOptimizationTime - afterOptimizationTime
    }

    var distanceSavedPercentage: Double {
        guard beforeOptimizationDistance > 0 else { return 0 }
        return (distanceSaved / beforeOptimizationDistance) * 100
    }

    var timeSavedPercentage: Double {
        guard beforeOptimizationTime > 0 else { return 0 }
        return (timeSaved / beforeOptimizationTime) * 100
    }

    var formattedBeforeDistance: String {
        return String(format: "%.1f 公里", beforeDistanceKm)
    }

    var formattedAfterDistance: String {
        return String(format: "%.1f 公里", afterDistanceKm)
    }

    var formattedBeforeTime: String {
        let hours = Int(beforeOptimizationTime) / 3600
        let minutes = (Int(beforeOptimizationTime) % 3600) / 60

        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }

    var formattedAfterTime: String {
        let hours = Int(afterOptimizationTime) / 3600
        let minutes = (Int(afterOptimizationTime) % 3600) / 60

        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }

    var formattedDistanceSaved: String {
        if distanceSaved >= 1000 {
            return String(format: "%.1f 公里", distanceSaved / 1000)
        } else {
            return String(format: "%.0f 米", distanceSaved)
        }
    }

    var formattedTimeSaved: String {
        let hours = Int(timeSaved) / 3600
        let minutes = (Int(timeSaved) % 3600) / 60

        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
}
