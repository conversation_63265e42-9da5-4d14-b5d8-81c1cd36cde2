import Foundation
import CoreLocation
import SwiftData
import SwiftUI

// 地址验证状态枚举
enum AddressValidationStatus: String, Codable, CaseIterable {
    case unknown = "unknown"    // 未验证
    case valid = "valid"        // 有效
    case invalid = "invalid"    // 无效
    case warning = "warning"    // 警告
    case mismatch = "mismatch"  // 不匹配

    var localizedName: String {
        switch self {
        case .unknown: return "address_validation_unknown".localized
        case .valid: return "address_validation_valid".localized
        case .invalid: return "address_validation_invalid".localized
        case .warning: return "address_validation_warning".localized
        case .mismatch: return "address_validation_mismatch".localized
        }
    }

    var iconName: String {
        switch self {
        case .unknown: return "questionmark.circle"
        case .valid: return "checkmark.circle.fill"
        case .invalid: return "xmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .mismatch: return "exclamationmark.circle.fill"
        }
    }

    var color: Color {
        switch self {
        case .unknown: return .gray
        case .valid: return .green
        case .invalid: return .red
        case .warning: return .orange
        case .mismatch: return .orange
        }
    }
}

// Delivery failure reason enumeration
enum DeliveryFailureReason: String, Codable, CaseIterable {
    case notAtHome = "notAtHome"        // Not at home
    case wrongAddress = "wrongAddress"  // Wrong address
    case noAccess = "noAccess"          // No access
    case rejected = "rejected"          // Rejected
    case other = "other"                // Other reason

    var localizedName: String {
        switch self {
        case .notAtHome: return "failure_reason_not_at_home".localized
        case .wrongAddress: return "failure_reason_wrong_address".localized
        case .noAccess: return "failure_reason_no_access".localized
        case .rejected: return "failure_reason_rejected".localized
        case .other: return "failure_reason_other".localized
        }
    }
}

// Delivery status enumeration
enum DeliveryStatus: String, Codable {
    case pending = "pending"       // Pending delivery
    case inProgress = "inProgress" // In progress
    case completed = "completed"   // Completed
    case failed = "failed"         // Failed

    var localizedName: String {
        switch self {
        case .pending: return "delivery_status_pending".localized
        case .inProgress: return "delivery_status_in_progress".localized
        case .completed: return "delivery_status_completed".localized
        case .failed: return "delivery_status_failed".localized
        }
    }

    var iconName: String {
        switch self {
        case .pending: return "hourglass"
        case .inProgress: return "arrow.triangle.turn.up.right.circle"
        case .completed: return "checkmark.circle"
        case .failed: return "exclamationmark.triangle"
        }
    }

    var color: Color {
        switch self {
        case .pending: return .orange
        case .inProgress: return .blue
        case .completed: return .green
        case .failed: return .red
        }
    }
}

// Delivery type enumeration
enum DeliveryType: String, Codable, CaseIterable {
    case delivery = "delivery"  // Delivery
    case pickup = "pickup"      // Pickup

    var localizedName: String {
        switch self {
        case .delivery: return "delivery_type_delivery".localized
        case .pickup: return "delivery_type_pickup".localized
        }
    }
}

// Package size enumeration
enum PackageSize: String, Codable, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"

    var localizedName: String {
        switch self {
        case .small: return "package_size_small".localized
        case .medium: return "package_size_medium".localized
        case .large: return "package_size_large".localized
        }
    }
}

// Package type enumeration
enum PackageType: String, Codable, CaseIterable {
    case box = "box"       // Box
    case bag = "bag"       // Bag
    case letter = "letter" // Letter

    var localizedName: String {
        switch self {
        case .box: return "package_type_box".localized
        case .bag: return "package_type_bag".localized
        case .letter: return "package_type_letter".localized
        }
    }
}

// Vehicle position enumeration
enum VehiclePosition: String, Codable, CaseIterable {
    case front = "front"   // Front area
    case middle = "middle" // Middle area
    case back = "back"     // Back area
    case left = "left"     // Left side
    case right = "right"   // Right side
    case floor = "floor"   // Floor/bottom
    case shelf = "shelf"   // Shelf/top

    var localizedName: String {
        switch self {
        case .front: return "vehicle_position_front".localized
        case .middle: return "vehicle_position_middle".localized
        case .back: return "vehicle_position_back".localized
        case .left: return "vehicle_position_left".localized
        case .right: return "vehicle_position_right".localized
        case .floor: return "vehicle_position_floor".localized
        case .shelf: return "vehicle_position_shelf".localized
        }
    }
}

// Delivery order enumeration
enum DeliveryOrder: String, Codable {
    case first = "first"   // First
    case auto = "auto"     // Auto
    case last = "last"     // Last

    var localizedName: String {
        switch self {
        case .first: return "delivery_order_first".localized
        case .auto: return "delivery_order_auto".localized
        case .last: return "delivery_order_last".localized
        }
    }
}

// 优先级枚举
enum DeliveryPriority: Int, CaseIterable {
    case none = 0
    case priority1 = 1
    case priority2 = 2
    case priority3 = 3

    var localizedName: String {
        switch self {
        case .none:
            return "no_priority".localized
        case .priority1:
            return "priority_1".localized
        case .priority2:
            return "priority_2".localized
        case .priority3:
            return "priority_3".localized
        }
    }

    var displayOrder: Int {
        // 优先级越高，排序越靠前
        switch self {
        case .priority1: return 1
        case .priority2: return 2
        case .priority3: return 3
        case .none: return 999 // 无优先级排在最后
        }
    }
}

@Model
final class DeliveryPoint: Identifiable, Equatable, @unchecked Sendable {
    // Basic identification
    var id: UUID
    var sort_number: Int = 0      // Entry order number (immutable)
    var sorted_number: Int = 0    // Sorted order number (mutable)
    var isOptimized: Bool = false // Whether optimized

    // 🏗️ Structured address fields - Primary address storage method
    var unitNumber: String?       // Unit/room number (e.g.: Unit 5, Room 1205, Shop 23)

    // 🎯 Original AI-scanned address - for third-party apps
    var originalAddress: String?  // Original address from AI scanning (preserves unit info)

    // 🏗️ Structured address fields - Better flexibility and internationalization support
    var streetNumber: String?     // Street number (e.g.: 123, 1/12)
    var streetName: String?       // Street name (e.g.: Kerferd Road)
    var suburb: String?           // Suburb/area (e.g.: Glen Waverley)
    var city: String?             // City (e.g.: Melbourne)
    var state: String?            // State/province (e.g.: VIC, NSW)
    var postalCode: String?       // Postal code (e.g.: 3150)
    var country: String?          // Country (e.g.: Australia)
    var countryCode: String?      // Country code (e.g.: AU, US, GB)

    // Coordinate information
    var latitude: Double          // Latitude
    var longitude: Double         // Longitude
    var accessInstructions: String? // Access instructions

    // Package information
    var packageCount: Int         // Package count
    var packageSize: String?      // Package size
    var packageType: String?      // Package type
    var vehiclePosition: String?  // Vehicle position (multi-select, comma-separated string)
    var trackingNumber: String?   // Tracking number (long numeric codes, e.g., 6110124724982)
    var thirdPartySortNumber: String? // Third-party sort number (e.g., D90, D91, D146)
    var customerName: String?     // Customer name (from delivery app screenshots)
    var sourceAppRaw: String?     // Source delivery app (raw string for persistence)
    var priority: Int = 0         // Priority (0=no priority, 1-3=priority 1-3)

    // Amazon Flex delivery time information
    var scheduledDeliveryTime: String? // Scheduled delivery time (e.g., "已预约 3:00 - 8:00 上午 今天")
    var deliveryTimeSlot: String?      // Parsed time slot (e.g., "3:00 - 8:00 AM")
    var deliveryDate: String?          // Delivery date (e.g., "今天", "Today")

    // Delivery information
    var deliveryType: String = DeliveryType.delivery.rawValue // Delivery type
    var deliveryOrder: String?    // Delivery order
    var status: String            // Delivery status
    var failureReason: String?    // Delivery failure reason
    var customFailureReason: String?  // Custom failure reason text
    var statusUpdateTime: Date?   // Status update time
    var arrivalTime: String?      // Arrival time
    var timeAtStop: Int?          // Stop time (minutes)

    // Delivery photo information
    var frontDoorPhotoPath: String?    // Front door photo path
    var buildingPhotoPath: String?     // Building photo path
    var mailboxPhotoPath: String?      // Mailbox photo path
    var directionsPhotoPath: String?   // Directions photo path
    var notePhotoPath: String?         // Note photo path

    // Legacy properties - for migration and compatibility
    var doorPhotoPath: String? {
        get { return frontDoorPhotoPath }
        set { frontDoorPhotoPath = newValue }
    }
    var packagePhotoPath: String? {
        get { return mailboxPhotoPath }
        set { mailboxPhotoPath = newValue }
    }
    var placementPhotoPath: String? {
        get { return buildingPhotoPath }
        set { buildingPhotoPath = newValue }
    }

    var photosAlbumName: String?  // Album name where photos are saved
    var photosCompletionDate: Date? // Photo completion time

    // Route information
    var isStartPoint: Bool = false // Whether it's a start point
    var isEndPoint: Bool = false   // Whether it's an end point
    var isAssignedToGroup: Bool    // Whether assigned to a group
    var assignedGroupNumber: Int?  // Assigned group number

    // Other information
    var notes: String?            // Notes
    var geocodingWarning: String? // Geocoding or distance-related warning information
    var coordinateValidated: Bool = false // Whether coordinates are validated (new)

    // User location-based validation (new)
    var distanceFromUserLocation: Double? // Distance from user location (meters)
    var isWithinValidRange: Bool = true   // Whether within valid range
    var locationValidationStatus: String = LocationValidationStatus.unknown.rawValue // Location validation status

    // Reverse geocoding validation fields (new)
    var reverseGeocodedAddress: String?      // Address obtained from reverse geocoding
    var streetNumberValidation: String = AddressValidationStatus.unknown.rawValue       // Street number validation status
    var streetNameValidation: String = AddressValidationStatus.unknown.rawValue         // Street name validation status
    var postalCodeValidation: String = AddressValidationStatus.unknown.rawValue         // Postal code validation status
    var countryValidation: String = AddressValidationStatus.unknown.rawValue            // Country validation status
    var addressValidationIssues: String?     // Validation issue details (JSON format)
    var addressValidationScore: Double = 0.0 // Total validation score (0-100 points)

    // Relationships
    @Relationship
    var route: Route?             // Associated route
    var group: DeliveryGroup?     // Associated group

    // 🏗️ 新的结构化地址初始化方法
    init(
        sort_number: Int = -2,
        streetNumber: String? = nil,
        streetName: String? = nil,
        suburb: String? = nil,
        city: String? = nil,
        state: String? = nil,
        postalCode: String? = nil,
        country: String? = nil,
        countryCode: String? = nil,
        unitNumber: String? = nil,
        originalAddress: String? = nil,
        latitude: Double,
        longitude: Double,
        isStartPoint: Bool = false,
        isEndPoint: Bool = false
    ) {
        self.id = UUID()

        // 根据点的类型设置sort_number和sorted_number的默认值
        if isStartPoint {
            // 起点: sort_number和sorted_number都设为0（在地图上显示为0）
            self.sort_number = 0
            self.sorted_number = 0
        } else if isEndPoint {
            // 终点: sort_number和sorted_number设为-1
            self.sort_number = -1
            self.sorted_number = -1
        } else {
            // 🎯 修复：普通停靠点编号逻辑
            let finalSortNumber = sort_number != 0 ? sort_number : -2
            self.sort_number = finalSortNumber
            // 🎯 对于未优化路线，sorted_number应该等于sort_number
            self.sorted_number = finalSortNumber
        }

        self.isOptimized = false // 初始未优化

        // 🏗️ 设置结构化地址字段
        self.unitNumber = unitNumber
        self.originalAddress = originalAddress
        self.streetNumber = streetNumber
        self.streetName = streetName
        self.suburb = suburb
        self.city = city
        self.state = state
        self.postalCode = postalCode
        self.country = country
        self.countryCode = countryCode

        self.latitude = latitude
        self.longitude = longitude

        // 初始化其他必需的存储属性
        self.packageCount = 1
        self.deliveryType = DeliveryType.delivery.rawValue
        self.status = DeliveryStatus.pending.rawValue
        self.statusUpdateTime = Date()
        self.isStartPoint = isStartPoint
        self.isEndPoint = isEndPoint
        self.isAssignedToGroup = false // 明确设置为未分组
        self.assignedGroupNumber = nil // 明确设置为无组号
        self.geocodingWarning = nil // 初始化时没有警告
        self.trackingNumber = nil // 初始无追踪号码
        self.thirdPartySortNumber = nil // 初始无第三方Sort Number
        self.customerName = nil // 初始无客户姓名
        self.sourceAppRaw = DeliveryAppType.manual.rawValue // 默认为手动输入
        self.vehiclePosition = "" // 初始化为空字符串而不是nil

        // 初始化时间相关字段
        self.scheduledDeliveryTime = nil
        self.deliveryTimeSlot = nil
        self.deliveryDate = nil

        // 初始化其他存储属性
        self.accessInstructions = nil
        self.notes = nil
        self.arrivalTime = nil
        self.timeAtStop = nil
        self.deliveryOrder = nil
        self.packageSize = nil
        self.packageType = nil
        self.failureReason = nil
        self.customFailureReason = nil
        self.photosAlbumName = nil
        self.photosCompletionDate = nil
        self.coordinateValidated = false
        self.distanceFromUserLocation = nil
        self.isWithinValidRange = true
        self.locationValidationStatus = LocationValidationStatus.unknown.rawValue
        self.reverseGeocodedAddress = nil
        self.streetNumberValidation = AddressValidationStatus.unknown.rawValue
        self.streetNameValidation = AddressValidationStatus.unknown.rawValue
        self.postalCodeValidation = AddressValidationStatus.unknown.rawValue
        self.countryValidation = AddressValidationStatus.unknown.rawValue
        self.addressValidationIssues = nil
        self.addressValidationScore = 0.0

        // 🏗️ 使用结构化地址格式创建
        Logger.debug("✅ 新建的DeliveryPoint使用结构化地址格式", type: .data) // 改为debug级别
    }

    // 🎯 简化架构：移除复杂的地址解析逻辑
    // 单元号现在需要用户手动设置，避免解析错误

    // 计算属性
    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }

    var deliveryStatus: DeliveryStatus {
        DeliveryStatus(rawValue: status) ?? .pending
    }

    var deliveryTypeEnum: DeliveryType {
        DeliveryType(rawValue: deliveryType) ?? .delivery
    }

    var deliveryOrderEnum: DeliveryOrder? {
        if let order = deliveryOrder {
            return DeliveryOrder(rawValue: order)
        }
        return nil
    }

    var packageSizeEnum: PackageSize? {
        if let size = packageSize {
            return PackageSize(rawValue: size)
        }
        return nil
    }

    var packageTypeEnum: PackageType? {
        if let type = packageType {
            return PackageType(rawValue: type)
        }
        return nil
    }

    var vehiclePositions: [VehiclePosition] {
        guard let positionString = vehiclePosition, !positionString.isEmpty else {
            return []
        }
        return positionString.split(separator: ",").compactMap { VehiclePosition(rawValue: String($0)) }
    }

    var deliveryFailureReasonEnum: DeliveryFailureReason? {
        if let reason = failureReason {
            return DeliveryFailureReason(rawValue: reason)
        }
        return nil
    }

    var priorityEnum: DeliveryPriority {
        return DeliveryPriority(rawValue: priority) ?? .none
    }

    /// 配送应用类型（计算属性）
    var sourceApp: DeliveryAppType {
        get {
            guard let rawValue = sourceAppRaw else { return .manual }
            return DeliveryAppType(rawValue: rawValue) ?? .manual
        }
        set {
            sourceAppRaw = newValue.rawValue
        }
    }

    /// 是否是第三方快递且有排序号（用于绿色显示）
    var isThirdPartyWithSort: Bool {
        // 检查是否是第三方快递（非manual）
        guard sourceApp != .manual else { return false }

        // 检查是否有第三方排序号
        guard let sortNumber = thirdPartySortNumber,
              !sortNumber.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }

        return true
    }

    // 🎯 第三方排序号具有最高优先级，完全覆盖其他所有号码
    var displayNumber: Int {
        // 🚀 第三方排序号存在时，绝对优先，覆盖一切其他号码逻辑
        if let thirdPartySortNumber = thirdPartySortNumber, !thirdPartySortNumber.isEmpty {
            let numbers = thirdPartySortNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
            // 如果提取失败，返回1确保第三方号码始终显示（而不是回退到其他号码）
            return Int(numbers) ?? 1
        }

        // 只有在没有第三方排序号时，才使用其他号码逻辑
        if isOptimized {
            return sorted_number  // 已优化时使用sorted_number（紫色pin）
        } else {
            return sort_number    // 未优化时使用sort_number（蓝色pin）
        }
    }

    // 获取要显示的失败原因文本
    var displayFailureReason: String {
        if let reason = deliveryFailureReasonEnum {
            if reason == .other && customFailureReason != nil && !customFailureReason!.isEmpty {
                return customFailureReason!
            }
            return reason.localizedName
        }
        return ""
    }

    // 🏠 单元号相关计算属性 - 简化版本
    var hasUnitNumber: Bool {
        return unitNumber != nil && !unitNumber!.isEmpty
    }

    // 🎯 Primary address - smart display based on source type with standardized street abbreviations
    var primaryAddress: String {
        // 🎯 For third-party apps, prefer original AI-scanned address but apply street abbreviation standardization
        if sourceApp != .manual, let originalAddress = originalAddress, !originalAddress.isEmpty {
            // 🏛️ 应用街道简称标准化，确保显示一致性（使用首字母大写格式）
            return StreetAbbreviationStandardizer.standardizeStreetAbbreviations(originalAddress)
        }

        // 🏗️ For manual input or when no original address, use structured format
        return formattedFullStructuredAddress ?? streetName ?? "address_not_set".localized
    }

    var fullDisplayAddress: String {
        return primaryAddress
    }

    // 🏗️ 结构化地址相关计算属性
    var hasStructuredAddress: Bool {
        return streetName != nil || suburb != nil || city != nil || country != nil
    }

    var formattedStreetAddress: String? {
        guard let streetName = streetName else { return nil }

        if let streetNumber = streetNumber {
            return "\(streetNumber) \(streetName)"
        } else {
            return streetName
        }
    }

    var formattedLocalityAddress: String? {
        var components: [String] = []

        if let suburb = suburb { components.append(suburb) }
        if let city = city, city != suburb { components.append(city) }
        if let state = state { components.append(state) }
        if let postalCode = postalCode { components.append(postalCode) }

        return components.isEmpty ? nil : components.joined(separator: ", ")
    }

    var formattedFullStructuredAddress: String? {
        var components: [String] = []

        // 🎯 简化地址格式：只保留必要信息
        var streetAddressPart = ""

        // 先添加街道地址
        if let streetAddress = formattedStreetAddress {
            streetAddressPart = streetAddress
        }

        // 添加街道地址部分
        if !streetAddressPart.isEmpty {
            components.append(streetAddressPart)
        }

        // 🇺🇸 美国地址标准：公寓号作为独立组件，在街道地址之后
        if let unitNumber = unitNumber {
            components.append(unitNumber)
        }

        // 🎯 智能地址组件：根据国家选择合适的地区字段
        if let countryCode = countryCode?.uppercased() {
            switch countryCode {
            case "US": // 美国：使用城市而不是县
                if let city = city {
                    components.append(city)
                }
            case "AU": // 澳大利亚：使用郊区
                if let suburb = suburb {
                    components.append(suburb)
                }
            default: // 其他国家：优先使用郊区，如果没有则使用城市
                if let suburb = suburb {
                    components.append(suburb)
                } else if let city = city {
                    components.append(city)
                }
            }
        } else {
            // 如果没有国家代码，优先使用郊区
            if let suburb = suburb {
                components.append(suburb)
            } else if let city = city {
                components.append(city)
            }
        }

        if let state = state {
            components.append(state)
        }

        // 🎯 修复：添加邮编
        if let postalCode = postalCode {
            components.append(postalCode)
        }

        // 🍎 统一使用国家代码而不是完整国家名，提高Apple Maps识别率
        if let countryCode = countryCode {
            components.append(countryCode)
        } else if let country = country {
            // 如果没有国家代码，尝试转换常见国家名为代码
            switch country.lowercased() {
            case "united states", "usa":
                components.append("US")
            case "australia":
                components.append("AU")
            case "canada":
                components.append("CA")
            case "united kingdom", "uk":
                components.append("GB")
            default:
                // 对于其他国家，保持原名但去掉可能的长名称
                if country.count > 10 {
                    // 如果国家名太长，跳过以提高识别率
                } else {
                    components.append(country)
                }
            }
        }

        return components.isEmpty ? nil : components.joined(separator: ", ")
    }

    // 地址验证相关计算属性
    var hasValidationIssues: Bool {
        return addressValidationScore < 80 ||
               (addressValidationIssues != nil && !addressValidationIssues!.isEmpty)
    }

    var validationIssuesList: [String] {
        guard let issuesString = addressValidationIssues,
              !issuesString.isEmpty else {
            return []
        }

        // 首先尝试解析为JSON数组
        if let data = issuesString.data(using: .utf8),
           let issues = try? JSONSerialization.jsonObject(with: data) as? [String] {
            return issues
        }

        // 如果JSON解析失败，按分号分割字符串（兼容旧格式）
        return issuesString.components(separatedBy: "; ").filter { !$0.isEmpty }
    }

    // 方法
    func updateStatus(_ newStatus: DeliveryStatus, failureReason: DeliveryFailureReason? = nil, customReason: String? = nil) {
        // 保存旧状态以便比较
        let _ = status

        // 更新状态
        status = newStatus.rawValue
        statusUpdateTime = Date()

        // 如果状态为失败，设置失败原因；否则清除失败原因
        if newStatus == .failed {
            self.failureReason = failureReason?.rawValue

            // 当选择"其他原因"时，保存自定义原因
            if failureReason == .other {
                self.customFailureReason = customReason
            } else {
                self.customFailureReason = nil
            }
        } else {
            self.failureReason = nil
            self.customFailureReason = nil
        }

        // 如果状态变为已完成，发送通知
        if newStatus == .completed {
            // 记录日志
            print("[INFO] DeliveryPoint - 配送点状态变更为已完成: \(primaryAddress)")

            // 发送通知，包含点的ID作为参数
            NotificationCenter.default.post(
                name: .deliveryStatusChanged,
                object: id
            )
            print("[INFO] DeliveryPoint - 已发送状态变更通知，点ID: \(id)")
        }
    }

    // 设置为起点和终点
    func setAsStartAndEndPoint() {
        isStartPoint = true
        isEndPoint = true
    }

    func addVehiclePosition(_ position: VehiclePosition) {
        var positions = vehiclePositions
        if !positions.contains(position) {
            positions.append(position)
            vehiclePosition = positions.map { $0.rawValue }.joined(separator: ",")
        }
    }

    func removeVehiclePosition(_ position: VehiclePosition) {
        var positions = vehiclePositions
        positions.removeAll { $0 == position }
        vehiclePosition = positions.isEmpty ? "" : positions.map { $0.rawValue }.joined(separator: ",")
    }

    // 🏗️ 从 CLPlacemark 填充结构化地址字段
    func populateStructuredAddress(from placemark: CLPlacemark) {
        print("🔍 [DEBUG] populateStructuredAddress 开始处理")
        print("🔍 调试 placemark 信息:")
        print("   - subThoroughfare: '\(placemark.subThoroughfare ?? "nil")'")
        print("   - thoroughfare: '\(placemark.thoroughfare ?? "nil")'")
        print("   - locality: '\(placemark.locality ?? "nil")'")
        print("   - subAdministrativeArea: '\(placemark.subAdministrativeArea ?? "nil")'")
        print("   - administrativeArea: '\(placemark.administrativeArea ?? "nil")'")
        print("   - postalCode: '\(placemark.postalCode ?? "nil")'")
        print("   - country: '\(placemark.country ?? "nil")'")
        print("   - isoCountryCode: '\(placemark.isoCountryCode ?? "nil")'")

        print("🔍 [DEBUG] 保存前的结构化字段状态:")
        print("   - 当前streetNumber: '\(streetNumber ?? "nil")'")
        print("   - 当前streetName: '\(streetName ?? "nil")'")
        print("   - 当前city: '\(city ?? "nil")'")
        print("   - 当前suburb: '\(suburb ?? "nil")'")
        print("   - 当前state: '\(state ?? "nil")'")
        print("   - 当前country: '\(country ?? "nil")'")

        // 检查 placemark 数据完整性 - 放宽要求，只需要基本字段
        let hasCompleteData = placemark.thoroughfare != nil &&
                             (placemark.locality != nil || placemark.subAdministrativeArea != nil)

        print("🔍 [DEBUG] hasCompleteData = \(hasCompleteData)")

        if hasCompleteData {
            print("✅ Placemark 数据完整，使用 Apple Maps 数据")

            // 保存原始完整地址，用于提取公寓号
            let originalAddress = self.primaryAddress

            // 🏢 注意：不在这里提取单元号，应该在调用此方法之前由外部代码处理
            // 这样可以避免重复提取和覆盖已经验证过的单元号
            print("🏢 保持现有单元号: \(self.unitNumber ?? "无")")
            print("🔒 原始地址仅用于调试: \(originalAddress)")

            // 设置门牌号
            self.streetNumber = placemark.subThoroughfare

            // 🏛️ 使用街道简称标准化，确保数据库存储的一致性（首字母大写格式）
            if let thoroughfare = placemark.thoroughfare {
                let standardizedThoroughfare = StreetAbbreviationStandardizer.standardizeStreetAbbreviations(thoroughfare)
                self.streetName = standardizedThoroughfare
                print("🏛️ 使用标准化街道名: \(standardizedThoroughfare)")
            }

            // 记录原始完整地址，方便调试
            print("🔒 保持原始完整地址: \(originalAddress)")

            // 🎯 根据国家代码智能分配地址组件
            let countryCode = placemark.isoCountryCode?.uppercased() ?? ""
            print("🌍 根据国家代码(\(countryCode))智能分配地址组件")

            switch countryCode {
            case "US": // 美国地址格式
                self.city = placemark.locality  // locality 是城市
                self.suburb = placemark.subAdministrativeArea  // subAdministrativeArea 是县/区
                print("🇺🇸 美国地址格式: 城市=\(placemark.locality ?? "无"), 县/区=\(placemark.subAdministrativeArea ?? "无")")

            case "AU": // 澳大利亚地址格式
                self.suburb = placemark.locality  // locality 是郊区
                self.city = placemark.subAdministrativeArea  // subAdministrativeArea 是城市
                print("🇦🇺 澳大利亚地址格式: 郊区=\(placemark.locality ?? "无"), 城市=\(placemark.subAdministrativeArea ?? "无")")

            default: // 其他国家默认格式
                self.suburb = placemark.locality
                self.city = placemark.subAdministrativeArea
                print("🌍 默认地址格式: 地区=\(placemark.locality ?? "无"), 城市=\(placemark.subAdministrativeArea ?? "无")")
            }

            self.state = placemark.administrativeArea
            self.postalCode = placemark.postalCode
            self.country = placemark.country
            self.countryCode = placemark.isoCountryCode
        } else {
            print("⚠️ Placemark 数据不完整，尝试设置可用的部分字段")

            // 尽量设置可用的部分字段，而不是完全跳过
            if let subThoroughfare = placemark.subThoroughfare {
                self.streetNumber = subThoroughfare
                print("✓ 设置部分字段: 街道号码 = \(subThoroughfare)")
            }

            if let thoroughfare = placemark.thoroughfare {
                let standardizedThoroughfare = AddressStandardizer.standardizeAddress(thoroughfare)
                self.streetName = standardizedThoroughfare
                print("✓ 设置部分字段: 街道名 = \(standardizedThoroughfare)")
            }

            if let locality = placemark.locality {
                self.suburb = locality
                print("✓ 设置部分字段: 郊区 = \(locality)")
            }

            if let administrativeArea = placemark.administrativeArea {
                self.state = administrativeArea
                print("✓ 设置部分字段: 州 = \(administrativeArea)")
            }

            if let postalCode = placemark.postalCode {
                self.postalCode = postalCode
                print("✓ 设置部分字段: 邮编 = \(postalCode)")
            }

            if let country = placemark.country {
                self.country = country
                print("✓ 设置部分字段: 国家 = \(country)")
            }

            if let countryCode = placemark.isoCountryCode {
                self.countryCode = countryCode
                print("✓ 设置部分字段: 国家代码 = \(countryCode)")
            }

            print("⚠️ 注意: 由于数据不完整，某些字段可能为空")
        }

        print("🏗️ [DEBUG] 填充结构化地址完成:")
        print("   街道号码: \(streetNumber ?? "无")")
        print("   街道名称: \(streetName ?? "无")")
        print("   单元号码: \(unitNumber ?? "无")")
        print("   郊区: \(suburb ?? "无")")
        print("   城市: \(city ?? "无")")
        print("   州: \(state ?? "无")")
        print("   邮编: \(postalCode ?? "无")")
        print("   国家: \(country ?? "无")")
        print("   国家代码: \(countryCode ?? "无")")
        print("🔍 [DEBUG] populateStructuredAddress 处理完成")
    }


    // 🏗️ 手动设置结构化地址字段
    func setStructuredAddress(
        streetNumber: String? = nil,
        streetName: String? = nil,
        suburb: String? = nil,
        city: String? = nil,
        state: String? = nil,
        postalCode: String? = nil,
        country: String? = nil,
        countryCode: String? = nil
    ) {
        self.streetNumber = streetNumber
        // 🏛️ 对街道名称应用标准化，确保使用首字母大写格式
        if let streetName = streetName {
            self.streetName = StreetAbbreviationStandardizer.standardizeStreetAbbreviations(streetName)
        } else {
            self.streetName = nil
        }
        self.suburb = suburb
        self.city = city
        self.state = state
        self.postalCode = postalCode
        self.country = country
        self.countryCode = countryCode
    }

    // 🎯 统一的地址更新接口（仅使用结构化地址）
    func updateAddress(
        streetNumber: String? = nil,
        streetName: String? = nil,
        suburb: String? = nil,
        city: String? = nil,
        state: String? = nil,
        postalCode: String? = nil,
        country: String? = nil,
        countryCode: String? = nil,
        unitNumber: String? = nil
    ) {
        // 更新结构化字段
        if let streetNumber = streetNumber { self.streetNumber = streetNumber }
        // 🏛️ 对街道名称应用标准化，确保使用首字母大写格式
        if let streetName = streetName {
            self.streetName = StreetAbbreviationStandardizer.standardizeStreetAbbreviations(streetName)
        }
        if let suburb = suburb { self.suburb = suburb }
        if let city = city { self.city = city }
        if let state = state { self.state = state }
        if let postalCode = postalCode { self.postalCode = postalCode }
        if let country = country { self.country = country }
        if let countryCode = countryCode { self.countryCode = countryCode }
        if let unitNumber = unitNumber { self.unitNumber = unitNumber }

        print("🎯 地址已更新: \(primaryAddress)")
    }

    // 🔄 数据迁移方法 - 从旧格式迁移到结构化格式
    func migrateToStructuredAddress() {
        // 如果已经有结构化地址，跳过迁移
        if hasStructuredAddress {
            print("🔄 地址已经是结构化格式，跳过迁移")
            return
        }

        // 旧地址字段已移除，需要重新设置地址
        print("🔄 旧地址字段已移除，需要重新设置结构化地址")
        print("🔄 建议重新验证以获取完整的结构化信息")
    }

    // 便利初始化方法 - 使用坐标
    convenience init(
        sort_number: Int = -2,
        streetNumber: String? = nil,
        streetName: String? = nil,
        suburb: String? = nil,
        city: String? = nil,
        state: String? = nil,
        postalCode: String? = nil,
        country: String? = nil,
        countryCode: String? = nil,
        unitNumber: String? = nil,
        originalAddress: String? = nil,
        coordinate: CLLocationCoordinate2D,
        isStartPoint: Bool = false,
        isEndPoint: Bool = false
    ) {
        self.init(
            sort_number: sort_number,
            streetNumber: streetNumber,
            streetName: streetName,
            suburb: suburb,
            city: city,
            state: state,
            postalCode: postalCode,
            country: country,
            countryCode: countryCode,
            unitNumber: unitNumber,
            originalAddress: originalAddress,
            latitude: coordinate.latitude,
            longitude: coordinate.longitude,
            isStartPoint: isStartPoint,
            isEndPoint: isEndPoint
        )
    }

    // 注意：迁移方法已移至DatabaseMigrator类

    // MARK: - 私有方法
    // 旧地址相关方法已移除，现在完全使用结构化地址

    // 全球化的统一坐标验证方法
    static func validateCoordinatesGlobally(
        latitude: Double,
        longitude: Double,
        userLocation: CLLocationCoordinate2D? = nil
    ) -> (isValid: Bool, warning: String?, validationStatus: LocationValidationStatus) {

        // 步骤1: 基本坐标有效性检查
        if latitude == 0 && longitude == 0 {
            return (false, "coordinates_origin_point".localized, .invalid)
        }

        if latitude.isNaN || longitude.isNaN {
            return (false, "coordinates_invalid_nan".localized, .invalid)
        }

        if latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180 {
            return (false, "coordinates_out_of_range".localized, .invalid)
        }

        // 步骤2: 与用户位置的距离检查（如果提供）
        if let userLocation = userLocation {
            let userLocationObj = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
            let pointLocation = CLLocation(latitude: latitude, longitude: longitude)
            let distance = userLocationObj.distance(from: pointLocation) // 单位：米

            // 警告距离：相距超过20000公里则提示（基本上禁用距离警告，方便开发）
            let warningDistance: Double = 20_000_000 // 20000公里

            if distance > warningDistance {
                return (true, "coordinates_far_from_user".localized, .warning)
            }
        }

        // 步骤3: 位于海洋/水域检查（可选，但需要进行反向地理编码，可能会有性能影响）
        // 这里我们只返回基本验证结果，实际的海洋检查应在导入时异步执行

        // 全球范围内的坐标有效
        return (true, nil, .valid)
    }

    // 实例方法，调用静态验证方法
    func validateCoordinatesGlobally(userLocation: CLLocationCoordinate2D? = nil) {
        let result = DeliveryPoint.validateCoordinatesGlobally(
            latitude: self.latitude,
            longitude: self.longitude,
            userLocation: userLocation
        )

        self.coordinateValidated = true
        self.isWithinValidRange = result.isValid
        self.locationValidationStatus = result.validationStatus.rawValue

        if let warning = result.warning {
            self.geocodingWarning = warning
        } else {
            self.geocodingWarning = nil
        }
    }

    // 向后兼容的验证方法（调用新的全球验证方法）
    func validateCoordinates() -> Bool {
        let result = DeliveryPoint.validateCoordinatesGlobally(
            latitude: self.latitude,
            longitude: self.longitude
        )

        self.coordinateValidated = true
        self.isWithinValidRange = result.isValid
        self.locationValidationStatus = result.validationStatus.rawValue

        // 设置警告信息
        if let warning = result.warning {
            self.geocodingWarning = warning
        } else {
            self.geocodingWarning = nil
        }

        return result.isValid
    }

    // MARK: - 🏢 单位信息提取工具

    /// 从完整地址中提取单位信息
    /// - Parameter address: 完整地址字符串
    /// - Returns: 提取到的单位号码，如果没有则返回nil
    static func extractUnitNumber(from address: String) -> String? {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 定义单位信息的正则表达式模式
        let patterns = [
            // 英文格式 - 修复字符类以包含大写字母
            "(?i)\\b(apt|apartment)\\s*([A-Za-z0-9]+)\\b",           // Apt 123, Apartment A
            "(?i)\\b(unit)\\s*([A-Za-z0-9]+)\\b",                   // Unit 5, Unit A
            "(?i)\\b(suite|ste)\\s*([A-Za-z0-9]+)\\b",             // Suite 456, Ste 123
            "(?i)\\b(room|rm)\\s*([A-Za-z0-9]+)\\b",               // Room 789, Rm 12
            "(?i)\\b(shop)\\s*([A-Za-z0-9]+)\\b",                  // Shop 23
            "(?i)\\b(lot)\\s*([A-Za-z0-9]+)\\b",                   // Lot 45
            "(?i)\\b(bldg|building)\\s*([A-Za-z0-9]+)\\b",         // Bldg A, Building 2
            "(?i)\\b(floor|fl)\\s*([A-Za-z0-9]+)\\b",              // Floor 3, Fl 2
            "#\\s*([A-Za-z0-9]+)\\b",                               // #123
            "(?i)\\bno\\.?\\s*([A-Za-z0-9]+)\\b",                  // No. 123, No 456

            // 复合格式 - 修复字符类以包含大写字母
            "(?i)\\b(unit)\\s*([A-Za-z0-9]+/[A-Za-z0-9]+)\\b",        // Unit 1/12
            "(?i)\\b(apt|apartment)\\s*([A-Za-z0-9]+[A-Za-z])\\b",    // Apt 12B

            // 中文格式 - 修复字符类以包含大写字母
            "(单元|房间|室|号房|号)\\s*([A-Za-z0-9\\u4e00-\\u9fff]+)",  // 单元123, 房间456, 室789
            "([0-9]+)\\s*(单元|房间|室|号房|号)",                    // 123单元, 456房间

            // 香港/台湾格式 - 修复字符类以包含大写字母
            "([A-Za-z0-9]+)\\s*(号铺|号舖|号店|号房)",                  // A101号铺
            "(铺|舖|店)\\s*([A-Za-z0-9]+)",                          // 铺123
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive]),
               let match = regex.firstMatch(in: trimmedAddress, options: [], range: NSRange(trimmedAddress.startIndex..<trimmedAddress.endIndex, in: trimmedAddress)) {

                // 提取匹配的单位信息
                if match.numberOfRanges >= 3 {
                    // 有前缀和数字的格式 (如 "Apt 123")
                    if let prefixRange = Range(match.range(at: 1), in: trimmedAddress),
                       let numberRange = Range(match.range(at: 2), in: trimmedAddress) {
                        let prefix = String(trimmedAddress[prefixRange]).capitalized
                        let number = String(trimmedAddress[numberRange])

                        // 🔍 验证单元号是否合理（避免OCR错误）
                        if isValidUnitNumber(number) {
                            return "\(prefix) \(number)"
                        } else {
                            print("🚫 跳过可疑的单元号: '\(number)' (可能是OCR错误)")
                            continue // 继续尝试其他模式
                        }
                    }
                } else if match.numberOfRanges >= 2 {
                    // 只有数字的格式 (如 "#123")
                    if let numberRange = Range(match.range(at: 1), in: trimmedAddress) {
                        let number = String(trimmedAddress[numberRange])

                        // 🔍 验证单元号是否合理（避免OCR错误）
                        if isValidUnitNumber(number) {
                            // 根据模式确定前缀
                            if pattern.contains("#") {
                                return "#\(number)"
                            } else if pattern.contains("no") {
                                return "No. \(number)"
                            } else {
                                return number
                            }
                        } else {
                            print("🚫 跳过可疑的单元号: '\(number)' (可能是OCR错误)")
                            continue // 继续尝试其他模式
                        }
                    }
                }
            }
        }

        return nil
    }

    /// 验证提取的单元号是否合理（避免OCR错误）
    /// - Parameter unitNumber: 提取的单元号
    /// - Returns: 是否是合理的单元号
    static func isValidUnitNumber(_ unitNumber: String) -> Bool {
        let trimmed = unitNumber.trimmingCharacters(in: .whitespacesAndNewlines)

        // 空字符串无效
        if trimmed.isEmpty {
            return false
        }

        // 常见的OCR错误模式
        let ocrErrorPatterns = [
            "^ed$",           // "Unit ed" - 明显的OCR错误
            "^er$",           // "Unit er" - 类似错误
            "^el$",           // "Unit el" - 类似错误
            "^es$",           // "Unit es" - 类似错误
            "^en$",           // "Unit en" - 类似错误
            "^et$",           // "Unit et" - 类似错误
            "^eo$",           // "Unit eo" - 类似错误
            "^ee$",           // "Unit ee" - 类似错误
            "^ll$",           // "Unit ll" - 双字母错误
            "^rr$",           // "Unit rr" - 双字母错误
            "^nn$",           // "Unit nn" - 双字母错误
        ]

        // 检查是否匹配OCR错误模式
        for pattern in ocrErrorPatterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive]),
               regex.firstMatch(in: trimmed, options: [], range: NSRange(trimmed.startIndex..<trimmed.endIndex, in: trimmed)) != nil {
                return false
            }
        }

        // 有效的单元号模式
        let validPatterns = [
            "^[0-9]+$",                    // 纯数字: 1, 123, 456
            "^[A-Za-z]$",                  // 单字母: A, B, C
            "^[0-9]+[A-Za-z]$",           // 数字+字母: 12A, 456B
            "^[A-Za-z][0-9]+$",           // 字母+数字: A12, B456
            "^[0-9]+/[0-9]+$",            // 分数格式: 1/12, 2/34
            "^[0-9]+-[0-9]+$",            // 范围格式: 1-3, 12-14
            "^[A-Za-z][0-9]+[A-Za-z]$",   // 复合格式: A12B
        ]

        // 检查是否匹配有效模式
        for pattern in validPatterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: []),
               regex.firstMatch(in: trimmed, options: [], range: NSRange(trimmed.startIndex..<trimmed.endIndex, in: trimmed)) != nil {
                return true
            }
        }

        return false
    }

    /// 从地址中移除单位信息，返回清理后的街道地址
    /// - Parameter address: 包含单位信息的完整地址
    /// - Returns: 移除单位信息后的街道地址
    static func removeUnitNumber(from address: String) -> String {
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)

        // 定义需要移除的单位信息模式 - 修复字符类以包含大写字母
        let patterns = [
            "(?i),?\\s*(apt|apartment)\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*(unit)\\s*[A-Za-z0-9/]+\\b",
            "(?i),?\\s*(suite|ste)\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*(room|rm)\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*(shop)\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*(lot)\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*(bldg|building)\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*(floor|fl)\\s*[A-Za-z0-9]+\\b",
            ",?\\s*#\\s*[A-Za-z0-9]+\\b",
            "(?i),?\\s*no\\.?\\s*[A-Za-z0-9]+\\b",
            ",?\\s*(单元|房间|室|号房|号)\\s*[A-Za-z0-9\\u4e00-\\u9fff]+",
            ",?\\s*[0-9]+\\s*(单元|房间|室|号房|号)",
            ",?\\s*[A-Za-z0-9]+\\s*(号铺|号舖|号店|号房)",
            ",?\\s*(铺|舖|店)\\s*[A-Za-z0-9]+",
        ]

        var cleanedAddress = trimmedAddress

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive]) {
                cleanedAddress = regex.stringByReplacingMatches(
                    in: cleanedAddress,
                    options: [],
                    range: NSRange(cleanedAddress.startIndex..<cleanedAddress.endIndex, in: cleanedAddress),
                    withTemplate: ""
                )
            }
        }

        // 清理多余的逗号和空格
        cleanedAddress = cleanedAddress
            .replacingOccurrences(of: ",,", with: ",")
            .replacingOccurrences(of: ", ,", with: ",")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除开头或结尾的逗号
        if cleanedAddress.hasPrefix(",") {
            cleanedAddress = String(cleanedAddress.dropFirst()).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        if cleanedAddress.hasSuffix(",") {
            cleanedAddress = String(cleanedAddress.dropLast()).trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return cleanedAddress
    }

    /// 🧪 测试单元号功能 - 简化版本
    static func testUnitNumberFeature() {
        let testCases = [
            ("123 Main Street, City, State", "Unit 5"),
            ("456 Oak Avenue, Town, State", "Apt 12B"),
            ("789 Pine Road, Village, State", nil)
        ]

        print("🧪 开始测试单元号功能")
        print(String(repeating: "=", count: 50))

        for (address, unit) in testCases {
            let point = DeliveryPoint(sort_number: 1, streetName: address, latitude: -37.8770, longitude: 145.1610)
            point.unitNumber = unit

            print("📍 地址: \(address)")
            print("   🏢 单元号: \(point.unitNumber ?? "无")")
            print("   📱 完整显示: \(point.primaryAddress)")
            print("   ✅ 有单元号: \(point.hasUnitNumber ? "是" : "否")")
            print("")
        }

        print("🎯 测试完成！")
    }

    /// 🧪 测试单位信息提取功能
    static func testUnitExtraction() {
        let testCases = [
            "1721 Marina Court, Apt D, San Mateo, CA",
            "123 Main Street, Unit 5, City, State",
            "456 Oak Avenue, Suite 123, Town, State",
            "789 Pine Road, #456, Village, State",
            "321 Elm Street, Room 789, City, State",
            "654 Maple Drive, No. 12, Town, State",
            "987 Oak Lane, Building A, City, State",
            "147 Pine Avenue, Floor 3, Town, State",
            "258 Cedar Street, Shop 23, City, State",
            "369 Birch Road, Unit 1/12, Town, State",
            "九龙九龙城明雅苑商场一楼 A101 号铺",
            "九龙大埔道 188 号路商 2,3,5,6,7,8,9 号地铺",
            "香港中环皇后大道中 123 号 单元456",
            "台北市信义区信义路五段 789 号 12 楼 A 室",
            "6974 S Park Reserve Drive, Midvale, UT", // 无单位信息的地址
            // 🚫 OCR错误测试用例
            "500 King Drive, Unit ed, San Mateo County, CA, 94015, US", // 应该被拒绝
            "123 Main St, Apt er, City, State", // 应该被拒绝
            "456 Oak Ave, Unit ll, Town, State", // 应该被拒绝
            "789 Pine Rd, Suite es, Village, State" // 应该被拒绝
        ]

        print("🧪 开始测试单位信息提取功能")
        print(String(repeating: "=", count: 60))

        for address in testCases {
            let extractedUnit = extractUnitNumber(from: address)
            let cleanedAddress = removeUnitNumber(from: address)

            print("📍 原始地址: \(address)")
            print("   🏢 提取单位: \(extractedUnit ?? "无")")
            print("   🧹 清理地址: \(cleanedAddress)")
            print("")
        }

        print("🎯 测试完成！")
    }

    /// 🧪 测试OCR错误修复功能
    static func testOCRErrorFix() {
        print("🧪 开始测试OCR错误修复功能")
        print(String(repeating: "=", count: 60))

        let testCases = [
            "500 King Drive, Unit ed, San Mateo County, CA, 94015, US", // 应该被拒绝
            "500 King Drive, Unit 5, San Mateo County, CA, 94015, US",  // 应该被接受
            "123 Main St, Apt er, City, State",                         // 应该被拒绝
            "123 Main St, Apt 12A, City, State",                        // 应该被接受
            "456 Oak Ave, Unit ll, Town, State",                        // 应该被拒绝
            "456 Oak Ave, Unit 1/12, Town, State",                      // 应该被接受
        ]

        for address in testCases {
            let extractedUnit = extractUnitNumber(from: address)
            print("📍 测试地址: \(address)")
            print("   🏢 提取结果: \(extractedUnit ?? "❌ 被拒绝")")
            print("   ✅ 状态: \(extractedUnit != nil ? "接受" : "拒绝")")
            print("")
        }

        print("🎯 OCR错误修复测试完成！")
    }

    /// 🧪 测试编辑地址时的错误单元号清除功能
    static func testEditAddressUnitCleanup() {
        print("🧪 开始测试编辑地址时的错误单元号清除功能")
        print(String(repeating: "=", count: 60))

        // 模拟现有地址的错误单元号
        let testCases = [
            ("Unit ed", false),    // 应该被清除
            ("Unit 5", true),      // 应该保留
            ("Apt er", false),     // 应该被清除
            ("Apt 12A", true),     // 应该保留
            ("Suite ll", false),   // 应该被清除
            ("Suite 456", true),   // 应该保留
        ]

        for (unitNumber, shouldKeep) in testCases {
            let unitPart = unitNumber.replacingOccurrences(of: "Unit ", with: "")
                .replacingOccurrences(of: "Apt ", with: "")
                .replacingOccurrences(of: "Suite ", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)

            let isValid = isValidUnitNumber(unitPart)
            let action = isValid ? "保留" : "清除"
            let expected = shouldKeep ? "保留" : "清除"
            let status = (isValid == shouldKeep) ? "✅" : "❌"

            print("📍 测试单元号: \(unitNumber)")
            print("   🔍 提取部分: '\(unitPart)'")
            print("   🎯 预期操作: \(expected)")
            print("   🛠️ 实际操作: \(action)")
            print("   \(status) 结果: \(isValid == shouldKeep ? "正确" : "错误")")
            print("")
        }

        print("🎯 编辑地址单元号清除测试完成！")
    }

    /// 🧪 测试完全清除和重新填充地址数据的功能
    static func testCompleteAddressReset() {
        print("🧪 开始测试完全清除和重新填充地址数据的功能")
        print(String(repeating: "=", count: 60))

        // 创建一个包含错误数据的DeliveryPoint
        let point = DeliveryPoint(sort_number: 1, streetName: "Old Street", latitude: -37.8770, longitude: 145.1610)
        point.streetNumber = "999"
        point.unitNumber = "Unit ed"  // 错误的单元号
        point.suburb = "Old Suburb"
        point.city = "Old City"
        point.state = "Old State"
        point.postalCode = "12345"
        point.country = "Old Country"
        point.countryCode = "OC"

        print("📍 清除前的地址数据:")
        print("   街道号码: \(point.streetNumber ?? "无")")
        print("   街道名称: \(point.streetName ?? "无")")
        print("   单元号码: \(point.unitNumber ?? "无")")
        print("   郊区: \(point.suburb ?? "无")")
        print("   城市: \(point.city ?? "无")")
        print("   州: \(point.state ?? "无")")
        print("   邮编: \(point.postalCode ?? "无")")
        print("   国家: \(point.country ?? "无")")
        print("   国家代码: \(point.countryCode ?? "无")")
        print("   坐标: (\(point.latitude), \(point.longitude))")
        print("")

        // 模拟完全清除操作
        print("🧹 执行完全清除操作...")
        point.streetNumber = nil
        point.streetName = nil
        point.unitNumber = nil
        point.suburb = nil
        point.city = nil
        point.state = nil
        point.postalCode = nil
        point.country = nil
        point.countryCode = nil
        point.latitude = 0
        point.longitude = 0

        print("📍 清除后的地址数据:")
        print("   街道号码: \(point.streetNumber ?? "无")")
        print("   街道名称: \(point.streetName ?? "无")")
        print("   单元号码: \(point.unitNumber ?? "无")")
        print("   郊区: \(point.suburb ?? "无")")
        print("   城市: \(point.city ?? "无")")
        print("   州: \(point.state ?? "无")")
        print("   邮编: \(point.postalCode ?? "无")")
        print("   国家: \(point.country ?? "无")")
        print("   国家代码: \(point.countryCode ?? "无")")
        print("   坐标: (\(point.latitude), \(point.longitude))")
        print("")

        // 模拟重新填充新数据
        print("🏗️ 重新填充新的正确数据...")
        point.streetNumber = "500"
        point.streetName = "King Drive"
        point.unitNumber = "Unit 5"  // 正确的单元号
        point.suburb = "San Mateo County"
        point.city = "Daly City"
        point.state = "CA"
        point.postalCode = "94015"
        point.country = "United States"
        point.countryCode = "US"
        point.latitude = 37.6546524
        point.longitude = -122.4539704

        print("📍 重新填充后的地址数据:")
        print("   街道号码: \(point.streetNumber ?? "无")")
        print("   街道名称: \(point.streetName ?? "无")")
        print("   单元号码: \(point.unitNumber ?? "无")")
        print("   郊区: \(point.suburb ?? "无")")
        print("   城市: \(point.city ?? "无")")
        print("   州: \(point.state ?? "无")")
        print("   邮编: \(point.postalCode ?? "无")")
        print("   国家: \(point.country ?? "无")")
        print("   国家代码: \(point.countryCode ?? "无")")
        print("   坐标: (\(point.latitude), \(point.longitude))")
        print("")

        print("✅ 完全清除和重新填充测试完成！")
        print("🎯 结果: 所有旧数据已清除，新数据完整填充，无残留问题")
    }

    /// 🧪 测试美国地址格式修复
    static func testUSAddressFormat() {
        print("🧪 开始测试美国地址格式修复")
        print(String(repeating: "=", count: 60))

        // 创建一个美国地址的DeliveryPoint
        let point = DeliveryPoint(sort_number: 1, streetName: "King Drive", latitude: 37.6546524, longitude: -122.4539704)
        point.streetNumber = "500"
        point.streetName = "King Drive"
        point.city = "Daly City"           // 城市
        point.suburb = "San Mateo County"  // 县
        point.state = "CA"
        point.postalCode = "94015"
        point.country = "United States"
        point.countryCode = "US"

        print("📍 美国地址数据:")
        print("   街道号码: \(point.streetNumber ?? "无")")
        print("   街道名称: \(point.streetName ?? "无")")
        print("   城市: \(point.city ?? "无")")
        print("   县/区: \(point.suburb ?? "无")")
        print("   州: \(point.state ?? "无")")
        print("   邮编: \(point.postalCode ?? "无")")
        print("   国家代码: \(point.countryCode ?? "无")")
        print("")

        let formattedAddress = point.formattedFullStructuredAddress
        print("📍 格式化地址结果:")
        print("   \(formattedAddress ?? "无")")
        print("")

        // 验证结果
        let expectedCity = "Daly City"
        let unexpectedCounty = "San Mateo County"

        if let address = formattedAddress {
            let containsCity = address.contains(expectedCity)
            let containsCounty = address.contains(unexpectedCounty)

            print("🔍 验证结果:")
            print("   包含城市 '\(expectedCity)': \(containsCity ? "✅" : "❌")")
            print("   包含县 '\(unexpectedCounty)': \(containsCounty ? "❌ 错误!" : "✅ 正确")")
            print("")

            if containsCity && !containsCounty {
                print("✅ 美国地址格式修复成功！")
                print("🎯 正确使用城市而不是县作为地址组件")
            } else {
                print("❌ 美国地址格式仍有问题")
                if !containsCity {
                    print("   - 缺少城市信息")
                }
                if containsCounty {
                    print("   - 错误包含县信息")
                }
            }
        } else {
            print("❌ 无法生成格式化地址")
        }

        print("")
        print("🎯 美国地址格式测试完成！")
    }

    /// 🧪 测试坐标更新功能
    static func testCoordinateUpdate() {
        print("🧪 开始测试坐标更新功能")
        print(String(repeating: "=", count: 60))

        // 创建一个测试点
        let point = DeliveryPoint(sort_number: 1, streetName: "Test Street", latitude: 0, longitude: 0)

        print("📍 初始状态:")
        print("   坐标: (\(point.latitude), \(point.longitude))")
        print("   坐标验证: \(point.coordinateValidated)")
        print("   有效范围: \(point.isWithinValidRange)")
        print("")

        // 模拟坐标更新
        let newLatitude = 37.6546524
        let newLongitude = -122.4539704

        print("🎯 更新坐标:")
        print("   新坐标: (\(newLatitude), \(newLongitude))")

        point.latitude = newLatitude
        point.longitude = newLongitude

        print("📍 更新后状态:")
        print("   坐标: (\(point.latitude), \(point.longitude))")
        print("")

        // 验证坐标
        let isValid = point.validateCoordinates()

        print("🔍 坐标验证结果:")
        print("   验证通过: \(isValid)")
        print("   坐标验证: \(point.coordinateValidated)")
        print("   有效范围: \(point.isWithinValidRange)")
        print("   验证状态: \(point.locationValidationStatus)")
        if let warning = point.geocodingWarning {
            print("   警告信息: \(warning)")
        } else {
            print("   警告信息: 无")
        }
        print("")

        // 检查坐标是否在合理范围内
        let isInUSA = newLatitude >= 24.0 && newLatitude <= 49.0 &&
                      newLongitude >= -125.0 && newLongitude <= -66.0

        print("🇺🇸 美国范围检查:")
        print("   在美国范围内: \(isInUSA)")
        print("   纬度范围 (24-49): \(newLatitude >= 24.0 && newLatitude <= 49.0)")
        print("   经度范围 (-125 to -66): \(newLongitude >= -125.0 && newLongitude <= -66.0)")
        print("")

        // 检查具体位置
        print("🏠 具体位置信息:")
        print("   地址: 500 King Drive, Daly City, CA")
        print("   坐标: (\(newLatitude), \(newLongitude))")
        print("   这个坐标应该指向加利福尼亚州戴利城的King Drive 500号")
        print("")

        if isValid && isInUSA {
            print("✅ 坐标更新测试成功！")
            print("🎯 坐标有效且在合理范围内")
        } else {
            print("❌ 坐标更新测试失败")
            if !isValid {
                print("   - 坐标验证失败")
            }
            if !isInUSA {
                print("   - 坐标不在美国范围内")
            }
        }

        print("")
        print("🎯 坐标更新测试完成！")
    }

    /// 🎯 为地址生成微调坐标候选（用于改善实景图可用性）
    static func generateCoordinateVariations(
        latitude: Double,
        longitude: Double,
        radiusMeters: Double = 20.0
    ) -> [(latitude: Double, longitude: Double, description: String)] {

        // 计算度数偏移（大约1米 ≈ 0.00001度）
        let meterToDegree = 0.00001
        let offset = radiusMeters * meterToDegree

        return [
            (latitude, longitude, "原始坐标"),
            (latitude + offset, longitude, "向北偏移\(Int(radiusMeters))米"),
            (latitude - offset, longitude, "向南偏移\(Int(radiusMeters))米"),
            (latitude, longitude + offset, "向东偏移\(Int(radiusMeters))米"),
            (latitude, longitude - offset, "向西偏移\(Int(radiusMeters))米"),
            (latitude + offset/2, longitude + offset/2, "向东北偏移\(Int(radiusMeters/2))米"),
            (latitude - offset/2, longitude + offset/2, "向东南偏移\(Int(radiusMeters/2))米"),
            (latitude + offset/2, longitude - offset/2, "向西北偏移\(Int(radiusMeters/2))米"),
            (latitude - offset/2, longitude - offset/2, "向西南偏移\(Int(radiusMeters/2))米"),
        ]
    }

    /// 🧪 测试Gatewood Village坐标变化
    static func testGatewoodVillageCoordinates() {
        print("🧪 开始测试Gatewood Village坐标变化")
        print(String(repeating: "=", count: 60))

        let originalLat = 37.6546524
        let originalLng = -122.4539704

        print("📍 原始地址: 500 King Drive, Daly City, CA 94015")
        print("📍 原始坐标: (\(originalLat), \(originalLng))")
        print("")

        let variations = generateCoordinateVariations(
            latitude: originalLat,
            longitude: originalLng,
            radiusMeters: 25.0
        )

        print("🎯 生成的坐标变化（用于改善实景图可用性）:")
        for (index, variation) in variations.enumerated() {
            print("   \(index + 1). \(variation.description)")
            print("      坐标: (\(String(format: "%.7f", variation.latitude)), \(String(format: "%.7f", variation.longitude)))")
        }
        print("")

        print("💡 使用建议:")
        print("   1. 如果当前坐标没有实景图，可以尝试这些微调坐标")
        print("   2. 通常向街道中心或建筑物入口方向的坐标有更好的实景图覆盖")
        print("   3. 偏移距离控制在25米内，确保仍然指向正确的地址")
        print("")

        print("🎯 Gatewood Village坐标测试完成！")
    }

    // MARK: - 🔄 数据迁移功能

    /// 为现有地址提取并更新单位信息
    /// - Parameter context: SwiftData模型上下文
    /// - Returns: 更新的地址数量
    @MainActor
    static func migrateUnitNumbers(context: ModelContext) throws -> Int {
        let descriptor = FetchDescriptor<DeliveryPoint>()
        let allPoints = try context.fetch(descriptor)

        var updatedCount = 0

        for point in allPoints {
            // 跳过已经有单位信息的地址
            if point.unitNumber != nil && !point.unitNumber!.isEmpty {
                continue
            }

            // 尝试从主要地址中提取单位信息
            let primaryAddress = point.primaryAddress
            if let extractedUnit = extractUnitNumber(from: primaryAddress) {
                point.unitNumber = extractedUnit
                updatedCount += 1
                print("🔄 迁移单位信息: \(extractedUnit) <- \(primaryAddress)")
            }
        }

        if updatedCount > 0 {
            try context.save()
            print("✅ 数据迁移完成：更新了 \(updatedCount) 个地址的单位信息")
        } else {
            print("ℹ️ 数据迁移完成：没有需要更新的地址")
        }

        return updatedCount
    }

    /// 批量更新指定路线的单位信息
    /// - Parameters:
    ///   - route: 目标路线
    ///   - context: SwiftData模型上下文
    /// - Returns: 更新的地址数量
    @MainActor
    static func migrateUnitNumbers(for route: Route, context: ModelContext) throws -> Int {
        var updatedCount = 0

        for point in route.points {
            // 跳过已经有单位信息的地址
            if point.unitNumber != nil && !point.unitNumber!.isEmpty {
                continue
            }

            // 尝试从主要地址中提取单位信息
            let primaryAddress = point.primaryAddress
            if let extractedUnit = extractUnitNumber(from: primaryAddress) {
                point.unitNumber = extractedUnit
                updatedCount += 1
                print("🔄 路线迁移单位信息: \(extractedUnit) <- \(primaryAddress)")
            }
        }

        if updatedCount > 0 {
            try context.save()
            print("✅ 路线数据迁移完成：更新了 \(updatedCount) 个地址的单位信息")
        } else {
            print("ℹ️ 路线数据迁移完成：没有需要更新的地址")
        }

        return updatedCount
    }

    // 向后兼容的基于用户位置验证方法（调用新的全球验证方法）
    func validateLocationBasedOnUserPosition(_ userLocation: CLLocationCoordinate2D?) {
        // 直接调用新的验证方法
        validateCoordinatesGlobally(userLocation: userLocation)

        // 如果提供了用户位置，计算并存储距离
        if let userLocation = userLocation {
            let userLocationObj = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
            let pointLocation = CLLocation(latitude: self.latitude, longitude: self.longitude)
            self.distanceFromUserLocation = userLocationObj.distance(from: pointLocation)
        }
    }

    // MARK: - Equatable
    static func == (lhs: DeliveryPoint, rhs: DeliveryPoint) -> Bool {
        return lhs.id == rhs.id
    }
}
