//
//  UserAddressDatabase.swift
//  NaviBatch
//
//  Created for v1.0.5 - User Address Database System
//  Copyright © 2024 NaviBatch. All rights reserved.
//

import Foundation
import SwiftData
import CoreLocation
import CoreLocation
import SwiftUI

// MARK: - Address Database Models

enum AddressSource: String, CaseIterable, Codable {
    case manual = "manual"
    case fileImport = "file_import"
    case screenshot = "screenshot"
    case amazonFlex = "amazon_flex"
    case imile = "imile"
    case aiCorrection = "ai_correction" // 🤖 AI智能修复的地址
}

enum AddressMatchType {
    case exact
    case normalized
    case fuzzy(similarity: Float)
}

struct ValidatedAddressResult {
    let coordinate: CLLocationCoordinate2D
    let matchType: AddressMatchType
    let lastUsed: Date
    let usageCount: Int
    let source: AddressSource
    let confidence: Float
}

struct AddressDatabaseStats {
    let totalCount: Int
    let hitRate: Float
    let totalSaved: TimeInterval
    let mostUsedAddresses: [String]
    let databaseSize: Int64 // bytes
}

// MARK: - SwiftData Model for User Address Database
// 🏠 用户地址数据库 - 持久化保存用户所有验证过的地址，提高重复地址的处理速度

@Model
final class ValidatedAddress: Identifiable {
    var id: UUID
    var originalAddress: String
    var normalizedAddress: String
    var latitude: Double
    var longitude: Double
    var confidence: Float
    var source: String // AddressSource.rawValue
    var createdAt: Date
    var lastUsedAt: Date
    var usageCount: Int
    var isVerified: Bool

    init(
        originalAddress: String,
        normalizedAddress: String,
        latitude: Double,
        longitude: Double,
        confidence: Float,
        source: AddressSource,
        createdAt: Date = Date(),
        lastUsedAt: Date = Date(),
        usageCount: Int = 1,
        isVerified: Bool = true
    ) {
        self.id = UUID()
        self.originalAddress = originalAddress
        self.normalizedAddress = normalizedAddress
        self.latitude = latitude
        self.longitude = longitude
        self.confidence = confidence
        self.source = source.rawValue
        self.createdAt = createdAt
        self.lastUsedAt = lastUsedAt
        self.usageCount = usageCount
        self.isVerified = isVerified
    }
}

// MARK: - User Address Database Manager

/// 用户地址数据库管理器 - 🏠 持久化地址存储系统
///
/// 功能说明：
/// - 保存用户所有验证过的地址和坐标，避免重复调用地理编码API
/// - 对于固定区域派送的用户，地址重复率可达100%，大幅提升处理速度
/// - 支持精确匹配、标准化匹配等多种查找方式
/// - 自动管理数据库大小，删除最少使用的旧地址
/// - 提供详细的使用统计和性能分析
/// - 🎯 重要：只保存经过完整geocoding验证的高质量地址
@MainActor
class UserAddressDatabase: ObservableObject {

    // MARK: - Properties

    @Published var isEnabled: Bool {
        didSet {
            UserDefaults.standard.set(isEnabled, forKey: "UserAddressDatabaseEnabled")
        }
    }

    @Published var databaseStats = AddressDatabaseStats(
        totalCount: 0,
        hitRate: 0.0,
        totalSaved: 0,
        mostUsedAddresses: [],
        databaseSize: 0
    )

    private let modelContext: ModelContext
    private let maxDatabaseSize = 10_000 // Maximum number of stored addresses
    private let minConfidenceThreshold: Float = 0.8

    // 🚀 优化：减少重复日志的静态变量
    #if DEBUG
    private static var loggedDatabaseHits: Set<String> = []
    private static var loggedNormalizedHits: Set<String> = []
    private static var loggedDatabaseMisses: Set<String> = []
    #endif

    // MARK: - Singleton

    static let shared = UserAddressDatabase()

    // MARK: - Initialization

    private init() {
        // 🚀 默认启用地址数据库，除非用户明确禁用
        if UserDefaults.standard.object(forKey: "UserAddressDatabaseEnabled") == nil {
            // 首次运行，默认启用数据库
            UserDefaults.standard.set(true, forKey: "UserAddressDatabaseEnabled")
        }
        self.isEnabled = UserDefaults.standard.bool(forKey: "UserAddressDatabaseEnabled")

        // 使用全局的 ModelContext
        self.modelContext = getPersistentContainer().mainContext

        // Update database stats
        Task {
            await updateDatabaseStats()
        }
    }

    // MARK: - Public Methods

    /// 查找用户地址数据库中的地址坐标
    func getValidatedAddress(for address: String) async -> ValidatedAddressResult? {
        guard isEnabled else {
            print("🏠 USER_ADDRESS_DB: 用户地址数据库已禁用")
            return nil
        }

        print("🏠 USER_ADDRESS_DB: 查询地址数据库: \(address)")

        // 1. 尝试精确匹配
        if let result = await exactMatch(address) {
            // 🚀 优化：减少重复的数据库日志
            #if DEBUG
            if !Self.loggedDatabaseHits.contains(address) {
                Self.loggedDatabaseHits.insert(address)
                print("🏠 USER_ADDRESS_DB: ✅ 精确匹配命中: \(address) (使用次数: \(result.usageCount))")

                // 限制缓存大小
                if Self.loggedDatabaseHits.count > 50 {
                    Self.loggedDatabaseHits.removeAll()
                }
            }
            #endif

            await updateUsage(for: result)
            return ValidatedAddressResult(
                coordinate: CLLocationCoordinate2D(latitude: result.latitude, longitude: result.longitude),
                matchType: .exact,
                lastUsed: result.lastUsedAt,
                usageCount: result.usageCount,
                source: AddressSource(rawValue: result.source) ?? .manual,
                confidence: result.confidence
            )
        }

        // 2. 尝试标准化匹配
        let normalizedAddress = normalizeAddress(address)
        if let result = await normalizedMatch(normalizedAddress) {
            // 🚀 优化：减少重复的标准化匹配日志
            #if DEBUG
            let logKey = "\(address)->\(normalizedAddress)"
            if !Self.loggedNormalizedHits.contains(logKey) {
                Self.loggedNormalizedHits.insert(logKey)
                print("🏠 USER_ADDRESS_DB: ✅ 标准化匹配命中: \(address) -> \(normalizedAddress) (使用次数: \(result.usageCount))")

                // 限制缓存大小
                if Self.loggedNormalizedHits.count > 50 {
                    Self.loggedNormalizedHits.removeAll()
                }
            }
            #endif

            await updateUsage(for: result)
            return ValidatedAddressResult(
                coordinate: CLLocationCoordinate2D(latitude: result.latitude, longitude: result.longitude),
                matchType: .normalized,
                lastUsed: result.lastUsedAt,
                usageCount: result.usageCount,
                source: AddressSource(rawValue: result.source) ?? .manual,
                confidence: result.confidence
            )
        }

        // 🚀 优化：减少重复的数据库未命中日志
        #if DEBUG
        if !Self.loggedDatabaseMisses.contains(address) {
            Self.loggedDatabaseMisses.insert(address)
            print("🏠 USER_ADDRESS_DB: ❌ 数据库未命中: \(address) (需要调用地理编码API)")

            // 限制缓存大小
            if Self.loggedDatabaseMisses.count > 50 {
                Self.loggedDatabaseMisses.removeAll()
            }
        }
        #endif

        return nil
    }

    /// 保存验证过的地址到用户地址数据库
    /// 🎯 重要：只有经过完整geocoding验证的地址才会被保存
    /// 🧹 自动清理：保存前会自动清理地址中的元数据信息（订单号、排序信息等）
    /// - Parameters:
    ///   - address: 原始地址字符串（可能包含元数据）
    ///   - coordinate: 验证过的坐标
    ///   - source: 地址来源（手动输入、文件导入、图片识别等）
    ///   - confidence: 置信度（0.0-1.0）
    func saveValidatedAddress(
        _ address: String,
        coordinate: CLLocationCoordinate2D,
        source: AddressSource,
        confidence: Float = 1.0
    ) async {
        guard isEnabled else {
            print("🏠 USER_ADDRESS_DB: 用户地址数据库已禁用，跳过保存")
            return
        }
        guard confidence >= minConfidenceThreshold else {
            print("🏠 USER_ADDRESS_DB: 置信度过低(\(confidence) < \(minConfidenceThreshold))，跳过保存: \(address)")
            return
        }
        guard coordinate.latitude != 0 || coordinate.longitude != 0 else {
            print("🏠 USER_ADDRESS_DB: 坐标无效，跳过保存: \(address)")
            return
        }

        // 🧹 关键修复：保存前清理地址中的元数据
        let cleanedAddress = cleanAddressMetadata(address)

        // 🎯 应用Apple Maps数据库存储格式化（移除ZIP码、国家后缀等）
        let baseAddress = cleanedAddress.isEmpty ? address : cleanedAddress
        let addressToSave = AppleMapsAddressFormatter.formatForDatabaseStorage(baseAddress)

        // 记录清理过程（仅在有变化时）
        if cleanedAddress != address {
            print("🧹 USER_ADDRESS_DB: 地址清理: '\(address)' -> '\(cleanedAddress)'")
        }

        print("🏠 USER_ADDRESS_DB: 保存验证地址到数据库: \(addressToSave) -> (\(coordinate.latitude), \(coordinate.longitude)), 来源: \(source.rawValue)")

        do {
            // 检查是否已存在相同的清理后地址
            let descriptor = FetchDescriptor<ValidatedAddress>(
                predicate: #Predicate { $0.originalAddress == addressToSave }
            )

            let existingResults = try modelContext.fetch(descriptor)

            if let existing = existingResults.first {
                // 更新现有记录
                print("🏠 USER_ADDRESS_DB: 更新现有地址记录: \(addressToSave), 使用次数: \(existing.usageCount + 1)")
                existing.lastUsedAt = Date()
                existing.usageCount += 1
                existing.confidence = max(existing.confidence, confidence)
                // 更新来源信息（如果新来源更可靠）
                if source != .manual && existing.source == AddressSource.manual.rawValue {
                    existing.source = source.rawValue
                }
            } else {
                // 创建新记录
                print("🏠 USER_ADDRESS_DB: 创建新地址记录: \(addressToSave)")
                let newAddress = ValidatedAddress(
                    originalAddress: addressToSave,
                    normalizedAddress: normalizeAddress(addressToSave),
                    latitude: coordinate.latitude,
                    longitude: coordinate.longitude,
                    confidence: confidence,
                    source: source
                )
                modelContext.insert(newAddress)
            }

            try modelContext.save()
            print("🏠 USER_ADDRESS_DB: ✅ 验证地址保存成功: \(addressToSave)")

            // 检查数据库大小限制
            await enforceMaxDatabaseSize()

        } catch {
            print("🏠 USER_ADDRESS_DB: ❌ 地址保存失败: \(addressToSave), 错误: \(error)")
        }

        // 更新统计信息
        await updateDatabaseStats()
    }

    /// 批量查找验证地址
    func batchGetValidatedAddresses(for addresses: [String]) async -> [String: ValidatedAddressResult] {
        guard isEnabled else { return [:] }

        var results: [String: ValidatedAddressResult] = [:]

        for address in addresses {
            if let validated = await getValidatedAddress(for: address) {
                results[address] = validated
            }
        }

        return results
    }

    /// 🚀 批量保存验证地址 - 性能优化版本
    /// 一次性保存多个地址，减少CoreData保存次数，大幅提升性能
    func batchSaveValidatedAddresses(_ addressData: [(String, CLLocationCoordinate2D, AddressSource, Float)]) async {
        guard isEnabled else {
            print("🏠 USER_ADDRESS_DB: 用户地址数据库已禁用，跳过批量保存")
            return
        }

        guard !addressData.isEmpty else { return }

        print("🏠 USER_ADDRESS_DB: 🚀 开始批量保存 \(addressData.count) 个验证地址")

        do {
            var savedCount = 0
            var updatedCount = 0

            for (address, coordinate, source, confidence) in addressData {
                // 跳过低置信度和无效坐标
                guard confidence >= minConfidenceThreshold else { continue }
                guard coordinate.latitude != 0 || coordinate.longitude != 0 else { continue }

                // 清理地址并应用格式化
                let cleanedAddress = cleanAddressMetadata(address)
                let baseAddress = cleanedAddress.isEmpty ? address : cleanedAddress
                let addressToSave = AppleMapsAddressFormatter.formatForDatabaseStorage(baseAddress)

                // 检查是否已存在
                let descriptor = FetchDescriptor<ValidatedAddress>(
                    predicate: #Predicate { $0.originalAddress == addressToSave }
                )

                let existingResults = try modelContext.fetch(descriptor)

                if let existing = existingResults.first {
                    // 更新现有记录
                    existing.lastUsedAt = Date()
                    existing.usageCount += 1
                    existing.confidence = max(existing.confidence, confidence)
                    if source != .manual && existing.source == AddressSource.manual.rawValue {
                        existing.source = source.rawValue
                    }
                    updatedCount += 1
                } else {
                    // 创建新记录
                    let newAddress = ValidatedAddress(
                        originalAddress: addressToSave,
                        normalizedAddress: normalizeAddress(addressToSave),
                        latitude: coordinate.latitude,
                        longitude: coordinate.longitude,
                        confidence: confidence,
                        source: source
                    )
                    modelContext.insert(newAddress)
                    savedCount += 1
                }
            }

            // 🚀 关键优化：一次性保存所有更改
            try modelContext.save()
            print("🏠 USER_ADDRESS_DB: ✅ 批量保存完成: 新增 \(savedCount), 更新 \(updatedCount)")

            // 检查数据库大小限制
            await enforceMaxDatabaseSize()

        } catch {
            print("🏠 USER_ADDRESS_DB: ❌ 批量保存失败: \(error)")
        }

        // 更新统计信息
        await updateDatabaseStats()
    }

    /// 清除所有地址数据库
    func clearDatabase() async {
        do {
            let descriptor = FetchDescriptor<ValidatedAddress>()
            let allAddresses = try modelContext.fetch(descriptor)

            for address in allAddresses {
                modelContext.delete(address)
            }

            try modelContext.save()
            print("🏠 USER_ADDRESS_DB: 已清空用户地址数据库")
        } catch {
            print("🏠 USER_ADDRESS_DB: 清空数据库失败: \(error)")
        }

        await updateDatabaseStats()
    }

    /// 🧹 批量清理现有地址数据中的元数据
    /// 清理数据库中已保存的包含元数据的地址
    func cleanExistingAddressMetadata() async -> (cleaned: Int, total: Int) {
        var cleanedCount = 0
        var totalCount = 0

        do {
            let descriptor = FetchDescriptor<ValidatedAddress>()
            let allAddresses = try modelContext.fetch(descriptor)
            totalCount = allAddresses.count

            print("🧹 USER_ADDRESS_DB: 开始清理现有地址数据，总计 \(totalCount) 条记录")

            for address in allAddresses {
                let originalAddress = address.originalAddress
                let cleanedAddress = cleanAddressMetadata(originalAddress)
                let baseAddress = cleanedAddress.isEmpty ? originalAddress : cleanedAddress
                let formattedAddress = AppleMapsAddressFormatter.formatForDatabaseStorage(baseAddress)

                // 如果地址有变化，更新记录
                if formattedAddress != originalAddress {
                    print("🧹 USER_ADDRESS_DB: 清理地址: '\(originalAddress)' -> '\(formattedAddress)'")

                    address.originalAddress = formattedAddress
                    address.normalizedAddress = normalizeAddress(formattedAddress)
                    cleanedCount += 1
                }
            }

            if cleanedCount > 0 {
                try modelContext.save()
                print("🧹 USER_ADDRESS_DB: ✅ 批量清理完成，清理了 \(cleanedCount)/\(totalCount) 条记录")
            } else {
                print("🧹 USER_ADDRESS_DB: ✅ 无需清理，所有地址都是纯净的")
            }

        } catch {
            print("🧹 USER_ADDRESS_DB: ❌ 批量清理失败: \(error)")
        }

        await updateDatabaseStats()
        return (cleaned: cleanedCount, total: totalCount)
    }

    /// 获取数据库统计信息
    func updateDatabaseStats() async {
        let totalCount = await getTotalAddressCount()
        let hitRate = await calculateHitRate()
        let mostUsed = await getMostUsedAddresses()
        let databaseSize = await calculateDatabaseSize()

        self.databaseStats = AddressDatabaseStats(
            totalCount: totalCount,
            hitRate: hitRate,
            totalSaved: 0, // TODO: 实现时间节省计算
            mostUsedAddresses: mostUsed,
            databaseSize: databaseSize
        )
    }

    // MARK: - Private Methods

    /// 清理地址中的元数据并标准化
    /// 🎯 重要：移除所有订单号、排序信息等元数据，只保留纯净的地址信息
    private func cleanAndNormalizeAddress(_ address: String) -> String {
        var cleanedAddress = address

        // 1. 🧹 首先清理元数据（订单号、排序信息等）
        cleanedAddress = cleanAddressMetadata(cleanedAddress)

        // 2. 📝 然后进行地址标准化
        return cleanedAddress
            .lowercased()
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .replacingOccurrences(of: "street", with: "st")
            .replacingOccurrences(of: "road", with: "rd")
            .replacingOccurrences(of: "avenue", with: "ave")
            .replacingOccurrences(of: "drive", with: "dr")
            .replacingOccurrences(of: "lane", with: "ln")
            .replacingOccurrences(of: "unit ", with: "u")
            .replacingOccurrences(of: "/", with: " ")
            .replacingOccurrences(of: ",", with: "")
    }

    /// 清理地址中的元数据信息
    /// 移除订单号、排序信息、追踪号等不属于地址本身的信息
    private func cleanAddressMetadata(_ address: String) -> String {
        var cleanedAddress = address

        // 移除第三方sort number模式（如 ISORT:8, D90, D91等）
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\b[A-Z]+:\\d+\\b",
            with: "",
            options: .regularExpression
        )

        // 移除单独的字母+数字组合（如 D90, D91, D146等）
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\b[A-Z]\\d+\\b",
            with: "",
            options: .regularExpression
        )

        // 🎯 关键：移除所有管道符号（|）后的元数据信息
        // 这包括 |SORT:2|TRACK:#b.111.ov|TIME:... 等所有元数据
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\|[^|\\n]*",
            with: "",
            options: .regularExpression
        )

        // 移除多余的空格和标点
        cleanedAddress = cleanedAddress.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        )

        // 移除开头和结尾的逗号、空格和 "|"
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)
        cleanedAddress = cleanedAddress.trimmingCharacters(in: CharacterSet(charactersIn: ",|"))
        cleanedAddress = cleanedAddress.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleanedAddress.isEmpty ? address : cleanedAddress
    }

    /// 🎯 地址标准化：应用Apple Maps格式化，确保数据库存储格式一致
    private func normalizeAddress(_ address: String) -> String {
        // 1. 首先清理元数据
        let cleanedAddress = cleanAddressMetadata(address)

        // 2. 应用Apple Maps数据库存储格式化（移除ZIP码、国家后缀等）
        let formattedAddress = AppleMapsAddressFormatter.formatForDatabaseStorage(cleanedAddress.isEmpty ? address : cleanedAddress)

        // 3. 基本标准化处理
        return formattedAddress
            .lowercased()
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
            .replacingOccurrences(of: "/", with: " ")
    }

    private func exactMatch(_ address: String) async -> ValidatedAddress? {
        do {
            var descriptor = FetchDescriptor<ValidatedAddress>(
                predicate: #Predicate { $0.originalAddress == address }
            )
            descriptor.fetchLimit = 1

            return try modelContext.fetch(descriptor).first
        } catch {
            print("🏠 USER_ADDRESS_DB: 精确匹配错误: \(error)")
            return nil
        }
    }

    private func normalizedMatch(_ normalizedAddress: String) async -> ValidatedAddress? {
        do {
            var descriptor = FetchDescriptor<ValidatedAddress>(
                predicate: #Predicate { $0.normalizedAddress == normalizedAddress }
            )
            descriptor.fetchLimit = 1

            return try modelContext.fetch(descriptor).first
        } catch {
            print("🏠 USER_ADDRESS_DB: 标准化匹配错误: \(error)")
            return nil
        }
    }

    private func updateUsage(for address: ValidatedAddress) async {
        do {
            address.lastUsedAt = Date()
            address.usageCount += 1
            try modelContext.save()
        } catch {
            print("🏠 USER_ADDRESS_DB: 更新使用记录失败: \(error)")
        }
    }

    private func enforceMaxDatabaseSize() async {
        do {
            let descriptor = FetchDescriptor<ValidatedAddress>(
                sortBy: [SortDescriptor(\.lastUsedAt, order: .forward)]
            )

            let allAddresses = try modelContext.fetch(descriptor)

            if allAddresses.count > maxDatabaseSize {
                let toDelete = allAddresses.prefix(allAddresses.count - maxDatabaseSize)

                for address in toDelete {
                    modelContext.delete(address)
                }

                try modelContext.save()
                print("🏠 USER_ADDRESS_DB: 执行数据库大小限制，删除了 \(toDelete.count) 个旧记录")
            }
        } catch {
            print("🏠 USER_ADDRESS_DB: 执行数据库大小限制失败: \(error)")
        }
    }

    private func getTotalAddressCount() async -> Int {
        do {
            let descriptor = FetchDescriptor<ValidatedAddress>()
            return try modelContext.fetchCount(descriptor)
        } catch {
            print("🏠 USER_ADDRESS_DB: 获取地址总数失败: \(error)")
            return 0
        }
    }

    private func calculateHitRate() async -> Float {
        // TODO: 实现命中率计算
        // 需要跟踪查询次数和命中次数
        return 0.0
    }

    private func getMostUsedAddresses() async -> [String] {
        do {
            var descriptor = FetchDescriptor<ValidatedAddress>(
                sortBy: [SortDescriptor(\.usageCount, order: .reverse)]
            )
            descriptor.fetchLimit = 5

            let results = try modelContext.fetch(descriptor)
            return results.map { $0.originalAddress }
        } catch {
            print("🏠 USER_ADDRESS_DB: 获取最常用地址失败: \(error)")
            return []
        }
    }

    private func calculateDatabaseSize() async -> Int64 {
        // TODO: 实现数据库大小计算
        return 0
    }
}

// MARK: - 向后兼容性支持
// 为了平滑过渡，提供旧接口的别名

extension UserAddressDatabase {
    /// 向后兼容：getCachedCoordinate -> getValidatedAddress
    func getCachedCoordinate(for address: String) async -> ValidatedAddressResult? {
        return await getValidatedAddress(for: address)
    }

    /// 向后兼容：cacheAddress -> saveValidatedAddress
    func cacheAddress(
        _ address: String,
        coordinate: CLLocationCoordinate2D,
        source: AddressSource,
        confidence: Float = 1.0
    ) async {
        await saveValidatedAddress(address, coordinate: coordinate, source: source, confidence: confidence)
    }
}
