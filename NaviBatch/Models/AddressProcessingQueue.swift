import Foundation
import CoreLocation
import SwiftData
import os.log

/// 地址处理任务
struct AddressTask: Identifiable {
    let id: UUID
    let address: String
    var coordinate: CLLocationCoordinate2D?
    var status: AddressProcessingStatus
    var retryCount: Int
    var type: AddressPointType
    var validationResult: AddressValidationResult?

    init(
        id: UUID = UUID(),
        address: String,
        coordinate: CLLocationCoordinate2D? = nil,
        status: AddressProcessingStatus = .pending,
        retryCount: Int = 0,
        type: AddressPointType = .stop,
        validationResult: AddressValidationResult? = nil
    ) {
        self.id = id
        self.address = address
        self.coordinate = coordinate
        self.status = status
        self.retryCount = retryCount
        self.type = type
        self.validationResult = validationResult
    }
}

/// 地址处理状态
enum AddressProcessingStatus {
    case pending    // 等待处理
    case processing // 处理中
    case completed  // 已完成
    case failed     // 失败
}

/// 地址处理队列 - 管理批量地址的地理编码和添加
class AddressProcessingQueue {
    // 单例模式
    static let shared = AddressProcessingQueue()

    // 处理队列
    private var queue: [AddressTask] = []

    // 处理状态
    private var isProcessing = false

    // 定时器
    private var timer: Timer?

    // 每分钟最大请求数（MapKit限制为50，我们设置为45留有余量）
    private let maxRequestsPerMinute = 45

    // 每次处理的请求数（改为串行处理）
    private let batchSize = 1

    // 动态处理间隔（根据成功率自动调整）
    private var processingInterval: TimeInterval = 0.8 // 初始间隔
    private let minInterval: TimeInterval = 0.3 // 最小间隔
    private let maxInterval: TimeInterval = 2.0 // 最大间隔

    // 性能统计
    private var successCount: Int = 0
    private var failureCount: Int = 0
    private var lastAdjustmentTime: Date = Date()

    // 当前路线和模型上下文
    private var currentRoute: Route?
    private var modelContext: ModelContext?
    private var userSelectedAppType: DeliveryAppType = .manual // 🎯 新增：用户选择的应用类型

    // 状态更新回调
    var onStatusUpdate: (() -> Void)?

    // 私有初始化方法（单例模式）
    private init() {}

    // 日志辅助函数
    private func logInfo(_ message: String) {
        print("[INFO] AddressProcessingQueue - \(message)")
    }

    private func logError(_ message: String) {
        print("[ERROR] AddressProcessingQueue - \(message)")
    }

    // 🍎 保持Apple Maps原生格式 - 不再"简化"单元号
    private func simplifyAddressForGeocoding(_ address: String) -> String {
        // Apple Maps原生支持 1/12 格式，无需简化
        // 只做基本的格式清理
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        // 移除空组件并重新组合
        let cleanComponents = components.filter { !$0.isEmpty }
        let cleanedAddress = cleanComponents.joined(separator: ", ")

        if cleanedAddress != address {
            logInfo("🍎 地址格式清理: '\(address)' -> '\(cleanedAddress)'")
        }

        return cleanedAddress
    }

    // 添加地址到队列
    func addAddresses(_ addresses: [String], type: AddressPointType, to route: Route, context: ModelContext, userSelectedAppType: DeliveryAppType = .manual) {
        // 确保在主线程上执行
        if Thread.isMainThread {
            // 检查订阅限制
            let subscriptionManager = SubscriptionManager.shared
            let maxAllowed = subscriptionManager.currentTier.maxStopsPerRoute
            let currentCount = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }.count
            let remainingSlots = maxAllowed - currentCount

            // 如果地址数量超过剩余槽位，截断地址列表
            var addressesToProcess = addresses
            if addresses.count > remainingSlots {
                logInfo("地址数量(\(addresses.count))超过剩余槽位(\(remainingSlots))，将截断地址列表")
                addressesToProcess = Array(addresses.prefix(remainingSlots))

                // 如果没有剩余槽位，直接返回
                if remainingSlots <= 0 {
                    logInfo("没有剩余槽位，无法添加更多地址")
                    return
                }
            }

            // 保存当前路线、上下文和用户选择的应用类型
            self.currentRoute = route
            self.modelContext = context
            self.userSelectedAppType = userSelectedAppType
            print("🎯 DEBUG: AddressProcessingQueue - 保存用户选择的应用类型: \(userSelectedAppType.rawValue) (\(userSelectedAppType.displayName))")

            // 创建任务并添加到队列
            let tasks = addressesToProcess.map { address in
                AddressTask(address: address, type: type)
            }

            queue.append(contentsOf: tasks)
            logInfo("添加了 \(tasks.count) 个地址到处理队列，当前队列长度: \(queue.count)")

            // 通知UI更新
            onStatusUpdate?()

            // 如果队列未在处理，开始处理
            if !isProcessing {
                startProcessing()
            }
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.addAddresses(addresses, type: type, to: route, context: context, userSelectedAppType: userSelectedAppType)
            }
        }
    }

    // 开始处理队列 - 优化版本：动态调整间隔
    private func startProcessing() {
        guard !isProcessing else { return }

        isProcessing = true
        logInfo("开始智能处理地址队列 (初始间隔: \(processingInterval)秒)")

        // 重置统计
        successCount = 0
        failureCount = 0
        lastAdjustmentTime = Date()

        // 创建定时器，使用动态间隔
        scheduleNextBatch()

        // 立即处理第一批
        processBatch()
    }

    // 调度下一批处理
    private func scheduleNextBatch() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: processingInterval, repeats: false) { [weak self] _ in
            self?.processBatch()
        }
    }

    // 动态调整处理间隔
    private func adjustProcessingInterval() {
        let now = Date()
        let timeSinceLastAdjustment = now.timeIntervalSince(lastAdjustmentTime)

        // 每10秒调整一次
        guard timeSinceLastAdjustment >= 10.0 else { return }

        let totalRequests = successCount + failureCount
        guard totalRequests >= 5 else { return } // 至少需要5个样本

        let successRate = Double(successCount) / Double(totalRequests)
        let oldInterval = processingInterval

        if successRate >= 0.9 {
            // 成功率高，可以加快速度
            processingInterval = max(minInterval, processingInterval * 0.8)
            logInfo("🚀 成功率高(\(Int(successRate * 100))%)，加快处理速度: \(oldInterval)s -> \(processingInterval)s")
        } else if successRate <= 0.6 {
            // 成功率低，需要放慢速度
            processingInterval = min(maxInterval, processingInterval * 1.3)
            logInfo("🐌 成功率低(\(Int(successRate * 100))%)，放慢处理速度: \(oldInterval)s -> \(processingInterval)s")
        } else {
            // 成功率中等，微调
            processingInterval = min(maxInterval, max(minInterval, processingInterval * 1.1))
            logInfo("⚖️ 成功率中等(\(Int(successRate * 100))%)，微调间隔: \(oldInterval)s -> \(processingInterval)s")
        }

        // 重置统计
        successCount = 0
        failureCount = 0
        lastAdjustmentTime = now
    }

    // 处理一批地址 - 优化版本：动态调度
    private func processBatch() {
        guard !queue.isEmpty, isProcessing else {
            // 队列为空或已停止处理，停止定时器
            stopProcessing()
            return
        }

        // 取出一批任务（最多batchSize个）
        let batchCount = min(batchSize, queue.count)
        let batch = Array(queue.prefix(batchCount))

        // 更新队列
        queue.removeFirst(batchCount)

        logInfo("开始处理地址 (\(batchCount)/\(queue.count + batchCount)): \(batch.first?.address ?? "未知地址")")

        // 使用 TaskGroup 来并行处理批次中的所有任务
        Task {
            await withTaskGroup(of: Bool.self) { group in
                for taskIndex in 0..<batch.count {
                    group.addTask {
                        return await self.processTaskWithResult(batch[taskIndex])
                    }
                }

                // 收集处理结果并更新统计
                for await success in group {
                    if success {
                        self.successCount += 1
                    } else {
                        self.failureCount += 1
                    }
                }
            }

            // 所有任务完成后，在主线程更新UI
            await MainActor.run {
                self.onStatusUpdate?()

                // 动态调整处理间隔
                self.adjustProcessingInterval()

                // 如果队列为空，停止处理；否则调度下一批
                if self.queue.isEmpty {
                    self.stopProcessing()
                } else {
                    self.scheduleNextBatch()
                }
            }
        }
    }

    // 处理单个任务并返回成功状态
    private func processTaskWithResult(_ task: AddressTask) async -> Bool {
        var taskCopy = task
        taskCopy.status = .processing

        let taskAddress = taskCopy.address
        let taskType = taskCopy.type
        let retryCount = taskCopy.retryCount

        // 使用统一验证服务验证地址
        let validationResult = await UnifiedAddressValidationService.shared.validateAddress(taskAddress)

        // 检查是否成功获取坐标
        let hasValidCoordinate = !(validationResult.coordinate.latitude == 0 && validationResult.coordinate.longitude == 0)

        // 创建任务对象，包含验证结果
        let taskStatus: AddressProcessingStatus = hasValidCoordinate ? .completed : .failed
        let completedTask = AddressTask(
            address: taskAddress,
            coordinate: validationResult.coordinate,
            status: taskStatus,
            retryCount: retryCount,
            type: taskType,
            validationResult: validationResult
        )

        // 添加到路线
        await MainActor.run {
            if let route = self.currentRoute, let context = self.modelContext {
                if !hasValidCoordinate {
                    logError("地址验证失败: \(taskAddress)，但仍将添加到路线中并标记为有问题")
                }
                self.addAddressToRoute(task: completedTask, route: route, context: context)
            }
        }

        return hasValidCoordinate
    }



    // 停止处理
    private func stopProcessing() {
        timer?.invalidate()
        timer = nil
        isProcessing = false
        logInfo("地址处理队列已停止，队列为空或处理已取消")

        // 通知UI更新
        onStatusUpdate?()
    }

    // 地理编码获取坐标 - 🍎 优先使用Apple Maps原生格式
    private func geocodeAddress(_ address: String) async -> CLLocationCoordinate2D? {
        logInfo("🎯 开始智能地理编码: \(address)")

        // 🍎 策略1: 原始地址（Apple Maps原生支持1/12格式）
        if let coordinate = await tryGeocodingWithStrategy(address, strategy: "原始地址", useOriginal: true) {
            return coordinate
        }

        // 策略2: 清理格式后的地址
        if let coordinate = await tryGeocodingWithStrategy(address, strategy: "清理地址") {
            return coordinate
        }

        // 策略3: 最小化地址（只保留街道名和郊区）
        if let coordinate = await tryGeocodingWithStrategy(address, strategy: "最小化地址", useMinimal: true) {
            return coordinate
        }

        logError("❌ 所有地理编码策略都失败了: \(address)")
        return nil
    }

    // 尝试特定策略的地理编码
    private func tryGeocodingWithStrategy(_ address: String, strategy: String, useOriginal: Bool = false, useMinimal: Bool = false) async -> CLLocationCoordinate2D? {
        let geocoder = CLGeocoder()

        do {
            var targetAddress: String

            if useOriginal {
                // 使用原始地址
                targetAddress = address
                logInfo("📍 策略[\(strategy)]: 使用原始地址")
            } else if useMinimal {
                // 最小化地址：只保留街道名和郊区
                targetAddress = createMinimalAddress(address)
                logInfo("📍 策略[\(strategy)]: 最小化地址 -> \(targetAddress)")
            } else {
                // 🍎 清理地址：保持Apple Maps格式
                targetAddress = simplifyAddressForGeocoding(address)
                logInfo("📍 策略[\(strategy)]: 清理地址 -> \(targetAddress)")
            }

            // 确保地址包含区域信息
            if !targetAddress.lowercased().contains("vic") && !targetAddress.lowercased().contains("australia") {
                targetAddress = "\(targetAddress), VIC, Australia"
                logInfo("📍 策略[\(strategy)]: 添加区域信息 -> \(targetAddress)")
            }

            // 🎯 地理编码，强制使用英文locale
            let placemarks = try await geocoder.geocodeAddressString(
                targetAddress,
                in: nil,
                preferredLocale: Locale(identifier: "en_US")
            )

            // 详细日志
            logInfo("📍 策略[\(strategy)]: 获得 \(placemarks.count) 个结果")
            for (index, placemark) in placemarks.enumerated() {
                if let location = placemark.location?.coordinate {
                    let country = placemark.country ?? "未知"
                    let locality = placemark.locality ?? "未知"
                    let thoroughfare = placemark.thoroughfare ?? "未知"
                    logInfo("📍   结果[\(index)]: \(thoroughfare), \(locality), \(country) -> (\(location.latitude), \(location.longitude))")
                }
            }

            // 优先选择Glen Waverley的结果，但要验证街道名匹配
            for (index, placemark) in placemarks.enumerated() {
                if let location = placemark.location?.coordinate,
                   placemark.locality?.lowercased().contains("glen waverley") == true,
                   placemark.country == "Australia" {

                    // 验证街道名是否匹配
                    let originalStreetName = extractStreetNameFromAddress(address)
                    let resultStreetName = placemark.thoroughfare ?? ""

                    logInfo("📍   验证结果[\(index)]: 原始街道[\(originalStreetName)] vs 结果街道[\(resultStreetName)]")

                    if isStreetNameMatch(originalStreetName, resultStreetName) {
                        logInfo("✅ 策略[\(strategy)]: 街道名匹配，使用此结果 -> (\(location.latitude), \(location.longitude))")
                        return location
                    } else {
                        logInfo("⚠️ 策略[\(strategy)]: 街道名不匹配，跳过此结果")
                    }
                }
            }

            // 其次选择澳大利亚的结果
            if let australiaPlacemark = placemarks.first(where: { $0.country == "Australia" }) {
                if let location = australiaPlacemark.location?.coordinate {
                    logInfo("✅ 策略[\(strategy)]: 成功找到澳大利亚地址 -> (\(location.latitude), \(location.longitude))")
                    return location
                }
            }

            // 最后使用第一个结果
            if let location = placemarks.first?.location?.coordinate {
                let country = placemarks.first?.country ?? "未知"
                logInfo("⚠️ 策略[\(strategy)]: 使用第一个结果 (\(country)) -> (\(location.latitude), \(location.longitude))")
                return location
            }

        } catch {
            logError("❌ 策略[\(strategy)]失败: \(error.localizedDescription)")
        }

        return nil
    }

    // 🚨 新增：更新现有DeliveryPoint的地址和坐标，保留其他信息
    private func updateExistingDeliveryPoint(_ existingPoint: DeliveryPoint, with task: AddressTask,
                                           cleanAddress: String, trackingNumber: String,
                                           customerName: String, deliveryTime: String,
                                           route: Route, context: ModelContext) throws {

        logInfo("🔄 开始更新现有点: ID=\(existingPoint.id), 原地址='\(existingPoint.primaryAddress)'")

        // 保存原始信息用于日志
        let originalAddress = existingPoint.primaryAddress
        let originalCoordinate = existingPoint.coordinate

        // 更新地址相关字段
        existingPoint.originalAddress = cleanAddress

        // 更新坐标（如果任务有有效坐标）
        if let newCoordinate = task.coordinate,
           !(newCoordinate.latitude == 0 && newCoordinate.longitude == 0) {
            existingPoint.latitude = newCoordinate.latitude
            existingPoint.longitude = newCoordinate.longitude
            logInfo("🗺️ 更新坐标: (\(originalCoordinate.latitude), \(originalCoordinate.longitude)) -> (\(newCoordinate.latitude), \(newCoordinate.longitude))")
        }

        // 更新追踪号码（如果有）
        if !trackingNumber.isEmpty {
            existingPoint.trackingNumber = trackingNumber
            logInfo("📦 更新追踪号码: '\(trackingNumber)'")
        }

        // 更新客户信息（如果有）
        if !customerName.isEmpty {
            existingPoint.customerName = customerName
            logInfo("👤 更新客户信息: '\(customerName)'")
        }

        // 更新配送时间（如果有）
        if !deliveryTime.isEmpty {
            existingPoint.scheduledDeliveryTime = deliveryTime
            logInfo("⏰ 更新配送时间: '\(deliveryTime)'")
        }

        // 🎯 重要：保留原有的sort_number, sorted_number, thirdPartySortNumber等关键信息
        // 这些字段不应该被修改

        // 保存更改
        try context.save()

        logInfo("✅ 成功更新现有点: '\(originalAddress)' -> '\(existingPoint.primaryAddress)'")

        // 通知UI更新
        NotificationCenter.default.post(name: Notification.Name("RoutePointsUpdated"), object: nil)
    }

    // 创建最小化地址（只保留街道名和郊区）
    private func createMinimalAddress(_ address: String) -> String {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        guard components.count >= 2 else { return address }

        // 提取街道名（去除门牌号）
        let firstComponent = components[0]
        let parts = firstComponent.components(separatedBy: " ")
        let streetName = parts.dropFirst().joined(separator: " ")

        // 只保留街道名和郊区
        return "\(streetName), \(components[1])"
    }

    // 从地址中提取街道名
    private func extractStreetNameFromAddress(_ address: String) -> String {
        let components = address.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        guard let firstComponent = components.first else { return "" }

        // 去除门牌号，提取街道名
        let parts = firstComponent.components(separatedBy: " ")
        return parts.dropFirst().joined(separator: " ").lowercased()
    }

    // 检查街道名是否匹配
    private func isStreetNameMatch(_ original: String, _ result: String) -> Bool {
        let normalizedOriginal = normalizeStreetNameForComparison(original)
        let normalizedResult = normalizeStreetNameForComparison(result)

        // 完全匹配
        if normalizedOriginal == normalizedResult {
            return true
        }

        // 检查是否包含主要关键词
        let originalWords = Set(normalizedOriginal.components(separatedBy: " ").filter { !$0.isEmpty })
        let resultWords = Set(normalizedResult.components(separatedBy: " ").filter { !$0.isEmpty })

        // 如果原始街道名的主要词汇都在结果中，认为匹配
        let intersection = originalWords.intersection(resultWords)
        let matchRatio = Double(intersection.count) / Double(originalWords.count)

        return matchRatio >= 0.7 // 70%的词汇匹配
    }

    // 标准化街道名用于比较
    private func normalizeStreetNameForComparison(_ streetName: String) -> String {
        var normalized = streetName.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        // 处理常见缩写
        let abbreviations = [
            ("parade", "pde"), ("street", "st"), ("road", "rd"), ("avenue", "ave"),
            ("drive", "dr"), ("court", "ct"), ("place", "pl"), ("lane", "ln"),
            ("close", "cl"), ("crescent", "cr"), ("grove", "gr")
        ]

        for (full, abbrev) in abbreviations {
            normalized = normalized.replacingOccurrences(of: " \(full)", with: " \(abbrev)")
            normalized = normalized.replacingOccurrences(of: " \(abbrev)", with: " \(abbrev)")
        }

        return normalized
    }

    // 添加地址到路线
    private func addAddressToRoute(task: AddressTask, route: Route, context: ModelContext) {
        do {
            // 不再需要检查坐标是否为nil，因为我们总是提供一个坐标（即使是默认的0,0）
            let coordinate = task.coordinate ?? CLLocationCoordinate2D(latitude: 0, longitude: 0)

            // 检查坐标是否有效（非0,0）
            let hasValidCoordinate = !(coordinate.latitude == 0 && coordinate.longitude == 0)

            // 🎯 使用DeliveryPointManager分离地址信息，获取完整的标签信息
            let addressInfo = DeliveryPointManager.shared.separateAddressAndTracking(task.address)
            let cleanAddress = addressInfo.address
            let thirdPartySortNumber = addressInfo.thirdPartySortNumber
            let trackingNumber = addressInfo.tracking
            let customerName = addressInfo.customer
            let deliveryTime = addressInfo.deliveryTime
            let extractedAppType = DeliveryAppType(rawValue: addressInfo.appType) ?? .manual

            // 🚨 新逻辑：检查是否需要更新现有点而不是创建新点
            if task.type == .stop && !thirdPartySortNumber.isEmpty {
                // 查找具有相同thirdPartySortNumber的现有点
                if let existingPoint = route.points.first(where: {
                    $0.thirdPartySortNumber == thirdPartySortNumber && !$0.isStartPoint && !$0.isEndPoint
                }) {
                    logInfo("🔄 找到现有点需要更新: sort_number=\(existingPoint.sort_number), thirdPartySortNumber='\(thirdPartySortNumber)'")

                    // 更新现有点的地址和坐标，保留其他信息
                    try updateExistingDeliveryPoint(existingPoint, with: task, cleanAddress: cleanAddress,
                                                  trackingNumber: trackingNumber, customerName: customerName,
                                                  deliveryTime: deliveryTime, route: route, context: context)
                    return
                }
            }

            // 检查重复（只对停靠点做校验，且没有找到需要更新的现有点）
            if task.type == .stop {
                // 🎯 改进的重复检测：提取基础地址进行比较
                let newBaseAddress = extractBaseAddress(cleanAddress)

                let stops = route.points.filter { !$0.isStartPoint && !$0.isEndPoint }
                let hasDuplicate = stops.contains { existingPoint in
                    let existingBaseAddress = extractBaseAddress(existingPoint.primaryAddress)
                    return areAddressesSimilar(newBaseAddress, existingBaseAddress)
                }

                if hasDuplicate {
                    logInfo("检测到重复停靠点地址，不添加: \(cleanAddress)")
                    return
                }
            }

            // 计算新点的 sort_number
            let allNumbers = route.points.map { $0.sort_number }
            let newNumber = (allNumbers.max() ?? 0) + 1

            // 🎯 修复：创建新点，通过结构化地址处理来正确设置各个字段
            let newPoint = DeliveryPoint(
                sort_number: newNumber,
                originalAddress: cleanAddress, // 🎯 保存清理后的原始AI扫描地址
                coordinate: coordinate,
                isStartPoint: task.type == .start,
                isEndPoint: task.type == .end
            )

            // 🎯 设置应用类型（优先使用从地址中提取的类型）
            let finalAppType = extractedAppType != .manual ? extractedAppType : self.userSelectedAppType
            print("🎯 DEBUG: AddressProcessingQueue - 应用类型设置逻辑 - finalAppType: '\(finalAppType.rawValue)', 地址: \(cleanAddress)")
            print("🎯 DEBUG: AddressProcessingQueue - 条件检查:")
            print("🎯 DEBUG: AddressProcessingQueue -   finalAppType != .manual = \(finalAppType != DeliveryAppType.manual)")
            print("🎯 DEBUG: AddressProcessingQueue -   finalAppType != .justPhoto = \(finalAppType != DeliveryAppType.justPhoto)")
            let shouldSetAppType = finalAppType != DeliveryAppType.manual && finalAppType != DeliveryAppType.justPhoto
            print("🎯 DEBUG: AddressProcessingQueue -   最终条件结果 = \(shouldSetAppType)")

            if shouldSetAppType {
                newPoint.sourceAppRaw = finalAppType.rawValue
                logInfo("📱 设置应用类型: \(finalAppType.rawValue) -> \(cleanAddress)")
            } else {
                // justPhoto或manual类型保持默认值（manual）
                newPoint.sourceAppRaw = DeliveryAppType.manual.rawValue
            }

            // 🎯 设置第三方排序号（最重要的数据，绝对不能丢失）
            if !thirdPartySortNumber.isEmpty {
                newPoint.thirdPartySortNumber = thirdPartySortNumber
                print("🎯 DEBUG: AddressProcessingQueue - 设置第三方排序号: \(thirdPartySortNumber) -> \(cleanAddress)")
            }

            // 🎯 设置其他解析出的信息
            if !trackingNumber.isEmpty {
                newPoint.trackingNumber = trackingNumber
                print("🎯 DEBUG: AddressProcessingQueue - 设置追踪号: \(trackingNumber)")
            }
            if !customerName.isEmpty {
                newPoint.customerName = customerName
                print("🎯 DEBUG: AddressProcessingQueue - 设置客户姓名: \(customerName)")
            }
            if !deliveryTime.isEmpty {
                newPoint.scheduledDeliveryTime = deliveryTime
                print("🎯 DEBUG: AddressProcessingQueue - 设置配送时间: \(deliveryTime)")
            }

            // 🏢 首先从原始地址中提取单位信息（在地理编码之前）
            let extractedUnit = DeliveryPoint.extractUnitNumber(from: cleanAddress)
            if let unit = extractedUnit {
                newPoint.unitNumber = unit
                logInfo("🏢 提取到单位信息: \(unit) <- \(cleanAddress)")
            }

            // 🏗️ 如果有验证结果且包含 placemark，填充结构化地址字段
            if let validationResult = task.validationResult,
               let placemark = validationResult.placemark {
                newPoint.populateStructuredAddress(from: placemark)

                // 🎯 重要：保持提取到的单位信息，不被placemark覆盖
                if let unit = extractedUnit {
                    newPoint.unitNumber = unit
                    logInfo("🏢 保持提取的单位信息: \(unit)（防止被placemark覆盖）")
                }

                logInfo("🏗️ 已填充结构化地址字段: \(newPoint.streetNumber ?? "无") \(newPoint.streetName ?? "无"), \(newPoint.suburb ?? "无")")
            } else {
                // 如果没有placemark，尝试从完整地址中提取街道名称部分
                let addressComponents = cleanAddress.components(separatedBy: ",")
                if let firstComponent = addressComponents.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
                    // 使用清理后的地址（移除单位信息）作为街道名称
                    let cleanedStreetAddress = DeliveryPoint.removeUnitNumber(from: firstComponent)
                    newPoint.streetName = cleanedStreetAddress
                    logInfo("🔧 从完整地址提取的街道名称: \(cleanedStreetAddress)")
                }
            }

            // 设置验证警告信息
            if let validationResult = task.validationResult {
                if let warningMessage = validationResult.warningMessage, !warningMessage.isEmpty {
                    // 根据警告类型设置相应的字段
                    if warningMessage.contains("无法获取坐标") || warningMessage.contains("cannot_get_coordinates") {
                        newPoint.setGeocodingWarning(.coordinateMissing)
                    } else if warningMessage.contains("地址精度较低") || warningMessage.contains("低精度") {
                        newPoint.setGeocodingWarning(.lowAccuracy)
                    } else if warningMessage.contains("地址部分匹配") || warningMessage.contains("部分匹配") {
                        newPoint.setGeocodingWarning(.partialMatch)
                    } else {
                        // 通用验证问题
                        newPoint.addressValidationIssues = warningMessage
                        newPoint.addressValidationScore = 50.0 // 设置一个中等分数
                    }
                    logInfo("AddressProcessingQueue - 保存验证警告: \(warningMessage)")
                }
            } else if !hasValidCoordinate {
                // 兼容旧逻辑：如果坐标无效且没有验证结果
                newPoint.setGeocodingWarning(.coordinateMissing)
            }

            // 插入到数据上下文
            context.insert(newPoint)

            // 确保点已经被正确插入到数据上下文
            logInfo("点已插入到数据上下文: \(newPoint.primaryAddress)")

            // 添加到路线的点数组中，使用路线提供的辅助方法
            if newPoint.isStartPoint {
                if let oldStart = route.points.first(where: { $0.isStartPoint }) {
                    logInfo("替换现有起点")
                    route.removePoint(oldStart, modelContext: context)
                }
                // 插入到开头
                route.insertPoint(newPoint, at: 0)
                logInfo("起点已插入到位置0: \(newPoint.primaryAddress)")
            } else if newPoint.isEndPoint {
                if let oldEnd = route.points.first(where: { $0.isEndPoint }) {
                    logInfo("替换现有终点")
                    route.removePoint(oldEnd, modelContext: context)
                }
                // 添加到末尾
                route.addPoint(newPoint)
                logInfo("终点已添加: \(newPoint.primaryAddress)")
            } else { // 停靠点
                if let endPointIndex = route.points.firstIndex(where: { $0.isEndPoint }) {
                    route.insertPoint(newPoint, at: endPointIndex)
                    logInfo("停靠点插入到终点前 at index \(endPointIndex): \(newPoint.primaryAddress)")
                } else {
                    route.addPoint(newPoint)
                    logInfo("停靠点添加到末尾: \(newPoint.primaryAddress)")
                }
            }

            // 确保关系已正确建立
            if newPoint.route == nil {
                logInfo("关系未建立，手动设置")
                newPoint.route = route
            }

            // 保存新点和关系
            try context.save()
            logInfo("新点 \(newPoint.primaryAddress) 已插入并保存")

            // 通知UI更新
            NotificationCenter.default.post(name: Notification.Name("RoutePointsUpdated"), object: nil)

        } catch {
            // 🎯 从地址中提取原始地址用于错误报告
            let errorAddressInfo = DeliveryPointManager.shared.separateAddressAndTracking(task.address)
            let errorCleanAddress = errorAddressInfo.address
            logError("添加地址到路线失败: \(errorCleanAddress), 错误: \(error.localizedDescription)")

            // 尝试恢复：如果保存失败，至少确保UI知道有问题
            NotificationCenter.default.post(
                name: Notification.Name("AddressProcessingError"),
                object: ["address": errorCleanAddress, "error": error.localizedDescription]
            )
        }
    }

    // 获取当前队列状态
    func getQueueStatus() -> (pending: Int, processing: Int, completed: Int, failed: Int) {
        // 创建队列的安全副本
        let queueCopy: [AddressTask] = {
            if Thread.isMainThread {
                return self.queue
            } else {
                var result: [AddressTask] = []
                let semaphore = DispatchSemaphore(value: 0)

                DispatchQueue.main.async { [weak self] in
                    if let queue = self?.queue {
                        result = queue
                    }
                    semaphore.signal()
                }

                semaphore.wait()
                return result
            }
        }()

        let pending = queueCopy.filter { $0.status == .pending }.count
        let processing = queueCopy.filter { $0.status == .processing }.count
        let completed = queueCopy.filter { $0.status == .completed }.count
        let failed = queueCopy.filter { $0.status == .failed }.count

        return (pending, processing, completed, failed)
    }

    // 清空队列
    func clearQueue() {
        // 确保在主线程上执行
        if Thread.isMainThread {
            queue.removeAll()
            stopProcessing()
            logInfo("队列已清空")

            // 通知UI更新
            onStatusUpdate?()
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.clearQueue()
            }
        }
    }

    // 🎯 从地址中提取原始地址和应用类型信息
    private func extractAddressAndAppType(from address: String) -> (cleanAddress: String, appType: DeliveryAppType) {
        if address.contains("|APP:") {
            let components = address.components(separatedBy: "|APP:")
            if components.count > 1 {
                let cleanAddress = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let appTypeString = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                let appType = DeliveryAppType(rawValue: appTypeString) ?? .manual
                return (cleanAddress, appType)
            }
        }
        return (address, .manual)
    }

    // 🎯 提取地址的基础部分用于重复检测
    private func extractBaseAddress(_ address: String) -> String {
        // 移除所有元数据标签
        var baseAddress = address
        let patterns = ["|SORT:", "|TRACK:", "|CUSTOMER:", "|APP:", "|TIME:"]
        for pattern in patterns {
            if let range = baseAddress.range(of: pattern) {
                baseAddress = String(baseAddress[..<range.lowerBound])
            }
        }

        // 提取门牌号和街道名的核心部分
        let components = baseAddress.components(separatedBy: ",")
        if let streetPart = components.first?.trimmingCharacters(in: .whitespacesAndNewlines) {
            return streetPart.lowercased()
                .replacingOccurrences(of: "circle", with: "cir")
                .replacingOccurrences(of: "drive", with: "dr")
                .replacingOccurrences(of: "street", with: "st")
                .replacingOccurrences(of: "avenue", with: "ave")
                .replacingOccurrences(of: "court", with: "ct")
                .replacingOccurrences(of: "lane", with: "ln")
                .replacingOccurrences(of: "road", with: "rd")
                .replacingOccurrences(of: "boulevard", with: "blvd")
                .trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return baseAddress.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // 🎯 比较两个地址是否相似（用于重复检测）
    private func areAddressesSimilar(_ address1: String, _ address2: String) -> Bool {
        let normalized1 = address1.replacingOccurrences(of: " ", with: "")
        let normalized2 = address2.replacingOccurrences(of: " ", with: "")

        // 完全匹配
        if normalized1 == normalized2 {
            return true
        }

        // 检查是否包含相同的门牌号和街道名关键词
        let words1 = Set(address1.components(separatedBy: " ").filter { !$0.isEmpty })
        let words2 = Set(address2.components(separatedBy: " ").filter { !$0.isEmpty })

        let intersection = words1.intersection(words2)
        let union = words1.union(words2)

        // 如果交集占并集的80%以上，认为是相似地址
        let similarity = Double(intersection.count) / Double(union.count)
        return similarity >= 0.8
    }
}
