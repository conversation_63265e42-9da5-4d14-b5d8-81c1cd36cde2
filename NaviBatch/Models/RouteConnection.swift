import Foundation
import CoreLocation

/// 路线连接线段，用于表示实际路线的坐标点序列
struct RouteConnection: Identifiable {
    let id = UUID()
    let startCoordinate: CLLocationCoordinate2D
    let endCoordinate: CLLocationCoordinate2D
    let routeCoordinates: [CLLocationCoordinate2D] // 🚨 新增：完整的路线坐标点

    // 🚨 旧的初始化方法已删除 - 强制使用实际路线坐标

    // 🚨 推荐的初始化方法（使用完整路线坐标）
    init(startCoordinate: CLLocationCoordinate2D, endCoordinate: CLLocationCoordinate2D, routeCoordinates: [CLLocationCoordinate2D]) {
        self.startCoordinate = startCoordinate
        self.endCoordinate = endCoordinate

        print("🔍 RouteConnection初始化:")
        print("🔍 起点: (\(String(format: "%.6f", startCoordinate.latitude)), \(String(format: "%.6f", startCoordinate.longitude)))")
        print("🔍 终点: (\(String(format: "%.6f", endCoordinate.latitude)), \(String(format: "%.6f", endCoordinate.longitude)))")
        print("🔍 传入坐标数: \(routeCoordinates.count)")

        // 🚨 严格验证：完全禁止直线连接降级
        if routeCoordinates.isEmpty {
            print("🚨 RouteConnection - 错误：传入空的路线坐标，拒绝创建直线连接")
            // 🚨 关键修复：不允许降级到直线连接，使用空数组表示无效路线
            self.routeCoordinates = []
        } else if routeCoordinates.count == 2 {
            // 🚨 验证是否为真正的直线连接
            let start = routeCoordinates[0]
            let end = routeCoordinates[1]
            print("🔍 检查2点路线:")
            print("🔍 路线第1点: (\(String(format: "%.6f", start.latitude)), \(String(format: "%.6f", start.longitude)))")
            print("🔍 路线第2点: (\(String(format: "%.6f", end.latitude)), \(String(format: "%.6f", end.longitude)))")

            let isDirectLine = (abs(start.latitude - startCoordinate.latitude) < 0.000001 &&
                               abs(start.longitude - startCoordinate.longitude) < 0.000001 &&
                               abs(end.latitude - endCoordinate.latitude) < 0.000001 &&
                               abs(end.longitude - endCoordinate.longitude) < 0.000001)

            if isDirectLine {
                print("🚨 RouteConnection - 检测到直线连接，拒绝创建")
                self.routeCoordinates = []
            } else {
                print("✅ RouteConnection - 使用2点路线坐标（可能是短距离真实路线）")
                self.routeCoordinates = routeCoordinates
            }
        } else {
            print("✅ RouteConnection - 使用完整路线坐标，点数: \(routeCoordinates.count)")
            if routeCoordinates.count > 2 {
                print("🔍 路线第1点: (\(String(format: "%.6f", routeCoordinates[0].latitude)), \(String(format: "%.6f", routeCoordinates[0].longitude)))")
                print("🔍 路线第2点: (\(String(format: "%.6f", routeCoordinates[1].latitude)), \(String(format: "%.6f", routeCoordinates[1].longitude)))")
                print("🔍 路线最后点: (\(String(format: "%.6f", routeCoordinates.last!.latitude)), \(String(format: "%.6f", routeCoordinates.last!.longitude)))")
            }
            self.routeCoordinates = routeCoordinates
        }

        print("🔍 最终RouteConnection坐标数: \(self.routeCoordinates.count)")
    }

    // 🚨 便利方法：检查是否为实际路线（而非直线）
    var isRealRoute: Bool {
        return routeCoordinates.count > 2
    }

    // 🚨 便利方法：获取路线总距离
    var totalDistance: CLLocationDistance {
        guard routeCoordinates.count >= 2 else { return 0 }

        var distance: CLLocationDistance = 0
        for i in 0..<(routeCoordinates.count - 1) {
            let start = CLLocation(latitude: routeCoordinates[i].latitude, longitude: routeCoordinates[i].longitude)
            let end = CLLocation(latitude: routeCoordinates[i + 1].latitude, longitude: routeCoordinates[i + 1].longitude)
            distance += start.distance(from: end)
        }
        return distance
    }
}
