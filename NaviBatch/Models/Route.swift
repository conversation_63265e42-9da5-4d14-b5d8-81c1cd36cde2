import Foundation
import SwiftData
import os.log

// 确保可以使用全局日志函数

// 添加路线状态枚举
enum RouteStatus: String, Codable {
    case pending = "pending"       // 待处理
    case inProgress = "inProgress" // 进行中
    case completed = "completed"   // 已完成

    // 可以添加 localizedName 等辅助属性
    var localizedName: String {
        switch self {
        case .pending: return "待处理"
        case .inProgress: return "进行中"
        case .completed: return "已完成"
        }
    }
}

@Model
final class Route: Identifiable {
    var id: UUID
    var name: String
    var createdAt: Date
    var status: String = RouteStatus.pending.rawValue // 添加 status 并提供默认值
    var isOptimized: Bool = false // 标记路线是否已经过优化处理

    // 🎯 动态本地化的路线名称
    var localizedName: String {
        // 检查是否是默认路线名称格式 (Route YYYY-MM-DD 或 路线 YYYY-MM-DD)
        if name.hasPrefix("Route ") || name.hasPrefix("路线 ") {
            // 提取日期部分
            let datePattern = "\\d{4}-\\d{2}-\\d{2}"
            if let regex = try? NSRegularExpression(pattern: datePattern, options: []),
               let match = regex.firstMatch(in: name, options: [], range: NSRange(location: 0, length: name.count)),
               let dateRange = Range(match.range, in: name) {
                let dateString = String(name[dateRange])

                // 检查是否有编号 (例如 "Route 2025-06-03 (2)")
                let numberPattern = "\\((\\d+)\\)$"
                if let numberRegex = try? NSRegularExpression(pattern: numberPattern, options: []),
                   let numberMatch = numberRegex.firstMatch(in: name, options: [], range: NSRange(location: 0, length: name.count)),
                   let numberRange = Range(numberMatch.range(at: 1), in: name) {
                    let number = String(name[numberRange])
                    return "\("route".localized) \(dateString) (\(number))"
                } else {
                    return "\("route".localized) \(dateString)"
                }
            }
        }

        // 如果不是默认格式，返回原始名称
        return name
    }

    // --- 关键关系 ---
    // 使用 @Relationship 显式定义
    // deleteRule: .cascade 表示删除 Route 时，其包含的 points 也删除
    @Relationship(deleteRule: .cascade)
    var points: [DeliveryPoint] = []



    // --- 可选的额外信息 ---
    var totalDistance: Double? // 总距离 (米)
    var estimatedDuration: Double? // 预计时长 (秒)

    // --- 路线规划选项 ---
    var avoidTolls: Bool = false // 避免收费道路
    var avoidHighways: Bool = false // 避免高速公路

    // 更新 init 方法以包含新字段
    init(id: UUID = UUID(), name: String, createdAt: Date = Date(), status: String = RouteStatus.pending.rawValue,
         points: [DeliveryPoint] = [], totalDistance: Double? = nil, estimatedDuration: Double? = nil, isOptimized: Bool = false,
         avoidTolls: Bool = false, avoidHighways: Bool = false) {
        self.id = id
        self.name = name
        self.createdAt = createdAt
        self.status = status // 设置 status
        self.points = points
        self.totalDistance = totalDistance // 设置可选字段
        self.estimatedDuration = estimatedDuration // 设置可选字段
        self.isOptimized = isOptimized // 设置优化状态
        self.avoidTolls = avoidTolls // 设置路线规划选项
        self.avoidHighways = avoidHighways // 设置路线规划选项
    }

    // 添加修改 points 的辅助方法
    func addPoint(_ point: DeliveryPoint) {
        // 确保关系已正确建立
        point.route = self

        // 添加点到数组
        points.append(point)
        print("[INFO] Route.addPoint - 已添加点 \(point.primaryAddress) 到路线 \(self.name)")

        // 验证点是否已正确添加
        if points.contains(where: { $0.id == point.id }) {
            print("[INFO] Route.addPoint - 验证成功：点已添加到路线的 points 数组中")
        } else {
            print("[WARNING] Route.addPoint - 验证失败：点未出现在路线的 points 数组中")
        }

        // 发送数据变化通知
        NotificationCenter.default.post(
            name: Notification.Name("RouteDataChanged"),
            object: self.id.uuidString
        )
        print("[INFO] Route.addPoint - 已发送数据变化通知")
    }

    func removePoint(_ point: DeliveryPoint, modelContext: ModelContext) {
        // 先记录详细日志
        print("[INFO] Route.removePoint - 开始移除点 ID=\(point.id.uuidString), 地址=\(point.primaryAddress)")
        print("[INFO] Route.removePoint - 当前路线有 \(points.count) 个点")

        // 确认点确实存在于当前路线的 points 集合中
        if points.contains(where: { $0.id == point.id }) {
            print("[INFO] Route.removePoint - 点存在于路线的 points 数组中。")

            // 🎯 先断开关系，然后删除点
            point.route = nil
            print("[INFO] Route.removePoint - 已断开点与路线的关系")

            // 从 ModelContext 中删除 DeliveryPoint
            modelContext.delete(point)
            print("[INFO] Route.removePoint - 已请求从 ModelContext 删除点")

            // 🎯 手动从points数组中移除，确保立即生效
            if let index = points.firstIndex(where: { $0.id == point.id }) {
                points.remove(at: index)
                print("[INFO] Route.removePoint - 已从points数组中移除点，剩余 \(points.count) 个点")
            }

            // 发送数据变化通知
            NotificationCenter.default.post(
                name: Notification.Name("RouteDataChanged"),
                object: self.id.uuidString
            )
            print("[INFO] Route.removePoint - 已发送数据变化通知")
        } else {
            print("[ERROR] Route.removePoint - 点 ID=\(point.id.uuidString) 未在当前路线的 points 数组中找到。可能已被删除或数据不一致。")
        }
    }

    func removeAllPoints() {
        for point in points {
            point.route = nil
        }
        points.removeAll()

        // 发送数据变化通知
        NotificationCenter.default.post(
            name: Notification.Name("RouteDataChanged"),
            object: self.id.uuidString
        )
        print("[INFO] Route.removeAllPoints - 已移除所有点并发送数据变化通知")
    }

    func insertPoint(_ point: DeliveryPoint, at index: Int) {
        // 确保关系已正确建立
        point.route = self

        // 插入点到数组
        if index < points.count {
            points.insert(point, at: index)
            print("[INFO] Route.insertPoint - 已在位置 \(index) 插入点 \(point.primaryAddress)")
        } else {
            points.append(point)
            print("[INFO] Route.insertPoint - 索引 \(index) 超出范围，已将点 \(point.primaryAddress) 添加到末尾")
        }

        // 发送数据变化通知
        NotificationCenter.default.post(
            name: Notification.Name("RouteDataChanged"),
            object: self.id.uuidString
        )
        print("[INFO] Route.insertPoint - 已发送数据变化通知")
    }

    // 创建一个带有当前日期的默认名称
    static func defaultName() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: Date())

        // 🎯 修复：使用固定的英文模板，避免本地化问题
        // 路线名称将在显示时动态本地化，而不是在创建时固定
        let baseName = "Route \(dateString)"

        // 使用 UserDefaults 来跟踪同一天创建的路线数量
        let defaults = UserDefaults.standard

        // 使用日期作为键的一部分，确保每天都有唯一的键
        let routeCountKey = "routeCount_\(dateString)"

        // 使用 UserDefaults 中的计数器
        let userDefaultsCount = defaults.integer(forKey: routeCountKey)
        let count = userDefaultsCount + 1

        // 保存新的计数
        defaults.set(count, forKey: routeCountKey)

        print("[INFO] Route.defaultName - 今天(\(dateString))创建的路线数: \(count)")

        // 如果是第一个路线，使用基本名称，否则添加编号
        if count == 1 {
            print("[INFO] Route.defaultName - 创建新路线，使用基本名称: \(baseName)")
            return baseName
        } else {
            let result = "\(baseName) (\(count))"
            print("[INFO] Route.defaultName - 创建新路线，使用递增名称: \(result)")
            return result
        }
    }

    // 当路线被删除时调用此方法来减少计数器
    static func decrementRouteCount(for routeName: String) {
        // 检查路线名称是否包含日期
        let datePattern = "\\d{4}-\\d{2}-\\d{2}"
        guard let regex = try? NSRegularExpression(pattern: datePattern, options: []),
              let match = regex.firstMatch(in: routeName, options: [], range: NSRange(location: 0, length: routeName.count)) else {
            print("[INFO] Route.decrementRouteCount - 无法从路线名称中提取日期: \(routeName)")
            return
        }

        // 提取日期字符串
        let dateRange = Range(match.range, in: routeName)!
        let dateString = String(routeName[dateRange])

        // 使用日期作为键的一部分
        let routeCountKey = "routeCount_\(dateString)"

        // 获取当前计数
        let defaults = UserDefaults.standard
        let currentCount = defaults.integer(forKey: routeCountKey)

        // 如果计数大于0，减少1
        if currentCount > 0 {
            defaults.set(currentCount - 1, forKey: routeCountKey)
            print("[INFO] Route.decrementRouteCount - 已减少\(dateString)的路线计数，新计数: \(currentCount - 1)")
        }
    }

    // 重置指定日期的路线计数器
    static func resetRouteCount(for date: Date? = nil) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        // 如果没有指定日期，使用当前日期
        let dateString = dateFormatter.string(from: date ?? Date())

        // 使用日期作为键的一部分
        let routeCountKey = "routeCount_\(dateString)"

        // 重置计数器
        UserDefaults.standard.set(0, forKey: routeCountKey)
        print("[INFO] Route.resetRouteCount - 已重置\(dateString)的路线计数器")
    }

    // 同步路线计数器与实际路线数量
    @MainActor
    static func syncRouteCount() async {
        do {
            // 使用应用程序的共享持久化存储容器，而不是创建新的临时容器
            let modelContainer = getPersistentContainer()
            let descriptor = FetchDescriptor<Route>()
            let routes = try modelContainer.mainContext.fetch(descriptor)

            // 记录数据库路径，用于调试
            if let url = modelContainer.configurations.first?.url {
                logInfo("Route.syncRouteCount - 使用数据库路径: \(url.path)")
            }

            // 按日期分组路线
            let datePattern = "\\d{4}-\\d{2}-\\d{2}"
            let regex = try NSRegularExpression(pattern: datePattern, options: [])

            // 创建一个字典来记录每个日期的路线数量
            var dateCountMap: [String: Int] = [:]

            for route in routes {
                if let match = regex.firstMatch(in: route.name, options: [], range: NSRange(location: 0, length: route.name.count)),
                   let dateRange = Range(match.range, in: route.name) {
                    let dateString = String(route.name[dateRange])
                    dateCountMap[dateString, default: 0] += 1
                }
            }

            // 更新 UserDefaults 中的计数器
            let defaults = UserDefaults.standard
            for (dateString, count) in dateCountMap {
                let routeCountKey = "routeCount_\(dateString)"
                defaults.set(count, forKey: routeCountKey)
                logInfo("Route.syncRouteCount - 已同步\(dateString)的路线计数器，实际路线数: \(count)")
            }

        } catch {
            logError("Route.syncRouteCount - 同步路线计数器失败: \(error.localizedDescription)")
        }
    }
}
