# Apple Developer Support Contact Template

## Subject: Urgent: Subscription Billing Issue - NaviBatch Route Planner

Dear Apple Developer Support Team,

I am writing to report a critical subscription billing issue with our app **NaviBatch Route Planner** that may have affected our users.

### App Information
- **App Name**: NaviBatch Route Planner
- **Bundle ID**: com.navibatch.app
- **Apple ID**: [Your App Store ID]
- **Developer Account**: [Your Developer Account Email]
- **Team ID**: [Your Team ID]

### Issue Description
We have discovered that our subscription products may have been affected by sandbox environment time acceleration, potentially causing unexpected billing for our users:

**Affected Products:**
1. **Monthly Pro Subscription**
   - Product ID: `com.navibatch.subscription.monthly`
   - Configured with 60-day free trial
   
2. **Annual Pro Subscription**
   - Product ID: `com.navibatch.subscription.annual`
   - Configured with 60-day free trial

### Problem Details
- **Issue**: Sandbox time acceleration may have caused trial periods to expire prematurely
- **Impact**: Users who intended to use the 60-day free trial may have been charged
- **Timeline**: [Specify the date range when this might have occurred]
- **Estimated Affected Users**: [If you have this data]

### Our Investigation
We have identified that:
1. StoreKit configuration shows correct 60-day trial period (`P60D`)
2. Sandbox environment accelerates subscription timelines for testing
3. Some users may have experienced shortened trial periods in production

### Requested Actions
1. **Investigation**: Please help us identify if any users were incorrectly charged
2. **Refunds**: Assist with processing refunds for affected users
3. **Prevention**: Guidance on preventing similar issues in the future
4. **User Communication**: Advice on how to communicate with affected users

### Supporting Documentation
- StoreKit configuration file showing correct trial period settings
- App Store Connect subscription setup screenshots
- User reports (if any)

### Our Commitment
We are committed to:
- Ensuring fair billing practices for all users
- Providing excellent customer service
- Maintaining compliance with App Store guidelines

### Contact Information
- **Primary Contact**: [Your Name]
- **Email**: [Your Email]
- **Phone**: [Your Phone Number]
- **Preferred Response Time**: Within 24-48 hours due to urgency

We appreciate your prompt attention to this matter and look forward to your guidance on resolving this issue for our users.

Best regards,
[Your Name]
[Your Title]
[Company Name]

---

### Additional Notes for Internal Use

**Before Sending:**
1. ✅ Replace all placeholder information with actual data
2. ✅ Gather supporting documentation
3. ✅ Check App Store Connect for any user complaints
4. ✅ Review subscription analytics for unusual patterns
5. ✅ Prepare to provide additional information if requested

**Follow-up Actions:**
1. Monitor App Store Connect for Apple's response
2. Prepare user communication plan
3. Consider temporary suspension of subscriptions if needed
4. Document all communications for compliance records
