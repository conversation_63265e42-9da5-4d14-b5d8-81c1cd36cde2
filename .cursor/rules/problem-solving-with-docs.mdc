---
description: get info form lastest doc
globs: 
alwaysApply: false
---
# 使用文档工具解决问题指南

当你在开发过程中遇到问题，并且认为相关文档可能包含解决方案时，请按以下步骤操作：

1.  **识别相关库**: 首先确定问题可能与哪个库或框架相关。

2.  **解析库ID**: 使用 `resolve-library-id` 工具函数来查找该库的Context7兼容ID。
    *   例如: `mcp_context7_resolve-library-id libraryName="<你认为相关的库名>"`
    *   仔细检查返回结果，确保选择的是正确的库。

3.  **获取并查阅库文档**: 获得准确的库ID后，使用 `get-library-docs` 工具函数来获取该库的文档。
    *   例如: `mcp_context7_get-library-docs context7CompatibleLibraryID="<从上一步获得的库ID>" topic="<可选，可以是你遇到的问题相关的关键词，如'error handling', 'async operations', 'state management'>"`
    *   认真阅读返回的文档，寻找与你问题相关的部分，尝试从中找到解决方案或线索。

这个流程可以帮助你更系统地利用可用的文档资源来解决开发中遇到的难题。
