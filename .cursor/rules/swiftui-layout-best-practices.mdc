---
description: 
globs: 
alwaysApply: true
---
# SwiftUI布局和对齐最佳实践

## 内边距管理原则

- **避免重复内边距**: 在嵌套视图中重复添加相同方向的padding会导致对齐问题
  - ❌ 错误: 在子视图和父视图都添加`.padding(.horizontal, 16)`
  - ✅ 正确: 只在容器级别添加一次水平内边距

- **利用SwiftUI的安全区域**: SwiftUI已内置对安全区域的支持，如[RouteBottomSheet.swift](mdc:NaviBatch/Views/Components/RouteBottomSheet.swift)中发现的，额外的padding可能破坏对齐

## 扁平化布局结构

- 优先使用扁平的视图层次而非深度嵌套
- 使用GeometryReader确保精确对齐时，记得设置固定尺寸
- 减少不必要的Button包装，可使用onTapGesture替代简单点击交互

## 对齐控制

- 使用固定宽度区域实现垂直对齐，例如时间轴组件使用固定宽度
- 确保顶部区域元素(如图标)与内容列表中对应元素宽度一致
- 使用ZStack和透明占位元素保持布局结构一致性

## 布局调试技巧

- 注释掉可疑的padding查看效果，特别是嵌套层次中的内边距
- 使用不同背景色临时标记区域边界，帮助直观识别对齐问题
- 在重构布局前保留注释掉的原始代码作为参考

## 列表项布局

- 对于如RoutePointRow这样的列表项，保持水平对齐至关重要
- 删除按钮等控件应使用固定宽度确保右对齐一致性
- 标签和图标使用适当的间距和尺寸确保视觉平衡
