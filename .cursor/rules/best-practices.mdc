---
description: 
globs: 
alwaysApply: true
---
# iOS应用开发最佳实践

## 代码版本控制
- 使用Git进行版本控制
- 遵循GitFlow或类似的分支策略
- 提交消息应清晰描述更改内容
- 定期合并主分支以避免冲突

## 项目结构组织
- 按功能模块组织代码
- 相关文件放在同一目录下
- 保持目录结构浅而清晰
- 避免过深的嵌套

## SwiftUI最佳实践
- 使用ViewBuilder创建可复用视图组件
- 使用@State、@Binding和@EnvironmentObject管理状态
- 避免在视图中放置复杂业务逻辑
- 使用预览功能加快开发速度

## Swift语言最佳实践
- 使用Swift的现代语言特性
- 优先使用结构体而非类（除非需要引用语义）
- 使用协议和扩展增强代码复用
- 使用合适的访问控制级别

## 性能优化
- 使用Instruments工具分析性能瓶颈
- 避免在ScrollView中放置复杂视图
- 使用懒加载减少启动时间
- 注意内存使用情况

## 错误处理
- 使用Result类型处理可恢复错误
- 为用户提供有意义的错误信息
- 实现全局错误处理机制
- 记录错误日志以便调试

## 依赖管理
- 最小化第三方库的使用
- 使用Swift Package Manager管理依赖
- 定期更新库版本以获取安全更新
- 考虑为关键功能创建自己的解决方案

## 本地化和国际化
- 从项目开始就计划国际化
- 使用NSLocalizedString或SwiftUI的本地化方法
- 支持从右到左的语言
- 考虑文本扩展对布局的影响

## 安全最佳实践
- 不在代码中硬编码敏感信息
- 实现适当的认证和授权
- 保护用户数据和隐私
- 遵循最新的苹果安全建议
