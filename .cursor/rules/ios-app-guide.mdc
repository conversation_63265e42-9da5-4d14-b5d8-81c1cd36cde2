---
description: 
globs: 
alwaysApply: true
---
# iOS应用开发指南

## 项目结构
- 主入口文件：[NaviBatchApp.swift](mdc:NaviBatch/NaviBatchApp.swift)
- 主视图：[ContentView.swift](mdc:NaviBatch/ContentView.swift)
- 资源目录：`NaviBatch/Assets.xcassets/`

## 架构规范
- 采用MVVM架构
- Models: [Models目录](mdc:NaviBatch/Models)
- Views: [Views目录](mdc:NaviBatch/Views)
- ViewModels: [ViewModels目录](mdc:NaviBatch/ViewModels)
- 实用工具: [Utilities目录](mdc:NaviBatch/Utilities)和[Utils目录](mdc:NaviBatch/Utils)

## 编码规范
- 使用最新版Swift，避免使用Objective-C
- 类名、枚举和协议使用UpperCamelCase
- 变量、函数和属性使用lowerCamelCase
- 使用描述性命名
- 添加必要的文档注释

## UI/UX规范
- 严格遵循苹果人机界面指南
- 支持暗黑模式和动态类型
- 使用SwiftUI自适应布局支持不同设备
- 设计友好的错误提示

## 数据处理
- 网络请求和数据存储在Managers目录下
- 使用[Managers目录](mdc:NaviBatch/Managers)中的类处理业务逻辑

## 测试规范
- 单元测试：[NaviBatchTests目录](mdc:NaviBatchTests)
- UI测试：[NaviBatchUITests目录](mdc:NaviBatchUITests)
- 确保测试覆盖率

## 性能优化
- 避免主线程阻塞
- 使用异步操作处理耗时任务
- 优化图片加载和缓存
- 避免内存泄漏
- 使用Instruments监控性能

## 安全规范
- 敏感数据使用Keychain存储
- 使用适当的数据加密
- 启用App Transport Security
- 实现适当的用户认证
