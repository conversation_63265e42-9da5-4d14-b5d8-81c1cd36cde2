---
description: 
globs: 
alwaysApply: true
---
# Apple设计规范指南

## 设计原则
- 清晰：文本应易于阅读，图标应精确且明确
- 尊重：流畅的动画和清晰的界面帮助用户理解和交互
- 深度：视觉层次和真实动效创造深度感
- 简洁：专注于基本元素，避免过度装饰

## 布局规范
- 使用安全区域(`safeAreaInsets`)确保内容不受刘海、动态岛等影响
- 尊重系统边距（通常为16pt）
- 使用动态字体支持所有字体大小
- 布局应支持设备旋转
- 支持全面屏设备的手势交互

## 导航和模态
- 使用标准iOS导航模式：标签栏、导航栏
- 模态视图应提供清晰的返回或关闭选项
- 上下文切换应该动画流畅，给用户明确的空间感
- 使用标准的导航手势

## 控件和交互
- 使用系统提供的标准控件(UIKit/SwiftUI)
- 交互元素最小尺寸为44×44点
- 采用系统标准间距和布局指南
- 支持暗黑模式
- 支持动态类型（文本大小调整）
- 使用系统标准的拖放交互

## 颜色和样式
- 使用系统颜色(如`Color.primary`, `Color.secondary`)
- 考虑色盲用户的颜色选择
- 确保暗黑模式下的适当对比度
- 遵循iOS的视觉风格（轻微阴影、半透明效果）
- 颜色应具有语义含义，保持一致性

## 按钮和控件
- 使用标准iOS按钮样式（不要自定义圆角和阴影）
- 主要操作使用突出的样式，次要操作使用标准样式
- 使用SF Symbols作为图标系统
- 交互控件应提供适当的触觉反馈
- 表单控件使用标准iOS样式

## 表单和输入
- 使用系统键盘和输入方式
- 明确的表单验证和错误提示
- 表单分组使用系统风格
- 提供适当的自动完成和智能建议
- 操作按钮放置在直观位置

## 列表和内容
- 使用标准的列表视图和单元格
- 支持滑动操作和上下文菜单
- 使用系统标准的分隔线和分组方式
- 空状态设计符合整体风格
- 长列表应支持搜索和筛选

## 文件导入/导出
- 使用系统文件选择器(UIDocumentPickerViewController)
- 支持标准的拖放操作
- 清晰显示支持的文件类型
- 使用标准的文件图标
- 提供清晰的导入/导出进度指示

## 无障碍设计
- 支持VoiceOver
- 提供适当的标签和提示
- 支持辅助触控
- 确保足够的颜色对比度
- 支持减弱动画效果的设置
