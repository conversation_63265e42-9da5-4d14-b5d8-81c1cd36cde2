---
description: 
globs: 
alwaysApply: true
---
# Material Design Components (MDC)指南

## MDC组件结构
应在NaviBatch项目中创建以下目录和文件：
- 组件目录: `NaviBatch/Components/MDC/`
- 基础组件文件:
  - `MDCButton.swift`: 按钮组件
  - `MDCTextField.swift`: 文本输入组件
  - `MDCCard.swift`: 卡片组件
  - `MDCDialog.swift`: 对话框组件
  - `MDCSnackbar.swift`: 提示条组件

## 组件设计规范
- 所有MDC组件应符合Material Design设计规范
- 支持浅色和深色主题
- 支持自定义主题颜色
- 使用SwiftUI实现所有组件

## 按钮组件规范
按钮组件应支持以下功能：
- 多种按钮类型：filled, outlined, text, elevated, tonal
- 支持图标和文本组合
- 支持禁用状态样式
- 自定义大小、圆角和颜色

## 文本输入组件规范
文本输入组件应支持以下功能：
- 输入框样式：outlined, filled
- 支持错误状态和帮助文本
- 支持前缀和后缀图标
- 支持字符计数和字符限制

## 卡片组件规范
卡片组件应支持以下功能：
- 支持不同的海拔高度和阴影
- 支持卡片头部和尾部
- 支持点击交互和涟漪效果
- 支持圆角自定义

## 对话框组件规范
对话框组件应支持以下功能：
- 支持标题、内容和操作按钮
- 支持全屏和模态两种模式
- 支持自定义进入和退出动画
- 支持可拖动行为

## 提示条组件规范
提示条组件应支持以下功能：
- 支持不同类型：info, success, warning, error
- 支持操作按钮
- 支持自动消失功能
- 支持手动关闭

## 组件使用指南
- 所有组件应有清晰的使用文档和示例
- 组件应尽可能支持SwiftUI的修饰符链式调用
- 组件应支持无障碍功能
- 应提供组件预览视图
