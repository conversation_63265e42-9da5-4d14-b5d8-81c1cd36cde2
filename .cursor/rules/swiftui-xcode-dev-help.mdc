---
description: doc
globs: 
alwaysApply: false
---
# SwiftUI 与 Xcode 开发问题指南

当遇到SwiftUI布局、代码组织或Xcode项目配置等相关问题时，请参考以下已有的规范和指南：

1.  **SwiftUI布局与对齐**:
    *   查阅 `swiftui-layout-best-practices.mdc` ([swiftui-layout-best-practices.mdc](mdc:.cursor/rules/swiftui-layout-best-practices.mdc)) 文件，获取关于内边距管理、扁平化布局结构、对齐控制以及列表项布局的最佳实践。

2.  **iOS应用开发最佳实践**:
    *   参考 `best-practices.mdc` ([best-practices.mdc](mdc:.cursor/rules/best-practices.mdc)) 文件，了解代码版本控制、项目结构、SwiftUI和Swift语言的最佳实践、性能优化、错误处理、依赖管理、本地化和安全等方面的通用指南。

3.  **项目特定指南 (NaviBatch)**:
    *   如果问题与 `NaviBatch` 项目相关，请查看 `ios-app-guide.mdc` ([ios-app-guide.mdc](mdc:.cursor/rules/ios-app-guide.mdc)) 文件，了解该项目的结构、架构规范、编码规范、UI/UX规范、数据处理和测试规范。

4.  **Apple设计规范**:
    *   对于UI/UX设计相关的问题，确保遵循 `apple-design-guidelines.mdc` ([apple-design-guidelines.mdc](mdc:.cursor/rules/apple-design-guidelines.mdc)) 中的设计原则和布局规范。

通过查阅这些文档，你应该能找到许多常见SwiftUI和Xcode开发问题的解决方案和指导。
