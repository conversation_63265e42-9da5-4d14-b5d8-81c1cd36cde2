---
description: 
globs: 
alwaysApply: true
---
# 文件导入UI规范指南

## 总体设计原则
- 遵循iOS原生设计风格，不使用Material Design等其他平台设计语言
- 使用系统提供的标准控件和交互模式
- 支持暗黑模式和动态字体大小
- 操作流程清晰简洁，减少用户认知负担

## 文件类型选择器
- 使用标准分段控制器(`SegmentedPickerStyle`)展示文件类型选项
- 每个选项使用SF Symbols图标和文本标签
- 不使用自定义标签风格或类似安卓的Material标签样式
- 选择器宽度适配屏幕，间距符合iOS标准

## 文件选择区域
- 使用系统文档选择器(`UIDocumentPickerViewController`)
- 拖放区域使用iOS风格的轮廓边框和轻微背景色
- 使用系统SF Symbols图标表示上传操作
- 操作提示文本使用系统字体层级
- 支持的文件类型说明使用次要文本样式

## 辅助操作按钮
- 示例文件下载和在线导入使用系统标准按钮样式
- 按钮包含SF Symbols图标和描述性文本
- 按钮大小符合iOS触摸目标最小尺寸(44pt)
- 按钮之间使用适当间距，保持整体布局平衡
- 交互状态（按下、禁用）符合iOS标准视觉反馈

## 主要操作按钮
- 底部操作按钮使用系统标准样式
- 主要操作使用填充式样式，强调其重要性
- 次要操作（如取消）使用轮廓式按钮样式
- 按钮位置符合iOS惯例（确认按钮在右侧）
- 使用标准的操作文本（"取消"、"导入"等）

## 导航与上下文
- 使用标准导航栏显示标题
- 提供清晰的关闭/取消选项
- 帮助按钮使用标准问号图标放置在导航栏
- 模态展示时使用卡片样式，支持下滑关闭手势
- 整体布局适当留白，创造清晰的视觉层次

## 错误处理与反馈
- 文件格式错误使用标准系统警告提示
- 导入进度使用系统进度指示器
- 操作成功使用适当的成功提示
- 错误信息清晰且提供解决建议
- 网络操作提供适当的加载状态指示

## 辅助功能支持
- 所有控件提供适当的VoiceOver标签
- 支持动态类型（放大字体）
- 颜色搭配考虑色盲用户
- 支持"减少动态效果"设置
- 所有交互元素符合最小点击区域要求
